"use client";

import React from "react";
import { Filter, <PERSON>, Setting<PERSON>, Eye, RotateCcw } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { componentStyles } from "@/lib/design-tokens";
import { useLayerManager, useLayerById } from "@/providers/tool-invocation-provider";
import { CompactResultTrigger } from "./common/compact-result-trigger";
import { getToolDisplayInfo } from "../annotations";

// 실제 타입에 맞게 업데이트
interface LayerFilterResponse {
  lyr_id: string;
  filter: string;
  description: string;
}

interface LayerFilterResultProps {
  content: LayerFilterResponse | string;
  className?: string;
}

// 컴팩트한 디자인을 위해 애니메이션 간소화



export function LayerFilterResult({ content, className }: LayerFilterResultProps) {
  let result: LayerFilterResponse;

  try {
    result = typeof content === 'string' ? JSON.parse(content) : content;
  } catch (e) {
    result = { lyr_id: '', filter: '', description: 'Invalid content format' };
  }

  // Hook을 최상단에 위치
  const { updateFilter } = useLayerManager();
  const layerInfo = useLayerById(result.lyr_id);

  const handleToggleFilter = async () => {
    try {
      updateFilter(result.lyr_id, result.filter);
      toast.success("필터가 적용되었습니다");
    } catch (error) {
      console.error("Filter update error:", error);
      toast.error("필터 적용 중 오류가 발생했습니다");
    }
  };

  const handleResetFilter = async () => {
    try {
      updateFilter(result.lyr_id, "");
      toast.success("필터가 초기화되었습니다");
    } catch (error) {
      console.error("Filter reset error:", error);
      toast.error("필터 초기화 중 오류가 발생했습니다");
    }
  };

  const handleEditFilter = () => {
    // 필터 편집 기능은 추후 구현
    toast.info("필터 편집 기능은 준비 중입니다");
  };

  if (!result.lyr_id || !result.filter) {
    return (
      <div className={cn(componentStyles.card.error, "p-3", className)}>
        <div className="flex items-center gap-2">
          <div className={cn(componentStyles.iconContainer.sm, "bg-red-100/80 text-red-600 border border-red-200/60")}>
            <Filter className="h-3 w-3" />
          </div>
          <div>
            <p className="font-medium text-red-900 text-sm">필터 적용에 실패했습니다</p>
            <p className="text-xs text-red-700">필터 조건을 확인하고 다시 시도해주세요</p>
          </div>
        </div>
      </div>
    );
  }

  const toolInfo = getToolDisplayInfo("createLayerFilter");

  return (
    <CompactResultTrigger
      icon={toolInfo.icon}
      title={toolInfo.label}
      state="result"
      className={className}
      titleExtra={
        <Badge variant="outline" className="text-xs py-0 border bg-emerald-100/80 text-emerald-700 border-emerald-200/60">
          필터 적용
        </Badge>
      }
    >

      <div className="space-y-3">
        <div className="text-xs text-neutral-600">
          레이어 ID: {result.lyr_id}
        </div>

        {/* 필터 조건 */}
        <div>
          <div className="flex items-center gap-2 mb-1">
            <Filter className="h-3 w-3 text-neutral-600" />
            <span className="text-sm font-medium text-neutral-700">필터 조건</span>
          </div>
          <div className="p-2 bg-white/60 rounded border border-neutral-200/40">
            <code className="text-xs text-neutral-800 font-mono break-all">
              {result.filter}
            </code>
          </div>
        </div>

        {/* 설명 */}
        {result.description && (
          <div>
            <div className="flex items-center gap-2 mb-1">
              <Settings className="h-3 w-3 text-neutral-600" />
              <span className="text-sm font-medium text-neutral-700">설명</span>
            </div>
            <p className="text-sm text-neutral-700">{result.description}</p>
          </div>
        )}

        {/* 액션 버튼들 */}
        <div className="flex items-center gap-2 pt-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleResetFilter}
            className="flex items-center gap-1 h-6 px-2 text-xs"
          >
            <RotateCcw className="h-3 w-3" />
            초기화
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleToggleFilter}
            className="flex items-center gap-1 h-6 px-2 text-xs"
          >
            <Eye className="h-3 w-3" />
            적용
          </Button>
        </div>
      </div>
    </CompactResultTrigger>
  );
}
