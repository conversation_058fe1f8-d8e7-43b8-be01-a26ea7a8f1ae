"use client";

import { useRouter } from "next/navigation";
import React, { useActionState, useEffect, useState } from "react";
import { toast } from "sonner";

import { login, LoginActionState } from "../actions";
import { AuthForm } from "@/components/auth-form";
import {SubmitButton} from "@/components/submit-button";
import {Badge} from "@/components/ui/badge";

export default function Page() {

	const router = useRouter();

	const [email, setEmail] = useState("admin");
	const [state, formAction] = useActionState<LoginActionState, FormData>(
		login,
		{
			status: "idle",
		},
	);

	useEffect(() => {
		if (state.status === "failed") {
			toast.error("로그인에 실패했습니다. 아이디와 비밀번호를 확인해주세요.");
		} else if (state.status === "invalid_data") {
			toast.error("로그인에 실패했습니다. 아이디와 비밀번호를 확인해주세요.");
		} else if (state.status === "success") {
			router.refresh();
		}
	}, [state.status, router]);

	const handleSubmit = (formData: FormData) => {
		setEmail(formData.get("email") as string);
		formAction(formData);
	};

	return (
		<div className="flex h-screen w-screen items-center justify-center bg-background">
			<div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12">
				<div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
					<h3 className="flex text-xl font-semibold gap-2 dark:text-zinc-50">
						{"로그인"}
						<Badge variant={"secondary"}>GeOn</Badge>
					</h3>
					<p className="text-sm text-gray-500 dark:text-zinc-400">
						{"admin 계정으로 로그인하세요."}
					</p>
				</div>
				<AuthForm action={handleSubmit} defaultEmail={email}>
					<SubmitButton>Sign in</SubmitButton>
					{/*<p className="text-center text-sm text-gray-600 mt-4 dark:text-zinc-400">*/}
					{/*	{"Don't have an account? "}*/}
					{/*	<Link*/}
					{/*		href="/register"*/}
					{/*		className="font-semibold text-gray-800 hover:underline dark:text-zinc-200"*/}
					{/*	>*/}
					{/*		Sign up*/}
					{/*	</Link>*/}
					{/*	{" for free."}*/}
					{/*</p>*/}
				</AuthForm>
			</div>
		</div>
	);
}
