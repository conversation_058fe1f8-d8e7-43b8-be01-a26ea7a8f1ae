{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Retriever 탐색\n", "- 외부데이터: \n", "  - test_kjy\n", "- retriever:\n", "  - <PERSON><PERSON><PERSON>\n", "  - bm25\n", "  - ensemble\n", "  - multi\n", "  - llm\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_openai import OpenAIEmbeddings\n", "from langchain_openai import ChatOpenAI\n", "from langchain_ollama import ChatOllama\n", "import os\n", "import csv\n", "import json\n", "# from langchain_openai import OpenAIEmbeddings\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_core.documents import Document\n", "\n", "# 토크나이저 추가\n", "from kiwipiepy import Kiwi\n", "kiwi = Kiwi()\n", "\n", "from langchain_text_splitters import MarkdownHeaderTextSplitter\n", "\n", "# 문서 검색기 생성\n", "from langchain_community.retrievers import BM25Retriever\n", "from langchain.retrievers import EnsembleRetriever\n", "from langchain_community.vectorstores import FAISS\n", "\n", "from dotenv import load_dotenv\n", "load_dotenv('./dot.env')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 실험데이터: test_kjy.md"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Document(metadata={'기능명': '배경지도 컨트롤 (BasemapControl)', '설명': '설명', 'source': 'test_kjy.md'}, page_content='배경지도를 설정하고 변경할 수 있는 컨트롤입니다. 다양한 유형의 배경지도를 지원하며, 사용자가 쉽게 전환할 수 있도록 합니다.'), Document(metadata={'기능명': '배경지도 컨트롤 (BasemapControl)', '설명': '설명', '세부항목': '키워드:', 'source': 'test_kjy.md'}, page_content='[배경지도 생성, 베이스맵, 베이스맵 컨트롤, basemapControl, 배경지도 설정]'), Document(metadata={'기능명': '배경지도 컨트롤 (BasemapControl)', '설명': '설명', '세부항목': '사용 예시:', 'source': 'test_kjy.md'}, page_content=\"```javascript\\n//배경지도 컨트롤 생성\\nvar basemapControl = new odf.BasemapControl();\\nbasemapControl.setMap(map);\\n\\n//특정 배경지도(색각지도)로 전환\\nbasemapControl.switchBaseLayer('eMapColor');\\n\\n//설정 가능한 배경지도 목록 조회\\nvar basemapList = basemapControl.getSetableBasemapList();\\n```\"), Document(metadata={'기능명': '배경지도 컨트롤 (BasemapControl)', '설명': '설명', '세부항목': '주요 메서드:', 'source': 'test_kjy.md'}, page_content='- setMap(map): 컨트롤을 지도에 추가\\n- switchBaseLayer(layerKey): 특정 배경지도로 전환\\n- getSetableBasemapList(): 설정 가능한 배경지도 목록 조회'), Document(metadata={'기능명': '배경지도 컨트롤 (BasemapControl)', '설명': '설명', '세부항목': '일반적인 사용 시나리오:', 'source': 'test_kjy.md'}, page_content='1. 기본 지도와 위성 이미지 간 전환\\n2. 테마별 지도 (교통, 지형 등) 제공\\n3. 사용자 정의 배경지도 추가 및 관리'), Document(metadata={'기능명': '줌 컨트롤 (ZoomControl)', '설명': '설명', 'source': 'test_kjy.md'}, page_content='지도의 확대 및 축소 기능을 제공하는 컨트롤입니다. 사용자가 지도의 상세 수준을 쉽게 조절할 수 있게 합니다.'), Document(metadata={'기능명': '줌 컨트롤 (ZoomControl)', '설명': '설명', '세부항목': '키워드:', 'source': 'test_kjy.md'}, page_content='[줌 컨트롤, 지도 확대, 지도 축소, 줌 레벨 조정]'), Document(metadata={'기능명': '줌 컨트롤 (ZoomControl)', '설명': '설명', '세부항목': '사용 예시:', 'source': 'test_kjy.md'}, page_content='```javascript\\n//줌 컨트롤 생성\\nvar zoomControl = new odf.ZoomControl();\\nzoomControl.setMap(map);\\n\\n//지도 확대\\nzoomControl.zoomIn();\\n\\n//지도 축소\\nzoomControl.zoomOut();\\n```'), Document(metadata={'기능명': '줌 컨트롤 (ZoomControl)', '설명': '설명', '세부항목': '주요 메서드:', 'source': 'test_kjy.md'}, page_content='- setMap(map): 컨트롤을 지도에 추가\\n- zoomIn(): 지도 확대\\n- zoomOut(): 지도 축소'), Document(metadata={'기능명': '줌 컨트롤 (ZoomControl)', '설명': '설명', '세부항목': '일반적인 사용 시나리오:', 'source': 'test_kjy.md'}, page_content='1. 특정 지역의 상세 정보 확인\\n2. 넓은 영역의 개요 파악\\n3. 동적인 지도 탐색'), Document(metadata={'기능명': '오버뷰 컨트롤 (OverviewMapControl)', '설명': '설명', 'source': 'test_kjy.md'}, page_content='현재 보고 있는 지도 영역의 전체적인 위치를 작은 지도로 보여주는 컨트롤입니다.'), Document(metadata={'기능명': '오버뷰 컨트롤 (OverviewMapControl)', '설명': '설명', '세부항목': '키워드:', 'source': 'test_kjy.md'}, page_content='[오버뷰 컨트롤, 미니맵, 전체 지도 보기, 위치 컨텍스트]'), Document(metadata={'기능명': '오버뷰 컨트롤 (OverviewMapControl)', '설명': '설명', '세부항목': '사용 예시:', 'source': 'test_kjy.md'}, page_content='```javascript\\n//overviewMapControl 생성\\nvar overviewMapControl = new odf.OverviewMapControl();\\noverviewMapControl.setMap(map);\\n```'), Document(metadata={'기능명': '오버뷰 컨트롤 (OverviewMapControl)', '설명': '설명', '세부항목': '주요 메서드:', 'source': 'test_kjy.md'}, page_content='- setMap(map): 컨트롤을 지도에 추가'), Document(metadata={'기능명': '오버뷰 컨트롤 (OverviewMapControl)', '설명': '설명', '세부항목': '일반적인 사용 시나리오:', 'source': 'test_kjy.md'}, page_content='1. 현재 보고 있는 영역의 주변 지역 파악\\n2. 빠른 네비게이션 및 위치 이동\\n3. 지도 전체에서의 현재 위치 확인'), Document(metadata={'기능명': '축척 컨트롤 (ScaleControl)', '설명': '설명', 'source': 'test_kjy.md'}, page_content='지도의 현재 축척을 표시하는 컨트롤입니다. 사용자에게 지도의 거리 정보를 제공합니다.'), Document(metadata={'기능명': '축척 컨트롤 (ScaleControl)', '설명': '설명', '세부항목': '키워드:', 'source': 'test_kjy.md'}, page_content='[축척, 지도 축척, 거리 표시, 스케일 바]'), Document(metadata={'기능명': '축척 컨트롤 (ScaleControl)', '설명': '설명', '세부항목': '사용 예시:', 'source': 'test_kjy.md'}, page_content='```javascript\\nvar scaleControl = new odf.ScaleControl();\\nscaleControl.setMap(map);\\n```'), Document(metadata={'기능명': '축척 컨트롤 (ScaleControl)', '설명': '설명', '세부항목': '주요 메서드:', 'source': 'test_kjy.md'}, page_content='- setMap(map): 컨트롤을 지도에 추가\\n- setScaleValue(scale): 축척 설정 (1:5000 -> 5000 입력)'), Document(metadata={'기능명': '축척 컨트롤 (ScaleControl)', '설명': '설명', '세부항목': '일반적인 사용 시나리오:', 'source': 'test_kjy.md'}, page_content='1. 지도 상의 실제 거리 파악\\n2. 정확한 측정 및 계획 수립\\n3. 다양한 줌 레벨에서의 거리 감각 제공'), Document(metadata={'기능명': '이전/다음 화면 이동 컨트롤 (MoveControl)', '설명': '설명', 'source': 'test_kjy.md'}, page_content='이전에 본 지도 위치로 이동하거나 다음 위치로 이동할 수 있는 기능을 제공하는 컨트롤입니다.'), Document(metadata={'기능명': '이전/다음 화면 이동 컨트롤 (MoveControl)', '설명': '설명', '세부항목': '키워드:', 'source': 'test_kjy.md'}, page_content='[이전/다음 화면 이동, 지도 네비게이션, 위치 기억, 브라우징 히스토리]'), Document(metadata={'기능명': '이전/다음 화면 이동 컨트롤 (MoveControl)', '설명': '설명', '세부항목': '사용 예시:', 'source': 'test_kjy.md'}, page_content='```javascript\\nvar moveControl = new odf.MoveControl();\\nmoveControl.setMap(map);\\n\\n//이전 위치로 이동\\nmoveControl.back();\\n\\n//다음 위치로 이동\\nmoveControl.forward();\\n```'), Document(metadata={'기능명': '이전/다음 화면 이동 컨트롤 (MoveControl)', '설명': '설명', '세부항목': '주요 메서드:', 'source': 'test_kjy.md'}, page_content='- setMap(map): 컨트롤을 지도에 추가\\n- back(): 이전 위치로 이동\\n- forward(): 다음 위치로 이동'), Document(metadata={'기능명': '이전/다음 화면 이동 컨트롤 (MoveControl)', '설명': '설명', '세부항목': '일반적인 사용 시나리오:', 'source': 'test_kjy.md'}, page_content='1. 여러 위치를 탐색한 후 이전 위치로 빠르게 돌아가기\\n2. 지도 브라우징 히스토리 관리\\n3. 효율적인 지도 탐색 경험 제공'), Document(metadata={'기능명': '마우스 좌표 컨트롤 (MousePositionControl)', '설명': '설명', 'source': 'test_kjy.md'}, page_content='마우스 커서의 현재 위치 좌표를 실시간으로 표시해주는 컨트롤입니다.'), Document(metadata={'기능명': '마우스 좌표 컨트롤 (MousePositionControl)', '설명': '설명', '세부항목': '키워드:', 'source': 'test_kjy.md'}, page_content='[마우스 좌표 표시, 위치 정보, 좌표계, 실시간 위치]'), Document(metadata={'기능명': '마우스 좌표 컨트롤 (MousePositionControl)', '설명': '설명', '세부항목': '사용 예시:', 'source': 'test_kjy.md'}, page_content='```html\\n<div id=\"coordDiv\"></div>\\n```\\n```javascript\\nvar mousePositionControl = new odf.MousePositionControl({\\nelement: document.querySelector(\\'#coordDiv\\'), // 마우스 좌표를 표시할 element 영역\\ncallback: function (position) {\\nconsole.log(position);\\n},\\n});\\nmousePositionControl.setMap(map);\\n```'), Document(metadata={'기능명': '마우스 좌표 컨트롤 (MousePositionControl)', '설명': '설명', '세부항목': '주요 메서드:', 'source': 'test_kjy.md'}, page_content='- setMap(map): 컨트롤을 지도에 추가'), Document(metadata={'기능명': '마우스 좌표 컨트롤 (MousePositionControl)', '설명': '설명', '세부항목': '일반적인 사용 시나리오:', 'source': 'test_kjy.md'}, page_content='1. 정확한 위치 정보 필요 시 (예: GIS 애플리케이션)\\n2. 개발 및 디버깅 과정에서의 위치 확인\\n3. 사용자에게 상세한 위치 정보 제공'), Document(metadata={'기능명': '그리기 도구 컨트롤 (DrawControl)', '설명': '설명', 'source': 'test_kjy.md'}, page_content='지도 위에 다양한 도형이나 마커를 그릴 수 있는 도구를 제공하는 컨트롤입니다.'), Document(metadata={'기능명': '그리기 도구 컨트롤 (DrawControl)', '설명': '설명', '세부항목': '키워드:', 'source': 'test_kjy.md'}, page_content='[그리기 도구, 지도 마킹, 도형 그리기, 사용자 정의 오버레이]'), Document(metadata={'기능명': '그리기 도구 컨트롤 (DrawControl)', '설명': '설명', '세부항목': '사용 예시:', 'source': 'test_kjy.md'}, page_content='```javascript\\nvar drawControl = new odf.DrawControl();\\ndrawControl.setMap(map);\\n\\n```'), Document(metadata={'기능명': '그리기 도구 컨트롤 (DrawControl)', '설명': '설명', '세부항목': '주요 메서드:', 'source': 'test_kjy.md'}, page_content='- setMap(map): 컨트롤을 지도에 추가\\n- setDrawingMode(mode): 그리기 모드 설정 (점, 선, 다각형 등)\\n- getFeatures(): 그려진 객체 목록 가져오기'), Document(metadata={'기능명': '그리기 도구 컨트롤 (DrawControl)', '설명': '설명', '세부항목': '일반적인 사용 시나리오:', 'source': 'test_kjy.md'}, page_content='1. 사용자 정의 영역 표시\\n2. 거리 또는 면적 측정\\n3. 협업을 위한 지도 상 정보 공유'), Document(metadata={'기능명': '레이어 추가 (odf.LayerFactory.produce)', '설명': '설명', 'source': 'test_kjy.md'}, page_content='지도 위에 레이어를 추가할 수 있습니다.'), Document(metadata={'기능명': '레이어 추가 (odf.LayerFactory.produce)', '설명': '설명', '세부항목': '키워드:', 'source': 'test_kjy.md'}, page_content='[레이어 추가, WMS, 지도 레이어, 데이터 시각화]'), Document(metadata={'기능명': '레이어 추가 (odf.LayerFactory.produce)', '설명': '설명', '세부항목': '사용 예시:', 'source': 'test_kjy.md'}, page_content=\"```javascript\\n// WMS 레이어 생성\\nvar wmsLayer = odf.LayerFactory.produce('geoserver', {\\nmethod: 'get',\\nserver: 'https://geoserver.geon.kr/geoserver',\\nlayer: 'geonpaas:L100000254',\\nservice: 'wms',\\ntiled: false,\\n});\\n```\"), Document(metadata={'기능명': '레이어 추가 (odf.LayerFactory.produce)', '설명': '설명', '세부항목': 'layer 정보:', 'source': 'test_kjy.md'}, page_content='- Wmappickadmin:L100002910 : 지하철역\\n- Wh2jung:L100002896 : 버거킹  \\n// 레이어를 지도에 추가\\nwmsLayer.setMap(map);  \\n// 레이어가 한눈에 보이는 extent로 화면 위치 이동 및 줌 레벨 변경\\nwmsLayer.fit();'), Document(metadata={'기능명': '레이어 추가 (odf.LayerFactory.produce)', '설명': '설명', '세부항목': '주요 메서드:', 'source': 'test_kjy.md'}, page_content='- produce(): 레이어 생성\\n- setMap(map): 레이어를 지도에 추가\\n- fit(): 레이어에 맞춰 지도 뷰 조정'), Document(metadata={'기능명': '레이어 추가 (odf.LayerFactory.produce)', '설명': '설명', '세부항목': '일반적인 사용 시나리오:', 'source': 'test_kjy.md'}, page_content='1. 외부 데이터 소스의 지리 정보 표시\\n2. 주제별 레이어 추가 (예: 인구 밀도, 기상 정보)\\n3. 동적 데이터 시각화'), Document(metadata={'기능명': '스와이퍼 컨트롤 (SwiperControl)', '설명': '설명', 'source': 'test_kjy.md'}, page_content='기본 지도를 생성한 후 스와이퍼 컨트롤(SwiperControl)을 추가합니다. 이 컨트롤은 두 개의 지도 레이어를 나란히 비교할 수 있게 해줍니다.'), Document(metadata={'기능명': '스와이퍼 컨트롤 (SwiperControl)', '설명': '설명', '세부항목': '키워드', 'source': 'test_kjy.md'}, page_content='[스와이퍼, 지도 비교, 레이어 비교, swiperControl]'), Document(metadata={'기능명': '스와이퍼 컨트롤 (SwiperControl)', '설명': '설명', '세부항목': '사용 예시', 'source': 'test_kjy.md'}, page_content='```javascript\\n// 배경지도 컨트롤에서 바로e맵 항공지도 레이어 조회, basemapControl이 위에 선언되어 있어야 합니다.\\nvar rightLayer = basemapControl.getBaseLayer(basemapKey);\\n//스와이퍼 컨트롤 생성(원본 레이어를 왼쪽 영역에 표출, 정의한 레이어는 오른쪽 영역에 표출)\\nvar swiperControl  = new odf.SwiperControl({\\nlayers : [rightLayer]\\n});\\n//생성한 스와이퍼 컨트롤을 지도 객체에 추가\\nswiperControl.setMap(map);trol.setMap(map);\\n```'), Document(metadata={'기능명': '스와이퍼 컨트롤 (SwiperControl)', '설명': '설명', '세부항목': '주요 메서드', 'source': 'test_kjy.md'}, page_content='- setMap(map): 컨트롤을 지도에 추가'), Document(metadata={'기능명': '스와이퍼 컨트롤 (SwiperControl)', '설명': '설명', '세부항목': '일반적인 사용 시나리오', 'source': 'test_kjy.md'}, page_content='1. 서로 다른 시기의 위성 이미지 비교\\n2. 일반 지도와 테마 지도 비교\\n3. 서로 다른 데이터 레이어 비교'), Document(metadata={'기능명': '분할지도 컨트롤 (DivideMapControl)', '설명': '설명', 'source': 'test_kjy.md'}, page_content='기본 지도를 생성한 후 분할지도 컨트롤(DivideMapControl)을 추가합니다. 이 컨트롤은 화면을 여러 개의 지도 영역으로 나누어 다양한 배경지도를 동시에 표시할 수 있게 해줍니다.'), Document(metadata={'기능명': '분할지도 컨트롤 (DivideMapControl)', '설명': '설명', '세부항목': '키워드', 'source': 'test_kjy.md'}, page_content='[분할지도, 멀티뷰, 다중 지도, divideMapControl]'), Document(metadata={'기능명': '분할지도 컨트롤 (DivideMapControl)', '설명': '설명', '세부항목': '사용 예시', 'source': 'test_kjy.md'}, page_content=\"```javascript\\n//분할지도 컨트롤 생성\\nvar divideMapControl = new odf.DivideMapControl({\\n//2분할지도 정의\\ndualMap : [\\n{\\nmapOption : {\\nbasemap : {baroEMap : ['eMapAIR']},\\n},\\n},\\n],\\n//3분할지도 정의\\nthreepleMap: [\\n{mapOption: {\\nbasemap: {baroEMap:['eMapColor']},\\n},\\n},\\n{ mapOption: {\\nbasemap:{baroEMap: ['eMapWhite']},\\n},\\n},\\n],\\n//4분할지도 정의\\nquadMap: [\\n{\\nmapOption: {\\nbasemap: {baroEMap : ['eMapAIR']},\\n},\\n},\\n{\\nmapOption: {\\nbasemap: {baroEMap : ['eMapColor']} ,\\n},\\n},\\n{\\nmapOption: {\\nbasemap: {baroEMap : ['eMapWhite']} ,\\n},\\n},\\n],\\n});\\n//생성한 분할지도 컨트롤을 지도 객체에 추가\\ndivideMapControl.setMap(map);\\n```\"), Document(metadata={'기능명': '분할지도 컨트롤 (DivideMapControl)', '설명': '설명', '세부항목': '주요 메서드', 'source': 'test_kjy.md'}, page_content='- setMap(map): 컨트롤을 지도에 추가'), Document(metadata={'기능명': '분할지도 컨트롤 (DivideMapControl)', '설명': '설명', '세부항목': '일반적인 사용 시나리오', 'source': 'test_kjy.md'}, page_content='1. 여러 지역의 동시 비교\\n2. 다양한 테마 지도의 동시 표시\\n3. 시계열 데이터의 동시 표현')]\n"]}], "source": ["file_path = './train_data/test_kjy.md'\n", "    \n", "# 헤더 구조 정의\n", "headers_to_split_on = [\n", "    (\"#\", \"기능명\"),\n", "    (\"##\", \"설명\"),\n", "    (\"###\", \"세부항목\")\n", "]\n", "markdown_splitter = MarkdownHeaderTextSplitter(\n", "    headers_to_split_on=headers_to_split_on\n", ")\n", "\n", "with open(file_path, \"r\", encoding='utf-8') as f:\n", "    markdown_content = f.read()\n", "\n", "split_docs = markdown_splitter.split_text(markdown_content)\n", "split_docs\n", "\n", "# 메타데이터 추가 및 문서 구조화\n", "docs = []\n", "for doc in split_docs:\n", "    metadata = doc.metadata.copy()\n", "    metadata['source'] = file_path.split('/')[-1]\n", "\n", "    # 키워드 추출 (있는 경우)\n", "    if '키워드:' in doc.page_content:\n", "        keywords_start = doc.page_content.index('키워드:')\n", "        keywords_end = doc.page_content.index('\\n', keywords_start)\n", "        keywords = doc.page_content[keywords_start:keywords_end].replace('키워드:', '').strip()\n", "        metadata['keywords'] = keywords\n", "\n", "    # 사용 예시 추출 (있는 경우)\n", "    if '사용 예시:' in doc.page_content:\n", "        example_start = doc.page_content.index('사용 예시:')\n", "        example_end = doc.page_content.index('```', example_start + 1)\n", "        example = doc.page_content[example_start:example_end].replace('사용 예시:', '').strip()\n", "        metadata['example'] = example\n", "\n", "    docs.append(Document(page_content=doc.page_content, metadata=metadata))\n", "\n", "print(docs)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["#bm25는 반환값을 list로 가지기에 아래 함수도 list 반환값 형식으로\n", "def kiwi_tokenize(text):\n", "    return [token.form for token in kiwi.tokenize(text)]\n", "\n", "bm25 = BM25Retriever.from_documents(docs)\n", "kiwi_bm25 = BM25Retriever.from_documents(docs, preprocess_func=kiwi_tokenize)\n", "faiss = FAISS.from_documents(docs, OpenAIEmbeddings()).as_retriever()\n", "bm25_faiss_73 = EnsembleRetriever(\n", "    retrievers=[bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.7, 0.3],  # 각 검색 모델의 결과에 적용할 가중치\n", "    search_type=\"mmr\",  # 검색 결과의 다양성을 증진시키는 MMR 방식을 사용\n", ")\n", "bm25_faiss_37 = EnsembleRetriever(\n", "    retrievers=[bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.7, 0.3],  # 각 검색 모델의 결과에 적용할 가중치\n", "    search_type=\"mmr\",  # 검색 결과의 다양성을 증진시키는 MMR 방식을 사용\n", ")\n", "kiwibm25_faiss_73 = EnsembleRetriever(\n", "    retrievers=[kiwi_bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.7, 0.3],  # 각 검색 모델의 결과에 적용할 가중치\n", "    search_type=\"mmr\",  # 검색 결과의 다양성을 증진시키는 MMR 방식을 사용\n", ")\n", "kiwibm25_faiss_37 = EnsembleRetriever(\n", "    retrievers=[kiwi_bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.3, 0.7],  # 각 검색 모델의 결과에 적용할 가중치\n", "    search_type=\"mmr\",  # 검색 결과의 다양성을 증진시키는 MMR 방식을 사용\n", ")\n", "kiwibm25_faiss_37_fetch = EnsembleRetriever(\n", "    retrievers=[kiwi_bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.3, 0.7],  # 각 검색 모델의 결과에 적용할 가중치\n", "    search_type=\"mmr\",\n", "    search_kwargd={\"k\":5, \"fetch_k\":50}\n", "    )\n", "\n", "kiwibm25_faiss_73_fetch = EnsembleRetriever(\n", "    retrievers=[kiwi_bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.7, 0.3],  # 각 검색 모델의 결과에 적용할 가중치\n", "    search_type=\"mmr\",\n", "    search_kwargd={\"k\":5, \"fetch_k\":50}\n", "    )\n", "\n", "kiwibm25_faiss_64_fetch = EnsembleRetriever(\n", "    retrievers=[kiwi_bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.6, 0.4],  # 각 검색 모델의 결과에 적용할 가중치\n", "    search_type=\"mmr\",\n", "    search_kwargd={\"k\":5, \"fetch_k\":50}\n", "    )\n", "\n", "kiwibm25_faiss_46_fetch = EnsembleRetriever(\n", "    retrievers=[kiwi_bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.4, 0.6],  # 각 검색 모델의 결과에 적용할 가중치\n", "    search_type=\"mmr\",\n", "    search_kwargd={\"k\":5, \"fetch_k\":50}\n", "    )\n", "\n", "kiwibm25_faiss_37_simil = EnsembleRetriever(\n", "    retrievers=[kiwi_bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.3, 0.7],  # 각 검색 모델의 결과에 적용할 가중치\n", "    search_type=\"similarity_score_threshold\",\n", "    search_kwargs={'score_threshold': 0.4}\n", "    )\n", "\n", "kiwibm25_faiss_73_simil = EnsembleRetriever(\n", "    retrievers=[kiwi_bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.7, 0.3],\n", "    search_type=\"similarity_score_threshold\",\n", "    search_kwargs={'score_threshold': 0.4}\n", "    )\n", "\n", "kiwibm25_faiss_64_simil = EnsembleRetriever(\n", "    retrievers=[kiwi_bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.6, 0.4],  # 각 검색 모델의 결과에 적용할 가중치\n", "    search_type=\"similarity_score_threshold\",\n", "    search_kwargs={'score_threshold': 0.4}\n", "    )\n", "\n", "kiwibm25_faiss_46_simil = EnsembleRetriever(\n", "    retrievers=[kiwi_bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.4, 0.6],  # 각 검색 모델의 결과에 적용할 가중치\n", "    search_type=\"similarity_score_threshold\",\n", "    search_kwargs={'score_threshold': 0.4}\n", "    )\n", "\n", "retrievers = {\n", "    \"bm25\": bm25,\n", "    \"kiwi_bm25\": kiwi_bm25,\n", "    \"faiss\": faiss,\n", "    \"bm25_faiss_73\": bm25_faiss_73,\n", "    \"bm25_faiss_37\": bm25_faiss_37,\n", "    \"kiwi_bm25_faiss_73\": kiwibm25_faiss_73,\n", "    \"kiwi_bm25_faiss_37\": kiwibm25_faiss_37,\n", "    \"kiwibm25_faiss_37_fetch\" : kiwibm25_faiss_37_fetch,\n", "    \"kiwibm25_faiss_73_fetch\" : kiwibm25_faiss_73_fetch,\n", "    \"kiwibm25_faiss_64_fetch\" : kiwibm25_faiss_64_fetch,\n", "    \"kiwibm25_faiss_46_fetch\" : kiwibm25_faiss_46_fetch,\n", "    \"kiwibm25_faiss_37_simil\" : kiwibm25_faiss_37_simil,\n", "    \"kiwibm25_faiss_73_simil\" : kiwibm25_faiss_73_simil,\n", "    \"kiwibm25_faiss_64_simil\" : kiwibm25_faiss_64_simil,\n", "    \"kiwibm25_faiss_46_simil\" : kiwibm25_faiss_46_simil, \n", "}"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["query = \"\"\"측정도구, 그리기, 배경지도, 분할지도, 스와이퍼 도구를 추가한 지도를 생성해줘\n", "\n", "            그리기도구는 선 색상을 랜덤으로 해줘\n", "\n", "            측정도구에는 거리와 면적을 측정할 수 있게 해줘\"\"\" "]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def print_search_results(retrievers, query):\n", "    print(f\"Query: {query}\")\n", "    for name, retriever in retrievers.items():\n", "        li_result = retriever.invoke(query)\n", "        print(name)\n", "        for i in li_result:\n", "            print(f\"\\n {i.metadata['기능명']}\")\n", "        print(\"===\" * 20)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: 측정도구, 그리기, 배경지도, 분할지도, 스와이퍼 도구를 추가한 지도를 생성해줘\n", "\n", "            그리기도구는 선 색상을 랜덤으로 해줘\n", "\n", "            측정도구에는 거리와 면적을 측정할 수 있게 해줘\n", "bm25\n", "\n", " 스와이퍼 컨트롤 (SwiperControl)\n", "\n", " 분할지도 컨트롤 (DivideMapControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 줌 컨트롤 (ZoomControl)\n", "============================================================\n", "kiwi_bm25\n", "\n", " 분할지도 컨트롤 (DivideMapControl)\n", "\n", " 스와이퍼 컨트롤 (SwiperControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 마우스 좌표 컨트롤 (MousePositionControl)\n", "============================================================\n", "faiss\n", "\n", " 축척 컨트롤 (ScaleControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 분할지도 컨트롤 (DivideMapControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "============================================================\n", "bm25_faiss_73\n", "\n", " 분할지도 컨트롤 (DivideMapControl)\n", "\n", " 스와이퍼 컨트롤 (SwiperControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 줌 컨트롤 (ZoomControl)\n", "\n", " 축척 컨트롤 (ScaleControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "============================================================\n", "bm25_faiss_37\n", "\n", " 분할지도 컨트롤 (DivideMapControl)\n", "\n", " 스와이퍼 컨트롤 (SwiperControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 줌 컨트롤 (ZoomControl)\n", "\n", " 축척 컨트롤 (ScaleControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "============================================================\n", "kiwi_bm25_faiss_73\n", "\n", " 분할지도 컨트롤 (DivideMapControl)\n", "\n", " 스와이퍼 컨트롤 (SwiperControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 마우스 좌표 컨트롤 (MousePositionControl)\n", "\n", " 축척 컨트롤 (ScaleControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "============================================================\n", "kiwi_bm25_faiss_37\n", "\n", " 분할지도 컨트롤 (DivideMapControl)\n", "\n", " 축척 컨트롤 (ScaleControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 스와이퍼 컨트롤 (SwiperControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 마우스 좌표 컨트롤 (MousePositionControl)\n", "============================================================\n", "kiwibm25_faiss_37_fetch\n", "\n", " 분할지도 컨트롤 (DivideMapControl)\n", "\n", " 축척 컨트롤 (ScaleControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 스와이퍼 컨트롤 (SwiperControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 마우스 좌표 컨트롤 (MousePositionControl)\n", "============================================================\n", "kiwibm25_faiss_73_fetch\n", "\n", " 분할지도 컨트롤 (DivideMapControl)\n", "\n", " 스와이퍼 컨트롤 (SwiperControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 마우스 좌표 컨트롤 (MousePositionControl)\n", "\n", " 축척 컨트롤 (ScaleControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "============================================================\n", "kiwibm25_faiss_64_fetch\n", "\n", " 분할지도 컨트롤 (DivideMapControl)\n", "\n", " 스와이퍼 컨트롤 (SwiperControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 마우스 좌표 컨트롤 (MousePositionControl)\n", "\n", " 축척 컨트롤 (ScaleControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "============================================================\n", "kiwibm25_faiss_46_fetch\n", "\n", " 분할지도 컨트롤 (DivideMapControl)\n", "\n", " 축척 컨트롤 (ScaleControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 스와이퍼 컨트롤 (SwiperControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 마우스 좌표 컨트롤 (MousePositionControl)\n", "============================================================\n", "kiwibm25_faiss_37_simil\n", "\n", " 분할지도 컨트롤 (DivideMapControl)\n", "\n", " 축척 컨트롤 (ScaleControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 스와이퍼 컨트롤 (SwiperControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 마우스 좌표 컨트롤 (MousePositionControl)\n", "============================================================\n", "kiwibm25_faiss_73_simil\n", "\n", " 분할지도 컨트롤 (DivideMapControl)\n", "\n", " 스와이퍼 컨트롤 (SwiperControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 마우스 좌표 컨트롤 (MousePositionControl)\n", "\n", " 축척 컨트롤 (ScaleControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "============================================================\n", "kiwibm25_faiss_64_simil\n", "\n", " 분할지도 컨트롤 (DivideMapControl)\n", "\n", " 스와이퍼 컨트롤 (SwiperControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 마우스 좌표 컨트롤 (MousePositionControl)\n", "\n", " 축척 컨트롤 (ScaleControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "============================================================\n", "kiwibm25_faiss_46_simil\n", "\n", " 분할지도 컨트롤 (DivideMapControl)\n", "\n", " 축척 컨트롤 (ScaleControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 스와이퍼 컨트롤 (SwiperControl)\n", "\n", " 그리기 도구 컨트롤 (DrawControl)\n", "\n", " 마우스 좌표 컨트롤 (MousePositionControl)\n", "============================================================\n"]}], "source": ["print_search_results(retrievers,query)"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['분할지도 컨트롤 (DivideMapControl)', '축척 컨트롤 (ScaleControl)', '그리기 도구 컨트롤 (DrawControl)', '스와이퍼 컨트롤 (SwiperControl)', '마우스 좌표 컨트롤 (MousePositionControl)']\n"]}], "source": ["query = \"\"\"\n", "측정도구, 그리기, 배경지도, 분할지도, 스와이퍼 도구를 추가한 지도를 생성해줘\n", "\n", "            그리기도구는 선 색상을 랜덤으로 해줘\n", "\n", "            측정도구에는 거리와 면적을 측정할 수 있게 해줘\n", "\n", "\"\"\"\n", "li_doc =  kiwibm25_faiss_46_simil.invoke(query)\n", "unique_list = []\n", "for item in li_doc:\n", "    if item not in unique_list:\n", "        unique_list.append(item)\n", "\n", "unique_documents = []\n", "seen_ids = set()\n", "\n", "for doc in li_doc:\n", "    if doc.metadata['기능명'] not in seen_ids:\n", "        unique_documents.append(doc)\n", "        seen_ids.add(doc.metadata['기능명'])\n", "\n", "print([doc.metadata['기능명']for doc in unique_documents])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "vectordb = FAISS.from_documents(code_splits, OpenAIEmbeddings(model='text-embedding-3-large'))\n", "retriever = vectordb.as_retriever(\n", "    search_type=\"similarity_score_threshold\",\n", "    search_kwargs={'score_threshold': 0.2}\n", ")\n", "\n", "retriever.get_relevant_documents(\"\"\"측정도구, 그리기, 배경지도, 분할지도, 스와이퍼 도구를 추가한 지도를 생성해줘\n", "\n", "            그리기도구는 선 색상을 랜덤으로 해줘\n", "\n", "            측정도구에는 거리와 면적을 측정할 수 있게 해줘\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.vectorstores import FAISS\n", "\n", "vectordb = FAISS.from_documents(code_splits, OpenAIEmbeddings(model='text-embedding-3-large'))\n", "retriever = vectordb.as_retriever(\n", "    search_type=\"mmr\",\n", "    search_kwargd={\"k\":5, \"fetch_k\":50}\n", ")\n", "\n", "retriever.get_relevant_documents(\"\"\"측정도구, 그리기, 배경지도, 분할지도, 스와이퍼 도구를 추가한 지도를 생성해줘\n", "\n", "            그리기도구는 선 색상을 랜덤으로 해줘\n", "\n", "            측정도구에는 거리와 면적을 측정할 수 있게 해줘\n", "\"\"\")"]}], "metadata": {"kernelspec": {"display_name": "mppckbt", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}