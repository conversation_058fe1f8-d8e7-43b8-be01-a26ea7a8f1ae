import { Button } from "@/components/ui/button"
import { Hover<PERSON><PERSON>, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { MapIcon, ChevronDown, Globe2, Mountain, Image, Paintbrush, LucideIcon } from "lucide-react"
import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
// import { Map, MapView } from "@/types/map"; // Replaced by @geon-map/odf types
import { UseMapReturn } from "@geon-map/odf";
import { useBasemap } from "@/providers/basemap-provider";

interface BasemapType {
  id: string;
  name: string;
  description: string;
  icon: LucideIcon;  // Changed to LucideIcon type
  color: string;
}

type BasemapId = 'eMapBasic' | 'eMapAIR' | 'eMapColor' | 'eMapWhite';

type BasemapsType = {
  [key in BasemapId]: BasemapType;
}

const BASE_MAPS: BasemapsType = {
  eMapBasic: {
    id: "eMapBasic",
    name: "일반지도",
    description: "도로, 건물, 지형지물이 포함된 기본 지도입니다.",
    icon: Globe2,
    color: "#4C6EF5"
  },
  eMapAIR: {
    id: "eMapAIR",
    name: "항공지도",
    description: "위성에서 촬영한 실제 지형을 보여주는 항공사진입니다.",
    icon: Image,
    color: "#40C057"
  },
  eMapColor: {
    id: "eMapColor",
    name: "색각지도",
    description: "색상 대비를 강조한 특수 목적 지도입니다.",
    icon: Paintbrush,
    color: "#FA5252"
  },
  eMapWhite: {
    id: "eMapWhite",
    name: "백지도",
    description: "깔끔한 흰색 배경의 심플한 지도입니다.",
    icon: Mountain,
    color: "#845EF7"
  }
}

interface BasemapButtonProps {
  mapState: UseMapReturn;
}

export function BasemapButton({ mapState }: BasemapButtonProps) {
  const [isHovered, setIsHovered] = useState(false);
  const { currentBasemap: currentBasemapId, changeBasemap } = useBasemap();

  const currentBasemap = BASE_MAPS[currentBasemapId] || BASE_MAPS.eMapBasic;

  const handleBasemapChange = (mapKey: BasemapId) => {
    changeBasemap(mapKey);
  };

  return (
    <HoverCard openDelay={100}>
      <HoverCardTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-9 flex items-center justify-between w-full bg-background/80 hover:bg-background/90 backdrop-blur-md shadow-sm border transition-all duration-300"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <motion.div
            initial={{ scale: 1 }}
            className="flex items-center gap-2"
          >
            <MapIcon className="h-4 w-4" style={{ color: currentBasemap.color }} />
            <span className="text-sm font-medium">{currentBasemap.name}</span>
          </motion.div>
          <motion.div
            animate={{ rotate: isHovered ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="h-4 w-4 opacity-40" />
          </motion.div>
        </Button>
      </HoverCardTrigger>
      <HoverCardContent 
        className="w-auto p-0.5 bg-background/90 backdrop-blur-md border shadow-lg rounded-xl" 
        align="end"
        side="left"
        alignOffset={-40}
        sideOffset={8}
      >
        <ScrollArea className="h-auto max-h-[280px]">
          <div className="space-y-0.5 p-1">
            {(Object.entries(BASE_MAPS) as [BasemapId, BasemapType][]).map(([key, basemapItem]) => { // Renamed 'map' to 'basemapItem'
              const Icon = basemapItem.icon;
              const isSelected = currentBasemapId === key; // Use currentBasemapId defined above
              
              return (
                <motion.div
                  key={key}
                  initial={false}
                  whileHover={{ scale: 0.98 }}
                  transition={{ duration: 0.2 }}
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`w-full py-3 justify-start text-sm relative group transition-all duration-200
                      ${isSelected ? 'bg-muted' : 'hover:bg-muted/50'}`}
                    onClick={() => handleBasemapChange(key)}
                  >
                    <AnimatePresence>
                      {isSelected && (
                        <motion.div
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -10 }}
                          className="absolute left-0 top-0 w-1 h-full rounded-full"
                          style={{ backgroundColor: basemapItem.color }}
                        />
                      )}
                    </AnimatePresence>
                    
                    <div className="flex items-center gap-3">
                      <div className={`p-1.5 rounded-lg transition-colors duration-200 
                        ${isSelected ? 'bg-muted-foreground/10' : 'bg-muted'}`}
                      >
                        <Icon 
                          className="h-4 w-4"
                          style={{ color: basemapItem.color }}
                        />
                      </div>
                      <div className="flex flex-col items-start gap-0.5">
                        <span className="font-medium">{basemapItem.name}</span>
                        <span className="text-[11px] text-muted-foreground leading-tight">{basemapItem.description}</span>
                      </div>
                    </div>
                  </Button>
                </motion.div>
              );
            })}
          </div>
        </ScrollArea>
      </HoverCardContent>
    </HoverCard>
  );
}