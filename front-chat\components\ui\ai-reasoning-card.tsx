"use client";

import React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";
import { MagicCard } from "@/components/magicui/magic-card";
import { BrainI<PERSON>, <PERSON>rkles } from "lucide-react";

interface AIReasoningCardProps {
  children: React.ReactNode;
  isEnabled: boolean;
  isThinking?: boolean;
  isDisabled?: boolean;
  disabledReason?: string;
  className?: string;
}

export function AIReasoningCard({
  children,
  isEnabled,
  isThinking = false,
  isDisabled = false,
  disabledReason,
  className,
}: AIReasoningCardProps) {

  const content = (
    <motion.div
      className="space-y-4 w-72"
      initial={{ opacity: 0, y: 8 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1, duration: 0.3, ease: "easeOut" }}
    >
      {/* Header with enhanced brain icon */}
      <div className="flex items-center gap-3">
        <motion.div
          className={cn(
            "relative p-2.5 rounded-xl transition-all duration-300",
            isDisabled
              ? "bg-gray-50 text-gray-300 border border-gray-200 opacity-50"
              : isEnabled
                ? "bg-gradient-to-br from-blue-50 to-indigo-50 text-blue-600 shadow-sm border border-blue-100"
                : "bg-gray-50 text-gray-400 border border-gray-200"
          )}
          animate={isEnabled && isThinking && !isDisabled ? {
            scale: [1, 1.05, 1],
          } : {}}
          transition={{
            duration: 2,
            repeat: isEnabled && isThinking && !isDisabled ? Infinity : 0,
            ease: "easeInOut",
          }}
        >
          <BrainIcon className="w-5 h-5" />
          <motion.div
            className="absolute -top-1 -right-1"
            animate={{
              scale: [0, 1, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <Sparkles className="w-3 h-3 text-blue-500" />
          </motion.div>
        </motion.div>

        <div className="flex-1">
          <h4 className={cn(
            "text-sm font-semibold transition-colors",
            isDisabled
              ? "text-gray-400"
              : isEnabled
                ? "text-gray-900"
                : "text-gray-500"
          )}>
            AI 추론 {isDisabled ? "지원 안됨" : isEnabled ? "활성화" : "비활성화"}
          </h4>
        </div>
      </div>

      {/* Feature highlights */}
      <div className="space-y-2">
        {isDisabled ? (
          <>
            {disabledReason && (
              <div className="flex items-center gap-2 text-xs">
                <div className="w-1.5 h-1.5 rounded-full bg-gray-300" />
                <span className="text-gray-400">
                  {disabledReason}
                </span>
              </div>
            )}
          </>
        ) : (
          <>
            <div className="flex items-center gap-2 text-xs">
              <div className={cn(
                "w-1.5 h-1.5 rounded-full",
                isEnabled ? "bg-blue-500" : "bg-gray-300"
              )} />
              <span className={cn(
                "transition-colors",
                isEnabled ? "text-gray-700" : "text-gray-400"
              )}>
                추론을 통해 좀 더 정확한 답변을 제공
              </span>
            </div>
            <div className="flex items-center gap-2 text-xs">
              <div className={cn(
                "w-1.5 h-1.5 rounded-full",
                isEnabled ? "bg-blue-500" : "bg-gray-300"
              )} />
              <span className={cn(
                "transition-colors",
                isEnabled ? "text-gray-700" : "text-gray-400"
              )}>
                시간이 좀 더 소요될 수 있습니다.
              </span>
            </div>
          </>
        )}
      </div>
    </motion.div>
  );

  return (
    <HoverCard openDelay={150} closeDelay={200}>
      <HoverCardTrigger asChild>
        {children}
      </HoverCardTrigger>
      <HoverCardContent
        side="top"
        align="center"
        className="p-0 border-0 bg-transparent shadow-none z-[100]"
        sideOffset={12}
        alignOffset={-50}
        avoidCollisions={true}
        collisionBoundary={typeof document !== 'undefined' ? document.body : undefined}
      >

        <MagicCard
          className={cn(
            "border-0 shadow-xl backdrop-blur-md p-4 overflow-hidden",
            isEnabled
              ? "bg-gradient-to-br from-white/95 to-blue-50/95"
              : "bg-white/95"
          )}
          gradientSize={120}
          gradientColor={isEnabled ? "#3b82f6" : "#6b7280"}
          gradientOpacity={isEnabled ? 0.15 : 0.08}
          gradientFrom={isEnabled ? "#3b82f6" : "#6b7280"}
          gradientTo={isEnabled ? "#6366f1" : "#9ca3af"}
        >
          {content}
        </MagicCard>
      </HoverCardContent>
    </HoverCard>
  );
}
