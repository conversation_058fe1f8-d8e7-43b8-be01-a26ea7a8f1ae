{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ollama 환경과 ChatOpenAI 의 호환성 테스트\n", "\n", "이 주피터 노트는 심플하게 ChatOpenAI 와 로컬 ollama 의 테스트를 하기 위한 것입니다.\n", "필요하시면 테스트해보시기 바랍니다."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv('../backend/.env')\n", "\n", "model = ChatOpenAI(\n", "    base_url = \"http://121.163.19.104:11434/v1\", # 반드시 끝에 v1 붙이세요!\n", "    model=\"llama3:8b\", \n", "    temperature=0.1, \n", "    streaming=True, \n", "    api_key=\"ollama\",\n", ")\n", "\n", "prompt = ChatPromptTemplate.from_messages(messages=[\n", "    (\"system\", \"You are very good at American culture knowledge AI Chatbot.\"),\n", "    (\"human\", \"What is Most Famous Thing in korean\")\n", "])\n", "\n", "chain = prompt | model | parser # 참고로 parser 를 설정 안하면 langchain_core.messages.ai.AIMessageChunk 타입의 결과가 반환된다.\n", "                                # 생각보다 유용한 정보가 많아서 도움이 되기도 하지만, 약간 장황하다."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for chunk in chain.stream({}):\n", "    print(chunk, end=\"\", flush=True)# block the main thread! use async!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# async for chunk in chain.astream({\"country\":\"korea\", \"topic\": \"sports\"}): # won't use main thread resource! but on event loop\n", "async for chunk in chain.astream({\"question\": \"what is most popular singer in the world?\"}): # won't use main thread resource! but on event loop\n", "    print(type(chunk))\n", "    print(chunk, end=\"\", flush=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async for event in chain.astream_events({\"country\":\"korea\", \"topic\": \"cuisine\"}, version=\"v2\"): # won't use main thread resource!    \n", "    print(event, flush=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["events = []\n", "async for event in chain.astream_events({\"country\":\"korea\", \"topic\": \"cuisine\"}, version=\"v2\"): # won't use main thread resource!\n", "    events.append(event)\n", "event_types = {event[\"event\"] for event in events}\n", "print(\"Unique event types:\", event_types)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}