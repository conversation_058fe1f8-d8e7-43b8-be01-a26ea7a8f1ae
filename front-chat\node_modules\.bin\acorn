#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/chatbot/front-chat/node_modules/.pnpm/acorn@8.14.0/node_modules/acorn/bin/node_modules:/mnt/c/chatbot/front-chat/node_modules/.pnpm/acorn@8.14.0/node_modules/acorn/node_modules:/mnt/c/chatbot/front-chat/node_modules/.pnpm/acorn@8.14.0/node_modules:/mnt/c/chatbot/front-chat/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/chatbot/front-chat/node_modules/.pnpm/acorn@8.14.0/node_modules/acorn/bin/node_modules:/mnt/c/chatbot/front-chat/node_modules/.pnpm/acorn@8.14.0/node_modules/acorn/node_modules:/mnt/c/chatbot/front-chat/node_modules/.pnpm/acorn@8.14.0/node_modules:/mnt/c/chatbot/front-chat/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/acorn@8.14.0/node_modules/acorn/bin/acorn" "$@"
else
  exec node  "$basedir/../.pnpm/acorn@8.14.0/node_modules/acorn/bin/acorn" "$@"
fi
