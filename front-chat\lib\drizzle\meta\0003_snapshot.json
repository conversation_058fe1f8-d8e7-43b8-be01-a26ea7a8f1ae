{"id": "32eba228-b08c-4713-991d-6c7f8335351d", "prevId": "c07583e1-3f5c-4fd8-8b54-bd284fab0ad3", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "providerAccountId": {"name": "providerAccountId", "type": "text", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token_expires_in": {"name": "refresh_token_expires_in", "type": "integer", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false}, "oauth_token_secret": {"name": "oauth_token_secret", "type": "text", "primaryKey": false, "notNull": false}, "oauth_token": {"name": "oauth_token", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"accounts_userId_index": {"name": "accounts_userId_index", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"accounts_userId_users_id_fk": {"name": "accounts_userId_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"accounts_provider_providerAccountId_pk": {"name": "accounts_provider_providerAccountId_pk", "columns": ["provider", "providerAccountId"]}}, "uniqueConstraints": {}}, "public.chat": {"name": "chat", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "messages": {"name": "messages", "type": "json", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.chat_map": {"name": "chat_map", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "chatId": {"name": "chatId", "type": "text", "primaryKey": false, "notNull": true}, "mapId": {"name": "mapId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_map_chatId_chat_id_fk": {"name": "chat_map_chatId_chat_id_fk", "tableFrom": "chat_map", "tableTo": "chat", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "chat_map_mapId_map_id_fk": {"name": "chat_map_mapId_map_id_fk", "tableFrom": "chat_map", "tableTo": "map", "columnsFrom": ["mapId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.map": {"name": "map", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "isPublic": {"name": "isPublic", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "layers": {"name": "layers", "type": "json", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.map_access": {"name": "map_access", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "mapId": {"name": "mapId", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "accessType": {"name": "accessType", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"map_access_mapId_map_id_fk": {"name": "map_access_mapId_map_id_fk", "tableFrom": "map_access", "tableTo": "map", "columnsFrom": ["mapId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.map_session": {"name": "map_session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "mapId": {"name": "mapId", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "lastActiveAt": {"name": "lastActiveAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "syncView": {"name": "s<PERSON><PERSON><PERSON><PERSON>", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "followingUserId": {"name": "followingUserId", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"map_session_mapId_map_id_fk": {"name": "map_session_mapId_map_id_fk", "tableFrom": "map_session", "tableTo": "map", "columnsFrom": ["mapId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.map_view": {"name": "map_view", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "mapId": {"name": "mapId", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "center": {"name": "center", "type": "json", "primaryKey": false, "notNull": true}, "zoom": {"name": "zoom", "type": "integer", "primaryKey": false, "notNull": true}, "basemap": {"name": "basemap", "type": "text", "primaryKey": false, "notNull": true}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"map_view_mapId_map_id_fk": {"name": "map_view_mapId_map_id_fk", "tableFrom": "map_view", "tableTo": "map", "columnsFrom": ["mapId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"sessionToken": {"name": "sessionToken", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"sessions_userId_index": {"name": "sessions_userId_index", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"sessions_userId_users_id_fk": {"name": "sessions_userId_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "gh_username": {"name": "gh_username", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "emailVerified": {"name": "emailVerified", "type": "timestamp", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}}, "public.verificationTokens": {"name": "verificationTokens", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verificationTokens_identifier_token_pk": {"name": "verificationTokens_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {"verificationTokens_token_unique": {"name": "verificationTokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}}}, "enums": {}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}