(()=>{var e={};e.id=379,e.ids=[379],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28999:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var o={};r.r(o),r.d(o,{POST:()=>u});var a=r(81160),s=r(18765),n=r(46332),i=r(28481);async function u(e){try{let{userId:t,lyrId:r,namespace:o,cntntsId:a,pageSize:s=50,pageIndex:n=1}=await e.json(),u=process.env.GEON_API_KEY,p=process.env.GEON_API_BASE_URL;if(!u||!p)return i.NextResponse.json({error:"API 키 또는 기본 URL이 설정되지 않았습니다."},{status:500});let d=await fetch(`${p}/builder/layer/column/select?crtfckey=${u}&lyrId=${r}&userId=${t}`,{method:"GET",headers:{"Content-Type":"application/json",crtfckey:u}});if(!d.ok)throw Error(`컬럼 정보 조회 실패: ${d.status} ${d.statusText}`);let c=await d.json(),l=await fetch(`${p}/builder/layer/attributes/select?crtfckey=${u}`,{method:"POST",headers:{"Content-Type":"application/json",crtfckey:u},body:JSON.stringify({typeName:`${o}:${a}`,pageIndex:n,pageSize:s})});if(!l.ok)throw Error(`속성 데이터 조회 실패: ${l.status} ${l.statusText}`);let m=await l.json();if(!c.code||200!==c.code||!m.code||200!==m.code)return i.NextResponse.json({error:"레이어 정보를 찾을 수 없습니다."},{status:404});let f={columns:c.result.map(e=>({name:e.columnNm,description:e.columnNcm,type:e.dataTy,editable:"Y"===e.editPosblAt,required:"Y"===e.esntlAt,minValue:e.mummLt,maxValue:e.mxmmLt,groupCode:e.cmmnGroupCode,groupName:e.cmmnGroupCodeNm,order:e.columnOrdr,indicator:"Y"===e.indictAt})),data:m.result.features.map(e=>e.properties),totalCount:m.result.pageInfo.totalCount,pageInfo:m.result.pageInfo,pkColumnName:m.result.pkColumnName};return i.NextResponse.json(f)}catch(e){return console.error("Error fetching layer detailed information:",e),i.NextResponse.json({error:`레이어 상세 정보 조회 중 오류가 발생했습니다: ${e.message}`},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/(map)/api/layer/detailed-info/route",pathname:"/api/layer/detailed-info",filename:"route",bundlePath:"app/(map)/api/layer/detailed-info/route"},resolvedPagePath:"C:\\chatbot\\front-chat\\app\\(map)\\api\\layer\\detailed-info\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:l}=p;function m(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},37758:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77926:()=>{},81160:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[332,481],()=>r(28999));module.exports=o})();