import { executeAgentWithRetry } from "./execute-with-retry";
import { createImprovementMessage } from "./evaluate";

interface RetryHandlerOptions {
  model: any;
  agentName: any;
  messages: any[];
  stateMessage: any;
  intentMessage: string;
  dataStream: any;
  session: any;
  enable_smart_navigation: boolean;
  isNonGeonProvider: boolean;
  maxIterations?: number;
  chatId?: string; // AI 응답을 DB에 저장하기 위한 chatId 추가
}

/**
 * 서버 기반 재시도 핸들러
 * 평가 결과에 따라 자동으로 재시도를 수행합니다.
 */
export async function handleAgentWithRetry({
  model,
  agentName,
  messages,
  stateMessage,
  intentMessage,
  dataStream,
  session,
  enable_smart_navigation,
  isNonGeonProvider,
  maxIterations = 3,
  chatId
}: RetryHandlerOptions) {
  
  let currentMessages = [...messages];
  let iteration = 0; // 0부터 시작: 첫 시도는 0, 재시도는 1, 2, ...
  let previousToolCalls: any[] = []; // 이전 시도의 도구 호출 결과 저장

  while (iteration < maxIterations) {
    console.log(`=== 재시도 핸들러: ${iteration + 1}/${maxIterations} ===`);

    let evaluationResult: any = null;
    let streamCompleted = false;

    // Agent 실행 (콜백으로 평가 결과 수집)
    const result = await executeAgentWithRetry({
      model,
      agentName,
      messages: currentMessages,
      stateMessage,
      intentMessage,
      dataStream,
      session,
      enable_smart_navigation,
      isNonGeonProvider,
      iteration,
      maxIterations,
      chatId,
      onEvaluationComplete: (evaluation) => {
        evaluationResult = evaluation;
        // 도구 호출 정보 저장 (다음 재시도에서 사용)
        if (evaluation.toolCalls) {
          previousToolCalls = evaluation.toolCalls;
        }
        streamCompleted = true;
      }
    });

    // 스트림을 데이터스트림에 병합
    result.mergeIntoDataStream(dataStream, {
      sendReasoning: true,
    });

    // 평가 완료까지 대기
    while (!streamCompleted) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 완료 여부 판단 (단순화된 기준)
    if (evaluationResult?.isCompleted) {
      console.log("=== 작업 완료 ===");

      // 최종 완료 어노테이션 (첫 번째 시도에서 성공한 경우는 제외)
      if (iteration > 0) {
        dataStream.writeMessageAnnotation({
          type: "retry_completed",
          totalIterations: iteration + 1,
          maxIterations,
          finalResult: "success",
          message: "모든 작업이 성공적으로 완료되었습니다"
        });
      }

      break;
    }

    // 재시도 준비
    if (iteration < maxIterations - 1) {
      console.log("=== 재시도 준비 중 ===");

      // 개선 메시지를 시스템 메시지로 추가
      const improvementMessage = createImprovementMessage(evaluationResult);
      currentMessages = [...currentMessages, {
        role: "system",
        content: improvementMessage
      }];

      // 이전 도구 호출 결과를 assistant 메시지로 추가 (컨텍스트 유지)
      if (previousToolCalls.length > 0) {
        const toolCallsContext = previousToolCalls.map((toolCall: any) => {
          const args = JSON.stringify(toolCall.args);
          const result = toolCall.result ? JSON.stringify(toolCall.result).substring(0, 200) + '...' : '결과 없음';
          return `${toolCall.toolName}(${args}) → ${result}`;
        }).join('\n');

        currentMessages = [...currentMessages, {
          role: "assistant",
          content: `이전 시도에서 수행한 작업 결과:\n${toolCallsContext}\n\n위 결과를 참고하여 재시도하세요.`
        }];
      }

      // 재시도 시작 알림
      dataStream.writeMessageAnnotation({
        type: "retry_starting",
        iteration: iteration + 2,
        maxIterations,
        message: `${iteration + 2}번째 시도를 시작합니다`,
        reason: evaluationResult.reason,
        improvementSuggestions: evaluationResult.improvementSuggestions || []
      });

      // iteration 증가: 작업이 미완료이므로 다음 시도 진행
      iteration++;
    } else {
      console.log("=== 최대 재시도 횟수 도달 ===");

      // 최대 재시도 도달 어노테이션
      dataStream.writeMessageAnnotation({
        type: "retry_limit_reached",
        totalIterations: maxIterations,
        maxIterations,
        message: `⏰ 최대 재시도 횟수(${maxIterations}회)에 도달했습니다`,
        finalEvaluation: evaluationResult
      });

      break;
    }
  }
  
  return "완료";
}
