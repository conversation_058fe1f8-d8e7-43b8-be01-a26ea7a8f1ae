import { NextRequest, NextResponse } from 'next/server';

const HEALTH_CHECK_URL = 'http://121.163.19.104:8005/health';
const TIMEOUT_DURATION = 5000; // 5초 타임아웃

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_DURATION);

    
    const response = await fetch(HEALTH_CHECK_URL, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;

    if (response.ok) {
      // 서버에서 받은 응답을 그대로 전달하거나, 간단한 상태 정보만 전달
      const data = await response.text();
      
      return NextResponse.json({
        status: 'healthy',
        responseTime,
        timestamp: new Date().toISOString(),
        data: data || 'OK'
      });
    } else {
      return NextResponse.json({
        status: 'unhealthy',
        responseTime,
        timestamp: new Date().toISOString(),
        error: `HTTP ${response.status}: ${response.statusText}`
      }, { status: response.status });
    }
  } catch (error) {
    const responseTime = Date.now() - startTime;
    let errorMessage = 'Unknown error';
    
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        errorMessage = 'Request timeout';
      } else if (error.message.includes('fetch')) {
        errorMessage = 'Network error - Server may be offline';
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json({
      status: 'unhealthy',
      responseTime,
      timestamp: new Date().toISOString(),
      error: errorMessage
    }, { status: 503 });
  }
}
