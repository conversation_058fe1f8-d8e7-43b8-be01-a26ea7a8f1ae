services:
  backend:
    build: ./backend
    expose:
      - "8000"
    ports:
      - "8000:8000"
  frontend:
    build: ./frontend
    depends_on:
      - backend
    ports:
      - "80:80"
  better-gpt:
    build: ./better-gpt
    depends_on:
      - backend
    ports:
      - "3000:3000"

#volumes:
#  db-data:
#    driver: flocker
#    driver_opts:
#      size: "10GiB"
#
#configs:
#  httpd-config:
#    external: true
#
#secrets:
#  server-certificate:
#    external: true
#
#networks:
#  front-tier: {}
#  back-tier: {}