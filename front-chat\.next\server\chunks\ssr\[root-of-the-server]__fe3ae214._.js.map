{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/app-sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppSidebar() from the server but AppSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/app-sidebar.tsx <module evaluation>\",\n    \"AppSidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,4DACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/app-sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppSidebar() from the server but AppSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/app-sidebar.tsx\",\n    \"AppSidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,wCACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Sidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"Sidebar\",\n);\nexport const SidebarContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarContent\",\n);\nexport const SidebarFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarFooter() from the server but <PERSON>barFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarFooter\",\n);\nexport const SidebarGroup = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroup\",\n);\nexport const SidebarGroupAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroupAction\",\n);\nexport const SidebarGroupContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroupContent\",\n);\nexport const SidebarGroupLabel = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroupLabel\",\n);\nexport const SidebarHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarHeader\",\n);\nexport const SidebarInput = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarInput\",\n);\nexport const SidebarInset = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarInset\",\n);\nexport const SidebarMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenu\",\n);\nexport const SidebarMenuAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuAction\",\n);\nexport const SidebarMenuBadge = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuBadge\",\n);\nexport const SidebarMenuButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuButton\",\n);\nexport const SidebarMenuItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuItem\",\n);\nexport const SidebarMenuSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSkeleton\",\n);\nexport const SidebarMenuSub = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSub\",\n);\nexport const SidebarMenuSubAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubAction() from the server but SidebarMenuSubAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSubAction\",\n);\nexport const SidebarMenuSubButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSubButton\",\n);\nexport const SidebarMenuSubItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSubItem\",\n);\nexport const SidebarProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarProvider\",\n);\nexport const SidebarRail = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarRail\",\n);\nexport const SidebarSeparator = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarSeparator\",\n);\nexport const SidebarTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarTrigger\",\n);\nexport const useSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx <module evaluation>\",\n    \"useSidebar\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2DACA;AAEG,MAAM,eAAe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,2DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,2DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,2DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2DACA;AAEG,MAAM,eAAe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2DACA;AAEG,MAAM,eAAe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2DACA;AAEG,MAAM,cAAc,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,2DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,2DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,2DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,2DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,2DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2DACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,2DACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,2DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,2DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2DACA;AAEG,MAAM,cAAc,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,2DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,2DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2DACA;AAEG,MAAM,aAAa,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2DACA", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Sidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"Sidebar\",\n);\nexport const SidebarContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarContent\",\n);\nexport const SidebarFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call <PERSON>barFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarFooter\",\n);\nexport const SidebarGroup = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarGroup\",\n);\nexport const SidebarGroupAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarGroupAction\",\n);\nexport const SidebarGroupContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarGroupContent\",\n);\nexport const SidebarGroupLabel = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarGroupLabel\",\n);\nexport const SidebarHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarHeader\",\n);\nexport const SidebarInput = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarInput\",\n);\nexport const SidebarInset = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarInset\",\n);\nexport const SidebarMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarMenu\",\n);\nexport const SidebarMenuAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarMenuAction\",\n);\nexport const SidebarMenuBadge = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarMenuBadge\",\n);\nexport const SidebarMenuButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarMenuButton\",\n);\nexport const SidebarMenuItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarMenuItem\",\n);\nexport const SidebarMenuSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarMenuSkeleton\",\n);\nexport const SidebarMenuSub = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarMenuSub\",\n);\nexport const SidebarMenuSubAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubAction() from the server but SidebarMenuSubAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarMenuSubAction\",\n);\nexport const SidebarMenuSubButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarMenuSubButton\",\n);\nexport const SidebarMenuSubItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarMenuSubItem\",\n);\nexport const SidebarProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarProvider\",\n);\nexport const SidebarRail = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarRail\",\n);\nexport const SidebarSeparator = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarSeparator\",\n);\nexport const SidebarTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"SidebarTrigger\",\n);\nexport const useSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sidebar.tsx\",\n    \"useSidebar\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,uCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,uCACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,uCACA;AAEG,MAAM,eAAe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,uCACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,uCACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,uCACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,uCACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,uCACA;AAEG,MAAM,eAAe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,uCACA;AAEG,MAAM,eAAe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,uCACA;AAEG,MAAM,cAAc,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,uCACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,uCACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,uCACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,uCACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,uCACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,uCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,uCACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,uCACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,uCACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,uCACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,uCACA;AAEG,MAAM,cAAc,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,uCACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,uCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,uCACA;AAEG,MAAM,aAAa,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uCACA", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28auth%29/auth.config.ts"], "sourcesContent": ["import {NextAuthConfig, Session, User} from \"next-auth\";\r\n\r\nexport const authConfig = {\r\n\tpages: {\r\n\t\tsignIn: \"/login\",\r\n\t\t// verifyRequest: `/login`,\r\n\t\t// error: \"/login\", // Error code passed in query string as ?error=\r\n\t\tnewUser: \"/\",\r\n\t},\r\n\tproviders: [\r\n\t\t// added later in auth.ts since it requires bcrypt which is only compatible with Node.js\r\n\t\t// while this file is also used in non-Node.js environments\r\n\t],\r\n\tcallbacks: {\r\n\t\tauthorized({ auth, request: { nextUrl } }) {\r\n\t\t\tconst isLoggedIn = !!auth?.user;\r\n\t\t\tconst isOnChat = nextUrl.pathname.startsWith(\"/geon-2d-map\");\r\n\t\t\tconst isOnLogin = nextUrl.pathname.startsWith(\"/login\");\r\n\t\t\tconst isOnRoot = nextUrl.pathname === \"/\";\r\n\t  \r\n\t\t\t// 루트 경로로 접근시 /geon-2d-map으로 리다이렉트\r\n\t\t\tif (isOnRoot) {\r\n\t\t\t  return Response.redirect(new URL(\"/geon-2d-map\", nextUrl));\r\n\t\t\t}\r\n\t  \r\n\t\t\t// 로그인된 상태에서 로그인/회원가입 페이지 접근시 /geon-2d-map으로 리다이렉트\r\n\t\t\tif (isLoggedIn && (isOnLogin)) {\r\n\t\t\t  return Response.redirect(new URL(\"/geon-2d-map\", nextUrl));\r\n\t\t\t}\r\n\t  \r\n\t\t\t// 로그인/회원가입 페이지는 항상 접근 가능\r\n\t\t\tif (isOnLogin) {\r\n\t\t\t  return true;\r\n\t\t\t}\r\n\t  \r\n\t\t\t// /geon-2d-map 페이지는 로그인한 사용자만 접근 가능\r\n\t\t\tif (isOnChat) {\r\n\t\t\t  return isLoggedIn;\r\n\t\t\t}\r\n\t  \r\n\t\t\treturn true;\r\n\t\t  },\r\n\t},\r\n} satisfies NextAuthConfig;\r\n"], "names": [], "mappings": ";;;AAEO,MAAM,aAAa;IACzB,OAAO;QACN,QAAQ;QACR,2BAA2B;QAC3B,mEAAmE;QACnE,SAAS;IACV;IACA,WAAW,EAGV;IACD,WAAW;QACV,YAAW,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;YACxC,MAAM,aAAa,CAAC,CAAC,MAAM;YAC3B,MAAM,WAAW,QAAQ,QAAQ,CAAC,UAAU,CAAC;YAC7C,MAAM,YAAY,QAAQ,QAAQ,CAAC,UAAU,CAAC;YAC9C,MAAM,WAAW,QAAQ,QAAQ,KAAK;YAEtC,kCAAkC;YAClC,IAAI,UAAU;gBACZ,OAAO,SAAS,QAAQ,CAAC,IAAI,IAAI,gBAAgB;YACnD;YAEA,kDAAkD;YAClD,IAAI,cAAe,WAAY;gBAC7B,OAAO,SAAS,QAAQ,CAAC,IAAI,IAAI,gBAAgB;YACnD;YAEA,yBAAyB;YACzB,IAAI,WAAW;gBACb,OAAO;YACT;YAEA,oCAAoC;YACpC,IAAI,UAAU;gBACZ,OAAO;YACT;YAEA,OAAO;QACN;IACH;AACD", "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28auth%29/auth.ts"], "sourcesContent": ["import NextAuth, {User, Session, CredentialsSignin} from \"next-auth\";\r\nimport type { Provider } from \"next-auth/providers\";\r\nimport Credentials from \"next-auth/providers/credentials\";\r\nimport { authConfig } from \"./auth.config\";\r\n\r\ninterface ExtendedSession extends Session {\r\n    user: User;\r\n}\r\n\r\ninterface ValidationResponse {\r\n    code: number;\r\n    message: string;\r\n    result: {\r\n        isValid: boolean;\r\n        message: string;\r\n    };\r\n}\r\n\r\ninterface UserResponse {\r\n    code: number;\r\n    message: string;\r\n    result: {\r\n        userId: string;\r\n        userNm: string;\r\n        emailaddr: string | null;\r\n        userSeCode: string;\r\n        userSeCodeNm: string;\r\n        userImage: string | null;\r\n        insttCode: string;\r\n        insttNm: string | null;\r\n        insttUrl: string | null;\r\n        message: string;\r\n    };\r\n}\r\n\r\nclass InvalidLoginError extends CredentialsSignin {\r\n    code = \"Invalid identifier or password\"\r\n}\r\n\r\nconst PRODUCTION = process.env.NODE_ENV === \"production\";\r\nconst API_CERTIFICATE_KEY = 'tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh'\r\nconst SMT_URL = 'https://gsapi.geon.kr/smt'\r\n\r\nasync function validateLogin(userId: string, password: string): Promise<ValidationResponse> {\r\n    const params = new URLSearchParams({\r\n        crtfckey: API_CERTIFICATE_KEY,\r\n        userId,\r\n        password\r\n    });\r\n\r\n    const response = await fetch(`${SMT_URL}/login/validation?${params.toString()}`, {\r\n        method: 'POST',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            'crtfckey': API_CERTIFICATE_KEY\r\n        }\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n        throw new Error('로그인 검증에 실패했습니다.');\r\n    }\r\n\r\n    return data;\r\n}\r\n\r\nasync function getUserInfo(userId: string): Promise<UserResponse> {\r\n    const params = new URLSearchParams({\r\n        crtfckey: API_CERTIFICATE_KEY,\r\n        userId\r\n    });\r\n\r\n    const response = await fetch(`${SMT_URL}/users/id?${params.toString()}`, {\r\n        method: 'GET',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            'crtfckey': API_CERTIFICATE_KEY\r\n        }\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n        throw new Error('사용자 정보를 가져오는데 실패했습니다.');\r\n    }\r\n\r\n    return data;\r\n}\r\n\r\nexport const providers: Provider[] = [\r\n    Credentials({\r\n        credentials: {},\r\n        async authorize({id, password}: any) {\r\n            try {\r\n                // admin 계정으로 프론트엔드 로그인 허용\r\n                const frontendUserId = process.env.FRONTEND_LOGIN_USER_ID || 'admin';\r\n                const frontendPassword = process.env.FRONTEND_LOGIN_PASSWORD || 'password1234';\r\n\r\n                if (id === frontendUserId && password === frontendPassword) {\r\n                    // admin 계정으로 로그인 성공\r\n                    return {\r\n                        id: frontendUserId,\r\n                        name: 'GeOn City',\r\n                        email: '@example.com',\r\n                        userId: frontendUserId,\r\n                        userNm: 'GeOn City',\r\n                        emailaddr: '@example.com',\r\n                        userSeCode: '14',\r\n                        userSeCodeNm: '관리자',\r\n                        userImage: null,\r\n                        insttCode: 'GEON',\r\n                        insttNm: 'GeOn',\r\n                        insttUrl: null,\r\n                        message: '로그인 성공'\r\n                    };\r\n                }\r\n\r\n                // 기존 geonuser 계정도 유지 (호환성을 위해)\r\n                if (id === 'geonuser') {\r\n                    // 1. 로그인 검증\r\n                    const validation = await validateLogin(id, password);\r\n\r\n                    if (!validation.result.isValid) {\r\n                        throw new CredentialsSignin(validation.result.message);\r\n                    }\r\n\r\n                    // 2. 유저 정보 조회\r\n                    const userResponse = await getUserInfo(id);\r\n\r\n                    if (userResponse.code !== 200) {\r\n                        return new CredentialsSignin(userResponse.result.message);\r\n                    }\r\n\r\n                    // 3. 유저 정보 반환\r\n                    return {\r\n                        ...userResponse.result,\r\n                        id: userResponse.result.userId,\r\n                        name: userResponse.result.userNm || id,\r\n                        email: userResponse.result.emailaddr || `${userResponse.result.userNm}`,\r\n                    };\r\n                }\r\n\r\n                // 허용되지 않은 계정\r\n                throw new CredentialsSignin('admin 또는 geonuser 계정으로만 로그인할 수 있습니다.');\r\n            } catch (error) {\r\n                console.error('Auth error:', error);\r\n                throw error;\r\n            }\r\n        },\r\n    })\r\n]\r\n\r\nexport const providerMap = providers\r\n  .map((provider) => {\r\n      if (typeof provider === \"function\") {\r\n          const providerData = provider()\r\n          return { id: providerData.id, name: providerData.name }\r\n      } else {\r\n          return { id: provider.id, name: provider.name }\r\n      }\r\n  })\r\n  .filter((provider) => provider.id !== \"credentials\")\r\n\r\nexport const {\r\n    handlers,\r\n    auth,\r\n    signIn,\r\n    signOut,\r\n} = NextAuth({\r\n    ...authConfig,\r\n    providers,\r\n    session: {\r\n        strategy: \"jwt\",\r\n        maxAge: 30 * 60, // 30분 (30분 * 60초)\r\n    },\r\n    callbacks: {\r\n        async jwt({ token, user }) {\r\n            if (user) {\r\n                token.id = user.id;\r\n            }\r\n            return token;\r\n        },\r\n        async session({session, token,}: {\r\n            session: ExtendedSession;\r\n            token: any;\r\n        }) {\r\n            if (session.user) {\r\n                session.user.id = token.id as string;\r\n            }\r\n            return session;\r\n        },\r\n    }\r\n    // adapter: DrizzleAdapter(db, {\r\n    //     // @ts-ignore GitHub 로그인의 경우 email Null 가능성 존재\r\n    //     usersTable: users,\r\n    //     accountsTable: accounts,\r\n    //     sessionsTable: sessions,\r\n    //     verificationTokensTable: verificationTokens,\r\n    // }) as Adapter,\r\n    // cookies: {\r\n    //     sessionToken: {\r\n    //         name: `${PRODUCTION ? \"__Secure-\" : \"\"}next-auth.session-token`,\r\n    //         options: {\r\n    //             httpOnly: true,\r\n    //             sameSite: \"lax\",\r\n    //             path: \"/\",\r\n    //             secure: PRODUCTION,\r\n    //             // When working on localhost, the cookie domain must be omitted entirely (https://stackoverflow.com/a/1188145)\r\n    //             domain: PRODUCTION\r\n    //               ? `.${process.env.NEXT_PUBLIC_ROOT_DOMAIN}`\r\n    //               : undefined,\r\n    //         },\r\n    //     },\r\n    // },\r\n});\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;;;;AAgCA,MAAM,0BAA0B,oMAAA,CAAA,oBAAiB;IAC7C,OAAO,iCAAgC;AAC3C;AAEA,MAAM,aAAa,oDAAyB;AAC5C,MAAM,sBAAsB;AAC5B,MAAM,UAAU;AAEhB,eAAe,cAAc,MAAc,EAAE,QAAgB;IACzD,MAAM,SAAS,IAAI,gBAAgB;QAC/B,UAAU;QACV;QACA;IACJ;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,kBAAkB,EAAE,OAAO,QAAQ,IAAI,EAAE;QAC7E,QAAQ;QACR,SAAS;YACL,gBAAgB;YAChB,YAAY;QAChB;IACJ;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;QACd,MAAM,IAAI,MAAM;IACpB;IAEA,OAAO;AACX;AAEA,eAAe,YAAY,MAAc;IACrC,MAAM,SAAS,IAAI,gBAAgB;QAC/B,UAAU;QACV;IACJ;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,UAAU,EAAE,OAAO,QAAQ,IAAI,EAAE;QACrE,QAAQ;QACR,SAAS;YACL,gBAAgB;YAChB,YAAY;QAChB;IACJ;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;QACd,MAAM,IAAI,MAAM;IACpB;IAEA,OAAO;AACX;AAEO,MAAM,YAAwB;IACjC,CAAA,GAAA,sNAAA,CAAA,UAAW,AAAD,EAAE;QACR,aAAa,CAAC;QACd,MAAM,WAAU,EAAC,EAAE,EAAE,QAAQ,EAAM;YAC/B,IAAI;gBACA,0BAA0B;gBAC1B,MAAM,iBAAiB,QAAQ,GAAG,CAAC,sBAAsB,IAAI;gBAC7D,MAAM,mBAAmB,QAAQ,GAAG,CAAC,uBAAuB,IAAI;gBAEhE,IAAI,OAAO,kBAAkB,aAAa,kBAAkB;oBACxD,oBAAoB;oBACpB,OAAO;wBACH,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,QAAQ;wBACR,QAAQ;wBACR,WAAW;wBACX,YAAY;wBACZ,cAAc;wBACd,WAAW;wBACX,WAAW;wBACX,SAAS;wBACT,UAAU;wBACV,SAAS;oBACb;gBACJ;gBAEA,+BAA+B;gBAC/B,IAAI,OAAO,YAAY;oBACnB,YAAY;oBACZ,MAAM,aAAa,MAAM,cAAc,IAAI;oBAE3C,IAAI,CAAC,WAAW,MAAM,CAAC,OAAO,EAAE;wBAC5B,MAAM,IAAI,oMAAA,CAAA,oBAAiB,CAAC,WAAW,MAAM,CAAC,OAAO;oBACzD;oBAEA,cAAc;oBACd,MAAM,eAAe,MAAM,YAAY;oBAEvC,IAAI,aAAa,IAAI,KAAK,KAAK;wBAC3B,OAAO,IAAI,oMAAA,CAAA,oBAAiB,CAAC,aAAa,MAAM,CAAC,OAAO;oBAC5D;oBAEA,cAAc;oBACd,OAAO;wBACH,GAAG,aAAa,MAAM;wBACtB,IAAI,aAAa,MAAM,CAAC,MAAM;wBAC9B,MAAM,aAAa,MAAM,CAAC,MAAM,IAAI;wBACpC,OAAO,aAAa,MAAM,CAAC,SAAS,IAAI,GAAG,aAAa,MAAM,CAAC,MAAM,EAAE;oBAC3E;gBACJ;gBAEA,aAAa;gBACb,MAAM,IAAI,oMAAA,CAAA,oBAAiB,CAAC;YAChC,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,eAAe;gBAC7B,MAAM;YACV;QACJ;IACJ;CACH;AAEM,MAAM,cAAc,UACxB,GAAG,CAAC,CAAC;IACF,IAAI,OAAO,aAAa,YAAY;QAChC,MAAM,eAAe;QACrB,OAAO;YAAE,IAAI,aAAa,EAAE;YAAE,MAAM,aAAa,IAAI;QAAC;IAC1D,OAAO;QACH,OAAO;YAAE,IAAI,SAAS,EAAE;YAAE,MAAM,SAAS,IAAI;QAAC;IAClD;AACJ,GACC,MAAM,CAAC,CAAC,WAAa,SAAS,EAAE,KAAK;AAEjC,MAAM,EACT,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,OAAO,EACV,GAAG,CAAA,GAAA,iQAAA,CAAA,UAAQ,AAAD,EAAE;IACT,GAAG,iIAAA,CAAA,aAAU;IACb;IACA,SAAS;QACL,UAAU;QACV,QAAQ,KAAK;IACjB;IACA,WAAW;QACP,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACrB,IAAI,MAAM;gBACN,MAAM,EAAE,GAAG,KAAK,EAAE;YACtB;YACA,OAAO;QACX;QACA,MAAM,SAAQ,EAAC,OAAO,EAAE,KAAK,EAG5B;YACG,IAAI,QAAQ,IAAI,EAAE;gBACd,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;YAC9B;YACA,OAAO;QACX;IACJ;AAuBJ", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/ai/models.ts"], "sourcesContent": ["// Define your models here.\r\n\r\nexport interface ModelCapabilities {\r\n  reasoning: boolean;\r\n  streaming: boolean;\r\n  tools: boolean;\r\n  vision: boolean;\r\n}\r\n\r\nexport interface Model {\r\n  id: string;\r\n  label: string;\r\n  apiIdentifier: string;\r\n  description: string;\r\n  provider: \"geon\" | \"openai\" | \"dify\";\r\n  capabilities: ModelCapabilities;\r\n}\r\n\r\nexport const models: Array<Model> = [\r\n  {\r\n    id: \"Qwen3-14B\",\r\n    label: \"Qwen3 14B\",\r\n    apiIdentifier: \"Qwen/Qwen2.5-14B\",\r\n    description: \"Qwen3 14B 모델입니다.\",\r\n    provider: \"geon\",\r\n    capabilities: {\r\n      reasoning: false,\r\n      streaming: true,\r\n      tools: true,\r\n      vision: false,\r\n    },\r\n  },\r\n  {\r\n    id: \"Qwen3-4B\",\r\n    label: \"Qwen3 (추론)\",\r\n    apiIdentifier: \"Qwen/Qwen3-4B\",\r\n    description: \"Qwen3-4B 추론 모델입니다.\",\r\n    provider: \"geon\",\r\n    capabilities: {\r\n      reasoning: true,\r\n      streaming: true,\r\n      tools: true,\r\n      vision: false,\r\n    },\r\n  },\r\n  // {\r\n  //   id: 'A.X-4-Light',\r\n  //   label: 'A.X-4 Light',\r\n  //   apiIdentifier: 'A.X-4-Light-awq',\r\n  //   description: 'A.X-4 Light 모델입니다.',\r\n  //   provider: 'geon',\r\n  //   capabilities: {\r\n  //     reasoning: false,\r\n  //     streaming: true,\r\n  //     tools: true,\r\n  //     vision: false,\r\n  //   },\r\n  // },\r\n  {\r\n    id: \"gpt-4.1-nano\",\r\n    label: \"GPT 4.1 Nano\",\r\n    apiIdentifier: \"gpt-4.1-nano\",\r\n    description: \"OpenAI의 GPT 4.1 Nano 모델입니다.\",\r\n    provider: \"openai\",\r\n    capabilities: {\r\n      reasoning: false,\r\n      streaming: true,\r\n      tools: true,\r\n      vision: false,\r\n    },\r\n  },\r\n] as const;\r\n\r\nexport const DEFAULT_MODEL_NAME: string = \"Qwen3-4B\";\r\n\r\n// 모델 유틸리티 함수들\r\nexport function getModelById(modelId: string): Model | undefined {\r\n  return models.find((model) => model.id === modelId);\r\n}\r\n\r\nexport function getModelCapabilities(\r\n  modelId: string\r\n): ModelCapabilities | undefined {\r\n  const model = getModelById(modelId);\r\n  return model?.capabilities;\r\n}\r\n\r\nexport function supportsReasoning(modelId: string): boolean {\r\n  const capabilities = getModelCapabilities(modelId);\r\n  return capabilities?.reasoning ?? false;\r\n}\r\n\r\nexport function supportsStreaming(modelId: string): boolean {\r\n  const capabilities = getModelCapabilities(modelId);\r\n  return capabilities?.streaming ?? false;\r\n}\r\n\r\nexport function supportsTools(modelId: string): boolean {\r\n  const capabilities = getModelCapabilities(modelId);\r\n  return capabilities?.tools ?? false;\r\n}\r\n\r\nexport function supportsVision(modelId: string): boolean {\r\n  const capabilities = getModelCapabilities(modelId);\r\n  return capabilities?.vision ?? false;\r\n}\r\n\r\nexport function getModelProvider(\r\n  modelId: string\r\n): \"geon\" | \"openai\" | \"dify\" | undefined {\r\n  const model = getModelById(modelId);\r\n  return model?.provider;\r\n}\r\n\r\nexport function getReasoningDisabledMessage(\r\n  modelId: string\r\n): string | undefined {\r\n  const model = getModelById(modelId);\r\n  if (!model || model.capabilities.reasoning) {\r\n    return undefined;\r\n  }\r\n\r\n  // 모델별 맞춤 메시지\r\n  switch (model.id) {\r\n    case \"gpt-4.1-nano\":\r\n      return \"GPT 4.1 Nano 에서 지원되지 않습니다.\";\r\n    default:\r\n      return \"현재 선택된 모델은 추론 기능을 지원하지 않습니다\";\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;;;;;;;;AAkBpB,MAAM,SAAuB;IAClC;QACE,IAAI;QACJ,OAAO;QACP,eAAe;QACf,aAAa;QACb,UAAU;QACV,cAAc;YACZ,WAAW;YACX,WAAW;YACX,OAAO;YACP,QAAQ;QACV;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,eAAe;QACf,aAAa;QACb,UAAU;QACV,cAAc;YACZ,WAAW;YACX,WAAW;YACX,OAAO;YACP,QAAQ;QACV;IACF;IACA,IAAI;IACJ,uBAAuB;IACvB,0BAA0B;IAC1B,sCAAsC;IACtC,uCAAuC;IACvC,sBAAsB;IACtB,oBAAoB;IACpB,wBAAwB;IACxB,uBAAuB;IACvB,mBAAmB;IACnB,qBAAqB;IACrB,OAAO;IACP,KAAK;IACL;QACE,IAAI;QACJ,OAAO;QACP,eAAe;QACf,aAAa;QACb,UAAU;QACV,cAAc;YACZ,WAAW;YACX,WAAW;YACX,OAAO;YACP,QAAQ;QACV;IACF;CACD;AAEM,MAAM,qBAA6B;AAGnC,SAAS,aAAa,OAAe;IAC1C,OAAO,OAAO,IAAI,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK;AAC7C;AAEO,SAAS,qBACd,OAAe;IAEf,MAAM,QAAQ,aAAa;IAC3B,OAAO,OAAO;AAChB;AAEO,SAAS,kBAAkB,OAAe;IAC/C,MAAM,eAAe,qBAAqB;IAC1C,OAAO,cAAc,aAAa;AACpC;AAEO,SAAS,kBAAkB,OAAe;IAC/C,MAAM,eAAe,qBAAqB;IAC1C,OAAO,cAAc,aAAa;AACpC;AAEO,SAAS,cAAc,OAAe;IAC3C,MAAM,eAAe,qBAAqB;IAC1C,OAAO,cAAc,SAAS;AAChC;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,eAAe,qBAAqB;IAC1C,OAAO,cAAc,UAAU;AACjC;AAEO,SAAS,iBACd,OAAe;IAEf,MAAM,QAAQ,aAAa;IAC3B,OAAO,OAAO;AAChB;AAEO,SAAS,4BACd,OAAe;IAEf,MAAM,QAAQ,aAAa;IAC3B,IAAI,CAAC,SAAS,MAAM,YAAY,CAAC,SAAS,EAAE;QAC1C,OAAO;IACT;IAEA,aAAa;IACb,OAAQ,MAAM,EAAE;QACd,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/geon-2d-map/layout.tsx"], "sourcesContent": ["import { cookies } from 'next/headers';\r\n\r\nimport { AppSidebar } from '@/components/app-sidebar';\r\nimport { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';\r\nimport { auth } from '@/app/(auth)/auth';\r\nimport { DEFAULT_MODEL_NAME, models } from '@/lib/ai/models';\r\n\r\n\r\nexport const experimental_ppr = true;\r\n\r\nexport default async function Layout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const [session, cookieStore] = await Promise.all([auth(), cookies()]);\r\n  const isCollapsed = cookieStore.get('sidebar:state')?.value !== 'true';\r\n  \r\n  const modelIdFromCookie = cookieStore.get('model-id')?.value;\r\n\r\n  const selectedModelId =\r\n    models.find((model) => model.id === modelIdFromCookie)?.id ||\r\n    DEFAULT_MODEL_NAME;\r\n\r\n  return ( \r\n    <SidebarProvider defaultOpen={isCollapsed}>\r\n      <AppSidebar user={session?.user} selectedModelId={selectedModelId} />\r\n      <SidebarInset>{children}</SidebarInset>\r\n    </SidebarProvider>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;AAEA;AACA;AACA;AACA;;;;;;;AAGO,MAAM,mBAAmB;AAEjB,eAAe,OAAO,EACnC,QAAQ,EAGT;IACC,MAAM,CAAC,SAAS,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;QAAC,CAAA,GAAA,uHAAA,CAAA,OAAI,AAAD;QAAK,CAAA,GAAA,wOAAA,CAAA,UAAO,AAAD;KAAI;IACpE,MAAM,cAAc,YAAY,GAAG,CAAC,kBAAkB,UAAU;IAEhE,MAAM,oBAAoB,YAAY,GAAG,CAAC,aAAa;IAEvD,MAAM,kBACJ,mHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK,oBAAoB,MACxD,mHAAA,CAAA,qBAAkB;IAEpB,qBACE,uVAAC,4HAAA,CAAA,kBAAe;QAAC,aAAa;;0BAC5B,uVAAC,6HAAA,CAAA,aAAU;gBAAC,MAAM,SAAS;gBAAM,iBAAiB;;;;;;0BAClD,uVAAC,4HAAA,CAAA,eAAY;0BAAE;;;;;;;;;;;;AAGrB", "debugId": null}}]}