"use strict";exports.id=343,exports.ids=[343],exports.modules={71272:(e,t,s)=>{let n;s.d(t,{g:()=>ta});var a,r,i,o,l=s(87113),u=s(56906),d=s(58747);function c(...e){return e.reduce((e,t)=>({...e,...null!=t?t:{}}),{})}function p(e){let t={};return e.headers.forEach((e,s)=>{t[s]=e}),t}var h=(({prefix:e,size:t=16,alphabet:s="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",separator:n="-"}={})=>{let a=(0,u.d)(s,t);if(null==e)return a;if(s.includes(n))throw new l.Di({argument:"separator",message:`The separator "${n}" must not be part of the alphabet "${s}".`});return t=>`${e}${n}${a(t)}`})();function m(e){return e instanceof Error&&("AbortError"===e.name||"TimeoutError"===e.name)}var f=Symbol.for("vercel.ai.validator");function g({value:e,schema:t}){var s;let n="object"==typeof t&&null!==t&&f in t&&!0===t[f]&&"validate"in t?t:(s=t,{[f]:!0,validate:e=>{let t=s.safeParse(e);return t.success?{success:!0,value:t.data}:{success:!1,error:t.error}}});try{if(null==n.validate)return{success:!0,value:e};let t=n.validate(e);if(t.success)return t;return{success:!1,error:l.iM.wrap({value:e,cause:t.error})}}catch(t){return{success:!1,error:l.iM.wrap({value:e,cause:t})}}}function y({text:e,schema:t}){try{let s=d.parse(e);if(null==t)return{success:!0,value:s,rawValue:s};let n=g({value:s,schema:t});return n.success?{...n,rawValue:s}:n}catch(t){return{success:!1,error:l.u6.isInstance(t)?t:new l.u6({text:e,cause:t})}}}var _=()=>globalThis.fetch,v=async({url:e,headers:t,body:s,failedResponseHandler:n,successfulResponseHandler:a,abortSignal:r,fetch:i})=>b({url:e,headers:{"Content-Type":"application/json",...t},body:{content:JSON.stringify(s),values:s},failedResponseHandler:n,successfulResponseHandler:a,abortSignal:r,fetch:i}),b=async({url:e,headers:t={},body:s,successfulResponseHandler:n,failedResponseHandler:a,abortSignal:r,fetch:i=_()})=>{try{var o;let u=await i(e,{method:"POST",headers:(o=t,Object.fromEntries(Object.entries(o).filter(([e,t])=>null!=t))),body:s.content,signal:r}),d=p(u);if(!u.ok){let t;try{t=await a({response:u,url:e,requestBodyValues:s.values})}catch(t){if(m(t)||l.hL.isInstance(t))throw t;throw new l.hL({message:"Failed to process error response",cause:t,statusCode:u.status,url:e,responseHeaders:d,requestBodyValues:s.values})}throw t.value}try{return await n({response:u,url:e,requestBodyValues:s.values})}catch(t){if(t instanceof Error&&(m(t)||l.hL.isInstance(t)))throw t;throw new l.hL({message:"Failed to process successful response",cause:t,statusCode:u.status,url:e,responseHeaders:d,requestBodyValues:s.values})}}catch(t){if(m(t))throw t;if(t instanceof TypeError&&"fetch failed"===t.message){let n=t.cause;if(null!=n)throw new l.hL({message:`Cannot connect to API: ${n.message}`,cause:n,url:e,requestBodyValues:s.values,isRetryable:!0})}throw t}},w=e=>async({response:t})=>{let s=p(t);if(null==t.body)throw new l.Tt({});return{responseHeaders:s,value:t.body.pipeThrough(new TextDecoderStream).pipeThrough(function(){let e,t,s,n="",a=[];function r(e,t){if(""===e)return void i(t);if(e.startsWith(":"))return;let s=e.indexOf(":");if(-1===s)return void o(e,"");let n=e.slice(0,s),a=s+1;o(n,a<e.length&&" "===e[a]?e.slice(a+1):e.slice(a))}function i(n){a.length>0&&(n.enqueue({event:e,data:a.join("\n"),id:t,retry:s}),a=[],e=void 0,s=void 0)}function o(n,r){switch(n){case"event":e=r;break;case"data":a.push(r);break;case"id":t=r;break;case"retry":let i=parseInt(r,10);isNaN(i)||(s=i)}}return new TransformStream({transform(e,t){let{lines:s,incompleteLine:a}=function(e,t){let s=[],n=e;for(let e=0;e<t.length;){let a=t[e++];"\n"===a?(s.push(n),n=""):"\r"===a?(s.push(n),n="","\n"===t[e]&&e++):n+=a}return{lines:s,incompleteLine:n}}(n,e);n=a;for(let e=0;e<s.length;e++)r(s[e],t)},flush(e){r(n,e),i(e)}})}()).pipeThrough(new TransformStream({transform({data:t},s){"[DONE]"!==t&&s.enqueue(y({text:t,schema:e}))}}))}},k=e=>async({response:t,url:s,requestBodyValues:n})=>{let a=await t.text(),r=y({text:a,schema:e}),i=p(t);if(!r.success)throw new l.hL({message:"Invalid JSON response",cause:r.error,statusCode:t.status,responseHeaders:i,responseBody:a,url:s,requestBodyValues:n});return{responseHeaders:i,value:r.value,rawValue:r.rawValue}},{btoa:x,atob:z}=globalThis;!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let s of e)t[s]=s;return t},e.getValidEnumValues=t=>{let s=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),n={};for(let e of s)n[e]=t[e];return e.objectValues(n)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.push(s);return t},e.find=(e,t)=>{for(let s of e)if(t(s))return s},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(a||(a={})),(r||(r={})).mergeShapes=(e,t)=>({...e,...t});let T=a.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),j=e=>{switch(typeof e){case"undefined":return T.undefined;case"string":return T.string;case"number":return Number.isNaN(e)?T.nan:T.number;case"boolean":return T.boolean;case"function":return T.function;case"bigint":return T.bigint;case"symbol":return T.symbol;case"object":if(Array.isArray(e))return T.array;if(null===e)return T.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return T.promise;if("undefined"!=typeof Map&&e instanceof Map)return T.map;if("undefined"!=typeof Set&&e instanceof Set)return T.set;if("undefined"!=typeof Date&&e instanceof Date)return T.date;return T.object;default:return T.unknown}},C=a.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class I extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},s={_errors:[]},n=e=>{for(let a of e.issues)if("invalid_union"===a.code)a.unionErrors.map(n);else if("invalid_return_type"===a.code)n(a.returnTypeError);else if("invalid_arguments"===a.code)n(a.argumentsError);else if(0===a.path.length)s._errors.push(t(a));else{let e=s,n=0;for(;n<a.path.length;){let s=a.path[n];n===a.path.length-1?(e[s]=e[s]||{_errors:[]},e[s]._errors.push(t(a))):e[s]=e[s]||{_errors:[]},e=e[s],n++}}};return n(this),s}static assert(e){if(!(e instanceof I))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,a.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},s=[];for(let n of this.issues)if(n.path.length>0){let s=n.path[0];t[s]=t[s]||[],t[s].push(e(n))}else s.push(e(n));return{formErrors:s,fieldErrors:t}}get formErrors(){return this.flatten()}}I.create=e=>new I(e);let S=(e,t)=>{let s;switch(e.code){case C.invalid_type:s=e.received===T.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case C.invalid_literal:s=`Invalid literal value, expected ${JSON.stringify(e.expected,a.jsonStringifyReplacer)}`;break;case C.unrecognized_keys:s=`Unrecognized key(s) in object: ${a.joinValues(e.keys,", ")}`;break;case C.invalid_union:s="Invalid input";break;case C.invalid_union_discriminator:s=`Invalid discriminator value. Expected ${a.joinValues(e.options)}`;break;case C.invalid_enum_value:s=`Invalid enum value. Expected ${a.joinValues(e.options)}, received '${e.received}'`;break;case C.invalid_arguments:s="Invalid function arguments";break;case C.invalid_return_type:s="Invalid function return type";break;case C.invalid_date:s="Invalid date";break;case C.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(s=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(s=`${s} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?s=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?s=`Invalid input: must end with "${e.validation.endsWith}"`:a.assertNever(e.validation):s="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case C.too_small:s="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type||"bigint"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case C.too_big:s="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case C.custom:s="Invalid input";break;case C.invalid_intersection_types:s="Intersection results could not be merged";break;case C.not_multiple_of:s=`Number must be a multiple of ${e.multipleOf}`;break;case C.not_finite:s="Number must be finite";break;default:s=t.defaultError,a.assertNever(e)}return{message:s}};!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(i||(i={}));let N=e=>{let{data:t,path:s,errorMaps:n,issueData:a}=e,r=[...s,...a.path||[]],i={...a,path:r};if(void 0!==a.message)return{...a,path:r,message:a.message};let o="";for(let e of n.filter(e=>!!e).slice().reverse())o=e(i,{data:t,defaultError:o}).message;return{...a,path:r,message:o}};function O(e,t){let s=N({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,S,S==S?void 0:S].filter(e=>!!e)});e.common.issues.push(s)}class A{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let s=[];for(let n of t){if("aborted"===n.status)return R;"dirty"===n.status&&e.dirty(),s.push(n.value)}return{status:e.value,value:s}}static async mergeObjectAsync(e,t){let s=[];for(let e of t){let t=await e.key,n=await e.value;s.push({key:t,value:n})}return A.mergeObjectSync(e,s)}static mergeObjectSync(e,t){let s={};for(let n of t){let{key:t,value:a}=n;if("aborted"===t.status||"aborted"===a.status)return R;"dirty"===t.status&&e.dirty(),"dirty"===a.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==a.value||n.alwaysSet)&&(s[t.value]=a.value)}return{status:e.value,value:s}}}let R=Object.freeze({status:"aborted"}),$=e=>({status:"dirty",value:e}),Z=e=>({status:"valid",value:e}),E=e=>"aborted"===e.status,M=e=>"dirty"===e.status,P=e=>"valid"===e.status,q=e=>"undefined"!=typeof Promise&&e instanceof Promise;class L{constructor(e,t,s,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=s,this._key=n}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let D=(e,t)=>{if(P(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new I(e.common.issues);return this._error=t,this._error}}};function F(e){if(!e)return{};let{errorMap:t,invalid_type_error:s,required_error:n,description:a}=e;if(t&&(s||n))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:a}:{errorMap:(t,a)=>{let{message:r}=e;return"invalid_enum_value"===t.code?{message:r??a.defaultError}:void 0===a.data?{message:r??n??a.defaultError}:"invalid_type"!==t.code?{message:a.defaultError}:{message:r??s??a.defaultError}},description:a}}class V{get description(){return this._def.description}_getType(e){return j(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:j(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new A,ctx:{common:e.parent.common,data:e.data,parsedType:j(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(q(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let s=this.safeParse(e,t);if(s.success)return s.data;throw s.error}safeParse(e,t){let s={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:j(e)},n=this._parseSync({data:e,path:s.path,parent:s});return D(s,n)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:j(e)};if(!this["~standard"].async)try{let s=this._parseSync({data:e,path:[],parent:t});return P(s)?{value:s.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>P(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let s=await this.safeParseAsync(e,t);if(s.success)return s.data;throw s.error}async safeParseAsync(e,t){let s={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:j(e)},n=this._parse({data:e,path:s.path,parent:s});return D(s,await (q(n)?n:Promise.resolve(n)))}refine(e,t){let s=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,n)=>{let a=e(t),r=()=>n.addIssue({code:C.custom,...s(t)});return"undefined"!=typeof Promise&&a instanceof Promise?a.then(e=>!!e||(r(),!1)):!!a||(r(),!1)})}refinement(e,t){return this._refinement((s,n)=>!!e(s)||(n.addIssue("function"==typeof t?t(s,n):t),!1))}_refinement(e){return new eE({schema:this,typeName:o.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return eM.create(this,this._def)}nullable(){return eP.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ev.create(this)}promise(){return eZ.create(this,this._def)}or(e){return ew.create([this,e],this._def)}and(e){return ez.create(this,e,this._def)}transform(e){return new eE({...F(this._def),schema:this,typeName:o.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new eq({...F(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:o.ZodDefault})}brand(){return new eF({typeName:o.ZodBranded,type:this,...F(this._def)})}catch(e){return new eL({...F(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:o.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return eV.create(this,e)}readonly(){return eU.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let U=/^c[^\s-]{8,}$/i,K=/^[0-9a-z]+$/,B=/^[0-9A-HJKMNP-TV-Z]{26}$/i,W=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,H=/^[a-z0-9_-]{21}$/i,G=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,J=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Y=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,X=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Q=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ee=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,et=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,es=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,en=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,ea="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",er=RegExp(`^${ea}$`);function ei(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let s=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${s}`}class eo extends V{_parse(e){var t,s,r,i;let o;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==T.string){let t=this._getOrReturnCtx(e);return O(t,{code:C.invalid_type,expected:T.string,received:t.parsedType}),R}let l=new A;for(let u of this._def.checks)if("min"===u.kind)e.data.length<u.value&&(O(o=this._getOrReturnCtx(e,o),{code:C.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),l.dirty());else if("max"===u.kind)e.data.length>u.value&&(O(o=this._getOrReturnCtx(e,o),{code:C.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),l.dirty());else if("length"===u.kind){let t=e.data.length>u.value,s=e.data.length<u.value;(t||s)&&(o=this._getOrReturnCtx(e,o),t?O(o,{code:C.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}):s&&O(o,{code:C.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}),l.dirty())}else if("email"===u.kind)Y.test(e.data)||(O(o=this._getOrReturnCtx(e,o),{validation:"email",code:C.invalid_string,message:u.message}),l.dirty());else if("emoji"===u.kind)n||(n=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),n.test(e.data)||(O(o=this._getOrReturnCtx(e,o),{validation:"emoji",code:C.invalid_string,message:u.message}),l.dirty());else if("uuid"===u.kind)W.test(e.data)||(O(o=this._getOrReturnCtx(e,o),{validation:"uuid",code:C.invalid_string,message:u.message}),l.dirty());else if("nanoid"===u.kind)H.test(e.data)||(O(o=this._getOrReturnCtx(e,o),{validation:"nanoid",code:C.invalid_string,message:u.message}),l.dirty());else if("cuid"===u.kind)U.test(e.data)||(O(o=this._getOrReturnCtx(e,o),{validation:"cuid",code:C.invalid_string,message:u.message}),l.dirty());else if("cuid2"===u.kind)K.test(e.data)||(O(o=this._getOrReturnCtx(e,o),{validation:"cuid2",code:C.invalid_string,message:u.message}),l.dirty());else if("ulid"===u.kind)B.test(e.data)||(O(o=this._getOrReturnCtx(e,o),{validation:"ulid",code:C.invalid_string,message:u.message}),l.dirty());else if("url"===u.kind)try{new URL(e.data)}catch{O(o=this._getOrReturnCtx(e,o),{validation:"url",code:C.invalid_string,message:u.message}),l.dirty()}else"regex"===u.kind?(u.regex.lastIndex=0,u.regex.test(e.data)||(O(o=this._getOrReturnCtx(e,o),{validation:"regex",code:C.invalid_string,message:u.message}),l.dirty())):"trim"===u.kind?e.data=e.data.trim():"includes"===u.kind?e.data.includes(u.value,u.position)||(O(o=this._getOrReturnCtx(e,o),{code:C.invalid_string,validation:{includes:u.value,position:u.position},message:u.message}),l.dirty()):"toLowerCase"===u.kind?e.data=e.data.toLowerCase():"toUpperCase"===u.kind?e.data=e.data.toUpperCase():"startsWith"===u.kind?e.data.startsWith(u.value)||(O(o=this._getOrReturnCtx(e,o),{code:C.invalid_string,validation:{startsWith:u.value},message:u.message}),l.dirty()):"endsWith"===u.kind?e.data.endsWith(u.value)||(O(o=this._getOrReturnCtx(e,o),{code:C.invalid_string,validation:{endsWith:u.value},message:u.message}),l.dirty()):"datetime"===u.kind?(function(e){let t=`${ea}T${ei(e)}`,s=[];return s.push(e.local?"Z?":"Z"),e.offset&&s.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${s.join("|")})`,RegExp(`^${t}$`)})(u).test(e.data)||(O(o=this._getOrReturnCtx(e,o),{code:C.invalid_string,validation:"datetime",message:u.message}),l.dirty()):"date"===u.kind?er.test(e.data)||(O(o=this._getOrReturnCtx(e,o),{code:C.invalid_string,validation:"date",message:u.message}),l.dirty()):"time"===u.kind?RegExp(`^${ei(u)}$`).test(e.data)||(O(o=this._getOrReturnCtx(e,o),{code:C.invalid_string,validation:"time",message:u.message}),l.dirty()):"duration"===u.kind?J.test(e.data)||(O(o=this._getOrReturnCtx(e,o),{validation:"duration",code:C.invalid_string,message:u.message}),l.dirty()):"ip"===u.kind?(t=e.data,!(("v4"===(s=u.version)||!s)&&X.test(t)||("v6"===s||!s)&&ee.test(t))&&1&&(O(o=this._getOrReturnCtx(e,o),{validation:"ip",code:C.invalid_string,message:u.message}),l.dirty())):"jwt"===u.kind?!function(e,t){if(!G.test(e))return!1;try{let[s]=e.split(".");if(!s)return!1;let n=s.replace(/-/g,"+").replace(/_/g,"/").padEnd(s.length+(4-s.length%4)%4,"="),a=JSON.parse(atob(n));if("object"!=typeof a||null===a||"typ"in a&&a?.typ!=="JWT"||!a.alg||t&&a.alg!==t)return!1;return!0}catch{return!1}}(e.data,u.alg)&&(O(o=this._getOrReturnCtx(e,o),{validation:"jwt",code:C.invalid_string,message:u.message}),l.dirty()):"cidr"===u.kind?(r=e.data,!(("v4"===(i=u.version)||!i)&&Q.test(r)||("v6"===i||!i)&&et.test(r))&&1&&(O(o=this._getOrReturnCtx(e,o),{validation:"cidr",code:C.invalid_string,message:u.message}),l.dirty())):"base64"===u.kind?es.test(e.data)||(O(o=this._getOrReturnCtx(e,o),{validation:"base64",code:C.invalid_string,message:u.message}),l.dirty()):"base64url"===u.kind?en.test(e.data)||(O(o=this._getOrReturnCtx(e,o),{validation:"base64url",code:C.invalid_string,message:u.message}),l.dirty()):a.assertNever(u);return{status:l.value,value:e.data}}_regex(e,t,s){return this.refinement(t=>e.test(t),{validation:t,code:C.invalid_string,...i.errToObj(s)})}_addCheck(e){return new eo({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...i.errToObj(e)})}url(e){return this._addCheck({kind:"url",...i.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...i.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...i.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...i.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...i.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...i.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...i.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...i.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...i.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...i.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...i.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...i.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...i.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...i.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...i.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...i.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...i.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...i.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...i.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...i.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...i.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...i.errToObj(t)})}nonempty(e){return this.min(1,i.errToObj(e))}trim(){return new eo({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new eo({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new eo({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}eo.create=e=>new eo({checks:[],typeName:o.ZodString,coerce:e?.coerce??!1,...F(e)});class el extends V{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==T.number){let t=this._getOrReturnCtx(e);return O(t,{code:C.invalid_type,expected:T.number,received:t.parsedType}),R}let s=new A;for(let n of this._def.checks)"int"===n.kind?a.isInteger(e.data)||(O(t=this._getOrReturnCtx(e,t),{code:C.invalid_type,expected:"integer",received:"float",message:n.message}),s.dirty()):"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(O(t=this._getOrReturnCtx(e,t),{code:C.too_small,minimum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),s.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(O(t=this._getOrReturnCtx(e,t),{code:C.too_big,maximum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),s.dirty()):"multipleOf"===n.kind?0!==function(e,t){let s=(e.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,a=s>n?s:n;return Number.parseInt(e.toFixed(a).replace(".",""))%Number.parseInt(t.toFixed(a).replace(".",""))/10**a}(e.data,n.value)&&(O(t=this._getOrReturnCtx(e,t),{code:C.not_multiple_of,multipleOf:n.value,message:n.message}),s.dirty()):"finite"===n.kind?Number.isFinite(e.data)||(O(t=this._getOrReturnCtx(e,t),{code:C.not_finite,message:n.message}),s.dirty()):a.assertNever(n);return{status:s.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,i.toString(t))}gt(e,t){return this.setLimit("min",e,!1,i.toString(t))}lte(e,t){return this.setLimit("max",e,!0,i.toString(t))}lt(e,t){return this.setLimit("max",e,!1,i.toString(t))}setLimit(e,t,s,n){return new el({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:i.toString(n)}]})}_addCheck(e){return new el({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:i.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:i.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:i.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:i.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:i.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:i.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:i.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:i.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:i.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&a.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let s of this._def.checks)if("finite"===s.kind||"int"===s.kind||"multipleOf"===s.kind)return!0;else"min"===s.kind?(null===t||s.value>t)&&(t=s.value):"max"===s.kind&&(null===e||s.value<e)&&(e=s.value);return Number.isFinite(t)&&Number.isFinite(e)}}el.create=e=>new el({checks:[],typeName:o.ZodNumber,coerce:e?.coerce||!1,...F(e)});class eu extends V{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==T.bigint)return this._getInvalidInput(e);let s=new A;for(let n of this._def.checks)"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(O(t=this._getOrReturnCtx(e,t),{code:C.too_small,type:"bigint",minimum:n.value,inclusive:n.inclusive,message:n.message}),s.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(O(t=this._getOrReturnCtx(e,t),{code:C.too_big,type:"bigint",maximum:n.value,inclusive:n.inclusive,message:n.message}),s.dirty()):"multipleOf"===n.kind?e.data%n.value!==BigInt(0)&&(O(t=this._getOrReturnCtx(e,t),{code:C.not_multiple_of,multipleOf:n.value,message:n.message}),s.dirty()):a.assertNever(n);return{status:s.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return O(t,{code:C.invalid_type,expected:T.bigint,received:t.parsedType}),R}gte(e,t){return this.setLimit("min",e,!0,i.toString(t))}gt(e,t){return this.setLimit("min",e,!1,i.toString(t))}lte(e,t){return this.setLimit("max",e,!0,i.toString(t))}lt(e,t){return this.setLimit("max",e,!1,i.toString(t))}setLimit(e,t,s,n){return new eu({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:i.toString(n)}]})}_addCheck(e){return new eu({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:i.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:i.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:i.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:i.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:i.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}eu.create=e=>new eu({checks:[],typeName:o.ZodBigInt,coerce:e?.coerce??!1,...F(e)});class ed extends V{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==T.boolean){let t=this._getOrReturnCtx(e);return O(t,{code:C.invalid_type,expected:T.boolean,received:t.parsedType}),R}return Z(e.data)}}ed.create=e=>new ed({typeName:o.ZodBoolean,coerce:e?.coerce||!1,...F(e)});class ec extends V{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==T.date){let t=this._getOrReturnCtx(e);return O(t,{code:C.invalid_type,expected:T.date,received:t.parsedType}),R}if(Number.isNaN(e.data.getTime()))return O(this._getOrReturnCtx(e),{code:C.invalid_date}),R;let s=new A;for(let n of this._def.checks)"min"===n.kind?e.data.getTime()<n.value&&(O(t=this._getOrReturnCtx(e,t),{code:C.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.value,type:"date"}),s.dirty()):"max"===n.kind?e.data.getTime()>n.value&&(O(t=this._getOrReturnCtx(e,t),{code:C.too_big,message:n.message,inclusive:!0,exact:!1,maximum:n.value,type:"date"}),s.dirty()):a.assertNever(n);return{status:s.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ec({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:i.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:i.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}ec.create=e=>new ec({checks:[],coerce:e?.coerce||!1,typeName:o.ZodDate,...F(e)});class ep extends V{_parse(e){if(this._getType(e)!==T.symbol){let t=this._getOrReturnCtx(e);return O(t,{code:C.invalid_type,expected:T.symbol,received:t.parsedType}),R}return Z(e.data)}}ep.create=e=>new ep({typeName:o.ZodSymbol,...F(e)});class eh extends V{_parse(e){if(this._getType(e)!==T.undefined){let t=this._getOrReturnCtx(e);return O(t,{code:C.invalid_type,expected:T.undefined,received:t.parsedType}),R}return Z(e.data)}}eh.create=e=>new eh({typeName:o.ZodUndefined,...F(e)});class em extends V{_parse(e){if(this._getType(e)!==T.null){let t=this._getOrReturnCtx(e);return O(t,{code:C.invalid_type,expected:T.null,received:t.parsedType}),R}return Z(e.data)}}em.create=e=>new em({typeName:o.ZodNull,...F(e)});class ef extends V{constructor(){super(...arguments),this._any=!0}_parse(e){return Z(e.data)}}ef.create=e=>new ef({typeName:o.ZodAny,...F(e)});class eg extends V{constructor(){super(...arguments),this._unknown=!0}_parse(e){return Z(e.data)}}eg.create=e=>new eg({typeName:o.ZodUnknown,...F(e)});class ey extends V{_parse(e){let t=this._getOrReturnCtx(e);return O(t,{code:C.invalid_type,expected:T.never,received:t.parsedType}),R}}ey.create=e=>new ey({typeName:o.ZodNever,...F(e)});class e_ extends V{_parse(e){if(this._getType(e)!==T.undefined){let t=this._getOrReturnCtx(e);return O(t,{code:C.invalid_type,expected:T.void,received:t.parsedType}),R}return Z(e.data)}}e_.create=e=>new e_({typeName:o.ZodVoid,...F(e)});class ev extends V{_parse(e){let{ctx:t,status:s}=this._processInputParams(e),n=this._def;if(t.parsedType!==T.array)return O(t,{code:C.invalid_type,expected:T.array,received:t.parsedType}),R;if(null!==n.exactLength){let e=t.data.length>n.exactLength.value,a=t.data.length<n.exactLength.value;(e||a)&&(O(t,{code:e?C.too_big:C.too_small,minimum:a?n.exactLength.value:void 0,maximum:e?n.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:n.exactLength.message}),s.dirty())}if(null!==n.minLength&&t.data.length<n.minLength.value&&(O(t,{code:C.too_small,minimum:n.minLength.value,type:"array",inclusive:!0,exact:!1,message:n.minLength.message}),s.dirty()),null!==n.maxLength&&t.data.length>n.maxLength.value&&(O(t,{code:C.too_big,maximum:n.maxLength.value,type:"array",inclusive:!0,exact:!1,message:n.maxLength.message}),s.dirty()),t.common.async)return Promise.all([...t.data].map((e,s)=>n.type._parseAsync(new L(t,e,t.path,s)))).then(e=>A.mergeArray(s,e));let a=[...t.data].map((e,s)=>n.type._parseSync(new L(t,e,t.path,s)));return A.mergeArray(s,a)}get element(){return this._def.type}min(e,t){return new ev({...this._def,minLength:{value:e,message:i.toString(t)}})}max(e,t){return new ev({...this._def,maxLength:{value:e,message:i.toString(t)}})}length(e,t){return new ev({...this._def,exactLength:{value:e,message:i.toString(t)}})}nonempty(e){return this.min(1,e)}}ev.create=(e,t)=>new ev({type:e,minLength:null,maxLength:null,exactLength:null,typeName:o.ZodArray,...F(t)});class eb extends V{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=a.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==T.object){let t=this._getOrReturnCtx(e);return O(t,{code:C.invalid_type,expected:T.object,received:t.parsedType}),R}let{status:t,ctx:s}=this._processInputParams(e),{shape:n,keys:a}=this._getCached(),r=[];if(!(this._def.catchall instanceof ey&&"strip"===this._def.unknownKeys))for(let e in s.data)a.includes(e)||r.push(e);let i=[];for(let e of a){let t=n[e],a=s.data[e];i.push({key:{status:"valid",value:e},value:t._parse(new L(s,a,s.path,e)),alwaysSet:e in s.data})}if(this._def.catchall instanceof ey){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of r)i.push({key:{status:"valid",value:e},value:{status:"valid",value:s.data[e]}});else if("strict"===e)r.length>0&&(O(s,{code:C.unrecognized_keys,keys:r}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of r){let n=s.data[t];i.push({key:{status:"valid",value:t},value:e._parse(new L(s,n,s.path,t)),alwaysSet:t in s.data})}}return s.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of i){let s=await t.key,n=await t.value;e.push({key:s,value:n,alwaysSet:t.alwaysSet})}return e}).then(e=>A.mergeObjectSync(t,e)):A.mergeObjectSync(t,i)}get shape(){return this._def.shape()}strict(e){return i.errToObj,new eb({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,s)=>{let n=this._def.errorMap?.(t,s).message??s.defaultError;return"unrecognized_keys"===t.code?{message:i.errToObj(e).message??n}:{message:n}}}:{}})}strip(){return new eb({...this._def,unknownKeys:"strip"})}passthrough(){return new eb({...this._def,unknownKeys:"passthrough"})}extend(e){return new eb({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new eb({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:o.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new eb({...this._def,catchall:e})}pick(e){let t={};for(let s of a.objectKeys(e))e[s]&&this.shape[s]&&(t[s]=this.shape[s]);return new eb({...this._def,shape:()=>t})}omit(e){let t={};for(let s of a.objectKeys(this.shape))e[s]||(t[s]=this.shape[s]);return new eb({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof eb){let s={};for(let n in t.shape){let a=t.shape[n];s[n]=eM.create(e(a))}return new eb({...t._def,shape:()=>s})}if(t instanceof ev)return new ev({...t._def,type:e(t.element)});if(t instanceof eM)return eM.create(e(t.unwrap()));if(t instanceof eP)return eP.create(e(t.unwrap()));if(t instanceof eT)return eT.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let s of a.objectKeys(this.shape)){let n=this.shape[s];e&&!e[s]?t[s]=n:t[s]=n.optional()}return new eb({...this._def,shape:()=>t})}required(e){let t={};for(let s of a.objectKeys(this.shape))if(e&&!e[s])t[s]=this.shape[s];else{let e=this.shape[s];for(;e instanceof eM;)e=e._def.innerType;t[s]=e}return new eb({...this._def,shape:()=>t})}keyof(){return eA(a.objectKeys(this.shape))}}eb.create=(e,t)=>new eb({shape:()=>e,unknownKeys:"strip",catchall:ey.create(),typeName:o.ZodObject,...F(t)}),eb.strictCreate=(e,t)=>new eb({shape:()=>e,unknownKeys:"strict",catchall:ey.create(),typeName:o.ZodObject,...F(t)}),eb.lazycreate=(e,t)=>new eb({shape:e,unknownKeys:"strip",catchall:ey.create(),typeName:o.ZodObject,...F(t)});class ew extends V{_parse(e){let{ctx:t}=this._processInputParams(e),s=this._def.options;if(t.common.async)return Promise.all(s.map(async e=>{let s={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:s}),ctx:s}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let s of e)if("dirty"===s.result.status)return t.common.issues.push(...s.ctx.common.issues),s.result;let s=e.map(e=>new I(e.ctx.common.issues));return O(t,{code:C.invalid_union,unionErrors:s}),R});{let e,n=[];for(let a of s){let s={...t,common:{...t.common,issues:[]},parent:null},r=a._parseSync({data:t.data,path:t.path,parent:s});if("valid"===r.status)return r;"dirty"!==r.status||e||(e={result:r,ctx:s}),s.common.issues.length&&n.push(s.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let a=n.map(e=>new I(e));return O(t,{code:C.invalid_union,unionErrors:a}),R}}get options(){return this._def.options}}ew.create=(e,t)=>new ew({options:e,typeName:o.ZodUnion,...F(t)});let ek=e=>{if(e instanceof eN)return ek(e.schema);if(e instanceof eE)return ek(e.innerType());if(e instanceof eO)return[e.value];if(e instanceof eR)return e.options;if(e instanceof e$)return a.objectValues(e.enum);else if(e instanceof eq)return ek(e._def.innerType);else if(e instanceof eh)return[void 0];else if(e instanceof em)return[null];else if(e instanceof eM)return[void 0,...ek(e.unwrap())];else if(e instanceof eP)return[null,...ek(e.unwrap())];else if(e instanceof eF)return ek(e.unwrap());else if(e instanceof eU)return ek(e.unwrap());else if(e instanceof eL)return ek(e._def.innerType);else return[]};class ex extends V{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==T.object)return O(t,{code:C.invalid_type,expected:T.object,received:t.parsedType}),R;let s=this.discriminator,n=t.data[s],a=this.optionsMap.get(n);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(O(t,{code:C.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[s]}),R)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,s){let n=new Map;for(let s of t){let t=ek(s.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let a of t){if(n.has(a))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(a)}`);n.set(a,s)}}return new ex({typeName:o.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:n,...F(s)})}}class ez extends V{_parse(e){let{status:t,ctx:s}=this._processInputParams(e),n=(e,n)=>{if(E(e)||E(n))return R;let r=function e(t,s){let n=j(t),r=j(s);if(t===s)return{valid:!0,data:t};if(n===T.object&&r===T.object){let n=a.objectKeys(s),r=a.objectKeys(t).filter(e=>-1!==n.indexOf(e)),i={...t,...s};for(let n of r){let a=e(t[n],s[n]);if(!a.valid)return{valid:!1};i[n]=a.data}return{valid:!0,data:i}}if(n===T.array&&r===T.array){if(t.length!==s.length)return{valid:!1};let n=[];for(let a=0;a<t.length;a++){let r=e(t[a],s[a]);if(!r.valid)return{valid:!1};n.push(r.data)}return{valid:!0,data:n}}if(n===T.date&&r===T.date&&+t==+s)return{valid:!0,data:t};return{valid:!1}}(e.value,n.value);return r.valid?((M(e)||M(n))&&t.dirty(),{status:t.value,value:r.data}):(O(s,{code:C.invalid_intersection_types}),R)};return s.common.async?Promise.all([this._def.left._parseAsync({data:s.data,path:s.path,parent:s}),this._def.right._parseAsync({data:s.data,path:s.path,parent:s})]).then(([e,t])=>n(e,t)):n(this._def.left._parseSync({data:s.data,path:s.path,parent:s}),this._def.right._parseSync({data:s.data,path:s.path,parent:s}))}}ez.create=(e,t,s)=>new ez({left:e,right:t,typeName:o.ZodIntersection,...F(s)});class eT extends V{_parse(e){let{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==T.array)return O(s,{code:C.invalid_type,expected:T.array,received:s.parsedType}),R;if(s.data.length<this._def.items.length)return O(s,{code:C.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),R;!this._def.rest&&s.data.length>this._def.items.length&&(O(s,{code:C.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let n=[...s.data].map((e,t)=>{let n=this._def.items[t]||this._def.rest;return n?n._parse(new L(s,e,s.path,t)):null}).filter(e=>!!e);return s.common.async?Promise.all(n).then(e=>A.mergeArray(t,e)):A.mergeArray(t,n)}get items(){return this._def.items}rest(e){return new eT({...this._def,rest:e})}}eT.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new eT({items:e,typeName:o.ZodTuple,rest:null,...F(t)})};class ej extends V{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==T.object)return O(s,{code:C.invalid_type,expected:T.object,received:s.parsedType}),R;let n=[],a=this._def.keyType,r=this._def.valueType;for(let e in s.data)n.push({key:a._parse(new L(s,e,s.path,e)),value:r._parse(new L(s,s.data[e],s.path,e)),alwaysSet:e in s.data});return s.common.async?A.mergeObjectAsync(t,n):A.mergeObjectSync(t,n)}get element(){return this._def.valueType}static create(e,t,s){return new ej(t instanceof V?{keyType:e,valueType:t,typeName:o.ZodRecord,...F(s)}:{keyType:eo.create(),valueType:e,typeName:o.ZodRecord,...F(t)})}}class eC extends V{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==T.map)return O(s,{code:C.invalid_type,expected:T.map,received:s.parsedType}),R;let n=this._def.keyType,a=this._def.valueType,r=[...s.data.entries()].map(([e,t],r)=>({key:n._parse(new L(s,e,s.path,[r,"key"])),value:a._parse(new L(s,t,s.path,[r,"value"]))}));if(s.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let s of r){let n=await s.key,a=await s.value;if("aborted"===n.status||"aborted"===a.status)return R;("dirty"===n.status||"dirty"===a.status)&&t.dirty(),e.set(n.value,a.value)}return{status:t.value,value:e}})}{let e=new Map;for(let s of r){let n=s.key,a=s.value;if("aborted"===n.status||"aborted"===a.status)return R;("dirty"===n.status||"dirty"===a.status)&&t.dirty(),e.set(n.value,a.value)}return{status:t.value,value:e}}}}eC.create=(e,t,s)=>new eC({valueType:t,keyType:e,typeName:o.ZodMap,...F(s)});class eI extends V{_parse(e){let{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==T.set)return O(s,{code:C.invalid_type,expected:T.set,received:s.parsedType}),R;let n=this._def;null!==n.minSize&&s.data.size<n.minSize.value&&(O(s,{code:C.too_small,minimum:n.minSize.value,type:"set",inclusive:!0,exact:!1,message:n.minSize.message}),t.dirty()),null!==n.maxSize&&s.data.size>n.maxSize.value&&(O(s,{code:C.too_big,maximum:n.maxSize.value,type:"set",inclusive:!0,exact:!1,message:n.maxSize.message}),t.dirty());let a=this._def.valueType;function r(e){let s=new Set;for(let n of e){if("aborted"===n.status)return R;"dirty"===n.status&&t.dirty(),s.add(n.value)}return{status:t.value,value:s}}let i=[...s.data.values()].map((e,t)=>a._parse(new L(s,e,s.path,t)));return s.common.async?Promise.all(i).then(e=>r(e)):r(i)}min(e,t){return new eI({...this._def,minSize:{value:e,message:i.toString(t)}})}max(e,t){return new eI({...this._def,maxSize:{value:e,message:i.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}eI.create=(e,t)=>new eI({valueType:e,minSize:null,maxSize:null,typeName:o.ZodSet,...F(t)});class eS extends V{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==T.function)return O(t,{code:C.invalid_type,expected:T.function,received:t.parsedType}),R;function s(e,s){return N({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,S,S].filter(e=>!!e),issueData:{code:C.invalid_arguments,argumentsError:s}})}function n(e,s){return N({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,S,S].filter(e=>!!e),issueData:{code:C.invalid_return_type,returnTypeError:s}})}let a={errorMap:t.common.contextualErrorMap},r=t.data;if(this._def.returns instanceof eZ){let e=this;return Z(async function(...t){let i=new I([]),o=await e._def.args.parseAsync(t,a).catch(e=>{throw i.addIssue(s(t,e)),i}),l=await Reflect.apply(r,this,o);return await e._def.returns._def.type.parseAsync(l,a).catch(e=>{throw i.addIssue(n(l,e)),i})})}{let e=this;return Z(function(...t){let i=e._def.args.safeParse(t,a);if(!i.success)throw new I([s(t,i.error)]);let o=Reflect.apply(r,this,i.data),l=e._def.returns.safeParse(o,a);if(!l.success)throw new I([n(o,l.error)]);return l.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new eS({...this._def,args:eT.create(e).rest(eg.create())})}returns(e){return new eS({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,s){return new eS({args:e||eT.create([]).rest(eg.create()),returns:t||eg.create(),typeName:o.ZodFunction,...F(s)})}}class eN extends V{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}eN.create=(e,t)=>new eN({getter:e,typeName:o.ZodLazy,...F(t)});class eO extends V{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return O(t,{received:t.data,code:C.invalid_literal,expected:this._def.value}),R}return{status:"valid",value:e.data}}get value(){return this._def.value}}function eA(e,t){return new eR({values:e,typeName:o.ZodEnum,...F(t)})}eO.create=(e,t)=>new eO({value:e,typeName:o.ZodLiteral,...F(t)});class eR extends V{_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),s=this._def.values;return O(t,{expected:a.joinValues(s),received:t.parsedType,code:C.invalid_type}),R}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){let t=this._getOrReturnCtx(e),s=this._def.values;return O(t,{received:t.data,code:C.invalid_enum_value,options:s}),R}return Z(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return eR.create(e,{...this._def,...t})}exclude(e,t=this._def){return eR.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}eR.create=eA;class e$ extends V{_parse(e){let t=a.getValidEnumValues(this._def.values),s=this._getOrReturnCtx(e);if(s.parsedType!==T.string&&s.parsedType!==T.number){let e=a.objectValues(t);return O(s,{expected:a.joinValues(e),received:s.parsedType,code:C.invalid_type}),R}if(this._cache||(this._cache=new Set(a.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){let e=a.objectValues(t);return O(s,{received:s.data,code:C.invalid_enum_value,options:e}),R}return Z(e.data)}get enum(){return this._def.values}}e$.create=(e,t)=>new e$({values:e,typeName:o.ZodNativeEnum,...F(t)});class eZ extends V{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==T.promise&&!1===t.common.async?(O(t,{code:C.invalid_type,expected:T.promise,received:t.parsedType}),R):Z((t.parsedType===T.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}eZ.create=(e,t)=>new eZ({type:e,typeName:o.ZodPromise,...F(t)});class eE extends V{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===o.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:s}=this._processInputParams(e),n=this._def.effect||null,r={addIssue:e=>{O(s,e),e.fatal?t.abort():t.dirty()},get path(){return s.path}};if(r.addIssue=r.addIssue.bind(r),"preprocess"===n.type){let e=n.transform(s.data,r);if(s.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return R;let n=await this._def.schema._parseAsync({data:e,path:s.path,parent:s});return"aborted"===n.status?R:"dirty"===n.status||"dirty"===t.value?$(n.value):n});{if("aborted"===t.value)return R;let n=this._def.schema._parseSync({data:e,path:s.path,parent:s});return"aborted"===n.status?R:"dirty"===n.status||"dirty"===t.value?$(n.value):n}}if("refinement"===n.type){let e=e=>{let t=n.refinement(e,r);if(s.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==s.common.async)return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(s=>"aborted"===s.status?R:("dirty"===s.status&&t.dirty(),e(s.value).then(()=>({status:t.value,value:s.value}))));{let n=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});return"aborted"===n.status?R:("dirty"===n.status&&t.dirty(),e(n.value),{status:t.value,value:n.value})}}if("transform"===n.type)if(!1!==s.common.async)return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(e=>P(e)?Promise.resolve(n.transform(e.value,r)).then(e=>({status:t.value,value:e})):R);else{let e=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});if(!P(e))return R;let a=n.transform(e.value,r);if(a instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:a}}a.assertNever(n)}}eE.create=(e,t,s)=>new eE({schema:e,typeName:o.ZodEffects,effect:t,...F(s)}),eE.createWithPreprocess=(e,t,s)=>new eE({schema:t,effect:{type:"preprocess",transform:e},typeName:o.ZodEffects,...F(s)});class eM extends V{_parse(e){return this._getType(e)===T.undefined?Z(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eM.create=(e,t)=>new eM({innerType:e,typeName:o.ZodOptional,...F(t)});class eP extends V{_parse(e){return this._getType(e)===T.null?Z(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eP.create=(e,t)=>new eP({innerType:e,typeName:o.ZodNullable,...F(t)});class eq extends V{_parse(e){let{ctx:t}=this._processInputParams(e),s=t.data;return t.parsedType===T.undefined&&(s=this._def.defaultValue()),this._def.innerType._parse({data:s,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}eq.create=(e,t)=>new eq({innerType:e,typeName:o.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...F(t)});class eL extends V{_parse(e){let{ctx:t}=this._processInputParams(e),s={...t,common:{...t.common,issues:[]}},n=this._def.innerType._parse({data:s.data,path:s.path,parent:{...s}});return q(n)?n.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new I(s.common.issues)},input:s.data})})):{status:"valid",value:"valid"===n.status?n.value:this._def.catchValue({get error(){return new I(s.common.issues)},input:s.data})}}removeCatch(){return this._def.innerType}}eL.create=(e,t)=>new eL({innerType:e,typeName:o.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...F(t)});class eD extends V{_parse(e){if(this._getType(e)!==T.nan){let t=this._getOrReturnCtx(e);return O(t,{code:C.invalid_type,expected:T.nan,received:t.parsedType}),R}return{status:"valid",value:e.data}}}eD.create=e=>new eD({typeName:o.ZodNaN,...F(e)}),Symbol("zod_brand");class eF extends V{_parse(e){let{ctx:t}=this._processInputParams(e),s=t.data;return this._def.type._parse({data:s,path:t.path,parent:t})}unwrap(){return this._def.type}}class eV extends V{_parse(e){let{status:t,ctx:s}=this._processInputParams(e);if(s.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:s.data,path:s.path,parent:s});return"aborted"===e.status?R:"dirty"===e.status?(t.dirty(),$(e.value)):this._def.out._parseAsync({data:e.value,path:s.path,parent:s})})();{let e=this._def.in._parseSync({data:s.data,path:s.path,parent:s});return"aborted"===e.status?R:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:s.path,parent:s})}}static create(e,t){return new eV({in:e,out:t,typeName:o.ZodPipeline})}}class eU extends V{_parse(e){let t=this._def.innerType._parse(e),s=e=>(P(e)&&(e.value=Object.freeze(e.value)),e);return q(t)?t.then(e=>s(e)):s(t)}unwrap(){return this._def.innerType}}eU.create=(e,t)=>new eU({innerType:e,typeName:o.ZodReadonly,...F(t)}),eb.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(o||(o={}));let eK=eo.create,eB=el.create;eD.create,eu.create,ed.create,ec.create,ep.create,eh.create,em.create,ef.create;let eW=eg.create;ey.create,e_.create;let eH=ev.create,eG=eb.create;eb.strictCreate,ew.create;let eJ=ex.create;ez.create,eT.create;let eY=ej.create;eC.create,eI.create,eS.create,eN.create;let eX=eO.create;eR.create,e$.create,eZ.create,eE.create,eM.create,eP.create,eE.createWithPreprocess,eV.create;var eQ=eG({id:eK(),answer:eK(),task_id:eK(),conversation_id:eK(),message_id:eK(),metadata:eG({usage:eG({completion_tokens:eB(),prompt_tokens:eB(),total_tokens:eB()})})}),e0=eG({code:eK(),message:eK(),status:eB()}),e1=eG({event:eK(),conversation_id:eK().optional(),message_id:eK().optional(),task_id:eK().optional(),created_at:eB().optional()}).passthrough(),e9=e1.extend({event:eX("workflow_started"),workflow_run_id:eK(),data:eG({id:eK(),workflow_id:eK(),created_at:eB()}).passthrough()}),e4=e1.extend({event:eX("workflow_finished"),workflow_run_id:eK(),data:eG({id:eK(),workflow_id:eK(),total_tokens:eB().optional(),created_at:eB()}).passthrough()}),e2=e1.extend({event:eX("node_started"),workflow_run_id:eK(),data:eG({id:eK(),node_id:eK(),node_type:eK()}).passthrough()}),e3=e1.extend({event:eX("node_finished"),workflow_run_id:eK(),data:eG({id:eK(),node_id:eK(),node_type:eK()}).passthrough()}),e6=e1.extend({event:eX("message"),id:eK().optional(),answer:eK(),from_variable_selector:eH(eK()).optional()}),e5=e1.extend({event:eX("message_end"),id:eK(),metadata:eG({usage:eG({prompt_tokens:eB(),completion_tokens:eB(),total_tokens:eB()}).passthrough()}).passthrough(),files:eH(eW()).optional()}),e8=e1.extend({event:eX("tts_message"),audio:eK()}),e7=e1.extend({event:eX("tts_message_end"),audio:eK()}),te=e1.extend({event:eX("agent_message"),answer:eK()}),tt=eJ("event",[e9,e4,e2,e3,e6,e5,e8,e7,e1.extend({event:eX("agent_thought"),thought:eK(),observation:eK(),tool:eK(),tool_labels:eY(eK(),eK()),tool_input:eK(),message_files:eH(eW())}),te]).or(e1),ts=(({errorSchema:e,errorToMessage:t,isRetryable:s})=>async({response:n,url:a,requestBodyValues:r})=>{let i=await n.text(),o=p(n);if(""===i.trim())return{responseHeaders:o,value:new l.hL({message:n.statusText,url:a,requestBodyValues:r,statusCode:n.status,responseHeaders:o,responseBody:i,isRetryable:null==s?void 0:s(n)})};try{let u=function({text:e,schema:t}){try{let s=d.parse(e);if(null==t)return s;return function({value:e,schema:t}){let s=g({value:e,schema:t});if(!s.success)throw l.iM.wrap({value:e,cause:s.error});return s.value}({value:s,schema:t})}catch(t){if(l.u6.isInstance(t)||l.iM.isInstance(t))throw t;throw new l.u6({text:e,cause:t})}}({text:i,schema:e});return{responseHeaders:o,value:new l.hL({message:t(u),url:a,requestBodyValues:r,statusCode:n.status,responseHeaders:o,responseBody:i,data:u,isRetryable:null==s?void 0:s(n,u)})}}catch(e){return{responseHeaders:o,value:new l.hL({message:n.statusText,url:a,requestBodyValues:r,statusCode:n.status,responseHeaders:o,responseBody:i,isRetryable:null==s?void 0:s(n)})}}})({errorSchema:e0,errorToMessage:e=>(console.log("Dify API error:",e),`Dify API error: ${e.message}`)}),tn=class{constructor(e,t,s){this.settings=t,this.specificationVersion="v1",this.defaultObjectGenerationMode=void 0,this.modelId=e,this.config=s,this.generateId=h,this.chatMessagesEndpoint=`${this.config.baseURL}/chat-messages`,this.settings.responseMode||(this.settings.responseMode="streaming")}get provider(){return this.config.provider}async doGenerate(e){var t,s,n,a;let{abortSignal:r}=e,i=this.getRequestBody(e),{responseHeaders:o,value:l}=await v({url:this.chatMessagesEndpoint,headers:c(this.config.headers(),e.headers),body:i,abortSignal:r,failedResponseHandler:ts,successfulResponseHandler:k(eQ),fetch:this.config.fetch});return{text:l.answer,toolCalls:[],finishReason:"stop",usage:{promptTokens:(null==(s=null==(t=l.metadata)?void 0:t.usage)?void 0:s.prompt_tokens)||0,completionTokens:(null==(a=null==(n=l.metadata)?void 0:n.usage)?void 0:a.completion_tokens)||0},rawCall:this.createRawCall(e),providerMetadata:{difyWorkflowData:{conversationId:l.conversation_id,messageId:l.message_id}},rawResponse:{headers:o,body:l},request:{body:JSON.stringify(i)},response:{id:l.id,timestamp:new Date}}}async doStream(e){let t,s,n,{abortSignal:a}=e,r={...this.getRequestBody(e),response_mode:"streaming"},{responseHeaders:i,value:o}=await v({url:this.chatMessagesEndpoint,headers:c(this.config.headers(),e.headers),body:r,failedResponseHandler:ts,successfulResponseHandler:w(tt),abortSignal:a,fetch:this.config.fetch});return{stream:o.pipeThrough(new TransformStream({transform(e,a){if(!e.success)return void a.enqueue({type:"error",error:e.error});let r=e.value;switch(r.conversation_id&&(t=r.conversation_id),r.message_id&&(s=r.message_id),r.task_id&&(n=r.task_id),r.event){case"workflow_finished":case"message_end":{let e=0;"data"in r&&r.data&&"object"==typeof r.data&&"total_tokens"in r.data&&"number"==typeof r.data.total_tokens&&(e=r.data.total_tokens),a.enqueue({type:"finish",finishReason:"stop",providerMetadata:{difyWorkflowData:{conversationId:t,messageId:s,taskId:n}},usage:{promptTokens:0,completionTokens:e}});break}case"message":case"agent_message":"answer"in r&&"string"==typeof r.answer&&(a.enqueue({type:"text-delta",textDelta:r.answer}),"id"in r&&"string"==typeof r.id&&a.enqueue({type:"response-metadata",id:r.id}))}}})),rawCall:this.createRawCall(e),rawResponse:{headers:i},request:{body:JSON.stringify(r)}}}getRequestBody(e){var t,s,n;let a=e.messages||e.prompt;if(!a||!a.length)throw new l.hL({message:"No messages provided",url:this.chatMessagesEndpoint,requestBodyValues:e});let r=a[a.length-1];if("user"!==r.role)throw new l.hL({message:"The last message must be a user message",url:this.chatMessagesEndpoint,requestBodyValues:{latestMessageRole:r.role}});if(Array.isArray(r.content)&&r.content.some(e=>"string"!=typeof e&&"image"===e.type))throw new l.hL({message:"Dify provider does not currently support image attachments",url:this.chatMessagesEndpoint,requestBodyValues:{hasAttachments:!0}});let i="";"string"==typeof r.content?i=r.content:Array.isArray(r.content)&&(i=r.content.map(e=>"string"==typeof e?e:"text"===e.type?e.text:"").filter(Boolean).join(" "));let o=null==(t=e.headers)?void 0:t["chat-id"],u=null!=(n=null==(s=e.headers)?void 0:s["user-id"])?n:"you_should_pass_user-id",{"chat-id":d,"user-id":c,...p}=e.headers||{};return e.headers=p,{inputs:this.settings.inputs||{},query:i,response_mode:this.settings.responseMode,conversation_id:o,user:u}}createRawCall(e){return{rawPrompt:e.messages||e.prompt,rawSettings:{...this.settings}}}supportsUrl(e){return!1}};function ta(e={}){let t=(t,s={})=>new tn(t,s,{provider:"dify.chat",baseURL:e.baseURL||"https://api.dify.ai/v1",headers:()=>({Authorization:`Bearer ${function({apiKey:e,environmentVariableName:t,apiKeyParameterName:s="apiKey",description:n}){if("string"==typeof e)return e;if(null!=e)throw new l.Kq({message:`${n} API key must be a string.`});if("undefined"==typeof process)throw new l.Kq({message:`${n} API key is missing. Pass it using the '${s}' parameter. Environment variables is not supported in this environment.`});if(null==(e=process.env[t]))throw new l.Kq({message:`${n} API key is missing. Pass it using the '${s}' parameter or the ${t} environment variable.`});if("string"!=typeof e)throw new l.Kq({message:`${n} API key must be a string. The value of the ${t} environment variable is not a string.`});return e}({apiKey:s.apiKey,environmentVariableName:"DIFY_API_KEY",description:"Dify API Key"})}`,"Content-Type":"application/json",...e.headers})}),s=function(e,s){if(new.target)throw Error("The model factory function cannot be called with the new keyword.");return t(e,s)};return s.chat=t,s}ta()},94062:(e,t,s)=>{s.d(t,{N:()=>H});var n=s(86746),a=s(87113),r=s(25613);function i(e){var t,s;return null!=(s=null==(t=null==e?void 0:e.content)?void 0:t.map(({token:e,logprob:t,top_logprobs:s})=>({token:e,logprob:t,topLogprobs:s?s.map(({token:e,logprob:t})=>({token:e,logprob:t})):[]})))?s:void 0}function o(e){switch(e){case"stop":return"stop";case"length":return"length";case"content_filter":return"content-filter";case"function_call":case"tool_calls":return"tool-calls";default:return"unknown"}}var l=r.z.object({error:r.z.object({message:r.z.string(),type:r.z.string().nullish(),param:r.z.any().nullish(),code:r.z.union([r.z.string(),r.z.number()]).nullish()})}),u=(0,n.sl)({errorSchema:l,errorToMessage:e=>e.error.message});function d({id:e,model:t,created:s}){return{id:null!=e?e:void 0,modelId:null!=t?t:void 0,timestamp:null!=s?new Date(1e3*s):void 0}}var c=class{constructor(e,t,s){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=s}get supportsStructuredOutputs(){var e;return null!=(e=this.settings.structuredOutputs)?e:f(this.modelId)}get defaultObjectGenerationMode(){return this.modelId.startsWith("gpt-4o-audio-preview")?"tool":this.supportsStructuredOutputs?"json":"tool"}get provider(){return this.config.provider}get supportsImageUrls(){return!this.settings.downloadImages}getArgs({mode:e,prompt:t,maxTokens:s,temperature:r,topP:i,topK:o,frequencyPenalty:l,presencePenalty:u,stopSequences:d,responseFormat:c,seed:p,providerMetadata:h}){var m,y,_,v,b,w,k,x,z,T,j;let C=e.type,I=[];null!=o&&I.push({type:"unsupported-setting",setting:"topK"}),(null==c?void 0:c.type)!=="json"||null==c.schema||this.supportsStructuredOutputs||I.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format schema is only supported with structuredOutputs"});let S=this.settings.useLegacyFunctionCalling;if(S&&!0===this.settings.parallelToolCalls)throw new a.b8({functionality:"useLegacyFunctionCalling with parallelToolCalls"});if(S&&this.supportsStructuredOutputs)throw new a.b8({functionality:"structuredOutputs with useLegacyFunctionCalling"});let{messages:N,warnings:O}=function({prompt:e,useLegacyFunctionCalling:t=!1,systemMessageMode:s="system"}){let r=[],i=[];for(let{role:o,content:l}of e)switch(o){case"system":switch(s){case"system":r.push({role:"system",content:l});break;case"developer":r.push({role:"developer",content:l});break;case"remove":i.push({type:"other",message:"system messages are removed for this model"});break;default:throw Error(`Unsupported system message mode: ${s}`)}break;case"user":if(1===l.length&&"text"===l[0].type){r.push({role:"user",content:l[0].text});break}r.push({role:"user",content:l.map((e,t)=>{var s,r,i,o;switch(e.type){case"text":return{type:"text",text:e.text};case"image":return{type:"image_url",image_url:{url:e.image instanceof URL?e.image.toString():`data:${null!=(s=e.mimeType)?s:"image/jpeg"};base64,${(0,n.n_)(e.image)}`,detail:null==(i=null==(r=e.providerMetadata)?void 0:r.openai)?void 0:i.imageDetail}};case"file":if(e.data instanceof URL)throw new a.b8({functionality:"'File content parts with URL data' functionality not supported."});switch(e.mimeType){case"audio/wav":return{type:"input_audio",input_audio:{data:e.data,format:"wav"}};case"audio/mp3":case"audio/mpeg":return{type:"input_audio",input_audio:{data:e.data,format:"mp3"}};case"application/pdf":return{type:"file",file:{filename:null!=(o=e.filename)?o:`part-${t}.pdf`,file_data:`data:application/pdf;base64,${e.data}`}};default:throw new a.b8({functionality:`File content part type ${e.mimeType} in user messages`})}}})});break;case"assistant":{let e="",s=[];for(let t of l)switch(t.type){case"text":e+=t.text;break;case"tool-call":s.push({id:t.toolCallId,type:"function",function:{name:t.toolName,arguments:JSON.stringify(t.args)}})}if(t){if(s.length>1)throw new a.b8({functionality:"useLegacyFunctionCalling with multiple tool calls in one message"});r.push({role:"assistant",content:e,function_call:s.length>0?s[0].function:void 0})}else r.push({role:"assistant",content:e,tool_calls:s.length>0?s:void 0});break}case"tool":for(let e of l)t?r.push({role:"function",name:e.toolName,content:JSON.stringify(e.result)}):r.push({role:"tool",tool_call_id:e.toolCallId,content:JSON.stringify(e.result)});break;default:throw Error(`Unsupported role: ${o}`)}return{messages:r,warnings:i}}({prompt:t,useLegacyFunctionCalling:S,systemMessageMode:f(z=this.modelId)?null!=(j=null==(T=g[z])?void 0:T.systemMessageMode)?j:"developer":"system"});I.push(...O);let A={model:this.modelId,logit_bias:this.settings.logitBias,logprobs:!0===this.settings.logprobs||"number"==typeof this.settings.logprobs||void 0,top_logprobs:"number"==typeof this.settings.logprobs?this.settings.logprobs:"boolean"==typeof this.settings.logprobs&&this.settings.logprobs?0:void 0,user:this.settings.user,parallel_tool_calls:this.settings.parallelToolCalls,max_tokens:s,temperature:r,top_p:i,frequency_penalty:l,presence_penalty:u,response_format:(null==c?void 0:c.type)==="json"?this.supportsStructuredOutputs&&null!=c.schema?{type:"json_schema",json_schema:{schema:c.schema,strict:!0,name:null!=(m=c.name)?m:"response",description:c.description}}:{type:"json_object"}:void 0,stop:d,seed:p,max_completion_tokens:null==(y=null==h?void 0:h.openai)?void 0:y.maxCompletionTokens,store:null==(_=null==h?void 0:h.openai)?void 0:_.store,metadata:null==(v=null==h?void 0:h.openai)?void 0:v.metadata,prediction:null==(b=null==h?void 0:h.openai)?void 0:b.prediction,reasoning_effort:null!=(k=null==(w=null==h?void 0:h.openai)?void 0:w.reasoningEffort)?k:this.settings.reasoningEffort,messages:N};switch(f(this.modelId)?(null!=A.temperature&&(A.temperature=void 0,I.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for reasoning models"})),null!=A.top_p&&(A.top_p=void 0,I.push({type:"unsupported-setting",setting:"topP",details:"topP is not supported for reasoning models"})),null!=A.frequency_penalty&&(A.frequency_penalty=void 0,I.push({type:"unsupported-setting",setting:"frequencyPenalty",details:"frequencyPenalty is not supported for reasoning models"})),null!=A.presence_penalty&&(A.presence_penalty=void 0,I.push({type:"unsupported-setting",setting:"presencePenalty",details:"presencePenalty is not supported for reasoning models"})),null!=A.logit_bias&&(A.logit_bias=void 0,I.push({type:"other",message:"logitBias is not supported for reasoning models"})),null!=A.logprobs&&(A.logprobs=void 0,I.push({type:"other",message:"logprobs is not supported for reasoning models"})),null!=A.top_logprobs&&(A.top_logprobs=void 0,I.push({type:"other",message:"topLogprobs is not supported for reasoning models"})),null!=A.max_tokens&&(null==A.max_completion_tokens&&(A.max_completion_tokens=A.max_tokens),A.max_tokens=void 0)):(this.modelId.startsWith("gpt-4o-search-preview")||this.modelId.startsWith("gpt-4o-mini-search-preview"))&&null!=A.temperature&&(A.temperature=void 0,I.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for the search preview models and has been removed."})),C){case"regular":{let{tools:t,tool_choice:s,functions:n,function_call:r,toolWarnings:i}=function({mode:e,useLegacyFunctionCalling:t=!1,structuredOutputs:s}){var n;let r=(null==(n=e.tools)?void 0:n.length)?e.tools:void 0,i=[];if(null==r)return{tools:void 0,tool_choice:void 0,toolWarnings:i};let o=e.toolChoice;if(t){let e=[];for(let t of r)"provider-defined"===t.type?i.push({type:"unsupported-tool",tool:t}):e.push({name:t.name,description:t.description,parameters:t.parameters});if(null==o)return{functions:e,function_call:void 0,toolWarnings:i};switch(o.type){case"auto":case"none":case void 0:return{functions:e,function_call:void 0,toolWarnings:i};case"required":throw new a.b8({functionality:"useLegacyFunctionCalling and toolChoice: required"});default:return{functions:e,function_call:{name:o.toolName},toolWarnings:i}}}let l=[];for(let e of r)"provider-defined"===e.type?i.push({type:"unsupported-tool",tool:e}):l.push({type:"function",function:{name:e.name,description:e.description,parameters:e.parameters,strict:!!s||void 0}});if(null==o)return{tools:l,tool_choice:void 0,toolWarnings:i};let u=o.type;switch(u){case"auto":case"none":case"required":return{tools:l,tool_choice:u,toolWarnings:i};case"tool":return{tools:l,tool_choice:{type:"function",function:{name:o.toolName}},toolWarnings:i};default:throw new a.b8({functionality:`Unsupported tool choice type: ${u}`})}}({mode:e,useLegacyFunctionCalling:S,structuredOutputs:this.supportsStructuredOutputs});return{args:{...A,tools:t,tool_choice:s,functions:n,function_call:r},warnings:[...I,...i]}}case"object-json":return{args:{...A,response_format:this.supportsStructuredOutputs&&null!=e.schema?{type:"json_schema",json_schema:{schema:e.schema,strict:!0,name:null!=(x=e.name)?x:"response",description:e.description}}:{type:"json_object"}},warnings:I};case"object-tool":return{args:S?{...A,function_call:{name:e.tool.name},functions:[{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters}]}:{...A,tool_choice:{type:"function",function:{name:e.tool.name}},tools:[{type:"function",function:{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters,strict:!!this.supportsStructuredOutputs||void 0}}]},warnings:I};default:throw Error(`Unsupported type: ${C}`)}}async doGenerate(e){var t,s,a,r,l,c,p,m;let{args:f,warnings:g}=this.getArgs(e),{responseHeaders:y,value:_,rawValue:v}=await (0,n.GU)({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:(0,n.m2)(this.config.headers(),e.headers),body:f,failedResponseHandler:u,successfulResponseHandler:(0,n.cV)(h),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:b,...w}=f,k=_.choices[0],x=null==(t=_.usage)?void 0:t.completion_tokens_details,z=null==(s=_.usage)?void 0:s.prompt_tokens_details,T={openai:{}};return(null==x?void 0:x.reasoning_tokens)!=null&&(T.openai.reasoningTokens=null==x?void 0:x.reasoning_tokens),(null==x?void 0:x.accepted_prediction_tokens)!=null&&(T.openai.acceptedPredictionTokens=null==x?void 0:x.accepted_prediction_tokens),(null==x?void 0:x.rejected_prediction_tokens)!=null&&(T.openai.rejectedPredictionTokens=null==x?void 0:x.rejected_prediction_tokens),(null==z?void 0:z.cached_tokens)!=null&&(T.openai.cachedPromptTokens=null==z?void 0:z.cached_tokens),{text:null!=(a=k.message.content)?a:void 0,toolCalls:this.settings.useLegacyFunctionCalling&&k.message.function_call?[{toolCallType:"function",toolCallId:(0,n.$C)(),toolName:k.message.function_call.name,args:k.message.function_call.arguments}]:null==(r=k.message.tool_calls)?void 0:r.map(e=>{var t;return{toolCallType:"function",toolCallId:null!=(t=e.id)?t:(0,n.$C)(),toolName:e.function.name,args:e.function.arguments}}),finishReason:o(k.finish_reason),usage:{promptTokens:null!=(c=null==(l=_.usage)?void 0:l.prompt_tokens)?c:NaN,completionTokens:null!=(m=null==(p=_.usage)?void 0:p.completion_tokens)?m:NaN},rawCall:{rawPrompt:b,rawSettings:w},rawResponse:{headers:y,body:v},request:{body:JSON.stringify(f)},response:d(_),warnings:g,logprobs:i(k.logprobs),providerMetadata:T}}async doStream(e){let t;if(this.settings.simulateStreaming){let t=await this.doGenerate(e);return{stream:new ReadableStream({start(e){if(e.enqueue({type:"response-metadata",...t.response}),t.text&&e.enqueue({type:"text-delta",textDelta:t.text}),t.toolCalls)for(let s of t.toolCalls)e.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:s.toolCallId,toolName:s.toolName,argsTextDelta:s.args}),e.enqueue({type:"tool-call",...s});e.enqueue({type:"finish",finishReason:t.finishReason,usage:t.usage,logprobs:t.logprobs,providerMetadata:t.providerMetadata}),e.close()}}),rawCall:t.rawCall,rawResponse:t.rawResponse,warnings:t.warnings}}let{args:s,warnings:r}=this.getArgs(e),l={...s,stream:!0,stream_options:"strict"===this.config.compatibility?{include_usage:!0}:void 0},{responseHeaders:c,value:p}=await (0,n.GU)({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:(0,n.m2)(this.config.headers(),e.headers),body:l,failedResponseHandler:u,successfulResponseHandler:(0,n.Ds)(m),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:h,...f}=s,g=[],y="unknown",_={promptTokens:void 0,completionTokens:void 0},v=!0,{useLegacyFunctionCalling:b}=this.settings,w={openai:{}};return{stream:p.pipeThrough(new TransformStream({transform(e,s){var r,l,u,c,p,h,m,f,k,x,z,T;if(!e.success){y="error",s.enqueue({type:"error",error:e.error});return}let j=e.value;if("error"in j){y="error",s.enqueue({type:"error",error:j.error});return}if(v&&(v=!1,s.enqueue({type:"response-metadata",...d(j)})),null!=j.usage){let{prompt_tokens:e,completion_tokens:t,prompt_tokens_details:s,completion_tokens_details:n}=j.usage;_={promptTokens:null!=e?e:void 0,completionTokens:null!=t?t:void 0},(null==n?void 0:n.reasoning_tokens)!=null&&(w.openai.reasoningTokens=null==n?void 0:n.reasoning_tokens),(null==n?void 0:n.accepted_prediction_tokens)!=null&&(w.openai.acceptedPredictionTokens=null==n?void 0:n.accepted_prediction_tokens),(null==n?void 0:n.rejected_prediction_tokens)!=null&&(w.openai.rejectedPredictionTokens=null==n?void 0:n.rejected_prediction_tokens),(null==s?void 0:s.cached_tokens)!=null&&(w.openai.cachedPromptTokens=null==s?void 0:s.cached_tokens)}let C=j.choices[0];if((null==C?void 0:C.finish_reason)!=null&&(y=o(C.finish_reason)),(null==C?void 0:C.delta)==null)return;let I=C.delta;null!=I.content&&s.enqueue({type:"text-delta",textDelta:I.content});let S=i(null==C?void 0:C.logprobs);(null==S?void 0:S.length)&&(void 0===t&&(t=[]),t.push(...S));let N=b&&null!=I.function_call?[{type:"function",id:(0,n.$C)(),function:I.function_call,index:0}]:I.tool_calls;if(null!=N)for(let e of N){let t=e.index;if(null==g[t]){if("function"!==e.type)throw new a.xn({data:e,message:"Expected 'function' type."});if(null==e.id)throw new a.xn({data:e,message:"Expected 'id' to be a string."});if((null==(r=e.function)?void 0:r.name)==null)throw new a.xn({data:e,message:"Expected 'function.name' to be a string."});g[t]={id:e.id,type:"function",function:{name:e.function.name,arguments:null!=(l=e.function.arguments)?l:""},hasFinished:!1};let i=g[t];(null==(u=i.function)?void 0:u.name)!=null&&(null==(c=i.function)?void 0:c.arguments)!=null&&(i.function.arguments.length>0&&s.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:i.id,toolName:i.function.name,argsTextDelta:i.function.arguments}),(0,n.v0)(i.function.arguments)&&(s.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(p=i.id)?p:(0,n.$C)(),toolName:i.function.name,args:i.function.arguments}),i.hasFinished=!0));continue}let i=g[t];!i.hasFinished&&((null==(h=e.function)?void 0:h.arguments)!=null&&(i.function.arguments+=null!=(f=null==(m=e.function)?void 0:m.arguments)?f:""),s.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:i.id,toolName:i.function.name,argsTextDelta:null!=(k=e.function.arguments)?k:""}),(null==(x=i.function)?void 0:x.name)!=null&&(null==(z=i.function)?void 0:z.arguments)!=null&&(0,n.v0)(i.function.arguments)&&(s.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(T=i.id)?T:(0,n.$C)(),toolName:i.function.name,args:i.function.arguments}),i.hasFinished=!0))}},flush(e){var s,n;e.enqueue({type:"finish",finishReason:y,logprobs:t,usage:{promptTokens:null!=(s=_.promptTokens)?s:NaN,completionTokens:null!=(n=_.completionTokens)?n:NaN},...null!=w?{providerMetadata:w}:{}})}})),rawCall:{rawPrompt:h,rawSettings:f},rawResponse:{headers:c},request:{body:JSON.stringify(l)},warnings:r}}},p=r.z.object({prompt_tokens:r.z.number().nullish(),completion_tokens:r.z.number().nullish(),prompt_tokens_details:r.z.object({cached_tokens:r.z.number().nullish()}).nullish(),completion_tokens_details:r.z.object({reasoning_tokens:r.z.number().nullish(),accepted_prediction_tokens:r.z.number().nullish(),rejected_prediction_tokens:r.z.number().nullish()}).nullish()}).nullish(),h=r.z.object({id:r.z.string().nullish(),created:r.z.number().nullish(),model:r.z.string().nullish(),choices:r.z.array(r.z.object({message:r.z.object({role:r.z.literal("assistant").nullish(),content:r.z.string().nullish(),function_call:r.z.object({arguments:r.z.string(),name:r.z.string()}).nullish(),tool_calls:r.z.array(r.z.object({id:r.z.string().nullish(),type:r.z.literal("function"),function:r.z.object({name:r.z.string(),arguments:r.z.string()})})).nullish()}),index:r.z.number(),logprobs:r.z.object({content:r.z.array(r.z.object({token:r.z.string(),logprob:r.z.number(),top_logprobs:r.z.array(r.z.object({token:r.z.string(),logprob:r.z.number()}))})).nullable()}).nullish(),finish_reason:r.z.string().nullish()})),usage:p}),m=r.z.union([r.z.object({id:r.z.string().nullish(),created:r.z.number().nullish(),model:r.z.string().nullish(),choices:r.z.array(r.z.object({delta:r.z.object({role:r.z.enum(["assistant"]).nullish(),content:r.z.string().nullish(),function_call:r.z.object({name:r.z.string().optional(),arguments:r.z.string().optional()}).nullish(),tool_calls:r.z.array(r.z.object({index:r.z.number(),id:r.z.string().nullish(),type:r.z.literal("function").nullish(),function:r.z.object({name:r.z.string().nullish(),arguments:r.z.string().nullish()})})).nullish()}).nullish(),logprobs:r.z.object({content:r.z.array(r.z.object({token:r.z.string(),logprob:r.z.number(),top_logprobs:r.z.array(r.z.object({token:r.z.string(),logprob:r.z.number()}))})).nullable()}).nullish(),finish_reason:r.z.string().nullish(),index:r.z.number()})),usage:p}),l]);function f(e){return e.startsWith("o")}var g={"o1-mini":{systemMessageMode:"remove"},"o1-mini-2024-09-12":{systemMessageMode:"remove"},"o1-preview":{systemMessageMode:"remove"},"o1-preview-2024-09-12":{systemMessageMode:"remove"},o3:{systemMessageMode:"developer"},"o3-2025-04-16":{systemMessageMode:"developer"},"o3-mini":{systemMessageMode:"developer"},"o3-mini-2025-01-31":{systemMessageMode:"developer"},"o4-mini":{systemMessageMode:"developer"},"o4-mini-2025-04-16":{systemMessageMode:"developer"}};function y(e){return null==e?void 0:e.tokens.map((t,s)=>({token:t,logprob:e.token_logprobs[s],topLogprobs:e.top_logprobs?Object.entries(e.top_logprobs[s]).map(([e,t])=>({token:e,logprob:t})):[]}))}var _=class{constructor(e,t,s){this.specificationVersion="v1",this.defaultObjectGenerationMode=void 0,this.modelId=e,this.settings=t,this.config=s}get provider(){return this.config.provider}getArgs({mode:e,inputFormat:t,prompt:s,maxTokens:n,temperature:r,topP:i,topK:o,frequencyPenalty:l,presencePenalty:u,stopSequences:d,responseFormat:c,seed:p}){var h;let m=e.type,f=[];null!=o&&f.push({type:"unsupported-setting",setting:"topK"}),null!=c&&"text"!==c.type&&f.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format is not supported."});let{prompt:g,stopSequences:y}=function({prompt:e,inputFormat:t,user:s="user",assistant:n="assistant"}){if("prompt"===t&&1===e.length&&"user"===e[0].role&&1===e[0].content.length&&"text"===e[0].content[0].type)return{prompt:e[0].content[0].text};let r="";for(let{role:t,content:i}of("system"===e[0].role&&(r+=`${e[0].content}

`,e=e.slice(1)),e))switch(t){case"system":throw new a.M3({message:"Unexpected system message in prompt: ${content}",prompt:e});case"user":{let e=i.map(e=>{switch(e.type){case"text":return e.text;case"image":throw new a.b8({functionality:"images"})}}).join("");r+=`${s}:
${e}

`;break}case"assistant":{let e=i.map(e=>{switch(e.type){case"text":return e.text;case"tool-call":throw new a.b8({functionality:"tool-call messages"})}}).join("");r+=`${n}:
${e}

`;break}case"tool":throw new a.b8({functionality:"tool messages"});default:throw Error(`Unsupported role: ${t}`)}return{prompt:r+=`${n}:
`,stopSequences:[`
${s}:`]}}({prompt:s,inputFormat:t}),_=[...null!=y?y:[],...null!=d?d:[]],v={model:this.modelId,echo:this.settings.echo,logit_bias:this.settings.logitBias,logprobs:"number"==typeof this.settings.logprobs?this.settings.logprobs:"boolean"==typeof this.settings.logprobs&&this.settings.logprobs?0:void 0,suffix:this.settings.suffix,user:this.settings.user,max_tokens:n,temperature:r,top_p:i,frequency_penalty:l,presence_penalty:u,seed:p,prompt:g,stop:_.length>0?_:void 0};switch(m){case"regular":if(null==(h=e.tools)?void 0:h.length)throw new a.b8({functionality:"tools"});if(e.toolChoice)throw new a.b8({functionality:"toolChoice"});return{args:v,warnings:f};case"object-json":throw new a.b8({functionality:"object-json mode"});case"object-tool":throw new a.b8({functionality:"object-tool mode"});default:throw Error(`Unsupported type: ${m}`)}}async doGenerate(e){let{args:t,warnings:s}=this.getArgs(e),{responseHeaders:a,value:r,rawValue:i}=await (0,n.GU)({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:(0,n.m2)(this.config.headers(),e.headers),body:t,failedResponseHandler:u,successfulResponseHandler:(0,n.cV)(v),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:l,...c}=t,p=r.choices[0];return{text:p.text,usage:{promptTokens:r.usage.prompt_tokens,completionTokens:r.usage.completion_tokens},finishReason:o(p.finish_reason),logprobs:y(p.logprobs),rawCall:{rawPrompt:l,rawSettings:c},rawResponse:{headers:a,body:i},response:d(r),warnings:s,request:{body:JSON.stringify(t)}}}async doStream(e){let t,{args:s,warnings:a}=this.getArgs(e),r={...s,stream:!0,stream_options:"strict"===this.config.compatibility?{include_usage:!0}:void 0},{responseHeaders:i,value:l}=await (0,n.GU)({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:(0,n.m2)(this.config.headers(),e.headers),body:r,failedResponseHandler:u,successfulResponseHandler:(0,n.Ds)(b),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:c,...p}=s,h="unknown",m={promptTokens:Number.NaN,completionTokens:Number.NaN},f=!0;return{stream:l.pipeThrough(new TransformStream({transform(e,s){if(!e.success){h="error",s.enqueue({type:"error",error:e.error});return}let n=e.value;if("error"in n){h="error",s.enqueue({type:"error",error:n.error});return}f&&(f=!1,s.enqueue({type:"response-metadata",...d(n)})),null!=n.usage&&(m={promptTokens:n.usage.prompt_tokens,completionTokens:n.usage.completion_tokens});let a=n.choices[0];(null==a?void 0:a.finish_reason)!=null&&(h=o(a.finish_reason)),(null==a?void 0:a.text)!=null&&s.enqueue({type:"text-delta",textDelta:a.text});let r=y(null==a?void 0:a.logprobs);(null==r?void 0:r.length)&&(void 0===t&&(t=[]),t.push(...r))},flush(e){e.enqueue({type:"finish",finishReason:h,logprobs:t,usage:m})}})),rawCall:{rawPrompt:c,rawSettings:p},rawResponse:{headers:i},warnings:a,request:{body:JSON.stringify(r)}}}},v=r.z.object({id:r.z.string().nullish(),created:r.z.number().nullish(),model:r.z.string().nullish(),choices:r.z.array(r.z.object({text:r.z.string(),finish_reason:r.z.string(),logprobs:r.z.object({tokens:r.z.array(r.z.string()),token_logprobs:r.z.array(r.z.number()),top_logprobs:r.z.array(r.z.record(r.z.string(),r.z.number())).nullable()}).nullish()})),usage:r.z.object({prompt_tokens:r.z.number(),completion_tokens:r.z.number()})}),b=r.z.union([r.z.object({id:r.z.string().nullish(),created:r.z.number().nullish(),model:r.z.string().nullish(),choices:r.z.array(r.z.object({text:r.z.string(),finish_reason:r.z.string().nullish(),index:r.z.number(),logprobs:r.z.object({tokens:r.z.array(r.z.string()),token_logprobs:r.z.array(r.z.number()),top_logprobs:r.z.array(r.z.record(r.z.string(),r.z.number())).nullable()}).nullish()})),usage:r.z.object({prompt_tokens:r.z.number(),completion_tokens:r.z.number()}).nullish()}),l]),w=class{constructor(e,t,s){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=s}get provider(){return this.config.provider}get maxEmbeddingsPerCall(){var e;return null!=(e=this.settings.maxEmbeddingsPerCall)?e:2048}get supportsParallelCalls(){var e;return null==(e=this.settings.supportsParallelCalls)||e}async doEmbed({values:e,headers:t,abortSignal:s}){if(e.length>this.maxEmbeddingsPerCall)throw new a.Ch({provider:this.provider,modelId:this.modelId,maxEmbeddingsPerCall:this.maxEmbeddingsPerCall,values:e});let{responseHeaders:r,value:i}=await (0,n.GU)({url:this.config.url({path:"/embeddings",modelId:this.modelId}),headers:(0,n.m2)(this.config.headers(),t),body:{model:this.modelId,input:e,encoding_format:"float",dimensions:this.settings.dimensions,user:this.settings.user},failedResponseHandler:u,successfulResponseHandler:(0,n.cV)(k),abortSignal:s,fetch:this.config.fetch});return{embeddings:i.data.map(e=>e.embedding),usage:i.usage?{tokens:i.usage.prompt_tokens}:void 0,rawResponse:{headers:r}}}},k=r.z.object({data:r.z.array(r.z.object({embedding:r.z.array(r.z.number())})),usage:r.z.object({prompt_tokens:r.z.number()}).nullish()}),x={"dall-e-3":1,"dall-e-2":10,"gpt-image-1":10},z=new Set(["gpt-image-1"]),T=class{constructor(e,t,s){this.modelId=e,this.settings=t,this.config=s,this.specificationVersion="v1"}get maxImagesPerCall(){var e,t;return null!=(t=null!=(e=this.settings.maxImagesPerCall)?e:x[this.modelId])?t:1}get provider(){return this.config.provider}async doGenerate({prompt:e,n:t,size:s,aspectRatio:a,seed:r,providerOptions:i,headers:o,abortSignal:l}){var d,c,p,h;let m=[];null!=a&&m.push({type:"unsupported-setting",setting:"aspectRatio",details:"This model does not support aspect ratio. Use `size` instead."}),null!=r&&m.push({type:"unsupported-setting",setting:"seed"});let f=null!=(p=null==(c=null==(d=this.config._internal)?void 0:d.currentDate)?void 0:c.call(d))?p:new Date,{value:g,responseHeaders:y}=await (0,n.GU)({url:this.config.url({path:"/images/generations",modelId:this.modelId}),headers:(0,n.m2)(this.config.headers(),o),body:{model:this.modelId,prompt:e,n:t,size:s,...null!=(h=i.openai)?h:{},...!z.has(this.modelId)?{response_format:"b64_json"}:{}},failedResponseHandler:u,successfulResponseHandler:(0,n.cV)(j),abortSignal:l,fetch:this.config.fetch});return{images:g.data.map(e=>e.b64_json),warnings:m,response:{timestamp:f,modelId:this.modelId,headers:y}}}},j=r.z.object({data:r.z.array(r.z.object({b64_json:r.z.string()}))}),C=r.z.object({include:r.z.array(r.z.string()).nullish(),language:r.z.string().nullish(),prompt:r.z.string().nullish(),temperature:r.z.number().min(0).max(1).nullish().default(0),timestampGranularities:r.z.array(r.z.enum(["word","segment"])).nullish().default(["segment"])}),I={afrikaans:"af",arabic:"ar",armenian:"hy",azerbaijani:"az",belarusian:"be",bosnian:"bs",bulgarian:"bg",catalan:"ca",chinese:"zh",croatian:"hr",czech:"cs",danish:"da",dutch:"nl",english:"en",estonian:"et",finnish:"fi",french:"fr",galician:"gl",german:"de",greek:"el",hebrew:"he",hindi:"hi",hungarian:"hu",icelandic:"is",indonesian:"id",italian:"it",japanese:"ja",kannada:"kn",kazakh:"kk",korean:"ko",latvian:"lv",lithuanian:"lt",macedonian:"mk",malay:"ms",marathi:"mr",maori:"mi",nepali:"ne",norwegian:"no",persian:"fa",polish:"pl",portuguese:"pt",romanian:"ro",russian:"ru",serbian:"sr",slovak:"sk",slovenian:"sl",spanish:"es",swahili:"sw",swedish:"sv",tagalog:"tl",tamil:"ta",thai:"th",turkish:"tr",ukrainian:"uk",urdu:"ur",vietnamese:"vi",welsh:"cy"},S=class{constructor(e,t){this.modelId=e,this.config=t,this.specificationVersion="v1"}get provider(){return this.config.provider}getArgs({audio:e,mediaType:t,providerOptions:s}){var a,r,i,o,l;let u=(0,n.xI)({provider:"openai",providerOptions:s,schema:C}),d=new FormData,c=e instanceof Uint8Array?new Blob([e]):new Blob([(0,n.Z9)(e)]);if(d.append("model",this.modelId),d.append("file",new File([c],"audio",{type:t})),u){let e={include:null!=(a=u.include)?a:void 0,language:null!=(r=u.language)?r:void 0,prompt:null!=(i=u.prompt)?i:void 0,temperature:null!=(o=u.temperature)?o:void 0,timestamp_granularities:null!=(l=u.timestampGranularities)?l:void 0};for(let t in e){let s=e[t];void 0!==s&&d.append(t,String(s))}}return{formData:d,warnings:[]}}async doGenerate(e){var t,s,a,r,i,o;let l=null!=(a=null==(s=null==(t=this.config._internal)?void 0:t.currentDate)?void 0:s.call(t))?a:new Date,{formData:d,warnings:c}=this.getArgs(e),{value:p,responseHeaders:h,rawValue:m}=await (0,n.S)({url:this.config.url({path:"/audio/transcriptions",modelId:this.modelId}),headers:(0,n.m2)(this.config.headers(),e.headers),formData:d,failedResponseHandler:u,successfulResponseHandler:(0,n.cV)(N),abortSignal:e.abortSignal,fetch:this.config.fetch}),f=null!=p.language&&p.language in I?I[p.language]:void 0;return{text:p.text,segments:null!=(i=null==(r=p.words)?void 0:r.map(e=>({text:e.word,startSecond:e.start,endSecond:e.end})))?i:[],language:f,durationInSeconds:null!=(o=p.duration)?o:void 0,warnings:c,response:{timestamp:l,modelId:this.modelId,headers:h,body:m}}}},N=r.z.object({text:r.z.string(),language:r.z.string().nullish(),duration:r.z.number().nullish(),words:r.z.array(r.z.object({word:r.z.string(),start:r.z.number(),end:r.z.number()})).nullish()});function O({finishReason:e,hasToolCalls:t}){switch(e){case void 0:case null:return t?"tool-calls":"stop";case"max_output_tokens":return"length";case"content_filter":return"content-filter";default:return t?"tool-calls":"unknown"}}var A=class{constructor(e,t){this.specificationVersion="v1",this.defaultObjectGenerationMode="json",this.supportsStructuredOutputs=!0,this.modelId=e,this.config=t}get provider(){return this.config.provider}getArgs({mode:e,maxTokens:t,temperature:s,stopSequences:r,topP:i,topK:o,presencePenalty:l,frequencyPenalty:u,seed:d,prompt:c,providerMetadata:p,responseFormat:h}){var m,f,g,y;let _=[],v=(y=this.modelId).startsWith("o")?y.startsWith("o1-mini")||y.startsWith("o1-preview")?{isReasoningModel:!0,systemMessageMode:"remove",requiredAutoTruncation:!1}:{isReasoningModel:!0,systemMessageMode:"developer",requiredAutoTruncation:!1}:{isReasoningModel:!1,systemMessageMode:"system",requiredAutoTruncation:!1},b=e.type;null!=o&&_.push({type:"unsupported-setting",setting:"topK"}),null!=d&&_.push({type:"unsupported-setting",setting:"seed"}),null!=l&&_.push({type:"unsupported-setting",setting:"presencePenalty"}),null!=u&&_.push({type:"unsupported-setting",setting:"frequencyPenalty"}),null!=r&&_.push({type:"unsupported-setting",setting:"stopSequences"});let{messages:w,warnings:k}=function({prompt:e,systemMessageMode:t}){let s=[],r=[];for(let{role:i,content:o}of e)switch(i){case"system":switch(t){case"system":s.push({role:"system",content:o});break;case"developer":s.push({role:"developer",content:o});break;case"remove":r.push({type:"other",message:"system messages are removed for this model"});break;default:throw Error(`Unsupported system message mode: ${t}`)}break;case"user":s.push({role:"user",content:o.map((e,t)=>{var s,r,i,o;switch(e.type){case"text":return{type:"input_text",text:e.text};case"image":return{type:"input_image",image_url:e.image instanceof URL?e.image.toString():`data:${null!=(s=e.mimeType)?s:"image/jpeg"};base64,${(0,n.n_)(e.image)}`,detail:null==(i=null==(r=e.providerMetadata)?void 0:r.openai)?void 0:i.imageDetail};case"file":if(e.data instanceof URL)throw new a.b8({functionality:"File URLs in user messages"});if("application/pdf"===e.mimeType)return{type:"input_file",filename:null!=(o=e.filename)?o:`part-${t}.pdf`,file_data:`data:application/pdf;base64,${e.data}`};throw new a.b8({functionality:"Only PDF files are supported in user messages"})}})});break;case"assistant":for(let e of o)switch(e.type){case"text":s.push({role:"assistant",content:[{type:"output_text",text:e.text}]});break;case"tool-call":s.push({type:"function_call",call_id:e.toolCallId,name:e.toolName,arguments:JSON.stringify(e.args)})}break;case"tool":for(let e of o)s.push({type:"function_call_output",call_id:e.toolCallId,output:JSON.stringify(e.result)});break;default:throw Error(`Unsupported role: ${i}`)}return{messages:s,warnings:r}}({prompt:c,systemMessageMode:v.systemMessageMode});_.push(...k);let x=(0,n.xI)({provider:"openai",providerOptions:p,schema:V}),z=null==(m=null==x?void 0:x.strictSchemas)||m,T={model:this.modelId,input:w,temperature:s,top_p:i,max_output_tokens:t,...(null==h?void 0:h.type)==="json"&&{text:{format:null!=h.schema?{type:"json_schema",strict:z,name:null!=(f=h.name)?f:"response",description:h.description,schema:h.schema}:{type:"json_object"}}},metadata:null==x?void 0:x.metadata,parallel_tool_calls:null==x?void 0:x.parallelToolCalls,previous_response_id:null==x?void 0:x.previousResponseId,store:null==x?void 0:x.store,user:null==x?void 0:x.user,instructions:null==x?void 0:x.instructions,...v.isReasoningModel&&((null==x?void 0:x.reasoningEffort)!=null||(null==x?void 0:x.reasoningSummary)!=null)&&{reasoning:{...(null==x?void 0:x.reasoningEffort)!=null&&{effort:x.reasoningEffort},...(null==x?void 0:x.reasoningSummary)!=null&&{summary:x.reasoningSummary}}},...v.requiredAutoTruncation&&{truncation:"auto"}};switch(v.isReasoningModel&&(null!=T.temperature&&(T.temperature=void 0,_.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for reasoning models"})),null!=T.top_p&&(T.top_p=void 0,_.push({type:"unsupported-setting",setting:"topP",details:"topP is not supported for reasoning models"}))),b){case"regular":{let{tools:t,tool_choice:s,toolWarnings:n}=function({mode:e,strict:t}){var s;let n=(null==(s=e.tools)?void 0:s.length)?e.tools:void 0,r=[];if(null==n)return{tools:void 0,tool_choice:void 0,toolWarnings:r};let i=e.toolChoice,o=[];for(let e of n)switch(e.type){case"function":o.push({type:"function",name:e.name,description:e.description,parameters:e.parameters,strict:!!t||void 0});break;case"provider-defined":"openai.web_search_preview"===e.id?o.push({type:"web_search_preview",search_context_size:e.args.searchContextSize,user_location:e.args.userLocation}):r.push({type:"unsupported-tool",tool:e});break;default:r.push({type:"unsupported-tool",tool:e})}if(null==i)return{tools:o,tool_choice:void 0,toolWarnings:r};let l=i.type;switch(l){case"auto":case"none":case"required":return{tools:o,tool_choice:l,toolWarnings:r};case"tool":if("web_search_preview"===i.toolName)return{tools:o,tool_choice:{type:"web_search_preview"},toolWarnings:r};return{tools:o,tool_choice:{type:"function",name:i.toolName},toolWarnings:r};default:throw new a.b8({functionality:`Unsupported tool choice type: ${l}`})}}({mode:e,strict:z});return{args:{...T,tools:t,tool_choice:s},warnings:[..._,...n]}}case"object-json":return{args:{...T,text:{format:null!=e.schema?{type:"json_schema",strict:z,name:null!=(g=e.name)?g:"response",description:e.description,schema:e.schema}:{type:"json_object"}}},warnings:_};case"object-tool":return{args:{...T,tool_choice:{type:"function",name:e.tool.name},tools:[{type:"function",name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters,strict:z}]},warnings:_};default:throw Error(`Unsupported type: ${b}`)}}async doGenerate(e){var t,s,a,i,o,l,d;let{args:c,warnings:p}=this.getArgs(e),{responseHeaders:h,value:m,rawValue:f}=await (0,n.GU)({url:this.config.url({path:"/responses",modelId:this.modelId}),headers:(0,n.m2)(this.config.headers(),e.headers),body:c,failedResponseHandler:u,successfulResponseHandler:(0,n.cV)(r.z.object({id:r.z.string(),created_at:r.z.number(),model:r.z.string(),output:r.z.array(r.z.discriminatedUnion("type",[r.z.object({type:r.z.literal("message"),role:r.z.literal("assistant"),content:r.z.array(r.z.object({type:r.z.literal("output_text"),text:r.z.string(),annotations:r.z.array(r.z.object({type:r.z.literal("url_citation"),start_index:r.z.number(),end_index:r.z.number(),url:r.z.string(),title:r.z.string()}))}))}),r.z.object({type:r.z.literal("function_call"),call_id:r.z.string(),name:r.z.string(),arguments:r.z.string()}),r.z.object({type:r.z.literal("web_search_call")}),r.z.object({type:r.z.literal("computer_call")}),r.z.object({type:r.z.literal("reasoning"),summary:r.z.array(r.z.object({type:r.z.literal("summary_text"),text:r.z.string()}))})])),incomplete_details:r.z.object({reason:r.z.string()}).nullable(),usage:R})),abortSignal:e.abortSignal,fetch:this.config.fetch}),g=m.output.filter(e=>"message"===e.type).flatMap(e=>e.content).filter(e=>"output_text"===e.type),y=m.output.filter(e=>"function_call"===e.type).map(e=>({toolCallType:"function",toolCallId:e.call_id,toolName:e.name,args:e.arguments})),_=null!=(s=null==(t=m.output.find(e=>"reasoning"===e.type))?void 0:t.summary)?s:null;return{text:g.map(e=>e.text).join("\n"),sources:g.flatMap(e=>e.annotations.map(e=>{var t,s,a;return{sourceType:"url",id:null!=(a=null==(s=(t=this.config).generateId)?void 0:s.call(t))?a:(0,n.$C)(),url:e.url,title:e.title}})),finishReason:O({finishReason:null==(a=m.incomplete_details)?void 0:a.reason,hasToolCalls:y.length>0}),toolCalls:y.length>0?y:void 0,reasoning:_?_.map(e=>({type:"text",text:e.text})):void 0,usage:{promptTokens:m.usage.input_tokens,completionTokens:m.usage.output_tokens},rawCall:{rawPrompt:void 0,rawSettings:{}},rawResponse:{headers:h,body:f},request:{body:JSON.stringify(c)},response:{id:m.id,timestamp:new Date(1e3*m.created_at),modelId:m.model},providerMetadata:{openai:{responseId:m.id,cachedPromptTokens:null!=(o=null==(i=m.usage.input_tokens_details)?void 0:i.cached_tokens)?o:null,reasoningTokens:null!=(d=null==(l=m.usage.output_tokens_details)?void 0:l.reasoning_tokens)?d:null}},warnings:p}}async doStream(e){let{args:t,warnings:s}=this.getArgs(e),{responseHeaders:a,value:r}=await (0,n.GU)({url:this.config.url({path:"/responses",modelId:this.modelId}),headers:(0,n.m2)(this.config.headers(),e.headers),body:{...t,stream:!0},failedResponseHandler:u,successfulResponseHandler:(0,n.Ds)(F),abortSignal:e.abortSignal,fetch:this.config.fetch}),i=this,o="unknown",l=NaN,d=NaN,c=null,p=null,h=null,m={},f=!1;return{stream:r.pipeThrough(new TransformStream({transform(e,t){var s,a,r,u,g,y,_,v,b;if(!e.success){o="error",t.enqueue({type:"error",error:e.error});return}let w=e.value;if("response.output_item.added"===w.type)"function_call"===w.item.type&&(m[w.output_index]={toolName:w.item.name,toolCallId:w.item.call_id},t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:w.item.call_id,toolName:w.item.name,argsTextDelta:w.item.arguments}));else if("response.function_call_arguments.delta"===w.type){let e=m[w.output_index];null!=e&&t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:e.toolCallId,toolName:e.toolName,argsTextDelta:w.delta})}else{"response.created"===w.type?(h=w.response.id,t.enqueue({type:"response-metadata",id:w.response.id,timestamp:new Date(1e3*w.response.created_at),modelId:w.response.model})):"response.output_text.delta"===w.type?t.enqueue({type:"text-delta",textDelta:w.delta}):"response.reasoning_summary_text.delta"===w.type?t.enqueue({type:"reasoning",textDelta:w.delta}):"response.output_item.done"===w.type&&"function_call"===w.item.type?(m[w.output_index]=void 0,f=!0,t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:w.item.call_id,toolName:w.item.name,args:w.item.arguments})):"response.completed"===(b=w).type||"response.incomplete"===b.type?(o=O({finishReason:null==(s=w.response.incomplete_details)?void 0:s.reason,hasToolCalls:f}),l=w.response.usage.input_tokens,d=w.response.usage.output_tokens,c=null!=(r=null==(a=w.response.usage.input_tokens_details)?void 0:a.cached_tokens)?r:c,p=null!=(g=null==(u=w.response.usage.output_tokens_details)?void 0:u.reasoning_tokens)?g:p):"response.output_text.annotation.added"===w.type&&t.enqueue({type:"source",source:{sourceType:"url",id:null!=(v=null==(_=(y=i.config).generateId)?void 0:_.call(y))?v:(0,n.$C)(),url:w.annotation.url,title:w.annotation.title}})}},flush(e){e.enqueue({type:"finish",finishReason:o,usage:{promptTokens:l,completionTokens:d},...(null!=c||null!=p)&&{providerMetadata:{openai:{responseId:h,cachedPromptTokens:c,reasoningTokens:p}}}})}})),rawCall:{rawPrompt:void 0,rawSettings:{}},rawResponse:{headers:a},request:{body:JSON.stringify(t)},warnings:s}}},R=r.z.object({input_tokens:r.z.number(),input_tokens_details:r.z.object({cached_tokens:r.z.number().nullish()}).nullish(),output_tokens:r.z.number(),output_tokens_details:r.z.object({reasoning_tokens:r.z.number().nullish()}).nullish()}),$=r.z.object({type:r.z.literal("response.output_text.delta"),delta:r.z.string()}),Z=r.z.object({type:r.z.enum(["response.completed","response.incomplete"]),response:r.z.object({incomplete_details:r.z.object({reason:r.z.string()}).nullish(),usage:R})}),E=r.z.object({type:r.z.literal("response.created"),response:r.z.object({id:r.z.string(),created_at:r.z.number(),model:r.z.string()})}),M=r.z.object({type:r.z.literal("response.output_item.done"),output_index:r.z.number(),item:r.z.discriminatedUnion("type",[r.z.object({type:r.z.literal("message")}),r.z.object({type:r.z.literal("function_call"),id:r.z.string(),call_id:r.z.string(),name:r.z.string(),arguments:r.z.string(),status:r.z.literal("completed")})])}),P=r.z.object({type:r.z.literal("response.function_call_arguments.delta"),item_id:r.z.string(),output_index:r.z.number(),delta:r.z.string()}),q=r.z.object({type:r.z.literal("response.output_item.added"),output_index:r.z.number(),item:r.z.discriminatedUnion("type",[r.z.object({type:r.z.literal("message")}),r.z.object({type:r.z.literal("function_call"),id:r.z.string(),call_id:r.z.string(),name:r.z.string(),arguments:r.z.string()})])}),L=r.z.object({type:r.z.literal("response.output_text.annotation.added"),annotation:r.z.object({type:r.z.literal("url_citation"),url:r.z.string(),title:r.z.string()})}),D=r.z.object({type:r.z.literal("response.reasoning_summary_text.delta"),item_id:r.z.string(),output_index:r.z.number(),summary_index:r.z.number(),delta:r.z.string()}),F=r.z.union([$,Z,E,M,P,q,L,D,r.z.object({type:r.z.string()}).passthrough()]),V=r.z.object({metadata:r.z.any().nullish(),parallelToolCalls:r.z.boolean().nullish(),previousResponseId:r.z.string().nullish(),store:r.z.boolean().nullish(),user:r.z.string().nullish(),reasoningEffort:r.z.string().nullish(),strictSchemas:r.z.boolean().nullish(),instructions:r.z.string().nullish(),reasoningSummary:r.z.string().nullish()}),U=r.z.object({}),K={webSearchPreview:function({searchContextSize:e,userLocation:t}={}){return{type:"provider-defined",id:"openai.web_search_preview",args:{searchContextSize:e,userLocation:t},parameters:U}}},B=r.z.object({instructions:r.z.string().nullish(),speed:r.z.number().min(.25).max(4).default(1).nullish()}),W=class{constructor(e,t){this.modelId=e,this.config=t,this.specificationVersion="v1"}get provider(){return this.config.provider}getArgs({text:e,voice:t="alloy",outputFormat:s="mp3",speed:a,instructions:r,providerOptions:i}){let o=[],l=(0,n.xI)({provider:"openai",providerOptions:i,schema:B}),u={model:this.modelId,input:e,voice:t,response_format:"mp3",speed:a,instructions:r};if(s&&(["mp3","opus","aac","flac","wav","pcm"].includes(s)?u.response_format=s:o.push({type:"unsupported-setting",setting:"outputFormat",details:`Unsupported output format: ${s}. Using mp3 instead.`})),l){let e={};for(let t in e){let s=e[t];void 0!==s&&(u[t]=s)}}return{requestBody:u,warnings:o}}async doGenerate(e){var t,s,a;let r=null!=(a=null==(s=null==(t=this.config._internal)?void 0:t.currentDate)?void 0:s.call(t))?a:new Date,{requestBody:i,warnings:o}=this.getArgs(e),{value:l,responseHeaders:d,rawValue:c}=await (0,n.GU)({url:this.config.url({path:"/audio/speech",modelId:this.modelId}),headers:(0,n.m2)(this.config.headers(),e.headers),body:i,failedResponseHandler:u,successfulResponseHandler:(0,n.HD)(),abortSignal:e.abortSignal,fetch:this.config.fetch});return{audio:l,warnings:o,request:{body:JSON.stringify(i)},response:{timestamp:r,modelId:this.modelId,headers:d,body:c}}}},H=function(e={}){var t,s,a;let r=null!=(t=(0,n.ae)(e.baseURL))?t:"https://api.openai.com/v1",i=null!=(s=e.compatibility)?s:"compatible",o=null!=(a=e.name)?a:"openai",l=()=>({Authorization:`Bearer ${(0,n.WL)({apiKey:e.apiKey,environmentVariableName:"OPENAI_API_KEY",description:"OpenAI"})}`,"OpenAI-Organization":e.organization,"OpenAI-Project":e.project,...e.headers}),u=(t,s={})=>new c(t,s,{provider:`${o}.chat`,url:({path:e})=>`${r}${e}`,headers:l,compatibility:i,fetch:e.fetch}),d=(t,s={})=>new _(t,s,{provider:`${o}.completion`,url:({path:e})=>`${r}${e}`,headers:l,compatibility:i,fetch:e.fetch}),p=(t,s={})=>new w(t,s,{provider:`${o}.embedding`,url:({path:e})=>`${r}${e}`,headers:l,fetch:e.fetch}),h=(t,s={})=>new T(t,s,{provider:`${o}.image`,url:({path:e})=>`${r}${e}`,headers:l,fetch:e.fetch}),m=t=>new S(t,{provider:`${o}.transcription`,url:({path:e})=>`${r}${e}`,headers:l,fetch:e.fetch}),f=t=>new W(t,{provider:`${o}.speech`,url:({path:e})=>`${r}${e}`,headers:l,fetch:e.fetch}),g=(e,t)=>{if(new.target)throw Error("The OpenAI model function cannot be called with the new keyword.");return"gpt-3.5-turbo-instruct"===e?d(e,t):u(e,t)},y=function(e,t){return g(e,t)};return y.languageModel=g,y.chat=u,y.completion=d,y.responses=t=>new A(t,{provider:`${o}.responses`,url:({path:e})=>`${r}${e}`,headers:l,fetch:e.fetch}),y.embedding=p,y.textEmbedding=p,y.textEmbeddingModel=p,y.image=h,y.imageModel=h,y.transcription=m,y.transcriptionModel=m,y.speech=f,y.speechModel=f,y.tools=K,y}({compatibility:"strict"})}};