# 🤖 Chatbot

이 프로젝트는 말로 만드는 지도 프론트엔드, 백엔드 소스코드와 Open-Web-UI에서 사용되는 파이프라인이 포함되어 있습니다. 이를 기반으로 로컬 LLM(Large Language Model)을 활용한 대화형 AI 서비스를 제공합니다.


## 프로젝트별 개요

- backend: 말로 만드는지도 FastAPI 기반 API 서버
- frontend: backend 관리자 UI (주의 : 현재 사용되지 않으며 추후에 삭제될 수 있습니다.)
- front-chat: 말로 만드는지도 인터페이스
- pipelines: Open-Web-UI pipelines

## 접속 정보
| 서비스 | URL |
|--------|-----|
| Backend API 문서 | http://geon-ai.geon.kr/docs |
| 관리자 UI | https://ai.geon.kr/backend/#/ChatScreen |
| 사용자 UI | http://ai.geon.kr |
| Open WebUI | https://ai-webui.geon.kr |

## Ollama 기동 상태 확인 및 재기동 방법

1. 사내 AI 서버에 SSH로 접근
2. 상태 확인: `nvidia-smi` 명령어 입력
    - `Processes` 항목에서 `ollama_server` 확인
    - 해당 서버의 메모리 사용량 확인
3. 재기동: `ollama_start` 명령어 입력

## 사용 가능 LLM Model 목록 조회 방법 

브라우저에서 `http://**************:11434/api/tags`를 입력하면 JSON 형태의 응답을 받을 수 있습니다.
응답 데이터에서 "model" 필드를 확인하시면 됩니다.
