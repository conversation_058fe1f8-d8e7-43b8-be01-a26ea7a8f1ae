"use strict";(()=>{var e={};e.id=878,e.ids=[878],e.modules={1749:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>w,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var n={};t.r(n),t.d(n,{DELETE:()=>v,POST:()=>l});var o=t(81160),s=t(18765),a=t(46332),i=t(88668),u=t(34112),d=t(71003),p=t(28538),c=t(85457);async function l(e){let{id:r,messages:t,modelId:n,conversationId:o}=await e.json(),s=d.Jn.find(e=>e.id===n);if(!s)return new Response("Model not found",{status:404});let a=(0,i.E2)(t),p=(0,c.Bv)(a);return p?(0,i.ke)({execute:e=>{(0,i.gM)({model:(0,u.gx)(s.id),messages:[{role:"user",content:p.content}],headers:{"user-id":"geon-user",...o?{"chat-id":o}:{}},onFinish:async({providerMetadata:r})=>{let t=r?.difyWorkflowData?.conversationId;t&&e.writeData({type:"conversation-id",id:t})}}).mergeIntoDataStream(e,{sendReasoning:!1})},onError:e=>(console.error("Agent processing error:",e),i.$o.isInstance(e))?"The model tried to call a unknown tool.":i.eK.isInstance(e)?"The model called a tool with invalid arguments.":i.TO.isInstance(e)?"An error occurred during tool execution.":"An unknown error occurred."}):new Response("No user message found",{status:400})}async function v(e){let{searchParams:r}=new URL(e.url),t=r.get("id");if(!t)return new Response("Not Found",{status:404});try{return await (0,p.qQ)({id:t}),new Response("OK")}catch(e){return console.error(e),new Response("Error",{status:500})}}let w=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/(preview)/api/dev-chat/route",pathname:"/api/dev-chat",filename:"route",bundlePath:"app/(preview)/api/dev-chat/route"},resolvedPagePath:"C:\\chatbot\\front-chat\\app\\(preview)\\api\\dev-chat\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:x}=w;function m(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},42449:e=>{e.exports=require("pg")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77598:e=>{e.exports=require("node:crypto")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[332,746,861,46,929,343,501],()=>t(1749));module.exports=n})();