"use client";

import { useState } from 'react';
import { Popup } from '@geon-map/odf';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { MapPin, X, Copy, Navigation, Info } from 'lucide-react';
import { toast } from 'sonner';

interface LocationPopupProps {
  position: [number, number];
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp?: number;
  onClose: () => void;
}

export function LocationPopup({
  position,
  latitude,
  longitude,
  accuracy,
  timestamp,
  onClose
}: LocationPopupProps) {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);

  const handleCopyCoordinates = () => {
    const coordText = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    navigator.clipboard.writeText(coordText);
    toast.success("좌표가 복사되었습니다");
  };

  const handleCopyProjectedCoordinates = () => {
    const coordText = `${position[0].toFixed(2)}, ${position[1].toFixed(2)}`;
    navigator.clipboard.writeText(coordText);
    toast.success("투영 좌표가 복사되었습니다");
  };

  const formatTimestamp = (timestamp?: number) => {
    if (!timestamp) return null;
    return new Date(timestamp).toLocaleString('ko-KR');
  };

  return (
    <Popup
      position={position}
      offset={[0, 0]}
      autoPan
      autoPanAnimation={250}
      autoPanMargin={20}      
    >
      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="default"
            size="sm"
            className="h-8 w-8 p-0 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg "
            onClick={() => setIsPopoverOpen(true)}
          >
            <MapPin className="h-10 w-10 text-white" />
          </Button>
        </PopoverTrigger>

        <PopoverContent
          className="w-80 p-0 shadow-lg border-0 backdrop-blur-sm"
          side="top"
          align="center"
        >
          <Card className="border-0 shadow-none bg-transparent">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-semibold flex items-center gap-2">
                  <div className="p-1.5 bg-blue-100 rounded-full">
                    <MapPin className="h-4 w-4 text-blue-600" />
                  </div>
                  현재 위치
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setIsPopoverOpen(false);
                    onClose();
                  }}
                  className="h-6 w-6 p-0 hover:bg-gray-100"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </CardHeader>

            <CardContent className="space-y-3">
              {/* GPS 좌표 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-gray-600">GPS 좌표 (WGS84)</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopyCoordinates}
                    className="h-6 px-2 text-xs hover:bg-gray-100"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    복사
                  </Button>
                </div>
                <div className="bg-gray-50 rounded-md p-2 font-mono text-xs">
                  <div>위도: {latitude.toFixed(6)}</div>
                  <div>경도: {longitude.toFixed(6)}</div>
                </div>
              </div>

              {/* 투영 좌표 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-gray-600">투영 좌표</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopyProjectedCoordinates}
                    className="h-6 px-2 text-xs hover:bg-gray-100"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    복사
                  </Button>
                </div>
                <div className="bg-gray-50 rounded-md p-2 font-mono text-xs">
                  <div>X: {position[0].toFixed(2)}</div>
                  <div>Y: {position[1].toFixed(2)}</div>
                </div>
              </div>

              {/* 정확도 정보 */}
              {accuracy && (
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-gray-600">정확도</span>
                  <Badge variant="outline" className="text-xs">
                    ±{Math.round(accuracy)}m
                  </Badge>
                </div>
              )}

              {/* 시간 정보 */}
              {timestamp && (
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-gray-600">측정 시간</span>
                  <span className="text-xs text-gray-500">
                    {formatTimestamp(timestamp)}
                  </span>
                </div>
              )}

              {/* 액션 버튼 */}
              <div className="pt-2 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    toast.success("지도 중심 이동 기능은 지도 컨텍스트에서 처리됩니다");
                  }}
                  className="w-full text-xs"
                >
                  <Navigation className="h-3 w-3 mr-1" />
                  이 위치로 지도 중심 이동
                </Button>
              </div>
            </CardContent>
          </Card>
        </PopoverContent>
      </Popover>
    </Popup>
  );
}
