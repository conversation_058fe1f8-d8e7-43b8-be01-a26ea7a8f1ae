{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/%40codemirror%2Blanguage%406.10.7/node_modules/%40codemirror/language/dist/index.js"], "sourcesContent": ["import { NodeProp, IterMode, Tree, TreeFragment, Parser, NodeType, NodeSet } from '@lezer/common';\nimport { StateEffect, StateField, Facet, EditorState, countColumn, combineConfig, RangeSet, RangeSetBuilder, Prec } from '@codemirror/state';\nimport { ViewPlugin, logException, EditorView, Decoration, WidgetType, gutter, GutterMarker, Direction } from '@codemirror/view';\nimport { tags, tagHighlighter, highlightTree, styleTags } from '@lezer/highlight';\nimport { StyleModule } from 'style-mod';\n\nvar _a;\n/**\nNode prop stored in a parser's top syntax node to provide the\nfacet that stores language-specific data for that language.\n*/\nconst languageDataProp = /*@__PURE__*/new NodeProp();\n/**\nHelper function to define a facet (to be added to the top syntax\nnode(s) for a language via\n[`languageDataProp`](https://codemirror.net/6/docs/ref/#language.languageDataProp)), that will be\nused to associate language data with the language. You\nprobably only need this when subclassing\n[`Language`](https://codemirror.net/6/docs/ref/#language.Language).\n*/\nfunction defineLanguageFacet(baseData) {\n    return Facet.define({\n        combine: baseData ? values => values.concat(baseData) : undefined\n    });\n}\n/**\nSyntax node prop used to register sublanguages. Should be added to\nthe top level node type for the language.\n*/\nconst sublanguageProp = /*@__PURE__*/new NodeProp();\n/**\nA language object manages parsing and per-language\n[metadata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt). Parse data is\nmanaged as a [Lezer](https://lezer.codemirror.net) tree. The class\ncan be used directly, via the [`LRLanguage`](https://codemirror.net/6/docs/ref/#language.LRLanguage)\nsubclass for [Lezer](https://lezer.codemirror.net/) LR parsers, or\nvia the [`StreamLanguage`](https://codemirror.net/6/docs/ref/#language.StreamLanguage) subclass\nfor stream parsers.\n*/\nclass Language {\n    /**\n    Construct a language object. If you need to invoke this\n    directly, first define a data facet with\n    [`defineLanguageFacet`](https://codemirror.net/6/docs/ref/#language.defineLanguageFacet), and then\n    configure your parser to [attach](https://codemirror.net/6/docs/ref/#language.languageDataProp) it\n    to the language's outer syntax node.\n    */\n    constructor(\n    /**\n    The [language data](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt) facet\n    used for this language.\n    */\n    data, parser, extraExtensions = [], \n    /**\n    A language name.\n    */\n    name = \"\") {\n        this.data = data;\n        this.name = name;\n        // Kludge to define EditorState.tree as a debugging helper,\n        // without the EditorState package actually knowing about\n        // languages and lezer trees.\n        if (!EditorState.prototype.hasOwnProperty(\"tree\"))\n            Object.defineProperty(EditorState.prototype, \"tree\", { get() { return syntaxTree(this); } });\n        this.parser = parser;\n        this.extension = [\n            language.of(this),\n            EditorState.languageData.of((state, pos, side) => {\n                let top = topNodeAt(state, pos, side), data = top.type.prop(languageDataProp);\n                if (!data)\n                    return [];\n                let base = state.facet(data), sub = top.type.prop(sublanguageProp);\n                if (sub) {\n                    let innerNode = top.resolve(pos - top.from, side);\n                    for (let sublang of sub)\n                        if (sublang.test(innerNode, state)) {\n                            let data = state.facet(sublang.facet);\n                            return sublang.type == \"replace\" ? data : data.concat(base);\n                        }\n                }\n                return base;\n            })\n        ].concat(extraExtensions);\n    }\n    /**\n    Query whether this language is active at the given position.\n    */\n    isActiveAt(state, pos, side = -1) {\n        return topNodeAt(state, pos, side).type.prop(languageDataProp) == this.data;\n    }\n    /**\n    Find the document regions that were parsed using this language.\n    The returned regions will _include_ any nested languages rooted\n    in this language, when those exist.\n    */\n    findRegions(state) {\n        let lang = state.facet(language);\n        if ((lang === null || lang === void 0 ? void 0 : lang.data) == this.data)\n            return [{ from: 0, to: state.doc.length }];\n        if (!lang || !lang.allowsNesting)\n            return [];\n        let result = [];\n        let explore = (tree, from) => {\n            if (tree.prop(languageDataProp) == this.data) {\n                result.push({ from, to: from + tree.length });\n                return;\n            }\n            let mount = tree.prop(NodeProp.mounted);\n            if (mount) {\n                if (mount.tree.prop(languageDataProp) == this.data) {\n                    if (mount.overlay)\n                        for (let r of mount.overlay)\n                            result.push({ from: r.from + from, to: r.to + from });\n                    else\n                        result.push({ from: from, to: from + tree.length });\n                    return;\n                }\n                else if (mount.overlay) {\n                    let size = result.length;\n                    explore(mount.tree, mount.overlay[0].from + from);\n                    if (result.length > size)\n                        return;\n                }\n            }\n            for (let i = 0; i < tree.children.length; i++) {\n                let ch = tree.children[i];\n                if (ch instanceof Tree)\n                    explore(ch, tree.positions[i] + from);\n            }\n        };\n        explore(syntaxTree(state), 0);\n        return result;\n    }\n    /**\n    Indicates whether this language allows nested languages. The\n    default implementation returns true.\n    */\n    get allowsNesting() { return true; }\n}\n/**\n@internal\n*/\nLanguage.setState = /*@__PURE__*/StateEffect.define();\nfunction topNodeAt(state, pos, side) {\n    let topLang = state.facet(language), tree = syntaxTree(state).topNode;\n    if (!topLang || topLang.allowsNesting) {\n        for (let node = tree; node; node = node.enter(pos, side, IterMode.ExcludeBuffers))\n            if (node.type.isTop)\n                tree = node;\n    }\n    return tree;\n}\n/**\nA subclass of [`Language`](https://codemirror.net/6/docs/ref/#language.Language) for use with Lezer\n[LR parsers](https://lezer.codemirror.net/docs/ref#lr.LRParser)\nparsers.\n*/\nclass LRLanguage extends Language {\n    constructor(data, parser, name) {\n        super(data, parser, [], name);\n        this.parser = parser;\n    }\n    /**\n    Define a language from a parser.\n    */\n    static define(spec) {\n        let data = defineLanguageFacet(spec.languageData);\n        return new LRLanguage(data, spec.parser.configure({\n            props: [languageDataProp.add(type => type.isTop ? data : undefined)]\n        }), spec.name);\n    }\n    /**\n    Create a new instance of this language with a reconfigured\n    version of its parser and optionally a new name.\n    */\n    configure(options, name) {\n        return new LRLanguage(this.data, this.parser.configure(options), name || this.name);\n    }\n    get allowsNesting() { return this.parser.hasWrappers(); }\n}\n/**\nGet the syntax tree for a state, which is the current (possibly\nincomplete) parse tree of the active\n[language](https://codemirror.net/6/docs/ref/#language.Language), or the empty tree if there is no\nlanguage available.\n*/\nfunction syntaxTree(state) {\n    let field = state.field(Language.state, false);\n    return field ? field.tree : Tree.empty;\n}\n/**\nTry to get a parse tree that spans at least up to `upto`. The\nmethod will do at most `timeout` milliseconds of work to parse\nup to that point if the tree isn't already available.\n*/\nfunction ensureSyntaxTree(state, upto, timeout = 50) {\n    var _a;\n    let parse = (_a = state.field(Language.state, false)) === null || _a === void 0 ? void 0 : _a.context;\n    if (!parse)\n        return null;\n    let oldVieport = parse.viewport;\n    parse.updateViewport({ from: 0, to: upto });\n    let result = parse.isDone(upto) || parse.work(timeout, upto) ? parse.tree : null;\n    parse.updateViewport(oldVieport);\n    return result;\n}\n/**\nQueries whether there is a full syntax tree available up to the\ngiven document position. If there isn't, the background parse\nprocess _might_ still be working and update the tree further, but\nthere is no guarantee of that—the parser will [stop\nworking](https://codemirror.net/6/docs/ref/#language.syntaxParserRunning) when it has spent a\ncertain amount of time or has moved beyond the visible viewport.\nAlways returns false if no language has been enabled.\n*/\nfunction syntaxTreeAvailable(state, upto = state.doc.length) {\n    var _a;\n    return ((_a = state.field(Language.state, false)) === null || _a === void 0 ? void 0 : _a.context.isDone(upto)) || false;\n}\n/**\nMove parsing forward, and update the editor state afterwards to\nreflect the new tree. Will work for at most `timeout`\nmilliseconds. Returns true if the parser managed get to the given\nposition in that time.\n*/\nfunction forceParsing(view, upto = view.viewport.to, timeout = 100) {\n    let success = ensureSyntaxTree(view.state, upto, timeout);\n    if (success != syntaxTree(view.state))\n        view.dispatch({});\n    return !!success;\n}\n/**\nTells you whether the language parser is planning to do more\nparsing work (in a `requestIdleCallback` pseudo-thread) or has\nstopped running, either because it parsed the entire document,\nbecause it spent too much time and was cut off, or because there\nis no language parser enabled.\n*/\nfunction syntaxParserRunning(view) {\n    var _a;\n    return ((_a = view.plugin(parseWorker)) === null || _a === void 0 ? void 0 : _a.isWorking()) || false;\n}\n/**\nLezer-style\n[`Input`](https://lezer.codemirror.net/docs/ref#common.Input)\nobject for a [`Text`](https://codemirror.net/6/docs/ref/#state.Text) object.\n*/\nclass DocInput {\n    /**\n    Create an input object for the given document.\n    */\n    constructor(doc) {\n        this.doc = doc;\n        this.cursorPos = 0;\n        this.string = \"\";\n        this.cursor = doc.iter();\n    }\n    get length() { return this.doc.length; }\n    syncTo(pos) {\n        this.string = this.cursor.next(pos - this.cursorPos).value;\n        this.cursorPos = pos + this.string.length;\n        return this.cursorPos - this.string.length;\n    }\n    chunk(pos) {\n        this.syncTo(pos);\n        return this.string;\n    }\n    get lineChunks() { return true; }\n    read(from, to) {\n        let stringStart = this.cursorPos - this.string.length;\n        if (from < stringStart || to >= this.cursorPos)\n            return this.doc.sliceString(from, to);\n        else\n            return this.string.slice(from - stringStart, to - stringStart);\n    }\n}\nlet currentContext = null;\n/**\nA parse context provided to parsers working on the editor content.\n*/\nclass ParseContext {\n    constructor(parser, \n    /**\n    The current editor state.\n    */\n    state, \n    /**\n    Tree fragments that can be reused by incremental re-parses.\n    */\n    fragments = [], \n    /**\n    @internal\n    */\n    tree, \n    /**\n    @internal\n    */\n    treeLen, \n    /**\n    The current editor viewport (or some overapproximation\n    thereof). Intended to be used for opportunistically avoiding\n    work (in which case\n    [`skipUntilInView`](https://codemirror.net/6/docs/ref/#language.ParseContext.skipUntilInView)\n    should be called to make sure the parser is restarted when the\n    skipped region becomes visible).\n    */\n    viewport, \n    /**\n    @internal\n    */\n    skipped, \n    /**\n    This is where skipping parsers can register a promise that,\n    when resolved, will schedule a new parse. It is cleared when\n    the parse worker picks up the promise. @internal\n    */\n    scheduleOn) {\n        this.parser = parser;\n        this.state = state;\n        this.fragments = fragments;\n        this.tree = tree;\n        this.treeLen = treeLen;\n        this.viewport = viewport;\n        this.skipped = skipped;\n        this.scheduleOn = scheduleOn;\n        this.parse = null;\n        /**\n        @internal\n        */\n        this.tempSkipped = [];\n    }\n    /**\n    @internal\n    */\n    static create(parser, state, viewport) {\n        return new ParseContext(parser, state, [], Tree.empty, 0, viewport, [], null);\n    }\n    startParse() {\n        return this.parser.startParse(new DocInput(this.state.doc), this.fragments);\n    }\n    /**\n    @internal\n    */\n    work(until, upto) {\n        if (upto != null && upto >= this.state.doc.length)\n            upto = undefined;\n        if (this.tree != Tree.empty && this.isDone(upto !== null && upto !== void 0 ? upto : this.state.doc.length)) {\n            this.takeTree();\n            return true;\n        }\n        return this.withContext(() => {\n            var _a;\n            if (typeof until == \"number\") {\n                let endTime = Date.now() + until;\n                until = () => Date.now() > endTime;\n            }\n            if (!this.parse)\n                this.parse = this.startParse();\n            if (upto != null && (this.parse.stoppedAt == null || this.parse.stoppedAt > upto) &&\n                upto < this.state.doc.length)\n                this.parse.stopAt(upto);\n            for (;;) {\n                let done = this.parse.advance();\n                if (done) {\n                    this.fragments = this.withoutTempSkipped(TreeFragment.addTree(done, this.fragments, this.parse.stoppedAt != null));\n                    this.treeLen = (_a = this.parse.stoppedAt) !== null && _a !== void 0 ? _a : this.state.doc.length;\n                    this.tree = done;\n                    this.parse = null;\n                    if (this.treeLen < (upto !== null && upto !== void 0 ? upto : this.state.doc.length))\n                        this.parse = this.startParse();\n                    else\n                        return true;\n                }\n                if (until())\n                    return false;\n            }\n        });\n    }\n    /**\n    @internal\n    */\n    takeTree() {\n        let pos, tree;\n        if (this.parse && (pos = this.parse.parsedPos) >= this.treeLen) {\n            if (this.parse.stoppedAt == null || this.parse.stoppedAt > pos)\n                this.parse.stopAt(pos);\n            this.withContext(() => { while (!(tree = this.parse.advance())) { } });\n            this.treeLen = pos;\n            this.tree = tree;\n            this.fragments = this.withoutTempSkipped(TreeFragment.addTree(this.tree, this.fragments, true));\n            this.parse = null;\n        }\n    }\n    withContext(f) {\n        let prev = currentContext;\n        currentContext = this;\n        try {\n            return f();\n        }\n        finally {\n            currentContext = prev;\n        }\n    }\n    withoutTempSkipped(fragments) {\n        for (let r; r = this.tempSkipped.pop();)\n            fragments = cutFragments(fragments, r.from, r.to);\n        return fragments;\n    }\n    /**\n    @internal\n    */\n    changes(changes, newState) {\n        let { fragments, tree, treeLen, viewport, skipped } = this;\n        this.takeTree();\n        if (!changes.empty) {\n            let ranges = [];\n            changes.iterChangedRanges((fromA, toA, fromB, toB) => ranges.push({ fromA, toA, fromB, toB }));\n            fragments = TreeFragment.applyChanges(fragments, ranges);\n            tree = Tree.empty;\n            treeLen = 0;\n            viewport = { from: changes.mapPos(viewport.from, -1), to: changes.mapPos(viewport.to, 1) };\n            if (this.skipped.length) {\n                skipped = [];\n                for (let r of this.skipped) {\n                    let from = changes.mapPos(r.from, 1), to = changes.mapPos(r.to, -1);\n                    if (from < to)\n                        skipped.push({ from, to });\n                }\n            }\n        }\n        return new ParseContext(this.parser, newState, fragments, tree, treeLen, viewport, skipped, this.scheduleOn);\n    }\n    /**\n    @internal\n    */\n    updateViewport(viewport) {\n        if (this.viewport.from == viewport.from && this.viewport.to == viewport.to)\n            return false;\n        this.viewport = viewport;\n        let startLen = this.skipped.length;\n        for (let i = 0; i < this.skipped.length; i++) {\n            let { from, to } = this.skipped[i];\n            if (from < viewport.to && to > viewport.from) {\n                this.fragments = cutFragments(this.fragments, from, to);\n                this.skipped.splice(i--, 1);\n            }\n        }\n        if (this.skipped.length >= startLen)\n            return false;\n        this.reset();\n        return true;\n    }\n    /**\n    @internal\n    */\n    reset() {\n        if (this.parse) {\n            this.takeTree();\n            this.parse = null;\n        }\n    }\n    /**\n    Notify the parse scheduler that the given region was skipped\n    because it wasn't in view, and the parse should be restarted\n    when it comes into view.\n    */\n    skipUntilInView(from, to) {\n        this.skipped.push({ from, to });\n    }\n    /**\n    Returns a parser intended to be used as placeholder when\n    asynchronously loading a nested parser. It'll skip its input and\n    mark it as not-really-parsed, so that the next update will parse\n    it again.\n    \n    When `until` is given, a reparse will be scheduled when that\n    promise resolves.\n    */\n    static getSkippingParser(until) {\n        return new class extends Parser {\n            createParse(input, fragments, ranges) {\n                let from = ranges[0].from, to = ranges[ranges.length - 1].to;\n                let parser = {\n                    parsedPos: from,\n                    advance() {\n                        let cx = currentContext;\n                        if (cx) {\n                            for (let r of ranges)\n                                cx.tempSkipped.push(r);\n                            if (until)\n                                cx.scheduleOn = cx.scheduleOn ? Promise.all([cx.scheduleOn, until]) : until;\n                        }\n                        this.parsedPos = to;\n                        return new Tree(NodeType.none, [], [], to - from);\n                    },\n                    stoppedAt: null,\n                    stopAt() { }\n                };\n                return parser;\n            }\n        };\n    }\n    /**\n    @internal\n    */\n    isDone(upto) {\n        upto = Math.min(upto, this.state.doc.length);\n        let frags = this.fragments;\n        return this.treeLen >= upto && frags.length && frags[0].from == 0 && frags[0].to >= upto;\n    }\n    /**\n    Get the context for the current parse, or `null` if no editor\n    parse is in progress.\n    */\n    static get() { return currentContext; }\n}\nfunction cutFragments(fragments, from, to) {\n    return TreeFragment.applyChanges(fragments, [{ fromA: from, toA: to, fromB: from, toB: to }]);\n}\nclass LanguageState {\n    constructor(\n    // A mutable parse state that is used to preserve work done during\n    // the lifetime of a state when moving to the next state.\n    context) {\n        this.context = context;\n        this.tree = context.tree;\n    }\n    apply(tr) {\n        if (!tr.docChanged && this.tree == this.context.tree)\n            return this;\n        let newCx = this.context.changes(tr.changes, tr.state);\n        // If the previous parse wasn't done, go forward only up to its\n        // end position or the end of the viewport, to avoid slowing down\n        // state updates with parse work beyond the viewport.\n        let upto = this.context.treeLen == tr.startState.doc.length ? undefined\n            : Math.max(tr.changes.mapPos(this.context.treeLen), newCx.viewport.to);\n        if (!newCx.work(20 /* Work.Apply */, upto))\n            newCx.takeTree();\n        return new LanguageState(newCx);\n    }\n    static init(state) {\n        let vpTo = Math.min(3000 /* Work.InitViewport */, state.doc.length);\n        let parseState = ParseContext.create(state.facet(language).parser, state, { from: 0, to: vpTo });\n        if (!parseState.work(20 /* Work.Apply */, vpTo))\n            parseState.takeTree();\n        return new LanguageState(parseState);\n    }\n}\nLanguage.state = /*@__PURE__*/StateField.define({\n    create: LanguageState.init,\n    update(value, tr) {\n        for (let e of tr.effects)\n            if (e.is(Language.setState))\n                return e.value;\n        if (tr.startState.facet(language) != tr.state.facet(language))\n            return LanguageState.init(tr.state);\n        return value.apply(tr);\n    }\n});\nlet requestIdle = (callback) => {\n    let timeout = setTimeout(() => callback(), 500 /* Work.MaxPause */);\n    return () => clearTimeout(timeout);\n};\nif (typeof requestIdleCallback != \"undefined\")\n    requestIdle = (callback) => {\n        let idle = -1, timeout = setTimeout(() => {\n            idle = requestIdleCallback(callback, { timeout: 500 /* Work.MaxPause */ - 100 /* Work.MinPause */ });\n        }, 100 /* Work.MinPause */);\n        return () => idle < 0 ? clearTimeout(timeout) : cancelIdleCallback(idle);\n    };\nconst isInputPending = typeof navigator != \"undefined\" && ((_a = navigator.scheduling) === null || _a === void 0 ? void 0 : _a.isInputPending)\n    ? () => navigator.scheduling.isInputPending() : null;\nconst parseWorker = /*@__PURE__*/ViewPlugin.fromClass(class ParseWorker {\n    constructor(view) {\n        this.view = view;\n        this.working = null;\n        this.workScheduled = 0;\n        // End of the current time chunk\n        this.chunkEnd = -1;\n        // Milliseconds of budget left for this chunk\n        this.chunkBudget = -1;\n        this.work = this.work.bind(this);\n        this.scheduleWork();\n    }\n    update(update) {\n        let cx = this.view.state.field(Language.state).context;\n        if (cx.updateViewport(update.view.viewport) || this.view.viewport.to > cx.treeLen)\n            this.scheduleWork();\n        if (update.docChanged || update.selectionSet) {\n            if (this.view.hasFocus)\n                this.chunkBudget += 50 /* Work.ChangeBonus */;\n            this.scheduleWork();\n        }\n        this.checkAsyncSchedule(cx);\n    }\n    scheduleWork() {\n        if (this.working)\n            return;\n        let { state } = this.view, field = state.field(Language.state);\n        if (field.tree != field.context.tree || !field.context.isDone(state.doc.length))\n            this.working = requestIdle(this.work);\n    }\n    work(deadline) {\n        this.working = null;\n        let now = Date.now();\n        if (this.chunkEnd < now && (this.chunkEnd < 0 || this.view.hasFocus)) { // Start a new chunk\n            this.chunkEnd = now + 30000 /* Work.ChunkTime */;\n            this.chunkBudget = 3000 /* Work.ChunkBudget */;\n        }\n        if (this.chunkBudget <= 0)\n            return; // No more budget\n        let { state, viewport: { to: vpTo } } = this.view, field = state.field(Language.state);\n        if (field.tree == field.context.tree && field.context.isDone(vpTo + 100000 /* Work.MaxParseAhead */))\n            return;\n        let endTime = Date.now() + Math.min(this.chunkBudget, 100 /* Work.Slice */, deadline && !isInputPending ? Math.max(25 /* Work.MinSlice */, deadline.timeRemaining() - 5) : 1e9);\n        let viewportFirst = field.context.treeLen < vpTo && state.doc.length > vpTo + 1000;\n        let done = field.context.work(() => {\n            return isInputPending && isInputPending() || Date.now() > endTime;\n        }, vpTo + (viewportFirst ? 0 : 100000 /* Work.MaxParseAhead */));\n        this.chunkBudget -= Date.now() - now;\n        if (done || this.chunkBudget <= 0) {\n            field.context.takeTree();\n            this.view.dispatch({ effects: Language.setState.of(new LanguageState(field.context)) });\n        }\n        if (this.chunkBudget > 0 && !(done && !viewportFirst))\n            this.scheduleWork();\n        this.checkAsyncSchedule(field.context);\n    }\n    checkAsyncSchedule(cx) {\n        if (cx.scheduleOn) {\n            this.workScheduled++;\n            cx.scheduleOn\n                .then(() => this.scheduleWork())\n                .catch(err => logException(this.view.state, err))\n                .then(() => this.workScheduled--);\n            cx.scheduleOn = null;\n        }\n    }\n    destroy() {\n        if (this.working)\n            this.working();\n    }\n    isWorking() {\n        return !!(this.working || this.workScheduled > 0);\n    }\n}, {\n    eventHandlers: { focus() { this.scheduleWork(); } }\n});\n/**\nThe facet used to associate a language with an editor state. Used\nby `Language` object's `extension` property (so you don't need to\nmanually wrap your languages in this). Can be used to access the\ncurrent language on a state.\n*/\nconst language = /*@__PURE__*/Facet.define({\n    combine(languages) { return languages.length ? languages[0] : null; },\n    enables: language => [\n        Language.state,\n        parseWorker,\n        EditorView.contentAttributes.compute([language], state => {\n            let lang = state.facet(language);\n            return lang && lang.name ? { \"data-language\": lang.name } : {};\n        })\n    ]\n});\n/**\nThis class bundles a [language](https://codemirror.net/6/docs/ref/#language.Language) with an\noptional set of supporting extensions. Language packages are\nencouraged to export a function that optionally takes a\nconfiguration object and returns a `LanguageSupport` instance, as\nthe main way for client code to use the package.\n*/\nclass LanguageSupport {\n    /**\n    Create a language support object.\n    */\n    constructor(\n    /**\n    The language object.\n    */\n    language, \n    /**\n    An optional set of supporting extensions. When nesting a\n    language in another language, the outer language is encouraged\n    to include the supporting extensions for its inner languages\n    in its own set of support extensions.\n    */\n    support = []) {\n        this.language = language;\n        this.support = support;\n        this.extension = [language, support];\n    }\n}\n/**\nLanguage descriptions are used to store metadata about languages\nand to dynamically load them. Their main role is finding the\nappropriate language for a filename or dynamically loading nested\nparsers.\n*/\nclass LanguageDescription {\n    constructor(\n    /**\n    The name of this language.\n    */\n    name, \n    /**\n    Alternative names for the mode (lowercased, includes `this.name`).\n    */\n    alias, \n    /**\n    File extensions associated with this language.\n    */\n    extensions, \n    /**\n    Optional filename pattern that should be associated with this\n    language.\n    */\n    filename, loadFunc, \n    /**\n    If the language has been loaded, this will hold its value.\n    */\n    support = undefined) {\n        this.name = name;\n        this.alias = alias;\n        this.extensions = extensions;\n        this.filename = filename;\n        this.loadFunc = loadFunc;\n        this.support = support;\n        this.loading = null;\n    }\n    /**\n    Start loading the the language. Will return a promise that\n    resolves to a [`LanguageSupport`](https://codemirror.net/6/docs/ref/#language.LanguageSupport)\n    object when the language successfully loads.\n    */\n    load() {\n        return this.loading || (this.loading = this.loadFunc().then(support => this.support = support, err => { this.loading = null; throw err; }));\n    }\n    /**\n    Create a language description.\n    */\n    static of(spec) {\n        let { load, support } = spec;\n        if (!load) {\n            if (!support)\n                throw new RangeError(\"Must pass either 'load' or 'support' to LanguageDescription.of\");\n            load = () => Promise.resolve(support);\n        }\n        return new LanguageDescription(spec.name, (spec.alias || []).concat(spec.name).map(s => s.toLowerCase()), spec.extensions || [], spec.filename, load, support);\n    }\n    /**\n    Look for a language in the given array of descriptions that\n    matches the filename. Will first match\n    [`filename`](https://codemirror.net/6/docs/ref/#language.LanguageDescription.filename) patterns,\n    and then [extensions](https://codemirror.net/6/docs/ref/#language.LanguageDescription.extensions),\n    and return the first language that matches.\n    */\n    static matchFilename(descs, filename) {\n        for (let d of descs)\n            if (d.filename && d.filename.test(filename))\n                return d;\n        let ext = /\\.([^.]+)$/.exec(filename);\n        if (ext)\n            for (let d of descs)\n                if (d.extensions.indexOf(ext[1]) > -1)\n                    return d;\n        return null;\n    }\n    /**\n    Look for a language whose name or alias matches the the given\n    name (case-insensitively). If `fuzzy` is true, and no direct\n    matchs is found, this'll also search for a language whose name\n    or alias occurs in the string (for names shorter than three\n    characters, only when surrounded by non-word characters).\n    */\n    static matchLanguageName(descs, name, fuzzy = true) {\n        name = name.toLowerCase();\n        for (let d of descs)\n            if (d.alias.some(a => a == name))\n                return d;\n        if (fuzzy)\n            for (let d of descs)\n                for (let a of d.alias) {\n                    let found = name.indexOf(a);\n                    if (found > -1 && (a.length > 2 || !/\\w/.test(name[found - 1]) && !/\\w/.test(name[found + a.length])))\n                        return d;\n                }\n        return null;\n    }\n}\n\n/**\nFacet that defines a way to provide a function that computes the\nappropriate indentation depth, as a column number (see\n[`indentString`](https://codemirror.net/6/docs/ref/#language.indentString)), at the start of a given\nline. A return value of `null` indicates no indentation can be\ndetermined, and the line should inherit the indentation of the one\nabove it. A return value of `undefined` defers to the next indent\nservice.\n*/\nconst indentService = /*@__PURE__*/Facet.define();\n/**\nFacet for overriding the unit by which indentation happens. Should\nbe a string consisting either entirely of the same whitespace\ncharacter. When not set, this defaults to 2 spaces.\n*/\nconst indentUnit = /*@__PURE__*/Facet.define({\n    combine: values => {\n        if (!values.length)\n            return \"  \";\n        let unit = values[0];\n        if (!unit || /\\S/.test(unit) || Array.from(unit).some(e => e != unit[0]))\n            throw new Error(\"Invalid indent unit: \" + JSON.stringify(values[0]));\n        return unit;\n    }\n});\n/**\nReturn the _column width_ of an indent unit in the state.\nDetermined by the [`indentUnit`](https://codemirror.net/6/docs/ref/#language.indentUnit)\nfacet, and [`tabSize`](https://codemirror.net/6/docs/ref/#state.EditorState^tabSize) when that\ncontains tabs.\n*/\nfunction getIndentUnit(state) {\n    let unit = state.facet(indentUnit);\n    return unit.charCodeAt(0) == 9 ? state.tabSize * unit.length : unit.length;\n}\n/**\nCreate an indentation string that covers columns 0 to `cols`.\nWill use tabs for as much of the columns as possible when the\n[`indentUnit`](https://codemirror.net/6/docs/ref/#language.indentUnit) facet contains\ntabs.\n*/\nfunction indentString(state, cols) {\n    let result = \"\", ts = state.tabSize, ch = state.facet(indentUnit)[0];\n    if (ch == \"\\t\") {\n        while (cols >= ts) {\n            result += \"\\t\";\n            cols -= ts;\n        }\n        ch = \" \";\n    }\n    for (let i = 0; i < cols; i++)\n        result += ch;\n    return result;\n}\n/**\nGet the indentation, as a column number, at the given position.\nWill first consult any [indent services](https://codemirror.net/6/docs/ref/#language.indentService)\nthat are registered, and if none of those return an indentation,\nthis will check the syntax tree for the [indent node\nprop](https://codemirror.net/6/docs/ref/#language.indentNodeProp) and use that if found. Returns a\nnumber when an indentation could be determined, and null\notherwise.\n*/\nfunction getIndentation(context, pos) {\n    if (context instanceof EditorState)\n        context = new IndentContext(context);\n    for (let service of context.state.facet(indentService)) {\n        let result = service(context, pos);\n        if (result !== undefined)\n            return result;\n    }\n    let tree = syntaxTree(context.state);\n    return tree.length >= pos ? syntaxIndentation(context, tree, pos) : null;\n}\n/**\nCreate a change set that auto-indents all lines touched by the\ngiven document range.\n*/\nfunction indentRange(state, from, to) {\n    let updated = Object.create(null);\n    let context = new IndentContext(state, { overrideIndentation: start => { var _a; return (_a = updated[start]) !== null && _a !== void 0 ? _a : -1; } });\n    let changes = [];\n    for (let pos = from; pos <= to;) {\n        let line = state.doc.lineAt(pos);\n        pos = line.to + 1;\n        let indent = getIndentation(context, line.from);\n        if (indent == null)\n            continue;\n        if (!/\\S/.test(line.text))\n            indent = 0;\n        let cur = /^\\s*/.exec(line.text)[0];\n        let norm = indentString(state, indent);\n        if (cur != norm) {\n            updated[line.from] = indent;\n            changes.push({ from: line.from, to: line.from + cur.length, insert: norm });\n        }\n    }\n    return state.changes(changes);\n}\n/**\nIndentation contexts are used when calling [indentation\nservices](https://codemirror.net/6/docs/ref/#language.indentService). They provide helper utilities\nuseful in indentation logic, and can selectively override the\nindentation reported for some lines.\n*/\nclass IndentContext {\n    /**\n    Create an indent context.\n    */\n    constructor(\n    /**\n    The editor state.\n    */\n    state, \n    /**\n    @internal\n    */\n    options = {}) {\n        this.state = state;\n        this.options = options;\n        this.unit = getIndentUnit(state);\n    }\n    /**\n    Get a description of the line at the given position, taking\n    [simulated line\n    breaks](https://codemirror.net/6/docs/ref/#language.IndentContext.constructor^options.simulateBreak)\n    into account. If there is such a break at `pos`, the `bias`\n    argument determines whether the part of the line line before or\n    after the break is used.\n    */\n    lineAt(pos, bias = 1) {\n        let line = this.state.doc.lineAt(pos);\n        let { simulateBreak, simulateDoubleBreak } = this.options;\n        if (simulateBreak != null && simulateBreak >= line.from && simulateBreak <= line.to) {\n            if (simulateDoubleBreak && simulateBreak == pos)\n                return { text: \"\", from: pos };\n            else if (bias < 0 ? simulateBreak < pos : simulateBreak <= pos)\n                return { text: line.text.slice(simulateBreak - line.from), from: simulateBreak };\n            else\n                return { text: line.text.slice(0, simulateBreak - line.from), from: line.from };\n        }\n        return line;\n    }\n    /**\n    Get the text directly after `pos`, either the entire line\n    or the next 100 characters, whichever is shorter.\n    */\n    textAfterPos(pos, bias = 1) {\n        if (this.options.simulateDoubleBreak && pos == this.options.simulateBreak)\n            return \"\";\n        let { text, from } = this.lineAt(pos, bias);\n        return text.slice(pos - from, Math.min(text.length, pos + 100 - from));\n    }\n    /**\n    Find the column for the given position.\n    */\n    column(pos, bias = 1) {\n        let { text, from } = this.lineAt(pos, bias);\n        let result = this.countColumn(text, pos - from);\n        let override = this.options.overrideIndentation ? this.options.overrideIndentation(from) : -1;\n        if (override > -1)\n            result += override - this.countColumn(text, text.search(/\\S|$/));\n        return result;\n    }\n    /**\n    Find the column position (taking tabs into account) of the given\n    position in the given string.\n    */\n    countColumn(line, pos = line.length) {\n        return countColumn(line, this.state.tabSize, pos);\n    }\n    /**\n    Find the indentation column of the line at the given point.\n    */\n    lineIndent(pos, bias = 1) {\n        let { text, from } = this.lineAt(pos, bias);\n        let override = this.options.overrideIndentation;\n        if (override) {\n            let overriden = override(from);\n            if (overriden > -1)\n                return overriden;\n        }\n        return this.countColumn(text, text.search(/\\S|$/));\n    }\n    /**\n    Returns the [simulated line\n    break](https://codemirror.net/6/docs/ref/#language.IndentContext.constructor^options.simulateBreak)\n    for this context, if any.\n    */\n    get simulatedBreak() {\n        return this.options.simulateBreak || null;\n    }\n}\n/**\nA syntax tree node prop used to associate indentation strategies\nwith node types. Such a strategy is a function from an indentation\ncontext to a column number (see also\n[`indentString`](https://codemirror.net/6/docs/ref/#language.indentString)) or null, where null\nindicates that no definitive indentation can be determined.\n*/\nconst indentNodeProp = /*@__PURE__*/new NodeProp();\n// Compute the indentation for a given position from the syntax tree.\nfunction syntaxIndentation(cx, ast, pos) {\n    let stack = ast.resolveStack(pos);\n    let inner = ast.resolveInner(pos, -1).resolve(pos, 0).enterUnfinishedNodesBefore(pos);\n    if (inner != stack.node) {\n        let add = [];\n        for (let cur = inner; cur != stack.node; cur = cur.parent)\n            add.push(cur);\n        for (let i = add.length - 1; i >= 0; i--)\n            stack = { node: add[i], next: stack };\n    }\n    return indentFor(stack, cx, pos);\n}\nfunction indentFor(stack, cx, pos) {\n    for (let cur = stack; cur; cur = cur.next) {\n        let strategy = indentStrategy(cur.node);\n        if (strategy)\n            return strategy(TreeIndentContext.create(cx, pos, cur));\n    }\n    return 0;\n}\nfunction ignoreClosed(cx) {\n    return cx.pos == cx.options.simulateBreak && cx.options.simulateDoubleBreak;\n}\nfunction indentStrategy(tree) {\n    let strategy = tree.type.prop(indentNodeProp);\n    if (strategy)\n        return strategy;\n    let first = tree.firstChild, close;\n    if (first && (close = first.type.prop(NodeProp.closedBy))) {\n        let last = tree.lastChild, closed = last && close.indexOf(last.name) > -1;\n        return cx => delimitedStrategy(cx, true, 1, undefined, closed && !ignoreClosed(cx) ? last.from : undefined);\n    }\n    return tree.parent == null ? topIndent : null;\n}\nfunction topIndent() { return 0; }\n/**\nObjects of this type provide context information and helper\nmethods to indentation functions registered on syntax nodes.\n*/\nclass TreeIndentContext extends IndentContext {\n    constructor(base, \n    /**\n    The position at which indentation is being computed.\n    */\n    pos, \n    /**\n    @internal\n    */\n    context) {\n        super(base.state, base.options);\n        this.base = base;\n        this.pos = pos;\n        this.context = context;\n    }\n    /**\n    The syntax tree node to which the indentation strategy\n    applies.\n    */\n    get node() { return this.context.node; }\n    /**\n    @internal\n    */\n    static create(base, pos, context) {\n        return new TreeIndentContext(base, pos, context);\n    }\n    /**\n    Get the text directly after `this.pos`, either the entire line\n    or the next 100 characters, whichever is shorter.\n    */\n    get textAfter() {\n        return this.textAfterPos(this.pos);\n    }\n    /**\n    Get the indentation at the reference line for `this.node`, which\n    is the line on which it starts, unless there is a node that is\n    _not_ a parent of this node covering the start of that line. If\n    so, the line at the start of that node is tried, again skipping\n    on if it is covered by another such node.\n    */\n    get baseIndent() {\n        return this.baseIndentFor(this.node);\n    }\n    /**\n    Get the indentation for the reference line of the given node\n    (see [`baseIndent`](https://codemirror.net/6/docs/ref/#language.TreeIndentContext.baseIndent)).\n    */\n    baseIndentFor(node) {\n        let line = this.state.doc.lineAt(node.from);\n        // Skip line starts that are covered by a sibling (or cousin, etc)\n        for (;;) {\n            let atBreak = node.resolve(line.from);\n            while (atBreak.parent && atBreak.parent.from == atBreak.from)\n                atBreak = atBreak.parent;\n            if (isParent(atBreak, node))\n                break;\n            line = this.state.doc.lineAt(atBreak.from);\n        }\n        return this.lineIndent(line.from);\n    }\n    /**\n    Continue looking for indentations in the node's parent nodes,\n    and return the result of that.\n    */\n    continue() {\n        return indentFor(this.context.next, this.base, this.pos);\n    }\n}\nfunction isParent(parent, of) {\n    for (let cur = of; cur; cur = cur.parent)\n        if (parent == cur)\n            return true;\n    return false;\n}\n// Check whether a delimited node is aligned (meaning there are\n// non-skipped nodes on the same line as the opening delimiter). And\n// if so, return the opening token.\nfunction bracketedAligned(context) {\n    let tree = context.node;\n    let openToken = tree.childAfter(tree.from), last = tree.lastChild;\n    if (!openToken)\n        return null;\n    let sim = context.options.simulateBreak;\n    let openLine = context.state.doc.lineAt(openToken.from);\n    let lineEnd = sim == null || sim <= openLine.from ? openLine.to : Math.min(openLine.to, sim);\n    for (let pos = openToken.to;;) {\n        let next = tree.childAfter(pos);\n        if (!next || next == last)\n            return null;\n        if (!next.type.isSkipped) {\n            if (next.from >= lineEnd)\n                return null;\n            let space = /^ */.exec(openLine.text.slice(openToken.to - openLine.from))[0].length;\n            return { from: openToken.from, to: openToken.to + space };\n        }\n        pos = next.to;\n    }\n}\n/**\nAn indentation strategy for delimited (usually bracketed) nodes.\nWill, by default, indent one unit more than the parent's base\nindent unless the line starts with a closing token. When `align`\nis true and there are non-skipped nodes on the node's opening\nline, the content of the node will be aligned with the end of the\nopening node, like this:\n\n    foo(bar,\n        baz)\n*/\nfunction delimitedIndent({ closing, align = true, units = 1 }) {\n    return (context) => delimitedStrategy(context, align, units, closing);\n}\nfunction delimitedStrategy(context, align, units, closing, closedAt) {\n    let after = context.textAfter, space = after.match(/^\\s*/)[0].length;\n    let closed = closing && after.slice(space, space + closing.length) == closing || closedAt == context.pos + space;\n    let aligned = align ? bracketedAligned(context) : null;\n    if (aligned)\n        return closed ? context.column(aligned.from) : context.column(aligned.to);\n    return context.baseIndent + (closed ? 0 : context.unit * units);\n}\n/**\nAn indentation strategy that aligns a node's content to its base\nindentation.\n*/\nconst flatIndent = (context) => context.baseIndent;\n/**\nCreates an indentation strategy that, by default, indents\ncontinued lines one unit more than the node's base indentation.\nYou can provide `except` to prevent indentation of lines that\nmatch a pattern (for example `/^else\\b/` in `if`/`else`\nconstructs), and you can change the amount of units used with the\n`units` option.\n*/\nfunction continuedIndent({ except, units = 1 } = {}) {\n    return (context) => {\n        let matchExcept = except && except.test(context.textAfter);\n        return context.baseIndent + (matchExcept ? 0 : units * context.unit);\n    };\n}\nconst DontIndentBeyond = 200;\n/**\nEnables reindentation on input. When a language defines an\n`indentOnInput` field in its [language\ndata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt), which must hold a regular\nexpression, the line at the cursor will be reindented whenever new\ntext is typed and the input from the start of the line up to the\ncursor matches that regexp.\n\nTo avoid unneccesary reindents, it is recommended to start the\nregexp with `^` (usually followed by `\\s*`), and end it with `$`.\nFor example, `/^\\s*\\}$/` will reindent when a closing brace is\nadded at the start of a line.\n*/\nfunction indentOnInput() {\n    return EditorState.transactionFilter.of(tr => {\n        if (!tr.docChanged || !tr.isUserEvent(\"input.type\") && !tr.isUserEvent(\"input.complete\"))\n            return tr;\n        let rules = tr.startState.languageDataAt(\"indentOnInput\", tr.startState.selection.main.head);\n        if (!rules.length)\n            return tr;\n        let doc = tr.newDoc, { head } = tr.newSelection.main, line = doc.lineAt(head);\n        if (head > line.from + DontIndentBeyond)\n            return tr;\n        let lineStart = doc.sliceString(line.from, head);\n        if (!rules.some(r => r.test(lineStart)))\n            return tr;\n        let { state } = tr, last = -1, changes = [];\n        for (let { head } of state.selection.ranges) {\n            let line = state.doc.lineAt(head);\n            if (line.from == last)\n                continue;\n            last = line.from;\n            let indent = getIndentation(state, line.from);\n            if (indent == null)\n                continue;\n            let cur = /^\\s*/.exec(line.text)[0];\n            let norm = indentString(state, indent);\n            if (cur != norm)\n                changes.push({ from: line.from, to: line.from + cur.length, insert: norm });\n        }\n        return changes.length ? [tr, { changes, sequential: true }] : tr;\n    });\n}\n\n/**\nA facet that registers a code folding service. When called with\nthe extent of a line, such a function should return a foldable\nrange that starts on that line (but continues beyond it), if one\ncan be found.\n*/\nconst foldService = /*@__PURE__*/Facet.define();\n/**\nThis node prop is used to associate folding information with\nsyntax node types. Given a syntax node, it should check whether\nthat tree is foldable and return the range that can be collapsed\nwhen it is.\n*/\nconst foldNodeProp = /*@__PURE__*/new NodeProp();\n/**\n[Fold](https://codemirror.net/6/docs/ref/#language.foldNodeProp) function that folds everything but\nthe first and the last child of a syntax node. Useful for nodes\nthat start and end with delimiters.\n*/\nfunction foldInside(node) {\n    let first = node.firstChild, last = node.lastChild;\n    return first && first.to < last.from ? { from: first.to, to: last.type.isError ? node.to : last.from } : null;\n}\nfunction syntaxFolding(state, start, end) {\n    let tree = syntaxTree(state);\n    if (tree.length < end)\n        return null;\n    let stack = tree.resolveStack(end, 1);\n    let found = null;\n    for (let iter = stack; iter; iter = iter.next) {\n        let cur = iter.node;\n        if (cur.to <= end || cur.from > end)\n            continue;\n        if (found && cur.from < start)\n            break;\n        let prop = cur.type.prop(foldNodeProp);\n        if (prop && (cur.to < tree.length - 50 || tree.length == state.doc.length || !isUnfinished(cur))) {\n            let value = prop(cur, state);\n            if (value && value.from <= end && value.from >= start && value.to > end)\n                found = value;\n        }\n    }\n    return found;\n}\nfunction isUnfinished(node) {\n    let ch = node.lastChild;\n    return ch && ch.to == node.to && ch.type.isError;\n}\n/**\nCheck whether the given line is foldable. First asks any fold\nservices registered through\n[`foldService`](https://codemirror.net/6/docs/ref/#language.foldService), and if none of them return\na result, tries to query the [fold node\nprop](https://codemirror.net/6/docs/ref/#language.foldNodeProp) of syntax nodes that cover the end\nof the line.\n*/\nfunction foldable(state, lineStart, lineEnd) {\n    for (let service of state.facet(foldService)) {\n        let result = service(state, lineStart, lineEnd);\n        if (result)\n            return result;\n    }\n    return syntaxFolding(state, lineStart, lineEnd);\n}\nfunction mapRange(range, mapping) {\n    let from = mapping.mapPos(range.from, 1), to = mapping.mapPos(range.to, -1);\n    return from >= to ? undefined : { from, to };\n}\n/**\nState effect that can be attached to a transaction to fold the\ngiven range. (You probably only need this in exceptional\ncircumstances—usually you'll just want to let\n[`foldCode`](https://codemirror.net/6/docs/ref/#language.foldCode) and the [fold\ngutter](https://codemirror.net/6/docs/ref/#language.foldGutter) create the transactions.)\n*/\nconst foldEffect = /*@__PURE__*/StateEffect.define({ map: mapRange });\n/**\nState effect that unfolds the given range (if it was folded).\n*/\nconst unfoldEffect = /*@__PURE__*/StateEffect.define({ map: mapRange });\nfunction selectedLines(view) {\n    let lines = [];\n    for (let { head } of view.state.selection.ranges) {\n        if (lines.some(l => l.from <= head && l.to >= head))\n            continue;\n        lines.push(view.lineBlockAt(head));\n    }\n    return lines;\n}\n/**\nThe state field that stores the folded ranges (as a [decoration\nset](https://codemirror.net/6/docs/ref/#view.DecorationSet)). Can be passed to\n[`EditorState.toJSON`](https://codemirror.net/6/docs/ref/#state.EditorState.toJSON) and\n[`fromJSON`](https://codemirror.net/6/docs/ref/#state.EditorState^fromJSON) to serialize the fold\nstate.\n*/\nconst foldState = /*@__PURE__*/StateField.define({\n    create() {\n        return Decoration.none;\n    },\n    update(folded, tr) {\n        folded = folded.map(tr.changes);\n        for (let e of tr.effects) {\n            if (e.is(foldEffect) && !foldExists(folded, e.value.from, e.value.to)) {\n                let { preparePlaceholder } = tr.state.facet(foldConfig);\n                let widget = !preparePlaceholder ? foldWidget :\n                    Decoration.replace({ widget: new PreparedFoldWidget(preparePlaceholder(tr.state, e.value)) });\n                folded = folded.update({ add: [widget.range(e.value.from, e.value.to)] });\n            }\n            else if (e.is(unfoldEffect)) {\n                folded = folded.update({ filter: (from, to) => e.value.from != from || e.value.to != to,\n                    filterFrom: e.value.from, filterTo: e.value.to });\n            }\n        }\n        // Clear folded ranges that cover the selection head\n        if (tr.selection) {\n            let onSelection = false, { head } = tr.selection.main;\n            folded.between(head, head, (a, b) => { if (a < head && b > head)\n                onSelection = true; });\n            if (onSelection)\n                folded = folded.update({\n                    filterFrom: head,\n                    filterTo: head,\n                    filter: (a, b) => b <= head || a >= head\n                });\n        }\n        return folded;\n    },\n    provide: f => EditorView.decorations.from(f),\n    toJSON(folded, state) {\n        let ranges = [];\n        folded.between(0, state.doc.length, (from, to) => { ranges.push(from, to); });\n        return ranges;\n    },\n    fromJSON(value) {\n        if (!Array.isArray(value) || value.length % 2)\n            throw new RangeError(\"Invalid JSON for fold state\");\n        let ranges = [];\n        for (let i = 0; i < value.length;) {\n            let from = value[i++], to = value[i++];\n            if (typeof from != \"number\" || typeof to != \"number\")\n                throw new RangeError(\"Invalid JSON for fold state\");\n            ranges.push(foldWidget.range(from, to));\n        }\n        return Decoration.set(ranges, true);\n    }\n});\n/**\nGet a [range set](https://codemirror.net/6/docs/ref/#state.RangeSet) containing the folded ranges\nin the given state.\n*/\nfunction foldedRanges(state) {\n    return state.field(foldState, false) || RangeSet.empty;\n}\nfunction findFold(state, from, to) {\n    var _a;\n    let found = null;\n    (_a = state.field(foldState, false)) === null || _a === void 0 ? void 0 : _a.between(from, to, (from, to) => {\n        if (!found || found.from > from)\n            found = { from, to };\n    });\n    return found;\n}\nfunction foldExists(folded, from, to) {\n    let found = false;\n    folded.between(from, from, (a, b) => { if (a == from && b == to)\n        found = true; });\n    return found;\n}\nfunction maybeEnable(state, other) {\n    return state.field(foldState, false) ? other : other.concat(StateEffect.appendConfig.of(codeFolding()));\n}\n/**\nFold the lines that are selected, if possible.\n*/\nconst foldCode = view => {\n    for (let line of selectedLines(view)) {\n        let range = foldable(view.state, line.from, line.to);\n        if (range) {\n            view.dispatch({ effects: maybeEnable(view.state, [foldEffect.of(range), announceFold(view, range)]) });\n            return true;\n        }\n    }\n    return false;\n};\n/**\nUnfold folded ranges on selected lines.\n*/\nconst unfoldCode = view => {\n    if (!view.state.field(foldState, false))\n        return false;\n    let effects = [];\n    for (let line of selectedLines(view)) {\n        let folded = findFold(view.state, line.from, line.to);\n        if (folded)\n            effects.push(unfoldEffect.of(folded), announceFold(view, folded, false));\n    }\n    if (effects.length)\n        view.dispatch({ effects });\n    return effects.length > 0;\n};\nfunction announceFold(view, range, fold = true) {\n    let lineFrom = view.state.doc.lineAt(range.from).number, lineTo = view.state.doc.lineAt(range.to).number;\n    return EditorView.announce.of(`${view.state.phrase(fold ? \"Folded lines\" : \"Unfolded lines\")} ${lineFrom} ${view.state.phrase(\"to\")} ${lineTo}.`);\n}\n/**\nFold all top-level foldable ranges. Note that, in most cases,\nfolding information will depend on the [syntax\ntree](https://codemirror.net/6/docs/ref/#language.syntaxTree), and folding everything may not work\nreliably when the document hasn't been fully parsed (either\nbecause the editor state was only just initialized, or because the\ndocument is so big that the parser decided not to parse it\nentirely).\n*/\nconst foldAll = view => {\n    let { state } = view, effects = [];\n    for (let pos = 0; pos < state.doc.length;) {\n        let line = view.lineBlockAt(pos), range = foldable(state, line.from, line.to);\n        if (range)\n            effects.push(foldEffect.of(range));\n        pos = (range ? view.lineBlockAt(range.to) : line).to + 1;\n    }\n    if (effects.length)\n        view.dispatch({ effects: maybeEnable(view.state, effects) });\n    return !!effects.length;\n};\n/**\nUnfold all folded code.\n*/\nconst unfoldAll = view => {\n    let field = view.state.field(foldState, false);\n    if (!field || !field.size)\n        return false;\n    let effects = [];\n    field.between(0, view.state.doc.length, (from, to) => { effects.push(unfoldEffect.of({ from, to })); });\n    view.dispatch({ effects });\n    return true;\n};\n// Find the foldable region containing the given line, if one exists\nfunction foldableContainer(view, lineBlock) {\n    // Look backwards through line blocks until we find a foldable region that\n    // intersects with the line\n    for (let line = lineBlock;;) {\n        let foldableRegion = foldable(view.state, line.from, line.to);\n        if (foldableRegion && foldableRegion.to > lineBlock.from)\n            return foldableRegion;\n        if (!line.from)\n            return null;\n        line = view.lineBlockAt(line.from - 1);\n    }\n}\n/**\nToggle folding at cursors. Unfolds if there is an existing fold\nstarting in that line, tries to find a foldable range around it\notherwise.\n*/\nconst toggleFold = (view) => {\n    let effects = [];\n    for (let line of selectedLines(view)) {\n        let folded = findFold(view.state, line.from, line.to);\n        if (folded) {\n            effects.push(unfoldEffect.of(folded), announceFold(view, folded, false));\n        }\n        else {\n            let foldRange = foldableContainer(view, line);\n            if (foldRange)\n                effects.push(foldEffect.of(foldRange), announceFold(view, foldRange));\n        }\n    }\n    if (effects.length > 0)\n        view.dispatch({ effects: maybeEnable(view.state, effects) });\n    return !!effects.length;\n};\n/**\nDefault fold-related key bindings.\n\n - Ctrl-Shift-[ (Cmd-Alt-[ on macOS): [`foldCode`](https://codemirror.net/6/docs/ref/#language.foldCode).\n - Ctrl-Shift-] (Cmd-Alt-] on macOS): [`unfoldCode`](https://codemirror.net/6/docs/ref/#language.unfoldCode).\n - Ctrl-Alt-[: [`foldAll`](https://codemirror.net/6/docs/ref/#language.foldAll).\n - Ctrl-Alt-]: [`unfoldAll`](https://codemirror.net/6/docs/ref/#language.unfoldAll).\n*/\nconst foldKeymap = [\n    { key: \"Ctrl-Shift-[\", mac: \"Cmd-Alt-[\", run: foldCode },\n    { key: \"Ctrl-Shift-]\", mac: \"Cmd-Alt-]\", run: unfoldCode },\n    { key: \"Ctrl-Alt-[\", run: foldAll },\n    { key: \"Ctrl-Alt-]\", run: unfoldAll }\n];\nconst defaultConfig = {\n    placeholderDOM: null,\n    preparePlaceholder: null,\n    placeholderText: \"…\"\n};\nconst foldConfig = /*@__PURE__*/Facet.define({\n    combine(values) { return combineConfig(values, defaultConfig); }\n});\n/**\nCreate an extension that configures code folding.\n*/\nfunction codeFolding(config) {\n    let result = [foldState, baseTheme$1];\n    if (config)\n        result.push(foldConfig.of(config));\n    return result;\n}\nfunction widgetToDOM(view, prepared) {\n    let { state } = view, conf = state.facet(foldConfig);\n    let onclick = (event) => {\n        let line = view.lineBlockAt(view.posAtDOM(event.target));\n        let folded = findFold(view.state, line.from, line.to);\n        if (folded)\n            view.dispatch({ effects: unfoldEffect.of(folded) });\n        event.preventDefault();\n    };\n    if (conf.placeholderDOM)\n        return conf.placeholderDOM(view, onclick, prepared);\n    let element = document.createElement(\"span\");\n    element.textContent = conf.placeholderText;\n    element.setAttribute(\"aria-label\", state.phrase(\"folded code\"));\n    element.title = state.phrase(\"unfold\");\n    element.className = \"cm-foldPlaceholder\";\n    element.onclick = onclick;\n    return element;\n}\nconst foldWidget = /*@__PURE__*/Decoration.replace({ widget: /*@__PURE__*/new class extends WidgetType {\n        toDOM(view) { return widgetToDOM(view, null); }\n    } });\nclass PreparedFoldWidget extends WidgetType {\n    constructor(value) {\n        super();\n        this.value = value;\n    }\n    eq(other) { return this.value == other.value; }\n    toDOM(view) { return widgetToDOM(view, this.value); }\n}\nconst foldGutterDefaults = {\n    openText: \"⌄\",\n    closedText: \"›\",\n    markerDOM: null,\n    domEventHandlers: {},\n    foldingChanged: () => false\n};\nclass FoldMarker extends GutterMarker {\n    constructor(config, open) {\n        super();\n        this.config = config;\n        this.open = open;\n    }\n    eq(other) { return this.config == other.config && this.open == other.open; }\n    toDOM(view) {\n        if (this.config.markerDOM)\n            return this.config.markerDOM(this.open);\n        let span = document.createElement(\"span\");\n        span.textContent = this.open ? this.config.openText : this.config.closedText;\n        span.title = view.state.phrase(this.open ? \"Fold line\" : \"Unfold line\");\n        return span;\n    }\n}\n/**\nCreate an extension that registers a fold gutter, which shows a\nfold status indicator before foldable lines (which can be clicked\nto fold or unfold the line).\n*/\nfunction foldGutter(config = {}) {\n    let fullConfig = Object.assign(Object.assign({}, foldGutterDefaults), config);\n    let canFold = new FoldMarker(fullConfig, true), canUnfold = new FoldMarker(fullConfig, false);\n    let markers = ViewPlugin.fromClass(class {\n        constructor(view) {\n            this.from = view.viewport.from;\n            this.markers = this.buildMarkers(view);\n        }\n        update(update) {\n            if (update.docChanged || update.viewportChanged ||\n                update.startState.facet(language) != update.state.facet(language) ||\n                update.startState.field(foldState, false) != update.state.field(foldState, false) ||\n                syntaxTree(update.startState) != syntaxTree(update.state) ||\n                fullConfig.foldingChanged(update))\n                this.markers = this.buildMarkers(update.view);\n        }\n        buildMarkers(view) {\n            let builder = new RangeSetBuilder();\n            for (let line of view.viewportLineBlocks) {\n                let mark = findFold(view.state, line.from, line.to) ? canUnfold\n                    : foldable(view.state, line.from, line.to) ? canFold : null;\n                if (mark)\n                    builder.add(line.from, line.from, mark);\n            }\n            return builder.finish();\n        }\n    });\n    let { domEventHandlers } = fullConfig;\n    return [\n        markers,\n        gutter({\n            class: \"cm-foldGutter\",\n            markers(view) { var _a; return ((_a = view.plugin(markers)) === null || _a === void 0 ? void 0 : _a.markers) || RangeSet.empty; },\n            initialSpacer() {\n                return new FoldMarker(fullConfig, false);\n            },\n            domEventHandlers: Object.assign(Object.assign({}, domEventHandlers), { click: (view, line, event) => {\n                    if (domEventHandlers.click && domEventHandlers.click(view, line, event))\n                        return true;\n                    let folded = findFold(view.state, line.from, line.to);\n                    if (folded) {\n                        view.dispatch({ effects: unfoldEffect.of(folded) });\n                        return true;\n                    }\n                    let range = foldable(view.state, line.from, line.to);\n                    if (range) {\n                        view.dispatch({ effects: foldEffect.of(range) });\n                        return true;\n                    }\n                    return false;\n                } })\n        }),\n        codeFolding()\n    ];\n}\nconst baseTheme$1 = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-foldPlaceholder\": {\n        backgroundColor: \"#eee\",\n        border: \"1px solid #ddd\",\n        color: \"#888\",\n        borderRadius: \".2em\",\n        margin: \"0 1px\",\n        padding: \"0 1px\",\n        cursor: \"pointer\"\n    },\n    \".cm-foldGutter span\": {\n        padding: \"0 1px\",\n        cursor: \"pointer\"\n    }\n});\n\n/**\nA highlight style associates CSS styles with higlighting\n[tags](https://lezer.codemirror.net/docs/ref#highlight.Tag).\n*/\nclass HighlightStyle {\n    constructor(\n    /**\n    The tag styles used to create this highlight style.\n    */\n    specs, options) {\n        this.specs = specs;\n        let modSpec;\n        function def(spec) {\n            let cls = StyleModule.newName();\n            (modSpec || (modSpec = Object.create(null)))[\".\" + cls] = spec;\n            return cls;\n        }\n        const all = typeof options.all == \"string\" ? options.all : options.all ? def(options.all) : undefined;\n        const scopeOpt = options.scope;\n        this.scope = scopeOpt instanceof Language ? (type) => type.prop(languageDataProp) == scopeOpt.data\n            : scopeOpt ? (type) => type == scopeOpt : undefined;\n        this.style = tagHighlighter(specs.map(style => ({\n            tag: style.tag,\n            class: style.class || def(Object.assign({}, style, { tag: null }))\n        })), {\n            all,\n        }).style;\n        this.module = modSpec ? new StyleModule(modSpec) : null;\n        this.themeType = options.themeType;\n    }\n    /**\n    Create a highlighter style that associates the given styles to\n    the given tags. The specs must be objects that hold a style tag\n    or array of tags in their `tag` property, and either a single\n    `class` property providing a static CSS class (for highlighter\n    that rely on external styling), or a\n    [`style-mod`](https://github.com/marijnh/style-mod#documentation)-style\n    set of CSS properties (which define the styling for those tags).\n    \n    The CSS rules created for a highlighter will be emitted in the\n    order of the spec's properties. That means that for elements that\n    have multiple tags associated with them, styles defined further\n    down in the list will have a higher CSS precedence than styles\n    defined earlier.\n    */\n    static define(specs, options) {\n        return new HighlightStyle(specs, options || {});\n    }\n}\nconst highlighterFacet = /*@__PURE__*/Facet.define();\nconst fallbackHighlighter = /*@__PURE__*/Facet.define({\n    combine(values) { return values.length ? [values[0]] : null; }\n});\nfunction getHighlighters(state) {\n    let main = state.facet(highlighterFacet);\n    return main.length ? main : state.facet(fallbackHighlighter);\n}\n/**\nWrap a highlighter in an editor extension that uses it to apply\nsyntax highlighting to the editor content.\n\nWhen multiple (non-fallback) styles are provided, the styling\napplied is the union of the classes they emit.\n*/\nfunction syntaxHighlighting(highlighter, options) {\n    let ext = [treeHighlighter], themeType;\n    if (highlighter instanceof HighlightStyle) {\n        if (highlighter.module)\n            ext.push(EditorView.styleModule.of(highlighter.module));\n        themeType = highlighter.themeType;\n    }\n    if (options === null || options === void 0 ? void 0 : options.fallback)\n        ext.push(fallbackHighlighter.of(highlighter));\n    else if (themeType)\n        ext.push(highlighterFacet.computeN([EditorView.darkTheme], state => {\n            return state.facet(EditorView.darkTheme) == (themeType == \"dark\") ? [highlighter] : [];\n        }));\n    else\n        ext.push(highlighterFacet.of(highlighter));\n    return ext;\n}\n/**\nReturns the CSS classes (if any) that the highlighters active in\nthe state would assign to the given style\n[tags](https://lezer.codemirror.net/docs/ref#highlight.Tag) and\n(optional) language\n[scope](https://codemirror.net/6/docs/ref/#language.HighlightStyle^define^options.scope).\n*/\nfunction highlightingFor(state, tags, scope) {\n    let highlighters = getHighlighters(state);\n    let result = null;\n    if (highlighters)\n        for (let highlighter of highlighters) {\n            if (!highlighter.scope || scope && highlighter.scope(scope)) {\n                let cls = highlighter.style(tags);\n                if (cls)\n                    result = result ? result + \" \" + cls : cls;\n            }\n        }\n    return result;\n}\nclass TreeHighlighter {\n    constructor(view) {\n        this.markCache = Object.create(null);\n        this.tree = syntaxTree(view.state);\n        this.decorations = this.buildDeco(view, getHighlighters(view.state));\n        this.decoratedTo = view.viewport.to;\n    }\n    update(update) {\n        let tree = syntaxTree(update.state), highlighters = getHighlighters(update.state);\n        let styleChange = highlighters != getHighlighters(update.startState);\n        let { viewport } = update.view, decoratedToMapped = update.changes.mapPos(this.decoratedTo, 1);\n        if (tree.length < viewport.to && !styleChange && tree.type == this.tree.type && decoratedToMapped >= viewport.to) {\n            this.decorations = this.decorations.map(update.changes);\n            this.decoratedTo = decoratedToMapped;\n        }\n        else if (tree != this.tree || update.viewportChanged || styleChange) {\n            this.tree = tree;\n            this.decorations = this.buildDeco(update.view, highlighters);\n            this.decoratedTo = viewport.to;\n        }\n    }\n    buildDeco(view, highlighters) {\n        if (!highlighters || !this.tree.length)\n            return Decoration.none;\n        let builder = new RangeSetBuilder();\n        for (let { from, to } of view.visibleRanges) {\n            highlightTree(this.tree, highlighters, (from, to, style) => {\n                builder.add(from, to, this.markCache[style] || (this.markCache[style] = Decoration.mark({ class: style })));\n            }, from, to);\n        }\n        return builder.finish();\n    }\n}\nconst treeHighlighter = /*@__PURE__*/Prec.high(/*@__PURE__*/ViewPlugin.fromClass(TreeHighlighter, {\n    decorations: v => v.decorations\n}));\n/**\nA default highlight style (works well with light themes).\n*/\nconst defaultHighlightStyle = /*@__PURE__*/HighlightStyle.define([\n    { tag: tags.meta,\n        color: \"#404740\" },\n    { tag: tags.link,\n        textDecoration: \"underline\" },\n    { tag: tags.heading,\n        textDecoration: \"underline\",\n        fontWeight: \"bold\" },\n    { tag: tags.emphasis,\n        fontStyle: \"italic\" },\n    { tag: tags.strong,\n        fontWeight: \"bold\" },\n    { tag: tags.strikethrough,\n        textDecoration: \"line-through\" },\n    { tag: tags.keyword,\n        color: \"#708\" },\n    { tag: [tags.atom, tags.bool, tags.url, tags.contentSeparator, tags.labelName],\n        color: \"#219\" },\n    { tag: [tags.literal, tags.inserted],\n        color: \"#164\" },\n    { tag: [tags.string, tags.deleted],\n        color: \"#a11\" },\n    { tag: [tags.regexp, tags.escape, /*@__PURE__*/tags.special(tags.string)],\n        color: \"#e40\" },\n    { tag: /*@__PURE__*/tags.definition(tags.variableName),\n        color: \"#00f\" },\n    { tag: /*@__PURE__*/tags.local(tags.variableName),\n        color: \"#30a\" },\n    { tag: [tags.typeName, tags.namespace],\n        color: \"#085\" },\n    { tag: tags.className,\n        color: \"#167\" },\n    { tag: [/*@__PURE__*/tags.special(tags.variableName), tags.macroName],\n        color: \"#256\" },\n    { tag: /*@__PURE__*/tags.definition(tags.propertyName),\n        color: \"#00c\" },\n    { tag: tags.comment,\n        color: \"#940\" },\n    { tag: tags.invalid,\n        color: \"#f00\" }\n]);\n\nconst baseTheme = /*@__PURE__*/EditorView.baseTheme({\n    \"&.cm-focused .cm-matchingBracket\": { backgroundColor: \"#328c8252\" },\n    \"&.cm-focused .cm-nonmatchingBracket\": { backgroundColor: \"#bb555544\" }\n});\nconst DefaultScanDist = 10000, DefaultBrackets = \"()[]{}\";\nconst bracketMatchingConfig = /*@__PURE__*/Facet.define({\n    combine(configs) {\n        return combineConfig(configs, {\n            afterCursor: true,\n            brackets: DefaultBrackets,\n            maxScanDistance: DefaultScanDist,\n            renderMatch: defaultRenderMatch\n        });\n    }\n});\nconst matchingMark = /*@__PURE__*/Decoration.mark({ class: \"cm-matchingBracket\" }), nonmatchingMark = /*@__PURE__*/Decoration.mark({ class: \"cm-nonmatchingBracket\" });\nfunction defaultRenderMatch(match) {\n    let decorations = [];\n    let mark = match.matched ? matchingMark : nonmatchingMark;\n    decorations.push(mark.range(match.start.from, match.start.to));\n    if (match.end)\n        decorations.push(mark.range(match.end.from, match.end.to));\n    return decorations;\n}\nconst bracketMatchingState = /*@__PURE__*/StateField.define({\n    create() { return Decoration.none; },\n    update(deco, tr) {\n        if (!tr.docChanged && !tr.selection)\n            return deco;\n        let decorations = [];\n        let config = tr.state.facet(bracketMatchingConfig);\n        for (let range of tr.state.selection.ranges) {\n            if (!range.empty)\n                continue;\n            let match = matchBrackets(tr.state, range.head, -1, config)\n                || (range.head > 0 && matchBrackets(tr.state, range.head - 1, 1, config))\n                || (config.afterCursor &&\n                    (matchBrackets(tr.state, range.head, 1, config) ||\n                        (range.head < tr.state.doc.length && matchBrackets(tr.state, range.head + 1, -1, config))));\n            if (match)\n                decorations = decorations.concat(config.renderMatch(match, tr.state));\n        }\n        return Decoration.set(decorations, true);\n    },\n    provide: f => EditorView.decorations.from(f)\n});\nconst bracketMatchingUnique = [\n    bracketMatchingState,\n    baseTheme\n];\n/**\nCreate an extension that enables bracket matching. Whenever the\ncursor is next to a bracket, that bracket and the one it matches\nare highlighted. Or, when no matching bracket is found, another\nhighlighting style is used to indicate this.\n*/\nfunction bracketMatching(config = {}) {\n    return [bracketMatchingConfig.of(config), bracketMatchingUnique];\n}\n/**\nWhen larger syntax nodes, such as HTML tags, are marked as\nopening/closing, it can be a bit messy to treat the whole node as\na matchable bracket. This node prop allows you to define, for such\na node, a ‘handle’—the part of the node that is highlighted, and\nthat the cursor must be on to activate highlighting in the first\nplace.\n*/\nconst bracketMatchingHandle = /*@__PURE__*/new NodeProp();\nfunction matchingNodes(node, dir, brackets) {\n    let byProp = node.prop(dir < 0 ? NodeProp.openedBy : NodeProp.closedBy);\n    if (byProp)\n        return byProp;\n    if (node.name.length == 1) {\n        let index = brackets.indexOf(node.name);\n        if (index > -1 && index % 2 == (dir < 0 ? 1 : 0))\n            return [brackets[index + dir]];\n    }\n    return null;\n}\nfunction findHandle(node) {\n    let hasHandle = node.type.prop(bracketMatchingHandle);\n    return hasHandle ? hasHandle(node.node) : node;\n}\n/**\nFind the matching bracket for the token at `pos`, scanning\ndirection `dir`. Only the `brackets` and `maxScanDistance`\nproperties are used from `config`, if given. Returns null if no\nbracket was found at `pos`, or a match result otherwise.\n*/\nfunction matchBrackets(state, pos, dir, config = {}) {\n    let maxScanDistance = config.maxScanDistance || DefaultScanDist, brackets = config.brackets || DefaultBrackets;\n    let tree = syntaxTree(state), node = tree.resolveInner(pos, dir);\n    for (let cur = node; cur; cur = cur.parent) {\n        let matches = matchingNodes(cur.type, dir, brackets);\n        if (matches && cur.from < cur.to) {\n            let handle = findHandle(cur);\n            if (handle && (dir > 0 ? pos >= handle.from && pos < handle.to : pos > handle.from && pos <= handle.to))\n                return matchMarkedBrackets(state, pos, dir, cur, handle, matches, brackets);\n        }\n    }\n    return matchPlainBrackets(state, pos, dir, tree, node.type, maxScanDistance, brackets);\n}\nfunction matchMarkedBrackets(_state, _pos, dir, token, handle, matching, brackets) {\n    let parent = token.parent, firstToken = { from: handle.from, to: handle.to };\n    let depth = 0, cursor = parent === null || parent === void 0 ? void 0 : parent.cursor();\n    if (cursor && (dir < 0 ? cursor.childBefore(token.from) : cursor.childAfter(token.to)))\n        do {\n            if (dir < 0 ? cursor.to <= token.from : cursor.from >= token.to) {\n                if (depth == 0 && matching.indexOf(cursor.type.name) > -1 && cursor.from < cursor.to) {\n                    let endHandle = findHandle(cursor);\n                    return { start: firstToken, end: endHandle ? { from: endHandle.from, to: endHandle.to } : undefined, matched: true };\n                }\n                else if (matchingNodes(cursor.type, dir, brackets)) {\n                    depth++;\n                }\n                else if (matchingNodes(cursor.type, -dir, brackets)) {\n                    if (depth == 0) {\n                        let endHandle = findHandle(cursor);\n                        return {\n                            start: firstToken,\n                            end: endHandle && endHandle.from < endHandle.to ? { from: endHandle.from, to: endHandle.to } : undefined,\n                            matched: false\n                        };\n                    }\n                    depth--;\n                }\n            }\n        } while (dir < 0 ? cursor.prevSibling() : cursor.nextSibling());\n    return { start: firstToken, matched: false };\n}\nfunction matchPlainBrackets(state, pos, dir, tree, tokenType, maxScanDistance, brackets) {\n    let startCh = dir < 0 ? state.sliceDoc(pos - 1, pos) : state.sliceDoc(pos, pos + 1);\n    let bracket = brackets.indexOf(startCh);\n    if (bracket < 0 || (bracket % 2 == 0) != (dir > 0))\n        return null;\n    let startToken = { from: dir < 0 ? pos - 1 : pos, to: dir > 0 ? pos + 1 : pos };\n    let iter = state.doc.iterRange(pos, dir > 0 ? state.doc.length : 0), depth = 0;\n    for (let distance = 0; !(iter.next()).done && distance <= maxScanDistance;) {\n        let text = iter.value;\n        if (dir < 0)\n            distance += text.length;\n        let basePos = pos + distance * dir;\n        for (let pos = dir > 0 ? 0 : text.length - 1, end = dir > 0 ? text.length : -1; pos != end; pos += dir) {\n            let found = brackets.indexOf(text[pos]);\n            if (found < 0 || tree.resolveInner(basePos + pos, 1).type != tokenType)\n                continue;\n            if ((found % 2 == 0) == (dir > 0)) {\n                depth++;\n            }\n            else if (depth == 1) { // Closing\n                return { start: startToken, end: { from: basePos + pos, to: basePos + pos + 1 }, matched: (found >> 1) == (bracket >> 1) };\n            }\n            else {\n                depth--;\n            }\n        }\n        if (dir > 0)\n            distance += text.length;\n    }\n    return iter.done ? { start: startToken, matched: false } : null;\n}\n\n// Counts the column offset in a string, taking tabs into account.\n// Used mostly to find indentation.\nfunction countCol(string, end, tabSize, startIndex = 0, startValue = 0) {\n    if (end == null) {\n        end = string.search(/[^\\s\\u00a0]/);\n        if (end == -1)\n            end = string.length;\n    }\n    let n = startValue;\n    for (let i = startIndex; i < end; i++) {\n        if (string.charCodeAt(i) == 9)\n            n += tabSize - (n % tabSize);\n        else\n            n++;\n    }\n    return n;\n}\n/**\nEncapsulates a single line of input. Given to stream syntax code,\nwhich uses it to tokenize the content.\n*/\nclass StringStream {\n    /**\n    Create a stream.\n    */\n    constructor(\n    /**\n    The line.\n    */\n    string, tabSize, \n    /**\n    The current indent unit size.\n    */\n    indentUnit, overrideIndent) {\n        this.string = string;\n        this.tabSize = tabSize;\n        this.indentUnit = indentUnit;\n        this.overrideIndent = overrideIndent;\n        /**\n        The current position on the line.\n        */\n        this.pos = 0;\n        /**\n        The start position of the current token.\n        */\n        this.start = 0;\n        this.lastColumnPos = 0;\n        this.lastColumnValue = 0;\n    }\n    /**\n    True if we are at the end of the line.\n    */\n    eol() { return this.pos >= this.string.length; }\n    /**\n    True if we are at the start of the line.\n    */\n    sol() { return this.pos == 0; }\n    /**\n    Get the next code unit after the current position, or undefined\n    if we're at the end of the line.\n    */\n    peek() { return this.string.charAt(this.pos) || undefined; }\n    /**\n    Read the next code unit and advance `this.pos`.\n    */\n    next() {\n        if (this.pos < this.string.length)\n            return this.string.charAt(this.pos++);\n    }\n    /**\n    Match the next character against the given string, regular\n    expression, or predicate. Consume and return it if it matches.\n    */\n    eat(match) {\n        let ch = this.string.charAt(this.pos);\n        let ok;\n        if (typeof match == \"string\")\n            ok = ch == match;\n        else\n            ok = ch && (match instanceof RegExp ? match.test(ch) : match(ch));\n        if (ok) {\n            ++this.pos;\n            return ch;\n        }\n    }\n    /**\n    Continue matching characters that match the given string,\n    regular expression, or predicate function. Return true if any\n    characters were consumed.\n    */\n    eatWhile(match) {\n        let start = this.pos;\n        while (this.eat(match)) { }\n        return this.pos > start;\n    }\n    /**\n    Consume whitespace ahead of `this.pos`. Return true if any was\n    found.\n    */\n    eatSpace() {\n        let start = this.pos;\n        while (/[\\s\\u00a0]/.test(this.string.charAt(this.pos)))\n            ++this.pos;\n        return this.pos > start;\n    }\n    /**\n    Move to the end of the line.\n    */\n    skipToEnd() { this.pos = this.string.length; }\n    /**\n    Move to directly before the given character, if found on the\n    current line.\n    */\n    skipTo(ch) {\n        let found = this.string.indexOf(ch, this.pos);\n        if (found > -1) {\n            this.pos = found;\n            return true;\n        }\n    }\n    /**\n    Move back `n` characters.\n    */\n    backUp(n) { this.pos -= n; }\n    /**\n    Get the column position at `this.pos`.\n    */\n    column() {\n        if (this.lastColumnPos < this.start) {\n            this.lastColumnValue = countCol(this.string, this.start, this.tabSize, this.lastColumnPos, this.lastColumnValue);\n            this.lastColumnPos = this.start;\n        }\n        return this.lastColumnValue;\n    }\n    /**\n    Get the indentation column of the current line.\n    */\n    indentation() {\n        var _a;\n        return (_a = this.overrideIndent) !== null && _a !== void 0 ? _a : countCol(this.string, null, this.tabSize);\n    }\n    /**\n    Match the input against the given string or regular expression\n    (which should start with a `^`). Return true or the regexp match\n    if it matches.\n    \n    Unless `consume` is set to `false`, this will move `this.pos`\n    past the matched text.\n    \n    When matching a string `caseInsensitive` can be set to true to\n    make the match case-insensitive.\n    */\n    match(pattern, consume, caseInsensitive) {\n        if (typeof pattern == \"string\") {\n            let cased = (str) => caseInsensitive ? str.toLowerCase() : str;\n            let substr = this.string.substr(this.pos, pattern.length);\n            if (cased(substr) == cased(pattern)) {\n                if (consume !== false)\n                    this.pos += pattern.length;\n                return true;\n            }\n            else\n                return null;\n        }\n        else {\n            let match = this.string.slice(this.pos).match(pattern);\n            if (match && match.index > 0)\n                return null;\n            if (match && consume !== false)\n                this.pos += match[0].length;\n            return match;\n        }\n    }\n    /**\n    Get the current token.\n    */\n    current() { return this.string.slice(this.start, this.pos); }\n}\n\nfunction fullParser(spec) {\n    return {\n        name: spec.name || \"\",\n        token: spec.token,\n        blankLine: spec.blankLine || (() => { }),\n        startState: spec.startState || (() => true),\n        copyState: spec.copyState || defaultCopyState,\n        indent: spec.indent || (() => null),\n        languageData: spec.languageData || {},\n        tokenTable: spec.tokenTable || noTokens\n    };\n}\nfunction defaultCopyState(state) {\n    if (typeof state != \"object\")\n        return state;\n    let newState = {};\n    for (let prop in state) {\n        let val = state[prop];\n        newState[prop] = (val instanceof Array ? val.slice() : val);\n    }\n    return newState;\n}\nconst IndentedFrom = /*@__PURE__*/new WeakMap();\n/**\nA [language](https://codemirror.net/6/docs/ref/#language.Language) class based on a CodeMirror\n5-style [streaming parser](https://codemirror.net/6/docs/ref/#language.StreamParser).\n*/\nclass StreamLanguage extends Language {\n    constructor(parser) {\n        let data = defineLanguageFacet(parser.languageData);\n        let p = fullParser(parser), self;\n        let impl = new class extends Parser {\n            createParse(input, fragments, ranges) {\n                return new Parse(self, input, fragments, ranges);\n            }\n        };\n        super(data, impl, [], parser.name);\n        this.topNode = docID(data, this);\n        self = this;\n        this.streamParser = p;\n        this.stateAfter = new NodeProp({ perNode: true });\n        this.tokenTable = parser.tokenTable ? new TokenTable(p.tokenTable) : defaultTokenTable;\n    }\n    /**\n    Define a stream language.\n    */\n    static define(spec) { return new StreamLanguage(spec); }\n    /**\n    @internal\n    */\n    getIndent(cx) {\n        let from = undefined;\n        let { overrideIndentation } = cx.options;\n        if (overrideIndentation) {\n            from = IndentedFrom.get(cx.state);\n            if (from != null && from < cx.pos - 1e4)\n                from = undefined;\n        }\n        let start = findState(this, cx.node.tree, cx.node.from, cx.node.from, from !== null && from !== void 0 ? from : cx.pos), statePos, state;\n        if (start) {\n            state = start.state;\n            statePos = start.pos + 1;\n        }\n        else {\n            state = this.streamParser.startState(cx.unit);\n            statePos = cx.node.from;\n        }\n        if (cx.pos - statePos > 10000 /* C.MaxIndentScanDist */)\n            return null;\n        while (statePos < cx.pos) {\n            let line = cx.state.doc.lineAt(statePos), end = Math.min(cx.pos, line.to);\n            if (line.length) {\n                let indentation = overrideIndentation ? overrideIndentation(line.from) : -1;\n                let stream = new StringStream(line.text, cx.state.tabSize, cx.unit, indentation < 0 ? undefined : indentation);\n                while (stream.pos < end - line.from)\n                    readToken(this.streamParser.token, stream, state);\n            }\n            else {\n                this.streamParser.blankLine(state, cx.unit);\n            }\n            if (end == cx.pos)\n                break;\n            statePos = line.to + 1;\n        }\n        let line = cx.lineAt(cx.pos);\n        if (overrideIndentation && from == null)\n            IndentedFrom.set(cx.state, line.from);\n        return this.streamParser.indent(state, /^\\s*(.*)/.exec(line.text)[1], cx);\n    }\n    get allowsNesting() { return false; }\n}\nfunction findState(lang, tree, off, startPos, before) {\n    let state = off >= startPos && off + tree.length <= before && tree.prop(lang.stateAfter);\n    if (state)\n        return { state: lang.streamParser.copyState(state), pos: off + tree.length };\n    for (let i = tree.children.length - 1; i >= 0; i--) {\n        let child = tree.children[i], pos = off + tree.positions[i];\n        let found = child instanceof Tree && pos < before && findState(lang, child, pos, startPos, before);\n        if (found)\n            return found;\n    }\n    return null;\n}\nfunction cutTree(lang, tree, from, to, inside) {\n    if (inside && from <= 0 && to >= tree.length)\n        return tree;\n    if (!inside && from == 0 && tree.type == lang.topNode)\n        inside = true;\n    for (let i = tree.children.length - 1; i >= 0; i--) {\n        let pos = tree.positions[i], child = tree.children[i], inner;\n        if (pos < to && child instanceof Tree) {\n            if (!(inner = cutTree(lang, child, from - pos, to - pos, inside)))\n                break;\n            return !inside ? inner\n                : new Tree(tree.type, tree.children.slice(0, i).concat(inner), tree.positions.slice(0, i + 1), pos + inner.length);\n        }\n    }\n    return null;\n}\nfunction findStartInFragments(lang, fragments, startPos, endPos, editorState) {\n    for (let f of fragments) {\n        let from = f.from + (f.openStart ? 25 : 0), to = f.to - (f.openEnd ? 25 : 0);\n        let found = from <= startPos && to > startPos && findState(lang, f.tree, 0 - f.offset, startPos, to), tree;\n        if (found && found.pos <= endPos && (tree = cutTree(lang, f.tree, startPos + f.offset, found.pos + f.offset, false)))\n            return { state: found.state, tree };\n    }\n    return { state: lang.streamParser.startState(editorState ? getIndentUnit(editorState) : 4), tree: Tree.empty };\n}\nclass Parse {\n    constructor(lang, input, fragments, ranges) {\n        this.lang = lang;\n        this.input = input;\n        this.fragments = fragments;\n        this.ranges = ranges;\n        this.stoppedAt = null;\n        this.chunks = [];\n        this.chunkPos = [];\n        this.chunk = [];\n        this.chunkReused = undefined;\n        this.rangeIndex = 0;\n        this.to = ranges[ranges.length - 1].to;\n        let context = ParseContext.get(), from = ranges[0].from;\n        let { state, tree } = findStartInFragments(lang, fragments, from, this.to, context === null || context === void 0 ? void 0 : context.state);\n        this.state = state;\n        this.parsedPos = this.chunkStart = from + tree.length;\n        for (let i = 0; i < tree.children.length; i++) {\n            this.chunks.push(tree.children[i]);\n            this.chunkPos.push(tree.positions[i]);\n        }\n        if (context && this.parsedPos < context.viewport.from - 100000 /* C.MaxDistanceBeforeViewport */ &&\n            ranges.some(r => r.from <= context.viewport.from && r.to >= context.viewport.from)) {\n            this.state = this.lang.streamParser.startState(getIndentUnit(context.state));\n            context.skipUntilInView(this.parsedPos, context.viewport.from);\n            this.parsedPos = context.viewport.from;\n        }\n        this.moveRangeIndex();\n    }\n    advance() {\n        let context = ParseContext.get();\n        let parseEnd = this.stoppedAt == null ? this.to : Math.min(this.to, this.stoppedAt);\n        let end = Math.min(parseEnd, this.chunkStart + 2048 /* C.ChunkSize */);\n        if (context)\n            end = Math.min(end, context.viewport.to);\n        while (this.parsedPos < end)\n            this.parseLine(context);\n        if (this.chunkStart < this.parsedPos)\n            this.finishChunk();\n        if (this.parsedPos >= parseEnd)\n            return this.finish();\n        if (context && this.parsedPos >= context.viewport.to) {\n            context.skipUntilInView(this.parsedPos, parseEnd);\n            return this.finish();\n        }\n        return null;\n    }\n    stopAt(pos) {\n        this.stoppedAt = pos;\n    }\n    lineAfter(pos) {\n        let chunk = this.input.chunk(pos);\n        if (!this.input.lineChunks) {\n            let eol = chunk.indexOf(\"\\n\");\n            if (eol > -1)\n                chunk = chunk.slice(0, eol);\n        }\n        else if (chunk == \"\\n\") {\n            chunk = \"\";\n        }\n        return pos + chunk.length <= this.to ? chunk : chunk.slice(0, this.to - pos);\n    }\n    nextLine() {\n        let from = this.parsedPos, line = this.lineAfter(from), end = from + line.length;\n        for (let index = this.rangeIndex;;) {\n            let rangeEnd = this.ranges[index].to;\n            if (rangeEnd >= end)\n                break;\n            line = line.slice(0, rangeEnd - (end - line.length));\n            index++;\n            if (index == this.ranges.length)\n                break;\n            let rangeStart = this.ranges[index].from;\n            let after = this.lineAfter(rangeStart);\n            line += after;\n            end = rangeStart + after.length;\n        }\n        return { line, end };\n    }\n    skipGapsTo(pos, offset, side) {\n        for (;;) {\n            let end = this.ranges[this.rangeIndex].to, offPos = pos + offset;\n            if (side > 0 ? end > offPos : end >= offPos)\n                break;\n            let start = this.ranges[++this.rangeIndex].from;\n            offset += start - end;\n        }\n        return offset;\n    }\n    moveRangeIndex() {\n        while (this.ranges[this.rangeIndex].to < this.parsedPos)\n            this.rangeIndex++;\n    }\n    emitToken(id, from, to, offset) {\n        let size = 4;\n        if (this.ranges.length > 1) {\n            offset = this.skipGapsTo(from, offset, 1);\n            from += offset;\n            let len0 = this.chunk.length;\n            offset = this.skipGapsTo(to, offset, -1);\n            to += offset;\n            size += this.chunk.length - len0;\n        }\n        let last = this.chunk.length - 4;\n        if (size == 4 && last >= 0 && this.chunk[last] == id && this.chunk[last + 2] == from)\n            this.chunk[last + 2] = to;\n        else\n            this.chunk.push(id, from, to, size);\n        return offset;\n    }\n    parseLine(context) {\n        let { line, end } = this.nextLine(), offset = 0, { streamParser } = this.lang;\n        let stream = new StringStream(line, context ? context.state.tabSize : 4, context ? getIndentUnit(context.state) : 2);\n        if (stream.eol()) {\n            streamParser.blankLine(this.state, stream.indentUnit);\n        }\n        else {\n            while (!stream.eol()) {\n                let token = readToken(streamParser.token, stream, this.state);\n                if (token)\n                    offset = this.emitToken(this.lang.tokenTable.resolve(token), this.parsedPos + stream.start, this.parsedPos + stream.pos, offset);\n                if (stream.start > 10000 /* C.MaxLineLength */)\n                    break;\n            }\n        }\n        this.parsedPos = end;\n        this.moveRangeIndex();\n        if (this.parsedPos < this.to)\n            this.parsedPos++;\n    }\n    finishChunk() {\n        let tree = Tree.build({\n            buffer: this.chunk,\n            start: this.chunkStart,\n            length: this.parsedPos - this.chunkStart,\n            nodeSet,\n            topID: 0,\n            maxBufferLength: 2048 /* C.ChunkSize */,\n            reused: this.chunkReused\n        });\n        tree = new Tree(tree.type, tree.children, tree.positions, tree.length, [[this.lang.stateAfter, this.lang.streamParser.copyState(this.state)]]);\n        this.chunks.push(tree);\n        this.chunkPos.push(this.chunkStart - this.ranges[0].from);\n        this.chunk = [];\n        this.chunkReused = undefined;\n        this.chunkStart = this.parsedPos;\n    }\n    finish() {\n        return new Tree(this.lang.topNode, this.chunks, this.chunkPos, this.parsedPos - this.ranges[0].from).balance();\n    }\n}\nfunction readToken(token, stream, state) {\n    stream.start = stream.pos;\n    for (let i = 0; i < 10; i++) {\n        let result = token(stream, state);\n        if (stream.pos > stream.start)\n            return result;\n    }\n    throw new Error(\"Stream parser failed to advance stream.\");\n}\nconst noTokens = /*@__PURE__*/Object.create(null);\nconst typeArray = [NodeType.none];\nconst nodeSet = /*@__PURE__*/new NodeSet(typeArray);\nconst warned = [];\n// Cache of node types by name and tags\nconst byTag = /*@__PURE__*/Object.create(null);\nconst defaultTable = /*@__PURE__*/Object.create(null);\nfor (let [legacyName, name] of [\n    [\"variable\", \"variableName\"],\n    [\"variable-2\", \"variableName.special\"],\n    [\"string-2\", \"string.special\"],\n    [\"def\", \"variableName.definition\"],\n    [\"tag\", \"tagName\"],\n    [\"attribute\", \"attributeName\"],\n    [\"type\", \"typeName\"],\n    [\"builtin\", \"variableName.standard\"],\n    [\"qualifier\", \"modifier\"],\n    [\"error\", \"invalid\"],\n    [\"header\", \"heading\"],\n    [\"property\", \"propertyName\"]\n])\n    defaultTable[legacyName] = /*@__PURE__*/createTokenType(noTokens, name);\nclass TokenTable {\n    constructor(extra) {\n        this.extra = extra;\n        this.table = Object.assign(Object.create(null), defaultTable);\n    }\n    resolve(tag) {\n        return !tag ? 0 : this.table[tag] || (this.table[tag] = createTokenType(this.extra, tag));\n    }\n}\nconst defaultTokenTable = /*@__PURE__*/new TokenTable(noTokens);\nfunction warnForPart(part, msg) {\n    if (warned.indexOf(part) > -1)\n        return;\n    warned.push(part);\n    console.warn(msg);\n}\nfunction createTokenType(extra, tagStr) {\n    let tags$1 = [];\n    for (let name of tagStr.split(\" \")) {\n        let found = [];\n        for (let part of name.split(\".\")) {\n            let value = (extra[part] || tags[part]);\n            if (!value) {\n                warnForPart(part, `Unknown highlighting tag ${part}`);\n            }\n            else if (typeof value == \"function\") {\n                if (!found.length)\n                    warnForPart(part, `Modifier ${part} used at start of tag`);\n                else\n                    found = found.map(value);\n            }\n            else {\n                if (found.length)\n                    warnForPart(part, `Tag ${part} used as modifier`);\n                else\n                    found = Array.isArray(value) ? value : [value];\n            }\n        }\n        for (let tag of found)\n            tags$1.push(tag);\n    }\n    if (!tags$1.length)\n        return 0;\n    let name = tagStr.replace(/ /g, \"_\"), key = name + \" \" + tags$1.map(t => t.id);\n    let known = byTag[key];\n    if (known)\n        return known.id;\n    let type = byTag[key] = NodeType.define({\n        id: typeArray.length,\n        name,\n        props: [styleTags({ [name]: tags$1 })]\n    });\n    typeArray.push(type);\n    return type.id;\n}\nfunction docID(data, lang) {\n    let type = NodeType.define({ id: typeArray.length, name: \"Document\", props: [\n            languageDataProp.add(() => data),\n            indentNodeProp.add(() => cx => lang.getIndent(cx))\n        ], top: true });\n    typeArray.push(type);\n    return type;\n}\n\nfunction buildForLine(line) {\n    return line.length <= 4096 && /[\\u0590-\\u05f4\\u0600-\\u06ff\\u0700-\\u08ac\\ufb50-\\ufdff]/.test(line);\n}\nfunction textHasRTL(text) {\n    for (let i = text.iter(); !i.next().done;)\n        if (buildForLine(i.value))\n            return true;\n    return false;\n}\nfunction changeAddsRTL(change) {\n    let added = false;\n    change.iterChanges((fA, tA, fB, tB, ins) => {\n        if (!added && textHasRTL(ins))\n            added = true;\n    });\n    return added;\n}\nconst alwaysIsolate = /*@__PURE__*/Facet.define({ combine: values => values.some(x => x) });\n/**\nMake sure nodes\n[marked](https://lezer.codemirror.net/docs/ref/#common.NodeProp^isolate)\nas isolating for bidirectional text are rendered in a way that\nisolates them from the surrounding text.\n*/\nfunction bidiIsolates(options = {}) {\n    let extensions = [isolateMarks];\n    if (options.alwaysIsolate)\n        extensions.push(alwaysIsolate.of(true));\n    return extensions;\n}\nconst isolateMarks = /*@__PURE__*/ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.always = view.state.facet(alwaysIsolate) ||\n            view.textDirection != Direction.LTR ||\n            view.state.facet(EditorView.perLineTextDirection);\n        this.hasRTL = !this.always && textHasRTL(view.state.doc);\n        this.tree = syntaxTree(view.state);\n        this.decorations = this.always || this.hasRTL ? buildDeco(view, this.tree, this.always) : Decoration.none;\n    }\n    update(update) {\n        let always = update.state.facet(alwaysIsolate) ||\n            update.view.textDirection != Direction.LTR ||\n            update.state.facet(EditorView.perLineTextDirection);\n        if (!always && !this.hasRTL && changeAddsRTL(update.changes))\n            this.hasRTL = true;\n        if (!always && !this.hasRTL)\n            return;\n        let tree = syntaxTree(update.state);\n        if (always != this.always || tree != this.tree || update.docChanged || update.viewportChanged) {\n            this.tree = tree;\n            this.always = always;\n            this.decorations = buildDeco(update.view, tree, always);\n        }\n    }\n}, {\n    provide: plugin => {\n        function access(view) {\n            var _a, _b;\n            return (_b = (_a = view.plugin(plugin)) === null || _a === void 0 ? void 0 : _a.decorations) !== null && _b !== void 0 ? _b : Decoration.none;\n        }\n        return [EditorView.outerDecorations.of(access),\n            Prec.lowest(EditorView.bidiIsolatedRanges.of(access))];\n    }\n});\nfunction buildDeco(view, tree, always) {\n    let deco = new RangeSetBuilder();\n    let ranges = view.visibleRanges;\n    if (!always)\n        ranges = clipRTLLines(ranges, view.state.doc);\n    for (let { from, to } of ranges) {\n        tree.iterate({\n            enter: node => {\n                let iso = node.type.prop(NodeProp.isolate);\n                if (iso)\n                    deco.add(node.from, node.to, marks[iso]);\n            },\n            from, to\n        });\n    }\n    return deco.finish();\n}\nfunction clipRTLLines(ranges, doc) {\n    let cur = doc.iter(), pos = 0, result = [], last = null;\n    for (let { from, to } of ranges) {\n        if (last && last.to > from) {\n            from = last.to;\n            if (from >= to)\n                continue;\n        }\n        if (pos + cur.value.length < from) {\n            cur.next(from - (pos + cur.value.length));\n            pos = from;\n        }\n        for (;;) {\n            let start = pos, end = pos + cur.value.length;\n            if (!cur.lineBreak && buildForLine(cur.value)) {\n                if (last && last.to > start - 10)\n                    last.to = Math.min(to, end);\n                else\n                    result.push(last = { from: start, to: Math.min(to, end) });\n            }\n            if (end >= to)\n                break;\n            pos = end;\n            cur.next();\n        }\n    }\n    return result;\n}\nconst marks = {\n    rtl: /*@__PURE__*/Decoration.mark({ class: \"cm-iso\", inclusive: true, attributes: { dir: \"rtl\" }, bidiIsolate: Direction.RTL }),\n    ltr: /*@__PURE__*/Decoration.mark({ class: \"cm-iso\", inclusive: true, attributes: { dir: \"ltr\" }, bidiIsolate: Direction.LTR }),\n    auto: /*@__PURE__*/Decoration.mark({ class: \"cm-iso\", inclusive: true, attributes: { dir: \"auto\" }, bidiIsolate: null })\n};\n\nexport { DocInput, HighlightStyle, IndentContext, LRLanguage, Language, LanguageDescription, LanguageSupport, ParseContext, StreamLanguage, StringStream, TreeIndentContext, bidiIsolates, bracketMatching, bracketMatchingHandle, codeFolding, continuedIndent, defaultHighlightStyle, defineLanguageFacet, delimitedIndent, ensureSyntaxTree, flatIndent, foldAll, foldCode, foldEffect, foldGutter, foldInside, foldKeymap, foldNodeProp, foldService, foldState, foldable, foldedRanges, forceParsing, getIndentUnit, getIndentation, highlightingFor, indentNodeProp, indentOnInput, indentRange, indentService, indentString, indentUnit, language, languageDataProp, matchBrackets, sublanguageProp, syntaxHighlighting, syntaxParserRunning, syntaxTree, syntaxTreeAvailable, toggleFold, unfoldAll, unfoldCode, unfoldEffect };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,IAAI;AACJ;;;AAGA,GACA,MAAM,mBAAmB,WAAW,GAAE,IAAI,mNAAA,CAAA,WAAQ;AAClD;;;;;;;AAOA,GACA,SAAS,oBAAoB,QAAQ;IACjC,OAAO,2NAAA,CAAA,QAAK,CAAC,MAAM,CAAC;QAChB,SAAS,WAAW,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;IAC5D;AACJ;AACA;;;AAGA,GACA,MAAM,kBAAkB,WAAW,GAAE,IAAI,mNAAA,CAAA,WAAQ;AACjD;;;;;;;;AAQA,GACA,MAAM;IACF;;;;;;IAMA,GACA,YACA;;;IAGA,GACA,IAAI,EAAE,MAAM,EAAE,kBAAkB,EAAE,EAClC;;IAEA,GACA,OAAO,EAAE,CAAE;QACP,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,2DAA2D;QAC3D,yDAAyD;QACzD,6BAA6B;QAC7B,IAAI,CAAC,2NAAA,CAAA,cAAW,CAAC,SAAS,CAAC,cAAc,CAAC,SACtC,OAAO,cAAc,CAAC,2NAAA,CAAA,cAAW,CAAC,SAAS,EAAE,QAAQ;YAAE;gBAAQ,OAAO,WAAW,IAAI;YAAG;QAAE;QAC9F,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG;YACb,SAAS,EAAE,CAAC,IAAI;YAChB,2NAAA,CAAA,cAAW,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,OAAO,KAAK;gBACrC,IAAI,MAAM,UAAU,OAAO,KAAK,OAAO,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC;gBAC5D,IAAI,CAAC,MACD,OAAO,EAAE;gBACb,IAAI,OAAO,MAAM,KAAK,CAAC,OAAO,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC;gBAClD,IAAI,KAAK;oBACL,IAAI,YAAY,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE;oBAC5C,KAAK,IAAI,WAAW,IAChB,IAAI,QAAQ,IAAI,CAAC,WAAW,QAAQ;wBAChC,IAAI,OAAO,MAAM,KAAK,CAAC,QAAQ,KAAK;wBACpC,OAAO,QAAQ,IAAI,IAAI,YAAY,OAAO,KAAK,MAAM,CAAC;oBAC1D;gBACR;gBACA,OAAO;YACX;SACH,CAAC,MAAM,CAAC;IACb;IACA;;IAEA,GACA,WAAW,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE;QAC9B,OAAO,UAAU,OAAO,KAAK,MAAM,IAAI,CAAC,IAAI,CAAC,qBAAqB,IAAI,CAAC,IAAI;IAC/E;IACA;;;;IAIA,GACA,YAAY,KAAK,EAAE;QACf,IAAI,OAAO,MAAM,KAAK,CAAC;QACvB,IAAI,CAAC,SAAS,QAAQ,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,EACpE,OAAO;YAAC;gBAAE,MAAM;gBAAG,IAAI,MAAM,GAAG,CAAC,MAAM;YAAC;SAAE;QAC9C,IAAI,CAAC,QAAQ,CAAC,KAAK,aAAa,EAC5B,OAAO,EAAE;QACb,IAAI,SAAS,EAAE;QACf,IAAI,UAAU,CAAC,MAAM;YACjB,IAAI,KAAK,IAAI,CAAC,qBAAqB,IAAI,CAAC,IAAI,EAAE;gBAC1C,OAAO,IAAI,CAAC;oBAAE;oBAAM,IAAI,OAAO,KAAK,MAAM;gBAAC;gBAC3C;YACJ;YACA,IAAI,QAAQ,KAAK,IAAI,CAAC,mNAAA,CAAA,WAAQ,CAAC,OAAO;YACtC,IAAI,OAAO;gBACP,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,qBAAqB,IAAI,CAAC,IAAI,EAAE;oBAChD,IAAI,MAAM,OAAO,EACb,KAAK,IAAI,KAAK,MAAM,OAAO,CACvB,OAAO,IAAI,CAAC;wBAAE,MAAM,EAAE,IAAI,GAAG;wBAAM,IAAI,EAAE,EAAE,GAAG;oBAAK;yBAEvD,OAAO,IAAI,CAAC;wBAAE,MAAM;wBAAM,IAAI,OAAO,KAAK,MAAM;oBAAC;oBACrD;gBACJ,OACK,IAAI,MAAM,OAAO,EAAE;oBACpB,IAAI,OAAO,OAAO,MAAM;oBACxB,QAAQ,MAAM,IAAI,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG;oBAC5C,IAAI,OAAO,MAAM,GAAG,MAChB;gBACR;YACJ;YACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,CAAC,MAAM,EAAE,IAAK;gBAC3C,IAAI,KAAK,KAAK,QAAQ,CAAC,EAAE;gBACzB,IAAI,cAAc,mNAAA,CAAA,OAAI,EAClB,QAAQ,IAAI,KAAK,SAAS,CAAC,EAAE,GAAG;YACxC;QACJ;QACA,QAAQ,WAAW,QAAQ;QAC3B,OAAO;IACX;IACA;;;IAGA,GACA,IAAI,gBAAgB;QAAE,OAAO;IAAM;AACvC;AACA;;AAEA,GACA,SAAS,QAAQ,GAAG,WAAW,GAAE,2NAAA,CAAA,cAAW,CAAC,MAAM;AACnD,SAAS,UAAU,KAAK,EAAE,GAAG,EAAE,IAAI;IAC/B,IAAI,UAAU,MAAM,KAAK,CAAC,WAAW,OAAO,WAAW,OAAO,OAAO;IACrE,IAAI,CAAC,WAAW,QAAQ,aAAa,EAAE;QACnC,IAAK,IAAI,OAAO,MAAM,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,mNAAA,CAAA,WAAQ,CAAC,cAAc,EAC5E,IAAI,KAAK,IAAI,CAAC,KAAK,EACf,OAAO;IACnB;IACA,OAAO;AACX;AACA;;;;AAIA,GACA,MAAM,mBAAmB;IACrB,YAAY,IAAI,EAAE,MAAM,EAAE,IAAI,CAAE;QAC5B,KAAK,CAAC,MAAM,QAAQ,EAAE,EAAE;QACxB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;;IAEA,GACA,OAAO,OAAO,IAAI,EAAE;QAChB,IAAI,OAAO,oBAAoB,KAAK,YAAY;QAChD,OAAO,IAAI,WAAW,MAAM,KAAK,MAAM,CAAC,SAAS,CAAC;YAC9C,OAAO;gBAAC,iBAAiB,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK,GAAG,OAAO;aAAW;QACxE,IAAI,KAAK,IAAI;IACjB;IACA;;;IAGA,GACA,UAAU,OAAO,EAAE,IAAI,EAAE;QACrB,OAAO,IAAI,WAAW,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,QAAQ,IAAI,CAAC,IAAI;IACtF;IACA,IAAI,gBAAgB;QAAE,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW;IAAI;AAC5D;AACA;;;;;AAKA,GACA,SAAS,WAAW,KAAK;IACrB,IAAI,QAAQ,MAAM,KAAK,CAAC,SAAS,KAAK,EAAE;IACxC,OAAO,QAAQ,MAAM,IAAI,GAAG,mNAAA,CAAA,OAAI,CAAC,KAAK;AAC1C;AACA;;;;AAIA,GACA,SAAS,iBAAiB,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE;IAC/C,IAAI;IACJ,IAAI,QAAQ,CAAC,KAAK,MAAM,KAAK,CAAC,SAAS,KAAK,EAAE,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO;IACrG,IAAI,CAAC,OACD,OAAO;IACX,IAAI,aAAa,MAAM,QAAQ;IAC/B,MAAM,cAAc,CAAC;QAAE,MAAM;QAAG,IAAI;IAAK;IACzC,IAAI,SAAS,MAAM,MAAM,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,QAAQ,MAAM,IAAI,GAAG;IAC5E,MAAM,cAAc,CAAC;IACrB,OAAO;AACX;AACA;;;;;;;;AAQA,GACA,SAAS,oBAAoB,KAAK,EAAE,OAAO,MAAM,GAAG,CAAC,MAAM;IACvD,IAAI;IACJ,OAAO,CAAC,CAAC,KAAK,MAAM,KAAK,CAAC,SAAS,KAAK,EAAE,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,KAAK;AACvH;AACA;;;;;AAKA,GACA,SAAS,aAAa,IAAI,EAAE,OAAO,KAAK,QAAQ,CAAC,EAAE,EAAE,UAAU,GAAG;IAC9D,IAAI,UAAU,iBAAiB,KAAK,KAAK,EAAE,MAAM;IACjD,IAAI,WAAW,WAAW,KAAK,KAAK,GAChC,KAAK,QAAQ,CAAC,CAAC;IACnB,OAAO,CAAC,CAAC;AACb;AACA;;;;;;AAMA,GACA,SAAS,oBAAoB,IAAI;IAC7B,IAAI;IACJ,OAAO,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,YAAY,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,EAAE,KAAK;AACpG;AACA;;;;AAIA,GACA,MAAM;IACF;;IAEA,GACA,YAAY,GAAG,CAAE;QACb,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI;IAC1B;IACA,IAAI,SAAS;QAAE,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM;IAAE;IACvC,OAAO,GAAG,EAAE;QACR,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,KAAK;QAC1D,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM;QACzC,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;IAC9C;IACA,MAAM,GAAG,EAAE;QACP,IAAI,CAAC,MAAM,CAAC;QACZ,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,IAAI,aAAa;QAAE,OAAO;IAAM;IAChC,KAAK,IAAI,EAAE,EAAE,EAAE;QACX,IAAI,cAAc,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;QACrD,IAAI,OAAO,eAAe,MAAM,IAAI,CAAC,SAAS,EAC1C,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM;aAElC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,aAAa,KAAK;IAC1D;AACJ;AACA,IAAI,iBAAiB;AACrB;;AAEA,GACA,MAAM;IACF,YAAY,MAAM,EAClB;;IAEA,GACA,KAAK,EACL;;IAEA,GACA,YAAY,EAAE,EACd;;IAEA,GACA,IAAI,EACJ;;IAEA,GACA,OAAO,EACP;;;;;;;IAOA,GACA,QAAQ,EACR;;IAEA,GACA,OAAO,EACP;;;;IAIA,GACA,UAAU,CAAE;QACR,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,KAAK,GAAG;QACb;;QAEA,GACA,IAAI,CAAC,WAAW,GAAG,EAAE;IACzB;IACA;;IAEA,GACA,OAAO,OAAO,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE;QACnC,OAAO,IAAI,aAAa,QAAQ,OAAO,EAAE,EAAE,mNAAA,CAAA,OAAI,CAAC,KAAK,EAAE,GAAG,UAAU,EAAE,EAAE;IAC5E;IACA,aAAa;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS;IAC9E;IACA;;IAEA,GACA,KAAK,KAAK,EAAE,IAAI,EAAE;QACd,IAAI,QAAQ,QAAQ,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAC7C,OAAO;QACX,IAAI,IAAI,CAAC,IAAI,IAAI,mNAAA,CAAA,OAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,QAAQ,SAAS,KAAK,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG;YACzG,IAAI,CAAC,QAAQ;YACb,OAAO;QACX;QACA,OAAO,IAAI,CAAC,WAAW,CAAC;YACpB,IAAI;YACJ,IAAI,OAAO,SAAS,UAAU;gBAC1B,IAAI,UAAU,KAAK,GAAG,KAAK;gBAC3B,QAAQ,IAAM,KAAK,GAAG,KAAK;YAC/B;YACA,IAAI,CAAC,IAAI,CAAC,KAAK,EACX,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU;YAChC,IAAI,QAAQ,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,KAC5E,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACtB,OAAS;gBACL,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO;gBAC7B,IAAI,MAAM;oBACN,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,mNAAA,CAAA,eAAY,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI;oBAC5G,IAAI,CAAC,OAAO,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM;oBACjG,IAAI,CAAC,IAAI,GAAG;oBACZ,IAAI,CAAC,KAAK,GAAG;oBACb,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS,QAAQ,SAAS,KAAK,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,GAC/E,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU;yBAE5B,OAAO;gBACf;gBACA,IAAI,SACA,OAAO;YACf;QACJ;IACJ;IACA;;IAEA,GACA,WAAW;QACP,IAAI,KAAK;QACT,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,IAAI,CAAC,OAAO,EAAE;YAC5D,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,KACvD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACtB,IAAI,CAAC,WAAW,CAAC;gBAAQ,MAAO,CAAC,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAG,CAAE;YAAE;YACpE,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,mNAAA,CAAA,eAAY,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE;YACzF,IAAI,CAAC,KAAK,GAAG;QACjB;IACJ;IACA,YAAY,CAAC,EAAE;QACX,IAAI,OAAO;QACX,iBAAiB,IAAI;QACrB,IAAI;YACA,OAAO;QACX,SACQ;YACJ,iBAAiB;QACrB;IACJ;IACA,mBAAmB,SAAS,EAAE;QAC1B,IAAK,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,IAChC,YAAY,aAAa,WAAW,EAAE,IAAI,EAAE,EAAE,EAAE;QACpD,OAAO;IACX;IACA;;IAEA,GACA,QAAQ,OAAO,EAAE,QAAQ,EAAE;QACvB,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,IAAI;QAC1D,IAAI,CAAC,QAAQ;QACb,IAAI,CAAC,QAAQ,KAAK,EAAE;YAChB,IAAI,SAAS,EAAE;YACf,QAAQ,iBAAiB,CAAC,CAAC,OAAO,KAAK,OAAO,MAAQ,OAAO,IAAI,CAAC;oBAAE;oBAAO;oBAAK;oBAAO;gBAAI;YAC3F,YAAY,mNAAA,CAAA,eAAY,CAAC,YAAY,CAAC,WAAW;YACjD,OAAO,mNAAA,CAAA,OAAI,CAAC,KAAK;YACjB,UAAU;YACV,WAAW;gBAAE,MAAM,QAAQ,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC;gBAAI,IAAI,QAAQ,MAAM,CAAC,SAAS,EAAE,EAAE;YAAG;YACzF,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACrB,UAAU,EAAE;gBACZ,KAAK,IAAI,KAAK,IAAI,CAAC,OAAO,CAAE;oBACxB,IAAI,OAAO,QAAQ,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,KAAK,QAAQ,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC;oBACjE,IAAI,OAAO,IACP,QAAQ,IAAI,CAAC;wBAAE;wBAAM;oBAAG;gBAChC;YACJ;QACJ;QACA,OAAO,IAAI,aAAa,IAAI,CAAC,MAAM,EAAE,UAAU,WAAW,MAAM,SAAS,UAAU,SAAS,IAAI,CAAC,UAAU;IAC/G;IACA;;IAEA,GACA,eAAe,QAAQ,EAAE;QACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,SAAS,EAAE,EACtE,OAAO;QACX,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,WAAW,IAAI,CAAC,OAAO,CAAC,MAAM;QAClC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAK;YAC1C,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE;YAClC,IAAI,OAAO,SAAS,EAAE,IAAI,KAAK,SAAS,IAAI,EAAE;gBAC1C,IAAI,CAAC,SAAS,GAAG,aAAa,IAAI,CAAC,SAAS,EAAE,MAAM;gBACpD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;YAC7B;QACJ;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,UACvB,OAAO;QACX,IAAI,CAAC,KAAK;QACV,OAAO;IACX;IACA;;IAEA,GACA,QAAQ;QACJ,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,QAAQ;YACb,IAAI,CAAC,KAAK,GAAG;QACjB;IACJ;IACA;;;;IAIA,GACA,gBAAgB,IAAI,EAAE,EAAE,EAAE;QACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YAAE;YAAM;QAAG;IACjC;IACA;;;;;;;;IAQA,GACA,OAAO,kBAAkB,KAAK,EAAE;QAC5B,OAAO,IAAI,cAAc,mNAAA,CAAA,SAAM;YAC3B,YAAY,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE;gBAClC,IAAI,OAAO,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE;gBAC5D,IAAI,SAAS;oBACT,WAAW;oBACX;wBACI,IAAI,KAAK;wBACT,IAAI,IAAI;4BACJ,KAAK,IAAI,KAAK,OACV,GAAG,WAAW,CAAC,IAAI,CAAC;4BACxB,IAAI,OACA,GAAG,UAAU,GAAG,GAAG,UAAU,GAAG,QAAQ,GAAG,CAAC;gCAAC,GAAG,UAAU;gCAAE;6BAAM,IAAI;wBAC9E;wBACA,IAAI,CAAC,SAAS,GAAG;wBACjB,OAAO,IAAI,mNAAA,CAAA,OAAI,CAAC,mNAAA,CAAA,WAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK;oBAChD;oBACA,WAAW;oBACX,WAAW;gBACf;gBACA,OAAO;YACX;QACJ;IACJ;IACA;;IAEA,GACA,OAAO,IAAI,EAAE;QACT,OAAO,KAAK,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM;QAC3C,IAAI,QAAQ,IAAI,CAAC,SAAS;QAC1B,OAAO,IAAI,CAAC,OAAO,IAAI,QAAQ,MAAM,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI;IACxF;IACA;;;IAGA,GACA,OAAO,MAAM;QAAE,OAAO;IAAgB;AAC1C;AACA,SAAS,aAAa,SAAS,EAAE,IAAI,EAAE,EAAE;IACrC,OAAO,mNAAA,CAAA,eAAY,CAAC,YAAY,CAAC,WAAW;QAAC;YAAE,OAAO;YAAM,KAAK;YAAI,OAAO;YAAM,KAAK;QAAG;KAAE;AAChG;AACA,MAAM;IACF,YACA,kEAAkE;IAClE,yDAAyD;IACzD,OAAO,CAAE;QACL,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI;IAC5B;IACA,MAAM,EAAE,EAAE;QACN,IAAI,CAAC,GAAG,UAAU,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAChD,OAAO,IAAI;QACf,IAAI,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,EAAE,GAAG,KAAK;QACrD,+DAA+D;QAC/D,iEAAiE;QACjE,qDAAqD;QACrD,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,GAAG,YACxD,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,MAAM,QAAQ,CAAC,EAAE;QACzE,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,cAAc,KAAI,OACjC,MAAM,QAAQ;QAClB,OAAO,IAAI,cAAc;IAC7B;IACA,OAAO,KAAK,KAAK,EAAE;QACf,IAAI,OAAO,KAAK,GAAG,CAAC,KAAK,qBAAqB,KAAI,MAAM,GAAG,CAAC,MAAM;QAClE,IAAI,aAAa,aAAa,MAAM,CAAC,MAAM,KAAK,CAAC,UAAU,MAAM,EAAE,OAAO;YAAE,MAAM;YAAG,IAAI;QAAK;QAC9F,IAAI,CAAC,WAAW,IAAI,CAAC,GAAG,cAAc,KAAI,OACtC,WAAW,QAAQ;QACvB,OAAO,IAAI,cAAc;IAC7B;AACJ;AACA,SAAS,KAAK,GAAG,WAAW,GAAE,2NAAA,CAAA,aAAU,CAAC,MAAM,CAAC;IAC5C,QAAQ,cAAc,IAAI;IAC1B,QAAO,KAAK,EAAE,EAAE;QACZ,KAAK,IAAI,KAAK,GAAG,OAAO,CACpB,IAAI,EAAE,EAAE,CAAC,SAAS,QAAQ,GACtB,OAAO,EAAE,KAAK;QACtB,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,WAChD,OAAO,cAAc,IAAI,CAAC,GAAG,KAAK;QACtC,OAAO,MAAM,KAAK,CAAC;IACvB;AACJ;AACA,IAAI,cAAc,CAAC;IACf,IAAI,UAAU,WAAW,IAAM,YAAY,IAAI,iBAAiB;IAChE,OAAO,IAAM,aAAa;AAC9B;AACA,IAAI,OAAO,uBAAuB,aAC9B,cAAc,CAAC;IACX,IAAI,OAAO,CAAC,GAAG,UAAU,WAAW;QAChC,OAAO,oBAAoB,UAAU;YAAE,SAAS,IAAI,iBAAiB,MAAK,IAAI,iBAAiB;QAAG;IACtG,GAAG,IAAI,iBAAiB;IACxB,OAAO,IAAM,OAAO,IAAI,aAAa,WAAW,mBAAmB;AACvE;AACJ,MAAM,iBAAiB,OAAO,aAAa,eAAe,CAAC,CAAC,KAAK,UAAU,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,cAAc,IACvI,IAAM,UAAU,UAAU,CAAC,cAAc,KAAK;AACpD,MAAM,cAAc,WAAW,GAAE,0NAAA,CAAA,aAAU,CAAC,SAAS,CAAC,MAAM;IACxD,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,aAAa,GAAG;QACrB,gCAAgC;QAChC,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,6CAA6C;QAC7C,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;QAC/B,IAAI,CAAC,YAAY;IACrB;IACA,OAAO,MAAM,EAAE;QACX,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,KAAK,EAAE,OAAO;QACtD,IAAI,GAAG,cAAc,CAAC,OAAO,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,OAAO,EAC7E,IAAI,CAAC,YAAY;QACrB,IAAI,OAAO,UAAU,IAAI,OAAO,YAAY,EAAE;YAC1C,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAClB,IAAI,CAAC,WAAW,IAAI,GAAG,oBAAoB;YAC/C,IAAI,CAAC,YAAY;QACrB;QACA,IAAI,CAAC,kBAAkB,CAAC;IAC5B;IACA,eAAe;QACX,IAAI,IAAI,CAAC,OAAO,EACZ;QACJ,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,QAAQ,MAAM,KAAK,CAAC,SAAS,KAAK;QAC7D,IAAI,MAAM,IAAI,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,MAAM,GAC1E,IAAI,CAAC,OAAO,GAAG,YAAY,IAAI,CAAC,IAAI;IAC5C;IACA,KAAK,QAAQ,EAAE;QACX,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,MAAM,KAAK,GAAG;QAClB,IAAI,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG;YAClE,IAAI,CAAC,QAAQ,GAAG,MAAM,MAAM,kBAAkB;YAC9C,IAAI,CAAC,WAAW,GAAG,KAAK,oBAAoB;QAChD;QACA,IAAI,IAAI,CAAC,WAAW,IAAI,GACpB,QAAQ,iBAAiB;QAC7B,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,QAAQ,MAAM,KAAK,CAAC,SAAS,KAAK;QACrF,IAAI,MAAM,IAAI,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,MAAM,CAAC,OAAO,OAAO,sBAAsB,MAC7F;QACJ,IAAI,UAAU,KAAK,GAAG,KAAK,KAAK,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,cAAc,KAAI,YAAY,CAAC,iBAAiB,KAAK,GAAG,CAAC,GAAG,iBAAiB,KAAI,SAAS,aAAa,KAAK,KAAK;QAC3K,IAAI,gBAAgB,MAAM,OAAO,CAAC,OAAO,GAAG,QAAQ,MAAM,GAAG,CAAC,MAAM,GAAG,OAAO;QAC9E,IAAI,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC;YAC1B,OAAO,kBAAkB,oBAAoB,KAAK,GAAG,KAAK;QAC9D,GAAG,OAAO,CAAC,gBAAgB,IAAI,OAAO,sBAAsB,GAAE;QAC9D,IAAI,CAAC,WAAW,IAAI,KAAK,GAAG,KAAK;QACjC,IAAI,QAAQ,IAAI,CAAC,WAAW,IAAI,GAAG;YAC/B,MAAM,OAAO,CAAC,QAAQ;YACtB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAAE,SAAS,SAAS,QAAQ,CAAC,EAAE,CAAC,IAAI,cAAc,MAAM,OAAO;YAAG;QACzF;QACA,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC,QAAQ,CAAC,aAAa,GAChD,IAAI,CAAC,YAAY;QACrB,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO;IACzC;IACA,mBAAmB,EAAE,EAAE;QACnB,IAAI,GAAG,UAAU,EAAE;YACf,IAAI,CAAC,aAAa;YAClB,GAAG,UAAU,CACR,IAAI,CAAC,IAAM,IAAI,CAAC,YAAY,IAC5B,KAAK,CAAC,CAAA,MAAO,CAAA,GAAA,0NAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,MAC3C,IAAI,CAAC,IAAM,IAAI,CAAC,aAAa;YAClC,GAAG,UAAU,GAAG;QACpB;IACJ;IACA,UAAU;QACN,IAAI,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO;IACpB;IACA,YAAY;QACR,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC;IACpD;AACJ,GAAG;IACC,eAAe;QAAE;YAAU,IAAI,CAAC,YAAY;QAAI;IAAE;AACtD;AACA;;;;;AAKA,GACA,MAAM,WAAW,WAAW,GAAE,2NAAA,CAAA,QAAK,CAAC,MAAM,CAAC;IACvC,SAAQ,SAAS;QAAI,OAAO,UAAU,MAAM,GAAG,SAAS,CAAC,EAAE,GAAG;IAAM;IACpE,SAAS,CAAA,WAAY;YACjB,SAAS,KAAK;YACd;YACA,0NAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC;gBAAC;aAAS,EAAE,CAAA;gBAC7C,IAAI,OAAO,MAAM,KAAK,CAAC;gBACvB,OAAO,QAAQ,KAAK,IAAI,GAAG;oBAAE,iBAAiB,KAAK,IAAI;gBAAC,IAAI,CAAC;YACjE;SACH;AACL;AACA;;;;;;AAMA,GACA,MAAM;IACF;;IAEA,GACA,YACA;;IAEA,GACA,QAAQ,EACR;;;;;IAKA,GACA,UAAU,EAAE,CAAE;QACV,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,SAAS,GAAG;YAAC;YAAU;SAAQ;IACxC;AACJ;AACA;;;;;AAKA,GACA,MAAM;IACF,YACA;;IAEA,GACA,IAAI,EACJ;;IAEA,GACA,KAAK,EACL;;IAEA,GACA,UAAU,EACV;;;IAGA,GACA,QAAQ,EAAE,QAAQ,EAClB;;IAEA,GACA,UAAU,SAAS,CAAE;QACjB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;IACnB;IACA;;;;IAIA,GACA,OAAO;QACH,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAA,UAAW,IAAI,CAAC,OAAO,GAAG,SAAS,CAAA;YAAS,IAAI,CAAC,OAAO,GAAG;YAAM,MAAM;QAAK,EAAE;IAC9I;IACA;;IAEA,GACA,OAAO,GAAG,IAAI,EAAE;QACZ,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;QACxB,IAAI,CAAC,MAAM;YACP,IAAI,CAAC,SACD,MAAM,IAAI,WAAW;YACzB,OAAO,IAAM,QAAQ,OAAO,CAAC;QACjC;QACA,OAAO,IAAI,oBAAoB,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,KAAK,UAAU,IAAI,EAAE,EAAE,KAAK,QAAQ,EAAE,MAAM;IAC1J;IACA;;;;;;IAMA,GACA,OAAO,cAAc,KAAK,EAAE,QAAQ,EAAE;QAClC,KAAK,IAAI,KAAK,MACV,IAAI,EAAE,QAAQ,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,WAC9B,OAAO;QACf,IAAI,MAAM,aAAa,IAAI,CAAC;QAC5B,IAAI,KACA;YAAA,KAAK,IAAI,KAAK,MACV,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAChC,OAAO;QAAC;QACpB,OAAO;IACX;IACA;;;;;;IAMA,GACA,OAAO,kBAAkB,KAAK,EAAE,IAAI,EAAE,QAAQ,IAAI,EAAE;QAChD,OAAO,KAAK,WAAW;QACvB,KAAK,IAAI,KAAK,MACV,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,KAAK,OACvB,OAAO;QACf,IAAI,OACA,KAAK,IAAI,KAAK,MACV,KAAK,IAAI,KAAK,EAAE,KAAK,CAAE;YACnB,IAAI,QAAQ,KAAK,OAAO,CAAC;YACzB,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,GAChG,OAAO;QACf;QACR,OAAO;IACX;AACJ;AAEA;;;;;;;;AAQA,GACA,MAAM,gBAAgB,WAAW,GAAE,2NAAA,CAAA,QAAK,CAAC,MAAM;AAC/C;;;;AAIA,GACA,MAAM,aAAa,WAAW,GAAE,2NAAA,CAAA,QAAK,CAAC,MAAM,CAAC;IACzC,SAAS,CAAA;QACL,IAAI,CAAC,OAAO,MAAM,EACd,OAAO;QACX,IAAI,OAAO,MAAM,CAAC,EAAE;QACpB,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,CAAA,IAAK,KAAK,IAAI,CAAC,EAAE,GACnE,MAAM,IAAI,MAAM,0BAA0B,KAAK,SAAS,CAAC,MAAM,CAAC,EAAE;QACtE,OAAO;IACX;AACJ;AACA;;;;;AAKA,GACA,SAAS,cAAc,KAAK;IACxB,IAAI,OAAO,MAAM,KAAK,CAAC;IACvB,OAAO,KAAK,UAAU,CAAC,MAAM,IAAI,MAAM,OAAO,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM;AAC9E;AACA;;;;;AAKA,GACA,SAAS,aAAa,KAAK,EAAE,IAAI;IAC7B,IAAI,SAAS,IAAI,KAAK,MAAM,OAAO,EAAE,KAAK,MAAM,KAAK,CAAC,WAAW,CAAC,EAAE;IACpE,IAAI,MAAM,MAAM;QACZ,MAAO,QAAQ,GAAI;YACf,UAAU;YACV,QAAQ;QACZ;QACA,KAAK;IACT;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IACtB,UAAU;IACd,OAAO;AACX;AACA;;;;;;;;AAQA,GACA,SAAS,eAAe,OAAO,EAAE,GAAG;IAChC,IAAI,mBAAmB,2NAAA,CAAA,cAAW,EAC9B,UAAU,IAAI,cAAc;IAChC,KAAK,IAAI,WAAW,QAAQ,KAAK,CAAC,KAAK,CAAC,eAAgB;QACpD,IAAI,SAAS,QAAQ,SAAS;QAC9B,IAAI,WAAW,WACX,OAAO;IACf;IACA,IAAI,OAAO,WAAW,QAAQ,KAAK;IACnC,OAAO,KAAK,MAAM,IAAI,MAAM,kBAAkB,SAAS,MAAM,OAAO;AACxE;AACA;;;AAGA,GACA,SAAS,YAAY,KAAK,EAAE,IAAI,EAAE,EAAE;IAChC,IAAI,UAAU,OAAO,MAAM,CAAC;IAC5B,IAAI,UAAU,IAAI,cAAc,OAAO;QAAE,qBAAqB,CAAA;YAAW,IAAI;YAAI,OAAO,CAAC,KAAK,OAAO,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,CAAC;QAAG;IAAE;IACrJ,IAAI,UAAU,EAAE;IAChB,IAAK,IAAI,MAAM,MAAM,OAAO,IAAK;QAC7B,IAAI,OAAO,MAAM,GAAG,CAAC,MAAM,CAAC;QAC5B,MAAM,KAAK,EAAE,GAAG;QAChB,IAAI,SAAS,eAAe,SAAS,KAAK,IAAI;QAC9C,IAAI,UAAU,MACV;QACJ,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,IAAI,GACpB,SAAS;QACb,IAAI,MAAM,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE;QACnC,IAAI,OAAO,aAAa,OAAO;QAC/B,IAAI,OAAO,MAAM;YACb,OAAO,CAAC,KAAK,IAAI,CAAC,GAAG;YACrB,QAAQ,IAAI,CAAC;gBAAE,MAAM,KAAK,IAAI;gBAAE,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM;gBAAE,QAAQ;YAAK;QAC7E;IACJ;IACA,OAAO,MAAM,OAAO,CAAC;AACzB;AACA;;;;;AAKA,GACA,MAAM;IACF;;IAEA,GACA,YACA;;IAEA,GACA,KAAK,EACL;;IAEA,GACA,UAAU,CAAC,CAAC,CAAE;QACV,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG,cAAc;IAC9B;IACA;;;;;;;IAOA,GACA,OAAO,GAAG,EAAE,OAAO,CAAC,EAAE;QAClB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC;QACjC,IAAI,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG,IAAI,CAAC,OAAO;QACzD,IAAI,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,IAAI,iBAAiB,KAAK,EAAE,EAAE;YACjF,IAAI,uBAAuB,iBAAiB,KACxC,OAAO;gBAAE,MAAM;gBAAI,MAAM;YAAI;iBAC5B,IAAI,OAAO,IAAI,gBAAgB,MAAM,iBAAiB,KACvD,OAAO;gBAAE,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,IAAI;gBAAG,MAAM;YAAc;iBAE/E,OAAO;gBAAE,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,gBAAgB,KAAK,IAAI;gBAAG,MAAM,KAAK,IAAI;YAAC;QACtF;QACA,OAAO;IACX;IACA;;;IAGA,GACA,aAAa,GAAG,EAAE,OAAO,CAAC,EAAE;QACxB,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,EACrE,OAAO;QACX,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK;QACtC,OAAO,KAAK,KAAK,CAAC,MAAM,MAAM,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE,MAAM,MAAM;IACpE;IACA;;IAEA,GACA,OAAO,GAAG,EAAE,OAAO,CAAC,EAAE;QAClB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK;QACtC,IAAI,SAAS,IAAI,CAAC,WAAW,CAAC,MAAM,MAAM;QAC1C,IAAI,WAAW,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,QAAQ,CAAC;QAC5F,IAAI,WAAW,CAAC,GACZ,UAAU,WAAW,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,MAAM,CAAC;QAC5D,OAAO;IACX;IACA;;;IAGA,GACA,YAAY,IAAI,EAAE,MAAM,KAAK,MAAM,EAAE;QACjC,OAAO,CAAA,GAAA,2NAAA,CAAA,cAAW,AAAD,EAAE,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;IACjD;IACA;;IAEA,GACA,WAAW,GAAG,EAAE,OAAO,CAAC,EAAE;QACtB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK;QACtC,IAAI,WAAW,IAAI,CAAC,OAAO,CAAC,mBAAmB;QAC/C,IAAI,UAAU;YACV,IAAI,YAAY,SAAS;YACzB,IAAI,YAAY,CAAC,GACb,OAAO;QACf;QACA,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,MAAM,CAAC;IAC9C;IACA;;;;IAIA,GACA,IAAI,iBAAiB;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI;IACzC;AACJ;AACA;;;;;;AAMA,GACA,MAAM,iBAAiB,WAAW,GAAE,IAAI,mNAAA,CAAA,WAAQ;AAChD,qEAAqE;AACrE,SAAS,kBAAkB,EAAE,EAAE,GAAG,EAAE,GAAG;IACnC,IAAI,QAAQ,IAAI,YAAY,CAAC;IAC7B,IAAI,QAAQ,IAAI,YAAY,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,0BAA0B,CAAC;IACjF,IAAI,SAAS,MAAM,IAAI,EAAE;QACrB,IAAI,MAAM,EAAE;QACZ,IAAK,IAAI,MAAM,OAAO,OAAO,MAAM,IAAI,EAAE,MAAM,IAAI,MAAM,CACrD,IAAI,IAAI,CAAC;QACb,IAAK,IAAI,IAAI,IAAI,MAAM,GAAG,GAAG,KAAK,GAAG,IACjC,QAAQ;YAAE,MAAM,GAAG,CAAC,EAAE;YAAE,MAAM;QAAM;IAC5C;IACA,OAAO,UAAU,OAAO,IAAI;AAChC;AACA,SAAS,UAAU,KAAK,EAAE,EAAE,EAAE,GAAG;IAC7B,IAAK,IAAI,MAAM,OAAO,KAAK,MAAM,IAAI,IAAI,CAAE;QACvC,IAAI,WAAW,eAAe,IAAI,IAAI;QACtC,IAAI,UACA,OAAO,SAAS,kBAAkB,MAAM,CAAC,IAAI,KAAK;IAC1D;IACA,OAAO;AACX;AACA,SAAS,aAAa,EAAE;IACpB,OAAO,GAAG,GAAG,IAAI,GAAG,OAAO,CAAC,aAAa,IAAI,GAAG,OAAO,CAAC,mBAAmB;AAC/E;AACA,SAAS,eAAe,IAAI;IACxB,IAAI,WAAW,KAAK,IAAI,CAAC,IAAI,CAAC;IAC9B,IAAI,UACA,OAAO;IACX,IAAI,QAAQ,KAAK,UAAU,EAAE;IAC7B,IAAI,SAAS,CAAC,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,mNAAA,CAAA,WAAQ,CAAC,QAAQ,CAAC,GAAG;QACvD,IAAI,OAAO,KAAK,SAAS,EAAE,SAAS,QAAQ,MAAM,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC;QACxE,OAAO,CAAA,KAAM,kBAAkB,IAAI,MAAM,GAAG,WAAW,UAAU,CAAC,aAAa,MAAM,KAAK,IAAI,GAAG;IACrG;IACA,OAAO,KAAK,MAAM,IAAI,OAAO,YAAY;AAC7C;AACA,SAAS;IAAc,OAAO;AAAG;AACjC;;;AAGA,GACA,MAAM,0BAA0B;IAC5B,YAAY,IAAI,EAChB;;IAEA,GACA,GAAG,EACH;;IAEA,GACA,OAAO,CAAE;QACL,KAAK,CAAC,KAAK,KAAK,EAAE,KAAK,OAAO;QAC9B,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,OAAO,GAAG;IACnB;IACA;;;IAGA,GACA,IAAI,OAAO;QAAE,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI;IAAE;IACvC;;IAEA,GACA,OAAO,OAAO,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE;QAC9B,OAAO,IAAI,kBAAkB,MAAM,KAAK;IAC5C;IACA;;;IAGA,GACA,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG;IACrC;IACA;;;;;;IAMA,GACA,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;IACvC;IACA;;;IAGA,GACA,cAAc,IAAI,EAAE;QAChB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,IAAI;QAC1C,kEAAkE;QAClE,OAAS;YACL,IAAI,UAAU,KAAK,OAAO,CAAC,KAAK,IAAI;YACpC,MAAO,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,IAAI,IAAI,QAAQ,IAAI,CACxD,UAAU,QAAQ,MAAM;YAC5B,IAAI,SAAS,SAAS,OAClB;YACJ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,IAAI;QAC7C;QACA,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI;IACpC;IACA;;;IAGA,GACA,WAAW;QACP,OAAO,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG;IAC3D;AACJ;AACA,SAAS,SAAS,MAAM,EAAE,EAAE;IACxB,IAAK,IAAI,MAAM,IAAI,KAAK,MAAM,IAAI,MAAM,CACpC,IAAI,UAAU,KACV,OAAO;IACf,OAAO;AACX;AACA,+DAA+D;AAC/D,oEAAoE;AACpE,mCAAmC;AACnC,SAAS,iBAAiB,OAAO;IAC7B,IAAI,OAAO,QAAQ,IAAI;IACvB,IAAI,YAAY,KAAK,UAAU,CAAC,KAAK,IAAI,GAAG,OAAO,KAAK,SAAS;IACjE,IAAI,CAAC,WACD,OAAO;IACX,IAAI,MAAM,QAAQ,OAAO,CAAC,aAAa;IACvC,IAAI,WAAW,QAAQ,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,IAAI;IACtD,IAAI,UAAU,OAAO,QAAQ,OAAO,SAAS,IAAI,GAAG,SAAS,EAAE,GAAG,KAAK,GAAG,CAAC,SAAS,EAAE,EAAE;IACxF,IAAK,IAAI,MAAM,UAAU,EAAE,GAAI;QAC3B,IAAI,OAAO,KAAK,UAAU,CAAC;QAC3B,IAAI,CAAC,QAAQ,QAAQ,MACjB,OAAO;QACX,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,EAAE;YACtB,IAAI,KAAK,IAAI,IAAI,SACb,OAAO;YACX,IAAI,QAAQ,MAAM,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,GAAG,SAAS,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM;YACnF,OAAO;gBAAE,MAAM,UAAU,IAAI;gBAAE,IAAI,UAAU,EAAE,GAAG;YAAM;QAC5D;QACA,MAAM,KAAK,EAAE;IACjB;AACJ;AACA;;;;;;;;;;AAUA,GACA,SAAS,gBAAgB,EAAE,OAAO,EAAE,QAAQ,IAAI,EAAE,QAAQ,CAAC,EAAE;IACzD,OAAO,CAAC,UAAY,kBAAkB,SAAS,OAAO,OAAO;AACjE;AACA,SAAS,kBAAkB,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ;IAC/D,IAAI,QAAQ,QAAQ,SAAS,EAAE,QAAQ,MAAM,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM;IACpE,IAAI,SAAS,WAAW,MAAM,KAAK,CAAC,OAAO,QAAQ,QAAQ,MAAM,KAAK,WAAW,YAAY,QAAQ,GAAG,GAAG;IAC3G,IAAI,UAAU,QAAQ,iBAAiB,WAAW;IAClD,IAAI,SACA,OAAO,SAAS,QAAQ,MAAM,CAAC,QAAQ,IAAI,IAAI,QAAQ,MAAM,CAAC,QAAQ,EAAE;IAC5E,OAAO,QAAQ,UAAU,GAAG,CAAC,SAAS,IAAI,QAAQ,IAAI,GAAG,KAAK;AAClE;AACA;;;AAGA,GACA,MAAM,aAAa,CAAC,UAAY,QAAQ,UAAU;AAClD;;;;;;;AAOA,GACA,SAAS,gBAAgB,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC;IAC/C,OAAO,CAAC;QACJ,IAAI,cAAc,UAAU,OAAO,IAAI,CAAC,QAAQ,SAAS;QACzD,OAAO,QAAQ,UAAU,GAAG,CAAC,cAAc,IAAI,QAAQ,QAAQ,IAAI;IACvE;AACJ;AACA,MAAM,mBAAmB;AACzB;;;;;;;;;;;;AAYA,GACA,SAAS;IACL,OAAO,2NAAA,CAAA,cAAW,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAA;QACpC,IAAI,CAAC,GAAG,UAAU,IAAI,CAAC,GAAG,WAAW,CAAC,iBAAiB,CAAC,GAAG,WAAW,CAAC,mBACnE,OAAO;QACX,IAAI,QAAQ,GAAG,UAAU,CAAC,cAAc,CAAC,iBAAiB,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI;QAC3F,IAAI,CAAC,MAAM,MAAM,EACb,OAAO;QACX,IAAI,MAAM,GAAG,MAAM,EAAE,EAAE,IAAI,EAAE,GAAG,GAAG,YAAY,CAAC,IAAI,EAAE,OAAO,IAAI,MAAM,CAAC;QACxE,IAAI,OAAO,KAAK,IAAI,GAAG,kBACnB,OAAO;QACX,IAAI,YAAY,IAAI,WAAW,CAAC,KAAK,IAAI,EAAE;QAC3C,IAAI,CAAC,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,aACxB,OAAO;QACX,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,OAAO,CAAC,GAAG,UAAU,EAAE;QAC3C,KAAK,IAAI,EAAE,IAAI,EAAE,IAAI,MAAM,SAAS,CAAC,MAAM,CAAE;YACzC,IAAI,OAAO,MAAM,GAAG,CAAC,MAAM,CAAC;YAC5B,IAAI,KAAK,IAAI,IAAI,MACb;YACJ,OAAO,KAAK,IAAI;YAChB,IAAI,SAAS,eAAe,OAAO,KAAK,IAAI;YAC5C,IAAI,UAAU,MACV;YACJ,IAAI,MAAM,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE;YACnC,IAAI,OAAO,aAAa,OAAO;YAC/B,IAAI,OAAO,MACP,QAAQ,IAAI,CAAC;gBAAE,MAAM,KAAK,IAAI;gBAAE,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM;gBAAE,QAAQ;YAAK;QACjF;QACA,OAAO,QAAQ,MAAM,GAAG;YAAC;YAAI;gBAAE;gBAAS,YAAY;YAAK;SAAE,GAAG;IAClE;AACJ;AAEA;;;;;AAKA,GACA,MAAM,cAAc,WAAW,GAAE,2NAAA,CAAA,QAAK,CAAC,MAAM;AAC7C;;;;;AAKA,GACA,MAAM,eAAe,WAAW,GAAE,IAAI,mNAAA,CAAA,WAAQ;AAC9C;;;;AAIA,GACA,SAAS,WAAW,IAAI;IACpB,IAAI,QAAQ,KAAK,UAAU,EAAE,OAAO,KAAK,SAAS;IAClD,OAAO,SAAS,MAAM,EAAE,GAAG,KAAK,IAAI,GAAG;QAAE,MAAM,MAAM,EAAE;QAAE,IAAI,KAAK,IAAI,CAAC,OAAO,GAAG,KAAK,EAAE,GAAG,KAAK,IAAI;IAAC,IAAI;AAC7G;AACA,SAAS,cAAc,KAAK,EAAE,KAAK,EAAE,GAAG;IACpC,IAAI,OAAO,WAAW;IACtB,IAAI,KAAK,MAAM,GAAG,KACd,OAAO;IACX,IAAI,QAAQ,KAAK,YAAY,CAAC,KAAK;IACnC,IAAI,QAAQ;IACZ,IAAK,IAAI,OAAO,OAAO,MAAM,OAAO,KAAK,IAAI,CAAE;QAC3C,IAAI,MAAM,KAAK,IAAI;QACnB,IAAI,IAAI,EAAE,IAAI,OAAO,IAAI,IAAI,GAAG,KAC5B;QACJ,IAAI,SAAS,IAAI,IAAI,GAAG,OACpB;QACJ,IAAI,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,QAAQ,CAAC,IAAI,EAAE,GAAG,KAAK,MAAM,GAAG,MAAM,KAAK,MAAM,IAAI,MAAM,GAAG,CAAC,MAAM,IAAI,CAAC,aAAa,IAAI,GAAG;YAC9F,IAAI,QAAQ,KAAK,KAAK;YACtB,IAAI,SAAS,MAAM,IAAI,IAAI,OAAO,MAAM,IAAI,IAAI,SAAS,MAAM,EAAE,GAAG,KAChE,QAAQ;QAChB;IACJ;IACA,OAAO;AACX;AACA,SAAS,aAAa,IAAI;IACtB,IAAI,KAAK,KAAK,SAAS;IACvB,OAAO,MAAM,GAAG,EAAE,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,CAAC,OAAO;AACpD;AACA;;;;;;;AAOA,GACA,SAAS,SAAS,KAAK,EAAE,SAAS,EAAE,OAAO;IACvC,KAAK,IAAI,WAAW,MAAM,KAAK,CAAC,aAAc;QAC1C,IAAI,SAAS,QAAQ,OAAO,WAAW;QACvC,IAAI,QACA,OAAO;IACf;IACA,OAAO,cAAc,OAAO,WAAW;AAC3C;AACA,SAAS,SAAS,KAAK,EAAE,OAAO;IAC5B,IAAI,OAAO,QAAQ,MAAM,CAAC,MAAM,IAAI,EAAE,IAAI,KAAK,QAAQ,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC;IACzE,OAAO,QAAQ,KAAK,YAAY;QAAE;QAAM;IAAG;AAC/C;AACA;;;;;;AAMA,GACA,MAAM,aAAa,WAAW,GAAE,2NAAA,CAAA,cAAW,CAAC,MAAM,CAAC;IAAE,KAAK;AAAS;AACnE;;AAEA,GACA,MAAM,eAAe,WAAW,GAAE,2NAAA,CAAA,cAAW,CAAC,MAAM,CAAC;IAAE,KAAK;AAAS;AACrE,SAAS,cAAc,IAAI;IACvB,IAAI,QAAQ,EAAE;IACd,KAAK,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,KAAK,CAAC,SAAS,CAAC,MAAM,CAAE;QAC9C,IAAI,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,QAAQ,EAAE,EAAE,IAAI,OAC1C;QACJ,MAAM,IAAI,CAAC,KAAK,WAAW,CAAC;IAChC;IACA,OAAO;AACX;AACA;;;;;;AAMA,GACA,MAAM,YAAY,WAAW,GAAE,2NAAA,CAAA,aAAU,CAAC,MAAM,CAAC;IAC7C;QACI,OAAO,0NAAA,CAAA,aAAU,CAAC,IAAI;IAC1B;IACA,QAAO,MAAM,EAAE,EAAE;QACb,SAAS,OAAO,GAAG,CAAC,GAAG,OAAO;QAC9B,KAAK,IAAI,KAAK,GAAG,OAAO,CAAE;YACtB,IAAI,EAAE,EAAE,CAAC,eAAe,CAAC,WAAW,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,EAAE,GAAG;gBACnE,IAAI,EAAE,kBAAkB,EAAE,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC;gBAC5C,IAAI,SAAS,CAAC,qBAAqB,aAC/B,0NAAA,CAAA,aAAU,CAAC,OAAO,CAAC;oBAAE,QAAQ,IAAI,mBAAmB,mBAAmB,GAAG,KAAK,EAAE,EAAE,KAAK;gBAAG;gBAC/F,SAAS,OAAO,MAAM,CAAC;oBAAE,KAAK;wBAAC,OAAO,KAAK,CAAC,EAAE,KAAK,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,EAAE;qBAAE;gBAAC;YAC3E,OACK,IAAI,EAAE,EAAE,CAAC,eAAe;gBACzB,SAAS,OAAO,MAAM,CAAC;oBAAE,QAAQ,CAAC,MAAM,KAAO,EAAE,KAAK,CAAC,IAAI,IAAI,QAAQ,EAAE,KAAK,CAAC,EAAE,IAAI;oBACjF,YAAY,EAAE,KAAK,CAAC,IAAI;oBAAE,UAAU,EAAE,KAAK,CAAC,EAAE;gBAAC;YACvD;QACJ;QACA,oDAAoD;QACpD,IAAI,GAAG,SAAS,EAAE;YACd,IAAI,cAAc,OAAO,EAAE,IAAI,EAAE,GAAG,GAAG,SAAS,CAAC,IAAI;YACrD,OAAO,OAAO,CAAC,MAAM,MAAM,CAAC,GAAG;gBAAQ,IAAI,IAAI,QAAQ,IAAI,MACvD,cAAc;YAAM;YACxB,IAAI,aACA,SAAS,OAAO,MAAM,CAAC;gBACnB,YAAY;gBACZ,UAAU;gBACV,QAAQ,CAAC,GAAG,IAAM,KAAK,QAAQ,KAAK;YACxC;QACR;QACA,OAAO;IACX;IACA,SAAS,CAAA,IAAK,0NAAA,CAAA,aAAU,CAAC,WAAW,CAAC,IAAI,CAAC;IAC1C,QAAO,MAAM,EAAE,KAAK;QAChB,IAAI,SAAS,EAAE;QACf,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,EAAE,CAAC,MAAM;YAAS,OAAO,IAAI,CAAC,MAAM;QAAK;QAC3E,OAAO;IACX;IACA,UAAS,KAAK;QACV,IAAI,CAAC,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,GAAG,GACxC,MAAM,IAAI,WAAW;QACzB,IAAI,SAAS,EAAE;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAG;YAC/B,IAAI,OAAO,KAAK,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,IAAI;YACtC,IAAI,OAAO,QAAQ,YAAY,OAAO,MAAM,UACxC,MAAM,IAAI,WAAW;YACzB,OAAO,IAAI,CAAC,WAAW,KAAK,CAAC,MAAM;QACvC;QACA,OAAO,0NAAA,CAAA,aAAU,CAAC,GAAG,CAAC,QAAQ;IAClC;AACJ;AACA;;;AAGA,GACA,SAAS,aAAa,KAAK;IACvB,OAAO,MAAM,KAAK,CAAC,WAAW,UAAU,2NAAA,CAAA,WAAQ,CAAC,KAAK;AAC1D;AACA,SAAS,SAAS,KAAK,EAAE,IAAI,EAAE,EAAE;IAC7B,IAAI;IACJ,IAAI,QAAQ;IACZ,CAAC,KAAK,MAAM,KAAK,CAAC,WAAW,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM;QAClG,IAAI,CAAC,SAAS,MAAM,IAAI,GAAG,MACvB,QAAQ;YAAE;YAAM;QAAG;IAC3B;IACA,OAAO;AACX;AACA,SAAS,WAAW,MAAM,EAAE,IAAI,EAAE,EAAE;IAChC,IAAI,QAAQ;IACZ,OAAO,OAAO,CAAC,MAAM,MAAM,CAAC,GAAG;QAAQ,IAAI,KAAK,QAAQ,KAAK,IACzD,QAAQ;IAAM;IAClB,OAAO;AACX;AACA,SAAS,YAAY,KAAK,EAAE,KAAK;IAC7B,OAAO,MAAM,KAAK,CAAC,WAAW,SAAS,QAAQ,MAAM,MAAM,CAAC,2NAAA,CAAA,cAAW,CAAC,YAAY,CAAC,EAAE,CAAC;AAC5F;AACA;;AAEA,GACA,MAAM,WAAW,CAAA;IACb,KAAK,IAAI,QAAQ,cAAc,MAAO;QAClC,IAAI,QAAQ,SAAS,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,KAAK,EAAE;QACnD,IAAI,OAAO;YACP,KAAK,QAAQ,CAAC;gBAAE,SAAS,YAAY,KAAK,KAAK,EAAE;oBAAC,WAAW,EAAE,CAAC;oBAAQ,aAAa,MAAM;iBAAO;YAAE;YACpG,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA;;AAEA,GACA,MAAM,aAAa,CAAA;IACf,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,WAAW,QAC7B,OAAO;IACX,IAAI,UAAU,EAAE;IAChB,KAAK,IAAI,QAAQ,cAAc,MAAO;QAClC,IAAI,SAAS,SAAS,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,KAAK,EAAE;QACpD,IAAI,QACA,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC,SAAS,aAAa,MAAM,QAAQ;IACzE;IACA,IAAI,QAAQ,MAAM,EACd,KAAK,QAAQ,CAAC;QAAE;IAAQ;IAC5B,OAAO,QAAQ,MAAM,GAAG;AAC5B;AACA,SAAS,aAAa,IAAI,EAAE,KAAK,EAAE,OAAO,IAAI;IAC1C,IAAI,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,MAAM,EAAE,SAAS,KAAK,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,MAAM;IACxG,OAAO,0NAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,OAAO,iBAAiB,kBAAkB,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;AACpJ;AACA;;;;;;;;AAQA,GACA,MAAM,UAAU,CAAA;IACZ,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,UAAU,EAAE;IAClC,IAAK,IAAI,MAAM,GAAG,MAAM,MAAM,GAAG,CAAC,MAAM,EAAG;QACvC,IAAI,OAAO,KAAK,WAAW,CAAC,MAAM,QAAQ,SAAS,OAAO,KAAK,IAAI,EAAE,KAAK,EAAE;QAC5E,IAAI,OACA,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;QAC/B,MAAM,CAAC,QAAQ,KAAK,WAAW,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE,EAAE,GAAG;IAC3D;IACA,IAAI,QAAQ,MAAM,EACd,KAAK,QAAQ,CAAC;QAAE,SAAS,YAAY,KAAK,KAAK,EAAE;IAAS;IAC9D,OAAO,CAAC,CAAC,QAAQ,MAAM;AAC3B;AACA;;AAEA,GACA,MAAM,YAAY,CAAA;IACd,IAAI,QAAQ,KAAK,KAAK,CAAC,KAAK,CAAC,WAAW;IACxC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,EACrB,OAAO;IACX,IAAI,UAAU,EAAE;IAChB,MAAM,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,MAAM;QAAS,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;YAAE;YAAM;QAAG;IAAK;IACrG,KAAK,QAAQ,CAAC;QAAE;IAAQ;IACxB,OAAO;AACX;AACA,oEAAoE;AACpE,SAAS,kBAAkB,IAAI,EAAE,SAAS;IACtC,0EAA0E;IAC1E,2BAA2B;IAC3B,IAAK,IAAI,OAAO,YAAa;QACzB,IAAI,iBAAiB,SAAS,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,KAAK,EAAE;QAC5D,IAAI,kBAAkB,eAAe,EAAE,GAAG,UAAU,IAAI,EACpD,OAAO;QACX,IAAI,CAAC,KAAK,IAAI,EACV,OAAO;QACX,OAAO,KAAK,WAAW,CAAC,KAAK,IAAI,GAAG;IACxC;AACJ;AACA;;;;AAIA,GACA,MAAM,aAAa,CAAC;IAChB,IAAI,UAAU,EAAE;IAChB,KAAK,IAAI,QAAQ,cAAc,MAAO;QAClC,IAAI,SAAS,SAAS,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,KAAK,EAAE;QACpD,IAAI,QAAQ;YACR,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC,SAAS,aAAa,MAAM,QAAQ;QACrE,OACK;YACD,IAAI,YAAY,kBAAkB,MAAM;YACxC,IAAI,WACA,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC,YAAY,aAAa,MAAM;QAClE;IACJ;IACA,IAAI,QAAQ,MAAM,GAAG,GACjB,KAAK,QAAQ,CAAC;QAAE,SAAS,YAAY,KAAK,KAAK,EAAE;IAAS;IAC9D,OAAO,CAAC,CAAC,QAAQ,MAAM;AAC3B;AACA;;;;;;;AAOA,GACA,MAAM,aAAa;IACf;QAAE,KAAK;QAAgB,KAAK;QAAa,KAAK;IAAS;IACvD;QAAE,KAAK;QAAgB,KAAK;QAAa,KAAK;IAAW;IACzD;QAAE,KAAK;QAAc,KAAK;IAAQ;IAClC;QAAE,KAAK;QAAc,KAAK;IAAU;CACvC;AACD,MAAM,gBAAgB;IAClB,gBAAgB;IAChB,oBAAoB;IACpB,iBAAiB;AACrB;AACA,MAAM,aAAa,WAAW,GAAE,2NAAA,CAAA,QAAK,CAAC,MAAM,CAAC;IACzC,SAAQ,MAAM;QAAI,OAAO,CAAA,GAAA,2NAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;IAAgB;AACnE;AACA;;AAEA,GACA,SAAS,YAAY,MAAM;IACvB,IAAI,SAAS;QAAC;QAAW;KAAY;IACrC,IAAI,QACA,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;IAC9B,OAAO;AACX;AACA,SAAS,YAAY,IAAI,EAAE,QAAQ;IAC/B,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,MAAM,KAAK,CAAC;IACzC,IAAI,UAAU,CAAC;QACX,IAAI,OAAO,KAAK,WAAW,CAAC,KAAK,QAAQ,CAAC,MAAM,MAAM;QACtD,IAAI,SAAS,SAAS,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,KAAK,EAAE;QACpD,IAAI,QACA,KAAK,QAAQ,CAAC;YAAE,SAAS,aAAa,EAAE,CAAC;QAAQ;QACrD,MAAM,cAAc;IACxB;IACA,IAAI,KAAK,cAAc,EACnB,OAAO,KAAK,cAAc,CAAC,MAAM,SAAS;IAC9C,IAAI,UAAU,SAAS,aAAa,CAAC;IACrC,QAAQ,WAAW,GAAG,KAAK,eAAe;IAC1C,QAAQ,YAAY,CAAC,cAAc,MAAM,MAAM,CAAC;IAChD,QAAQ,KAAK,GAAG,MAAM,MAAM,CAAC;IAC7B,QAAQ,SAAS,GAAG;IACpB,QAAQ,OAAO,GAAG;IAClB,OAAO;AACX;AACA,MAAM,aAAa,WAAW,GAAE,0NAAA,CAAA,aAAU,CAAC,OAAO,CAAC;IAAE,QAAQ,WAAW,GAAE,IAAI,cAAc,0NAAA,CAAA,aAAU;QAC9F,MAAM,IAAI,EAAE;YAAE,OAAO,YAAY,MAAM;QAAO;IAClD;AAAE;AACN,MAAM,2BAA2B,0NAAA,CAAA,aAAU;IACvC,YAAY,KAAK,CAAE;QACf,KAAK;QACL,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,GAAG,KAAK,EAAE;QAAE,OAAO,IAAI,CAAC,KAAK,IAAI,MAAM,KAAK;IAAE;IAC9C,MAAM,IAAI,EAAE;QAAE,OAAO,YAAY,MAAM,IAAI,CAAC,KAAK;IAAG;AACxD;AACA,MAAM,qBAAqB;IACvB,UAAU;IACV,YAAY;IACZ,WAAW;IACX,kBAAkB,CAAC;IACnB,gBAAgB,IAAM;AAC1B;AACA,MAAM,mBAAmB,0NAAA,CAAA,eAAY;IACjC,YAAY,MAAM,EAAE,IAAI,CAAE;QACtB,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,GAAG,KAAK,EAAE;QAAE,OAAO,IAAI,CAAC,MAAM,IAAI,MAAM,MAAM,IAAI,IAAI,CAAC,IAAI,IAAI,MAAM,IAAI;IAAE;IAC3E,MAAM,IAAI,EAAE;QACR,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EACrB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI;QAC1C,IAAI,OAAO,SAAS,aAAa,CAAC;QAClC,KAAK,WAAW,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU;QAC5E,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,cAAc;QACzD,OAAO;IACX;AACJ;AACA;;;;AAIA,GACA,SAAS,WAAW,SAAS,CAAC,CAAC;IAC3B,IAAI,aAAa,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,qBAAqB;IACtE,IAAI,UAAU,IAAI,WAAW,YAAY,OAAO,YAAY,IAAI,WAAW,YAAY;IACvF,IAAI,UAAU,0NAAA,CAAA,aAAU,CAAC,SAAS,CAAC;QAC/B,YAAY,IAAI,CAAE;YACd,IAAI,CAAC,IAAI,GAAG,KAAK,QAAQ,CAAC,IAAI;YAC9B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;QACrC;QACA,OAAO,MAAM,EAAE;YACX,IAAI,OAAO,UAAU,IAAI,OAAO,eAAe,IAC3C,OAAO,UAAU,CAAC,KAAK,CAAC,aAAa,OAAO,KAAK,CAAC,KAAK,CAAC,aACxD,OAAO,UAAU,CAAC,KAAK,CAAC,WAAW,UAAU,OAAO,KAAK,CAAC,KAAK,CAAC,WAAW,UAC3E,WAAW,OAAO,UAAU,KAAK,WAAW,OAAO,KAAK,KACxD,WAAW,cAAc,CAAC,SAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,IAAI;QACpD;QACA,aAAa,IAAI,EAAE;YACf,IAAI,UAAU,IAAI,2NAAA,CAAA,kBAAe;YACjC,KAAK,IAAI,QAAQ,KAAK,kBAAkB,CAAE;gBACtC,IAAI,OAAO,SAAS,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,KAAK,EAAE,IAAI,YAChD,SAAS,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,KAAK,EAAE,IAAI,UAAU;gBAC3D,IAAI,MACA,QAAQ,GAAG,CAAC,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE;YAC1C;YACA,OAAO,QAAQ,MAAM;QACzB;IACJ;IACA,IAAI,EAAE,gBAAgB,EAAE,GAAG;IAC3B,OAAO;QACH;QACA,CAAA,GAAA,0NAAA,CAAA,SAAM,AAAD,EAAE;YACH,OAAO;YACP,SAAQ,IAAI;gBAAI,IAAI;gBAAI,OAAO,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,KAAK,2NAAA,CAAA,WAAQ,CAAC,KAAK;YAAE;YAChI;gBACI,OAAO,IAAI,WAAW,YAAY;YACtC;YACA,kBAAkB,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,mBAAmB;gBAAE,OAAO,CAAC,MAAM,MAAM;oBACnF,IAAI,iBAAiB,KAAK,IAAI,iBAAiB,KAAK,CAAC,MAAM,MAAM,QAC7D,OAAO;oBACX,IAAI,SAAS,SAAS,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,KAAK,EAAE;oBACpD,IAAI,QAAQ;wBACR,KAAK,QAAQ,CAAC;4BAAE,SAAS,aAAa,EAAE,CAAC;wBAAQ;wBACjD,OAAO;oBACX;oBACA,IAAI,QAAQ,SAAS,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,KAAK,EAAE;oBACnD,IAAI,OAAO;wBACP,KAAK,QAAQ,CAAC;4BAAE,SAAS,WAAW,EAAE,CAAC;wBAAO;wBAC9C,OAAO;oBACX;oBACA,OAAO;gBACX;YAAE;QACV;QACA;KACH;AACL;AACA,MAAM,cAAc,WAAW,GAAE,0NAAA,CAAA,aAAU,CAAC,SAAS,CAAC;IAClD,uBAAuB;QACnB,iBAAiB;QACjB,QAAQ;QACR,OAAO;QACP,cAAc;QACd,QAAQ;QACR,SAAS;QACT,QAAQ;IACZ;IACA,uBAAuB;QACnB,SAAS;QACT,QAAQ;IACZ;AACJ;AAEA;;;AAGA,GACA,MAAM;IACF,YACA;;IAEA,GACA,KAAK,EAAE,OAAO,CAAE;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI;QACJ,SAAS,IAAI,IAAI;YACb,IAAI,MAAM,+MAAA,CAAA,cAAW,CAAC,OAAO;YAC7B,CAAC,WAAW,CAAC,UAAU,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,IAAI,GAAG;YAC1D,OAAO;QACX;QACA,MAAM,MAAM,OAAO,QAAQ,GAAG,IAAI,WAAW,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG,IAAI,QAAQ,GAAG,IAAI;QAC5F,MAAM,WAAW,QAAQ,KAAK;QAC9B,IAAI,CAAC,KAAK,GAAG,oBAAoB,WAAW,CAAC,OAAS,KAAK,IAAI,CAAC,qBAAqB,SAAS,IAAI,GAC5F,WAAW,CAAC,OAAS,QAAQ,WAAW;QAC9C,IAAI,CAAC,KAAK,GAAG,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,GAAG,CAAC,CAAA,QAAS,CAAC;gBAC5C,KAAK,MAAM,GAAG;gBACd,OAAO,MAAM,KAAK,IAAI,IAAI,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;oBAAE,KAAK;gBAAK;YACnE,CAAC,IAAI;YACD;QACJ,GAAG,KAAK;QACR,IAAI,CAAC,MAAM,GAAG,UAAU,IAAI,+MAAA,CAAA,cAAW,CAAC,WAAW;QACnD,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS;IACtC;IACA;;;;;;;;;;;;;;IAcA,GACA,OAAO,OAAO,KAAK,EAAE,OAAO,EAAE;QAC1B,OAAO,IAAI,eAAe,OAAO,WAAW,CAAC;IACjD;AACJ;AACA,MAAM,mBAAmB,WAAW,GAAE,2NAAA,CAAA,QAAK,CAAC,MAAM;AAClD,MAAM,sBAAsB,WAAW,GAAE,2NAAA,CAAA,QAAK,CAAC,MAAM,CAAC;IAClD,SAAQ,MAAM;QAAI,OAAO,OAAO,MAAM,GAAG;YAAC,MAAM,CAAC,EAAE;SAAC,GAAG;IAAM;AACjE;AACA,SAAS,gBAAgB,KAAK;IAC1B,IAAI,OAAO,MAAM,KAAK,CAAC;IACvB,OAAO,KAAK,MAAM,GAAG,OAAO,MAAM,KAAK,CAAC;AAC5C;AACA;;;;;;AAMA,GACA,SAAS,mBAAmB,WAAW,EAAE,OAAO;IAC5C,IAAI,MAAM;QAAC;KAAgB,EAAE;IAC7B,IAAI,uBAAuB,gBAAgB;QACvC,IAAI,YAAY,MAAM,EAClB,IAAI,IAAI,CAAC,0NAAA,CAAA,aAAU,CAAC,WAAW,CAAC,EAAE,CAAC,YAAY,MAAM;QACzD,YAAY,YAAY,SAAS;IACrC;IACA,IAAI,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,QAAQ,EAClE,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC/B,IAAI,WACL,IAAI,IAAI,CAAC,iBAAiB,QAAQ,CAAC;QAAC,0NAAA,CAAA,aAAU,CAAC,SAAS;KAAC,EAAE,CAAA;QACvD,OAAO,MAAM,KAAK,CAAC,0NAAA,CAAA,aAAU,CAAC,SAAS,KAAK,CAAC,aAAa,MAAM,IAAI;YAAC;SAAY,GAAG,EAAE;IAC1F;SAEA,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACjC,OAAO;AACX;AACA;;;;;;AAMA,GACA,SAAS,gBAAgB,KAAK,EAAE,IAAI,EAAE,KAAK;IACvC,IAAI,eAAe,gBAAgB;IACnC,IAAI,SAAS;IACb,IAAI,cACA,KAAK,IAAI,eAAe,aAAc;QAClC,IAAI,CAAC,YAAY,KAAK,IAAI,SAAS,YAAY,KAAK,CAAC,QAAQ;YACzD,IAAI,MAAM,YAAY,KAAK,CAAC;YAC5B,IAAI,KACA,SAAS,SAAS,SAAS,MAAM,MAAM;QAC/C;IACJ;IACJ,OAAO;AACX;AACA,MAAM;IACF,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,SAAS,GAAG,OAAO,MAAM,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,WAAW,KAAK,KAAK;QACjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,gBAAgB,KAAK,KAAK;QAClE,IAAI,CAAC,WAAW,GAAG,KAAK,QAAQ,CAAC,EAAE;IACvC;IACA,OAAO,MAAM,EAAE;QACX,IAAI,OAAO,WAAW,OAAO,KAAK,GAAG,eAAe,gBAAgB,OAAO,KAAK;QAChF,IAAI,cAAc,gBAAgB,gBAAgB,OAAO,UAAU;QACnE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,IAAI,EAAE,oBAAoB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE;QAC5F,IAAI,KAAK,MAAM,GAAG,SAAS,EAAE,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,qBAAqB,SAAS,EAAE,EAAE;YAC9G,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,OAAO;YACtD,IAAI,CAAC,WAAW,GAAG;QACvB,OACK,IAAI,QAAQ,IAAI,CAAC,IAAI,IAAI,OAAO,eAAe,IAAI,aAAa;YACjE,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,EAAE;YAC/C,IAAI,CAAC,WAAW,GAAG,SAAS,EAAE;QAClC;IACJ;IACA,UAAU,IAAI,EAAE,YAAY,EAAE;QAC1B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAClC,OAAO,0NAAA,CAAA,aAAU,CAAC,IAAI;QAC1B,IAAI,UAAU,IAAI,2NAAA,CAAA,kBAAe;QACjC,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,KAAK,aAAa,CAAE;YACzC,CAAA,GAAA,yNAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC,MAAM,IAAI;gBAC9C,QAAQ,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,0NAAA,CAAA,aAAU,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAM,EAAE;YAC7G,GAAG,MAAM;QACb;QACA,OAAO,QAAQ,MAAM;IACzB;AACJ;AACA,MAAM,kBAAkB,WAAW,GAAE,2NAAA,CAAA,OAAI,CAAC,IAAI,CAAC,WAAW,GAAE,0NAAA,CAAA,aAAU,CAAC,SAAS,CAAC,iBAAiB;IAC9F,aAAa,CAAA,IAAK,EAAE,WAAW;AACnC;AACA;;AAEA,GACA,MAAM,wBAAwB,WAAW,GAAE,eAAe,MAAM,CAAC;IAC7D;QAAE,KAAK,yNAAA,CAAA,OAAI,CAAC,IAAI;QACZ,OAAO;IAAU;IACrB;QAAE,KAAK,yNAAA,CAAA,OAAI,CAAC,IAAI;QACZ,gBAAgB;IAAY;IAChC;QAAE,KAAK,yNAAA,CAAA,OAAI,CAAC,OAAO;QACf,gBAAgB;QAChB,YAAY;IAAO;IACvB;QAAE,KAAK,yNAAA,CAAA,OAAI,CAAC,QAAQ;QAChB,WAAW;IAAS;IACxB;QAAE,KAAK,yNAAA,CAAA,OAAI,CAAC,MAAM;QACd,YAAY;IAAO;IACvB;QAAE,KAAK,yNAAA,CAAA,OAAI,CAAC,aAAa;QACrB,gBAAgB;IAAe;IACnC;QAAE,KAAK,yNAAA,CAAA,OAAI,CAAC,OAAO;QACf,OAAO;IAAO;IAClB;QAAE,KAAK;YAAC,yNAAA,CAAA,OAAI,CAAC,IAAI;YAAE,yNAAA,CAAA,OAAI,CAAC,IAAI;YAAE,yNAAA,CAAA,OAAI,CAAC,GAAG;YAAE,yNAAA,CAAA,OAAI,CAAC,gBAAgB;YAAE,yNAAA,CAAA,OAAI,CAAC,SAAS;SAAC;QAC1E,OAAO;IAAO;IAClB;QAAE,KAAK;YAAC,yNAAA,CAAA,OAAI,CAAC,OAAO;YAAE,yNAAA,CAAA,OAAI,CAAC,QAAQ;SAAC;QAChC,OAAO;IAAO;IAClB;QAAE,KAAK;YAAC,yNAAA,CAAA,OAAI,CAAC,MAAM;YAAE,yNAAA,CAAA,OAAI,CAAC,OAAO;SAAC;QAC9B,OAAO;IAAO;IAClB;QAAE,KAAK;YAAC,yNAAA,CAAA,OAAI,CAAC,MAAM;YAAE,yNAAA,CAAA,OAAI,CAAC,MAAM;YAAE,WAAW,GAAE,yNAAA,CAAA,OAAI,CAAC,OAAO,CAAC,yNAAA,CAAA,OAAI,CAAC,MAAM;SAAE;QACrE,OAAO;IAAO;IAClB;QAAE,KAAK,WAAW,GAAE,yNAAA,CAAA,OAAI,CAAC,UAAU,CAAC,yNAAA,CAAA,OAAI,CAAC,YAAY;QACjD,OAAO;IAAO;IAClB;QAAE,KAAK,WAAW,GAAE,yNAAA,CAAA,OAAI,CAAC,KAAK,CAAC,yNAAA,CAAA,OAAI,CAAC,YAAY;QAC5C,OAAO;IAAO;IAClB;QAAE,KAAK;YAAC,yNAAA,CAAA,OAAI,CAAC,QAAQ;YAAE,yNAAA,CAAA,OAAI,CAAC,SAAS;SAAC;QAClC,OAAO;IAAO;IAClB;QAAE,KAAK,yNAAA,CAAA,OAAI,CAAC,SAAS;QACjB,OAAO;IAAO;IAClB;QAAE,KAAK;YAAC,WAAW,GAAE,yNAAA,CAAA,OAAI,CAAC,OAAO,CAAC,yNAAA,CAAA,OAAI,CAAC,YAAY;YAAG,yNAAA,CAAA,OAAI,CAAC,SAAS;SAAC;QACjE,OAAO;IAAO;IAClB;QAAE,KAAK,WAAW,GAAE,yNAAA,CAAA,OAAI,CAAC,UAAU,CAAC,yNAAA,CAAA,OAAI,CAAC,YAAY;QACjD,OAAO;IAAO;IAClB;QAAE,KAAK,yNAAA,CAAA,OAAI,CAAC,OAAO;QACf,OAAO;IAAO;IAClB;QAAE,KAAK,yNAAA,CAAA,OAAI,CAAC,OAAO;QACf,OAAO;IAAO;CACrB;AAED,MAAM,YAAY,WAAW,GAAE,0NAAA,CAAA,aAAU,CAAC,SAAS,CAAC;IAChD,oCAAoC;QAAE,iBAAiB;IAAY;IACnE,uCAAuC;QAAE,iBAAiB;IAAY;AAC1E;AACA,MAAM,kBAAkB,OAAO,kBAAkB;AACjD,MAAM,wBAAwB,WAAW,GAAE,2NAAA,CAAA,QAAK,CAAC,MAAM,CAAC;IACpD,SAAQ,OAAO;QACX,OAAO,CAAA,GAAA,2NAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;YAC1B,aAAa;YACb,UAAU;YACV,iBAAiB;YACjB,aAAa;QACjB;IACJ;AACJ;AACA,MAAM,eAAe,WAAW,GAAE,0NAAA,CAAA,aAAU,CAAC,IAAI,CAAC;IAAE,OAAO;AAAqB,IAAI,kBAAkB,WAAW,GAAE,0NAAA,CAAA,aAAU,CAAC,IAAI,CAAC;IAAE,OAAO;AAAwB;AACpK,SAAS,mBAAmB,KAAK;IAC7B,IAAI,cAAc,EAAE;IACpB,IAAI,OAAO,MAAM,OAAO,GAAG,eAAe;IAC1C,YAAY,IAAI,CAAC,KAAK,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,MAAM,KAAK,CAAC,EAAE;IAC5D,IAAI,MAAM,GAAG,EACT,YAAY,IAAI,CAAC,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE;IAC5D,OAAO;AACX;AACA,MAAM,uBAAuB,WAAW,GAAE,2NAAA,CAAA,aAAU,CAAC,MAAM,CAAC;IACxD;QAAW,OAAO,0NAAA,CAAA,aAAU,CAAC,IAAI;IAAE;IACnC,QAAO,IAAI,EAAE,EAAE;QACX,IAAI,CAAC,GAAG,UAAU,IAAI,CAAC,GAAG,SAAS,EAC/B,OAAO;QACX,IAAI,cAAc,EAAE;QACpB,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;QAC5B,KAAK,IAAI,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAE;YACzC,IAAI,CAAC,MAAM,KAAK,EACZ;YACJ,IAAI,QAAQ,cAAc,GAAG,KAAK,EAAE,MAAM,IAAI,EAAE,CAAC,GAAG,WAC5C,MAAM,IAAI,GAAG,KAAK,cAAc,GAAG,KAAK,EAAE,MAAM,IAAI,GAAG,GAAG,GAAG,WAC7D,OAAO,WAAW,IAClB,CAAC,cAAc,GAAG,KAAK,EAAE,MAAM,IAAI,EAAE,GAAG,WACnC,MAAM,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,IAAI,cAAc,GAAG,KAAK,EAAE,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,OAAQ;YACrG,IAAI,OACA,cAAc,YAAY,MAAM,CAAC,OAAO,WAAW,CAAC,OAAO,GAAG,KAAK;QAC3E;QACA,OAAO,0NAAA,CAAA,aAAU,CAAC,GAAG,CAAC,aAAa;IACvC;IACA,SAAS,CAAA,IAAK,0NAAA,CAAA,aAAU,CAAC,WAAW,CAAC,IAAI,CAAC;AAC9C;AACA,MAAM,wBAAwB;IAC1B;IACA;CACH;AACD;;;;;AAKA,GACA,SAAS,gBAAgB,SAAS,CAAC,CAAC;IAChC,OAAO;QAAC,sBAAsB,EAAE,CAAC;QAAS;KAAsB;AACpE;AACA;;;;;;;AAOA,GACA,MAAM,wBAAwB,WAAW,GAAE,IAAI,mNAAA,CAAA,WAAQ;AACvD,SAAS,cAAc,IAAI,EAAE,GAAG,EAAE,QAAQ;IACtC,IAAI,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,mNAAA,CAAA,WAAQ,CAAC,QAAQ,GAAG,mNAAA,CAAA,WAAQ,CAAC,QAAQ;IACtE,IAAI,QACA,OAAO;IACX,IAAI,KAAK,IAAI,CAAC,MAAM,IAAI,GAAG;QACvB,IAAI,QAAQ,SAAS,OAAO,CAAC,KAAK,IAAI;QACtC,IAAI,QAAQ,CAAC,KAAK,QAAQ,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,GAC3C,OAAO;YAAC,QAAQ,CAAC,QAAQ,IAAI;SAAC;IACtC;IACA,OAAO;AACX;AACA,SAAS,WAAW,IAAI;IACpB,IAAI,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC;IAC/B,OAAO,YAAY,UAAU,KAAK,IAAI,IAAI;AAC9C;AACA;;;;;AAKA,GACA,SAAS,cAAc,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;IAC/C,IAAI,kBAAkB,OAAO,eAAe,IAAI,iBAAiB,WAAW,OAAO,QAAQ,IAAI;IAC/F,IAAI,OAAO,WAAW,QAAQ,OAAO,KAAK,YAAY,CAAC,KAAK;IAC5D,IAAK,IAAI,MAAM,MAAM,KAAK,MAAM,IAAI,MAAM,CAAE;QACxC,IAAI,UAAU,cAAc,IAAI,IAAI,EAAE,KAAK;QAC3C,IAAI,WAAW,IAAI,IAAI,GAAG,IAAI,EAAE,EAAE;YAC9B,IAAI,SAAS,WAAW;YACxB,IAAI,UAAU,CAAC,MAAM,IAAI,OAAO,OAAO,IAAI,IAAI,MAAM,OAAO,EAAE,GAAG,MAAM,OAAO,IAAI,IAAI,OAAO,OAAO,EAAE,GAClG,OAAO,oBAAoB,OAAO,KAAK,KAAK,KAAK,QAAQ,SAAS;QAC1E;IACJ;IACA,OAAO,mBAAmB,OAAO,KAAK,KAAK,MAAM,KAAK,IAAI,EAAE,iBAAiB;AACjF;AACA,SAAS,oBAAoB,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ;IAC7E,IAAI,SAAS,MAAM,MAAM,EAAE,aAAa;QAAE,MAAM,OAAO,IAAI;QAAE,IAAI,OAAO,EAAE;IAAC;IAC3E,IAAI,QAAQ,GAAG,SAAS,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,MAAM;IACrF,IAAI,UAAU,CAAC,MAAM,IAAI,OAAO,WAAW,CAAC,MAAM,IAAI,IAAI,OAAO,UAAU,CAAC,MAAM,EAAE,CAAC,GACjF,GAAG;QACC,IAAI,MAAM,IAAI,OAAO,EAAE,IAAI,MAAM,IAAI,GAAG,OAAO,IAAI,IAAI,MAAM,EAAE,EAAE;YAC7D,IAAI,SAAS,KAAK,SAAS,OAAO,CAAC,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,OAAO,IAAI,GAAG,OAAO,EAAE,EAAE;gBAClF,IAAI,YAAY,WAAW;gBAC3B,OAAO;oBAAE,OAAO;oBAAY,KAAK,YAAY;wBAAE,MAAM,UAAU,IAAI;wBAAE,IAAI,UAAU,EAAE;oBAAC,IAAI;oBAAW,SAAS;gBAAK;YACvH,OACK,IAAI,cAAc,OAAO,IAAI,EAAE,KAAK,WAAW;gBAChD;YACJ,OACK,IAAI,cAAc,OAAO,IAAI,EAAE,CAAC,KAAK,WAAW;gBACjD,IAAI,SAAS,GAAG;oBACZ,IAAI,YAAY,WAAW;oBAC3B,OAAO;wBACH,OAAO;wBACP,KAAK,aAAa,UAAU,IAAI,GAAG,UAAU,EAAE,GAAG;4BAAE,MAAM,UAAU,IAAI;4BAAE,IAAI,UAAU,EAAE;wBAAC,IAAI;wBAC/F,SAAS;oBACb;gBACJ;gBACA;YACJ;QACJ;IACJ,QAAS,MAAM,IAAI,OAAO,WAAW,KAAK,OAAO,WAAW,GAAI;IACpE,OAAO;QAAE,OAAO;QAAY,SAAS;IAAM;AAC/C;AACA,SAAS,mBAAmB,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,QAAQ;IACnF,IAAI,UAAU,MAAM,IAAI,MAAM,QAAQ,CAAC,MAAM,GAAG,OAAO,MAAM,QAAQ,CAAC,KAAK,MAAM;IACjF,IAAI,UAAU,SAAS,OAAO,CAAC;IAC/B,IAAI,UAAU,KAAK,AAAC,UAAU,KAAK,KAAO,MAAM,GAC5C,OAAO;IACX,IAAI,aAAa;QAAE,MAAM,MAAM,IAAI,MAAM,IAAI;QAAK,IAAI,MAAM,IAAI,MAAM,IAAI;IAAI;IAC9E,IAAI,OAAO,MAAM,GAAG,CAAC,SAAS,CAAC,KAAK,MAAM,IAAI,MAAM,GAAG,CAAC,MAAM,GAAG,IAAI,QAAQ;IAC7E,IAAK,IAAI,WAAW,GAAG,CAAC,AAAC,KAAK,IAAI,GAAI,IAAI,IAAI,YAAY,iBAAkB;QACxE,IAAI,OAAO,KAAK,KAAK;QACrB,IAAI,MAAM,GACN,YAAY,KAAK,MAAM;QAC3B,IAAI,UAAU,MAAM,WAAW;QAC/B,IAAK,IAAI,MAAM,MAAM,IAAI,IAAI,KAAK,MAAM,GAAG,GAAG,MAAM,MAAM,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG,OAAO,KAAK,OAAO,IAAK;YACpG,IAAI,QAAQ,SAAS,OAAO,CAAC,IAAI,CAAC,IAAI;YACtC,IAAI,QAAQ,KAAK,KAAK,YAAY,CAAC,UAAU,KAAK,GAAG,IAAI,IAAI,WACzD;YACJ,IAAI,AAAC,QAAQ,KAAK,KAAO,MAAM,GAAI;gBAC/B;YACJ,OACK,IAAI,SAAS,GAAG;gBACjB,OAAO;oBAAE,OAAO;oBAAY,KAAK;wBAAE,MAAM,UAAU;wBAAK,IAAI,UAAU,MAAM;oBAAE;oBAAG,SAAS,AAAC,SAAS,KAAO,WAAW;gBAAG;YAC7H,OACK;gBACD;YACJ;QACJ;QACA,IAAI,MAAM,GACN,YAAY,KAAK,MAAM;IAC/B;IACA,OAAO,KAAK,IAAI,GAAG;QAAE,OAAO;QAAY,SAAS;IAAM,IAAI;AAC/D;AAEA,kEAAkE;AAClE,mCAAmC;AACnC,SAAS,SAAS,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,aAAa,CAAC,EAAE,aAAa,CAAC;IAClE,IAAI,OAAO,MAAM;QACb,MAAM,OAAO,MAAM,CAAC;QACpB,IAAI,OAAO,CAAC,GACR,MAAM,OAAO,MAAM;IAC3B;IACA,IAAI,IAAI;IACR,IAAK,IAAI,IAAI,YAAY,IAAI,KAAK,IAAK;QACnC,IAAI,OAAO,UAAU,CAAC,MAAM,GACxB,KAAK,UAAW,IAAI;aAEpB;IACR;IACA,OAAO;AACX;AACA;;;AAGA,GACA,MAAM;IACF;;IAEA,GACA,YACA;;IAEA,GACA,MAAM,EAAE,OAAO,EACf;;IAEA,GACA,UAAU,EAAE,cAAc,CAAE;QACxB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,cAAc,GAAG;QACtB;;QAEA,GACA,IAAI,CAAC,GAAG,GAAG;QACX;;QAEA,GACA,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,eAAe,GAAG;IAC3B;IACA;;IAEA,GACA,MAAM;QAAE,OAAO,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM;IAAE;IAC/C;;IAEA,GACA,MAAM;QAAE,OAAO,IAAI,CAAC,GAAG,IAAI;IAAG;IAC9B;;;IAGA,GACA,OAAO;QAAE,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK;IAAW;IAC3D;;IAEA,GACA,OAAO;QACH,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAC7B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG;IAC1C;IACA;;;IAGA,GACA,IAAI,KAAK,EAAE;QACP,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG;QACpC,IAAI;QACJ,IAAI,OAAO,SAAS,UAChB,KAAK,MAAM;aAEX,KAAK,MAAM,CAAC,iBAAiB,SAAS,MAAM,IAAI,CAAC,MAAM,MAAM,GAAG;QACpE,IAAI,IAAI;YACJ,EAAE,IAAI,CAAC,GAAG;YACV,OAAO;QACX;IACJ;IACA;;;;IAIA,GACA,SAAS,KAAK,EAAE;QACZ,IAAI,QAAQ,IAAI,CAAC,GAAG;QACpB,MAAO,IAAI,CAAC,GAAG,CAAC,OAAQ,CAAE;QAC1B,OAAO,IAAI,CAAC,GAAG,GAAG;IACtB;IACA;;;IAGA,GACA,WAAW;QACP,IAAI,QAAQ,IAAI,CAAC,GAAG;QACpB,MAAO,aAAa,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAChD,EAAE,IAAI,CAAC,GAAG;QACd,OAAO,IAAI,CAAC,GAAG,GAAG;IACtB;IACA;;IAEA,GACA,YAAY;QAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;IAAE;IAC7C;;;IAGA,GACA,OAAO,EAAE,EAAE;QACP,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,GAAG;QAC5C,IAAI,QAAQ,CAAC,GAAG;YACZ,IAAI,CAAC,GAAG,GAAG;YACX,OAAO;QACX;IACJ;IACA;;IAEA,GACA,OAAO,CAAC,EAAE;QAAE,IAAI,CAAC,GAAG,IAAI;IAAG;IAC3B;;IAEA,GACA,SAAS;QACL,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,EAAE;YACjC,IAAI,CAAC,eAAe,GAAG,SAAS,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,eAAe;YAC/G,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK;QACnC;QACA,OAAO,IAAI,CAAC,eAAe;IAC/B;IACA;;IAEA,GACA,cAAc;QACV,IAAI;QACJ,OAAO,CAAC,KAAK,IAAI,CAAC,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,SAAS,IAAI,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,OAAO;IAC/G;IACA;;;;;;;;;;IAUA,GACA,MAAM,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE;QACrC,IAAI,OAAO,WAAW,UAAU;YAC5B,IAAI,QAAQ,CAAC,MAAQ,kBAAkB,IAAI,WAAW,KAAK;YAC3D,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,MAAM;YACxD,IAAI,MAAM,WAAW,MAAM,UAAU;gBACjC,IAAI,YAAY,OACZ,IAAI,CAAC,GAAG,IAAI,QAAQ,MAAM;gBAC9B,OAAO;YACX,OAEI,OAAO;QACf,OACK;YACD,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC;YAC9C,IAAI,SAAS,MAAM,KAAK,GAAG,GACvB,OAAO;YACX,IAAI,SAAS,YAAY,OACrB,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM;YAC/B,OAAO;QACX;IACJ;IACA;;IAEA,GACA,UAAU;QAAE,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG;IAAG;AAChE;AAEA,SAAS,WAAW,IAAI;IACpB,OAAO;QACH,MAAM,KAAK,IAAI,IAAI;QACnB,OAAO,KAAK,KAAK;QACjB,WAAW,KAAK,SAAS,IAAI,CAAC,KAAQ,CAAC;QACvC,YAAY,KAAK,UAAU,IAAI,CAAC,IAAM,IAAI;QAC1C,WAAW,KAAK,SAAS,IAAI;QAC7B,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAM,IAAI;QAClC,cAAc,KAAK,YAAY,IAAI,CAAC;QACpC,YAAY,KAAK,UAAU,IAAI;IACnC;AACJ;AACA,SAAS,iBAAiB,KAAK;IAC3B,IAAI,OAAO,SAAS,UAChB,OAAO;IACX,IAAI,WAAW,CAAC;IAChB,IAAK,IAAI,QAAQ,MAAO;QACpB,IAAI,MAAM,KAAK,CAAC,KAAK;QACrB,QAAQ,CAAC,KAAK,GAAI,eAAe,QAAQ,IAAI,KAAK,KAAK;IAC3D;IACA,OAAO;AACX;AACA,MAAM,eAAe,WAAW,GAAE,IAAI;AACtC;;;AAGA,GACA,MAAM,uBAAuB;IACzB,YAAY,MAAM,CAAE;QAChB,IAAI,OAAO,oBAAoB,OAAO,YAAY;QAClD,IAAI,IAAI,WAAW,SAAS;QAC5B,IAAI,OAAO,IAAI,cAAc,mNAAA,CAAA,SAAM;YAC/B,YAAY,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE;gBAClC,OAAO,IAAI,MAAM,MAAM,OAAO,WAAW;YAC7C;QACJ;QACA,KAAK,CAAC,MAAM,MAAM,EAAE,EAAE,OAAO,IAAI;QACjC,IAAI,CAAC,OAAO,GAAG,MAAM,MAAM,IAAI;QAC/B,OAAO,IAAI;QACX,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,UAAU,GAAG,IAAI,mNAAA,CAAA,WAAQ,CAAC;YAAE,SAAS;QAAK;QAC/C,IAAI,CAAC,UAAU,GAAG,OAAO,UAAU,GAAG,IAAI,WAAW,EAAE,UAAU,IAAI;IACzE;IACA;;IAEA,GACA,OAAO,OAAO,IAAI,EAAE;QAAE,OAAO,IAAI,eAAe;IAAO;IACvD;;IAEA,GACA,UAAU,EAAE,EAAE;QACV,IAAI,OAAO;QACX,IAAI,EAAE,mBAAmB,EAAE,GAAG,GAAG,OAAO;QACxC,IAAI,qBAAqB;YACrB,OAAO,aAAa,GAAG,CAAC,GAAG,KAAK;YAChC,IAAI,QAAQ,QAAQ,OAAO,GAAG,GAAG,GAAG,KAChC,OAAO;QACf;QACA,IAAI,QAAQ,UAAU,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,SAAS,QAAQ,SAAS,KAAK,IAAI,OAAO,GAAG,GAAG,GAAG,UAAU;QACnI,IAAI,OAAO;YACP,QAAQ,MAAM,KAAK;YACnB,WAAW,MAAM,GAAG,GAAG;QAC3B,OACK;YACD,QAAQ,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,IAAI;YAC5C,WAAW,GAAG,IAAI,CAAC,IAAI;QAC3B;QACA,IAAI,GAAG,GAAG,GAAG,WAAW,MAAM,uBAAuB,KACjD,OAAO;QACX,MAAO,WAAW,GAAG,GAAG,CAAE;YACtB,IAAI,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,EAAE,KAAK,EAAE;YACxE,IAAI,KAAK,MAAM,EAAE;gBACb,IAAI,cAAc,sBAAsB,oBAAoB,KAAK,IAAI,IAAI,CAAC;gBAC1E,IAAI,SAAS,IAAI,aAAa,KAAK,IAAI,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,GAAG,IAAI,EAAE,cAAc,IAAI,YAAY;gBAClG,MAAO,OAAO,GAAG,GAAG,MAAM,KAAK,IAAI,CAC/B,UAAU,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ;YACnD,OACK;gBACD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI;YAC9C;YACA,IAAI,OAAO,GAAG,GAAG,EACb;YACJ,WAAW,KAAK,EAAE,GAAG;QACzB;QACA,IAAI,OAAO,GAAG,MAAM,CAAC,GAAG,GAAG;QAC3B,IAAI,uBAAuB,QAAQ,MAC/B,aAAa,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,IAAI;QACxC,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,WAAW,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE,EAAE;IAC1E;IACA,IAAI,gBAAgB;QAAE,OAAO;IAAO;AACxC;AACA,SAAS,UAAU,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM;IAChD,IAAI,QAAQ,OAAO,YAAY,MAAM,KAAK,MAAM,IAAI,UAAU,KAAK,IAAI,CAAC,KAAK,UAAU;IACvF,IAAI,OACA,OAAO;QAAE,OAAO,KAAK,YAAY,CAAC,SAAS,CAAC;QAAQ,KAAK,MAAM,KAAK,MAAM;IAAC;IAC/E,IAAK,IAAI,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAChD,IAAI,QAAQ,KAAK,QAAQ,CAAC,EAAE,EAAE,MAAM,MAAM,KAAK,SAAS,CAAC,EAAE;QAC3D,IAAI,QAAQ,iBAAiB,mNAAA,CAAA,OAAI,IAAI,MAAM,UAAU,UAAU,MAAM,OAAO,KAAK,UAAU;QAC3F,IAAI,OACA,OAAO;IACf;IACA,OAAO;AACX;AACA,SAAS,QAAQ,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM;IACzC,IAAI,UAAU,QAAQ,KAAK,MAAM,KAAK,MAAM,EACxC,OAAO;IACX,IAAI,CAAC,UAAU,QAAQ,KAAK,KAAK,IAAI,IAAI,KAAK,OAAO,EACjD,SAAS;IACb,IAAK,IAAI,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAChD,IAAI,MAAM,KAAK,SAAS,CAAC,EAAE,EAAE,QAAQ,KAAK,QAAQ,CAAC,EAAE,EAAE;QACvD,IAAI,MAAM,MAAM,iBAAiB,mNAAA,CAAA,OAAI,EAAE;YACnC,IAAI,CAAC,CAAC,QAAQ,QAAQ,MAAM,OAAO,OAAO,KAAK,KAAK,KAAK,OAAO,GAC5D;YACJ,OAAO,CAAC,SAAS,QACX,IAAI,mNAAA,CAAA,OAAI,CAAC,KAAK,IAAI,EAAE,KAAK,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,QAAQ,KAAK,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,MAAM,MAAM,MAAM;QACzH;IACJ;IACA,OAAO;AACX;AACA,SAAS,qBAAqB,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW;IACxE,KAAK,IAAI,KAAK,UAAW;QACrB,IAAI,OAAO,EAAE,IAAI,GAAG,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,GAAG,KAAK,EAAE,EAAE,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,CAAC;QAC3E,IAAI,QAAQ,QAAQ,YAAY,KAAK,YAAY,UAAU,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,KAAK;QACtG,IAAI,SAAS,MAAM,GAAG,IAAI,UAAU,CAAC,OAAO,QAAQ,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,GAAG,GAAG,EAAE,MAAM,EAAE,MAAM,GAC/G,OAAO;YAAE,OAAO,MAAM,KAAK;YAAE;QAAK;IAC1C;IACA,OAAO;QAAE,OAAO,KAAK,YAAY,CAAC,UAAU,CAAC,cAAc,cAAc,eAAe;QAAI,MAAM,mNAAA,CAAA,OAAI,CAAC,KAAK;IAAC;AACjH;AACA,MAAM;IACF,YAAY,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAE;QACxC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE;QACtC,IAAI,UAAU,aAAa,GAAG,IAAI,OAAO,MAAM,CAAC,EAAE,CAAC,IAAI;QACvD,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,qBAAqB,MAAM,WAAW,MAAM,IAAI,CAAC,EAAE,EAAE,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK;QAC1I,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,OAAO,KAAK,MAAM;QACrD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,CAAC,MAAM,EAAE,IAAK;YAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,CAAC,EAAE;YACjC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE;QACxC;QACA,IAAI,WAAW,IAAI,CAAC,SAAS,GAAG,QAAQ,QAAQ,CAAC,IAAI,GAAG,OAAO,+BAA+B,OAC1F,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,QAAQ,QAAQ,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,QAAQ,QAAQ,CAAC,IAAI,GAAG;YACpF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,cAAc,QAAQ,KAAK;YAC1E,QAAQ,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,QAAQ,CAAC,IAAI;YAC7D,IAAI,CAAC,SAAS,GAAG,QAAQ,QAAQ,CAAC,IAAI;QAC1C;QACA,IAAI,CAAC,cAAc;IACvB;IACA,UAAU;QACN,IAAI,UAAU,aAAa,GAAG;QAC9B,IAAI,WAAW,IAAI,CAAC,SAAS,IAAI,OAAO,IAAI,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,SAAS;QAClF,IAAI,MAAM,KAAK,GAAG,CAAC,UAAU,IAAI,CAAC,UAAU,GAAG,KAAK,eAAe;QACnE,IAAI,SACA,MAAM,KAAK,GAAG,CAAC,KAAK,QAAQ,QAAQ,CAAC,EAAE;QAC3C,MAAO,IAAI,CAAC,SAAS,GAAG,IACpB,IAAI,CAAC,SAAS,CAAC;QACnB,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,EAChC,IAAI,CAAC,WAAW;QACpB,IAAI,IAAI,CAAC,SAAS,IAAI,UAClB,OAAO,IAAI,CAAC,MAAM;QACtB,IAAI,WAAW,IAAI,CAAC,SAAS,IAAI,QAAQ,QAAQ,CAAC,EAAE,EAAE;YAClD,QAAQ,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE;YACxC,OAAO,IAAI,CAAC,MAAM;QACtB;QACA,OAAO;IACX;IACA,OAAO,GAAG,EAAE;QACR,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,UAAU,GAAG,EAAE;QACX,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;YACxB,IAAI,MAAM,MAAM,OAAO,CAAC;YACxB,IAAI,MAAM,CAAC,GACP,QAAQ,MAAM,KAAK,CAAC,GAAG;QAC/B,OACK,IAAI,SAAS,MAAM;YACpB,QAAQ;QACZ;QACA,OAAO,MAAM,MAAM,MAAM,IAAI,IAAI,CAAC,EAAE,GAAG,QAAQ,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG;IAC5E;IACA,WAAW;QACP,IAAI,OAAO,IAAI,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,MAAM,OAAO,KAAK,MAAM;QAChF,IAAK,IAAI,QAAQ,IAAI,CAAC,UAAU,GAAI;YAChC,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YACpC,IAAI,YAAY,KACZ;YACJ,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,CAAC,MAAM,KAAK,MAAM;YAClD;YACA,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,EAC3B;YACJ,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI;YACxC,IAAI,QAAQ,IAAI,CAAC,SAAS,CAAC;YAC3B,QAAQ;YACR,MAAM,aAAa,MAAM,MAAM;QACnC;QACA,OAAO;YAAE;YAAM;QAAI;IACvB;IACA,WAAW,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE;QAC1B,OAAS;YACL,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,SAAS,MAAM;YAC1D,IAAI,OAAO,IAAI,MAAM,SAAS,OAAO,QACjC;YACJ,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI;YAC/C,UAAU,QAAQ;QACtB;QACA,OAAO;IACX;IACA,iBAAiB;QACb,MAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,CACnD,IAAI,CAAC,UAAU;IACvB;IACA,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE;QAC5B,IAAI,OAAO;QACX,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG;YACxB,SAAS,IAAI,CAAC,UAAU,CAAC,MAAM,QAAQ;YACvC,QAAQ;YACR,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;YAC5B,SAAS,IAAI,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC;YACtC,MAAM;YACN,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QAChC;QACA,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QAC/B,IAAI,QAAQ,KAAK,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,MAC5E,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG;aAEvB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,IAAI;QAClC,OAAO;IACX;IACA,UAAU,OAAO,EAAE;QACf,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,IAAI,SAAS,GAAG,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI;QAC7E,IAAI,SAAS,IAAI,aAAa,MAAM,UAAU,QAAQ,KAAK,CAAC,OAAO,GAAG,GAAG,UAAU,cAAc,QAAQ,KAAK,IAAI;QAClH,IAAI,OAAO,GAAG,IAAI;YACd,aAAa,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,UAAU;QACxD,OACK;YACD,MAAO,CAAC,OAAO,GAAG,GAAI;gBAClB,IAAI,QAAQ,UAAU,aAAa,KAAK,EAAE,QAAQ,IAAI,CAAC,KAAK;gBAC5D,IAAI,OACA,SAAS,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,SAAS,GAAG,OAAO,KAAK,EAAE,IAAI,CAAC,SAAS,GAAG,OAAO,GAAG,EAAE;gBAC7H,IAAI,OAAO,KAAK,GAAG,MAAM,mBAAmB,KACxC;YACR;QACJ;QACA,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,cAAc;QACnB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,EAAE,EACxB,IAAI,CAAC,SAAS;IACtB;IACA,cAAc;QACV,IAAI,OAAO,mNAAA,CAAA,OAAI,CAAC,KAAK,CAAC;YAClB,QAAQ,IAAI,CAAC,KAAK;YAClB,OAAO,IAAI,CAAC,UAAU;YACtB,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU;YACxC;YACA,OAAO;YACP,iBAAiB,KAAK,eAAe;YACrC,QAAQ,IAAI,CAAC,WAAW;QAC5B;QACA,OAAO,IAAI,mNAAA,CAAA,OAAI,CAAC,KAAK,IAAI,EAAE,KAAK,QAAQ,EAAE,KAAK,SAAS,EAAE,KAAK,MAAM,EAAE;YAAC;gBAAC,IAAI,CAAC,IAAI,CAAC,UAAU;gBAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK;aAAE;SAAC;QAC7I,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI;QACxD,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS;IACpC;IACA,SAAS;QACL,OAAO,IAAI,mNAAA,CAAA,OAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,OAAO;IAChH;AACJ;AACA,SAAS,UAAU,KAAK,EAAE,MAAM,EAAE,KAAK;IACnC,OAAO,KAAK,GAAG,OAAO,GAAG;IACzB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;QACzB,IAAI,SAAS,MAAM,QAAQ;QAC3B,IAAI,OAAO,GAAG,GAAG,OAAO,KAAK,EACzB,OAAO;IACf;IACA,MAAM,IAAI,MAAM;AACpB;AACA,MAAM,WAAW,WAAW,GAAE,OAAO,MAAM,CAAC;AAC5C,MAAM,YAAY;IAAC,mNAAA,CAAA,WAAQ,CAAC,IAAI;CAAC;AACjC,MAAM,UAAU,WAAW,GAAE,IAAI,mNAAA,CAAA,UAAO,CAAC;AACzC,MAAM,SAAS,EAAE;AACjB,uCAAuC;AACvC,MAAM,QAAQ,WAAW,GAAE,OAAO,MAAM,CAAC;AACzC,MAAM,eAAe,WAAW,GAAE,OAAO,MAAM,CAAC;AAChD,KAAK,IAAI,CAAC,YAAY,KAAK,IAAI;IAC3B;QAAC;QAAY;KAAe;IAC5B;QAAC;QAAc;KAAuB;IACtC;QAAC;QAAY;KAAiB;IAC9B;QAAC;QAAO;KAA0B;IAClC;QAAC;QAAO;KAAU;IAClB;QAAC;QAAa;KAAgB;IAC9B;QAAC;QAAQ;KAAW;IACpB;QAAC;QAAW;KAAwB;IACpC;QAAC;QAAa;KAAW;IACzB;QAAC;QAAS;KAAU;IACpB;QAAC;QAAU;KAAU;IACrB;QAAC;QAAY;KAAe;CAC/B,CACG,YAAY,CAAC,WAAW,GAAG,WAAW,GAAE,gBAAgB,UAAU;AACtE,MAAM;IACF,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK,GAAG,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO;IACpD;IACA,QAAQ,GAAG,EAAE;QACT,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,gBAAgB,IAAI,CAAC,KAAK,EAAE,IAAI;IAC5F;AACJ;AACA,MAAM,oBAAoB,WAAW,GAAE,IAAI,WAAW;AACtD,SAAS,YAAY,IAAI,EAAE,GAAG;IAC1B,IAAI,OAAO,OAAO,CAAC,QAAQ,CAAC,GACxB;IACJ,OAAO,IAAI,CAAC;IACZ,QAAQ,IAAI,CAAC;AACjB;AACA,SAAS,gBAAgB,KAAK,EAAE,MAAM;IAClC,IAAI,SAAS,EAAE;IACf,KAAK,IAAI,QAAQ,OAAO,KAAK,CAAC,KAAM;QAChC,IAAI,QAAQ,EAAE;QACd,KAAK,IAAI,QAAQ,KAAK,KAAK,CAAC,KAAM;YAC9B,IAAI,QAAS,KAAK,CAAC,KAAK,IAAI,yNAAA,CAAA,OAAI,CAAC,KAAK;YACtC,IAAI,CAAC,OAAO;gBACR,YAAY,MAAM,CAAC,yBAAyB,EAAE,MAAM;YACxD,OACK,IAAI,OAAO,SAAS,YAAY;gBACjC,IAAI,CAAC,MAAM,MAAM,EACb,YAAY,MAAM,CAAC,SAAS,EAAE,KAAK,qBAAqB,CAAC;qBAEzD,QAAQ,MAAM,GAAG,CAAC;YAC1B,OACK;gBACD,IAAI,MAAM,MAAM,EACZ,YAAY,MAAM,CAAC,IAAI,EAAE,KAAK,iBAAiB,CAAC;qBAEhD,QAAQ,MAAM,OAAO,CAAC,SAAS,QAAQ;oBAAC;iBAAM;YACtD;QACJ;QACA,KAAK,IAAI,OAAO,MACZ,OAAO,IAAI,CAAC;IACpB;IACA,IAAI,CAAC,OAAO,MAAM,EACd,OAAO;IACX,IAAI,OAAO,OAAO,OAAO,CAAC,MAAM,MAAM,MAAM,OAAO,MAAM,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IAC7E,IAAI,QAAQ,KAAK,CAAC,IAAI;IACtB,IAAI,OACA,OAAO,MAAM,EAAE;IACnB,IAAI,OAAO,KAAK,CAAC,IAAI,GAAG,mNAAA,CAAA,WAAQ,CAAC,MAAM,CAAC;QACpC,IAAI,UAAU,MAAM;QACpB;QACA,OAAO;YAAC,CAAA,GAAA,yNAAA,CAAA,YAAS,AAAD,EAAE;gBAAE,CAAC,KAAK,EAAE;YAAO;SAAG;IAC1C;IACA,UAAU,IAAI,CAAC;IACf,OAAO,KAAK,EAAE;AAClB;AACA,SAAS,MAAM,IAAI,EAAE,IAAI;IACrB,IAAI,OAAO,mNAAA,CAAA,WAAQ,CAAC,MAAM,CAAC;QAAE,IAAI,UAAU,MAAM;QAAE,MAAM;QAAY,OAAO;YACpE,iBAAiB,GAAG,CAAC,IAAM;YAC3B,eAAe,GAAG,CAAC,IAAM,CAAA,KAAM,KAAK,SAAS,CAAC;SACjD;QAAE,KAAK;IAAK;IACjB,UAAU,IAAI,CAAC;IACf,OAAO;AACX;AAEA,SAAS,aAAa,IAAI;IACtB,OAAO,KAAK,MAAM,IAAI,QAAQ,yDAAyD,IAAI,CAAC;AAChG;AACA,SAAS,WAAW,IAAI;IACpB,IAAK,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,EAAE,IAAI,GAAG,IAAI,EACpC,IAAI,aAAa,EAAE,KAAK,GACpB,OAAO;IACf,OAAO;AACX;AACA,SAAS,cAAc,MAAM;IACzB,IAAI,QAAQ;IACZ,OAAO,WAAW,CAAC,CAAC,IAAI,IAAI,IAAI,IAAI;QAChC,IAAI,CAAC,SAAS,WAAW,MACrB,QAAQ;IAChB;IACA,OAAO;AACX;AACA,MAAM,gBAAgB,WAAW,GAAE,2NAAA,CAAA,QAAK,CAAC,MAAM,CAAC;IAAE,SAAS,CAAA,SAAU,OAAO,IAAI,CAAC,CAAA,IAAK;AAAG;AACzF;;;;;AAKA,GACA,SAAS,aAAa,UAAU,CAAC,CAAC;IAC9B,IAAI,aAAa;QAAC;KAAa;IAC/B,IAAI,QAAQ,aAAa,EACrB,WAAW,IAAI,CAAC,cAAc,EAAE,CAAC;IACrC,OAAO;AACX;AACA,MAAM,eAAe,WAAW,GAAE,0NAAA,CAAA,aAAU,CAAC,SAAS,CAAC;IACnD,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,CAAC,KAAK,CAAC,kBAC3B,KAAK,aAAa,IAAI,0NAAA,CAAA,YAAS,CAAC,GAAG,IACnC,KAAK,KAAK,CAAC,KAAK,CAAC,0NAAA,CAAA,aAAU,CAAC,oBAAoB;QACpD,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,WAAW,KAAK,KAAK,CAAC,GAAG;QACvD,IAAI,CAAC,IAAI,GAAG,WAAW,KAAK,KAAK;QACjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,GAAG,UAAU,MAAM,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,IAAI,0NAAA,CAAA,aAAU,CAAC,IAAI;IAC7G;IACA,OAAO,MAAM,EAAE;QACX,IAAI,SAAS,OAAO,KAAK,CAAC,KAAK,CAAC,kBAC5B,OAAO,IAAI,CAAC,aAAa,IAAI,0NAAA,CAAA,YAAS,CAAC,GAAG,IAC1C,OAAO,KAAK,CAAC,KAAK,CAAC,0NAAA,CAAA,aAAU,CAAC,oBAAoB;QACtD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,IAAI,cAAc,OAAO,OAAO,GACvD,IAAI,CAAC,MAAM,GAAG;QAClB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EACvB;QACJ,IAAI,OAAO,WAAW,OAAO,KAAK;QAClC,IAAI,UAAU,IAAI,CAAC,MAAM,IAAI,QAAQ,IAAI,CAAC,IAAI,IAAI,OAAO,UAAU,IAAI,OAAO,eAAe,EAAE;YAC3F,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,WAAW,GAAG,UAAU,OAAO,IAAI,EAAE,MAAM;QACpD;IACJ;AACJ,GAAG;IACC,SAAS,CAAA;QACL,SAAS,OAAO,IAAI;YAChB,IAAI,IAAI;YACR,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,0NAAA,CAAA,aAAU,CAAC,IAAI;QACjJ;QACA,OAAO;YAAC,0NAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACnC,2NAAA,CAAA,OAAI,CAAC,MAAM,CAAC,0NAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC,EAAE,CAAC;SAAS;IAC9D;AACJ;AACA,SAAS,UAAU,IAAI,EAAE,IAAI,EAAE,MAAM;IACjC,IAAI,OAAO,IAAI,2NAAA,CAAA,kBAAe;IAC9B,IAAI,SAAS,KAAK,aAAa;IAC/B,IAAI,CAAC,QACD,SAAS,aAAa,QAAQ,KAAK,KAAK,CAAC,GAAG;IAChD,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,OAAQ;QAC7B,KAAK,OAAO,CAAC;YACT,OAAO,CAAA;gBACH,IAAI,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,mNAAA,CAAA,WAAQ,CAAC,OAAO;gBACzC,IAAI,KACA,KAAK,GAAG,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE,EAAE,KAAK,CAAC,IAAI;YAC/C;YACA;YAAM;QACV;IACJ;IACA,OAAO,KAAK,MAAM;AACtB;AACA,SAAS,aAAa,MAAM,EAAE,GAAG;IAC7B,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,GAAG,SAAS,EAAE,EAAE,OAAO;IACnD,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,OAAQ;QAC7B,IAAI,QAAQ,KAAK,EAAE,GAAG,MAAM;YACxB,OAAO,KAAK,EAAE;YACd,IAAI,QAAQ,IACR;QACR;QACA,IAAI,MAAM,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM;YAC/B,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM;YACvC,MAAM;QACV;QACA,OAAS;YACL,IAAI,QAAQ,KAAK,MAAM,MAAM,IAAI,KAAK,CAAC,MAAM;YAC7C,IAAI,CAAC,IAAI,SAAS,IAAI,aAAa,IAAI,KAAK,GAAG;gBAC3C,IAAI,QAAQ,KAAK,EAAE,GAAG,QAAQ,IAC1B,KAAK,EAAE,GAAG,KAAK,GAAG,CAAC,IAAI;qBAEvB,OAAO,IAAI,CAAC,OAAO;oBAAE,MAAM;oBAAO,IAAI,KAAK,GAAG,CAAC,IAAI;gBAAK;YAChE;YACA,IAAI,OAAO,IACP;YACJ,MAAM;YACN,IAAI,IAAI;QACZ;IACJ;IACA,OAAO;AACX;AACA,MAAM,QAAQ;IACV,KAAK,WAAW,GAAE,0NAAA,CAAA,aAAU,CAAC,IAAI,CAAC;QAAE,OAAO;QAAU,WAAW;QAAM,YAAY;YAAE,KAAK;QAAM;QAAG,aAAa,0NAAA,CAAA,YAAS,CAAC,GAAG;IAAC;IAC7H,KAAK,WAAW,GAAE,0NAAA,CAAA,aAAU,CAAC,IAAI,CAAC;QAAE,OAAO;QAAU,WAAW;QAAM,YAAY;YAAE,KAAK;QAAM;QAAG,aAAa,0NAAA,CAAA,YAAS,CAAC,GAAG;IAAC;IAC7H,MAAM,WAAW,GAAE,0NAAA,CAAA,aAAU,CAAC,IAAI,CAAC;QAAE,OAAO;QAAU,WAAW;QAAM,YAAY;YAAE,KAAK;QAAO;QAAG,aAAa;IAAK;AAC1H", "ignoreList": [0], "debugId": null}}]}