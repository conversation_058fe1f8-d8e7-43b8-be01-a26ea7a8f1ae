from huggingface_hub import snapshot_download
from huggingface_hub import hf_hub_download

download_type = input("다운로드 타입을 설정해주세요(repo or file):")

if download_type == 'repo':

    local_dir = input("local_dir를 입력하세요:")
    repo_id  = input("ropo_id를 입력하세요:")

    snapshot_download(repo_id = repo_id,
                      local_dir = local_dir)

else : 
    local_dir = input("local_dir를 입력하세요:")
    file_name = input("file_name을 입력하세요:")
    repo_id  = input("ropo_id를 입력하세요:")

    hf_hub_download(repo_id=repo_id, 
                    filename=file_name,
                    local_dir = local_dir)