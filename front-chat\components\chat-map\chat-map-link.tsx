'use client'

import * as React from 'react'
import {EarthIcon, MapIcon} from "lucide-react";
import Link from 'next/link'
import {usePathname} from "next/navigation";

export function ChatGeon2dMapLink() {

	const pathname = usePathname();
	const paths = pathname
		.split('/')
		.map(segment => decodeURIComponent(segment))
		.filter(segment => segment !== '')

	return (
		<Link
			href={'/geon-2d-map'}
			className={`flex h-9 w-9 items-center justify-center rounded-lg text-muted-foreground transition-colors hover:text-foreground md:h-8 md:w-8`}
			prefetch={false}
		>
			<EarthIcon className="transition-all" />
			<span className="sr-only">Toggle theme</span>
		</Link>
	)
}
