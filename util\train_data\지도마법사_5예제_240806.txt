<html>
    <head>
        <meta charset="utf-8">
        <title>wizard page</title>
        <link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
        <script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
    </head>
    <body>

        <div id="map" class="odf-view" style="height:550px;"></div>

        <script>

            // 지도 생성 start
            var mapContainer = document.getElementById('map');
            // 중심 좌표 정의
            var coord = new odf.Coordinate(199312.9996, 551784.6924);

            var mapOption = {
                center:coord,
                zoom:11,
                projection:'EPSG:5186',
                baroEMapURL: 'https://geon-gateway.geon.kr/map/api/map/baroemap',
                baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',
                basemap:{
                    baroEMap:['eMapBasic','eMapAIR']
                },
            };
            var map = new odf.Map(mapContainer, mapOption);
            // 지도 생성 end

            //배경지도(베이스맵) 컨트롤 생성 start
            var basemapControl = new odf.BasemapControl();
            //배경지도(베이스맵) 컨트롤 생성 end
            //지도에 생성한 배경지도 컨트롤 적용 start
            basemapControl.setMap(map);
            //지도에 생성한 배경지도 컨트롤 적용 end

            //줌(확대/축소) 컨트롤 생성 start
            var zoomControl = new odf.ZoomControl();
            //줌(확대/축소) 컨트롤 생성 end
            //지도에 생성한 줌(확대/축소) 컨트롤 적용 start
            zoomControl.setMap(map);
            //지도에 생성한 줌(확대/축소) 컨트롤 적용 end

            //인덱스 맵 컨트롤 생성 start
            var overviewMapControl = new odf.OverviewMapControl();
            //인덱스 맵 컨트롤 생성 end
            //지도에 생성한 인덱스 맵 컨트롤 적용 start
            overviewMapControl.setMap(map);
            //지도에 생성한 인덱스 맵 컨트롤 적용 end

            //축척 컨트롤 생성 start
            var scaleControl = new odf.ScaleControl();
            //축척 컨트롤 생성 end
            //지도에 생성한 축척 컨트롤 적용 start
            scaleControl.setMap(map);
            //지도에 생성한 축척 컨트롤 적용 end

            //이전/다음 화면 이동 컨트롤 생성 start
            var moveControl = new odf.MoveControl();
            //이전/다음 화면 이동 컨트롤 생성 end
            //지도에 생성한 이전/다음 화면 이동 컨트롤 적용 start
            moveControl.setMap(map);
            //지도에 생성한 이전/다음 화면 이동 컨트롤 적용 end
        </script>

    </body>
</html>
