'use client';

import { EditorView, lineNumbers as createLineNumbers } from '@codemirror/view';
import { EditorState, Transaction } from '@codemirror/state';
import { javascript } from '@codemirror/lang-javascript';
import { python } from '@codemirror/lang-python';
import { html } from '@codemirror/lang-html';
import { oneDark } from '@codemirror/theme-one-dark';
import { minimalSetup } from 'codemirror';
import React, { memo, useEffect, useRef } from 'react';
import { useTheme } from 'next-themes';

// 에디터 테마 스타일 확장
const modernThemeLight = EditorView.theme({
  '&': {
    backgroundColor: '#fafafa',
    height: '100%',
    fontSize: '14px',
    borderRadius: '6px',
  },
  '.cm-content': {
    fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace',
    padding: '1rem',
  },
  '.cm-line': {
    padding: '0 4px',
    lineHeight: '1.6',
  },
  '.cm-matchingBracket': {
    backgroundColor: '#e2e8f0',
    color: '#1e293b',
  },
  '.cm-activeLine': {
    backgroundColor: '#f8fafc',
  },
});

const modernThemeDark = EditorView.theme({
  '&': {
    backgroundColor: '#18181b',
    height: '100%',
    fontSize: '14px',
    borderRadius: '6px',
  },
  '.cm-content': {
    fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace',
    padding: '1rem',
  },
  '.cm-line': {
    padding: '0 4px',
    lineHeight: '1.6',
  },
  '.cm-matchingBracket': {
    backgroundColor: '#27272a',
    color: '#e4e4e7',
  },
  '.cm-activeLine': {
    backgroundColor: '#1f1f23',
  },
});

const customSetup = [
  minimalSetup,
  EditorView.lineWrapping,
  EditorState.allowMultipleSelections.of(true),
  EditorView.contentAttributes.of({ autocomplete: 'off' }),
];

type EditorProps = {
  content: string;
  status: 'streaming' | 'idle';
  language?: string;
  mode?: 'view' | 'edit';
  lineNumbers?: boolean;
  onChange?: (value: string) => void;
};

function PureCodeEditor({ 
  content, 
  status, 
  language = 'javascript',
  mode = 'view',
  lineNumbers = false,
  onChange,
}: EditorProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<EditorView | null>(null);
  const { resolvedTheme } = useTheme();

  const getLanguageExtension = (lang: EditorProps['language']) => {
    switch (lang) {
      case 'html':
        return html();
      case 'javascript':
      case 'typescript':
        return javascript();
      case 'python':
        return python();
        
      default:
        return javascript();
    }
  };

  useEffect(() => {
    if (containerRef.current && !editorRef.current) {
      const startState = EditorState.create({
        doc: content,
        extensions: [
          ...customSetup,
          getLanguageExtension(language),
          resolvedTheme === 'dark' ? [oneDark, modernThemeDark] : modernThemeLight,
          mode === 'view' ? EditorView.editable.of(false) : [],
          lineNumbers ? createLineNumbers() : [],
          EditorView.updateListener.of(update => {
            if (update.docChanged && onChange) {
              onChange(update.state.doc.toString());
            }
          }),
        ],
      });

      editorRef.current = new EditorView({
        state: startState,
        parent: containerRef.current,
      });
    }

    return () => {
      if (editorRef.current) {
        editorRef.current.destroy();
        editorRef.current = null;
      }
    };
  }, [language, resolvedTheme, mode, lineNumbers]);

  useEffect(() => {
    if (editorRef.current && content) {
      const currentContent = editorRef.current.state.doc.toString();

      if (status === 'streaming' || currentContent !== content) {
        const transaction = editorRef.current.state.update({
          changes: {
            from: 0,
            to: currentContent.length,
            insert: content,
          },
          annotations: [Transaction.remote.of(true)],
        });

        editorRef.current.dispatch(transaction);
      }
    }
  }, [content, status]);

  return (
    <div
      className="relative w-full overflow-hidden rounded-lg border border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900"
      ref={containerRef}
    />
  );
}

function areEqual(prevProps: EditorProps, nextProps: EditorProps) {
  if (prevProps.status === 'streaming' && nextProps.status === 'streaming')
    return false;
  if (prevProps.content !== nextProps.content) return false;
  if (prevProps.language !== nextProps.language) return false;
  if (prevProps.mode !== nextProps.mode) return false;
  if (prevProps.lineNumbers !== nextProps.lineNumbers) return false;

  return true;
}

export const CodeEditor = memo(PureCodeEditor, areEqual);