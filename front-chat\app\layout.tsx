import "./globals.css";
import { GeistSans } from "geist/font/sans";
import {Providers} from "@/app/providers";
import Script from "next/script";
import type { Metadata, Viewport } from "next";

export const metadata: Metadata = {
  title: {
    default: "말로 만드는 지도",
    template: "%s"
  },
  description: "AI 기반 지도 서비스 - 말로 만드는 지도와 지능형 어시스턴트",
  keywords: ["말로만드는지도", "지도", "AI", "어시스턴트", "GeOn", "GIS", "지리정보"],
  authors: [{ name: "GeOn Team" }],
  creator: "GeOn Team",
  publisher: "GeOn",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'),
  openGraph: {
    type: "website",
    locale: "ko_KR",
    siteName: "업무지원(챗봇)",
    title: "업무지원(챗봇)",
    description: "AI 기반 지도 서비스 - 말로 만드는 지도와 지능형 어시스턴트",
  },
  twitter: {
    card: "summary_large_image",
    title: "업무지원(챗봇)",
    description: "AI 기반 지도 서비스 - 말로 만드는 지도와 지능형 어시스턴트",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    // Google Search Console 등의 검증 코드가 있다면 여기에 추가
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    { media: '(prefers-color-scheme: dark)', color: 'black' },
  ],
};

export default function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (

    <html suppressHydrationWarning lang='ko'>
      <body className={`${GeistSans.className} overflow-hidden`}>
      {/* <body className={`${GeistSans.className}`}> */}
        <Providers>
          {children}
          <Script src={'/js/odf/odf.min.js'}/>
        </Providers>
      </body>
    </html>
  );
}
