'use client';

// components/code-block.tsx
import { usePreview } from '@/lib/hooks/use-preview';
import { Button } from './ui/button';
import { CopyIcon, FullscreenIcon } from 'lucide-react';
import { toast } from 'sonner';
import { CodeEditor } from './code-editor';
import { useCopyToClipboard } from 'usehooks-ts';
import { cn } from '@/lib/utils';
import { BetterTooltip } from './ui/tooltip';

interface CodeBlockProps {
  node: any;
  inline: boolean;
  className: string;
  children: any;
}

export function CodeBlock({
  node,
  inline,
  className,
  children,
  ...props
}: CodeBlockProps) {
  const match = /language-(\w+)/.exec(className || '');
  const [_, copyToClipboard] = useCopyToClipboard();
  const { setPreview } = usePreview();

  if (inline || !match) {
    return (
      <code className={cn("font-mono", "rounded-md", {
        "bg-zinc-100 dark:bg-zinc-800 py-0.5 px-1.5 text-sm": inline,
        "bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 text-sm py-1 px-2.5 mx-1": !match
      })} {...props}>
        {children}
      </code>
    );
  }

  const language = match[1].toLowerCase();
  const content = String(children);

  const handlePreviewClick = () => {
    setPreview(prev => ({
      ...prev,
      isVisible: true,
      content,
      kind: language,
      title: 'HTML Preview',
      status: 'idle'
    }));
  };

  return (
    <div className="overflow-hidden rounded-xl border border-zinc-200 dark:border-zinc-700">
      <div className="flex items-center justify-between border-b border-zinc-200 dark:border-zinc-700 bg-zinc-50 dark:bg-zinc-900 px-4 py-2">
        <span className="text-xs font-medium text-zinc-500">
          {language.toUpperCase()}
        </span>
        <div className="flex items-center space-x-2">
          {language === 'html' && (
            <BetterTooltip content="미리보기 (완성 후 클릭하세요)">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={handlePreviewClick}
              >
                <FullscreenIcon className="h-4 w-4" />
                <span className="sr-only">미리보기</span>
              </Button>
            </BetterTooltip>

          )}
          <BetterTooltip content="복사하기">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => {
                copyToClipboard(content);
                toast.success('클립보드에 복사되었습니다.');
              }}
            >
              <CopyIcon className="h-4 w-4" />
              <span className="sr-only">복사하기</span>
            </Button>
          </BetterTooltip>
        </div>
      </div>

      <div className="p-3 bg-background max-h-[350px] overflow-auto styled-scrollbar">
        <CodeEditor
          content={content}
          language={language}
          mode="view"
          status="streaming"
        />
      </div>
    </div>
  );
}