"use client";

import { SessionProvider } from "next-auth/react";
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { ThemeProviderProps } from "next-themes/dist/types";
import { ExpandableChatProvider } from "@/lib/hooks/use-expandable-chat";
import { SWRConfig } from "swr";

export function Providers({ children, ...props }: ThemeProviderProps) {
  return (
    <SessionProvider>
      <SWRConfig
        value={{
          onError: (error) => {
            // 401 에러는 fetcher에서 이미 처리되므로 여기서는 다른 에러만 처리
            if (error.status !== 401) {
              console.error('SWR Error:', error);
            }
          },
          revalidateOnFocus: false, // 포커스 시 자동 재검증 비활성화
          revalidateOnReconnect: true, // 네트워크 재연결 시 재검증
        }}
      >
        <Toaster position={"top-center"} />
        <NextThemesProvider {...props} attribute="class">
          <TooltipProvider>
            <ExpandableChatProvider>{children}</ExpandableChatProvider>
          </TooltipProvider>
        </NextThemesProvider>
      </SWRConfig>
    </SessionProvider>
  );
}
