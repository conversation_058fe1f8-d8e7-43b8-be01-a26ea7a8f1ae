CREATE TABLE IF NOT EXISTS "chat_map" (
	"id" text PRIMARY KEY NOT NULL,
	"chatId" text NOT NULL,
	"mapId" text NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "map" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"userId" text NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	"isPublic" boolean DEFAULT false,
	"layers" json NOT NULL,
	"version" integer DEFAULT 1 NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "map_access" (
	"id" text PRIMARY KEY NOT NULL,
	"mapId" text NOT NULL,
	"userId" text NOT NULL,
	"accessType" text NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "map_session" (
	"id" text PRIMARY KEY NOT NULL,
	"mapId" text NOT NULL,
	"userId" text NOT NULL,
	"isActive" boolean DEFAULT true,
	"lastActiveAt" timestamp DEFAULT now() NOT NULL,
	"syncView" boolean DEFAULT false,
	"followingUserId" text
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "map_view" (
	"id" text PRIMARY KEY NOT NULL,
	"mapId" text NOT NULL,
	"userId" text NOT NULL,
	"center" json NOT NULL,
	"zoom" integer NOT NULL,
	"basemap" text NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "chat_map" ADD CONSTRAINT "chat_map_chatId_chat_id_fk" FOREIGN KEY ("chatId") REFERENCES "public"."chat"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "chat_map" ADD CONSTRAINT "chat_map_mapId_map_id_fk" FOREIGN KEY ("mapId") REFERENCES "public"."map"("id") ON DELETE restrict ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "map_access" ADD CONSTRAINT "map_access_mapId_map_id_fk" FOREIGN KEY ("mapId") REFERENCES "public"."map"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "map_session" ADD CONSTRAINT "map_session_mapId_map_id_fk" FOREIGN KEY ("mapId") REFERENCES "public"."map"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "map_view" ADD CONSTRAINT "map_view_mapId_map_id_fk" FOREIGN KEY ("mapId") REFERENCES "public"."map"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
