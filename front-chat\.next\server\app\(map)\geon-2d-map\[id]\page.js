"use strict";(()=>{var e={};e.id=144,e.ids=[144],e.modules={1708:e=>{e.exports=require("node:process")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26480:(e,t,a)=>{a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var r=a(49994),o=a(18765),s=a(42117),n=a.n(s),i=a(91962),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);a.d(t,d);let l={children:["",{children:["(map)",{children:["geon-2d-map",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,66081)),"C:\\chatbot\\front-chat\\app\\(map)\\geon-2d-map\\[id]\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,72516)),"C:\\chatbot\\front-chat\\app\\(map)\\geon-2d-map\\layout.tsx"]}]},{forbidden:[()=>Promise.resolve().then(a.t.bind(a,81494,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,61135,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(a.bind(a,96592)),"C:\\chatbot\\front-chat\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,76532)),"C:\\chatbot\\front-chat\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,81494,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,61135,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["C:\\chatbot\\front-chat\\app\\(map)\\geon-2d-map\\[id]\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(map)/geon-2d-map/[id]/page",pathname:"/geon-2d-map/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},28538:(e,t,a)=>{a.d(t,{qQ:()=>O,yo:()=>ea,wA:()=>H,TJ:()=>S,Lz:()=>V,zU:()=>Z,V7:()=>Y,kA:()=>j,de:()=>G,mV:()=>W,yd:()=>K,Y0:()=>ee,yM:()=>B,$:()=>X,xZ:()=>et,Ci:()=>L});var r={};a.r(r),a.d(r,{accounts:()=>v,accountsRelations:()=>$,chat:()=>b,chatMap:()=>_,chatRelation:()=>E,map:()=>g,mapAccess:()=>Q,mapSession:()=>P,mapView:()=>x,mapsRelation:()=>k,message:()=>N,sessions:()=>q,sessionsRelations:()=>F,user:()=>w,userRelations:()=>U,verificationTokens:()=>I,vote:()=>A});var o=a(9652),s=a(33094),n=a(32019),i=a(57052),d=a(52363),l=a(4471),c=a(56850),u=a(94485),p=a(10302),m=a(7320),h=a(16077),f=a(56839),y=a(86746);let w=(0,n.cJ)("user",{id:(0,i.Qq)("id").primaryKey().$defaultFn(()=>(0,o.sX)()),name:(0,i.Qq)("name"),username:(0,i.Qq)("username"),gh_username:(0,i.Qq)("gh_username"),email:(0,i.Qq)("email").unique(),emailVerified:(0,d.vE)("emailVerified",{mode:"date"}),image:(0,i.Qq)("image"),createdAt:(0,d.vE)("createdAt",{mode:"date"}).defaultNow().notNull(),updatedAt:(0,d.vE)("updatedAt",{mode:"date"}).notNull().$onUpdate(()=>new Date)}),q=(0,n.cJ)("sessions",{sessionToken:(0,i.Qq)("sessionToken").primaryKey(),userId:(0,i.Qq)("userId").notNull().references(()=>w.id,{onDelete:"cascade",onUpdate:"cascade"}),expires:(0,d.vE)("expires",{mode:"date"}).notNull()},e=>({userIdIdx:(0,l.Pe)().on(e.userId)})),I=(0,n.cJ)("verificationTokens",{identifier:(0,i.Qq)("identifier").notNull(),token:(0,i.Qq)("token").notNull().unique(),expires:(0,d.vE)("expires",{mode:"date"}).notNull()},e=>({compositePk:(0,c.ie)({columns:[e.identifier,e.token]})})),v=(0,n.cJ)("accounts",{userId:(0,i.Qq)("userId").notNull().references(()=>w.id,{onDelete:"cascade",onUpdate:"cascade"}),type:(0,i.Qq)("type").notNull(),provider:(0,i.Qq)("provider").notNull(),providerAccountId:(0,i.Qq)("providerAccountId").notNull(),refresh_token:(0,i.Qq)("refresh_token"),refreshTokenExpiresIn:(0,u.nd)("refresh_token_expires_in"),access_token:(0,i.Qq)("access_token"),expires_at:(0,u.nd)("expires_at"),token_type:(0,i.Qq)("token_type"),scope:(0,i.Qq)("scope"),id_token:(0,i.Qq)("id_token"),session_state:(0,i.Qq)("session_state"),oauth_token_secret:(0,i.Qq)("oauth_token_secret"),oauth_token:(0,i.Qq)("oauth_token")},e=>({userIdIdx:(0,l.Pe)().on(e.userId),compositePk:(0,c.ie)({columns:[e.provider,e.providerAccountId]})})),b=(0,n.cJ)("Chat",{id:(0,i.Qq)("id").primaryKey(),createdAt:(0,d.vE)("createdAt").notNull(),title:(0,i.Qq)("title").notNull().default("New Chat"),userId:(0,i.Qq)("userId").notNull(),visibility:(0,p.yf)("visibility",{enum:["public","private"]}).notNull().default("private")}),N=(0,n.cJ)("Message",{id:(0,m.uR)("id").primaryKey().notNull().defaultRandom(),chatId:(0,i.Qq)("chatId").notNull().references(()=>b.id),role:(0,p.yf)("role").notNull(),content:(0,h.Pq)("content"),parts:(0,h.Pq)("parts"),attachments:(0,h.Pq)("attachments"),createdAt:(0,d.vE)("createdAt").notNull()}),A=(0,n.cJ)("Vote",{chatId:(0,i.Qq)("chatId").notNull().references(()=>b.id),messageId:(0,m.uR)("messageId").notNull().references(()=>N.id),isUpvoted:(0,f.zM)("isUpvoted").notNull()},e=>({pk:(0,c.ie)({columns:[e.chatId,e.messageId]})})),g=(0,n.cJ)("map",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,y.$C)()),name:(0,i.Qq)("name").notNull(),createdBy:(0,i.Qq)("userId").notNull(),createdAt:(0,d.vE)("createdAt").notNull().defaultNow(),updatedAt:(0,d.vE)("updatedAt").notNull().defaultNow(),isPublic:(0,f.zM)("isPublic").default(!1),layers:(0,h.Pq)("layers").notNull(),version:(0,u.nd)("version").notNull().default(1)}),x=(0,n.cJ)("map_view",{id:(0,i.Qq)("id").notNull().$defaultFn(()=>(0,y.$C)()),mapId:(0,i.Qq)("mapId").notNull().references(()=>g.id,{onDelete:"cascade"}),userId:(0,i.Qq)("userId").notNull(),center:(0,h.Pq)("center").notNull(),zoom:(0,u.nd)("zoom").notNull(),basemap:(0,i.Qq)("basemap").notNull(),updatedAt:(0,d.vE)("updatedAt").notNull().defaultNow()},e=>({compoundKey:(0,c.ie)({columns:[e.mapId,e.userId]}),idIdx:(0,l.Pe)("map_view_id_idx").on(e.id)})),_=(0,n.cJ)("chat_map",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,y.$C)()),chatId:(0,i.Qq)("chatId").notNull().references(()=>b.id,{onDelete:"cascade"}),mapId:(0,i.Qq)("mapId").notNull().references(()=>g.id,{onDelete:"restrict"}),createdAt:(0,d.vE)("createdAt").notNull().defaultNow()}),Q=(0,n.cJ)("map_access",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,y.$C)()),mapId:(0,i.Qq)("mapId").notNull().references(()=>g.id,{onDelete:"cascade"}),userId:(0,i.Qq)("userId").notNull(),accessType:(0,i.Qq)("accessType").notNull(),createdAt:(0,d.vE)("createdAt").notNull().defaultNow()}),P=(0,n.cJ)("map_session",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,y.$C)()),mapId:(0,i.Qq)("mapId").notNull().references(()=>g.id,{onDelete:"cascade"}),userId:(0,i.Qq)("userId").notNull(),isActive:(0,f.zM)("isActive").default(!0),lastActiveAt:(0,d.vE)("lastActiveAt").notNull().defaultNow(),syncView:(0,f.zM)("syncView").default(!1),followingUserId:(0,i.Qq)("followingUserId")}),k=(0,s.K1)(g,({many:e})=>({views:e(x),access:e(Q),sessions:e(P),chats:e(_)})),E=(0,s.K1)(b,({many:e})=>({maps:e(_)})),F=(0,s.K1)(q,({one:e})=>({user:e(w,{references:[w.id],fields:[q.userId]})})),$=(0,s.K1)(v,({one:e})=>({user:e(w,{references:[w.id],fields:[v.userId]})})),U=(0,s.K1)(w,({many:e})=>({accounts:e(v),sessions:e(q)}));var C=a(60988),T=a(1770),R=a(7160),D=a(42449),J=a(8738);let M=new D.Pool({connectionString:process.env.POSTGRES_URL}),z=(0,J.f)(M,{schema:r,logger:!0});async function K({id:e,userId:t,title:a}){try{return await z.insert(b).values({id:e,createdAt:new Date,userId:t,title:a})}catch(e){throw console.error("Failed to save chat in database"),e}}async function B({messages:e}){try{return await z.insert(N).values(e)}catch(e){throw console.error("Failed to save messages in database",e),e}}async function O({id:e}){try{return await z.delete(A).where((0,C.eq)(A.chatId,e)),await z.delete(N).where((0,C.eq)(N.chatId,e)),await z.delete(b).where((0,C.eq)(b.id,e))}catch(e){throw console.error("Failed to delete chat by id from database"),e}}async function V({id:e}){try{return await z.select().from(b).where((0,C.eq)(b.userId,e)).orderBy((0,T.i)(b.createdAt))}catch(e){throw console.error("Failed to get chats by user from database"),e}}async function S({id:e}){try{let[t]=await z.select().from(b).where((0,C.eq)(b.id,e));return t}catch(e){throw console.error("Failed to get chat by id from database"),e}}async function j({id:e}){try{return await z.select().from(N).where((0,C.eq)(N.id,e))}catch(e){throw console.error("Failed to get message by id from database"),e}}async function G({id:e}){try{return await z.select().from(N).where((0,C.eq)(N.chatId,e)).orderBy((0,T.Y)(N.createdAt))}catch(e){throw console.error("Failed to get messages by chat id from database",e),e}}async function L({chatId:e,messageId:t,type:a}){try{let[r]=await z.select().from(A).where((0,C.Uo)((0,C.eq)(A.messageId,t)));if(r)return await z.update(A).set({isUpvoted:"up"===a}).where((0,C.Uo)((0,C.eq)(A.messageId,t),(0,C.eq)(A.chatId,e)));return await z.insert(A).values({chatId:e,messageId:t,isUpvoted:"up"===a})}catch(e){throw console.error("Failed to upvote message in database",e),e}}async function W({id:e}){try{return await z.select().from(A).where((0,C.eq)(A.chatId,e))}catch(e){throw console.error("Failed to get votes by chat id from database",e),e}}async function H({chatId:e,timestamp:t}){try{return await z.delete(N).where((0,C.Uo)((0,C.eq)(N.chatId,e),(0,C.RO)(N.createdAt,t)))}catch(e){throw console.error("Failed to delete messages by id after timestamp from database"),e}}async function X({chatId:e,visibility:t}){try{return await z.update(b).set({visibility:t}).where((0,C.eq)(b.id,e))}catch(e){throw console.error("Failed to update chat visibility in database"),e}}async function Y({userId:e}){try{return await z.select({id:g.id,name:g.name,createdAt:g.createdAt,updatedAt:g.updatedAt,layers:g.layers,isPublic:g.isPublic,activeUsers:(0,R.ll)`
        (
          SELECT COUNT(DISTINCT ${x.userId})
          FROM ${x}
          WHERE ${x.mapId} = ${g.id}
          AND ${x.updatedAt} > NOW() - INTERVAL '5 minutes'
        )
        `.as("activeUsers"),view:{center:x.center,zoom:x.zoom,basemap:x.basemap}}).from(g).leftJoin(Q,(0,C.Uo)((0,C.eq)(Q.mapId,g.id),(0,C.eq)(Q.userId,e))).leftJoin(x,(0,C.Uo)((0,C.eq)(x.mapId,g.id),(0,C.eq)(x.userId,e))).where((0,C.or)((0,C.eq)(g.createdBy,e),(0,C.eq)(Q.userId,e))).orderBy((0,T.i)(g.updatedAt))}catch(e){throw e instanceof Error&&console.error("Failed to get maps by user from database:",{message:e.message,stack:e.stack}),e}}async function Z({id:e,userId:t}){try{let[a]=await z.select({id:g.id,name:g.name,createdAt:g.createdAt,updatedAt:g.updatedAt,layers:g.layers,isPublic:g.isPublic,createdBy:g.createdBy,activeUsers:(0,R.ll)`
        (
          SELECT COUNT(DISTINCT ${x.userId})
          FROM ${x}
          WHERE ${x.mapId} = ${g.id}
          AND ${x.updatedAt} > NOW() - INTERVAL '5 minutes'
        )
        `.as("activeUsers")}).from(g).where((0,C.eq)(g.id,e));if(!a)throw Error("Map not found");if(Array.isArray(a.layers)&&(a.layers=a.layers.map(e=>({...e,style:e.style?"string"==typeof e.style?JSON.parse(e.style):e.style:void 0}))),a.createdBy!==t&&!a.isPublic&&!await er({mapId:e,userId:t}))throw Error("Forbidden");let[r]=await z.select().from(x).where((0,C.Uo)((0,C.eq)(x.mapId,e),(0,C.eq)(x.userId,t)));return{...a,view:r||null}}catch(e){throw console.error("Failed to get map by id from database"),e}}async function ee({id:e,name:t,layers:a,userId:r}){try{if((await z.select().from(g).where((0,C.eq)(g.id,e))).length>0)return await z.update(g).set({name:t,layers:a,updatedAt:new Date}).where((0,C.eq)(g.id,e));return await z.insert(g).values({id:e,name:t,layers:a,createdBy:r,createdAt:new Date,updatedAt:new Date})}catch(e){throw console.error("Failed to save map in database"),e}}async function et({mapId:e,userId:t,view:a}){try{return await z.insert(x).values({mapId:e,userId:t,center:a.center??{lat:36.5,lng:127.5},zoom:a.zoom??7,basemap:a.basemap??"eMapBasic",updatedAt:new Date}).onConflictDoUpdate({target:[x.mapId,x.userId],set:{...a,updatedAt:new Date}})}catch(e){throw console.error("Failed to update map view state"),e}}async function ea({id:e,userId:t}){try{let[a]=await z.select().from(g).where((0,C.eq)(g.id,e));if(!a)throw Error("Map not found");if(a.createdBy!==t)throw Error("Forbidden");return await z.delete(g).where((0,C.eq)(g.id,e))}catch(e){throw console.error("Failed to delete map from database"),e}}async function er({mapId:e,userId:t}){try{return(await z.select().from(Q).where((0,C.Uo)((0,C.eq)(Q.mapId,e),(0,C.eq)(Q.userId,t)))).length>0}catch(e){throw console.error("Failed to check map access"),e}}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},42449:e=>{e.exports=require("pg")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66081:(e,t,a)=>{a.r(t),a.d(t,{default:()=>p,generateMetadata:()=>u});var r=a(33626),o=a(90561),s=a(47841),n=a(34934),i=a(28538),d=a(85457),l=a(45346),c=a(75077);async function u(e){let{id:t}=await e.params;try{let e=await (0,i.TJ)({id:t});if(!e)return{title:"채팅을 찾을 수 없음",description:"요청하신 채팅을 찾을 수 없습니다."};let a=e.title||"말로 만드는 지도";return{title:a,description:`${a} - AI와 대화하며 지도를 탐색하고 분석하세요.`,openGraph:{title:`${a} | 업무지원(챗봇)`,description:`${a} - AI와 대화하며 지도를 탐색하고 분석하세요.`,type:"website"},twitter:{card:"summary_large_image",title:`${a} | 업무지원(챗봇)`,description:`${a} - AI와 대화하며 지도를 탐색하고 분석하세요.`}}}catch(e){return{title:"말로 만드는 지도",description:"AI와 대화하며 지도를 탐색하고 분석하세요."}}}async function p(e){let{id:t}=await e.params,a=await (0,i.TJ)({id:t});a||(0,o.notFound)();let u=await (0,s.j2)();if("private"===a.visibility&&(!u||!u.user||u.user.id!==a.userId))return(0,o.notFound)();let p=await (0,i.de)({id:t}),m=await (0,l.UL)(),h=m.get("model-id")?.value,f=c.Jn.find(e=>e.id===h)?.id||c.oQ;return(0,r.jsx)(n.ChatMap,{id:a.id,initialMessages:(0,d.b2)(p),selectedModelId:f,selectedVisibilityType:a.visibility,isReadOnly:u?.user?.id!==a.userId},t)}},73136:e=>{e.exports=require("node:url")},76760:e=>{e.exports=require("node:path")},77598:e=>{e.exports=require("node:crypto")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[332,481,741,115,746,861,180,228,309,572,753,391,152,352],()=>a(26480));module.exports=r})();