{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/ai/custom-middleware.ts"], "sourcesContent": ["import { LanguageModelV1StreamPart, LanguageModelV1Middleware } from \"ai\";\r\n\r\nexport const customMiddleware: LanguageModelV1Middleware = {\r\n  wrapStream: async ({ doStream }) => {\r\n\tconst { stream, ...rest } = await doStream();\r\n\r\n    const transformStream = new TransformStream<\r\n      LanguageModelV1StreamPart,\r\n      LanguageModelV1StreamPart\r\n    >({\r\n      transform(chunk, controller) {\r\n        controller.enqueue(chunk);\r\n      },\r\n\r\n      flush() {\r\n      },\r\n    });\r\n\r\n    return {\r\n      stream: stream.pipeThrough(transformStream),\r\n      ...rest,\r\n    };\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAEO,MAAM,mBAA8C;IACzD,YAAY,OAAO,EAAE,QAAQ,EAAE;QAChC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG,MAAM;QAE/B,MAAM,kBAAkB,IAAI,gBAG1B;YACA,WAAU,KAAK,EAAE,UAAU;gBACzB,WAAW,OAAO,CAAC;YACrB;YAEA,UACA;QACF;QAEA,OAAO;YACL,QAAQ,OAAO,WAAW,CAAC;YAC3B,GAAG,IAAI;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/ai/dev-models.ts"], "sourcesContent": ["// Define your models here.\r\n\r\nexport interface Model {\r\n  id: string;\r\n  label: string;\r\n  apiIdentifier: string; // Dify Application ID\r\n  description: string;\r\n  apiKey?: string;\r\n}\r\n\r\nexport const models: Array<Model> = [\r\n  {\r\n    id: '지자체 공간정보 플랫폼 챗봇',\r\n    label: '지자체 공간정보 플랫폼 챗봇',\r\n    apiIdentifier: 'EIjFYMz0dmL2HxkQJuBifqvF', // Dify Application ID (app- 제거)\r\n    description: '지자체 공간정보 플랫폼 챗봇',\r\n    apiKey: 'app-Hd682MZtRJh95QtTUe5H9aCl', // Dify Assistant API 키\r\n  },\r\n  {\r\n    id: '지도개발 어시스턴트',\r\n    label: '지도개발 어시스턴트',\r\n    apiIdentifier: 'EIjFYMz0dmL2HxkQJuBifqvF', // Dify Application ID (app- 제거)\r\n    description: '지도개발을 위한 문서를 학습한 어시스턴트',\r\n    apiKey: 'app-EIjFYMz0dmL2HxkQJuBifqvF', // Dify Assistant API 키\r\n  },\r\n] as const;\r\n\r\nexport const DEFAULT_MODEL_NAME: string = '지자체 공간정보 플랫폼 챗봇';\r\n\r\n// API 키를 모델 ID로 매핑하는 함수\r\nexport function getApiKeyByModelId(modelId: string): string | undefined {\r\n  const model = models.find(m => m.id === modelId);\r\n  return model?.apiKey;\r\n}\r\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;AAUpB,MAAM,SAAuB;IAClC;QACE,IAAI;QACJ,OAAO;QACP,eAAe;QACf,aAAa;QACb,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,eAAe;QACf,aAAa;QACb,QAAQ;IACV;CACD;AAEM,MAAM,qBAA6B;AAGnC,SAAS,mBAAmB,OAAe;IAChD,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACxC,OAAO,OAAO;AAChB", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/ai/index.ts"], "sourcesContent": ["import { wrapLanguageModel } from \"ai\";\r\nimport { customMiddleware } from \"./custom-middleware\";\r\nimport { models } from \"./dev-models\";\r\nimport { createGeon } from \"@ai-sdk/geon\";\r\nimport { openai } from \"@ai-sdk/openai\";\r\nimport { createDifyProvider } from \"dify-ai-provider\";\r\n\r\n// 기본 Geon 인스턴스 (Qwen3 모델용)\r\nexport const geon = createGeon({\r\n  baseURL: \"http://121.163.19.104:8005/v1\",\r\n  apiKey: \"123\"\r\n});\r\n\r\n// Qwen2.5 14B 모델용 Geon 인스턴스\r\nexport const geonQwen25 = createGeon({\r\n  baseURL: \"http://121.163.19.104:8002/v1\",\r\n  apiKey: \"123\"\r\n});\r\n\r\n// A.X-4 Light AWQ 모델용 Geon 인스턴스\r\nexport const geonAX4 = createGeon({\r\n  baseURL: \"http://121.163.19.104:8007/v1\",\r\n  apiKey: \"123\"\r\n});\r\n\r\n// Dify provider 인스턴스 생성\r\nexport const difyProvider = createDifyProvider({\r\n  baseURL: \"https://ai-dify.geon.kr/v1\",\r\n});\r\n\r\nexport const customModel = (modelId: string) => {\r\n  // 모델 ID에 따라 적절한 Geon 인스턴스 선택\r\n  let geonInstance = geon; // 기본값\r\n\r\n  if (modelId.includes('Qwen2.5-14B')) {\r\n    geonInstance = geonQwen25;\r\n  } else if (modelId.includes('A.X-4-Light-awq')) {\r\n    geonInstance = geonAX4;\r\n  }\r\n\r\n  return wrapLanguageModel({\r\n    model: geonInstance(modelId),\r\n    middleware: customMiddleware,\r\n  });\r\n};\r\n\r\nexport const difyModel = (modelId: string) => {\r\n  const model = models.find(m => m.id === modelId);\r\n  if (!model) {\r\n    throw new Error(`Model not found: ${modelId}`);\r\n  }\r\n\r\n  const difyModelInstance = difyProvider(model.apiIdentifier, {\r\n    apiKey: model.apiKey,\r\n    responseMode: \"streaming\",\r\n  });\r\n\r\n  return wrapLanguageModel({\r\n    model: difyModelInstance,\r\n    middleware: customMiddleware,\r\n  });\r\n};\r\n\r\nexport const openaiModel = (modelId: string) => {\r\n  return wrapLanguageModel({\r\n    model: openai(modelId),\r\n    middleware: customMiddleware,\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAGO,MAAM,OAAO,CAAA,GAAA,iQAAA,CAAA,aAAU,AAAD,EAAE;IAC7B,SAAS;IACT,QAAQ;AACV;AAGO,MAAM,aAAa,CAAA,GAAA,iQAAA,CAAA,aAAU,AAAD,EAAE;IACnC,SAAS;IACT,QAAQ;AACV;AAGO,MAAM,UAAU,CAAA,GAAA,iQAAA,CAAA,aAAU,AAAD,EAAE;IAChC,SAAS;IACT,QAAQ;AACV;AAGO,MAAM,eAAe,CAAA,GAAA,6NAAA,CAAA,qBAAkB,AAAD,EAAE;IAC7C,SAAS;AACX;AAEO,MAAM,cAAc,CAAC;IAC1B,6BAA6B;IAC7B,IAAI,eAAe,MAAM,MAAM;IAE/B,IAAI,QAAQ,QAAQ,CAAC,gBAAgB;QACnC,eAAe;IACjB,OAAO,IAAI,QAAQ,QAAQ,CAAC,oBAAoB;QAC9C,eAAe;IACjB;IAEA,OAAO,CAAA,GAAA,gPAAA,CAAA,oBAAiB,AAAD,EAAE;QACvB,OAAO,aAAa;QACpB,YAAY,mIAAA,CAAA,mBAAgB;IAC9B;AACF;AAEO,MAAM,YAAY,CAAC;IACxB,MAAM,QAAQ,4HAAA,CAAA,SAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACxC,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,SAAS;IAC/C;IAEA,MAAM,oBAAoB,aAAa,MAAM,aAAa,EAAE;QAC1D,QAAQ,MAAM,MAAM;QACpB,cAAc;IAChB;IAEA,OAAO,CAAA,GAAA,gPAAA,CAAA,oBAAiB,AAAD,EAAE;QACvB,OAAO;QACP,YAAY,mIAAA,CAAA,mBAAgB;IAC9B;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO,CAAA,GAAA,gPAAA,CAAA,oBAAiB,AAAD,EAAE;QACvB,OAAO,CAAA,GAAA,gPAAA,CAAA,SAAM,AAAD,EAAE;QACd,YAAY,mIAAA,CAAA,mBAAgB;IAC9B;AACF", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28auth%29/auth.config.ts"], "sourcesContent": ["import {NextAuthConfig, Session, User} from \"next-auth\";\r\n\r\nexport const authConfig = {\r\n\tpages: {\r\n\t\tsignIn: \"/login\",\r\n\t\t// verifyRequest: `/login`,\r\n\t\t// error: \"/login\", // Error code passed in query string as ?error=\r\n\t\tnewUser: \"/\",\r\n\t},\r\n\tproviders: [\r\n\t\t// added later in auth.ts since it requires bcrypt which is only compatible with Node.js\r\n\t\t// while this file is also used in non-Node.js environments\r\n\t],\r\n\tcallbacks: {\r\n\t\tauthorized({ auth, request: { nextUrl } }) {\r\n\t\t\tconst isLoggedIn = !!auth?.user;\r\n\t\t\tconst isOnChat = nextUrl.pathname.startsWith(\"/geon-2d-map\");\r\n\t\t\tconst isOnLogin = nextUrl.pathname.startsWith(\"/login\");\r\n\t\t\tconst isOnRoot = nextUrl.pathname === \"/\";\r\n\t  \r\n\t\t\t// 루트 경로로 접근시 /geon-2d-map으로 리다이렉트\r\n\t\t\tif (isOnRoot) {\r\n\t\t\t  return Response.redirect(new URL(\"/geon-2d-map\", nextUrl));\r\n\t\t\t}\r\n\t  \r\n\t\t\t// 로그인된 상태에서 로그인/회원가입 페이지 접근시 /geon-2d-map으로 리다이렉트\r\n\t\t\tif (isLoggedIn && (isOnLogin)) {\r\n\t\t\t  return Response.redirect(new URL(\"/geon-2d-map\", nextUrl));\r\n\t\t\t}\r\n\t  \r\n\t\t\t// 로그인/회원가입 페이지는 항상 접근 가능\r\n\t\t\tif (isOnLogin) {\r\n\t\t\t  return true;\r\n\t\t\t}\r\n\t  \r\n\t\t\t// /geon-2d-map 페이지는 로그인한 사용자만 접근 가능\r\n\t\t\tif (isOnChat) {\r\n\t\t\t  return isLoggedIn;\r\n\t\t\t}\r\n\t  \r\n\t\t\treturn true;\r\n\t\t  },\r\n\t},\r\n} satisfies NextAuthConfig;\r\n"], "names": [], "mappings": ";;;AAEO,MAAM,aAAa;IACzB,OAAO;QACN,QAAQ;QACR,2BAA2B;QAC3B,mEAAmE;QACnE,SAAS;IACV;IACA,WAAW,EAGV;IACD,WAAW;QACV,YAAW,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;YACxC,MAAM,aAAa,CAAC,CAAC,MAAM;YAC3B,MAAM,WAAW,QAAQ,QAAQ,CAAC,UAAU,CAAC;YAC7C,MAAM,YAAY,QAAQ,QAAQ,CAAC,UAAU,CAAC;YAC9C,MAAM,WAAW,QAAQ,QAAQ,KAAK;YAEtC,kCAAkC;YAClC,IAAI,UAAU;gBACZ,OAAO,SAAS,QAAQ,CAAC,IAAI,IAAI,gBAAgB;YACnD;YAEA,kDAAkD;YAClD,IAAI,cAAe,WAAY;gBAC7B,OAAO,SAAS,QAAQ,CAAC,IAAI,IAAI,gBAAgB;YACnD;YAEA,yBAAyB;YACzB,IAAI,WAAW;gBACb,OAAO;YACT;YAEA,oCAAoC;YACpC,IAAI,UAAU;gBACZ,OAAO;YACT;YAEA,OAAO;QACN;IACH;AACD", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28auth%29/auth.ts"], "sourcesContent": ["import NextAuth, {User, Session, CredentialsSignin} from \"next-auth\";\r\nimport type { Provider } from \"next-auth/providers\";\r\nimport Credentials from \"next-auth/providers/credentials\";\r\nimport { authConfig } from \"./auth.config\";\r\n\r\ninterface ExtendedSession extends Session {\r\n    user: User;\r\n}\r\n\r\ninterface ValidationResponse {\r\n    code: number;\r\n    message: string;\r\n    result: {\r\n        isValid: boolean;\r\n        message: string;\r\n    };\r\n}\r\n\r\ninterface UserResponse {\r\n    code: number;\r\n    message: string;\r\n    result: {\r\n        userId: string;\r\n        userNm: string;\r\n        emailaddr: string | null;\r\n        userSeCode: string;\r\n        userSeCodeNm: string;\r\n        userImage: string | null;\r\n        insttCode: string;\r\n        insttNm: string | null;\r\n        insttUrl: string | null;\r\n        message: string;\r\n    };\r\n}\r\n\r\nclass InvalidLoginError extends CredentialsSignin {\r\n    code = \"Invalid identifier or password\"\r\n}\r\n\r\nconst PRODUCTION = process.env.NODE_ENV === \"production\";\r\nconst API_CERTIFICATE_KEY = 'tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh'\r\nconst SMT_URL = 'https://gsapi.geon.kr/smt'\r\n\r\nasync function validateLogin(userId: string, password: string): Promise<ValidationResponse> {\r\n    const params = new URLSearchParams({\r\n        crtfckey: API_CERTIFICATE_KEY,\r\n        userId,\r\n        password\r\n    });\r\n\r\n    const response = await fetch(`${SMT_URL}/login/validation?${params.toString()}`, {\r\n        method: 'POST',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            'crtfckey': API_CERTIFICATE_KEY\r\n        }\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n        throw new Error('로그인 검증에 실패했습니다.');\r\n    }\r\n\r\n    return data;\r\n}\r\n\r\nasync function getUserInfo(userId: string): Promise<UserResponse> {\r\n    const params = new URLSearchParams({\r\n        crtfckey: API_CERTIFICATE_KEY,\r\n        userId\r\n    });\r\n\r\n    const response = await fetch(`${SMT_URL}/users/id?${params.toString()}`, {\r\n        method: 'GET',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            'crtfckey': API_CERTIFICATE_KEY\r\n        }\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n        throw new Error('사용자 정보를 가져오는데 실패했습니다.');\r\n    }\r\n\r\n    return data;\r\n}\r\n\r\nexport const providers: Provider[] = [\r\n    Credentials({\r\n        credentials: {},\r\n        async authorize({id, password}: any) {\r\n            try {\r\n                // admin 계정으로 프론트엔드 로그인 허용\r\n                const frontendUserId = process.env.FRONTEND_LOGIN_USER_ID || 'admin';\r\n                const frontendPassword = process.env.FRONTEND_LOGIN_PASSWORD || 'password1234';\r\n\r\n                if (id === frontendUserId && password === frontendPassword) {\r\n                    // admin 계정으로 로그인 성공\r\n                    return {\r\n                        id: frontendUserId,\r\n                        name: 'GeOn City',\r\n                        email: '@example.com',\r\n                        userId: frontendUserId,\r\n                        userNm: 'GeOn City',\r\n                        emailaddr: '@example.com',\r\n                        userSeCode: '14',\r\n                        userSeCodeNm: '관리자',\r\n                        userImage: null,\r\n                        insttCode: 'GEON',\r\n                        insttNm: 'GeOn',\r\n                        insttUrl: null,\r\n                        message: '로그인 성공'\r\n                    };\r\n                }\r\n\r\n                // 기존 geonuser 계정도 유지 (호환성을 위해)\r\n                if (id === 'geonuser') {\r\n                    // 1. 로그인 검증\r\n                    const validation = await validateLogin(id, password);\r\n\r\n                    if (!validation.result.isValid) {\r\n                        throw new CredentialsSignin(validation.result.message);\r\n                    }\r\n\r\n                    // 2. 유저 정보 조회\r\n                    const userResponse = await getUserInfo(id);\r\n\r\n                    if (userResponse.code !== 200) {\r\n                        return new CredentialsSignin(userResponse.result.message);\r\n                    }\r\n\r\n                    // 3. 유저 정보 반환\r\n                    return {\r\n                        ...userResponse.result,\r\n                        id: userResponse.result.userId,\r\n                        name: userResponse.result.userNm || id,\r\n                        email: userResponse.result.emailaddr || `${userResponse.result.userNm}`,\r\n                    };\r\n                }\r\n\r\n                // 허용되지 않은 계정\r\n                throw new CredentialsSignin('admin 또는 geonuser 계정으로만 로그인할 수 있습니다.');\r\n            } catch (error) {\r\n                console.error('Auth error:', error);\r\n                throw error;\r\n            }\r\n        },\r\n    })\r\n]\r\n\r\nexport const providerMap = providers\r\n  .map((provider) => {\r\n      if (typeof provider === \"function\") {\r\n          const providerData = provider()\r\n          return { id: providerData.id, name: providerData.name }\r\n      } else {\r\n          return { id: provider.id, name: provider.name }\r\n      }\r\n  })\r\n  .filter((provider) => provider.id !== \"credentials\")\r\n\r\nexport const {\r\n    handlers,\r\n    auth,\r\n    signIn,\r\n    signOut,\r\n} = NextAuth({\r\n    ...authConfig,\r\n    providers,\r\n    session: {\r\n        strategy: \"jwt\",\r\n        maxAge: 30 * 60, // 30분 (30분 * 60초)\r\n    },\r\n    callbacks: {\r\n        async jwt({ token, user }) {\r\n            if (user) {\r\n                token.id = user.id;\r\n            }\r\n            return token;\r\n        },\r\n        async session({session, token,}: {\r\n            session: ExtendedSession;\r\n            token: any;\r\n        }) {\r\n            if (session.user) {\r\n                session.user.id = token.id as string;\r\n            }\r\n            return session;\r\n        },\r\n    }\r\n    // adapter: DrizzleAdapter(db, {\r\n    //     // @ts-ignore GitHub 로그인의 경우 email Null 가능성 존재\r\n    //     usersTable: users,\r\n    //     accountsTable: accounts,\r\n    //     sessionsTable: sessions,\r\n    //     verificationTokensTable: verificationTokens,\r\n    // }) as Adapter,\r\n    // cookies: {\r\n    //     sessionToken: {\r\n    //         name: `${PRODUCTION ? \"__Secure-\" : \"\"}next-auth.session-token`,\r\n    //         options: {\r\n    //             httpOnly: true,\r\n    //             sameSite: \"lax\",\r\n    //             path: \"/\",\r\n    //             secure: PRODUCTION,\r\n    //             // When working on localhost, the cookie domain must be omitted entirely (https://stackoverflow.com/a/1188145)\r\n    //             domain: PRODUCTION\r\n    //               ? `.${process.env.NEXT_PUBLIC_ROOT_DOMAIN}`\r\n    //               : undefined,\r\n    //         },\r\n    //     },\r\n    // },\r\n});\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;;;;AAgCA,MAAM,0BAA0B,sMAAA,CAAA,oBAAiB;IAC7C,OAAO,iCAAgC;AAC3C;AAEA,MAAM,aAAa,oDAAyB;AAC5C,MAAM,sBAAsB;AAC5B,MAAM,UAAU;AAEhB,eAAe,cAAc,MAAc,EAAE,QAAgB;IACzD,MAAM,SAAS,IAAI,gBAAgB;QAC/B,UAAU;QACV;QACA;IACJ;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,kBAAkB,EAAE,OAAO,QAAQ,IAAI,EAAE;QAC7E,QAAQ;QACR,SAAS;YACL,gBAAgB;YAChB,YAAY;QAChB;IACJ;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;QACd,MAAM,IAAI,MAAM;IACpB;IAEA,OAAO;AACX;AAEA,eAAe,YAAY,MAAc;IACrC,MAAM,SAAS,IAAI,gBAAgB;QAC/B,UAAU;QACV;IACJ;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,UAAU,EAAE,OAAO,QAAQ,IAAI,EAAE;QACrE,QAAQ;QACR,SAAS;YACL,gBAAgB;YAChB,YAAY;QAChB;IACJ;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;QACd,MAAM,IAAI,MAAM;IACpB;IAEA,OAAO;AACX;AAEO,MAAM,YAAwB;IACjC,CAAA,GAAA,wNAAA,CAAA,UAAW,AAAD,EAAE;QACR,aAAa,CAAC;QACd,MAAM,WAAU,EAAC,EAAE,EAAE,QAAQ,EAAM;YAC/B,IAAI;gBACA,0BAA0B;gBAC1B,MAAM,iBAAiB,QAAQ,GAAG,CAAC,sBAAsB,IAAI;gBAC7D,MAAM,mBAAmB,QAAQ,GAAG,CAAC,uBAAuB,IAAI;gBAEhE,IAAI,OAAO,kBAAkB,aAAa,kBAAkB;oBACxD,oBAAoB;oBACpB,OAAO;wBACH,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,QAAQ;wBACR,QAAQ;wBACR,WAAW;wBACX,YAAY;wBACZ,cAAc;wBACd,WAAW;wBACX,WAAW;wBACX,SAAS;wBACT,UAAU;wBACV,SAAS;oBACb;gBACJ;gBAEA,+BAA+B;gBAC/B,IAAI,OAAO,YAAY;oBACnB,YAAY;oBACZ,MAAM,aAAa,MAAM,cAAc,IAAI;oBAE3C,IAAI,CAAC,WAAW,MAAM,CAAC,OAAO,EAAE;wBAC5B,MAAM,IAAI,sMAAA,CAAA,oBAAiB,CAAC,WAAW,MAAM,CAAC,OAAO;oBACzD;oBAEA,cAAc;oBACd,MAAM,eAAe,MAAM,YAAY;oBAEvC,IAAI,aAAa,IAAI,KAAK,KAAK;wBAC3B,OAAO,IAAI,sMAAA,CAAA,oBAAiB,CAAC,aAAa,MAAM,CAAC,OAAO;oBAC5D;oBAEA,cAAc;oBACd,OAAO;wBACH,GAAG,aAAa,MAAM;wBACtB,IAAI,aAAa,MAAM,CAAC,MAAM;wBAC9B,MAAM,aAAa,MAAM,CAAC,MAAM,IAAI;wBACpC,OAAO,aAAa,MAAM,CAAC,SAAS,IAAI,GAAG,aAAa,MAAM,CAAC,MAAM,EAAE;oBAC3E;gBACJ;gBAEA,aAAa;gBACb,MAAM,IAAI,sMAAA,CAAA,oBAAiB,CAAC;YAChC,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,eAAe;gBAC7B,MAAM;YACV;QACJ;IACJ;CACH;AAEM,MAAM,cAAc,UACxB,GAAG,CAAC,CAAC;IACF,IAAI,OAAO,aAAa,YAAY;QAChC,MAAM,eAAe;QACrB,OAAO;YAAE,IAAI,aAAa,EAAE;YAAE,MAAM,aAAa,IAAI;QAAC;IAC1D,OAAO;QACH,OAAO;YAAE,IAAI,SAAS,EAAE;YAAE,MAAM,SAAS,IAAI;QAAC;IAClD;AACJ,GACC,MAAM,CAAC,CAAC,WAAa,SAAS,EAAE,KAAK;AAEjC,MAAM,EACT,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,OAAO,EACV,GAAG,CAAA,GAAA,mQAAA,CAAA,UAAQ,AAAD,EAAE;IACT,GAAG,mIAAA,CAAA,aAAU;IACb;IACA,SAAS;QACL,UAAU;QACV,QAAQ,KAAK;IACjB;IACA,WAAW;QACP,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACrB,IAAI,MAAM;gBACN,MAAM,EAAE,GAAG,KAAK,EAAE;YACtB;YACA,OAAO;QACX;QACA,MAAM,SAAQ,EAAC,OAAO,EAAE,KAAK,EAG5B;YACG,IAAI,QAAQ,IAAI,EAAE;gBACd,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;YAC9B;YACA,OAAO;QACX;IACJ;AAuBJ", "debugId": null}}, {"offset": {"line": 416, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/db/schema.ts"], "sourcesContent": ["import { createId } from \"@paralleldrive/cuid2\";\r\nimport { InferSelectModel, relations } from \"drizzle-orm\";\r\nimport {\r\n  boolean,\r\n  index,\r\n  integer,\r\n  json,\r\n  pgTable,\r\n  primaryKey,\r\n  text,\r\n  timestamp,\r\n  uuid,\r\n  varchar,\r\n} from \"drizzle-orm/pg-core\";\r\nimport { generateId } from \"ai\";\r\n\r\n/**\r\n * 사용자, 인증, 인가 관련 테이블은 자유롭게 수정 가능합니다.\r\n * 현재 Chat 테이블 외에는 사용하지 않음.\r\n */\r\nexport const user = pgTable(\"user\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .$defaultFn(() => createId()),\r\n  name: text(\"name\"),\r\n  // if you are using Github OAuth, you can get rid of the username attribute (that is for Twitter OAuth)\r\n  username: text(\"username\"),\r\n  gh_username: text(\"gh_username\"),\r\n  email: text(\"email\").unique(),\r\n  emailVerified: timestamp(\"emailVerified\", { mode: \"date\" }),\r\n  image: text(\"image\"),\r\n  createdAt: timestamp(\"createdAt\", { mode: \"date\" }).defaultNow().notNull(),\r\n  updatedAt: timestamp(\"updatedAt\", { mode: \"date\" })\r\n    .notNull()\r\n    .$onUpdate(() => new Date()),\r\n});\r\n\r\nexport const sessions = pgTable(\r\n  \"sessions\",\r\n  {\r\n    sessionToken: text(\"sessionToken\").primaryKey(),\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => user.id, { onDelete: \"cascade\", onUpdate: \"cascade\" }),\r\n    expires: timestamp(\"expires\", { mode: \"date\" }).notNull(),\r\n  },\r\n  (table) => {\r\n    return {\r\n      userIdIdx: index().on(table.userId),\r\n    };\r\n  }\r\n);\r\n\r\nexport const verificationTokens = pgTable(\r\n  \"verificationTokens\",\r\n  {\r\n    identifier: text(\"identifier\").notNull(),\r\n    token: text(\"token\").notNull().unique(),\r\n    expires: timestamp(\"expires\", { mode: \"date\" }).notNull(),\r\n  },\r\n  (table) => {\r\n    return {\r\n      compositePk: primaryKey({ columns: [table.identifier, table.token] }),\r\n    };\r\n  }\r\n);\r\n\r\nexport const accounts = pgTable(\r\n  \"accounts\",\r\n  {\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => user.id, { onDelete: \"cascade\", onUpdate: \"cascade\" }),\r\n    type: text(\"type\").notNull(),\r\n    provider: text(\"provider\").notNull(),\r\n    providerAccountId: text(\"providerAccountId\").notNull(),\r\n    refresh_token: text(\"refresh_token\"),\r\n    refreshTokenExpiresIn: integer(\"refresh_token_expires_in\"),\r\n    access_token: text(\"access_token\"),\r\n    expires_at: integer(\"expires_at\"),\r\n    token_type: text(\"token_type\"),\r\n    scope: text(\"scope\"),\r\n    id_token: text(\"id_token\"),\r\n    session_state: text(\"session_state\"),\r\n    oauth_token_secret: text(\"oauth_token_secret\"),\r\n    oauth_token: text(\"oauth_token\"),\r\n  },\r\n  (table) => {\r\n    return {\r\n      userIdIdx: index().on(table.userId),\r\n      compositePk: primaryKey({\r\n        columns: [table.provider, table.providerAccountId],\r\n      }),\r\n    };\r\n  }\r\n);\r\n\r\nexport type User = InferSelectModel<typeof user>;\r\n\r\nexport const chat = pgTable(\"Chat\", {\r\n  id: text(\"id\").primaryKey(),\r\n  createdAt: timestamp(\"createdAt\").notNull(),\r\n  title: text(\"title\").notNull().default('New Chat'), // 기본값 추가\r\n  userId: text(\"userId\").notNull(),\r\n  visibility: varchar(\"visibility\", { enum: [\"public\", \"private\"] })\r\n    .notNull()\r\n    .default(\"private\"),\r\n});\r\n\r\nexport type Chat = InferSelectModel<typeof chat>;\r\n\r\nexport const message = pgTable(\"Message\", {\r\n  id: uuid(\"id\").primaryKey().notNull().defaultRandom(),\r\n  chatId: text(\"chatId\")\r\n    .notNull()\r\n    .references(() => chat.id),\r\n  role: varchar(\"role\").notNull(),\r\n  content: json(\"content\"), // 기존 호환성을 위해 nullable로 변경\r\n  parts: json(\"parts\"), // AI SDK parts 배열\r\n  attachments: json(\"attachments\"), // AI SDK attachments 배열\r\n  createdAt: timestamp(\"createdAt\").notNull(),\r\n});\r\n\r\nexport type Message = InferSelectModel<typeof message>;\r\n\r\nexport const vote = pgTable(\r\n  \"Vote\",\r\n  {\r\n    chatId: text(\"chatId\")\r\n      .notNull()\r\n      .references(() => chat.id),\r\n    messageId: uuid(\"messageId\")\r\n      .notNull()\r\n      .references(() => message.id),\r\n    isUpvoted: boolean(\"isUpvoted\").notNull(),\r\n  },\r\n  (table) => {\r\n    return {\r\n      pk: primaryKey({ columns: [table.chatId, table.messageId] }),\r\n    };\r\n  }\r\n);\r\n\r\nexport type Vote = InferSelectModel<typeof vote>;\r\n\r\nexport const map = pgTable(\"map\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .notNull()\r\n    .$defaultFn(() => generateId()),\r\n  name: text(\"name\").notNull(),\r\n  createdBy: text(\"userId\").notNull(),\r\n  createdAt: timestamp(\"createdAt\").notNull().defaultNow(),\r\n  updatedAt: timestamp(\"updatedAt\").notNull().defaultNow(),\r\n  isPublic: boolean(\"isPublic\").default(false),\r\n  // 공유되는 핵심 상태\r\n  layers: json(\"layers\").notNull(), // Layer[]\r\n  version: integer(\"version\").notNull().default(1), // 동시성 제어를 위한 버전\r\n});\r\n\r\n// 사용자별 지도 뷰 상태\r\nexport const mapView = pgTable(\r\n  \"map_view\",\r\n  {\r\n    id: text(\"id\")\r\n      .notNull()\r\n      .$defaultFn(() => generateId()), // PRIMARY KEY 제거\r\n    mapId: text(\"mapId\")\r\n      .notNull()\r\n      .references(() => map.id, { onDelete: \"cascade\" }),\r\n    userId: text(\"userId\").notNull(),\r\n    center: json(\"center\").notNull(),\r\n    zoom: integer(\"zoom\").notNull(),\r\n    basemap: text(\"basemap\").notNull(),\r\n    updatedAt: timestamp(\"updatedAt\").notNull().defaultNow(),\r\n  },\r\n  (table) => {\r\n    return {\r\n      compoundKey: primaryKey({ columns: [table.mapId, table.userId] }), // 복합 키 설정\r\n      idIdx: index(\"map_view_id_idx\").on(table.id), // id는 unique index로 변경\r\n    };\r\n  }\r\n);\r\n\r\n// 채팅-지도 연결 테이블\r\nexport const chatMap = pgTable(\"chat_map\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .notNull()\r\n    .$defaultFn(() => generateId()),\r\n  chatId: text(\"chatId\")\r\n    .notNull()\r\n    .references(() => chat.id, { onDelete: \"cascade\" }),\r\n  mapId: text(\"mapId\")\r\n    .notNull()\r\n    .references(() => map.id, { onDelete: \"restrict\" }), // 지도는 보존\r\n  createdAt: timestamp(\"createdAt\").notNull().defaultNow(),\r\n});\r\n\r\n// 지도 접근 권한\r\nexport const mapAccess = pgTable(\"map_access\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .notNull()\r\n    .$defaultFn(() => generateId()),\r\n  mapId: text(\"mapId\")\r\n    .notNull()\r\n    .references(() => map.id, { onDelete: \"cascade\" }),\r\n  userId: text(\"userId\").notNull(),\r\n  accessType: text(\"accessType\").notNull(), // 'owner', 'edit', 'view'\r\n  createdAt: timestamp(\"createdAt\").notNull().defaultNow(),\r\n});\r\n\r\n// 실시간 협업 세션\r\nexport const mapSession = pgTable(\"map_session\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .notNull()\r\n    .$defaultFn(() => generateId()),\r\n  mapId: text(\"mapId\")\r\n    .notNull()\r\n    .references(() => map.id, { onDelete: \"cascade\" }),\r\n  userId: text(\"userId\").notNull(),\r\n  isActive: boolean(\"isActive\").default(true),\r\n  lastActiveAt: timestamp(\"lastActiveAt\").notNull().defaultNow(),\r\n  // 선택적 view 동기화 설정\r\n  syncView: boolean(\"syncView\").default(false),\r\n  followingUserId: text(\"followingUserId\"), // 다른 사용자의 view를 따라갈 때\r\n});\r\n\r\n// 관계 설정\r\nexport const mapsRelation = relations(map, ({ many }) => ({\r\n  views: many(mapView),\r\n  access: many(mapAccess),\r\n  sessions: many(mapSession),\r\n  chats: many(chatMap),\r\n}));\r\n\r\nexport const chatRelation = relations(chat, ({ many }) => ({\r\n  maps: many(chatMap),\r\n}));\r\n\r\nexport const sessionsRelations = relations(sessions, ({ one }) => ({\r\n  user: one(user, { references: [user.id], fields: [sessions.userId] }),\r\n}));\r\n\r\nexport const accountsRelations = relations(accounts, ({ one }) => ({\r\n  user: one(user, { references: [user.id], fields: [accounts.userId] }),\r\n}));\r\n\r\nexport const userRelations = relations(user, ({ many }) => ({\r\n  accounts: many(accounts),\r\n  sessions: many(sessions),\r\n}));\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;;;AAMO,MAAM,OAAO,CAAA,GAAA,gQAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;IAClC,IAAI,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,MACN,UAAU,GACV,UAAU,CAAC,IAAM,CAAA,GAAA,wNAAA,CAAA,WAAQ,AAAD;IAC3B,MAAM,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;IACX,uGAAuG;IACvG,UAAU,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;IACf,aAAa,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;IAClB,OAAO,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,SAAS,MAAM;IAC3B,eAAe,CAAA,GAAA,+QAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;QAAE,MAAM;IAAO;IACzD,OAAO,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;IACZ,WAAW,CAAA,GAAA,+QAAA,CAAA,YAAS,AAAD,EAAE,aAAa;QAAE,MAAM;IAAO,GAAG,UAAU,GAAG,OAAO;IACxE,WAAW,CAAA,GAAA,+QAAA,CAAA,YAAS,AAAD,EAAE,aAAa;QAAE,MAAM;IAAO,GAC9C,OAAO,GACP,SAAS,CAAC,IAAM,IAAI;AACzB;AAEO,MAAM,WAAW,CAAA,GAAA,gQAAA,CAAA,UAAO,AAAD,EAC5B,YACA;IACE,cAAc,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,UAAU;IAC7C,QAAQ,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IACxE,SAAS,CAAA,GAAA,+QAAA,CAAA,YAAS,AAAD,EAAE,WAAW;QAAE,MAAM;IAAO,GAAG,OAAO;AACzD,GACA,CAAC;IACC,OAAO;QACL,WAAW,CAAA,GAAA,kQAAA,CAAA,QAAK,AAAD,IAAI,EAAE,CAAC,MAAM,MAAM;IACpC;AACF;AAGK,MAAM,qBAAqB,CAAA,GAAA,gQAAA,CAAA,UAAO,AAAD,EACtC,sBACA;IACE,YAAY,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACtC,OAAO,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,GAAG,MAAM;IACrC,SAAS,CAAA,GAAA,+QAAA,CAAA,YAAS,AAAD,EAAE,WAAW;QAAE,MAAM;IAAO,GAAG,OAAO;AACzD,GACA,CAAC;IACC,OAAO;QACL,aAAa,CAAA,GAAA,0QAAA,CAAA,aAAU,AAAD,EAAE;YAAE,SAAS;gBAAC,MAAM,UAAU;gBAAE,MAAM,KAAK;aAAC;QAAC;IACrE;AACF;AAGK,MAAM,WAAW,CAAA,GAAA,gQAAA,CAAA,UAAO,AAAD,EAC5B,YACA;IACE,QAAQ,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IACxE,MAAM,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,UAAU,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO;IAClC,mBAAmB,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,qBAAqB,OAAO;IACpD,eAAe,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;IACpB,uBAAuB,CAAA,GAAA,6QAAA,CAAA,UAAO,AAAD,EAAE;IAC/B,cAAc,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;IACnB,YAAY,CAAA,GAAA,6QAAA,CAAA,UAAO,AAAD,EAAE;IACpB,YAAY,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;IACjB,OAAO,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;IACZ,UAAU,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;IACf,eAAe,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;IACpB,oBAAoB,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;IACzB,aAAa,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;AACpB,GACA,CAAC;IACC,OAAO;QACL,WAAW,CAAA,GAAA,kQAAA,CAAA,QAAK,AAAD,IAAI,EAAE,CAAC,MAAM,MAAM;QAClC,aAAa,CAAA,GAAA,0QAAA,CAAA,aAAU,AAAD,EAAE;YACtB,SAAS;gBAAC,MAAM,QAAQ;gBAAE,MAAM,iBAAiB;aAAC;QACpD;IACF;AACF;AAKK,MAAM,OAAO,CAAA,GAAA,gQAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;IAClC,IAAI,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,+QAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO;IACzC,OAAO,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,GAAG,OAAO,CAAC;IACvC,QAAQ,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,YAAY,CAAA,GAAA,6QAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;YAAC;YAAU;SAAU;IAAC,GAC7D,OAAO,GACP,OAAO,CAAC;AACb;AAIO,MAAM,UAAU,CAAA,GAAA,gQAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IACxC,IAAI,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,OAAO,GAAG,aAAa;IACnD,QAAQ,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE;IAC3B,MAAM,CAAA,GAAA,6QAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAAO;IAC7B,SAAS,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;IACd,OAAO,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;IACZ,aAAa,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;IAClB,WAAW,CAAA,GAAA,+QAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO;AAC3C;AAIO,MAAM,OAAO,CAAA,GAAA,gQAAA,CAAA,UAAO,AAAD,EACxB,QACA;IACE,QAAQ,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE;IAC3B,WAAW,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,aACb,OAAO,GACP,UAAU,CAAC,IAAM,QAAQ,EAAE;IAC9B,WAAW,CAAA,GAAA,6QAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO;AACzC,GACA,CAAC;IACC,OAAO;QACL,IAAI,CAAA,GAAA,0QAAA,CAAA,aAAU,AAAD,EAAE;YAAE,SAAS;gBAAC,MAAM,MAAM;gBAAE,MAAM,SAAS;aAAC;QAAC;IAC5D;AACF;AAKK,MAAM,MAAM,CAAA,GAAA,gQAAA,CAAA,UAAO,AAAD,EAAE,OAAO;IAChC,IAAI,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,MACN,UAAU,GACV,OAAO,GACP,UAAU,CAAC,IAAM,CAAA,GAAA,qQAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,WAAW,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IACjC,WAAW,CAAA,GAAA,+QAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO,GAAG,UAAU;IACtD,WAAW,CAAA,GAAA,+QAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO,GAAG,UAAU;IACtD,UAAU,CAAA,GAAA,6QAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,CAAC;IACtC,aAAa;IACb,QAAQ,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,SAAS,CAAA,GAAA,6QAAA,CAAA,UAAO,AAAD,EAAE,WAAW,OAAO,GAAG,OAAO,CAAC;AAChD;AAGO,MAAM,UAAU,CAAA,GAAA,gQAAA,CAAA,UAAO,AAAD,EAC3B,YACA;IACE,IAAI,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,MACN,OAAO,GACP,UAAU,CAAC,IAAM,CAAA,GAAA,qQAAA,CAAA,aAAU,AAAD;IAC7B,OAAO,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,SACT,OAAO,GACP,UAAU,CAAC,IAAM,IAAI,EAAE,EAAE;QAAE,UAAU;IAAU;IAClD,QAAQ,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,QAAQ,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,MAAM,CAAA,GAAA,6QAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAAO;IAC7B,SAAS,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO;IAChC,WAAW,CAAA,GAAA,+QAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO,GAAG,UAAU;AACxD,GACA,CAAC;IACC,OAAO;QACL,aAAa,CAAA,GAAA,0QAAA,CAAA,aAAU,AAAD,EAAE;YAAE,SAAS;gBAAC,MAAM,KAAK;gBAAE,MAAM,MAAM;aAAC;QAAC;QAC/D,OAAO,CAAA,GAAA,kQAAA,CAAA,QAAK,AAAD,EAAE,mBAAmB,EAAE,CAAC,MAAM,EAAE;IAC7C;AACF;AAIK,MAAM,UAAU,CAAA,GAAA,gQAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IACzC,IAAI,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,MACN,UAAU,GACV,OAAO,GACP,UAAU,CAAC,IAAM,CAAA,GAAA,qQAAA,CAAA,aAAU,AAAD;IAC7B,QAAQ,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE,EAAE;QAAE,UAAU;IAAU;IACnD,OAAO,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,SACT,OAAO,GACP,UAAU,CAAC,IAAM,IAAI,EAAE,EAAE;QAAE,UAAU;IAAW;IACnD,WAAW,CAAA,GAAA,+QAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO,GAAG,UAAU;AACxD;AAGO,MAAM,YAAY,CAAA,GAAA,gQAAA,CAAA,UAAO,AAAD,EAAE,cAAc;IAC7C,IAAI,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,MACN,UAAU,GACV,OAAO,GACP,UAAU,CAAC,IAAM,CAAA,GAAA,qQAAA,CAAA,aAAU,AAAD;IAC7B,OAAO,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,SACT,OAAO,GACP,UAAU,CAAC,IAAM,IAAI,EAAE,EAAE;QAAE,UAAU;IAAU;IAClD,QAAQ,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,YAAY,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACtC,WAAW,CAAA,GAAA,+QAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO,GAAG,UAAU;AACxD;AAGO,MAAM,aAAa,CAAA,GAAA,gQAAA,CAAA,UAAO,AAAD,EAAE,eAAe;IAC/C,IAAI,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,MACN,UAAU,GACV,OAAO,GACP,UAAU,CAAC,IAAM,CAAA,GAAA,qQAAA,CAAA,aAAU,AAAD;IAC7B,OAAO,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,SACT,OAAO,GACP,UAAU,CAAC,IAAM,IAAI,EAAE,EAAE;QAAE,UAAU;IAAU;IAClD,QAAQ,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,UAAU,CAAA,GAAA,6QAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,CAAC;IACtC,cAAc,CAAA,GAAA,+QAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,OAAO,GAAG,UAAU;IAC5D,kBAAkB;IAClB,UAAU,CAAA,GAAA,6QAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,CAAC;IACtC,iBAAiB,CAAA,GAAA,0QAAA,CAAA,OAAI,AAAD,EAAE;AACxB;AAGO,MAAM,eAAe,CAAA,GAAA,sPAAA,CAAA,YAAS,AAAD,EAAE,KAAK,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QACxD,OAAO,KAAK;QACZ,QAAQ,KAAK;QACb,UAAU,KAAK;QACf,OAAO,KAAK;IACd,CAAC;AAEM,MAAM,eAAe,CAAA,GAAA,sPAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QACzD,MAAM,KAAK;IACb,CAAC;AAEM,MAAM,oBAAoB,CAAA,GAAA,sPAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACjE,MAAM,IAAI,MAAM;YAAE,YAAY;gBAAC,KAAK,EAAE;aAAC;YAAE,QAAQ;gBAAC,SAAS,MAAM;aAAC;QAAC;IACrE,CAAC;AAEM,MAAM,oBAAoB,CAAA,GAAA,sPAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACjE,MAAM,IAAI,MAAM;YAAE,YAAY;gBAAC,KAAK,EAAE;aAAC;YAAE,QAAQ;gBAAC,SAAS,MAAM;aAAC;QAAC;IACrE,CAAC;AAEM,MAAM,gBAAgB,CAAA,GAAA,sPAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QAC1D,UAAU,KAAK;QACf,UAAU,KAAK;IACjB,CAAC", "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/db/index.ts"], "sourcesContent": ["import { Pool } from \"pg\";\r\nimport { drizzle } from \"drizzle-orm/node-postgres\";\r\nimport * as schema from \"./schema\";\r\n\r\nconst pool  = new Pool  ({\r\n\tconnectionString: process.env.POSTGRES_URL\r\n});\r\n\r\nconst db = drizzle(pool, { schema, logger: true });\r\n\r\nexport default db;\r\nexport type DrizzleClient = typeof db;\r\n\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,OAAQ,IAAI,6FAAA,CAAA,OAAI,CAAG;IACxB,kBAAkB,QAAQ,GAAG,CAAC,YAAY;AAC3C;AAEA,MAAM,KAAK,CAAA,GAAA,uQAAA,CAAA,UAAO,AAAD,EAAE,MAAM;IAAE,QAAA;IAAQ,QAAQ;AAAK;uCAEjC", "debugId": null}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/db/queries.ts"], "sourcesContent": ["// db/queries.ts\r\nimport { map, mapView, mapAccess, chat, Message, message, vote } from \"@/lib/db/schema\";\r\nimport { desc, eq, and, or, sql, gte, asc } from \"drizzle-orm\";\r\nimport db from \"@/lib/db\";\r\nimport { Layer, MapView } from \"@/types/map\";\r\n\r\n// Chat Queries (기존)\r\nexport async function saveChat({\r\n  id,\r\n  userId,\r\n  title,\r\n}: {\r\n  id: string;\r\n  userId: string;\r\n  title: string;\r\n}) {\r\n  try {\r\n    return await db.insert(chat).values({\r\n      id,\r\n      createdAt: new Date(),\r\n      userId,\r\n      title,\r\n    });\r\n  } catch (error) {\r\n    console.error('Failed to save chat in database');\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function saveMessages({ messages }: { messages: Array<Message> }) {\r\n  try {\r\n    return await db.insert(message).values(messages);\r\n  } catch (error) {\r\n    console.error('Failed to save messages in database', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function deleteChatById({ id }: { id: string }) {\r\n  try {\r\n    await db.delete(vote).where(eq(vote.chatId, id));\r\n    await db.delete(message).where(eq(message.chatId, id));\r\n\r\n    return await db.delete(chat).where(eq(chat.id, id));\r\n  } catch (error) {\r\n    console.error('Failed to delete chat by id from database');\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function getChatsByUserId({ id }: { id: string }) {\r\n  try {\r\n    return await db\r\n      .select()\r\n      .from(chat)\r\n      .where(eq(chat.userId, id))\r\n      .orderBy(desc(chat.createdAt));\r\n  } catch (error) {\r\n    console.error(\"Failed to get chats by user from database\");\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function getChatById({ id }: { id: string }) {\r\n  try {\r\n    const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));\r\n    return selectedChat;\r\n  } catch (error) {\r\n    console.error(\"Failed to get chat by id from database\");\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function getMessageById({ id }: { id: string }) {\r\n  try {\r\n    return await db.select().from(message).where(eq(message.id, id));\r\n  } catch (error) {\r\n    console.error('Failed to get message by id from database');\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function getMessagesByChatId({ id }: { id: string }) {\r\n  try {\r\n    return await db\r\n      .select()\r\n      .from(message)\r\n      .where(eq(message.chatId, id))\r\n      .orderBy(asc(message.createdAt));\r\n  } catch (error) {\r\n    console.error('Failed to get messages by chat id from database', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function voteMessage({\r\n  chatId,\r\n  messageId,\r\n  type,\r\n}: {\r\n  chatId: string;\r\n  messageId: string;\r\n  type: 'up' | 'down';\r\n}) {\r\n  try {\r\n    const [existingVote] = await db\r\n      .select()\r\n      .from(vote)\r\n      .where(and(eq(vote.messageId, messageId)));\r\n\r\n    if (existingVote) {\r\n      return await db\r\n        .update(vote)\r\n        .set({ isUpvoted: type === 'up' })\r\n        .where(and(eq(vote.messageId, messageId), eq(vote.chatId, chatId)));\r\n    }\r\n    return await db.insert(vote).values({\r\n      chatId,\r\n      messageId,\r\n      isUpvoted: type === 'up',\r\n    });\r\n  } catch (error) {\r\n    console.error('Failed to upvote message in database', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function getVotesByChatId({ id }: { id: string }) {\r\n  try {\r\n    return await db.select().from(vote).where(eq(vote.chatId, id));\r\n  } catch (error) {\r\n    console.error('Failed to get votes by chat id from database', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function deleteMessagesByChatIdAfterTimestamp({\r\n  chatId,\r\n  timestamp,\r\n}: {\r\n  chatId: string;\r\n  timestamp: Date;\r\n}) {\r\n  try {\r\n    return await db\r\n      .delete(message)\r\n      .where(\r\n        and(eq(message.chatId, chatId), gte(message.createdAt, timestamp)),\r\n      );\r\n  } catch (error) {\r\n    console.error(\r\n      'Failed to delete messages by id after timestamp from database',\r\n    );\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function updateChatVisiblityById({\r\n  chatId,\r\n  visibility,\r\n}: {\r\n  chatId: string;\r\n  visibility: 'private' | 'public';\r\n}) {\r\n  try {\r\n    return await db.update(chat).set({ visibility }).where(eq(chat.id, chatId));\r\n  } catch (error) {\r\n    console.error('Failed to update chat visibility in database');\r\n    throw error;\r\n  }\r\n}\r\n\r\n\r\n// Map Queries\r\nexport async function getMapsByUserId({ userId }: { userId: string }) {\r\n  try {\r\n    return await db\r\n      .select({\r\n        id: map.id,\r\n        name: map.name,\r\n        createdAt: map.createdAt,\r\n        updatedAt: map.updatedAt,\r\n        layers: map.layers,\r\n        isPublic: map.isPublic,\r\n        activeUsers: sql<number>`\r\n        (\r\n          SELECT COUNT(DISTINCT ${mapView.userId})\r\n          FROM ${mapView}\r\n          WHERE ${mapView.mapId} = ${map.id}\r\n          AND ${mapView.updatedAt} > NOW() - INTERVAL '5 minutes'\r\n        )\r\n        `.as(\"activeUsers\"),\r\n        view: {\r\n          center: mapView.center,\r\n          zoom: mapView.zoom,\r\n          basemap: mapView.basemap,\r\n        },\r\n      })\r\n      .from(map)\r\n      .leftJoin(\r\n        mapAccess,\r\n        and(eq(mapAccess.mapId, map.id), eq(mapAccess.userId, userId))\r\n      )\r\n      .leftJoin(\r\n        mapView,\r\n        and(\r\n          eq(mapView.mapId, map.id),\r\n          eq(mapView.userId, userId)\r\n        )\r\n      )\r\n      .where(or(eq(map.createdBy, userId), eq(mapAccess.userId, userId)))\r\n      .orderBy(desc(map.updatedAt));\r\n  } catch (error) {\r\n    if (error instanceof Error) {\r\n      console.error(\"Failed to get maps by user from database:\", {\r\n        message: error.message,\r\n        stack: error.stack,\r\n      });\r\n    }\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function getMapById({\r\n  id,\r\n  userId,\r\n}: {\r\n  id: string;\r\n  userId: string;\r\n}) {\r\n  try {\r\n    const [mapData] = await db\r\n      .select({\r\n        id: map.id,\r\n        name: map.name,\r\n        createdAt: map.createdAt,\r\n        updatedAt: map.updatedAt,\r\n        layers: map.layers,\r\n        isPublic: map.isPublic,\r\n        createdBy: map.createdBy,\r\n        activeUsers: sql<number>`\r\n        (\r\n          SELECT COUNT(DISTINCT ${mapView.userId})\r\n          FROM ${mapView}\r\n          WHERE ${mapView.mapId} = ${map.id}\r\n          AND ${mapView.updatedAt} > NOW() - INTERVAL '5 minutes'\r\n        )\r\n        `.as(\"activeUsers\"),\r\n      })\r\n      .from(map)\r\n      .where(eq(map.id, id));\r\n\r\n    if (!mapData) {\r\n      throw new Error(\"Map not found\");\r\n    }\r\n\r\n    // layers 배열의 style 객체 파싱\r\n    if (Array.isArray(mapData.layers)) {\r\n      mapData.layers = mapData.layers.map((layer: Layer) => ({\r\n        ...layer,\r\n        style: layer.style ? (\r\n          typeof layer.style === 'string' ? JSON.parse(layer.style) : layer.style\r\n        ) : undefined\r\n      }));\r\n    }\r\n\r\n    // 접근 권한 확인\r\n    if (\r\n      mapData.createdBy !== userId &&\r\n      !mapData.isPublic &&\r\n      !(await hasMapAccess({ mapId: id, userId }))\r\n    ) {\r\n      throw new Error(\"Forbidden\");\r\n    }\r\n\r\n    // 사용자별 뷰 상태 조회\r\n    const [view] = await db\r\n      .select()\r\n      .from(mapView)\r\n      .where(and(eq(mapView.mapId, id), eq(mapView.userId, userId)));\r\n\r\n    return {\r\n      ...mapData,\r\n      view: view || null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Failed to get map by id from database\");\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function saveMap({\r\n  id,\r\n  name,\r\n  layers,\r\n  userId,\r\n}: {\r\n  id: string;\r\n  name: string;\r\n  layers: any[];\r\n  userId: string;\r\n}) {\r\n  try {\r\n    const existingMap = await db.select().from(map).where(eq(map.id, id));\r\n\r\n    if (existingMap.length > 0) {\r\n      return await db\r\n        .update(map)\r\n        .set({\r\n          name,\r\n          layers,\r\n          updatedAt: new Date(),\r\n        })\r\n        .where(eq(map.id, id));\r\n    }\r\n\r\n    return await db.insert(map).values({\r\n      id,\r\n      name,\r\n      layers,\r\n      createdBy: userId,\r\n      createdAt: new Date(),\r\n      updatedAt: new Date(),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Failed to save map in database\");\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function updateMapView({\r\n  mapId,\r\n  userId,\r\n  view,\r\n}: {\r\n  mapId: string;\r\n  userId: string;\r\n  view: MapView;\r\n}) {\r\n  try {\r\n    return await db\r\n      .insert(mapView)\r\n      .values({\r\n        mapId,\r\n        userId,\r\n        center: view.center ?? { lat: 36.5, lng: 127.5 },\r\n        zoom: view.zoom ?? 7,\r\n        basemap: view.basemap ?? \"eMapBasic\",\r\n        updatedAt: new Date(),\r\n      })\r\n      .onConflictDoUpdate({\r\n        target: [mapView.mapId, mapView.userId],\r\n        set: {\r\n          ...view,\r\n          updatedAt: new Date(),\r\n        },\r\n      });\r\n  } catch (error) {\r\n    console.error(\"Failed to update map view state\");\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function deleteMapById({\r\n  id,\r\n  userId,\r\n}: {\r\n  id: string;\r\n  userId: string;\r\n}) {\r\n  try {\r\n    const [mapData] = await db.select().from(map).where(eq(map.id, id));\r\n\r\n    if (!mapData) {\r\n      throw new Error(\"Map not found\");\r\n    }\r\n\r\n    if (mapData.createdBy !== userId) {\r\n      throw new Error(\"Forbidden\");\r\n    }\r\n\r\n    return await db.delete(map).where(eq(map.id, id));\r\n  } catch (error) {\r\n    console.error(\"Failed to delete map from database\");\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function hasMapAccess({\r\n  mapId,\r\n  userId,\r\n}: {\r\n  mapId: string;\r\n  userId: string;\r\n}) {\r\n  try {\r\n    const access = await db\r\n      .select()\r\n      .from(mapAccess)\r\n      .where(and(eq(mapAccess.mapId, mapId), eq(mapAccess.userId, userId)));\r\n\r\n    return access.length > 0;\r\n  } catch (error) {\r\n    console.error(\"Failed to check map access\");\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function shareMap({\r\n  mapId,\r\n  userId,\r\n  targetUserId,\r\n  accessType = \"view\",\r\n}: {\r\n  mapId: string;\r\n  userId: string;\r\n  targetUserId: string;\r\n  accessType?: \"view\" | \"edit\";\r\n}) {\r\n  try {\r\n    // 공유 권한 확인\r\n    const [mapData] = await db.select().from(map).where(eq(map.id, mapId));\r\n\r\n    if (!mapData || mapData.createdBy !== userId) {\r\n      throw new Error(\"Forbidden\");\r\n    }\r\n\r\n    return await db.insert(mapAccess).values({\r\n      mapId,\r\n      userId: targetUserId,\r\n      accessType,\r\n      createdAt: new Date(),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Failed to share map\");\r\n    throw error;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;;;;;;;;;;;;;;;;;AAChB;AACA;AAAA;AAAA;AACA;;;;AAIO,eAAe,SAAS,EAC7B,EAAE,EACF,MAAM,EACN,KAAK,EAKN;IACC,IAAI;QACF,OAAO,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,qHAAA,CAAA,OAAI,EAAE,MAAM,CAAC;YAClC;YACA,WAAW,IAAI;YACf;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,aAAa,EAAE,QAAQ,EAAgC;IAC3E,IAAI;QACF,OAAO,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,qHAAA,CAAA,UAAO,EAAE,MAAM,CAAC;IACzC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE,EAAE,EAAkB;IACzD,IAAI;QACF,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,qHAAA,CAAA,OAAI,EAAE,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,MAAM,EAAE;QAC5C,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,qHAAA,CAAA,UAAO,EAAE,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,MAAM,EAAE;QAElD,OAAO,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,qHAAA,CAAA,OAAI,EAAE,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,EAAE,EAAE;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,EAAE,EAAE,EAAkB;IAC3D,IAAI;QACF,OAAO,MAAM,oHAAA,CAAA,UAAE,CACZ,MAAM,GACN,IAAI,CAAC,qHAAA,CAAA,OAAI,EACT,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,MAAM,EAAE,KACtB,OAAO,CAAC,CAAA,GAAA,yQAAA,CAAA,OAAI,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,SAAS;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,YAAY,EAAE,EAAE,EAAkB;IACtD,IAAI;QACF,MAAM,CAAC,aAAa,GAAG,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,qHAAA,CAAA,OAAI,EAAE,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,EAAE,EAAE;QACtE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE,EAAE,EAAkB;IACzD,IAAI;QACF,OAAO,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,qHAAA,CAAA,UAAO,EAAE,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,EAAE,EAAE;IAC9D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,oBAAoB,EAAE,EAAE,EAAkB;IAC9D,IAAI;QACF,OAAO,MAAM,oHAAA,CAAA,UAAE,CACZ,MAAM,GACN,IAAI,CAAC,qHAAA,CAAA,UAAO,EACZ,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,MAAM,EAAE,KACzB,OAAO,CAAC,CAAA,GAAA,yQAAA,CAAA,MAAG,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,SAAS;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mDAAmD;QACjE,MAAM;IACR;AACF;AAEO,eAAe,YAAY,EAChC,MAAM,EACN,SAAS,EACT,IAAI,EAKL;IACC,IAAI;QACF,MAAM,CAAC,aAAa,GAAG,MAAM,oHAAA,CAAA,UAAE,CAC5B,MAAM,GACN,IAAI,CAAC,qHAAA,CAAA,OAAI,EACT,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,SAAS,EAAE;QAEhC,IAAI,cAAc;YAChB,OAAO,MAAM,oHAAA,CAAA,UAAE,CACZ,MAAM,CAAC,qHAAA,CAAA,OAAI,EACX,GAAG,CAAC;gBAAE,WAAW,SAAS;YAAK,GAC/B,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,SAAS,EAAE,YAAY,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,MAAM,EAAE;QAC9D;QACA,OAAO,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,qHAAA,CAAA,OAAI,EAAE,MAAM,CAAC;YAClC;YACA;YACA,WAAW,SAAS;QACtB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,EAAE,EAAE,EAAkB;IAC3D,IAAI;QACF,OAAO,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,qHAAA,CAAA,OAAI,EAAE,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,MAAM,EAAE;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM;IACR;AACF;AAEO,eAAe,qCAAqC,EACzD,MAAM,EACN,SAAS,EAIV;IACC,IAAI;QACF,OAAO,MAAM,oHAAA,CAAA,UAAE,CACZ,MAAM,CAAC,qHAAA,CAAA,UAAO,EACd,KAAK,CACJ,CAAA,GAAA,6QAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,MAAM,EAAE,SAAS,CAAA,GAAA,6QAAA,CAAA,MAAG,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,SAAS,EAAE;IAE7D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CACX;QAEF,MAAM;IACR;AACF;AAEO,eAAe,wBAAwB,EAC5C,MAAM,EACN,UAAU,EAIX;IACC,IAAI;QACF,OAAO,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,qHAAA,CAAA,OAAI,EAAE,GAAG,CAAC;YAAE;QAAW,GAAG,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,EAAE,EAAE;IACrE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAIO,eAAe,gBAAgB,EAAE,MAAM,EAAsB;IAClE,IAAI;QACF,OAAO,MAAM,oHAAA,CAAA,UAAE,CACZ,MAAM,CAAC;YACN,IAAI,qHAAA,CAAA,MAAG,CAAC,EAAE;YACV,MAAM,qHAAA,CAAA,MAAG,CAAC,IAAI;YACd,WAAW,qHAAA,CAAA,MAAG,CAAC,SAAS;YACxB,WAAW,qHAAA,CAAA,MAAG,CAAC,SAAS;YACxB,QAAQ,qHAAA,CAAA,MAAG,CAAC,MAAM;YAClB,UAAU,qHAAA,CAAA,MAAG,CAAC,QAAQ;YACtB,aAAa,uPAAA,CAAA,MAAG,AAAQ,CAAC;;gCAED,EAAE,qHAAA,CAAA,UAAO,CAAC,MAAM,CAAC;eAClC,EAAE,qHAAA,CAAA,UAAO,CAAC;gBACT,EAAE,qHAAA,CAAA,UAAO,CAAC,KAAK,CAAC,GAAG,EAAE,qHAAA,CAAA,MAAG,CAAC,EAAE,CAAC;cAC9B,EAAE,qHAAA,CAAA,UAAO,CAAC,SAAS,CAAC;;QAE1B,CAAC,CAAC,EAAE,CAAC;YACL,MAAM;gBACJ,QAAQ,qHAAA,CAAA,UAAO,CAAC,MAAM;gBACtB,MAAM,qHAAA,CAAA,UAAO,CAAC,IAAI;gBAClB,SAAS,qHAAA,CAAA,UAAO,CAAC,OAAO;YAC1B;QACF,GACC,IAAI,CAAC,qHAAA,CAAA,MAAG,EACR,QAAQ,CACP,qHAAA,CAAA,YAAS,EACT,CAAA,GAAA,6QAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,YAAS,CAAC,KAAK,EAAE,qHAAA,CAAA,MAAG,CAAC,EAAE,GAAG,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,YAAS,CAAC,MAAM,EAAE,UAEvD,QAAQ,CACP,qHAAA,CAAA,UAAO,EACP,CAAA,GAAA,6QAAA,CAAA,MAAG,AAAD,EACA,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,KAAK,EAAE,qHAAA,CAAA,MAAG,CAAC,EAAE,GACxB,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,MAAM,EAAE,UAGtB,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,MAAG,CAAC,SAAS,EAAE,SAAS,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,YAAS,CAAC,MAAM,EAAE,UACzD,OAAO,CAAC,CAAA,GAAA,yQAAA,CAAA,OAAI,AAAD,EAAE,qHAAA,CAAA,MAAG,CAAC,SAAS;IAC/B,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,OAAO;YAC1B,QAAQ,KAAK,CAAC,6CAA6C;gBACzD,SAAS,MAAM,OAAO;gBACtB,OAAO,MAAM,KAAK;YACpB;QACF;QACA,MAAM;IACR;AACF;AAEO,eAAe,WAAW,EAC/B,EAAE,EACF,MAAM,EAIP;IACC,IAAI;QACF,MAAM,CAAC,QAAQ,GAAG,MAAM,oHAAA,CAAA,UAAE,CACvB,MAAM,CAAC;YACN,IAAI,qHAAA,CAAA,MAAG,CAAC,EAAE;YACV,MAAM,qHAAA,CAAA,MAAG,CAAC,IAAI;YACd,WAAW,qHAAA,CAAA,MAAG,CAAC,SAAS;YACxB,WAAW,qHAAA,CAAA,MAAG,CAAC,SAAS;YACxB,QAAQ,qHAAA,CAAA,MAAG,CAAC,MAAM;YAClB,UAAU,qHAAA,CAAA,MAAG,CAAC,QAAQ;YACtB,WAAW,qHAAA,CAAA,MAAG,CAAC,SAAS;YACxB,aAAa,uPAAA,CAAA,MAAG,AAAQ,CAAC;;gCAED,EAAE,qHAAA,CAAA,UAAO,CAAC,MAAM,CAAC;eAClC,EAAE,qHAAA,CAAA,UAAO,CAAC;gBACT,EAAE,qHAAA,CAAA,UAAO,CAAC,KAAK,CAAC,GAAG,EAAE,qHAAA,CAAA,MAAG,CAAC,EAAE,CAAC;cAC9B,EAAE,qHAAA,CAAA,UAAO,CAAC,SAAS,CAAC;;QAE1B,CAAC,CAAC,EAAE,CAAC;QACP,GACC,IAAI,CAAC,qHAAA,CAAA,MAAG,EACR,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,MAAG,CAAC,EAAE,EAAE;QAEpB,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,yBAAyB;QACzB,IAAI,MAAM,OAAO,CAAC,QAAQ,MAAM,GAAG;YACjC,QAAQ,MAAM,GAAG,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,QAAiB,CAAC;oBACrD,GAAG,KAAK;oBACR,OAAO,MAAM,KAAK,GAChB,OAAO,MAAM,KAAK,KAAK,WAAW,KAAK,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,KAAK,GACrE;gBACN,CAAC;QACH;QAEA,WAAW;QACX,IACE,QAAQ,SAAS,KAAK,UACtB,CAAC,QAAQ,QAAQ,IACjB,CAAE,MAAM,aAAa;YAAE,OAAO;YAAI;QAAO,IACzC;YACA,MAAM,IAAI,MAAM;QAClB;QAEA,eAAe;QACf,MAAM,CAAC,KAAK,GAAG,MAAM,oHAAA,CAAA,UAAE,CACpB,MAAM,GACN,IAAI,CAAC,qHAAA,CAAA,UAAO,EACZ,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,KAAK,EAAE,KAAK,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,MAAM,EAAE;QAEvD,OAAO;YACL,GAAG,OAAO;YACV,MAAM,QAAQ;QAChB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,QAAQ,EAC5B,EAAE,EACF,IAAI,EACJ,MAAM,EACN,MAAM,EAMP;IACC,IAAI;QACF,MAAM,cAAc,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,qHAAA,CAAA,MAAG,EAAE,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,MAAG,CAAC,EAAE,EAAE;QAEjE,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,OAAO,MAAM,oHAAA,CAAA,UAAE,CACZ,MAAM,CAAC,qHAAA,CAAA,MAAG,EACV,GAAG,CAAC;gBACH;gBACA;gBACA,WAAW,IAAI;YACjB,GACC,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,MAAG,CAAC,EAAE,EAAE;QACtB;QAEA,OAAO,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,qHAAA,CAAA,MAAG,EAAE,MAAM,CAAC;YACjC;YACA;YACA;YACA,WAAW;YACX,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAClC,KAAK,EACL,MAAM,EACN,IAAI,EAKL;IACC,IAAI;QACF,OAAO,MAAM,oHAAA,CAAA,UAAE,CACZ,MAAM,CAAC,qHAAA,CAAA,UAAO,EACd,MAAM,CAAC;YACN;YACA;YACA,QAAQ,KAAK,MAAM,IAAI;gBAAE,KAAK;gBAAM,KAAK;YAAM;YAC/C,MAAM,KAAK,IAAI,IAAI;YACnB,SAAS,KAAK,OAAO,IAAI;YACzB,WAAW,IAAI;QACjB,GACC,kBAAkB,CAAC;YAClB,QAAQ;gBAAC,qHAAA,CAAA,UAAO,CAAC,KAAK;gBAAE,qHAAA,CAAA,UAAO,CAAC,MAAM;aAAC;YACvC,KAAK;gBACH,GAAG,IAAI;gBACP,WAAW,IAAI;YACjB;QACF;IACJ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAClC,EAAE,EACF,MAAM,EAIP;IACC,IAAI;QACF,MAAM,CAAC,QAAQ,GAAG,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,qHAAA,CAAA,MAAG,EAAE,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,MAAG,CAAC,EAAE,EAAE;QAE/D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,QAAQ,SAAS,KAAK,QAAQ;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,qHAAA,CAAA,MAAG,EAAE,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,MAAG,CAAC,EAAE,EAAE;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,aAAa,EACjC,KAAK,EACL,MAAM,EAIP;IACC,IAAI;QACF,MAAM,SAAS,MAAM,oHAAA,CAAA,UAAE,CACpB,MAAM,GACN,IAAI,CAAC,qHAAA,CAAA,YAAS,EACd,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,YAAS,CAAC,KAAK,EAAE,QAAQ,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,YAAS,CAAC,MAAM,EAAE;QAE9D,OAAO,OAAO,MAAM,GAAG;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,SAAS,EAC7B,KAAK,EACL,MAAM,EACN,YAAY,EACZ,aAAa,MAAM,EAMpB;IACC,IAAI;QACF,WAAW;QACX,MAAM,CAAC,QAAQ,GAAG,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,qHAAA,CAAA,MAAG,EAAE,KAAK,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,MAAG,CAAC,EAAE,EAAE;QAE/D,IAAI,CAAC,WAAW,QAAQ,SAAS,KAAK,QAAQ;YAC5C,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,MAAM,oHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,qHAAA,CAAA,YAAS,EAAE,MAAM,CAAC;YACvC;YACA,QAAQ;YACR;YACA,WAAW,IAAI;QACjB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/ai/models.ts"], "sourcesContent": ["// Define your models here.\r\n\r\nexport interface ModelCapabilities {\r\n  reasoning: boolean;\r\n  streaming: boolean;\r\n  tools: boolean;\r\n  vision: boolean;\r\n}\r\n\r\nexport interface Model {\r\n  id: string;\r\n  label: string;\r\n  apiIdentifier: string;\r\n  description: string;\r\n  provider: \"geon\" | \"openai\" | \"dify\";\r\n  capabilities: ModelCapabilities;\r\n}\r\n\r\nexport const models: Array<Model> = [\r\n  {\r\n    id: \"Qwen3-14B\",\r\n    label: \"Qwen3 14B\",\r\n    apiIdentifier: \"Qwen/Qwen2.5-14B\",\r\n    description: \"Qwen3 14B 모델입니다.\",\r\n    provider: \"geon\",\r\n    capabilities: {\r\n      reasoning: false,\r\n      streaming: true,\r\n      tools: true,\r\n      vision: false,\r\n    },\r\n  },\r\n  {\r\n    id: \"Qwen3-4B\",\r\n    label: \"Qwen3 (추론)\",\r\n    apiIdentifier: \"Qwen/Qwen3-4B\",\r\n    description: \"Qwen3-4B 추론 모델입니다.\",\r\n    provider: \"geon\",\r\n    capabilities: {\r\n      reasoning: true,\r\n      streaming: true,\r\n      tools: true,\r\n      vision: false,\r\n    },\r\n  },\r\n  // {\r\n  //   id: 'A.X-4-Light',\r\n  //   label: 'A.X-4 Light',\r\n  //   apiIdentifier: 'A.X-4-Light-awq',\r\n  //   description: 'A.X-4 Light 모델입니다.',\r\n  //   provider: 'geon',\r\n  //   capabilities: {\r\n  //     reasoning: false,\r\n  //     streaming: true,\r\n  //     tools: true,\r\n  //     vision: false,\r\n  //   },\r\n  // },\r\n  {\r\n    id: \"gpt-4.1-nano\",\r\n    label: \"GPT 4.1 Nano\",\r\n    apiIdentifier: \"gpt-4.1-nano\",\r\n    description: \"OpenAI의 GPT 4.1 Nano 모델입니다.\",\r\n    provider: \"openai\",\r\n    capabilities: {\r\n      reasoning: false,\r\n      streaming: true,\r\n      tools: true,\r\n      vision: false,\r\n    },\r\n  },\r\n] as const;\r\n\r\nexport const DEFAULT_MODEL_NAME: string = \"Qwen3-4B\";\r\n\r\n// 모델 유틸리티 함수들\r\nexport function getModelById(modelId: string): Model | undefined {\r\n  return models.find((model) => model.id === modelId);\r\n}\r\n\r\nexport function getModelCapabilities(\r\n  modelId: string\r\n): ModelCapabilities | undefined {\r\n  const model = getModelById(modelId);\r\n  return model?.capabilities;\r\n}\r\n\r\nexport function supportsReasoning(modelId: string): boolean {\r\n  const capabilities = getModelCapabilities(modelId);\r\n  return capabilities?.reasoning ?? false;\r\n}\r\n\r\nexport function supportsStreaming(modelId: string): boolean {\r\n  const capabilities = getModelCapabilities(modelId);\r\n  return capabilities?.streaming ?? false;\r\n}\r\n\r\nexport function supportsTools(modelId: string): boolean {\r\n  const capabilities = getModelCapabilities(modelId);\r\n  return capabilities?.tools ?? false;\r\n}\r\n\r\nexport function supportsVision(modelId: string): boolean {\r\n  const capabilities = getModelCapabilities(modelId);\r\n  return capabilities?.vision ?? false;\r\n}\r\n\r\nexport function getModelProvider(\r\n  modelId: string\r\n): \"geon\" | \"openai\" | \"dify\" | undefined {\r\n  const model = getModelById(modelId);\r\n  return model?.provider;\r\n}\r\n\r\nexport function getReasoningDisabledMessage(\r\n  modelId: string\r\n): string | undefined {\r\n  const model = getModelById(modelId);\r\n  if (!model || model.capabilities.reasoning) {\r\n    return undefined;\r\n  }\r\n\r\n  // 모델별 맞춤 메시지\r\n  switch (model.id) {\r\n    case \"gpt-4.1-nano\":\r\n      return \"GPT 4.1 Nano 에서 지원되지 않습니다.\";\r\n    default:\r\n      return \"현재 선택된 모델은 추론 기능을 지원하지 않습니다\";\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;;;;;;;;AAkBpB,MAAM,SAAuB;IAClC;QACE,IAAI;QACJ,OAAO;QACP,eAAe;QACf,aAAa;QACb,UAAU;QACV,cAAc;YACZ,WAAW;YACX,WAAW;YACX,OAAO;YACP,QAAQ;QACV;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,eAAe;QACf,aAAa;QACb,UAAU;QACV,cAAc;YACZ,WAAW;YACX,WAAW;YACX,OAAO;YACP,QAAQ;QACV;IACF;IACA,IAAI;IACJ,uBAAuB;IACvB,0BAA0B;IAC1B,sCAAsC;IACtC,uCAAuC;IACvC,sBAAsB;IACtB,oBAAoB;IACpB,wBAAwB;IACxB,uBAAuB;IACvB,mBAAmB;IACnB,qBAAqB;IACrB,OAAO;IACP,KAAK;IACL;QACE,IAAI;QACJ,OAAO;QACP,eAAe;QACf,aAAa;QACb,UAAU;QACV,cAAc;YACZ,WAAW;YACX,WAAW;YACX,OAAO;YACP,QAAQ;QACV;IACF;CACD;AAEM,MAAM,qBAA6B;AAGnC,SAAS,aAAa,OAAe;IAC1C,OAAO,OAAO,IAAI,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK;AAC7C;AAEO,SAAS,qBACd,OAAe;IAEf,MAAM,QAAQ,aAAa;IAC3B,OAAO,OAAO;AAChB;AAEO,SAAS,kBAAkB,OAAe;IAC/C,MAAM,eAAe,qBAAqB;IAC1C,OAAO,cAAc,aAAa;AACpC;AAEO,SAAS,kBAAkB,OAAe;IAC/C,MAAM,eAAe,qBAAqB;IAC1C,OAAO,cAAc,aAAa;AACpC;AAEO,SAAS,cAAc,OAAe;IAC3C,MAAM,eAAe,qBAAqB;IAC1C,OAAO,cAAc,SAAS;AAChC;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,eAAe,qBAAqB;IAC1C,OAAO,cAAc,UAAU;AACjC;AAEO,SAAS,iBACd,OAAe;IAEf,MAAM,QAAQ,aAAa;IAC3B,OAAO,OAAO;AAChB;AAEO,SAAS,4BACd,OAAe;IAEf,MAAM,QAAQ,aAAa;IAC3B,IAAI,CAAC,SAAS,MAAM,YAAY,CAAC,SAAS,EAAE;QAC1C,OAAO;IACT;IAEA,aAAa;IACb,OAAQ,MAAM,EAAE;QACd,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 1126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/utils.ts"], "sourcesContent": ["import { Chat } from \"@/lib/db/schema\"\r\nimport { CoreMessage, generateId, Message } from \"ai\"\r\nimport { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nimport type { Message as DBMessage } from '@/lib/db/schema';\r\n/**\r\n * Merge tailwind\r\n * @param inputs\r\n */\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport function formatDate(input: string | number | Date): string {\r\n  const date = new Date(input)\r\n  return date.toLocaleDateString('korean', {\r\n    month: 'long',\r\n    day: 'numeric',\r\n    year: 'numeric'\r\n  })\r\n}\r\n\r\ninterface ApplicationError extends Error {\r\n  info: string;\r\n  status: number;\r\n}\r\n\r\nexport const fetcher = async (url: string) => {\r\n  const res = await fetch(url);\r\n\r\n  if (!res.ok) {\r\n    // 401 에러 시 로그인 페이지로 리다이렉트\r\n    if (res.status === 401) {\r\n      // 클라이언트 사이드에서만 리다이렉트 실행\r\n      if (typeof window !== 'undefined') {\r\n        const currentUrl = window.location.pathname + window.location.search;\r\n        window.location.href = `/login?callbackUrl=${encodeURIComponent(currentUrl)}`;\r\n        return; // 리다이렉트 후 함수 종료\r\n      }\r\n    }\r\n\r\n    const error = new Error(\r\n      'An error occurred while fetching the data.'\r\n    ) as ApplicationError;\r\n\r\n    error.info = await res.json();\r\n    error.status = res.status;\r\n\r\n    throw error;\r\n  }\r\n\r\n  return res.json();\r\n};\r\n\r\nexport function generateUUID(): string {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    const r = (Math.random() * 16) | 0;\r\n    const v = c === 'x' ? r : (r & 0x3) | 0x8;\r\n    return v.toString(16);\r\n  });\r\n}\r\n\r\n\r\n\r\nexport function convertToUIMessages(\r\n  messages: Array<DBMessage>,\r\n): Array<Message> {\r\n  // Vercel AI Chatbot 예제 패턴: parts 기반으로 메시지 변환\r\n  return messages.map((message) => ({\r\n    id: message.id,\r\n    parts: message.parts as Message['parts'],\r\n    role: message.role as Message['role'],\r\n    // Note: content will soon be deprecated in @ai-sdk/react\r\n    content: '',\r\n    createdAt: message.createdAt,\r\n    experimental_attachments: (message.attachments as Array<any>) ?? [],\r\n  }));\r\n}\r\n\r\nexport function getMostRecentUserMessage(messages: Array<CoreMessage>) {\r\n  const userMessages = messages.filter((message) => message.role === 'user');\r\n  return userMessages.at(-1);\r\n}\r\n\r\nexport function getMessageIdFromAnnotations(message: Message) {\r\n  if (!message.annotations) return message.id;\r\n\r\n  const [annotation] = message.annotations;\r\n  if (!annotation) return message.id;\r\n\r\n  // @ts-expect-error messageIdFromServer is not defined in MessageAnnotation\r\n  return annotation.messageIdFromServer;\r\n}\r\n\r\n\r\n\r\nexport const defaultSystemMessage = ``\r\nexport const defaultModelId = '2JgxF9fviqd4cezxuT4UAuJCcRP2';\r\nexport const defaultChatbotId = 'chbt_CRNiaMw';\r\nexport const V2ModelId = 'Lk5521ILdWe9t18yi0zcJKU0teE3';\r\nexport const V2ChatbotId = 'chbt_ahiNhfU';\r\n\r\nexport const prunedMessages = (messages: Message[]): Message[] => {\r\n  // 1. 마지막 4개 메시지만 선택\r\n  const recentMessages = messages.slice(-4);\r\n\r\n  console.log('Pruned messages count:', recentMessages.length)\r\n\r\n  // 2. Human-in-the-loop 도구 호출 후 사용자가 tool-result 없이 채팅한 경우 처리\r\n  const processedMessages = recentMessages.map((message, index) => {\r\n    // Assistant 메시지에서 미완료된 tool-invocation 확인 (parts 배열 사용)\r\n    if (message.role === 'assistant' && Array.isArray(message.parts)) {\r\n      const hasIncompleteToolCall = message.parts.some((part: any) =>\r\n        part.type === 'tool-invocation' &&\r\n        part.toolInvocation?.state === 'call' &&\r\n        !part.toolInvocation?.result\r\n      );\r\n\r\n      // 미완료된 tool-invocation이 있고, 다음 메시지가 user 메시지인 경우\r\n      if (hasIncompleteToolCall && index < recentMessages.length - 1) {\r\n        const nextMessage = recentMessages[index + 1];\r\n        if (nextMessage?.role === 'user') {\r\n          // 미완료된 tool-invocation을 제거하고 텍스트 응답만 유지\r\n          const filteredParts = message.parts.filter((part: any) =>\r\n            part.type !== 'tool-invocation' ||\r\n            (part.type === 'tool-invocation' && part.toolInvocation?.result)\r\n          );\r\n\r\n          // 텍스트 응답이 없으면 기본 응답 추가\r\n          if (filteredParts.length === 0 || !filteredParts.some((p: any) => p.type === 'text')) {\r\n            filteredParts.unshift({\r\n              type: 'text',\r\n              text: '요청을 처리하고 있습니다. 추가로 도움이 필요한 것이 있나요?'\r\n            });\r\n          }\r\n\r\n          return { ...message, parts: filteredParts };\r\n        }\r\n      }\r\n\r\n      // 3. 도구 결과에서 불필요한 대용량 필드 제거\r\n      const cleanedParts = message.parts.map((part: any) => {\r\n        if (part.type === 'tool-invocation' && part.toolInvocation?.state === 'result') {\r\n          const toolName = part.toolInvocation?.toolName;\r\n          const result = part.toolInvocation.result;\r\n\r\n          // searchAddress, searchOrigin, searchDestination: geom, buildGeom 필드 제거\r\n          if (['searchAddress', 'searchOrigin', 'searchDestination'].includes(toolName)) {\r\n            if (result && typeof result === 'object' && result.result?.jusoList) {\r\n              // jusoList 배열의 각 항목에서 geom, buildGeom 필드 제거\r\n              const cleanedJusoList = result.result.jusoList.map((item: any) => {\r\n                const { geom, buildGeom, ...cleanedItem } = item;\r\n                return cleanedItem;\r\n              });\r\n\r\n              return {\r\n                ...part,\r\n                toolInvocation: {\r\n                  ...part.toolInvocation,\r\n                  result: {\r\n                    ...result,\r\n                    result: {\r\n                      ...result.result,\r\n                      jusoList: cleanedJusoList\r\n                    }\r\n                  }\r\n                }\r\n              };\r\n            }\r\n          }\r\n\r\n          // searchDirections: sections 필드 제거 (상세 경로 데이터)\r\n          if (toolName === 'searchDirections') {\r\n            if (result && typeof result === 'object' && result.routes) {\r\n              const cleanedRoutes = result.routes.map((route: any) => {\r\n                const { sections, ...cleanedRoute } = route;\r\n                return cleanedRoute;\r\n              });\r\n\r\n              return {\r\n                ...part,\r\n                toolInvocation: {\r\n                  ...part.toolInvocation,\r\n                  result: {\r\n                    ...result,\r\n                    routes: cleanedRoutes\r\n                  }\r\n                }\r\n              };\r\n            }\r\n          }\r\n\r\n          // densityAnalysis: features 필드 제거 (GeoJSON 형상 데이터)\r\n          if (toolName === 'performDensityAnalysis') {\r\n            if (result && typeof result === 'object') {\r\n              const { features, ...cleanedResult } = result;\r\n\r\n              return {\r\n                ...part,\r\n                toolInvocation: {\r\n                  ...part.toolInvocation,\r\n                  result: {\r\n                    ...cleanedResult,\r\n                    // features 배열 크기만 유지 (실제 데이터는 제거)\r\n                    featuresCount: Array.isArray(features) ? features.length : 0\r\n                  }\r\n                }\r\n              };\r\n            }\r\n          }\r\n\r\n        }\r\n        return part;\r\n      });\r\n\r\n      return { ...message, parts: cleanedParts };\r\n    }\r\n\r\n    return message;\r\n  });\r\n\r\n  return processedMessages;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAEA;AACA;;;AAOO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,KAA6B;IACtD,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,UAAU;QACvC,OAAO;QACP,KAAK;QACL,MAAM;IACR;AACF;AAOO,MAAM,UAAU,OAAO;IAC5B,MAAM,MAAM,MAAM,MAAM;IAExB,IAAI,CAAC,IAAI,EAAE,EAAE;QACX,0BAA0B;QAC1B,IAAI,IAAI,MAAM,KAAK,KAAK;YACtB,wBAAwB;YACxB,uCAAmC;;YAInC;QACF;QAEA,MAAM,QAAQ,IAAI,MAChB;QAGF,MAAM,IAAI,GAAG,MAAM,IAAI,IAAI;QAC3B,MAAM,MAAM,GAAG,IAAI,MAAM;QAEzB,MAAM;IACR;IAEA,OAAO,IAAI,IAAI;AACjB;AAEO,SAAS;IACd,OAAO,uCAAuC,OAAO,CAAC,SAAS,CAAC;QAC9D,MAAM,IAAI,AAAC,KAAK,MAAM,KAAK,KAAM;QACjC,MAAM,IAAI,MAAM,MAAM,IAAI,AAAC,IAAI,MAAO;QACtC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF;AAIO,SAAS,oBACd,QAA0B;IAE1B,6CAA6C;IAC7C,OAAO,SAAS,GAAG,CAAC,CAAC,UAAY,CAAC;YAChC,IAAI,QAAQ,EAAE;YACd,OAAO,QAAQ,KAAK;YACpB,MAAM,QAAQ,IAAI;YAClB,yDAAyD;YACzD,SAAS;YACT,WAAW,QAAQ,SAAS;YAC5B,0BAA0B,AAAC,QAAQ,WAAW,IAAmB,EAAE;QACrE,CAAC;AACH;AAEO,SAAS,yBAAyB,QAA4B;IACnE,MAAM,eAAe,SAAS,MAAM,CAAC,CAAC,UAAY,QAAQ,IAAI,KAAK;IACnE,OAAO,aAAa,EAAE,CAAC,CAAC;AAC1B;AAEO,SAAS,4BAA4B,OAAgB;IAC1D,IAAI,CAAC,QAAQ,WAAW,EAAE,OAAO,QAAQ,EAAE;IAE3C,MAAM,CAAC,WAAW,GAAG,QAAQ,WAAW;IACxC,IAAI,CAAC,YAAY,OAAO,QAAQ,EAAE;IAElC,2EAA2E;IAC3E,OAAO,WAAW,mBAAmB;AACvC;AAIO,MAAM,uBAAuB,EAAE;AAC/B,MAAM,iBAAiB;AACvB,MAAM,mBAAmB;AACzB,MAAM,YAAY;AAClB,MAAM,cAAc;AAEpB,MAAM,iBAAiB,CAAC;IAC7B,oBAAoB;IACpB,MAAM,iBAAiB,SAAS,KAAK,CAAC,CAAC;IAEvC,QAAQ,GAAG,CAAC,0BAA0B,eAAe,MAAM;IAE3D,6DAA6D;IAC7D,MAAM,oBAAoB,eAAe,GAAG,CAAC,CAAC,SAAS;QACrD,wDAAwD;QACxD,IAAI,QAAQ,IAAI,KAAK,eAAe,MAAM,OAAO,CAAC,QAAQ,KAAK,GAAG;YAChE,MAAM,wBAAwB,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,OAChD,KAAK,IAAI,KAAK,qBACd,KAAK,cAAc,EAAE,UAAU,UAC/B,CAAC,KAAK,cAAc,EAAE;YAGxB,iDAAiD;YACjD,IAAI,yBAAyB,QAAQ,eAAe,MAAM,GAAG,GAAG;gBAC9D,MAAM,cAAc,cAAc,CAAC,QAAQ,EAAE;gBAC7C,IAAI,aAAa,SAAS,QAAQ;oBAChC,wCAAwC;oBACxC,MAAM,gBAAgB,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAC,OAC1C,KAAK,IAAI,KAAK,qBACb,KAAK,IAAI,KAAK,qBAAqB,KAAK,cAAc,EAAE;oBAG3D,uBAAuB;oBACvB,IAAI,cAAc,MAAM,KAAK,KAAK,CAAC,cAAc,IAAI,CAAC,CAAC,IAAW,EAAE,IAAI,KAAK,SAAS;wBACpF,cAAc,OAAO,CAAC;4BACpB,MAAM;4BACN,MAAM;wBACR;oBACF;oBAEA,OAAO;wBAAE,GAAG,OAAO;wBAAE,OAAO;oBAAc;gBAC5C;YACF;YAEA,4BAA4B;YAC5B,MAAM,eAAe,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC;gBACtC,IAAI,KAAK,IAAI,KAAK,qBAAqB,KAAK,cAAc,EAAE,UAAU,UAAU;oBAC9E,MAAM,WAAW,KAAK,cAAc,EAAE;oBACtC,MAAM,SAAS,KAAK,cAAc,CAAC,MAAM;oBAEzC,wEAAwE;oBACxE,IAAI;wBAAC;wBAAiB;wBAAgB;qBAAoB,CAAC,QAAQ,CAAC,WAAW;wBAC7E,IAAI,UAAU,OAAO,WAAW,YAAY,OAAO,MAAM,EAAE,UAAU;4BACnE,4CAA4C;4BAC5C,MAAM,kBAAkB,OAAO,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gCAClD,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,aAAa,GAAG;gCAC5C,OAAO;4BACT;4BAEA,OAAO;gCACL,GAAG,IAAI;gCACP,gBAAgB;oCACd,GAAG,KAAK,cAAc;oCACtB,QAAQ;wCACN,GAAG,MAAM;wCACT,QAAQ;4CACN,GAAG,OAAO,MAAM;4CAChB,UAAU;wCACZ;oCACF;gCACF;4BACF;wBACF;oBACF;oBAEA,+CAA+C;oBAC/C,IAAI,aAAa,oBAAoB;wBACnC,IAAI,UAAU,OAAO,WAAW,YAAY,OAAO,MAAM,EAAE;4BACzD,MAAM,gBAAgB,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;gCACvC,MAAM,EAAE,QAAQ,EAAE,GAAG,cAAc,GAAG;gCACtC,OAAO;4BACT;4BAEA,OAAO;gCACL,GAAG,IAAI;gCACP,gBAAgB;oCACd,GAAG,KAAK,cAAc;oCACtB,QAAQ;wCACN,GAAG,MAAM;wCACT,QAAQ;oCACV;gCACF;4BACF;wBACF;oBACF;oBAEA,mDAAmD;oBACnD,IAAI,aAAa,0BAA0B;wBACzC,IAAI,UAAU,OAAO,WAAW,UAAU;4BACxC,MAAM,EAAE,QAAQ,EAAE,GAAG,eAAe,GAAG;4BAEvC,OAAO;gCACL,GAAG,IAAI;gCACP,gBAAgB;oCACd,GAAG,KAAK,cAAc;oCACtB,QAAQ;wCACN,GAAG,aAAa;wCAChB,kCAAkC;wCAClC,eAAe,MAAM,OAAO,CAAC,YAAY,SAAS,MAAM,GAAG;oCAC7D;gCACF;4BACF;wBACF;oBACF;gBAEF;gBACA,OAAO;YACT;YAEA,OAAO;gBAAE,GAAG,OAAO;gBAAE,OAAO;YAAa;QAC3C;QAEA,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { geon } from '@/lib/ai';\r\nimport { openai } from '@ai-sdk/openai';\r\nimport { VisibilityType } from '@/components/visibility-selector';\r\nimport { deleteMessagesByChatIdAfterTimestamp, getMessageById, updateChatVisiblityById } from '@/lib/db/queries';\r\nimport { CoreUserMessage, generateText } from 'ai';\r\nimport { cookies } from 'next/headers';\r\n\r\nexport async function saveModelId(model: string) {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set('model-id', model);\r\n}\r\nexport async function saveDevModelId(model: string) {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set('dev-model-id', model);\r\n}\r\n\r\nexport async function generateTitleFromUserMessage({\r\n  message,\r\n}: {\r\n  message: CoreUserMessage;\r\n}) {\r\n  const { text: title } = await generateText({\r\n    model: openai('gpt-4o-mini'),\r\n    system: `\\n\r\n    - you will generate a short title based on the first message a user begins a conversation with\r\n    - ensure it is not more than 80 characters long\r\n    - the title should be a summary of the user's message\r\n    - do not use quotes or colons`,\r\n    prompt: JSON.stringify(message),\r\n  });\r\n\r\n  return title;\r\n}\r\n\r\nexport async function deleteTrailingMessages({ id }: { id: string }) {\r\n  const [message] = await getMessageById({ id });\r\n\r\n  await deleteMessagesByChatIdAfterTimestamp({\r\n    chatId: message.chatId,\r\n    timestamp: message.createdAt,\r\n  });\r\n}\r\n\r\nexport async function updateChatVisibility({\r\n  chatId,\r\n  visibility,\r\n}: {\r\n  chatId: string;\r\n  visibility: VisibilityType;\r\n}) {\r\n  await updateChatVisiblityById({ chatId, visibility });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAGA;AAEA;AACA;AACA;;;;;;;;AAEO,eAAe,YAAY,KAAa;IAC7C,MAAM,cAAc,MAAM,CAAA,GAAA,0OAAA,CAAA,UAAO,AAAD;IAChC,YAAY,GAAG,CAAC,YAAY;AAC9B;AACO,eAAe,eAAe,KAAa;IAChD,MAAM,cAAc,MAAM,CAAA,GAAA,0OAAA,CAAA,UAAO,AAAD;IAChC,YAAY,GAAG,CAAC,gBAAgB;AAClC;AAEO,eAAe,6BAA6B,EACjD,OAAO,EAGR;IACC,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,gPAAA,CAAA,eAAY,AAAD,EAAE;QACzC,OAAO,CAAA,GAAA,gPAAA,CAAA,SAAM,AAAD,EAAE;QACd,QAAQ,CAAC;;;;iCAIoB,CAAC;QAC9B,QAAQ,KAAK,SAAS,CAAC;IACzB;IAEA,OAAO;AACT;AAEO,eAAe,uBAAuB,EAAE,EAAE,EAAkB;IACjE,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;QAAE;IAAG;IAE5C,MAAM,CAAA,GAAA,sHAAA,CAAA,uCAAoC,AAAD,EAAE;QACzC,QAAQ,QAAQ,MAAM;QACtB,WAAW,QAAQ,SAAS;IAC9B;AACF;AAEO,eAAe,qBAAqB,EACzC,MAAM,EACN,UAAU,EAIX;IACC,MAAM,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE;QAAE;QAAQ;IAAW;AACrD;;;IA5CsB;IAIA;IAKA;IAkBA;IASA;;AApCA,0VAAA;AAIA,0VAAA;AAKA,0VAAA;AAkBA,0VAAA;AASA,0VAAA", "debugId": null}}, {"offset": {"line": 1396, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/api/chat/intent-rules.ts"], "sourcesContent": ["// 의도분석 규칙 및 템플릿 정의\n\n// 헬퍼 함수들\nfunction extractBaseKeyword(context: string): string {\n  const input = context.toLowerCase();\n\n  // 주요 키워드 매핑\n  const keywordMap = {\n    '건물': ['건물', '빌딩', '건축물'],\n    '도로': ['도로', '길', '거리'],\n    '상점': ['상점', '매장', '점포'],\n    '학교': ['학교', '교육기관'],\n    '병원': ['병원', '의료기관'],\n    '공원': ['공원', '녹지']\n  };\n\n  for (const [base, variants] of Object.entries(keywordMap)) {\n    if (variants.some(variant => input.includes(variant))) {\n      return base;\n    }\n  }\n\n  // 지역명 제거 후 키워드 추출\n  const regions = ['서울', '부산', '대구', '인천', '광주', '대전', '울산', '세종'];\n  let cleanInput = input;\n  regions.forEach(region => {\n    cleanInput = cleanInput.replace(region, '').trim();\n  });\n\n  return cleanInput.split(' ')[0] || '레이어';\n}\n\nfunction extractAttributeCondition(context: string): string {\n  const input = context.toLowerCase();\n\n  const conditionMap = {\n    '노후화된': '건축년도나 노후화',\n    '오래된': '건축년도나 사용연한',\n    '높은': '높이나 층수',\n    '큰': '규모나 면적',\n    '작은': '규모나 면적',\n    '새로운': '건축년도나 준공년도',\n    '최신': '건축년도나 준공년도'\n  };\n\n  for (const [pattern, condition] of Object.entries(conditionMap)) {\n    if (input.includes(pattern)) {\n      return condition;\n    }\n  }\n\n  return '특정 조건';\n}\n\nfunction extractColorCondition(context: string): string | null {\n  const input = context.toLowerCase();\n  const colors = ['흰색', '노란색', '빨간색', '파란색', '초록색', '검은색'];\n\n  for (const color of colors) {\n    if (input.includes(color)) {\n      return color;\n    }\n  }\n\n  return null;\n}\n\nexport const INTENT_CATEGORIES = {\n  UNSUPPORTED_FEATURE: {\n    priority: 1,\n    keywords: [\"근처\", \"주변\", \"실시간\", \"업로드\", \"전체목록\", \"CSV\", \"파일\", \"데이터 추가\", \"내 데이터\"],\n    description: \"지원하지 않는 기능 요청\"\n  },\n  DENSITY_ANALYSIS: {\n    priority: 2,\n    keywords: [\"밀도\", \"분포\", \"집중도\", \"히트맵\", \"밀집\"],\n    constraints: \"점 타입 레이어에서만 가능\",\n    description: \"공간 밀도 분석\"\n  },\n  LAYER_STYLE: {\n    priority: 3,\n    keywords: [\"색상\", \"색깔\", \"스타일\", \"투명\", \"굵기\", \"파란색\", \"빨간색\", \"노란색\", \"초록색\"],\n    description: \"레이어 스타일 변경\"\n  },\n  LAYER_ADD: {\n    priority: 4,\n    keywords: [\"추가\", \"보여줘\", \"찾아서\", \"검색해서\"],\n    description: \"레이어 추가\",\n    // 속성 기반 요청 패턴 추가\n    attributePatterns: [\n      \"노후화된\", \"오래된\", \"낡은\", \"높은\", \"큰\", \"작은\", \"새로운\", \"최신\",\n      \"~년도 이후\", \"~년도 이전\", \"~이상\", \"~이하\", \"~보다\"\n    ],\n    colorPatterns: [\"흰색\", \"노란색\", \"빨간색\", \"파란색\", \"초록색\", \"검은색\"]\n  },\n  LAYER_REMOVE: {\n    priority: 4,\n    keywords: [\"삭제\", \"제거\", \"없애\"],\n    description: \"레이어 삭제\"\n  },\n  LAYER_FILTER: {\n    priority: 4,\n    keywords: [\"필터\", \"조건\", \"범위\"],\n    description: \"레이어 필터링\"\n  },\n  LAYER_LIST: {\n    priority: 4,\n    keywords: [\"목록\", \"리스트\", \"어떤 레이어\"],\n    description: \"레이어 목록 조회\"\n  },\n  MAP_CONTROL: {\n    priority: 5,\n    keywords: [\"확대\", \"축소\", \"이동\", \"중심\", \"북쪽\", \"남쪽\", \"동쪽\", \"서쪽\"],\n    description: \"지도 조작 및 제어\"\n  },\n  BASEMAP_CHANGE: {\n    priority: 5,\n    keywords: [\"배경지도\", \"기본지도\", \"항공지도\", \"위성지도\"],\n    description: \"배경지도 변경\"\n  },\n  NAVIGATION: {\n    priority: 6,\n    keywords: [\"찾기\", \"검색\", \"길찾기\", \"경로\", \"위치\", \"어디\"],\n    description: \"장소 검색 및 길찾기\"\n  },\n  GENERAL_CONVERSATION: {\n    priority: 7,\n    keywords: [],\n    description: \"일반 대화 및 기타\"\n  }\n} as const;\n\nexport const MESSAGE_GENERATION_RULES = {\n  // 메시지 생성 원칙\n  principles: [\n    \"구체적인 작업 단계 제시\",\n    \"필요한 도구와 순서 명시\",\n    \"제약사항 및 주의사항 포함\",\n    \"내부 프로세스 언급 금지\"\n  ],\n  \n  // 카테고리별 메시지 템플릿\n  templates: {\n    DENSITY_ANALYSIS: (context: string) => \n      `${context}의 밀도분석을 수행하겠습니다. 밀도분석은 점 타입 레이어에서만 가능하므로, 점 타입 레이어를 검색하여 선택한 후 공간적 밀집도를 분석하고 히트맵으로 시각화하겠습니다.`,\n    \n    LAYER_STYLE: (context: string) => \n      `${context} 스타일을 변경하겠습니다. 현재 지도 상태를 확인하여 대상 레이어가 있는지 확인하고, 없으면 레이어를 검색하여 추가한 후 요청된 스타일을 적용하겠습니다.`,\n    \n    LAYER_ADD: (context: string, conversationContext?: string) => {\n      // 대화 컨텍스트에서 워크플로우 상태 분석\n      const hasChooseOptionResult = conversationContext?.includes('chooseOption') ||\n                                   conversationContext?.includes('사용자가 선택한') ||\n                                   conversationContext?.includes('선택된 레이어');\n\n\n\n      // 속성 기반 요청인지 확인\n      const attributePatterns = INTENT_CATEGORIES.LAYER_ADD.attributePatterns || [];\n      const colorPatterns = INTENT_CATEGORIES.LAYER_ADD.colorPatterns || [];\n\n      const hasAttributePattern = attributePatterns.some(pattern =>\n        context.toLowerCase().includes(pattern.toLowerCase())\n      );\n      const hasColorPattern = colorPatterns.some(pattern =>\n        context.toLowerCase().includes(pattern.toLowerCase())\n      );\n\n      // 워크플로우 상태별 메시지 생성\n      if (hasChooseOptionResult && (hasAttributePattern || hasColorPattern)) {\n        // 레이어가 선택된 상태 + 속성 조건이 있는 경우\n\n        const attributeCondition = extractAttributeCondition(context);\n        const colorCondition = extractColorCondition(context);\n\n        let message = `선택한 레이어를 추가하고, ${attributeCondition} 기준으로`;\n\n        if (colorCondition) {\n          message += ` ${colorCondition}으로 표시해야 합니다.`;\n        } else {\n          message += ` 필터링하여 표시해야 합니다.`;\n        }\n\n        message += ` 정확한 기준을 위해 속성 정보를 조회하고 사용자에게 확인을 요청하세요.`;\n\n        return message;\n      }\n\n      if (hasAttributePattern || hasColorPattern) {\n        // 속성 기반 요청이지만 레이어가 아직 선택되지 않은 경우\n        const baseKeyword = extractBaseKeyword(context);\n        const attributeCondition = extractAttributeCondition(context);\n        const colorCondition = extractColorCondition(context);\n\n        let message = `${baseKeyword} 관련 레이어를 검색하고, ${attributeCondition} 기준에 따라 필터링하여`;\n\n        if (colorCondition) {\n          message += ` ${colorCondition}으로 표시해주세요.`;\n        } else {\n          message += ` 지도에 표시해주세요.`;\n        }\n\n        return message;\n      }\n\n      // 일반적인 레이어 추가 요청\n      return `${context} 레이어를 검색하여 지도에 추가하겠습니다. 키워드로 관련 레이어를 찾아 사용자가 선택할 수 있도록 목록을 제공하겠습니다.`;\n    },\n    \n    LAYER_REMOVE: (context: string) => \n      `${context} 레이어를 삭제하겠습니다. 현재 지도에서 해당 레이어를 찾아 제거하겠습니다.`,\n    \n    LAYER_FILTER: (context: string) => \n      `${context} 조건으로 레이어를 필터링하겠습니다. 해당 레이어의 속성 정보를 확인하여 조건에 맞는 데이터만 표시하겠습니다.`,\n    \n    LAYER_LIST: () => \n      `현재 지도에 추가된 레이어 목록을 조회하겠습니다.`,\n    \n    MAP_CONTROL: (context: string) => \n      `지도 ${context} 작업을 수행하겠습니다.`,\n    \n    BASEMAP_CHANGE: (context: string) => \n      `배경지도를 ${context}로 변경하겠습니다. 사용 가능한 배경지도 옵션을 제공하겠습니다.`,\n    \n    NAVIGATION: (context: string) => \n      `${context} 관련 장소 검색 및 길찾기를 수행하겠습니다.`,\n    \n    UNSUPPORTED_FEATURE: (context: string) => \n      `${context} 기능은 현재 지원하지 않습니다. 현재 지원하는 기능들을 안내하고 대안을 제시하겠습니다.`,\n    \n    GENERAL_CONVERSATION: (context: string) => \n      `${context}에 대해 답변드리겠습니다.`\n  }\n} as const;\n\n// 의도 분류 로직\nexport function classifyIntent(userInput: string): string {\n  const input = userInput.toLowerCase();\n  \n  // 우선순위 순으로 키워드 매칭\n  const categories = Object.entries(INTENT_CATEGORIES)\n    .sort(([,a], [,b]) => a.priority - b.priority);\n  \n  for (const [intent, config] of categories) {\n    if (config.keywords.length === 0) continue; // GENERAL_CONVERSATION은 마지막에 처리\n    \n    const hasKeyword = config.keywords.some(keyword => \n      input.includes(keyword.toLowerCase())\n    );\n    \n    if (hasKeyword) {\n      return intent;\n    }\n  }\n  \n  return 'GENERAL_CONVERSATION';\n}\n\n// 메시지 생성 로직\nexport function generateMessage(intent: string, userInput: string, conversationContext?: string): string {\n  const template = MESSAGE_GENERATION_RULES.templates[intent as keyof typeof MESSAGE_GENERATION_RULES.templates];\n\n  if (typeof template === 'function') {\n    return template(userInput, conversationContext);\n  }\n\n  return `사용자 요청을 처리하겠습니다: ${userInput}`;\n}\n\n// 간소화된 프롬프트 생성\nexport function createIntentAnalysisPrompt(): string {\n  return `\n당신은 사용자 요청을 분석하여 다음 중 하나로 분류합니다:\n\n**의도 카테고리:**\n- LAYER_*: 레이어 관련 작업 (추가/삭제/스타일/필터)\n- MAP_*: 지도 조작 (확대/축소/이동/배경지도)\n- NAVIGATION: 장소 검색/길찾기\n- DENSITY_ANALYSIS: 밀도 분석 (점 타입 레이어만 가능)\n- UNSUPPORTED_FEATURE: 지원 불가 기능\n- GENERAL_CONVERSATION: 일반 대화\n\n**지원 불가 키워드:** \"근처\", \"주변\", \"실시간\", \"업로드\", \"전체목록\", \"CSV\", \"파일\"\n\n**워크플로우 상태 인식:**\n- 이전 대화에서 getLayerList, chooseOption 등의 도구 호출 결과를 확인하세요\n- 레이어가 이미 선택된 상태인지, 아직 검색 단계인지 파악하세요\n- 현재 워크플로우 단계에 맞는 구체적인 다음 작업을 지시하세요\n\n**속성 기반 레이어 요청 식별:**\n- 형용사: \"노후화된\", \"높은\", \"오래된\", \"새로운\", \"큰\", \"작은\"\n- 색상 지정: \"흰색으로\", \"노란색으로\", \"빨간색으로\"\n- 이런 패턴이 감지되면 LAYER_ADD로 분류하고 구체적인 필터링 지시 생성\n\n**응답 형식:** {\"intent\": \"CATEGORY\", \"message\": \"작업 지시\"}\n\n**메시지 작성 원칙:**\n- **레이어 선택 완료 + 속성 조건**: \"선택한 레이어를 추가하고, X 기준으로 표시해야 합니다. 속성 정보를 조회하고 사용자에게 확인을 요청하세요.\"\n- **속성 기반 요청 (초기)**: \"X 관련 레이어를 검색하고, Y 기준에 따라 필터링하여 Z로 표시해주세요\"\n- **일반 레이어 요청**: \"X 레이어를 검색하여 지도에 추가하겠습니다\"\n- 구체적인 작업 단계와 도구 사용 계획 제시\n- 제약사항 명시 (예: 밀도분석은 점 타입만 가능)\n\n사용자 요청을 분석하여 적절한 의도로 분류하고 다음 에이전트를 위한 명확한 작업 지시를 생성하세요.\n  `.trim();\n}\n\n// 테스트용 함수 (개발 시에만 사용)\nexport function testMessageGeneration(userInput: string): string {\n  const intent = classifyIntent(userInput);\n  return generateMessage(intent, userInput);\n}\n\n// 예시 테스트\n// console.log(testMessageGeneration(\"서울의 노후화된 건물을 보여줘\"));\n// 예상 결과: \"건물 관련 레이어를 검색하고, 건축년도나 노후화 기준에 따라 필터링하여 지도에 표시해주세요.\"\n\n// console.log(testMessageGeneration(\"서울의 노후화된 건물을 흰색으로 보여줘\"));\n// 예상 결과: \"건물 관련 레이어를 검색하고, 건축년도나 노후화 기준에 따라 필터링하여 흰색으로 표시해주세요.\"\n"], "names": [], "mappings": "AAAA,mBAAmB;AAEnB,SAAS;;;;;;;;;AACT,SAAS,mBAAmB,OAAe;IACzC,MAAM,QAAQ,QAAQ,WAAW;IAEjC,YAAY;IACZ,MAAM,aAAa;QACjB,MAAM;YAAC;YAAM;YAAM;SAAM;QACzB,MAAM;YAAC;YAAM;YAAK;SAAK;QACvB,MAAM;YAAC;YAAM;YAAM;SAAK;QACxB,MAAM;YAAC;YAAM;SAAO;QACpB,MAAM;YAAC;YAAM;SAAO;QACpB,MAAM;YAAC;YAAM;SAAK;IACpB;IAEA,KAAK,MAAM,CAAC,MAAM,SAAS,IAAI,OAAO,OAAO,CAAC,YAAa;QACzD,IAAI,SAAS,IAAI,CAAC,CAAA,UAAW,MAAM,QAAQ,CAAC,WAAW;YACrD,OAAO;QACT;IACF;IAEA,kBAAkB;IAClB,MAAM,UAAU;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAChE,IAAI,aAAa;IACjB,QAAQ,OAAO,CAAC,CAAA;QACd,aAAa,WAAW,OAAO,CAAC,QAAQ,IAAI,IAAI;IAClD;IAEA,OAAO,WAAW,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;AACrC;AAEA,SAAS,0BAA0B,OAAe;IAChD,MAAM,QAAQ,QAAQ,WAAW;IAEjC,MAAM,eAAe;QACnB,QAAQ;QACR,OAAO;QACP,MAAM;QACN,KAAK;QACL,MAAM;QACN,OAAO;QACP,MAAM;IACR;IAEA,KAAK,MAAM,CAAC,SAAS,UAAU,IAAI,OAAO,OAAO,CAAC,cAAe;QAC/D,IAAI,MAAM,QAAQ,CAAC,UAAU;YAC3B,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAAS,sBAAsB,OAAe;IAC5C,MAAM,QAAQ,QAAQ,WAAW;IACjC,MAAM,SAAS;QAAC;QAAM;QAAO;QAAO;QAAO;QAAO;KAAM;IAExD,KAAK,MAAM,SAAS,OAAQ;QAC1B,IAAI,MAAM,QAAQ,CAAC,QAAQ;YACzB,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEO,MAAM,oBAAoB;IAC/B,qBAAqB;QACnB,UAAU;QACV,UAAU;YAAC;YAAM;YAAM;YAAO;YAAO;YAAQ;YAAO;YAAM;YAAU;SAAQ;QAC5E,aAAa;IACf;IACA,kBAAkB;QAChB,UAAU;QACV,UAAU;YAAC;YAAM;YAAM;YAAO;YAAO;SAAK;QAC1C,aAAa;QACb,aAAa;IACf;IACA,aAAa;QACX,UAAU;QACV,UAAU;YAAC;YAAM;YAAM;YAAO;YAAM;YAAM;YAAO;YAAO;YAAO;SAAM;QACrE,aAAa;IACf;IACA,WAAW;QACT,UAAU;QACV,UAAU;YAAC;YAAM;YAAO;YAAO;SAAO;QACtC,aAAa;QACb,iBAAiB;QACjB,mBAAmB;YACjB;YAAQ;YAAO;YAAM;YAAM;YAAK;YAAM;YAAO;YAC7C;YAAU;YAAU;YAAO;YAAO;SACnC;QACD,eAAe;YAAC;YAAM;YAAO;YAAO;YAAO;YAAO;SAAM;IAC1D;IACA,cAAc;QACZ,UAAU;QACV,UAAU;YAAC;YAAM;YAAM;SAAK;QAC5B,aAAa;IACf;IACA,cAAc;QACZ,UAAU;QACV,UAAU;YAAC;YAAM;YAAM;SAAK;QAC5B,aAAa;IACf;IACA,YAAY;QACV,UAAU;QACV,UAAU;YAAC;YAAM;YAAO;SAAS;QACjC,aAAa;IACf;IACA,aAAa;QACX,UAAU;QACV,UAAU;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;QAC1D,aAAa;IACf;IACA,gBAAgB;QACd,UAAU;QACV,UAAU;YAAC;YAAQ;YAAQ;YAAQ;SAAO;QAC1C,aAAa;IACf;IACA,YAAY;QACV,UAAU;QACV,UAAU;YAAC;YAAM;YAAM;YAAO;YAAM;YAAM;SAAK;QAC/C,aAAa;IACf;IACA,sBAAsB;QACpB,UAAU;QACV,UAAU,EAAE;QACZ,aAAa;IACf;AACF;AAEO,MAAM,2BAA2B;IACtC,YAAY;IACZ,YAAY;QACV;QACA;QACA;QACA;KACD;IAED,gBAAgB;IAChB,WAAW;QACT,kBAAkB,CAAC,UACjB,GAAG,QAAQ,4FAA4F,CAAC;QAE1G,aAAa,CAAC,UACZ,GAAG,QAAQ,qFAAqF,CAAC;QAEnG,WAAW,CAAC,SAAiB;YAC3B,wBAAwB;YACxB,MAAM,wBAAwB,qBAAqB,SAAS,mBAC/B,qBAAqB,SAAS,eAC9B,qBAAqB,SAAS;YAI3D,gBAAgB;YAChB,MAAM,oBAAoB,kBAAkB,SAAS,CAAC,iBAAiB,IAAI,EAAE;YAC7E,MAAM,gBAAgB,kBAAkB,SAAS,CAAC,aAAa,IAAI,EAAE;YAErE,MAAM,sBAAsB,kBAAkB,IAAI,CAAC,CAAA,UACjD,QAAQ,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW;YAEpD,MAAM,kBAAkB,cAAc,IAAI,CAAC,CAAA,UACzC,QAAQ,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW;YAGpD,mBAAmB;YACnB,IAAI,yBAAyB,CAAC,uBAAuB,eAAe,GAAG;gBACrE,6BAA6B;gBAE7B,MAAM,qBAAqB,0BAA0B;gBACrD,MAAM,iBAAiB,sBAAsB;gBAE7C,IAAI,UAAU,CAAC,eAAe,EAAE,mBAAmB,KAAK,CAAC;gBAEzD,IAAI,gBAAgB;oBAClB,WAAW,CAAC,CAAC,EAAE,eAAe,YAAY,CAAC;gBAC7C,OAAO;oBACL,WAAW,CAAC,gBAAgB,CAAC;gBAC/B;gBAEA,WAAW,CAAC,wCAAwC,CAAC;gBAErD,OAAO;YACT;YAEA,IAAI,uBAAuB,iBAAiB;gBAC1C,iCAAiC;gBACjC,MAAM,cAAc,mBAAmB;gBACvC,MAAM,qBAAqB,0BAA0B;gBACrD,MAAM,iBAAiB,sBAAsB;gBAE7C,IAAI,UAAU,GAAG,YAAY,eAAe,EAAE,mBAAmB,aAAa,CAAC;gBAE/E,IAAI,gBAAgB;oBAClB,WAAW,CAAC,CAAC,EAAE,eAAe,UAAU,CAAC;gBAC3C,OAAO;oBACL,WAAW,CAAC,YAAY,CAAC;gBAC3B;gBAEA,OAAO;YACT;YAEA,iBAAiB;YACjB,OAAO,GAAG,QAAQ,mEAAmE,CAAC;QACxF;QAEA,cAAc,CAAC,UACb,GAAG,QAAQ,0CAA0C,CAAC;QAExD,cAAc,CAAC,UACb,GAAG,QAAQ,6DAA6D,CAAC;QAE3E,YAAY,IACV,CAAC,2BAA2B,CAAC;QAE/B,aAAa,CAAC,UACZ,CAAC,GAAG,EAAE,QAAQ,aAAa,CAAC;QAE9B,gBAAgB,CAAC,UACf,CAAC,MAAM,EAAE,QAAQ,mCAAmC,CAAC;QAEvD,YAAY,CAAC,UACX,GAAG,QAAQ,yBAAyB,CAAC;QAEvC,qBAAqB,CAAC,UACpB,GAAG,QAAQ,iDAAiD,CAAC;QAE/D,sBAAsB,CAAC,UACrB,GAAG,QAAQ,cAAc,CAAC;IAC9B;AACF;AAGO,SAAS,eAAe,SAAiB;IAC9C,MAAM,QAAQ,UAAU,WAAW;IAEnC,kBAAkB;IAClB,MAAM,aAAa,OAAO,OAAO,CAAC,mBAC/B,IAAI,CAAC,CAAC,GAAE,EAAE,EAAE,GAAE,EAAE,GAAK,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAE/C,KAAK,MAAM,CAAC,QAAQ,OAAO,IAAI,WAAY;QACzC,IAAI,OAAO,QAAQ,CAAC,MAAM,KAAK,GAAG,UAAU,gCAAgC;QAE5E,MAAM,aAAa,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,UACtC,MAAM,QAAQ,CAAC,QAAQ,WAAW;QAGpC,IAAI,YAAY;YACd,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAGO,SAAS,gBAAgB,MAAc,EAAE,SAAiB,EAAE,mBAA4B;IAC7F,MAAM,WAAW,yBAAyB,SAAS,CAAC,OAA0D;IAE9G,IAAI,OAAO,aAAa,YAAY;QAClC,OAAO,SAAS,WAAW;IAC7B;IAEA,OAAO,CAAC,iBAAiB,EAAE,WAAW;AACxC;AAGO,SAAS;IACd,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAiCR,CAAC,CAAC,IAAI;AACR;AAGO,SAAS,sBAAsB,SAAiB;IACrD,MAAM,SAAS,eAAe;IAC9B,OAAO,gBAAgB,QAAQ;AACjC,EAEA,SAAS;CACT,0DAA0D;CAC1D,+DAA+D;CAE/D,+DAA+D;CAC/D,gEAAgE", "debugId": null}}, {"offset": {"line": 1771, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/api-config.ts"], "sourcesContent": ["/**\n * API 설정 및 인증 정보를 관리하는 유틸리티\n */\n\nexport interface ApiConfig {\n  baseUrl: string;\n  headers: {\n    crtfckey: string;\n  };\n  auth: {\n    userId: string;\n    password: string;\n  };\n}\n\n/**\n * API 요청을 위한 설정을 반환합니다.\n * 프론트엔드 로그인과 별개로 백엔드 API 요청 시에는 항상 geonuser 계정을 사용합니다.\n */\nexport const getApiConfig = (): ApiConfig => {\n  const baseUrl =\n    process.env.GEON_API_BASE_URL || \"http://121.163.19.101:14090\";\n\n  // MCP 서버 자체 API 키 사용 (클라이언트 토큰과 별개)\n  const apiKey = process.env.GEON_API_KEY;\n  if (!apiKey) {\n    console.warn(\"GEON_API_KEY가 설정되지 않았습니다.\");\n  }\n\n  // 백엔드 API 요청용 계정 정보 (환경변수에서 가져오거나 기본값 사용)\n  const apiUserId = process.env.GEON_API_USER_ID || 'geonuser';\n  const apiUserPassword = process.env.GEON_API_USER_PASSWORD || 'wavus1234!';\n\n  return {\n    baseUrl,\n    headers: {\n      crtfckey: apiKey || \"\",\n    },\n    auth: {\n      userId: apiUserId,\n      password: apiUserPassword,\n    },\n  };\n};\n\n/**\n * API 요청 시 사용할 URLSearchParams에 인증 정보를 추가합니다.\n */\nexport const addAuthToParams = (params: URLSearchParams, config?: ApiConfig): URLSearchParams => {\n  const apiConfig = config || getApiConfig();\n  \n  // API 키 추가\n  if (apiConfig.headers.crtfckey) {\n    params.append(\"crtfckey\", apiConfig.headers.crtfckey);\n  }\n  \n  return params;\n};\n\n/**\n * API 요청 시 사용할 헤더를 반환합니다.\n */\nexport const getApiHeaders = (config?: ApiConfig): Record<string, string> => {\n  const apiConfig = config || getApiConfig();\n  \n  return {\n    \"Content-Type\": \"application/json\",\n    ...apiConfig.headers,\n  };\n};\n\n/**\n * 백엔드 API 요청에서 사용할 userId를 반환합니다.\n * 프론트엔드 로그인 계정과 관계없이 항상 geonuser를 사용합니다.\n */\nexport const getApiUserId = (config?: ApiConfig): string => {\n  const apiConfig = config || getApiConfig();\n  return apiConfig.auth.userId;\n};\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;AAiBM,MAAM,eAAe;IAC1B,MAAM,UACJ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;IAEnC,oCAAoC;IACpC,MAAM,SAAS,QAAQ,GAAG,CAAC,YAAY;IACvC,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;IACf;IAEA,0CAA0C;IAC1C,MAAM,YAAY,QAAQ,GAAG,CAAC,gBAAgB,IAAI;IAClD,MAAM,kBAAkB,QAAQ,GAAG,CAAC,sBAAsB,IAAI;IAE9D,OAAO;QACL;QACA,SAAS;YACP,UAAU,UAAU;QACtB;QACA,MAAM;YACJ,QAAQ;YACR,UAAU;QACZ;IACF;AACF;AAKO,MAAM,kBAAkB,CAAC,QAAyB;IACvD,MAAM,YAAY,UAAU;IAE5B,WAAW;IACX,IAAI,UAAU,OAAO,CAAC,QAAQ,EAAE;QAC9B,OAAO,MAAM,CAAC,YAAY,UAAU,OAAO,CAAC,QAAQ;IACtD;IAEA,OAAO;AACT;AAKO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,YAAY,UAAU;IAE5B,OAAO;QACL,gBAAgB;QAChB,GAAG,UAAU,OAAO;IACtB;AACF;AAMO,MAAM,eAAe,CAAC;IAC3B,MAAM,YAAY,UAAU;IAC5B,OAAO,UAAU,IAAI,CAAC,MAAM;AAC9B", "debugId": null}}, {"offset": {"line": 1825, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/api/chat/tools.ts"], "sourcesContent": ["import {\r\n  confirmWithCheckbox,\r\n  getUserInput,\r\n  performDensityAnalysis as originalPerformDensityAnalysis,\r\n  searchAddress,\r\n  searchDirections,\r\n} from \"@geon-ai/tools\";\r\nimport { tool, generateObject } from \"ai\";\r\nimport { openai } from \"@ai-sdk/openai\";\r\nimport { z } from \"zod\";\r\nimport { getApiConfig, getApiUserId } from \"@/lib/api-config\";\r\n\r\n// 베이스맵 목록 정의\r\nconst baseMap = {\r\n  eMapBasic: \"바로e맵 일반지도\",\r\n  eMapAIR: \"바로e맵 항공지도\",\r\n  eMapColor: \"바로e맵 색각지도\",\r\n  eMapWhite: \"바로e맵 백지도\",\r\n} as const;\r\n\r\n// 밀도분석 도구 - LLM용 결과 prune 기능 추가\r\nexport const performDensityAnalysis = {\r\n  ...originalPerformDensityAnalysis,\r\n  experimental_toToolResultContent: (result: any) => {\r\n    // LLM에게는 텍스트 형태의 요약된 정보만 전달 (대용량 GeoJSON 데이터 제외)\r\n    const featureCount = result.features?.length || 0;\r\n    const analysisType =\r\n      result.type === \"FeatureCollection\" ? \"공간 밀도 분석\" : \"밀도 분석\";\r\n\r\n    const summary = `${analysisType}이 완료되었습니다. 총 ${featureCount}개의 공간 데이터를 분석하여 밀도 분포를 계산했습니다. 분석 결과가 지도에 히트맵 형태로 시각화되었습니다.`;\r\n\r\n    return [\r\n      {\r\n        type: \"text\" as const,\r\n        text: summary,\r\n      },\r\n    ];\r\n  },\r\n};\r\n\r\n// 주소검색 도구 - LLM용 결과 prune 기능 추가\r\nexport const searchAddressOptimized = {\r\n  ...searchAddress,\r\n  experimental_toToolResultContent: (result: any) => {\r\n    // LLM에게는 핵심 정보만 전달 (상세한 메타데이터 제외)\r\n    if (!result.result?.jusoList?.length) {\r\n      return [\r\n        {\r\n          type: \"text\" as const,\r\n          text: \"검색 결과가 없습니다. 다른 키워드로 다시 검색해보세요.\",\r\n        },\r\n      ];\r\n    }\r\n\r\n    const addresses = result.result.jusoList;\r\n    const addressCount = addresses.length;\r\n\r\n    // 최대 3개까지만 LLM에 전달\r\n    const topAddresses = addresses.slice(0, 3).map((addr: any) => ({\r\n      roadAddr: addr.roadAddr,\r\n      buildName: addr.buildName || addr.poiName,\r\n      buildLo: addr.buildLo, // 경도 (X좌표)\r\n      buildLa: addr.buildLa, // 위도 (Y좌표)\r\n    }));\r\n\r\n    const summary = `주소 검색이 완료되었습니다. 총 ${addressCount}개의 위치를 찾았습니다.`;\r\n\r\n    let addressInfo = topAddresses\r\n      .map(\r\n        (addr: any, index: number) =>\r\n          `${index + 1}. ${addr.roadAddr}${\r\n            addr.buildName ? ` (${addr.buildName})` : \"\"\r\n          } - 좌표: ${addr.buildLo},${addr.buildLa}`\r\n      )\r\n      .join(\"\\n\");\r\n\r\n    if (addressCount > 3) {\r\n      addressInfo += `\\n... 외 ${addressCount - 3}개 추가 결과`;\r\n    }\r\n\r\n    return [\r\n      {\r\n        type: \"text\" as const,\r\n        text: `${summary}\\n\\n${addressInfo}`,\r\n      },\r\n    ];\r\n  },\r\n};\r\n\r\n// 출발지 검색 도구 - searchAddress와 동일한 로직이지만 UI에서 구분 가능\r\nexport const searchOrigin = {\r\n  ...searchAddress,\r\n  description:\r\n    \"출발지 위치를 검색합니다. 경로 탐색의 시작점이 되는 주소나 건물명을 검색하여 좌표 정보를 얻습니다.\",\r\n  experimental_toToolResultContent: (result: any) => {\r\n    if (!result.result?.jusoList?.length) {\r\n      return [\r\n        {\r\n          type: \"text\" as const,\r\n          text: \"출발지를 찾을 수 없습니다. 다른 키워드로 다시 검색해보세요.\",\r\n        },\r\n      ];\r\n    }\r\n\r\n    const addresses = result.result.jusoList;\r\n    const addressCount = addresses.length;\r\n    const topAddresses = addresses.slice(0, 3).map((addr: any) => ({\r\n      roadAddr: addr.roadAddr,\r\n      buildName: addr.buildName || addr.poiName,\r\n      buildLo: addr.buildLo,\r\n      buildLa: addr.buildLa,\r\n    }));\r\n\r\n    const summary = `출발지 검색이 완료되었습니다. 총 ${addressCount}개의 위치를 찾았습니다.`;\r\n    let addressInfo = topAddresses\r\n      .map(\r\n        (addr: any, index: number) =>\r\n          `${index + 1}. ${addr.roadAddr}${\r\n            addr.buildName ? ` (${addr.buildName})` : \"\"\r\n          } - 좌표: ${addr.buildLo},${addr.buildLa}`\r\n      )\r\n      .join(\"\\n\");\r\n\r\n    if (addressCount > 3) {\r\n      addressInfo += `\\n... 외 ${addressCount - 3}개 추가 결과`;\r\n    }\r\n\r\n    return [\r\n      {\r\n        type: \"text\" as const,\r\n        text: `${summary}\\n\\n${addressInfo}`,\r\n      },\r\n    ];\r\n  },\r\n};\r\n\r\n// 목적지 검색 도구 - searchAddress와 동일한 로직이지만 UI에서 구분 가능\r\nexport const searchDestination = {\r\n  ...searchAddress,\r\n  description:\r\n    \"목적지 위치를 검색합니다. 경로 탐색의 도착점이 되는 주소나 건물명을 검색하여 좌표 정보를 얻습니다.\",\r\n  experimental_toToolResultContent: (result: any) => {\r\n    if (!result.result?.jusoList?.length) {\r\n      return [\r\n        {\r\n          type: \"text\" as const,\r\n          text: \"목적지를 찾을 수 없습니다. 다른 키워드로 다시 검색해보세요.\",\r\n        },\r\n      ];\r\n    }\r\n\r\n    const addresses = result.result.jusoList;\r\n    const addressCount = addresses.length;\r\n    const topAddresses = addresses.slice(0, 3).map((addr: any) => ({\r\n      roadAddr: addr.roadAddr,\r\n      buildName: addr.buildName || addr.poiName,\r\n      buildLo: addr.buildLo,\r\n      buildLa: addr.buildLa,\r\n    }));\r\n\r\n    const summary = `목적지 검색이 완료되었습니다. 총 ${addressCount}개의 위치를 찾았습니다.`;\r\n    let addressInfo = topAddresses\r\n      .map(\r\n        (addr: any, index: number) =>\r\n          `${index + 1}. ${addr.roadAddr}${\r\n            addr.buildName ? ` (${addr.buildName})` : \"\"\r\n          } - 좌표: ${addr.buildLo},${addr.buildLa}`\r\n      )\r\n      .join(\"\\n\");\r\n\r\n    if (addressCount > 3) {\r\n      addressInfo += `\\n... 외 ${addressCount - 3}개 추가 결과`;\r\n    }\r\n\r\n    return [\r\n      {\r\n        type: \"text\" as const,\r\n        text: `${summary}\\n\\n${addressInfo}`,\r\n      },\r\n    ];\r\n  },\r\n};\r\n\r\n// 길찾기 도구 - LLM용 결과 prune 기능 추가 + avoid 파라미터 제거\r\nexport const searchDirectionsOptimized = {\r\n  ...searchDirections,\r\n  execute: async (params: any, options: any) => {\r\n    // avoid 파라미터 제거 (API 오류 방지)\r\n    const { avoid, ...cleanParams } = params;\r\n    console.log(\"searchDirections - avoid 파라미터 제거됨:\", avoid);\r\n    return searchDirections.execute(cleanParams, options);\r\n  },\r\n  experimental_toToolResultContent: (result: any) => {\r\n    // LLM에게는 핵심 정보만 전달 (상세한 경로 데이터 제외)\r\n\r\n    // 에러 처리\r\n    if (result.error) {\r\n      return [\r\n        {\r\n          type: \"text\" as const,\r\n          text: `경로 탐색 실패: ${result.error}`,\r\n        },\r\n      ];\r\n    }\r\n\r\n    // routes 배열에서 첫 번째 경로의 정보 추출\r\n    if (!result.routes || !result.routes.length) {\r\n      return [\r\n        {\r\n          type: \"text\" as const,\r\n          text: \"경로를 찾을 수 없습니다. 출발지와 목적지를 확인해주세요.\",\r\n        },\r\n      ];\r\n    }\r\n\r\n    const route = result.routes[0];\r\n\r\n    // result_code로 성공/실패 판단\r\n    if (route.result_code !== 0) {\r\n      return [\r\n        {\r\n          type: \"text\" as const,\r\n          text: `경로 탐색 실패: ${\r\n            route.result_msg || \"알 수 없는 오류가 발생했습니다.\"\r\n          }`,\r\n        },\r\n      ];\r\n    }\r\n\r\n    // 성공한 경우 핵심 정보만 추출\r\n    const summary = route.summary;\r\n    const distance = summary?.distance\r\n      ? `${(summary.distance / 1000).toFixed(1)}km`\r\n      : \"정보 없음\";\r\n    const duration = summary?.duration\r\n      ? `${Math.floor(summary.duration / 60)}분`\r\n      : \"정보 없음\";\r\n    const taxiFare = summary?.fare?.taxi\r\n      ? `${summary.fare.taxi.toLocaleString()}원`\r\n      : \"정보 없음\";\r\n    const tollFare = summary?.fare?.toll\r\n      ? `${summary.fare.toll.toLocaleString()}원`\r\n      : \"무료\";\r\n\r\n    // 출발지/목적지 정보\r\n    const originName = result.origin?.name || \"출발지\";\r\n    const destinationName = result.destination?.name || \"목적지\";\r\n\r\n    const routeInfo = `경로 탐색이 완료되었습니다.\r\n\r\n📍 ${originName} → ${destinationName}\r\n🚗 거리: ${distance}\r\n⏱️ 소요시간: ${duration}\r\n💰 예상 택시요금: ${taxiFare}\r\n🛣️ 통행료: ${tollFare}\r\n\r\n경로가 지도에 자동으로 표시되었습니다.`;\r\n\r\n    return [\r\n      {\r\n        type: \"text\" as const,\r\n        text: routeInfo,\r\n      },\r\n    ];\r\n  },\r\n};\r\n\r\n// 커스텀 베이스맵 도구들\r\nexport const getBasemapList = tool({\r\n  description: `사용 가능한 배경지도 목록을 조회합니다.\r\n  반환되는 배경지도 목록:\r\n  - eMapBasic: 바로e맵 일반지도 (도로, 건물, 지형지물 포함)\r\n  - eMapAIR: 바로e맵 항공지도 (위성 항공사진)\r\n  - eMapColor: 바로e맵 색각지도 (색상 대비 강화)\r\n  - eMapWhite: 바로e맵 백지도 (깔끔한 흰색 배경)`,\r\n  parameters: z.object({}),\r\n  execute: async () => {\r\n    return {\r\n      basemaps: Object.entries(baseMap).map(([id, name]) => ({\r\n        id,\r\n        name,\r\n        displayName: name,\r\n      })),\r\n    };\r\n  },\r\n});\r\n\r\nexport const changeBasemap = tool({\r\n  description: `배경지도를 변경합니다.\r\n  사용 가능한 배경지도 ID:\r\n  - eMapBasic: 일반지도 (기본 지도)\r\n  - eMapAIR: 항공지도 (위성지도)\r\n  - eMapColor: 색각지도 (색상 대비)\r\n  - eMapWhite: 백지도 (심플한 배경)\r\n\r\n  키워드 매칭:\r\n  - \"위성지도\", \"항공지도\" → eMapAIR\r\n  - \"일반지도\", \"기본지도\" → eMapBasic\r\n  - \"색각지도\", \"컬러지도\" → eMapColor\r\n  - \"백지도\", \"흰지도\" → eMapWhite`,\r\n  parameters: z.object({\r\n    basemapId: z\r\n      .enum([\"eMapBasic\", \"eMapAIR\", \"eMapColor\", \"eMapWhite\"])\r\n      .describe(\r\n        \"변경할 배경지도의 ID (eMapBasic, eMapAIR, eMapColor, eMapWhite 중 하나)\"\r\n      ),\r\n  }),\r\n  execute: async ({ basemapId }) => {\r\n    const basemapName = baseMap[basemapId];\r\n\r\n    if (!basemapName) {\r\n      return {\r\n        error: `지원하지 않는 배경지도 ID입니다: ${basemapId}`,\r\n        availableBasemaps: Object.keys(baseMap),\r\n      };\r\n    }\r\n\r\n    return {\r\n      basemap: basemapId,\r\n      basemapId: basemapId,\r\n      basemapName: basemapName,\r\n      success: true,\r\n      message: `배경지도가 ${basemapName}로 변경되었습니다.`,\r\n    };\r\n  },\r\n});\r\n\r\nexport const setMapCenter = tool({\r\n  description: `지도의 중심점을 특정 좌표로 이동합니다.\r\n\r\n  사용 예시:\r\n  - 특정 좌표로 이동: \"지도를 127.027926, 37.497175로 이동\"\r\n  - 상대적 이동: \"지도를 동쪽으로 500m 이동\", \"북쪽으로 1km 이동\"\r\n  - 방향별 이동: \"위로\", \"아래로\", \"왼쪽으로\", \"오른쪽으로\"`,\r\n  parameters: z.object({\r\n    longitude: z.number().describe(\"경도 (X좌표)\"),\r\n    latitude: z.number().describe(\"위도 (Y좌표)\"),\r\n    moveType: z\r\n      .enum([\"absolute\", \"relative\"])\r\n      .describe(\"이동 타입: absolute(절대좌표), relative(상대이동)\")\r\n      .optional()\r\n      .default(\"absolute\"),\r\n    description: z.string().describe(\"이동에 대한 설명\").optional(),\r\n  }),\r\n  execute: async ({ longitude, latitude, moveType, description }) => {\r\n    try {\r\n      return {\r\n        success: true,\r\n        center: [longitude, latitude],\r\n        moveType,\r\n        message:\r\n          description ||\r\n          `지도 중심점이 경도 ${longitude}, 위도 ${latitude}로 이동되었습니다.`,\r\n      };\r\n    } catch (error: any) {\r\n      return {\r\n        success: false,\r\n        error: `지도 중심점 이동 실패: ${error.message}`,\r\n      };\r\n    }\r\n  },\r\n});\r\n\r\nexport const setMapZoom = tool({\r\n  description: `지도의 확대/축소 레벨을 조정합니다.\r\n\r\n  확대/축소 레벨:\r\n  - 1-5: 국가/대륙 레벨 (매우 넓은 범위)\r\n  - 6-10: 지역/도시 레벨 (넓은 범위)\r\n  - 11-15: 구/동 레벨 (중간 범위)\r\n  - 16-20: 건물/도로 레벨 (상세 범위)\r\n\r\n  사용 예시:\r\n  - \"지도 확대해줘\" → zoomDirection: \"in\", zoomLevel: 2, zoomType: \"relative\"\r\n  - \"더 넓게 보여줘\" → zoomDirection: \"out\", zoomLevel: 2, zoomType: \"relative\"\r\n  - \"최대한 확대\" → zoomLevel: 18, zoomType: \"absolute\"\r\n  - \"전체 보기\" → zoomLevel: 2, zoomType: \"absolute\"\r\n`,\r\n  parameters: z.object({\r\n    zoomLevel: z\r\n      .number()\r\n      .optional()\r\n      .describe(\"확대/축소 레벨 또는 변경량 (1-20)\"),\r\n    zoomType: z\r\n      .enum([\"absolute\", \"relative\"])\r\n      .describe(\"확대/축소 타입: absolute(절대레벨), relative(상대변경)\")\r\n      .optional()\r\n      .default(\"relative\"),\r\n    zoomDirection: z\r\n      .enum([\"in\", \"out\"])\r\n      .describe(\"확대/축소 방향: in(확대), out(축소)\"),\r\n    description: z.string().describe(\"확대/축소에 대한 설명\").optional(),\r\n  }),\r\n  execute: async ({ zoomLevel = 2, zoomType, zoomDirection, description }) => {\r\n    try {\r\n      return {\r\n        success: true,\r\n        zoom: zoomLevel,\r\n        zoomType,\r\n        zoomDirection,\r\n        message:\r\n          description ||\r\n          `지도 ${zoomDirection === \"in\" ? \"확대\" : \"축소\"} 레벨이 ${\r\n            zoomType === \"relative\" ? \"변경\" : \"설정\"\r\n          }되었습니다.`,\r\n      };\r\n    } catch (error: any) {\r\n      return {\r\n        success: false,\r\n        error: `지도 확대/축소 실패: ${error.message}`,\r\n      };\r\n    }\r\n  },\r\n});\r\n\r\nexport const moveMapByDirection = tool({\r\n  description: `지도를 특정 방향으로 이동합니다.\r\n\r\n  지원하는 방향:\r\n  - north, up, 위, 북쪽: 북쪽으로 이동\r\n  - south, down, 아래, 남쪽: 남쪽으로 이동\r\n  - east, right, 오른쪽, 동쪽: 동쪽으로 이동\r\n  - west, left, 왼쪽, 서쪽: 서쪽으로 이동\r\n\r\n  거리 형식:\r\n  - \"500m\", \"1km\", \"2000m\" 등의 형태로 입력\r\n  - 단위가 없으면 미터(m)로 간주\r\n  - 기본값: \"500m\"\r\n\r\n  좌표계 지원:\r\n  - EPSG:5186 (Korea 2000 / Central Belt 2010): 미터 단위 직접 계산\r\n  - 정확한 거리 이동을 위해 투영 좌표계 사용`,\r\n  parameters: z.object({\r\n    direction: z\r\n      .enum([\"north\", \"south\", \"east\", \"west\", \"up\", \"down\", \"left\", \"right\"])\r\n      .describe(\"이동 방향\"),\r\n    distance: z\r\n      .string()\r\n      .describe(\"이동 거리 (예: '500m', '1km', '2000m')\")\r\n      .optional()\r\n      .default(\"500m\"),\r\n  }),\r\n  execute: async ({ direction, distance }) => {\r\n    try {\r\n      // 거리 문자열 파싱 (예: \"500m\", \"1km\", \"2000\")\r\n      const parseDistance = (distanceStr: string): number => {\r\n        const match = distanceStr.match(/^(\\d+(?:\\.\\d+)?)\\s*(m|km)?$/i);\r\n        if (!match) {\r\n          throw new Error(`잘못된 거리 형식: ${distanceStr}`);\r\n        }\r\n\r\n        const value = parseFloat(match[1]);\r\n        const unit = match[2]?.toLowerCase() || 'm';\r\n\r\n        return unit === 'km' ? value * 1000 : value;\r\n      };\r\n\r\n      const distanceInMeters = parseDistance(distance);\r\n      console.log(`moveMapByDirection: ${direction}, ${distance} -> ${distanceInMeters}m`);\r\n\r\n      // EPSG:5186 좌표계에서는 미터 단위로 직접 계산\r\n      // 이 좌표계는 한국 중부 지역에 최적화된 투영 좌표계로 미터 단위 사용\r\n      let deltaX = 0; // 동서 방향 (X축)\r\n      let deltaY = 0; // 남북 방향 (Y축)\r\n\r\n      switch (direction) {\r\n        case \"north\":\r\n        case \"up\":\r\n          deltaY = distanceInMeters; // 북쪽으로 이동 (Y 증가)\r\n          break;\r\n        case \"south\":\r\n        case \"down\":\r\n          deltaY = -distanceInMeters; // 남쪽으로 이동 (Y 감소)\r\n          break;\r\n        case \"east\":\r\n        case \"right\":\r\n          deltaX = distanceInMeters; // 동쪽으로 이동 (X 증가)\r\n          break;\r\n        case \"west\":\r\n        case \"left\":\r\n          deltaX = -distanceInMeters; // 서쪽으로 이동 (X 감소)\r\n          break;\r\n      }\r\n\r\n      const directionNames = {\r\n        north: \"북쪽\",\r\n        south: \"남쪽\",\r\n        east: \"동쪽\",\r\n        west: \"서쪽\",\r\n        up: \"위쪽\",\r\n        down: \"아래쪽\",\r\n        left: \"왼쪽\",\r\n        right: \"오른쪽\",\r\n      };\r\n\r\n      return {\r\n        success: true,\r\n        direction,\r\n        deltaX, // EPSG:5186에서는 X, Y 좌표 사용\r\n        deltaY,\r\n        distance: distanceInMeters,\r\n        coordinateSystem: \"EPSG:5186\",\r\n        message: `지도가 ${directionNames[direction]}으로 ${distance} 이동되었습니다.`,\r\n      };\r\n    } catch (error: any) {\r\n      return {\r\n        success: false,\r\n        error: `지도 방향 이동 실패: ${error.message}`,\r\n      };\r\n    }\r\n  },\r\n});\r\n\r\n\r\n\r\nexport const chooseOption = tool({\r\n  description:\r\n    \"사용자에게 여러 선택지 중 하나를 고르도록 요청합니다. 결과는 options 배열의 값 중 하나입니다.\",\r\n  parameters: z.object({\r\n    message: z.string().describe(\"사용자에게 보여줄 안내 문구\"),\r\n    options: z\r\n      .array(\r\n        z\r\n          .string()\r\n          .describe(\r\n            \"사용자에게 표시될 텍스트. 반드시 'key|value' 형태로 제공하세요.\"\r\n          )\r\n      )\r\n      .describe(\"사용자가 선택할 수 있는 옵션 문자열 배열\"),\r\n  }),\r\n});\r\n\r\nexport const getLayerList = tool({\r\n  description: `레이어 목록을 검색합니다.\r\n**레이어 타입 매핑 (lyrTySeCode): 명시하지 않는 경우 반드시 값을 비워둡니다**\r\n`,\r\n  parameters: z.object({\r\n    userId: z.string().describe(\"사용자 ID\"),\r\n    layerName: z.string().optional().describe(\"레이어 이름\"),\r\n    lyrTySeCode: z\r\n      .string()\r\n      .optional()\r\n      .describe(\"레이어 유형 코드 ('1': 점, '2': 선, '3': 면, 비어있는 경우 전체)\"),\r\n    holdDataSeCode: z\r\n      .string()\r\n      .optional()\r\n      .describe(\r\n        \"데이터 구분 코드 ('0': 전체, '1': 사용자, '2': 공유, '9': 국가 (기본값 '0'))\"\r\n      ),\r\n    pageIndex: z\r\n      .string()\r\n      .optional()\r\n      .describe(\"조회할 페이지 인덱스 (기본값 '1')\"),\r\n    pageSize: z\r\n      .string()\r\n      .optional()\r\n      .describe(\"한 페이지당 조회할 레이어 수 (기본값 '10')\"),\r\n  }),\r\n  async execute({\r\n    userId,\r\n    layerName = \"\",\r\n    lyrTySeCode = \"\",\r\n    holdDataSeCode = \"0\",\r\n    pageIndex = \"1\",\r\n    pageSize = \"10\",\r\n  }) {\r\n    try {\r\n      const config = getApiConfig();\r\n\r\n      const params = new URLSearchParams({\r\n        userId: getApiUserId(config), // 항상 geonuser 계정 사용\r\n        holdDataSeCode,\r\n        pageIndex,\r\n        pageSize,\r\n      });\r\n\r\n      // layerName이 있을 경우에만 searchTxt 파라미터 추가\r\n      if (layerName && layerName.trim() !== \"\") {\r\n        params.append(\"searchTxt\", layerName.trim());\r\n      }\r\n\r\n      if (lyrTySeCode && lyrTySeCode.trim() !== \"\") {\r\n        params.append(\"lyrTySeCode\", lyrTySeCode.trim());\r\n      }\r\n\r\n      // 인증 정보 추가\r\n      if (config.headers.crtfckey) {\r\n        params.append(\"crtfckey\", config.headers.crtfckey);\r\n      }\r\n\r\n      const response = await fetch(\r\n        `${config.baseUrl}/smt/layer/info/list?${params.toString()}`,\r\n        {\r\n          method: \"GET\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            ...config.headers,\r\n          },\r\n        }\r\n      );\r\n\r\n      if (!response.ok) {\r\n        response.json().then((data) => {\r\n          console.error(\"API request failed with data\", data);\r\n        });\r\n        throw new Error(`API request failed with status ${response.status}`);\r\n      }\r\n\r\n      const data = (await response.json()) as any;\r\n\r\n      if (!data || !data.result) {\r\n        return { error: \"레이어 목록 조회 실패: 응답 데이터가 없습니다.\" };\r\n      }\r\n\r\n      return data;\r\n    } catch (error: any) {\r\n      return { error: `레이어 목록 조회 실패: ${error.message}` };\r\n    }\r\n  },\r\n});\r\n\r\n// 레이어 스타일 업데이트 도구\r\nexport const updateLayerStyle = tool({\r\n  description: `레이어의 시각적 스타일을 변경합니다. 레이어 타입(점/선/면)에 따라 적절한 스타일이 적용됩니다.\r\n\r\n**지원하는 스타일 속성:**\r\n- color: 기본 색상 (hex 코드, 예: \"#FF0000\") - 모든 타입\r\n- fillOpacity: 채우기 투명도 (0.0-1.0) - 점, 면 타입\r\n- strokeColor: 윤곽선 색상 (hex 코드) - 모든 타입\r\n- strokeWidth: 윤곽선 두께 (픽셀) - 모든 타입\r\n- radius: 점 크기 (픽셀) - 점 타입만\r\n- width: 선 두께 (픽셀) - 선 타입만\r\n- symbol: 심볼 타입 (점 타입만) - \"circle\", \"square\", \"triangle\", \"star\", \"cross\", \"x\"\r\n\r\n**레이어 타입별 적용:**\r\n- 점(Point) 레이어: color, fillOpacity, strokeColor, strokeWidth, radius, symbol\r\n- 선(Line) 레이어: color, strokeColor, strokeWidth, width\r\n- 면(Polygon) 레이어: color, fillOpacity, strokeColor(윤곽선), strokeWidth(윤곽선)\r\n\r\n**사용 예시:**\r\n- \"빨간색으로 바꿔줘\" → color: \"#FF0000\"\r\n- \"투명하게 해줘\" → fillOpacity: 0.3\r\n- \"윤곽선을 두껍게\" → strokeWidth: 3\r\n- \"크기를 키워줘\" → radius: 10 (점) 또는 width: 5 (선)\r\n- \"별 모양으로 바꿔줘\" → symbol: \"star\"\r\n- \"사각형으로 바꿔줘\" → symbol: \"square\"\r\n- \"십자 모양으로 해줘\" → symbol: \"cross\"`,\r\n  parameters: z.object({\r\n    layerId: z.string().describe(\"스타일을 변경할 레이어 ID\"),\r\n    color: z.string().optional().describe(\"기본 색상 (hex 코드, 예: #FF0000)\"),\r\n    fillOpacity: z.number().min(0).max(1).optional().describe(\"채우기 투명도 (0.0-1.0)\"),\r\n    strokeColor: z.string().optional().describe(\"윤곽선 색상 (hex 코드)\"),\r\n    strokeWidth: z.number().min(0).max(20).optional().describe(\"윤곽선 두께 (픽셀)\"),\r\n    radius: z.number().min(1).max(50).optional().describe(\"점 크기 (픽셀, 점 타입만)\"),\r\n    width: z.number().min(1).max(20).optional().describe(\"선 두께 (픽셀, 선 타입만)\"),\r\n    symbol: z.enum([\"circle\", \"square\", \"triangle\", \"star\", \"cross\", \"x\"]).optional().describe(\"심볼 타입 (점 타입만): circle(원), square(사각형), triangle(삼각형), star(별), cross(십자), x(X자)\"),\r\n    description: z.string().optional().describe(\"변경 사항에 대한 설명\")\r\n  }),\r\n  async execute({ layerId, color, fillOpacity, strokeColor, strokeWidth, radius, width, symbol, description }) {\r\n    try {\r\n      // 스타일 객체 구성\r\n      const styleUpdate: any = {};\r\n\r\n      if (color) styleUpdate.color = color;\r\n      if (fillOpacity !== undefined) styleUpdate.fillOpacity = fillOpacity;\r\n      if (strokeColor) styleUpdate.strokeColor = strokeColor;\r\n      if (strokeWidth !== undefined) styleUpdate.strokeWidth = strokeWidth;\r\n      if (radius !== undefined) styleUpdate.radius = radius;\r\n      if (width !== undefined) styleUpdate.width = width;\r\n      if (symbol) styleUpdate.symbol = symbol;\r\n\r\n      return {\r\n        layerId,\r\n        styleUpdate,\r\n        description: description || \"레이어 스타일이 업데이트되었습니다.\",\r\n        success: true\r\n      };\r\n    } catch (error: any) {\r\n      return {\r\n        error: `스타일 업데이트 실패: ${error.message}`,\r\n        success: false\r\n      };\r\n    }\r\n  },\r\n});\r\n\r\n// 유형별 스타일 생성 도구 (단순화된 파라미터)\r\nexport const generateCategoricalStyle = tool({\r\n  description: `레이어에 여러 조건별 스타일을 적용합니다. 사용자가 \"A는 빨간색, B는 파란색\" 같은 요청을 할 때 사용합니다.\r\nattributes 매개변수로 filter 조건에 사용할 수 있는 속성 정보를 제공해야 합니다.\r\n예시: \r\n`,\r\n  parameters: z.object({\r\n    layerId: z.string().describe(\"스타일을 적용할 레이어 ID\"),\r\n    lyrNm: z.string().describe(\"레이어이름\"),\r\n    attributes: z.string().describe(\"🚨 필수: 속성명과 설명을 모두 포함한 형태로 제공 🚨 - 형식: 'a17(건폐율), a13(사용승인일자)' 또는 'c1(주소), c2(건물명)' - 절대 'a17'처럼 속성명만 제공하지 마세요\"),\r\n    userInstruction: z.string().describe(\"사용자의 자연어 스타일링 요청 (예: '용산구는 노란색, 강남구는 파란색, 나머지는 회색으로', '벽돌구조이면서 1990년도 이전인 건물은 빨간색으로')\")\r\n  }),\r\n  async execute({ layerId, lyrNm, attributes, userInstruction }) {\r\n    try {\r\n      console.log(\"generateCategoricalStyle called with:\", {\r\n        layerId,\r\n        lyrNm,\r\n        attributes,\r\n        userInstruction\r\n      });\r\n\r\n      // generateObject를 사용해서 userInstruction에서 바로 processedStyleRules 생성\r\n      const { object: processedStyleRules } = await generateObject({\r\n        model: openai(\"gpt-4.1-nano\", { structuredOutputs: true }),\r\n        temperature: 0,\r\n        schema: z.object({\r\n          styleRules: z.array(z.object({\r\n            description: z.string().describe(\"규칙 설명\"),\r\n            color: z.string().describe(\"HEX 색상 코드 (예: #FF0000, #0000FF, #00FF00)\"),\r\n            conditions: z.array(z.object({\r\n              attributeName: z.string().describe(\"속성명 - 반드시 attributes 매개변수에서 제공된 속성명만 사용\"),\r\n              condition: z.enum([\"like\", \"equal\", \"greater\", \"less\", \"greaterEqual\", \"lessEqual\", \"default\"]).describe(\"조건 타입\"),\r\n              value: z.string().describe(\"조건 값\")\r\n            })).describe(\"조건 배열 (복수 조건 지원). default 조건인 경우 빈 배열\"),\r\n            logicalOperator: z.enum([\"AND\", \"OR\"]).describe(\"복수 조건 간 논리 연산자 (기본값: AND)\")\r\n          }))\r\n        }),\r\n        prompt: `다음 자연어 스타일링 요청을 분석해서 스타일 규칙으로 변환해주세요:\r\n\r\n**사용자 요청**: \"${userInstruction}\"\r\n**레이어명**: \"${lyrNm}\"\r\n**사용 가능한 속성들**: \"${attributes}\"\r\n\r\n**🚨 중요 제약사항 🚨**:\r\n- **반드시 위의 \"사용 가능한 속성들\"에서 속성명을 추출하여 사용하세요**\r\n- 속성 형식: \"a17(건폐율), a13(사용승인일자)\" → 속성명은 \"a17\", \"a13\" 사용\r\n- 속성 형식: \"c1(주소), c2(건물명)\" → 속성명은 \"c1\", \"c2\" 사용\r\n- **괄호 안의 설명은 무시하고 괄호 앞의 속성명만 사용하세요**\r\n- 존재하지 않는 가상의 속성명을 절대 만들어내지 마세요\r\n\r\n**변환 규칙**:\r\n1. 자연어에서 조건과 색상을 추출하여 스타일 규칙 생성\r\n2. 색상은 HEX 코드로 직접 생성 (예: #FF0000, #0000FF, #00FF00, #FFFF00, #800080, #FFA500, #FFC0CB, #A52A2A, #808080, #000000, #FFFFFF)\r\n3. **속성명 선택 규칙**:\r\n   - 사용자 요청을 분석하여 위의 \"사용 가능한 속성들\" 목록에서만 적절한 속성을 선택\r\n   - 목록에 없는 속성은 절대 사용하지 마세요\r\n   - **복합 조건**: 여러 속성을 조합하여 사용 가능 (단, 모두 위 목록에 있는 속성만)\r\n4. **🚨 복합 조건 처리 규칙 🚨**:\r\n   - **\"그리고\", \"이면서\", \"동시에\", \"또한\"**: 하나의 규칙으로 처리, conditions 배열에 여러 조건 포함\r\n   - **\"또는\", \"이거나\"**: 하나의 규칙으로 처리, logicalOperator를 \"OR\"로 설정\r\n   - **\"A는 X색, B는 Y색\"**: 별개의 규칙들로 분리\r\n   - **단일 조건**: conditions 배열에 하나의 조건만 포함\r\n5. 조건 타입:\r\n   - \"like\": 텍스트 포함 검색 (기본값, 대부분의 경우)\r\n   - \"equal\": 정확한 값 매칭 (특별히 정확한 매칭이 필요한 경우만)\r\n   - \"greater\": 숫자 > 비교 (명시적으로 \"초과\", \"보다 큰\" 등이 언급된 경우)\r\n   - \"less\": 숫자 < 비교 (명시적으로 \"미만\", \"보다 작은\" 등이 언급된 경우)\r\n   - \"greaterEqual\": 숫자 >= 비교 (명시적으로 \"이상\" 등이 언급된 경우)\r\n   - \"lessEqual\": 숫자 <= 비교 (명시적으로 \"이하\" 등이 언급된 경우)\r\n   - \"default\": 기본/나머지 스타일 (조건 없음)\r\n\r\n**변환 예시** (속성명 추출 방법):\r\n\r\n**🚨 중요: \"그리고\", \"이면서\", \"동시에\" 등은 하나의 규칙으로 처리! 🚨**\r\n\r\n**속성명 추출 방법:**\r\n- 속성 정보: \"a17(건폐율), a13(사용승인일자)\" → 사용할 속성명: \"a17\", \"a13\"\r\n- 속성 정보: \"c1(주소), c2(건물명)\" → 사용할 속성명: \"c1\", \"c2\"\r\n\r\n**단일 조건:**\r\n- \"서울에 있는 데이터만 파란색으로\" (속성: \"정제지번주소\") →\r\n  [{\"description\": \"서울 지역 - 파란색\", \"color\": \"#0000FF\", \"conditions\": [{\"attributeName\": \"정제지번주소\", \"condition\": \"like\", \"value\": \"서울\"}], \"logicalOperator\": \"AND\"}]\r\n\r\n**복합 조건 (하나의 규칙):**\r\n- \"건폐율 50 이상이고 사용승인일자가 1980년 이전인 건물\" (속성: \"a17(건폐율), a13(사용승인일자)\") →\r\n  [{\"description\": \"건폐율 50 이상이고 사용승인일자가 1980년 이전인 건물 - 빨간색\", \"color\": \"#FF0000\", \"conditions\": [{\"attributeName\": \"a17\", \"condition\": \"greaterEqual\", \"value\": \"50\"}, {\"attributeName\": \"a13\", \"condition\": \"less\", \"value\": \"1980\"}], \"logicalOperator\": \"AND\"}]\r\n\r\n**여러 규칙 (별개 조건):**\r\n- \"대학은 파란색, 중학교는 빨간색으로\" (속성: 교육기관타입) →\r\n  [\r\n    {\"description\": \"대학 - 파란색\", \"color\": \"#0000FF\", \"conditions\": [{\"attributeName\": \"교육기관타입\", \"condition\": \"like\", \"value\": \"대학\"}], \"logicalOperator\": \"AND\"},\r\n    {\"description\": \"중학교 - 빨간색\", \"color\": \"#FF0000\", \"conditions\": [{\"attributeName\": \"교육기관타입\", \"condition\": \"like\", \"value\": \"중학교\"}], \"logicalOperator\": \"AND\"}\r\n  ]\r\n\r\n- \"나머지/기본 스타일\" →\r\n  [{\"description\": \"나머지 - 회색\", \"color\": \"#808080\", \"conditions\": [], \"logicalOperator\": \"AND\"}]\r\n\r\n**중요**:\r\n- 특별한 언급이 없으면 condition은 \"like\" 사용\r\n- **logicalOperator는 항상 포함**: 단일 조건이어도 \"AND\" 명시, 복합 조건은 \"AND\" 또는 \"OR\" 명시\r\n- **항상 default 규칙을 포함**: 사용자가 특정 조건만 언급해도 나머지 데이터를 위한 기본 스타일(회색 #808080)을 자동으로 추가\r\n- 사용자가 \"나머지는 X색으로\" 명시한 경우에만 해당 색상 사용, 그렇지 않으면 기본 회색 사용\r\n- **다시 한번 강조: 반드시 제공된 속성 목록에서만 속성명을 선택하세요**`\r\n      });\r\n\r\n      console.log(\"Processed style rules:\", processedStyleRules.styleRules);\r\n\r\n      // logicalOperator가 누락된 경우 기본값 설정\r\n      const normalizedStyleRules = processedStyleRules.styleRules.map(rule => ({\r\n        ...rule,\r\n        logicalOperator: rule.logicalOperator || \"AND\"\r\n      }));\r\n\r\n      // default 규칙이 없으면 자동으로 추가\r\n      const hasDefaultRule = normalizedStyleRules.some(rule =>\r\n        rule.conditions.length === 0 || rule.conditions.some(cond => cond.condition === 'default')\r\n      );\r\n      let finalStyleRules = [...normalizedStyleRules];\r\n\r\n      if (!hasDefaultRule) {\r\n        finalStyleRules.push({\r\n          description: \"나머지 - 기본 스타일\",\r\n          color: \"#808080\", // 기본 회색\r\n          conditions: [], // 빈 배열은 default 규칙을 의미\r\n          logicalOperator: \"AND\" as const\r\n        });\r\n        console.log(\"Added default rule automatically\");\r\n      }\r\n\r\n      return {\r\n        layerId,\r\n        attributes,\r\n        styleRules: finalStyleRules,\r\n        description: `${finalStyleRules.length}개 유형별 스타일 적용 (${attributes.length}개 속성 활용)`,\r\n        success: true,\r\n        type: 'categorical'\r\n      };\r\n    } catch (error: any) {\r\n      return {\r\n        error: `유형별 스타일 생성 실패: ${error.message}`,\r\n        success: false\r\n      };\r\n    }\r\n  },\r\n});\r\n\r\n// 레이어 삭제 도구\r\nexport const removeLayer = tool({\r\n  description: `지도에서 레이어를 삭제합니다. 삭제된 레이어는 복구할 수 없으므로 신중하게 사용하세요.\r\n\r\n**사용 방법:**\r\n- layerId: 삭제할 레이어의 ID를 정확히 입력하세요\r\n- Current map state에서 레이어 ID를 확인할 수 있습니다\r\n\r\n**사용 예시:**\r\n- \"스타벅스 레이어를 삭제해줘\" → layerId: \"LR0000001234\"\r\n- \"이 레이어를 지워줘\" → 현재 활성 레이어의 ID 사용\r\n- \"모든 레이어를 삭제해줘\" → 각 레이어 ID를 순차적으로 삭제`,\r\n  parameters: z.object({\r\n    layerId: z.string().describe(\"삭제할 레이어의 ID\"),\r\n    description: z.string().optional().describe(\"삭제 사유 또는 설명\")\r\n  }),\r\n  async execute({ layerId, description }) {\r\n    try {\r\n      console.log(\"removeLayer called with layerId:\", layerId);\r\n      console.log(\"description:\", description);\r\n      return {\r\n        layerId,\r\n        description: description || \"레이어가 삭제되었습니다.\",\r\n        success: true\r\n      };\r\n    } catch (error: any) {\r\n      return {\r\n        error: `레이어 삭제 실패: ${error.message}`,\r\n        success: false\r\n      };\r\n    }\r\n  },\r\n});\r\n\r\nexport const createLayerFilter = tool({\r\n  description: `레이어의 속성정보를 기반으로 CQL 필터를 생성합니다. 단일 조건과 복수 조건을 모두 지원합니다.\r\n\r\n  필드 선택 가이드:\r\n  - getLayerAttributes 도구로 조회한 properties의 필드명을 사용하세요\r\n  - 필드명은 정확히 일치해야 합니다 (대소문자 구분)\r\n\r\n  연산자 사용 가이드:\r\n  - 텍스트: LIKE (부분일치), = (완전일치)\r\n  - 숫자: >, >=, <, <=, =\r\n  - 목록: IN\r\n\r\n  복수 조건 지원:\r\n  - AND: 모든 조건이 참이어야 함\r\n  - OR: 하나 이상의 조건이 참이면 됨\r\n  - 예시: \"서울이면서 인구 100만 이상\", \"강남구 또는 서초구\"\r\n\r\n  Args:\r\n    lyr_id: 필터 적용할 레이어 ID\r\n    attributes: 사용 가능한 속성 정보 (형식: \"a17(건폐율), a13(사용승인일자)\")\r\n    user_instruction: 자연어 필터링 요청 (예: \"서울이면서 인구 100만 이상\", \"강남구 또는 서초구\")\r\n     `,\r\n  parameters: z.object({\r\n    lyrId: z.string().min(1).describe(\"필터를 적용할 레이어 ID\"),\r\n    attributes: z.string().describe(\"🚨 필수: 속성명과 설명을 모두 포함한 형태로 제공 🚨 - 형식: 'a17(건폐율), a13(사용승인일자)' 또는 'c1(주소), c2(건물명)' - 절대 'a17'처럼 속성명만 제공하지 마세요\"),\r\n    userInstruction: z.string().describe(\"사용자의 자연어 필터링 요청 (예: '서울이면서 인구 100만 이상', '강남구 또는 서초구', '건폐율 50 이상')\")\r\n  }),\r\n  execute: async ({\r\n    lyrId,\r\n    attributes,\r\n    userInstruction,\r\n  }) => {\r\n    try {\r\n      console.log(\"createLayerFilter called with:\", {\r\n        lyrId,\r\n        attributes,\r\n        userInstruction\r\n      });\r\n\r\n      // generateObject를 사용해서 userInstruction에서 바로 필터 조건 생성\r\n      const { object: filterConditions } = await generateObject({\r\n        model: openai(\"gpt-4.1-nano\", { structuredOutputs: true }),\r\n        schema: z.object({\r\n          conditions: z.array(z.object({\r\n            attributeName: z.string().describe(\"속성명 - 반드시 attributes 매개변수에서 제공된 속성명만 사용\"),\r\n            operator: z.enum([\"=\", \">\", \"<\", \">=\", \"<=\", \"LIKE\", \"IN\"]).describe(\"비교 연산자\"),\r\n            value: z.string().describe(\"조건 값\")\r\n          })).describe(\"필터 조건 배열\"),\r\n          logicalOperator: z.enum([\"AND\", \"OR\"]).describe(\"복수 조건 간 논리 연산자 (기본값: AND)\")\r\n        }),\r\n        prompt: `다음 자연어 필터링 요청을 분석해서 CQL 필터 조건으로 변환해주세요:\r\n\r\n**사용자 요청**: \"${userInstruction}\"\r\n**사용 가능한 속성들**: \"${attributes}\"\r\n\r\n**🚨 중요 제약사항 🚨**:\r\n- **반드시 위의 \"사용 가능한 속성들\"에서 속성명을 추출하여 사용하세요**\r\n- 속성 형식: \"a17(건폐율), a13(사용승인일자)\" → 속성명은 \"a17\", \"a13\" 사용\r\n- 속성 형식: \"c1(주소), c2(건물명)\" → 속성명은 \"c1\", \"c2\" 사용\r\n- **괄호 안의 설명은 무시하고 괄호 앞의 속성명만 사용하세요**\r\n- 존재하지 않는 가상의 속성명을 절대 만들어내지 마세요\r\n\r\n**변환 규칙**:\r\n1. 자연어에서 조건을 추출하여 필터 조건 생성\r\n2. **속성명 선택 규칙**:\r\n   - 사용자 요청을 분석하여 위의 \"사용 가능한 속성들\" 목록에서만 적절한 속성을 선택\r\n   - 목록에 없는 속성은 절대 사용하지 마세요\r\n3. **논리 연산자 처리 규칙**:\r\n   - **\"그리고\", \"이면서\", \"동시에\", \"또한\"**: logicalOperator를 \"AND\"로 설정\r\n   - **\"또는\", \"이거나\"**: logicalOperator를 \"OR\"로 설정\r\n   - **단일 조건**: logicalOperator를 \"AND\"로 설정\r\n4. 연산자 타입:\r\n   - \"LIKE\": 텍스트 포함 검색 (기본값, 대부분의 경우)\r\n   - \"=\": 정확한 값 매칭 (특별히 정확한 매칭이 필요한 경우만)\r\n   - \">\": 숫자 > 비교 (명시적으로 \"초과\", \"보다 큰\" 등이 언급된 경우)\r\n   - \"<\": 숫자 < 비교 (명시적으로 \"미만\", \"보다 작은\" 등이 언급된 경우)\r\n   - \">=\": 숫자 >= 비교 (명시적으로 \"이상\" 등이 언급된 경우)\r\n   - \"<=\": 숫자 <= 비교 (명시적으로 \"이하\" 등이 언급된 경우)\r\n   - \"IN\": 목록 값 매칭 (여러 값 중 하나와 일치)\r\n\r\n**변환 예시**:\r\n- \"서울에 있는 데이터만\" (속성: \"정제지번주소\") →\r\n  {\"conditions\": [{\"attributeName\": \"정제지번주소\", \"operator\": \"LIKE\", \"value\": \"서울\"}], \"logicalOperator\": \"AND\"}\r\n\r\n- \"건폐율 50 이상이고 사용승인일자가 1980년 이전\" (속성: \"a17(건폐율), a13(사용승인일자)\") →\r\n  {\"conditions\": [{\"attributeName\": \"a17\", \"operator\": \">=\", \"value\": \"50\"}, {\"attributeName\": \"a13\", \"operator\": \"<\", \"value\": \"1980\"}], \"logicalOperator\": \"AND\"}\r\n\r\n- \"강남구 또는 서초구\" (속성: \"구명\") →\r\n  {\"conditions\": [{\"attributeName\": \"구명\", \"operator\": \"LIKE\", \"value\": \"강남구\"}, {\"attributeName\": \"구명\", \"operator\": \"LIKE\", \"value\": \"서초구\"}], \"logicalOperator\": \"OR\"}\r\n\r\n**중요**:\r\n- 특별한 언급이 없으면 operator는 \"LIKE\" 사용\r\n- **logicalOperator는 항상 포함**: 단일 조건이어도 \"AND\" 명시\r\n- **다시 한번 강조: 반드시 제공된 속성 목록에서만 속성명을 선택하세요**`\r\n      });\r\n\r\n      console.log(\"Processed filter conditions:\", filterConditions);\r\n\r\n      // CQL 필터 문자열 생성\r\n      let filterStr = \"\";\r\n      if (filterConditions.conditions.length === 0) {\r\n        return { error: \"필터 조건이 생성되지 않았습니다.\" };\r\n      }\r\n\r\n      if (filterConditions.conditions.length === 1) {\r\n        // 단일 조건\r\n        const condition = filterConditions.conditions[0];\r\n        filterStr = buildCQLCondition(condition);\r\n      } else {\r\n        // 복수 조건\r\n        const conditionStrings = filterConditions.conditions.map(condition =>\r\n          buildCQLCondition(condition)\r\n        );\r\n        const operator = filterConditions.logicalOperator || \"AND\";\r\n        filterStr = conditionStrings.join(` ${operator} `);\r\n      }\r\n\r\n      return {\r\n        lyr_id: lyrId,\r\n        filter: filterStr,\r\n        description: `${filterConditions.conditions.length}개 조건으로 필터링: ${userInstruction}`,\r\n        conditions: filterConditions.conditions,\r\n        logicalOperator: filterConditions.logicalOperator\r\n      };\r\n    } catch (error: any) {\r\n      return { error: `필터 생성 실패: ${error.message}` };\r\n    }\r\n  },\r\n});\r\n\r\n// CQL 조건 문자열 생성 헬퍼 함수\r\nfunction buildCQLCondition(condition: { attributeName: string; operator: string; value: string }): string {\r\n  const { attributeName, operator, value } = condition;\r\n\r\n  // 공백과 따옴표 제거\r\n  const cleanFieldName = attributeName.trim().replace(/['\"]/g, \"\");\r\n\r\n  if (operator.toUpperCase() === \"LIKE\") {\r\n    return `\"${cleanFieldName}\" LIKE '%${value}%'`;\r\n  } else if (operator.toUpperCase() === \"IN\") {\r\n    const values = value.split(\",\").map((v) => v.trim());\r\n    const valueStr = values.map((v) => `'${v}'`).join(\",\");\r\n    return `\"${cleanFieldName}\" IN (${valueStr})`;\r\n  } else {\r\n    const isNumeric = !isNaN(Number(value));\r\n    return `\"${cleanFieldName}\"${operator}${isNumeric ? value : `'${value}'`}`;\r\n  }\r\n}\r\n\r\n// 레이어 속성개수 조회 도구\r\nexport const getLayerAttributesCount = tool({\r\n  description: `레이어의 속성 데이터 개수를 조회합니다. 필터 조건을 적용하여 특정 조건에 맞는 데이터의 개수를 확인할 수 있습니다.`,\r\n  parameters: z.object({\r\n    typeName: z.string().describe(\"레이어 타입명 (예: Wgeontest3:L100000249)\"),\r\n    attributeFilter: z.string().optional().describe(\"속성 필터 내용 (예: \\\"구청명\\\" like '강%')\"),\r\n    spatialFilter: z.string().optional().describe(\"공간 필터 내용 (예: POLYGON((190000 540000, 200000 540000, 200000 550000, 190000 550000, 190000 540000)))\"),\r\n    spatialFilterSrid: z.string().optional().describe(\"공간 필터 SRID (예: 5186)\")\r\n  }),\r\n  execute: async ({\r\n    typeName,\r\n    attributeFilter,\r\n    spatialFilter,\r\n    spatialFilterSrid\r\n  }) => {\r\n    try {\r\n      const config = getApiConfig();\r\n\r\n      const params = new URLSearchParams({\r\n        typeName: typeName,\r\n      });\r\n\r\n      // 선택적 파라미터들 추가\r\n      if (attributeFilter && attributeFilter.trim() !== \"\") {\r\n        params.append(\"attributeFilter\", attributeFilter.trim());\r\n      }\r\n\r\n      if (spatialFilter && spatialFilter.trim() !== \"\") {\r\n        params.append(\"spatialFilter\", spatialFilter.trim());\r\n      }\r\n\r\n      if (spatialFilterSrid && spatialFilterSrid.trim() !== \"\") {\r\n        params.append(\"spatialFilterSrid\", spatialFilterSrid.trim());\r\n      }\r\n\r\n      // 인증 정보 추가\r\n      if (config.headers.crtfckey) {\r\n        params.append(\"crtfckey\", config.headers.crtfckey);\r\n      }\r\n\r\n      const response = await fetch(\r\n        `${config.baseUrl}/layer/attributes/count?${params.toString()}`,\r\n        {\r\n          method: \"GET\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            ...config.headers,\r\n          },\r\n        }\r\n      );\r\n\r\n      if (!response.ok) {\r\n        response.json().then((data) => {\r\n          console.error(\"API request failed with data\", data);\r\n        });\r\n        throw new Error(`API request failed with status ${response.status}`);\r\n      }\r\n\r\n      const data = (await response.json()) as any;\r\n\r\n      if (!data) {\r\n        return { error: \"레이어 속성개수 조회 실패: 응답 데이터가 없습니다.\" };\r\n      }\r\n\r\n      return {\r\n        typeName,\r\n        count: data.count || data.result || data,\r\n        attributeFilter,\r\n        spatialFilter,\r\n        spatialFilterSrid,\r\n        description: `레이어 ${typeName}의 속성 데이터 개수: ${data.count || data.result || data}개`\r\n      };\r\n    } catch (error: any) {\r\n      return { error: `레이어 속성개수 조회 실패: ${error.message}` };\r\n    }\r\n  },\r\n});\r\n\r\nexport const HILTools = {\r\n  chooseOption,\r\n  getUserInput,\r\n  confirmWithCheckbox,\r\n} as const;\r\n\r\nexport default HILTools;\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAOA;AACA;AACA;AACA;;;;;;AAEA,aAAa;AACb,MAAM,UAAU;IACd,WAAW;IACX,SAAS;IACT,WAAW;IACX,WAAW;AACb;AAGO,MAAM,yBAAyB;IACpC,GAAG,iPAAA,CAAA,yBAA8B;IACjC,kCAAkC,CAAC;QACjC,iDAAiD;QACjD,MAAM,eAAe,OAAO,QAAQ,EAAE,UAAU;QAChD,MAAM,eACJ,OAAO,IAAI,KAAK,sBAAsB,aAAa;QAErD,MAAM,UAAU,GAAG,aAAa,aAAa,EAAE,aAAa,2DAA2D,CAAC;QAExH,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;YACR;SACD;IACH;AACF;AAGO,MAAM,yBAAyB;IACpC,GAAG,iPAAA,CAAA,gBAAa;IAChB,kCAAkC,CAAC;QACjC,kCAAkC;QAClC,IAAI,CAAC,OAAO,MAAM,EAAE,UAAU,QAAQ;YACpC,OAAO;gBACL;oBACE,MAAM;oBACN,MAAM;gBACR;aACD;QACH;QAEA,MAAM,YAAY,OAAO,MAAM,CAAC,QAAQ;QACxC,MAAM,eAAe,UAAU,MAAM;QAErC,mBAAmB;QACnB,MAAM,eAAe,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAc,CAAC;gBAC7D,UAAU,KAAK,QAAQ;gBACvB,WAAW,KAAK,SAAS,IAAI,KAAK,OAAO;gBACzC,SAAS,KAAK,OAAO;gBACrB,SAAS,KAAK,OAAO;YACvB,CAAC;QAED,MAAM,UAAU,CAAC,kBAAkB,EAAE,aAAa,aAAa,CAAC;QAEhE,IAAI,cAAc,aACf,GAAG,CACF,CAAC,MAAW,QACV,GAAG,QAAQ,EAAE,EAAE,EAAE,KAAK,QAAQ,GAC5B,KAAK,SAAS,GAAG,CAAC,EAAE,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,GAC3C,OAAO,EAAE,KAAK,OAAO,CAAC,CAAC,EAAE,KAAK,OAAO,EAAE,EAE3C,IAAI,CAAC;QAER,IAAI,eAAe,GAAG;YACpB,eAAe,CAAC,QAAQ,EAAE,eAAe,EAAE,OAAO,CAAC;QACrD;QAEA,OAAO;YACL;gBACE,MAAM;gBACN,MAAM,GAAG,QAAQ,IAAI,EAAE,aAAa;YACtC;SACD;IACH;AACF;AAGO,MAAM,eAAe;IAC1B,GAAG,iPAAA,CAAA,gBAAa;IAChB,aACE;IACF,kCAAkC,CAAC;QACjC,IAAI,CAAC,OAAO,MAAM,EAAE,UAAU,QAAQ;YACpC,OAAO;gBACL;oBACE,MAAM;oBACN,MAAM;gBACR;aACD;QACH;QAEA,MAAM,YAAY,OAAO,MAAM,CAAC,QAAQ;QACxC,MAAM,eAAe,UAAU,MAAM;QACrC,MAAM,eAAe,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAc,CAAC;gBAC7D,UAAU,KAAK,QAAQ;gBACvB,WAAW,KAAK,SAAS,IAAI,KAAK,OAAO;gBACzC,SAAS,KAAK,OAAO;gBACrB,SAAS,KAAK,OAAO;YACvB,CAAC;QAED,MAAM,UAAU,CAAC,mBAAmB,EAAE,aAAa,aAAa,CAAC;QACjE,IAAI,cAAc,aACf,GAAG,CACF,CAAC,MAAW,QACV,GAAG,QAAQ,EAAE,EAAE,EAAE,KAAK,QAAQ,GAC5B,KAAK,SAAS,GAAG,CAAC,EAAE,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,GAC3C,OAAO,EAAE,KAAK,OAAO,CAAC,CAAC,EAAE,KAAK,OAAO,EAAE,EAE3C,IAAI,CAAC;QAER,IAAI,eAAe,GAAG;YACpB,eAAe,CAAC,QAAQ,EAAE,eAAe,EAAE,OAAO,CAAC;QACrD;QAEA,OAAO;YACL;gBACE,MAAM;gBACN,MAAM,GAAG,QAAQ,IAAI,EAAE,aAAa;YACtC;SACD;IACH;AACF;AAGO,MAAM,oBAAoB;IAC/B,GAAG,iPAAA,CAAA,gBAAa;IAChB,aACE;IACF,kCAAkC,CAAC;QACjC,IAAI,CAAC,OAAO,MAAM,EAAE,UAAU,QAAQ;YACpC,OAAO;gBACL;oBACE,MAAM;oBACN,MAAM;gBACR;aACD;QACH;QAEA,MAAM,YAAY,OAAO,MAAM,CAAC,QAAQ;QACxC,MAAM,eAAe,UAAU,MAAM;QACrC,MAAM,eAAe,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAc,CAAC;gBAC7D,UAAU,KAAK,QAAQ;gBACvB,WAAW,KAAK,SAAS,IAAI,KAAK,OAAO;gBACzC,SAAS,KAAK,OAAO;gBACrB,SAAS,KAAK,OAAO;YACvB,CAAC;QAED,MAAM,UAAU,CAAC,mBAAmB,EAAE,aAAa,aAAa,CAAC;QACjE,IAAI,cAAc,aACf,GAAG,CACF,CAAC,MAAW,QACV,GAAG,QAAQ,EAAE,EAAE,EAAE,KAAK,QAAQ,GAC5B,KAAK,SAAS,GAAG,CAAC,EAAE,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,GAC3C,OAAO,EAAE,KAAK,OAAO,CAAC,CAAC,EAAE,KAAK,OAAO,EAAE,EAE3C,IAAI,CAAC;QAER,IAAI,eAAe,GAAG;YACpB,eAAe,CAAC,QAAQ,EAAE,eAAe,EAAE,OAAO,CAAC;QACrD;QAEA,OAAO;YACL;gBACE,MAAM;gBACN,MAAM,GAAG,QAAQ,IAAI,EAAE,aAAa;YACtC;SACD;IACH;AACF;AAGO,MAAM,4BAA4B;IACvC,GAAG,iPAAA,CAAA,mBAAgB;IACnB,SAAS,OAAO,QAAa;QAC3B,4BAA4B;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,aAAa,GAAG;QAClC,QAAQ,GAAG,CAAC,sCAAsC;QAClD,OAAO,iPAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,aAAa;IAC/C;IACA,kCAAkC,CAAC;QACjC,mCAAmC;QAEnC,QAAQ;QACR,IAAI,OAAO,KAAK,EAAE;YAChB,OAAO;gBACL;oBACE,MAAM;oBACN,MAAM,CAAC,UAAU,EAAE,OAAO,KAAK,EAAE;gBACnC;aACD;QACH;QAEA,6BAA6B;QAC7B,IAAI,CAAC,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,MAAM,EAAE;YAC3C,OAAO;gBACL;oBACE,MAAM;oBACN,MAAM;gBACR;aACD;QACH;QAEA,MAAM,QAAQ,OAAO,MAAM,CAAC,EAAE;QAE9B,wBAAwB;QACxB,IAAI,MAAM,WAAW,KAAK,GAAG;YAC3B,OAAO;gBACL;oBACE,MAAM;oBACN,MAAM,CAAC,UAAU,EACf,MAAM,UAAU,IAAI,sBACpB;gBACJ;aACD;QACH;QAEA,mBAAmB;QACnB,MAAM,UAAU,MAAM,OAAO;QAC7B,MAAM,WAAW,SAAS,WACtB,GAAG,CAAC,QAAQ,QAAQ,GAAG,IAAI,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,GAC3C;QACJ,MAAM,WAAW,SAAS,WACtB,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,GAAG,IAAI,CAAC,CAAC,GACvC;QACJ,MAAM,WAAW,SAAS,MAAM,OAC5B,GAAG,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,GACxC;QACJ,MAAM,WAAW,SAAS,MAAM,OAC5B,GAAG,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,GACxC;QAEJ,aAAa;QACb,MAAM,aAAa,OAAO,MAAM,EAAE,QAAQ;QAC1C,MAAM,kBAAkB,OAAO,WAAW,EAAE,QAAQ;QAEpD,MAAM,YAAY,CAAC;;GAEpB,EAAE,WAAW,GAAG,EAAE,gBAAgB;OAC9B,EAAE,SAAS;SACT,EAAE,SAAS;YACR,EAAE,SAAS;SACd,EAAE,SAAS;;qBAEC,CAAC;QAElB,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;YACR;SACD;IACH;AACF;AAGO,MAAM,iBAAiB,CAAA,GAAA,gPAAA,CAAA,OAAI,AAAD,EAAE;IACjC,aAAa,CAAC;;;;;mCAKmB,CAAC;IAClC,YAAY,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC,CAAC;IACtB,SAAS;QACP,OAAO;YACL,UAAU,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,GAAK,CAAC;oBACrD;oBACA;oBACA,aAAa;gBACf,CAAC;QACH;IACF;AACF;AAEO,MAAM,gBAAgB,CAAA,GAAA,gPAAA,CAAA,OAAI,AAAD,EAAE;IAChC,aAAa,CAAC;;;;;;;;;;;4BAWY,CAAC;IAC3B,YAAY,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,WAAW,uLAAA,CAAA,IAAC,CACT,IAAI,CAAC;YAAC;YAAa;YAAW;YAAa;SAAY,EACvD,QAAQ,CACP;IAEN;IACA,SAAS,OAAO,EAAE,SAAS,EAAE;QAC3B,MAAM,cAAc,OAAO,CAAC,UAAU;QAEtC,IAAI,CAAC,aAAa;YAChB,OAAO;gBACL,OAAO,CAAC,oBAAoB,EAAE,WAAW;gBACzC,mBAAmB,OAAO,IAAI,CAAC;YACjC;QACF;QAEA,OAAO;YACL,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS,CAAC,MAAM,EAAE,YAAY,UAAU,CAAC;QAC3C;IACF;AACF;AAEO,MAAM,eAAe,CAAA,GAAA,gPAAA,CAAA,OAAI,AAAD,EAAE;IAC/B,aAAa,CAAC;;;;;wCAKwB,CAAC;IACvC,YAAY,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,WAAW,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC/B,UAAU,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,UAAU,uLAAA,CAAA,IAAC,CACR,IAAI,CAAC;YAAC;YAAY;SAAW,EAC7B,QAAQ,CAAC,yCACT,QAAQ,GACR,OAAO,CAAC;QACX,aAAa,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,QAAQ;IACxD;IACA,SAAS,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE;QAC5D,IAAI;YACF,OAAO;gBACL,SAAS;gBACT,QAAQ;oBAAC;oBAAW;iBAAS;gBAC7B;gBACA,SACE,eACA,CAAC,WAAW,EAAE,UAAU,KAAK,EAAE,SAAS,UAAU,CAAC;YACvD;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,cAAc,EAAE,MAAM,OAAO,EAAE;YACzC;QACF;IACF;AACF;AAEO,MAAM,aAAa,CAAA,GAAA,gPAAA,CAAA,OAAI,AAAD,EAAE;IAC7B,aAAa,CAAC;;;;;;;;;;;;;AAahB,CAAC;IACC,YAAY,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,WAAW,uLAAA,CAAA,IAAC,CACT,MAAM,GACN,QAAQ,GACR,QAAQ,CAAC;QACZ,UAAU,uLAAA,CAAA,IAAC,CACR,IAAI,CAAC;YAAC;YAAY;SAAW,EAC7B,QAAQ,CAAC,4CACT,QAAQ,GACR,OAAO,CAAC;QACX,eAAe,uLAAA,CAAA,IAAC,CACb,IAAI,CAAC;YAAC;YAAM;SAAM,EAClB,QAAQ,CAAC;QACZ,aAAa,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,gBAAgB,QAAQ;IAC3D;IACA,SAAS,OAAO,EAAE,YAAY,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE;QACrE,IAAI;YACF,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN;gBACA;gBACA,SACE,eACA,CAAC,GAAG,EAAE,kBAAkB,OAAO,OAAO,KAAK,KAAK,EAC9C,aAAa,aAAa,OAAO,KAClC,MAAM,CAAC;YACZ;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,aAAa,EAAE,MAAM,OAAO,EAAE;YACxC;QACF;IACF;AACF;AAEO,MAAM,qBAAqB,CAAA,GAAA,gPAAA,CAAA,OAAI,AAAD,EAAE;IACrC,aAAa,CAAC;;;;;;;;;;;;;;;2BAeW,CAAC;IAC1B,YAAY,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,WAAW,uLAAA,CAAA,IAAC,CACT,IAAI,CAAC;YAAC;YAAS;YAAS;YAAQ;YAAQ;YAAM;YAAQ;YAAQ;SAAQ,EACtE,QAAQ,CAAC;QACZ,UAAU,uLAAA,CAAA,IAAC,CACR,MAAM,GACN,QAAQ,CAAC,qCACT,QAAQ,GACR,OAAO,CAAC;IACb;IACA,SAAS,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE;QACrC,IAAI;YACF,uCAAuC;YACvC,MAAM,gBAAgB,CAAC;gBACrB,MAAM,QAAQ,YAAY,KAAK,CAAC;gBAChC,IAAI,CAAC,OAAO;oBACV,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,aAAa;gBAC7C;gBAEA,MAAM,QAAQ,WAAW,KAAK,CAAC,EAAE;gBACjC,MAAM,OAAO,KAAK,CAAC,EAAE,EAAE,iBAAiB;gBAExC,OAAO,SAAS,OAAO,QAAQ,OAAO;YACxC;YAEA,MAAM,mBAAmB,cAAc;YACvC,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,UAAU,EAAE,EAAE,SAAS,IAAI,EAAE,iBAAiB,CAAC,CAAC;YAEnF,gCAAgC;YAChC,yCAAyC;YACzC,IAAI,SAAS,GAAG,aAAa;YAC7B,IAAI,SAAS,GAAG,aAAa;YAE7B,OAAQ;gBACN,KAAK;gBACL,KAAK;oBACH,SAAS,kBAAkB,iBAAiB;oBAC5C;gBACF,KAAK;gBACL,KAAK;oBACH,SAAS,CAAC,kBAAkB,iBAAiB;oBAC7C;gBACF,KAAK;gBACL,KAAK;oBACH,SAAS,kBAAkB,iBAAiB;oBAC5C;gBACF,KAAK;gBACL,KAAK;oBACH,SAAS,CAAC,kBAAkB,iBAAiB;oBAC7C;YACJ;YAEA,MAAM,iBAAiB;gBACrB,OAAO;gBACP,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,OAAO;YACT;YAEA,OAAO;gBACL,SAAS;gBACT;gBACA;gBACA;gBACA,UAAU;gBACV,kBAAkB;gBAClB,SAAS,CAAC,IAAI,EAAE,cAAc,CAAC,UAAU,CAAC,GAAG,EAAE,SAAS,SAAS,CAAC;YACpE;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,aAAa,EAAE,MAAM,OAAO,EAAE;YACxC;QACF;IACF;AACF;AAIO,MAAM,eAAe,CAAA,GAAA,gPAAA,CAAA,OAAI,AAAD,EAAE;IAC/B,aACE;IACF,YAAY,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,SAAS,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC7B,SAAS,uLAAA,CAAA,IAAC,CACP,KAAK,CACJ,uLAAA,CAAA,IAAC,CACE,MAAM,GACN,QAAQ,CACP,8CAGL,QAAQ,CAAC;IACd;AACF;AAEO,MAAM,eAAe,CAAA,GAAA,gPAAA,CAAA,OAAI,AAAD,EAAE;IAC/B,aAAa,CAAC;;AAEhB,CAAC;IACC,YAAY,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,QAAQ,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC5B,WAAW,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QAC1C,aAAa,uLAAA,CAAA,IAAC,CACX,MAAM,GACN,QAAQ,GACR,QAAQ,CAAC;QACZ,gBAAgB,uLAAA,CAAA,IAAC,CACd,MAAM,GACN,QAAQ,GACR,QAAQ,CACP;QAEJ,WAAW,uLAAA,CAAA,IAAC,CACT,MAAM,GACN,QAAQ,GACR,QAAQ,CAAC;QACZ,UAAU,uLAAA,CAAA,IAAC,CACR,MAAM,GACN,QAAQ,GACR,QAAQ,CAAC;IACd;IACA,MAAM,SAAQ,EACZ,MAAM,EACN,YAAY,EAAE,EACd,cAAc,EAAE,EAChB,iBAAiB,GAAG,EACpB,YAAY,GAAG,EACf,WAAW,IAAI,EAChB;QACC,IAAI;YACF,MAAM,SAAS,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;YAE1B,MAAM,SAAS,IAAI,gBAAgB;gBACjC,QAAQ,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;gBACrB;gBACA;gBACA;YACF;YAEA,uCAAuC;YACvC,IAAI,aAAa,UAAU,IAAI,OAAO,IAAI;gBACxC,OAAO,MAAM,CAAC,aAAa,UAAU,IAAI;YAC3C;YAEA,IAAI,eAAe,YAAY,IAAI,OAAO,IAAI;gBAC5C,OAAO,MAAM,CAAC,eAAe,YAAY,IAAI;YAC/C;YAEA,WAAW;YACX,IAAI,OAAO,OAAO,CAAC,QAAQ,EAAE;gBAC3B,OAAO,MAAM,CAAC,YAAY,OAAO,OAAO,CAAC,QAAQ;YACnD;YAEA,MAAM,WAAW,MAAM,MACrB,GAAG,OAAO,OAAO,CAAC,qBAAqB,EAAE,OAAO,QAAQ,IAAI,EAC5D;gBACE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,GAAG,OAAO,OAAO;gBACnB;YACF;YAGF,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,SAAS,IAAI,GAAG,IAAI,CAAC,CAAC;oBACpB,QAAQ,KAAK,CAAC,gCAAgC;gBAChD;gBACA,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS,MAAM,EAAE;YACrE;YAEA,MAAM,OAAQ,MAAM,SAAS,IAAI;YAEjC,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAM,EAAE;gBACzB,OAAO;oBAAE,OAAO;gBAA8B;YAChD;YAEA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,OAAO,CAAC,cAAc,EAAE,MAAM,OAAO,EAAE;YAAC;QACnD;IACF;AACF;AAGO,MAAM,mBAAmB,CAAA,GAAA,gPAAA,CAAA,OAAI,AAAD,EAAE;IACnC,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;gCAuBgB,CAAC;IAC/B,YAAY,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,SAAS,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC7B,OAAO,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACtC,aAAa,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC;QAC1D,aAAa,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QAC5C,aAAa,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,GAAG,QAAQ,CAAC;QAC3D,QAAQ,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,GAAG,QAAQ,CAAC;QACtD,OAAO,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,GAAG,QAAQ,CAAC;QACrD,QAAQ,uLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAU;YAAU;YAAY;YAAQ;YAAS;SAAI,EAAE,QAAQ,GAAG,QAAQ,CAAC;QAC3F,aAAa,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC9C;IACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;QACzG,IAAI;YACF,YAAY;YACZ,MAAM,cAAmB,CAAC;YAE1B,IAAI,OAAO,YAAY,KAAK,GAAG;YAC/B,IAAI,gBAAgB,WAAW,YAAY,WAAW,GAAG;YACzD,IAAI,aAAa,YAAY,WAAW,GAAG;YAC3C,IAAI,gBAAgB,WAAW,YAAY,WAAW,GAAG;YACzD,IAAI,WAAW,WAAW,YAAY,MAAM,GAAG;YAC/C,IAAI,UAAU,WAAW,YAAY,KAAK,GAAG;YAC7C,IAAI,QAAQ,YAAY,MAAM,GAAG;YAEjC,OAAO;gBACL;gBACA;gBACA,aAAa,eAAe;gBAC5B,SAAS;YACX;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,OAAO,CAAC,aAAa,EAAE,MAAM,OAAO,EAAE;gBACtC,SAAS;YACX;QACF;IACF;AACF;AAGO,MAAM,2BAA2B,CAAA,GAAA,gPAAA,CAAA,OAAI,AAAD,EAAE;IAC3C,aAAa,CAAC;;;AAGhB,CAAC;IACC,YAAY,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,SAAS,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC7B,OAAO,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC3B,YAAY,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAChC,iBAAiB,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACvC;IACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE;QAC3D,IAAI;YACF,QAAQ,GAAG,CAAC,yCAAyC;gBACnD;gBACA;gBACA;gBACA;YACF;YAEA,mEAAmE;YACnE,MAAM,EAAE,QAAQ,mBAAmB,EAAE,GAAG,MAAM,CAAA,GAAA,gPAAA,CAAA,iBAAc,AAAD,EAAE;gBAC3D,OAAO,CAAA,GAAA,gPAAA,CAAA,SAAM,AAAD,EAAE,gBAAgB;oBAAE,mBAAmB;gBAAK;gBACxD,aAAa;gBACb,QAAQ,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;oBACf,YAAY,uLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;wBAC3B,aAAa,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;wBACjC,OAAO,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;wBAC3B,YAAY,uLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;4BAC3B,eAAe,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;4BACnC,WAAW,uLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;gCAAC;gCAAQ;gCAAS;gCAAW;gCAAQ;gCAAgB;gCAAa;6BAAU,EAAE,QAAQ,CAAC;4BACzG,OAAO,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;wBAC7B,IAAI,QAAQ,CAAC;wBACb,iBAAiB,uLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;4BAAC;4BAAO;yBAAK,EAAE,QAAQ,CAAC;oBAClD;gBACF;gBACA,QAAQ,CAAC;;aAEJ,EAAE,gBAAgB;WACpB,EAAE,MAAM;iBACF,EAAE,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CA6Da,CAAC;YACtC;YAEA,QAAQ,GAAG,CAAC,0BAA0B,oBAAoB,UAAU;YAEpE,iCAAiC;YACjC,MAAM,uBAAuB,oBAAoB,UAAU,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACvE,GAAG,IAAI;oBACP,iBAAiB,KAAK,eAAe,IAAI;gBAC3C,CAAC;YAED,0BAA0B;YAC1B,MAAM,iBAAiB,qBAAqB,IAAI,CAAC,CAAA,OAC/C,KAAK,UAAU,CAAC,MAAM,KAAK,KAAK,KAAK,UAAU,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK;YAElF,IAAI,kBAAkB;mBAAI;aAAqB;YAE/C,IAAI,CAAC,gBAAgB;gBACnB,gBAAgB,IAAI,CAAC;oBACnB,aAAa;oBACb,OAAO;oBACP,YAAY,EAAE;oBACd,iBAAiB;gBACnB;gBACA,QAAQ,GAAG,CAAC;YACd;YAEA,OAAO;gBACL;gBACA;gBACA,YAAY;gBACZ,aAAa,GAAG,gBAAgB,MAAM,CAAC,cAAc,EAAE,WAAW,MAAM,CAAC,QAAQ,CAAC;gBAClF,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,OAAO,CAAC,eAAe,EAAE,MAAM,OAAO,EAAE;gBACxC,SAAS;YACX;QACF;IACF;AACF;AAGO,MAAM,cAAc,CAAA,GAAA,gPAAA,CAAA,OAAI,AAAD,EAAE;IAC9B,aAAa,CAAC;;;;;;;;;qCASqB,CAAC;IACpC,YAAY,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,SAAS,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC7B,aAAa,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC9C;IACA,MAAM,SAAQ,EAAE,OAAO,EAAE,WAAW,EAAE;QACpC,IAAI;YACF,QAAQ,GAAG,CAAC,oCAAoC;YAChD,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,OAAO;gBACL;gBACA,aAAa,eAAe;gBAC5B,SAAS;YACX;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,OAAO,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE;gBACpC,SAAS;YACX;QACF;IACF;AACF;AAEO,MAAM,oBAAoB,CAAA,GAAA,gPAAA,CAAA,OAAI,AAAD,EAAE;IACpC,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;KAoBX,CAAC;IACJ,YAAY,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,OAAO,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ,CAAC;QAClC,YAAY,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAChC,iBAAiB,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACvC;IACA,SAAS,OAAO,EACd,KAAK,EACL,UAAU,EACV,eAAe,EAChB;QACC,IAAI;YACF,QAAQ,GAAG,CAAC,kCAAkC;gBAC5C;gBACA;gBACA;YACF;YAEA,qDAAqD;YACrD,MAAM,EAAE,QAAQ,gBAAgB,EAAE,GAAG,MAAM,CAAA,GAAA,gPAAA,CAAA,iBAAc,AAAD,EAAE;gBACxD,OAAO,CAAA,GAAA,gPAAA,CAAA,SAAM,AAAD,EAAE,gBAAgB;oBAAE,mBAAmB;gBAAK;gBACxD,QAAQ,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;oBACf,YAAY,uLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;wBAC3B,eAAe,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;wBACnC,UAAU,uLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;4BAAC;4BAAK;4BAAK;4BAAK;4BAAM;4BAAM;4BAAQ;yBAAK,EAAE,QAAQ,CAAC;wBACrE,OAAO,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;oBAC7B,IAAI,QAAQ,CAAC;oBACb,iBAAiB,uLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;wBAAC;wBAAO;qBAAK,EAAE,QAAQ,CAAC;gBAClD;gBACA,QAAQ,CAAC;;aAEJ,EAAE,gBAAgB;iBACd,EAAE,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAwCa,CAAC;YACtC;YAEA,QAAQ,GAAG,CAAC,gCAAgC;YAE5C,gBAAgB;YAChB,IAAI,YAAY;YAChB,IAAI,iBAAiB,UAAU,CAAC,MAAM,KAAK,GAAG;gBAC5C,OAAO;oBAAE,OAAO;gBAAqB;YACvC;YAEA,IAAI,iBAAiB,UAAU,CAAC,MAAM,KAAK,GAAG;gBAC5C,QAAQ;gBACR,MAAM,YAAY,iBAAiB,UAAU,CAAC,EAAE;gBAChD,YAAY,kBAAkB;YAChC,OAAO;gBACL,QAAQ;gBACR,MAAM,mBAAmB,iBAAiB,UAAU,CAAC,GAAG,CAAC,CAAA,YACvD,kBAAkB;gBAEpB,MAAM,WAAW,iBAAiB,eAAe,IAAI;gBACrD,YAAY,iBAAiB,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;YACnD;YAEA,OAAO;gBACL,QAAQ;gBACR,QAAQ;gBACR,aAAa,GAAG,iBAAiB,UAAU,CAAC,MAAM,CAAC,YAAY,EAAE,iBAAiB;gBAClF,YAAY,iBAAiB,UAAU;gBACvC,iBAAiB,iBAAiB,eAAe;YACnD;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,OAAO,CAAC,UAAU,EAAE,MAAM,OAAO,EAAE;YAAC;QAC/C;IACF;AACF;AAEA,sBAAsB;AACtB,SAAS,kBAAkB,SAAqE;IAC9F,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE3C,aAAa;IACb,MAAM,iBAAiB,cAAc,IAAI,GAAG,OAAO,CAAC,SAAS;IAE7D,IAAI,SAAS,WAAW,OAAO,QAAQ;QACrC,OAAO,CAAC,CAAC,EAAE,eAAe,SAAS,EAAE,MAAM,EAAE,CAAC;IAChD,OAAO,IAAI,SAAS,WAAW,OAAO,MAAM;QAC1C,MAAM,SAAS,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI;QACjD,MAAM,WAAW,OAAO,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;QAClD,OAAO,CAAC,CAAC,EAAE,eAAe,MAAM,EAAE,SAAS,CAAC,CAAC;IAC/C,OAAO;QACL,MAAM,YAAY,CAAC,MAAM,OAAO;QAChC,OAAO,CAAC,CAAC,EAAE,eAAe,CAAC,EAAE,WAAW,YAAY,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE;IAC5E;AACF;AAGO,MAAM,0BAA0B,CAAA,GAAA,gPAAA,CAAA,OAAI,AAAD,EAAE;IAC1C,aAAa,CAAC,iEAAiE,CAAC;IAChF,YAAY,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,UAAU,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,iBAAiB,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QAChD,eAAe,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QAC9C,mBAAmB,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACpD;IACA,SAAS,OAAO,EACd,QAAQ,EACR,eAAe,EACf,aAAa,EACb,iBAAiB,EAClB;QACC,IAAI;YACF,MAAM,SAAS,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;YAE1B,MAAM,SAAS,IAAI,gBAAgB;gBACjC,UAAU;YACZ;YAEA,eAAe;YACf,IAAI,mBAAmB,gBAAgB,IAAI,OAAO,IAAI;gBACpD,OAAO,MAAM,CAAC,mBAAmB,gBAAgB,IAAI;YACvD;YAEA,IAAI,iBAAiB,cAAc,IAAI,OAAO,IAAI;gBAChD,OAAO,MAAM,CAAC,iBAAiB,cAAc,IAAI;YACnD;YAEA,IAAI,qBAAqB,kBAAkB,IAAI,OAAO,IAAI;gBACxD,OAAO,MAAM,CAAC,qBAAqB,kBAAkB,IAAI;YAC3D;YAEA,WAAW;YACX,IAAI,OAAO,OAAO,CAAC,QAAQ,EAAE;gBAC3B,OAAO,MAAM,CAAC,YAAY,OAAO,OAAO,CAAC,QAAQ;YACnD;YAEA,MAAM,WAAW,MAAM,MACrB,GAAG,OAAO,OAAO,CAAC,wBAAwB,EAAE,OAAO,QAAQ,IAAI,EAC/D;gBACE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,GAAG,OAAO,OAAO;gBACnB;YACF;YAGF,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,SAAS,IAAI,GAAG,IAAI,CAAC,CAAC;oBACpB,QAAQ,KAAK,CAAC,gCAAgC;gBAChD;gBACA,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS,MAAM,EAAE;YACrE;YAEA,MAAM,OAAQ,MAAM,SAAS,IAAI;YAEjC,IAAI,CAAC,MAAM;gBACT,OAAO;oBAAE,OAAO;gBAAgC;YAClD;YAEA,OAAO;gBACL;gBACA,OAAO,KAAK,KAAK,IAAI,KAAK,MAAM,IAAI;gBACpC;gBACA;gBACA;gBACA,aAAa,CAAC,IAAI,EAAE,SAAS,aAAa,EAAE,KAAK,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,CAAC,CAAC;YAClF;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,OAAO,CAAC,gBAAgB,EAAE,MAAM,OAAO,EAAE;YAAC;QACrD;IACF;AACF;AAEO,MAAM,WAAW;IACtB;IACA,cAAA,iPAAA,CAAA,eAAY;IACZ,qBAAA,iPAAA,CAAA,sBAAmB;AACrB;uCAEe", "debugId": null}}, {"offset": {"line": 2812, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/api/chat/agents.ts"], "sourcesContent": ["import {\r\n  getLayer,\r\n  getLayerAttributes,\r\n  getLocation,\r\n  performDensityAnalysis,\r\n} from \"@geon-ai/tools\";\r\nimport { createIntentAnalysisPrompt } from \"./intent-rules\";\r\nimport {\r\n  createLayerFilter,\r\n  getLayerList,\r\n  getLayerAttributesCount,\r\n  HILTools,\r\n  searchAddressOptimized,\r\n  changeBasemap,\r\n  setMapZoom,\r\n  moveMapByDirection,\r\n  searchDestination,\r\n  searchDirectionsOptimized,\r\n  searchOrigin,\r\n  updateLayerStyle,\r\n  removeLayer,\r\n  generateCategoricalStyle,\r\n} from \"./tools\";\r\nimport { tool, type Tool } from \"ai\";\r\nimport { z } from \"zod\";\r\n// import { getLayerToolRulesForAgent } from \"./layer-tool-rules\"; // 단순화로 인해 사용하지 않음\r\n\r\nexport type AgentName =\r\n  | \"navigation\"\r\n  | \"map_control\"\r\n  | \"layer_agent\"\r\n  | \"density_analysis\"\r\n  | \"intent_analyzer\"\r\n  | \"default_agent\"\r\n  | \"unsupported_feature\";\r\n\r\nexport interface AgentConfig {\r\n  tools: Record<string, Tool<any, any>>;\r\n  system: string;\r\n  maxSteps?: number; // Optional: Agent별 최대 스텝 수 설정\r\n  // TODO: 추후 Agent별로 필요한 다른 설정을 추가할 수 있음 (예: toolChoice)\r\n  outputSchema?: any; // Add outputSchema property\r\n}\r\n\r\n// 지원되지 않는 기능 정의 (중앙 관리)\r\nexport const UNSUPPORTED_FEATURES = {\r\n  NEARBY_POI_SEARCH: {\r\n    keywords: [\r\n      \"근처\",\r\n      \"주변\",\r\n      \"이 근처\",\r\n      \"여기 근처\",\r\n      \"주위\",\r\n      \"인근\",\r\n      \"nearby\",\r\n      \"around here\",\r\n      \"close to\",\r\n    ],\r\n    description: \"주변/근처 POI 검색\",\r\n    examples: [\"여기 근처에 맛집\", \"주변 카페\", \"이 근처 병원\"],\r\n    alternative: \"구체적인 장소명 검색 (예: '강남역 스타벅스')\",\r\n  },\r\n  CATEGORY_LIST_REQUEST: {\r\n    keywords: [\"전체 목록\", \"모든 ~\", \"~ 목록\", \"~ 리스트\"],\r\n    description: \"카테고리별 전체 목록 요청\",\r\n    examples: [\"맛집 목록\", \"카페 목록\", \"병원 목록\"],\r\n    alternative: \"구체적인 브랜드명 검색 (예: '스타벅스', '이디야')\",\r\n    exceptions: [\"레이어 목록\", \"사용가능한 레이어\", \"데이터 종류\"], // 이런 경우는 LAYER_LIST로 분류\r\n  },\r\n  REALTIME_INFO: {\r\n    keywords: [\r\n      \"실시간\",\r\n      \"지금\",\r\n      \"현재\",\r\n      \"실시간 교통\",\r\n      \"교통상황\",\r\n      \"정체\",\r\n      \"실시간 날씨\",\r\n      \"현재 날씨\",\r\n      \"실시간 위치\",\r\n    ],\r\n    description: \"실시간 정보 요청\",\r\n    examples: [\"실시간 교통상황\", \"지금 날씨\", \"현재 교통정보\"],\r\n    alternative: \"정적 데이터 기반 길찾기 및 레이어 정보\",\r\n  },\r\n  DATA_MANAGEMENT: {\r\n    keywords: [\r\n      \"업로드\",\r\n      \"올리기\",\r\n      \"파일 추가\",\r\n      \"편집\",\r\n      \"수정\",\r\n      \"삭제\",\r\n      \"다운로드\",\r\n      \"저장\",\r\n      \"내보내기\",\r\n      \"export\",\r\n      \"새로 만들기\",\r\n      \"생성\",\r\n    ],\r\n    description: \"데이터 관리 기능\",\r\n    examples: [\"데이터 업로드\", \"파일 편집\", \"레이어 생성\"],\r\n    alternative: \"기본 제공 레이어 활용 및 검색\",\r\n  },\r\n  ADVANCED_SPATIAL_ANALYSIS: {\r\n    keywords: [\r\n      \"버퍼\",\r\n      \"반경\",\r\n      \"buffer\",\r\n      \"radius\",\r\n      \"오버레이\",\r\n      \"overlay\",\r\n      \"중첩\",\r\n      \"네트워크 분석\",\r\n      \"공간분석\",\r\n      \"spatial analysis\",\r\n    ],\r\n    description: \"고급 공간분석\",\r\n    examples: [\"500m 버퍼 분석\", \"오버레이 분석\", \"네트워크 분석\"],\r\n    alternative: \"밀도 분석 기능\",\r\n    exceptions: [\"밀도 분석\", \"밀도\"], // 이런 경우는 DENSITY_ANALYSIS로 분류\r\n  },\r\n  ADVANCED_MAP_FEATURES: {\r\n    keywords: [\r\n      \"3D\",\r\n      \"삼차원\",\r\n      \"입체\",\r\n      \"애니메이션\",\r\n      \"시간대별\",\r\n      \"temporal\",\r\n      \"사용자 정의 심볼\",\r\n      \"커스텀 스타일\",\r\n      \"회전\",\r\n      \"돌려줘\",\r\n      \"각도\",\r\n      \"기울여\",\r\n      \"틸트\",\r\n      \"rotate\",\r\n      \"tilt\",\r\n    ],\r\n    description: \"고급 지도 기능\",\r\n    examples: [\r\n      \"3D 지도\",\r\n      \"시간대별 애니메이션\",\r\n      \"커스텀 심볼\",\r\n      \"지도 회전\",\r\n      \"지도 기울기\",\r\n    ],\r\n    alternative: \"기본 2D 지도, 표준 스타일링, 지도 이동 및 확대/축소\",\r\n  },\r\n} as const;\r\n\r\n// 에이전트별 제한사항 정의\r\nexport const AGENT_LIMITATIONS = {\r\n  navigation: {\r\n    unsupported: [\r\n      UNSUPPORTED_FEATURES.NEARBY_POI_SEARCH,\r\n      UNSUPPORTED_FEATURES.CATEGORY_LIST_REQUEST,\r\n      UNSUPPORTED_FEATURES.REALTIME_INFO,\r\n    ],\r\n    supported: [\"구체적인 장소명 검색\", \"두 지점 간 길찾기\", \"특정 주소 검색\"],\r\n  },\r\n  layer_agent: {\r\n    unsupported: [\r\n      UNSUPPORTED_FEATURES.DATA_MANAGEMENT,\r\n      UNSUPPORTED_FEATURES.CATEGORY_LIST_REQUEST,\r\n      UNSUPPORTED_FEATURES.ADVANCED_SPATIAL_ANALYSIS,\r\n    ],\r\n    supported: [\r\n      \"키워드 기반 레이어 검색\",\r\n      \"레이어 추가/삭제\",\r\n      \"레이어 목록 조회\",\r\n      \"레이어 스타일 변경\",\r\n      \"조건부 레이어 필터링\",\r\n      \"속성 기반 필터링\",\r\n    ],\r\n  },\r\n  density_analysis: {\r\n    unsupported: [\r\n      UNSUPPORTED_FEATURES.ADVANCED_SPATIAL_ANALYSIS,\r\n      UNSUPPORTED_FEATURES.DATA_MANAGEMENT,\r\n    ],\r\n    supported: [\"포인트 데이터 밀도 분석\", \"히트맵 시각화\"],\r\n  },\r\n  basemap: {\r\n    unsupported: [\r\n      UNSUPPORTED_FEATURES.ADVANCED_MAP_FEATURES,\r\n      UNSUPPORTED_FEATURES.DATA_MANAGEMENT,\r\n    ],\r\n    supported: [\"배경지도 변경\", \"기본 지도 스타일 전환\"],\r\n  },\r\n} as const;\r\n\r\n// 공통 시스템 프롬프트 상수\r\nexport const COMMON_SYSTEM_PROMPTS = {\r\n  language: `응답은 한국어로 작성하세요.`,\r\n  tone: `친근하고 자연스러운 톤으로 사용자에게 도움을 제공하세요.`,\r\n  interaction: `명확한 지시는 즉시 실행하고, 정말 애매한 경우에만 사용자 입력 도구를 사용하세요. 이미 동의한 작업을 다시 확인하지 마세요.`,\r\n  coreRules: `\r\n🚨🚨🚨 **모든 에이전트 공통 핵심 규칙** 🚨🚨🚨\r\n1. **컨텍스트 유지**: 대화 맥락을 파악하여 지시사항을 준수하세요.\r\n2. **정확한 파라미터**: 키워드가 없으면 빈 값으로 설정\r\n`,\r\n} as const;\r\n\r\n// Schema for the intent analyzer's response\r\n\r\n// Intent enum을 const assertion으로 정의하여 타입 안정성 확보\r\nexport const IntentEnum = [\r\n  \"LAYER_ADD\",\r\n  \"LAYER_REMOVE\",\r\n  \"LAYER_STYLE\",\r\n  \"LAYER_FILTER\",\r\n  \"LAYER_LIST\",\r\n  \"NAVIGATION\",\r\n  \"MAP_CONTROL\",\r\n  \"BASEMAP_CHANGE\",\r\n  \"DENSITY_ANALYSIS\",\r\n  \"GENERAL_CONVERSATION\",\r\n  \"UNSUPPORTED_FEATURE\",\r\n  \"UNSURE\",\r\n] as const;\r\n\r\nexport const IntentResponseSchema = z.object({\r\n  intent: z.enum(IntentEnum).describe(\"분석된 사용자의 핵심 의도 카테고리\"),\r\n  message: z\r\n    .string()\r\n    .describe(\r\n      \"다음 에이전트에게 전달할 구체적인 작업 지시 메시지 (시스템 내부 통신용)\"\r\n    ),\r\n  targetLayer: z.string().optional().describe(\"대상 레이어명 (있는 경우)\"),\r\n  layerExists: z.boolean().optional().describe(\"대상 레이어가 현재 지도에 존재하는지\"),\r\n  requiredActions: z.array(z.string()).describe(\"필요한 작업 단계들\"),\r\n  styleRequirements: z.object({\r\n    color: z.string().optional(),\r\n    shape: z.string().optional(),\r\n    size: z.string().optional()\r\n  }).optional().describe(\"스타일 요구사항\")\r\n});\r\n\r\nexport type IntentResponseType = z.infer<typeof IntentResponseSchema>;\r\n\r\nexport const intentAnalyzerAgentConfig: AgentConfig = {\r\n  tools: {},\r\n  system: `\r\n    ${COMMON_SYSTEM_PROMPTS.language}\r\n\r\n    당신은 지도 서비스의 의도분석 전문가입니다.\r\n\r\n    **핵심 역할:**\r\n    1. 사용자 요청의 정확한 의도 파악\r\n    2. 현재 지도 상태 기반 작업 계획 수립\r\n    3. 다음 에이전트를 위한 구체적 지시사항 생성\r\n\r\n    **분석 단계:**\r\n    1. **현재 상황 파악**: 지도에 있는 레이어들 확인\r\n    2. **요청 분석**: 사용자가 원하는 것이 무엇인지 정확히 파악\r\n    3. **작업 계획**: 현재 상태에서 목표까지의 구체적 단계 수립\r\n\r\n    **상황별 분석 예시:**\r\n\r\n    **케이스 1: 레이어가 이미 존재하는 경우**\r\n    사용자: \"스타벅스를 빨간색으로 바꿔줘\"\r\n    현재 지도: 스타벅스 레이어 존재\r\n    → intent: \"LAYER_STYLE\"\r\n    → targetLayer: \"스타벅스\"\r\n    → layerExists: true\r\n    → requiredActions: [\"updateLayerStyle로 빨간색 적용\"]\r\n    → styleRequirements: {\"color\": \"빨간색\"}\r\n    → message: \"스타벅스 레이어가 이미 지도에 있으므로 바로 빨간색 스타일을 적용하겠습니다.\"\r\n\r\n    **케이스 2: 레이어가 존재하지 않는 경우**\r\n    사용자: \"백년가게를 노란색 별모양으로 보여줘\"\r\n    현재 지도: 백년가게 레이어 없음\r\n    → intent: \"LAYER_STYLE\"\r\n    → targetLayer: \"백년가게\"\r\n    → layerExists: false\r\n    → requiredActions: [\"getLayerList로 백년가게 검색\", \"chooseOption으로 선택\", \"getLayer로 추가\", \"updateLayerStyle로 노란색 별모양 적용\"]\r\n    → styleRequirements: {\"color\": \"노란색\", \"shape\": \"별모양\"}\r\n    → message: \"백년가게 레이어가 현재 지도에 없으므로 먼저 검색하여 추가한 후 노란색 별모양 스타일을 적용하겠습니다. 구체적으로: 1) getLayerList('백년가게') 2) chooseOption으로 적절한 레이어 선택 3) getLayer로 추가 4) updateLayerStyle로 노란색 별 스타일 적용\"\r\n\r\n    **케이스 3: 밀도분석 요청**\r\n    사용자: \"스타벅스 밀도분석 해줘\"\r\n    현재 지도: 스타벅스 레이어 있음 (점 타입)\r\n    → intent: \"DENSITY_ANALYSIS\"\r\n    → targetLayer: \"스타벅스\"\r\n    → layerExists: true\r\n    → requiredActions: [\"기존 스타벅스 레이어로 밀도분석 수행\"]\r\n    → message: \"현재 지도의 스타벅스 레이어(점 타입)를 사용하여 밀도분석을 수행하겠습니다.\"\r\n\r\n    **중요: 이전 대화 컨텍스트 분석**\r\n    - 이전 어시스턴트 메시지에서 도구 호출 결과(getLayerList, chooseOption 등)를 확인하세요\r\n    - 현재 워크플로우 단계를 파악하고 다음 단계에 맞는 구체적인 지시를 생성하세요\r\n    - 레이어가 이미 선택된 상태라면 다음 단계로 진행하도록 지시하세요\r\n\r\n    ${createIntentAnalysisPrompt()}\r\n  `,\r\n  maxSteps: 1,\r\n  outputSchema: IntentResponseSchema,\r\n};\r\n\r\nexport const agentConfigs: Record<AgentName, AgentConfig> = {\r\n  intent_analyzer: intentAnalyzerAgentConfig,\r\n  navigation: {\r\n    tools: {\r\n      searchAddress: searchAddressOptimized,\r\n      searchOrigin: searchOrigin,\r\n      searchDestination: searchDestination,\r\n      searchDirections: searchDirectionsOptimized,\r\n      getLocation,\r\n      ...HILTools,\r\n    },\r\n    system: `\r\n      ${COMMON_SYSTEM_PROMPTS.language}\r\n      ${COMMON_SYSTEM_PROMPTS.tone}\r\n      ${COMMON_SYSTEM_PROMPTS.interaction}\r\n      당신은 위치 검색 및 길찾기 전문가입니다. 사용자의 요청을 받으면 **즉시 해당 도구를 호출**하여 작업을 수행합니다.\r\n\r\n      **🚨 재시도 시 중요 규칙:**\r\n      - 이전에 searchOrigin/searchDestination을 성공적으로 호출했다면, 그 결과를 재사용하세요\r\n      - 동일한 장소를 다시 검색할 필요 없이, 이전 검색 결과의 좌표를 직접 사용하세요\r\n      - 예: 이전에 \"웨이버스\" 검색 성공 → 재시도 시 동일한 좌표 사용\r\n\r\n      **🚨 절대 지원하지 않는 기능 - 도구 호출 금지 🚨:**\r\n\r\n      **1. 주변/근처 POI 검색 (절대 불가능):**\r\n      - \"여기 근처에 맛집\", \"주변 카페\", \"이 근처 병원\", \"근처 편의점\" 등\r\n      - **중요**: 어떤 장소명을 제공받아도 \"주변\" 검색은 불가능\r\n      - **절대 searchAddress나 다른 도구를 호출하지 마세요**\r\n\r\n      **2. 카테고리별 POI 목록 (절대 불가능):**\r\n      - \"맛집 목록\", \"카페 목록\", \"병원 목록\" 등\r\n      - **절대 도구 호출하지 마세요**\r\n\r\n      **🚨 이런 요청 시 반드시 이렇게 응답하세요:**\r\n      \"죄송하지만 주변 POI 검색 기능은 현재 지원하지 않습니다. 저는 다음 기능만 도와드릴 수 있어요:\r\n      1. 구체적인 장소명 검색 (예: '강남역 찾아줘')\r\n      2. 두 지점 간 길찾기 (예: '강남역에서 홍대까지 가는 길')\r\n      3. 특정 주소 검색\r\n\r\n      구체적인 장소명을 알려주시면 해당 위치를 찾아드릴 수 있습니다.\"\r\n\r\n      **경로찾기 패턴 인식 (최우선):**\r\n      다음 패턴들은 **반드시 searchDirections 도구를 호출**해야 합니다:\r\n      - \"A에서 B까지\" / \"A부터 B까지\" / \"A → B\" / \"A에서 B로\"\r\n      - \"가는 길\" / \"길찾기\" / \"경로\" / \"루트\" / \"네비게이션\"\r\n      - \"어떻게 가\" / \"어떻게 이동\" / \"방법\" (목적지 포함시)\r\n      - \"여기서 ~까지\" / \"현재 위치에서 ~까지\"\r\n      - 예시: \"웨이버스에서 평촌역까지\", \"여기서 서울역 가는 길\", \"강남역에서 홍대 어떻게 가?\"\r\n\r\n      **필수 작업 절차:**\r\n\r\n      0.  **🚨 지원되지 않는 기능 요청 확인 (최우선) 🚨**:\r\n          **패턴 감지 키워드:**\r\n          - \"근처\", \"주변\", \"이 근처\", \"여기 근처\" + POI 카테고리\r\n          - POI 카테고리: \"맛집\", \"카페\", \"병원\", \"편의점\", \"마트\", \"약국\", \"은행\" 등\r\n\r\n          **이런 요청 시 절대 규칙:**\r\n          - **어떤 도구도 호출하지 마세요 (searchAddress, getLocation 등 모두 금지)**\r\n          - **즉시 제한사항 안내 메시지만 출력**\r\n          - **\"해당 위치에서 검색해드릴 수 있습니다\" 같은 잘못된 안내 금지**\r\n\r\n      1.  **경로찾기 요청 (최우선 처리)**:\r\n          → 출발지 인식: \"여기서\"/\"현재 위치\" → getLocation 호출\r\n          → 출발지 인식: 구체적 장소명 → searchOrigin 호출\r\n          → 도착지 인식: 항상 searchDestination 호출\r\n          → **반드시 searchDirections 도구 호출**하여 경로 검색\r\n          → 경로 결과를 사용자에게 제공\r\n\r\n      **searchDirections 도구 호출 시 필수 좌표 형식:**\r\n\r\n      ✅ **정확한 형식 예시:**\r\n      - origin: \"127.111202,37.394912\" (경도,위도)\r\n      - destination: \"127.111202,37.394912\" (경도,위도)\r\n\r\n      **좌표 추출 방법:**\r\n      1. searchOrigin/searchDestination 결과에서 buildLo(경도), buildLa(위도) 값 사용\r\n      2. 형식: \"{buildLo},{buildLa}\" 또는 \"{buildLo},{buildLa},name={buildName}\"\r\n      3. **반드시 실제 검색 결과의 좌표를 사용하세요** (예시 좌표 사용 금지)\r\n\r\n      **🚨 searchDirections 호출 시 중요 규칙:**\r\n      - avoid 파라미터는 생략하세요 (기본값 사용)\r\n      - 잘못된 avoid 값 사용 시 API 오류 발생\r\n\r\n      2.  **단순 위치 이동 요청** (\"웨이버스로 이동해줘\", \"강남역 보여줘\"):\r\n          → **즉시 'searchAddress' 도구 호출**하여 장소 검색\r\n          → **🎯 중요**: searchAddress 도구 호출 시 **자동으로 지도가 첫 번째 검색 결과로 이동**됩니다\r\n          → 검색 결과가 여러 개면 'chooseOption' 도구로 사용자 선택 유도\r\n          → 지도 이동이 완료되었음을 사용자에게 안내\r\n\r\n      3.  **주변 시설 검색** (\"근처 카페\", \"주변 맛집\"):\r\n          → **즉시 'searchAddress' 도구 호출**하여 검색\r\n          → 여러 결과시 'chooseOption' 도구로 선택 유도\r\n\r\n      **중요 규칙:**\r\n      - 설명만 하지 말고 **반드시 도구를 호출**하세요\r\n      - 경로찾기 패턴 감지시 → **무조건 searchDirections 호출**\r\n      - \"이동해줘\", \"보여줘\", \"찾아줘\" → searchAddress 즉시 호출\r\n      - **searchAddress 호출 시**: 지도가 자동으로 첫 번째 결과 위치로 이동됨\r\n      - 검색 결과 여러 개 → chooseOption 즉시 호출\r\n      - 지도 이동 완료 후 사용자에게 \"위치를 찾았습니다\" 등의 완료 메시지 제공\r\n\r\n      사용자 요청을 받으면 즉시 적절한 도구를 호출하여 결과를 제공하세요.\r\n    `,\r\n  },\r\n  map_control: {\r\n    tools: {\r\n      changeBasemap,\r\n      setMapZoom,\r\n      moveMapByDirection,\r\n      getLocation,\r\n      ...HILTools,\r\n    },\r\n    system: `\r\n      ${COMMON_SYSTEM_PROMPTS.language}\r\n      ${COMMON_SYSTEM_PROMPTS.tone}\r\n      당신은 지도 제어 전문가입니다. 배경지도 변경, 지도 확대/축소, 중심점 이동 등 **지원되는 지도 제어 기능만** 담당합니다.\r\n\r\n      **🚨 최우선 규칙: 명확한 지도 조작 요청은 \"~할까요?\" 같은 확인 질문 없이 즉시 도구 호출! 🚨**\r\n\r\n      **절대 금지 사항:**\r\n      - \"확대를 진행할까요?\", \"위성지도로 변경할까요?\" 같은 확인 질문\r\n      - 명확한 키워드가 있는 요청에 대한 추가 설명이나 확인\r\n      - 지원되는 기능에 대한 사전 안내나 선택 옵션 제공\r\n\r\n      **🚨 절대 규칙: 지원되는 기능만 도구 호출! 지원되지 않는 기능은 명확히 안내! 🚨**\r\n\r\n      **🚨 지원되지 않는 기능 (절대 도구 호출 금지):**\r\n      - **지도 회전**: \"회전해줘\", \"돌려줘\", \"각도 변경\" 등\r\n      - **지도 기울기**: \"기울여줘\", \"3D 뷰\", \"틸트\" 등\r\n\r\n      현재 지원하는 지도 제어 기능:\r\n      - 배경지도 변경 (일반지도, 위성지도, 색각지도, 백지도)\r\n      - 지도 확대/축소 (레벨 1-20)\r\n      - 지도 이동 (북쪽, 남쪽, 동쪽, 서쪽으로 특정 거리)\r\n\r\n      이 중에서 도움이 될 만한 기능이 있으시면 말씀해 주세요!\"\r\n\r\n      **지원하는 지도 제어 기능:**\r\n\r\n      **1. 배경지도 변경 (changeBasemap):**\r\n      - eMapBasic: 일반지도 (기본 지도)\r\n      - eMapAIR: 항공지도 (위성지도)\r\n      - eMapColor: 색각지도 (색상 대비)\r\n      - eMapWhite: 백지도 (심플한 배경)\r\n\r\n      키워드: \"위성지도\", \"항공지도\" → eMapAIR / \"일반지도\", \"기본지도\" → eMapBasic\r\n\r\n      **2. 지도 확대/축소 (setMapZoom):**\r\n      - 확대: \"확대\", \"더 자세히\", \"크게\" → 현재 레벨 + 2\r\n      - 축소: \"축소\", \"더 넓게\", \"작게\" → 현재 레벨 - 2\r\n      - 최대 확대: \"최대한 확대\" → 레벨 18\r\n      - 전체 보기: \"전체 보기\", \"전국 보기\" → 레벨 8\r\n\r\n      **3. 지도 이동 (moveMapByDirection):**\r\n      - 방향 이동: \"북쪽으로\", \"위로\", \"오른쪽으로\" → moveMapByDirection\r\n      - 거리 지정: \"동쪽으로 500m\", \"북쪽으로 1km\" → moveMapByDirection\r\n\r\n      **키워드 매칭 및 즉시 실행:**\r\n\r\n      **✅ 지원되는 기능 - 즉시 도구 호출 (확인 질문 절대 금지):**\r\n\r\n      **배경지도 변경 키워드:** \"위성지도\", \"항공지도\", \"일반지도\", \"기본지도\", \"색각지도\", \"백지도\"\r\n      사용자: \"위성지도로 바꿔줘\" → 즉시 changeBasemap({ basemapId: \"eMapAIR\" }) 호출\r\n      사용자: \"일반지도로 전환\" → 즉시 changeBasemap({ basemapId: \"eMapBasic\" }) 호출\r\n      ❌ 잘못된 응답: \"위성지도로 변경할까요?\" (확인 질문 금지)\r\n      ✅ 올바른 응답: 즉시 도구 호출 후 \"위성지도로 변경했습니다\"\r\n\r\n      **지도 확대/축소 키워드:** \"확대\", \"축소\", \"넓게\", \"자세히\", \"크게\", \"작게\", \"줌인\", \"줌아웃\"\r\n      사용자: \"지도 확대해줘\" → 즉시 setMapZoom({ zoomLevel: 2, zoomType: \"relative\", zoomDirection: \"in\" }) 호출\r\n      사용자: \"더 넓게 보여줘\" → 즉시 setMapZoom({ zoomLevel: 2, zoomType: \"relative\", zoomDirection: \"out\" }) 호출\r\n      사용자: \"최대한 확대\" → 즉시 setMapZoom({ zoomLevel: 18, zoomType: \"absolute\", zoomDirection: \"in\" }) 호출\r\n      ❌ 잘못된 응답: \"확대를 진행할까요?\" (확인 질문 금지)\r\n      ✅ 올바른 응답: 즉시 도구 호출 후 \"지도를 확대했습니다\"\r\n\r\n      **지도 이동 키워드:** \"북쪽으로\", \"남쪽으로\", \"동쪽으로\", \"서쪽으로\", \"위로\", \"아래로\", \"왼쪽으로\", \"오른쪽으로\" + 거리\r\n      사용자: \"지도를 북쪽으로 이동\" → 즉시 moveMapByDirection({ direction: \"north\" }) 호출\r\n      사용자: \"오른쪽으로 500m\" → 즉시 moveMapByDirection({ direction: \"right\", distance: 500 }) 호출\r\n      ❌ 잘못된 응답: \"북쪽으로 이동할까요?\" (확인 질문 금지)\r\n      ✅ 올바른 응답: 즉시 도구 호출 후 \"북쪽으로 이동했습니다\"\r\n\r\n      **❌ 지원되지 않는 기능 - 도구 호출 금지, 안내 메시지만:**\r\n\r\n      **지도 회전 키워드:** \"회전\", \"돌려줘\", \"각도\", \"방향 바꿔\", \"rotate\"\r\n      사용자: \"회전해줘\" → 지원되지 않음을 안내하고 대안 제시\r\n\r\n      **지도 기울기 키워드:** \"기울여\", \"틸트\", \"3D\", \"입체\", \"tilt\"\r\n      사용자: \"지도 기울여줘\" → 지원되지 않음을 안내하고 대안 제시\r\n\r\n      **🚨 중요 규칙 - 즉시 실행 원칙:**\r\n      1. **지원되는 기능**: 추가 확인 없이 즉시 해당 도구 호출 (설명이나 \"~할까요?\" 같은 확인 질문 금지)\r\n      2. **지원되지 않는 기능**: 절대 도구 호출 금지, 제한사항 안내 메시지만 제공\r\n      3. **모호한 요청**: 정말 애매한 경우에만 chooseOption 사용 (명확한 키워드가 있으면 즉시 실행)\r\n      4. **복합 요청**: 지원되는 기능만 추출하여 즉시 처리, 지원되지 않는 부분은 안내\r\n\r\n      **실행 예시:**\r\n      - \"회전해줘\" → 도구 호출 ❌, \"지도 회전은 지원되지 않습니다\" 안내 ✅\r\n      - \"확대해줘\" → 즉시 setMapZoom 도구 호출 ✅ (확인 질문 ❌)\r\n      - \"지도 확대해줘\" → 즉시 setMapZoom 도구 호출 ✅ (확인 질문 ❌)\r\n      - \"위성지도로 바꿔줘\" → 즉시 changeBasemap 도구 호출 ✅ (확인 질문 ❌)\r\n      - \"북쪽으로 이동해줘\" → 즉시 moveMapByDirection 도구 호출 ✅ (확인 질문 ❌)\r\n      - \"확대하면서 회전해줘\" → 즉시 setMapZoom 호출 + 회전 미지원 안내\r\n    `,\r\n  },\r\n  layer_agent: {\r\n    tools: {\r\n      getLayer,\r\n      getLayerList,\r\n      getLayerAttributesCount,\r\n      updateLayerStyle,\r\n      removeLayer,\r\n      getLayerAttributes,\r\n      generateCategoricalStyle,\r\n      createLayerFilter,\r\n      ...HILTools,\r\n    },\r\n    system: `\r\n      당신은 레이어 관리 실행자입니다. 의도분석 결과로 받은 구체적인 작업 지시사항을 순서대로 실행합니다.\r\n\r\n      **핵심 원칙:**\r\n      1. 의도분석에서 제공된 작업 단계를 순서대로 실행\r\n      2. 각 단계별로 명확한 도구 호출\r\n      3. 복잡한 판단은 하지 않고 지시사항만 따름\r\n\r\n      **기본 실행 규칙:**\r\n      - getLayerList 호출 시: userId='geonuser', lyrTySeCode='' (비워둠)\r\n      - 검색 결과 여러개: chooseOption 사용\r\n      - 레이어 추가: getLayer 호출\r\n      - 스타일 적용: updateLayerStyle 호출\r\n      - 에러 발생 시: 해당 단계 재시도\r\n\r\n      **지원하는 작업 패턴:**\r\n      1. SEARCH_AND_ADD: getLayerList → chooseOption → getLayer\r\n      2. DIRECT_ADD: getLayer (레이어 ID가 명확한 경우)\r\n      3. STYLE_EXISTING: updateLayerStyle (기존 레이어 스타일 변경)\r\n      4. SEARCH_ADD_STYLE: getLayerList → chooseOption → getLayer → updateLayerStyle\r\n      5. REMOVE_LAYER: removeLayer\r\n\r\n      작업 지시사항에 명시된 순서대로 도구를 호출하세요.\r\n\r\n      ${COMMON_SYSTEM_PROMPTS.language}\r\n      ${COMMON_SYSTEM_PROMPTS.interaction}\r\n    `,\r\n  },\r\n  density_analysis: {\r\n    tools: { performDensityAnalysis, getLayerList, getLayer, ...HILTools },\r\n    system: `\r\n      ${COMMON_SYSTEM_PROMPTS.language}\r\n      ${COMMON_SYSTEM_PROMPTS.tone}\r\n      ${COMMON_SYSTEM_PROMPTS.interaction}\r\n      ${COMMON_SYSTEM_PROMPTS.coreRules}\r\n      당신은 밀도 분석 전문가입니다. 특정 레이어의 공간적 밀집도를 분석하여 시각화합니다.\r\n\r\n      **🚨 중요: 밀도 분석은 점(Point) 타입 레이어에서만 가능합니다! 🚨**\r\n\r\n      **작업 프로세스 (즉시 실행):**\r\n      **중요: 의도분석 결과를 최우선으로 활용하세요!**\r\n\r\n      0. **컨텍스트 정보 확인**:\r\n         - Current map state 메시지에서 \"의도분석 결과\" 섹션 확인\r\n\r\n      1. **🚨 즉시 점 타입 레이어 검색 실행 🚨**:\r\n         - **컨텍스트에 레이어 ID가 있는 경우**:\r\n           * getLayer 호출하여 geometryType 확인\r\n           * 점 타입이 아니면 즉시 getLayerList(lyrTySeCode=\"1\") 호출하여 점 타입 레이어만 검색\r\n         - **컨텍스트 정보가 없는 경우**:\r\n           * 첫 응답에서 바로 getLayerList 도구 호출\r\n           * **반드시 lyrTySeCode=\"1\" 파라미터 포함** (점 타입만 검색)\r\n         - **설명 없이 즉시 실행**: getLayerList(\"스타벅스\", lyrTySeCode=\"1\") 같은 형태로 바로 호출\r\n         - 예: \"스타벅스 레이어 밀도분석\" → 즉시 getLayerList(layerName=\"스타벅스\", lyrTySeCode=\"1\") 호출\r\n\r\n      2. **점 타입 레이어 선택**:\r\n         - **검색 결과가 없는 경우**:\r\n           * getUserInput으로 \"밀도 분석이 가능한 점 타입 레이어가 없습니다. 다른 키워드로 검색하시겠습니까?\" 안내\r\n         - **자동 선택 조건**: 검색 결과가 1개인 경우 자동 선택\r\n         - **chooseOption 사용 조건**: 검색 결과가 여러 개인 경우\r\n           * 반드시 \"레이어명 (점 타입)|레이어ID\" 형태로 옵션 제공\r\n           * 예: chooseOption(\"어떤 점 타입 레이어의 밀도를 분석하시겠습니까?\", [\"스타벅스 서울 매장 (점 타입)|LR000123\", \"스타벅스 전국 DT 매장 (점 타입)|LR000456\"])\r\n\r\n      3. **레이어 상세 조회 및 최종 검증**:\r\n         - getLayer로 선택된 레이어의 상세 정보 조회\r\n         - **chooseOption 결과 처리**:\r\n           * 결과가 \"레이어명|레이어ID\" 형태인 경우 → \"|\" 뒤의 레이어ID 추출하여 사용\r\n           * 결과가 단순 문자열인 경우 → 해당 문자열을 레이어명으로 처리\r\n         - **최종 포인트 타입 검증**: geometryType이 'point'인지 재확인\r\n         - **포인트 타입이 아닌 경우**:\r\n           * getUserInput으로 \"선택하신 레이어는 면/선 타입으로 밀도 분석이 불가능합니다. 점 타입 레이어를 다시 검색하시겠습니까?\" 안내\r\n           * 다시 getLayerList(lyrTySeCode=\"1\") 호출\r\n\r\n      4. **밀도 분석 수행**:\r\n         - **중요**: getLayer 결과의 'layer' 필드를 performDensityAnalysis의 trgetTypeName으로 사용\r\n         - **예시**: getLayer 결과에서 layer: \"Wgeontest4:L100004762\" → trgetTypeName: \"Wgeontest4:L100004762\"\r\n         - **userId**: \"geonuser\" 고정값 사용\r\n         - **lyrNm**: 선택사항, 기본값은 \"밀도 분석 결과\"\r\n\r\n      **핵심 규칙 (절대 준수):**\r\n      - **🚨 중요: 모든 getLayerList 호출 시 반드시 lyrTySeCode=\"1\" 파라미터 포함! 🚨**\r\n      - **🚨 중요: 사용자 질문에서 키워드를 특정할 수 없다면 getLayerList 호출 시 layerName 파라미터는 비워두세요! 🚨**\r\n      - **getLayer 결과의 'layer' 필드를 performDensityAnalysis의 trgetTypeName으로 반드시 사용**\r\n      - 포인트 타입이 아닌 레이어는 절대 분석하지 말고 사용자에게 점 타입 레이어 선택 유도\r\n\r\n      **performDensityAnalysis 파라미터 사용법:**\r\n      - userId: \"geonuser\" (고정값)\r\n      - trgetTypeName: getLayer 결과의 'layer' 필드 값 (예: \"Wgeontest4:L100004762\")\r\n      - lyrNm: 결과 레이어 이름 (선택사항, 예: \"스타벅스 매장 밀도 분석\")\r\n\r\n      **예시 시나리오 1: \"스타벅스 레이어에 대해서 밀도분석을 수행해줘\"**\r\n      1. getLayerList(layerName=\"스타벅스\", lyrTySeCode=\"1\") 호출 (점 타입만 검색)\r\n      2. **검색 결과 처리**:\r\n         - 결과가 없으면: getUserInput(\"밀도 분석이 가능한 스타벅스 점 타입 레이어가 없습니다. 다른 키워드로 검색하시겠습니까?\")\r\n         - 결과가 1개면: 자동 선택\r\n         - 결과가 여러 개면: chooseOption 사용 (반드시 \"레이어명 (점 타입)|레이어ID\" 형태로)\r\n      3. getLayer(선택된레이어ID) 호출\r\n      4. geometryType이 'point'인지 최종 확인\r\n      5. performDensityAnalysis 호출:\r\n         - userId: \"geonuser\"\r\n         - trgetTypeName: getLayer 결과의 'layer' 필드 값\r\n         - lyrNm: \"스타벅스 매장 밀도 분석\"\r\n\r\n      **예시 시나리오 2: \"밀도분석은 어떻게해\"**\r\n      1. getLayerList(lyrTySeCode=\"1\") 호출 (점 타입만 검색, layerName은 비워둠)\r\n      2. chooseOption으로 밀도 분석이 가능한 점 타입 레이어 목록 제시\r\n\r\n      **예시 시나리오 3: \"밀도분석 요청\"**\r\n      1. Current map state에서 Active layers 확인\r\n      2. 선택된 레이어의 geometryType이 'point'인지 확인\r\n      3. 'point'가 아니면 \"선택하신 레이어는 면/선 타입으로 밀도 분석이 불가능합니다. 점 타입 레이어를 다시 검색하시겠습니까?\" 안내\r\n      4. 'point'이면 performDensityAnalysis 호출\r\n\r\n      **사용자 안내 메시지 예시:**\r\n      - \"밀도 분석은 점(Point) 타입 레이어에서만 가능합니다.\"\r\n      - \"면 타입 레이어나 선 타입 레이어는 밀도 분석이 지원되지 않습니다.\"\r\n      - \"점 타입 레이어를 선택해주세요.\"\r\n\r\n      **중요**: 절대로 일반 텍스트로 레이어 목록을 나열하지 마세요. 반드시 HIL 도구를 사용하세요!\r\n\r\n    `,\r\n  },\r\n  default_agent: {\r\n    tools: {\r\n      ...HILTools,\r\n    },\r\n    system: `\r\n      ${COMMON_SYSTEM_PROMPTS.language}\r\n      ${COMMON_SYSTEM_PROMPTS.tone}\r\n      ${COMMON_SYSTEM_PROMPTS.interaction}\r\n      당신은 친절한 GIS 지도 서비스 어시스턴트입니다. 지도와 직접적으로 관련되지 않은 일반적인 대화나 지원되지 않는 기능 요청을 처리합니다.\r\n\r\n      **현재 지원하는 주요 기능:**\r\n      1. **장소 검색 및 길찾기**: 특정 장소 찾기, 경로 안내, 주변 시설 검색\r\n         - 예: \"강남역 찾아줘\", \"여기서 서울역까지 가는 길\", \"근처 카페 보여줘\"\r\n      2. **레이어 관리**: 키워드 기반 레이어 검색 및 추가, 필터링\r\n         - 예: \"스타벅스 레이어 추가\", \"미세먼지 정보 보여줘\", \"서울의 높은 건물만\"\r\n      3. **배경지도 변경**: 일반지도, 위성지도, 색각지도, 백지도 전환\r\n         - 예: \"위성지도로 바꿔줘\", \"배경지도 변경\"\r\n      4. **밀도 분석**: 포인트 데이터의 공간적 밀집도 분석 및 시각화\r\n         - 예: \"스타벅스 밀도 분석\", \"인구밀도 분석\"\r\n      5. **지도 조작**: 확대/축소, 이동 등 기본 지도 컨트롤\r\n\r\n      **현재 지원하지 않는 기능 (명확한 안내 필요):**\r\n      - **데이터 업로드**: 사용자 개인 데이터 파일 업로드 및 추가\r\n      - **데이터 편집**: 기존 레이어 데이터의 수정, 삭제, 편집\r\n      - **데이터 다운로드**: 지도 데이터나 분석 결과의 파일 다운로드\r\n      - **사용자 정의 레이어 생성**: 새로운 레이어 직접 생성\r\n      - **고급 공간 분석**: 버퍼 분석, 오버레이 분석 등 복합 공간 분석\r\n\r\n      **주요 임무:**\r\n      1. **일반 대화 처리**: 인사, 감사 표현, 날씨 문의 등 일상적인 대화에 친근하고 자연스럽게 응답\r\n      2. **서비스 안내**: 지원 가능한 기능과 지원하지 않는 기능을 명확히 구분하여 안내\r\n      3. **기능 제한 안내**: 지원하지 않는 기능 요청 시 현재 제한사항을 정중하게 설명하고 대안 제시\r\n      4. **의도 명확화**: 사용자의 요청이 불분명할 때 'getUserInput'이나 'chooseOption'을 사용하여 정확한 의도를 파악\r\n      5. **지도 기능 유도**: 적절한 상황에서 지원 가능한 지도 기능들을 소개하고 안내\r\n\r\n      **지원하지 않는 기능 요청 시 응답 가이드라인:**\r\n      - 현재 해당 기능이 지원되지 않음을 정중하게 안내\r\n      - 가능한 경우 유사한 대안 기능 제안\r\n      - 향후 업데이트 계획이 있을 수 있음을 언급 (구체적인 일정은 제시하지 않음)\r\n      - 현재 사용 가능한 관련 기능들을 소개\r\n\r\n      **응답 스타일:**\r\n      - 친근하고 도움이 되는 톤으로 대화\r\n      - 한국어로 자연스럽게 소통\r\n      - 제한사항을 설명할 때도 긍정적이고 건설적인 톤 유지\r\n      - 복잡한 요청은 단계별로 안내\r\n      - 지도 관련 질문이면 지원 가능한 해당 기능을 추천\r\n\r\n      **예시 응답:**\r\n      사용자: \"데이터 업로드 가능해?\"\r\n      응답: \"죄송하지만 현재 개인 데이터 파일 업로드 기능은 지원하지 않습니다. 대신 기본으로 제공되는 레이어들(건물 정보, 행정경계 등)을 활용하실 수 있어요. 어떤 종류의 데이터를 찾고 계신지 알려주시면 관련된 기존 레이어를 추천해드릴 수 있습니다!\"\r\n\r\n      사용자: \"넌 뭘 잘해?\"\r\n      응답: \"안녕하세요! 저는 지도 서비스 전문 AI 어시스턴트입니다. 장소 검색, 길찾기, 레이어 추가, 배경지도 변경, 밀도 분석 등 다양한 지도 관련 기능을 도와드릴 수 있어요. 어떤 도움이 필요하신가요?\"\r\n\r\n      사용자가 편안하게 서비스를 이용할 수 있도록 따뜻하고 전문적인 도움을 제공하되, 기능 제한사항은 명확하고 정직하게 안내하세요.\r\n    `,\r\n  },\r\n  unsupported_feature: {\r\n    tools: {\r\n      ...HILTools,\r\n    },\r\n    system: `\r\n      ${COMMON_SYSTEM_PROMPTS.language}\r\n      ${COMMON_SYSTEM_PROMPTS.tone}\r\n      ${COMMON_SYSTEM_PROMPTS.interaction}\r\n      당신은 지원되지 않는 기능 요청을 전문적으로 처리하는 에이전트입니다.\r\n      사용자가 현재 시스템에서 지원하지 않는 기능을 요청했을 때, 명확하고 친절하게 제한사항을 설명하고 대안을 제시합니다.\r\n\r\n      **🚨 절대 지원하지 않는 기능 카테고리:**\r\n\r\n      **1. 주변/근처 POI 검색:**\r\n      - \"여기 근처에 맛집\", \"주변 카페\", \"이 근처 병원\", \"근처 편의점\" 등\r\n      - 현재 위치 기반 주변 시설 검색 기능\r\n\r\n      **2. 카테고리별 POI 목록:**\r\n      - \"맛집 목록\", \"카페 목록\", \"병원 목록\", \"전체 편의점 목록\" 등\r\n      - 특정 카테고리의 전체 시설 목록 제공\r\n\r\n      **3. 실시간 정보:**\r\n      - 실시간 교통정보, 교통상황, 정체 정보\r\n      - 실시간 날씨 정보\r\n\r\n      **4. 데이터 관리:**\r\n      - 개인 데이터 파일 업로드\r\n      - 기존 레이어 데이터 편집, 수정, 삭제\r\n      - 지도 데이터나 분석 결과 다운로드\r\n      - 사용자 정의 레이어 생성\r\n\r\n      **5. 고급 공간분석:**\r\n      - 버퍼 분석 (반경 분석)\r\n      - 오버레이 분석\r\n      - 네트워크 분석\r\n      - 복합 공간 분석\r\n\r\n      **6. 고급 지도 기능:**\r\n      - 3D 지도 표시\r\n      - 시간대별 데이터 애니메이션\r\n      - 사용자 정의 심볼 생성\r\n      - 고급 스타일링 옵션\r\n\r\n      **응답 가이드라인:**\r\n      1. **명확한 제한사항 설명**: 해당 기능이 현재 지원되지 않음을 정중하게 안내\r\n      2. **현재 시스템 범위 명시**: 지도 서비스의 현재 기능 범위를 명확히 설명\r\n      3. **실제 지원 기능만 제안**: 현재 시스템에서 실제로 사용 가능한 기능만 안내\r\n      4. **잘못된 기대 방지**: 지원하지 않는 기능에 대한 추가 질문이나 기대를 유발하지 않음\r\n\r\n      **현재 지원하는 기능 (이것만 안내):**\r\n      - **구체적인 장소 검색**: \"강남역 찾아줘\", \"서울시청 이동\"\r\n      - **키워드 기반 레이어 검색**: \"스타벅스\", \"서울\", \"편의점\" 등\r\n      - **두 지점 간 길찾기**: \"A에서 B까지 가는 길\"\r\n      - **밀도 분석**: 포인트 데이터의 공간적 밀집도 분석\r\n      - **레이어 필터링**: 조건에 따른 데이터 필터링\r\n      - **배경지도 변경**: 일반지도, 위성지도, 색각지도, 백지도\r\n\r\n      **응답 예시:**\r\n      \"죄송하지만 [요청된 기능]은 현재 지원하지 않습니다.\r\n\r\n      현재 저희 지도 서비스에서는 다음 기능들을 이용하실 수 있습니다:\r\n      - 구체적인 장소명 검색 (예: '강남역 찾아줘')\r\n      - 키워드 기반 레이어 추가 (예: '스타벅스 레이어 추가')\r\n      - 두 지점 간 길찾기 (예: '강남역에서 홍대까지')\r\n\r\n      이 중에서 도움이 될 만한 기능이 있으시면 말씀해 주세요!\"\r\n\r\n      사용자의 요청을 이해하고 공감하면서도, 현재 시스템의 한계를 명확히 전달하고\r\n      실제로 도움이 될 수 있는 대안을 적극적으로 제시하세요.\r\n    `,\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAMA;AACA;AAiBA;;;;;AAqBO,MAAM,uBAAuB;IAClC,mBAAmB;QACjB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,UAAU;YAAC;YAAa;YAAS;SAAU;QAC3C,aAAa;IACf;IACA,uBAAuB;QACrB,UAAU;YAAC;YAAS;YAAQ;YAAQ;SAAQ;QAC5C,aAAa;QACb,UAAU;YAAC;YAAS;YAAS;SAAQ;QACrC,aAAa;QACb,YAAY;YAAC;YAAU;YAAa;SAAS;IAC/C;IACA,eAAe;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,UAAU;YAAC;YAAY;YAAS;SAAU;QAC1C,aAAa;IACf;IACA,iBAAiB;QACf,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,UAAU;YAAC;YAAW;YAAS;SAAS;QACxC,aAAa;IACf;IACA,2BAA2B;QACzB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,UAAU;YAAC;YAAc;YAAW;SAAU;QAC9C,aAAa;QACb,YAAY;YAAC;YAAS;SAAK;IAC7B;IACA,uBAAuB;QACrB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;IACf;AACF;AAGO,MAAM,oBAAoB;IAC/B,YAAY;QACV,aAAa;YACX,qBAAqB,iBAAiB;YACtC,qBAAqB,qBAAqB;YAC1C,qBAAqB,aAAa;SACnC;QACD,WAAW;YAAC;YAAe;YAAc;SAAW;IACtD;IACA,aAAa;QACX,aAAa;YACX,qBAAqB,eAAe;YACpC,qBAAqB,qBAAqB;YAC1C,qBAAqB,yBAAyB;SAC/C;QACD,WAAW;YACT;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,kBAAkB;QAChB,aAAa;YACX,qBAAqB,yBAAyB;YAC9C,qBAAqB,eAAe;SACrC;QACD,WAAW;YAAC;YAAiB;SAAU;IACzC;IACA,SAAS;QACP,aAAa;YACX,qBAAqB,qBAAqB;YAC1C,qBAAqB,eAAe;SACrC;QACD,WAAW;YAAC;YAAW;SAAe;IACxC;AACF;AAGO,MAAM,wBAAwB;IACnC,UAAU,CAAC,eAAe,CAAC;IAC3B,MAAM,CAAC,+BAA+B,CAAC;IACvC,aAAa,CAAC,sEAAsE,CAAC;IACrF,WAAW,CAAC;;;;AAId,CAAC;AACD;AAKO,MAAM,aAAa;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,uBAAuB,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,QAAQ,uLAAA,CAAA,IAAC,CAAC,IAAI,CAAC,YAAY,QAAQ,CAAC;IACpC,SAAS,uLAAA,CAAA,IAAC,CACP,MAAM,GACN,QAAQ,CACP;IAEJ,aAAa,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC5C,aAAa,uLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC7C,iBAAiB,uLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,uLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC9C,mBAAmB,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC1B,OAAO,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,OAAO,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,MAAM,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,GAAG,QAAQ,GAAG,QAAQ,CAAC;AACzB;AAIO,MAAM,4BAAyC;IACpD,OAAO,CAAC;IACR,QAAQ,CAAC;IACP,EAAE,sBAAsB,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkDjC,EAAE,CAAA,GAAA,kJAAA,CAAA,6BAA0B,AAAD,IAAI;EACjC,CAAC;IACD,UAAU;IACV,cAAc;AAChB;AAEO,MAAM,eAA+C;IAC1D,iBAAiB;IACjB,YAAY;QACV,OAAO;YACL,eAAe,wIAAA,CAAA,yBAAsB;YACrC,cAAc,wIAAA,CAAA,eAAY;YAC1B,mBAAmB,wIAAA,CAAA,oBAAiB;YACpC,kBAAkB,wIAAA,CAAA,4BAAyB;YAC3C,aAAA,iPAAA,CAAA,cAAW;YACX,GAAG,wIAAA,CAAA,WAAQ;QACb;QACA,QAAQ,CAAC;MACP,EAAE,sBAAsB,QAAQ,CAAC;MACjC,EAAE,sBAAsB,IAAI,CAAC;MAC7B,EAAE,sBAAsB,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwFtC,CAAC;IACH;IACA,aAAa;QACX,OAAO;YACL,eAAA,wIAAA,CAAA,gBAAa;YACb,YAAA,wIAAA,CAAA,aAAU;YACV,oBAAA,wIAAA,CAAA,qBAAkB;YAClB,aAAA,iPAAA,CAAA,cAAW;YACX,GAAG,wIAAA,CAAA,WAAQ;QACb;QACA,QAAQ,CAAC;MACP,EAAE,sBAAsB,QAAQ,CAAC;MACjC,EAAE,sBAAsB,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuF/B,CAAC;IACH;IACA,aAAa;QACX,OAAO;YACL,UAAA,iPAAA,CAAA,WAAQ;YACR,cAAA,wIAAA,CAAA,eAAY;YACZ,yBAAA,wIAAA,CAAA,0BAAuB;YACvB,kBAAA,wIAAA,CAAA,mBAAgB;YAChB,aAAA,wIAAA,CAAA,cAAW;YACX,oBAAA,iPAAA,CAAA,qBAAkB;YAClB,0BAAA,wIAAA,CAAA,2BAAwB;YACxB,mBAAA,wIAAA,CAAA,oBAAiB;YACjB,GAAG,wIAAA,CAAA,WAAQ;QACb;QACA,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;MAwBP,EAAE,sBAAsB,QAAQ,CAAC;MACjC,EAAE,sBAAsB,WAAW,CAAC;IACtC,CAAC;IACH;IACA,kBAAkB;QAChB,OAAO;YAAE,wBAAA,iPAAA,CAAA,yBAAsB;YAAE,cAAA,wIAAA,CAAA,eAAY;YAAE,UAAA,iPAAA,CAAA,WAAQ;YAAE,GAAG,wIAAA,CAAA,WAAQ;QAAC;QACrE,QAAQ,CAAC;MACP,EAAE,sBAAsB,QAAQ,CAAC;MACjC,EAAE,sBAAsB,IAAI,CAAC;MAC7B,EAAE,sBAAsB,WAAW,CAAC;MACpC,EAAE,sBAAsB,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsFpC,CAAC;IACH;IACA,eAAe;QACb,OAAO;YACL,GAAG,wIAAA,CAAA,WAAQ;QACb;QACA,QAAQ,CAAC;MACP,EAAE,sBAAsB,QAAQ,CAAC;MACjC,EAAE,sBAAsB,IAAI,CAAC;MAC7B,EAAE,sBAAsB,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiDtC,CAAC;IACH;IACA,qBAAqB;QACnB,OAAO;YACL,GAAG,wIAAA,CAAA,WAAQ;QACb;QACA,QAAQ,CAAC;MACP,EAAE,sBAAsB,QAAQ,CAAC;MACjC,EAAE,sBAAsB,IAAI,CAAC;MAC7B,EAAE,sBAAsB,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA8DtC,CAAC;IACH;AACF", "debugId": null}}, {"offset": {"line": 3588, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/api/chat/evaluate.ts"], "sourcesContent": ["import { openai } from \"@ai-sdk/openai\";\nimport { generateObject } from \"ai\";\nimport { z } from \"zod\";\n\n// 평가 결과 스키마 (단순화)\nexport const evaluationSchema = z.object({\n  isCompleted: z.boolean().describe(\"사용자의 의도가 충분히 해결되었는지 (도구 호출 여부와 무관)\"),\n  reason: z.string().describe(\"완료/미완료 판단 이유\"),\n  improvementSuggestions: z.array(z.string()).optional().describe(\"미완료 시 개선 제안사항\"),\n});\n\nexport type EvaluationResult = z.infer<typeof evaluationSchema>;\n\n// 평가자 함수\nexport async function evaluateAgentResult(\n  intentMessage: string,\n  agentResponse: string,\n  toolCalls: any[]\n): Promise<EvaluationResult> {\n  try {\n    // HIL 도구 호출 확인 (최우선 처리)\n    const hilTools = ['chooseOption', 'getUserInput', 'confirmWithCheckbox', 'getLocation'];\n    const hasHILTool = toolCalls.some(call => hilTools.includes(call.toolName));\n\n    if (hasHILTool) {\n      return {\n        isCompleted: true,\n        reason: \"사용자와의 상호작용을 진행 중이므로 완료로 판단합니다.\"\n      };\n    }\n    const system = `당신은 AI Agent의 작업 수행 결과를 평가하는 전문가입니다.\n\n      **핵심 평가 원칙:**\n      1. 도구 호출 여부와 관계없이 사용자의 의도가 충분히 해결되었는지 판단\n      2. \"안녕하세요\" 같은 간단한 인사는 도구 호출 없이도 완료될 수 있음\n      3. 단순하게 \"완료\" vs \"미완료\" 두 가지로만 구분\n\n      **완료 판단 기준 (isCompleted: true):**\n      - 사용자의 원래 요청이 충분히 해결됨\n      - 의도분석에서 제시한 목표가 달성됨\n      - 더 이상 추가 작업이 필요하지 않음\n\n      **미완료 판단 기준 (isCompleted: false):**\n      - 사용자 요청이 아직 해결되지 않음\n      - 필요한 작업이 수행되지 않음\n      - 도구 호출 실패나 에러 발생\n      - \"분석을 시작하겠습니다\" 등의 예고만 하고 실제 작업 미수행\n\n      **판단 가이드라인:**\n      - 전체 맥락을 종합적으로 고려하여 판단\n      - 특정 키워드나 패턴에만 의존하지 말 것\n      - 사용자 관점에서 요청이 해결되었는지 중점 평가\n      `\n      ;\n\n    const prompt = `다음 Agent 수행 결과를 평가하세요:\n\n**의도분석 메시지:**\n${intentMessage}\n\n**Agent 응답:**\n${agentResponse}\n\n**도구 정보:**\n${toolCalls.length > 0 ? \n  toolCalls.map((call) => {\n    return `toolName: ${call.toolName}, args: ${JSON.stringify(call.args)}`;\n  }).join(\"\\n\")\n  : \"도구 호출 없음\"}\n\n**도구 호출 결과:**\n${\n  toolCalls.length > 0\n    ? toolCalls\n        .map((call) => {\n          return `${JSON.stringify(call.result)}`;\n        })\n        .join(\"\\n\")\n    : \"\"\n}\n\n**종합 평가 요청:**\n\n**평가 기준**\n1. **의도분석 목표 달성도**: 요청된 작업이 실제로 완료되었는가?\n2. **작업 진행 상태 파악**:\n   - 준비만 하고 핵심 작업 미수행 → isCompleted: false\n   - 예: \"분석을 시작하겠습니다\" 후 performDensityAnalysis 미호출\n   - 예: \"스타일을 변경하겠습니다\" 후 updateLayerStyle 미호출\n3. **에러 및 실패 상황**: 도구 호출 실패나 잘못된 사용 → isCompleted: false\n\n**🚨 핵심 판단 원칙:**\n- **HIL 도구 호출 시**: 무조건 isCompleted: true (최우선)\n- **작업 예고 후 미수행**: isCompleted: false\n- **실제 작업 완료**: isCompleted: true\n- **에러 발생**: isCompleted: false\n`;\n\n\n    console.log(\"=== 평가자 호출 ===\");\n    console.log(\"시스템 프롬프트:\", system);\n    console.log(\"평가 프롬프트:\", prompt);\n\n    const { object: evaluation } = await generateObject({\n      model: openai(\"gpt-4.1-nano\"),\n      schema: evaluationSchema,\n      system,\n      prompt,\n      temperature: 0\n    });\n\n    return evaluation;\n  } catch (error) {\n    console.error(\"평가자 실행 실패:\", error);\n\n    // 평가자 실패 시 기본값 반환 (미완료로 처리하여 재시도)\n    return {\n      isCompleted: false,\n      reason: \"평가자 오류로 인한 기본 판단: 작업이 미완료된 것으로 처리\",\n      improvementSuggestions: [\n        \"의도분석 메시지에 따라 필요한 도구를 호출하세요\",\n        \"구체적인 작업을 수행하여 사용자의 요청을 완료하세요\"\n      ],\n    };\n  }\n}\n\n// 개선 메시지 생성\nexport function createImprovementMessage(evaluation: EvaluationResult): string {\n  const suggestions = evaluation.improvementSuggestions || [\"작업을 완료하기 위해 필요한 도구를 호출하세요\"];\n  const suggestionText = suggestions.join(\"\\n- \");\n\n  return `🔄 이전 시도가 불완전했습니다. 다음 사항을 개선하여 작업을 완료하세요:\n\n**미완료 이유:**\n${evaluation.reason}\n\n**개선사항:**\n- ${suggestionText}\n\n**중요:** 설명보다는 실제 도구 호출을 통해 작업을 수행하세요.`;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAGO,MAAM,mBAAmB,uLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,aAAa,uLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ,CAAC;IAClC,QAAQ,uLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5B,wBAAwB,uLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,uLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,GAAG,QAAQ,CAAC;AAClE;AAKO,eAAe,oBACpB,aAAqB,EACrB,aAAqB,EACrB,SAAgB;IAEhB,IAAI;QACF,wBAAwB;QACxB,MAAM,WAAW;YAAC;YAAgB;YAAgB;YAAuB;SAAc;QACvF,MAAM,aAAa,UAAU,IAAI,CAAC,CAAA,OAAQ,SAAS,QAAQ,CAAC,KAAK,QAAQ;QAEzE,IAAI,YAAY;YACd,OAAO;gBACL,aAAa;gBACb,QAAQ;YACV;QACF;QACA,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;MAsBd,CAAC;QAGH,MAAM,SAAS,CAAC;;;AAGpB,EAAE,cAAc;;;AAGhB,EAAE,cAAc;;;AAGhB,EAAE,UAAU,MAAM,GAAG,IACnB,UAAU,GAAG,CAAC,CAAC;YACb,OAAO,CAAC,UAAU,EAAE,KAAK,QAAQ,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,KAAK,IAAI,GAAG;QACzE,GAAG,IAAI,CAAC,QACN,WAAW;;;AAGf,EACE,UAAU,MAAM,GAAG,IACf,UACG,GAAG,CAAC,CAAC;YACJ,OAAO,GAAG,KAAK,SAAS,CAAC,KAAK,MAAM,GAAG;QACzC,GACC,IAAI,CAAC,QACR,GACL;;;;;;;;;;;;;;;;;AAiBD,CAAC;QAGG,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,aAAa;QACzB,QAAQ,GAAG,CAAC,YAAY;QAExB,MAAM,EAAE,QAAQ,UAAU,EAAE,GAAG,MAAM,CAAA,GAAA,gPAAA,CAAA,iBAAc,AAAD,EAAE;YAClD,OAAO,CAAA,GAAA,gPAAA,CAAA,SAAM,AAAD,EAAE;YACd,QAAQ;YACR;YACA;YACA,aAAa;QACf;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAE5B,kCAAkC;QAClC,OAAO;YACL,aAAa;YACb,QAAQ;YACR,wBAAwB;gBACtB;gBACA;aACD;QACH;IACF;AACF;AAGO,SAAS,yBAAyB,UAA4B;IACnE,MAAM,cAAc,WAAW,sBAAsB,IAAI;QAAC;KAA4B;IACtF,MAAM,iBAAiB,YAAY,IAAI,CAAC;IAExC,OAAO,CAAC;;;AAGV,EAAE,WAAW,MAAM,CAAC;;;EAGlB,EAAE,eAAe;;qCAEkB,CAAC;AACtC", "debugId": null}}, {"offset": {"line": 3722, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/api/chat/execute-with-retry.ts"], "sourcesContent": ["import {\n  streamText,\n  createDataStreamResponse,\n  generateObject,\n  smoothStream,\n  appendResponseMessages,\n} from \"ai\";\nimport { evaluateAgentResult } from \"./evaluate\";\nimport { agentConfigs, AgentName } from \"./agents\";\nimport { openai } from \"@ai-sdk/openai\";\nimport { saveMessages } from \"@/lib/db/queries\";\nimport { generateUUID } from \"@/lib/utils\";\n\ninterface ExecuteWithRetryOptions {\n  model: any;\n  agentName: AgentName;\n  messages: any[];\n  stateMessage: any;\n  intentMessage: string;\n  dataStream: any;\n  session: any;\n  enable_smart_navigation: boolean;\n  isNonGeonProvider: boolean;\n  iteration?: number;\n  maxIterations?: number;\n  onEvaluationComplete?: (evaluationResult: any) => void;\n  chatId?: string; // AI 응답을 DB에 저장하기 위한 chatId 추가\n}\n\n/**\n * Agent 실행 및 평가 결과 반환\n */\nexport async function executeAgentWithRetry({\n  model,\n  agentName,\n  messages,\n  stateMessage,\n  intentMessage,\n  dataStream,\n  session,\n  enable_smart_navigation,\n  isNonGeonProvider,\n  iteration = 0, // 0부터 시작하여 재시도 시마다 증가\n  maxIterations = 3,\n  onEvaluationComplete,\n  chatId,\n}: ExecuteWithRetryOptions) {\n  console.log(`=== Agent 실행 (${iteration + 1}/${maxIterations}) ===`);\n\n  const agentConfig = agentConfigs[agentName];\n\n  // 평가를 위한 정보 수집 변수들\n  let allToolCalls: any[] = [];\n  let agentResponse = \"\";\n  let evaluationResult: any = null;\n\n  const result = streamText({\n    model,\n    messages: [\n      {\n        role: \"system\",\n        content: agentConfig.system,\n      },\n      stateMessage,\n      ...messages,\n    ],\n    temperature: 0,\n    tools: agentConfig.tools,\n    toolCallStreaming: true,\n    maxSteps: agentConfig.maxSteps || 5,\n    experimental_transform: smoothStream(),\n    experimental_continueSteps: true,\n    // experimental_repairToolCall: async ({\n    //   toolCall,\n    //   tools,\n    //   parameterSchema,\n    //   error,\n    // }) => {\n    //   console.log(\n    //     `[TOOL_REPAIR] Attempting to repair tool call: ${toolCall.toolName}`\n    //   );\n    //   console.log(`[TOOL_REPAIR] Error: ${error.message}`);\n\n    //   if (error.message.includes(\"No such tool\")) {\n    //     console.log(`[TOOL_REPAIR] Tool not found, cannot repair`);\n    //     return null;\n    //   }\n\n    //   const tool = tools[toolCall.toolName as keyof typeof tools];\n    //   const { object: repairedArgs } = await generateObject({\n    //     model: openai(\"gpt-4.1-nano\", { structuredOutputs: true }),\n    //     schema: tool.parameters,\n    //     prompt: [\n    //       `The model tried to call the tool \"${toolCall.toolName}\" with the following arguments:`,\n    //       JSON.stringify(toolCall.args),\n    //       `The tool accepts the following schema:`,\n    //       JSON.stringify(parameterSchema(toolCall)),\n    //       \"Please fix the arguments.\",\n    //     ].join(\"\\n\"),\n    //   });\n\n    //   return { ...toolCall, args: JSON.stringify(repairedArgs) };\n    // },\n    ...(isNonGeonProvider\n      ? {}\n      : {\n          providerOptions: {\n            geon: {\n              metadata: {\n                chat_template_kwargs: { enable_thinking: false },\n              },\n            },\n          },\n        }),\n    onStepFinish: ({ toolCalls, text, toolResults }) => {\n      // 평가를 위한 정보 수집\n      if (toolCalls && toolCalls.length > 0) {\n        allToolCalls.push(...toolCalls);\n      }\n\n      // 도구 결과도 수집 (다음 시도에서 참조할 수 있도록)\n      if (toolResults && toolResults.length > 0) {\n        // toolResults를 allToolCalls에 매핑하여 저장\n        toolResults.forEach((result: any, index) => {\n          const toolCallIndex =\n            allToolCalls.length - toolResults.length + index;\n          if (allToolCalls[toolCallIndex]) {\n            const toolCall = allToolCalls[toolCallIndex];\n            const tool = agentConfig.tools[toolCall.toolName];\n\n            // toToolResultContent가 있는 도구는 프루닝된 결과를, 없는 도구는 전체 결과를 저장\n            if (\n              tool &&\n              \"experimental_toToolResultContent\" in tool &&\n              tool.experimental_toToolResultContent\n            ) {\n              // 프루닝된 결과 저장 (평가자용)\n              (toolCall as any).result = tool.experimental_toToolResultContent(\n                result.result\n              );\n            } else {\n              // 전체 결과 저장\n              (toolCall as any).result = result.result;\n            }\n          }\n        });\n      }\n\n      // 도구 호출 정보를 어노테이션으로 전송\n      if (toolCalls && toolCalls.length > 0) {\n        toolCalls.forEach((toolCall) => {\n          dataStream.writeMessageAnnotation({\n            type: \"tool_call\",\n            toolName: toolCall.toolName,\n            args: toolCall.args,\n            enableSmartNavigation: enable_smart_navigation,\n          });\n        });\n      }\n    },\n    onFinish: async ({ text, toolCalls, response }) => {\n      // 최종 정보 수집\n      if (text) {\n        agentResponse += text;\n      }\n      if (toolCalls && toolCalls.length > 0) {\n        allToolCalls.push(...toolCalls);\n      }\n\n      // 평가자 실행 시작 알림\n      dataStream.writeMessageAnnotation({\n        type: \"evaluation_start\",\n        iteration: iteration + 1,\n        maxIterations,\n        message: \"작업 결과를 평가하고 있습니다...\",\n      });\n\n      // 평가자 실행\n      try {\n        evaluationResult = await evaluateAgentResult(\n          intentMessage,\n          agentResponse,\n          allToolCalls\n        );\n\n        // 평가 완료 어노테이션\n        const evaluationMessage = evaluationResult.isCompleted\n          ? \"작업이 성공적으로 완료되었습니다\"\n          : \"작업이 미완료되어 계속 진행합니다\";\n\n        dataStream.writeMessageAnnotation({\n          type: \"evaluation_completed\",\n          iteration: iteration + 1,\n          maxIterations,\n          isCompleted: evaluationResult.isCompleted,\n          shouldContinue: !evaluationResult.isCompleted,\n          message: evaluationMessage,\n          reason: evaluationResult.reason,\n          improvementSuggestions: evaluationResult.improvementSuggestions,\n        });\n\n        // 콜백으로 평가 결과와 도구 호출 정보 전달\n        if (onEvaluationComplete) {\n          onEvaluationComplete({\n            ...evaluationResult,\n            toolCalls: allToolCalls,\n            agentResponse: agentResponse,\n          });\n        }\n      } catch (error) {\n        console.error(\"평가자 실행 실패:\", error);\n        evaluationResult = {\n          isCompleted: false,\n          reason: \"평가자 실행 중 오류가 발생하여 미완료로 처리\",\n          improvementSuggestions: [\"평가자 오류로 인한 재시도가 필요합니다\"],\n        };\n      }\n\n      // AI SDK 4 공식 패턴: response.messages를 사용하여 tool calls와 results 포함하여 저장\n      if (\n        session.user?.id &&\n        chatId &&\n        response.messages &&\n        response.messages.length > 0\n      ) {\n        try {\n          // AI SDK의 appendResponseMessages를 사용하여 기존 메시지에 응답 메시지들을 추가\n          const updatedMessages = appendResponseMessages({\n            messages,\n            responseMessages: response.messages,\n          });\n\n          // 새로 추가된 메시지들만 저장 (기존 메시지는 이미 저장됨)\n          const newMessages = updatedMessages.slice(messages.length);\n\n          const messagesToSave = newMessages.map((message) => {\n            const messageId = generateUUID();\n\n            if (message.role === \"assistant\") {\n              dataStream.writeMessageAnnotation({\n                messageIdFromServer: messageId,\n              });\n            }\n\n            return {\n              id: messageId,\n              chatId,\n              role: message.role,\n              content: message.content, // 기존 호환성을 위해 유지\n              parts: message.parts || [], // AI SDK parts 배열\n              attachments: message.experimental_attachments || [], // AI SDK attachments\n              createdAt: new Date(),\n            };\n          });\n\n          if (messagesToSave.length > 0) {\n            await saveMessages({\n              messages: messagesToSave,\n            });\n          }\n        } catch (error) {\n          console.error(\"메시지 저장 실패:\", error);\n        }\n      }\n\n      // 작업 완료 메시지 전송\n      if (session.user?.id) {\n        try {\n          dataStream.writeMessageAnnotation({\n            type: \"agent_completed\",\n            agent: agentName,\n            message: evaluationResult?.isCompleted\n              ? \"작업이 완료되었습니다.\"\n              : \"작업이 진행 중입니다.\",\n            finalEvaluation: evaluationResult,\n            iteration: iteration + 1,\n            maxIterations,\n          });\n        } catch (error) {\n          console.error(\"메시지 저장 실패:\", error);\n        }\n      }\n    },\n  });\n\n  return result;\n}\n\n/**\n * 평가 결과를 포함한 Agent 실행 래퍼\n */\nexport async function executeAgentWithEvaluation({\n  model,\n  agentName,\n  messages,\n  stateMessage,\n  intentMessage,\n  dataStream,\n  session,\n  enable_smart_navigation,\n  isNonGeonProvider,\n  iteration = 0,\n  maxIterations = 3,\n  chatId,\n}: ExecuteWithRetryOptions): Promise<{\n  streamResult: any;\n  evaluationResult: any;\n}> {\n  let evaluationResult: any = null;\n\n  // Agent 실행\n  const result = await executeAgentWithRetry({\n    model,\n    agentName,\n    messages,\n    stateMessage,\n    intentMessage,\n    dataStream,\n    session,\n    enable_smart_navigation,\n    isNonGeonProvider,\n    iteration,\n    maxIterations,\n    chatId,\n  });\n\n  // 스트림을 데이터스트림에 병합하고 완료 대기\n  await new Promise<void>((resolve) => {\n    result.mergeIntoDataStream(dataStream, {\n      sendReasoning: true,\n    });\n\n    // onFinish에서 평가 결과가 설정될 때까지 대기\n    const checkEvaluation = () => {\n      // 여기서 평가 결과를 가져오는 로직 필요\n      // 현재는 간단히 완료로 처리\n      setTimeout(() => {\n        resolve();\n      }, 1000);\n    };\n\n    checkEvaluation();\n  });\n\n  return {\n    streamResult: result,\n    evaluationResult: evaluationResult,\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA;AAOA;AACA;AAEA;AACA;;;;;;AAqBO,eAAe,sBAAsB,EAC1C,KAAK,EACL,SAAS,EACT,QAAQ,EACR,YAAY,EACZ,aAAa,EACb,UAAU,EACV,OAAO,EACP,uBAAuB,EACvB,iBAAiB,EACjB,YAAY,CAAC,EACb,gBAAgB,CAAC,EACjB,oBAAoB,EACpB,MAAM,EACkB;IACxB,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,EAAE,CAAC,EAAE,cAAc,KAAK,CAAC;IAElE,MAAM,cAAc,yIAAA,CAAA,eAAY,CAAC,UAAU;IAE3C,mBAAmB;IACnB,IAAI,eAAsB,EAAE;IAC5B,IAAI,gBAAgB;IACpB,IAAI,mBAAwB;IAE5B,MAAM,SAAS,CAAA,GAAA,gPAAA,CAAA,aAAU,AAAD,EAAE;QACxB;QACA,UAAU;YACR;gBACE,MAAM;gBACN,SAAS,YAAY,MAAM;YAC7B;YACA;eACG;SACJ;QACD,aAAa;QACb,OAAO,YAAY,KAAK;QACxB,mBAAmB;QACnB,UAAU,YAAY,QAAQ,IAAI;QAClC,wBAAwB,CAAA,GAAA,gPAAA,CAAA,eAAY,AAAD;QACnC,4BAA4B;QAC5B,wCAAwC;QACxC,cAAc;QACd,WAAW;QACX,qBAAqB;QACrB,WAAW;QACX,UAAU;QACV,iBAAiB;QACjB,2EAA2E;QAC3E,OAAO;QACP,0DAA0D;QAE1D,kDAAkD;QAClD,kEAAkE;QAClE,mBAAmB;QACnB,MAAM;QAEN,iEAAiE;QACjE,4DAA4D;QAC5D,kEAAkE;QAClE,+BAA+B;QAC/B,gBAAgB;QAChB,iGAAiG;QACjG,uCAAuC;QACvC,kDAAkD;QAClD,mDAAmD;QACnD,qCAAqC;QACrC,oBAAoB;QACpB,QAAQ;QAER,gEAAgE;QAChE,KAAK;QACL,GAAI,oBACA,CAAC,IACD;YACE,iBAAiB;gBACf,MAAM;oBACJ,UAAU;wBACR,sBAAsB;4BAAE,iBAAiB;wBAAM;oBACjD;gBACF;YACF;QACF,CAAC;QACL,cAAc,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,WAAW,EAAE;YAC7C,eAAe;YACf,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;gBACrC,aAAa,IAAI,IAAI;YACvB;YAEA,gCAAgC;YAChC,IAAI,eAAe,YAAY,MAAM,GAAG,GAAG;gBACzC,qCAAqC;gBACrC,YAAY,OAAO,CAAC,CAAC,QAAa;oBAChC,MAAM,gBACJ,aAAa,MAAM,GAAG,YAAY,MAAM,GAAG;oBAC7C,IAAI,YAAY,CAAC,cAAc,EAAE;wBAC/B,MAAM,WAAW,YAAY,CAAC,cAAc;wBAC5C,MAAM,OAAO,YAAY,KAAK,CAAC,SAAS,QAAQ,CAAC;wBAEjD,yDAAyD;wBACzD,IACE,QACA,sCAAsC,QACtC,KAAK,gCAAgC,EACrC;4BACA,oBAAoB;4BACnB,SAAiB,MAAM,GAAG,KAAK,gCAAgC,CAC9D,OAAO,MAAM;wBAEjB,OAAO;4BACL,WAAW;4BACV,SAAiB,MAAM,GAAG,OAAO,MAAM;wBAC1C;oBACF;gBACF;YACF;YAEA,uBAAuB;YACvB,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;gBACrC,UAAU,OAAO,CAAC,CAAC;oBACjB,WAAW,sBAAsB,CAAC;wBAChC,MAAM;wBACN,UAAU,SAAS,QAAQ;wBAC3B,MAAM,SAAS,IAAI;wBACnB,uBAAuB;oBACzB;gBACF;YACF;QACF;QACA,UAAU,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE;YAC5C,WAAW;YACX,IAAI,MAAM;gBACR,iBAAiB;YACnB;YACA,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;gBACrC,aAAa,IAAI,IAAI;YACvB;YAEA,eAAe;YACf,WAAW,sBAAsB,CAAC;gBAChC,MAAM;gBACN,WAAW,YAAY;gBACvB;gBACA,SAAS;YACX;YAEA,SAAS;YACT,IAAI;gBACF,mBAAmB,MAAM,CAAA,GAAA,2IAAA,CAAA,sBAAmB,AAAD,EACzC,eACA,eACA;gBAGF,cAAc;gBACd,MAAM,oBAAoB,iBAAiB,WAAW,GAClD,sBACA;gBAEJ,WAAW,sBAAsB,CAAC;oBAChC,MAAM;oBACN,WAAW,YAAY;oBACvB;oBACA,aAAa,iBAAiB,WAAW;oBACzC,gBAAgB,CAAC,iBAAiB,WAAW;oBAC7C,SAAS;oBACT,QAAQ,iBAAiB,MAAM;oBAC/B,wBAAwB,iBAAiB,sBAAsB;gBACjE;gBAEA,0BAA0B;gBAC1B,IAAI,sBAAsB;oBACxB,qBAAqB;wBACnB,GAAG,gBAAgB;wBACnB,WAAW;wBACX,eAAe;oBACjB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,cAAc;gBAC5B,mBAAmB;oBACjB,aAAa;oBACb,QAAQ;oBACR,wBAAwB;wBAAC;qBAAwB;gBACnD;YACF;YAEA,sEAAsE;YACtE,IACE,QAAQ,IAAI,EAAE,MACd,UACA,SAAS,QAAQ,IACjB,SAAS,QAAQ,CAAC,MAAM,GAAG,GAC3B;gBACA,IAAI;oBACF,2DAA2D;oBAC3D,MAAM,kBAAkB,CAAA,GAAA,gPAAA,CAAA,yBAAsB,AAAD,EAAE;wBAC7C;wBACA,kBAAkB,SAAS,QAAQ;oBACrC;oBAEA,mCAAmC;oBACnC,MAAM,cAAc,gBAAgB,KAAK,CAAC,SAAS,MAAM;oBAEzD,MAAM,iBAAiB,YAAY,GAAG,CAAC,CAAC;wBACtC,MAAM,YAAY,CAAA,GAAA,8GAAA,CAAA,eAAY,AAAD;wBAE7B,IAAI,QAAQ,IAAI,KAAK,aAAa;4BAChC,WAAW,sBAAsB,CAAC;gCAChC,qBAAqB;4BACvB;wBACF;wBAEA,OAAO;4BACL,IAAI;4BACJ;4BACA,MAAM,QAAQ,IAAI;4BAClB,SAAS,QAAQ,OAAO;4BACxB,OAAO,QAAQ,KAAK,IAAI,EAAE;4BAC1B,aAAa,QAAQ,wBAAwB,IAAI,EAAE;4BACnD,WAAW,IAAI;wBACjB;oBACF;oBAEA,IAAI,eAAe,MAAM,GAAG,GAAG;wBAC7B,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;4BACjB,UAAU;wBACZ;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,cAAc;gBAC9B;YACF;YAEA,eAAe;YACf,IAAI,QAAQ,IAAI,EAAE,IAAI;gBACpB,IAAI;oBACF,WAAW,sBAAsB,CAAC;wBAChC,MAAM;wBACN,OAAO;wBACP,SAAS,kBAAkB,cACvB,iBACA;wBACJ,iBAAiB;wBACjB,WAAW,YAAY;wBACvB;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,cAAc;gBAC9B;YACF;QACF;IACF;IAEA,OAAO;AACT;AAKO,eAAe,2BAA2B,EAC/C,KAAK,EACL,SAAS,EACT,QAAQ,EACR,YAAY,EACZ,aAAa,EACb,UAAU,EACV,OAAO,EACP,uBAAuB,EACvB,iBAAiB,EACjB,YAAY,CAAC,EACb,gBAAgB,CAAC,EACjB,MAAM,EACkB;IAIxB,IAAI,mBAAwB;IAE5B,WAAW;IACX,MAAM,SAAS,MAAM,sBAAsB;QACzC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,0BAA0B;IAC1B,MAAM,IAAI,QAAc,CAAC;QACvB,OAAO,mBAAmB,CAAC,YAAY;YACrC,eAAe;QACjB;QAEA,+BAA+B;QAC/B,MAAM,kBAAkB;YACtB,wBAAwB;YACxB,iBAAiB;YACjB,WAAW;gBACT;YACF,GAAG;QACL;QAEA;IACF;IAEA,OAAO;QACL,cAAc;QACd,kBAAkB;IACpB;AACF", "debugId": null}}, {"offset": {"line": 3980, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/api/chat/retry-handler.ts"], "sourcesContent": ["import { executeAgentWithRetry } from \"./execute-with-retry\";\nimport { createImprovementMessage } from \"./evaluate\";\n\ninterface RetryHandlerOptions {\n  model: any;\n  agentName: any;\n  messages: any[];\n  stateMessage: any;\n  intentMessage: string;\n  dataStream: any;\n  session: any;\n  enable_smart_navigation: boolean;\n  isNonGeonProvider: boolean;\n  maxIterations?: number;\n  chatId?: string; // AI 응답을 DB에 저장하기 위한 chatId 추가\n}\n\n/**\n * 서버 기반 재시도 핸들러\n * 평가 결과에 따라 자동으로 재시도를 수행합니다.\n */\nexport async function handleAgentWithRetry({\n  model,\n  agentName,\n  messages,\n  stateMessage,\n  intentMessage,\n  dataStream,\n  session,\n  enable_smart_navigation,\n  isNonGeonProvider,\n  maxIterations = 3,\n  chatId\n}: RetryHandlerOptions) {\n  \n  let currentMessages = [...messages];\n  let iteration = 0; // 0부터 시작: 첫 시도는 0, 재시도는 1, 2, ...\n  let previousToolCalls: any[] = []; // 이전 시도의 도구 호출 결과 저장\n\n  while (iteration < maxIterations) {\n    console.log(`=== 재시도 핸들러: ${iteration + 1}/${maxIterations} ===`);\n    console.log(\"Current Messages:\", JSON.stringify(currentMessages));\n\n    let evaluationResult: any = null;\n    let streamCompleted = false;\n\n    // Agent 실행 (콜백으로 평가 결과 수집)\n    const result = await executeAgentWithRetry({\n      model,\n      agentName,\n      messages: currentMessages,\n      stateMessage,\n      intentMessage,\n      dataStream,\n      session,\n      enable_smart_navigation,\n      isNonGeonProvider,\n      iteration,\n      maxIterations,\n      chatId,\n      onEvaluationComplete: (evaluation) => {\n        evaluationResult = evaluation;\n        // 도구 호출 정보 저장 (다음 재시도에서 사용)\n        if (evaluation.toolCalls) {\n          previousToolCalls = evaluation.toolCalls;\n        }\n        streamCompleted = true;\n      }\n    });\n\n    // 스트림을 데이터스트림에 병합\n    result.mergeIntoDataStream(dataStream, {\n      sendReasoning: true,\n    });\n\n    // 평가 완료까지 대기\n    while (!streamCompleted) {\n      await new Promise(resolve => setTimeout(resolve, 100));\n    }\n\n    // 완료 여부 판단 (단순화된 기준)\n    if (evaluationResult?.isCompleted) {\n      console.log(\"=== 작업 완료 ===\");\n\n      // 최종 완료 어노테이션 (첫 번째 시도에서 성공한 경우는 제외)\n      if (iteration > 0) {\n        dataStream.writeMessageAnnotation({\n          type: \"retry_completed\",\n          totalIterations: iteration + 1,\n          maxIterations,\n          finalResult: \"success\",\n          message: \"모든 작업이 성공적으로 완료되었습니다\"\n        });\n      }\n\n      break;\n    }\n\n    // 재시도 준비\n    if (iteration < maxIterations - 1) {\n      console.log(\"=== 재시도 준비 중 ===\");\n\n      // 개선 메시지를 시스템 메시지로 추가\n      const improvementMessage = createImprovementMessage(evaluationResult);\n      currentMessages = [...currentMessages, {\n        role: \"system\",\n        content: improvementMessage\n      }];\n\n      // 이전 도구 호출 결과를 assistant 메시지로 추가 (컨텍스트 유지)\n      if (previousToolCalls.length > 0) {\n        const toolCallsContext = previousToolCalls.map((toolCall: any) => {\n          const args = JSON.stringify(toolCall.args);\n          const result = toolCall.result ? JSON.stringify(toolCall.result).substring(0, 200) + '...' : '결과 없음';\n          return `${toolCall.toolName}(${args}) → ${result}`;\n        }).join('\\n');\n\n        currentMessages = [...currentMessages, {\n          role: \"assistant\",\n          content: `이전 시도에서 수행한 작업 결과:\\n${toolCallsContext}\\n\\n위 결과를 참고하여 재시도하세요.`\n        }];\n      }\n\n      // 재시도 시작 알림\n      dataStream.writeMessageAnnotation({\n        type: \"retry_starting\",\n        iteration: iteration + 2,\n        maxIterations,\n        message: `${iteration + 2}번째 시도를 시작합니다`,\n        reason: evaluationResult.reason,\n        improvementSuggestions: evaluationResult.improvementSuggestions || []\n      });\n\n      // iteration 증가: 작업이 미완료이므로 다음 시도 진행\n      iteration++;\n    } else {\n      console.log(\"=== 최대 재시도 횟수 도달 ===\");\n\n      // 최대 재시도 도달 어노테이션\n      dataStream.writeMessageAnnotation({\n        type: \"retry_limit_reached\",\n        totalIterations: maxIterations,\n        maxIterations,\n        message: `⏰ 최대 재시도 횟수(${maxIterations}회)에 도달했습니다`,\n        finalEvaluation: evaluationResult\n      });\n\n      break;\n    }\n  }\n  \n  return \"완료\";\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAoBO,eAAe,qBAAqB,EACzC,KAAK,EACL,SAAS,EACT,QAAQ,EACR,YAAY,EACZ,aAAa,EACb,UAAU,EACV,OAAO,EACP,uBAAuB,EACvB,iBAAiB,EACjB,gBAAgB,CAAC,EACjB,MAAM,EACc;IAEpB,IAAI,kBAAkB;WAAI;KAAS;IACnC,IAAI,YAAY,GAAG,kCAAkC;IACrD,IAAI,oBAA2B,EAAE,EAAE,qBAAqB;IAExD,MAAO,YAAY,cAAe;QAChC,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,EAAE,CAAC,EAAE,cAAc,IAAI,CAAC;QAChE,QAAQ,GAAG,CAAC,qBAAqB,KAAK,SAAS,CAAC;QAEhD,IAAI,mBAAwB;QAC5B,IAAI,kBAAkB;QAEtB,2BAA2B;QAC3B,MAAM,SAAS,MAAM,CAAA,GAAA,2JAAA,CAAA,wBAAqB,AAAD,EAAE;YACzC;YACA;YACA,UAAU;YACV;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,sBAAsB,CAAC;gBACrB,mBAAmB;gBACnB,4BAA4B;gBAC5B,IAAI,WAAW,SAAS,EAAE;oBACxB,oBAAoB,WAAW,SAAS;gBAC1C;gBACA,kBAAkB;YACpB;QACF;QAEA,kBAAkB;QAClB,OAAO,mBAAmB,CAAC,YAAY;YACrC,eAAe;QACjB;QAEA,aAAa;QACb,MAAO,CAAC,gBAAiB;YACvB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;QAEA,qBAAqB;QACrB,IAAI,kBAAkB,aAAa;YACjC,QAAQ,GAAG,CAAC;YAEZ,qCAAqC;YACrC,IAAI,YAAY,GAAG;gBACjB,WAAW,sBAAsB,CAAC;oBAChC,MAAM;oBACN,iBAAiB,YAAY;oBAC7B;oBACA,aAAa;oBACb,SAAS;gBACX;YACF;YAEA;QACF;QAEA,SAAS;QACT,IAAI,YAAY,gBAAgB,GAAG;YACjC,QAAQ,GAAG,CAAC;YAEZ,sBAAsB;YACtB,MAAM,qBAAqB,CAAA,GAAA,2IAAA,CAAA,2BAAwB,AAAD,EAAE;YACpD,kBAAkB;mBAAI;gBAAiB;oBACrC,MAAM;oBACN,SAAS;gBACX;aAAE;YAEF,2CAA2C;YAC3C,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAChC,MAAM,mBAAmB,kBAAkB,GAAG,CAAC,CAAC;oBAC9C,MAAM,OAAO,KAAK,SAAS,CAAC,SAAS,IAAI;oBACzC,MAAM,SAAS,SAAS,MAAM,GAAG,KAAK,SAAS,CAAC,SAAS,MAAM,EAAE,SAAS,CAAC,GAAG,OAAO,QAAQ;oBAC7F,OAAO,GAAG,SAAS,QAAQ,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE,QAAQ;gBACpD,GAAG,IAAI,CAAC;gBAER,kBAAkB;uBAAI;oBAAiB;wBACrC,MAAM;wBACN,SAAS,CAAC,oBAAoB,EAAE,iBAAiB,sBAAsB,CAAC;oBAC1E;iBAAE;YACJ;YAEA,YAAY;YACZ,WAAW,sBAAsB,CAAC;gBAChC,MAAM;gBACN,WAAW,YAAY;gBACvB;gBACA,SAAS,GAAG,YAAY,EAAE,YAAY,CAAC;gBACvC,QAAQ,iBAAiB,MAAM;gBAC/B,wBAAwB,iBAAiB,sBAAsB,IAAI,EAAE;YACvE;YAEA,oCAAoC;YACpC;QACF,OAAO;YACL,QAAQ,GAAG,CAAC;YAEZ,kBAAkB;YAClB,WAAW,sBAAsB,CAAC;gBAChC,MAAM;gBACN,iBAAiB;gBACjB;gBACA,SAAS,CAAC,YAAY,EAAE,cAAc,UAAU,CAAC;gBACjD,iBAAiB;YACnB;YAEA;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 4103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/api/chat/route.ts"], "sourcesContent": ["import {\r\n  convertToCoreMessages,\r\n  Message,\r\n  Output,\r\n  smoothStream,\r\n  streamObject,\r\n  appendResponseMessages,\r\n  InvalidToolArgumentsError,\r\n  NoSuchToolError,\r\n  ToolExecutionError,\r\n  generateObject,\r\n} from \"ai\";\r\nimport { customModel, openaiModel } from \"@/lib/ai\";\r\nimport { auth } from \"@/app/(auth)/auth\";\r\nimport {\r\n  deleteChatById,\r\n  getChatById,\r\n  saveChat,\r\n  saveMessages,\r\n} from \"@/lib/db/queries\";\r\nimport {\r\n  models,\r\n  getModelById,\r\n  getModelProvider,\r\n  supportsReasoning,\r\n} from \"@/lib/ai/models\";\r\nimport {\r\n  generateUUID,\r\n  getMostRecentUserMessage,\r\n  prunedMessages,\r\n} from \"@/lib/utils\";\r\nimport { generateTitleFromUserMessage } from \"../../actions\";\r\nimport {\r\n  agentConfigs,\r\n  intentAnalyzerAgentConfig,\r\n  IntentResponseSchema,\r\n  AgentName,\r\n  IntentResponseType,\r\n  IntentEnum,\r\n} from \"./agents\";\r\nimport { createDataStreamResponse, streamText } from \"ai\";\r\nimport { LayerProps } from \"@geon-map/odf\";\r\nimport { openai } from \"@ai-sdk/openai\";\r\nimport {\r\n  getApiConfig,\r\n  addAuthToParams,\r\n  getApiHeaders,\r\n  getApiUserId,\r\n} from \"@/lib/api-config\";\r\nimport { evaluateAgentResult } from \"./evaluate\";\r\nimport { executeAgentWithRetry } from \"./execute-with-retry\";\r\nimport { handleAgentWithRetry } from \"./retry-handler\";\r\n\r\n// 지도 상태 정보 생성 유틸리티\r\nconst createMapStateInfo = (layers: LayerProps[]) => {\r\n  if (layers.length === 0) {\r\n    return `지도 상태: 빈 지도 (레이어 없음)\r\n- 모든 레이어 요청은 검색 → 추가 과정이 필요합니다.`;\r\n  }\r\n\r\n  const layerSummary = layers.map(layer => ({\r\n    name: (layer as any).name || (layer as any).lyrNm || '이름없음',\r\n    id: layer.id,\r\n    type: (layer as any).geometryType || (layer as any).type || '타입불명',\r\n    visible: layer.visible\r\n  }));\r\n\r\n  const visibleLayers = layerSummary.filter(l => l.visible);\r\n  const hiddenLayers = layerSummary.filter(l => !l.visible);\r\n\r\n  return `지도 상태: ${layers.length}개 레이어 존재\r\n\r\n**현재 표시중인 레이어 (${visibleLayers.length}개):**\r\n${visibleLayers.map(l => `- ${l.name} (${l.type}, ID: ${l.id})`).join('\\n')}\r\n\r\n**숨겨진 레이어 (${hiddenLayers.length}개):**\r\n${hiddenLayers.map(l => `- ${l.name} (${l.type}, ID: ${l.id})`).join('\\n')}\r\n\r\n**레이어 타입 분포:**\r\n- 점(Point): ${layerSummary.filter(l => l.type === 'point').length}개\r\n- 선(Line): ${layerSummary.filter(l => l.type === 'line').length}개\r\n- 면(Polygon): ${layerSummary.filter(l => l.type === 'polygon').length}개`;\r\n};\r\n\r\n// 의도분석 메시지 생성 함수\r\nconst createIntentAnalyzerMessages = (\r\n  userMessage: any,\r\n  lastAssistantMsg: any,\r\n  layers: LayerProps[],\r\n  intentAnalyzerAgentConfig: any\r\n) => {\r\n  const messages: Array<{role: \"system\" | \"assistant\" | \"user\"; content: string}> = [\r\n    { role: \"system\", content: intentAnalyzerAgentConfig.system }\r\n  ];\r\n\r\n  // 지도 상태 정보 추가\r\n  const mapStateInfo = createMapStateInfo(layers);\r\n  messages.push({\r\n    role: \"system\",\r\n    content: `현재 지도 상태 정보:\r\n${mapStateInfo}\r\n\r\n이 정보를 바탕으로 사용자 요청을 분석하고 최적의 작업 계획을 수립하세요.`\r\n  });\r\n\r\n  // 이전 대화 맥락\r\n  if (lastAssistantMsg) {\r\n    const assistantContent = typeof lastAssistantMsg.content === \"string\"\r\n      ? lastAssistantMsg.content\r\n      : JSON.stringify(lastAssistantMsg.content);\r\n    messages.push({\r\n      role: \"assistant\",\r\n      content: assistantContent,\r\n    });\r\n  }\r\n\r\n  // 사용자 메시지\r\n  const userContentStr = typeof userMessage.content === \"string\"\r\n    ? userMessage.content\r\n    : JSON.stringify(userMessage.content);\r\n  messages.push({ role: \"user\", content: userContentStr });\r\n\r\n  return messages;\r\n};\r\n\r\n// 실제 사용 가능한 레이어 목록을 조회하는 함수\r\nasync function getAvailableLayersContext(): Promise<string> {\r\n  try {\r\n    const config = getApiConfig();\r\n\r\n    const params = new URLSearchParams({\r\n      userId: getApiUserId(config),\r\n      holdDataSeCode: \"0\",\r\n      pageIndex: \"1\",\r\n      pageSize: \"10\", // 상위 10개만 조회\r\n    });\r\n\r\n    // 인증 정보 추가\r\n    addAuthToParams(params, config);\r\n\r\n    const response = await fetch(\r\n      `${config.baseUrl}/smt/layer/info/list?${params.toString()}`,\r\n      {\r\n        method: \"GET\",\r\n        headers: getApiHeaders(config),\r\n      }\r\n    );\r\n\r\n    if (!response.ok) {\r\n      console.warn(\"Failed to fetch available layers for context\");\r\n      return \"사용 가능한 레이어 정보를 조회할 수 없습니다.\";\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    if (!data || !data.result || !data.result.list) {\r\n      return \"사용 가능한 레이어 정보를 조회할 수 없습니다.\";\r\n    }\r\n\r\n    const layers = data.result.list.slice(0, 5); // 상위 5개만 사용\r\n    const layerInfo = layers\r\n      .map((layer: any) => {\r\n        const typeMap: { [key: string]: string } = {\r\n          \"1\": \"점\",\r\n          \"2\": \"선\",\r\n          \"3\": \"면\",\r\n        };\r\n        const typeName = typeMap[layer.lyrTySeCode] || \"기타\";\r\n        return `${layer.lyrNm}(${typeName})`;\r\n      })\r\n      .join(\", \");\r\n\r\n    return `실제 사용 가능한 레이어 예시: ${layerInfo}`;\r\n  } catch (error) {\r\n    console.warn(\"Error fetching available layers for context:\", error);\r\n    return \"사용 가능한 레이어 정보를 조회할 수 없습니다.\";\r\n  }\r\n}\r\n\r\nexport const maxDuration = 100;\r\n\r\nexport async function POST(req: Request) {\r\n  const {\r\n    id,\r\n    messages,\r\n    layers,\r\n    modelId,\r\n    enable_thinking,\r\n    enable_smart_navigation,\r\n  }: {\r\n    id: string;\r\n    messages: Array<Message>;\r\n    layers: LayerProps[];\r\n    modelId?: string;\r\n    enable_thinking: boolean;\r\n    enable_smart_navigation: boolean;\r\n  } = await req.json();\r\n\r\n  const session = await auth();\r\n\r\n  if (!session || !session.user || !session.user.id) {\r\n    return new Response(\"Unauthorized\", { status: 401 });\r\n  }\r\n\r\n  // 모델 선택 및 초기화\r\n  let model = customModel(\"Qwen/Qwen3-4B\");\r\n  let selectedModel = getModelById(modelId || \"Qwen3-4B\");\r\n  let modelSupportsReasoning = true; // 기본값\r\n  let isNonGeonProvider = false;\r\n\r\n  if (selectedModel) {\r\n    const provider = getModelProvider(selectedModel.id);\r\n    modelSupportsReasoning = supportsReasoning(selectedModel.id);\r\n\r\n    switch (provider) {\r\n      case \"openai\":\r\n        model = openaiModel(selectedModel.apiIdentifier);\r\n        isNonGeonProvider = true;\r\n        break;\r\n      case \"geon\":\r\n        model = customModel(selectedModel.apiIdentifier);\r\n        break;\r\n      case \"dify\":\r\n        // difyModel 함수가 있다면 사용\r\n        model = customModel(selectedModel.apiIdentifier);\r\n        break;\r\n      default:\r\n        model = customModel(selectedModel.apiIdentifier);\r\n    }\r\n  }\r\n\r\n  const coreMessages = convertToCoreMessages(prunedMessages(messages));\r\n  const userMessage = getMostRecentUserMessage(coreMessages);\r\n\r\n  if (!userMessage) {\r\n    return new Response(\"No user message found\", { status: 400 });\r\n  }\r\n\r\n  const chat = await getChatById({ id });\r\n\r\n  if (!chat) {\r\n    const title = await generateTitleFromUserMessage({ message: userMessage });\r\n    await saveChat({ id, userId: session.user.id, title });\r\n  }\r\n\r\n  const userMessageId = generateUUID();\r\n\r\n  // 사용자 메시지를 AI SDK 패턴에 맞게 저장\r\n  const userMessageToSave = {\r\n    id: userMessageId,\r\n    chatId: id,\r\n    role: userMessage.role,\r\n    content: \"\", // AI SDK 권장: content는 빈 문자열\r\n    parts: Array.isArray(userMessage.content)\r\n      ? userMessage.content\r\n      : [{ type: \"text\", text: userMessage.content }], // content를 parts로 변환\r\n    attachments: [],\r\n    createdAt: new Date(),\r\n  };\r\n\r\n  await saveMessages({\r\n    messages: [userMessageToSave],\r\n  });\r\n\r\n  // createDataStreamResponse를 사용한 Sequential Processing과 Routing\r\n  return createDataStreamResponse({\r\n    execute: async (dataStream) => {\r\n      try {\r\n        // Step 1: Intent Analysis (의도 분석) - 내부 프로세스는 숨김\r\n        // Prepare messages for intent analyzer: include last assistant message for context and current map state\r\n        const lastAssistantMsg = [...coreMessages]\r\n          .reverse()\r\n          .find((msg) => msg.role === \"assistant\");\r\n\r\n        // 개선된 의도분석 메시지 생성 (지도 상태 정보 포함)\r\n        const intentAnalyzerMessages = createIntentAnalyzerMessages(\r\n          userMessage,\r\n          lastAssistantMsg,\r\n          layers,\r\n          intentAnalyzerAgentConfig\r\n        );\r\n\r\n        const { experimental_partialOutputStream } = streamText({\r\n          model,\r\n          temperature: 0,\r\n          messages: intentAnalyzerMessages,\r\n          experimental_output: Output.object({\r\n            schema: IntentResponseSchema,\r\n          }),\r\n          onChunk({ chunk }) {\r\n            if (chunk.type === \"reasoning\") {\r\n              dataStream.writeMessageAnnotation(chunk);\r\n            }\r\n          },\r\n          // experimental_transform: smoothStream({\r\n          //   delayInMs: 10\r\n          // }),\r\n          ...(isNonGeonProvider\r\n            ? {}\r\n            : {\r\n                providerOptions: {\r\n                  geon: {\r\n                    metadata: {\r\n                      chat_template_kwargs: {\r\n                        enable_thinking:\r\n                          enable_thinking && modelSupportsReasoning,\r\n                      },\r\n                    },\r\n                  },\r\n                },\r\n              }),\r\n        });\r\n\r\n        // experimental_partialOutputStream에서 intent 추출\r\n        let intent: any = {\r\n          intent: \"GENERAL_CONVERSATION\",\r\n          message: \"사용자 요청을 처리하고 있습니다...\",\r\n        };\r\n\r\n        // 사용자 메시지 처리 과정을 보여주면서 동기식으로 출력이 완성될 때까지 기다림\r\n        for await (const partial of experimental_partialOutputStream) {\r\n          if (partial.intent && partial.message) {\r\n            intent = {\r\n              intent: partial.intent,\r\n              message: partial.message,\r\n            };\r\n          }\r\n        }\r\n\r\n        // 의도분석 결과와 작업 계획을 사용자에게 표시\r\n        dataStream.writeMessageAnnotation({\r\n          type: \"intent_analyzed\",\r\n          intent: intent.intent,\r\n          message: intent.message,\r\n        });\r\n\r\n        // Step 2: Agent Routing (에이전트 라우팅)\r\n        const agentName = mapIntentToAgent(intent.intent);\r\n        console.log(\r\n          `[ROUTING] Intent: ${intent.intent} → Agent: ${agentName || \"null\"}`\r\n        );\r\n        if (!agentName) {\r\n          // General conversation이나 처리할 수 없는 의도인 경우\r\n          const result = streamText({\r\n            model,\r\n            messages: [\r\n              {\r\n                role: \"system\",\r\n                content:\r\n                  \"당신은 친근하고 도움이 되는 지도 AI 어시스턴트입니다. 사용자와 자연스럽게 대화하세요.\",\r\n              },\r\n              ...coreMessages,\r\n            ],\r\n            temperature: 0,\r\n            maxSteps: 5,\r\n          });\r\n\r\n          result.mergeIntoDataStream(dataStream);\r\n          return;\r\n        }\r\n\r\n        // 에이전트 시작 알림\r\n        dataStream.writeMessageAnnotation({\r\n          type: \"agent_start\",\r\n          agent: agentName,\r\n          message: `${agentName} 에이전트가 작업을 시작합니다`,\r\n        });\r\n\r\n        // 개선된 stateMessage 생성\r\n        const createExecutionMessage = (intent: any, layers: LayerProps[]) => {\r\n          let content = `Current map state:\r\n${JSON.stringify(layers, null, 2)}\r\n\r\n의도분석 결과: ${intent.message}`;\r\n\r\n          // 조건부 추가 정보\r\n          if (intent.targetLayer) {\r\n            content += `\\n\\n대상 레이어: ${intent.targetLayer}`;\r\n            content += `\\n레이어 존재 여부: ${intent.layerExists ? '존재함' : '존재하지 않음'}`;\r\n          }\r\n\r\n          if (intent.requiredActions && intent.requiredActions.length > 0) {\r\n            content += `\\n\\n필수 작업 단계:`;\r\n            intent.requiredActions.forEach((action: string, index: number) => {\r\n              content += `\\n${index + 1}. ${action}`;\r\n            });\r\n          }\r\n\r\n          if (intent.styleRequirements) {\r\n            content += `\\n\\n스타일 요구사항: ${JSON.stringify(intent.styleRequirements)}`;\r\n          }\r\n\r\n          content += `\\n\\n위 계획에 따라 단계별로 작업을 수행하세요.`;\r\n\r\n          return {\r\n            role: \"system\" as const,\r\n            content\r\n          };\r\n        };\r\n\r\n        const stateMessage = createExecutionMessage(intent, layers);\r\n\r\n        // 새로운 방식\r\n        await handleAgentWithRetry({\r\n          model,\r\n          agentName,\r\n          messages: coreMessages,\r\n          stateMessage,\r\n          intentMessage: intent.message,\r\n          dataStream,\r\n          session,\r\n          enable_smart_navigation,\r\n          isNonGeonProvider,\r\n          chatId: id, // AI 응답을 DB에 저장하기 위한 chatId 전달\r\n        });\r\n\r\n        // 작업 완료 시 메시지 저장\r\n      } catch (error) {\r\n        console.error(\"Agent processing error:\", error);\r\n      }\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Agent processing error:\", error);\r\n      if (NoSuchToolError.isInstance(error)) {\r\n        return \"The model tried to call a unknown tool.\";\r\n      } else if (InvalidToolArgumentsError.isInstance(error)) {\r\n        return \"The model called a tool with invalid arguments.\";\r\n      } else if (ToolExecutionError.isInstance(error)) {\r\n        return \"An error occurred during tool execution.\";\r\n      } else {\r\n        return \"An unknown error occurred.\";\r\n      }\r\n    },\r\n  });\r\n}\r\n\r\n// 의도를 에이전트로 매핑하는 헬퍼 함수\r\nfunction mapIntentToAgent(\r\n  intent: (typeof IntentEnum)[number]\r\n): AgentName | null {\r\n  const intentToAgentMap: Record<\r\n    (typeof IntentEnum)[number],\r\n    AgentName | null\r\n  > = {\r\n    LAYER_ADD: \"layer_agent\",\r\n    LAYER_REMOVE: \"layer_agent\",\r\n    LAYER_STYLE: \"layer_agent\", // 스타일 변경을 layer_agent가 처리\r\n    LAYER_LIST: \"layer_agent\", // 레이어 목록 조회를 layer_agent가 처리\r\n\r\n    LAYER_FILTER: \"layer_agent\", // 레이어 필터링 요청도 layer_agent가 처리\r\n\r\n    NAVIGATION: \"navigation\",\r\n    MAP_CONTROL: \"map_control\", // 지도 컨트롤을 map_control 에이전트가 처리\r\n    BASEMAP_CHANGE: \"map_control\", // 배경지도 변경 요청을 map_control 에이전트가 처리\r\n\r\n    DENSITY_ANALYSIS: \"density_analysis\", // 밀도 분석 요청을 density_analysis 에이전트가 처리\r\n\r\n    GENERAL_CONVERSATION: \"default_agent\", // 일반 대화는 default_agent로 처리\r\n    UNSURE: \"default_agent\", // 불확실한 경우도 default_agent로 처리\r\n\r\n    UNSUPPORTED_FEATURE: \"unsupported_feature\", // 지원되지 않는 기능 요청을 전용 에이전트가 처리\r\n  };\r\n\r\n  return intentToAgentMap[intent];\r\n}\r\n\r\nexport async function DELETE(request: Request) {\r\n  const { searchParams } = new URL(request.url);\r\n  const id = searchParams.get(\"id\");\r\n\r\n  if (!id) {\r\n    return new Response(\"Not Found\", { status: 404 });\r\n  }\r\n\r\n  const session = await auth();\r\n\r\n  if (!session || !session.user) {\r\n    return new Response(\"Unauthorized\", { status: 401 });\r\n  }\r\n\r\n  try {\r\n    const chat = await getChatById({ id });\r\n\r\n    if (chat.userId !== session.user.id) {\r\n      return new Response(\"Unauthorized\", { status: 401 });\r\n    }\r\n\r\n    await deleteChatById({ id });\r\n\r\n    return new Response(\"Chat deleted\", { status: 200 });\r\n  } catch (error) {\r\n    return new Response(\"An error occurred while processing your request\", {\r\n      status: 500,\r\n    });\r\n  }\r\n}\r\n\r\n// deprecated\r\n// const result = streamText({\r\n//   model,\r\n//   messages: [\r\n//     {\r\n//       role: \"system\",\r\n//       content: agentConfig.system,\r\n//     },\r\n//     stateMessage,\r\n//     ...coreMessages,\r\n//   ],\r\n//   temperature: 0,\r\n//   tools: agentConfig.tools,\r\n//   toolCallStreaming: true,\r\n//   maxSteps: agentConfig.maxSteps || 10,\r\n//   experimental_transform: smoothStream(),\r\n//   experimental_continueSteps: true,\r\n//   experimental_repairToolCall: async ({\r\n//     toolCall,\r\n//     tools,\r\n//     parameterSchema,\r\n//     error,\r\n//   }) => {\r\n//     console.log(\r\n//       `[TOOL_REPAIR] Attempting to repair tool call: ${toolCall.toolName}`\r\n//     );\r\n//     console.log(`[TOOL_REPAIR] Error: ${error.message}`);\r\n//     console.log(`[TOOL_REPAIR] Original args:`, toolCall.args);\r\n\r\n//     if (NoSuchToolError.isInstance(error)) {\r\n//       console.log(`[TOOL_REPAIR] Tool not found, cannot repair`);\r\n//       return null; // 존재하지 않는 도구는 수정할 수 없음\r\n//     }\r\n//     const tool = tools[toolCall.toolName as keyof typeof tools];\r\n\r\n//     const { object: repairedArgs } = await generateObject({\r\n//       model: openai(\"gpt-4.1-nano\", { structuredOutputs: true }),\r\n//       schema: tool.parameters,\r\n//       prompt: [\r\n//         `The model tried to call the tool \"${toolCall.toolName}\"` +\r\n//           ` with the following arguments:`,\r\n//         JSON.stringify(toolCall.args),\r\n//         `The tool accepts the following schema:`,\r\n//         JSON.stringify(parameterSchema(toolCall)),\r\n//         \"Please fix the arguments.\",\r\n//       ].join(\"\\n\"),\r\n//     });\r\n\r\n//     return { ...toolCall, args: JSON.stringify(repairedArgs) };\r\n//   },\r\n//   ...(isNonGeonProvider\r\n//     ? {}\r\n//     : {\r\n//         providerOptions: {\r\n//           geon: {\r\n//             metadata: {\r\n//               chat_template_kwargs: { enable_thinking: false },\r\n//             },\r\n//           },\r\n//         },\r\n//       }),\r\n//   onStepFinish: ({ toolCalls, text }) => {\r\n//     // 평가를 위한 정보 수집\r\n//     if (toolCalls && toolCalls.length > 0) {\r\n//       allToolCalls.push(...toolCalls);\r\n//     }\r\n//     if (text) {\r\n//       agentResponse += text;\r\n//     }\r\n\r\n//     // 도구 호출 정보를 어노테이션으로 전송\r\n//     if (toolCalls && toolCalls.length > 0) {\r\n//       toolCalls.forEach((toolCall) => {\r\n//         dataStream.writeMessageAnnotation({\r\n//           type: \"tool_call\",\r\n//           toolName: toolCall.toolName,\r\n//           args: toolCall.args,\r\n//           enableSmartNavigation: enable_smart_navigation, // 스마트 네비게이션 상태 전달\r\n//         });\r\n//       });\r\n//     }\r\n//   },\r\n//   onFinish: async ({ response, text, toolCalls }) => {\r\n//     // 최종 정보 수집\r\n//     if (text) {\r\n//       agentResponse += text;\r\n//     }\r\n//     if (toolCalls && toolCalls.length > 0) {\r\n//       allToolCalls.push(...toolCalls);\r\n//     }\r\n//     // 평가자 실행 테스트\r\n//     if (intent?.message) {\r\n//       try {\r\n//         const evaluation = await evaluateAgentResult(\r\n//           intent.message,\r\n//           agentResponse,\r\n//           allToolCalls\r\n//         );\r\n//       } catch (error) {\r\n//         console.error(\"평가자 실행 실패:\", error);\r\n//       }\r\n//     }\r\n\r\n//     if (session.user?.id) {\r\n//       try {\r\n//         dataStream.writeMessageAnnotation({\r\n//           type: \"agent_completed\",\r\n//           agent: agentName,\r\n//           message: \"작업이 완료되었습니다.\",\r\n//         });\r\n\r\n//         // AI SDK 공식 패턴: appendResponseMessages 사용\r\n//         const updatedMessages = appendResponseMessages({\r\n//           messages,\r\n//           responseMessages: response.messages,\r\n//         });\r\n\r\n//         // 새로 추가된 메시지들만 저장 (기존 메시지는 이미 저장됨)\r\n//         const newMessages = updatedMessages.slice(messages.length);\r\n\r\n//         const messagesToSave = newMessages.map((message) => {\r\n//           const messageId = generateUUID();\r\n\r\n//           if (message.role === \"assistant\") {\r\n//             dataStream.writeMessageAnnotation({\r\n//               messageIdFromServer: messageId,\r\n//             });\r\n//           }\r\n\r\n//           return {\r\n//             id: messageId,\r\n//             chatId: id,\r\n//             role: message.role,\r\n//             content: message.content, // 기존 호환성을 위해 유지\r\n//             parts: message.parts || [], // AI SDK parts 배열\r\n//             attachments: message.experimental_attachments || [], // AI SDK attachments\r\n//             createdAt: new Date(),\r\n//           };\r\n//         });\r\n\r\n//         if (messagesToSave.length > 0) {\r\n//           await saveMessages({\r\n//             messages: messagesToSave,\r\n//           });\r\n//         }\r\n//       } catch (error) {\r\n//         console.error(\"Failed to save chat:\", error);\r\n//       }\r\n//     }\r\n//   },\r\n// });\r\n// 최종 결과를 데이터 스트림에 병합\r\n// result.mergeIntoDataStream(dataStream, {\r\n//   sendReasoning: true,\r\n// });\r\n"], "names": [], "mappings": ";;;;;AAAA;AAYA;AACA;AACA;AAMA;AAMA;AAKA;AACA;AAWA;AAQA;;;;;;;;;;;;AAEA,mBAAmB;AACnB,MAAM,qBAAqB,CAAC;IAC1B,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB,OAAO,CAAC;+BACmB,CAAC;IAC9B;IAEA,MAAM,eAAe,OAAO,GAAG,CAAC,CAAA,QAAS,CAAC;YACxC,MAAM,AAAC,MAAc,IAAI,IAAI,AAAC,MAAc,KAAK,IAAI;YACrD,IAAI,MAAM,EAAE;YACZ,MAAM,AAAC,MAAc,YAAY,IAAI,AAAC,MAAc,IAAI,IAAI;YAC5D,SAAS,MAAM,OAAO;QACxB,CAAC;IAED,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO;IACxD,MAAM,eAAe,aAAa,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,OAAO;IAExD,OAAO,CAAC,OAAO,EAAE,OAAO,MAAM,CAAC;;eAElB,EAAE,cAAc,MAAM,CAAC;AACtC,EAAE,cAAc,GAAG,CAAC,CAAA,IAAK,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM;;WAEjE,EAAE,aAAa,MAAM,CAAC;AACjC,EAAE,aAAa,GAAG,CAAC,CAAA,IAAK,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM;;;YAG/D,EAAE,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM,CAAC;WACvD,EAAE,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM,CAAC;cAClD,EAAE,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM,CAAC,CAAC,CAAC;AACxE;AAEA,iBAAiB;AACjB,MAAM,+BAA+B,CACnC,aACA,kBACA,QACA;IAEA,MAAM,WAA4E;QAChF;YAAE,MAAM;YAAU,SAAS,0BAA0B,MAAM;QAAC;KAC7D;IAED,cAAc;IACd,MAAM,eAAe,mBAAmB;IACxC,SAAS,IAAI,CAAC;QACZ,MAAM;QACN,SAAS,CAAC;AACd,EAAE,aAAa;;yCAE0B,CAAC;IACxC;IAEA,WAAW;IACX,IAAI,kBAAkB;QACpB,MAAM,mBAAmB,OAAO,iBAAiB,OAAO,KAAK,WACzD,iBAAiB,OAAO,GACxB,KAAK,SAAS,CAAC,iBAAiB,OAAO;QAC3C,SAAS,IAAI,CAAC;YACZ,MAAM;YACN,SAAS;QACX;IACF;IAEA,UAAU;IACV,MAAM,iBAAiB,OAAO,YAAY,OAAO,KAAK,WAClD,YAAY,OAAO,GACnB,KAAK,SAAS,CAAC,YAAY,OAAO;IACtC,SAAS,IAAI,CAAC;QAAE,MAAM;QAAQ,SAAS;IAAe;IAEtD,OAAO;AACT;AAEA,4BAA4B;AAC5B,eAAe;IACb,IAAI;QACF,MAAM,SAAS,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;QAE1B,MAAM,SAAS,IAAI,gBAAgB;YACjC,QAAQ,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;YACrB,gBAAgB;YAChB,WAAW;YACX,UAAU;QACZ;QAEA,WAAW;QACX,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ;QAExB,MAAM,WAAW,MAAM,MACrB,GAAG,OAAO,OAAO,CAAC,qBAAqB,EAAE,OAAO,QAAQ,IAAI,EAC5D;YACE,QAAQ;YACR,SAAS,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE;QACzB;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,CAAC,IAAI,EAAE;YAC9C,OAAO;QACT;QAEA,MAAM,SAAS,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,YAAY;QACzD,MAAM,YAAY,OACf,GAAG,CAAC,CAAC;YACJ,MAAM,UAAqC;gBACzC,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YACA,MAAM,WAAW,OAAO,CAAC,MAAM,WAAW,CAAC,IAAI;YAC/C,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QACtC,GACC,IAAI,CAAC;QAER,OAAO,CAAC,kBAAkB,EAAE,WAAW;IACzC,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,gDAAgD;QAC7D,OAAO;IACT;AACF;AAEO,MAAM,cAAc;AAEpB,eAAe,KAAK,GAAY;IACrC,MAAM,EACJ,EAAE,EACF,QAAQ,EACR,MAAM,EACN,OAAO,EACP,eAAe,EACf,uBAAuB,EACxB,GAOG,MAAM,IAAI,IAAI;IAElB,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,OAAI,AAAD;IAEzB,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE,EAAE;QACjD,OAAO,IAAI,SAAS,gBAAgB;YAAE,QAAQ;QAAI;IACpD;IAEA,cAAc;IACd,IAAI,QAAQ,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;IACxB,IAAI,gBAAgB,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE,WAAW;IAC5C,IAAI,yBAAyB,MAAM,MAAM;IACzC,IAAI,oBAAoB;IAExB,IAAI,eAAe;QACjB,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD,EAAE,cAAc,EAAE;QAClD,yBAAyB,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD,EAAE,cAAc,EAAE;QAE3D,OAAQ;YACN,KAAK;gBACH,QAAQ,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE,cAAc,aAAa;gBAC/C,oBAAoB;gBACpB;YACF,KAAK;gBACH,QAAQ,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE,cAAc,aAAa;gBAC/C;YACF,KAAK;gBACH,uBAAuB;gBACvB,QAAQ,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE,cAAc,aAAa;gBAC/C;YACF;gBACE,QAAQ,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE,cAAc,aAAa;QACnD;IACF;IAEA,MAAM,eAAe,CAAA,GAAA,gPAAA,CAAA,wBAAqB,AAAD,EAAE,CAAA,GAAA,8GAAA,CAAA,iBAAc,AAAD,EAAE;IAC1D,MAAM,cAAc,CAAA,GAAA,8GAAA,CAAA,2BAAwB,AAAD,EAAE;IAE7C,IAAI,CAAC,aAAa;QAChB,OAAO,IAAI,SAAS,yBAAyB;YAAE,QAAQ;QAAI;IAC7D;IAEA,MAAM,OAAO,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;QAAE;IAAG;IAEpC,IAAI,CAAC,MAAM;QACT,MAAM,QAAQ,MAAM,CAAA,GAAA,2HAAA,CAAA,+BAA4B,AAAD,EAAE;YAAE,SAAS;QAAY;QACxE,MAAM,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD,EAAE;YAAE;YAAI,QAAQ,QAAQ,IAAI,CAAC,EAAE;YAAE;QAAM;IACtD;IAEA,MAAM,gBAAgB,CAAA,GAAA,8GAAA,CAAA,eAAY,AAAD;IAEjC,4BAA4B;IAC5B,MAAM,oBAAoB;QACxB,IAAI;QACJ,QAAQ;QACR,MAAM,YAAY,IAAI;QACtB,SAAS;QACT,OAAO,MAAM,OAAO,CAAC,YAAY,OAAO,IACpC,YAAY,OAAO,GACnB;YAAC;gBAAE,MAAM;gBAAQ,MAAM,YAAY,OAAO;YAAC;SAAE;QACjD,aAAa,EAAE;QACf,WAAW,IAAI;IACjB;IAEA,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;QACjB,UAAU;YAAC;SAAkB;IAC/B;IAEA,+DAA+D;IAC/D,OAAO,CAAA,GAAA,gPAAA,CAAA,2BAAwB,AAAD,EAAE;QAC9B,SAAS,OAAO;YACd,IAAI;gBACF,gDAAgD;gBAChD,yGAAyG;gBACzG,MAAM,mBAAmB;uBAAI;iBAAa,CACvC,OAAO,GACP,IAAI,CAAC,CAAC,MAAQ,IAAI,IAAI,KAAK;gBAE9B,gCAAgC;gBAChC,MAAM,yBAAyB,6BAC7B,aACA,kBACA,QACA,yIAAA,CAAA,4BAAyB;gBAG3B,MAAM,EAAE,gCAAgC,EAAE,GAAG,CAAA,GAAA,gPAAA,CAAA,aAAU,AAAD,EAAE;oBACtD;oBACA,aAAa;oBACb,UAAU;oBACV,qBAAqB,gPAAA,CAAA,SAAM,CAAC,MAAM,CAAC;wBACjC,QAAQ,yIAAA,CAAA,uBAAoB;oBAC9B;oBACA,SAAQ,EAAE,KAAK,EAAE;wBACf,IAAI,MAAM,IAAI,KAAK,aAAa;4BAC9B,WAAW,sBAAsB,CAAC;wBACpC;oBACF;oBACA,yCAAyC;oBACzC,kBAAkB;oBAClB,MAAM;oBACN,GAAI,oBACA,CAAC,IACD;wBACE,iBAAiB;4BACf,MAAM;gCACJ,UAAU;oCACR,sBAAsB;wCACpB,iBACE,mBAAmB;oCACvB;gCACF;4BACF;wBACF;oBACF,CAAC;gBACP;gBAEA,+CAA+C;gBAC/C,IAAI,SAAc;oBAChB,QAAQ;oBACR,SAAS;gBACX;gBAEA,6CAA6C;gBAC7C,WAAW,MAAM,WAAW,iCAAkC;oBAC5D,IAAI,QAAQ,MAAM,IAAI,QAAQ,OAAO,EAAE;wBACrC,SAAS;4BACP,QAAQ,QAAQ,MAAM;4BACtB,SAAS,QAAQ,OAAO;wBAC1B;oBACF;gBACF;gBAEA,2BAA2B;gBAC3B,WAAW,sBAAsB,CAAC;oBAChC,MAAM;oBACN,QAAQ,OAAO,MAAM;oBACrB,SAAS,OAAO,OAAO;gBACzB;gBAEA,mCAAmC;gBACnC,MAAM,YAAY,iBAAiB,OAAO,MAAM;gBAChD,QAAQ,GAAG,CACT,CAAC,kBAAkB,EAAE,OAAO,MAAM,CAAC,UAAU,EAAE,aAAa,QAAQ;gBAEtE,IAAI,CAAC,WAAW;oBACd,yCAAyC;oBACzC,MAAM,SAAS,CAAA,GAAA,gPAAA,CAAA,aAAU,AAAD,EAAE;wBACxB;wBACA,UAAU;4BACR;gCACE,MAAM;gCACN,SACE;4BACJ;+BACG;yBACJ;wBACD,aAAa;wBACb,UAAU;oBACZ;oBAEA,OAAO,mBAAmB,CAAC;oBAC3B;gBACF;gBAEA,aAAa;gBACb,WAAW,sBAAsB,CAAC;oBAChC,MAAM;oBACN,OAAO;oBACP,SAAS,GAAG,UAAU,gBAAgB,CAAC;gBACzC;gBAEA,sBAAsB;gBACtB,MAAM,yBAAyB,CAAC,QAAa;oBAC3C,IAAI,UAAU,CAAC;AACzB,EAAE,KAAK,SAAS,CAAC,QAAQ,MAAM,GAAG;;SAEzB,EAAE,OAAO,OAAO,EAAE;oBAEjB,YAAY;oBACZ,IAAI,OAAO,WAAW,EAAE;wBACtB,WAAW,CAAC,YAAY,EAAE,OAAO,WAAW,EAAE;wBAC9C,WAAW,CAAC,aAAa,EAAE,OAAO,WAAW,GAAG,QAAQ,WAAW;oBACrE;oBAEA,IAAI,OAAO,eAAe,IAAI,OAAO,eAAe,CAAC,MAAM,GAAG,GAAG;wBAC/D,WAAW,CAAC,aAAa,CAAC;wBAC1B,OAAO,eAAe,CAAC,OAAO,CAAC,CAAC,QAAgB;4BAC9C,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ;wBACxC;oBACF;oBAEA,IAAI,OAAO,iBAAiB,EAAE;wBAC5B,WAAW,CAAC,cAAc,EAAE,KAAK,SAAS,CAAC,OAAO,iBAAiB,GAAG;oBACxE;oBAEA,WAAW,CAAC,4BAA4B,CAAC;oBAEzC,OAAO;wBACL,MAAM;wBACN;oBACF;gBACF;gBAEA,MAAM,eAAe,uBAAuB,QAAQ;gBAEpD,SAAS;gBACT,MAAM,CAAA,GAAA,mJAAA,CAAA,uBAAoB,AAAD,EAAE;oBACzB;oBACA;oBACA,UAAU;oBACV;oBACA,eAAe,OAAO,OAAO;oBAC7B;oBACA;oBACA;oBACA;oBACA,QAAQ;gBACV;YAEA,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,2BAA2B;YACzC,IAAI,gPAAA,CAAA,kBAAe,CAAC,UAAU,CAAC,QAAQ;gBACrC,OAAO;YACT,OAAO,IAAI,gPAAA,CAAA,4BAAyB,CAAC,UAAU,CAAC,QAAQ;gBACtD,OAAO;YACT,OAAO,IAAI,gPAAA,CAAA,qBAAkB,CAAC,UAAU,CAAC,QAAQ;gBAC/C,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF;IACF;AACF;AAEA,uBAAuB;AACvB,SAAS,iBACP,MAAmC;IAEnC,MAAM,mBAGF;QACF,WAAW;QACX,cAAc;QACd,aAAa;QACb,YAAY;QAEZ,cAAc;QAEd,YAAY;QACZ,aAAa;QACb,gBAAgB;QAEhB,kBAAkB;QAElB,sBAAsB;QACtB,QAAQ;QAER,qBAAqB;IACvB;IAEA,OAAO,gBAAgB,CAAC,OAAO;AACjC;AAEO,eAAe,OAAO,OAAgB;IAC3C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,KAAK,aAAa,GAAG,CAAC;IAE5B,IAAI,CAAC,IAAI;QACP,OAAO,IAAI,SAAS,aAAa;YAAE,QAAQ;QAAI;IACjD;IAEA,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,OAAI,AAAD;IAEzB,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,EAAE;QAC7B,OAAO,IAAI,SAAS,gBAAgB;YAAE,QAAQ;QAAI;IACpD;IAEA,IAAI;QACF,MAAM,OAAO,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;YAAE;QAAG;QAEpC,IAAI,KAAK,MAAM,KAAK,QAAQ,IAAI,CAAC,EAAE,EAAE;YACnC,OAAO,IAAI,SAAS,gBAAgB;gBAAE,QAAQ;YAAI;QACpD;QAEA,MAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;YAAE;QAAG;QAE1B,OAAO,IAAI,SAAS,gBAAgB;YAAE,QAAQ;QAAI;IACpD,EAAE,OAAO,OAAO;QACd,OAAO,IAAI,SAAS,mDAAmD;YACrE,QAAQ;QACV;IACF;AACF,EAEA,aAAa;CACb,8BAA8B;CAC9B,WAAW;CACX,gBAAgB;CAChB,QAAQ;CACR,wBAAwB;CACxB,qCAAqC;CACrC,SAAS;CACT,oBAAoB;CACpB,uBAAuB;CACvB,OAAO;CACP,oBAAoB;CACpB,8BAA8B;CAC9B,6BAA6B;CAC7B,0CAA0C;CAC1C,4CAA4C;CAC5C,sCAAsC;CACtC,0CAA0C;CAC1C,gBAAgB;CAChB,aAAa;CACb,uBAAuB;CACvB,aAAa;CACb,YAAY;CACZ,mBAAmB;CACnB,6EAA6E;CAC7E,SAAS;CACT,4DAA4D;CAC5D,kEAAkE;CAElE,+CAA+C;CAC/C,oEAAoE;CACpE,6CAA6C;CAC7C,QAAQ;CACR,mEAAmE;CAEnE,8DAA8D;CAC9D,oEAAoE;CACpE,iCAAiC;CACjC,kBAAkB;CAClB,sEAAsE;CACtE,8CAA8C;CAC9C,yCAAyC;CACzC,oDAAoD;CACpD,qDAAqD;CACrD,uCAAuC;CACvC,sBAAsB;CACtB,UAAU;CAEV,kEAAkE;CAClE,OAAO;CACP,0BAA0B;CAC1B,WAAW;CACX,UAAU;CACV,6BAA6B;CAC7B,oBAAoB;CACpB,0BAA0B;CAC1B,kEAAkE;CAClE,iBAAiB;CACjB,eAAe;CACf,aAAa;CACb,YAAY;CACZ,6CAA6C;CAC7C,sBAAsB;CACtB,+CAA+C;CAC/C,yCAAyC;CACzC,QAAQ;CACR,kBAAkB;CAClB,+BAA+B;CAC/B,QAAQ;CAER,8BAA8B;CAC9B,+CAA+C;CAC/C,0CAA0C;CAC1C,8CAA8C;CAC9C,+BAA+B;CAC/B,yCAAyC;CACzC,iCAAiC;CACjC,+EAA+E;CAC/E,cAAc;CACd,YAAY;CACZ,QAAQ;CACR,OAAO;CACP,yDAAyD;CACzD,kBAAkB;CAClB,kBAAkB;CAClB,+BAA+B;CAC/B,QAAQ;CACR,+CAA+C;CAC/C,yCAAyC;CACzC,QAAQ;CACR,oBAAoB;CACpB,6BAA6B;CAC7B,cAAc;CACd,wDAAwD;CACxD,4BAA4B;CAC5B,2BAA2B;CAC3B,yBAAyB;CACzB,aAAa;CACb,0BAA0B;CAC1B,8CAA8C;CAC9C,UAAU;CACV,QAAQ;CAER,8BAA8B;CAC9B,cAAc;CACd,8CAA8C;CAC9C,qCAAqC;CACrC,8BAA8B;CAC9B,qCAAqC;CACrC,cAAc;CAEd,qDAAqD;CACrD,2DAA2D;CAC3D,sBAAsB;CACtB,iDAAiD;CACjD,cAAc;CAEd,8CAA8C;CAC9C,sEAAsE;CAEtE,gEAAgE;CAChE,8CAA8C;CAE9C,gDAAgD;CAChD,kDAAkD;CAClD,gDAAgD;CAChD,kBAAkB;CAClB,cAAc;CAEd,qBAAqB;CACrB,6BAA6B;CAC7B,0BAA0B;CAC1B,kCAAkC;CAClC,yDAAyD;CACzD,6DAA6D;CAC7D,yFAAyF;CACzF,qCAAqC;CACrC,eAAe;CACf,cAAc;CAEd,2CAA2C;CAC3C,iCAAiC;CACjC,wCAAwC;CACxC,gBAAgB;CAChB,YAAY;CACZ,0BAA0B;CAC1B,wDAAwD;CACxD,UAAU;CACV,QAAQ;CACR,OAAO;CACP,MAAM;CACN,qBAAqB;CACrB,2CAA2C;CAC3C,yBAAyB;CACzB,MAAM", "debugId": null}}]}