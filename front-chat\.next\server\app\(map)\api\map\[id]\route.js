(()=>{var e={};e.id=716,e.ids=[716],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24099:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{DELETE:()=>l,GET:()=>c,PATCH:()=>u});var s=r(81160),n=r(18765),o=r(46332),i=r(47841),d=r(28538);async function c(e,{params:t}){let r=await (0,i.j2)(),a=(await t).id;if(!r?.user)return Response.json("Unauthorized!",{status:401});if(!r.user.id)return Response.json("User ID is not defined",{status:401});try{let e=await (0,d.zU)({id:a,userId:r.user.id});return Response.json(e)}catch(e){if("Forbidden"===e.message)return Response.json("Forbidden",{status:403});return Response.json("Failed to fetch map",{status:500})}}async function u(e,{params:t}){let r=await (0,i.j2)(),a=(await t).id;if(!r?.user)return Response.json("Unauthorized!",{status:401});if(!r.user.id)return Response.json("User ID is not defined",{status:401});try{let{mapInfo:t,view:s}=await e.json();t&&await (0,d.Y0)({id:t.id||a,name:t.name,layers:t.layers||[],userId:r.user.id}),s&&await (0,d.xZ)({mapId:a,userId:r.user.id,view:s});let n=await (0,d.zU)({id:a,userId:r.user.id});return Response.json(n)}catch(e){return console.error("Failed to update map:",e),Response.json({success:!1,error:"Failed to update map"},{status:500})}}async function l(e,{params:t}){let r=await (0,i.j2)(),a=(await t).id;if(!r?.user)return Response.json("Unauthorized!",{status:401});if(!r.user.id)return Response.json("User ID is not defined",{status:401});try{return await (0,d.yo)({id:a,userId:r.user.id}),Response.json({success:!0})}catch(e){if("Forbidden"===e.message)return Response.json("Forbidden",{status:403});return Response.json("Failed to delete map",{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/(map)/api/map/[id]/route",pathname:"/api/map/[id]",filename:"route",bundlePath:"app/(map)/api/map/[id]/route"},resolvedPagePath:"C:\\chatbot\\front-chat\\app\\(map)\\api\\map\\[id]\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:h}=p;function y(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},28538:(e,t,r)=>{"use strict";r.d(t,{qQ:()=>K,yo:()=>er,wA:()=>Y,TJ:()=>L,Lz:()=>M,zU:()=>Z,V7:()=>X,kA:()=>V,de:()=>B,mV:()=>W,yd:()=>S,Y0:()=>ee,yM:()=>J,$:()=>H,xZ:()=>et,Ci:()=>G});var a={};r.r(a),r.d(a,{accounts:()=>v,accountsRelations:()=>_,chat:()=>N,chatMap:()=>k,chatRelation:()=>U,map:()=>b,mapAccess:()=>x,mapSession:()=>E,mapView:()=>Q,mapsRelation:()=>R,message:()=>g,sessions:()=>I,sessionsRelations:()=>F,user:()=>w,userRelations:()=>j,verificationTokens:()=>q,vote:()=>A});var s=r(9652),n=r(33094),o=r(32019),i=r(57052),d=r(52363),c=r(4471),u=r(56850),l=r(94485),p=r(10302),m=r(7320),f=r(16077),h=r(56839),y=r(86746);let w=(0,o.cJ)("user",{id:(0,i.Qq)("id").primaryKey().$defaultFn(()=>(0,s.sX)()),name:(0,i.Qq)("name"),username:(0,i.Qq)("username"),gh_username:(0,i.Qq)("gh_username"),email:(0,i.Qq)("email").unique(),emailVerified:(0,d.vE)("emailVerified",{mode:"date"}),image:(0,i.Qq)("image"),createdAt:(0,d.vE)("createdAt",{mode:"date"}).defaultNow().notNull(),updatedAt:(0,d.vE)("updatedAt",{mode:"date"}).notNull().$onUpdate(()=>new Date)}),I=(0,o.cJ)("sessions",{sessionToken:(0,i.Qq)("sessionToken").primaryKey(),userId:(0,i.Qq)("userId").notNull().references(()=>w.id,{onDelete:"cascade",onUpdate:"cascade"}),expires:(0,d.vE)("expires",{mode:"date"}).notNull()},e=>({userIdIdx:(0,c.Pe)().on(e.userId)})),q=(0,o.cJ)("verificationTokens",{identifier:(0,i.Qq)("identifier").notNull(),token:(0,i.Qq)("token").notNull().unique(),expires:(0,d.vE)("expires",{mode:"date"}).notNull()},e=>({compositePk:(0,u.ie)({columns:[e.identifier,e.token]})})),v=(0,o.cJ)("accounts",{userId:(0,i.Qq)("userId").notNull().references(()=>w.id,{onDelete:"cascade",onUpdate:"cascade"}),type:(0,i.Qq)("type").notNull(),provider:(0,i.Qq)("provider").notNull(),providerAccountId:(0,i.Qq)("providerAccountId").notNull(),refresh_token:(0,i.Qq)("refresh_token"),refreshTokenExpiresIn:(0,l.nd)("refresh_token_expires_in"),access_token:(0,i.Qq)("access_token"),expires_at:(0,l.nd)("expires_at"),token_type:(0,i.Qq)("token_type"),scope:(0,i.Qq)("scope"),id_token:(0,i.Qq)("id_token"),session_state:(0,i.Qq)("session_state"),oauth_token_secret:(0,i.Qq)("oauth_token_secret"),oauth_token:(0,i.Qq)("oauth_token")},e=>({userIdIdx:(0,c.Pe)().on(e.userId),compositePk:(0,u.ie)({columns:[e.provider,e.providerAccountId]})})),N=(0,o.cJ)("Chat",{id:(0,i.Qq)("id").primaryKey(),createdAt:(0,d.vE)("createdAt").notNull(),title:(0,i.Qq)("title").notNull().default("New Chat"),userId:(0,i.Qq)("userId").notNull(),visibility:(0,p.yf)("visibility",{enum:["public","private"]}).notNull().default("private")}),g=(0,o.cJ)("Message",{id:(0,m.uR)("id").primaryKey().notNull().defaultRandom(),chatId:(0,i.Qq)("chatId").notNull().references(()=>N.id),role:(0,p.yf)("role").notNull(),content:(0,f.Pq)("content"),parts:(0,f.Pq)("parts"),attachments:(0,f.Pq)("attachments"),createdAt:(0,d.vE)("createdAt").notNull()}),A=(0,o.cJ)("Vote",{chatId:(0,i.Qq)("chatId").notNull().references(()=>N.id),messageId:(0,m.uR)("messageId").notNull().references(()=>g.id),isUpvoted:(0,h.zM)("isUpvoted").notNull()},e=>({pk:(0,u.ie)({columns:[e.chatId,e.messageId]})})),b=(0,o.cJ)("map",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,y.$C)()),name:(0,i.Qq)("name").notNull(),createdBy:(0,i.Qq)("userId").notNull(),createdAt:(0,d.vE)("createdAt").notNull().defaultNow(),updatedAt:(0,d.vE)("updatedAt").notNull().defaultNow(),isPublic:(0,h.zM)("isPublic").default(!1),layers:(0,f.Pq)("layers").notNull(),version:(0,l.nd)("version").notNull().default(1)}),Q=(0,o.cJ)("map_view",{id:(0,i.Qq)("id").notNull().$defaultFn(()=>(0,y.$C)()),mapId:(0,i.Qq)("mapId").notNull().references(()=>b.id,{onDelete:"cascade"}),userId:(0,i.Qq)("userId").notNull(),center:(0,f.Pq)("center").notNull(),zoom:(0,l.nd)("zoom").notNull(),basemap:(0,i.Qq)("basemap").notNull(),updatedAt:(0,d.vE)("updatedAt").notNull().defaultNow()},e=>({compoundKey:(0,u.ie)({columns:[e.mapId,e.userId]}),idIdx:(0,c.Pe)("map_view_id_idx").on(e.id)})),k=(0,o.cJ)("chat_map",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,y.$C)()),chatId:(0,i.Qq)("chatId").notNull().references(()=>N.id,{onDelete:"cascade"}),mapId:(0,i.Qq)("mapId").notNull().references(()=>b.id,{onDelete:"restrict"}),createdAt:(0,d.vE)("createdAt").notNull().defaultNow()}),x=(0,o.cJ)("map_access",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,y.$C)()),mapId:(0,i.Qq)("mapId").notNull().references(()=>b.id,{onDelete:"cascade"}),userId:(0,i.Qq)("userId").notNull(),accessType:(0,i.Qq)("accessType").notNull(),createdAt:(0,d.vE)("createdAt").notNull().defaultNow()}),E=(0,o.cJ)("map_session",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,y.$C)()),mapId:(0,i.Qq)("mapId").notNull().references(()=>b.id,{onDelete:"cascade"}),userId:(0,i.Qq)("userId").notNull(),isActive:(0,h.zM)("isActive").default(!0),lastActiveAt:(0,d.vE)("lastActiveAt").notNull().defaultNow(),syncView:(0,h.zM)("syncView").default(!1),followingUserId:(0,i.Qq)("followingUserId")}),R=(0,n.K1)(b,({many:e})=>({views:e(Q),access:e(x),sessions:e(E),chats:e(k)})),U=(0,n.K1)(N,({many:e})=>({maps:e(k)})),F=(0,n.K1)(I,({one:e})=>({user:e(w,{references:[w.id],fields:[I.userId]})})),_=(0,n.K1)(v,({one:e})=>({user:e(w,{references:[w.id],fields:[v.userId]})})),j=(0,n.K1)(w,({many:e})=>({accounts:e(v),sessions:e(I)}));var P=r(60988),T=r(1770),D=r(7160),$=r(42449),C=r(8738);let z=new $.Pool({connectionString:process.env.POSTGRES_URL}),O=(0,C.f)(z,{schema:a,logger:!0});async function S({id:e,userId:t,title:r}){try{return await O.insert(N).values({id:e,createdAt:new Date,userId:t,title:r})}catch(e){throw console.error("Failed to save chat in database"),e}}async function J({messages:e}){try{return await O.insert(g).values(e)}catch(e){throw console.error("Failed to save messages in database",e),e}}async function K({id:e}){try{return await O.delete(A).where((0,P.eq)(A.chatId,e)),await O.delete(g).where((0,P.eq)(g.chatId,e)),await O.delete(N).where((0,P.eq)(N.id,e))}catch(e){throw console.error("Failed to delete chat by id from database"),e}}async function M({id:e}){try{return await O.select().from(N).where((0,P.eq)(N.userId,e)).orderBy((0,T.i)(N.createdAt))}catch(e){throw console.error("Failed to get chats by user from database"),e}}async function L({id:e}){try{let[t]=await O.select().from(N).where((0,P.eq)(N.id,e));return t}catch(e){throw console.error("Failed to get chat by id from database"),e}}async function V({id:e}){try{return await O.select().from(g).where((0,P.eq)(g.id,e))}catch(e){throw console.error("Failed to get message by id from database"),e}}async function B({id:e}){try{return await O.select().from(g).where((0,P.eq)(g.chatId,e)).orderBy((0,T.Y)(g.createdAt))}catch(e){throw console.error("Failed to get messages by chat id from database",e),e}}async function G({chatId:e,messageId:t,type:r}){try{let[a]=await O.select().from(A).where((0,P.Uo)((0,P.eq)(A.messageId,t)));if(a)return await O.update(A).set({isUpvoted:"up"===r}).where((0,P.Uo)((0,P.eq)(A.messageId,t),(0,P.eq)(A.chatId,e)));return await O.insert(A).values({chatId:e,messageId:t,isUpvoted:"up"===r})}catch(e){throw console.error("Failed to upvote message in database",e),e}}async function W({id:e}){try{return await O.select().from(A).where((0,P.eq)(A.chatId,e))}catch(e){throw console.error("Failed to get votes by chat id from database",e),e}}async function Y({chatId:e,timestamp:t}){try{return await O.delete(g).where((0,P.Uo)((0,P.eq)(g.chatId,e),(0,P.RO)(g.createdAt,t)))}catch(e){throw console.error("Failed to delete messages by id after timestamp from database"),e}}async function H({chatId:e,visibility:t}){try{return await O.update(N).set({visibility:t}).where((0,P.eq)(N.id,e))}catch(e){throw console.error("Failed to update chat visibility in database"),e}}async function X({userId:e}){try{return await O.select({id:b.id,name:b.name,createdAt:b.createdAt,updatedAt:b.updatedAt,layers:b.layers,isPublic:b.isPublic,activeUsers:(0,D.ll)`
        (
          SELECT COUNT(DISTINCT ${Q.userId})
          FROM ${Q}
          WHERE ${Q.mapId} = ${b.id}
          AND ${Q.updatedAt} > NOW() - INTERVAL '5 minutes'
        )
        `.as("activeUsers"),view:{center:Q.center,zoom:Q.zoom,basemap:Q.basemap}}).from(b).leftJoin(x,(0,P.Uo)((0,P.eq)(x.mapId,b.id),(0,P.eq)(x.userId,e))).leftJoin(Q,(0,P.Uo)((0,P.eq)(Q.mapId,b.id),(0,P.eq)(Q.userId,e))).where((0,P.or)((0,P.eq)(b.createdBy,e),(0,P.eq)(x.userId,e))).orderBy((0,T.i)(b.updatedAt))}catch(e){throw e instanceof Error&&console.error("Failed to get maps by user from database:",{message:e.message,stack:e.stack}),e}}async function Z({id:e,userId:t}){try{let[r]=await O.select({id:b.id,name:b.name,createdAt:b.createdAt,updatedAt:b.updatedAt,layers:b.layers,isPublic:b.isPublic,createdBy:b.createdBy,activeUsers:(0,D.ll)`
        (
          SELECT COUNT(DISTINCT ${Q.userId})
          FROM ${Q}
          WHERE ${Q.mapId} = ${b.id}
          AND ${Q.updatedAt} > NOW() - INTERVAL '5 minutes'
        )
        `.as("activeUsers")}).from(b).where((0,P.eq)(b.id,e));if(!r)throw Error("Map not found");if(Array.isArray(r.layers)&&(r.layers=r.layers.map(e=>({...e,style:e.style?"string"==typeof e.style?JSON.parse(e.style):e.style:void 0}))),r.createdBy!==t&&!r.isPublic&&!await ea({mapId:e,userId:t}))throw Error("Forbidden");let[a]=await O.select().from(Q).where((0,P.Uo)((0,P.eq)(Q.mapId,e),(0,P.eq)(Q.userId,t)));return{...r,view:a||null}}catch(e){throw console.error("Failed to get map by id from database"),e}}async function ee({id:e,name:t,layers:r,userId:a}){try{if((await O.select().from(b).where((0,P.eq)(b.id,e))).length>0)return await O.update(b).set({name:t,layers:r,updatedAt:new Date}).where((0,P.eq)(b.id,e));return await O.insert(b).values({id:e,name:t,layers:r,createdBy:a,createdAt:new Date,updatedAt:new Date})}catch(e){throw console.error("Failed to save map in database"),e}}async function et({mapId:e,userId:t,view:r}){try{return await O.insert(Q).values({mapId:e,userId:t,center:r.center??{lat:36.5,lng:127.5},zoom:r.zoom??7,basemap:r.basemap??"eMapBasic",updatedAt:new Date}).onConflictDoUpdate({target:[Q.mapId,Q.userId],set:{...r,updatedAt:new Date}})}catch(e){throw console.error("Failed to update map view state"),e}}async function er({id:e,userId:t}){try{let[r]=await O.select().from(b).where((0,P.eq)(b.id,e));if(!r)throw Error("Map not found");if(r.createdBy!==t)throw Error("Forbidden");return await O.delete(b).where((0,P.eq)(b.id,e))}catch(e){throw console.error("Failed to delete map from database"),e}}async function ea({mapId:e,userId:t}){try{return(await O.select().from(x).where((0,P.Uo)((0,P.eq)(x.mapId,e),(0,P.eq)(x.userId,t)))).length>0}catch(e){throw console.error("Failed to check map access"),e}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},37758:()=>{},42449:e=>{"use strict";e.exports=require("pg")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47841:(e,t,r)=>{"use strict";r.d(t,{j2:()=>l,Y9:()=>u});var a=r(393),s=r(95983);a.xz;let n="tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh",o="https://gsapi.geon.kr/smt";async function i(e,t){let r=new URLSearchParams({crtfckey:n,userId:e,password:t}),a=await fetch(`${o}/login/validation?${r.toString()}`,{method:"POST",headers:{"Content-Type":"application/json",crtfckey:n}}),s=await a.json();if(!a.ok)throw Error("로그인 검증에 실패했습니다.");return s}async function d(e){let t=new URLSearchParams({crtfckey:n,userId:e}),r=await fetch(`${o}/users/id?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",crtfckey:n}}),a=await r.json();if(!r.ok)throw Error("사용자 정보를 가져오는데 실패했습니다.");return a}let c=[(0,s.A)({credentials:{},async authorize({id:e,password:t}){try{let r=process.env.FRONTEND_LOGIN_USER_ID||"admin",s=process.env.FRONTEND_LOGIN_PASSWORD||"password1234";if(e===r&&t===s)return{id:r,name:"GeOn City",email:"@example.com",userId:r,userNm:"GeOn City",emailaddr:"@example.com",userSeCode:"14",userSeCodeNm:"관리자",userImage:null,insttCode:"GEON",insttNm:"GeOn",insttUrl:null,message:"로그인 성공"};if("geonuser"===e){let r=await i(e,t);if(!r.result.isValid)throw new a.xz(r.result.message);let s=await d(e);if(200!==s.code)return new a.xz(s.result.message);return{...s.result,id:s.result.userId,name:s.result.userNm||e,email:s.result.emailaddr||`${s.result.userNm}`}}throw new a.xz("admin 또는 geonuser 계정으로만 로그인할 수 있습니다.")}catch(e){throw console.error("Auth error:",e),e}}})];c.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"credentials"!==e.id);let{handlers:u,auth:l,signIn:p,signOut:m}=(0,a.Ay)({pages:{signIn:"/login",newUser:"/"},providers:[],callbacks:{authorized({auth:e,request:{nextUrl:t}}){let r=!!e?.user,a=t.pathname.startsWith("/geon-2d-map"),s=t.pathname.startsWith("/login");return"/"===t.pathname||r&&s?Response.redirect(new URL("/geon-2d-map",t)):!!s||!a||r}},providers:c,session:{strategy:"jwt",maxAge:1800},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id),e),session:async({session:e,token:t})=>(e.user&&(e.user.id=t.id),e)}})},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77598:e=>{"use strict";e.exports=require("node:crypto")},77926:()=>{},81160:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[332,481,741,115,746,861],()=>r(24099));module.exports=a})();