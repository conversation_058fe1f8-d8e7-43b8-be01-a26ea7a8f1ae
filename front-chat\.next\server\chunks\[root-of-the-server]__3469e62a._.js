module.exports = {

"[project]/.next-internal/server/app/api/layers/get-layer/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/lib/api-config.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * API 설정 및 인증 정보를 관리하는 유틸리티
 */ __turbopack_context__.s({
    "addAuthToParams": (()=>addAuthToParams),
    "getApiConfig": (()=>getApiConfig),
    "getApiHeaders": (()=>getApiHeaders),
    "getApiUserId": (()=>getApiUserId)
});
const getApiConfig = ()=>{
    const baseUrl = process.env.GEON_API_BASE_URL || "http://**************:14090";
    // MCP 서버 자체 API 키 사용 (클라이언트 토큰과 별개)
    const apiKey = process.env.GEON_API_KEY;
    if (!apiKey) {
        console.warn("GEON_API_KEY가 설정되지 않았습니다.");
    }
    // 백엔드 API 요청용 계정 정보 (환경변수에서 가져오거나 기본값 사용)
    const apiUserId = process.env.GEON_API_USER_ID || 'geonuser';
    const apiUserPassword = process.env.GEON_API_USER_PASSWORD || 'wavus1234!';
    return {
        baseUrl,
        headers: {
            crtfckey: apiKey || ""
        },
        auth: {
            userId: apiUserId,
            password: apiUserPassword
        }
    };
};
const addAuthToParams = (params, config)=>{
    const apiConfig = config || getApiConfig();
    // API 키 추가
    if (apiConfig.headers.crtfckey) {
        params.append("crtfckey", apiConfig.headers.crtfckey);
    }
    return params;
};
const getApiHeaders = (config)=>{
    const apiConfig = config || getApiConfig();
    return {
        "Content-Type": "application/json",
        ...apiConfig.headers
    };
};
const getApiUserId = (config)=>{
    const apiConfig = config || getApiConfig();
    return apiConfig.auth.userId;
};
}}),
"[project]/app/api/layers/get-layer/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@geon-ai+tools@0.0.11_react@19.0.0/node_modules/@geon-ai/tools/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api-config.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        const { lyrId } = await request.json();
        if (!lyrId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: "레이어 ID가 필요합니다"
            }, {
                status: 400
            });
        }
        const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiConfig"])();
        // 디버깅을 위한 로깅
        console.log("=== getLayer API 호출 시작 ===");
        console.log("lyrId:", lyrId);
        console.log("userId: admin (AI 대화와 동일)");
        console.log("insttCode: geonpaas (AI 대화와 동일)");
        console.log("userSeCode: 14");
        console.log("API Key:", config.headers.crtfckey ? "존재함" : "없음");
        console.log("Base URL:", config.baseUrl);
        // getLayer 도구 실행 (AI 대화와 동일한 파라미터 사용)
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLayer"].execute({
            userId: "admin",
            insttCode: "geonpaas",
            userSeCode: "14",
            lyrId: lyrId
        }, {
            abortSignal: new AbortController().signal,
            toolCallId: `api-layer-add-${lyrId}-${Date.now()}`,
            messages: []
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
    } catch (error) {
        console.error("=== 레이어 조회 실패 ===");
        console.error("Error:", error);
        console.error("Stack:", error instanceof Error ? error.stack : "No stack");
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: "레이어 조회에 실패했습니다"
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__3469e62a._.js.map