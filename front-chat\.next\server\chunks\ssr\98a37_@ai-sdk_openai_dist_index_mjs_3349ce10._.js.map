{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/openai-provider.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/openai-chat-language-model.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/convert-to-openai-chat-messages.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/map-openai-chat-logprobs.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/map-openai-finish-reason.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/openai-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/get-response-metadata.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/openai-prepare-tools.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/openai-completion-language-model.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/convert-to-openai-completion-prompt.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/map-openai-completion-logprobs.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/openai-embedding-model.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/openai-image-model.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/openai-image-settings.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/openai-transcription-model.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/responses/openai-responses-language-model.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/responses/convert-to-openai-responses-messages.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/responses/map-openai-responses-finish-reason.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/responses/openai-responses-prepare-tools.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/openai-tools.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bopenai%401.3.22_zod%403.24.1/node_modules/%40ai-sdk/openai/src/openai-speech-model.ts"], "sourcesContent": ["import {\n  EmbeddingModelV1,\n  ImageModelV1,\n  TranscriptionModelV1,\n  LanguageModelV1,\n  ProviderV1,\n  SpeechModelV1,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  loadApiKey,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport { OpenAIChatLanguageModel } from './openai-chat-language-model';\nimport { OpenAIChatModelId, OpenAIChatSettings } from './openai-chat-settings';\nimport { OpenAICompletionLanguageModel } from './openai-completion-language-model';\nimport {\n  OpenAICompletionModelId,\n  OpenAICompletionSettings,\n} from './openai-completion-settings';\nimport { OpenAIEmbeddingModel } from './openai-embedding-model';\nimport {\n  OpenAIEmbeddingModelId,\n  OpenAIEmbeddingSettings,\n} from './openai-embedding-settings';\nimport { OpenAIImageModel } from './openai-image-model';\nimport {\n  OpenAIImageModelId,\n  OpenAIImageSettings,\n} from './openai-image-settings';\nimport { OpenAITranscriptionModel } from './openai-transcription-model';\nimport { OpenAITranscriptionModelId } from './openai-transcription-settings';\nimport { OpenAIResponsesLanguageModel } from './responses/openai-responses-language-model';\nimport { OpenAIResponsesModelId } from './responses/openai-responses-settings';\nimport { openaiTools } from './openai-tools';\nimport { OpenAISpeechModel } from './openai-speech-model';\nimport { OpenAISpeechModelId } from './openai-speech-settings';\n\nexport interface OpenAIProvider extends ProviderV1 {\n  (\n    modelId: 'gpt-3.5-turbo-instruct',\n    settings?: OpenAICompletionSettings,\n  ): OpenAICompletionLanguageModel;\n  (modelId: OpenAIChatModelId, settings?: OpenAIChatSettings): LanguageModelV1;\n\n  /**\nCreates an OpenAI model for text generation.\n   */\n  languageModel(\n    modelId: 'gpt-3.5-turbo-instruct',\n    settings?: OpenAICompletionSettings,\n  ): OpenAICompletionLanguageModel;\n  languageModel(\n    modelId: OpenAIChatModelId,\n    settings?: OpenAIChatSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates an OpenAI chat model for text generation.\n   */\n  chat(\n    modelId: OpenAIChatModelId,\n    settings?: OpenAIChatSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates an OpenAI responses API model for text generation.\n   */\n  responses(modelId: OpenAIResponsesModelId): LanguageModelV1;\n\n  /**\nCreates an OpenAI completion model for text generation.\n   */\n  completion(\n    modelId: OpenAICompletionModelId,\n    settings?: OpenAICompletionSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates a model for text embeddings.\n   */\n  embedding(\n    modelId: OpenAIEmbeddingModelId,\n    settings?: OpenAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  /**\nCreates a model for text embeddings.\n\n@deprecated Use `textEmbeddingModel` instead.\n   */\n  textEmbedding(\n    modelId: OpenAIEmbeddingModelId,\n    settings?: OpenAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  /**\nCreates a model for text embeddings.\n   */\n  textEmbeddingModel(\n    modelId: OpenAIEmbeddingModelId,\n    settings?: OpenAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  /**\nCreates a model for image generation.\n   */\n  image(\n    modelId: OpenAIImageModelId,\n    settings?: OpenAIImageSettings,\n  ): ImageModelV1;\n\n  /**\nCreates a model for image generation.\n   */\n  imageModel(\n    modelId: OpenAIImageModelId,\n    settings?: OpenAIImageSettings,\n  ): ImageModelV1;\n\n  /**\nCreates a model for transcription.\n   */\n  transcription(modelId: OpenAITranscriptionModelId): TranscriptionModelV1;\n\n  /**\nCreates a model for speech generation.\n   */\n  speech(modelId: OpenAISpeechModelId): SpeechModelV1;\n\n  /**\nOpenAI-specific tools.\n   */\n  tools: typeof openaiTools;\n}\n\nexport interface OpenAIProviderSettings {\n  /**\nBase URL for the OpenAI API calls.\n     */\n  baseURL?: string;\n\n  /**\nAPI key for authenticating requests.\n     */\n  apiKey?: string;\n\n  /**\nOpenAI Organization.\n     */\n  organization?: string;\n\n  /**\nOpenAI project.\n     */\n  project?: string;\n\n  /**\nCustom headers to include in the requests.\n     */\n  headers?: Record<string, string>;\n\n  /**\nOpenAI compatibility mode. Should be set to `strict` when using the OpenAI API,\nand `compatible` when using 3rd party providers. In `compatible` mode, newer\ninformation such as streamOptions are not being sent. Defaults to 'compatible'.\n   */\n  compatibility?: 'strict' | 'compatible';\n\n  /**\nProvider name. Overrides the `openai` default name for 3rd party providers.\n   */\n  name?: string;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n}\n\n/**\nCreate an OpenAI provider instance.\n */\nexport function createOpenAI(\n  options: OpenAIProviderSettings = {},\n): OpenAIProvider {\n  const baseURL =\n    withoutTrailingSlash(options.baseURL) ?? 'https://api.openai.com/v1';\n\n  // we default to compatible, because strict breaks providers like Groq:\n  const compatibility = options.compatibility ?? 'compatible';\n\n  const providerName = options.name ?? 'openai';\n\n  const getHeaders = () => ({\n    Authorization: `Bearer ${loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: 'OPENAI_API_KEY',\n      description: 'OpenAI',\n    })}`,\n    'OpenAI-Organization': options.organization,\n    'OpenAI-Project': options.project,\n    ...options.headers,\n  });\n\n  const createChatModel = (\n    modelId: OpenAIChatModelId,\n    settings: OpenAIChatSettings = {},\n  ) =>\n    new OpenAIChatLanguageModel(modelId, settings, {\n      provider: `${providerName}.chat`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      compatibility,\n      fetch: options.fetch,\n    });\n\n  const createCompletionModel = (\n    modelId: OpenAICompletionModelId,\n    settings: OpenAICompletionSettings = {},\n  ) =>\n    new OpenAICompletionLanguageModel(modelId, settings, {\n      provider: `${providerName}.completion`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      compatibility,\n      fetch: options.fetch,\n    });\n\n  const createEmbeddingModel = (\n    modelId: OpenAIEmbeddingModelId,\n    settings: OpenAIEmbeddingSettings = {},\n  ) =>\n    new OpenAIEmbeddingModel(modelId, settings, {\n      provider: `${providerName}.embedding`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const createImageModel = (\n    modelId: OpenAIImageModelId,\n    settings: OpenAIImageSettings = {},\n  ) =>\n    new OpenAIImageModel(modelId, settings, {\n      provider: `${providerName}.image`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const createTranscriptionModel = (modelId: OpenAITranscriptionModelId) =>\n    new OpenAITranscriptionModel(modelId, {\n      provider: `${providerName}.transcription`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const createSpeechModel = (modelId: OpenAISpeechModelId) =>\n    new OpenAISpeechModel(modelId, {\n      provider: `${providerName}.speech`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const createLanguageModel = (\n    modelId: OpenAIChatModelId | OpenAICompletionModelId,\n    settings?: OpenAIChatSettings | OpenAICompletionSettings,\n  ) => {\n    if (new.target) {\n      throw new Error(\n        'The OpenAI model function cannot be called with the new keyword.',\n      );\n    }\n\n    if (modelId === 'gpt-3.5-turbo-instruct') {\n      return createCompletionModel(\n        modelId,\n        settings as OpenAICompletionSettings,\n      );\n    }\n\n    return createChatModel(modelId, settings as OpenAIChatSettings);\n  };\n\n  const createResponsesModel = (modelId: OpenAIResponsesModelId) => {\n    return new OpenAIResponsesLanguageModel(modelId, {\n      provider: `${providerName}.responses`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n  };\n\n  const provider = function (\n    modelId: OpenAIChatModelId | OpenAICompletionModelId,\n    settings?: OpenAIChatSettings | OpenAICompletionSettings,\n  ) {\n    return createLanguageModel(modelId, settings);\n  };\n\n  provider.languageModel = createLanguageModel;\n  provider.chat = createChatModel;\n  provider.completion = createCompletionModel;\n  provider.responses = createResponsesModel;\n  provider.embedding = createEmbeddingModel;\n  provider.textEmbedding = createEmbeddingModel;\n  provider.textEmbeddingModel = createEmbeddingModel;\n\n  provider.image = createImageModel;\n  provider.imageModel = createImageModel;\n\n  provider.transcription = createTranscriptionModel;\n  provider.transcriptionModel = createTranscriptionModel;\n\n  provider.speech = createSpeechModel;\n  provider.speechModel = createSpeechModel;\n\n  provider.tools = openaiTools;\n\n  return provider as OpenAIProvider;\n}\n\n/**\nDefault OpenAI provider instance. It uses 'strict' compatibility mode.\n */\nexport const openai = createOpenAI({\n  compatibility: 'strict', // strict for OpenAI API\n});\n", "import {\n  InvalidResponseDataError,\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1LogProbs,\n  LanguageModelV1ProviderMetadata,\n  LanguageModelV1StreamPart,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  ParseResult,\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  generateId,\n  isParsableJson,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { convertToOpenAIChatMessages } from './convert-to-openai-chat-messages';\nimport { mapOpenAIChatLogProbsOutput } from './map-openai-chat-logprobs';\nimport { mapOpenAIFinishReason } from './map-openai-finish-reason';\nimport { OpenAIChatModelId, OpenAIChatSettings } from './openai-chat-settings';\nimport {\n  openaiErrorDataSchema,\n  openaiFailedResponseHandler,\n} from './openai-error';\nimport { getResponseMetadata } from './get-response-metadata';\nimport { prepareTools } from './openai-prepare-tools';\n\ntype OpenAIChatConfig = {\n  provider: string;\n  compatibility: 'strict' | 'compatible';\n  headers: () => Record<string, string | undefined>;\n  url: (options: { modelId: string; path: string }) => string;\n  fetch?: FetchFunction;\n};\n\nexport class OpenAIChatLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n\n  readonly modelId: OpenAIChatModelId;\n  readonly settings: OpenAIChatSettings;\n\n  private readonly config: OpenAIChatConfig;\n\n  constructor(\n    modelId: OpenAIChatModelId,\n    settings: OpenAIChatSettings,\n    config: OpenAIChatConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  get supportsStructuredOutputs(): boolean {\n    // enable structured outputs for reasoning models by default:\n    // TODO in the next major version, remove this and always use json mode for models\n    // that support structured outputs (blacklist other models)\n    return this.settings.structuredOutputs ?? isReasoningModel(this.modelId);\n  }\n\n  get defaultObjectGenerationMode() {\n    // audio models don't support structured outputs:\n    if (isAudioModel(this.modelId)) {\n      return 'tool';\n    }\n\n    return this.supportsStructuredOutputs ? 'json' : 'tool';\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  get supportsImageUrls(): boolean {\n    // image urls can be sent if downloadImages is disabled (default):\n    return !this.settings.downloadImages;\n  }\n\n  private getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    providerMetadata,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]) {\n    const type = mode.type;\n\n    const warnings: LanguageModelV1CallWarning[] = [];\n\n    if (topK != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'topK',\n      });\n    }\n\n    if (\n      responseFormat?.type === 'json' &&\n      responseFormat.schema != null &&\n      !this.supportsStructuredOutputs\n    ) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'responseFormat',\n        details:\n          'JSON response format schema is only supported with structuredOutputs',\n      });\n    }\n\n    const useLegacyFunctionCalling = this.settings.useLegacyFunctionCalling;\n\n    if (useLegacyFunctionCalling && this.settings.parallelToolCalls === true) {\n      throw new UnsupportedFunctionalityError({\n        functionality: 'useLegacyFunctionCalling with parallelToolCalls',\n      });\n    }\n\n    if (useLegacyFunctionCalling && this.supportsStructuredOutputs) {\n      throw new UnsupportedFunctionalityError({\n        functionality: 'structuredOutputs with useLegacyFunctionCalling',\n      });\n    }\n\n    const { messages, warnings: messageWarnings } = convertToOpenAIChatMessages(\n      {\n        prompt,\n        useLegacyFunctionCalling,\n        systemMessageMode: getSystemMessageMode(this.modelId),\n      },\n    );\n\n    warnings.push(...messageWarnings);\n\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n\n      // model specific settings:\n      logit_bias: this.settings.logitBias,\n      logprobs:\n        this.settings.logprobs === true ||\n        typeof this.settings.logprobs === 'number'\n          ? true\n          : undefined,\n      top_logprobs:\n        typeof this.settings.logprobs === 'number'\n          ? this.settings.logprobs\n          : typeof this.settings.logprobs === 'boolean'\n            ? this.settings.logprobs\n              ? 0\n              : undefined\n            : undefined,\n      user: this.settings.user,\n      parallel_tool_calls: this.settings.parallelToolCalls,\n\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      response_format:\n        responseFormat?.type === 'json'\n          ? this.supportsStructuredOutputs && responseFormat.schema != null\n            ? {\n                type: 'json_schema',\n                json_schema: {\n                  schema: responseFormat.schema,\n                  strict: true,\n                  name: responseFormat.name ?? 'response',\n                  description: responseFormat.description,\n                },\n              }\n            : { type: 'json_object' }\n          : undefined,\n      stop: stopSequences,\n      seed,\n\n      // openai specific settings:\n      // TODO remove in next major version; we auto-map maxTokens now\n      max_completion_tokens: providerMetadata?.openai?.maxCompletionTokens,\n      store: providerMetadata?.openai?.store,\n      metadata: providerMetadata?.openai?.metadata,\n      prediction: providerMetadata?.openai?.prediction,\n      reasoning_effort:\n        providerMetadata?.openai?.reasoningEffort ??\n        this.settings.reasoningEffort,\n\n      // messages:\n      messages,\n    };\n\n    if (isReasoningModel(this.modelId)) {\n      // remove unsupported settings for reasoning models\n      // see https://platform.openai.com/docs/guides/reasoning#limitations\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'temperature',\n          details: 'temperature is not supported for reasoning models',\n        });\n      }\n      if (baseArgs.top_p != null) {\n        baseArgs.top_p = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'topP',\n          details: 'topP is not supported for reasoning models',\n        });\n      }\n      if (baseArgs.frequency_penalty != null) {\n        baseArgs.frequency_penalty = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'frequencyPenalty',\n          details: 'frequencyPenalty is not supported for reasoning models',\n        });\n      }\n      if (baseArgs.presence_penalty != null) {\n        baseArgs.presence_penalty = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'presencePenalty',\n          details: 'presencePenalty is not supported for reasoning models',\n        });\n      }\n      if (baseArgs.logit_bias != null) {\n        baseArgs.logit_bias = undefined;\n        warnings.push({\n          type: 'other',\n          message: 'logitBias is not supported for reasoning models',\n        });\n      }\n      if (baseArgs.logprobs != null) {\n        baseArgs.logprobs = undefined;\n        warnings.push({\n          type: 'other',\n          message: 'logprobs is not supported for reasoning models',\n        });\n      }\n      if (baseArgs.top_logprobs != null) {\n        baseArgs.top_logprobs = undefined;\n        warnings.push({\n          type: 'other',\n          message: 'topLogprobs is not supported for reasoning models',\n        });\n      }\n\n      // reasoning models use max_completion_tokens instead of max_tokens:\n      if (baseArgs.max_tokens != null) {\n        if (baseArgs.max_completion_tokens == null) {\n          baseArgs.max_completion_tokens = baseArgs.max_tokens;\n        }\n        baseArgs.max_tokens = undefined;\n      }\n    } else if (\n      this.modelId.startsWith('gpt-4o-search-preview') ||\n      this.modelId.startsWith('gpt-4o-mini-search-preview')\n    ) {\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'temperature',\n          details:\n            'temperature is not supported for the search preview models and has been removed.',\n        });\n      }\n    }\n    switch (type) {\n      case 'regular': {\n        const { tools, tool_choice, functions, function_call, toolWarnings } =\n          prepareTools({\n            mode,\n            useLegacyFunctionCalling,\n            structuredOutputs: this.supportsStructuredOutputs,\n          });\n\n        return {\n          args: {\n            ...baseArgs,\n            tools,\n            tool_choice,\n            functions,\n            function_call,\n          },\n          warnings: [...warnings, ...toolWarnings],\n        };\n      }\n\n      case 'object-json': {\n        return {\n          args: {\n            ...baseArgs,\n            response_format:\n              this.supportsStructuredOutputs && mode.schema != null\n                ? {\n                    type: 'json_schema',\n                    json_schema: {\n                      schema: mode.schema,\n                      strict: true,\n                      name: mode.name ?? 'response',\n                      description: mode.description,\n                    },\n                  }\n                : { type: 'json_object' },\n          },\n          warnings,\n        };\n      }\n\n      case 'object-tool': {\n        return {\n          args: useLegacyFunctionCalling\n            ? {\n                ...baseArgs,\n                function_call: {\n                  name: mode.tool.name,\n                },\n                functions: [\n                  {\n                    name: mode.tool.name,\n                    description: mode.tool.description,\n                    parameters: mode.tool.parameters,\n                  },\n                ],\n              }\n            : {\n                ...baseArgs,\n                tool_choice: {\n                  type: 'function',\n                  function: { name: mode.tool.name },\n                },\n                tools: [\n                  {\n                    type: 'function',\n                    function: {\n                      name: mode.tool.name,\n                      description: mode.tool.description,\n                      parameters: mode.tool.parameters,\n                      strict: this.supportsStructuredOutputs ? true : undefined,\n                    },\n                  },\n                ],\n              },\n          warnings,\n        };\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { args: body, warnings } = this.getArgs(options);\n\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse,\n    } = await postJsonToApi({\n      url: this.config.url({\n        path: '/chat/completions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        openaiChatResponseSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = body;\n    const choice = response.choices[0];\n\n    // provider metadata:\n    const completionTokenDetails = response.usage?.completion_tokens_details;\n    const promptTokenDetails = response.usage?.prompt_tokens_details;\n    const providerMetadata: LanguageModelV1ProviderMetadata = { openai: {} };\n    if (completionTokenDetails?.reasoning_tokens != null) {\n      providerMetadata.openai.reasoningTokens =\n        completionTokenDetails?.reasoning_tokens;\n    }\n    if (completionTokenDetails?.accepted_prediction_tokens != null) {\n      providerMetadata.openai.acceptedPredictionTokens =\n        completionTokenDetails?.accepted_prediction_tokens;\n    }\n    if (completionTokenDetails?.rejected_prediction_tokens != null) {\n      providerMetadata.openai.rejectedPredictionTokens =\n        completionTokenDetails?.rejected_prediction_tokens;\n    }\n    if (promptTokenDetails?.cached_tokens != null) {\n      providerMetadata.openai.cachedPromptTokens =\n        promptTokenDetails?.cached_tokens;\n    }\n\n    return {\n      text: choice.message.content ?? undefined,\n      toolCalls:\n        this.settings.useLegacyFunctionCalling && choice.message.function_call\n          ? [\n              {\n                toolCallType: 'function',\n                toolCallId: generateId(),\n                toolName: choice.message.function_call.name,\n                args: choice.message.function_call.arguments,\n              },\n            ]\n          : choice.message.tool_calls?.map(toolCall => ({\n              toolCallType: 'function',\n              toolCallId: toolCall.id ?? generateId(),\n              toolName: toolCall.function.name,\n              args: toolCall.function.arguments!,\n            })),\n      finishReason: mapOpenAIFinishReason(choice.finish_reason),\n      usage: {\n        promptTokens: response.usage?.prompt_tokens ?? NaN,\n        completionTokens: response.usage?.completion_tokens ?? NaN,\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders, body: rawResponse },\n      request: { body: JSON.stringify(body) },\n      response: getResponseMetadata(response),\n      warnings,\n      logprobs: mapOpenAIChatLogProbsOutput(choice.logprobs),\n      providerMetadata,\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    if (this.settings.simulateStreaming) {\n      const result = await this.doGenerate(options);\n\n      const simulatedStream = new ReadableStream<LanguageModelV1StreamPart>({\n        start(controller) {\n          controller.enqueue({ type: 'response-metadata', ...result.response });\n          if (result.text) {\n            controller.enqueue({\n              type: 'text-delta',\n              textDelta: result.text,\n            });\n          }\n          if (result.toolCalls) {\n            for (const toolCall of result.toolCalls) {\n              controller.enqueue({\n                type: 'tool-call-delta',\n                toolCallType: 'function',\n                toolCallId: toolCall.toolCallId,\n                toolName: toolCall.toolName,\n                argsTextDelta: toolCall.args,\n              });\n\n              controller.enqueue({\n                type: 'tool-call',\n                ...toolCall,\n              });\n            }\n          }\n          controller.enqueue({\n            type: 'finish',\n            finishReason: result.finishReason,\n            usage: result.usage,\n            logprobs: result.logprobs,\n            providerMetadata: result.providerMetadata,\n          });\n          controller.close();\n        },\n      });\n      return {\n        stream: simulatedStream,\n        rawCall: result.rawCall,\n        rawResponse: result.rawResponse,\n        warnings: result.warnings,\n      };\n    }\n\n    const { args, warnings } = this.getArgs(options);\n\n    const body = {\n      ...args,\n      stream: true,\n\n      // only include stream_options when in strict compatibility mode:\n      stream_options:\n        this.config.compatibility === 'strict'\n          ? { include_usage: true }\n          : undefined,\n    };\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: '/chat/completions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        openaiChatChunkSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n\n    const toolCalls: Array<{\n      id: string;\n      type: 'function';\n      function: {\n        name: string;\n        arguments: string;\n      };\n      hasFinished: boolean;\n    }> = [];\n\n    let finishReason: LanguageModelV1FinishReason = 'unknown';\n    let usage: {\n      promptTokens: number | undefined;\n      completionTokens: number | undefined;\n    } = {\n      promptTokens: undefined,\n      completionTokens: undefined,\n    };\n    let logprobs: LanguageModelV1LogProbs;\n    let isFirstChunk = true;\n\n    const { useLegacyFunctionCalling } = this.settings;\n\n    const providerMetadata: LanguageModelV1ProviderMetadata = { openai: {} };\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof openaiChatChunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            // handle failed chunk parsing / validation:\n            if (!chunk.success) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            // handle error chunks:\n            if ('error' in value) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: value.error });\n              return;\n            }\n\n            if (isFirstChunk) {\n              isFirstChunk = false;\n\n              controller.enqueue({\n                type: 'response-metadata',\n                ...getResponseMetadata(value),\n              });\n            }\n\n            if (value.usage != null) {\n              const {\n                prompt_tokens,\n                completion_tokens,\n                prompt_tokens_details,\n                completion_tokens_details,\n              } = value.usage;\n\n              usage = {\n                promptTokens: prompt_tokens ?? undefined,\n                completionTokens: completion_tokens ?? undefined,\n              };\n\n              if (completion_tokens_details?.reasoning_tokens != null) {\n                providerMetadata.openai.reasoningTokens =\n                  completion_tokens_details?.reasoning_tokens;\n              }\n              if (\n                completion_tokens_details?.accepted_prediction_tokens != null\n              ) {\n                providerMetadata.openai.acceptedPredictionTokens =\n                  completion_tokens_details?.accepted_prediction_tokens;\n              }\n              if (\n                completion_tokens_details?.rejected_prediction_tokens != null\n              ) {\n                providerMetadata.openai.rejectedPredictionTokens =\n                  completion_tokens_details?.rejected_prediction_tokens;\n              }\n              if (prompt_tokens_details?.cached_tokens != null) {\n                providerMetadata.openai.cachedPromptTokens =\n                  prompt_tokens_details?.cached_tokens;\n              }\n            }\n\n            const choice = value.choices[0];\n\n            if (choice?.finish_reason != null) {\n              finishReason = mapOpenAIFinishReason(choice.finish_reason);\n            }\n\n            if (choice?.delta == null) {\n              return;\n            }\n\n            const delta = choice.delta;\n\n            if (delta.content != null) {\n              controller.enqueue({\n                type: 'text-delta',\n                textDelta: delta.content,\n              });\n            }\n\n            const mappedLogprobs = mapOpenAIChatLogProbsOutput(\n              choice?.logprobs,\n            );\n            if (mappedLogprobs?.length) {\n              if (logprobs === undefined) logprobs = [];\n              logprobs.push(...mappedLogprobs);\n            }\n\n            const mappedToolCalls: typeof delta.tool_calls =\n              useLegacyFunctionCalling && delta.function_call != null\n                ? [\n                    {\n                      type: 'function',\n                      id: generateId(),\n                      function: delta.function_call,\n                      index: 0,\n                    },\n                  ]\n                : delta.tool_calls;\n\n            if (mappedToolCalls != null) {\n              for (const toolCallDelta of mappedToolCalls) {\n                const index = toolCallDelta.index;\n\n                // Tool call start. OpenAI returns all information except the arguments in the first chunk.\n                if (toolCalls[index] == null) {\n                  if (toolCallDelta.type !== 'function') {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function' type.`,\n                    });\n                  }\n\n                  if (toolCallDelta.id == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'id' to be a string.`,\n                    });\n                  }\n\n                  if (toolCallDelta.function?.name == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function.name' to be a string.`,\n                    });\n                  }\n\n                  toolCalls[index] = {\n                    id: toolCallDelta.id,\n                    type: 'function',\n                    function: {\n                      name: toolCallDelta.function.name,\n                      arguments: toolCallDelta.function.arguments ?? '',\n                    },\n                    hasFinished: false,\n                  };\n\n                  const toolCall = toolCalls[index];\n\n                  if (\n                    toolCall.function?.name != null &&\n                    toolCall.function?.arguments != null\n                  ) {\n                    // send delta if the argument text has already started:\n                    if (toolCall.function.arguments.length > 0) {\n                      controller.enqueue({\n                        type: 'tool-call-delta',\n                        toolCallType: 'function',\n                        toolCallId: toolCall.id,\n                        toolName: toolCall.function.name,\n                        argsTextDelta: toolCall.function.arguments,\n                      });\n                    }\n\n                    // check if tool call is complete\n                    // (some providers send the full tool call in one chunk):\n                    if (isParsableJson(toolCall.function.arguments)) {\n                      controller.enqueue({\n                        type: 'tool-call',\n                        toolCallType: 'function',\n                        toolCallId: toolCall.id ?? generateId(),\n                        toolName: toolCall.function.name,\n                        args: toolCall.function.arguments,\n                      });\n                      toolCall.hasFinished = true;\n                    }\n                  }\n\n                  continue;\n                }\n\n                // existing tool call, merge if not finished\n                const toolCall = toolCalls[index];\n\n                if (toolCall.hasFinished) {\n                  continue;\n                }\n\n                if (toolCallDelta.function?.arguments != null) {\n                  toolCall.function!.arguments +=\n                    toolCallDelta.function?.arguments ?? '';\n                }\n\n                // send delta\n                controller.enqueue({\n                  type: 'tool-call-delta',\n                  toolCallType: 'function',\n                  toolCallId: toolCall.id,\n                  toolName: toolCall.function.name,\n                  argsTextDelta: toolCallDelta.function.arguments ?? '',\n                });\n\n                // check if tool call is complete\n                if (\n                  toolCall.function?.name != null &&\n                  toolCall.function?.arguments != null &&\n                  isParsableJson(toolCall.function.arguments)\n                ) {\n                  controller.enqueue({\n                    type: 'tool-call',\n                    toolCallType: 'function',\n                    toolCallId: toolCall.id ?? generateId(),\n                    toolName: toolCall.function.name,\n                    args: toolCall.function.arguments,\n                  });\n                  toolCall.hasFinished = true;\n                }\n              }\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({\n              type: 'finish',\n              finishReason,\n              logprobs,\n              usage: {\n                promptTokens: usage.promptTokens ?? NaN,\n                completionTokens: usage.completionTokens ?? NaN,\n              },\n              ...(providerMetadata != null ? { providerMetadata } : {}),\n            });\n          },\n        }),\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      request: { body: JSON.stringify(body) },\n      warnings,\n    };\n  }\n}\n\nconst openaiTokenUsageSchema = z\n  .object({\n    prompt_tokens: z.number().nullish(),\n    completion_tokens: z.number().nullish(),\n    prompt_tokens_details: z\n      .object({\n        cached_tokens: z.number().nullish(),\n      })\n      .nullish(),\n    completion_tokens_details: z\n      .object({\n        reasoning_tokens: z.number().nullish(),\n        accepted_prediction_tokens: z.number().nullish(),\n        rejected_prediction_tokens: z.number().nullish(),\n      })\n      .nullish(),\n  })\n  .nullish();\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openaiChatResponseSchema = z.object({\n  id: z.string().nullish(),\n  created: z.number().nullish(),\n  model: z.string().nullish(),\n  choices: z.array(\n    z.object({\n      message: z.object({\n        role: z.literal('assistant').nullish(),\n        content: z.string().nullish(),\n        function_call: z\n          .object({\n            arguments: z.string(),\n            name: z.string(),\n          })\n          .nullish(),\n        tool_calls: z\n          .array(\n            z.object({\n              id: z.string().nullish(),\n              type: z.literal('function'),\n              function: z.object({\n                name: z.string(),\n                arguments: z.string(),\n              }),\n            }),\n          )\n          .nullish(),\n      }),\n      index: z.number(),\n      logprobs: z\n        .object({\n          content: z\n            .array(\n              z.object({\n                token: z.string(),\n                logprob: z.number(),\n                top_logprobs: z.array(\n                  z.object({\n                    token: z.string(),\n                    logprob: z.number(),\n                  }),\n                ),\n              }),\n            )\n            .nullable(),\n        })\n        .nullish(),\n      finish_reason: z.string().nullish(),\n    }),\n  ),\n  usage: openaiTokenUsageSchema,\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openaiChatChunkSchema = z.union([\n  z.object({\n    id: z.string().nullish(),\n    created: z.number().nullish(),\n    model: z.string().nullish(),\n    choices: z.array(\n      z.object({\n        delta: z\n          .object({\n            role: z.enum(['assistant']).nullish(),\n            content: z.string().nullish(),\n            function_call: z\n              .object({\n                name: z.string().optional(),\n                arguments: z.string().optional(),\n              })\n              .nullish(),\n            tool_calls: z\n              .array(\n                z.object({\n                  index: z.number(),\n                  id: z.string().nullish(),\n                  type: z.literal('function').nullish(),\n                  function: z.object({\n                    name: z.string().nullish(),\n                    arguments: z.string().nullish(),\n                  }),\n                }),\n              )\n              .nullish(),\n          })\n          .nullish(),\n        logprobs: z\n          .object({\n            content: z\n              .array(\n                z.object({\n                  token: z.string(),\n                  logprob: z.number(),\n                  top_logprobs: z.array(\n                    z.object({\n                      token: z.string(),\n                      logprob: z.number(),\n                    }),\n                  ),\n                }),\n              )\n              .nullable(),\n          })\n          .nullish(),\n        finish_reason: z.string().nullish(),\n        index: z.number(),\n      }),\n    ),\n    usage: openaiTokenUsageSchema,\n  }),\n  openaiErrorDataSchema,\n]);\n\nfunction isReasoningModel(modelId: string) {\n  return modelId.startsWith('o');\n}\n\nfunction isAudioModel(modelId: string) {\n  return modelId.startsWith('gpt-4o-audio-preview');\n}\n\nfunction getSystemMessageMode(modelId: string) {\n  if (!isReasoningModel(modelId)) {\n    return 'system';\n  }\n\n  return (\n    reasoningModels[modelId as keyof typeof reasoningModels]\n      ?.systemMessageMode ?? 'developer'\n  );\n}\n\nconst reasoningModels = {\n  'o1-mini': {\n    systemMessageMode: 'remove',\n  },\n  'o1-mini-2024-09-12': {\n    systemMessageMode: 'remove',\n  },\n  'o1-preview': {\n    systemMessageMode: 'remove',\n  },\n  'o1-preview-2024-09-12': {\n    systemMessageMode: 'remove',\n  },\n  o3: {\n    systemMessageMode: 'developer',\n  },\n  'o3-2025-04-16': {\n    systemMessageMode: 'developer',\n  },\n  'o3-mini': {\n    systemMessageMode: 'developer',\n  },\n  'o3-mini-2025-01-31': {\n    systemMessageMode: 'developer',\n  },\n  'o4-mini': {\n    systemMessageMode: 'developer',\n  },\n  'o4-mini-2025-04-16': {\n    systemMessageMode: 'developer',\n  },\n} as const;\n", "import {\n  LanguageModelV1CallWarning,\n  LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { convertUint8ArrayToBase64 } from '@ai-sdk/provider-utils';\nimport { OpenAIChatPrompt } from './openai-chat-prompt';\n\nexport function convertToOpenAIChatMessages({\n  prompt,\n  useLegacyFunctionCalling = false,\n  systemMessageMode = 'system',\n}: {\n  prompt: LanguageModelV1Prompt;\n  useLegacyFunctionCalling?: boolean;\n  systemMessageMode?: 'system' | 'developer' | 'remove';\n}): {\n  messages: OpenAIChatPrompt;\n  warnings: Array<LanguageModelV1CallWarning>;\n} {\n  const messages: OpenAIChatPrompt = [];\n  const warnings: Array<LanguageModelV1CallWarning> = [];\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case 'system': {\n        switch (systemMessageMode) {\n          case 'system': {\n            messages.push({ role: 'system', content });\n            break;\n          }\n          case 'developer': {\n            messages.push({ role: 'developer', content });\n            break;\n          }\n          case 'remove': {\n            warnings.push({\n              type: 'other',\n              message: 'system messages are removed for this model',\n            });\n            break;\n          }\n          default: {\n            const _exhaustiveCheck: never = systemMessageMode;\n            throw new Error(\n              `Unsupported system message mode: ${_exhaustiveCheck}`,\n            );\n          }\n        }\n        break;\n      }\n\n      case 'user': {\n        if (content.length === 1 && content[0].type === 'text') {\n          messages.push({ role: 'user', content: content[0].text });\n          break;\n        }\n\n        messages.push({\n          role: 'user',\n          content: content.map((part, index) => {\n            switch (part.type) {\n              case 'text': {\n                return { type: 'text', text: part.text };\n              }\n              case 'image': {\n                return {\n                  type: 'image_url',\n                  image_url: {\n                    url:\n                      part.image instanceof URL\n                        ? part.image.toString()\n                        : `data:${\n                            part.mimeType ?? 'image/jpeg'\n                          };base64,${convertUint8ArrayToBase64(part.image)}`,\n\n                    // OpenAI specific extension: image detail\n                    detail: part.providerMetadata?.openai?.imageDetail,\n                  },\n                };\n              }\n              case 'file': {\n                if (part.data instanceof URL) {\n                  throw new UnsupportedFunctionalityError({\n                    functionality:\n                      \"'File content parts with URL data' functionality not supported.\",\n                  });\n                }\n\n                switch (part.mimeType) {\n                  case 'audio/wav': {\n                    return {\n                      type: 'input_audio',\n                      input_audio: { data: part.data, format: 'wav' },\n                    };\n                  }\n                  case 'audio/mp3':\n                  case 'audio/mpeg': {\n                    return {\n                      type: 'input_audio',\n                      input_audio: { data: part.data, format: 'mp3' },\n                    };\n                  }\n                  case 'application/pdf': {\n                    return {\n                      type: 'file',\n                      file: {\n                        filename: part.filename ?? `part-${index}.pdf`,\n                        file_data: `data:application/pdf;base64,${part.data}`,\n                      },\n                    };\n                  }\n                  default: {\n                    throw new UnsupportedFunctionalityError({\n                      functionality: `File content part type ${part.mimeType} in user messages`,\n                    });\n                  }\n                }\n              }\n            }\n          }),\n        });\n\n        break;\n      }\n\n      case 'assistant': {\n        let text = '';\n        const toolCalls: Array<{\n          id: string;\n          type: 'function';\n          function: { name: string; arguments: string };\n        }> = [];\n\n        for (const part of content) {\n          switch (part.type) {\n            case 'text': {\n              text += part.text;\n              break;\n            }\n            case 'tool-call': {\n              toolCalls.push({\n                id: part.toolCallId,\n                type: 'function',\n                function: {\n                  name: part.toolName,\n                  arguments: JSON.stringify(part.args),\n                },\n              });\n              break;\n            }\n          }\n        }\n\n        if (useLegacyFunctionCalling) {\n          if (toolCalls.length > 1) {\n            throw new UnsupportedFunctionalityError({\n              functionality:\n                'useLegacyFunctionCalling with multiple tool calls in one message',\n            });\n          }\n\n          messages.push({\n            role: 'assistant',\n            content: text,\n            function_call:\n              toolCalls.length > 0 ? toolCalls[0].function : undefined,\n          });\n        } else {\n          messages.push({\n            role: 'assistant',\n            content: text,\n            tool_calls: toolCalls.length > 0 ? toolCalls : undefined,\n          });\n        }\n\n        break;\n      }\n\n      case 'tool': {\n        for (const toolResponse of content) {\n          if (useLegacyFunctionCalling) {\n            messages.push({\n              role: 'function',\n              name: toolResponse.toolName,\n              content: JSON.stringify(toolResponse.result),\n            });\n          } else {\n            messages.push({\n              role: 'tool',\n              tool_call_id: toolResponse.toolCallId,\n              content: JSON.stringify(toolResponse.result),\n            });\n          }\n        }\n        break;\n      }\n\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return { messages, warnings };\n}\n", "import { LanguageModelV1LogProbs } from '@ai-sdk/provider';\n\ntype OpenAIChatLogProbs = {\n  content:\n    | {\n        token: string;\n        logprob: number;\n        top_logprobs:\n          | {\n              token: string;\n              logprob: number;\n            }[]\n          | null;\n      }[]\n    | null;\n};\n\nexport function mapOpenAIChatLogProbsOutput(\n  logprobs: OpenAIChatLogProbs | null | undefined,\n): LanguageModelV1LogProbs | undefined {\n  return (\n    logprobs?.content?.map(({ token, logprob, top_logprobs }) => ({\n      token,\n      logprob,\n      topLogprobs: top_logprobs\n        ? top_logprobs.map(({ token, logprob }) => ({\n            token,\n            logprob,\n          }))\n        : [],\n    })) ?? undefined\n  );\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\n\nexport function mapOpenAIFinishReason(\n  finishReason: string | null | undefined,\n): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case 'stop':\n      return 'stop';\n    case 'length':\n      return 'length';\n    case 'content_filter':\n      return 'content-filter';\n    case 'function_call':\n    case 'tool_calls':\n      return 'tool-calls';\n    default:\n      return 'unknown';\n  }\n}\n", "import { z } from 'zod';\nimport { createJsonErrorResponseHandler } from '@ai-sdk/provider-utils';\n\nexport const openaiErrorDataSchema = z.object({\n  error: z.object({\n    message: z.string(),\n\n    // The additional information below is handled loosely to support\n    // OpenAI-compatible providers that have slightly different error\n    // responses:\n    type: z.string().nullish(),\n    param: z.any().nullish(),\n    code: z.union([z.string(), z.number()]).nullish(),\n  }),\n});\n\nexport type OpenAIErrorData = z.infer<typeof openaiErrorDataSchema>;\n\nexport const openaiFailedResponseHandler = createJsonErrorResponseHandler({\n  errorSchema: openaiErrorDataSchema,\n  errorToMessage: data => data.error.message,\n});\n", "export function getResponseMetadata({\n  id,\n  model,\n  created,\n}: {\n  id?: string | undefined | null;\n  created?: number | undefined | null;\n  model?: string | undefined | null;\n}) {\n  return {\n    id: id ?? undefined,\n    modelId: model ?? undefined,\n    timestamp: created != null ? new Date(created * 1000) : undefined,\n  };\n}\n", "import {\n  JSONSchema7,\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\n\nexport function prepareTools({\n  mode,\n  useLegacyFunctionCalling = false,\n  structuredOutputs,\n}: {\n  mode: Parameters<LanguageModelV1['doGenerate']>[0]['mode'] & {\n    type: 'regular';\n  };\n  useLegacyFunctionCalling: boolean | undefined;\n  structuredOutputs: boolean;\n}): {\n  tools?: {\n    type: 'function';\n    function: {\n      name: string;\n      description: string | undefined;\n      parameters: JSONSchema7;\n      strict?: boolean;\n    };\n  }[];\n  tool_choice?:\n    | 'auto'\n    | 'none'\n    | 'required'\n    | { type: 'function'; function: { name: string } };\n\n  // legacy support\n  functions?: {\n    name: string;\n    description: string | undefined;\n    parameters: JSONSchema7;\n  }[];\n  function_call?: { name: string };\n  toolWarnings: Array<LanguageModelV1CallWarning>;\n} {\n  // when the tools array is empty, change it to undefined to prevent errors:\n  const tools = mode.tools?.length ? mode.tools : undefined;\n\n  const toolWarnings: LanguageModelV1CallWarning[] = [];\n\n  if (tools == null) {\n    return { tools: undefined, tool_choice: undefined, toolWarnings };\n  }\n\n  const toolChoice = mode.toolChoice;\n\n  if (useLegacyFunctionCalling) {\n    const openaiFunctions: Array<{\n      name: string;\n      description: string | undefined;\n      parameters: JSONSchema7;\n    }> = [];\n\n    for (const tool of tools) {\n      if (tool.type === 'provider-defined') {\n        toolWarnings.push({ type: 'unsupported-tool', tool });\n      } else {\n        openaiFunctions.push({\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters,\n        });\n      }\n    }\n\n    if (toolChoice == null) {\n      return {\n        functions: openaiFunctions,\n        function_call: undefined,\n        toolWarnings,\n      };\n    }\n\n    const type = toolChoice.type;\n\n    switch (type) {\n      case 'auto':\n      case 'none':\n      case undefined:\n        return {\n          functions: openaiFunctions,\n          function_call: undefined,\n          toolWarnings,\n        };\n      case 'required':\n        throw new UnsupportedFunctionalityError({\n          functionality: 'useLegacyFunctionCalling and toolChoice: required',\n        });\n      default:\n        return {\n          functions: openaiFunctions,\n          function_call: { name: toolChoice.toolName },\n          toolWarnings,\n        };\n    }\n  }\n\n  const openaiTools: Array<{\n    type: 'function';\n    function: {\n      name: string;\n      description: string | undefined;\n      parameters: JSONSchema7;\n      strict: boolean | undefined;\n    };\n  }> = [];\n\n  for (const tool of tools) {\n    if (tool.type === 'provider-defined') {\n      toolWarnings.push({ type: 'unsupported-tool', tool });\n    } else {\n      openaiTools.push({\n        type: 'function',\n        function: {\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters,\n          strict: structuredOutputs ? true : undefined,\n        },\n      });\n    }\n  }\n\n  if (toolChoice == null) {\n    return { tools: openaiTools, tool_choice: undefined, toolWarnings };\n  }\n\n  const type = toolChoice.type;\n\n  switch (type) {\n    case 'auto':\n    case 'none':\n    case 'required':\n      return { tools: openaiTools, tool_choice: type, toolWarnings };\n    case 'tool':\n      return {\n        tools: openaiTools,\n        tool_choice: {\n          type: 'function',\n          function: {\n            name: toolChoice.toolName,\n          },\n        },\n        toolWarnings,\n      };\n    default: {\n      const _exhaustiveCheck: never = type;\n      throw new UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`,\n      });\n    }\n  }\n}\n", "import {\n  LanguageModelV1,\n  LanguageModelV1Call<PERSON>arning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1LogProbs,\n  LanguageModelV1StreamPart,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  ParseResult,\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { convertToOpenAICompletionPrompt } from './convert-to-openai-completion-prompt';\nimport { mapOpenAICompletionLogProbs } from './map-openai-completion-logprobs';\nimport { mapOpenAIFinishReason } from './map-openai-finish-reason';\nimport {\n  OpenAICompletionModelId,\n  OpenAICompletionSettings,\n} from './openai-completion-settings';\nimport {\n  openaiErrorDataSchema,\n  openaiFailedResponseHandler,\n} from './openai-error';\nimport { getResponseMetadata } from './get-response-metadata';\n\ntype OpenAICompletionConfig = {\n  provider: string;\n  compatibility: 'strict' | 'compatible';\n  headers: () => Record<string, string | undefined>;\n  url: (options: { modelId: string; path: string }) => string;\n  fetch?: FetchFunction;\n};\n\nexport class OpenAICompletionLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n  readonly defaultObjectGenerationMode = undefined;\n\n  readonly modelId: OpenAICompletionModelId;\n  readonly settings: OpenAICompletionSettings;\n\n  private readonly config: OpenAICompletionConfig;\n\n  constructor(\n    modelId: OpenAICompletionModelId,\n    settings: OpenAICompletionSettings,\n    config: OpenAICompletionConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  private getArgs({\n    mode,\n    inputFormat,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences: userStopSequences,\n    responseFormat,\n    seed,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]) {\n    const type = mode.type;\n\n    const warnings: LanguageModelV1CallWarning[] = [];\n\n    if (topK != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'topK',\n      });\n    }\n\n    if (responseFormat != null && responseFormat.type !== 'text') {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'responseFormat',\n        details: 'JSON response format is not supported.',\n      });\n    }\n\n    const { prompt: completionPrompt, stopSequences } =\n      convertToOpenAICompletionPrompt({ prompt, inputFormat });\n\n    const stop = [...(stopSequences ?? []), ...(userStopSequences ?? [])];\n\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n\n      // model specific settings:\n      echo: this.settings.echo,\n      logit_bias: this.settings.logitBias,\n      logprobs:\n        typeof this.settings.logprobs === 'number'\n          ? this.settings.logprobs\n          : typeof this.settings.logprobs === 'boolean'\n            ? this.settings.logprobs\n              ? 0\n              : undefined\n            : undefined,\n      suffix: this.settings.suffix,\n      user: this.settings.user,\n\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      seed,\n\n      // prompt:\n      prompt: completionPrompt,\n\n      // stop sequences:\n      stop: stop.length > 0 ? stop : undefined,\n    };\n\n    switch (type) {\n      case 'regular': {\n        if (mode.tools?.length) {\n          throw new UnsupportedFunctionalityError({\n            functionality: 'tools',\n          });\n        }\n\n        if (mode.toolChoice) {\n          throw new UnsupportedFunctionalityError({\n            functionality: 'toolChoice',\n          });\n        }\n\n        return { args: baseArgs, warnings };\n      }\n\n      case 'object-json': {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'object-json mode',\n        });\n      }\n\n      case 'object-tool': {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'object-tool mode',\n        });\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { args, warnings } = this.getArgs(options);\n\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse,\n    } = await postJsonToApi({\n      url: this.config.url({\n        path: '/completions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        openaiCompletionResponseSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { prompt: rawPrompt, ...rawSettings } = args;\n    const choice = response.choices[0];\n\n    return {\n      text: choice.text,\n      usage: {\n        promptTokens: response.usage.prompt_tokens,\n        completionTokens: response.usage.completion_tokens,\n      },\n      finishReason: mapOpenAIFinishReason(choice.finish_reason),\n      logprobs: mapOpenAICompletionLogProbs(choice.logprobs),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders, body: rawResponse },\n      response: getResponseMetadata(response),\n      warnings,\n      request: { body: JSON.stringify(args) },\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    const { args, warnings } = this.getArgs(options);\n\n    const body = {\n      ...args,\n      stream: true,\n\n      // only include stream_options when in strict compatibility mode:\n      stream_options:\n        this.config.compatibility === 'strict'\n          ? { include_usage: true }\n          : undefined,\n    };\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: '/completions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        openaiCompletionChunkSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { prompt: rawPrompt, ...rawSettings } = args;\n\n    let finishReason: LanguageModelV1FinishReason = 'unknown';\n    let usage: { promptTokens: number; completionTokens: number } = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN,\n    };\n    let logprobs: LanguageModelV1LogProbs;\n    let isFirstChunk = true;\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof openaiCompletionChunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            // handle failed chunk parsing / validation:\n            if (!chunk.success) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            // handle error chunks:\n            if ('error' in value) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: value.error });\n              return;\n            }\n\n            if (isFirstChunk) {\n              isFirstChunk = false;\n\n              controller.enqueue({\n                type: 'response-metadata',\n                ...getResponseMetadata(value),\n              });\n            }\n\n            if (value.usage != null) {\n              usage = {\n                promptTokens: value.usage.prompt_tokens,\n                completionTokens: value.usage.completion_tokens,\n              };\n            }\n\n            const choice = value.choices[0];\n\n            if (choice?.finish_reason != null) {\n              finishReason = mapOpenAIFinishReason(choice.finish_reason);\n            }\n\n            if (choice?.text != null) {\n              controller.enqueue({\n                type: 'text-delta',\n                textDelta: choice.text,\n              });\n            }\n\n            const mappedLogprobs = mapOpenAICompletionLogProbs(\n              choice?.logprobs,\n            );\n            if (mappedLogprobs?.length) {\n              if (logprobs === undefined) logprobs = [];\n              logprobs.push(...mappedLogprobs);\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({\n              type: 'finish',\n              finishReason,\n              logprobs,\n              usage,\n            });\n          },\n        }),\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n      request: { body: JSON.stringify(body) },\n    };\n  }\n}\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openaiCompletionResponseSchema = z.object({\n  id: z.string().nullish(),\n  created: z.number().nullish(),\n  model: z.string().nullish(),\n  choices: z.array(\n    z.object({\n      text: z.string(),\n      finish_reason: z.string(),\n      logprobs: z\n        .object({\n          tokens: z.array(z.string()),\n          token_logprobs: z.array(z.number()),\n          top_logprobs: z.array(z.record(z.string(), z.number())).nullable(),\n        })\n        .nullish(),\n    }),\n  ),\n  usage: z.object({\n    prompt_tokens: z.number(),\n    completion_tokens: z.number(),\n  }),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openaiCompletionChunkSchema = z.union([\n  z.object({\n    id: z.string().nullish(),\n    created: z.number().nullish(),\n    model: z.string().nullish(),\n    choices: z.array(\n      z.object({\n        text: z.string(),\n        finish_reason: z.string().nullish(),\n        index: z.number(),\n        logprobs: z\n          .object({\n            tokens: z.array(z.string()),\n            token_logprobs: z.array(z.number()),\n            top_logprobs: z.array(z.record(z.string(), z.number())).nullable(),\n          })\n          .nullish(),\n      }),\n    ),\n    usage: z\n      .object({\n        prompt_tokens: z.number(),\n        completion_tokens: z.number(),\n      })\n      .nullish(),\n  }),\n  openaiErrorDataSchema,\n]);\n", "import {\n  InvalidPromptError,\n  LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\n\nexport function convertToOpenAICompletionPrompt({\n  prompt,\n  inputFormat,\n  user = 'user',\n  assistant = 'assistant',\n}: {\n  prompt: LanguageModelV1Prompt;\n  inputFormat: 'prompt' | 'messages';\n  user?: string;\n  assistant?: string;\n}): {\n  prompt: string;\n  stopSequences?: string[];\n} {\n  // When the user supplied a prompt input, we don't transform it:\n  if (\n    inputFormat === 'prompt' &&\n    prompt.length === 1 &&\n    prompt[0].role === 'user' &&\n    prompt[0].content.length === 1 &&\n    prompt[0].content[0].type === 'text'\n  ) {\n    return { prompt: prompt[0].content[0].text };\n  }\n\n  // otherwise transform to a chat message format:\n  let text = '';\n\n  // if first message is a system message, add it to the text:\n  if (prompt[0].role === 'system') {\n    text += `${prompt[0].content}\\n\\n`;\n    prompt = prompt.slice(1);\n  }\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case 'system': {\n        throw new InvalidPromptError({\n          message: 'Unexpected system message in prompt: ${content}',\n          prompt,\n        });\n      }\n\n      case 'user': {\n        const userMessage = content\n          .map(part => {\n            switch (part.type) {\n              case 'text': {\n                return part.text;\n              }\n              case 'image': {\n                throw new UnsupportedFunctionalityError({\n                  functionality: 'images',\n                });\n              }\n            }\n          })\n          .join('');\n\n        text += `${user}:\\n${userMessage}\\n\\n`;\n        break;\n      }\n\n      case 'assistant': {\n        const assistantMessage = content\n          .map(part => {\n            switch (part.type) {\n              case 'text': {\n                return part.text;\n              }\n              case 'tool-call': {\n                throw new UnsupportedFunctionalityError({\n                  functionality: 'tool-call messages',\n                });\n              }\n            }\n          })\n          .join('');\n\n        text += `${assistant}:\\n${assistantMessage}\\n\\n`;\n        break;\n      }\n\n      case 'tool': {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'tool messages',\n        });\n      }\n\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  // Assistant message prefix:\n  text += `${assistant}:\\n`;\n\n  return {\n    prompt: text,\n    stopSequences: [`\\n${user}:`],\n  };\n}\n", "import { LanguageModelV1LogProbs } from '@ai-sdk/provider';\n\ntype OpenAICompletionLogProps = {\n  tokens: string[];\n  token_logprobs: number[];\n  top_logprobs: Record<string, number>[] | null;\n};\n\nexport function mapOpenAICompletionLogProbs(\n  logprobs: OpenAICompletionLogProps | null | undefined,\n): LanguageModelV1LogProbs | undefined {\n  return logprobs?.tokens.map((token, index) => ({\n    token,\n    logprob: logprobs.token_logprobs[index],\n    topLogprobs: logprobs.top_logprobs\n      ? Object.entries(logprobs.top_logprobs[index]).map(\n          ([token, logprob]) => ({\n            token,\n            logprob,\n          }),\n        )\n      : [],\n  }));\n}\n", "import {\n  EmbeddingModelV1,\n  TooManyEmbeddingValuesForCallError,\n} from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createJsonResponseHandler,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { OpenAIConfig } from './openai-config';\nimport {\n  OpenAIEmbeddingModelId,\n  OpenAIEmbeddingSettings,\n} from './openai-embedding-settings';\nimport { openaiFailedResponseHandler } from './openai-error';\n\nexport class OpenAIEmbeddingModel implements EmbeddingModelV1<string> {\n  readonly specificationVersion = 'v1';\n  readonly modelId: OpenAIEmbeddingModelId;\n\n  private readonly config: OpenAIConfig;\n  private readonly settings: OpenAIEmbeddingSettings;\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  get maxEmbeddingsPerCall(): number {\n    return this.settings.maxEmbeddingsPerCall ?? 2048;\n  }\n\n  get supportsParallelCalls(): boolean {\n    return this.settings.supportsParallelCalls ?? true;\n  }\n\n  constructor(\n    modelId: OpenAIEmbeddingModelId,\n    settings: OpenAIEmbeddingSettings,\n    config: OpenAIConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  async doEmbed({\n    values,\n    headers,\n    abortSignal,\n  }: Parameters<EmbeddingModelV1<string>['doEmbed']>[0]): Promise<\n    Awaited<ReturnType<EmbeddingModelV1<string>['doEmbed']>>\n  > {\n    if (values.length > this.maxEmbeddingsPerCall) {\n      throw new TooManyEmbeddingValuesForCallError({\n        provider: this.provider,\n        modelId: this.modelId,\n        maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n        values,\n      });\n    }\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: '/embeddings',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), headers),\n      body: {\n        model: this.modelId,\n        input: values,\n        encoding_format: 'float',\n        dimensions: this.settings.dimensions,\n        user: this.settings.user,\n      },\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        openaiTextEmbeddingResponseSchema,\n      ),\n      abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    return {\n      embeddings: response.data.map(item => item.embedding),\n      usage: response.usage\n        ? { tokens: response.usage.prompt_tokens }\n        : undefined,\n      rawResponse: { headers: responseHeaders },\n    };\n  }\n}\n\n// minimal version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openaiTextEmbeddingResponseSchema = z.object({\n  data: z.array(z.object({ embedding: z.array(z.number()) })),\n  usage: z.object({ prompt_tokens: z.number() }).nullish(),\n});\n", "import { ImageModelV1, ImageModelV1CallWarning } from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createJsonResponseHandler,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { OpenAIConfig } from './openai-config';\nimport { openaiFailedResponseHandler } from './openai-error';\nimport {\n  OpenAIImageModelId,\n  OpenAIImageSettings,\n  modelMaxImagesPerCall,\n  hasDefaultResponseFormat,\n} from './openai-image-settings';\n\ninterface OpenAIImageModelConfig extends OpenAIConfig {\n  _internal?: {\n    currentDate?: () => Date;\n  };\n}\n\nexport class OpenAIImageModel implements ImageModelV1 {\n  readonly specificationVersion = 'v1';\n\n  get maxImagesPerCall(): number {\n    return (\n      this.settings.maxImagesPerCall ?? modelMaxImagesPerCall[this.modelId] ?? 1\n    );\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  constructor(\n    readonly modelId: OpenAIImageModelId,\n    private readonly settings: OpenAIImageSettings,\n    private readonly config: OpenAIImageModelConfig,\n  ) {}\n\n  async doGenerate({\n    prompt,\n    n,\n    size,\n    aspectRatio,\n    seed,\n    providerOptions,\n    headers,\n    abortSignal,\n  }: Parameters<ImageModelV1['doGenerate']>[0]): Promise<\n    Awaited<ReturnType<ImageModelV1['doGenerate']>>\n  > {\n    const warnings: Array<ImageModelV1CallWarning> = [];\n\n    if (aspectRatio != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'aspectRatio',\n        details:\n          'This model does not support aspect ratio. Use `size` instead.',\n      });\n    }\n\n    if (seed != null) {\n      warnings.push({ type: 'unsupported-setting', setting: 'seed' });\n    }\n\n    const currentDate = this.config._internal?.currentDate?.() ?? new Date();\n    const { value: response, responseHeaders } = await postJsonToApi({\n      url: this.config.url({\n        path: '/images/generations',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), headers),\n      body: {\n        model: this.modelId,\n        prompt,\n        n,\n        size,\n        ...(providerOptions.openai ?? {}),\n        ...(!hasDefaultResponseFormat.has(this.modelId)\n          ? { response_format: 'b64_json' }\n          : {}),\n      },\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        openaiImageResponseSchema,\n      ),\n      abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    return {\n      images: response.data.map(item => item.b64_json),\n      warnings,\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders,\n      },\n    };\n  }\n}\n\n// minimal version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openaiImageResponseSchema = z.object({\n  data: z.array(z.object({ b64_json: z.string() })),\n});\n", "export type OpenAIImageModelId =\n  | 'gpt-image-1'\n  | 'dall-e-3'\n  | 'dall-e-2'\n  | (string & {});\n\n// https://platform.openai.com/docs/guides/images\nexport const modelMaxImagesPerCall: Record<OpenAIImageModelId, number> = {\n  'dall-e-3': 1,\n  'dall-e-2': 10,\n  'gpt-image-1': 10,\n};\n\nexport const hasDefaultResponseFormat = new Set(['gpt-image-1']);\n\nexport interface OpenAIImageSettings {\n  /**\nOverride the maximum number of images per call (default is dependent on the\nmodel, or 1 for an unknown model).\n   */\n  maxImagesPerCall?: number;\n}\n", "import {\n  TranscriptionModelV1,\n  TranscriptionModelV1CallOptions,\n  TranscriptionModelV1CallWarning,\n} from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  convertBase64ToUint8Array,\n  createJsonResponseHandler,\n  parseProviderOptions,\n  postFormDataToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { OpenAIConfig } from './openai-config';\nimport { openaiFailedResponseHandler } from './openai-error';\nimport {\n  OpenAITranscriptionModelId,\n  OpenAITranscriptionModelOptions,\n} from './openai-transcription-settings';\n\n// https://platform.openai.com/docs/api-reference/audio/createTranscription\nconst openAIProviderOptionsSchema = z.object({\n  include: z.array(z.string()).nullish(),\n  language: z.string().nullish(),\n  prompt: z.string().nullish(),\n  temperature: z.number().min(0).max(1).nullish().default(0),\n  timestampGranularities: z\n    .array(z.enum(['word', 'segment']))\n    .nullish()\n    .default(['segment']),\n});\n\nexport type OpenAITranscriptionCallOptions = Omit<\n  TranscriptionModelV1CallOptions,\n  'providerOptions'\n> & {\n  providerOptions?: {\n    openai?: z.infer<typeof openAIProviderOptionsSchema>;\n  };\n};\n\ninterface OpenAITranscriptionModelConfig extends OpenAIConfig {\n  _internal?: {\n    currentDate?: () => Date;\n  };\n}\n\n// https://platform.openai.com/docs/guides/speech-to-text#supported-languages\nconst languageMap = {\n  afrikaans: 'af',\n  arabic: 'ar',\n  armenian: 'hy',\n  azerbaijani: 'az',\n  belarusian: 'be',\n  bosnian: 'bs',\n  bulgarian: 'bg',\n  catalan: 'ca',\n  chinese: 'zh',\n  croatian: 'hr',\n  czech: 'cs',\n  danish: 'da',\n  dutch: 'nl',\n  english: 'en',\n  estonian: 'et',\n  finnish: 'fi',\n  french: 'fr',\n  galician: 'gl',\n  german: 'de',\n  greek: 'el',\n  hebrew: 'he',\n  hindi: 'hi',\n  hungarian: 'hu',\n  icelandic: 'is',\n  indonesian: 'id',\n  italian: 'it',\n  japanese: 'ja',\n  kannada: 'kn',\n  kazakh: 'kk',\n  korean: 'ko',\n  latvian: 'lv',\n  lithuanian: 'lt',\n  macedonian: 'mk',\n  malay: 'ms',\n  marathi: 'mr',\n  maori: 'mi',\n  nepali: 'ne',\n  norwegian: 'no',\n  persian: 'fa',\n  polish: 'pl',\n  portuguese: 'pt',\n  romanian: 'ro',\n  russian: 'ru',\n  serbian: 'sr',\n  slovak: 'sk',\n  slovenian: 'sl',\n  spanish: 'es',\n  swahili: 'sw',\n  swedish: 'sv',\n  tagalog: 'tl',\n  tamil: 'ta',\n  thai: 'th',\n  turkish: 'tr',\n  ukrainian: 'uk',\n  urdu: 'ur',\n  vietnamese: 'vi',\n  welsh: 'cy',\n};\n\nexport class OpenAITranscriptionModel implements TranscriptionModelV1 {\n  readonly specificationVersion = 'v1';\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  constructor(\n    readonly modelId: OpenAITranscriptionModelId,\n    private readonly config: OpenAITranscriptionModelConfig,\n  ) {}\n\n  private getArgs({\n    audio,\n    mediaType,\n    providerOptions,\n  }: OpenAITranscriptionCallOptions) {\n    const warnings: TranscriptionModelV1CallWarning[] = [];\n\n    // Parse provider options\n    const openAIOptions = parseProviderOptions({\n      provider: 'openai',\n      providerOptions,\n      schema: openAIProviderOptionsSchema,\n    });\n\n    // Create form data with base fields\n    const formData = new FormData();\n    const blob =\n      audio instanceof Uint8Array\n        ? new Blob([audio])\n        : new Blob([convertBase64ToUint8Array(audio)]);\n\n    formData.append('model', this.modelId);\n    formData.append('file', new File([blob], 'audio', { type: mediaType }));\n\n    // Add provider-specific options\n    if (openAIOptions) {\n      const transcriptionModelOptions: OpenAITranscriptionModelOptions = {\n        include: openAIOptions.include ?? undefined,\n        language: openAIOptions.language ?? undefined,\n        prompt: openAIOptions.prompt ?? undefined,\n        temperature: openAIOptions.temperature ?? undefined,\n        timestamp_granularities:\n          openAIOptions.timestampGranularities ?? undefined,\n      };\n\n      for (const key in transcriptionModelOptions) {\n        const value =\n          transcriptionModelOptions[\n            key as keyof OpenAITranscriptionModelOptions\n          ];\n        if (value !== undefined) {\n          formData.append(key, String(value));\n        }\n      }\n    }\n\n    return {\n      formData,\n      warnings,\n    };\n  }\n\n  async doGenerate(\n    options: OpenAITranscriptionCallOptions,\n  ): Promise<Awaited<ReturnType<TranscriptionModelV1['doGenerate']>>> {\n    const currentDate = this.config._internal?.currentDate?.() ?? new Date();\n    const { formData, warnings } = this.getArgs(options);\n\n    const {\n      value: response,\n      responseHeaders,\n      rawValue: rawResponse,\n    } = await postFormDataToApi({\n      url: this.config.url({\n        path: '/audio/transcriptions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      formData,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        openaiTranscriptionResponseSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const language =\n      response.language != null && response.language in languageMap\n        ? languageMap[response.language as keyof typeof languageMap]\n        : undefined;\n\n    return {\n      text: response.text,\n      segments:\n        response.words?.map(word => ({\n          text: word.word,\n          startSecond: word.start,\n          endSecond: word.end,\n        })) ?? [],\n      language,\n      durationInSeconds: response.duration ?? undefined,\n      warnings,\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders,\n        body: rawResponse,\n      },\n    };\n  }\n}\n\nconst openaiTranscriptionResponseSchema = z.object({\n  text: z.string(),\n  language: z.string().nullish(),\n  duration: z.number().nullish(),\n  words: z\n    .array(\n      z.object({\n        word: z.string(),\n        start: z.number(),\n        end: z.number(),\n      }),\n    )\n    .nullish(),\n});\n", "import {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1StreamPart,\n} from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  generateId,\n  parseProviderOptions,\n  ParseResult,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { OpenAIConfig } from '../openai-config';\nimport { openaiFailedResponseHandler } from '../openai-error';\nimport { convertToOpenAIResponsesMessages } from './convert-to-openai-responses-messages';\nimport { mapOpenAIResponseFinishReason } from './map-openai-responses-finish-reason';\nimport { prepareResponsesTools } from './openai-responses-prepare-tools';\nimport { OpenAIResponsesModelId } from './openai-responses-settings';\n\nexport class OpenAIResponsesLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n  readonly defaultObjectGenerationMode = 'json';\n  readonly supportsStructuredOutputs = true;\n\n  readonly modelId: OpenAIResponsesModelId;\n\n  private readonly config: OpenAIConfig;\n\n  constructor(modelId: OpenAIResponsesModelId, config: OpenAIConfig) {\n    this.modelId = modelId;\n    this.config = config;\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  private getArgs({\n    mode,\n    maxTokens,\n    temperature,\n    stopSequences,\n    topP,\n    topK,\n    presencePenalty,\n    frequencyPenalty,\n    seed,\n    prompt,\n    providerMetadata,\n    responseFormat,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]) {\n    const warnings: LanguageModelV1CallWarning[] = [];\n    const modelConfig = getResponsesModelConfig(this.modelId);\n    const type = mode.type;\n\n    if (topK != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'topK',\n      });\n    }\n\n    if (seed != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'seed',\n      });\n    }\n\n    if (presencePenalty != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'presencePenalty',\n      });\n    }\n\n    if (frequencyPenalty != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'frequencyPenalty',\n      });\n    }\n\n    if (stopSequences != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'stopSequences',\n      });\n    }\n\n    const { messages, warnings: messageWarnings } =\n      convertToOpenAIResponsesMessages({\n        prompt,\n        systemMessageMode: modelConfig.systemMessageMode,\n      });\n\n    warnings.push(...messageWarnings);\n\n    const openaiOptions = parseProviderOptions({\n      provider: 'openai',\n      providerOptions: providerMetadata,\n      schema: openaiResponsesProviderOptionsSchema,\n    });\n\n    const isStrict = openaiOptions?.strictSchemas ?? true;\n\n    const baseArgs = {\n      model: this.modelId,\n      input: messages,\n      temperature,\n      top_p: topP,\n      max_output_tokens: maxTokens,\n\n      ...(responseFormat?.type === 'json' && {\n        text: {\n          format:\n            responseFormat.schema != null\n              ? {\n                  type: 'json_schema',\n                  strict: isStrict,\n                  name: responseFormat.name ?? 'response',\n                  description: responseFormat.description,\n                  schema: responseFormat.schema,\n                }\n              : { type: 'json_object' },\n        },\n      }),\n\n      // provider options:\n      metadata: openaiOptions?.metadata,\n      parallel_tool_calls: openaiOptions?.parallelToolCalls,\n      previous_response_id: openaiOptions?.previousResponseId,\n      store: openaiOptions?.store,\n      user: openaiOptions?.user,\n      instructions: openaiOptions?.instructions,\n\n      // model-specific settings:\n      ...(modelConfig.isReasoningModel &&\n        (openaiOptions?.reasoningEffort != null ||\n          openaiOptions?.reasoningSummary != null) && {\n          reasoning: {\n            ...(openaiOptions?.reasoningEffort != null && {\n              effort: openaiOptions.reasoningEffort,\n            }),\n            ...(openaiOptions?.reasoningSummary != null && {\n              summary: openaiOptions.reasoningSummary,\n            }),\n          },\n        }),\n      ...(modelConfig.requiredAutoTruncation && {\n        truncation: 'auto',\n      }),\n    };\n\n    if (modelConfig.isReasoningModel) {\n      // remove unsupported settings for reasoning models\n      // see https://platform.openai.com/docs/guides/reasoning#limitations\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'temperature',\n          details: 'temperature is not supported for reasoning models',\n        });\n      }\n\n      if (baseArgs.top_p != null) {\n        baseArgs.top_p = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'topP',\n          details: 'topP is not supported for reasoning models',\n        });\n      }\n    }\n\n    switch (type) {\n      case 'regular': {\n        const { tools, tool_choice, toolWarnings } = prepareResponsesTools({\n          mode,\n          strict: isStrict, // TODO support provider options on tools\n        });\n\n        return {\n          args: {\n            ...baseArgs,\n            tools,\n            tool_choice,\n          },\n          warnings: [...warnings, ...toolWarnings],\n        };\n      }\n\n      case 'object-json': {\n        return {\n          args: {\n            ...baseArgs,\n            text: {\n              format:\n                mode.schema != null\n                  ? {\n                      type: 'json_schema',\n                      strict: isStrict,\n                      name: mode.name ?? 'response',\n                      description: mode.description,\n                      schema: mode.schema,\n                    }\n                  : { type: 'json_object' },\n            },\n          },\n          warnings,\n        };\n      }\n\n      case 'object-tool': {\n        return {\n          args: {\n            ...baseArgs,\n            tool_choice: { type: 'function', name: mode.tool.name },\n            tools: [\n              {\n                type: 'function',\n                name: mode.tool.name,\n                description: mode.tool.description,\n                parameters: mode.tool.parameters,\n                strict: isStrict,\n              },\n            ],\n          },\n          warnings,\n        };\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { args: body, warnings } = this.getArgs(options);\n\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse,\n    } = await postJsonToApi({\n      url: this.config.url({\n        path: '/responses',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        z.object({\n          id: z.string(),\n          created_at: z.number(),\n          model: z.string(),\n          output: z.array(\n            z.discriminatedUnion('type', [\n              z.object({\n                type: z.literal('message'),\n                role: z.literal('assistant'),\n                content: z.array(\n                  z.object({\n                    type: z.literal('output_text'),\n                    text: z.string(),\n                    annotations: z.array(\n                      z.object({\n                        type: z.literal('url_citation'),\n                        start_index: z.number(),\n                        end_index: z.number(),\n                        url: z.string(),\n                        title: z.string(),\n                      }),\n                    ),\n                  }),\n                ),\n              }),\n              z.object({\n                type: z.literal('function_call'),\n                call_id: z.string(),\n                name: z.string(),\n                arguments: z.string(),\n              }),\n              z.object({\n                type: z.literal('web_search_call'),\n              }),\n              z.object({\n                type: z.literal('computer_call'),\n              }),\n              z.object({\n                type: z.literal('reasoning'),\n                summary: z.array(\n                  z.object({\n                    type: z.literal('summary_text'),\n                    text: z.string(),\n                  }),\n                ),\n              }),\n            ]),\n          ),\n          incomplete_details: z.object({ reason: z.string() }).nullable(),\n          usage: usageSchema,\n        }),\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const outputTextElements = response.output\n      .filter(output => output.type === 'message')\n      .flatMap(output => output.content)\n      .filter(content => content.type === 'output_text');\n\n    const toolCalls = response.output\n      .filter(output => output.type === 'function_call')\n      .map(output => ({\n        toolCallType: 'function' as const,\n        toolCallId: output.call_id,\n        toolName: output.name,\n        args: output.arguments,\n      }));\n\n    const reasoningSummary =\n      response.output.find(item => item.type === 'reasoning')?.summary ?? null;\n\n    return {\n      text: outputTextElements.map(content => content.text).join('\\n'),\n      sources: outputTextElements.flatMap(content =>\n        content.annotations.map(annotation => ({\n          sourceType: 'url',\n          id: this.config.generateId?.() ?? generateId(),\n          url: annotation.url,\n          title: annotation.title,\n        })),\n      ),\n      finishReason: mapOpenAIResponseFinishReason({\n        finishReason: response.incomplete_details?.reason,\n        hasToolCalls: toolCalls.length > 0,\n      }),\n      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,\n      reasoning: reasoningSummary\n        ? reasoningSummary.map(summary => ({\n            type: 'text' as const,\n            text: summary.text,\n          }))\n        : undefined,\n      usage: {\n        promptTokens: response.usage.input_tokens,\n        completionTokens: response.usage.output_tokens,\n      },\n      rawCall: {\n        rawPrompt: undefined,\n        rawSettings: {},\n      },\n      rawResponse: {\n        headers: responseHeaders,\n        body: rawResponse,\n      },\n      request: {\n        body: JSON.stringify(body),\n      },\n      response: {\n        id: response.id,\n        timestamp: new Date(response.created_at * 1000),\n        modelId: response.model,\n      },\n      providerMetadata: {\n        openai: {\n          responseId: response.id,\n          cachedPromptTokens:\n            response.usage.input_tokens_details?.cached_tokens ?? null,\n          reasoningTokens:\n            response.usage.output_tokens_details?.reasoning_tokens ?? null,\n        },\n      },\n      warnings,\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    const { args: body, warnings } = this.getArgs(options);\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: '/responses',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: {\n        ...body,\n        stream: true,\n      },\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        openaiResponsesChunkSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const self = this;\n\n    let finishReason: LanguageModelV1FinishReason = 'unknown';\n    let promptTokens = NaN;\n    let completionTokens = NaN;\n    let cachedPromptTokens: number | null = null;\n    let reasoningTokens: number | null = null;\n    let responseId: string | null = null;\n    const ongoingToolCalls: Record<\n      number,\n      { toolName: string; toolCallId: string } | undefined\n    > = {};\n    let hasToolCalls = false;\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof openaiResponsesChunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            // handle failed chunk parsing / validation:\n            if (!chunk.success) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            if (isResponseOutputItemAddedChunk(value)) {\n              if (value.item.type === 'function_call') {\n                ongoingToolCalls[value.output_index] = {\n                  toolName: value.item.name,\n                  toolCallId: value.item.call_id,\n                };\n\n                controller.enqueue({\n                  type: 'tool-call-delta',\n                  toolCallType: 'function',\n                  toolCallId: value.item.call_id,\n                  toolName: value.item.name,\n                  argsTextDelta: value.item.arguments,\n                });\n              }\n            } else if (isResponseFunctionCallArgumentsDeltaChunk(value)) {\n              const toolCall = ongoingToolCalls[value.output_index];\n\n              if (toolCall != null) {\n                controller.enqueue({\n                  type: 'tool-call-delta',\n                  toolCallType: 'function',\n                  toolCallId: toolCall.toolCallId,\n                  toolName: toolCall.toolName,\n                  argsTextDelta: value.delta,\n                });\n              }\n            } else if (isResponseCreatedChunk(value)) {\n              responseId = value.response.id;\n              controller.enqueue({\n                type: 'response-metadata',\n                id: value.response.id,\n                timestamp: new Date(value.response.created_at * 1000),\n                modelId: value.response.model,\n              });\n            } else if (isTextDeltaChunk(value)) {\n              controller.enqueue({\n                type: 'text-delta',\n                textDelta: value.delta,\n              });\n            } else if (isResponseReasoningSummaryTextDeltaChunk(value)) {\n              controller.enqueue({\n                type: 'reasoning',\n                textDelta: value.delta,\n              });\n            } else if (\n              isResponseOutputItemDoneChunk(value) &&\n              value.item.type === 'function_call'\n            ) {\n              ongoingToolCalls[value.output_index] = undefined;\n              hasToolCalls = true;\n              controller.enqueue({\n                type: 'tool-call',\n                toolCallType: 'function',\n                toolCallId: value.item.call_id,\n                toolName: value.item.name,\n                args: value.item.arguments,\n              });\n            } else if (isResponseFinishedChunk(value)) {\n              finishReason = mapOpenAIResponseFinishReason({\n                finishReason: value.response.incomplete_details?.reason,\n                hasToolCalls,\n              });\n              promptTokens = value.response.usage.input_tokens;\n              completionTokens = value.response.usage.output_tokens;\n              cachedPromptTokens =\n                value.response.usage.input_tokens_details?.cached_tokens ??\n                cachedPromptTokens;\n              reasoningTokens =\n                value.response.usage.output_tokens_details?.reasoning_tokens ??\n                reasoningTokens;\n            } else if (isResponseAnnotationAddedChunk(value)) {\n              controller.enqueue({\n                type: 'source',\n                source: {\n                  sourceType: 'url',\n                  id: self.config.generateId?.() ?? generateId(),\n                  url: value.annotation.url,\n                  title: value.annotation.title,\n                },\n              });\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({\n              type: 'finish',\n              finishReason,\n              usage: { promptTokens, completionTokens },\n              ...((cachedPromptTokens != null || reasoningTokens != null) && {\n                providerMetadata: {\n                  openai: {\n                    responseId,\n                    cachedPromptTokens,\n                    reasoningTokens,\n                  },\n                },\n              }),\n            });\n          },\n        }),\n      ),\n      rawCall: {\n        rawPrompt: undefined,\n        rawSettings: {},\n      },\n      rawResponse: { headers: responseHeaders },\n      request: { body: JSON.stringify(body) },\n      warnings,\n    };\n  }\n}\n\nconst usageSchema = z.object({\n  input_tokens: z.number(),\n  input_tokens_details: z\n    .object({ cached_tokens: z.number().nullish() })\n    .nullish(),\n  output_tokens: z.number(),\n  output_tokens_details: z\n    .object({ reasoning_tokens: z.number().nullish() })\n    .nullish(),\n});\n\nconst textDeltaChunkSchema = z.object({\n  type: z.literal('response.output_text.delta'),\n  delta: z.string(),\n});\n\nconst responseFinishedChunkSchema = z.object({\n  type: z.enum(['response.completed', 'response.incomplete']),\n  response: z.object({\n    incomplete_details: z.object({ reason: z.string() }).nullish(),\n    usage: usageSchema,\n  }),\n});\n\nconst responseCreatedChunkSchema = z.object({\n  type: z.literal('response.created'),\n  response: z.object({\n    id: z.string(),\n    created_at: z.number(),\n    model: z.string(),\n  }),\n});\n\nconst responseOutputItemDoneSchema = z.object({\n  type: z.literal('response.output_item.done'),\n  output_index: z.number(),\n  item: z.discriminatedUnion('type', [\n    z.object({\n      type: z.literal('message'),\n    }),\n    z.object({\n      type: z.literal('function_call'),\n      id: z.string(),\n      call_id: z.string(),\n      name: z.string(),\n      arguments: z.string(),\n      status: z.literal('completed'),\n    }),\n  ]),\n});\n\nconst responseFunctionCallArgumentsDeltaSchema = z.object({\n  type: z.literal('response.function_call_arguments.delta'),\n  item_id: z.string(),\n  output_index: z.number(),\n  delta: z.string(),\n});\n\nconst responseOutputItemAddedSchema = z.object({\n  type: z.literal('response.output_item.added'),\n  output_index: z.number(),\n  item: z.discriminatedUnion('type', [\n    z.object({\n      type: z.literal('message'),\n    }),\n    z.object({\n      type: z.literal('function_call'),\n      id: z.string(),\n      call_id: z.string(),\n      name: z.string(),\n      arguments: z.string(),\n    }),\n  ]),\n});\n\nconst responseAnnotationAddedSchema = z.object({\n  type: z.literal('response.output_text.annotation.added'),\n  annotation: z.object({\n    type: z.literal('url_citation'),\n    url: z.string(),\n    title: z.string(),\n  }),\n});\n\nconst responseReasoningSummaryTextDeltaSchema = z.object({\n  type: z.literal('response.reasoning_summary_text.delta'),\n  item_id: z.string(),\n  output_index: z.number(),\n  summary_index: z.number(),\n  delta: z.string(),\n});\n\nconst openaiResponsesChunkSchema = z.union([\n  textDeltaChunkSchema,\n  responseFinishedChunkSchema,\n  responseCreatedChunkSchema,\n  responseOutputItemDoneSchema,\n  responseFunctionCallArgumentsDeltaSchema,\n  responseOutputItemAddedSchema,\n  responseAnnotationAddedSchema,\n  responseReasoningSummaryTextDeltaSchema,\n  z.object({ type: z.string() }).passthrough(), // fallback for unknown chunks\n]);\n\nfunction isTextDeltaChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof textDeltaChunkSchema> {\n  return chunk.type === 'response.output_text.delta';\n}\n\nfunction isResponseOutputItemDoneChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof responseOutputItemDoneSchema> {\n  return chunk.type === 'response.output_item.done';\n}\n\nfunction isResponseFinishedChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof responseFinishedChunkSchema> {\n  return (\n    chunk.type === 'response.completed' || chunk.type === 'response.incomplete'\n  );\n}\n\nfunction isResponseCreatedChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof responseCreatedChunkSchema> {\n  return chunk.type === 'response.created';\n}\n\nfunction isResponseFunctionCallArgumentsDeltaChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof responseFunctionCallArgumentsDeltaSchema> {\n  return chunk.type === 'response.function_call_arguments.delta';\n}\n\nfunction isResponseOutputItemAddedChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof responseOutputItemAddedSchema> {\n  return chunk.type === 'response.output_item.added';\n}\n\nfunction isResponseAnnotationAddedChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof responseAnnotationAddedSchema> {\n  return chunk.type === 'response.output_text.annotation.added';\n}\n\nfunction isResponseReasoningSummaryTextDeltaChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof responseReasoningSummaryTextDeltaSchema> {\n  return chunk.type === 'response.reasoning_summary_text.delta';\n}\n\ntype ResponsesModelConfig = {\n  isReasoningModel: boolean;\n  systemMessageMode: 'remove' | 'system' | 'developer';\n  requiredAutoTruncation: boolean;\n};\n\nfunction getResponsesModelConfig(modelId: string): ResponsesModelConfig {\n  // o series reasoning models:\n  if (modelId.startsWith('o')) {\n    if (modelId.startsWith('o1-mini') || modelId.startsWith('o1-preview')) {\n      return {\n        isReasoningModel: true,\n        systemMessageMode: 'remove',\n        requiredAutoTruncation: false,\n      };\n    }\n\n    return {\n      isReasoningModel: true,\n      systemMessageMode: 'developer',\n      requiredAutoTruncation: false,\n    };\n  }\n\n  // gpt models:\n  return {\n    isReasoningModel: false,\n    systemMessageMode: 'system',\n    requiredAutoTruncation: false,\n  };\n}\n\nconst openaiResponsesProviderOptionsSchema = z.object({\n  metadata: z.any().nullish(),\n  parallelToolCalls: z.boolean().nullish(),\n  previousResponseId: z.string().nullish(),\n  store: z.boolean().nullish(),\n  user: z.string().nullish(),\n  reasoningEffort: z.string().nullish(),\n  strictSchemas: z.boolean().nullish(),\n  instructions: z.string().nullish(),\n  reasoningSummary: z.string().nullish(),\n});\n\nexport type OpenAIResponsesProviderOptions = z.infer<\n  typeof openaiResponsesProviderOptionsSchema\n>;\n", "import {\n  LanguageModelV1CallWarning,\n  LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { convertUint8ArrayToBase64 } from '@ai-sdk/provider-utils';\nimport { OpenAIResponsesPrompt } from './openai-responses-api-types';\n\nexport function convertToOpenAIResponsesMessages({\n  prompt,\n  systemMessageMode,\n}: {\n  prompt: LanguageModelV1Prompt;\n  systemMessageMode: 'system' | 'developer' | 'remove';\n}): {\n  messages: OpenAIResponsesPrompt;\n  warnings: Array<LanguageModelV1CallWarning>;\n} {\n  const messages: OpenAIResponsesPrompt = [];\n  const warnings: Array<LanguageModelV1CallWarning> = [];\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case 'system': {\n        switch (systemMessageMode) {\n          case 'system': {\n            messages.push({ role: 'system', content });\n            break;\n          }\n          case 'developer': {\n            messages.push({ role: 'developer', content });\n            break;\n          }\n          case 'remove': {\n            warnings.push({\n              type: 'other',\n              message: 'system messages are removed for this model',\n            });\n            break;\n          }\n          default: {\n            const _exhaustiveCheck: never = systemMessageMode;\n            throw new Error(\n              `Unsupported system message mode: ${_exhaustiveCheck}`,\n            );\n          }\n        }\n        break;\n      }\n\n      case 'user': {\n        messages.push({\n          role: 'user',\n          content: content.map((part, index) => {\n            switch (part.type) {\n              case 'text': {\n                return { type: 'input_text', text: part.text };\n              }\n              case 'image': {\n                return {\n                  type: 'input_image',\n                  image_url:\n                    part.image instanceof URL\n                      ? part.image.toString()\n                      : `data:${\n                          part.mimeType ?? 'image/jpeg'\n                        };base64,${convertUint8ArrayToBase64(part.image)}`,\n\n                  // OpenAI specific extension: image detail\n                  detail: part.providerMetadata?.openai?.imageDetail,\n                };\n              }\n              case 'file': {\n                if (part.data instanceof URL) {\n                  // The AI SDK automatically downloads files for user file parts with URLs\n                  throw new UnsupportedFunctionalityError({\n                    functionality: 'File URLs in user messages',\n                  });\n                }\n\n                switch (part.mimeType) {\n                  case 'application/pdf': {\n                    return {\n                      type: 'input_file',\n                      filename: part.filename ?? `part-${index}.pdf`,\n                      file_data: `data:application/pdf;base64,${part.data}`,\n                    };\n                  }\n                  default: {\n                    throw new UnsupportedFunctionalityError({\n                      functionality:\n                        'Only PDF files are supported in user messages',\n                    });\n                  }\n                }\n              }\n            }\n          }),\n        });\n\n        break;\n      }\n\n      case 'assistant': {\n        for (const part of content) {\n          switch (part.type) {\n            case 'text': {\n              messages.push({\n                role: 'assistant',\n                content: [{ type: 'output_text', text: part.text }],\n              });\n              break;\n            }\n            case 'tool-call': {\n              messages.push({\n                type: 'function_call',\n                call_id: part.toolCallId,\n                name: part.toolName,\n                arguments: JSON.stringify(part.args),\n              });\n              break;\n            }\n          }\n        }\n\n        break;\n      }\n\n      case 'tool': {\n        for (const part of content) {\n          messages.push({\n            type: 'function_call_output',\n            call_id: part.toolCallId,\n            output: JSON.stringify(part.result),\n          });\n        }\n\n        break;\n      }\n\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return { messages, warnings };\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\n\nexport function mapOpenAIResponseFinishReason({\n  finishReason,\n  hasToolCalls,\n}: {\n  finishReason: string | null | undefined;\n  hasToolCalls: boolean;\n}): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case undefined:\n    case null:\n      return hasToolCalls ? 'tool-calls' : 'stop';\n    case 'max_output_tokens':\n      return 'length';\n    case 'content_filter':\n      return 'content-filter';\n    default:\n      return hasToolCalls ? 'tool-calls' : 'unknown';\n  }\n}\n", "import {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { OpenAIResponsesTool } from './openai-responses-api-types';\n\nexport function prepareResponsesTools({\n  mode,\n  strict,\n}: {\n  mode: Parameters<LanguageModelV1['doGenerate']>[0]['mode'] & {\n    type: 'regular';\n  };\n  strict: boolean;\n}): {\n  tools?: Array<OpenAIResponsesTool>;\n  tool_choice?:\n    | 'auto'\n    | 'none'\n    | 'required'\n    | { type: 'web_search_preview' }\n    | { type: 'function'; name: string };\n  toolWarnings: LanguageModelV1CallWarning[];\n} {\n  // when the tools array is empty, change it to undefined to prevent errors:\n  const tools = mode.tools?.length ? mode.tools : undefined;\n\n  const toolWarnings: LanguageModelV1CallWarning[] = [];\n\n  if (tools == null) {\n    return { tools: undefined, tool_choice: undefined, toolWarnings };\n  }\n\n  const toolChoice = mode.toolChoice;\n\n  const openaiTools: Array<OpenAIResponsesTool> = [];\n\n  for (const tool of tools) {\n    switch (tool.type) {\n      case 'function':\n        openaiTools.push({\n          type: 'function',\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters,\n          strict: strict ? true : undefined,\n        });\n        break;\n      case 'provider-defined':\n        switch (tool.id) {\n          case 'openai.web_search_preview':\n            openaiTools.push({\n              type: 'web_search_preview',\n              search_context_size: tool.args.searchContextSize as\n                | 'low'\n                | 'medium'\n                | 'high',\n              user_location: tool.args.userLocation as {\n                type: 'approximate';\n                city: string;\n                region: string;\n              },\n            });\n            break;\n          default:\n            toolWarnings.push({ type: 'unsupported-tool', tool });\n            break;\n        }\n        break;\n      default:\n        toolWarnings.push({ type: 'unsupported-tool', tool });\n        break;\n    }\n  }\n\n  if (toolChoice == null) {\n    return { tools: openaiTools, tool_choice: undefined, toolWarnings };\n  }\n\n  const type = toolChoice.type;\n\n  switch (type) {\n    case 'auto':\n    case 'none':\n    case 'required':\n      return { tools: openaiTools, tool_choice: type, toolWarnings };\n    case 'tool': {\n      if (toolChoice.toolName === 'web_search_preview') {\n        return {\n          tools: openaiTools,\n          tool_choice: {\n            type: 'web_search_preview',\n          },\n          toolWarnings,\n        };\n      }\n      return {\n        tools: openaiTools,\n        tool_choice: {\n          type: 'function',\n          name: toolChoice.toolName,\n        },\n        toolWarnings,\n      };\n    }\n    default: {\n      const _exhaustiveCheck: never = type;\n      throw new UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`,\n      });\n    }\n  }\n}\n", "import { z } from 'zod';\n\nconst WebSearchPreviewParameters = z.object({});\n\nfunction webSearchPreviewTool({\n  searchContextSize,\n  userLocation,\n}: {\n  searchContextSize?: 'low' | 'medium' | 'high';\n  userLocation?: {\n    type?: 'approximate';\n    city?: string;\n    region?: string;\n    country?: string;\n    timezone?: string;\n  };\n} = {}): {\n  type: 'provider-defined';\n  id: 'openai.web_search_preview';\n  args: {};\n  parameters: typeof WebSearchPreviewParameters;\n} {\n  return {\n    type: 'provider-defined',\n    id: 'openai.web_search_preview',\n    args: {\n      searchContextSize,\n      userLocation,\n    },\n    parameters: WebSearchPreviewParameters,\n  };\n}\n\nexport const openaiTools = {\n  webSearchPreview: webSearchPreviewTool,\n};\n", "import { SpeechModelV1, SpeechModelV1CallWarning } from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createBinaryResponseHandler,\n  parseProviderOptions,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { OpenAIConfig } from './openai-config';\nimport { openaiFailedResponseHandler } from './openai-error';\nimport { OpenAISpeechModelId } from './openai-speech-settings';\nimport { OpenAISpeechAPITypes } from './openai-api-types';\n\n// https://platform.openai.com/docs/api-reference/audio/createSpeech\nconst OpenAIProviderOptionsSchema = z.object({\n  instructions: z.string().nullish(),\n  speed: z.number().min(0.25).max(4.0).default(1.0).nullish(),\n});\n\nexport type OpenAISpeechCallOptions = z.infer<\n  typeof OpenAIProviderOptionsSchema\n>;\n\ninterface OpenAISpeechModelConfig extends OpenAIConfig {\n  _internal?: {\n    currentDate?: () => Date;\n  };\n}\n\nexport class OpenAISpeechModel implements SpeechModelV1 {\n  readonly specificationVersion = 'v1';\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  constructor(\n    readonly modelId: OpenAISpeechModelId,\n    private readonly config: OpenAISpeechModelConfig,\n  ) {}\n\n  private getArgs({\n    text,\n    voice = 'alloy',\n    outputFormat = 'mp3',\n    speed,\n    instructions,\n    providerOptions,\n  }: Parameters<SpeechModelV1['doGenerate']>[0]) {\n    const warnings: SpeechModelV1CallWarning[] = [];\n\n    // Parse provider options\n    const openAIOptions = parseProviderOptions({\n      provider: 'openai',\n      providerOptions,\n      schema: OpenAIProviderOptionsSchema,\n    });\n\n    // Create request body\n    const requestBody: Record<string, unknown> = {\n      model: this.modelId,\n      input: text,\n      voice,\n      response_format: 'mp3',\n      speed,\n      instructions,\n    };\n\n    if (outputFormat) {\n      if (['mp3', 'opus', 'aac', 'flac', 'wav', 'pcm'].includes(outputFormat)) {\n        requestBody.response_format = outputFormat;\n      } else {\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'outputFormat',\n          details: `Unsupported output format: ${outputFormat}. Using mp3 instead.`,\n        });\n      }\n    }\n\n    // Add provider-specific options\n    if (openAIOptions) {\n      const speechModelOptions: OpenAISpeechAPITypes = {};\n\n      for (const key in speechModelOptions) {\n        const value = speechModelOptions[key as keyof OpenAISpeechAPITypes];\n        if (value !== undefined) {\n          requestBody[key] = value;\n        }\n      }\n    }\n\n    return {\n      requestBody,\n      warnings,\n    };\n  }\n\n  async doGenerate(\n    options: Parameters<SpeechModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<SpeechModelV1['doGenerate']>>> {\n    const currentDate = this.config._internal?.currentDate?.() ?? new Date();\n    const { requestBody, warnings } = this.getArgs(options);\n\n    const {\n      value: audio,\n      responseHeaders,\n      rawValue: rawResponse,\n    } = await postJsonToApi({\n      url: this.config.url({\n        path: '/audio/speech',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: requestBody,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createBinaryResponseHandler(),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    return {\n      audio,\n      warnings,\n      request: {\n        body: JSON.stringify(requestBody),\n      },\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders,\n        body: rawResponse,\n      },\n    };\n  }\n}\n"], "names": ["UnsupportedFunctionalityError", "z", "token", "logprob", "UnsupportedFunctionalityError", "type", "openaiTools", "UnsupportedFunctionalityError", "_a", "toolCall", "z", "UnsupportedFunctionalityError", "combineHeaders", "createEventSourceResponseHandler", "createJsonResponseHandler", "postJsonToApi", "z", "UnsupportedFunctionalityError", "token", "UnsupportedFunctionalityError", "postJsonToApi", "combineHeaders", "createJsonResponseHandler", "createEventSourceResponseHandler", "z", "combineHeaders", "createJsonResponseHandler", "postJsonToApi", "z", "postJsonToApi", "combineHeaders", "createJsonResponseHandler", "z", "combineHeaders", "createJsonResponseHandler", "postJsonToApi", "z", "postJsonToApi", "combineHeaders", "createJsonResponseHandler", "z", "combineHeaders", "createJsonResponseHandler", "z", "z", "combineHeaders", "createJsonResponseHandler", "combineHeaders", "createEventSourceResponseHandler", "createJsonResponseHandler", "generateId", "parseProviderOptions", "postJsonToApi", "z", "UnsupportedFunctionalityError", "convertUint8ArrayToBase64", "UnsupportedFunctionalityError", "openaiTools", "parseProviderOptions", "postJsonToApi", "combineHeaders", "createJsonResponseHandler", "z", "_a", "_b", "_c", "generateId", "createEventSourceResponseHandler", "z", "combineHeaders", "parseProviderOptions", "postJsonToApi", "z", "z", "parseProviderOptions", "postJsonToApi", "combineHeaders"], "mappings": ";;;;;AAQA;;ACRA;AAoBA,SAAS,KAAAC,UAAS;;;;;;;ACZX,SAAS,4BAA4B,EAC1C,MAAA,EACA,2BAA2B,KAAA,EAC3B,oBAAoB,QAAA,EACtB,EAOE;IACA,MAAM,WAA6B,CAAC,CAAA;IACpC,MAAM,WAA8C,CAAC,CAAA;IAErD,KAAA,MAAW,EAAE,IAAA,EAAM,OAAA,CAAQ,CAAA,IAAK,OAAQ;QACtC,OAAQ,MAAM;YACZ,KAAK;gBAAU;oBACb,OAAQ,mBAAmB;wBACzB,KAAK;4BAAU;gCACb,SAAS,IAAA,CAAK;oCAAE,MAAM;oCAAU;gCAAQ,CAAC;gCACzC;4BACF;wBACA,KAAK;4BAAa;gCAChB,SAAS,IAAA,CAAK;oCAAE,MAAM;oCAAa;gCAAQ,CAAC;gCAC5C;4BACF;wBACA,KAAK;4BAAU;gCACb,SAAS,IAAA,CAAK;oCACZ,MAAM;oCACN,SAAS;gCACX,CAAC;gCACD;4BACF;wBACA;4BAAS;gCACP,MAAM,mBAA0B;gCAChC,MAAM,IAAI,MACR,CAAA,iCAAA,EAAoC,gBAAgB,EAAA;4BAExD;oBACF;oBACA;gBACF;YAEA,KAAK;gBAAQ;oBACX,IAAI,QAAQ,MAAA,KAAW,KAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,IAAA,KAAS,QAAQ;wBACtD,SAAS,IAAA,CAAK;4BAAE,MAAM;4BAAQ,SAAS,OAAA,CAAQ,CAAC,CAAA,CAAE,IAAA;wBAAK,CAAC;wBACxD;oBACF;oBAEA,SAAS,IAAA,CAAK;wBACZ,MAAM;wBACN,SAAS,QAAQ,GAAA,CAAI,CAAC,MAAM,UAAU;4BA5DhD,IAAA,IAAA,IAAA,IAAA;4BA6DY,OAAQ,KAAK,IAAA,EAAM;gCACjB,KAAK;oCAAQ;wCACX,OAAO;4CAAE,MAAM;4CAAQ,MAAM,KAAK,IAAA;wCAAK;oCACzC;gCACA,KAAK;oCAAS;wCACZ,OAAO;4CACL,MAAM;4CACN,WAAW;gDACT,KACE,KAAK,KAAA,YAAiB,MAClB,KAAK,KAAA,CAAM,QAAA,CAAS,IACpB,CAAA,KAAA,EAAA,CACE,KAAA,KAAK,QAAA,KAAL,OAAA,KAAiB,YACnB,CAAA,QAAA,0QAAW,4BAAA,EAA0B,KAAK,KAAK,CAAC,EAAA;gDAAA,0CAAA;gDAGtD,QAAA,CAAQ,KAAA,CAAA,KAAA,KAAK,gBAAA,KAAL,OAAA,KAAA,IAAA,GAAuB,MAAA,KAAvB,OAAA,KAAA,IAAA,GAA+B,WAAA;4CACzC;wCACF;oCACF;gCACA,KAAK;oCAAQ;wCACX,IAAI,KAAK,IAAA,YAAgB,KAAK;4CAC5B,MAAM,kOAAI,gCAAA,CAA8B;gDACtC,eACE;4CACJ,CAAC;wCACH;wCAEA,OAAQ,KAAK,QAAA,EAAU;4CACrB,KAAK;gDAAa;oDAChB,OAAO;wDACL,MAAM;wDACN,aAAa;4DAAE,MAAM,KAAK,IAAA;4DAAM,QAAQ;wDAAM;oDAChD;gDACF;4CACA,KAAK;4CACL,KAAK;gDAAc;oDACjB,OAAO;wDACL,MAAM;wDACN,aAAa;4DAAE,MAAM,KAAK,IAAA;4DAAM,QAAQ;wDAAM;oDAChD;gDACF;4CACA,KAAK;gDAAmB;oDACtB,OAAO;wDACL,MAAM;wDACN,MAAM;4DACJ,UAAA,CAAU,KAAA,KAAK,QAAA,KAAL,OAAA,KAAiB,CAAA,KAAA,EAAQ,KAAK,CAAA,IAAA,CAAA;4DACxC,WAAW,CAAA,4BAAA,EAA+B,KAAK,IAAI,EAAA;wDACrD;oDACF;gDACF;4CACA;gDAAS;oDACP,MAAM,IAAI,8PAAA,CAA8B;wDACtC,eAAe,CAAA,uBAAA,EAA0B,KAAK,QAAQ,CAAA,iBAAA,CAAA;oDACxD,CAAC;gDACH;wCACF;oCACF;4BACF;wBACF,CAAC;oBACH,CAAC;oBAED;gBACF;YAEA,KAAK;gBAAa;oBAChB,IAAI,OAAO;oBACX,MAAM,YAID,CAAC,CAAA;oBAEN,KAAA,MAAW,QAAQ,QAAS;wBAC1B,OAAQ,KAAK,IAAA,EAAM;4BACjB,KAAK;gCAAQ;oCACX,QAAQ,KAAK,IAAA;oCACb;gCACF;4BACA,KAAK;gCAAa;oCAChB,UAAU,IAAA,CAAK;wCACb,IAAI,KAAK,UAAA;wCACT,MAAM;wCACN,UAAU;4CACR,MAAM,KAAK,QAAA;4CACX,WAAW,KAAK,SAAA,CAAU,KAAK,IAAI;wCACrC;oCACF,CAAC;oCACD;gCACF;wBACF;oBACF;oBAEA,IAAI,0BAA0B;wBAC5B,IAAI,UAAU,MAAA,GAAS,GAAG;4BACxB,MAAM,kOAAI,gCAAA,CAA8B;gCACtC,eACE;4BACJ,CAAC;wBACH;wBAEA,SAAS,IAAA,CAAK;4BACZ,MAAM;4BACN,SAAS;4BACT,eACE,UAAU,MAAA,GAAS,IAAI,SAAA,CAAU,CAAC,CAAA,CAAE,QAAA,GAAW,KAAA;wBACnD,CAAC;oBACH,OAAO;wBACL,SAAS,IAAA,CAAK;4BACZ,MAAM;4BACN,SAAS;4BACT,YAAY,UAAU,MAAA,GAAS,IAAI,YAAY,KAAA;wBACjD,CAAC;oBACH;oBAEA;gBACF;YAEA,KAAK;gBAAQ;oBACX,KAAA,MAAW,gBAAgB,QAAS;wBAClC,IAAI,0BAA0B;4BAC5B,SAAS,IAAA,CAAK;gCACZ,MAAM;gCACN,MAAM,aAAa,QAAA;gCACnB,SAAS,KAAK,SAAA,CAAU,aAAa,MAAM;4BAC7C,CAAC;wBACH,OAAO;4BACL,SAAS,IAAA,CAAK;gCACZ,MAAM;gCACN,cAAc,aAAa,UAAA;gCAC3B,SAAS,KAAK,SAAA,CAAU,aAAa,MAAM;4BAC7C,CAAC;wBACH;oBACF;oBACA;gBACF;YAEA;gBAAS;oBACP,MAAM,mBAA0B;oBAChC,MAAM,IAAI,MAAM,CAAA,kBAAA,EAAqB,gBAAgB,EAAE;gBACzD;QACF;IACF;IAEA,OAAO;QAAE;QAAU;IAAS;AAC9B;;AC7LO,SAAS,4BACd,QAAA,EACqC;IAnBvC,IAAA,IAAA;IAoBE,OAAA,CACE,KAAA,CAAA,KAAA,YAAA,OAAA,KAAA,IAAA,SAAU,OAAA,KAAV,OAAA,KAAA,IAAA,GAAmB,GAAA,CAAI,CAAC,EAAE,KAAA,EAAO,OAAA,EAAS,YAAA,CAAa,CAAA,GAAA,CAAO;YAC5D;YACA;YACA,aAAa,eACT,aAAa,GAAA,CAAI,CAAC,EAAE,OAAAC,MAAAA,EAAO,SAAAC,QAAAA,CAAQ,CAAA,GAAA,CAAO;oBACxC,OAAAD;oBACA,SAAAC;gBACF,CAAA,CAAE,IACF,CAAC,CAAA;QACP,CAAA,EAAA,KATA,OAAA,KASO,KAAA;AAEX;;AC9BO,SAAS,sBACd,YAAA,EAC6B;IAC7B,OAAQ,cAAc;QACpB,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;;;ACfO,IAAM,wBAAwB,0LAAA,CAAE,MAAA,CAAO;IAC5C,6LAAO,IAAA,CAAE,MAAA,CAAO;QACd,+LAAS,IAAA,CAAE,MAAA,CAAO;QAAA,iEAAA;QAAA,iEAAA;QAAA,aAAA;QAKlB,MAAM,0LAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QACzB,6LAAO,IAAA,CAAE,GAAA,CAAI,EAAE,OAAA,CAAQ;QACvB,4LAAM,IAAA,CAAE,KAAA,CAAM;kMAAC,IAAA,CAAE,MAAA,CAAO;kMAAG,IAAA,CAAE,MAAA,CAAO,CAAC;SAAC,EAAE,OAAA,CAAQ;IAClD,CAAC;AACH,CAAC;AAIM,IAAM,sSAA8B,iCAAA,EAA+B;IACxE,aAAa;IACb,gBAAgB,CAAA,OAAQ,KAAK,KAAA,CAAM,OAAA;AACrC,CAAC;;ACrBM,SAAS,oBAAoB,EAClC,EAAA,EACA,KAAA,EACA,OAAA,EACF,EAIG;IACD,OAAO;QACL,IAAI,MAAA,OAAA,KAAM,KAAA;QACV,SAAS,SAAA,OAAA,QAAS,KAAA;QAClB,WAAW,WAAW,OAAO,IAAI,KAAK,UAAU,GAAI,IAAI,KAAA;IAC1D;AACF;;ACPO,SAAS,aAAa,EAC3B,IAAA,EACA,2BAA2B,KAAA,EAC3B,iBAAA,EACF,EA8BE;IAzCF,IAAA;IA2CE,MAAM,QAAA,CAAA,CAAQ,KAAA,KAAK,KAAA,KAAL,OAAA,KAAA,IAAA,GAAY,MAAA,IAAS,KAAK,KAAA,GAAQ,KAAA;IAEhD,MAAM,eAA6C,CAAC,CAAA;IAEpD,IAAI,SAAS,MAAM;QACjB,OAAO;YAAE,OAAO,KAAA;YAAW,aAAa,KAAA;YAAW;QAAa;IAClE;IAEA,MAAM,aAAa,KAAK,UAAA;IAExB,IAAI,0BAA0B;QAC5B,MAAM,kBAID,CAAC,CAAA;QAEN,KAAA,MAAW,QAAQ,MAAO;YACxB,IAAI,KAAK,IAAA,KAAS,oBAAoB;gBACpC,aAAa,IAAA,CAAK;oBAAE,MAAM;oBAAoB;gBAAK,CAAC;YACtD,OAAO;gBACL,gBAAgB,IAAA,CAAK;oBACnB,MAAM,KAAK,IAAA;oBACX,aAAa,KAAK,WAAA;oBAClB,YAAY,KAAK,UAAA;gBACnB,CAAC;YACH;QACF;QAEA,IAAI,cAAc,MAAM;YACtB,OAAO;gBACL,WAAW;gBACX,eAAe,KAAA;gBACf;YACF;QACF;QAEA,MAAME,QAAO,WAAW,IAAA;QAExB,OAAQA,OAAM;YACZ,KAAK;YACL,KAAK;YACL,KAAK,KAAA;gBACH,OAAO;oBACL,WAAW;oBACX,eAAe,KAAA;oBACf;gBACF;YACF,KAAK;gBACH,MAAM,kOAAID,gCAAAA,CAA8B;oBACtC,eAAe;gBACjB,CAAC;YACH;gBACE,OAAO;oBACL,WAAW;oBACX,eAAe;wBAAE,MAAM,WAAW,QAAA;oBAAS;oBAC3C;gBACF;QACJ;IACF;IAEA,MAAME,eAQD,CAAC,CAAA;IAEN,KAAA,MAAW,QAAQ,MAAO;QACxB,IAAI,KAAK,IAAA,KAAS,oBAAoB;YACpC,aAAa,IAAA,CAAK;gBAAE,MAAM;gBAAoB;YAAK,CAAC;QACtD,OAAO;YACLA,aAAY,IAAA,CAAK;gBACf,MAAM;gBACN,UAAU;oBACR,MAAM,KAAK,IAAA;oBACX,aAAa,KAAK,WAAA;oBAClB,YAAY,KAAK,UAAA;oBACjB,QAAQ,oBAAoB,OAAO,KAAA;gBACrC;YACF,CAAC;QACH;IACF;IAEA,IAAI,cAAc,MAAM;QACtB,OAAO;YAAE,OAAOA;YAAa,aAAa,KAAA;YAAW;QAAa;IACpE;IAEA,MAAM,OAAO,WAAW,IAAA;IAExB,OAAQ,MAAM;QACZ,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;gBAAE,OAAOA;gBAAa,aAAa;gBAAM;YAAa;QAC/D,KAAK;YACH,OAAO;gBACL,OAAOA;gBACP,aAAa;oBACX,MAAM;oBACN,UAAU;wBACR,MAAM,WAAW,QAAA;oBACnB;gBACF;gBACA;YACF;QACF;YAAS;gBACP,MAAM,mBAA0B;gBAChC,MAAM,kOAAIF,gCAAAA,CAA8B;oBACtC,eAAe,CAAA,8BAAA,EAAiC,gBAAgB,EAAA;gBAClE,CAAC;YACH;IACF;AACF;;ANvHO,IAAM,0BAAN,MAAyD;IAQ9D,YACE,OAAA,EACA,QAAA,EACA,MAAA,CACA;QAXF,IAAA,CAAS,oBAAA,GAAuB;QAY9B,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,MAAA,GAAS;IAChB;IAEA,IAAI,4BAAqC;QA1D3C,IAAA;QA8DI,OAAA,CAAO,KAAA,IAAA,CAAK,QAAA,CAAS,iBAAA,KAAd,OAAA,KAAmC,iBAAiB,IAAA,CAAK,OAAO;IACzE;IAEA,IAAI,8BAA8B;QAEhC,IAAI,aAAa,IAAA,CAAK,OAAO,GAAG;YAC9B,OAAO;QACT;QAEA,OAAO,IAAA,CAAK,yBAAA,GAA4B,SAAS;IACnD;IAEA,IAAI,WAAmB;QACrB,OAAO,IAAA,CAAK,MAAA,CAAO,QAAA;IACrB;IAEA,IAAI,oBAA6B;QAE/B,OAAO,CAAC,IAAA,CAAK,QAAA,CAAS,cAAA;IACxB;IAEQ,QAAQ,EACd,IAAA,EACA,MAAA,EACA,SAAA,EACA,WAAA,EACA,IAAA,EACA,IAAA,EACA,gBAAA,EACA,eAAA,EACA,aAAA,EACA,cAAA,EACA,IAAA,EACA,gBAAA,EACF,EAAiD;QAhGnD,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;QAiGI,MAAM,OAAO,KAAK,IAAA;QAElB,MAAM,WAAyC,CAAC,CAAA;QAEhD,IAAI,QAAQ,MAAM;YAChB,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,IAAA,CACE,kBAAA,OAAA,KAAA,IAAA,eAAgB,IAAA,MAAS,UACzB,eAAe,MAAA,IAAU,QACzB,CAAC,IAAA,CAAK,yBAAA,EACN;YACA,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;gBACT,SACE;YACJ,CAAC;QACH;QAEA,MAAM,2BAA2B,IAAA,CAAK,QAAA,CAAS,wBAAA;QAE/C,IAAI,4BAA4B,IAAA,CAAK,QAAA,CAAS,iBAAA,KAAsB,MAAM;YACxE,MAAM,kOAAIG,gCAAAA,CAA8B;gBACtC,eAAe;YACjB,CAAC;QACH;QAEA,IAAI,4BAA4B,IAAA,CAAK,yBAAA,EAA2B;YAC9D,MAAM,kOAAIA,gCAAAA,CAA8B;gBACtC,eAAe;YACjB,CAAC;QACH;QAEA,MAAM,EAAE,QAAA,EAAU,UAAU,eAAA,CAAgB,CAAA,GAAI,4BAC9C;YACE;YACA;YACA,mBAAmB,qBAAqB,IAAA,CAAK,OAAO;QACtD;QAGF,SAAS,IAAA,CAAK,GAAG,eAAe;QAEhC,MAAM,WAAW;YAAA,YAAA;YAEf,OAAO,IAAA,CAAK,OAAA;YAAA,2BAAA;YAGZ,YAAY,IAAA,CAAK,QAAA,CAAS,SAAA;YAC1B,UACE,IAAA,CAAK,QAAA,CAAS,QAAA,KAAa,QAC3B,OAAO,IAAA,CAAK,QAAA,CAAS,QAAA,KAAa,WAC9B,OACA,KAAA;YACN,cACE,OAAO,IAAA,CAAK,QAAA,CAAS,QAAA,KAAa,WAC9B,IAAA,CAAK,QAAA,CAAS,QAAA,GACd,OAAO,IAAA,CAAK,QAAA,CAAS,QAAA,KAAa,YAChC,IAAA,CAAK,QAAA,CAAS,QAAA,GACZ,IACA,KAAA,IACF,KAAA;YACR,MAAM,IAAA,CAAK,QAAA,CAAS,IAAA;YACpB,qBAAqB,IAAA,CAAK,QAAA,CAAS,iBAAA;YAAA,yBAAA;YAGnC,YAAY;YACZ;YACA,OAAO;YACP,mBAAmB;YACnB,kBAAkB;YAClB,iBAAA,CACE,kBAAA,OAAA,KAAA,IAAA,eAAgB,IAAA,MAAS,SACrB,IAAA,CAAK,yBAAA,IAA6B,eAAe,MAAA,IAAU,OACzD;gBACE,MAAM;gBACN,aAAa;oBACX,QAAQ,eAAe,MAAA;oBACvB,QAAQ;oBACR,MAAA,CAAM,KAAA,eAAe,IAAA,KAAf,OAAA,KAAuB;oBAC7B,aAAa,eAAe,WAAA;gBAC9B;YACF,IACA;gBAAE,MAAM;YAAc,IACxB,KAAA;YACN,MAAM;YACN;YAAA,4BAAA;YAAA,+DAAA;YAIA,uBAAA,CAAuB,KAAA,oBAAA,OAAA,KAAA,IAAA,iBAAkB,MAAA,KAAlB,OAAA,KAAA,IAAA,GAA0B,mBAAA;YACjD,OAAA,CAAO,KAAA,oBAAA,OAAA,KAAA,IAAA,iBAAkB,MAAA,KAAlB,OAAA,KAAA,IAAA,GAA0B,KAAA;YACjC,UAAA,CAAU,KAAA,oBAAA,OAAA,KAAA,IAAA,iBAAkB,MAAA,KAAlB,OAAA,KAAA,IAAA,GAA0B,QAAA;YACpC,YAAA,CAAY,KAAA,oBAAA,OAAA,KAAA,IAAA,iBAAkB,MAAA,KAAlB,OAAA,KAAA,IAAA,GAA0B,UAAA;YACtC,kBAAA,CACE,KAAA,CAAA,KAAA,oBAAA,OAAA,KAAA,IAAA,iBAAkB,MAAA,KAAlB,OAAA,KAAA,IAAA,GAA0B,eAAA,KAA1B,OAAA,KACA,IAAA,CAAK,QAAA,CAAS,eAAA;YAAA,YAAA;YAGhB;QACF;QAEA,IAAI,iBAAiB,IAAA,CAAK,OAAO,GAAG;YAGlC,IAAI,SAAS,WAAA,IAAe,MAAM;gBAChC,SAAS,WAAA,GAAc,KAAA;gBACvB,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;oBACT,SAAS;gBACX,CAAC;YACH;YACA,IAAI,SAAS,KAAA,IAAS,MAAM;gBAC1B,SAAS,KAAA,GAAQ,KAAA;gBACjB,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;oBACT,SAAS;gBACX,CAAC;YACH;YACA,IAAI,SAAS,iBAAA,IAAqB,MAAM;gBACtC,SAAS,iBAAA,GAAoB,KAAA;gBAC7B,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;oBACT,SAAS;gBACX,CAAC;YACH;YACA,IAAI,SAAS,gBAAA,IAAoB,MAAM;gBACrC,SAAS,gBAAA,GAAmB,KAAA;gBAC5B,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;oBACT,SAAS;gBACX,CAAC;YACH;YACA,IAAI,SAAS,UAAA,IAAc,MAAM;gBAC/B,SAAS,UAAA,GAAa,KAAA;gBACtB,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;gBACX,CAAC;YACH;YACA,IAAI,SAAS,QAAA,IAAY,MAAM;gBAC7B,SAAS,QAAA,GAAW,KAAA;gBACpB,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;gBACX,CAAC;YACH;YACA,IAAI,SAAS,YAAA,IAAgB,MAAM;gBACjC,SAAS,YAAA,GAAe,KAAA;gBACxB,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;gBACX,CAAC;YACH;YAGA,IAAI,SAAS,UAAA,IAAc,MAAM;gBAC/B,IAAI,SAAS,qBAAA,IAAyB,MAAM;oBAC1C,SAAS,qBAAA,GAAwB,SAAS,UAAA;gBAC5C;gBACA,SAAS,UAAA,GAAa,KAAA;YACxB;QACF,OAAA,IACE,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,uBAAuB,KAC/C,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,4BAA4B,GACpD;YACA,IAAI,SAAS,WAAA,IAAe,MAAM;gBAChC,SAAS,WAAA,GAAc,KAAA;gBACvB,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;oBACT,SACE;gBACJ,CAAC;YACH;QACF;QACA,OAAQ,MAAM;YACZ,KAAK;gBAAW;oBACd,MAAM,EAAE,KAAA,EAAO,WAAA,EAAa,SAAA,EAAW,aAAA,EAAe,YAAA,CAAa,CAAA,GACjE,aAAa;wBACX;wBACA;wBACA,mBAAmB,IAAA,CAAK,yBAAA;oBAC1B,CAAC;oBAEH,OAAO;wBACL,MAAM;4BACJ,GAAG,QAAA;4BACH;4BACA;4BACA;4BACA;wBACF;wBACA,UAAU,CAAC;+BAAG,UAAU;+BAAG,YAAY;yBAAA;oBACzC;gBACF;YAEA,KAAK;gBAAe;oBAClB,OAAO;wBACL,MAAM;4BACJ,GAAG,QAAA;4BACH,iBACE,IAAA,CAAK,yBAAA,IAA6B,KAAK,MAAA,IAAU,OAC7C;gCACE,MAAM;gCACN,aAAa;oCACX,QAAQ,KAAK,MAAA;oCACb,QAAQ;oCACR,MAAA,CAAM,KAAA,KAAK,IAAA,KAAL,OAAA,KAAa;oCACnB,aAAa,KAAK,WAAA;gCACpB;4BACF,IACA;gCAAE,MAAM;4BAAc;wBAC9B;wBACA;oBACF;gBACF;YAEA,KAAK;gBAAe;oBAClB,OAAO;wBACL,MAAM,2BACF;4BACE,GAAG,QAAA;4BACH,eAAe;gCACb,MAAM,KAAK,IAAA,CAAK,IAAA;4BAClB;4BACA,WAAW;gCACT;oCACE,MAAM,KAAK,IAAA,CAAK,IAAA;oCAChB,aAAa,KAAK,IAAA,CAAK,WAAA;oCACvB,YAAY,KAAK,IAAA,CAAK,UAAA;gCACxB;6BACF;wBACF,IACA;4BACE,GAAG,QAAA;4BACH,aAAa;gCACX,MAAM;gCACN,UAAU;oCAAE,MAAM,KAAK,IAAA,CAAK,IAAA;gCAAK;4BACnC;4BACA,OAAO;gCACL;oCACE,MAAM;oCACN,UAAU;wCACR,MAAM,KAAK,IAAA,CAAK,IAAA;wCAChB,aAAa,KAAK,IAAA,CAAK,WAAA;wCACvB,YAAY,KAAK,IAAA,CAAK,UAAA;wCACtB,QAAQ,IAAA,CAAK,yBAAA,GAA4B,OAAO,KAAA;oCAClD;gCACF;6BACF;wBACF;wBACJ;oBACF;gBACF;YAEA;gBAAS;oBACP,MAAM,mBAA0B;oBAChC,MAAM,IAAI,MAAM,CAAA,kBAAA,EAAqB,gBAAgB,EAAE;gBACzD;QACF;IACF;IAEA,MAAM,WACJ,OAAA,EAC6D;QAnXjE,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;QAoXI,MAAM,EAAE,MAAM,IAAA,EAAM,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,OAAO;QAErD,MAAM,EACJ,eAAA,EACA,OAAO,QAAA,EACP,UAAU,WAAA,EACZ,GAAI,8QAAM,gBAAA,EAAc;YACtB,KAAK,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI;gBACnB,MAAM;gBACN,SAAS,IAAA,CAAK,OAAA;YAChB,CAAC;YACD,iRAAS,iBAAA,EAAe,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,QAAQ,OAAO;YAC9D;YACA,uBAAuB;YACvB,2BAA2B,oSAAA,EACzB;YAEF,aAAa,QAAQ,WAAA;YACrB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;QACrB,CAAC;QAED,MAAM,EAAE,UAAU,SAAA,EAAW,GAAG,YAAY,CAAA,GAAI;QAChD,MAAM,SAAS,SAAS,OAAA,CAAQ,CAAC,CAAA;QAGjC,MAAM,yBAAA,CAAyB,KAAA,SAAS,KAAA,KAAT,OAAA,KAAA,IAAA,GAAgB,yBAAA;QAC/C,MAAM,qBAAA,CAAqB,KAAA,SAAS,KAAA,KAAT,OAAA,KAAA,IAAA,GAAgB,qBAAA;QAC3C,MAAM,mBAAoD;YAAE,QAAQ,CAAC;QAAE;QACvE,IAAA,CAAI,0BAAA,OAAA,KAAA,IAAA,uBAAwB,gBAAA,KAAoB,MAAM;YACpD,iBAAiB,MAAA,CAAO,eAAA,GACtB,0BAAA,OAAA,KAAA,IAAA,uBAAwB,gBAAA;QAC5B;QACA,IAAA,CAAI,0BAAA,OAAA,KAAA,IAAA,uBAAwB,0BAAA,KAA8B,MAAM;YAC9D,iBAAiB,MAAA,CAAO,wBAAA,GACtB,0BAAA,OAAA,KAAA,IAAA,uBAAwB,0BAAA;QAC5B;QACA,IAAA,CAAI,0BAAA,OAAA,KAAA,IAAA,uBAAwB,0BAAA,KAA8B,MAAM;YAC9D,iBAAiB,MAAA,CAAO,wBAAA,GACtB,0BAAA,OAAA,KAAA,IAAA,uBAAwB,0BAAA;QAC5B;QACA,IAAA,CAAI,sBAAA,OAAA,KAAA,IAAA,mBAAoB,aAAA,KAAiB,MAAM;YAC7C,iBAAiB,MAAA,CAAO,kBAAA,GACtB,sBAAA,OAAA,KAAA,IAAA,mBAAoB,aAAA;QACxB;QAEA,OAAO;YACL,MAAA,CAAM,KAAA,OAAO,OAAA,CAAQ,OAAA,KAAf,OAAA,KAA0B,KAAA;YAChC,WACE,IAAA,CAAK,QAAA,CAAS,wBAAA,IAA4B,OAAO,OAAA,CAAQ,aAAA,GACrD;gBACE;oBACE,cAAc;oBACd,oRAAY,aAAA,CAAW;oBACvB,UAAU,OAAO,OAAA,CAAQ,aAAA,CAAc,IAAA;oBACvC,MAAM,OAAO,OAAA,CAAQ,aAAA,CAAc,SAAA;gBACrC;aACF,GAAA,CACA,KAAA,OAAO,OAAA,CAAQ,UAAA,KAAf,OAAA,KAAA,IAAA,GAA2B,GAAA,CAAI,CAAA,aAAS;gBA7apD,IAAAC;gBA6awD,OAAA;oBAC1C,cAAc;oBACd,YAAA,CAAYA,MAAA,SAAS,EAAA,KAAT,OAAAA,8QAAe,aAAA,CAAW;oBACtC,UAAU,SAAS,QAAA,CAAS,IAAA;oBAC5B,MAAM,SAAS,QAAA,CAAS,SAAA;gBAC1B;YAAA;YACN,cAAc,sBAAsB,OAAO,aAAa;YACxD,OAAO;gBACL,cAAA,CAAc,KAAA,CAAA,KAAA,SAAS,KAAA,KAAT,OAAA,KAAA,IAAA,GAAgB,aAAA,KAAhB,OAAA,KAAiC;gBAC/C,kBAAA,CAAkB,KAAA,CAAA,KAAA,SAAS,KAAA,KAAT,OAAA,KAAA,IAAA,GAAgB,iBAAA,KAAhB,OAAA,KAAqC;YACzD;YACA,SAAS;gBAAE;gBAAW;YAAY;YAClC,aAAa;gBAAE,SAAS;gBAAiB,MAAM;YAAY;YAC3D,SAAS;gBAAE,MAAM,KAAK,SAAA,CAAU,IAAI;YAAE;YACtC,UAAU,oBAAoB,QAAQ;YACtC;YACA,UAAU,4BAA4B,OAAO,QAAQ;YACrD;QACF;IACF;IAEA,MAAM,SACJ,OAAA,EAC2D;QAC3D,IAAI,IAAA,CAAK,QAAA,CAAS,iBAAA,EAAmB;YACnC,MAAM,SAAS,MAAM,IAAA,CAAK,UAAA,CAAW,OAAO;YAE5C,MAAM,kBAAkB,IAAI,eAA0C;gBACpE,OAAM,UAAA,EAAY;oBAChB,WAAW,OAAA,CAAQ;wBAAE,MAAM;wBAAqB,GAAG,OAAO,QAAA;oBAAS,CAAC;oBACpE,IAAI,OAAO,IAAA,EAAM;wBACf,WAAW,OAAA,CAAQ;4BACjB,MAAM;4BACN,WAAW,OAAO,IAAA;wBACpB,CAAC;oBACH;oBACA,IAAI,OAAO,SAAA,EAAW;wBACpB,KAAA,MAAW,YAAY,OAAO,SAAA,CAAW;4BACvC,WAAW,OAAA,CAAQ;gCACjB,MAAM;gCACN,cAAc;gCACd,YAAY,SAAS,UAAA;gCACrB,UAAU,SAAS,QAAA;gCACnB,eAAe,SAAS,IAAA;4BAC1B,CAAC;4BAED,WAAW,OAAA,CAAQ;gCACjB,MAAM;gCACN,GAAG,QAAA;4BACL,CAAC;wBACH;oBACF;oBACA,WAAW,OAAA,CAAQ;wBACjB,MAAM;wBACN,cAAc,OAAO,YAAA;wBACrB,OAAO,OAAO,KAAA;wBACd,UAAU,OAAO,QAAA;wBACjB,kBAAkB,OAAO,gBAAA;oBAC3B,CAAC;oBACD,WAAW,KAAA,CAAM;gBACnB;YACF,CAAC;YACD,OAAO;gBACL,QAAQ;gBACR,SAAS,OAAO,OAAA;gBAChB,aAAa,OAAO,WAAA;gBACpB,UAAU,OAAO,QAAA;YACnB;QACF;QAEA,MAAM,EAAE,IAAA,EAAM,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,OAAO;QAE/C,MAAM,OAAO;YACX,GAAG,IAAA;YACH,QAAQ;YAAA,iEAAA;YAGR,gBACE,IAAA,CAAK,MAAA,CAAO,aAAA,KAAkB,WAC1B;gBAAE,eAAe;YAAK,IACtB,KAAA;QACR;QAEA,MAAM,EAAE,eAAA,EAAiB,OAAO,QAAA,CAAS,CAAA,GAAI,8QAAM,gBAAA,EAAc;YAC/D,KAAK,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI;gBACnB,MAAM;gBACN,SAAS,IAAA,CAAK,OAAA;YAChB,CAAC;YACD,iRAAS,iBAAA,EAAe,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,QAAQ,OAAO;YAC9D;YACA,uBAAuB;YACvB,mSAA2B,mCAAA,EACzB;YAEF,aAAa,QAAQ,WAAA;YACrB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;QACrB,CAAC;QAED,MAAM,EAAE,UAAU,SAAA,EAAW,GAAG,YAAY,CAAA,GAAI;QAEhD,MAAM,YAQD,CAAC,CAAA;QAEN,IAAI,eAA4C;QAChD,IAAI,QAGA;YACF,cAAc,KAAA;YACd,kBAAkB,KAAA;QACpB;QACA,IAAI;QACJ,IAAI,eAAe;QAEnB,MAAM,EAAE,wBAAA,CAAyB,CAAA,GAAI,IAAA,CAAK,QAAA;QAE1C,MAAM,mBAAoD;YAAE,QAAQ,CAAC;QAAE;QAEvE,OAAO;YACL,QAAQ,SAAS,WAAA,CACf,IAAI,gBAGF;gBACA,WAAU,KAAA,EAAO,UAAA,EAAY;oBAhjBvC,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;oBAkjBY,IAAI,CAAC,MAAM,OAAA,EAAS;wBAClB,eAAe;wBACf,WAAW,OAAA,CAAQ;4BAAE,MAAM;4BAAS,OAAO,MAAM,KAAA;wBAAM,CAAC;wBACxD;oBACF;oBAEA,MAAM,QAAQ,MAAM,KAAA;oBAGpB,IAAI,WAAW,OAAO;wBACpB,eAAe;wBACf,WAAW,OAAA,CAAQ;4BAAE,MAAM;4BAAS,OAAO,MAAM,KAAA;wBAAM,CAAC;wBACxD;oBACF;oBAEA,IAAI,cAAc;wBAChB,eAAe;wBAEf,WAAW,OAAA,CAAQ;4BACjB,MAAM;4BACN,GAAG,oBAAoB,KAAK,CAAA;wBAC9B,CAAC;oBACH;oBAEA,IAAI,MAAM,KAAA,IAAS,MAAM;wBACvB,MAAM,EACJ,aAAA,EACA,iBAAA,EACA,qBAAA,EACA,yBAAA,EACF,GAAI,MAAM,KAAA;wBAEV,QAAQ;4BACN,cAAc,iBAAA,OAAA,gBAAiB,KAAA;4BAC/B,kBAAkB,qBAAA,OAAA,oBAAqB,KAAA;wBACzC;wBAEA,IAAA,CAAI,6BAAA,OAAA,KAAA,IAAA,0BAA2B,gBAAA,KAAoB,MAAM;4BACvD,iBAAiB,MAAA,CAAO,eAAA,GACtB,6BAAA,OAAA,KAAA,IAAA,0BAA2B,gBAAA;wBAC/B;wBACA,IAAA,CACE,6BAAA,OAAA,KAAA,IAAA,0BAA2B,0BAAA,KAA8B,MACzD;4BACA,iBAAiB,MAAA,CAAO,wBAAA,GACtB,6BAAA,OAAA,KAAA,IAAA,0BAA2B,0BAAA;wBAC/B;wBACA,IAAA,CACE,6BAAA,OAAA,KAAA,IAAA,0BAA2B,0BAAA,KAA8B,MACzD;4BACA,iBAAiB,MAAA,CAAO,wBAAA,GACtB,6BAAA,OAAA,KAAA,IAAA,0BAA2B,0BAAA;wBAC/B;wBACA,IAAA,CAAI,yBAAA,OAAA,KAAA,IAAA,sBAAuB,aAAA,KAAiB,MAAM;4BAChD,iBAAiB,MAAA,CAAO,kBAAA,GACtB,yBAAA,OAAA,KAAA,IAAA,sBAAuB,aAAA;wBAC3B;oBACF;oBAEA,MAAM,SAAS,MAAM,OAAA,CAAQ,CAAC,CAAA;oBAE9B,IAAA,CAAI,UAAA,OAAA,KAAA,IAAA,OAAQ,aAAA,KAAiB,MAAM;wBACjC,eAAe,sBAAsB,OAAO,aAAa;oBAC3D;oBAEA,IAAA,CAAI,UAAA,OAAA,KAAA,IAAA,OAAQ,KAAA,KAAS,MAAM;wBACzB;oBACF;oBAEA,MAAM,QAAQ,OAAO,KAAA;oBAErB,IAAI,MAAM,OAAA,IAAW,MAAM;wBACzB,WAAW,OAAA,CAAQ;4BACjB,MAAM;4BACN,WAAW,MAAM,OAAA;wBACnB,CAAC;oBACH;oBAEA,MAAM,iBAAiB,4BACrB,UAAA,OAAA,KAAA,IAAA,OAAQ,QAAA;oBAEV,IAAI,kBAAA,OAAA,KAAA,IAAA,eAAgB,MAAA,EAAQ;wBAC1B,IAAI,aAAa,KAAA,EAAW,CAAA,WAAW,CAAC,CAAA;wBACxC,SAAS,IAAA,CAAK,GAAG,cAAc;oBACjC;oBAEA,MAAM,kBACJ,4BAA4B,MAAM,aAAA,IAAiB,OAC/C;wBACE;4BACE,MAAM;4BACN,4QAAI,aAAA,CAAW;4BACf,UAAU,MAAM,aAAA;4BAChB,OAAO;wBACT;qBACF,GACA,MAAM,UAAA;oBAEZ,IAAI,mBAAmB,MAAM;wBAC3B,KAAA,MAAW,iBAAiB,gBAAiB;4BAC3C,MAAM,QAAQ,cAAc,KAAA;4BAG5B,IAAI,SAAA,CAAU,KAAK,CAAA,IAAK,MAAM;gCAC5B,IAAI,cAAc,IAAA,KAAS,YAAY;oCACrC,MAAM,kOAAI,2BAAA,CAAyB;wCACjC,MAAM;wCACN,SAAS,CAAA,yBAAA,CAAA;oCACX,CAAC;gCACH;gCAEA,IAAI,cAAc,EAAA,IAAM,MAAM;oCAC5B,MAAM,kOAAI,2BAAA,CAAyB;wCACjC,MAAM;wCACN,SAAS,CAAA,6BAAA,CAAA;oCACX,CAAC;gCACH;gCAEA,IAAA,CAAA,CAAI,KAAA,cAAc,QAAA,KAAd,OAAA,KAAA,IAAA,GAAwB,IAAA,KAAQ,MAAM;oCACxC,MAAM,kOAAI,2BAAA,CAAyB;wCACjC,MAAM;wCACN,SAAS,CAAA,wCAAA,CAAA;oCACX,CAAC;gCACH;gCAEA,SAAA,CAAU,KAAK,CAAA,GAAI;oCACjB,IAAI,cAAc,EAAA;oCAClB,MAAM;oCACN,UAAU;wCACR,MAAM,cAAc,QAAA,CAAS,IAAA;wCAC7B,WAAA,CAAW,KAAA,cAAc,QAAA,CAAS,SAAA,KAAvB,OAAA,KAAoC;oCACjD;oCACA,aAAa;gCACf;gCAEA,MAAMC,YAAW,SAAA,CAAU,KAAK,CAAA;gCAEhC,IAAA,CAAA,CACE,KAAAA,UAAS,QAAA,KAAT,OAAA,KAAA,IAAA,GAAmB,IAAA,KAAQ,QAAA,CAAA,CAC3B,KAAAA,UAAS,QAAA,KAAT,OAAA,KAAA,IAAA,GAAmB,SAAA,KAAa,MAChC;oCAEA,IAAIA,UAAS,QAAA,CAAS,SAAA,CAAU,MAAA,GAAS,GAAG;wCAC1C,WAAW,OAAA,CAAQ;4CACjB,MAAM;4CACN,cAAc;4CACd,YAAYA,UAAS,EAAA;4CACrB,UAAUA,UAAS,QAAA,CAAS,IAAA;4CAC5B,eAAeA,UAAS,QAAA,CAAS,SAAA;wCACnC,CAAC;oCACH;oCAIA,4QAAI,iBAAA,EAAeA,UAAS,QAAA,CAAS,SAAS,GAAG;wCAC/C,WAAW,OAAA,CAAQ;4CACjB,MAAM;4CACN,cAAc;4CACd,YAAA,CAAY,KAAAA,UAAS,EAAA,KAAT,OAAA,KAAe,qRAAA,CAAW;4CACtC,UAAUA,UAAS,QAAA,CAAS,IAAA;4CAC5B,MAAMA,UAAS,QAAA,CAAS,SAAA;wCAC1B,CAAC;wCACDA,UAAS,WAAA,GAAc;oCACzB;gCACF;gCAEA;4BACF;4BAGA,MAAM,WAAW,SAAA,CAAU,KAAK,CAAA;4BAEhC,IAAI,SAAS,WAAA,EAAa;gCACxB;4BACF;4BAEA,IAAA,CAAA,CAAI,KAAA,cAAc,QAAA,KAAd,OAAA,KAAA,IAAA,GAAwB,SAAA,KAAa,MAAM;gCAC7C,SAAS,QAAA,CAAU,SAAA,IAAA,CACjB,KAAA,CAAA,KAAA,cAAc,QAAA,KAAd,OAAA,KAAA,IAAA,GAAwB,SAAA,KAAxB,OAAA,KAAqC;4BACzC;4BAGA,WAAW,OAAA,CAAQ;gCACjB,MAAM;gCACN,cAAc;gCACd,YAAY,SAAS,EAAA;gCACrB,UAAU,SAAS,QAAA,CAAS,IAAA;gCAC5B,eAAA,CAAe,KAAA,cAAc,QAAA,CAAS,SAAA,KAAvB,OAAA,KAAoC;4BACrD,CAAC;4BAGD,IAAA,CAAA,CACE,KAAA,SAAS,QAAA,KAAT,OAAA,KAAA,IAAA,GAAmB,IAAA,KAAQ,QAAA,CAAA,CAC3B,KAAA,SAAS,QAAA,KAAT,OAAA,KAAA,IAAA,GAAmB,SAAA,KAAa,gRAChC,iBAAA,EAAe,SAAS,QAAA,CAAS,SAAS,GAC1C;gCACA,WAAW,OAAA,CAAQ;oCACjB,MAAM;oCACN,cAAc;oCACd,YAAA,CAAY,KAAA,SAAS,EAAA,KAAT,OAAA,4QAAe,cAAA,CAAW;oCACtC,UAAU,SAAS,QAAA,CAAS,IAAA;oCAC5B,MAAM,SAAS,QAAA,CAAS,SAAA;gCAC1B,CAAC;gCACD,SAAS,WAAA,GAAc;4BACzB;wBACF;oBACF;gBACF;gBAEA,OAAM,UAAA,EAAY;oBAnwB5B,IAAA,IAAA;oBAowBY,WAAW,OAAA,CAAQ;wBACjB,MAAM;wBACN;wBACA;wBACA,OAAO;4BACL,cAAA,CAAc,KAAA,MAAM,YAAA,KAAN,OAAA,KAAsB;4BACpC,kBAAA,CAAkB,KAAA,MAAM,gBAAA,KAAN,OAAA,KAA0B;wBAC9C;wBACA,GAAI,oBAAoB,OAAO;4BAAE;wBAAiB,IAAI,CAAC,CAAA;oBACzD,CAAC;gBACH;YACF,CAAC;YAEH,SAAS;gBAAE;gBAAW;YAAY;YAClC,aAAa;gBAAE,SAAS;YAAgB;YACxC,SAAS;gBAAE,MAAM,KAAK,SAAA,CAAU,IAAI;YAAE;YACtC;QACF;IACF;AACF;AAEA,IAAM,+MAAyBC,IAAAA,CAC5B,MAAA,CAAO;IACN,qMAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAClC,yMAAmBA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACtC,6MAAuBA,IAAAA,CACpB,MAAA,CAAO;QACN,qMAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACpC,CAAC,EACA,OAAA,CAAQ;IACX,iNAA2BA,IAAAA,CACxB,MAAA,CAAO;QACN,kBAAkBA,0LAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QACrC,kNAA4BA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QAC/C,kNAA4BA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACjD,CAAC,EACA,OAAA,CAAQ;AACb,CAAC,EACA,OAAA,CAAQ;AAIX,IAAM,iNAA2BA,IAAAA,CAAE,MAAA,CAAO;IACxC,0LAAIA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACvB,+LAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC5B,OAAOA,0LAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC1B,+LAASA,IAAAA,CAAE,KAAA,sLACTA,KAAAA,CAAE,MAAA,CAAO;QACP,+LAASA,IAAAA,CAAE,MAAA,CAAO;YAChB,MAAMA,0LAAAA,CAAE,OAAA,CAAQ,WAAW,EAAE,OAAA,CAAQ;YACrC,+LAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YAC5B,qMAAeA,IAAAA,CACZ,MAAA,CAAO;gBACN,iMAAWA,IAAAA,CAAE,MAAA,CAAO;gBACpB,4LAAMA,IAAAA,CAAE,MAAA,CAAO;YACjB,CAAC,EACA,OAAA,CAAQ;YACX,kMAAYA,IAAAA,CACT,KAAA,uLACCA,IAAAA,CAAE,MAAA,CAAO;gBACP,0LAAIA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;gBACvB,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,UAAU;gBAC1B,gMAAUA,IAAAA,CAAE,MAAA,CAAO;oBACjB,MAAMA,0LAAAA,CAAE,MAAA,CAAO;oBACf,iMAAWA,IAAAA,CAAE,MAAA,CAAO;gBACtB,CAAC;YACH,CAAC,GAEF,OAAA,CAAQ;QACb,CAAC;QACD,6LAAOA,IAAAA,CAAE,MAAA,CAAO;QAChB,gMAAUA,IAAAA,CACP,MAAA,CAAO;YACN,+LAASA,IAAAA,CACN,KAAA,uLACCA,IAAAA,CAAE,MAAA,CAAO;gBACP,6LAAOA,IAAAA,CAAE,MAAA,CAAO;gBAChB,8LAASA,KAAAA,CAAE,MAAA,CAAO;gBAClB,oMAAcA,IAAAA,CAAE,KAAA,CACdA,0LAAAA,CAAE,MAAA,CAAO;oBACP,6LAAOA,IAAAA,CAAE,MAAA,CAAO;oBAChB,+LAASA,IAAAA,CAAE,MAAA,CAAO;gBACpB,CAAC;YAEL,CAAC,GAEF,QAAA,CAAS;QACd,CAAC,EACA,OAAA,CAAQ;QACX,qMAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACpC,CAAC;IAEH,OAAO;AACT,CAAC;AAID,IAAM,8MAAwBA,IAAAA,CAAE,KAAA,CAAM;0LACpCA,IAAAA,CAAE,MAAA,CAAO;QACP,IAAIA,0LAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QACvB,8LAASA,KAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QAC5B,6LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QAC1B,+LAASA,IAAAA,CAAE,KAAA,uLACTA,IAAAA,CAAE,MAAA,CAAO;YACP,6LAAOA,IAAAA,CACJ,MAAA,CAAO;gBACN,4LAAMA,IAAAA,CAAE,IAAA,CAAK;oBAAC,WAAW;iBAAC,EAAE,OAAA,CAAQ;gBACpC,+LAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;gBAC5B,qMAAeA,IAAAA,CACZ,MAAA,CAAO;oBACN,4LAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;oBAC1B,iMAAWA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;gBACjC,CAAC,EACA,OAAA,CAAQ;gBACX,kMAAYA,IAAAA,CACT,KAAA,uLACCA,IAAAA,CAAE,MAAA,CAAO;oBACP,6LAAOA,IAAAA,CAAE,MAAA,CAAO;oBAChB,0LAAIA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;oBACvB,MAAMA,0LAAAA,CAAE,OAAA,CAAQ,UAAU,EAAE,OAAA,CAAQ;oBACpC,gMAAUA,IAAAA,CAAE,MAAA,CAAO;wBACjB,4LAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;wBACzB,iMAAWA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;oBAChC,CAAC;gBACH,CAAC,GAEF,OAAA,CAAQ;YACb,CAAC,EACA,OAAA,CAAQ;YACX,gMAAUA,IAAAA,CACP,MAAA,CAAO;gBACN,+LAASA,IAAAA,CACN,KAAA,uLACCA,IAAAA,CAAE,MAAA,CAAO;oBACP,6LAAOA,IAAAA,CAAE,MAAA,CAAO;oBAChB,+LAASA,IAAAA,CAAE,MAAA,CAAO;oBAClB,oMAAcA,IAAAA,CAAE,KAAA,uLACdA,IAAAA,CAAE,MAAA,CAAO;wBACP,6LAAOA,IAAAA,CAAE,MAAA,CAAO;wBAChB,SAASA,0LAAAA,CAAE,MAAA,CAAO;oBACpB,CAAC;gBAEL,CAAC,GAEF,QAAA,CAAS;YACd,CAAC,EACA,OAAA,CAAQ;YACX,qMAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YAClC,OAAOA,0LAAAA,CAAE,MAAA,CAAO;QAClB,CAAC;QAEH,OAAO;IACT,CAAC;IACD;CACD;AAED,SAAS,iBAAiB,OAAA,EAAiB;IACzC,OAAO,QAAQ,UAAA,CAAW,GAAG;AAC/B;AAEA,SAAS,aAAa,OAAA,EAAiB;IACrC,OAAO,QAAQ,UAAA,CAAW,sBAAsB;AAClD;AAEA,SAAS,qBAAqB,OAAA,EAAiB;IAx6B/C,IAAA,IAAA;IAy6BE,IAAI,CAAC,iBAAiB,OAAO,GAAG;QAC9B,OAAO;IACT;IAEA,OAAA,CACE,KAAA,CAAA,KAAA,eAAA,CAAgB,OAAuC,CAAA,KAAvD,OAAA,KAAA,IAAA,GACI,iBAAA,KADJ,OAAA,KACyB;AAE7B;AAEA,IAAM,kBAAkB;IACtB,WAAW;QACT,mBAAmB;IACrB;IACA,sBAAsB;QACpB,mBAAmB;IACrB;IACA,cAAc;QACZ,mBAAmB;IACrB;IACA,yBAAyB;QACvB,mBAAmB;IACrB;IACA,IAAI;QACF,mBAAmB;IACrB;IACA,iBAAiB;QACf,mBAAmB;IACrB;IACA,WAAW;QACT,mBAAmB;IACrB;IACA,sBAAsB;QACpB,mBAAmB;IACrB;IACA,WAAW;QACT,mBAAmB;IACrB;IACA,sBAAsB;QACpB,mBAAmB;IACrB;AACF;;;;;AQ58BO,SAAS,gCAAgC,EAC9C,MAAA,EACA,WAAA,EACA,OAAO,MAAA,EACP,YAAY,WAAA,EACd,EAQE;IAEA,IACE,gBAAgB,YAChB,OAAO,MAAA,KAAW,KAClB,MAAA,CAAO,CAAC,CAAA,CAAE,IAAA,KAAS,UACnB,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,CAAQ,MAAA,KAAW,KAC7B,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,CAAQ,CAAC,CAAA,CAAE,IAAA,KAAS,QAC9B;QACA,OAAO;YAAE,QAAQ,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,CAAQ,CAAC,CAAA,CAAE,IAAA;QAAK;IAC7C;IAGA,IAAI,OAAO;IAGX,IAAI,MAAA,CAAO,CAAC,CAAA,CAAE,IAAA,KAAS,UAAU;QAC/B,QAAQ,GAAG,MAAA,CAAO,CAAC,CAAA,CAAE,OAAO,CAAA;;AAAA,CAAA;QAC5B,SAAS,OAAO,KAAA,CAAM,CAAC;IACzB;IAEA,KAAA,MAAW,EAAE,IAAA,EAAM,OAAA,CAAQ,CAAA,IAAK,OAAQ;QACtC,OAAQ,MAAM;YACZ,KAAK;gBAAU;oBACb,MAAM,kOAAI,qBAAA,CAAmB;wBAC3B,SAAS;wBACT;oBACF,CAAC;gBACH;YAEA,KAAK;gBAAQ;oBACX,MAAM,cAAc,QACjB,GAAA,CAAI,CAAA,SAAQ;wBACX,OAAQ,KAAK,IAAA,EAAM;4BACjB,KAAK;gCAAQ;oCACX,OAAO,KAAK,IAAA;gCACd;4BACA,KAAK;gCAAS;oCACZ,MAAM,kOAAIO,gCAAAA,CAA8B;wCACtC,eAAe;oCACjB,CAAC;gCACH;wBACF;oBACF,CAAC,EACA,IAAA,CAAK,EAAE;oBAEV,QAAQ,GAAG,IAAI,CAAA;AAAA,EAAM,WAAW,CAAA;;AAAA,CAAA;oBAChC;gBACF;YAEA,KAAK;gBAAa;oBAChB,MAAM,mBAAmB,QACtB,GAAA,CAAI,CAAA,SAAQ;wBACX,OAAQ,KAAK,IAAA,EAAM;4BACjB,KAAK;gCAAQ;oCACX,OAAO,KAAK,IAAA;gCACd;4BACA,KAAK;gCAAa;oCAChB,MAAM,kOAAIA,gCAAAA,CAA8B;wCACtC,eAAe;oCACjB,CAAC;gCACH;wBACF;oBACF,CAAC,EACA,IAAA,CAAK,EAAE;oBAEV,QAAQ,GAAG,SAAS,CAAA;AAAA,EAAM,gBAAgB,CAAA;;AAAA,CAAA;oBAC1C;gBACF;YAEA,KAAK;gBAAQ;oBACX,MAAM,kOAAIA,gCAAAA,CAA8B;wBACtC,eAAe;oBACjB,CAAC;gBACH;YAEA;gBAAS;oBACP,MAAM,mBAA0B;oBAChC,MAAM,IAAI,MAAM,CAAA,kBAAA,EAAqB,gBAAgB,EAAE;gBACzD;QACF;IACF;IAGA,QAAQ,GAAG,SAAS,CAAA;AAAA,CAAA;IAEpB,OAAO;QACL,QAAQ;QACR,eAAe;YAAC,CAAA;AAAA,EAAK,IAAI,CAAA,CAAA,CAAG;SAAA;IAC9B;AACF;;ACrGO,SAAS,4BACd,QAAA,EACqC;IACrC,OAAO,YAAA,OAAA,KAAA,IAAA,SAAU,MAAA,CAAO,GAAA,CAAI,CAAC,OAAO,QAAA,CAAW;YAC7C;YACA,SAAS,SAAS,cAAA,CAAe,KAAK,CAAA;YACtC,aAAa,SAAS,YAAA,GAClB,OAAO,OAAA,CAAQ,SAAS,YAAA,CAAa,KAAK,CAAC,EAAE,GAAA,CAC3C,CAAC,CAACC,QAAO,OAAO,CAAA,GAAA,CAAO;oBACrB,OAAAA;oBACA;gBACF,CAAA,KAEF,CAAC,CAAA;QACP,CAAA;AACF;;AFeO,IAAM,gCAAN,MAA+D;IASpE,YACE,OAAA,EACA,QAAA,EACA,MAAA,CACA;QAZF,IAAA,CAAS,oBAAA,GAAuB;QAChC,IAAA,CAAS,2BAAA,GAA8B,KAAA;QAYrC,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,MAAA,GAAS;IAChB;IAEA,IAAI,WAAmB;QACrB,OAAO,IAAA,CAAK,MAAA,CAAO,QAAA;IACrB;IAEQ,QAAQ,EACd,IAAA,EACA,WAAA,EACA,MAAA,EACA,SAAA,EACA,WAAA,EACA,IAAA,EACA,IAAA,EACA,gBAAA,EACA,eAAA,EACA,eAAe,iBAAA,EACf,cAAA,EACA,IAAA,EACF,EAAiD;QA1EnD,IAAA;QA2EI,MAAM,OAAO,KAAK,IAAA;QAElB,MAAM,WAAyC,CAAC,CAAA;QAEhD,IAAI,QAAQ,MAAM;YAChB,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,IAAI,kBAAkB,QAAQ,eAAe,IAAA,KAAS,QAAQ;YAC5D,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;gBACT,SAAS;YACX,CAAC;QACH;QAEA,MAAM,EAAE,QAAQ,gBAAA,EAAkB,aAAA,CAAc,CAAA,GAC9C,gCAAgC;YAAE;YAAQ;QAAY,CAAC;QAEzD,MAAM,OAAO,CAAC;eAAI,iBAAA,OAAA,gBAAiB,CAAC,CAAA,EAAI;eAAI,qBAAA,OAAA,oBAAqB,CAAC,CAAE;SAAA;QAEpE,MAAM,WAAW;YAAA,YAAA;YAEf,OAAO,IAAA,CAAK,OAAA;YAAA,2BAAA;YAGZ,MAAM,IAAA,CAAK,QAAA,CAAS,IAAA;YACpB,YAAY,IAAA,CAAK,QAAA,CAAS,SAAA;YAC1B,UACE,OAAO,IAAA,CAAK,QAAA,CAAS,QAAA,KAAa,WAC9B,IAAA,CAAK,QAAA,CAAS,QAAA,GACd,OAAO,IAAA,CAAK,QAAA,CAAS,QAAA,KAAa,YAChC,IAAA,CAAK,QAAA,CAAS,QAAA,GACZ,IACA,KAAA,IACF,KAAA;YACR,QAAQ,IAAA,CAAK,QAAA,CAAS,MAAA;YACtB,MAAM,IAAA,CAAK,QAAA,CAAS,IAAA;YAAA,yBAAA;YAGpB,YAAY;YACZ;YACA,OAAO;YACP,mBAAmB;YACnB,kBAAkB;YAClB;YAAA,UAAA;YAGA,QAAQ;YAAA,kBAAA;YAGR,MAAM,KAAK,MAAA,GAAS,IAAI,OAAO,KAAA;QACjC;QAEA,OAAQ,MAAM;YACZ,KAAK;gBAAW;oBACd,IAAA,CAAI,KAAA,KAAK,KAAA,KAAL,OAAA,KAAA,IAAA,GAAY,MAAA,EAAQ;wBACtB,MAAM,kOAAIC,gCAAAA,CAA8B;4BACtC,eAAe;wBACjB,CAAC;oBACH;oBAEA,IAAI,KAAK,UAAA,EAAY;wBACnB,MAAM,kOAAIA,gCAAAA,CAA8B;4BACtC,eAAe;wBACjB,CAAC;oBACH;oBAEA,OAAO;wBAAE,MAAM;wBAAU;oBAAS;gBACpC;YAEA,KAAK;gBAAe;oBAClB,MAAM,kOAAIA,gCAAAA,CAA8B;wBACtC,eAAe;oBACjB,CAAC;gBACH;YAEA,KAAK;gBAAe;oBAClB,MAAM,kOAAIA,gCAAAA,CAA8B;wBACtC,eAAe;oBACjB,CAAC;gBACH;YAEA;gBAAS;oBACP,MAAM,mBAA0B;oBAChC,MAAM,IAAI,MAAM,CAAA,kBAAA,EAAqB,gBAAgB,EAAE;gBACzD;QACF;IACF;IAEA,MAAM,WACJ,OAAA,EAC6D;QAC7D,MAAM,EAAE,IAAA,EAAM,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,OAAO;QAE/C,MAAM,EACJ,eAAA,EACA,OAAO,QAAA,EACP,UAAU,WAAA,EACZ,GAAI,8QAAMC,gBAAAA,EAAc;YACtB,KAAK,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI;gBACnB,MAAM;gBACN,SAAS,IAAA,CAAK,OAAA;YAChB,CAAC;YACD,UAASC,wRAAAA,EAAe,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,QAAQ,OAAO;YAC9D,MAAM;YACN,uBAAuB;YACvB,4BAA2BC,mSAAAA,EACzB;YAEF,aAAa,QAAQ,WAAA;YACrB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;QACrB,CAAC;QAED,MAAM,EAAE,QAAQ,SAAA,EAAW,GAAG,YAAY,CAAA,GAAI;QAC9C,MAAM,SAAS,SAAS,OAAA,CAAQ,CAAC,CAAA;QAEjC,OAAO;YACL,MAAM,OAAO,IAAA;YACb,OAAO;gBACL,cAAc,SAAS,KAAA,CAAM,aAAA;gBAC7B,kBAAkB,SAAS,KAAA,CAAM,iBAAA;YACnC;YACA,cAAc,sBAAsB,OAAO,aAAa;YACxD,UAAU,4BAA4B,OAAO,QAAQ;YACrD,SAAS;gBAAE;gBAAW;YAAY;YAClC,aAAa;gBAAE,SAAS;gBAAiB,MAAM;YAAY;YAC3D,UAAU,oBAAoB,QAAQ;YACtC;YACA,SAAS;gBAAE,MAAM,KAAK,SAAA,CAAU,IAAI;YAAE;QACxC;IACF;IAEA,MAAM,SACJ,OAAA,EAC2D;QAC3D,MAAM,EAAE,IAAA,EAAM,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,OAAO;QAE/C,MAAM,OAAO;YACX,GAAG,IAAA;YACH,QAAQ;YAAA,iEAAA;YAGR,gBACE,IAAA,CAAK,MAAA,CAAO,aAAA,KAAkB,WAC1B;gBAAE,eAAe;YAAK,IACtB,KAAA;QACR;QAEA,MAAM,EAAE,eAAA,EAAiB,OAAO,QAAA,CAAS,CAAA,GAAI,8QAAMF,gBAAAA,EAAc;YAC/D,KAAK,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI;gBACnB,MAAM;gBACN,SAAS,IAAA,CAAK,OAAA;YAChB,CAAC;YACD,iRAASC,iBAAAA,EAAe,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,QAAQ,OAAO;YAC9D;YACA,uBAAuB;YACvB,mSAA2BE,mCAAAA,EACzB;YAEF,aAAa,QAAQ,WAAA;YACrB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;QACrB,CAAC;QAED,MAAM,EAAE,QAAQ,SAAA,EAAW,GAAG,YAAY,CAAA,GAAI;QAE9C,IAAI,eAA4C;QAChD,IAAI,QAA4D;YAC9D,cAAc,OAAO,GAAA;YACrB,kBAAkB,OAAO,GAAA;QAC3B;QACA,IAAI;QACJ,IAAI,eAAe;QAEnB,OAAO;YACL,QAAQ,SAAS,WAAA,CACf,IAAI,gBAGF;gBACA,WAAU,KAAA,EAAO,UAAA,EAAY;oBAE3B,IAAI,CAAC,MAAM,OAAA,EAAS;wBAClB,eAAe;wBACf,WAAW,OAAA,CAAQ;4BAAE,MAAM;4BAAS,OAAO,MAAM,KAAA;wBAAM,CAAC;wBACxD;oBACF;oBAEA,MAAM,QAAQ,MAAM,KAAA;oBAGpB,IAAI,WAAW,OAAO;wBACpB,eAAe;wBACf,WAAW,OAAA,CAAQ;4BAAE,MAAM;4BAAS,OAAO,MAAM,KAAA;wBAAM,CAAC;wBACxD;oBACF;oBAEA,IAAI,cAAc;wBAChB,eAAe;wBAEf,WAAW,OAAA,CAAQ;4BACjB,MAAM;4BACN,GAAG,oBAAoB,KAAK,CAAA;wBAC9B,CAAC;oBACH;oBAEA,IAAI,MAAM,KAAA,IAAS,MAAM;wBACvB,QAAQ;4BACN,cAAc,MAAM,KAAA,CAAM,aAAA;4BAC1B,kBAAkB,MAAM,KAAA,CAAM,iBAAA;wBAChC;oBACF;oBAEA,MAAM,SAAS,MAAM,OAAA,CAAQ,CAAC,CAAA;oBAE9B,IAAA,CAAI,UAAA,OAAA,KAAA,IAAA,OAAQ,aAAA,KAAiB,MAAM;wBACjC,eAAe,sBAAsB,OAAO,aAAa;oBAC3D;oBAEA,IAAA,CAAI,UAAA,OAAA,KAAA,IAAA,OAAQ,IAAA,KAAQ,MAAM;wBACxB,WAAW,OAAA,CAAQ;4BACjB,MAAM;4BACN,WAAW,OAAO,IAAA;wBACpB,CAAC;oBACH;oBAEA,MAAM,iBAAiB,4BACrB,UAAA,OAAA,KAAA,IAAA,OAAQ,QAAA;oBAEV,IAAI,kBAAA,OAAA,KAAA,IAAA,eAAgB,MAAA,EAAQ;wBAC1B,IAAI,aAAa,KAAA,EAAW,CAAA,WAAW,CAAC,CAAA;wBACxC,SAAS,IAAA,CAAK,GAAG,cAAc;oBACjC;gBACF;gBAEA,OAAM,UAAA,EAAY;oBAChB,WAAW,OAAA,CAAQ;wBACjB,MAAM;wBACN;wBACA;wBACA;oBACF,CAAC;gBACH;YACF,CAAC;YAEH,SAAS;gBAAE;gBAAW;YAAY;YAClC,aAAa;gBAAE,SAAS;YAAgB;YACxC;YACA,SAAS;gBAAE,MAAM,KAAK,SAAA,CAAU,IAAI;YAAE;QACxC;IACF;AACF;AAIA,IAAM,uNAAiCC,IAAAA,CAAE,MAAA,CAAO;IAC9C,0LAAIA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACvB,SAASA,0LAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC5B,6LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC1B,+LAASA,IAAAA,CAAE,KAAA,uLACTA,IAAAA,CAAE,MAAA,CAAO;QACP,4LAAMA,IAAAA,CAAE,MAAA,CAAO;QACf,qMAAeA,IAAAA,CAAE,MAAA,CAAO;QACxB,gMAAUA,IAAAA,CACP,MAAA,CAAO;YACN,8LAAQA,IAAAA,CAAE,KAAA,uLAAMA,IAAAA,CAAE,MAAA,CAAO,CAAC;YAC1B,gBAAgBA,0LAAAA,CAAE,KAAA,uLAAMA,IAAAA,CAAE,MAAA,CAAO,CAAC;YAClC,oMAAcA,IAAAA,CAAE,KAAA,uLAAMA,IAAAA,CAAE,MAAA,sLAAOA,KAAAA,CAAE,MAAA,CAAO,yLAAGA,IAAAA,CAAE,MAAA,CAAO,CAAC,CAAC,EAAE,QAAA,CAAS;QACnE,CAAC,EACA,OAAA,CAAQ;IACb,CAAC;IAEH,OAAOA,0LAAAA,CAAE,MAAA,CAAO;QACd,qMAAeA,IAAAA,CAAE,MAAA,CAAO;QACxB,yMAAmBA,IAAAA,CAAE,MAAA,CAAO;IAC9B,CAAC;AACH,CAAC;AAID,IAAM,oNAA8BA,IAAAA,CAAE,KAAA,CAAM;IAC1CA,0LAAAA,CAAE,MAAA,CAAO;QACP,0LAAIA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QACvB,+LAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QAC5B,6LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QAC1B,+LAASA,IAAAA,CAAE,KAAA,uLACTA,IAAAA,CAAE,MAAA,CAAO;YACP,4LAAMA,IAAAA,CAAE,MAAA,CAAO;YACf,qMAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YAClC,6LAAOA,IAAAA,CAAE,MAAA,CAAO;YAChB,gMAAUA,IAAAA,CACP,MAAA,CAAO;gBACN,8LAAQA,IAAAA,CAAE,KAAA,CAAMA,0LAAAA,CAAE,MAAA,CAAO,CAAC;gBAC1B,sMAAgBA,IAAAA,CAAE,KAAA,uLAAMA,IAAAA,CAAE,MAAA,CAAO,CAAC;gBAClC,oMAAcA,IAAAA,CAAE,KAAA,uLAAMA,IAAAA,CAAE,MAAA,uLAAOA,IAAAA,CAAE,MAAA,CAAO,yLAAGA,IAAAA,CAAE,MAAA,CAAO,CAAC,CAAC,EAAE,QAAA,CAAS;YACnE,CAAC,EACA,OAAA,CAAQ;QACb,CAAC;QAEH,6LAAOA,IAAAA,CACJ,MAAA,CAAO;YACN,qMAAeA,IAAAA,CAAE,MAAA,CAAO;YACxB,yMAAmBA,IAAAA,CAAE,MAAA,CAAO;QAC9B,CAAC,EACA,OAAA,CAAQ;IACb,CAAC;IACD;CACD;;;;AGhXM,IAAM,uBAAN,MAA+D;IAmBpE,YACE,OAAA,EACA,QAAA,EACA,MAAA,CACA;QAtBF,IAAA,CAAS,oBAAA,GAAuB;QAuB9B,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,MAAA,GAAS;IAChB;IApBA,IAAI,WAAmB;QACrB,OAAO,IAAA,CAAK,MAAA,CAAO,QAAA;IACrB;IAEA,IAAI,uBAA+B;QA5BrC,IAAA;QA6BI,OAAA,CAAO,KAAA,IAAA,CAAK,QAAA,CAAS,oBAAA,KAAd,OAAA,KAAsC;IAC/C;IAEA,IAAI,wBAAiC;QAhCvC,IAAA;QAiCI,OAAA,CAAO,KAAA,IAAA,CAAK,QAAA,CAAS,qBAAA,KAAd,OAAA,KAAuC;IAChD;IAYA,MAAM,QAAQ,EACZ,MAAA,EACA,OAAA,EACA,WAAA,EACF,EAEE;QACA,IAAI,OAAO,MAAA,GAAS,IAAA,CAAK,oBAAA,EAAsB;YAC7C,MAAM,kOAAI,qCAAA,CAAmC;gBAC3C,UAAU,IAAA,CAAK,QAAA;gBACf,SAAS,IAAA,CAAK,OAAA;gBACd,sBAAsB,IAAA,CAAK,oBAAA;gBAC3B;YACF,CAAC;QACH;QAEA,MAAM,EAAE,eAAA,EAAiB,OAAO,QAAA,CAAS,CAAA,GAAI,MAAMK,wRAAAA,EAAc;YAC/D,KAAK,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI;gBACnB,MAAM;gBACN,SAAS,IAAA,CAAK,OAAA;YAChB,CAAC;YACD,iRAASC,iBAAAA,EAAe,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,OAAO;YACtD,MAAM;gBACJ,OAAO,IAAA,CAAK,OAAA;gBACZ,OAAO;gBACP,iBAAiB;gBACjB,YAAY,IAAA,CAAK,QAAA,CAAS,UAAA;gBAC1B,MAAM,IAAA,CAAK,QAAA,CAAS,IAAA;YACtB;YACA,uBAAuB;YACvB,2BAA2BC,oSAAAA,EACzB;YAEF;YACA,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;QACrB,CAAC;QAED,OAAO;YACL,YAAY,SAAS,IAAA,CAAK,GAAA,CAAI,CAAA,OAAQ,KAAK,SAAS;YACpD,OAAO,SAAS,KAAA,GACZ;gBAAE,QAAQ,SAAS,KAAA,CAAM,aAAA;YAAc,IACvC,KAAA;YACJ,aAAa;gBAAE,SAAS;YAAgB;QAC1C;IACF;AACF;AAIA,IAAM,0NAAoCC,IAAAA,CAAE,MAAA,CAAO;IACjD,4LAAMA,IAAAA,CAAE,KAAA,sLAAMA,KAAAA,CAAE,MAAA,CAAO;QAAE,iMAAWA,IAAAA,CAAE,KAAA,uLAAMA,IAAAA,CAAE,MAAA,CAAO,CAAC;IAAE,CAAC,CAAC;IAC1D,6LAAOA,IAAAA,CAAE,MAAA,CAAO;QAAE,oMAAeA,KAAAA,CAAE,MAAA,CAAO;IAAE,CAAC,EAAE,OAAA,CAAQ;AACzD,CAAC;;;;AE3FM,IAAM,wBAA4D;IACvE,YAAY;IACZ,YAAY;IACZ,eAAe;AACjB;AAEO,IAAM,2BAA2B,aAAA,GAAA,IAAI,IAAI;IAAC,aAAa;CAAC;;ADSxD,IAAM,mBAAN,MAA+C;IAapD,YACW,OAAA,EACQ,QAAA,EACA,MAAA,CACjB;QAHS,IAAA,CAAA,OAAA,GAAA;QACQ,IAAA,CAAA,QAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QAfnB,IAAA,CAAS,oBAAA,GAAuB;IAgB7B;IAdH,IAAI,mBAA2B;QAzBjC,IAAA,IAAA;QA0BI,OAAA,CACE,KAAA,CAAA,KAAA,IAAA,CAAK,QAAA,CAAS,gBAAA,KAAd,OAAA,KAAkC,qBAAA,CAAsB,IAAA,CAAK,OAAO,CAAA,KAApE,OAAA,KAAyE;IAE7E;IAEA,IAAI,WAAmB;QACrB,OAAO,IAAA,CAAK,MAAA,CAAO,QAAA;IACrB;IAQA,MAAM,WAAW,EACf,MAAA,EACA,CAAA,EACA,IAAA,EACA,WAAA,EACA,IAAA,EACA,eAAA,EACA,OAAA,EACA,WAAA,EACF,EAEE;QApDJ,IAAA,IAAA,IAAA,IAAA;QAqDI,MAAM,WAA2C,CAAC,CAAA;QAElD,IAAI,eAAe,MAAM;YACvB,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;gBACT,SACE;YACJ,CAAC;QACH;QAEA,IAAI,QAAQ,MAAM;YAChB,SAAS,IAAA,CAAK;gBAAE,MAAM;gBAAuB,SAAS;YAAO,CAAC;QAChE;QAEA,MAAM,cAAA,CAAc,KAAA,CAAA,KAAA,CAAA,KAAA,IAAA,CAAK,MAAA,CAAO,SAAA,KAAZ,OAAA,KAAA,IAAA,GAAuB,WAAA,KAAvB,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,GAAA,KAAA,OAAA,KAA0C,aAAA,GAAA,IAAI,KAAK;QACvE,MAAM,EAAE,OAAO,QAAA,EAAU,eAAA,CAAgB,CAAA,GAAI,8QAAMK,gBAAAA,EAAc;YAC/D,KAAK,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI;gBACnB,MAAM;gBACN,SAAS,IAAA,CAAK,OAAA;YAChB,CAAC;YACD,iRAASC,iBAAAA,EAAe,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,OAAO;YACtD,MAAM;gBACJ,OAAO,IAAA,CAAK,OAAA;gBACZ;gBACA;gBACA;gBACA,GAAA,CAAI,KAAA,gBAAgB,MAAA,KAAhB,OAAA,KAA0B,CAAC,CAAA;gBAC/B,GAAI,CAAC,yBAAyB,GAAA,CAAI,IAAA,CAAK,OAAO,IAC1C;oBAAE,iBAAiB;gBAAW,IAC9B,CAAC,CAAA;YACP;YACA,uBAAuB;YACvB,mSAA2BC,4BAAAA,EACzB;YAEF;YACA,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;QACrB,CAAC;QAED,OAAO;YACL,QAAQ,SAAS,IAAA,CAAK,GAAA,CAAI,CAAA,OAAQ,KAAK,QAAQ;YAC/C;YACA,UAAU;gBACR,WAAW;gBACX,SAAS,IAAA,CAAK,OAAA;gBACd,SAAS;YACX;QACF;IACF;AACF;AAIA,IAAM,4BAA4BC,0LAAAA,CAAE,MAAA,CAAO;IACzC,4LAAMA,IAAAA,CAAE,KAAA,uLAAMA,IAAAA,CAAE,MAAA,CAAO;QAAE,gMAAUA,IAAAA,CAAE,MAAA,CAAO;IAAE,CAAC,CAAC;AAClD,CAAC;;;AExFD,IAAM,oNAA8BI,IAAAA,CAAE,MAAA,CAAO;IAC3C,+LAASA,IAAAA,CAAE,KAAA,uLAAMA,IAAAA,CAAE,MAAA,CAAO,CAAC,EAAE,OAAA,CAAQ;IACrC,gMAAUA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC7B,8LAAQA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC3B,mMAAaA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC,EAAE,GAAA,CAAI,CAAC,EAAE,OAAA,CAAQ,EAAE,OAAA,CAAQ,CAAC;IACzD,8MAAwBA,IAAAA,CACrB,KAAA,uLAAMA,IAAAA,CAAE,IAAA,CAAK;QAAC;QAAQ,SAAS;KAAC,CAAC,EACjC,OAAA,CAAQ,EACR,OAAA,CAAQ;QAAC,SAAS;KAAC;AACxB,CAAC;AAkBD,IAAM,cAAc;IAClB,WAAW;IACX,QAAQ;IACR,UAAU;IACV,aAAa;IACb,YAAY;IACZ,SAAS;IACT,WAAW;IACX,SAAS;IACT,SAAS;IACT,UAAU;IACV,OAAO;IACP,QAAQ;IACR,OAAO;IACP,SAAS;IACT,UAAU;IACV,SAAS;IACT,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,OAAO;IACP,WAAW;IACX,WAAW;IACX,YAAY;IACZ,SAAS;IACT,UAAU;IACV,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,YAAY;IACZ,YAAY;IACZ,OAAO;IACP,SAAS;IACT,OAAO;IACP,QAAQ;IACR,WAAW;IACX,SAAS;IACT,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,SAAS;IACT,SAAS;IACT,QAAQ;IACR,WAAW;IACX,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,OAAO;IACP,MAAM;IACN,SAAS;IACT,WAAW;IACX,MAAM;IACN,YAAY;IACZ,OAAO;AACT;AAEO,IAAM,2BAAN,MAA+D;IAOpE,YACW,OAAA,EACQ,MAAA,CACjB;QAFS,IAAA,CAAA,OAAA,GAAA;QACQ,IAAA,CAAA,MAAA,GAAA;QARnB,IAAA,CAAS,oBAAA,GAAuB;IAS7B;IAPH,IAAI,WAAmB;QACrB,OAAO,IAAA,CAAK,MAAA,CAAO,QAAA;IACrB;IAOQ,QAAQ,EACd,KAAA,EACA,SAAA,EACA,eAAA,EACF,EAAmC;QA5HrC,IAAA,IAAA,IAAA,IAAA,IAAA;QA6HI,MAAM,WAA8C,CAAC,CAAA;QAGrD,MAAM,gBAAgB,+RAAA,EAAqB;YACzC,UAAU;YACV;YACA,QAAQ;QACV,CAAC;QAGD,MAAM,WAAW,IAAI,SAAS;QAC9B,MAAM,OACJ,iBAAiB,aACb,IAAI,KAAK;YAAC,KAAK;SAAC,IAChB,IAAI,KAAK;aAAC,mSAAA,EAA0B,KAAK,CAAC;SAAC;QAEjD,SAAS,MAAA,CAAO,SAAS,IAAA,CAAK,OAAO;QACrC,SAAS,MAAA,CAAO,QAAQ,IAAI,KAAK;YAAC,IAAI;SAAA,EAAG,SAAS;YAAE,MAAM;QAAU,CAAC,CAAC;QAGtE,IAAI,eAAe;YACjB,MAAM,4BAA6D;gBACjE,SAAA,CAAS,KAAA,cAAc,OAAA,KAAd,OAAA,KAAyB,KAAA;gBAClC,UAAA,CAAU,KAAA,cAAc,QAAA,KAAd,OAAA,KAA0B,KAAA;gBACpC,QAAA,CAAQ,KAAA,cAAc,MAAA,KAAd,OAAA,KAAwB,KAAA;gBAChC,aAAA,CAAa,KAAA,cAAc,WAAA,KAAd,OAAA,KAA6B,KAAA;gBAC1C,yBAAA,CACE,KAAA,cAAc,sBAAA,KAAd,OAAA,KAAwC,KAAA;YAC5C;YAEA,IAAA,MAAW,OAAO,0BAA2B;gBAC3C,MAAM,QACJ,yBAAA,CACE,GACF,CAAA;gBACF,IAAI,UAAU,KAAA,GAAW;oBACvB,SAAS,MAAA,CAAO,KAAK,OAAO,KAAK,CAAC;gBACpC;YACF;QACF;QAEA,OAAO;YACL;YACA;QACF;IACF;IAEA,MAAM,WACJ,OAAA,EACkE;QA9KtE,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;QA+KI,MAAM,cAAA,CAAc,KAAA,CAAA,KAAA,CAAA,KAAA,IAAA,CAAK,MAAA,CAAO,SAAA,KAAZ,OAAA,KAAA,IAAA,GAAuB,WAAA,KAAvB,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,GAAA,KAAA,OAAA,KAA0C,aAAA,GAAA,IAAI,KAAK;QACvE,MAAM,EAAE,QAAA,EAAU,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,OAAO;QAEnD,MAAM,EACJ,OAAO,QAAA,EACP,eAAA,EACA,UAAU,WAAA,EACZ,GAAI,8QAAM,oBAAA,EAAkB;YAC1B,KAAK,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI;gBACnB,MAAM;gBACN,SAAS,IAAA,CAAK,OAAA;YAChB,CAAC;YACD,aAASC,qRAAAA,EAAe,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,QAAQ,OAAO;YAC9D;YACA,uBAAuB;YACvB,mSAA2BC,4BAAAA,EACzB;YAEF,aAAa,QAAQ,WAAA;YACrB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;QACrB,CAAC;QAED,MAAM,WACJ,SAAS,QAAA,IAAY,QAAQ,SAAS,QAAA,IAAY,cAC9C,WAAA,CAAY,SAAS,QAAoC,CAAA,GACzD,KAAA;QAEN,OAAO;YACL,MAAM,SAAS,IAAA;YACf,UAAA,CACE,KAAA,CAAA,KAAA,SAAS,KAAA,KAAT,OAAA,KAAA,IAAA,GAAgB,GAAA,CAAI,CAAA,OAAA,CAAS;oBAC3B,MAAM,KAAK,IAAA;oBACX,aAAa,KAAK,KAAA;oBAClB,WAAW,KAAK,GAAA;gBAClB,CAAA,EAAA,KAJA,OAAA,KAIO,CAAC,CAAA;YACV;YACA,mBAAA,CAAmB,KAAA,SAAS,QAAA,KAAT,OAAA,KAAqB,KAAA;YACxC;YACA,UAAU;gBACR,WAAW;gBACX,SAAS,IAAA,CAAK,OAAA;gBACd,SAAS;gBACT,MAAM;YACR;QACF;IACF;AACF;AAEA,IAAM,0NAAoCF,IAAAA,CAAE,MAAA,CAAO;IACjD,4LAAMA,IAAAA,CAAE,MAAA,CAAO;IACf,gMAAUA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC7B,gMAAUA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC7B,6LAAOA,IAAAA,CACJ,KAAA,uLACCA,IAAAA,CAAE,MAAA,CAAO;QACP,4LAAMA,IAAAA,CAAE,MAAA,CAAO;QACf,OAAOA,0LAAAA,CAAE,MAAA,CAAO;QAChB,2LAAKA,IAAAA,CAAE,MAAA,CAAO;IAChB,CAAC,GAEF,OAAA,CAAQ;AACb,CAAC;;;;;AEpOM,SAAS,iCAAiC,EAC/C,MAAA,EACA,iBAAA,EACF,EAME;IACA,MAAM,WAAkC,CAAC,CAAA;IACzC,MAAM,WAA8C,CAAC,CAAA;IAErD,KAAA,MAAW,EAAE,IAAA,EAAM,OAAA,CAAQ,CAAA,IAAK,OAAQ;QACtC,OAAQ,MAAM;YACZ,KAAK;gBAAU;oBACb,OAAQ,mBAAmB;wBACzB,KAAK;4BAAU;gCACb,SAAS,IAAA,CAAK;oCAAE,MAAM;oCAAU;gCAAQ,CAAC;gCACzC;4BACF;wBACA,KAAK;4BAAa;gCAChB,SAAS,IAAA,CAAK;oCAAE,MAAM;oCAAa;gCAAQ,CAAC;gCAC5C;4BACF;wBACA,KAAK;4BAAU;gCACb,SAAS,IAAA,CAAK;oCACZ,MAAM;oCACN,SAAS;gCACX,CAAC;gCACD;4BACF;wBACA;4BAAS;gCACP,MAAM,mBAA0B;gCAChC,MAAM,IAAI,MACR,CAAA,iCAAA,EAAoC,gBAAgB,EAAA;4BAExD;oBACF;oBACA;gBACF;YAEA,KAAK;gBAAQ;oBACX,SAAS,IAAA,CAAK;wBACZ,MAAM;wBACN,SAAS,QAAQ,GAAA,CAAI,CAAC,MAAM,UAAU;4BArDhD,IAAA,IAAA,IAAA,IAAA;4BAsDY,OAAQ,KAAK,IAAA,EAAM;gCACjB,KAAK;oCAAQ;wCACX,OAAO;4CAAE,MAAM;4CAAc,MAAM,KAAK,IAAA;wCAAK;oCAC/C;gCACA,KAAK;oCAAS;wCACZ,OAAO;4CACL,MAAM;4CACN,WACE,KAAK,KAAA,YAAiB,MAClB,KAAK,KAAA,CAAM,QAAA,CAAS,IACpB,CAAA,KAAA,EAAA,CACE,KAAA,KAAK,QAAA,KAAL,OAAA,KAAiB,YACnB,CAAA,QAAA,EAAWW,oSAAAA,EAA0B,KAAK,KAAK,CAAC,EAAA;4CAAA,0CAAA;4CAGtD,QAAA,CAAQ,KAAA,CAAA,KAAA,KAAK,gBAAA,KAAL,OAAA,KAAA,IAAA,GAAuB,MAAA,KAAvB,OAAA,KAAA,IAAA,GAA+B,WAAA;wCACzC;oCACF;gCACA,KAAK;oCAAQ;wCACX,IAAI,KAAK,IAAA,YAAgB,KAAK;4CAE5B,MAAM,kOAAID,gCAAAA,CAA8B;gDACtC,eAAe;4CACjB,CAAC;wCACH;wCAEA,OAAQ,KAAK,QAAA,EAAU;4CACrB,KAAK;gDAAmB;oDACtB,OAAO;wDACL,MAAM;wDACN,UAAA,CAAU,KAAA,KAAK,QAAA,KAAL,OAAA,KAAiB,CAAA,KAAA,EAAQ,KAAK,CAAA,IAAA,CAAA;wDACxC,WAAW,CAAA,4BAAA,EAA+B,KAAK,IAAI,EAAA;oDACrD;gDACF;4CACA;gDAAS;oDACP,MAAM,kOAAIA,gCAAAA,CAA8B;wDACtC,eACE;oDACJ,CAAC;gDACH;wCACF;oCACF;4BACF;wBACF,CAAC;oBACH,CAAC;oBAED;gBACF;YAEA,KAAK;gBAAa;oBAChB,KAAA,MAAW,QAAQ,QAAS;wBAC1B,OAAQ,KAAK,IAAA,EAAM;4BACjB,KAAK;gCAAQ;oCACX,SAAS,IAAA,CAAK;wCACZ,MAAM;wCACN,SAAS;4CAAC;gDAAE,MAAM;gDAAe,MAAM,KAAK,IAAA;4CAAK,CAAC;yCAAA;oCACpD,CAAC;oCACD;gCACF;4BACA,KAAK;gCAAa;oCAChB,SAAS,IAAA,CAAK;wCACZ,MAAM;wCACN,SAAS,KAAK,UAAA;wCACd,MAAM,KAAK,QAAA;wCACX,WAAW,KAAK,SAAA,CAAU,KAAK,IAAI;oCACrC,CAAC;oCACD;gCACF;wBACF;oBACF;oBAEA;gBACF;YAEA,KAAK;gBAAQ;oBACX,KAAA,MAAW,QAAQ,QAAS;wBAC1B,SAAS,IAAA,CAAK;4BACZ,MAAM;4BACN,SAAS,KAAK,UAAA;4BACd,QAAQ,KAAK,SAAA,CAAU,KAAK,MAAM;wBACpC,CAAC;oBACH;oBAEA;gBACF;YAEA;gBAAS;oBACP,MAAM,mBAA0B;oBAChC,MAAM,IAAI,MAAM,CAAA,kBAAA,EAAqB,gBAAgB,EAAE;gBACzD;QACF;IACF;IAEA,OAAO;QAAE;QAAU;IAAS;AAC9B;;AClJO,SAAS,8BAA8B,EAC5C,YAAA,EACA,YAAA,EACF,EAGgC;IAC9B,OAAQ,cAAc;QACpB,KAAK,KAAA;QACL,KAAK;YACH,OAAO,eAAe,eAAe;QACvC,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,eAAe,eAAe;IACzC;AACF;;ACbO,SAAS,sBAAsB,EACpC,IAAA,EACA,MAAA,EACF,EAcE;IAxBF,IAAA;IA0BE,MAAM,QAAA,CAAA,CAAQ,KAAA,KAAK,KAAA,KAAL,OAAA,KAAA,IAAA,GAAY,MAAA,IAAS,KAAK,KAAA,GAAQ,KAAA;IAEhD,MAAM,eAA6C,CAAC,CAAA;IAEpD,IAAI,SAAS,MAAM;QACjB,OAAO;YAAE,OAAO,KAAA;YAAW,aAAa,KAAA;YAAW;QAAa;IAClE;IAEA,MAAM,aAAa,KAAK,UAAA;IAExB,MAAMG,eAA0C,CAAC,CAAA;IAEjD,KAAA,MAAW,QAAQ,MAAO;QACxB,OAAQ,KAAK,IAAA,EAAM;YACjB,KAAK;gBACHA,aAAY,IAAA,CAAK;oBACf,MAAM;oBACN,MAAM,KAAK,IAAA;oBACX,aAAa,KAAK,WAAA;oBAClB,YAAY,KAAK,UAAA;oBACjB,QAAQ,SAAS,OAAO,KAAA;gBAC1B,CAAC;gBACD;YACF,KAAK;gBACH,OAAQ,KAAK,EAAA,EAAI;oBACf,KAAK;wBACHA,aAAY,IAAA,CAAK;4BACf,MAAM;4BACN,qBAAqB,KAAK,IAAA,CAAK,iBAAA;4BAI/B,eAAe,KAAK,IAAA,CAAK,YAAA;wBAK3B,CAAC;wBACD;oBACF;wBACE,aAAa,IAAA,CAAK;4BAAE,MAAM;4BAAoB;wBAAK,CAAC;wBACpD;gBACJ;gBACA;YACF;gBACE,aAAa,IAAA,CAAK;oBAAE,MAAM;oBAAoB;gBAAK,CAAC;gBACpD;QACJ;IACF;IAEA,IAAI,cAAc,MAAM;QACtB,OAAO;YAAE,OAAOA;YAAa,aAAa,KAAA;YAAW;QAAa;IACpE;IAEA,MAAM,OAAO,WAAW,IAAA;IAExB,OAAQ,MAAM;QACZ,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;gBAAE,OAAOA;gBAAa,aAAa;gBAAM;YAAa;QAC/D,KAAK;YAAQ;gBACX,IAAI,WAAW,QAAA,KAAa,sBAAsB;oBAChD,OAAO;wBACL,OAAOA;wBACP,aAAa;4BACX,MAAM;wBACR;wBACA;oBACF;gBACF;gBACA,OAAO;oBACL,OAAOA;oBACP,aAAa;wBACX,MAAM;wBACN,MAAM,WAAW,QAAA;oBACnB;oBACA;gBACF;YACF;QACA;YAAS;gBACP,MAAM,mBAA0B;gBAChC,MAAM,kOAAID,gCAAAA,CAA8B;oBACtC,eAAe,CAAA,8BAAA,EAAiC,gBAAgB,EAAA;gBAClE,CAAC;YACH;IACF;AACF;;AH1FO,IAAM,+BAAN,MAA8D;IASnE,YAAY,OAAA,EAAiC,MAAA,CAAsB;QARnE,IAAA,CAAS,oBAAA,GAAuB;QAChC,IAAA,CAAS,2BAAA,GAA8B;QACvC,IAAA,CAAS,yBAAA,GAA4B;QAOnC,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,MAAA,GAAS;IAChB;IAEA,IAAI,WAAmB;QACrB,OAAO,IAAA,CAAK,MAAA,CAAO,QAAA;IACrB;IAEQ,QAAQ,EACd,IAAA,EACA,SAAA,EACA,WAAA,EACA,aAAA,EACA,IAAA,EACA,IAAA,EACA,eAAA,EACA,gBAAA,EACA,IAAA,EACA,MAAA,EACA,gBAAA,EACA,cAAA,EACF,EAAiD;QAtDnD,IAAA,IAAA,IAAA;QAuDI,MAAM,WAAyC,CAAC,CAAA;QAChD,MAAM,cAAc,wBAAwB,IAAA,CAAK,OAAO;QACxD,MAAM,OAAO,KAAK,IAAA;QAElB,IAAI,QAAQ,MAAM;YAChB,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,IAAI,QAAQ,MAAM;YAChB,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,IAAI,mBAAmB,MAAM;YAC3B,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,IAAI,oBAAoB,MAAM;YAC5B,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,IAAI,iBAAiB,MAAM;YACzB,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,MAAM,EAAE,QAAA,EAAU,UAAU,eAAA,CAAgB,CAAA,GAC1C,iCAAiC;YAC/B;YACA,mBAAmB,YAAY,iBAAA;QACjC,CAAC;QAEH,SAAS,IAAA,CAAK,GAAG,eAAe;QAEhC,MAAM,gBAAgBE,+RAAAA,EAAqB;YACzC,UAAU;YACV,iBAAiB;YACjB,QAAQ;QACV,CAAC;QAED,MAAM,WAAA,CAAW,KAAA,iBAAA,OAAA,KAAA,IAAA,cAAe,aAAA,KAAf,OAAA,KAAgC;QAEjD,MAAM,WAAW;YACf,OAAO,IAAA,CAAK,OAAA;YACZ,OAAO;YACP;YACA,OAAO;YACP,mBAAmB;YAEnB,GAAA,CAAI,kBAAA,OAAA,KAAA,IAAA,eAAgB,IAAA,MAAS,UAAU;gBACrC,MAAM;oBACJ,QACE,eAAe,MAAA,IAAU,OACrB;wBACE,MAAM;wBACN,QAAQ;wBACR,MAAA,CAAM,KAAA,eAAe,IAAA,KAAf,OAAA,KAAuB;wBAC7B,aAAa,eAAe,WAAA;wBAC5B,QAAQ,eAAe,MAAA;oBACzB,IACA;wBAAE,MAAM;oBAAc;gBAC9B;YACF,CAAA;YAAA,oBAAA;YAGA,UAAU,iBAAA,OAAA,KAAA,IAAA,cAAe,QAAA;YACzB,qBAAqB,iBAAA,OAAA,KAAA,IAAA,cAAe,iBAAA;YACpC,sBAAsB,iBAAA,OAAA,KAAA,IAAA,cAAe,kBAAA;YACrC,OAAO,iBAAA,OAAA,KAAA,IAAA,cAAe,KAAA;YACtB,MAAM,iBAAA,OAAA,KAAA,IAAA,cAAe,IAAA;YACrB,cAAc,iBAAA,OAAA,KAAA,IAAA,cAAe,YAAA;YAAA,2BAAA;YAG7B,GAAI,YAAY,gBAAA,IAAA,CAAA,CACb,iBAAA,OAAA,KAAA,IAAA,cAAe,eAAA,KAAmB,QAAA,CACjC,iBAAA,OAAA,KAAA,IAAA,cAAe,gBAAA,KAAoB,IAAA,KAAS;gBAC5C,WAAW;oBACT,GAAA,CAAI,iBAAA,OAAA,KAAA,IAAA,cAAe,eAAA,KAAmB,QAAQ;wBAC5C,QAAQ,cAAc,eAAA;oBACxB,CAAA;oBACA,GAAA,CAAI,iBAAA,OAAA,KAAA,IAAA,cAAe,gBAAA,KAAoB,QAAQ;wBAC7C,SAAS,cAAc,gBAAA;oBACzB,CAAA;gBACF;YACF,CAAA;YACF,GAAI,YAAY,sBAAA,IAA0B;gBACxC,YAAY;YACd,CAAA;QACF;QAEA,IAAI,YAAY,gBAAA,EAAkB;YAGhC,IAAI,SAAS,WAAA,IAAe,MAAM;gBAChC,SAAS,WAAA,GAAc,KAAA;gBACvB,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;oBACT,SAAS;gBACX,CAAC;YACH;YAEA,IAAI,SAAS,KAAA,IAAS,MAAM;gBAC1B,SAAS,KAAA,GAAQ,KAAA;gBACjB,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;oBACT,SAAS;gBACX,CAAC;YACH;QACF;QAEA,OAAQ,MAAM;YACZ,KAAK;gBAAW;oBACd,MAAM,EAAE,KAAA,EAAO,WAAA,EAAa,YAAA,CAAa,CAAA,GAAI,sBAAsB;wBACjE;wBACA,QAAQ;oBACV,CAAC;oBAED,OAAO;wBACL,MAAM;4BACJ,GAAG,QAAA;4BACH;4BACA;wBACF;wBACA,UAAU,CAAC;+BAAG,UAAU;+BAAG,YAAY;yBAAA;oBACzC;gBACF;YAEA,KAAK;gBAAe;oBAClB,OAAO;wBACL,MAAM;4BACJ,GAAG,QAAA;4BACH,MAAM;gCACJ,QACE,KAAK,MAAA,IAAU,OACX;oCACE,MAAM;oCACN,QAAQ;oCACR,MAAA,CAAM,KAAA,KAAK,IAAA,KAAL,OAAA,KAAa;oCACnB,aAAa,KAAK,WAAA;oCAClB,QAAQ,KAAK,MAAA;gCACf,IACA;oCAAE,MAAM;gCAAc;4BAC9B;wBACF;wBACA;oBACF;gBACF;YAEA,KAAK;gBAAe;oBAClB,OAAO;wBACL,MAAM;4BACJ,GAAG,QAAA;4BACH,aAAa;gCAAE,MAAM;gCAAY,MAAM,KAAK,IAAA,CAAK,IAAA;4BAAK;4BACtD,OAAO;gCACL;oCACE,MAAM;oCACN,MAAM,KAAK,IAAA,CAAK,IAAA;oCAChB,aAAa,KAAK,IAAA,CAAK,WAAA;oCACvB,YAAY,KAAK,IAAA,CAAK,UAAA;oCACtB,QAAQ;gCACV;6BACF;wBACF;wBACA;oBACF;gBACF;YAEA;gBAAS;oBACP,MAAM,mBAA0B;oBAChC,MAAM,IAAI,MAAM,CAAA,kBAAA,EAAqB,gBAAgB,EAAE;gBACzD;QACF;IACF;IAEA,MAAM,WACJ,OAAA,EAC6D;QAtPjE,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;QAuPI,MAAM,EAAE,MAAM,IAAA,EAAM,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,OAAO;QAErD,MAAM,EACJ,eAAA,EACA,OAAO,QAAA,EACP,UAAU,WAAA,EACZ,GAAI,8QAAMC,gBAAAA,EAAc;YACtB,KAAK,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI;gBACnB,MAAM;gBACN,SAAS,IAAA,CAAK,OAAA;YAChB,CAAC;YACD,iRAASC,iBAAAA,EAAe,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,QAAQ,OAAO;YAC9D;YACA,uBAAuB;YACvB,kSAA2BC,6BAAAA,wLACzBC,IAAAA,CAAE,MAAA,CAAO;gBACP,0LAAIA,IAAAA,CAAE,MAAA,CAAO;gBACb,kMAAYA,IAAAA,CAAE,MAAA,CAAO;gBACrB,OAAOA,0LAAAA,CAAE,MAAA,CAAO;gBAChB,8LAAQA,IAAAA,CAAE,KAAA,CACRA,0LAAAA,CAAE,kBAAA,CAAmB,QAAQ;0MAC3BA,IAAAA,CAAE,MAAA,CAAO;wBACP,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,SAAS;wBACzB,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,WAAW;wBAC3B,SAASA,0LAAAA,CAAE,KAAA,uLACTA,IAAAA,CAAE,MAAA,CAAO;4BACP,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,aAAa;4BAC7B,4LAAMA,IAAAA,CAAE,MAAA,CAAO;4BACf,aAAaA,0LAAAA,CAAE,KAAA,uLACbA,IAAAA,CAAE,MAAA,CAAO;gCACP,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,cAAc;gCAC9B,mMAAaA,IAAAA,CAAE,MAAA,CAAO;gCACtB,iMAAWA,IAAAA,CAAE,MAAA,CAAO;gCACpB,2LAAKA,IAAAA,CAAE,MAAA,CAAO;gCACd,6LAAOA,IAAAA,CAAE,MAAA,CAAO;4BAClB,CAAC;wBAEL,CAAC;oBAEL,CAAC;0MACDA,IAAAA,CAAE,MAAA,CAAO;wBACP,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,eAAe;wBAC/B,+LAASA,IAAAA,CAAE,MAAA,CAAO;wBAClB,MAAMA,0LAAAA,CAAE,MAAA,CAAO;wBACf,iMAAWA,IAAAA,CAAE,MAAA,CAAO;oBACtB,CAAC;0MACDA,IAAAA,CAAE,MAAA,CAAO;wBACP,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,iBAAiB;oBACnC,CAAC;yMACDA,KAAAA,CAAE,MAAA,CAAO;wBACP,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,eAAe;oBACjC,CAAC;0MACDA,IAAAA,CAAE,MAAA,CAAO;wBACP,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,WAAW;wBAC3B,+LAASA,IAAAA,CAAE,KAAA,uLACTA,IAAAA,CAAE,MAAA,CAAO;4BACP,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,cAAc;4BAC9B,4LAAMA,IAAAA,CAAE,MAAA,CAAO;wBACjB,CAAC;oBAEL,CAAC;iBACF;gBAEH,0MAAoBA,IAAAA,CAAE,MAAA,CAAO;oBAAE,QAAQA,0LAAAA,CAAE,MAAA,CAAO;gBAAE,CAAC,EAAE,QAAA,CAAS;gBAC9D,OAAO;YACT,CAAC;YAEH,aAAa,QAAQ,WAAA;YACrB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;QACrB,CAAC;QAED,MAAM,qBAAqB,SAAS,MAAA,CACjC,MAAA,CAAO,CAAA,SAAU,OAAO,IAAA,KAAS,SAAS,EAC1C,OAAA,CAAQ,CAAA,SAAU,OAAO,OAAO,EAChC,MAAA,CAAO,CAAA,UAAW,QAAQ,IAAA,KAAS,aAAa;QAEnD,MAAM,YAAY,SAAS,MAAA,CACxB,MAAA,CAAO,CAAA,SAAU,OAAO,IAAA,KAAS,eAAe,EAChD,GAAA,CAAI,CAAA,SAAA,CAAW;gBACd,cAAc;gBACd,YAAY,OAAO,OAAA;gBACnB,UAAU,OAAO,IAAA;gBACjB,MAAM,OAAO,SAAA;YACf,CAAA,CAAE;QAEJ,MAAM,mBAAA,CACJ,KAAA,CAAA,KAAA,SAAS,MAAA,CAAO,IAAA,CAAK,CAAA,OAAQ,KAAK,IAAA,KAAS,WAAW,CAAA,KAAtD,OAAA,KAAA,IAAA,GAAyD,OAAA,KAAzD,OAAA,KAAoE;QAEtE,OAAO;YACL,MAAM,mBAAmB,GAAA,CAAI,CAAA,UAAW,QAAQ,IAAI,EAAE,IAAA,CAAK,IAAI;YAC/D,SAAS,mBAAmB,OAAA,CAAQ,CAAA,UAClC,QAAQ,WAAA,CAAY,GAAA,CAAI,CAAA,eAAW;oBAlV3C,IAAAC,KAAAC,KAAAC;oBAkV+C,OAAA;wBACrC,YAAY;wBACZ,IAAA,CAAIA,MAAAA,CAAAD,MAAAA,CAAAD,MAAA,IAAA,CAAK,MAAA,EAAO,UAAA,KAAZ,OAAA,KAAA,IAAAC,IAAA,IAAA,CAAAD,IAAAA,KAAA,OAAAE,MAA8BC,qRAAAA,CAAW;wBAC7C,KAAK,WAAW,GAAA;wBAChB,OAAO,WAAW,KAAA;oBACpB;gBAAA,CAAE;YAEJ,cAAc,8BAA8B;gBAC1C,cAAA,CAAc,KAAA,SAAS,kBAAA,KAAT,OAAA,KAAA,IAAA,GAA6B,MAAA;gBAC3C,cAAc,UAAU,MAAA,GAAS;YACnC,CAAC;YACD,WAAW,UAAU,MAAA,GAAS,IAAI,YAAY,KAAA;YAC9C,WAAW,mBACP,iBAAiB,GAAA,CAAI,CAAA,UAAA,CAAY;oBAC/B,MAAM;oBACN,MAAM,QAAQ,IAAA;gBAChB,CAAA,CAAE,IACF,KAAA;YACJ,OAAO;gBACL,cAAc,SAAS,KAAA,CAAM,YAAA;gBAC7B,kBAAkB,SAAS,KAAA,CAAM,aAAA;YACnC;YACA,SAAS;gBACP,WAAW,KAAA;gBACX,aAAa,CAAC;YAChB;YACA,aAAa;gBACX,SAAS;gBACT,MAAM;YACR;YACA,SAAS;gBACP,MAAM,KAAK,SAAA,CAAU,IAAI;YAC3B;YACA,UAAU;gBACR,IAAI,SAAS,EAAA;gBACb,WAAW,IAAI,KAAK,SAAS,UAAA,GAAa,GAAI;gBAC9C,SAAS,SAAS,KAAA;YACpB;YACA,kBAAkB;gBAChB,QAAQ;oBACN,YAAY,SAAS,EAAA;oBACrB,oBAAA,CACE,KAAA,CAAA,KAAA,SAAS,KAAA,CAAM,oBAAA,KAAf,OAAA,KAAA,IAAA,GAAqC,aAAA,KAArC,OAAA,KAAsD;oBACxD,iBAAA,CACE,KAAA,CAAA,KAAA,SAAS,KAAA,CAAM,qBAAA,KAAf,OAAA,KAAA,IAAA,GAAsC,gBAAA,KAAtC,OAAA,KAA0D;gBAC9D;YACF;YACA;QACF;IACF;IAEA,MAAM,SACJ,OAAA,EAC2D;QAC3D,MAAM,EAAE,MAAM,IAAA,EAAM,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,OAAO;QAErD,MAAM,EAAE,eAAA,EAAiB,OAAO,QAAA,CAAS,CAAA,GAAI,8QAAMP,gBAAAA,EAAc;YAC/D,KAAK,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI;gBACnB,MAAM;gBACN,SAAS,IAAA,CAAK,OAAA;YAChB,CAAC;YACD,iRAASC,iBAAAA,EAAe,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,QAAQ,OAAO;YAC9D,MAAM;gBACJ,GAAG,IAAA;gBACH,QAAQ;YACV;YACA,uBAAuB;YACvB,4BAA2BO,0SAAAA,EACzB;YAEF,aAAa,QAAQ,WAAA;YACrB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;QACrB,CAAC;QAED,MAAM,OAAO,IAAA;QAEb,IAAI,eAA4C;QAChD,IAAI,eAAe;QACnB,IAAI,mBAAmB;QACvB,IAAI,qBAAoC;QACxC,IAAI,kBAAiC;QACrC,IAAI,aAA4B;QAChC,MAAM,mBAGF,CAAC;QACL,IAAI,eAAe;QAEnB,OAAO;YACL,QAAQ,SAAS,WAAA,CACf,IAAI,gBAGF;gBACA,WAAU,KAAA,EAAO,UAAA,EAAY;oBAhbvC,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;oBAkbY,IAAI,CAAC,MAAM,OAAA,EAAS;wBAClB,eAAe;wBACf,WAAW,OAAA,CAAQ;4BAAE,MAAM;4BAAS,OAAO,MAAM,KAAA;wBAAM,CAAC;wBACxD;oBACF;oBAEA,MAAM,QAAQ,MAAM,KAAA;oBAEpB,IAAI,+BAA+B,KAAK,GAAG;wBACzC,IAAI,MAAM,IAAA,CAAK,IAAA,KAAS,iBAAiB;4BACvC,gBAAA,CAAiB,MAAM,YAAY,CAAA,GAAI;gCACrC,UAAU,MAAM,IAAA,CAAK,IAAA;gCACrB,YAAY,MAAM,IAAA,CAAK,OAAA;4BACzB;4BAEA,WAAW,OAAA,CAAQ;gCACjB,MAAM;gCACN,cAAc;gCACd,YAAY,MAAM,IAAA,CAAK,OAAA;gCACvB,UAAU,MAAM,IAAA,CAAK,IAAA;gCACrB,eAAe,MAAM,IAAA,CAAK,SAAA;4BAC5B,CAAC;wBACH;oBACF,OAAA,IAAW,0CAA0C,KAAK,GAAG;wBAC3D,MAAM,WAAW,gBAAA,CAAiB,MAAM,YAAY,CAAA;wBAEpD,IAAI,YAAY,MAAM;4BACpB,WAAW,OAAA,CAAQ;gCACjB,MAAM;gCACN,cAAc;gCACd,YAAY,SAAS,UAAA;gCACrB,UAAU,SAAS,QAAA;gCACnB,eAAe,MAAM,KAAA;4BACvB,CAAC;wBACH;oBACF,OAAA,IAAW,uBAAuB,KAAK,GAAG;wBACxC,aAAa,MAAM,QAAA,CAAS,EAAA;wBAC5B,WAAW,OAAA,CAAQ;4BACjB,MAAM;4BACN,IAAI,MAAM,QAAA,CAAS,EAAA;4BACnB,WAAW,IAAI,KAAK,MAAM,QAAA,CAAS,UAAA,GAAa,GAAI;4BACpD,SAAS,MAAM,QAAA,CAAS,KAAA;wBAC1B,CAAC;oBACH,OAAA,IAAW,iBAAiB,KAAK,GAAG;wBAClC,WAAW,OAAA,CAAQ;4BACjB,MAAM;4BACN,WAAW,MAAM,KAAA;wBACnB,CAAC;oBACH,OAAA,IAAW,yCAAyC,KAAK,GAAG;wBAC1D,WAAW,OAAA,CAAQ;4BACjB,MAAM;4BACN,WAAW,MAAM,KAAA;wBACnB,CAAC;oBACH,OAAA,IACE,8BAA8B,KAAK,KACnC,MAAM,IAAA,CAAK,IAAA,KAAS,iBACpB;wBACA,gBAAA,CAAiB,MAAM,YAAY,CAAA,GAAI,KAAA;wBACvC,eAAe;wBACf,WAAW,OAAA,CAAQ;4BACjB,MAAM;4BACN,cAAc;4BACd,YAAY,MAAM,IAAA,CAAK,OAAA;4BACvB,UAAU,MAAM,IAAA,CAAK,IAAA;4BACrB,MAAM,MAAM,IAAA,CAAK,SAAA;wBACnB,CAAC;oBACH,OAAA,IAAW,wBAAwB,KAAK,GAAG;wBACzC,eAAe,8BAA8B;4BAC3C,cAAA,CAAc,KAAA,MAAM,QAAA,CAAS,kBAAA,KAAf,OAAA,KAAA,IAAA,GAAmC,MAAA;4BACjD;wBACF,CAAC;wBACD,eAAe,MAAM,QAAA,CAAS,KAAA,CAAM,YAAA;wBACpC,mBAAmB,MAAM,QAAA,CAAS,KAAA,CAAM,aAAA;wBACxC,qBAAA,CACE,KAAA,CAAA,KAAA,MAAM,QAAA,CAAS,KAAA,CAAM,oBAAA,KAArB,OAAA,KAAA,IAAA,GAA2C,aAAA,KAA3C,OAAA,KACA;wBACF,kBAAA,CACE,KAAA,CAAA,KAAA,MAAM,QAAA,CAAS,KAAA,CAAM,qBAAA,KAArB,OAAA,KAAA,IAAA,GAA4C,gBAAA,KAA5C,OAAA,KACA;oBACJ,OAAA,IAAW,+BAA+B,KAAK,GAAG;wBAChD,WAAW,OAAA,CAAQ;4BACjB,MAAM;4BACN,QAAQ;gCACN,YAAY;gCACZ,IAAA,CAAI,KAAA,CAAA,KAAA,CAAA,KAAA,KAAK,MAAA,EAAO,UAAA,KAAZ,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,GAAA,KAAA,OAAA,6QAA8BD,aAAAA,CAAW;gCAC7C,KAAK,MAAM,UAAA,CAAW,GAAA;gCACtB,OAAO,MAAM,UAAA,CAAW,KAAA;4BAC1B;wBACF,CAAC;oBACH;gBACF;gBAEA,OAAM,UAAA,EAAY;oBAChB,WAAW,OAAA,CAAQ;wBACjB,MAAM;wBACN;wBACA,OAAO;4BAAE;4BAAc;wBAAiB;wBACxC,GAAA,CAAK,sBAAsB,QAAQ,mBAAmB,IAAA,KAAS;4BAC7D,kBAAkB;gCAChB,QAAQ;oCACN;oCACA;oCACA;gCACF;4BACF;wBACF,CAAA;oBACF,CAAC;gBACH;YACF,CAAC;YAEH,SAAS;gBACP,WAAW,KAAA;gBACX,aAAa,CAAC;YAChB;YACA,aAAa;gBAAE,SAAS;YAAgB;YACxC,SAAS;gBAAE,MAAM,KAAK,SAAA,CAAU,IAAI;YAAE;YACtC;QACF;IACF;AACF;AAEA,IAAM,oMAAcJ,IAAAA,CAAE,MAAA,CAAO;IAC3B,oMAAcA,IAAAA,CAAE,MAAA,CAAO;IACvB,4MAAsBA,IAAAA,CACnB,MAAA,CAAO;QAAE,eAAeA,0LAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAAE,CAAC,EAC9C,OAAA,CAAQ;IACX,qMAAeA,IAAAA,CAAE,MAAA,CAAO;IACxB,6MAAuBA,IAAAA,CACpB,MAAA,CAAO;QAAE,wMAAkBA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAAE,CAAC,EACjD,OAAA,CAAQ;AACb,CAAC;AAED,IAAM,6MAAuBA,IAAAA,CAAE,MAAA,CAAO;IACpC,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,4BAA4B;IAC5C,6LAAOA,IAAAA,CAAE,MAAA,CAAO;AAClB,CAAC;AAED,IAAM,oNAA8BA,IAAAA,CAAE,MAAA,CAAO;IAC3C,4LAAMA,IAAAA,CAAE,IAAA,CAAK;QAAC;QAAsB,qBAAqB;KAAC;IAC1D,gMAAUA,IAAAA,CAAE,MAAA,CAAO;QACjB,0MAAoBA,IAAAA,CAAE,MAAA,CAAO;YAAE,8LAAQA,IAAAA,CAAE,MAAA,CAAO;QAAE,CAAC,EAAE,OAAA,CAAQ;QAC7D,OAAO;IACT,CAAC;AACH,CAAC;AAED,IAAM,mNAA6BA,IAAAA,CAAE,MAAA,CAAO;IAC1C,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,kBAAkB;IAClC,gMAAUA,IAAAA,CAAE,MAAA,CAAO;QACjB,0LAAIA,IAAAA,CAAE,MAAA,CAAO;QACb,kMAAYA,IAAAA,CAAE,MAAA,CAAO;QACrB,6LAAOA,IAAAA,CAAE,MAAA,CAAO;IAClB,CAAC;AACH,CAAC;AAED,IAAM,+BAA+BA,0LAAAA,CAAE,MAAA,CAAO;IAC5C,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,2BAA2B;IAC3C,oMAAcA,IAAAA,CAAE,MAAA,CAAO;IACvB,4LAAMA,IAAAA,CAAE,kBAAA,CAAmB,QAAQ;8LACjCA,IAAAA,CAAE,MAAA,CAAO;YACP,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,SAAS;QAC3B,CAAC;8LACDA,IAAAA,CAAE,MAAA,CAAO;YACP,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,eAAe;YAC/B,0LAAIA,IAAAA,CAAE,MAAA,CAAO;YACb,+LAASA,IAAAA,CAAE,MAAA,CAAO;YAClB,2LAAMA,KAAAA,CAAE,MAAA,CAAO;YACf,iMAAWA,IAAAA,CAAE,MAAA,CAAO;YACpB,8LAAQA,IAAAA,CAAE,OAAA,CAAQ,WAAW;QAC/B,CAAC;KACF;AACH,CAAC;AAED,IAAM,iOAA2CA,IAAAA,CAAE,MAAA,CAAO;IACxD,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,wCAAwC;IACxD,8LAASA,KAAAA,CAAE,MAAA,CAAO;IAClB,oMAAcA,IAAAA,CAAE,MAAA,CAAO;IACvB,OAAOA,0LAAAA,CAAE,MAAA,CAAO;AAClB,CAAC;AAED,IAAM,sNAAgCA,IAAAA,CAAE,MAAA,CAAO;IAC7C,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,4BAA4B;IAC5C,oMAAcA,IAAAA,CAAE,MAAA,CAAO;IACvB,MAAMA,0LAAAA,CAAE,kBAAA,CAAmB,QAAQ;8LACjCA,IAAAA,CAAE,MAAA,CAAO;YACP,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,SAAS;QAC3B,CAAC;8LACDA,IAAAA,CAAE,MAAA,CAAO;YACP,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,eAAe;YAC/B,0LAAIA,IAAAA,CAAE,MAAA,CAAO;YACb,SAASA,0LAAAA,CAAE,MAAA,CAAO;YAClB,4LAAMA,IAAAA,CAAE,MAAA,CAAO;YACf,iMAAWA,IAAAA,CAAE,MAAA,CAAO;QACtB,CAAC;KACF;AACH,CAAC;AAED,IAAM,sNAAgCA,IAAAA,CAAE,MAAA,CAAO;IAC7C,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,uCAAuC;IACvD,kMAAYA,IAAAA,CAAE,MAAA,CAAO;QACnB,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,cAAc;QAC9B,KAAKA,0LAAAA,CAAE,MAAA,CAAO;QACd,6LAAOA,IAAAA,CAAE,MAAA,CAAO;IAClB,CAAC;AACH,CAAC;AAED,IAAM,gOAA0CA,IAAAA,CAAE,MAAA,CAAO;IACvD,4LAAMA,IAAAA,CAAE,OAAA,CAAQ,uCAAuC;IACvD,SAASA,0LAAAA,CAAE,MAAA,CAAO;IAClB,oMAAcA,IAAAA,CAAE,MAAA,CAAO;IACvB,qMAAeA,IAAAA,CAAE,MAAA,CAAO;IACxB,OAAOA,0LAAAA,CAAE,MAAA,CAAO;AAClB,CAAC;AAED,IAAM,mNAA6BA,IAAAA,CAAE,KAAA,CAAM;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;0LACAA,IAAAA,CAAE,MAAA,CAAO;QAAE,4LAAMA,IAAAA,CAAE,MAAA,CAAO;IAAE,CAAC,EAAE,WAAA,CAAY;CAC5C;AAED,SAAS,iBACP,KAAA,EAC+C;IAC/C,OAAO,MAAM,IAAA,KAAS;AACxB;AAEA,SAAS,8BACP,KAAA,EACuD;IACvD,OAAO,MAAM,IAAA,KAAS;AACxB;AAEA,SAAS,wBACP,KAAA,EACsD;IACtD,OACE,MAAM,IAAA,KAAS,wBAAwB,MAAM,IAAA,KAAS;AAE1D;AAEA,SAAS,uBACP,KAAA,EACqD;IACrD,OAAO,MAAM,IAAA,KAAS;AACxB;AAEA,SAAS,0CACP,KAAA,EACmE;IACnE,OAAO,MAAM,IAAA,KAAS;AACxB;AAEA,SAAS,+BACP,KAAA,EACwD;IACxD,OAAO,MAAM,IAAA,KAAS;AACxB;AAEA,SAAS,+BACP,KAAA,EACwD;IACxD,OAAO,MAAM,IAAA,KAAS;AACxB;AAEA,SAAS,yCACP,KAAA,EACkE;IAClE,OAAO,MAAM,IAAA,KAAS;AACxB;AAQA,SAAS,wBAAwB,OAAA,EAAuC;IAEtE,IAAI,QAAQ,UAAA,CAAW,GAAG,GAAG;QAC3B,IAAI,QAAQ,UAAA,CAAW,SAAS,KAAK,QAAQ,UAAA,CAAW,YAAY,GAAG;YACrE,OAAO;gBACL,kBAAkB;gBAClB,mBAAmB;gBACnB,wBAAwB;YAC1B;QACF;QAEA,OAAO;YACL,kBAAkB;YAClB,mBAAmB;YACnB,wBAAwB;QAC1B;IACF;IAGA,OAAO;QACL,kBAAkB;QAClB,mBAAmB;QACnB,wBAAwB;IAC1B;AACF;AAEA,IAAM,uCAAuCA,0LAAAA,CAAE,MAAA,CAAO;IACpD,gMAAUA,IAAAA,CAAE,GAAA,CAAI,EAAE,OAAA,CAAQ;IAC1B,yMAAmBA,IAAAA,CAAE,OAAA,CAAQ,EAAE,OAAA,CAAQ;IACvC,oBAAoBA,0LAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACvC,6LAAOA,IAAAA,CAAE,OAAA,CAAQ,EAAE,OAAA,CAAQ;IAC3B,4LAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACzB,uMAAiBA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACpC,qMAAeA,IAAAA,CAAE,OAAA,CAAQ,EAAE,OAAA,CAAQ;IACnC,oMAAcA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACjC,kBAAkBA,0LAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;AACvC,CAAC;;AI7uBD,IAAM,mNAA6BM,IAAAA,CAAE,MAAA,CAAO,CAAC,CAAC;AAE9C,SAAS,qBAAqB,EAC5B,iBAAA,EACA,YAAA,EACF,GASI,CAAC,CAAA,EAKH;IACA,OAAO;QACL,MAAM;QACN,IAAI;QACJ,MAAM;YACJ;YACA;QACF;QACA,YAAY;IACd;AACF;AAEO,IAAM,cAAc;IACzB,kBAAkB;AACpB;;;ACrBA,IAAM,oNAA8BK,IAAAA,CAAE,MAAA,CAAO;IAC3C,oMAAcA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACjC,6LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,IAAI,EAAE,GAAA,CAAI,CAAG,EAAE,OAAA,CAAQ,CAAG,EAAE,OAAA,CAAQ;AAC5D,CAAC;AAYM,IAAM,oBAAN,MAAiD;IAOtD,YACW,OAAA,EACQ,MAAA,CACjB;QAFS,IAAA,CAAA,OAAA,GAAA;QACQ,IAAA,CAAA,MAAA,GAAA;QARnB,IAAA,CAAS,oBAAA,GAAuB;IAS7B;IAPH,IAAI,WAAmB;QACrB,OAAO,IAAA,CAAK,MAAA,CAAO,QAAA;IACrB;IAOQ,QAAQ,EACd,IAAA,EACA,QAAQ,OAAA,EACR,eAAe,KAAA,EACf,KAAA,EACA,YAAA,EACA,eAAA,EACF,EAA+C;QAC7C,MAAM,WAAuC,CAAC,CAAA;QAG9C,MAAM,uRAAgBC,wBAAAA,EAAqB;YACzC,UAAU;YACV;YACA,QAAQ;QACV,CAAC;QAGD,MAAM,cAAuC;YAC3C,OAAO,IAAA,CAAK,OAAA;YACZ,OAAO;YACP;YACA,iBAAiB;YACjB;YACA;QACF;QAEA,IAAI,cAAc;YAChB,IAAI;gBAAC;gBAAO;gBAAQ;gBAAO;gBAAQ;gBAAO,KAAK;aAAA,CAAE,QAAA,CAAS,YAAY,GAAG;gBACvE,YAAY,eAAA,GAAkB;YAChC,OAAO;gBACL,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;oBACT,SAAS,CAAA,2BAAA,EAA8B,YAAY,CAAA,oBAAA,CAAA;gBACrD,CAAC;YACH;QACF;QAGA,IAAI,eAAe;YACjB,MAAM,qBAA2C,CAAC;YAElD,IAAA,MAAW,OAAO,mBAAoB;gBACpC,MAAM,QAAQ,kBAAA,CAAmB,GAAiC,CAAA;gBAClE,IAAI,UAAU,KAAA,GAAW;oBACvB,WAAA,CAAY,GAAG,CAAA,GAAI;gBACrB;YACF;QACF;QAEA,OAAO;YACL;YACA;QACF;IACF;IAEA,MAAM,WACJ,OAAA,EAC2D;QApG/D,IAAA,IAAA,IAAA;QAqGI,MAAM,cAAA,CAAc,KAAA,CAAA,KAAA,CAAA,KAAA,IAAA,CAAK,MAAA,CAAO,SAAA,KAAZ,OAAA,KAAA,IAAA,GAAuB,WAAA,KAAvB,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,GAAA,KAAA,OAAA,KAA0C,aAAA,GAAA,IAAI,KAAK;QACvE,MAAM,EAAE,WAAA,EAAa,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,OAAO;QAEtD,MAAM,EACJ,OAAO,KAAA,EACP,eAAA,EACA,UAAU,WAAA,EACZ,GAAI,8QAAMC,gBAAAA,EAAc;YACtB,KAAK,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI;gBACnB,MAAM;gBACN,SAAS,IAAA,CAAK,OAAA;YAChB,CAAC;YACD,iRAASC,iBAAAA,EAAe,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,QAAQ,OAAO;YAC9D,MAAM;YACN,uBAAuB;YACvB,mSAA2B,8BAAA,CAA4B;YACvD,aAAa,QAAQ,WAAA;YACrB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;QACrB,CAAC;QAED,OAAO;YACL;YACA;YACA,SAAS;gBACP,MAAM,KAAK,SAAA,CAAU,WAAW;YAClC;YACA,UAAU;gBACR,WAAW;gBACX,SAAS,IAAA,CAAK,OAAA;gBACd,SAAS;gBACT,MAAM;YACR;QACF;IACF;AACF;;ApBiDO,SAAS,aACd,UAAkC,CAAC,CAAA,EACnB;IA1LlB,IAAA,IAAA,IAAA;IA2LE,MAAM,UAAA,CACJ,KAAA,CAAA,GAAA,mQAAA,CAAA,uBAAA,EAAqB,QAAQ,OAAO,CAAA,KAApC,OAAA,KAAyC;IAG3C,MAAM,gBAAA,CAAgB,KAAA,QAAQ,aAAA,KAAR,OAAA,KAAyB;IAE/C,MAAM,eAAA,CAAe,KAAA,QAAQ,IAAA,KAAR,OAAA,KAAgB;IAErC,MAAM,aAAa,IAAA,CAAO;YACxB,eAAe,CAAA,OAAA,0QAAU,aAAA,EAAW;gBAClC,QAAQ,QAAQ,MAAA;gBAChB,yBAAyB;gBACzB,aAAa;YACf,CAAC,CAAC,EAAA;YACF,uBAAuB,QAAQ,YAAA;YAC/B,kBAAkB,QAAQ,OAAA;YAC1B,GAAG,QAAQ,OAAA;QACb,CAAA;IAEA,MAAM,kBAAkB,CACtB,SACA,WAA+B,CAAC,CAAA,GAEhC,IAAI,wBAAwB,SAAS,UAAU;YAC7C,UAAU,GAAG,YAAY,CAAA,KAAA,CAAA;YACzB,KAAK,CAAC,EAAE,IAAA,CAAK,CAAA,GAAM,GAAG,OAAO,GAAG,IAAI,EAAA;YACpC,SAAS;YACT;YACA,OAAO,QAAQ,KAAA;QACjB,CAAC;IAEH,MAAM,wBAAwB,CAC5B,SACA,WAAqC,CAAC,CAAA,GAEtC,IAAI,8BAA8B,SAAS,UAAU;YACnD,UAAU,GAAG,YAAY,CAAA,WAAA,CAAA;YACzB,KAAK,CAAC,EAAE,IAAA,CAAK,CAAA,GAAM,GAAG,OAAO,GAAG,IAAI,EAAA;YACpC,SAAS;YACT;YACA,OAAO,QAAQ,KAAA;QACjB,CAAC;IAEH,MAAM,uBAAuB,CAC3B,SACA,WAAoC,CAAC,CAAA,GAErC,IAAI,qBAAqB,SAAS,UAAU;YAC1C,UAAU,GAAG,YAAY,CAAA,UAAA,CAAA;YACzB,KAAK,CAAC,EAAE,IAAA,CAAK,CAAA,GAAM,GAAG,OAAO,GAAG,IAAI,EAAA;YACpC,SAAS;YACT,OAAO,QAAQ,KAAA;QACjB,CAAC;IAEH,MAAM,mBAAmB,CACvB,SACA,WAAgC,CAAC,CAAA,GAEjC,IAAI,iBAAiB,SAAS,UAAU;YACtC,UAAU,GAAG,YAAY,CAAA,MAAA,CAAA;YACzB,KAAK,CAAC,EAAE,IAAA,CAAK,CAAA,GAAM,GAAG,OAAO,GAAG,IAAI,EAAA;YACpC,SAAS;YACT,OAAO,QAAQ,KAAA;QACjB,CAAC;IAEH,MAAM,2BAA2B,CAAC,UAChC,IAAI,yBAAyB,SAAS;YACpC,UAAU,GAAG,YAAY,CAAA,cAAA,CAAA;YACzB,KAAK,CAAC,EAAE,IAAA,CAAK,CAAA,GAAM,GAAG,OAAO,GAAG,IAAI,EAAA;YACpC,SAAS;YACT,OAAO,QAAQ,KAAA;QACjB,CAAC;IAEH,MAAM,oBAAoB,CAAC,UACzB,IAAI,kBAAkB,SAAS;YAC7B,UAAU,GAAG,YAAY,CAAA,OAAA,CAAA;YACzB,KAAK,CAAC,EAAE,IAAA,CAAK,CAAA,GAAM,GAAG,OAAO,GAAG,IAAI,EAAA;YACpC,SAAS;YACT,OAAO,QAAQ,KAAA;QACjB,CAAC;IAEH,MAAM,sBAAsB,CAC1B,SACA,aACG;QACH,IAAI,YAAY;YACd,MAAM,IAAI,MACR;QAEJ;QAEA,IAAI,YAAY,0BAA0B;YACxC,OAAO,sBACL,SACA;QAEJ;QAEA,OAAO,gBAAgB,SAAS,QAA8B;IAChE;IAEA,MAAM,uBAAuB,CAAC,YAAoC;QAChE,OAAO,IAAI,6BAA6B,SAAS;YAC/C,UAAU,GAAG,YAAY,CAAA,UAAA,CAAA;YACzB,KAAK,CAAC,EAAE,IAAA,CAAK,CAAA,GAAM,GAAG,OAAO,GAAG,IAAI,EAAA;YACpC,SAAS;YACT,OAAO,QAAQ,KAAA;QACjB,CAAC;IACH;IAEA,MAAM,WAAW,SACf,OAAA,EACA,QAAA,EACA;QACA,OAAO,oBAAoB,SAAS,QAAQ;IAC9C;IAEA,SAAS,aAAA,GAAgB;IACzB,SAAS,IAAA,GAAO;IAChB,SAAS,UAAA,GAAa;IACtB,SAAS,SAAA,GAAY;IACrB,SAAS,SAAA,GAAY;IACrB,SAAS,aAAA,GAAgB;IACzB,SAAS,kBAAA,GAAqB;IAE9B,SAAS,KAAA,GAAQ;IACjB,SAAS,UAAA,GAAa;IAEtB,SAAS,aAAA,GAAgB;IACzB,SAAS,kBAAA,GAAqB;IAE9B,SAAS,MAAA,GAAS;IAClB,SAAS,WAAA,GAAc;IAEvB,SAAS,KAAA,GAAQ;IAEjB,OAAO;AACT;AAKO,IAAM,SAAS,aAAa;IACjC,eAAe;AACjB,CAAC", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20], "debugId": null}}]}