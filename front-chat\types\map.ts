// 레이어 파라미터 타입
export interface LayerParams {
  method: string;
  server: string;
  layer: string;
  service: string;
  bbox: boolean;
  matrixSet: string | null;
  crtfckey: string;
  projection: string;
  serviceTy: string;
  geometryType: string;
  [key: string]: any;
}

// 레이어 정보 타입
export interface LayerInfo {
  lyrId: string; // 레이어 고유 ID
  cntntsId: string; // 컨텐츠 ID
  jobClCode: string; // 업무 분류 코드
  lyrNm: string; // 레이어 이름
  lyrClCode: string; // 레이어 분류 코드
  lyrTySeCode: string; // 레이어 타입 구분 코드
  namespace: string; // 네임스페이스
}

// 레이어 설정 타입
export interface LayerConfig {
  type: string;
  params: LayerParams;
}

// 최종 레이어 타입
export interface Layer {
  id: string; // 레이어 고유 ID (lyrId 사용)
  name: string; // 레이어 표시 이름 (lyrNm 사용)
  type: string; // 레이어 타입 (geoserver, vector 등)
  visible: boolean; // 레이어 표시 여부
  odfLayer: any; // ODF 맵 레이어 객체
  params: LayerParams; // 레이어 파라미터
  info: LayerInfo; // 레이어 메타 정보
  style?: {
    // 레이어 스타일
    [key: string]: any;
  };
  filter?: string; // 레이어 필터
}

export interface MapView {
  center: number[];
  zoom: number;
  basemap: string;
}

export interface MapInfo {
  id: string;
  name: string;
  activeUsers: number;
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
  layers: Layer[];
}

export interface Map {
  map: any;
  mapInfo: MapInfo;
  view: MapView;
  markers: any[];
  highlightLayer: any;
}
