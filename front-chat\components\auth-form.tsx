import Form from 'next/form';
import {Label} from "@/components/ui/label";
import { Input } from '@/components/ui/input';


export function AuthForm({
	                         action,
	                         children,
	                         defaultEmail = '',
                         }: {
	action: any;
	children: React.ReactNode;
	defaultEmail?: string;
}) {
	return (
		<Form action={action} className="flex flex-col gap-4 px-4 sm:px-16">
			<div className="flex flex-col gap-2">
				<Label
					htmlFor="id"
					className="text-zinc-600 font-normal dark:text-zinc-400"
				>
					아이디
				</Label>

				<Input
					id="id"
					name="id"
					className="bg-muted text-md md:text-sm"
					type="text"
					placeholder="admin"
					autoComplete="username"
					required
					defaultValue={defaultEmail}
				/>

				{/*<Label*/}
				{/*	htmlFor="email"*/}
				{/*	className="text-zinc-600 font-normal dark:text-zinc-400"*/}
				{/*>*/}
				{/*	Email Address*/}
				{/*</Label>*/}

				{/*<Input*/}
				{/*	id="email"*/}
				{/*	name="email"*/}
				{/*	className="bg-muted text-md md:text-sm"*/}
				{/*	type="email"*/}
				{/*	placeholder="<EMAIL>"*/}
				{/*	autoComplete="email"*/}
				{/*	required*/}
				{/*	defaultValue={defaultEmail}*/}
				{/*/>*/}

				<Label
					htmlFor="password"
					className="text-zinc-600 font-normal dark:text-zinc-400"
				>
					비밀번호
				</Label>

				<Input
					id="password"
					name="password"
					className="bg-muted text-md md:text-sm"
					type="password"
					required
				/>
			</div>

			{children}
		</Form>
	);
}
