exports.id=501,exports.ids=[501],exports.modules={28538:(e,t,a)=>{"use strict";a.d(t,{qQ:()=>z,yo:()=>ea,wA:()=>W,TJ:()=>V,Lz:()=>S,zU:()=>G,V7:()=>Z,kA:()=>O,de:()=>j,mV:()=>Y,yd:()=>C,Y0:()=>ee,yM:()=>B,$:()=>X,xZ:()=>et,Ci:()=>H});var r={};a.r(r),a.d(r,{accounts:()=>q,accountsRelations:()=>$,chat:()=>N,chatMap:()=>g,chatRelation:()=>_,map:()=>b,mapAccess:()=>k,mapSession:()=>E,mapView:()=>Q,mapsRelation:()=>F,message:()=>A,sessions:()=>I,sessionsRelations:()=>U,user:()=>w,userRelations:()=>D,verificationTokens:()=>v,vote:()=>x});var o=a(9652),s=a(33094),n=a(32019),i=a(57052),d=a(52363),l=a(4471),c=a(56850),u=a(94485),p=a(10302),m=a(7320),f=a(16077),y=a(56839),h=a(86746);let w=(0,n.cJ)("user",{id:(0,i.Qq)("id").primaryKey().$defaultFn(()=>(0,o.sX)()),name:(0,i.Qq)("name"),username:(0,i.Qq)("username"),gh_username:(0,i.Qq)("gh_username"),email:(0,i.Qq)("email").unique(),emailVerified:(0,d.vE)("emailVerified",{mode:"date"}),image:(0,i.Qq)("image"),createdAt:(0,d.vE)("createdAt",{mode:"date"}).defaultNow().notNull(),updatedAt:(0,d.vE)("updatedAt",{mode:"date"}).notNull().$onUpdate(()=>new Date)}),I=(0,n.cJ)("sessions",{sessionToken:(0,i.Qq)("sessionToken").primaryKey(),userId:(0,i.Qq)("userId").notNull().references(()=>w.id,{onDelete:"cascade",onUpdate:"cascade"}),expires:(0,d.vE)("expires",{mode:"date"}).notNull()},e=>({userIdIdx:(0,l.Pe)().on(e.userId)})),v=(0,n.cJ)("verificationTokens",{identifier:(0,i.Qq)("identifier").notNull(),token:(0,i.Qq)("token").notNull().unique(),expires:(0,d.vE)("expires",{mode:"date"}).notNull()},e=>({compositePk:(0,c.ie)({columns:[e.identifier,e.token]})})),q=(0,n.cJ)("accounts",{userId:(0,i.Qq)("userId").notNull().references(()=>w.id,{onDelete:"cascade",onUpdate:"cascade"}),type:(0,i.Qq)("type").notNull(),provider:(0,i.Qq)("provider").notNull(),providerAccountId:(0,i.Qq)("providerAccountId").notNull(),refresh_token:(0,i.Qq)("refresh_token"),refreshTokenExpiresIn:(0,u.nd)("refresh_token_expires_in"),access_token:(0,i.Qq)("access_token"),expires_at:(0,u.nd)("expires_at"),token_type:(0,i.Qq)("token_type"),scope:(0,i.Qq)("scope"),id_token:(0,i.Qq)("id_token"),session_state:(0,i.Qq)("session_state"),oauth_token_secret:(0,i.Qq)("oauth_token_secret"),oauth_token:(0,i.Qq)("oauth_token")},e=>({userIdIdx:(0,l.Pe)().on(e.userId),compositePk:(0,c.ie)({columns:[e.provider,e.providerAccountId]})})),N=(0,n.cJ)("Chat",{id:(0,i.Qq)("id").primaryKey(),createdAt:(0,d.vE)("createdAt").notNull(),title:(0,i.Qq)("title").notNull().default("New Chat"),userId:(0,i.Qq)("userId").notNull(),visibility:(0,p.yf)("visibility",{enum:["public","private"]}).notNull().default("private")}),A=(0,n.cJ)("Message",{id:(0,m.uR)("id").primaryKey().notNull().defaultRandom(),chatId:(0,i.Qq)("chatId").notNull().references(()=>N.id),role:(0,p.yf)("role").notNull(),content:(0,f.Pq)("content"),parts:(0,f.Pq)("parts"),attachments:(0,f.Pq)("attachments"),createdAt:(0,d.vE)("createdAt").notNull()}),x=(0,n.cJ)("Vote",{chatId:(0,i.Qq)("chatId").notNull().references(()=>N.id),messageId:(0,m.uR)("messageId").notNull().references(()=>A.id),isUpvoted:(0,y.zM)("isUpvoted").notNull()},e=>({pk:(0,c.ie)({columns:[e.chatId,e.messageId]})})),b=(0,n.cJ)("map",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,h.$C)()),name:(0,i.Qq)("name").notNull(),createdBy:(0,i.Qq)("userId").notNull(),createdAt:(0,d.vE)("createdAt").notNull().defaultNow(),updatedAt:(0,d.vE)("updatedAt").notNull().defaultNow(),isPublic:(0,y.zM)("isPublic").default(!1),layers:(0,f.Pq)("layers").notNull(),version:(0,u.nd)("version").notNull().default(1)}),Q=(0,n.cJ)("map_view",{id:(0,i.Qq)("id").notNull().$defaultFn(()=>(0,h.$C)()),mapId:(0,i.Qq)("mapId").notNull().references(()=>b.id,{onDelete:"cascade"}),userId:(0,i.Qq)("userId").notNull(),center:(0,f.Pq)("center").notNull(),zoom:(0,u.nd)("zoom").notNull(),basemap:(0,i.Qq)("basemap").notNull(),updatedAt:(0,d.vE)("updatedAt").notNull().defaultNow()},e=>({compoundKey:(0,c.ie)({columns:[e.mapId,e.userId]}),idIdx:(0,l.Pe)("map_view_id_idx").on(e.id)})),g=(0,n.cJ)("chat_map",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,h.$C)()),chatId:(0,i.Qq)("chatId").notNull().references(()=>N.id,{onDelete:"cascade"}),mapId:(0,i.Qq)("mapId").notNull().references(()=>b.id,{onDelete:"restrict"}),createdAt:(0,d.vE)("createdAt").notNull().defaultNow()}),k=(0,n.cJ)("map_access",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,h.$C)()),mapId:(0,i.Qq)("mapId").notNull().references(()=>b.id,{onDelete:"cascade"}),userId:(0,i.Qq)("userId").notNull(),accessType:(0,i.Qq)("accessType").notNull(),createdAt:(0,d.vE)("createdAt").notNull().defaultNow()}),E=(0,n.cJ)("map_session",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,h.$C)()),mapId:(0,i.Qq)("mapId").notNull().references(()=>b.id,{onDelete:"cascade"}),userId:(0,i.Qq)("userId").notNull(),isActive:(0,y.zM)("isActive").default(!0),lastActiveAt:(0,d.vE)("lastActiveAt").notNull().defaultNow(),syncView:(0,y.zM)("syncView").default(!1),followingUserId:(0,i.Qq)("followingUserId")}),F=(0,s.K1)(b,({many:e})=>({views:e(Q),access:e(k),sessions:e(E),chats:e(g)})),_=(0,s.K1)(N,({many:e})=>({maps:e(g)})),U=(0,s.K1)(I,({one:e})=>({user:e(w,{references:[w.id],fields:[I.userId]})})),$=(0,s.K1)(q,({one:e})=>({user:e(w,{references:[w.id],fields:[q.userId]})})),D=(0,s.K1)(w,({many:e})=>({accounts:e(q),sessions:e(I)}));var J=a(60988),R=a(1770),T=a(7160),K=a(42449),P=a(8738);let M=new K.Pool({connectionString:process.env.POSTGRES_URL}),L=(0,P.f)(M,{schema:r,logger:!0});async function C({id:e,userId:t,title:a}){try{return await L.insert(N).values({id:e,createdAt:new Date,userId:t,title:a})}catch(e){throw console.error("Failed to save chat in database"),e}}async function B({messages:e}){try{return await L.insert(A).values(e)}catch(e){throw console.error("Failed to save messages in database",e),e}}async function z({id:e}){try{return await L.delete(x).where((0,J.eq)(x.chatId,e)),await L.delete(A).where((0,J.eq)(A.chatId,e)),await L.delete(N).where((0,J.eq)(N.id,e))}catch(e){throw console.error("Failed to delete chat by id from database"),e}}async function S({id:e}){try{return await L.select().from(N).where((0,J.eq)(N.userId,e)).orderBy((0,R.i)(N.createdAt))}catch(e){throw console.error("Failed to get chats by user from database"),e}}async function V({id:e}){try{let[t]=await L.select().from(N).where((0,J.eq)(N.id,e));return t}catch(e){throw console.error("Failed to get chat by id from database"),e}}async function O({id:e}){try{return await L.select().from(A).where((0,J.eq)(A.id,e))}catch(e){throw console.error("Failed to get message by id from database"),e}}async function j({id:e}){try{return await L.select().from(A).where((0,J.eq)(A.chatId,e)).orderBy((0,R.Y)(A.createdAt))}catch(e){throw console.error("Failed to get messages by chat id from database",e),e}}async function H({chatId:e,messageId:t,type:a}){try{let[r]=await L.select().from(x).where((0,J.Uo)((0,J.eq)(x.messageId,t)));if(r)return await L.update(x).set({isUpvoted:"up"===a}).where((0,J.Uo)((0,J.eq)(x.messageId,t),(0,J.eq)(x.chatId,e)));return await L.insert(x).values({chatId:e,messageId:t,isUpvoted:"up"===a})}catch(e){throw console.error("Failed to upvote message in database",e),e}}async function Y({id:e}){try{return await L.select().from(x).where((0,J.eq)(x.chatId,e))}catch(e){throw console.error("Failed to get votes by chat id from database",e),e}}async function W({chatId:e,timestamp:t}){try{return await L.delete(A).where((0,J.Uo)((0,J.eq)(A.chatId,e),(0,J.RO)(A.createdAt,t)))}catch(e){throw console.error("Failed to delete messages by id after timestamp from database"),e}}async function X({chatId:e,visibility:t}){try{return await L.update(N).set({visibility:t}).where((0,J.eq)(N.id,e))}catch(e){throw console.error("Failed to update chat visibility in database"),e}}async function Z({userId:e}){try{return await L.select({id:b.id,name:b.name,createdAt:b.createdAt,updatedAt:b.updatedAt,layers:b.layers,isPublic:b.isPublic,activeUsers:(0,T.ll)`
        (
          SELECT COUNT(DISTINCT ${Q.userId})
          FROM ${Q}
          WHERE ${Q.mapId} = ${b.id}
          AND ${Q.updatedAt} > NOW() - INTERVAL '5 minutes'
        )
        `.as("activeUsers"),view:{center:Q.center,zoom:Q.zoom,basemap:Q.basemap}}).from(b).leftJoin(k,(0,J.Uo)((0,J.eq)(k.mapId,b.id),(0,J.eq)(k.userId,e))).leftJoin(Q,(0,J.Uo)((0,J.eq)(Q.mapId,b.id),(0,J.eq)(Q.userId,e))).where((0,J.or)((0,J.eq)(b.createdBy,e),(0,J.eq)(k.userId,e))).orderBy((0,R.i)(b.updatedAt))}catch(e){throw e instanceof Error&&console.error("Failed to get maps by user from database:",{message:e.message,stack:e.stack}),e}}async function G({id:e,userId:t}){try{let[a]=await L.select({id:b.id,name:b.name,createdAt:b.createdAt,updatedAt:b.updatedAt,layers:b.layers,isPublic:b.isPublic,createdBy:b.createdBy,activeUsers:(0,T.ll)`
        (
          SELECT COUNT(DISTINCT ${Q.userId})
          FROM ${Q}
          WHERE ${Q.mapId} = ${b.id}
          AND ${Q.updatedAt} > NOW() - INTERVAL '5 minutes'
        )
        `.as("activeUsers")}).from(b).where((0,J.eq)(b.id,e));if(!a)throw Error("Map not found");if(Array.isArray(a.layers)&&(a.layers=a.layers.map(e=>({...e,style:e.style?"string"==typeof e.style?JSON.parse(e.style):e.style:void 0}))),a.createdBy!==t&&!a.isPublic&&!await er({mapId:e,userId:t}))throw Error("Forbidden");let[r]=await L.select().from(Q).where((0,J.Uo)((0,J.eq)(Q.mapId,e),(0,J.eq)(Q.userId,t)));return{...a,view:r||null}}catch(e){throw console.error("Failed to get map by id from database"),e}}async function ee({id:e,name:t,layers:a,userId:r}){try{if((await L.select().from(b).where((0,J.eq)(b.id,e))).length>0)return await L.update(b).set({name:t,layers:a,updatedAt:new Date}).where((0,J.eq)(b.id,e));return await L.insert(b).values({id:e,name:t,layers:a,createdBy:r,createdAt:new Date,updatedAt:new Date})}catch(e){throw console.error("Failed to save map in database"),e}}async function et({mapId:e,userId:t,view:a}){try{return await L.insert(Q).values({mapId:e,userId:t,center:a.center??{lat:36.5,lng:127.5},zoom:a.zoom??7,basemap:a.basemap??"eMapBasic",updatedAt:new Date}).onConflictDoUpdate({target:[Q.mapId,Q.userId],set:{...a,updatedAt:new Date}})}catch(e){throw console.error("Failed to update map view state"),e}}async function ea({id:e,userId:t}){try{let[a]=await L.select().from(b).where((0,J.eq)(b.id,e));if(!a)throw Error("Map not found");if(a.createdBy!==t)throw Error("Forbidden");return await L.delete(b).where((0,J.eq)(b.id,e))}catch(e){throw console.error("Failed to delete map from database"),e}}async function er({mapId:e,userId:t}){try{return(await L.select().from(k).where((0,J.Uo)((0,J.eq)(k.mapId,e),(0,J.eq)(k.userId,t)))).length>0}catch(e){throw console.error("Failed to check map access"),e}}},34112:(e,t,a)=>{"use strict";a.d(t,{Lc:()=>m,gx:()=>f,k5:()=>y});var r=a(88668);let o={wrapStream:async({doStream:e})=>{let{stream:t,...a}=await e(),r=new TransformStream({transform(e,t){t.enqueue(e)},flush(){}});return{stream:t.pipeThrough(r),...a}}};var s=a(71003),n=a(61478),i=a(94062),d=a(71272);let l=(0,n.m)({baseURL:"http://**************:8005/v1",apiKey:"123"}),c=(0,n.m)({baseURL:"http://**************:8002/v1",apiKey:"123"}),u=(0,n.m)({baseURL:"http://**************:8007/v1",apiKey:"123"}),p=(0,d.g)({baseURL:"https://ai-dify.geon.kr/v1"}),m=e=>{let t=l;return e.includes("Qwen2.5-14B")?t=c:e.includes("A.X-4-Light-awq")&&(t=u),(0,r.ae)({model:t(e),middleware:o})},f=e=>{let t=s.Jn.find(t=>t.id===e);if(!t)throw Error(`Model not found: ${e}`);let a=p(t.apiIdentifier,{apiKey:t.apiKey,responseMode:"streaming"});return(0,r.ae)({model:a,middleware:o})},y=e=>(0,r.ae)({model:(0,i.N)(e),middleware:o})},37758:()=>{},71003:(e,t,a)=>{"use strict";a.d(t,{Jn:()=>r,oQ:()=>o});let r=[{id:"지자체 공간정보 플랫폼 챗봇",label:"지자체 공간정보 플랫폼 챗봇",apiIdentifier:"EIjFYMz0dmL2HxkQJuBifqvF",description:"지자체 공간정보 플랫폼 챗봇",apiKey:"app-Hd682MZtRJh95QtTUe5H9aCl"},{id:"지도개발 어시스턴트",label:"지도개발 어시스턴트",apiIdentifier:"EIjFYMz0dmL2HxkQJuBifqvF",description:"지도개발을 위한 문서를 학습한 어시스턴트",apiKey:"app-EIjFYMz0dmL2HxkQJuBifqvF"}],o="지자체 공간정보 플랫폼 챗봇"},77926:()=>{},85457:(e,t,a)=>{"use strict";function r(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}function o(e){return e.map(e=>({id:e.id,parts:e.parts,role:e.role,content:"",createdAt:e.createdAt,experimental_attachments:e.attachments??[]}))}function s(e){return e.filter(e=>"user"===e.role).at(-1)}a.d(t,{Bv:()=>s,Vs:()=>n,b2:()=>o,lk:()=>r});let n=e=>{let t=e.slice(-4);return console.log("Pruned messages count:",t.length),t.map((e,a)=>{if("assistant"===e.role&&Array.isArray(e.parts)){if(e.parts.some(e=>"tool-invocation"===e.type&&e.toolInvocation?.state==="call"&&!e.toolInvocation?.result)&&a<t.length-1){let r=t[a+1];if(r?.role==="user"){let t=e.parts.filter(e=>"tool-invocation"!==e.type||"tool-invocation"===e.type&&e.toolInvocation?.result);return 0!==t.length&&t.some(e=>"text"===e.type)||t.unshift({type:"text",text:"요청을 처리하고 있습니다. 추가로 도움이 필요한 것이 있나요?"}),{...e,parts:t}}}let r=e.parts.map(e=>{if("tool-invocation"===e.type&&e.toolInvocation?.state==="result"){let t=e.toolInvocation?.toolName,a=e.toolInvocation.result;if(["searchAddress","searchOrigin","searchDestination"].includes(t)&&a&&"object"==typeof a&&a.result?.jusoList){let t=a.result.jusoList.map(e=>{let{geom:t,buildGeom:a,...r}=e;return r});return{...e,toolInvocation:{...e.toolInvocation,result:{...a,result:{...a.result,jusoList:t}}}}}if("searchDirections"===t&&a&&"object"==typeof a&&a.routes){let t=a.routes.map(e=>{let{sections:t,...a}=e;return a});return{...e,toolInvocation:{...e.toolInvocation,result:{...a,routes:t}}}}if("performDensityAnalysis"===t&&a&&"object"==typeof a){let{features:t,...r}=a;return{...e,toolInvocation:{...e.toolInvocation,result:{...r,featuresCount:Array.isArray(t)?t.length:0}}}}}return e});return{...e,parts:r}}return e})}}};