import {NextAuthConfig, Session, User} from "next-auth";

export const authConfig = {
	pages: {
		signIn: "/login",
		// verifyRequest: `/login`,
		// error: "/login", // Error code passed in query string as ?error=
		newUser: "/",
	},
	providers: [
		// added later in auth.ts since it requires bcrypt which is only compatible with Node.js
		// while this file is also used in non-Node.js environments
	],
	callbacks: {
		authorized({ auth, request: { nextUrl } }) {
			const isLoggedIn = !!auth?.user;
			const isOnChat = nextUrl.pathname.startsWith("/geon-2d-map");
			const isOnLogin = nextUrl.pathname.startsWith("/login");
			const isOnRoot = nextUrl.pathname === "/";
	  
			// 루트 경로로 접근시 /geon-2d-map으로 리다이렉트
			if (isOnRoot) {
			  return Response.redirect(new URL("/geon-2d-map", nextUrl));
			}
	  
			// 로그인된 상태에서 로그인/회원가입 페이지 접근시 /geon-2d-map으로 리다이렉트
			if (isLoggedIn && (isOnLogin)) {
			  return Response.redirect(new URL("/geon-2d-map", nextUrl));
			}
	  
			// 로그인/회원가입 페이지는 항상 접근 가능
			if (isOnLogin) {
			  return true;
			}
	  
			// /geon-2d-map 페이지는 로그인한 사용자만 접근 가능
			if (isOnChat) {
			  return isLoggedIn;
			}
	  
			return true;
		  },
	},
} satisfies NextAuthConfig;
