### 실제 사용 시 환경변수로 아래의 값을 변경하세요.

# Redis 데이터베이스 연결 URL
REDIS_URL=rediss://username:password@hostname:port

# PostgreSQL 데이터베이스 연결 정보
POSTGRES_URL=postgres://username:password@hostname:port/database?sslmode=require
POSTGRES_USER=your_postgres_username
POSTGRES_HOST=your_postgres_host
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DATABASE=your_database_name

# GitHub OAuth 인증 정보
AUTH_GITHUB_ID=your_github_client_id
AUTH_GITHUB_SECRET=your_github_client_secret

# GitLab OAuth 인증 정보
AUTH_GITLAB_ID=your_gitlab_client_id
AUTH_GITLAB_SECRET=your_gitlab_client_secret

# NextAuth.js 암호화 키
NEXTAUTH_SECRET=your_nextauth_secret

# OpenAI API 키
OPENAI_API_KEY=your_openai_api_key

# GEON API 키 (OpenAI API 키와 같은 값 사용)
GEON_API_KEY=your_geon_api_key

# GEON API 인증용 계정 정보 (백엔드 API 요청 시 사용)
GEON_API_USER_ID=geonuser
GEON_API_USER_PASSWORD=wavus1234!

# 프론트엔드 로그인용 계정 정보
FRONTEND_LOGIN_USER_ID=admin
FRONTEND_LOGIN_PASSWORD=password1234
