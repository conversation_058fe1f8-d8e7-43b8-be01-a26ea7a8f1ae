"use strict";exports.id=746,exports.ids=[746],exports.modules={56906:(e,r,t)=>{t.d(r,{d:()=>s});let s=(e,r=21)=>(t=r)=>{let s="",n=0|t;for(;n--;)s+=e[Math.random()*e.length|0];return s}},58747:e=>{let r="undefined"!=typeof Buffer,t=/"(?:_|\\u005[Ff])(?:_|\\u005[Ff])(?:p|\\u0070)(?:r|\\u0072)(?:o|\\u006[Ff])(?:t|\\u0074)(?:o|\\u006[Ff])(?:_|\\u005[Ff])(?:_|\\u005[Ff])"\s*:/,s=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/;function n(e,n,o){null==o&&null!==n&&"object"==typeof n&&(o=n,n=void 0),r&&Buffer.isBuffer(e)&&(e=e.toString()),e&&65279===e.charCodeAt(0)&&(e=e.slice(1));let i=JSON.parse(e,n);if(null===i||"object"!=typeof i)return i;let u=o&&o.protoAction||"error",l=o&&o.constructorAction||"error";if("ignore"===u&&"ignore"===l)return i;if("ignore"!==u&&"ignore"!==l){if(!1===t.test(e)&&!1===s.test(e))return i}else if("ignore"!==u&&"ignore"===l){if(!1===t.test(e))return i}else if(!1===s.test(e))return i;return a(i,{protoAction:u,constructorAction:l,safe:o&&o.safe})}function a(e,{protoAction:r="error",constructorAction:t="error",safe:s}={}){let n=[e];for(;n.length;){let e=n;for(let a of(n=[],e)){if("ignore"!==r&&Object.prototype.hasOwnProperty.call(a,"__proto__")){if(!0===s)return null;if("error"===r)throw SyntaxError("Object contains forbidden prototype property");delete a.__proto__}if("ignore"!==t&&Object.prototype.hasOwnProperty.call(a,"constructor")&&Object.prototype.hasOwnProperty.call(a.constructor,"prototype")){if(!0===s)return null;if("error"===t)throw SyntaxError("Object contains forbidden prototype property");delete a.constructor}for(let e in a){let r=a[e];r&&"object"==typeof r&&n.push(r)}}}return e}function o(e,r,t){let s=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return n(e,r,t)}finally{Error.stackTraceLimit=s}}e.exports=o,e.exports.default=o,e.exports.parse=o,e.exports.safeParse=function(e,r){let t=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return n(e,r,{safe:!0})}catch(e){return null}finally{Error.stackTraceLimit=t}},e.exports.scan=a},86746:(e,r,t)=>{t.d(r,{$C:()=>d,Ds:()=>A,GU:()=>E,HD:()=>k,JA:()=>l,N8:()=>g,NR:()=>i,PX:()=>c,S:()=>$,WL:()=>y,Z9:()=>P,ZZ:()=>v,ae:()=>L,cV:()=>T,cb:()=>u,eu:()=>m,hK:()=>f,m2:()=>o,n_:()=>M,sl:()=>S,u1:()=>h,v0:()=>b,xI:()=>w,zf:()=>p});var s=t(87113),n=t(56906),a=t(58747);function o(...e){return e.reduce((e,r)=>({...e,...null!=r?r:{}}),{})}function i(e){return new ReadableStream({async pull(r){try{let{value:t,done:s}=await e.next();s?r.close():r.enqueue(t)}catch(e){r.error(e)}},cancel(){}})}async function u(e){return null==e?Promise.resolve():new Promise(r=>setTimeout(r,e))}function l(){let e,r,t,s="",n=[];function a(e,r){if(""===e)return void o(r);if(e.startsWith(":"))return;let t=e.indexOf(":");if(-1===t)return void i(e,"");let s=e.slice(0,t),n=t+1;i(s,n<e.length&&" "===e[n]?e.slice(n+1):e.slice(n))}function o(s){n.length>0&&(s.enqueue({event:e,data:n.join("\n"),id:r,retry:t}),n=[],e=void 0,t=void 0)}function i(s,a){switch(s){case"event":e=a;break;case"data":n.push(a);break;case"id":r=a;break;case"retry":let o=parseInt(a,10);isNaN(o)||(t=o)}}return new TransformStream({transform(e,r){let{lines:t,incompleteLine:n}=function(e,r){let t=[],s=e;for(let e=0;e<r.length;){let n=r[e++];"\n"===n?(t.push(s),s=""):"\r"===n?(t.push(s),s="","\n"===r[e]&&e++):s+=n}return{lines:t,incompleteLine:s}}(s,e);s=n;for(let e=0;e<t.length;e++)a(t[e],r)},flush(e){a(s,e),o(e)}})}function c(e){let r={};return e.headers.forEach((e,t)=>{r[t]=e}),r}var f=({prefix:e,size:r=16,alphabet:t="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",separator:a="-"}={})=>{let o=(0,n.d)(t,r);if(null==e)return o;if(t.includes(a))throw new s.Di({argument:"separator",message:`The separator "${a}" must not be part of the alphabet "${t}".`});return r=>`${e}${a}${o(r)}`},d=f();function h(e){return null==e?"unknown error":"string"==typeof e?e:e instanceof Error?e.message:JSON.stringify(e)}function p(e){return e instanceof Error&&("AbortError"===e.name||"TimeoutError"===e.name)}function y({apiKey:e,environmentVariableName:r,apiKeyParameterName:t="apiKey",description:n}){if("string"==typeof e)return e;if(null!=e)throw new s.Kq({message:`${n} API key must be a string.`});if("undefined"==typeof process)throw new s.Kq({message:`${n} API key is missing. Pass it using the '${t}' parameter. Environment variables is not supported in this environment.`});if(null==(e=process.env[r]))throw new s.Kq({message:`${n} API key is missing. Pass it using the '${t}' parameter or the ${r} environment variable.`});if("string"!=typeof e)throw new s.Kq({message:`${n} API key must be a string. The value of the ${r} environment variable is not a string.`});return e}var m=Symbol.for("vercel.ai.validator");function v({value:e,schema:r}){var t;let n="object"==typeof r&&null!==r&&m in r&&!0===r[m]&&"validate"in r?r:(t=r,{[m]:!0,validate:e=>{let r=t.safeParse(e);return r.success?{success:!0,value:r.data}:{success:!1,error:r.error}}});try{if(null==n.validate)return{success:!0,value:e};let r=n.validate(e);if(r.success)return r;return{success:!1,error:s.iM.wrap({value:e,cause:r.error})}}catch(r){return{success:!1,error:s.iM.wrap({value:e,cause:r})}}}function g({text:e,schema:r}){try{let t=a.parse(e);if(null==r)return{success:!0,value:t,rawValue:t};let s=v({value:t,schema:r});return s.success?{...s,rawValue:t}:s}catch(r){return{success:!1,error:s.u6.isInstance(r)?r:new s.u6({text:e,cause:r})}}}function b(e){try{return a.parse(e),!0}catch(e){return!1}}function w({provider:e,providerOptions:r,schema:t}){if((null==r?void 0:r[e])==null)return;let n=v({value:r[e],schema:t});if(!n.success)throw new s.Di({argument:"providerOptions",message:`invalid ${e} provider options`,cause:n.error});return n.value}var I=()=>globalThis.fetch,E=async({url:e,headers:r,body:t,failedResponseHandler:s,successfulResponseHandler:n,abortSignal:a,fetch:o})=>x({url:e,headers:{"Content-Type":"application/json",...r},body:{content:JSON.stringify(t),values:t},failedResponseHandler:s,successfulResponseHandler:n,abortSignal:a,fetch:o}),$=async({url:e,headers:r,formData:t,failedResponseHandler:s,successfulResponseHandler:n,abortSignal:a,fetch:o})=>x({url:e,headers:r,body:{content:t,values:Object.fromEntries(t.entries())},failedResponseHandler:s,successfulResponseHandler:n,abortSignal:a,fetch:o}),x=async({url:e,headers:r={},body:t,successfulResponseHandler:n,failedResponseHandler:a,abortSignal:o,fetch:i=I()})=>{try{var u;let l=await i(e,{method:"POST",headers:(u=r,Object.fromEntries(Object.entries(u).filter(([e,r])=>null!=r))),body:t.content,signal:o}),f=c(l);if(!l.ok){let r;try{r=await a({response:l,url:e,requestBodyValues:t.values})}catch(r){if(p(r)||s.hL.isInstance(r))throw r;throw new s.hL({message:"Failed to process error response",cause:r,statusCode:l.status,url:e,responseHeaders:f,requestBodyValues:t.values})}throw r.value}try{return await n({response:l,url:e,requestBodyValues:t.values})}catch(r){if(r instanceof Error&&(p(r)||s.hL.isInstance(r)))throw r;throw new s.hL({message:"Failed to process successful response",cause:r,statusCode:l.status,url:e,responseHeaders:f,requestBodyValues:t.values})}}catch(r){if(p(r))throw r;if(r instanceof TypeError&&"fetch failed"===r.message){let n=r.cause;if(null!=n)throw new s.hL({message:`Cannot connect to API: ${n.message}`,cause:n,url:e,requestBodyValues:t.values,isRetryable:!0})}throw r}},S=({errorSchema:e,errorToMessage:r,isRetryable:t})=>async({response:n,url:o,requestBodyValues:i})=>{let u=await n.text(),l=c(n);if(""===u.trim())return{responseHeaders:l,value:new s.hL({message:n.statusText,url:o,requestBodyValues:i,statusCode:n.status,responseHeaders:l,responseBody:u,isRetryable:null==t?void 0:t(n)})};try{let c=function({text:e,schema:r}){try{let t=a.parse(e);if(null==r)return t;return function({value:e,schema:r}){let t=v({value:e,schema:r});if(!t.success)throw s.iM.wrap({value:e,cause:t.error});return t.value}({value:t,schema:r})}catch(r){if(s.u6.isInstance(r)||s.iM.isInstance(r))throw r;throw new s.u6({text:e,cause:r})}}({text:u,schema:e});return{responseHeaders:l,value:new s.hL({message:r(c),url:o,requestBodyValues:i,statusCode:n.status,responseHeaders:l,responseBody:u,data:c,isRetryable:null==t?void 0:t(n,c)})}}catch(e){return{responseHeaders:l,value:new s.hL({message:n.statusText,url:o,requestBodyValues:i,statusCode:n.status,responseHeaders:l,responseBody:u,isRetryable:null==t?void 0:t(n)})}}},A=e=>async({response:r})=>{let t=c(r);if(null==r.body)throw new s.Tt({});return{responseHeaders:t,value:r.body.pipeThrough(new TextDecoderStream).pipeThrough(l()).pipeThrough(new TransformStream({transform({data:r},t){"[DONE]"!==r&&t.enqueue(g({text:r,schema:e}))}}))}},T=e=>async({response:r,url:t,requestBodyValues:n})=>{let a=await r.text(),o=g({text:a,schema:e}),i=c(r);if(!o.success)throw new s.hL({message:"Invalid JSON response",cause:o.error,statusCode:r.status,responseHeaders:i,responseBody:a,url:t,requestBodyValues:n});return{responseHeaders:i,value:o.value,rawValue:o.rawValue}},k=()=>async({response:e,url:r,requestBodyValues:t})=>{let n=c(e);if(!e.body)throw new s.hL({message:"Response body is empty",url:r,requestBodyValues:t,statusCode:e.status,responseHeaders:n,responseBody:void 0});try{let r=await e.arrayBuffer();return{responseHeaders:n,value:new Uint8Array(r)}}catch(a){throw new s.hL({message:"Failed to read response as array buffer",url:r,requestBodyValues:t,statusCode:e.status,responseHeaders:n,responseBody:void 0,cause:a})}},{btoa:O,atob:_}=globalThis;function P(e){let r=_(e.replace(/-/g,"+").replace(/_/g,"/"));return Uint8Array.from(r,e=>e.codePointAt(0))}function M(e){let r="";for(let t=0;t<e.length;t++)r+=String.fromCodePoint(e[t]);return O(r)}function L(e){return null==e?void 0:e.replace(/\/$/,"")}},87113:(e,r,t)=>{t.d(r,{Ch:()=>es,Di:()=>P,Kq:()=>z,M3:()=>j,Tt:()=>A,b8:()=>ed,bD:()=>g,cj:()=>ep,eM:()=>Y,hL:()=>E,iM:()=>eu,k9:()=>ey,u1:()=>T,u6:()=>D,xn:()=>V});var s,n,a,o,i,u,l,c,f,d,h,p,y="vercel.ai.error",m=Symbol.for(y),v=class e extends Error{constructor({name:e,message:r,cause:t}){super(r),this[s]=!0,this.name=e,this.cause=t}static isInstance(r){return e.hasMarker(r,y)}static hasMarker(e,r){let t=Symbol.for(r);return null!=e&&"object"==typeof e&&t in e&&"boolean"==typeof e[t]&&!0===e[t]}};s=m;var g=v,b="AI_APICallError",w=`vercel.ai.error.${b}`,I=Symbol.for(w),E=class extends g{constructor({message:e,url:r,requestBodyValues:t,statusCode:s,responseHeaders:a,responseBody:o,cause:i,isRetryable:u=null!=s&&(408===s||409===s||429===s||s>=500),data:l}){super({name:b,message:e,cause:i}),this[n]=!0,this.url=r,this.requestBodyValues=t,this.statusCode=s,this.responseHeaders=a,this.responseBody=o,this.isRetryable=u,this.data=l}static isInstance(e){return g.hasMarker(e,w)}};n=I;var $="AI_EmptyResponseBodyError",x=`vercel.ai.error.${$}`,S=Symbol.for(x),A=class extends g{constructor({message:e="Empty response body"}={}){super({name:$,message:e}),this[a]=!0}static isInstance(e){return g.hasMarker(e,x)}};function T(e){return null==e?"unknown error":"string"==typeof e?e:e instanceof Error?e.message:JSON.stringify(e)}a=S;var k="AI_InvalidArgumentError",O=`vercel.ai.error.${k}`,_=Symbol.for(O),P=class extends g{constructor({message:e,cause:r,argument:t}){super({name:k,message:e,cause:r}),this[o]=!0,this.argument=t}static isInstance(e){return g.hasMarker(e,O)}};o=_;var M="AI_InvalidPromptError",L=`vercel.ai.error.${M}`,C=Symbol.for(L),j=class extends g{constructor({prompt:e,message:r,cause:t}){super({name:M,message:`Invalid prompt: ${r}`,cause:t}),this[i]=!0,this.prompt=e}static isInstance(e){return g.hasMarker(e,L)}};i=C;var N="AI_InvalidResponseDataError",q=`vercel.ai.error.${N}`,B=Symbol.for(q),V=class extends g{constructor({data:e,message:r=`Invalid response data: ${JSON.stringify(e)}.`}){super({name:N,message:r}),this[u]=!0,this.data=e}static isInstance(e){return g.hasMarker(e,q)}};u=B;var F="AI_JSONParseError",J=`vercel.ai.error.${F}`,R=Symbol.for(J),D=class extends g{constructor({text:e,cause:r}){super({name:F,message:`JSON parsing failed: Text: ${e}.
Error message: ${T(r)}`,cause:r}),this[l]=!0,this.text=e}static isInstance(e){return g.hasMarker(e,J)}};l=R;var K="AI_LoadAPIKeyError",U=`vercel.ai.error.${K}`,Z=Symbol.for(U),z=class extends g{constructor({message:e}){super({name:K,message:e}),this[c]=!0}static isInstance(e){return g.hasMarker(e,U)}};c=Z;var G=Symbol.for("vercel.ai.error.AI_LoadSettingError"),H=Symbol.for("vercel.ai.error.AI_NoContentGeneratedError"),W="AI_NoSuchModelError",X=`vercel.ai.error.${W}`,Q=Symbol.for(X),Y=class extends g{constructor({errorName:e=W,modelId:r,modelType:t,message:s=`No such ${t}: ${r}`}){super({name:e,message:s}),this[f]=!0,this.modelId=r,this.modelType=t}static isInstance(e){return g.hasMarker(e,X)}};f=Q;var ee="AI_TooManyEmbeddingValuesForCallError",er=`vercel.ai.error.${ee}`,et=Symbol.for(er),es=class extends g{constructor(e){super({name:ee,message:`Too many values for a single embedding call. The ${e.provider} model "${e.modelId}" can only embed up to ${e.maxEmbeddingsPerCall} values per call, but ${e.values.length} values were provided.`}),this[d]=!0,this.provider=e.provider,this.modelId=e.modelId,this.maxEmbeddingsPerCall=e.maxEmbeddingsPerCall,this.values=e.values}static isInstance(e){return g.hasMarker(e,er)}};d=et;var en="AI_TypeValidationError",ea=`vercel.ai.error.${en}`,eo=Symbol.for(ea),ei=class e extends g{constructor({value:e,cause:r}){super({name:en,message:`Type validation failed: Value: ${JSON.stringify(e)}.
Error message: ${T(r)}`,cause:r}),this[h]=!0,this.value=e}static isInstance(e){return g.hasMarker(e,ea)}static wrap({value:r,cause:t}){return e.isInstance(t)&&t.value===r?t:new e({value:r,cause:t})}};h=eo;var eu=ei,el="AI_UnsupportedFunctionalityError",ec=`vercel.ai.error.${el}`,ef=Symbol.for(ec),ed=class extends g{constructor({functionality:e,message:r=`'${e}' functionality not supported.`}){super({name:el,message:r}),this[p]=!0,this.functionality=e}static isInstance(e){return g.hasMarker(e,ec)}};function eh(e){return null===e||"string"==typeof e||"number"==typeof e||"boolean"==typeof e||(Array.isArray(e)?e.every(eh):"object"==typeof e&&Object.entries(e).every(([e,r])=>"string"==typeof e&&eh(r)))}function ep(e){return Array.isArray(e)&&e.every(eh)}function ey(e){return null!=e&&"object"==typeof e&&Object.entries(e).every(([e,r])=>"string"==typeof e&&eh(r))}p=ef}};