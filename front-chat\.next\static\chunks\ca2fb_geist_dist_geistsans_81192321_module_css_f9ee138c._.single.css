/* [project]/node_modules/.pnpm/geist@1.3.1_next@15.3.3_@op_f8000df042c8d8b310bf973985bf4e18/node_modules/geist/dist/geistsans_81192321.module.css [app-client] (css) */
@font-face {
  font-family: GeistSans;
  src: url("../media/Geist_Variable-s.p.781b491f.woff2") format("woff2");
  font-display: swap;
  font-weight: 100 900;
}

@font-face {
  font-family: GeistSans Fallback;
  src: local(Arial);
  ascent-override: 85.83%;
  descent-override: 20.52%;
  line-gap-override: 9.33%;
  size-adjust: 107.19%;
}

.geistsans_81192321-module__k7IecG__className {
  font-family: GeistSans, GeistSans Fallback;
}

.geistsans_81192321-module__k7IecG__variable {
  --font-geist-sans: "Geist<PERSON><PERSON>", "GeistSans Fallback";
}

/*# sourceMappingURL=ca2fb_geist_dist_geistsans_81192321_module_css_f9ee138c._.single.css.map*/