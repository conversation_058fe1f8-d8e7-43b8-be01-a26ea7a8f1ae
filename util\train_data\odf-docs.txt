## Classes

<dl>
<dt><a href="#Map">Map</a></dt>
<dd><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p>
</dd>
</dl>

## Typedefs

<dl>
<dt><a href="#mapInfo">mapInfo</a> : <code>object</code></dt>
<dd></dd>
<dt><a href="#modifyObject">modifyObject</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#splitFeatureCallback">splitFeatureCallback</a> : <code>function</code></dt>
<dd></dd>
<dt><a href="#modifiedCallback">modifiedCallback</a> : <code>function</code></dt>
<dd></dd>
<dt><a href="#odf_scaleInfo_object">odf_scaleInfo_object</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_scaleInfo">odf_scaleInfo</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#forEachFeatureAtPixel_callback">forEachFeatureAtPixel_callback</a> : <code>function</code></dt>
<dd></dd>
</dl>

<a name="Map"></a>

## Map
지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스

**Kind**: global class
**Summary**: Map 생성 클래스
   ```javascript
   let mapContainer = document.getElementById('map');
   const center = new odf.Coordinate(955156.7761, 1951925.0984);
   let mapOption = {
     center: center,
     zoom: 15,
     projection: 'EPSG:5179',
     maxZoom: 20,
     minZoom: 8,
   };
   let map = new odf.Map(mapContainer, mapOption);
   ```

* [Map](#Map)
  * [new Map(mapContainer, mapOption)](#new_Map_new)
  * [.setResizable(resizable)](#Map+setResizable)
  * [.getProjection()](#Map+getProjection) ⇒ <code>Projection</code>
  * [.getInfo()](#Map+getInfo) ⇒ [<code>mapInfo</code>](#mapInfo)
  * [.getCenter()](#Map+getCenter) ⇒ <code>odf.Coordinate</code>
  * [.setCenter(center)](#Map+setCenter)
  * [.getZoom()](#Map+getZoom) ⇒ <code>number</code>
  * [.setZoom(zoom)](#Map+setZoom)
  * [.switchLayer(layerId, condition)](#Map+switchLayer)
  * [.switchLayerList(layers)](#Map+switchLayerList)
  * [.modifyLayer(type, evt, tooltipflag)](#Map+modifyLayer)
  * [.setModifyLayer(type, evt, layer, tooltipflag)](#Map+setModifyLayer)
  * [.modifyComplete(type)](#Map+modifyComplete) ⇒ [<code>modifyObject</code>](#modifyObject)
  * [.sendToServerModified(result, layer, type, flag)](#Map+sendToServerModified) ⇒ <code>String</code> \| <code>Object</code>
  * [.setZIndex(odfId, zIdx)](#Map+setZIndex)
  * [.getZIndex(odfId)](#Map+getZIndex) ⇒ <code>Number</code>
  * [.findLayer(odfId)](#Map+findLayer) ⇒ <code>odf.Layer</code>
  * [.setDragRotate(param)](#Map+setDragRotate)
  * [.setDraggable(param)](#Map+setDraggable)
  * [.setDoubleClickZoomable(param)](#Map+setDoubleClickZoomable)
  * [.setSelectCluster(options)](#Map+setSelectCluster)
  * [.removeSelectCluster()](#Map+removeSelectCluster)
  * [.splitPolygonByLine(polygonFeature, lineFeature, callback)](#Map+splitPolygonByLine)
  * [.setZoomable(param)](#Map+setZoomable)
  * [.selectFeatureOnClick(evt)](#Map+selectFeatureOnClick)
  * [.selectFeature(parameters)](#Map+selectFeature) ⇒ <code>Array.&lt;Feature&gt;</code>
  * [.deleteVertex(extentFeature, targetFeature, callback)](#Map+deleteVertex)
  * [.selectFeatureOnArea(drawType, callback, targetLayer, featureReturn)](#Map+selectFeatureOnArea) ⇒ <code>Array</code>
  * [.getODFLayers()](#Map+getODFLayers) ⇒ <code>List.&lt;Layer&gt;</code>
  * [.getODFLayerList()](#Map+getODFLayerList) ⇒ <code>List.&lt;Layer&gt;</code>
  * [.getODFLayer(searchString, likeStatus)](#Map+getODFLayer) ⇒ <code>List.&lt;Layer&gt;</code>
  * [.getMarkers()](#Map+getMarkers) ⇒ <code>js\_Map.&lt;String, Marker&gt;</code>
  * [.getMarker(markerId)](#Map+getMarker) ⇒ <code>Marker</code>
  * [.getPopups()](#Map+getPopups) ⇒ <code>js\_Map.&lt;String, Popup&gt;</code>
  * [.getPopup(popupId)](#Map+getPopup) ⇒ <code>Popup</code>
  * [.setModifiable(flag)](#Map+setModifiable)
  * [.setModifyFeature(feature)](#Map+setModifyFeature)
  * [.setDraggableODFFeature(flag, options)](#Map+setDraggableODFFeature)
  * [.setDraggableODFLayer(layer, options)](#Map+setDraggableODFLayer)
  * [.setDraggableSelectFeature(feature, options)](#Map+setDraggableSelectFeature)
  * [.setDragTranslate(flag, options)](#Map+setDragTranslate)
  * [.getDragTranslateTarget()](#Map+getDragTranslateTarget)
  * [.setDragTranslateTargetFeature()](#Map+setDragTranslateTargetFeature)
  * [.setDragTranslateTargetLayer()](#Map+setDragTranslateTargetLayer)
  * [.setTransformable(flag, options)](#Map+setTransformable)
  * [.setTransformLayer(layer, options)](#Map+setTransformLayer)
  * [.setTransformFeature(feature, options)](#Map+setTransformFeature)
  * [.setTranslate(layer, options)](#Map+setTranslate)
  * [.removeTranslate()](#Map+removeTranslate)
  * [.setSnap(options, layerArray)](#Map+setSnap)
  * [.removeSnap()](#Map+removeSnap)
  * [.getSLDScale()](#Map+getSLDScale) ⇒ [<code>odf\_scaleInfo</code>](#odf_scaleInfo)
  * [.hasFeatureAtPixel(evt)](#Map+hasFeatureAtPixel) ⇒ <code>Boolean</code>
  * [.forEachFeatureAtPixel(pixel, callback, layerFlag, options)](#Map+forEachFeatureAtPixel)
  * [.getPixelFromCoordinate(coordinate)](#Map+getPixelFromCoordinate)
  * [.getCoordinateFromPixel(pixel)](#Map+getCoordinateFromPixel)

<a name="new_Map_new"></a>

### new Map(mapContainer, mapOption)

| Param | Type | Description |
| --- | --- | --- |
| mapContainer | <code>Element</code> | 지도에 사용할 Div Element |
| mapOption | <code>Object</code> | 지도 생성에 사용할 옵션 |
| mapOption.center | <code>Odf.Coordinate</code> | 지도 중심점 좌표 |
| mapOption.zoom | <code>Number</code> | 현재 확대 레벨 |
| mapOption.maxZoom | <code>Number</code> | 최대 확대레벨 |
| mapOption.minZoom | <code>Number</code> | 최소 확대레벨 |
| mapOption.projection | <code>String</code> | 좌표계 SRS ID |
| mapOption.proxyURL | <code>String</code> | 프록시 URL |
| mapOption.proxyParam | <code>String</code> | 프록시에서 사용할 PARAM명 없으면 PARAMETER명 없이 넘김 |
| mapOption.apiGateWayKey | <code>String</code> | api GateWay Key |
| mapOption.basemap | <code>odf\_basemap\_option</code> | 베이스맵 옵션(사용할 베이스맵 선택) |
| mapOption.baroEMapURL | <code>String</code> | 바로e맵 경로 |
| mapOption.baroEMapKey | <code>String</code> | 바로e맵 API KEY |
| mapOption.vWorldURL | <code>String</code> | vWord url |
| mapOption.vWorldKey | <code>String</code> | [선택] vWord API KEY |
| mapOption.kakaoURL | <code>String</code> | kakao url |
| mapOption.controlGroup | <code>String</code> | 컨트롤 그룹 구조 |
| mapOption.optimization | <code>String</code> | 배경지도 최적화 기능 사용여부 |
| mapOption.wfsPostPropertyNameEncodeYn | <code>String</code> | wfs 포스트 getFeature요청시 propertyName 파라미터 인코딩 여부 |
| mapOption.wfsPostCQLFilterTwiceEncodeYn | <code>String</code> | wfs 포스트 getFeature요청시 cql_filter 파라미터 인코딩 여부 |
| mapOption.wmsPostCQLFilterTwiceEncodeYn | <code>String</code> | wms 포스트 getFeature요청시 cql_filter 파라미터 인코딩 여부 사용할 컨트롤에 대해서는 모두 정의되있어야함. (ex)  [        ['basemap'],        ['move'],        ['zoom'],        ['initscreen', 'rotate', 'fullscreen'],        ['clear', 'draw', 'measure', 'image', 'print', 'download', 'overviewmap'],        ['swiper', 'dividemap'],     ]; |
| [mapOption.crtfckey] | <code>String</code> | api 사용시 인증 키 |

<a name="Map+setResizable"></a>

### map.setResizable(resizable)
지도 리사이징

```javascript
      let map = new odf.Map(mapContainer, mapOption);

      map.setResizable(true);
      map.setResizable(false);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| resizable | <code>Boolean</code> | 사용여부 True : 허용, false : 차단 |

<a name="Map+getProjection"></a>

### map.getProjection() ⇒ <code>Projection</code>
지도의 좌표계 정보 조회

   ```javascript
   let map = new odf.Map(mapContainer, mapOption);
   map.getProjection();
   ```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>Projection</code> - 지도의 좌표계
<a name="Map+getInfo"></a>

### map.getInfo() ⇒ [<code>mapInfo</code>](#mapInfo)
지도 정보 조회

   ```javascript
   let map = new odf.Map(mapContainer, mapOption);
   const mapInfo = map.getInfo();
   const zoom = mapInfo.zoom;
   ```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: [<code>mapInfo</code>](#mapInfo) - 지도 정보
<a name="Map+getCenter"></a>

### map.getCenter() ⇒ <code>odf.Coordinate</code>
지도 중심점 좌표 정보 조회

   ```javascript
   let map = new odf.Map(mapContainer, mapOption);
   const center = map.getCenter();
   ```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>odf.Coordinate</code> - 중심 좌표
<a name="Map+setCenter"></a>

### map.setCenter(center)
지도 중심점 좌표 설정
   ```javascript
   let map = new odf.Map(mapContainer, mapOption);
   const center = new odf.Coordinate(955156.7761, 1951925.0984);
   map.setCenter(center);
   ```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| center | <code>Coordinate</code> | 지도 중심점 좌표 설정 {위도, 경도} |

<a name="Map+getZoom"></a>

### map.getZoom() ⇒ <code>number</code>
지도 줌레벨 조회
   ```javascript
   let map = new odf.Map(mapContainer, mapOption);
   const zoom = map.getZoom();
   ```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>number</code> - zoom level
<a name="Map+setZoom"></a>

### map.setZoom(zoom)
지도 줌레벨 설정

   ```javascript
   let map = new odf.Map(mapContainer, mapOption);
   map.setZoom(17);
   ```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| zoom | <code>number</code> | 설정할 줌레벨 |

<a name="Map+switchLayer"></a>

### map.switchLayer(layerId, condition)
지도 레이어 on/off
   ```javascript
   let map = new odf.Map(mapContainer, mapOption);
   ```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| layerId | <code>string</code> | : odf-layer-생성시간 |
| condition | <code>boolean</code> | : on/off 여부 |

<a name="Map+switchLayerList"></a>

### map.switchLayerList(layers)
지도에 등록된 레이어 목록을 layers로 교체
```javascript
map.switchLayerList(layers);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| layers | <code>Array.&lt;Layer&gt;</code> | : 바꿔치기 할 레이어 목록 |

<a name="Map+modifyLayer"></a>

### map.modifyLayer(type, evt, tooltipflag)
레이어 편집 - 지도상에서 선택한 레이어 수정
```javascript
let map = new odf.Map(mapContainer, mapOption);
odf.event.addListener(map, 'click', function (evt) {
      map.modifyLayer(type,evt);
        });
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| type | <code>String</code> | : 레이어편집유형 ('update', 'insert', 'delete') |
| evt | <code>event</code> | : 지도객체 마우스 클릭 이벤트 |
| tooltipflag | <code>boolean</code> | : 툴팁생성여부 (default : false); |

<a name="Map+setModifyLayer"></a>

### map.setModifyLayer(type, evt, layer, tooltipflag)
레이어 편집 - 특정 레이어에 대한 수정
```javascript
let map = new odf.Map(mapContainer, mapOption);
odf.event.addListener(map, 'click', function (evt) {
      map.modifyLayer(type,evt, layer);
        });
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| type | <code>String</code> | : 레이어편집유형 ('update', 'insert', 'delete','moveFeature','transform') |
| evt | <code>event</code> | : 지도객체 마우스 클릭 이벤트 |
| layer | <code>Layer</code> | : layer 객체 |
| tooltipflag | <code>boolean</code> | : 툴팁생성여부 (default : false); |

<a name="Map+modifyComplete"></a>

### map.modifyComplete(type) ⇒ [<code>modifyObject</code>](#modifyObject)
레이어 수정 완료 - 수정된 피쳐값 리턴

```javascript
 map.modifyComplete('update');
```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: [<code>modifyObject</code>](#modifyObject) - 피쳐 결과 배열

| Param | Type | Description |
| --- | --- | --- |
| type | <code>String</code> | : 레이어편집유형 ('update', 'insert', 'delete' , 'moveFeature','transform') |

<a name="Map+sendToServerModified"></a>

### map.sendToServerModified(result, layer, type, flag) ⇒ <code>String</code> \| <code>Object</code>
서버에 피쳐값 전송

   ```javascript
 map.sendToServerModified(result, _layer, type, flag);
```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>String</code> \| <code>Object</code> - Flag === True : 서버와 직접 통신 시 서버 통신 결과 ; Flag === false : 서버 전송할 XML 데이터 문자열

| Param | Type | Description |
| --- | --- | --- |
| result | <code>Array</code> \| <code>Feature</code> | : 피쳐 배열 |
| layer | <code>Layer</code> | 타겟 레이어 |
| type | <code>String</code> | : 레이어편집유형 ('update', 'insert', 'delete') |
| flag | <code>boolean</code> | : geoserver 통신 여부 - Default : false |

<a name="Map+setZIndex"></a>

### map.setZIndex(odfId, zIdx)
해당 layer의 z-index 설정
 ```javascript
const layerId = layer.getODFId();
const z = map.getZIndex(layerID, 100);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| odfId | <code>String</code> | : 레이어Id |
| zIdx | <code>Number</code> | : z index 값 |

<a name="Map+getZIndex"></a>

### map.getZIndex(odfId) ⇒ <code>Number</code>
해당 layer의 z-index 반환
 ```javascript
const layerId = layer.getODFId();
const z = map.getZIndex(layerID);
```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>Number</code> - 해당 layer의 z-index.

| Param | Type | Description |
| --- | --- | --- |
| odfId | <code>String</code> | : 레이어Id |

<a name="Map+findLayer"></a>

### map.findLayer(odfId) ⇒ <code>odf.Layer</code>
지도객체에 추가되있는 레이어를 odfId로 찾아 반환
 ```javascript
const layerId = layer.getODFId();
const findLayer = map.findLayer(layerID);
```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>odf.Layer</code> - 레이어 id와 일치하는 레이어

| Param | Type | Description |
| --- | --- | --- |
| odfId | <code>String</code> | : 레이어Id |

<a name="Map+setDragRotate"></a>

### map.setDragRotate(param)
지도 회전 설정

   ```javascript
   let map = new odf.Map(mapContainer, mapOption);

   map.setDragRotate(true);
   map.setDragRotate(false);

   ```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| param | <code>Boolean</code> | 사용여부 True : 허용, false : 차단 |

<a name="Map+setDraggable"></a>

### map.setDraggable(param)
지도 마우스 드래그 설정

   ```javascript
   let map = new odf.Map(mapContainer, mapOption);

   map.setDraggable(true);
   map.setDraggable(false);

   ```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| param | <code>Boolean</code> | 사용여부 True : 허용, false : 차단 |

<a name="Map+setDoubleClickZoomable"></a>

### map.setDoubleClickZoomable(param)
지도 더블클릭 줌 설정

   ```javascript
   let map = new odf.Map(mapContainer, mapOption);

   map.setDoubleClickZoomable(true);
   map.setDoubleClickZoomable(false);

   ```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| param | <code>Boolean</code> | 사용여부 True : 허용, false : 차단 |

<a name="Map+setSelectCluster"></a>

### map.setSelectCluster(options)
클러스터 표출 인터렉션 추가

   ```javascript
   let map = new odf.Map(mapContainer, mapOption);

   map.setSelectCluster(
     layers : [pointLayer], //클러스터 클릭 인터렉션 설정 할 레이어 배열 목록
    addCallback : function(e){ //클러스터 클릭 시 지도에 피쳐 추가 콜백 함수
      console.log(e) ;
      },
    removeCallback : function(e){ //지도에서 피쳐 사라질 시 콜백 함수
      console.log(e);
        }
   )
   ```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>object</code> | 생성옵션 |
| options.layers | <code>Array</code> | 적용할 대상 레이어 목록 |
| options.addCallback | <code>function</code> | 객체 선택 시 콜백함수 |
| options.removeCallback | <code>function</code> | 객체 선택 해제 시 콜백함수 |

<a name="Map+removeSelectCluster"></a>

### map.removeSelectCluster()
클러스터 표출 인터렉션 제거

   ```javascript
   let map = new odf.Map(mapContainer, mapOption);

   map.removeSelectCluster();
   ```

**Kind**: instance method of [<code>Map</code>](#Map)
<a name="Map+splitPolygonByLine"></a>

### map.splitPolygonByLine(polygonFeature, lineFeature, callback)
라인으로 다각형 자르기 (라인이 다각형을 2분할 시에만 가능 2분할 이상으로 분할 시 원본 유지)

```javascript
let map = new odf.Map(mapContainer, mapOption);
map.splitPolygonByLine(polygon, line, callback);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| polygonFeature | <code>odf.Feature</code> | 대상 피쳐 (polygon, multipolygon 타입만 가능) |
| lineFeature | <code>odf.Feature</code> | cutting 할 피쳐(line | lineString 타입) |
| callback | <code>function</code> | 그리기 완료후 분할된 피쳐, 원본 피쳐 리턴받을 콜백 함수 return {originalFeature : 원본피쳐, splitFeatures : [분할된피쳐1, ....]} |

<a name="Map+setZoomable"></a>

### map.setZoomable(param)
지도 마우스 휠 확대 축소 설정

   ```javascript
   let map = new odf.Map(mapContainer, mapOption);

   map.setZoomable(true);
   map.setZoomable(false);
   ```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| param | <code>Boolean</code> | 사용여부 True : 허용, false : 차단 |

<a name="Map+selectFeatureOnClick"></a>

### map.selectFeatureOnClick(evt)
지도에서 클릭한 Feature를 선택

   ```javascript
   let map = new odf.Map(mapContainer, mapOption);
      odf.event.addListener(map, 'click', function (evt) {
      let feature = map.selectFeatureOnClick(evt);
  });
   ```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| evt | <code>event</code> | 마우스 지도 클릭 이벤트 |

<a name="Map+selectFeature"></a>

### map.selectFeature(parameters) ⇒ <code>Array.&lt;Feature&gt;</code>
지도에서 클릭한 Feature를 선택

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>Array.&lt;Feature&gt;</code> - 선택된 영역으로 부터 포함관계인 피쳐 배열

| Param | Type | Description |
| --- | --- | --- |
| parameters | <code>Object</code> |  |
| [parameters.extractType] | <code>String</code> | 피쳐 추출 유형 - draw 직접 그린 도형 내에 속한 피쳐 추출 - view 현재 지도영역에서 피쳐 추출 - pixel 특정 픽셀에 곂치는 피쳐 추출 - feature 특정 도형(compareFeature) 내에 속한 피쳐 추출 - cql cql_filter로 피처 추출 |
| [parameters.drawType] | <code>String</code> | 그리기 유형. extractType의 값이 'draw'일 경우 필수값 - polygon 다각형 - box 사각형 - circle 원형 - point 점 |
| [parameters.targetLayer] | <code>Layer</code> | 특정 Layer 내에서만 feature 추출, 미입력시 모든 레이어에서 추출 |
| [parameters.featureReturn] | <code>Boolean</code> | 필터링 대상이 되는 도형 또는 영역 정보를 함께 리턴할지 여부(기본값 false) |
| [parameters.pixel] | <code>Number</code> | 피쳐 추출을 위한 좌표 기준. extractType의 값이 'pixel'일 경우 필수값. |
| [parameters.callback] | <code>function</code> | 그리기 영역으로 부터 선택된 피쳐 배열을 리턴받을 콜백 함수. 없으면 selectFeature 함수에서 결과 반환됨(extractType의 값이 'draw' 타입일 경우 제외) |
| [parameters.permanentDrawLifeCycle] | <code>Boolean</code> | 영구적 그리기 interaction 활성화 여부. extractType의 값이 'draw'일 경우 해당(기본값 true) |
| [parameters.compareFeature] | <code>ODF.Feature</code> | extractType이 feature일 경우, 이 도형 내에 속한 피처 추출 - point 로 geometryType 제한 |
| [parameters.includeNoGeometryData] | <code>Boolean</code> | geometry가 없는 feature도 조회할지 여부(기본값 false, geometry 없는 feature 조회  x) |
| [parameters.cql] | <code>String</code> | cql 문자열 |
| [parameters.maxAreaSize] | <code>Number</code> | 그리기 도형 크기 제한(1000000이 1㎢) |
| [parameters.pointBuffer] | <code>Number</code> | 대상 좌표에 버퍼를 지정 (단위:픽셀) (기본값 20) ※ 피처 추출 유형이 pixel이거나 draw이면서 drawType이 point일 경우 적용됨 |
| [parameters.message] | <code>Object</code> | extractType이 'draw'일때, message 값 정의시 툴팁메세지에 사용자 정의 메세지 적용 |
| parameters.message.DRAWSTART_POINT | <code>String</code> | 점 그리기 시작 안내 메세지 (기본값) '점을 그리기 위해 지도를 클릭해주세요' |
| parameters.message.DRAWSTART_POLYGON | <code>String</code> | 면 그리기 시작 안내 메세지 (기본값) '면을 그리기 위해 지도를 클릭해주세요' |
| parameters.message.DRAWSTART_CIRCLE | <code>String</code> | 원 그리기 시작 안내 메세지 (기본값) '원을 그리기 위해 지도를 클릭해주세요.' |
| parameters.message.DRAWSTART_BOX | <code>String</code> | 사각형 그리기 시작 안내 메세지 (기본값) '사각형을 그리기 위해 지도를 클릭해주세요.' |
| options.message.DRAWEND_DBCLICK | <code>String</code> | 그리기 종료 안내 메세지(더블클릭) (기본값) '더블클릭시 그리기가 종료됩니다.' |
| [parameters.useVisible] | <code>Boolean</code> | 레이어 visible 여부에 따라 selectFeature 대상 변경 |

**Example**
```js
//01. 도형을 직접 그려서 그 영역에서 feature 검색
map.selectFeature({
  extractType : 'draw',
  drawType : 'polygon', //그리기 유형 polygon, box, circle, point 가능. extractType값이 'draw'일때만 유효함
  callback : function(response){//결과 정보를 받을 콜백 함수. 정의하지 않으면 결과 정보 받을 수 없음
    //response.result => 선택된 도형 배열
    //response.feature => 필터링 대상이 되는 도형 또는 영역 정보. featureReturn 값이 true여야 반환됨
  },
 //featureReturn : true, // 필터링 대상이 되는 도형 또는 영역 정보를 함께 리턴할지 여부(기본값 false)
 //targetLayer : tempLayer,//특정 Layer 내에서만 feature 추출, 미입력시 모든 레이어에서 추출
})
   //02. 현재 지도 영역에서 feature 검색
   map.selectFeature({
     extractType : 'view',
     callback : function(response){//결과 정보를 받을 콜백 함수, 없으면 selectFeature 함수에서 결과 반환됨
       //response.result => 선택된 도형 배열
       //response.feature => 필터링 대상이 되는 도형 또는 영역 정보. featureReturn 값이 true여야 반환됨
     },
    //featureReturn : true, // 필터링 대상이 영역 정보를 함께 리턴할지 여부(기본값 false)
    //targetLayer : tempLayer,//특정 Layer 내에서만 feature 추출, 미입력시 모든 레이어에서 추출
   })
   //03. 지도 영역 pixel 값이 위치한 지점 근방에 있는 feature검색
   let features = map.selectFeature({
     extractType : 'pixel',
     pixel : [x좌표,y좌표],//조회할 지도영역 pixel 좌표값
     //callback : function(response){//결과 정보를 받을 콜백 함수, 없으면 selectFeature 함수에서 결과 반환됨
     //  //response.result => 선택된 도형 배열
     //  //response.feature => 필터링 대상이 되는 도형 또는 영역 정보. featureReturn 값이 true여야 반환됨
     //},
    //featureReturn : true, // 필터링 대상이 영역 정보를 함께 리턴할지 여부(기본값 false)
    //targetLayer : tempLayer,//특정 Layer 내에서만 feature 추출, 미입력시 모든 레이어에서 추출
   })
```
<a name="Map+deleteVertex"></a>

### map.deleteVertex(extentFeature, targetFeature, callback)
지도에서 클릭한 Feature를 선택

  ```javascript
   map.deleteVertex(extentFeature, targetFeature);
  ```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| extentFeature | <code>Feature</code> | 버텍스를 삭제할 범위를 지정할 feature |
| targetFeature | <code>Feature</code> | 버텍스 삭제 대상 feature |
| callback | <code>function</code> \| <code>Object</code> | 결과값 리턴받을 콜백 함수 {original : 원본피쳐, changed : 변경된 피쳐} |

<a name="Map+selectFeatureOnArea"></a>

### map.selectFeatureOnArea(drawType, callback, targetLayer, featureReturn) ⇒ <code>Array</code>
지도에서 클릭한 Feature를 선택

  ```javascript
   map.selectFeatureOnArea('polygon', function(e){
   console.log(e);
 })
  ```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>Array</code> - 선택된 영역으로 부터 포함관계인 피쳐 배열

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| drawType | <code>String</code> |  | 영역으로 선택할 그리기 타입 (polygon, box, circle, point, view : 현재지도영역, event 특정이벤트마다 함수 실행) |
| callback | <code>splitPolygonCallback</code> |  | 그리기 영역으로 부터 선택된 피쳐 배열을 리턴받을 콜백 함수 , drawType event 일시 pixel 값 입력 필요 |
| targetLayer | <code>Layer</code> |  | 특정 Layer 내에서만 feature 추출 |
| featureReturn | <code>Boolean</code> | <code>false</code> | 필터링 대상이 되는 도형 또는 영역 정보를 함께 리턴할지 여부(기본값 false) |

<a name="Map+getODFLayers"></a>

### map.getODFLayers() ⇒ <code>List.&lt;Layer&gt;</code>
지도에 등록된 모든 레이어  조회
   ```javascript
   map.getODFLayers();
   ```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>List.&lt;Layer&gt;</code> - - 지도에 등록된 모든 레이어 목록
<a name="Map+getODFLayerList"></a>

### map.getODFLayerList() ⇒ <code>List.&lt;Layer&gt;</code>
지도에 등록된 모든 레이어  조회
   ```javascript
   map.getODFLayerList();
   ```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>List.&lt;Layer&gt;</code> - - 지도에 등록된 모든 레이어 목록
<a name="Map+getODFLayer"></a>

### map.getODFLayer(searchString, likeStatus) ⇒ <code>List.&lt;Layer&gt;</code>
지도에 등록된 레이어를 odfId로 조회
   ```javascript
   //odfId가 'odf-jsonLayer-vector1705996383773s97sq0a2l4h'인 레이어 찾기
   map.getODFLayer('odf-jsonLayer-vector1705996383773s97sq0a2l4h');

   //odfId에 'jsonLayer' 문자열이 포함된 레이어 찾기
   map.getODFLayer('odf-jsonLayer-vector1705996383773s97sq0a2l4h',-1);

   //odfId가 'odf-jsonLayer-vector1705996383773s97sq0a2l4h'인 레이어 찾기
   map.getODFLayer('odf-jsonLayer-vector1705996383773s97sq0a2l4h',0);

   //odfId가 'odf-jsonLayer-'로 시작하는 레이어 찾기
   map.getODFLayer('odf-jsonLayer-',1);

   //odfId가 'unique'로 끝나는는 레이어 찾기
   map.getODFLayer('unique',2);
   ```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>List.&lt;Layer&gt;</code> - 지도에 등록된 모든 레이어 목록

| Param | Type | Description |
| --- | --- | --- |
| searchString | <code>String</code> | 조회할 레이어의 id |
| likeStatus | <code>Number</code> | 검색조건 - searchString 포함 : -1 - searchString과 일치(기본값) : 0, - searchString으로 시작 : 1, - searchString으로 끝남 : 2 |

<a name="Map+getMarkers"></a>

### map.getMarkers() ⇒ <code>js\_Map.&lt;String, Marker&gt;</code>
지도에 등록된 모든 Marker  조회
   ```javascript
   map.getMarkers();
   ```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>js\_Map.&lt;String, Marker&gt;</code> - - 지도에 등록된 모든 Marker를 보관하는 javascript Map 객체
<a name="Map+getMarker"></a>

### map.getMarker(markerId) ⇒ <code>Marker</code>
지도에 등록된 Marker 중 특정 id를 갖는 마커 조회
   ```javascript
   map.getMarker('M_012654987');
   ```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>Marker</code> - - markerId를 id로 갖는 Marker 반환

| Param | Type | Description |
| --- | --- | --- |
| markerId | <code>String</code> | 조회할 마커의 고유 id |

<a name="Map+getPopups"></a>

### map.getPopups() ⇒ <code>js\_Map.&lt;String, Popup&gt;</code>
지도에 등록된 모든 Popup  조회
   ```javascript
   map.getPopups();
   ```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>js\_Map.&lt;String, Popup&gt;</code> - - 지도에 등록된 모든 Popup을 보관하는 javascript Map 객체
<a name="Map+getPopup"></a>

### map.getPopup(popupId) ⇒ <code>Popup</code>
지도에 등록된 Popup 중 특정 id를 갖는 Popup 조회
   ```javascript
   map.getPopup('P_012654987');
   ```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>Popup</code> - - popupId를 id로 갖는 Popup 반환

| Param | Type | Description |
| --- | --- | --- |
| popupId | <code>String</code> | 조회할 팝업의 고유 id |

<a name="Map+setModifiable"></a>

### map.setModifiable(flag)
도형 편집 기능 활성화 상태 변경
```javascript
//modify 인터렉션 활성화
map.setModifiable(true);
//modify 인터렉션 비활성화
map.setModifiable(false);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| flag | <code>Boolean</code> | 변경할 활성화 상태 - true : 활성화 - false : 비활성화 |

<a name="Map+setModifyFeature"></a>

### map.setModifyFeature(feature)
도형 편집 기능 특정 도형으로 제한
```javascript
//modify 인터렉션 활성화
map.setModifiable(true);
//특정 도형 선택하여 그 도형만 편집 가능하게 제한
map.setModifyFeature(feature);
//특정 도형 선택 해제 ※ 도형 선택이 해제되면 modify 인터렉션도 비활성화됨
map.setModifyFeature();
//modify 인터렉션 비활성화
//map.setModifiable(false);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| feature | <code>Feature</code> | 선택된 도형 |

<a name="Map+setDraggableODFFeature"></a>

### map.setDraggableODFFeature(flag, options)
도형 이동 기능 활성화 상태 변경
```javascript
//drag 인터렉션 활성화 - 도형 드래그시 이동 가능
map.setDraggableODFFeature(true);
//drag 인터렉션 비활성화 - 도형 이동 기능 비활성화
map.setDraggableODFFeature(false);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| flag | <code>Boolean</code> | 변경할 활성화 상태 - true : 활성화 - false : 비활성화 |
| options | <code>Obejct</code> | 변형기능 상세 옵션 |
| options.targetFeature | <code>Feature</code> | 변형 대상 도형 |
| options.targetLayer | <code>Layer</code> | 도형 선택을 정의한 레이어로 제한 |
| options.changeTargetFeature | <code>Boolean</code> | 변형 대상 도형 변경 가능 여부(기본값 false) |
| options.useHilight | <code>Boolean</code> | 선택 도형 하이라이트 스타일 적용 여부(기본값 true) |
| options.hilightStyle | <code>odf\_styleOption</code> | 선택 도형 적용 hilightStyle |
| options.modifiedCallback | [<code>modifiedCallback</code>](#modifiedCallback) | 도형 수정시 수정 도형을 받을 콜백함수 |

<a name="Map+setDraggableODFLayer"></a>

### map.setDraggableODFLayer(layer, options)
도형 이동 레이어 제한
```javascript
//drag 인터렉션 활성화 - 도형 드래그시 이동 가능
map.setDraggableODFFeature(true);
//도형이동 레이어 제한 -  선택된 레이어에 속한 도형만 이동 가능
map.setDraggableODFLayer(layer);
//도형이동 레이어 제한 해제 ※도형이동 레이어 제한 해제되어있어도, drag 인터렉션은 활성화된 상태임
map.setDraggableODFLayer();
// drag 인터렉션 비활성화
//map.setDraggableODFFeature(false);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| layer | <code>Layer</code> | 제한할 레이어 |
| options | <code>Obejct</code> | 변형기능 상세 옵션 |
| options.targetFeature | <code>Feature</code> | 변형 대상 도형 |
| options.targetLayer | <code>Layer</code> | 도형 선택을 정의한 레이어로 제한 |
| options.changeTargetFeature | <code>Boolean</code> | 변형 대상 도형 변경 가능 여부(기본값 false) |
| options.useHilight | <code>Boolean</code> | 선택 도형 하이라이트 스타일 적용 여부(기본값 true) |
| options.hilightStyle | <code>odf\_styleOption</code> | 선택 도형 적용 hilightStyle |
| options.modifiedCallback | [<code>modifiedCallback</code>](#modifiedCallback) | 도형 수정시 수정 도형을 받을 콜백함수 |

<a name="Map+setDraggableSelectFeature"></a>

### map.setDraggableSelectFeature(feature, options)
도형 이동 기능 특정 도형으로 제한
```javascript
//drag 인터렉션 활성화 - 도형 드래그시 이동 가능
map.setDraggableODFFeature(true);
//특정 도형 선택하여 그 도형만 이동 가능하게 제한
map.setDraggableSelectFeature(feature);
//특정 도형 선택 해제 ※ 도형 선택이 해제 되었어도, drag 인터렉션은 활성화된 상태임
map.setDraggableSelectFeature();
// drag 인터렉션 비활성화
//map.setDraggableODFFeature(false);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| feature | <code>Feature</code> | 선택된 도형 |
| options | <code>Obejct</code> | 변형기능 상세 옵션 |
| options.targetFeature | <code>Feature</code> | 변형 대상 도형 |
| options.targetLayer | <code>Layer</code> | 도형 선택을 정의한 레이어로 제한 |
| options.changeTargetFeature | <code>Boolean</code> | 변형 대상 도형 변경 가능 여부(기본값 false) |
| options.useHilight | <code>Boolean</code> | 선택 도형 하이라이트 스타일 적용 여부(기본값 true) |
| options.hilightStyle | <code>odf\_styleOption</code> | 선택 도형 적용 hilightStyle |
| options.modifiedCallback | [<code>modifiedCallback</code>](#modifiedCallback) | 도형 수정시 수정 도형을 받을 콜백함수 |

<a name="Map+setDragTranslate"></a>

### map.setDragTranslate(flag, options)
벡터 레이어의 도형 변형(이동) 기능 활성화 상태 변경
```javascript
//DragTranslate 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전/이동) 가능
map.setDragTranslate(true,{
      translateType : [  'move', 'rotate','resize'], //사용할 변형 기능
      targetType : 'feature', // 변형 대상 유형
      targetLayer : wfsLayer,// 레이어 제한
      //targetFeature : feature,//변경 대상 도형 지정. 미정의시 클릭한 도형 편집 활성화
      changeTargetFeature : true,//변형 대상 도형 변경 가능 여부
      //hilightStyle : {
      //  fill : { color: [0, 0, 0, 0.4] },
      //  stroke : { color: [255, 0, 0, 0.7], width: 2 },
      //  image : {
      //    circle: {
      //      fill: { color: [0, 0, 0, 0.4] },
      //      stroke: { color: [255, 0, 0, 0.7], width: 2 },
      //      radius: 10,
      //    },
      //  }
      //}
});
//DragTranslate 인터렉션 비활성화 - 도형 변형(확대/축소/회전/이동) 기능 비활성화
map.setDragTranslate(false);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| flag | <code>Boolean</code> | 변경할 활성화 상태 - true : 활성화 - false : 비활성화 |
| options | <code>Obejct</code> | 변형기능 상세 옵션 |
| options.translateType | <code>Array.&lt;String&gt;</code> | 사용할 변형 기능 - 'move' : 이동 기능 - 'rotate' : 회전 기능 - 'resize' : 확대/축소 기능 |
| options.targetType | <code>String</code> | 변형 대상 유형 - 'layer' : 레이어 - 'feature' : 도형 |
| options.targetFeature | <code>Feature</code> | 변형 대상 도형 |
| options.targetLayer | <code>Layer</code> | - targetType 값이 'layer'일때 변형 대상 레이어 - targetType 값이 'feature'일때 도형 선택을 정의한 레이어로 제한 |
| options.changeTargetFeature | <code>Boolean</code> | 변형 대상 도형 변경 가능 여부(기본값 false,targetType 값이 'feature'일때 사용됨) |
| options.useHilight | <code>Boolean</code> | 선택 도형 하이라이트 스타일 적용 여부(기본값 true) |
| options.hilightStyle | <code>odf\_styleOption</code> | 선택 도형 적용 hilightStyle |
| options.modifiedCallback | [<code>modifiedCallback</code>](#modifiedCallback) | 도형 수정시 수정 도형을 받을 콜백함수 |

<a name="Map+getDragTranslateTarget"></a>

### map.getDragTranslateTarget()
벡터 레이어의 도형 변형(이동) 대상 도형 조회
```javascript
//DragTranslate 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전/이동) 가능
map.setDragTranslate(true,{
      translateType : [  'move', 'rotate','resize'], //사용할 변형 기능
      targetType : 'feature', // 변형 대상 유형
      targetLayer : wfsLayer,// 레이어 제한
      //targetFeature : feature,//변경 대상 도형 지정. 미정의시 클릭한 도형 편집 활성화
      changeTargetFeature : true,//변형 대상 도형 변경 가능 여부
      //hilightStyle : {
      //  fill : { color: [0, 0, 0, 0.4] },
      //  stroke : { color: [255, 0, 0, 0.7], width: 2 },
      //  image : {
      //    circle: {
      //      fill: { color: [0, 0, 0, 0.4] },
      //      stroke: { color: [255, 0, 0, 0.7], width: 2 },
      //      radius: 10,
      //    },
      //  }
      //}
});
//벡터 레이어의 도형 변형(이동) 대상 도형 조회
map.getDragTranslateTarget();

//DragTranslate 인터렉션 비활성화 - 도형 변형(확대/축소/회전/이동) 기능 비활성화
//map.setDragTranslate(false);
```

**Kind**: instance method of [<code>Map</code>](#Map)
<a name="Map+setDragTranslateTargetFeature"></a>

### map.setDragTranslateTargetFeature()
벡터 레이어의 도형 변형(이동) 대상 도형 셋팅
```javascript
//DragTranslate 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전/이동) 가능
map.setDragTranslate(true,{
      translateType : [  'move', 'rotate','resize'], //사용할 변형 기능
      targetType : 'feature', // 변형 대상 유형
      //targetLayer : wfsLayer,// 레이어 제한
      //targetFeature : feature,//변경 대상 도형 지정. 미정의시 클릭한 도형 편집 활성화
      changeTargetFeature : true,//변형 대상 도형 변경 가능 여부
      //hilightStyle : {
      //  fill : { color: [0, 0, 0, 0.4] },
      //  stroke : { color: [255, 0, 0, 0.7], width: 2 },
      //  image : {
      //    circle: {
      //      fill: { color: [0, 0, 0, 0.4] },
      //      stroke: { color: [255, 0, 0, 0.7], width: 2 },
      //      radius: 10,
      //    },
      //  }
      //}
});
//벡터 레이어의 도형 변형(이동) 대상 도형 셋팅
map.setDragTranslateTargetFeature(feature);

//DragTranslate 인터렉션 비활성화 - 도형 변형(확대/축소/회전/이동) 기능 비활성화
//map.setDragTranslate(false);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| options.targetFeature | <code>Feature</code> | 변형 대상 도형 |

<a name="Map+setDragTranslateTargetLayer"></a>

### map.setDragTranslateTargetLayer()
벡터 레이어의 도형 변형(이동) 대상 레이어 셋팅
```javascript
//DragTranslate 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전/이동) 가능
map.setDragTranslate(true,{
      translateType : [  'move', 'rotate','resize'], //사용할 변형 기능
      targetType : 'feature', // 변형 대상 유형
      //targetLayer : wfsLayer,// 레이어 제한
      //targetFeature : feature,//변경 대상 도형 지정. 미정의시 클릭한 도형 편집 활성화
      changeTargetFeature : true,//변형 대상 도형 변경 가능 여부
      //hilightStyle : {
      //  fill : { color: [0, 0, 0, 0.4] },
      //  stroke : { color: [255, 0, 0, 0.7], width: 2 },
      //  image : {
      //    circle: {
      //      fill: { color: [0, 0, 0, 0.4] },
      //      stroke: { color: [255, 0, 0, 0.7], width: 2 },
      //      radius: 10,
      //    },
      //  }
      //}
});
//벡터 레이어의 도형 변형(이동) 대상 레이어 셋팅
map.setDragTranslateTargetLayer(wfsLayer);

//DragTranslate 인터렉션 비활성화 - 도형 변형(확대/축소/회전/이동) 기능 비활성화
//map.setDragTranslate(false);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| options.targetFeature | <code>Feature</code> | 변형 대상 도형 |

<a name="Map+setTransformable"></a>

### map.setTransformable(flag, options)
도형 변형(확대/축소/회전) 기능 활성화 상태 변경
```javascript
//transform 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전) 가능
map.setTransformable(true);
//transform 인터렉션 비활성화 - 도형 변형 기능 비활성화
map.setTransformable(false);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| flag | <code>Boolean</code> | 변경할 활성화 상태 - true : 활성화 - false : 비활성화 |
| options | <code>Obejct</code> | 변형기능 상세 옵션 |
| options.targetFeature | <code>Feature</code> | 변형 대상 도형 |
| options.targetLayer | <code>Layer</code> | 도형 선택을 정의한 레이어로 제한 |
| options.changeTargetFeature | <code>Boolean</code> | 변형 대상 도형 변경 가능 여부(기본값 false) |
| options.useHilight | <code>Boolean</code> | 선택 도형 하이라이트 스타일 적용 여부(기본값 true) |
| options.hilightStyle | <code>odf\_styleOption</code> | 선택 도형 적용 hilightStyle |
| options.modifiedCallback | [<code>modifiedCallback</code>](#modifiedCallback) | 도형 수정시 수정 도형을 받을 콜백함수 |

<a name="Map+setTransformLayer"></a>

### map.setTransformLayer(layer, options)
도형 변형(확대/축소/회전) 레이어 제한
```javascript
// transform 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전) 가능
map.setTransformable(true);
//도형 변형 레이어 제한 -  선택된 레이어에 속한 도형만 변형 가능
map.setTransformLayer(layer);
//도형 변형 레이어 제한 해제 ※ 레이어 제한이 해제되었어도, transform 인터렉션은 활성화된 상태임
map.setTransformLayer();
// transform 인터렉션 비활성화
//map.setTransformable(false);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| layer | <code>Layer</code> | 제한할 레이어 |
| options | <code>Obejct</code> | 변형기능 상세 옵션 |
| options.targetFeature | <code>Feature</code> | 변형 대상 도형 |
| options.targetLayer | <code>Layer</code> | 도형 선택을 정의한 레이어로 제한 |
| options.changeTargetFeature | <code>Boolean</code> | 변형 대상 도형 변경 가능 여부(기본값 false) |
| options.useHilight | <code>Boolean</code> | 선택 도형 하이라이트 스타일 적용 여부(기본값 true) |
| options.hilightStyle | <code>odf\_styleOption</code> | 선택 도형 적용 hilightStyle |
| options.modifiedCallback | [<code>modifiedCallback</code>](#modifiedCallback) | 도형 수정시 수정 도형을 받을 콜백함수 |

<a name="Map+setTransformFeature"></a>

### map.setTransformFeature(feature, options)
도형 변형(확대/축소/회전) 기능 특정 도형으로 제한
```javascript
// transform 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전) 가능
map.setTransformable(true);
//특정 도형 선택하여 그 도형만 변형 가능하게 제한
map.setTransformFeature(feature);
//특정 도형 선택 해제 ※ 도형 선택이 해제 되었어도, transform 인터렉션은 활성화된 상태임
map.setTransformFeature();
// transform 인터렉션 비활성화
//map.setTransformable(false);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| feature | <code>Feature</code> | 선택된 도형 |
| options | <code>Obejct</code> | 변형기능 상세 옵션 |
| options.targetFeature | <code>Feature</code> | 변형 대상 도형 |
| options.targetLayer | <code>Layer</code> | 도형 선택을 정의한 레이어로 제한 |
| options.changeTargetFeature | <code>Boolean</code> | 변형 대상 도형 변경 가능 여부(기본값 false) |
| options.useHilight | <code>Boolean</code> | 선택 도형 하이라이트 스타일 적용 여부(기본값 true) |
| options.hilightStyle | <code>odf\_styleOption</code> | 선택 도형 적용 hilightStyle |
| options.modifiedCallback | [<code>modifiedCallback</code>](#modifiedCallback) | 도형 수정시 수정 도형을 받을 콜백함수 |

<a name="Map+setTranslate"></a>

### map.setTranslate(layer, options)
레이어 전체 이동 활성화 (현재 view영역에 속한 피쳐로 한정)
```javascript
map.setTranslate(layer);
레이어 전체 이동 활성화
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| layer | <code>Layer</code> | 타겟 레이어 |
| options | <code>Obejct</code> | 변형기능 상세 옵션 |
| options.targetFeature | <code>Feature</code> | 변형 대상 도형 |
| options.targetLayer | <code>Layer</code> | 도형 선택을 정의한 레이어로 제한 |
| options.changeTargetFeature | <code>Boolean</code> | 변형 대상 도형 변경 가능 여부(기본값 false) |
| options.useHilight | <code>Boolean</code> | 선택 도형 하이라이트 스타일 적용 여부(기본값 true) |
| options.hilightStyle | <code>odf\_styleOption</code> | 선택 도형 적용 hilightStyle |
| options.modifiedCallback | [<code>modifiedCallback</code>](#modifiedCallback) | 도형 수정시 수정 도형을 받을 콜백함수 |

<a name="Map+removeTranslate"></a>

### map.removeTranslate()
레이어 전체 이동 비활성화 (현재 view영역에 속한 피쳐로 한정)
```javascript
map.removeTranslate();
레이어 전체 이동 비활성화
```

**Kind**: instance method of [<code>Map</code>](#Map)
<a name="Map+setSnap"></a>

### map.setSnap(options, layerArray)
편집/그리기 시 Snap 인터렉션 설정
```javascript
//snap 인터렉션 활성화
map.setSnap({pixelTolerance : 10, edge : true, vertex : true}, [layer1, layer2]);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | 스냅 인터렉션 옵션 |
| [options.pixelTolerance] | <code>Number</code> | 픽셀 민감도 default 10 |
| [options.edge] | <code>Boolean</code> | 선으로 Snap 활성화 여부 |
| [options.vertex] | <code>Boolean</code> | 꼭지점으로 Snap 활성화 여부 |
| layerArray | <code>Array</code> \| <code>Layer</code> | 스냅 인터렉션을 활성화할 ODF 레이어 배열 |

<a name="Map+removeSnap"></a>

### map.removeSnap()
스Snap 인터렉션 비활성화
```javascript
map.removeSnap();
```

**Kind**: instance method of [<code>Map</code>](#Map)
<a name="Map+getSLDScale"></a>

### map.getSLDScale() ⇒ [<code>odf\_scaleInfo</code>](#odf_scaleInfo)
해당  wms 레이어와 연결된 사용자정의 스타일 조회
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.setMap(map)
sample.getWMSScale();
```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: [<code>odf\_scaleInfo</code>](#odf_scaleInfo) - 해당 wms 레이어의  zoomLevel별 resolution과 sldScale 정보
<a name="Map+hasFeatureAtPixel"></a>

### map.hasFeatureAtPixel(evt) ⇒ <code>Boolean</code>
해당 마우스 포인터 아래 도형 정보가 있는지 조회
 ```javascript
//마우스 포인터 아래 도형정보가 있다면 커서를 포인터 형태로 변경
odf.event.addListener(map, 'pointermove', function (evt) {
      var hit = map.hasFeatureAtPixel(evt);
      map.getTarget().style.cursor = hit ? 'pointer' : '';
    })
```

**Kind**: instance method of [<code>Map</code>](#Map)
**Returns**: <code>Boolean</code> - 도형이 있으면 true, 도형이 없으면 false 반환

| Param | Type | Description |
| --- | --- | --- |
| evt | <code>Event</code> | map 관련 이벤트 객체(click, pointermove 등) |

<a name="Map+forEachFeatureAtPixel"></a>

### map.forEachFeatureAtPixel(pixel, callback, layerFlag, options)
해당 픽셀 아래 도형이 있으면 각 도형별로 콜백 호출
 ```javascript
//마우스 포인터 아래 도형정보가 있다면 커서를 포인터 형태로 변경
odf.event.addListener(map, 'pointermove', function (evt) {
      map.forEachFeatureAtPixel(evt.pixel,(feature, layer)=>{
        // somthing to do
      },true,{

      });
    })
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| pixel | <code>Array.&lt;Number&gt;</code> |  | 도형을 조회할 위치 |
| callback | [<code>forEachFeatureAtPixel\_callback</code>](#forEachFeatureAtPixel_callback) |  | 해당 위치에 도형이 있다면 호출되는 콜백 |
| layerFlag | <code>Array.&lt;Number&gt;</code> | <code>true</code> | 레이어 반환 여부 |
| options | <code>Object</code> |  | 도형 조회 옵션 |
| options.layerFilter | <code>function</code> |  | 레이어 필터 함수 |
| options.hitTolerance | <code>Number</code> |  | 감지 허용 오차(css 픽셀 단위) |

<a name="Map+getPixelFromCoordinate"></a>

### map.getPixelFromCoordinate(coordinate)
coordinate 값을 화면상의 좌표값(pixel)로 변경
 ```javascript
//지도상의 좌표값을 화면상의 위치(pixel)값으로 변경
map.getPixelFromCoordinate([x,y]);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| coordinate | <code>Array.&lt;Number&gt;</code> | 좌표(지도 좌표계) |

<a name="Map+getCoordinateFromPixel"></a>

### map.getCoordinateFromPixel(pixel)
coordinate 값을 화면상의 좌표값(pixel)로 변경
 ```javascript
//화면상의 위치(pixel)를 지도상의 좌표값 값으로 변경
map.getCoordinateFromPixel([x,y]);
```

**Kind**: instance method of [<code>Map</code>](#Map)

| Param | Type | Description |
| --- | --- | --- |
| pixel | <code>Array.&lt;Number&gt;</code> | 화면상의 위치(pixel) |

<a name="mapInfo"></a>

## mapInfo : <code>object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| center | <code>odf.Coordinate</code> | 현재 지도 중심 좌표 |
| zoom | <code>number</code> | 현재 줌레벨 |
| maxZoom | <code>number</code> | 최대 줌레벨 |
| minZoom | <code>number</code> | 최소 줌레벨 |
| resolution | <code>number</code> | 해상도 |
| rotation | <code>number</code> | 회전 |
| projection | <code>string</code> | 좌표계 |

<a name="modifyObject"></a>

## modifyObject : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| update | <code>Object</code> | 원본 피쳐, 수정된 피쳐 배열 |
| update.original | <code>Feature</code> | 원본 피쳐 |
| update.modify | <code>Feature</code> | 수정된 피쳐 |
| insert | <code>Array</code> \| <code>Feature</code> | 추가된 피쳐 배열 |
| delete | <code>Feature</code> | 삭제할 피쳐 |
| moveFeature | <code>Object</code> | 원본 피쳐, 수정된 피쳐 배열 |
| moveFeature.original | <code>Feature</code> | 원본 피쳐 |
| moveFeature.modify | <code>Feature</code> | 수정된 피쳐 |
| transform | <code>Object</code> | 원본 피쳐, 수정된 피쳐 배열 |
| transform.original | <code>Feature</code> | 원본 피쳐 |
| transform.modify | <code>Feature</code> | 수정된 피쳐 |

<a name="splitFeatureCallback"></a>

## splitFeatureCallback : <code>function</code>
**Kind**: global typedef
<a name="modifiedCallback"></a>

## modifiedCallback : <code>function</code>
**Kind**: global typedef

| Param | Type | Description |
| --- | --- | --- |
| callbackObject | <code>Object</code> | 수정 도형 정보 |
| original | <code>Feature</code> | 수정 전 도형 정보 |
| modify | <code>Feature</code> | 수정 후 도형 정보 |

<a name="odf_scaleInfo_object"></a>

## odf\_scaleInfo\_object : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| zoomLevel | <code>Number</code> | geoserver 제공 소스로 레이어 생성(table) |
| resolution | <code>Number</code> | geojson을 소스로 vector레이어 생성 |
| sldScale | <code>Number</code> | 빈 vector레이어 생성 |

<a name="odf_scaleInfo"></a>

## odf\_scaleInfo : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| level | [<code>odf\_scaleInfo\_object</code>](#odf_scaleInfo_object) | zoomLevel별 resolution과 sldScale 정보 |

<a name="forEachFeatureAtPixel_callback"></a>

## forEachFeatureAtPixel\_callback : <code>function</code>
**Kind**: global typedef

| Param | Type | Description |
| --- | --- | --- |
| feature | <code>ODF.Feature</code> | 조회된 도형 |
| layer | <code>ODF.Layer</code> | 조회된 도형이 속한 레이어(layerFlag가 true일때 반환됨) |


<a name="Coordinate"></a>

## Coordinate
좌표 설정 클래스

**Kind**: global class
**Summary**: 좌표 클래스

* [Coordinate](#Coordinate)
  * [new Coordinate(x, y)](#new_Coordinate_new)
  * [.set(x, y)](#Coordinate+set) ⇒ <code>void</code>

<a name="new_Coordinate_new"></a>

### new Coordinate(x, y)

| Param | Type | Description |
| --- | --- | --- |
| x | <code>Number</code> | x 좌표값 |
| y | <code>Number</code> | y 좌표값 |

**Example**
```js
[방법1]
    let coord = new odf.Coordinate(955156.7761, 1951925.0984);
    [방법2]
    let coord = new odf.Coordinate([955156.7761, 1951925.0984]);
```
<a name="Coordinate+set"></a>

### coordinate.set(x, y) ⇒ <code>void</code>
객체의 x, y 좌표를 설정합니다.

**Kind**: instance method of [<code>Coordinate</code>](#Coordinate)

| Param | Type | Description |
| --- | --- | --- |
| x | <code>Array</code> \| <code>number</code> | x 좌표값 혹은 x와 y 좌표를 포함하는 배열. |
| y | <code>number</code> | y 좌표값 (optional). |


## Classes

<dl>
<dt><a href="#event">event</a></dt>
<dd><p>이벤트 생성 클래스</p>
</dd>
</dl>

## Typedefs

<dl>
<dt><a href="#event_expiration">event_expiration</a> : <code>Boolean</code> | <code>String</code></dt>
<dd></dd>
<dt><a href="#event_type">event_type</a> : <code>String</code></dt>
<dd><p>이벤트 target에 따라 적용 가능한 event 종류</p>
</dd>
<dt><a href="#event_excludeEvent_item">event_excludeEvent_item</a> : <code>Object</code></dt>
<dd></dd>
</dl>

<a name="event"></a>

## event
이벤트 생성 클래스

**Kind**: global class

* [event](#event)
  * [.addListener(target, eventType, callback, expiration)](#event.addListener) ⇒ <code>String</code>
  * [.removeListener(evtneId)](#event.removeListener)
  * [.trigger(target, eventType, param)](#event.trigger)
  * [.hasEventId(eventId)](#event.hasEventId) ⇒ <code>Boolean</code>
  * [.hasEvent(target, eventType)](#event.hasEvent) ⇒ <code>number</code>
  * [.stateChange(eventId, state, excludeThis)](#event.stateChange) ⇒ <code>boolean</code>
  * [.excludeEvent(parameter)](#event.excludeEvent) ⇒ [<code>Array.&lt;event\_excludeEvent\_item&gt;</code>](#event_excludeEvent_item)

<a name="event.addListener"></a>

### event.addListener(target, eventType, callback, expiration) ⇒ <code>String</code>
이벤트 등록

```javascript
//이벤트 연결
   let map = new odf.Map(document.getElementById('map'), {
  center: new odf.Coordinate(955673.3095379177, 1954893.4607722224),
  zoom: 17,
  projection: 'EPSG:5179',
  maxZoom: 20,
  minZoom: 8,
});
   //map 객체에 클릭 이벤트 연결
   let clickEventId = odf.event.addListener(map,'click',(evt)=>{
    //클릭한 위치에 마커 추가
  let marker = new odf.Marker({
    position : new odf.Coordinate(evt.coordinate)
  });
  marker.setMap(map);
});
```

**Kind**: static method of [<code>event</code>](#event)
**Returns**: <code>String</code> - 이벤트를 특정하는 Id

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| target | <code>String</code> \| <code>HTMLElement</code> \| <code>odf.ExtObject</code> |  | 이벤트를 연결할 대상(css 선택자 텍스트, odf.ExtObject) (ex)   '#moveBtn' | '.mtbBtn' | (new Map({...})) |
| eventType | [<code>event\_type</code>](#event_type) |  | target에 연결 가능한 이벤트 종류 (ex) 'click', 'mousedown',   'moveend','custom-event-01' |
| callback | <code>function</code> |  | 이벤트가 발생 될때 실행할 function |
| expiration | [<code>event\_expiration</code>](#event_expiration) | <code>odfDeleted</code> | event 해제 시기별 type. |

<a name="event.removeListener"></a>

### event.removeListener(evtneId)
이벤트 해제

```javascript
  //map 객체 생성
   let map = new odf.Map(document.getElementById('map'), {
  center: new odf.Coordinate(955673.3095379177, 1954893.4607722224),
  zoom: 17,
  projection: 'EPSG:5179',
  maxZoom: 20,
  minZoom: 8,
});

   //map 객체에 클릭 이벤트 연결
   let clickEventId = odf.event.addListener(map,'click',(evt)=>{
    //클릭한 위치에 마커 추가
  let marker = new odf.Marker({
    position : new odf.Coordinate(evt.coordinate)
  });
  marker.setMap(map);
});

   //클릭 이벤트 해제
   odf.event.removeListener(clickEventId);
```

**Kind**: static method of [<code>event</code>](#event)

| Param | Type | Description |
| --- | --- | --- |
| evtneId | <code>String</code> | 이벤트를 특정하는 Id |

<a name="event.trigger"></a>

### event.trigger(target, eventType, param)
이벤트 강제로 실행
```javascript
//사용자 정의 이벤트 생성
odf.event.addListener(map,'custom_event',(obj)=>{
 console.log(obj.a+obj.b);
});

//사용자 정의 이벤트 강제 실행
odf.event.trigger(map,'custom_event' ,{a:1000,b:4});
```

**Kind**: static method of [<code>event</code>](#event)

| Param | Type | Description |
| --- | --- | --- |
| target | <code>String</code> \| <code>HTMLElement</code> \| <code>odf.ExtObject</code> \| <code>MapBase</code> | - 강제로 이벤트를 실행시킬 대상(css 선택자 텍스트, odf.ExtObject)         (ex) '#moveBtn' | '.mtbBtn' | (new Map({...})) |
| eventType | <code>String</code> | 강제로 실행시킬 이벤트의 종류         (ex) 'click', 'mousedown', 'moveend','custom-event-01' |
| param | <code>Object</code> | 이벤트를 강제 실행시키면서 해당 콜백 function에 넘길 파라미터 |

<a name="event.hasEventId"></a>

### event.hasEventId(eventId) ⇒ <code>Boolean</code>
특정 id를 갖는 이벤트가 등록되어있는지 확인
```javascript
let clickEventId = odf.event.addListener(map,'click',(evt)=>{...});
//clickEventId를 id로 하는 이벤트가 등록되있는 것이 있는지 확인
odf.event.hasEventId(clickEventId); //true
odf.event.hasEventId('aaaa'); //false
```

**Kind**: static method of [<code>event</code>](#event)
**Returns**: <code>Boolean</code> - - 이벤트 등록 여부 반환

| Param | Type | Description |
| --- | --- | --- |
| eventId | <code>String</code> | 이벤트 등록 여부를 확인할 eventId |

<a name="event.hasEvent"></a>

### event.hasEvent(target, eventType) ⇒ <code>number</code>
특정 객체에 특정 타입의 이벤트가 있는지 확인
```javascript
odf.event.addListener(map,'click',(evt)=>{...});
//map 객체에 'click' 이벤트가 등록된것이 있나 확인
odf.event.hasEvent(map,'click'); //true
```

**Kind**: static method of [<code>event</code>](#event)
**Returns**: <code>number</code> - 이벤트 타겟에 등록된 이벤트 갯수

| Param | Type | Description |
| --- | --- | --- |
| target | <code>String</code> \| <code>HTMLElement</code> \| <code>odf.ExtObject</code> | 이벤트 타겟 |
| eventType | [<code>event\_type</code>](#event_type) | target에 연결 가능한 이벤트 종류         (ex) 'click', 'mousedown', 'moveend','custom-event-01' |

<a name="event.stateChange"></a>

### event.stateChange(eventId, state, excludeThis) ⇒ <code>boolean</code>
queue에 등록된 event의 상태값 변경
```javascript
    //이벤트 활성화
    odf.event.stateChange('click_1553666324943','normal');
    //이벤트 비활성화
    odf.event.stateChange('click_1553666324943','stopped');
    //click_1553666324943 이벤트 활성화
    odf.event.stateChange('click_1553666324943','normal',false);
    //click_1553666324943 이벤트 비활성화
    odf.event.stateChange('click_1553666324943','stopped',false);
    //click_1553666324943 이벤트와 eventType과 target이 동일한 이벤트들을 활성화
    odf.event.stateChange('click_1553666324943','normal',true);
    //click_1553666324943 이벤트와 eventType과 target이 동일한 이벤트들을 비활성화
    odf.event.stateChange('click_1553666324943','stopped',true);
```

**Kind**: static method of [<code>event</code>](#event)
**Returns**: <code>boolean</code> - - 작업 성공 여부 (ex) true=> 성공, false =>실패

| Param | Type | Description |
| --- | --- | --- |
| eventId | <code>string</code> | 상태를 변경할 이벤트의 id |
| state | <code>event\_state</code> | 이벤트 상태 |
| excludeThis | <code>boolean</code> | 입력한 eventId를 제외하고 eventId에 해당하는 이벤트 target과 이벤트 type이 일치하는 다른 이벤트들을 대상으로 할지 여부 - true : 입력한 eventId를 제외하고 eventId에 해당하는 이벤트 target과 이벤트 type이 일치하는 다른 이벤트들을 대상으로 처리 - false : (기본값)입력한 eventId를 대상으로 처리 |

<a name="event.excludeEvent"></a>

### event.excludeEvent(parameter) ⇒ [<code>Array.&lt;event\_excludeEvent\_item&gt;</code>](#event_excludeEvent_item)
queue에 등록된 event의 상태값 변경
```javascript
    let backupData = [];
    // 등록된 이벤트 중에 이벤트 id 'click_1553666324943' 에 해당하는 타겟과 이벤트 타입을 갖는 이벤트 목록 반환
    let targetList = odf.event.excludeEvent('click_1553666324943');
```

**Kind**: static method of [<code>event</code>](#event)
**Returns**: [<code>Array.&lt;event\_excludeEvent\_item&gt;</code>](#event_excludeEvent_item) - - 대상 이벤트 id 배열

| Param | Type | Description |
| --- | --- | --- |
| parameter | <code>Object</code> | 대상 이벤트 |
| parameter.eventId | <code>string</code> | 이벤트 id |
| parameter.target | <code>String</code> \| <code>HTMLElement</code> \| <code>odf.ExtObject</code> | 이벤트 타겟 |
| parameter.eventType | [<code>event\_type</code>](#event_type) | 이벤트 타입 |

<a name="event_expiration"></a>

## event\_expiration : <code>Boolean</code> \| <code>String</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| true | <code>Boolean</code> | 한번만 호출하고 해제 |
| false | <code>Boolean</code> | 지도객체가 없어지거나 disconncet 할 때 해제 |
| customize | <code>String</code> | 사용자가 원하는 시점에서 1번 호출하고 해제 |
| odfDeleted | <code>String</code> | (기본값) 지도객체가 없어지면 해제 |

<a name="event_type"></a>

## event\_type : <code>String</code>
이벤트 target에 따라 적용 가능한 event 종류

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| 'change' | <code>String</code> | [map] 일반적인 변경 이벤트. revision counter가 증가 하면 발생(table) |
| 'change:layerGroup' | <code>String</code> | [map] layerGroup 속성 변경시 발생 |
| 'change:size' | <code>String</code> | [map] size 속성 변경시 발생 |
| 'change:target' | <code>String</code> | [map] target 속성 변경시 발생 |
| 'change:view' | <code>String</code> | [map] view 속성 변경시 발생 |
| 'click' | <code>String</code> | [map] drag 없는 클릭. 더블 클릭을 하면 두 번 발생 |
| 'dblclick' | <code>String</code> | [map] drag 없는 더블 클릭 |
| 'moveend' | <code>String</code> | [map] 지도가 이동 된 후 발생 |
| 'movestart' | <code>String</code> | [map] 지도가 이동을 시작할 때 발생 |
| 'pointerdrag' | <code>String</code> | [map] 마우스 drag 할 때 발생 |
| 'pointermove' | <code>String</code> | [map] 마우스 포인터가 이동할 때 마다 발생 |
| 'precompose' | <code>String</code> | [map] layer를 그리기 전에 발생 |
| 'postcompose' | <code>String</code> | [map] layer를 모두 그린 후 발생 |
| 'postrender' | <code>String</code> | [map] 지도 프레임이 렌더링 된 후에 발생 (precompose → postcompose → postrender 순서) |
| 'propertychange' | <code>String</code> | [map] 속성이 변경 되면 발생 |
| 'singleclick' | <code>String</code> | [map] drag 와 더블 클릭이 없는 단일 클릭. 더블클릭과 구분하기 위해 |
| 'contextmenu' | <code>String</code> | [map] 우클릭시 트리거 |
| 'modifyend' | <code>String</code> | [map] 레이어 수정 완료 시 (피쳐 이동 종료) 발생 |
| 'editend' | <code>String</code> | [map] 레이어 편집 종료 시(종료 버튼 클릭) 발생 (현재 우클릭으로 편집 가능한 레이어는 geoImageLayer뿐) |
| 'editstart' | <code>String</code> | [map] 레이어 편집 시작 시(이동/회전/크기조절 버튼 클릭) 발생 (현재 우클릭으로 편집 가능한 레이어는 geoImageLayer뿐)   250ms 지연발생 |
| 'change' | <code>String</code> | [view] 일반적인 변경 이벤트. revision counter가 증가 하면 발생 |
| 'change:center' | <code>String</code> | [view] center 속성 변경시 발생 |
| 'change:resolution' | <code>String</code> | [view] resolution 속성 변경시 발생 |
| 'change:rotation' | <code>String</code> | [view] rotation 속성 변경시 발생 |
| 'propertychange' | <code>String</code> | [view] 속성이 변경 되면 발생 |
| 'change' | <code>String</code> | [layer 공통] 일반적인 변경 이벤트. revision counter가 증가 하면 발생 |
| 'change:extent' | <code>String</code> | [layer 공통] extent 속성 변경시 발생 |
| 'change:maxResolution' | <code>String</code> | [layer 공통] maxResolution 속성 변경시 발생 |
| 'change:minResolution' | <code>String</code> | [layer 공통] minResolution 속성 변경시 발생 |
| 'change:opacity' | <code>String</code> | [layer 공통] opacity 속성 변경시 발생 |
| 'change:visible' | <code>String</code> | [layer 공통] visible 속성 변경시 발생 |
| 'change:zIndex' | <code>String</code> | [layer 공통] zIndex 속성 변경시 발생 |
| 'propertychange' | <code>String</code> | [layer 공통] 속성이 변경되면 발생 |
| 'precompose' | <code>String</code> | [WebGL 레이어]  레이어가 구성되기 전에 트리거 |
| 'prerender' | <code>String</code> | [layer 공통] layer 렌더링 전 트리거 |
| 'postcompose' | <code>String</code> | [WebGL 레이어]  레이어가 구성된 후에 트리거 |
| 'postrender' | <code>String</code> | [layer 공통] layer 렌더링 후 트리거 |
| 'rendercomplete' | <code>String</code> | [layer 공통] 렌더링 완료시 트리거 (precompose → prerender → postcompose → postrender → rendercomplete 순서) |
| 'change:blur' | <code>String</code> | [Heatmap 레이어] blur 속성 변경시 발생 |
| 'change:gradient' | <code>String</code> | [Heatmap 레이어] gradient 속성 변경시 발생 |
| 'change:radius' | <code>String</code> | [Heatmap 레이어] radius 속성 변경시 발생 |
| 'change:source' | <code>String</code> | [Heatmap|Image|Tile|Vector|VectorTile 레이어] source 속성   변경시 발생 |
| 'change:preload' | <code>String</code> | [Tile|VectorTile 레이어] preload 속성 변경시 발생 |
| 'change:useInterimTilesOnError | <code>String</code> | [Tile|VectorTile  레이어]   useInterimTilesOnError 속성 변경시 발생 |
| 'change' | <code>String</code> | [source 공통] 일반적인 변경 이벤트. revision counter가 증가 하면 발생 |
| 'propertychange' | <code>String</code> | [source 공통] 속성이 변경되면 발생 |
| 'imageloadend' | <code>String</code> | [ImageArcGISRest|ImageMapGuide|ImageWMS 소스] 이미지로드가 완료되면   발생 |
| 'imageloaderror' | <code>String</code> | [ImageArcGISRest|ImageMapGuide|ImageWMS 소스] 이미지로드로 인해 오류가   발생하면 발생 |
| 'imageloadstart' | <code>String</code> | [ImageArcGISRest|ImageMapGuide|ImageWMS 소스] 이미지로드가 시작될 때   발생 |
| 'afteroperations' | <code>String</code> | [Raster 소스] 작업이 실행 된 후에 발생 |
| 'beforeoperations' | <code>String</code> | [Raster 소스] 작업이 실행 되기 전에 발생 |
| 'tileloadstart' | <code>String</code> | [UrlTile|TileImage|BingMaps|TileArcGISRest|TileJSON|TileWMS|WMTS|XYZ|VectorTile 소스] 타일 load가   시잘할때 발생 |
| 'tileloadend' | <code>String</code> | [UrlTile|TileImage|BingMaps|TileArcGISRest|TileJSON|TileWMS|WMTS|XYZ|VectorTile 소스] 데이터로드시   또는 타일이 더 이상 필요 없기 때문에로드가 중단 된 경우 타일로드가 완료되면 발생 |
| 'tileloaderror' | <code>String</code> | [UrlTile|TileImage|BingMaps|TileArcGISRest|TileJSON|TileWMS|WMTS|XYZ|VectorTile 소스] 타일로드로 인해   오류가 발생하면 발생 |
| 'addfeature' | <code>String</code> | [Vector|Cluster 소스] Feature가 source에 추가 되면 발생 |
| 'featureloadend' | <code>String</code> | [Vector|Cluster 소스] Feature가 source에 모두 로드되면 발생 |
| 'changefeature' | <code>String</code> | [Vector|Cluster 소스] Feature가 업데이트 되면 발생 |
| 'clear' | <code>String</code> | [Vector|Cluster 소스] source에서 clear 메소드가 호츨 되면 발생 |
| 'removefeature' | <code>String</code> | [Vector|Cluster 소스] feature가 제거되면 발생. source.clear 제외 |
| 'change' | <code>String</code> | [Marker]  일반 변경 이벤트.position,draggable,map 값 변경시 발생 |
| 'change:position' | <code>String</code> | [Marker]  position 변경시 발생 |
| 'change:draggable' | <code>String</code> | [Marker]  draggable 변경시 발생 |
| 'change:map' | <code>String</code> | [Marker]  map 변경시 발생 |
| 'click' | <code>String</code> | [Marker]  marker 클릭시 발생 |
| 'markerdragstart' | <code>String</code> | [Marker]  draggable 마커 drag를 시작했을때 발생 |
| 'markerdrag' | <code>String</code> | [Marker]  draggable 마커를 중 마우스 이동 발생했을때 발생 |
| 'markerdragend' | <code>String</code> | [Marker]  draggable 마커 drag가 종료됬을때 발생 |
| 'drawstart' | <code>String</code> | [DrawControl]  DrawControl을 이용하여 그리기를 시작할때 발생                     (buffer 도형 그리기에서는 트리거 되지 않음) |
| 'drawend' | <code>String</code> | [DrawControl] DrawControl을 이용하여 그리기를 종료할때 발생 |

<a name="event_excludeEvent_item"></a>

## event\_excludeEvent\_item : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| eventId | <code>string</code> | 대상 이벤트의 id |
| state | <code>event\_state</code> | 이벤트 상태 |


## Classes

<dl>
<dt><a href="#Marker">Marker</a></dt>
<dd><p>마커 생성 클래스</p>
</dd>
</dl>

## Typedefs

<dl>
<dt><a href="#odf_marker_positioning">odf_marker_positioning</a> : <code>String</code></dt>
<dd></dd>
<dt><a href="#markerOption">markerOption</a> : <code>Object</code></dt>
<dd></dd>
</dl>

<a name="Marker"></a>

## Marker
마커 생성 클래스

**Kind**: global class
**Summary**: 마커 생성자


 ```javascript
//기본 마커 생성
let marker = new odf.Marker({
  position : new odf.Coordinate(955156.7761, 1951925.0984),
  //position : [955156.7761, 1951925.0984],
 });
 marker.setMap(map);
```

```javascript
//마커가 지도 영역 벗어났을 경우 지도가 이동되는 마커 생성
let marker = new odf.Marker({
  position : new odf.Coordinate(955156.7761, 1951925.0984),
  autoPan: true,
  autoPanAnimation: {
     duration: 250
  }
 });
 marker.setMap(map);
```

```javascript
//드래그 가능한 마커 생성
  let marker = new odf.Marker({
    position : new odf.Coordinate(955156.7761, 1951925.0984),
    draggable : true
  });
  marker.setMap(map);
```

 ```javascript
//커스터마이징 마커 1
 let marker = new odf.Marker({
   position : new odf.Coordinate(955156.7761, 1951925.0984),
   style : {
     width : '20px',
     height : '20px',
     src : 'images/smileIcon'
   }
 });
 marker.setMap(map);
```
 ```javascript
 //커스터마이징 마커 2
   let marker = new odf.Marker({
     position : new odf.Coordinate(955156.7761, 1951925.0984),
     style : {
       element : document.getElementById('customMarker')
     }
   });
   marker.setMap(map);
 ```

* [Marker](#Marker)
  * [new Marker(options)](#new_Marker_new)
  * [.getElement()](#Marker+getElement) ⇒ <code>Element</code>
  * [.getPosition()](#Marker+getPosition) ⇒ <code>Coordinate</code>
  * [.setPosition(coord)](#Marker+setPosition)
  * [.setPositioning(position)](#Marker+setPositioning)
  * [.setOffset(position)](#Marker+setOffset)
  * [.getMap()](#Marker+getMap) ⇒ <code>Map</code>
  * [.setMap(Map)](#Marker+setMap)
  * [.setDraggable(draggable)](#Marker+setDraggable)
  * [.getDraggable()](#Marker+getDraggable) ⇒ <code>Boolean</code>
  * [.removeMap()](#Marker+removeMap)

<a name="new_Marker_new"></a>

### new Marker(options)

| Param | Type | Description |
| --- | --- | --- |
| options | [<code>markerOption</code>](#markerOption) | 마커 생성을 위한 option |

<a name="Marker+getElement"></a>

### marker.getElement() ⇒ <code>Element</code>
마커와 연결된 element 조회

```javascript
       //마커와 연결된 element 반환
       let marker = new odf.Marker({
       position : new odf.Coordinate(955156.7761, 1951925.0984),
      });
       marker.setMap(map);
       let mElement  = marker.getElement();
```

**Kind**: instance method of [<code>Marker</code>](#Marker)
**Returns**: <code>Element</code> - 마커와 연결된 Html element
<a name="Marker+getPosition"></a>

### marker.getPosition() ⇒ <code>Coordinate</code>
마커의 위치정보 반환

```javascript
       //마커의 위치정보 조회
       let marker = new odf.Marker({
       position : new odf.Coordinate(955156.7761, 1951925.0984),
      });
       marker.setMap(map);
       let mCoord  = marker.getPosition();
```

**Kind**: instance method of [<code>Marker</code>](#Marker)
**Returns**: <code>Coordinate</code> - 마커의 위치정보
<a name="Marker+setPosition"></a>

### marker.setPosition(coord)
마커의 위치정보 변경
```javascript
       //마커의 위치정보 변경
       let marker = new odf.Marker({
     position : new odf.Coordinate(955156.7761, 1951925.0984),
    });
       marker.setMap(map);
       marker.setPosition(odf.Coordinate(955909.5218701352, 1954955.496334219));
       //marker.setPosition([955909.5218701352, 1954955.496334219]);
```

**Kind**: instance method of [<code>Marker</code>](#Marker)

| Param | Type | Description |
| --- | --- | --- |
| coord | <code>Coordinate</code> \| <code>Array.&lt;Number&gt;</code> | 마커의 위치정보 |

<a name="Marker+setPositioning"></a>

### marker.setPositioning(position)
마커의 속성과 관련하여 실제로 배치되는 방식을 정의
```javascript
       let marker = new odf.Marker({
   position : new odf.Coordinate(955156.7761, 1951925.0984),
  });
       marker.setMap(map);
       marker.setPosition(odf.Coordinate(955909.5218701352, 1954955.496334219));
       marker.setPositioning('bottom-left');
```

**Kind**: instance method of [<code>Marker</code>](#Marker)

| Param | Type | Description |
| --- | --- | --- |
| position | <code>String</code> | 지도의 해당 지점을 기준으로 배치되는 방식 |

<a name="Marker+setOffset"></a>

### marker.setOffset(position)
배치할 때 사용되는 오프셋(픽셀)입니다. 배열의 첫 번째 요소는 수평 오프셋(양수 값은 오버레이를 오른쪽으로 이동) 배열의 두 번째 요소는 수직 오프셋(양수 값은 오버레이를 아래로 이동합니다.)
```javascript
       let marker = new odf.Marker({
     position : new odf.Coordinate(955156.7761, 1951925.0984),
    });
       marker.setMap(map);
       marker.setPosition(odf.Coordinate(955909.5218701352, 1954955.496334219));
       marker.setOffset([0, -10]);
```

**Kind**: instance method of [<code>Marker</code>](#Marker)

| Param | Type | Description |
| --- | --- | --- |
| position | <code>String</code> | 지도의 해당 지점을 기준으로 배치되는 방식 |

<a name="Marker+getMap"></a>

### marker.getMap() ⇒ <code>Map</code>
마커에 연결된 map객체 반환
```javascript
       //마커에 연결된 map객체 조회
       let marker = new odf.Marker({
         position : new odf.Coordinate(955156.7761, 1951925.0984),
       });
       marker.setMap(map);
       let _map = marker.getMap();
 ```

**Kind**: instance method of [<code>Marker</code>](#Marker)
**Returns**: <code>Map</code> - 마커에 연결된 map객체, 없으면 undefined
<a name="Marker+setMap"></a>

### marker.setMap(Map)
마커에 map객체 연결
```javascript
       //마커에 map객체 연결
       let marker = new odf.Marker({
         position : new odf.Coordinate(955156.7761, 1951925.0984),
       });
       marker.setMap(map);
```

**Kind**: instance method of [<code>Marker</code>](#Marker)

| Param | Type | Description |
| --- | --- | --- |
| Map | <code>Map</code> | 마커에 연결할 map객체 |

<a name="Marker+setDraggable"></a>

### marker.setDraggable(draggable)
마커의 드래그 가능 여부 변경
```javascript
         //마커의 드래그 가능 여부 변경
         let marker = new odf.Marker({
           position : new odf.Coordinate(955156.7761, 1951925.0984),
         });
         marker.setMap(map);

         marker.setDraggable(true);//드래그 가능한 마커로 변경
         //marker.setDraggable(false));//드래그 불가능한 마커로 변경
 ```

**Kind**: instance method of [<code>Marker</code>](#Marker)

| Param | Type | Description |
| --- | --- | --- |
| draggable | <code>Boolean</code> | 마커의 드래그 가능 여부 |

<a name="Marker+getDraggable"></a>

### marker.getDraggable() ⇒ <code>Boolean</code>
마커의 드래그 가능 여부 반환
```javascript
         //마커의 드래그 가능 여부 조회
         let marker = new odf.Marker({
         position : new odf.Coordinate(955156.7761, 1951925.0984),
        });
         marker.setMap(map);

         marker.getDraggable();//드래그 가능여부 기본 false
```

**Kind**: instance method of [<code>Marker</code>](#Marker)
**Returns**: <code>Boolean</code> - 마커의 드래그 가능 여부
<a name="Marker+removeMap"></a>

### marker.removeMap()
마커와 연결된 map 객체 제거
```javascript
       //마커와 연결된 map 객체 제거
       let marker = new odf.Marker({
        position : new odf.Coordinate(955156.7761, 1951925.0984),
      });
       marker.setMap(map);
       marker.removeMap();
```

**Kind**: instance method of [<code>Marker</code>](#Marker)
<a name="odf_marker_positioning"></a>

## odf\_marker\_positioning : <code>String</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| 'top-left' | <code>String</code> | position 값 기준 상단 좌측에 위치(table) |
| 'top-center' | <code>String</code> | position 값 기준 상단 중앙에 위치 |
| 'top-right' | <code>String</code> | position 값 기준 상단 우측에 위치 |
| 'center-left' | <code>String</code> | position 값 기준 중앙 좌측에 위치 |
| 'center-center' | <code>String</code> | position 값 기준 중앙 중앙에 위치 |
| 'center-right' | <code>String</code> | position 값 기준 중앙 우측에 위치 |
| 'bottom-left' | <code>String</code> | position 값 기준 하단 우측에 위치 |
| 'bottom-center' | <code>String</code> | position 값 기준 하단 중앙에 위치(기본값) |
| 'bottom-right' | <code>String</code> | position 값 기준 하단 좌측에 위치 |

<a name="markerOption"></a>

## markerOption : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| position | <code>Coordinate</code> \| <code>Array.&lt;Number&gt;</code> | 마커의 위치 |
| draggable | <code>Boolean</code> | 드래그 가능 여부(기본값 false) |
| positioning | [<code>odf\_marker\_positioning</code>](#odf_marker_positioning) | 마커의 상대적 위치 (기본값 'bottom-center') |
| offset | <code>Array.&lt;number&gt;</code> | 기준점으로 부터 정의한 값만큼 마커를 이동(픽셀) (기본값 [0,0])  - 첫번째 요소 : 수평 오프셋 값. 양수는 마커를 오른쪽으로 이동시킴  - 두번째 요소 : 수직 오프셋 값. 양수는 마커를 아래로 이동시킴 |
| stopEvent | <code>Boolean</code> | 마커 이벤트 전파 중지 여부. true => 이벤트 전파 중지, false=> 이벤트 전파 (기본값 false) |
| style | <code>Object</code> | 마커의 스타일 정의 |
| style.width | <code>String</code> | 마커의 너비 정의 |
| style.height | <code>String</code> | 마커의 높이 정의 |
| style.src | <code>String</code> | 마커의 배경 이미지 url 정의 |
| style.element | <code>HTMLElement</code> | 사용자 정의 마커 element |
| autoPan | <code>Boolean</code> | 지도 화면에서 팝업이 잘려나오게 될 경우, 지도를 이동할지 여부 - true : 지도 화면에서 팝업이 잘려나오게 될 경우, 지도를 이동합니다. (기본값 : false) |
| autoPanAnimation | <code>number</code> | autoPan이 ture일때, 지도를 이동하는데 사용되는 애니메이션 입니다. (기본값 : 250) 최소 :0 최대 : 100000 |
| autoPanMargin | <code>number</code> | autoPan이 ture일때, 팝업과 지도 사이의 여백(픽셀) 지정 (기본값 : 20) |


<a name="ZoomControl"></a>

## ZoomControl
지도 줌 설정클래스

**Kind**: global class
**Summary**: zoomControl 클래스 생성자

```javascript
 let zoomControl = new odf.ZoomControl({
   zoomSlider : true
 });
```

* [ZoomControl](#ZoomControl)
  * [new ZoomControl(options)](#new_ZoomControl_new)
  * [.getConstructorOptions()](#ZoomControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
  * [.setMap(map, createElementFlag)](#ZoomControl+setMap)
  * [.removeMap()](#ZoomControl+removeMap)
  * [.zoomIn()](#ZoomControl+zoomIn)
  * [.zoomOut()](#ZoomControl+zoomOut)

<a name="new_ZoomControl_new"></a>

### new ZoomControl(options)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | 줌 컨트롤 생성에 사용할 옵션 |
| options.zoomSlider | <code>Boolean</code> | 줌슬라이더 생성여부 |

<a name="ZoomControl+getConstructorOptions"></a>

### zoomControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>
컨트롤 생성 옵션 반환
```javascript
 //ZoomControl 생성
 let zoomControl = new odf.ZoomControl({
   zoomSlider : true
 });
 zoomControl.getConstructorOptions();
```

**Kind**: instance method of [<code>ZoomControl</code>](#ZoomControl)
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환
<a name="ZoomControl+setMap"></a>

### zoomControl.setMap(map, createElementFlag)
ZoomControl map 객체 연결
```javascript
ZoomControl에 map 객체 연결
 //ZoomControl 생성
 let zoomControl = new odf.ZoomControl({
   zoomSider : true
 });
 zoomControl.setMap(map, createElementFlag); //ZoomControl 객체에 map 객체 연결
```

**Kind**: instance method of [<code>ZoomControl</code>](#ZoomControl)

| Param | Type | Description |
| --- | --- | --- |
| map | <code>Map</code> | ZoomControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | ZoomControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x |

<a name="ZoomControl+removeMap"></a>

### zoomControl.removeMap()
ZoomControl map 객체 해제
```javascript
 zoomControl.removeMap();
```

**Kind**: instance method of [<code>ZoomControl</code>](#ZoomControl)
<a name="ZoomControl+zoomIn"></a>

### zoomControl.zoomIn()
ZoomControl 줌레벨 확대
```javascript
 zoomControl.setZoomIn(); //줌레벨 확대
```

**Kind**: instance method of [<code>ZoomControl</code>](#ZoomControl)
<a name="ZoomControl+zoomOut"></a>

### zoomControl.zoomOut()
ZoomControl 줌레벨 축소
```javascript
 zoomControl.setZoomOut(); //줌레벨 축소
```

**Kind**: instance method of [<code>ZoomControl</code>](#ZoomControl)

## Classes

<dl>
<dt><a href="#OverviewMapControl">OverviewMapControl</a></dt>
<dd><p>인덱스맵 생성 클래스</p>
</dd>
</dl>

## Typedefs

<dl>
<dt><a href="#positionType">positionType</a> : <code>Array</code></dt>
<dd><p>아래 값들중 선택하여 배열에 넣을 수 있다.
위치 유형 (&#39;right-up&#39; / &#39;right-down&#39; / &#39;left-up&#39; / &#39;left-down&#39;)</p>
</dd>
</dl>

<a name="OverviewMapControl"></a>

## OverviewMapControl
인덱스맵 생성 클래스

**Kind**: global class
**Summary**: 오버뷰 맵 클래스

```javascript
let overviewMapControl = new odf.OverviewMapControl();
```

* [OverviewMapControl](#OverviewMapControl)
  * [.getConstructorOptions()](#OverviewMapControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
  * [.setMap(map, createElementFlag)](#OverviewMapControl+setMap)
  * [.removeMap()](#OverviewMapControl+removeMap)
  * [.changeState()](#OverviewMapControl+changeState)
  * [.getState()](#OverviewMapControl+getState)
  * [.changeOverviewMapPosition(positionType)](#OverviewMapControl+changeOverviewMapPosition)
  * [.updateSize()](#OverviewMapControl+updateSize)

<a name="OverviewMapControl+getConstructorOptions"></a>

### overviewMapControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>
컨트롤 생성 옵션 반환
```javascript
 let overviewMapControl = new odf.OverviewMapControl();
 overviewMapControl.getConstructorOptions();
```

**Kind**: instance method of [<code>OverviewMapControl</code>](#OverviewMapControl)
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환
<a name="OverviewMapControl+setMap"></a>

### overviewMapControl.setMap(map, createElementFlag)
OverviewMap을 map객체와 연결 설정
OverviewMap HTMLElement를 만들고, 이벤트 바인딩 시켜, map 객체와 연결

```javascript
 let overviewMapControl = new odf.OverviewMapControl();//OverviewMapControl 생성
 overviewMapControl.setMap(map,createElementFlag); //OverviewMapControl 객체에 map 객체 연결
```

**Kind**: instance method of [<code>OverviewMapControl</code>](#OverviewMapControl)

| Param | Type | Description |
| --- | --- | --- |
| map | <code>Map</code> | OverviewMapControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | OverviewMapControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x |

<a name="OverviewMapControl+removeMap"></a>

### overviewMapControl.removeMap()
OverviewMapControl map 객체 해제
```javascript
 OverviewMapControl.removeMap();
```

**Kind**: instance method of [<code>OverviewMapControl</code>](#OverviewMapControl)
<a name="OverviewMapControl+changeState"></a>

### overviewMapControl.changeState()
OverviewMap 상태 변경
OverviewMap 상태값에 따라 활성화/비활성화하고 상태값 변경
 ```javascript
 let overviewMapControl = new odf.OverviewMapControl();//OverviewMapControl 생성
 overviewMapControl.changeState(); //OverviewMap 활성화/비활성화
```

**Kind**: instance method of [<code>OverviewMapControl</code>](#OverviewMapControl)
<a name="OverviewMapControl+getState"></a>

### overviewMapControl.getState()
OverviewMap 상태 조회
OverviewMap 상태값 조회 함수
 ```javascript
 let overviewMapControl = new odf.OverviewMapControl();//OverviewMapControl 생성
 overviewMapControl.getState(); //OverviewMap 활성화/비활성화
```

**Kind**: instance method of [<code>OverviewMapControl</code>](#OverviewMapControl)
<a name="OverviewMapControl+changeOverviewMapPosition"></a>

### overviewMapControl.changeOverviewMapPosition(positionType)
OverviewMap 표출 위치 변경
매개변수 positionType 값에 따라 OverviewMap  위치 변경
```javascript
//OverviewMap을 버튼의 왼쪽 위에 위치시키도록 함
let overviewMapControl = new odf.OverviewMapControl();//OverviewMapControl 생성
overviewMapControl.changeOverviewMapPosition('left-up');
```
```javascript
//OverviewMap을 버튼의 왼쪽 아래에 위치시키도록 함
let overviewMapControl = new odf.OverviewMapControl();//OverviewMapControl 생성
overviewMapControl.changeOverviewMapPosition('left-down');
```
```javascript
//OverviewMap을 버튼의 오른쪽 위에 위치시키도록 함
let overviewMapControl = new odf.OverviewMapControl();//OverviewMapControl 생성
overviewMapControl.changeOverviewMapPosition('right-up');
```
```javascript
//OverviewMap을 버튼의 오른쪽 아래에 위치시키도록 함
let overviewMapControl = new odf.OverviewMapControl();//OverviewMapControl 생성
overviewMapControl.changeOverviewMapPosition('right-down');
```

**Kind**: instance method of [<code>OverviewMapControl</code>](#OverviewMapControl)

| Param | Type | Description |
| --- | --- | --- |
| positionType | [<code>positionType</code>](#positionType) | 위치 유형 ('right-up' / 'right-down' / 'left-up' / 'left-down') |

<a name="OverviewMapControl+updateSize"></a>

### overviewMapControl.updateSize()
OverviewMap viewport 크기를 강제로 재계산
```javascript
let overviewMapControl = new odf.OverviewMapControl();//OverviewMapControl 생성
//OverviewMap viewport 크기를 강제로 재계산
overviewMapControl.updateSize();
```

**Kind**: instance method of [<code>OverviewMapControl</code>](#OverviewMapControl)
<a name="positionType"></a>

## positionType : <code>Array</code>
아래 값들중 선택하여 배열에 넣을 수 있다.
위치 유형 ('right-up' / 'right-down' / 'left-up' / 'left-down')

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| right-up | <code>String</code> | 오른쪽 위 : 위치가 개요지도 버튼의 오른쪽 위에 위치 |
| right-down | <code>String</code> | 오른쪽 아래 : 위치가 개요지도 버튼의 오른쪽 아래에 위치 |
| left-up | <code>String</code> | 왼쪽 위 : 위치가 개요지도 버튼의 왼쪽 위에 위치 |
| left-down | <code>String</code> | 왼쪽 아래 : 위치가 개요지도 버튼의 왼쪽 아래에 위치 |


<a name="MoveControl"></a>

## MoveControl
현재 화면 기준으로 이전/다음 화면 이동 생성 클래스

**Kind**: global class
**Summary**: 화면의 이동/줌 레벨 저장하고 실행 하는 클래스

```javascript
let moveControl = new odf.MoveControl();
```

* [MoveControl](#MoveControl)
  * [.getConstructorOptions()](#MoveControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
  * [.setMap(map, createElementFlag)](#MoveControl+setMap)
  * [.removeMap()](#MoveControl+removeMap)
  * [.capture()](#MoveControl+capture)
  * [.back()](#MoveControl+back)
  * [.forward()](#MoveControl+forward)

<a name="MoveControl+getConstructorOptions"></a>

### moveControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>
컨트롤 생성 옵션 반환
```javascript
 let moveControl = new odf.MoveControl();
 moveControl.getConstructorOptions();
```

**Kind**: instance method of [<code>MoveControl</code>](#MoveControl)
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환
<a name="MoveControl+setMap"></a>

### moveControl.setMap(map, createElementFlag)
MoveControl을 Map 객체에 추가

```javascript
let moveControl = new odf.MoveControl();
moveControl.setMap(map,createElementFlag);
```

**Kind**: instance method of [<code>MoveControl</code>](#MoveControl)

| Param | Type | Description |
| --- | --- | --- |
| map | <code>Map</code> | MoveControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | MoveControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x |

<a name="MoveControl+removeMap"></a>

### moveControl.removeMap()
MoveControl map 객체 해제
```javascript
 MoveControl.removeMap();
```

**Kind**: instance method of [<code>MoveControl</code>](#MoveControl)
<a name="MoveControl+capture"></a>

### moveControl.capture()
MoveControl 화면 저장 함수
```javascript
 MoveControl.capture();
```

**Kind**: instance method of [<code>MoveControl</code>](#MoveControl)
<a name="MoveControl+back"></a>

### moveControl.back()
MoveControl 이전화면 실행함수
```javascript
 MoveControl.back();
```

**Kind**: instance method of [<code>MoveControl</code>](#MoveControl)
<a name="MoveControl+forward"></a>

### moveControl.forward()
MoveControl 다음화면 실행함수
```javascript
 MoveControl.forward();
```

**Kind**: instance method of [<code>MoveControl</code>](#MoveControl)

## Classes

<dl>
<dt><a href="#BasemapControl">BasemapControl</a></dt>
<dd><p>배경지도 설정 클래스</p>
</dd>
</dl>

## Typedefs

<dl>
<dt><a href="#odf_basemap_baroEMapKey">odf_basemap_baroEMapKey</a> : <code>String</code></dt>
<dd><p>baseMap 생성 키</p>
</dd>
<dt><a href="#odf_basemap_vWorldMapKey">odf_basemap_vWorldMapKey</a> : <code>String</code></dt>
<dd><p>vWorld 생성 키</p>
</dd>
<dt><a href="#odf_basemap_kakaoMapKey">odf_basemap_kakaoMapKey</a> : <code>String</code></dt>
<dd><p>kakao 생성 키</p>
</dd>
<dt><a href="#odf_basemap_OSMMapKey">odf_basemap_OSMMapKey</a> : <code>String</code></dt>
<dd><p>kakao 생성 키</p>
</dd>
<dt><a href="#odf_basemap_option">odf_basemap_option</a> : <code>Object</code></dt>
<dd><p>사용자가 어떤 그룹의 어떤 베이스맵을 사용할지 정의</p>
</dd>
</dl>

<a name="BasemapControl"></a>

## BasemapControl
배경지도 설정 클래스

**Kind**: global class
**Summary**: BasemapControl 생성자

```javascript
기본지도와 항공지도를 베이스맵으로 선택하여 생성(프록시 사용)
 let basemapControl = new odf.BasemapControl({
   baroEMap:['eMapBasic','eMapAIR']
 },{
proxyURL : '/proxy.jsp',
proxyParam : 'url',});//BasemapControl 생성
```
```javascript
 기본지도와 영문지도를 베이스맵으로 선택하여 생성(프록시 미사용)
 let basemapControl = new odf.BasemapControl({
   baroEMap:['eMapBasic','eMapAIR']
 });//BasemapControl 생성
```

* [BasemapControl](#BasemapControl)
  * [new BasemapControl(basemapOption, options)](#new_BasemapControl_new)
  * [.getConstructorOptions()](#BasemapControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
  * [.getMap()](#BasemapControl+getMap)
  * [.setMap(map, createElementFlag)](#BasemapControl+setMap)
  * [.hasGrp(grpKey)](#BasemapControl+hasGrp) ⇒ <code>Boolean</code>
  * [.setGrp(grpKey)](#BasemapControl+setGrp)
  * [.removeGrp(grpKey)](#BasemapControl+removeGrp)
  * [.hasBaseLayer(layerKey)](#BasemapControl+hasBaseLayer) ⇒ <code>Boolean</code>
  * [.setBaseLayer(grpKey, layerKey, layerName, layer)](#BasemapControl+setBaseLayer)
  * [.removeBaseLayer(layerKey)](#BasemapControl+removeBaseLayer)
  * [.rebuildElement()](#BasemapControl+rebuildElement)
  * [.setSwitchBaseLayerCallback(callback)](#BasemapControl+setSwitchBaseLayerCallback)
  * [.switchBaseLayer(layerKey)](#BasemapControl+switchBaseLayer)
  * [.getPresentBaseGrpKey()](#BasemapControl+getPresentBaseGrpKey) ⇒ <code>String</code>
  * [.getPresentBaseLayerKey()](#BasemapControl+getPresentBaseLayerKey) ⇒ <code>String</code>
  * [.getPresentBaseLayer()](#BasemapControl+getPresentBaseLayer) ⇒ <code>Layer</code>
  * [.getSetableBasemapList()](#BasemapControl+getSetableBasemapList) ⇒ <code>Json</code>
  * [.getBaseLayer(key)](#BasemapControl+getBaseLayer)

<a name="new_BasemapControl_new"></a>

### new BasemapControl(basemapOption, options)

| Param | Type | Description |
| --- | --- | --- |
| basemapOption | [<code>odf\_basemap\_option</code>](#odf_basemap_option) | 사용자가 어떤 그룹의 어떤 베이스맵을 사용할지 정의한 Object (ex) {baroEMap :['eMapKorean','eMapAIR'] }; |
| options | <code>Object</code> | proxyURL, baroEMapURL,baroEMapAirURL,vWorldURL, 바로e맵 API KEY를 정의 |
| options.proxyURL | <code>String</code> | [선택] 프록시를 사용하고자 하는경우 프록시 URL을 정의 |
| options.proxyParam | <code>String</code> | [선택] 프록시를 사용하고자 하는경우 프록시파라미터 정의 |
| options.baroEMapURL | <code>String</code> | 일반 바로 eMap url |
| options.vWorldURL | <code>String</code> | vWord url |
| options.kakaoURL | <code>String</code> | kakao url |
| options.baroEMapAirURL | <code>String</code> | [선택] 바로e맵 영상지도 url |
| options.baroEMapKey | <code>String</code> | [선택] 바로e맵 API KEY |
| options.vWorldKey | <code>String</code> | [선택] vWord API KEY |

<a name="BasemapControl+getConstructorOptions"></a>

### basemapControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>
컨트롤 생성 옵션 반환
```javascript
 let basemapControl = new odf.BasemapControl({
   baroEMap:['eMapBasic','eMapAIR']
 },{
proxyURL : '/proxy.jsp',
proxyParam : 'url',});//BasemapControl 생성
 basemapControl.getConstructorOptions();
```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환
<a name="BasemapControl+getMap"></a>

### basemapControl.getMap()
BasemapControl 객체와 연결된 Map 객체 반환
```javascript
 let basemapControl = new odf.BasemapControl([
   'eMapBasic','eMapAIR'
 ],{
proxyURL : '/proxy.jsp',
proxyParam : 'url',});//BasemapControl 생성
 basemapControl.getMap();//undefined
 basemapControl.setMap(map);
 basemapControl.getMap();//map
```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)
<a name="BasemapControl+setMap"></a>

### basemapControl.setMap(map, createElementFlag)
BasemapControl 객체와 Map 객체를 연결
```javascript
 let basemapControl = new odf.BasemapControl([
   'eMapBasic','eMapAIR'
 ],{
proxyURL : '/proxy.jsp',
proxyParam : 'url',});//BasemapControl 생성
 basemapControl.setMap(map);
```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)

| Param | Type | Description |
| --- | --- | --- |
| map | <code>Map</code> | BasemapControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | BasemapControl 버튼 생성 여부 |

<a name="BasemapControl+hasGrp"></a>

### basemapControl.hasGrp(grpKey) ⇒ <code>Boolean</code>
베이스맵 그룹 존재 여부 확인
```javascript
 //레이어 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //새로운 베이스맵 존재 여부 확인
 basemapControl.hasGrp('myGrp');
```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)
**Returns**: <code>Boolean</code> - 입력한 베이스맵 그룹 key가 존재하는지 여부

| Param | Type | Description |
| --- | --- | --- |
| grpKey | <code>String</code> | 베이스맵 그룹이 존재하는지 확인할 grpKey |

<a name="BasemapControl+setGrp"></a>

### basemapControl.setGrp(grpKey)
베이스맵 그룹 생성
```javascript
 //레이어 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //새로운 베이스맵 그룹 생성
 basemapControl.setGrp('myGrp');
```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)

| Param | Type | Description |
| --- | --- | --- |
| grpKey | <code>String</code> | 베이스맵 그룹으로 등록할 grpKey. |

<a name="BasemapControl+removeGrp"></a>

### basemapControl.removeGrp(grpKey)
베이스맵 그룹 제거
```javascript
 //베이스컨트롤 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //새로운 베이스맵 그룹 생성
 basemapControl.setGrp('myGrp');
 //새로만든 베이스맵 그룹 제거
 basemapControl.removeGrp('myGrp')
```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)

| Param | Type | Description |
| --- | --- | --- |
| grpKey | <code>String</code> | 제거할 대상이 되는 베이스맵 그룹으로 등록된 grpKey |

<a name="BasemapControl+hasBaseLayer"></a>

### basemapControl.hasBaseLayer(layerKey) ⇒ <code>Boolean</code>
특정 키를 갖는 베이스 레이어가 존재하는지 확인
```javascript
 //베이스컨트롤 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //'myLayer'를 키로 하는 베이스맵이 존재하는지 확인
 basemapControl.hasBaseLayer('myLayer');

```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)
**Returns**: <code>Boolean</code> - 입력한 베이스레이어 key가 존재하는지 여부

| Param | Type | Description |
| --- | --- | --- |
| layerKey | <code>String</code> | 등록할 베이스레이어의 key 값 |

<a name="BasemapControl+setBaseLayer"></a>

### basemapControl.setBaseLayer(grpKey, layerKey, layerName, layer)
사용자 정의 베이스레이어 추가 및 베이스맵 컨트롤 재구축
```javascript
 //베이스컨트롤 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //베이스맵 기본그룹 'basic'에 새로운 레이어 추가
 basemapControl.setBaseLayer('basic','myLayer','내레이어',odf.LayerFactory.produce(...));

 //새로운 베이스맵 그룹 생성
 //basemapControl.setGrp('myGrp');
 //새로운 베이스맵 그룹에 새로운 레이어 추가
 //basemapControl.setBaseLayer('myGrp','myNewGroupLayer','내새그룹레이어',odf.LayerFactory.produce(...));

 //정의된 베이스맵 그룹과 베이스맵 레이어 정보에따라 basemap 컨트롤을 지우고 다시 생성
 basemapControl.rebuildElement();
```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)

| Param | Type | Description |
| --- | --- | --- |
| grpKey | <code>String</code> | 베이스맵 그룹으로 등록된 grpKey |
| layerKey | <code>String</code> | 등록할 베이스레이어의 key 값 |
| layerName | <code>String</code> | 컨트롤에서 나타날 베이스 레이어 명 |
| layer | <code>odf.Layer</code> | 베이스맵으로 등록할 레이어 객체 |

<a name="BasemapControl+removeBaseLayer"></a>

### basemapControl.removeBaseLayer(layerKey)
사용자 정의 베이스레이어 제거
```javascript
 //베이스컨트롤 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //베이스맵 기본그룹 'basic'에 새로운 레이어 추가
 basemapControl.setBaseLayer('basic','myLayer','내레이어',odf.LayerFactory.produce(...));

 //새로운 베이스맵 그룹 생성
 //basemapControl.setGrp('myGrp');
 //새로운 베이스맵 그룹에 새로운 레이어 추가
 //basemapControl.setBaseLayer('myGrp','myNewGroupLayer','내새그룹레이어',odf.LayerFactory.produce(...));

 //정의된 베이스맵 그룹과 베이스맵 레이어 정보에따라 basemap 컨트롤을 지우고 다시 생성
 basemapControl.rebuildElement();

 //새로 생성한 베이스맵 레이어 제거
 basemapControl.removeBaseLayer('myNewGroupLayer');

```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)

| Param | Type | Description |
| --- | --- | --- |
| layerKey | <code>String</code> | 등록할 베이스레이어의 key 값. 영문 대/소문자와 언더바(_)로 구성 |

<a name="BasemapControl+rebuildElement"></a>

### basemapControl.rebuildElement()
정의된 베이스맵 그룹과 베이스맵 레이어 정보에따라 basemap 컨트롤을 지우고 다시 생성
```javascript
 //베이스컨트롤 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //새로운 베이스맵 그룹 생성
 basemapControl.setGrp('myGrp');
 //새로운 베이스맵 그룹에 새로운 레이어 추가
 basemapControl.setBaseLayer('myGrp','myLayer','내레이어',odf.LayerFactory.produce(...));

 //생성된 컨트롤 element 재생성
 basemapControl.rebuildElement();
```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)
<a name="BasemapControl+setSwitchBaseLayerCallback"></a>

### basemapControl.setSwitchBaseLayerCallback(callback)
배경지도 변경 완료 후 호출할 콜백
```javascript
 //베이스컨트롤 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //switchBaseLayer 호출 직후 사용될 callback 셋팅
 basemapControl.setSwitchBaseLayerCallback(function(beforeLayer,afterLayer){
   //do something
});
```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)

| Param | Type | Description |
| --- | --- | --- |
| callback | <code>function</code> | beforeLayer와 afterLayer를 매개변수로 갖는 사용자 정의 callback function -  첫번째 매개변수(beforeLayer) : 변경 이전 layer -  두번째 매개변수(afterLayer) : 변경될 layer |

<a name="BasemapControl+switchBaseLayer"></a>

### basemapControl.switchBaseLayer(layerKey)
배경지도 변경
```javascript
 //베이스컨트롤 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //색각지도로 변경
 basemapControl.switchBaseLayer('eMapColor');
```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)

| Param | Type | Description |
| --- | --- | --- |
| layerKey | [<code>odf\_basemap\_baroEMapKey</code>](#odf_basemap_baroEMapKey) \| [<code>odf\_basemap\_vWorldMapKey</code>](#odf_basemap_vWorldMapKey) \| [<code>odf\_basemap\_kakaoMapKey</code>](#odf_basemap_kakaoMapKey) \| [<code>odf\_basemap\_OSMMapKey</code>](#odf_basemap_OSMMapKey) \| <code>String</code> | 변경할 baselayer의 키 값  - 사용자정의 베이스맵의 키 값 허용  - undefined 또는 null 입력시 배경지도 삭제 |

<a name="BasemapControl+getPresentBaseGrpKey"></a>

### basemapControl.getPresentBaseGrpKey() ⇒ <code>String</code>
현재 설정된 베이스맵 그룹 key 조회

```javascript
 let basemapControl = new odf.BasemapControl();//BasemapControl 생성
 basemapControl.getPresentBaseGrpKey();
```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)
**Returns**: <code>String</code> - 현재 설정된 베이스맵 그룹 key
<a name="BasemapControl+getPresentBaseLayerKey"></a>

### basemapControl.getPresentBaseLayerKey() ⇒ <code>String</code>
현재 설정된 베이스맵 key 조회

```javascript
 let basemapControl = new odf.BasemapControl();//BasemapControl 생성
 basemapControl.getPresentBaseLayer();
```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)
**Returns**: <code>String</code> - 현재 설정된 베이스맵 key
<a name="BasemapControl+getPresentBaseLayer"></a>

### basemapControl.getPresentBaseLayer() ⇒ <code>Layer</code>
현재 설정된 베이스맵 레이어 조회

```javascript
 let basemapControl = new odf.BasemapControl();//BasemapControl 생성
 basemapControl.getPresentBaseLayer();
```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)
**Returns**: <code>Layer</code> - 현재 설정된 베이스맵 레이어
<a name="BasemapControl+getSetableBasemapList"></a>

### basemapControl.getSetableBasemapList() ⇒ <code>Json</code>
설정가능한 BasemapList 조회

```javascript
 let basemapControl = new odf.BasemapControl();//BasemapControl 생성
 basemapControl.getSetableBasemapList();
```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)
**Returns**: <code>Json</code> - 배경지도로 설정가능한 목록 및 상세 명칭
<a name="BasemapControl+getBaseLayer"></a>

### basemapControl.getBaseLayer(key)
베이스맵의 key 값으로 baselayer 조회

```javascript
 프록시 사용하지 않고 eMapBasic 레이어 객체 조회
 let basemapControl = new odf.BasemapControl(['eMapBasic','eMapAIR'],'/proxy.jsp');//BasemapControl 생성
 basemapControl.getBaseLayer('eMapBasic');
```

**Kind**: instance method of [<code>BasemapControl</code>](#BasemapControl)

| Param | Type | Description |
| --- | --- | --- |
| key | <code>odf.baseMapKey</code> | 조회할 베이스맵의 key 값 |

<a name="odf_basemap_baroEMapKey"></a>

## odf\_basemap\_baroEMapKey : <code>String</code>
baseMap 생성 키

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| 'eMapBasic' | <code>String</code> | 바로e맵 기본 지도(table) |
| 'eMapColor' | <code>String</code> | 바로e맵 색각 지도 |
| 'eMapLowV' | <code>String</code> | 바로e맵 큰글씨 지도 |
| 'eMapWhite' | <code>String</code> | 바로e맵 백지도 |
| 'eMapEnglish' | <code>String</code> | 바로e맵 영어 지도 |
| 'eMapChinese' | <code>String</code> | 바로e맵 중어 지도 |
| 'eMapJapanese' | <code>String</code> | 바로e맵 일어 지도 |
| 'eMapWhiteEdu' | <code>String</code> | 바로e맵 교육용 백지도 |
| 'eMapAIR' | <code>String</code> | 바로e맵  영상지도 |

<a name="odf_basemap_vWorldMapKey"></a>

## odf\_basemap\_vWorldMapKey : <code>String</code>
vWorld 생성 키

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| 'vWorldBase' | <code>String</code> | vWorld 기본 지도(table) |
| 'vWorldWhite' | <code>String</code> | vWorld 회색 지도 |
| 'vWorldMidnight' | <code>String</code> | vWorld 야간 지도 |
| 'vWorldHybrid' | <code>String</code> | vWorld 하이브리드 지도 |
| 'vWorldSatellite' | <code>String</code> | vWorld 영상 지도 |

<a name="odf_basemap_kakaoMapKey"></a>

## odf\_basemap\_kakaoMapKey : <code>String</code>
kakao 생성 키

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| 'kakaoBase' | <code>String</code> | kakao 기본 지도(table) |
| 'kakaoSkyview' | <code>String</code> | kakao 영상 지도 |

<a name="odf_basemap_OSMMapKey"></a>

## odf\_basemap\_OSMMapKey : <code>String</code>
kakao 생성 키

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| 'OSM' | <code>String</code> | OSM 기본 지도(table) |

<a name="odf_basemap_option"></a>

## odf\_basemap\_option : <code>Object</code>
사용자가 어떤 그룹의 어떤 베이스맵을 사용할지 정의

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| baroEMap | [<code>Array.&lt;odf\_basemap\_baroEMapKey&gt;</code>](#odf_basemap_baroEMapKey) \| <code>boolean</code> | 바로e맵 그룹 내 어떤 배경 지도를 사용할지 정의 (ex) ['eMapBasic','eMapColor','eMapLowV','eMapWhite','eMapEnglish','eMapChinese','eMapKorean','eMapJapanese','eMapWhiteEdu','eMapAIR']  :  바로e맵 그룹의 기본지도와 영상 지도 사용 (ex) true  :  바로e맵 그룹의 모든 배경지도 사용 |
| vWorld | [<code>Array.&lt;odf\_basemap\_vWorldMapKey&gt;</code>](#odf_basemap_vWorldMapKey) \| <code>boolean</code> | 바로e맵 그룹 내 어떤 배경 지도를 사용할지 정의 (ex) ['vWorldBase', 'vWorldWhite' ,'vWorldMidnight','vWorldHybrid','vWorldSatellite']  :  vWorld 그룹의 기본지도와 회색 지도 사용 (ex) true  :  vWorld 그룹의 모든 배경지도 사용 |
| kakao | [<code>Array.&lt;odf\_basemap\_kakaoMapKey&gt;</code>](#odf_basemap_kakaoMapKey) \| <code>boolean</code> | 바로e맵 그룹 내 어떤 배경 지도를 사용할지 정의 (ex) ['kakaoBase','kakaoSkyview' ] :  카카오 그룹의 기본지도와 영상 지도 사용 (ex) true  :  카카오 그룹의 모든 배경지도 사용 |
| OSM | [<code>Array.&lt;odf\_basemap\_OSMMapKey&gt;</code>](#odf_basemap_OSMMapKey) \| <code>boolean</code> | 바로e맵 그룹 내 어떤 배경 지도를 사용할지 정의 (ex) ['OSM']  :  OSM 지도 사용 (ex) true  :  OSM 그룹의 모든 배경지도 사용 |


<a name="MeasureControl"></a>

## MeasureControl
지도 측정 도구 클래스

**Kind**: global class
**Summary**: 지도 측정 도구 생성

```javascript
 let measureControl = new odf.MeasureControl();
  ```

* [MeasureControl](#MeasureControl)
  * [new MeasureControl(options, rightClickDelete)](#new_MeasureControl_new)
  * [.getConstructorOptions()](#MeasureControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
  * [.setMap(map, createElementFlag)](#MeasureControl+setMap)
  * [.removeMap()](#MeasureControl+removeMap)
  * [.executeOff()](#MeasureControl+executeOff)
  * [.executeArea()](#MeasureControl+executeArea)
  * [.executeDistance()](#MeasureControl+executeDistance)
  * [.executeRound()](#MeasureControl+executeRound)
  * [.executeSpot()](#MeasureControl+executeSpot)
  * [.clear()](#MeasureControl+clear)
  * [.removeHelpTooltip()](#MeasureControl+removeHelpTooltip)
  * [.findDrawVectorLayer()](#MeasureControl+findDrawVectorLayer) ⇒ <code>Layer</code>

<a name="new_MeasureControl_new"></a>

### new MeasureControl(options, rightClickDelete)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | DrawControl 객체생성 옵션 |
| options.displayOption | <code>Object</code> | 거리 측정 표시 옵션 |
| options.displayOption.area | <code>Object</code> | 면적 측정 표시 옵션 |
| options.displayOption.area.decimalPoint | <code>Number</code> | 면적 측정 소수점 표기 자리수 (소수몇번째자리까지 표출) default : 2 |
| options.displayOption.area.transformUnit | <code>Number</code> | 면적 측정 단위 변경 제곱미터기준(㎡ -> ㎢ 단위로 변경) ex) 1000000 -> 1000000㎡ 부터 1 ㎢로 표출 default : 1000000 |
| options.displayOption.distance | <code>Object</code> | 거리 측정 표시 옵션 |
| options.displayOption.distance.decimalPoint | <code>Number</code> | 거리측정 소수점 표기 자리수 (소수몇번째자리까지 표출) default : 2 |
| options.displayOption.distance.transformUnit | <code>Number</code> | 거리 측정 단위 변경 미터기준(m -> km 단위로 변경) ex) 1000 -> 1000m 부터 1 km로 표출 default : 1000 |
| options.displayOption.round | <code>Object</code> | 반경 측정 표시 옵션 |
| options.displayOption.round.decimalPoint | <code>Number</code> | 반경 측정 소수점 표기 자리수 (소수몇번째자리까지 표출) default : 2 |
| options.displayOption.round.transformUnit | <code>Number</code> | 반경 측정 단위 변경 미터기준(m -> km 단위로 변경) ex) 1000 -> 1000m 부터 1 km로 표출 default : 1000 |
| options.style | <code>odf\_styleOption</code> | 측정 도형 style 설정 옵션 |
| options.message | <code>Object</code> | 툴팁 메세지 |
| options.message.DRAWSTART | <code>String</code> | 그리기 시작 안내 메세지 (기본값) '클릭하여 측정을 시작하세요' |
| options.message.DRAWEND_POLYGON | <code>String</code> | 면 그리기 종료 안내 메세지 (기본값) '클릭하여 폴리곤을 그리거나, 더블클릭하여 그리기를 종료하세요' |
| options.message.DRAWEND_LINE | <code>String</code> | 선 그리기 종료 안내 메세지 (기본값) '클릭하여 라인을 그리거나, 더블클릭하여 그리기를 종료하세요' |
| options.continuity | <code>Boolean</code> | 연속 측정 여부 (기본값 false) |
| rightClickDelete | <code>Boolean</code> | 우클릭 삭제 기능 활성화 여부 (기본값 false) |
| options.tools | <code>Array.&lt;String&gt;</code> | 생성할 툴 배열 |
| options.tools.distance | <code>String</code> | 거리 측정 툴 |
| options.tools.area | <code>String</code> | 면적측정 툴 |
| options.tools.round | <code>String</code> | 원의 면적측정 툴 |
| options.tools.spot | <code>String</code> | 좌표 측정 툴 |
| options.spotProjection | <code>String</code> | 좌표측정시 사용할 좌표계 (기본값 지도의 좌표계) |

<a name="MeasureControl+getConstructorOptions"></a>

### measureControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>
컨트롤 생성 옵션 반환
```javascript
 let measureControl = new odf.MeasureControl();
 measureControl.getConstructorOptions();
```

**Kind**: instance method of [<code>MeasureControl</code>](#MeasureControl)
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환
<a name="MeasureControl+setMap"></a>

### measureControl.setMap(map, createElementFlag)
측정 도구 설정

```javascript
let map = new odf.Map(mapContainer, mapOption);
let measureControl = new odf.MeasureControl();
measureControl.setMap(map,createElementFlag);
```

**Kind**: instance method of [<code>MeasureControl</code>](#MeasureControl)

| Param | Type | Description |
| --- | --- | --- |
| map | <code>Map</code> | MeasureControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | MeasureControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x |

<a name="MeasureControl+removeMap"></a>

### measureControl.removeMap()
측정 도구에 연결된 map 객체 제거

```javascript
let map = new odf.Map(mapContainer, mapOption);
let measureControl = new odf.MeasureControl();
measureControl.setMap(map);
measureControl.removeMap();

**Kind**: instance method of [<code>MeasureControl</code>](#MeasureControl)
<a name="MeasureControl+executeOff"></a>

### measureControl.executeOff()
MeasureControl 측정 종료

```javascript
measureControl.executeOff();

**Kind**: instance method of [<code>MeasureControl</code>](#MeasureControl)
<a name="MeasureControl+executeArea"></a>

### measureControl.executeArea()
MeasureControl 면적 측정

```javascript
measureControl.executeArea();

**Kind**: instance method of [<code>MeasureControl</code>](#MeasureControl)
<a name="MeasureControl+executeDistance"></a>

### measureControl.executeDistance()
MeasureControl 거리 측정

```javascript
measureControl.executeDistance();

**Kind**: instance method of [<code>MeasureControl</code>](#MeasureControl)
<a name="MeasureControl+executeRound"></a>

### measureControl.executeRound()
MeasureControl 거리 측정

```javascript
measureControl.executeRound();

**Kind**: instance method of [<code>MeasureControl</code>](#MeasureControl)
<a name="MeasureControl+executeSpot"></a>

### measureControl.executeSpot()
MeasureControl 지점 좌표 측정

```javascript
measureControl.executeSpot();

**Kind**: instance method of [<code>MeasureControl</code>](#MeasureControl)
<a name="MeasureControl+clear"></a>

### measureControl.clear()
측정 인터렉션 삭제 및 측정 오버레이 삭제, 측정 중 도형 삭제

**Kind**: instance method of [<code>MeasureControl</code>](#MeasureControl)
<a name="MeasureControl+removeHelpTooltip"></a>

### measureControl.removeHelpTooltip()
MeasureControl 툴팁 삭제 함수

```javascript
measureControl.removeHelpTooltip();

**Kind**: instance method of [<code>MeasureControl</code>](#MeasureControl)
<a name="MeasureControl+findDrawVectorLayer"></a>

### measureControl.findDrawVectorLayer() ⇒ <code>Layer</code>
측정 도구로 그린 피쳐가 있는 벡터레이어가 있는지 확인,없을 경우 벡터레이어 생성

**Kind**: instance method of [<code>MeasureControl</code>](#MeasureControl)
**Returns**: <code>Layer</code> - 측정용 레이어 반환

<a name="DrawControl"></a>

## DrawControl
그리기 도구 클래스

**Kind**: global class
**Summary**: 지도에 그릴수 있는 다양한 그리기 도구를 제공

```javascript
let drawControl = new odf.DrawControl({
        //style : {} //그리기 피쳐 odf 스타일 옵션,
        //bufferStyle : {} //버퍼 도형 odf 스타일 옵션,
        //createNewLayer : true //drawControl 생성 시 새로운 레이어 생성 여부
        //message : { //그리기 도구 시작시 툴팁 메세지
          //DRAWSATRT_POINT : '점 그리기 측정입니다.',
          //DRAWSTART_LINESTRING : '',
          //DRAWSTART_POLYGON : '',
          //DRAWSTART_CURVE : '' ,
          //DRAWSTART_TEXT : '',
          //DRAWSTART_BUFFER : '',
          //DRAWSTART_CIRCLE : '',
          //DRAWSTART_BOX : '',
          //DRAWEND_DRAG : '',
          //DRAWEND_DBCLICK : '',
        //},
        //measure : false, 측정 옵션 활성화 여부, (선, 원형만 측정 가능)
        //continuity : false, 연속 측정 여부  (기본값 false),
        //rightClickDelete : false, 우클릭 삭제 기능 활성화 여부,
        //tools : ['text', 'polygon','lineString','point','circle','curve','box','square','buffer'],
});
```

* [DrawControl](#DrawControl)
  * [new DrawControl(options)](#new_DrawControl_new)
  * [.getConstructorOptions()](#DrawControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
  * [.setMap(_map, createElementFlag)](#DrawControl+setMap)
  * [.removeMap()](#DrawControl+removeMap)
  * [.draw(type)](#DrawControl+draw)
  * [.drawText()](#DrawControl+drawText)
  * [.drawPolygon()](#DrawControl+drawPolygon)
  * [.drawLineString()](#DrawControl+drawLineString)
  * [.drawPoint()](#DrawControl+drawPoint)
  * [.drawCurve()](#DrawControl+drawCurve)
  * [.drawBox()](#DrawControl+drawBox)
  * [.drawSquare()](#DrawControl+drawSquare)
  * [.drawCircle()](#DrawControl+drawCircle)
  * [.findDrawVectorLayer()](#DrawControl+findDrawVectorLayer) ⇒ <code>Layer</code>
  * [.clear()](#DrawControl+clear)
  * [.removeToolTip()](#DrawControl+removeToolTip)
  * [.setStyle(styleObject)](#DrawControl+setStyle)
  * [.getActiveType()](#DrawControl+getActiveType) ⇒ <code>String</code>

<a name="new_DrawControl_new"></a>

### new DrawControl(options)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | DrawControl 객체생성 옵션 |
| options.style | <code>odf\_styleOption</code> | 그리기 도형 style 설정 옵션 |
| options.bufferStyle | <code>odf\_styleOption</code> | 버퍼 그리기 도형 style 설정 옵션 |
| options.createNewLayer | <code>boolean</code> | drawControl 생성 시 마다 새 레이어 생성 여부 |
| options.message | <code>Object</code> | 툴팁 메세지 |
| options.message.DRAWSTART_POINT | <code>String</code> | 점 그리기 시작 안내 메세지 (기본값) '점을 그리기 위해 지도를 클릭해주세요' |
| options.message.DRAWSTART_LINESTRING | <code>String</code> | 선 그리기 시작 안내 메세지 (기본값) '선을 그리기 위해 지도를 클릭해주세요' |
| options.message.DRAWSTART_POLYGON | <code>String</code> | 면 그리기 시작 안내 메세지 (기본값) '면을 그리기 위해 지도를 클릭해주세요' |
| options.message.DRAWSTART_CURVE | <code>String</code> | 곡선 그리기 시작 안내 메세지 (기본값) '곡선을 그리기 위해 지도를 드래그해주세요' |
| options.message.DRAWSTART_TEXT | <code>String</code> | 텍스트 그리기 시작 안내 메세지 (기본값) '텍스트를 입력하기 위해 지도를 클릭해주세요.' |
| options.message.DRAWSTART_BUFFER | <code>String</code> | 버퍼 그리기 시작 안내 메세지 (기본값) '버퍼를 생성하기 위해 레이어를 선택해주세요.' |
| options.message.DRAWSTART_CIRCLE | <code>String</code> | 원 그리기 시작 안내 메세지 (기본값) '원을 그리기 위해 지도를 클릭해주세요.' |
| options.message.DRAWSTART_BOX | <code>String</code> | 사각형 그리기 시작 안내 메세지 (기본값) '사각형을 그리기 위해 지도를 클릭해주세요.' |
| options.message.DRAWEND_DRAG | <code>String</code> | 그리기 종료 안내 메세지(드래그) (기본값) '드래그를 멈추면 그리기가 종료됩니다.' |
| options.message.DRAWEND_DBCLICK | <code>String</code> | 그리기 종료 안내 메세지(더블클릭) (기본값) '더블클릭시 그리기가 종료됩니다.' |
| options.measure | <code>Boolean</code> | 측정 옵션 활성화 여부(기본값 false) |
| options.continuity | <code>Boolean</code> | 연속 그리기 여부 (기본값 false) |
| options.rightClickDelete | <code>Boolean</code> | 우클릭 삭제 기능 활성화 여부 (기본값 false) |
| options.tools | <code>Array.&lt;String&gt;</code> | 생성할 툴 배열 |
| options.tools.text | <code>String</code> | 텍스트 그리기 툴 |
| options.tools.polygon | <code>String</code> | 다각형 그리기 툴 |
| options.tools.lineString | <code>String</code> | 선 그리기 툴 |
| options.tools.box | <code>String</code> | 사각형 그리기 툴 |
| options.tools.square | <code>String</code> | 정사각형 그리기 툴 |
| options.tools.point | <code>String</code> | 점 그리기 툴 |
| options.tools.circle | <code>String</code> | 원 그리기 툴 |
| options.tools.curve | <code>String</code> | 곡선 그리기 툴 |
| options.tools.buffer | <code>String</code> | 버퍼 그리기 툴 |

<a name="DrawControl+getConstructorOptions"></a>

### drawControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>
컨트롤 생성 옵션 반환
```javascript
 let drawControl = new odf.DrawControl();
 drawControl.getConstructorOptions();
```

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환
<a name="DrawControl+setMap"></a>

### drawControl.setMap(_map, createElementFlag)
DrawControl map 객체 연결

```javascript
 let drawControl = new odf.DrawControl({
      });
 drawControl.setMap(map,createElementFlag); //DrawControl 객체에 map 객체 연결
 ```

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)

| Param | Type | Description |
| --- | --- | --- |
| _map | <code>Map</code> | DrawControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | DrawControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x |

<a name="DrawControl+removeMap"></a>

### drawControl.removeMap()
그리기 도구에 연결된 map 객체 제거

```javascript
let map = new odf.Map(mapContainer, mapOption);
let drawControl = new odf.drawControl();
drawControl.setMap(map);
drawControl.removeMap();

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)
<a name="DrawControl+draw"></a>

### drawControl.draw(type)
DrawControl 그리기

```javascript
drawControl.draw('text');

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)

| Param | Type | Description |
| --- | --- | --- |
| type | <code>String</code> | 그리기 유형  - 'text' : 텍스트  - 'polygon' : 다각형  - 'lineString' : 선  - 'box' : 사각형  - 'square' : 정사각형  - 'point' : 점  - 'circle' : 원  - 'curve' : 곡선  - 'buffer' : 버퍼 |

<a name="DrawControl+drawText"></a>

### drawControl.drawText()
DrawControl 텍스트 그리기

```javascript
drawControl.drawText();
```

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)
<a name="DrawControl+drawPolygon"></a>

### drawControl.drawPolygon()
DrawControl 폴리곤 그리기

```javascript
drawControl.drawPolygon();

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)
<a name="DrawControl+drawLineString"></a>

### drawControl.drawLineString()
DrawControl 라인 그리기

```javascript
drawControl.drawLineString();

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)
<a name="DrawControl+drawPoint"></a>

### drawControl.drawPoint()
DrawControl 점 그리기

```javascript
drawControl.drawPoint();

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)
<a name="DrawControl+drawCurve"></a>

### drawControl.drawCurve()
DrawControl 곡선 그리기

```javascript
drawControl.drawCurve();

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)
<a name="DrawControl+drawBox"></a>

### drawControl.drawBox()
DrawControl 사각형 그리기

```javascript
drawControl.drawBox();

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)
<a name="DrawControl+drawSquare"></a>

### drawControl.drawSquare()
DrawControl 정사각형 그리기

```javascript
drawControl.drawSquare();

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)
<a name="DrawControl+drawCircle"></a>

### drawControl.drawCircle()
DrawControl 원 그리기

```javascript
drawControl.drawCircle();

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)
<a name="DrawControl+findDrawVectorLayer"></a>

### drawControl.findDrawVectorLayer() ⇒ <code>Layer</code>
그리기 도구로 그린 피쳐가 있는 벡터레이어가 있는지 확인,없을 경우 벡터레이어 생성
```javascript
drawControl.findDrawVectorLayer();
```

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)
**Returns**: <code>Layer</code> - 그리기 레이어 반환
<a name="DrawControl+clear"></a>

### drawControl.clear()
그리기 인터렉션 삭제 및 그리기 오버레이 삭제, 그리던 도형 삭제
```javascript
drawControl.clear();
```

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)
<a name="DrawControl+removeToolTip"></a>

### drawControl.removeToolTip()
DrawControl 툴팁 삭제 함수

```javascript
drawControl.removeHelpTooltip();

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)
<a name="DrawControl+setStyle"></a>

### drawControl.setStyle(styleObject)
DrawControl 스타일 변경 함수

```javascript
drawControl.setStyle({
             fill : {
          color : [1,10,100,0.4]
      },
      stroke : {
          color : [1,10,100,0.4],
          width : 3,
      },
      point : { // 점 스타일
         icon : {
              width : 20, // scale 옵션과 함께 사용할 수 없습니다.
              height: 20, // scale 옵션과 함께 사용할 수 없습니다.
              scale: 1,
              color: 'red',
              opacity: 1,
              img: HTMLImageElement // src 옵션과 함께 사용할 수 없습니다.
              src: 'images/icon.png' // img 옵션과 함께 사용할 수 없습니다.
         }
      },
      text : {
          text: '',
          fill: { color: [1, 10, 100, 1] },
          stroke: { color: [1, 10, 100, 1] },
          font: '20px sans-serif',
      },
     bufferStyle : {
          stroke: {
              color: [67, 116, 217, 1],
              width: 2,
            },
            fill: { color: [178, 204, 255, 0.3] },
      },

})

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)

| Param | Type | Description |
| --- | --- | --- |
| styleObject | <code>Object</code> | 스타일을 생성할 Object |

<a name="DrawControl+getActiveType"></a>

### drawControl.getActiveType() ⇒ <code>String</code>
어떤 그리기가 활성화 상태인지 조회
```javascript
 let drawControl = new odf.DrawControl();
 drawControl.getActiveType();//어떤 그리기가 활성화 상태인지
```

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)
**Returns**: <code>String</code> - 어떤 그리기가 활성화 상태인지
- 'none' : 그리기 활성화상태 x
- 'text' : 텍스트 그리기 활성화 상태
- 'polygon' : 다각형 그리기 활성화 상태
- 'lineString' : 선 그리기 활성화 상태
- 'point' : 점 그리기 활성화 상태
- 'circle' : 원 그리기 활성화 상태
- 'box' : 사각형 그리기 활성화 상태
- 'square' : 정사각형 그리기 활성화 상태
- 'curve' : 곡선 그리기 활성화 상태
- 'buffer' : 버퍼 그리기 활성화 상태

<a name="ClearControl"></a>

## ClearControl
지도 그리기 이벤트 초기화 클래스

**Kind**: global class
**Summary**: ClearControl 생성자

 ```javascript
 let clearControl = new odf.ClearControl();//ClearControl 생성
```

* [ClearControl](#ClearControl)
  * [.getConstructorOptions()](#ClearControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
  * [.setMap(_map, createElementFlag)](#ClearControl+setMap)
  * [.removeMap()](#ClearControl+removeMap)
  * [.clear()](#ClearControl+clear)

<a name="ClearControl+getConstructorOptions"></a>

### clearControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>
컨트롤 생성 옵션 반환
```javascript
 let clearControl = new odf.ClearControl();//ClearControl 생성
 clearControl.getConstructorOptions();
```

**Kind**: instance method of [<code>ClearControl</code>](#ClearControl)
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환
<a name="ClearControl+setMap"></a>

### clearControl.setMap(_map, createElementFlag)
ClearControl map 객체 연결
clear HTMLElement를 만들고, 이벤트 바인딩 시켜, map 객체와 연결

 ```javascript
 let clearControl = new odf.ClearControl();//ClearControl 생성
 clearControl.setMap(map,createElementFlag); //ClearControl 객체에 map 객체 연결
```

**Kind**: instance method of [<code>ClearControl</code>](#ClearControl)

| Param | Type | Description |
| --- | --- | --- |
| _map | <code>Map</code> | ClearControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | ClearControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x |

<a name="ClearControl+removeMap"></a>

### clearControl.removeMap()
ClearControl map 객체 연결 해제

```javascript
 clearControl.removeMap(); //ClearControl 객체에 map 연결 해제
```

**Kind**: instance method of [<code>ClearControl</code>](#ClearControl)
<a name="ClearControl+clear"></a>

### clearControl.clear()
ClearControl map 객체에서 그리기 이벤트 삭제

```javascript
 clearControl.clear();
```

**Kind**: instance method of [<code>ClearControl</code>](#ClearControl)

<a name="MousePositionControl"></a>

## MousePositionControl
마우스 좌표 생성 클래스

**Kind**: global class
**Summary**: 지도 위 좌표를 표기
```javascript
MousePositionControl 생성
   [TYPE 01] 마우스 위치를 htmlElemnt에 바인딩
   let mousePositionControl = new MousePositionControl({
     element : document.querySelector('#positionStr')
   })

   [TYPE 02]  마우스 위치를 callback fucntion 으로 보냄
   let mousePositionControl = new MousePositionControl({
     callback : function(position){console.log(position)}
   })

   [TYPE 03]  마우스 위치를 htmlElemnt에 바인딩하고, 마우스 위치를 callback fucntion 으로 보냄
   let mousePositionControl = new MousePositionControl({
     element : document.querySelector('#positionStr'),
     callback : function(position){console.log(position)}
   })
   ```

* [MousePositionControl](#MousePositionControl)
  * [new MousePositionControl(options)](#new_MousePositionControl_new)
  * [.getConstructorOptions()](#MousePositionControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
  * [.setMap(map)](#MousePositionControl+setMap)
  * [.removeMap()](#MousePositionControl+removeMap)

<a name="new_MousePositionControl_new"></a>

### new MousePositionControl(options)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | 마우스 좌표 생성에 사용할 옵션 |
| options.element | <code>Element</code> | 마우스 좌표를 나타낼 Element |
| options.callback | <code>function</code> | 마우스 좌표를 마라미터로 받는 함수 |

<a name="MousePositionControl+getConstructorOptions"></a>

### mousePositionControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>
컨트롤 생성 옵션 반환
```javascript
 let mousePositionControl = new MousePositionControl({
   element : document.querySelector('#positionStr'),
    callback : function(position){console.log(position)}
 });
 mousePositionControl.getConstructorOptions();
```

**Kind**: instance method of [<code>MousePositionControl</code>](#MousePositionControl)
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환
<a name="MousePositionControl+setMap"></a>

### mousePositionControl.setMap(map)
마우스 좌표 지도에 설정

```javascript
let mousePositionControl = new odf.MousePositionControl({
        element: document.querySelector('ElementId'), //좌표 표시할 element
           callback: function (position) {       //옵션
            함수()
           },
      });
      mousePositionControl.setMap(map);
  ```

**Kind**: instance method of [<code>MousePositionControl</code>](#MousePositionControl)

| Param | Type | Description |
| --- | --- | --- |
| map | <code>Map</code> | MousePositionControl 객체와 연결할 Map 객체 |

<a name="MousePositionControl+removeMap"></a>

### mousePositionControl.removeMap()
마우스 좌표 지도에서 제거


```javascript
 mousePositionControl.removeMap();
```

**Kind**: instance method of [<code>MousePositionControl</code>](#MousePositionControl)

<a name="PrintControl"></a>

## PrintControl
프린트 설정 클래스

**Kind**: global class
**Summary**: PrintControl 생성자

```javascript
 let printControl = new odf.PrintControl();
  ```

* [PrintControl](#PrintControl)
  * [new PrintControl(options)](#new_PrintControl_new)
  * [.getConstructorOptions()](#PrintControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
  * [.setMap(map, createElementFlag)](#PrintControl+setMap)
  * [.removeMap()](#PrintControl+removeMap)
  * [.prints()](#PrintControl+prints)

<a name="new_PrintControl_new"></a>

### new PrintControl(options)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | PrintControl 객체생성 옵션 |
| options.title | <code>Object</code> | 프린트 시 상단에 표시될 텍스트 (기본값: 공백) |

<a name="PrintControl+getConstructorOptions"></a>

### printControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>
컨트롤 생성 옵션 반환
```javascript
 let printControl = new odf.PrintControl({title : '출력 텍스트'});
 printControl.getConstructorOptions();
```

**Kind**: instance method of [<code>PrintControl</code>](#PrintControl)
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환
<a name="PrintControl+setMap"></a>

### printControl.setMap(map, createElementFlag)
PrintControl map 객체 연결

```javascript
 let printControl = new odf.PrintControl();
 printControl.setMap(map, createElementFlag); //PrintControl 객체에 map 객체 연결
```

**Kind**: instance method of [<code>PrintControl</code>](#PrintControl)

| Param | Type | Description |
| --- | --- | --- |
| map | <code>Map</code> | PrintControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | PrintControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x |

<a name="PrintControl+removeMap"></a>

### printControl.removeMap()
PrintControl map 객체 연결 해제

```javascript
 printControl.removeMap(); //PrintControl 객체에 map 연결 해제
```

**Kind**: instance method of [<code>PrintControl</code>](#PrintControl)
<a name="PrintControl+prints"></a>

### printControl.prints()
PrintControl 프린트 실행 함수

```javascript
 printControl.prints(); //PrintControl 프린트 실행 함수
```

**Kind**: instance method of [<code>PrintControl</code>](#PrintControl)

<a name="DownloadControl"></a>

## DownloadControl
다운로드 설정 클래스

**Kind**: global class
**Summary**: DownloadControl 생성자

```javascript
 let downloadControl = new odf.DownloadControl({});
```

* [DownloadControl](#DownloadControl)
  * [.getConstructorOptions()](#DownloadControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
  * [.setMap(_map, createElementFlag)](#DownloadControl+setMap)
  * [.removeMap()](#DownloadControl+removeMap)
  * [.downloadsImage()](#DownloadControl+downloadsImage)
  * [.downloadsPDF()](#DownloadControl+downloadsPDF)

<a name="DownloadControl+getConstructorOptions"></a>

### downloadControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>
컨트롤 생성 옵션 반환
```javascript
 let downloadControl = new odf.DownloadControl({})
 downloadControl.getConstructorOptions();
```

**Kind**: instance method of [<code>DownloadControl</code>](#DownloadControl)
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환
<a name="DownloadControl+setMap"></a>

### downloadControl.setMap(_map, createElementFlag)
DownloadControl map 객체 연결

```javascript
 let downloadControl = new odf.DownloadControl({
        });
 downloadControl.setMap(map, createElementFlag); //DownloadControl 객체에 map 객체 연결
```

**Kind**: instance method of [<code>DownloadControl</code>](#DownloadControl)

| Param | Type | Description |
| --- | --- | --- |
| _map | <code>Map</code> | DownloadControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | DownloadControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x |

<a name="DownloadControl+removeMap"></a>

### downloadControl.removeMap()
DownloadControl map 객체 연결 해제

```javascript
 downloadControl.removeMap(); //DownloadControl 객체에 map 연결 해제
```

**Kind**: instance method of [<code>DownloadControl</code>](#DownloadControl)
<a name="DownloadControl+downloadsImage"></a>

### downloadControl.downloadsImage()
DownloadControl  이미지 다운로드 함수

```javascript
 downloadControl.downloadsImage();
```

**Kind**: instance method of [<code>DownloadControl</code>](#DownloadControl)
<a name="DownloadControl+downloadsPDF"></a>

### downloadControl.downloadsPDF()
DownloadControl PDF 다운로드 함수

```javascript
 downloadControl.downloadsPDF();
```

**Kind**: instance method of [<code>DownloadControl</code>](#DownloadControl)

## Classes

<dl>
<dt><a href="#DivideMapControl">DivideMapControl</a></dt>
<dd><p>지도 분할 설정 클래스</p>
</dd>
</dl>

## Typedefs

<dl>
<dt><a href="#odf_divideMapOption">odf_divideMapOption</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_divideMaps">odf_divideMaps</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_divideMap_info">odf_divideMap_info</a> : <code>Object</code></dt>
<dd></dd>
</dl>

<a name="DivideMapControl"></a>

## DivideMapControl
지도 분할 설정 클래스

**Kind**: global class
**Summary**: DivideMapControl 생성자

 ```javascript
 let divideMapControl = new odf.DivideMapControl({
        dualMap: [
          {
            position: 1,
            mapOption: {
              //지정안한 map옵션은 mainmap 생성시 사용한 mapoption적용
              basemap: {baroEMap:['eMapColor']},
            },
            //controlOption: {//사용할 컨트롤 지정
            // basemap: true,
            // zoom: false,
            // clear: false,
            // download: false,
            // print: false,
            // overviewmap: false,
            // draw: false,
            // measure: false,
            // move: false,
            //},
          },
        ],
        quadMap: [
          {
            // position: 1, //지정안하면 기본 1
            mapOption: {
              basemap: {baroEMap:['eMapColor']},
            },
          },
          {
            //position: 2, //지정안하면 기본 3
            mapOption: {
              basemap:{baroEMap: ['eMapWhite']},
            },
          },
          {
            //position: 4,//지정안하면 기본 4
            mapOption: {
              basemap: {baroEMap:['eMapEnglish']},
            },
          },
        ],
        config : {
          dualMap:{
            divType : 'vertical'//'vertical'(수직분할), 'horizonal'(수평분할)
          }
        }
      });//DivideMapControl 생성
```

* [DivideMapControl](#DivideMapControl)
  * [new DivideMapControl(options)](#new_DivideMapControl_new)
  * [.getConstructorOptions()](#DivideMapControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
  * [.setMap(_map, createElementFlag)](#DivideMapControl+setMap)
  * [.removeMap()](#DivideMapControl+removeMap)
  * [.setOn(key, flag, sizeFlag, connectFlag)](#DivideMapControl+setOn)
  * [.setConnect(flag)](#DivideMapControl+setConnect)
  * [.getDividMaps()](#DivideMapControl+getDividMaps) ⇒ [<code>odf\_divideMaps</code>](#odf_divideMaps)

<a name="new_DivideMapControl_new"></a>

### new DivideMapControl(options)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | 분할지도 생성을 위한 option |
| options.dualMap | [<code>Array.&lt;odf\_divideMapOption&gt;</code>](#odf_divideMapOption) | 2분할지도 option |
| options.threepleMap | [<code>Array.&lt;odf\_divideMapOption&gt;</code>](#odf_divideMapOption) | 3분할지도 option |
| options.quadMap | [<code>Array.&lt;odf\_divideMapOption&gt;</code>](#odf_divideMapOption) | 4분할지도 option |
| options.config | <code>Object</code> | 분할지도 상세 생성 옵션 |
| options.config.createElementFlag | <code>Boolean</code> | 분할지도 내 컨트롤 ui 생성 여부, 기본값 true |
| options.config.dualMap | <code>Object</code> | 2분할지도 상세 생성 옵션 |
| options.config.dualMap.divType | <code>String</code> | 2분할지도 분할 유형.  - 'vertical' : 수직 분할 (기본값) ┌─┬─┐ │1│2│ └─┴─┘ - 'horizonal' : 수평 분할 ┌───┐ │ 1 │ ├───┤ │ 2 │ └───┘ |
| options.config.threepleMap | <code>Object</code> | 3분할지도 상세 생성 옵션 |
| options.config.threepleMap.divType | <code>String</code> | 3분할지도 분할 유형 - 'vertical' : 수직 분할 (기본값) ┌─┬─┬─┐ │1│2│3│ └─┴─┴─┘ - 'horizonal' : 수평 분할 ┌───┐ │ 1 │ ├───┤ │ 2 │ ├───┤ │ 3 │ └───┘ - 'complex-01' : 복합형 1 ┌─┬───┐ │ │ 2 │ │1├───┤ │ │ 3 │ └─┴───┘ - 'complex-02' : 복합형 2 ┌─────┐ │  1  │ ├──┬──┤ │2 │ 3│ └──┴──┘ - 'complex-03' : 복합형 3 ┌──┬─┐ │2 │ │ ├──┤1│ │3 │ │ └──┴─┘ - 'complex-04' : 복합형 4 ┌──┬──┐ │ 1│2 │ ├──┴──┤ │  3  │ └─────┘ |
| options.config.quadMap | <code>Object</code> | 4분할지도 상세 생성 옵션 |
| options.config.quadMap.divType | <code>String</code> | 4분할지도 분할 유형.  - 'complex' : 수직,수평 분할 (기본값) ┌───┬───┐ │ 1 │ 2 │ ├───┼───┤ │ 3 │ 4 │ └───┴───┘ - 'vertical' : 수직 분할  ┌─┬─┬─┬─┐ │1│2│3│4│ └─┴─┴─┴─┘ - 'horizonal' : 수평 분할 ┌─────┐ │  1  │ ├─────┤ │  2  │ ├─────┤ │  3  │ ├─────┤ │  4  │ └─────┘ |

<a name="DivideMapControl+getConstructorOptions"></a>

### divideMapControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>
컨트롤 생성 옵션 반환
```javascript
 let divideMapControl = new odf.DivideMapControl({...});
 divideMapControl.getConstructorOptions();
```

**Kind**: instance method of [<code>DivideMapControl</code>](#DivideMapControl)
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환
<a name="DivideMapControl+setMap"></a>

### divideMapControl.setMap(_map, createElementFlag)
DivideMapControl map 객체 연결
DivideMapControl HTMLElement를 만들고, 이벤트 바인딩 시켜, map 객체와 연결

 ```javascript
 let divideMapControl = new odf.DivideMapControl();//DivideMapControl 생성
 divideMapControl.setMap(map,createElementFlag); //DivideMapControl 객체에 map 객체 연결
```

**Kind**: instance method of [<code>DivideMapControl</code>](#DivideMapControl)

| Param | Type | Description |
| --- | --- | --- |
| _map | <code>Map</code> | DivideMapControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | DivideMapControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x |

<a name="DivideMapControl+removeMap"></a>

### divideMapControl.removeMap()
DivideMapControl map 객체 연결 해제

```javascript
 divideMapControl.removeMap(); //DivideMapControl 객체에 map 연결 해제
```

**Kind**: instance method of [<code>DivideMapControl</code>](#DivideMapControl)
<a name="DivideMapControl+setOn"></a>

### divideMapControl.setOn(key, flag, sizeFlag, connectFlag)
분할지도 활성화/비활성화
 ```javascript
 let divideMapControl = new odf.DivideMapControl();//DivideMapControl 생성
 divideMapControl.setMap(map); //DivideMapControl 객체에 map 객체 연결
 divideMapControl.setOn('dualMap',true); //dualMap 활성화
 //divideMapControl.setOn('dualMap',false); //dualMap 비활성화
```

**Kind**: instance method of [<code>DivideMapControl</code>](#DivideMapControl)

| Param | Type | Description |
| --- | --- | --- |
| key | <code>String</code> | (비)활성화 시킬 대상 => 'dualMap' 또는 'quadMap' |
| flag | <code>Boolean</code> | 활성화 여부 =>true : 활성화, false:비활성화 |
| sizeFlag | <code>Boolean</code> | 사이즈 변환여부(default true) =>true : 사이즈 변환, false:사이즈 변환x |
| connectFlag | <code>Boolean</code> | 지도 동기화 여부(default true) =>true : 지도 동기화, false:지도 비동기화 |

<a name="DivideMapControl+setConnect"></a>

### divideMapControl.setConnect(flag)
분할지도 뷰공유 활성화/비활성화
 ```javascript
 let divideMapControl = new odf.DivideMapControl();//DivideMapControl 생성
 divideMapControl.setMap(map); //DivideMapControl 객체에 map 객체 연결
 divideMapControl.setConnect(true); //dualMap 활성화
```

**Kind**: instance method of [<code>DivideMapControl</code>](#DivideMapControl)

| Param | Type | Description |
| --- | --- | --- |
| flag | <code>Boolean</code> | 지도 뷰공유 여부(default true) =>true : 지도 뷰공유, false:지도 비공유 |

<a name="DivideMapControl+getDividMaps"></a>

### divideMapControl.getDividMaps() ⇒ [<code>odf\_divideMaps</code>](#odf_divideMaps)
분할지도 지도객체 조회
 ```javascript
 let divideMapControl = new odf.DivideMapControl();//DivideMapControl 생성
 divideMapControl.setMap(map); //DivideMapControl 객체에 map 객체 연결
 divideMapControl.getDividMaps();//분할지도 지도객체 조회
```

**Kind**: instance method of [<code>DivideMapControl</code>](#DivideMapControl)
**Returns**: [<code>odf\_divideMaps</code>](#odf_divideMaps) - -분할지도 지도 객체
<a name="odf_divideMapOption"></a>

## odf\_divideMapOption : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| position | <code>number</code> | 분할지도가 위치할 번호(지정하지 않을 경우 기본값 지정) 2분할 지도(수직분할)의 경우 1은 좌측, 2는 우측 2분할 지도(수평분할)의 경우 1은 상단, 2는 하단 4분할 지도의 경우 1은 좌상단, 2는 우상단, 3은 좌하단, 4는 우하단 |
| mapOption | <code>Object</code> | 지도 생성에 사용할 옵션 |
| mapOption.center | <code>Odf.Coordinate</code> | 지도 중심점 좌표 |
| mapOption.zoom | <code>Number</code> | 현재 확대 레벨 |
| mapOption.maxZoom | <code>Number</code> | 최대 확대레벨 |
| mapOption.minZoom | <code>Number</code> | 최소 확대레벨 |
| mapOption.projection | <code>String</code> | 좌표계 SRS ID |
| mapOption.proxyURL | <code>String</code> | 프록시 URL |
| mapOption.proxyParam | <code>String</code> | 프록시 파라미터 |
| mapOption.apiGateWayKey | <code>String</code> | api GateWay Key |
| mapOption.basemap | <code>odf\_basemap\_option</code> | 사용할 베이스맵 선택 옵션 |
| mapOption.baroEMapURL | <code>String</code> | 바로e맵 경로 |
| mapOption.baroEMapKey | <code>String</code> | 바로e맵 API KEY |
| controlOption | <code>Object</code> | 분할지도 내에서 사용할 컨트롤 지정 |
| controlOption.basemap | <code>Boolean</code> | basemap 컨트롤을 사용할지 여부 |
| controlOption.clear | <code>Boolean</code> | clear 컨트롤을 사용할지 여부 |
| controlOption.download | <code>Boolean</code> | download 컨트롤을 사용할지 여부 |
| controlOption.print | <code>Boolean</code> | print 컨트롤을 사용할지 여부 |
| controlOption.overviewmap | <code>Boolean</code> | overviewmap 컨트롤을 사용할지 여부 |
| controlOption.draw | <code>Boolean</code> | draw 컨트롤을 사용할지 여부 |
| controlOption.measure | <code>Boolean</code> | measure 컨트롤을 사용할지 여부 |
| controlOption.move | <code>Boolean</code> | move 컨트롤을 사용할지 여부 |

<a name="odf_divideMaps"></a>

## odf\_divideMaps : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| dualMap | [<code>Array.&lt;odf\_divideMap\_info&gt;</code>](#odf_divideMap_info) | 2분할지도 객체 |
| quadMap | [<code>Array.&lt;odf\_divideMap\_info&gt;</code>](#odf_divideMap_info) | 4분할지도 객체 |

<a name="odf_divideMap_info"></a>

## odf\_divideMap\_info : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| map | <code>Map</code> | 분할지도 지도 객체 |
| position | <code>Number</code> | 분할지도 위치 (1~4) |
| mainMapFlag | <code>Boolean</code> | 메인 지도 여부 ※true이면 메인지도, false이면 분할지도 객체 |


<a name="FullScreenControl"></a>

## FullScreenControl
전체화면 생성 클래스

**Kind**: global class
**Summary**: FullScreenControl 생성자
 ```javascript
 let fullScreenControl = new odf.FullScreenControl();//FullScreenControl 생성
```

* [FullScreenControl](#FullScreenControl)
  * [new FullScreenControl(options)](#new_FullScreenControl_new)
  * [.getConstructorOptions()](#FullScreenControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
  * [.setMap(_map, createElementFlag)](#FullScreenControl+setMap)
  * [.removeMap()](#FullScreenControl+removeMap)
  * [.doFullScreen()](#FullScreenControl+doFullScreen)
  * [.getState()](#FullScreenControl+getState) ⇒ <code>Boolean</code>

<a name="new_FullScreenControl_new"></a>

### new FullScreenControl(options)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | 전체화면 컨트롤 생성 옵션 |
| options.target | <code>Boolean</code> | 전체화면 영역 정의 |

<a name="FullScreenControl+getConstructorOptions"></a>

### fullScreenControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>
컨트롤 생성 옵션 반환
```javascript
 let fullScreenControl = new odf.FullScreenControl();//FullScreenControl 생성
 fullScreenControl.getConstructorOptions();
```

**Kind**: instance method of [<code>FullScreenControl</code>](#FullScreenControl)
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환
<a name="FullScreenControl+setMap"></a>

### fullScreenControl.setMap(_map, createElementFlag)
FullScreenControl map 객체 연결
FullScreen HTMLElement를 만들고, 이벤트 바인딩 시켜, map 객체와 연결

 ```javascript
 let fullScreenControl = new odf.FullScreenControl();//FullScreenControl 생성
 fullScreenControl.setMap(map,createElementFlag); //FullScreenControl 객체에 map 객체 연결
```

**Kind**: instance method of [<code>FullScreenControl</code>](#FullScreenControl)

| Param | Type | Description |
| --- | --- | --- |
| _map | <code>Map</code> | FullScreenControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | FullScreenControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x |

<a name="FullScreenControl+removeMap"></a>

### fullScreenControl.removeMap()
FullScreenControl map 객체 연결 해제

```javascript
 fullScreenControl.removeMap(); //FullScreenControl 객체에 map 연결 해제
```

**Kind**: instance method of [<code>FullScreenControl</code>](#FullScreenControl)
<a name="FullScreenControl+doFullScreen"></a>

### fullScreenControl.doFullScreen()
FullScreenControl 활성화/비활성화

```javascript
 fullScreenControl.doFullScreen();
```

**Kind**: instance method of [<code>FullScreenControl</code>](#FullScreenControl)
<a name="FullScreenControl+getState"></a>

### fullScreenControl.getState() ⇒ <code>Boolean</code>
FullScreenControl의 상태 반환
```javascript
 fullScreenControl.getState();
```

**Kind**: instance method of [<code>FullScreenControl</code>](#FullScreenControl)
**Returns**: <code>Boolean</code> - FullScreenControl의 상태
- true :  fullscreen 활성화 상태
- false :  fullscreen 비활성화 상태

<a name="RotationControl"></a>

## RotationControl
화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전

**Kind**: global class
**Summary**: RotationControl 생성자
 ```javascript
 let RotationControl = new odf.RotationControl();//RotationControl 생성
```

* [RotationControl](#RotationControl)
  * [.getConstructorOptions()](#RotationControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
  * [.setMap(_map, createElementFlag)](#RotationControl+setMap)
  * [.removeMap()](#RotationControl+removeMap)
  * [.setRotation(value)](#RotationControl+setRotation)
  * [.getRotation()](#RotationControl+getRotation) ⇒ <code>Number</code>

<a name="RotationControl+getConstructorOptions"></a>

### rotationControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>
컨트롤 생성 옵션 반환
```javascript
 let rotationControl = new odf.RotationControl();//RotationControl 생성
 rotationControl.getConstructorOptions();
```

**Kind**: instance method of [<code>RotationControl</code>](#RotationControl)
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환
<a name="RotationControl+setMap"></a>

### rotationControl.setMap(_map, createElementFlag)
RotationControl map 객체 연결
RotationControl HTMLElement를 만들고, 이벤트 바인딩 시켜, map 객체와 연결

 ```javascript
 let rotationControl = new odf.RotationControl();//RotationControl 생성
 rotationControl.setMap(map,createElementFlag); //RotationControl 객체에 map 객체 연결
```

**Kind**: instance method of [<code>RotationControl</code>](#RotationControl)

| Param | Type | Description |
| --- | --- | --- |
| _map | <code>Map</code> | RotationControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | RotationControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x |

<a name="RotationControl+removeMap"></a>

### rotationControl.removeMap()
RotationControl map 객체 연결 해제

```javascript
 rotationControl.removeMap(); //RotationControl 객체에 map 연결 해제
```

**Kind**: instance method of [<code>RotationControl</code>](#RotationControl)
<a name="RotationControl+setRotation"></a>

### rotationControl.setRotation(value)
RotationControl 활성화/비활성화
```javascript
 rotationControl.setRotation(90*Math.PI/180);//회전
```

**Kind**: instance method of [<code>RotationControl</code>](#RotationControl)

| Param | Type | Description |
| --- | --- | --- |
| value | <code>Number</code> | 회전시킬 값 (단위:라디안) |

<a name="RotationControl+getRotation"></a>

### rotationControl.getRotation() ⇒ <code>Number</code>
RotationControl의 상태 반환
```javascript
 rotationControl.getRotation();
```

**Kind**: instance method of [<code>RotationControl</code>](#RotationControl)
**Returns**: <code>Number</code> - 회전값

## Classes

<dl>
<dt><a href="#LayerFactory">LayerFactory</a></dt>
<dd><p>레이어 생성 클래스</p>
</dd>
</dl>

## Typedefs

<dl>
<dt><a href="#service">service</a> : <code>String</code></dt>
<dd><p>레이어 호출 종류</p>
</dd>
<dt><a href="#geoserverOption">geoserverOption</a> : <code>Object</code></dt>
<dd><p>레이어 생성을 위한 옵션</p>
</dd>
<dt><a href="#odfWebGLCustomAttributeDefine">odfWebGLCustomAttributeDefine</a> ⇒ <code>?</code></dt>
<dd></dd>
<dt><a href="#odfWebGLAttributeObject">odfWebGLAttributeObject</a> : <code>Object</code></dt>
<dd><p>사용자 정의 속성 정보</p>
</dd>
<dt><a href="#odfWebGLVectorTileStyle">odfWebGLVectorTileStyle</a> : <code>Object</code></dt>
<dd><p>webGL vector tile 레이어 스타일 렌더 옵션</p>
</dd>
<dt><a href="#imageOption">imageOption</a> : <code>Object</code></dt>
<dd><p>이미지 레이어 생성을 위한 옵션</p>
</dd>
<dt><a href="#geotiffSourceOption">geotiffSourceOption</a> : <code>Object</code></dt>
<dd><p>geotiff 레이어 생성 옵션</p>
</dd>
<dt><a href="#geotiffOption">geotiffOption</a> : <code>Object</code></dt>
<dd><p>geotiff 레이어 생성 옵션</p>
</dd>
<dt><a href="#svgOption">svgOption</a> : <code>Object</code></dt>
<dd><p>svg 레이어 생성 옵션</p>
</dd>
<dt><a href="#odfFlatStyle">odfFlatStyle</a> : <code>Object</code></dt>
<dd><p>odf flat 스타일</p>
</dd>
<dt><a href="#odfStyleExpression">odfStyleExpression</a> : <code>Array.&lt;(Sting|Number|odfStyleExpression)&gt;</code></dt>
<dd><p>odf 표현식</p>
<ul>
<li><p>읽기 연산자</p>
<ul>
<li><code>[&#39;band&#39;, bandIndex, xOffset, yOffset]</code> [타일 ​​레이어]</li>
<li><code>[&#39;get&#39;, &#39;attributeName&#39;, typeHint]</code> 레이어에서 프로퍼티 명이 &#39;attributeName&#39;인 값 조회. typeHint는 옵셔널한 값
 &#39;string&#39;, &#39;color&#39;, &#39;number&#39;, &#39;boolean&#39;, &#39;number[]&#39;</li>
<li><code>[&#39;geometry-type&#39;]</code> 도형의 지오메트리 타입
 &#39;LineString&#39;, &#39;Point&#39;, &#39;Polygon&#39;, <code>MultiLineString</code>, <code>MultiPoint</code>, <code>MultiPolygon</code></li>
<li><code>[&#39;resolution&#39;]</code> 현재 해상도 값</li>
<li><code>[&#39;time&#39;]</code> 현재 해상도 값</li>
<li><code>[&#39;var&#39;, &#39;varName&#39;]</code> 스타일 변수에서 값을 가져옵니다. 해당 변수가 정의되지 않은 경우 오류가 발생</li>
<li><code>[&#39;zoom&#39;]</code> 현재 확대/축소 수준</li>
<li><code>[&#39;id&#39;]</code> feature의 id</li>
</ul>
</li>
<li><p>수학 연산자</p>
<ul>
<li><code>[&#39;*&#39;, value1, value2, ...]</code> <code>value1</code> * <code>value2</code> * ...</li>
<li><code>[&#39;/&#39;, value1, value2]</code> <code>value1</code> / <code>value2</code></li>
<li><code>[&#39;+&#39;, value1, value2, ...]</code> <code>value1</code> + <code>value2</code> + ...</li>
<li><code>[&#39;-&#39;, value1, value2]</code> <code>value1</code> - <code>value2</code></li>
<li><code>[&#39;clamp&#39;, value, low, high]</code> <code>low</code>와 <code>high</code> 사이에 값을 고정. <code>value</code>가 <code>low</code>보다 작으면 <code>low</code> 반환, <code>value</code>가 <code>high</code>보다 크면 <code>high</code> 반환</li>
<li><code>[&#39;%&#39;, value1, value2]</code> <code>value1 % value2</code></li>
<li><code>[&#39;^&#39;, value1, value2]</code> <code>value1</code> ^ <code>value2</code></li>
<li><code>[&#39;abs&#39;, value1]</code> <code>value1</code>의 절대값</li>
<li><code>[&#39;floor&#39;, value1]</code> <code>value1</code>과 작거나 같은 가장 가까운 정수 반환(내림)</li>
<li><code>[&#39;round&#39;, value1]</code> <code>value1</code>과 가장 가까운 정수 반환(반올림)</li>
<li><code>[&#39;ceil&#39;, value1]</code> <code>value1</code>과 크나 같은 가장 가까운 정수 반환(올림)</li>
<li><code>[&#39;sin&#39;, value1]</code> <code>value1</code>의 sin값 반환</li>
<li><code>[&#39;cos&#39;, value1]</code> <code>value1</code>의 cosine값 반환</li>
<li><code>[&#39;atan&#39;, value1, value2]</code> atan2(value1, value2) 반환. <code>value2</code>가 없을 경우 atan(value1) 반환</li>
<li><code>[&#39;sqrt&#39;, value1]</code> <code>value1</code>의 제곱근 반환</li>
</ul>
</li>
<li><p>변환 연산자</p>
<ul>
<li><code>[&#39;case&#39;, condition1, output1, ...conditionN, outputN, fallback]</code> <code>condition1</code> 조건이 참이면 <code>output1</code> 반환, <code>conditionN</code>이 참이면 <code>outputN</code> 반환. 일치하는 조건이 없으면 <code>fallback</code> 반환</li>
<li><code>[&#39;match&#39;, input, match1, output1, ...matchN, outputN, fallback]</code> <code>input</code> 값이 <code>match1</code>과 같으면 <code>output1</code> 반환,<code>matchN</code>과 같으면 <code>outputN</code> 반환. 일치하는 값이 없으면 <code>fallback</code> 반환</li>
<li><code>[&#39;interpolate&#39;, interpolation, input, stop1, output1, ...stopN, outputN]</code> 값을 보간하여 반환. <code>input</code> 값이 <code>stop1</code>과 같으면 <code>output1</code> 반환,<code>stopN</code>과 같으면 <code>outputN</code> 반환</li>
</ul>
</li>
</ul>
<ul>
<li><code>input</code>값이 <code>stop1</code>과 <code>stop2</code> 사이의 값이라면 <code>output1</code>과  <code>output2</code>사이의 값을 보간하여 반환</li>
<li>interpolation은 보간 방법 정의. [&#39;linear&#39;] :  선형 보간법 ,[&#39;exponential&#39;, base] : 지수보간법(base는 stop A에서 stop B까지의 증가율)</li>
</ul>
<ul>
<li>논리 연산자<ul>
<li><code>[&#39;&lt;&#39;, value1, value2]</code> <code>value1</code> &lt; <code>value2</code></li>
<li><code>[&#39;&lt;=&#39;, value1, value2]</code> <code>value1</code> &lt;= <code>value2</code></li>
<li><code>[&#39;&gt;&#39;, value1, value2]</code> <code>value1</code> &gt; <code>value2</code></li>
<li><code>[&#39;&gt;=&#39;, value1, value2]</code> <code>value1</code> &gt;= <code>value2</code></li>
<li><code>[&#39;==&#39;, value1, value2]</code> <code>value1</code> == <code>value2</code></li>
<li><code>[&#39;!=&#39;, value1, value2]</code> <code>value1</code> != <code>value2</code></li>
<li><code>[&#39;!&#39;, value1]</code> !<code>value1</code></li>
<li><code>[&#39;all&#39;, value1, value2, ...]</code> <code>value1</code>과 <code>value2</code> ...가 모두 true일때 true 반환, 그 외 모두 false</li>
<li><code>[&#39;any&#39;, value1, value2, ...]</code> <code>value1</code> 또는 <code>value2</code> ... 중 하나라도 true면 true 반환. 모두 false일때 false 반환</li>
<li><code>[&#39;between&#39;, value1, value2, value3]</code>  <code>value1</code>이 <code>value2</code>와 <code>value3</code> 사이에 있으면 true, 아니면 false 반환. <code>value1</code>이 <code>value2</code> 또는 <code>value3</code>과 동일해도 true</li>
<li><code>[&#39;in&#39;, needle, haystack]</code> <code>needle</code>이 <code>haystack</code>안에서 발견되면  true, 아니면 false.</li>
</ul>
</li>
</ul>
<ul>
<li><code>haystack</code>은 숫자 또는 문자열로 구성된 배열. 현재는 상수만 지원됨.</li>
</ul>
<ul>
<li>변환 연산자<ul>
<li><code>[&#39;concat&#39;, value1, ...valueN]</code> 문자열을 합쳐서 반환</li>
<li><code>[&#39;array&#39;, value1, ...valueN]</code> 숫자 배열 생성. 현재는 2~4개 요소만 생성 가능</li>
<li><code>[&#39;color&#39;, red, green, blue, alpha]</code> 색상 값 생성. alpha 값은 0~1 사이의 값</li>
<li><code>[&#39;palette&#39;, index, colors]</code> colors(색상배열)에서 index 위치의 색상 추출</li>
</ul>
</li>
</ul>
<ul>
<li>colors는 문자열의 배열이며, 색상은 hex 형태(&#39;#aaa&#39;) 또는 rgb(a) 형태(<code>&#39;rgb(134, 161, 54)&#39;</code> 또는 <code>&#39;rgba(134, 161, 54, 1)&#39;</code>)가 가능</li>
</ul>
</dd>
<dt><a href="#odfStyleRule">odfStyleRule</a> : <code><a href="#odfStyleRuleObject">Array.&lt;odfStyleRuleObject&gt;</a></code></dt>
<dd><p>odf 스타일 규칙</p>
<pre><code class="language-javascript">  const rules = [
  {
    filter: [&#39;&gt;&#39;, [&#39;get&#39;, &#39;population&#39;], 1_000_000],
    style: {
      &#39;circle-radius&#39;: 10,
      &#39;circle-fill-color&#39;: &#39;red&#39;,
    }
  },
  {
    else: true,
    style: {
      &#39;circle-radius&#39;: 5,
      &#39;circle-fill-color&#39;: &#39;blue&#39;,
    },
  },
];
</code></pre>
</dd>
<dt><a href="#odfStyleRuleObject">odfStyleRuleObject</a> : <code>Object</code></dt>
<dd><p>odf 스타일 규칙</p>
</dd>
<dt><a href="#geojsonOption">geojsonOption</a> : <code>Object</code></dt>
<dd><p>geojson 레이어 생성을 위한 옵션</p>
</dd>
<dt><a href="#kmlOption">kmlOption</a> : <code>Object</code></dt>
<dd><p>KML 레이어 생성을 위한 옵션</p>
</dd>
<dt><a href="#csvOption">csvOption</a> : <code>Object</code></dt>
<dd><p>CSV 레이어 생성을 위한 옵션</p>
</dd>
<dt><a href="#apiOption">apiOption</a> : <code>Object</code></dt>
<dd><p>api 레이어 생성을 위한 옵션</p>
</dd>
</dl>

<a name="LayerFactory"></a>

## LayerFactory
레이어 생성 클래스

**Kind**: global class

* [LayerFactory](#LayerFactory)
  * [.produce(type, params)](#LayerFactory.produce) ⇒ <code>Layer</code>
  * [.getWmtsLayerOpt(opt)](#LayerFactory.getWmtsLayerOpt) ⇒ <code>Json</code>
  * [.intersect(al, tla)](#LayerFactory.intersect) ⇒ <code>Feature</code>

<a name="LayerFactory.produce"></a>

### LayerFactory.produce(type, params) ⇒ <code>Layer</code>
레이어 객체 생성

```javascript
let option = {
  server: 'GeoServer URL 입력',
  layer: 'Store 명칭:Layer 명칭 입력',
  service: '서비스 종류', //wfs, wms, wcs, wmts
  //matrixSet: 'GeoServer에 발행된 타일매트릭스 중 선택', // wmts 일 경우에 사용. 생략하면 지도 좌표계 이용
}

let sample = odf.LayerFactory.produce(type, option);
sample.setMap(map);
```
```javascript
//그룹레이어
let groupLayer = odf.LayerFactory.produce('geoserver', { // 레이어 호출 방법 (ex. geoserver, geojson)
 server: 'GeoServer URL 입력',  //wfs, wms, wcs
  layer: 그룹레이어 명 ('저장소:레이어명')
  service: 'group',
});
```
```javascript
//kml 텍스트
var kmlText = '<?xml version="1.0" encoding="UTF-8"?><kml xmlns="http://www.opengis.net/kml/2.2"  xmlns:gx="http://www.google.com/kml/ext/2.2" xmlns:kml="http://www.opengis.net/kml/2.2"        xmlns:atom="http://www.w3.org/2005/Atom"><Document><name><![CDATA[odf-emptyLayer-vector1605257609045f9x2gq94bc]]></name><description><![CDATA[odf-emptyLayer-vector1605257609045f9x2gq94bc]]></description><visibility>1</visibility><open>1</open><Style id="area"><LineStyle><color>ff0000ff</color><width>3</width></LineStyle><PolyStyle><color>55ffff55</color></PolyStyle></Style><Folder><description>Polygon</description><visibility>1</visibility><open>0</open><name>Polygon</name><Placemark><styleUrl>#area</styleUrl><MultiGeometry><Polygon><outerBoundaryIs><LinearRing><coordinates>1102899.2389631933,1717024.0130509103 1102999.45025565,1717133.768275982 1102975.5904241127,1716985.8373204507 1103142.6092448737,1716947.661589991 1102985.1343567276,1716899.9419269164 1103018.5381208798,1716732.9231061554 1102927.870761038,1716847.4502975345 1102846.7473338114,1716728.151139848 1102832.4314348889,1716866.5381627642 1102698.81637828,1716966.749455221 1102899.2389631933,1717024.0130509103 </coordinates></LinearRing></outerBoundaryIs></Polygon><Polygon><outerBoundaryIs><LinearRing><coordinates>1103548.2263810078,1717004.9251856806 1103572.0862125452,1717181.4879390565 1103681.8414376169,1717033.5569835252 1103901.35188776,1717071.7327139848 1103786.8246963809,1716938.117657376 1103872.7200899152,1716842.6783312266 1103724.789134384,1716880.8540616864 1103610.2619430048,1716670.8875441581 1103605.4899766974,1716866.538162764 1103385.9795265542,1716871.3101290714 1103548.2263810078,1717004.9251856806 </coordinates></LinearRing></outerBoundaryIs></Polygon><Polygon><outerBoundaryIs><LinearRing><coordinates>1103223.7326721007,1716346.393835251 1103223.7326721007,1716494.324790782 1103319.1719982498,1716351.1658015584 1103424.1552570139,1716379.7975994032 1103385.9795265542,1716260.4984417167 1103467.1029537811,1716198.4628797197 1103323.9439645573,1716212.778778642 1103228.504638408,1716088.707654648 1103199.8728405633,1716212.778778642 1103047.1699187246,1716260.4984417167 1103223.7326721007,1716346.393835251 </coordinates></LinearRing></outerBoundaryIs></Polygon></MultiGeometry></Placemark><Placemark><styleUrl>#area</styleUrl><Polygon><outerBoundaryIs><LinearRing><coordinates>1099959.7077177984,1716260.498441717 1100098.0947407146,1716608.8519821614 1100675.5026639171,1716794.9586681523 1101190.875025123,1716561.132319087 1101114.5235642034,1716308.2181047916 1100661.1867649949,1715859.6532718902 1099773.6010318075,1715530.3875966757 1099353.667996751,1716160.2871492603 1099277.3165358317,1716584.992150624 1099329.8081652136,1717028.785017218 1099730.6533350402,1717119.4523770595 1100083.7788417924,1717024.0130509103 1100145.8144037894,1716713.8352409257 1099959.7077177984,1716260.498441717 </coordinates></LinearRing></outerBoundaryIs></Polygon></Placemark></Folder></Document></kml>';
//kml 레이어 생성
var kmlLayer = odf.LayerFactory.produce('kml', {
     	data: kmlText,//kml 형식 텍스트
      dataProjectionCode: 'EPSG:5179', //원본 좌표계
      featureProjectionCode: 'EPSG:5179',// 변환할 좌표계(지도좌표계)
});
kmlLayer.setMap(map);
```
```javascript
//csv 텍스트
var csvText = '[[csv 텍스트]]';
//csv 레이어 생성
var csvLayer = odf.LayerFactory.produce('csv', {
	     data: csvText,//csv 형식 텍스트
      dataProjectionCode: 'EPSG:5179', //원본 좌표계
      featureProjectionCode: 'EPSG:5179',// 변환할 좌표계(지도좌표계)
      geometryColumnName : 'the_geom',//geometry column
      delimiter : ','//구분자(기본값 콤마(,))
});
csvLayer.setMap(map);
```
```javascript
// api 레이어 생성(국가공간정보 포탈 WMS API)
   var apiLayer = odf.LayerFactory.produce('api', {
        server: 'http://openapi.nsdi.go.kr/nsdi/map/LandInfoBaseMapITRF2000BlueService', // API 주소
        layers: '0', //레이어명. 여러개일경우, 쉼표(,)로 구분
        service: 'wms', // wms/ wfs/xyz/wmts
        crs : 'EPSG:5179', // 요청 좌표계
        //bgcolor : '0xRRGGBB', //배경색
        transparent : 'true',//반환 이미지 배경의 투명 여부-'true'/'false'
        //exceptions:''//예외 발생 시 처리 방법 - 'blank'/'xml'/'inimage'
        //originalOption : {//odf에서 제공해주는 기본 파라미터 적용 여부
          //SERVICE : true,//(기본값 true)
          //REQUEST : true, //(기본값 true)
          //WIDTH : true,//(기본값 true)
          //HEIGHT : true,//(기본값 true)
          //BBOX : true,//(기본값 true)
          //FORMAT : true,//(기본값 false)
          //TRANSPARENT : true,//(기본값 false)
          //STYLES : true,//(기본값 false)
          //CRS : false,//(기본값 false)
          //VERSION : false,//(기본값 false)
       // },
        authkey : '[발급받은 api key]',//발급받은 api key
    });
   apiLayer.setMap(map);
```
```javascript
   //애니메이션 클러스터 레이어 생성
   var pointLayer = odf.LayerFactory.produce('geoserver', {
    method : 'get',
    server : 'https://geoserver.gonp.duckdns.org/geoserver', // 분석결과 레이어가 발행된 주소
    layer : 'geonpass:L100000256', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
    crtfckey : '인증키 입력',
    service : 'animatedCluster',
    //distance : 50, default 50,
    //animationDuration :700  애니메이션 표출 시간 단위 ms
  });
```
```javascript
// api 레이어 생성(VWORLD WMS API)

   var apiLayer = odf.LayerFactory.produce('api', {
        server:{url:'http://api.vworld.kr/req/wms'} , // API 주소
        service: 'wms', // wms/wfs/xyz/wmts

        layers: 'lt_c_adsigg_info,lt_c_ademd_info', //하나 또는 쉼표(,)로 분리된 지도레이어 목록, 최대 4개
        styles: 'lt_c_adsigg,lt_c_ademd_3d', //LAYERS와 1대1 관계의 하나 또는 쉼표(,)로 분리된 스타일 목록
        // version : '1.3.0',//요청 서비스 버전
        crs : 'EPSG:5179',//응답결과 좌표계와 bbox 파라미터의 좌표계
        //transparent : 'true',//지도 배경의 투명도 여부
        //bgcolor:'0xFFFFFF',//배경색
        //exceptions:'text/xml',

        //originalOption : {//odf에서 제공해주는 기본 파라미터 적용 여부
          //SERVICE : true,//(기본값 true)
          //REQUEST : true, //(기본값 true)
          //WIDTH : true,//(기본값 true)
          //HEIGHT : true,//(기본값 true)
          //BBOX : true,//(기본값 true)

          //★BBOX★
          //odf에서 기본 제공하는 bbox 배열은 minx,miny,maxx,maxy 순인 반면에
          //vworld에서는 EPSG:4326/EPSG:5186/EPSG:5187일 경우 bbox 배열을 miny,minx, maxy,maxx 순으로 입력받음
          //해당 경우에는 BBOX 값을 '{{miny}},{{minx}},{{maxy}},{{maxx}}' 와같이 입력하면 x와 y의 순서가 바뀌어 적용됨.
          //이때 bbox는 지도 좌표계를 따르는데, bbox의 좌표계를 설정하기 위해서는 LAYER_PROJECTION값에 'EPSG:4326'과 같이 변환할 좌표계를 입력해야한다.

          //BBOX : '{{miny}},{{minx}},{{maxy}},{{maxx}}',
          //LAYER_PROJECTION : 'EPSG:4326',

          //FORMAT : true,//(기본값 false)
          //TRANSPARENT : true,//(기본값 false)
          //STYLES : true,//(기본값 false)
          //CRS : false,//(기본값 false)
          //VERSION : false,//(기본값 false)
        //},

        domain:'[API KEY를 발급받을때 입력했던 URL]',//API KEY를 발급받을때 입력했던 URL
        KEY : '[발급받은 api key]',//발급받은 api key

    });
   apiLayer.setMap(map);
```
```javascript
// api 레이어 생성(VWORLD WFS API)
   var apiLayer = odf.LayerFactory.produce('api', {
        server:{url:'http://api.vworld.kr/req/wfs'} , // API 주소
        service: 'wfs', // wms/wfs/xyz/wmts
        typename: "lt_c_adsigg_info", //레이어명
        srsname : 'EPSG:5179', //사용 좌표계
        //VERSION : '1.1.0', //버젼
        //EXCEPTIONS:'text/xml',//에러 받는 타입
        output: 'application/json',//응답결과 포맷
        //maxfeatures:50,//출력되는 피처의 최대 개수
        //originalOption : {//odf에서 제공해주는 기본 파라미터 적용 여부
        //  REQUEST : true, //(기본값 : true)
        //  BBOX : true, //(기본값 : true)
        //  SERVICE : true, //(기본값 : true)
        //},
        domain:'[API KEY를 발급받을때 입력했던 URL]' //API KEY를 발급받을때 입력했던 URL
        KEY : '[발급받은 api key]', //발급받은 api key
    });
   apiLayer.setMap(map);
```
```javascript
// api 레이어 생성(카카오 지적편집도)
   var apiLayer = odf.LayerFactory.produce('api', {
        server: 'https://map0.daumcdn.net/map_usedistrict/2009alo/L{{15-z}}/{{-y-1}}/{{x}}.png', // API 주소
        service: 'xyz', // wms/wfs/xyz/wmts
        projection : 'EPSG:5181', // 요청 좌표계
        extent : [-30000, -60000, 494288, 988576],
        tileGrid:{
          origin: [-30000, -60000],
          resolutions: [4096, 2048, 1024, 512, 256, 128, 64, 32, 16, 8, 4, 2, 1, 0.5, 0.25, 0.125],
          tileSize : 256,
        },
    });
   apiLayer.setMap(map);
```


  ```javascript
    // tms 레이어
    let tmsLayer = odf.LayerFactory.produce('api', {
      service : 'tms',
      projection : 'EPSG:5186',
      server: {
        url : '서버 경로/{{z}}/{{-y-1}}/{{x}}.png',
      //proxyURL: 'proxyUrl.jsp',
      //proxyParam: 'url',
      },
      tileGrid: {
        origin :[...],
        resolutions : [...],
      },
    });
    tmsLayer.setMap(map);
  ```
```javascript
// wmts api 레이어 생성(바로e맵)
   var apiLayer = odf.LayerFactory.produce('api', {
        server: 'http://mapapi.ngii.go.kr:8013/openapi/Gettile.do', // API 주소
        service: 'wmts', // wms/wfs/xyz/wmts
        projection : 'EPSG:5179', // 요청 좌표계
        layer : 'korean_map',
        style : 'korean',
        //version: '1.0.0',
        //format: 'image/jpg',
        tileGrid: {
          origin: [-200000.0, 4000000.0],
          resolutions: [2088.96, 1044.48, 522.24, 261.12, 130.56, 65.28, 32.64, 16.32, 8.16, 4.08, 2.04, 1.02, 0.51, 0.255, 0.1275, 0.06375,],
          matrixIds: ['L05', 'L06', 'L07', 'L08', 'L09', 'L10', 'L11', 'L12', 'L13', 'L14', 'L15', 'L16', 'L17', 'L18', 'L19', 'L20',],
      },
      //★바로e맵의경우 행망 서비스중 '영상'지도를 제외한 레이어는 COL과 ROW 반전 적용시키고 있음
      //originalOption : {//odf에서 제공해주는 기본 파라미터 적용 여부
      //  TILEROW_TILECOL_INVERTED_STATE : false (기본값 false)
      //}
    });
   apiLayer.setMap(map);
```
```javascript
// wmts api 레이어 생성(브이월드)
   var apiLayer = odf.LayerFactory.produce('api', {
        server: 'http://api.vworld.kr/req/wmts/1.0.0/{{key}}/{{layer}}/{{tileMatrix}}/{{tileRow}}/{{tileCol}}.png', // API 주소
        service: 'wmts', // wms/wfs/xyz/wmts
        projection : 'EPSG:3857', // 요청 좌표계
        layer : 'Base',
        //version: '1.0.0',
        //format: 'image/jpg',
        key : '[발급받은 키]',
        tileGrid: {
          origin: [-20037508.342789244, 20037508.342789244],
          resolutions: [2445.98490512564, 1222.99245256282, 611.49622628141, 305.748113140705, 152.8740565703525, 76.43702828517625, 38.21851414258813, 19.109257071294063, 9.554628535647032, 4.777314267823516, 2.388657133911758, 1.194328566955879, 0.5971642834779395, 0.29858214173896974,],
          matrixIds: [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
        },
        //originalOption : {//odf에서 제공해주는 기본 파라미터 적용 여부
        //  TILEROW_TILECOL_INVERTED_STATE : false (기본값 false)
        //}
    });
   apiLayer.setMap(map);
```
```javascript
// 이미지 레이어 생성
   var imageLayer = odf.LayerFactory.produce('geoImage', {
    url : '이미지 경로', //이미지 정보
    projection : 'EPSG:5186',// 좌표계 정보
    imageCenter : [이미지중심x좌표, 이미지 중심 y좌표],//이미지 중심점 좌표
    editMenu : ['translate'], // 우클릭시 이미지 위치 이동 및 수정 이벤트 생성
    imageRotate : 0 , //이미지 회전 값
   });
   imageLayer.setMap(map);

   // 이미지레이어 수정 활성화시 트리거
   odf.event.addListener(map, 'editstart', (layerObject) => {});
   // 이미지레이어 수정 종료시 트리거
   odf.event.addListener(map, 'editend', (layerObject) => {
    //수정된 좌표/회전각 정보를 저장
   });
```


  ```javascript
//vectortile 레이어 생성
var vectortileLayer = odf.LayerFactory.produce('api', {
    service: 'vectortile',
    server: {
        url: '/data/{z}/{x}/{y}.pbf'
    },
    maxResolution: 610.8116873548161,
    minResolution: 0.32,
    projection: 'EPSG:5179',
    tileGrid: {
        extent: [],
        tileSize: 256,
        minZoom: 8,
        maxZoom: 18,
    }
})
vectortileLayer.setMap(map);
//벡터타일 레이어에 스타일 설정
let styleFunction = odf.StyleFactory.produceFunction([
    {
      seperatorFunc  : (feature, resolution)=>{
         return feature.get("layer") == "레이어명1";
      },
      style : {...}
    },
    ...
    {
      seperatorFunc  : "default",
      style : {...}
    }
  ]);
  vectortileLayer.setStyle(styleFunction);
  ```

  ```javascript
  // webGL vector(wfs) 레이어 생성
  let webGLVectorLayer = odf.LayerFactory.produce('geoserver', {
      method: 'get',
      server: '서버 경로',
      layer: '레이어 typeName',
      crtfckey: '',
      service: 'wfs',
      webGLRender :true,
      //bbox:true,
      renderOptions: {
          style: {
              'stroke-color': 'red',
              'stroke-width': 3,
          }
      }
  });
  webGLVectorLayer.setMap(map);
  ```

  ```javascript
  // webGL vectortile 레이어 생성
var webGLvectortileLayer = odf.LayerFactory.produce('api', {
    service: 'vectortile',
    server: {
        url: '/data/{z}/{x}/{y}.pbf'
    },
    maxResolution: 610.8116873548161,
    minResolution: 0.32,
    webGLRender :true,
    projection: 'EPSG:5179',
    tileGrid: {
        extent: [],
        tileSize: 256,
        minZoom: 8,
        maxZoom: 18,
    },
    renderOptions: {
      style : {
          builder : {
              'fill-color': ['get', 'fillColor'],
              'stroke-color': ['get', 'strokeColor'],
              'stroke-width': ['get', 'strokeWidth'],
              'circle-radius': 4,
              'circle-fill-color': '#777',
          },
          attributes : {
              fillColor: {
                  size: 2,
                  callback: (feature) => {
                      const style = this.getStyle()(feature, 1)[0];
                      const color = asArray(style?.getFill()?.getColor() || '#eee');
                      return odf.ColorFactory.packColor(color);
                  },
              },
              strokeColor: {
                  size: 2,
                  callback: (feature) => {
                      const style = this.getStyle()(feature, 1)[0];
                      const color = asArray(style?.getStroke()?.getColor() || '#eee');
                      return odf.ColorFactory.packColor(color);
                  },
              },
              strokeWidth: {
                  size: 1,
                  callback: (feature) => {
                      const style = this.getStyle()(feature, 1)[0];
                      return style?.getStroke()?.getWidth() * 2 || 0;
                  },
              },
          }
      }
  }
}
})
webGLvectortileLayer.setMap(map);
  ```

  ```javascript
  // webGL tile(wms) 레이어 생성
  let webGLTileLayer = odf.LayerFactory.produce('geoserver', {
      method: 'get',
      server: '서버 경로',
      layer: '레이어 typeName',
      crtfckey: '',
      service: 'wms',
      webGLRender :true,
      //bbox:true,
  });
  webGLTileLayer.setMap(map);
  ```

  ```javascript
    // tileDebug 레이어
    let tileDebugLayer = odf.LayerFactory.produce('tileDebug', {
      projection: 'EPSG:5186',
      tileGrid: {
          origin: [...],
          resolutions: [...],
      }
    });
    tileDebugLayer.setMap(map);
  ```

  ```javascript
    // geotiff 레이어
    let geotiffLayer = odf.LayerFactory.produce('geotiff', {
      sources: [
        {url: 'data/geotiff/Wgeonedu12-L100002134.tif'}
      ]
    });
    geotiffLayer.setMap(map);
  ```

**Kind**: static method of [<code>LayerFactory</code>](#LayerFactory)
**Summary**: 레이어 생성 함수
```javascript
let sample = odf.LayerFactory.produce(type, {
  server: 'GeoServer URL 입력',  //wfs, wms, wcs, wmts
  layer: 'Store 명칭:Layer 명칭 입력',
  service: '서비스 종류',
  //matrixSet: 'GeoServer에 발행된 타일매트릭스 중 선택', // wmts 일 경우에 사용. 생략하면 지도 좌표계 이용
});
sample.setMap(map);
```
**Returns**: <code>Layer</code> - 레이어 객체

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| type | <code>type</code> | <code>empty</code> | 레이어 생성 방법 종류  - 'geoserver' : GeoServer에 발행되어 있는 데이터를 호출하여 레이어 생성  - 'geojson' : GeoJson 형태의 데이터를 이용하여 레이어 생성  - 'kml' : KML 형태의 데이터를 이용하여 레이어 생성  - 'csv' : CSV 형태의 데이터를 이용하여 레이어 생성  - 'empty' : 빈 벡터 레이어 생성  - 'api' : 빈 벡터 레이어 생성  - 'geoImage' : 이미지 레이어 생성  - 'geotiff' : geotiff 레이어 생성  - 'svg' :  SVG 이미지 레이어 |
| params | [<code>geoserverOption</code>](#geoserverOption) \| [<code>geojsonOption</code>](#geojsonOption) \| [<code>kmlOption</code>](#kmlOption) \| [<code>csvOption</code>](#csvOption) \| [<code>apiOption</code>](#apiOption) \| [<code>imageOption</code>](#imageOption) \| [<code>geotiffOption</code>](#geotiffOption) \| [<code>svgOption</code>](#svgOption) |  | 레이어 생성 옵션 |

<a name="LayerFactory.getWmtsLayerOpt"></a>

### LayerFactory.getWmtsLayerOpt(opt) ⇒ <code>Json</code>
wmts 옵션값 생성(기준, OpenLayers)

```javascript
let opt = {
 level : 13,
 epsg : 'epsg:5179'
}

let option = odf.LayerFactory.getWmtsLayerOpt(opt);
```

**Kind**: static method of [<code>LayerFactory</code>](#LayerFactory)
**Returns**: <code>Json</code> - 옵션 객체

| Param | Type | Description |
| --- | --- | --- |
| opt | <code>Json</code> | 옵션을 생성하기 위한 값 |
| opt.level | <code>option</code> | WMTS Zoom Level Step |
| opt.epsg | <code>option</code> | WMTS 옵션 생성에 기준이 될 좌표 |

<a name="LayerFactory.intersect"></a>

### LayerFactory.intersect(al, tla) ⇒ <code>Feature</code>
레이어 교체값 추출

```javascript
let list = map.getODFLayers();
let option = odf.LayerFactory.intersect(Target, list);
```

**Kind**: static method of [<code>LayerFactory</code>](#LayerFactory)
**Returns**: <code>Feature</code> - 교차된 대상

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| al | <code>Layer</code> \| <code>Extent</code> |  | 조회하고자 하는 영역(Layer or Extent) |
| tla | <code>Array</code> | <code></code> | 비교하고자 하는 LayerList |

<a name="service"></a>

## service : <code>String</code>
레이어 호출 종류

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| wfs | <code>String</code> | wfs 레이어 호출 |
| wms | <code>String</code> | wms 레이어 호출 |
| group | <code>String</code> | group 레이어 호출 (레이어그룹 호출 시 그룹명으로 레이어 생성 시 속해있는 레이어들 호출) |
| cluster | <code>String</code> | cluster 분석 레이어 호출 |
| hotspot | <code>String</code> | hotspot 분석 레이어 호출 |
| heatmap | <code>String</code> | heatmap 분석 레이어 호출 |
| aggregate | <code>String</code> | aggregate 분석 레이어 호출 |

<a name="geoserverOption"></a>

## geoserverOption : <code>Object</code>
레이어 생성을 위한 옵션

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| server | <code>String</code> | 레이어 호출 서버 주소 |
| server | <code>Object</code> | 레이어 호출 서버 옵션 |
| server.url | <code>String</code> | 레이어 호출 서버 주소 |
| server.version | <code>String</code> | 레이어 호출 서버 버전 |
| server.proxyURL | <code>String</code> | 프록시 url |
| server.proxyParam | <code>String</code> | 프록시 매개변수 명 |
| layer | <code>String</code> | 레이어 저장소명과 레이어 명칭 (ex) nsid_dev:L100000254 |
| service | [<code>service</code>](#service) | 레이어 호출 종류 |
| limit | <code>Number</code> | 레이어 호출 제한 수 |
| [crtfckey] | <code>String</code> | api 사용시 인증 키 |
| [bbox] | <code>Boolean</code> | bbox 사용 여부(wfs 레이어일 때만 의미 있음) - true :  feature 정보를 사용자가 보는 영역만큼 매번 요청하여 가져옴 - false : feature정보를 한번에 다 가져옴 (기본값) |
| [method] | <code>&#x27;get&#x27;</code> \| <code>&#x27;post&#x27;</code> | 정보를 조회할때 get 요청을 할지, post 요청을할지 |
| [tiled] | <code>Boolean</code> | (wms 레이어일 때만 의미 있음) 이미지를 타일링하여 요청할지 여부 |
| className | <code>String</code> | 레이어 element의 클래스 명 |
| webGLRender | <code>Boolean</code> | webGL을 이용하여 렌더링(기본값 false) |
| renderOptions | <code>Object</code> | 렌더링 옵션(webGLRender가 true일때 사용됨) |
| renderOptions.style | [<code>odfFlatStyle</code>](#odfFlatStyle) \| [<code>odfStyleRule</code>](#odfStyleRule) \| [<code>odfWebGLVectorTileStyle</code>](#odfWebGLVectorTileStyle) | flat style 형식 |
| attributions | <code>Array.&lt;String&gt;</code> | 귀속 정의 |

<a name="odfWebGLCustomAttributeDefine"></a>

## odfWebGLCustomAttributeDefine ⇒ <code>?</code>
**Kind**: global typedef
**Returns**: <code>?</code> - 사용자 정의 속성 값

| Param | Type | Description |
| --- | --- | --- |
| 대상 | <code>Feature</code> | feature |

<a name="odfWebGLAttributeObject"></a>

## odfWebGLAttributeObject : <code>Object</code>
사용자 정의 속성 정보

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| size | <code>Number</code> |  |
| callback | [<code>odfWebGLCustomAttributeDefine</code>](#odfWebGLCustomAttributeDefine) | 사용자 정의 속성 값을 반환하는 함수 |

<a name="odfWebGLVectorTileStyle"></a>

## odfWebGLVectorTileStyle : <code>Object</code>
webGL vector tile 레이어 스타일 렌더 옵션

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| builder | [<code>odfFlatStyle</code>](#odfFlatStyle) | 사용자 정의 속성 값을 사용할 수 있는 flat style |
| attributes | [<code>Array.&lt;odfWebGLAttributeObject&gt;</code>](#odfWebGLAttributeObject) | 사용자 정의 속성 정보  ```javascript {             builder : {                 'fill-color': ['get', 'fillColor'], //사용자 정의 속성인 'fillColor' 값으로 채우기 색 설정                 'stroke-color': ['get', 'strokeColor'], //사용자 정의 속성인 'strokeColor' 값으로 윤곽선 색 설정                 'stroke-width': ['get', 'strokeWidth'],//사용자 정의 속성인 'strokeWidth' 값으로 윤곽선 두께 설정                 'circle-radius': 4, //원의 크기                 'circle-fill-color': '#777',//원 채우기 색상             },             attributes : {                 fillColor: {                     size: 2,                     callback: (feature) => {                         const style = this.getStyle()(feature, 1)[0];                         const color = asArray(style?.getFill()?.getColor() || '#eee');                         //색상을 두개의 부동소수점 배열로 묶는다(압축)                         return odf.ColorFactory.packColor(color);                     },                 },                 strokeColor: {                     size: 2,                     callback: (feature) => {                         const style = this.getStyle()(feature, 1)[0];                         const color = asArray(style?.getStroke()?.getColor() || '#eee');                         return odf.ColorFactory.packColor(color);                     },                 },                 strokeWidth: {                     size: 1,                     callback: (feature) => {                         const style = this.getStyle()(feature, 1)[0];                         return style?.getStroke()?.getWidth() * 2 || 0;                     },                 },             }         } ``` |

<a name="imageOption"></a>

## imageOption : <code>Object</code>
이미지 레이어 생성을 위한 옵션

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| url | <code>String</code> | 이미지 경로 |
| projection | <code>String</code> | 레이어 좌표계 정보 (ex)'EPSG:5186' |
| imageCenter | <code>Array.&lt;Number&gt;</code> | 레이어 중심점 |
| editMenu | <code>Array.&lt;String&gt;</code> | 이미지 위치/회전 편집 기능 사용 정의 - ['translate'] : 편집 기능 사용 - [] | undefined : 편집기능 사용 안함 |
| imageRotate | <code>Number</code> | 이미지 회전 각도(도) |
| className | <code>String</code> | 레이어 element의 클래스 명 |
| attributions | <code>Array.&lt;String&gt;</code> | 귀속 정의 |

<a name="geotiffSourceOption"></a>

## geotiffSourceOption : <code>Object</code>
geotiff 레이어 생성 옵션

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| url | <code>String</code> | geoTiff 파일 경로. tileGrid가 정의되어있다면 '~/~//{{z}}/{{y}}/{{x}}.tif' 이와 같이 사용 가능 |
| overviews | <code>Array.&lt;String&gt;</code> | 원본 GeoTIFF 파일의 저해상도 버전 파일. url이 정의됬을때 사용됨 |
| proxyURL | <code>String</code> | 프록시 url |
| proxyParam | <code>String</code> | 프록시 매개변수 명 |
| blob | <code>Blob</code> | goeTiff를 blob 타입으로 받아 생성 |
| bands | <code>Array.&lt;Number&gt;</code> | 읽을 밴드 번호. 제공되지 않으면 모든 밴드를 읽습니다. 예를 들어 GeoTIFF에 파란색(1), 녹색(2), 빨간색(3) 및 근적외선(4) 대역이 있고 근적외선 대역만 필요한 경우 'band : [4]'로 설정 |
| min | <code>Number</code> | 최소 소스 데이터 값. 렌더링된 값은 구성된 최소 및 최대 값을 기준으로 0에서 1까지 조정 (기본값 0)     이 값을 설정하지 않으면 래스터 통계를 사용합니다. 이 동작을 비활성화하려면 normalize 옵션을 false로 설정하세요 |
| max | <code>Number</code> | 최대 소스 데이터 값. 렌더링된 값은 구성된 최소 및 최대 값을 기준으로 0에서 1까지 조정. 이 값을 설정하지 않으면 래스터 통계를 사용합니다. 이 동작을 비활성화하려면 normalize 옵션을 false로 설정하세요 |
| nodata | <code>Number</code> | 삭제할 값. geoTiff의 메타데이터의 nodata 값을 덮어씌움 |

<a name="geotiffOption"></a>

## geotiffOption : <code>Object</code>
geotiff 레이어 생성 옵션

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| tileGrid | <code>Object</code> | 타일링 정보 |
| tileGrid.extent | <code>Array.&lt;Number&gt;</code> | 타일 ​​그리드의 범위. 이 범위를 벗어나는 타일은 소스에서 요청되지 않습니다. |
| resolutions | <code>Array.&lt;Number&gt;</code> | 제공 레졸루션 목록 정의 |
| tileSizes | <code>Array.&lt;Array.&lt;Number&gt;&gt;</code> | 타일 ​​크기 |
| normalize | <code>Boolean</code> | 기본적으로 소스 데이터는 래스터 통계 또는 각 소스의 min 또는 max 속성을 기반으로 한 배율 인수를 사용하여 0과 1 사이의 값으로 정규화됩니다.(default true)  대신 스타일 표현식의 원시 값으로 작업하려면, 이 값을 false로 설정하세요.  이 옵션을 false로 설정하면 소스의 모든 속성 min과 max속성이 무시됩니다. |
| wrapX | <code>Boolean</code> | 타일 ​그리드 범위를 넘어 타일을 렌더링할지 여부. (defualt false) |
| sources | [<code>Array.&lt;geotiffSourceOption&gt;</code>](#geotiffSourceOption) | GeoTIFF 소스에 대한 정보 목록, 배율을 적용한 후 해상도 세트가 동일하다면 여러 소스를 결합할 수 있음 |
| opaque | <code>Boolean</code> | 레이어가 불투명한지여부 |
| transition | <code>Number</code> | 불투명도. 불투명도 전환을 비활성화하려면 0 전달 |
| renderOptions | <code>Object</code> | 렌더링 옵션 |
| renderOptions.style | [<code>odfFlatStyle</code>](#odfFlatStyle) \| [<code>odfStyleRule</code>](#odfStyleRule) \| [<code>odfWebGLVectorTileStyle</code>](#odfWebGLVectorTileStyle) | flat style 형식 |
| attributions | <code>Array.&lt;String&gt;</code> | 귀속 정의 |

<a name="svgOption"></a>

## svgOption : <code>Object</code>
svg 레이어 생성 옵션

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| svgContainer | <code>HTMLElement</code> | svg html element |
| extent | <code>Array.&lt;Number&gt;</code> |  |
| attributions | <code>Array.&lt;String&gt;</code> | 귀속 정의 |

<a name="odfFlatStyle"></a>

## odfFlatStyle : <code>Object</code>
odf flat 스타일

**Kind**: global typedef
**Properties**

| Name | Type | Default | Description |
| --- | --- | --- | --- |
| [valiables] | <code>Object</code> |  | 사용자 정의 변수(스타일 표현식에서 ['var','[사용자정의변수명]']으로 사용 가능) |
| [fill-color] | <code>String</code> \| <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 채우기 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5] |
| [stroke-color] | <code>String</code> \| <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 윤곽선 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5] |
| [stroke-width] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 너비 |
| [stroke-line-cap] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 선의 끝 모양  - `butt`, `round`(기본값), `square`. |
| [stroke-line-join] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 선이 꺾이는 부분의 모양  - `bevel`, `round`(기본값), `miter`. |
| [stroke-line-dash] | <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 점선 모양 |
| [stroke-line-dash-offset] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | Line dash offset. 기본값 0 |
| [stroke-miter-limit] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) | <code>10</code> | Miter limit. |
| [text-value] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 텍스트 값 |
| [text-font] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 폰트 크기(필수) 및 글씨체(필수), 두께(옵션) (ex) 'bold 10px sans-serif', 기본값 :'10px sans-serif' |
| [text-max-angle] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 텍스트가 꺾이는 정도를 제한(placement 속성이 'line'일때 적용) (단위:라디안) (ex)Math.PI*270/180 |
| [text-offset-x] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 텍스트의 수평 이동(양수일 때 오른쪽으로 이동) |
| [text-offset-y] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 텍스트의 수직 이동(양수일 때 아래로 이동) |
| [text-overflow] | <code>Boolean</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부(placement 속성이 'line'일때 적용). 기본값 false |
| [text-placement] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 텍스트를 타나낼 위치를 점 기준으로 할지, 피쳐의 모양에 따라 나타나게 할지 여부.'line' 또는 'point'(default) |
| [text-repeat] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 간격을 픽셀 단위로 반복합니다. 설정하면 이 간격으로 텍스트가 반복됩니다.(placement 속성이 'line'일때 적용되며 'text-align'속성보다 우선 적용됩니다.) |
| [text-scale] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 텍스트 크기를 정해진 값의 n배로 적용 |
| [text-rotate-with-view] | <code>Boolean</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 지도가 회전할때 텍스트도 적절하게 회전할지 여부(기본값 false) |
| [text-rotation] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 텍스트 회전 각도(단위:라디안) (ex)Math.PI*270/180 |
| [text-align] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 텍스트 수직정렬 - 'left', 'right', 'center', 'end', 'start' |
| [text-justify] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 텍스트 상자 내의 텍스트 맞춤 - 'left', 'center', 'right' |
| [text-baseline] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 텍스트 수직정렬 -'bottom', 'top', 'middle'(기본값), 'alphabetic','hanging', 'ideographic' |
| [text-padding] | <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | text와 background영역 사이의 여백(placement 속성이 'point'일때 적용) (ex)[10,5,5,5] |
| [text-fill-color] | <code>String</code> \| <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 텍스트 색상 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5] |
| [text-background-fill-color] | <code>String</code> \| <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 텍스트 배경 채우기 색상(ex) 'red' | '#55dd20' | [255,0,0,0.5] |
| [text-stroke-color] | <code>String</code> \| <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 택스트  색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5] |
| [text-stroke-line-cap] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 택스트 선의 끝 모양 - `butt`, `round`(기본값), `square`. |
| [text-stroke-line-join] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 택스트 선이 꺾이는 부분의 모양  - `bevel`, `round`(기본값), `miter`. |
| [text-stroke-line-dash] | <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 택스트 점선 모양 |
| [text-stroke-line-dash-offset] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 택스트 선 Line dash offset. 기본값 0 |
| [text-stroke-miter-limit] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 택스트 선 Miter limit. |
| [text-stroke-width] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 택스트 선 두께 |
| [text-background-stroke-color] | <code>String</code> \| <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 택스트 배경 테두리 선 색상 |
| [text-background-stroke-line-cap] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 택스트 배경 테두리 선의 끝 모양 - `butt`, `round`(기본값), `square`. |
| [text-background-stroke-line-join] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 택스트 배경 테두리 선이 꺾이는 부분의 모양  - `bevel`, `round`(기본값), `miter`. |
| [text-background-stroke-line-dash] | <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 택스트 배경 테두리 점선 모양 |
| [text-background-stroke-line-dash-offset] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 택스트 배경 테두리 선 Line dash offset. 기본값 0 |
| [text-background-stroke-miter-limit] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 택스트 배경 테두리 선 Miter limit. |
| [text-background-stroke-width] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 택스트 배경 테두리 선 두께 |
| [icon-src] | <code>String</code> |  | 이미지 소스 URI |
| [icon-anchor] | <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | Anchor. Default value is the icon center.(기본값 [0.5, 0.5]) |
| [icon-anchor-origin] | <code>&#x27;bottom-left&#x27;</code> \| <code>&#x27;bottom-right&#x27;</code> \| <code>&#x27;top-left&#x27;</code> \| <code>&#x27;top-right&#x27;</code> |  | 기준점으로부터의 아이콘 위치 (기본값 'top-left') |
| [icon-anchor-x-units] | <code>&#x27;fraction&#x27;</code> \| <code>&#x27;pixels&#x27;</code> |  | 아이콘 x축 위치 조정 단위.(기본값 'fraction') |
| [icon-anchor-y-units] | <code>&#x27;fraction&#x27;</code> \| <code>&#x27;pixels&#x27;</code> |  | 아이콘 y축 위치 조정 단위.(기본값 'fraction') |
| [icon-color] | <code>String</code> \| <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 이미지 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5] |
| [icon-cross-origin] | <code>null</code> \| <code>String</code> |  | CORS 관련 셋팅. (기본값 'anonymous') |
| [icon-offset] | <code>Array.&lt;Number&gt;</code> |  | x축 y축 좌표위치 이동 (기본값 [0, 0]) |
| [icon-displacement] | <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 위치이동. 양수 값은 모양을 오른쪽 및 위쪽으로 이동 (기본값 [0,0]) |
| [icon-offset-origin] | <code>&#x27;bottom-left&#x27;</code> \| <code>&#x27;bottom-right&#x27;</code> \| <code>&#x27;top-left&#x27;</code> \| <code>&#x27;top-right&#x27;</code> |  | 오프셋 원점(기본값 'top-left') |
| [icon-opacity] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 투명도(기본값 1) |
| [icon-scale] | <code>Number</code> \| <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 크기를 정해진 값의 n배로 셋팅(기본값 1) |
| [icon-width] | <code>Number</code> |  | 아이콘의 너비(픽셀). `scale`과 함께 이용할 수 없음 |
| [icon-height] | <code>Number</code> |  | 아이콘의 높이(픽셀). `scale`과 함께 이용할 수 없음 |
| [icon-rotation] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 회전(기본값 0) (ex) 30*Math.PI/180 |
| [icon-rotate-with-view] | <code>Boolean</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 지도가 회전할 때 이미지도 적절하게 회전할지 여부 (기본값 false) |
| [icon-size] | <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 자연수 배열 (단위 : 픽셀) |
| [icon-declutter-mode] | <code>&quot;declutter&quot;</code> \| <code>&quot;obstacle&quot;</code> \| <code>&quot;none&quot;</code> \| <code>undefined</code> |  | 디클러터 모드. |
| [shape-points] | <code>Number</code> |  | 정다각형의 점 수. 다각형의 경우 점의 개수는 변의 개수 |
| [shape-fill-color] | <code>String</code> \| <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 정다각형의 채우기 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5] |
| [shape-stroke-color] | <code>String</code> \| <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 정다각형 윤곽선 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5] |
| [shape-stroke-width] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 정다각형 윤곽선 두께 |
| [shape-stroke-line-cap] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 정다각형 윤곽선의 끝 모양 - `butt`, `round`(기본값), `square`. |
| [shape-stroke-line-join] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 정다각형 윤곽선이 꺾이는 부분의 모양  - `bevel`, `round`(기본값), `miter`. |
| [shape-stroke-line-dash] | <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 정다각형 윤곽선 점선 모양 |
| [shape-stroke-line-dash-offset] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 정다각형 윤곽선 Line dash offset. 기본값 0 |
| [shape-stroke-miter-limit] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 정다각형 윤곽선 Miter limit. |
| [shape-radius] | <code>Number</code> |  | 정다각형의 반경 |
| [shape-radius2] | <code>Number</code> |  | 정다각형의 내부 반경 |
| [shape-angle] | <code>Number</code> |  | 모양의 각도(라디안). 값이 0이면 모양의 점 중 하나가 위를 향하게 됩니다. 기본값은 0 |
| [shape-displacement] | <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 모양의 변위(픽셀). 양수 값은 모양을 오른쪽 및 위쪽으로 이동 (기본값 [0,0]) |
| [shape-rotation] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 라디안 단위의 회전입니다(시계 방향으로 양의 회전)(기본값 0) |
| [shape-rotate-with-view] | <code>Boolean</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 뷰와 함께 모양을 회전할지 여부(기본값 false) |
| [shape-scale] | <code>Number</code> \| <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 규모. (기본값 1) 2차원 스케일링이 필요하지 않은 경우 반경, 반경1 및 반경2에 대한 적절한 설정을 사용하면 더 나은 결과를 얻을 수 있음 |
| [shape-declutter-mode] | <code>&quot;declutter&quot;</code> \| <code>&quot;obstacle&quot;</code> \| <code>&quot;none&quot;</code> \| <code>undefined</code> |  | 디클러터 모드. |
| [circle-radius] | <code>Number</code> |  | 원의 반지름 |
| [circle-fill-color] | <code>String</code> \| <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 원의 채우기 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5] |
| [circle-stroke-color] | <code>String</code> \| <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 원의 윤곽선 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5] |
| [circle-stroke-width] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 원의 테두리 선 두께 |
| [circle-stroke-line-cap] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 원의 테두리 선의 끝 모양 - `butt`, `round`(기본값), `square`. |
| [circle-stroke-line-join] | <code>String</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 원의 테두리 선이 꺾이는 부분의 모양  - `bevel`, `round`(기본값), `miter`. |
| [circle-stroke-line-dash] | <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 원의 테두리 선 점선 모양 |
| [circle-stroke-line-dash-offset] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 원의 테두리 선 Line dash offset. 기본값 0 |
| [circle-stroke-miter-limit] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 원의 테두리 선 Miter limit. |
| [circle-displacement] | <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 위치이동. 양수 값은 모양을 오른쪽 및 위쪽으로 이동 (기본값 [0,0]) |
| [circle-scale] | <code>Number</code> \| <code>Array.&lt;Number&gt;</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 원의 크기(radius로 정해진 크기의 n배). 배열일 때, [가로  축척, 세로 축척] 형태임 |
| [circle-rotation] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 원의 회전각도 (circle-scale이 배열형태로 되어 타원형태가 되었을때 의미있음) |
| [circle-rotate-with-view] | <code>Boolean</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 뷰와 함께 모양을 회전할지 여부(기본값 false) |
| [circle-declutter-mode] | <code>&quot;declutter&quot;</code> \| <code>&quot;obstacle&quot;</code> \| <code>&quot;none&quot;</code> \| <code>undefined</code> |  | 디클러터 모드. |
| [z-index] | <code>Number</code> \| [<code>odfStyleExpression</code>](#odfStyleExpression) |  | 스타일 z-index |

<a name="odfStyleExpression"></a>

## odfStyleExpression : <code>Array.&lt;(Sting\|Number\|odfStyleExpression)&gt;</code>
odf 표현식

* 읽기 연산자
  * `['band', bandIndex, xOffset, yOffset]` [타일 ​​레이어]
  * `['get', 'attributeName', typeHint]` 레이어에서 프로퍼티 명이 'attributeName'인 값 조회. typeHint는 옵셔널한 값
    'string', 'color', 'number', 'boolean', 'number[]'
  * `['geometry-type']` 도형의 지오메트리 타입
    'LineString', 'Point', 'Polygon', `MultiLineString`, `MultiPoint`, `MultiPolygon`
  * `['resolution']` 현재 해상도 값
  * `['time']` 현재 해상도 값
  * `['var', 'varName']` 스타일 변수에서 값을 가져옵니다. 해당 변수가 정의되지 않은 경우 오류가 발생
  * `['zoom']` 현재 확대/축소 수준
  * `['id']` feature의 id

* 수학 연산자
  * `['*', value1, value2, ...]` `value1` * `value2` * ...
  * `['/', value1, value2]` `value1` / `value2`
  * `['+', value1, value2, ...]` `value1` + `value2` + ...
  * `['-', value1, value2]` `value1` - `value2`
  * `['clamp', value, low, high]` `low`와 `high` 사이에 값을 고정. `value`가 `low`보다 작으면 `low` 반환, `value`가 `high`보다 크면 `high` 반환
  * `['%', value1, value2]` `value1 % value2`
  * `['^', value1, value2]` `value1` ^ `value2`
  * `['abs', value1]` `value1`의 절대값
  * `['floor', value1]` `value1`과 작거나 같은 가장 가까운 정수 반환(내림)
  * `['round', value1]` `value1`과 가장 가까운 정수 반환(반올림)
  * `['ceil', value1]` `value1`과 크나 같은 가장 가까운 정수 반환(올림)
  * `['sin', value1]` `value1`의 sin값 반환
  * `['cos', value1]` `value1`의 cosine값 반환
  * `['atan', value1, value2]` atan2(value1, value2) 반환. `value2`가 없을 경우 atan(value1) 반환
  * `['sqrt', value1]` `value1`의 제곱근 반환

* 변환 연산자
  * `['case', condition1, output1, ...conditionN, outputN, fallback]` `condition1` 조건이 참이면 `output1` 반환, `conditionN`이 참이면 `outputN` 반환. 일치하는 조건이 없으면 `fallback` 반환
  * `['match', input, match1, output1, ...matchN, outputN, fallback]` `input` 값이 `match1`과 같으면 `output1` 반환,`matchN`과 같으면 `outputN` 반환. 일치하는 값이 없으면 `fallback` 반환
  * `['interpolate', interpolation, input, stop1, output1, ...stopN, outputN]` 값을 보간하여 반환. `input` 값이 `stop1`과 같으면 `output1` 반환,`stopN`과 같으면 `outputN` 반환
- `input`값이 `stop1`과 `stop2` 사이의 값이라면 `output1`과  `output2`사이의 값을 보간하여 반환
- interpolation은 보간 방법 정의. ['linear'] :  선형 보간법 ,['exponential', base] : 지수보간법(base는 stop A에서 stop B까지의 증가율)

* 논리 연산자
  * `['<', value1, value2]` `value1` < `value2`
  * `['<=', value1, value2]` `value1` <= `value2`
  * `['>', value1, value2]` `value1` > `value2`
  * `['>=', value1, value2]` `value1` >= `value2`
  * `['==', value1, value2]` `value1` == `value2`
  * `['!=', value1, value2]` `value1` != `value2`
  * `['!', value1]` !`value1`
  * `['all', value1, value2, ...]` `value1`과 `value2` ...가 모두 true일때 true 반환, 그 외 모두 false
  * `['any', value1, value2, ...]` `value1` 또는 `value2` ... 중 하나라도 true면 true 반환. 모두 false일때 false 반환
  * `['between', value1, value2, value3]`  `value1`이 `value2`와 `value3` 사이에 있으면 true, 아니면 false 반환. `value1`이 `value2` 또는 `value3`과 동일해도 true
  * `['in', needle, haystack]` `needle`이 `haystack`안에서 발견되면  true, 아니면 false.
- `haystack`은 숫자 또는 문자열로 구성된 배열. 현재는 상수만 지원됨.

* 변환 연산자
  * `['concat', value1, ...valueN]` 문자열을 합쳐서 반환
  * `['array', value1, ...valueN]` 숫자 배열 생성. 현재는 2~4개 요소만 생성 가능
  * `['color', red, green, blue, alpha]` 색상 값 생성. alpha 값은 0~1 사이의 값
  * `['palette', index, colors]` colors(색상배열)에서 index 위치의 색상 추출
- colors는 문자열의 배열이며, 색상은 hex 형태('#aaa') 또는 rgb(a) 형태(`'rgb(134, 161, 54)'` 또는 `'rgba(134, 161, 54, 1)'`)가 가능

**Kind**: global typedef
<a name="odfStyleRule"></a>

## odfStyleRule : [<code>Array.&lt;odfStyleRuleObject&gt;</code>](#odfStyleRuleObject)
odf 스타일 규칙

```javascript
  const rules = [
  {
    filter: ['>', ['get', 'population'], 1_000_000],
    style: {
      'circle-radius': 10,
      'circle-fill-color': 'red',
    }
  },
  {
    else: true,
    style: {
      'circle-radius': 5,
      'circle-fill-color': 'blue',
    },
  },
];
```

**Kind**: global typedef
<a name="odfStyleRuleObject"></a>

## odfStyleRuleObject : <code>Object</code>
odf 스타일 규칙

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| filter | [<code>odfStyleExpression</code>](#odfStyleExpression) | 스타일 적용 조건. |
| else | <code>Boolean</code> | true이면 해당 스타일 적용 |
| style | [<code>odfFlatStyle</code>](#odfFlatStyle) | 적용 스타일(flat 스타일) |

<a name="geojsonOption"></a>

## geojsonOption : <code>Object</code>
geojson 레이어 생성을 위한 옵션

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| data | <code>Object</code> | geojson 형식의 object 데이터 |
| dataProjectionCode | <code>String</code> | 원본 좌표계 코드 ex)'EPSG:5179' |
| featureProjectionCode | <code>String</code> | 변환활 좌표계 코드 ex)'EPSG:5179' |
| className | <code>String</code> | 레이어 element의 클래스 명 |
| webGLRender | <code>Boolean</code> | webGL을 이용하여 렌더링(기본값 false) |
| renderOptions | <code>Object</code> | 렌더링 옵션(webGLRender가 true일때 사용됨) |
| renderOptions.style | [<code>odfFlatStyle</code>](#odfFlatStyle) \| [<code>odfStyleRule</code>](#odfStyleRule) \| [<code>odfWebGLVectorTileStyle</code>](#odfWebGLVectorTileStyle) | flat style 형식 |
| attributions | <code>Array.&lt;String&gt;</code> | 귀속 정의 |

<a name="kmlOption"></a>

## kmlOption : <code>Object</code>
KML 레이어 생성을 위한 옵션

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| data | <code>String</code> | KML 형식의 텍스트 데이터 |
| dataProjectionCode | <code>String</code> | 원본 좌표계 코드 ex)'EPSG:5179' |
| featureProjectionCode | <code>String</code> | 변환활 좌표계 코드 ex)'EPSG:5179' |
| className | <code>String</code> | 레이어 element의 클래스 명 |
| webGLRender | <code>Boolean</code> | webGL을 이용하여 렌더링(기본값 false) |
| renderOptions | <code>Object</code> | 렌더링 옵션(webGLRender가 true일때 사용됨) |
| renderOptions.style | [<code>odfFlatStyle</code>](#odfFlatStyle) \| [<code>odfStyleRule</code>](#odfStyleRule) \| [<code>odfWebGLVectorTileStyle</code>](#odfWebGLVectorTileStyle) | flat style 형식 |
| attributions | <code>Array.&lt;String&gt;</code> | 귀속 정의 |

<a name="csvOption"></a>

## csvOption : <code>Object</code>
CSV 레이어 생성을 위한 옵션

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| data | <code>String</code> | CSV 형식의 텍스트 데이터 |
| dataProjectionCode | <code>String</code> | 원본 좌표계 코드 ex)'EPSG:5179' |
| featureProjectionCode | <code>String</code> | 변환활 좌표계 코드 ex)'EPSG:5179' |
| geometryColumnName | <code>String</code> | csv 파일의 geometry column 이름  ex)'the_geom' |
| delimiter | <code>String</code> | csv 파일의 컴럼을 구분할 구분자 (기본값 콤마(,)) |
| className | <code>String</code> | 레이어 element의 클래스 명 |
| webGLRender | <code>Boolean</code> | webGL을 이용하여 렌더링(기본값 false) |
| renderOptions | <code>Object</code> | 렌더링 옵션(webGLRender가 true일때 사용됨) |
| renderOptions.style | [<code>odfFlatStyle</code>](#odfFlatStyle) \| [<code>odfStyleRule</code>](#odfStyleRule) \| [<code>odfWebGLVectorTileStyle</code>](#odfWebGLVectorTileStyle) | flat style 형식 |
| attributions | <code>Array.&lt;String&gt;</code> | 귀속 정의 |

<a name="apiOption"></a>

## apiOption : <code>Object</code>
api 레이어 생성을 위한 옵션

**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| server | <code>String</code> | (필수값)API 주소 |
| server | <code>Object</code> \| <code>String</code> | 레이어 호출 서버 옵션, API 서버 주소 |
| server.url | <code>String</code> | (필수값) API 주소 |
| server.proxyURL | <code>String</code> | 프록시 url |
| server.proxyParam | <code>String</code> | 프록시 매개변수 명 |
| service | <code>String</code> | (필수값) 적용 서비스 'wms' 또는 'wfs' |
| bbox | <code>Boolean</code> | wfs 레이어의 경우 이용됨. 기본값 false  - true : 사용자가 보고있는 지도 영역에 걸치는 도형만 조회  - false : 최초 1회 모든 도형을 조회 |
| tiled | <code>Boolean</code> | wms 레이어 타일링 옵션. 기본값 false  - true : 사용자가 보고있는 지도 영역을 이미지 여러 장으로 조회  - false : 사용자가 보고있는 지도 영역을 이미지 1장으로 조회 |
| tileGrid | <code>Object</code> | wmts tileGrid(service값이 wmts일 경우 필수값) |
| tileGrid.origin | <code>Array.&lt;Number&gt;</code> | wmts origin |
| tileGrid.resolutions | <code>Array.&lt;Number&gt;</code> | wmts resolutions |
| tileGrid.matrixIds | <code>Array.&lt;String&gt;</code> \| <code>Array.&lt;Number&gt;</code> | wmts matrixIds |
| originalOption | <code>Object</code> | odf 기본 적용 파라미터 |
| originalOption.REQUEST | <code>Boolean</code> | (wms/wfs)odf 기본 적용 REQUEST 파라미터 사용 여부 (기본값 true) |
| originalOption.SERVICE | <code>Boolean</code> | (wms/wfs)odf 기본 적용 SERVICE 파라미터 사용 여부 (기본값 true) |
| originalOption.BBOX | <code>Boolean</code> \| <code>String</code> | (wms/wfs)odf 기본 적용 BBOX 파라미터 사용 여부 (기본값 true) odf에서 기본 제공하는 bbox 배열은 minx,miny,maxx,maxy 순, 하지만 api에 따라 x와 y가 반전되어 bbox 배열이 miny,minx, maxy,maxx 순인 경우가 있다. 해당 경우에는 BBOX 값을 '{{miny}},{{minx}},{{maxy}},{{maxx}}' 와같이 입력하면 x와 y의 순서가 바뀌어 적용됨. bbox는 기본적으로 지도 좌표계의 값을 따라가는데, 지도 좌표계와 api 레이어의 좌표계가 상이한 경우, 좌표변환을 위해서 LAYER_PROJECTION 값 또한 정의해야함 |
| originalOption.LAYER_PROJECTION | <code>String</code> | 지도 좌표계와 api 레이어의 좌표계가 상이할때 API의 레이어 좌표계 정의, 지도좌표계와 API 레이어의 좌표계가 같은 경우 정의 안해도 됨  - (ex) 'EPSG:5186'  (입력포멧 'EPSG:[숫자]') |
| originalOption.TILEROW_TILECOL_INVERTED_STATE | <code>Boolean</code> | WMTS 레이어일 경우, tileRow 값 tileCol 값 반전 여부 (기본값 false) |
| originalOption.WIDTH | <code>Boolean</code> | (wms)odf 기본 적용 WIDTH 파라미터 사용 여부 (기본값 true) |
| originalOption.HEIGHT | <code>Boolean</code> | (wms)odf 기본 적용 HEIGHT 파라미터 사용 여부 (기본값 true) |
| originalOption.VERSION | <code>Boolean</code> | (wms)odf 기본 적용 VERSION 파라미터 사용 여부 (기본값 false) |
| originalOption.TRANSPARENT | <code>Boolean</code> | (wms)odf 기본 적용 TRANSPARENT 파라미터 사용 여부 (기본값 false) |
| originalOption.STYLES | <code>Boolean</code> | (wms)odf 기본 적용 STYLES 파라미터 사용 여부 (기본값 false) |
| originalOption.CRS | <code>Boolean</code> | (wms)odf 기본 적용 CRS 파라미터 사용 여부 (기본값 false) |
| className | <code>String</code> | 레이어 element의 클래스 명 |
| webGLRender | <code>Boolean</code> | webGL을 이용하여 렌더링(기본값 false) |
| renderOptions | <code>Object</code> | 렌더링 옵션(webGLRender가 true일때 사용됨) |
| renderOptions.style | [<code>odfFlatStyle</code>](#odfFlatStyle) \| [<code>odfStyleRule</code>](#odfStyleRule) \| [<code>odfWebGLVectorTileStyle</code>](#odfWebGLVectorTileStyle) | flat style 형식 |
| attributions | <code>Array.&lt;String&gt;</code> | 귀속 정의 |


## Classes

<dl>
<dt><a href="#Layer">Layer</a></dt>
<dd><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p>
</dd>
</dl>

## Typedefs

<dl>
<dt><a href="#odf_layer_option">odf_layer_option</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_layer_source_type">odf_layer_source_type</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_layer_key">odf_layer_key</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_attribute">odf_attribute</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_attribute_dataType">odf_attribute_dataType</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_attribute_range">odf_attribute_range</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_layer_geoJson_object">odf_layer_geoJson_object</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_extent">odf_extent</a> : <code>Array</code></dt>
<dd></dd>
</dl>

<a name="Layer"></a>

## Layer
레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.

**Kind**: global class

* [Layer](#Layer)
  * [.getInitialOption()](#Layer+getInitialOption) ⇒ <code>Object</code>
  * [.getRotation()](#Layer+getRotation) ⇒ <code>Number</code>
  * [.setRotation()](#Layer+setRotation)
  * [.getCenter()](#Layer+getCenter) ⇒ <code>Array.&lt;Number&gt;</code>
  * [.setCenter()](#Layer+setCenter)
  * [.getScale()](#Layer+getScale) ⇒ <code>Number</code>
  * [.setScale()](#Layer+setScale)
  * [.getCrop()](#Layer+getCrop) ⇒ <code>Array.&lt;Number&gt;</code>
  * [.setCrop()](#Layer+setCrop) ⇒ <code>Array.&lt;Number&gt;</code>
  * [.getEditOverlay()](#Layer+getEditOverlay) ⇒ <code>ExtOverlay</code>
  * [.getEditMenuTarget()](#Layer+getEditMenuTarget) ⇒ <code>HTMLElement</code>
  * [.getAttributes(typeFilter, filterFlag)](#Layer+getAttributes) ⇒ [<code>Array.&lt;odf\_attribute&gt;</code>](#odf_attribute)
  * [.getAttributesRange(attributeName, limit)](#Layer+getAttributesRange) ⇒ [<code>odf\_attribute\_range</code>](#odf_attribute_range)
  * [.getAttributesFeaturesValueRange(attributeName)](#Layer+getAttributesFeaturesValueRange) ⇒ [<code>odf\_attribute\_range</code>](#odf_attribute_range)
  * [.defineQuery(fParam)](#Layer+defineQuery)
  * [.getCQLFilter()](#Layer+getCQLFilter) ⇒ <code>String</code>
  * [.setStyle(_style)](#Layer+setStyle)
  * [.getStyle()](#Layer+getStyle)
  * [.getClosestFeatureToCoordinate(coordinate)](#Layer+getClosestFeatureToCoordinate) ⇒ <code>ODF.Feature</code>
  * [.getFeatures()](#Layer+getFeatures) ⇒ <code>Array.&lt;Feature&gt;</code>
  * [.addFeature(feature)](#Layer+addFeature)
  * [.addFeatures(features)](#Layer+addFeatures)
  * [.getFeatureById(featureId)](#Layer+getFeatureById) ⇒ <code>Feature</code>
  * [.buffer(meter)](#Layer+buffer) ⇒ <code>Array.&lt;Feature&gt;</code>
  * [.layerFilter(option)](#Layer+layerFilter)
  * [.toGeoJson()](#Layer+toGeoJson) ⇒ [<code>odf\_layer\_geoJson\_object</code>](#odf_layer_geoJson_object)
  * [.fromKML(kml, dataProjectionCode, featureProjectionCode)](#Layer+fromKML)
  * [.toKML(downloadFile)](#Layer+toKML) ⇒ <code>String</code>
  * [.removeFeature(feature)](#Layer+removeFeature)
  * [.removeFeatureById(featureId)](#Layer+removeFeatureById)
  * [.clear()](#Layer+clear)
  * [.clearFeatures()](#Layer+clearFeatures)
  * [.getFeatureInfoUrl(coordinate, resolution, projection, params)](#Layer+getFeatureInfoUrl)
  * [.setSLD(sld)](#Layer+setSLD)
  * [.getSLD()](#Layer+getSLD) ⇒ <code>SLD</code>
  * [.getDefaultSLD()](#Layer+getDefaultSLD) ⇒ <code>Promise</code>
  * [.getProperties()](#Layer+getProperties) ⇒ <code>Object</code>
  * [.setSource(source)](#Layer+setSource)
  * [.setODFId(변경할)](#Layer+setODFId)
  * [.getODFId()](#Layer+getODFId) ⇒ <code>String</code>
  * [.getOpacity()](#Layer+getOpacity) ⇒ <code>Number</code>
  * [.setOpacity(val)](#Layer+setOpacity)
  * [.getFitExtent(calculateFlag)](#Layer+getFitExtent) ⇒ [<code>odf\_extent</code>](#odf_extent)
  * [.fit(duration)](#Layer+fit)
  * [.getZIndex()](#Layer+getZIndex) ⇒ <code>Number</code>
  * [.setZIndex()](#Layer+setZIndex)
  * [.setMap()](#Layer+setMap)
  * [.getMap()](#Layer+getMap) ⇒ <code>Map</code>
  * [.getVisible()](#Layer+getVisible)
  * [.setVisible()](#Layer+setVisible)
  * [.refresh()](#Layer+refresh)
  * [.on()](#Layer+on)
  * [.un()](#Layer+un)
  * [.getMinResolution()](#Layer+getMinResolution) ⇒ <code>Number</code>
  * [.setMinResolution(최소)](#Layer+setMinResolution)
  * [.getMaxResolution()](#Layer+getMaxResolution) ⇒ <code>Number</code>
  * [.setMaxResolution(최대)](#Layer+setMaxResolution)
  * [.setMaxZoom(최대)](#Layer+setMaxZoom)
  * [.setMinZoom(최소)](#Layer+setMinZoom)
  * [.getOriginCRS()](#Layer+getOriginCRS) ⇒ <code>Array</code>
  * [.cropByFeature(feature)](#Layer+cropByFeature)
  * [.revertLayer()](#Layer+revertLayer)

<a name="Layer+getInitialOption"></a>

### layer.getInitialOption() ⇒ <code>Object</code>
LayerFactory를 통해 Layer 생성한 경우 , 생성 옵션 값을 반환
 ```javascript
let sample = odf.LayerFactory.produce(type, {
  server: 'GeoServer URL 입력',
  layer: 'Store 명칭:Layer 명칭 입력',
  service: '서비스 종류',
}); // sample은 Layer 클래스로 만들어진 객체
sample.getInitialOption();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Object</code> - 레이어 생성 옵션
<a name="Layer+getRotation"></a>

### layer.getRotation() ⇒ <code>Number</code>
이미지 레이어 회전각 조회 기능
 ```javascript
var imageLayer = odf.LayerFactory.produce('geoImage', {...});
        imageLayer.setMap(map);
        imageLayer.getRotaion();//0
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Number</code> - 이미지 레이어 회전각
<a name="Layer+setRotation"></a>

### layer.setRotation()
이미지 레이어 회전각 수정 기능
 ```javascript
var imageLayer = odf.LayerFactory.produce('geoImage', {...});
        imageLayer.setMap(map);
        imageLayer.setRotation(180);//회전각 수정
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
<a name="Layer+getCenter"></a>

### layer.getCenter() ⇒ <code>Array.&lt;Number&gt;</code>
이미지 레이어 중심점 조회 기능
 ```javascript
var imageLayer = odf.LayerFactory.produce('geoImage', {...});
        imageLayer.setMap(map);
        imageLayer.getCenter();//[이미지중심x좌표, 이미지 중심 y좌표]
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Array.&lt;Number&gt;</code> - 이미지 레이어 중심점
<a name="Layer+setCenter"></a>

### layer.setCenter()
이미지 레이어 중심점 수정 기능
 ```javascript
var imageLayer = odf.LayerFactory.produce('geoImage', {...});
        imageLayer.setMap(map);
        imageLayer.setCenter([수정_이미지중심x좌표, 수정_이미지 중심 y좌표]);//중심점 수정
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
<a name="Layer+getScale"></a>

### layer.getScale() ⇒ <code>Number</code>
이미지 레이어 크기 조회 기능
 ```javascript
var imageLayer = odf.LayerFactory.produce('geoImage', {...});
        imageLayer.getScale();//이미지 크기 값 조회
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Number</code> - 이미지 크기
<a name="Layer+setScale"></a>

### layer.setScale()
이미지 레이어 크기 수정 기능
 ```javascript
var imageLayer = odf.LayerFactory.produce('geoImage', {...});
        imageLayer.setMap(map);
        imageLayer.setScale(2);//이미지 크기를 2배로 키움
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
<a name="Layer+getCrop"></a>

### layer.getCrop() ⇒ <code>Array.&lt;Number&gt;</code>
이미지 레이어 잘린 영역 조회
 ```javascript
var imageLayer = odf.LayerFactory.produce('geoImage', {...});
        imageLayer.setMap(map);

        imageLayer.setCrop([minX, minY, maxX, maxY]);//자를 이미지 영역 셋팅
        imageLayer.getCrop();//이미지 레이어 잘린 영역 조회
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Array.&lt;Number&gt;</code> - 잘린 이미지 영역
<a name="Layer+setCrop"></a>

### layer.setCrop() ⇒ <code>Array.&lt;Number&gt;</code>
이미지 레이어 잘린 영역 조회
 ```javascript
var imageLayer = odf.LayerFactory.produce('geoImage', {...});
        imageLayer.setMap(map);

        imageLayer.setCrop([minX, minY, maxX, maxY]);//자를 이미지 영역 셋팅
        imageLayer.getCrop();//이미지 레이어 잘린 영역 조회
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Array.&lt;Number&gt;</code> - 잘린 이미지 영역
<a name="Layer+getEditOverlay"></a>

### layer.getEditOverlay() ⇒ <code>ExtOverlay</code>
이미지 레이어 편집 오버레이 조회
 ```javascript
var imageLayer = odf.LayerFactory.produce('geoImage', {...});
        imageLayer.setMap(map);
        let overlay = imageLayer.getEditOverlay();//이미지 레이어 편집 오버레이 조회
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>ExtOverlay</code> - 편집 오버레이 객체
<a name="Layer+getEditMenuTarget"></a>

### layer.getEditMenuTarget() ⇒ <code>HTMLElement</code>
이미지 레이어 편집 오버레이 target html 조회
 ```javascript
var imageLayer = odf.LayerFactory.produce('geoImage', {...});
        imageLayer.setMap(map);
        let element = imageLayer.getEditMenuTarget();//이미지 레이어 편집 오버레이 target html element조회
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>HTMLElement</code> - 이미지 레이어 편집 오버레이 target html
<a name="Layer+getAttributes"></a>

### layer.getAttributes(typeFilter, filterFlag) ⇒ [<code>Array.&lt;odf\_attribute&gt;</code>](#odf_attribute)
해당 WFS/WMS 레이어의 속성 정보 조회
```javascript
let sample = odf.LayerFactory.produce(...);
sample.getAttributes();//모든 데이터 타입의 속성 항목 조회
sample.getAttributes(['string']);//string 타입의 속성 항목만 조회
sample.getAttributes(['string','geometry']);//string, geometry 타입 속성 항목만 조회
sample.getAttributes(['string'],false);//string 타입이 아닌 속성 항목만 조회
sample.getAttributes(['string','geometry'],false);//string, geometry 타입이 아닌 속성 항목만 조회
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: [<code>Array.&lt;odf\_attribute&gt;</code>](#odf_attribute) - 레이어의 속성정보 배열

| Param | Type | Description |
| --- | --- | --- |
| typeFilter | [<code>Array.&lt;odf\_attribute\_dataType&gt;</code>](#odf_attribute_dataType) | 생성할 색의 계열 |
| filterFlag | <code>boolean</code> | typeFilter를 제외한 데이터 타입을 조회(true)할지, typeFilter에 해당하는 데이터 타입만 조회(false)할지 여부. true가 기본값 |

<a name="Layer+getAttributesRange"></a>

### layer.getAttributesRange(attributeName, limit) ⇒ [<code>odf\_attribute\_range</code>](#odf_attribute_range)
해당 WFS/WMS 레이어의 특성 속성 값의 범위 조회(지오서버로 조회한 feature 데이터 기준)
 ```javascript
let sample = odf.LayerFactory.produce(...);
//name 속성의 값 정보
sample.getAttributesRange('name');
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: [<code>odf\_attribute\_range</code>](#odf_attribute_range) - 조회된 속성 값의 범위

| Param | Type | Description |
| --- | --- | --- |
| attributeName | <code>String</code> | 조회할 속성명 |
| limit | <code>Number</code> | 조회 피쳐 개수 제한 최소 0, 최대 100000 |

<a name="Layer+getAttributesFeaturesValueRange"></a>

### layer.getAttributesFeaturesValueRange(attributeName) ⇒ [<code>odf\_attribute\_range</code>](#odf_attribute_range)
해당 WFS 레이어의 특성 속성 값의 범위 조회(실제 레이어가 갖는 feature 기준)
 ```javascript
let sample = odf.LayerFactory.produce(...);
//name 속성의 값 정보
sample.getAttributesFeaturesValueRange('name');
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: [<code>odf\_attribute\_range</code>](#odf_attribute_range) - 조회된 속성 값의 범위

| Param | Type | Description |
| --- | --- | --- |
| attributeName | <code>String</code> | 조회할 속성명 |

<a name="Layer+defineQuery"></a>

### layer.defineQuery(fParam)
layer 필터링
 ```javascript
let sample = odf.LayerFactory.produce(...);
//newSource로 sample 레이어의 Source 변경
sample.defineQuery({
 condition : '컬럼명=속성값'
});
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| fParam | <code>Object</code> | 레이어 필터링 옵션 |
| fParam.condition | <code>String</code> | 어떤 조건으로 필터링할 것인가에 대한 정의. CQL_FILTER 형식의 문자열 |

<a name="Layer+getCQLFilter"></a>

### layer.getCQLFilter() ⇒ <code>String</code>
레이어에 적용된 cql 필터 값을 반환
 ```javascript
layer.getCQLFilter();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>String</code> - 레이어에 적용된 cql 필터 값
<a name="Layer+setStyle"></a>

### layer.setStyle(_style)
해당 레이어와 연결된 스타일 변경
 ```javascript
let sample = odf.LayerFactory.produce(...);
let style = odf.StyleFacotry.produce(...);
sample.setStyle(style);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| _style | <code>Style</code> \| <code>Array.&lt;Style&gt;</code> \| <code>StyleFunction</code> | 변경할 스타일 객체 |

<a name="Layer+getStyle"></a>

### layer.getStyle()
해당 레이어와 연결된 스타일 반환
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.getStyle();//따로 스타일 설정 안 했으니, 기본 스타일 반환
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
<a name="Layer+getClosestFeatureToCoordinate"></a>

### layer.getClosestFeatureToCoordinate(coordinate) ⇒ <code>ODF.Feature</code>
레이어의 도형중 입력 좌표와 가장 가까운 도형 반환
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.getClosestFeatureToCoordinate([x좌표,y좌표]);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>ODF.Feature</code> - 레이어의 도형중 입력 좌표와 가장 가까운 도형

| Param | Type | Description |
| --- | --- | --- |
| coordinate | <code>coordinate</code> | 조회할 좌표 |

<a name="Layer+getFeatures"></a>

### layer.getFeatures() ⇒ <code>Array.&lt;Feature&gt;</code>
해당 벡터레이어가 소유한 feature 배열 반환
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.getFeatures();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Array.&lt;Feature&gt;</code> - 해당 벡터레이어가 소유한 feature 배열
<a name="Layer+addFeature"></a>

### layer.addFeature(feature)
해당 벡터레이어에 feature 추가
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.addFeature(feature);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| feature | <code>Feature</code> | 추가할 feature |

<a name="Layer+addFeatures"></a>

### layer.addFeatures(features)
해당 벡터레이어에 feature들 추가
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.addFeatures([feature,feature ...]);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| features | <code>Array.&lt;Feature&gt;</code> | 추가할 feature 배열 |

<a name="Layer+getFeatureById"></a>

### layer.getFeatureById(featureId) ⇒ <code>Feature</code>
feature id로 feature 객체 리턴
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.getFeatureById('id01');
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Feature</code> - 입력한 featureId 값을 id로 갖는 Feature

| Param | Type | Description |
| --- | --- | --- |
| featureId | <code>String</code> | 조회할 Featuer Id |

<a name="Layer+buffer"></a>

### layer.buffer(meter) ⇒ <code>Array.&lt;Feature&gt;</code>
해당 벡터레이어의 모든 피쳐에 버퍼 적용
 ```javascript
let sample = odf.LayerFactory.produce(...);
let style = odf.StyleFacotry.produce(...);
sample.buffer(50);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Array.&lt;Feature&gt;</code> - 해당 벡터레이어의 모든 피처의 버퍼 도형들의 배열

| Param | Type | Description |
| --- | --- | --- |
| meter | <code>number</code> | 적용할 버퍼의 크기 |

<a name="Layer+layerFilter"></a>

### layer.layerFilter(option)
해당 벡터레이어를 Style을 이용해 필터링
필터링된 데이터는 화면상에서 보이지 않을 뿐 feature 목록에 여전히 존재
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.layerODFFilter({
 컬럼명 : '속성값'
});
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| option | <code>Object</code> | 필터링 옵션 |
| option.id | <code>Object</code> | 필터링할 feature의  id |

<a name="Layer+toGeoJson"></a>

### layer.toGeoJson() ⇒ [<code>odf\_layer\_geoJson\_object</code>](#odf_layer_geoJson_object)
해당 벡터레이어를 geojson 타입의 Object로 변환
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.toGeoJson();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: [<code>odf\_layer\_geoJson\_object</code>](#odf_layer_geoJson_object) - - 변환된 geojson 타입의 Object
<a name="Layer+fromKML"></a>

### layer.fromKML(kml, dataProjectionCode, featureProjectionCode)
KML 형식 데이터 불러오기
 ```javascript
let wfsLayer = odf.LayerFactory.produce(...);
var newKmlText = '<?xml version="1.0" encoding="UTF-8"?><kml xmlns="http://www.opengis.net/kml/2.2"  xmlns:gx="http://www.google.com/kml/ext/2.2" xmlns:kml="http://www.opengis.net/kml/2.2"        xmlns:atom="http://www.w3.org/2005/Atom"><Document><name><![CDATA[odf-emptyLayer-vector16054860583025tj71on5y7q]]></name><description><![CDATA[odf-emptyLayer-vector16054860583025tj71on5y7q]]></description><visibility>1</visibility><open>1</open><Style id="line"><LineStyle><color>ffff55ff</color><width>3</width></LineStyle></Style><Folder><description>LineString</description><visibility>1</visibility><open>0</open><name>LineString</name><Placemark><styleUrl>#line</styleUrl><MultiGeometry><LineString><coordinates>1097802.7789468267,1717572.789176268 1097621.4442271432,1717505.9816479636 1097535.5488336089,1717315.1029956653 1097549.8647325314,1717071.7327139848 1097821.8668120564,1716947.6615899908 1098093.8688915817,1717033.5569835252 1098198.8521503457,1717367.5946250472 1098031.8333295847,1717477.3498501189 1097745.5153511371,1717472.5778838114 1097630.988159758,1717291.243164128 1097645.3040586805,1717133.7682759818 1097812.3228794415,1717086.048612907 1097974.5697338951,1717210.119736901 1097955.4818686654,1717353.278726125 1097817.094845749,1717310.3310293579 1097840.9546772863,1717233.9795684384 </coordinates></LineString><LineString><coordinates>1098590.1533875575,1717768.4397948738 1098337.2391732621,1717749.3519296441 1098284.74754388,1717568.0172099606 1098408.818667874,1717200.5758042862 1098833.523669238,1717114.6804107518 1099105.5257487632,1717319.874961973 1099134.157546608,1717658.6845698026 1098924.1910290797,1717725.492098107 1098509.0299603308,1717653.9126034952 1098370.6429374146,1717458.2619848894 1098518.5738929457,1717281.6992315133 1098809.6638377008,1717286.4711978207 1099014.8583889215,1717525.0695131938 1098876.4713660053,1717630.0527719578 1098628.3291180173,1717534.6134458086 1098614.0132190948,1717381.91052397 1098709.452545244,1717381.91052397 1098823.9797366231,1717510.7536142713 </coordinates></LineString></MultiGeometry></Placemark></Folder></Document></kml>';
wfsLayer.fromKML(newKmlText, 'EPSG:5179', 'EPSG:5179');
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| kml | <code>String</code> | kml 형식의 문자열 |
| dataProjectionCode | <code>String</code> | 원본 kml data 프로젝션 코드 (ex) 'EPSG:5179' , 'EPSG:4326' ... |
| featureProjectionCode | <code>String</code> | 지도 프로젝션 코드 (ex) 'EPSG:5179' , 'EPSG:4326' ... |

<a name="Layer+toKML"></a>

### layer.toKML(downloadFile) ⇒ <code>String</code>
KML 형식으로 변환
 ```javascript
let wfsLayer = odf.LayerFactory.produce(...);
var kmlText = wfsLayer.toKML();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>String</code> - kml 형식 문자열

| Param | Type | Description |
| --- | --- | --- |
| downloadFile | <code>Boolean</code> | 다운로드 여부(기본값 false) |

<a name="Layer+removeFeature"></a>

### layer.removeFeature(feature)
특정 feature 제거
 ```javascript
let wfsLayer = odf.LayerFactory.produce(...);
let feature = wfsLayer.getFeatures()[0];
wfsLayer.removeFeature(feature);//특정 feature 제거
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| feature | <code>Feature</code> | 제거할 feature |

<a name="Layer+removeFeatureById"></a>

### layer.removeFeatureById(featureId)
특정 featureId를 갖는 feature 제거
 ```javascript
let wfsLayer = odf.LayerFactory.produce(...);
wfsLayer.removeFeatureById(wfsLayer.getFeatures()[0].getId());//특정 feature 제거
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| featureId | <code>String</code> | 제거할 feature의 id |

<a name="Layer+clear"></a>

### layer.clear()
feature 리로드. server에서 끌어오는 데이터는 지우고 다시불러오고, 로컬데이터를 불러온 경우 모든 피쳐 삭제됨
 ```javascript
let wfsLayer = odf.LayerFactory.produce(...);
wfsLayer.clear();//모든 feature 제거
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
<a name="Layer+clearFeatures"></a>

### layer.clearFeatures()
모든 feature 제거
 ```javascript
let wfsLayer = odf.LayerFactory.produce(...);
wfsLayer.clearFeatures();//모든 feature 제거
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
<a name="Layer+getFeatureInfoUrl"></a>

### layer.getFeatureInfoUrl(coordinate, resolution, projection, params)
GetFeatureInfo URL을 반환
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.getFeatureInfoUrl();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| coordinate | <code>Array</code> | 좌표 |
| resolution | <code>Number</code> | 축척 |
| projection | <code>String</code> | 좌표계 |
| params | <code>Object</code> | GetFeatureInfo URL 생성 옵션 |
| params.INFO_FORMAT | <code>String</code> | 출력물 포멧 |
| params.QUERY_LAYERS | <code>String</code> | 쿼리할 레이어 |
| params.LAYERS | <code>String</code> | 표시할 레이어 (QUERY_LAYERS 와 동일) |
| params.VERSION | <code>String</code> | 서비스의 버전 |

<a name="Layer+setSLD"></a>

### layer.setSLD(sld)
해당 wms 레이어와 연결된 스타일 변경
 ```javascript
let sample = odf.LayerFactory.produce(...);
let sld = odf.StyleFactory.produceSLD({
          name: "My Style",
          rules: [
            {
              name: "My Rule",
              symbolizers: [
                {
                  kind: "Mark",
                  wellKnownName: "Circle",
                  color: "#FF0000",
                  radius: 6
                }
              ]
            }
          ]
        })
sample.setStyle(sld);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| sld | <code>SLD</code> | sld 객체 |

<a name="Layer+getSLD"></a>

### layer.getSLD() ⇒ <code>SLD</code>
해당  wms 레이어와 연결된 사용자정의 스타일 조회
 ```javascript
let sample = odf.LayerFactory.produce(...);
let sld = odf.StyleFactory.produceSLD({
 name: "My Style",
 rules: [
   {
     name: "My Rule",
     symbolizers: [
       {
         kind: "Mark",
         wellKnownName: "Circle",
         color: "#FF0000",
         radius: 6
       }
     ]
   }
 ]
});
sample.setStyle(sld);
sample.getSLD();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>SLD</code> - 해당 wms 레이어에 적용된 사용자정의 스타일
<a name="Layer+getDefaultSLD"></a>

### layer.getDefaultSLD() ⇒ <code>Promise</code>
해당  wms 레이어와 연결된 사용자정의 스타일 조회
 ```javascript
let sample = odf.LayerFactory.produce(...);
let sld = odf.StyleFactory.produceSLD({
 name: "My Style",
 rules: [
   {
     name: "My Rule",
     symbolizers: [
       {
         kind: "Mark",
         wellKnownName: "Circle",
         color: "#FF0000",
         radius: 6
       }
     ]
   }
 ]
});
sample.setStyle(sld);
sample.getDefaultSLD().then(sld=>{
 //control sld
 //sld.download();
});
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Promise</code> - 해당 wms 레이어에 적용된 사용자정의 스타일을 PromiseValue로 갖는 promise객체
<a name="Layer+getProperties"></a>

### layer.getProperties() ⇒ <code>Object</code>
해당 객체가 갖고있는 모든 속성명과 속성값 정보 반환
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.getProperties();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Object</code> - 해당 객체가 갖고있는 모든 속성명과 속성값 정보
<a name="Layer+setSource"></a>

### layer.setSource(source)
layer와 연결된 source 변경
 ```javascript
let sample = odf.LayerFactory.produce(...);
//newSource로 sample 레이어의 Source 변경
sample.setSource(newSource);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| source | <code>Source</code> | layer와 연결할 source |

<a name="Layer+setODFId"></a>

### layer.setODFId(변경할)
해당 layer의 고유 id 변경
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.setODFId('new_odf_id_0000001');
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| 변경할 | <code>String</code> | layer의 고유 id |

<a name="Layer+getODFId"></a>

### layer.getODFId() ⇒ <code>String</code>
해당 layer의 고유 id 반환
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.getODFId();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>String</code> - 해당 layer의 고유 id
<a name="Layer+getOpacity"></a>

### layer.getOpacity() ⇒ <code>Number</code>
해당 layer의 투명도 조회
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.getOpacity();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Number</code> - 해당 레이어의 투명도 값 조회
<a name="Layer+setOpacity"></a>

### layer.setOpacity(val)
해당 layer의 투명도 설정
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.setOpacity(1);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| val | <code>Number</code> | 해당 레이어의 투명도 값 설정 (0~1) |

<a name="Layer+getFitExtent"></a>

### layer.getFitExtent(calculateFlag) ⇒ [<code>odf\_extent</code>](#odf_extent)
해당 layer가 한눈에 보이는 보여주는 extent 반환
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.getFitExtent();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: [<code>odf\_extent</code>](#odf_extent) - 해당 layer가 한눈에 보이는 보여주는 extent형식의 문자열

| Param | Type | Description |
| --- | --- | --- |
| calculateFlag | <code>Boolean</code> | extent값 계산 여부 .기본값 false - true : (wfs 레이어일 경우) 로드된 feature 기준으로 extent 계산 값 이용 / (geoImage 레이어인 경우) 로드된 이미지 기준으로 extent 계산 - false : geoserver에 등록된 extent 값 이용 |

<a name="Layer+fit"></a>

### layer.fit(duration)
해당 layer가 한눈에 보이는 보여주는 extent로 화면 위치 이동 및 줌 레벨 변경
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.fit();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| duration | <code>Number</code> | 부드럽게 이동. 기본값 0 - 값의 크기가 클수록 천천히 이동 |

<a name="Layer+getZIndex"></a>

### layer.getZIndex() ⇒ <code>Number</code>
해당 layer의 z-index 반환
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.setMap(map);
sample.getZIndex();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Number</code> - 해당 layer의 z-index.
- setMap을 한 순서 대로 drawing 한다. ( 1 -> 2 -> 3 )
- 보이는 순서는 역순 이다. ( 3 -> 2 -> 1)
  <a name="Layer+setZIndex"></a>

### layer.setZIndex()
해당 layer의 z-index 수정
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.setMap(map);
sample.setZIndex(10);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
<a name="Layer+setMap"></a>

### layer.setMap()
해당 layer를 map 객체와 연결
 ```javascript
let map = new odf.Map(...);
let sample = odf.LayerFactory.produce(...);
sample.setMap(map);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
<a name="Layer+getMap"></a>

### layer.getMap() ⇒ <code>Map</code>
해당 layer와 연결된 map 객체 반환
 ```javascript
let map = new odf.Map(...);
let sample = odf.LayerFactory.produce(...);
sample.setMap(map);
sample.getMap();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Map</code> - 해당 layer와 연결된 map 객체
<a name="Layer+getVisible"></a>

### layer.getVisible()
해당 layer의 가시성 정보 조회
 ```javascript
let map = new odf.Map(...);
let sample = odf.LayerFactory.produce(...);
sample.setMap(map);
sample.getVisible();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
<a name="Layer+setVisible"></a>

### layer.setVisible()
해당 layer의 가시성 정보 수정
 ```javascript
let map = new odf.Map(...);
let sample = odf.LayerFactory.produce(...);
sample.setMap(map);

//레이어가 화면상에 보이지 않게 처리
sample.setVisible(false);

//레이어가 화면상에 보이게 처리
sample.setVisible(true);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
<a name="Layer+refresh"></a>

### layer.refresh()
해당 layer refresh
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.refresh();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
<a name="Layer+on"></a>

### layer.on()
해당 layer에 이벤트 연결
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.on('render',function(){
 //do something
});
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
<a name="Layer+un"></a>

### layer.un()
해당 layer의 이벤트 연결 해제
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.un('render',function(){
 //do something
});
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
<a name="Layer+getMinResolution"></a>

### layer.getMinResolution() ⇒ <code>Number</code>
해당 layer 뷰잉 최소 해상도 조회
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.getMinResolution();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Number</code> - 최소 해상도 설정값
<a name="Layer+setMinResolution"></a>

### layer.setMinResolution(최소)
해당 layer 뷰잉 최소 해상도 설정
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.setMinResolution(9.718);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| 최소 | <code>Number</code> | 해상도 설정값 |

<a name="Layer+getMaxResolution"></a>

### layer.getMaxResolution() ⇒ <code>Number</code>
해당 layer 뷰잉 최대 해상도 조회
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.getMaxResolution();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Number</code> - 최대 해상도 설정값
<a name="Layer+setMaxResolution"></a>

### layer.setMaxResolution(최대)
해당 layer 뷰잉 최대 해상도 설정
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.setMaxResolution(9.718);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| 최대 | <code>Number</code> | 해상도 설정값 |

<a name="Layer+setMaxZoom"></a>

### layer.setMaxZoom(최대)
해당 layer 뷰잉 최대 줌레벨 설정
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.setMaxZoom(10);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| 최대 | <code>Number</code> | 줌레벨 설정값 |

<a name="Layer+setMinZoom"></a>

### layer.setMinZoom(최소)
해당 layer 뷰잉 최소 줌레벨 설정
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.setMinResolution(10);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| 최소 | <code>Number</code> | 줌레벨 설정값 |

<a name="Layer+getOriginCRS"></a>

### layer.getOriginCRS() ⇒ <code>Array</code>
해당 layer 지오서버 발행 좌표계 조회
 ```javascript
let sample = odf.LayerFactory.produce(...);
sample.getOriginCRS();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
**Returns**: <code>Array</code> - 해당 layer의 서버 발행 원본 좌표계
<a name="Layer+cropByFeature"></a>

### layer.cropByFeature(feature)
해당 layer를 feature 모양만큼만 보이게 처리
 ```javascript
//원형 feature 생성
let circleFeature = odf.FeatureFactory.produce({
            geometryType: 'circle',
            coordinates: [949597.4353529032, 1933961.3294866967],
            circleSize: 3732
        });
layer.cropByFeature(circleFeature);
```
 ```javascript
//다각형 feature 생성
let polygonFeature = odf.FeatureFactory.produce({
        geometryType: 'polygon',
        coordinates: [
          [
            [949301.5734418407, 1935602.8858964627],
            [950628.1800753145, 1936891.316799477],
            [951353.5189540483, 1935240.216457096],
            [953911.2928948466, 1934782.1076915797],
            [951782.9959217197, 1933675.0115082492],
            [951391.694684508, 1932291.141279086],
            [950532.7407491653, 1933340.973866727],
            [947125.5568056392, 1933207.3588101182],
            [948824.3768110948, 1934304.9110608338],
            [947163.7325360989, 1935784.2206161462],
            [949301.5734418407, 1935602.8858964627],
          ],
        ],
      });
layer.cropByFeature(polygonFeature);
```

**Kind**: instance method of [<code>Layer</code>](#Layer)

| Param | Type | Description |
| --- | --- | --- |
| feature | <code>Feature</code> | geometry 타입이 circle 또는 polygon 또는 multiPolygon인 feature |

<a name="Layer+revertLayer"></a>

### layer.revertLayer()
feature 모양만큼만 보이게 처리한 레이어를 원래 상태로 원복

 ```javascript
 //원형 feature 생성
let circleFeature = odf.FeatureFactory.produce({
            geometryType: 'circle',
            coordinates: [949597.4353529032, 1933961.3294866967],
            circleSize: 3732
        });
layer.cropByFeature(circleFeature);
...
layer.revertLayer();
```

**Kind**: instance method of [<code>Layer</code>](#Layer)
<a name="odf_layer_option"></a>

## odf\_layer\_option : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| source | <code>odf.Source</code> | 레이어 소스 |
| mkType | [<code>odf\_layer\_source\_type</code>](#odf_layer_source_type) | 레이어 소스 제공 (ex)'geoserver' 또는 'geojson' 또는 'empty' |
| id | <code>String</code> | 레이어 식별 id |

<a name="odf_layer_source_type"></a>

## odf\_layer\_source\_type : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| 'geoserver' | <code>String</code> | geoserver 제공 소스로 레이어 생성(table) |
| 'geojson' | <code>String</code> | geojson을 소스로 vector레이어 생성 |
| 'empty' | <code>String</code> | 빈 vector레이어 생성 |

<a name="odf_layer_key"></a>

## odf\_layer\_key : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| 'image' | <code>String</code> | Image 레이어 생성 (table) |
| 'vector' | <code>String</code> | Vector 레이어 생성 |
| 'heatmap' | <code>String</code> | Heatmap 레이어 생성 |
| 'imagetile' | <code>String</code> | Tile 레이어 생성 |

<a name="odf_attribute"></a>

## odf\_attribute : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| name | <code>String</code> | 속성명 |
| type | <code>String</code> | 데이터 타입  (ex) string, int, geometry |

<a name="odf_attribute_dataType"></a>

## odf\_attribute\_dataType : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| 'geometry' | <code>String</code> | 지오메트리(table) |
| 'string' | <code>String</code> | 문자열 |
| 'int' | <code>String</code> | 숫자 |

<a name="odf_attribute_range"></a>

## odf\_attribute\_range : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| values | <code>Object</code> | 속성값을 키로, 해당 속성 값을 갖는 요소의 갯수를 count 속성 값으로 갖는 Object (ex) { 'aaa' : {count : 1}, 'bbb' : {count : 5}, 'ccc' : {count : 3}, } |
| min | <code>String</code> | 해당 속성 값들의 최소값 |
| max | <code>String</code> | 해당 속성 값들의 최대값 |

<a name="odf_layer_geoJson_object"></a>

## odf\_layer\_geoJson\_object : <code>Object</code>
**Kind**: global typedef

| Param | Type | Description |
| --- | --- | --- |
| features | <code>Array.&lt;odf\_feature\_geojson\_object&gt;</code> | feature 정보 |

**Properties**

| Name | Type | Description |
| --- | --- | --- |
| type | <code>String</code> | geoJson 타입. 'FeatureCollection' |

<a name="odf_extent"></a>

## odf\_extent : <code>Array</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| odf_extent[0]; | <code>number</code> | 좌상단 x좌표 |
| odf_extent[1]; | <code>number</code> | 좌상단 y좌표 |
| odf_extent[2]; | <code>number</code> | 우하단 x좌표 |
| odf_extent[3]; | <code>number</code> | 우하단 y좌표 |


## Classes

<dl>
<dt><a href="#Feature">Feature</a></dt>
<dd><p>Feature 클래스</p>
</dd>
</dl>

## Typedefs

<dl>
<dt><a href="#odf_coordinate">odf_coordinate</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_geometryType">odf_geometryType</a> : <code>String</code></dt>
<dd></dd>
<dt><a href="#odf_feature_geojson_object">odf_feature_geojson_object</a> : <code>Object</code></dt>
<dd></dd>
</dl>

<a name="odf_coordinate"></a>

## odf\_coordinate : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| odf_coordinate[x | <code>number</code> | x 좌표 |
| odf_coordinate[y | <code>number</code> | y 좌표 |

<a name="odf_geometryType"></a>

## odf\_geometryType : <code>String</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| 'point' | <code>String</code> | point 타입 (table) |
| 'multipoint' | <code>String</code> | multipoint 타입 |
| 'linestring' | <code>String</code> | linestring 타입 |
| 'multilinestring' | <code>String</code> | multilinestring 타입 |
| 'polygon' | <code>String</code> | polygon 타입 |
| 'multipolygon' | <code>String</code> | multipolygon 타입 |
| 'circle' | <code>String</code> | circle 타입 |

<a name="odf_feature_geojson_object"></a>

## odf\_feature\_geojson\_object : <code>Object</code>
**Kind**: global typedef

| Param | Type | Description |
| --- | --- | --- |
| geometry.type | [<code>odf\_geometryType</code>](#odf_geometryType) | 지오메트리 타입 |
| geometry.coordinates | [<code>odf\_coordinate</code>](#odf_coordinate) \| [<code>Array.&lt;odf\_coordinate&gt;</code>](#odf_coordinate) \| <code>Array.&lt;Array.&lt;odf\_coordinate&gt;&gt;</code> \| <code>Array.&lt;Array.&lt;Array.&lt;odf\_coordinate&gt;&gt;&gt;</code> | 지오메트리   좌표값 |

**Properties**

| Name | Type | Description |
| --- | --- | --- |
| geometry | <code>Object</code> | 지오메트리 정보 |


## Classes

<dl>
<dt><a href="#StyleFactory">StyleFactory</a></dt>
<dd><p>스타일 생성 클래스</p>
</dd>
</dl>

## Typedefs

<dl>
<dt><a href="#odf_sld_option">odf_sld_option</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_sld_rule">odf_sld_rule</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_sld_mark_symbolizer">odf_sld_mark_symbolizer</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_sld_icon_symbolizer">odf_sld_icon_symbolizer</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_sld_line_symbolizer">odf_sld_line_symbolizer</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_sld_fill_symbolizer">odf_sld_fill_symbolizer</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_sld_text_symbolizer">odf_sld_text_symbolizer</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_sld_point_label">odf_sld_point_label</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_sld_point_label_inner">odf_sld_point_label_inner</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_sld_point_label_inner_anchorpoint">odf_sld_point_label_inner_anchorpoint</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_sld_point_label_inner_displacement">odf_sld_point_label_inner_displacement</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_sld_line_label">odf_sld_line_label</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_sld_line_label_inner">odf_sld_line_label_inner</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_styleOption_stroke">odf_styleOption_stroke</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_StyleOption_fill">odf_StyleOption_fill</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_StyleOption_fillPattern_pattern">odf_StyleOption_fillPattern_pattern</a> : <code>String</code></dt>
<dd></dd>
<dt><a href="#odf_StyleOption_fillPattern">odf_StyleOption_fillPattern</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_StyleOption_text">odf_StyleOption_text</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_styleOption_icon">odf_styleOption_icon</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_styleOption_circle">odf_styleOption_circle</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_styleOption_regularShape">odf_styleOption_regularShape</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_styleOption_chart">odf_styleOption_chart</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_styleOption_geometryType">odf_styleOption_geometryType</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_styleOption">odf_styleOption</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_StyleFunctionOption">odf_StyleFunctionOption</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_default_pattern_define">odf_default_pattern_define</a> : <code>Object</code></dt>
<dd></dd>
</dl>

<a name="StyleFactory"></a>

## StyleFactory
스타일 생성 클래스

**Kind**: global class

* [StyleFactory](#StyleFactory)
  * [.validationCheck(options)](#StyleFactory.validationCheck) ⇒ <code>Boolean</code>
  * [.produceStroke(options)](#StyleFactory.produceStroke) ⇒ <code>odf.Stroke</code>
  * [.produceFill(options)](#StyleFactory.produceFill) ⇒ <code>odf.Fill</code>
  * [.produceFillPattern(options)](#StyleFactory.produceFillPattern) ⇒ <code>odf.FillPattern</code>
  * [.produceText(options)](#StyleFactory.produceText) ⇒ <code>odf.Text</code>
  * [.produceCircle(options)](#StyleFactory.produceCircle) ⇒ <code>odf.Circle</code>
  * [.produceIcon(options)](#StyleFactory.produceIcon) ⇒ <code>odf.Icon</code>
  * [.produceChart(options)](#StyleFactory.produceChart) ⇒ <code>odf.Chart</code>
  * [.produceRegularShape(options)](#StyleFactory.produceRegularShape) ⇒ <code>odf.RegularShape</code>
  * [.produce(options)](#StyleFactory.produce) ⇒ <code>odf.Style</code>
  * [.produceSLD(options)](#StyleFactory.produceSLD) ⇒ <code>SLD</code>
  * [.cvtSLD2Object(sld)](#StyleFactory.cvtSLD2Object) ⇒ <code>Promise</code>
  * [.produceElement(options, size, labelFlag)](#StyleFactory.produceElement) ⇒ <code>HTMLElement</code>
  * [.produceFunction(options, mode)](#StyleFactory.produceFunction) ⇒ <code>function</code>
  * [.cvtStyle2Json(style)](#StyleFactory.cvtStyle2Json) ⇒ <code>String</code>
  * [.cvtStyle2Object(style)](#StyleFactory.cvtStyle2Object) ⇒ [<code>Array.&lt;odf\_styleOption&gt;</code>](#odf_styleOption) \| [<code>odf\_styleOption</code>](#odf_styleOption) \| [<code>Array.&lt;odf\_StyleFunctionOption&gt;</code>](#odf_StyleFunctionOption)
  * [.getValidFillPatternList()](#StyleFactory.getValidFillPatternList) ⇒ [<code>Array.&lt;odf\_default\_pattern\_define&gt;</code>](#odf_default_pattern_define)
  * [.addPattern(title, options)](#StyleFactory.addPattern)

<a name="StyleFactory.validationCheck"></a>

### StyleFactory.validationCheck(options) ⇒ <code>Boolean</code>
스타일 생성 Option의 유효성검사

```javascript
   //스타일 유효성 검사
   odf.StyleFactory.validationCheck({
     image : {
       circle : {
         radius:50,//크기
         fill:{
           color:'gray'//채우기 색
          },//채우기
          stroke: {//윤곽선
            color:'red',//테두리 색
            width:10,//굵기
            lineDash:[4, 8]//점선 설정
          },
        }
      },
      text : {
        text : '텍스트 내용', //텍스트 내용
        font : 'bold 20px Courier New' //폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
      }
    });
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: <code>Boolean</code> - 파라미터로 넘어온 options의 값이 스타일을 생성하기에 유효하다면 true 값 반환

| Param | Type | Description |
| --- | --- | --- |
| options | [<code>odf\_styleOption</code>](#odf_styleOption) | 스타일 생성을 위한 option |

<a name="StyleFactory.produceStroke"></a>

### StyleFactory.produceStroke(options) ⇒ <code>odf.Stroke</code>
Stroke Style 생성
```javascript
      let strokeStyle = odf.StyleFactory.produceStroke({
        color:'red',
        lineCap : 'butt',//선의 끝부분 모양('butt'(네모지게-선이 원래 길이보다 조금 일찍 끝남) / 'round' (둥글게) / 'square'(네모지게))
        lineJoin : 'miter',//('bevel' (꺾이는 부분을 지붕모양으로 )/ 'round' (둥글게)/ 'miter'(뾰족하게))
        //lineDash : [10],//점선의 간격 크기
        width:20
      });
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: <code>odf.Stroke</code> - Stroke Style

| Param | Type | Description |
| --- | --- | --- |
| options | [<code>odf\_styleOption\_stroke</code>](#odf_styleOption_stroke) | Stroke Style 생성을 위한 옵션 |

<a name="StyleFactory.produceFill"></a>

### StyleFactory.produceFill(options) ⇒ <code>odf.Fill</code>
fill Style 생성
```javascript
      let fillStyle = odf.StyleFactory.produceFill({color:'red'});
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: <code>odf.Fill</code> - Fill Style

| Param | Type | Description |
| --- | --- | --- |
| options | <code>odf\_styleOption\_fill</code> | Fill Style 생성을 위한 옵션 |

<a name="StyleFactory.produceFillPattern"></a>

### StyleFactory.produceFillPattern(options) ⇒ <code>odf.FillPattern</code>
fillPattern Style 생성
```javascript
      let fillPatternStyle = odf.StyleFactory.produceFillPattern({
        pattern : 'hash',//패턴 유형
        patternColor : '#55dd20',//패턴색상(전경색)
        fill : '#ffffff',//패턴 배경 색상
      });
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: <code>odf.FillPattern</code> - Fill Style

| Param | Type | Description |
| --- | --- | --- |
| options | <code>odf\_styleOption\_fillPattern</code> | FillPattern Style 생성을 위한 옵션 |

<a name="StyleFactory.produceText"></a>

### StyleFactory.produceText(options) ⇒ <code>odf.Text</code>
Text Style 생성
```javascript
      let textStyle = odf.StyleFactory.produceText({
        text:'텍스트 내용',//텍스트 내용
        //offsetX : 0,//기준점으로부터 텍스트 x좌표 위치 이동
        //offsetY : 0,//기준점으로부터 텍스트 Y좌표 위치 이동
        //rotation : (Math.PI*270/180), //회전
        //textAlign : 'left',//텍스트 수평정렬
        //textBaseline : 'middle',//텍스트 수직정렬
        font : 'bold 20px Courier New',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
        fill : {color:'red'},
        stroke : {//text 안의 stroke는 width/lineCap/lineJoin/lineDash/lineDashOffset/miterLimit 옵션 적용  x
          color:'blue',
        },
        padding : [10,5,5,5],//text와 background영역 사이의 여백 //placement :'line' 일 경우 미적용
        backgroundStroke : {color:'black'},//placement :'line' 일경우 미적용
        backgroundFill : {color:'white'},//placement :'line' 일경우 미적용
        placement :'line',//텍스트를 나열하는 위치를 line을 따라 나타나게 할지, 특정 point에 나타나게 할지
        maxAngle : 90*Math.PI/180,//placement :'line' 일경우 적용
        overflow : false,//placement :'line' 일경우 적용//텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부
        scale : 2, //텍스트 크기를 정해진 값의 n배로 셋팅
        rotateWithView: true//지도가 회전할때 텍스트도 적절하게 회전할지 여부
      });
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: <code>odf.Text</code> - Text Style

| Param | Type | Description |
| --- | --- | --- |
| options | <code>odf\_styleOption\_text</code> | Text Style 생성을 위한 옵션 |

<a name="StyleFactory.produceCircle"></a>

### StyleFactory.produceCircle(options) ⇒ <code>odf.Circle</code>
Circle Style 생성
```javascript
      let circleStyle = odf.StyleFactory.produceCircle({
         radius:50,//크기
         fill:{
           color:'gray'//채우기 색
          },//채우기
          stroke: {//윤곽선
            color:'red',//테두리 색
            width:10,//굵기
            lineDash:[4, 8]//점선 설정
          },
          snapToPixel : false //true : sharp, false : blur
      });
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: <code>odf.Circle</code> - Circle Style

| Param | Type | Description |
| --- | --- | --- |
| options | [<code>odf\_styleOption\_circle</code>](#odf_styleOption_circle) | Circle Style 생성을 위한 옵션 |

<a name="StyleFactory.produceIcon"></a>

### StyleFactory.produceIcon(options) ⇒ <code>odf.Icon</code>
Icon Style 생성
```javascript
      let iconStyle = odf.StyleFactory.produceIcon({
          anchor:[70, 0.7], //아이콘 위치 조정 값
          anchorOrigin:'bottom-right', //아이콘 위치 조정 기준점
          anchorXUnits:'pixels', //아이콘 위치 조정 단위 설정 x축
          anchorYUnits:'fraction', //아이콘 위치 조정 단위 설정 y축
          color:'blue', //이미지 색상
          offset:[20,20], //offsetOrigin으로 부터 x축, y축 좌표위치 이동
          offsetOrigin:'top-left', //offset의 기준점
          opacity:0.5, //투명도
          scale:0.5, //크기를 정해진 값의 n배로 셋팅
          snapToPixel:false,  //true : sharp, false : blur
          rotateWithView:false, //지도가 회전할때 텍스트도 적절하게 회전할지 여부
          rotation:30*Math.PI/180, //시계방향으로 회전
          size:[100, 100],  //이미지가 그려지는 도형 크기 ※ 그림을 그린 도화지의 크기
          src:'images/sample.png' //이미지 경로
        });
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: <code>odf.Icon</code> - Icon Style

| Param | Type | Description |
| --- | --- | --- |
| options | [<code>odf\_styleOption\_icon</code>](#odf_styleOption_icon) | Icon Style 생성을 위한 옵션 |

<a name="StyleFactory.produceChart"></a>

### StyleFactory.produceChart(options) ⇒ <code>odf.Chart</code>
Chart Style 생성
```javascript
      let chartStyle = odf.StyleFactory.produceChart( {
            type : 'pie', // 2차원 파이 : 'pie', 3차원 파이 : 'pie3D', 도넛형태 : 'donut'
            datas : ['X', 'Y', 'id'],
            stroke : {
              color : '#ffffff', //테두리 색상
              width:2, //테두리 두께
            },
            radius : 50,//파이의 크기
            //파이차트에서 사용할 색상
            //colors : 'pale' , // 'classic', 'dark', 'pale', 'pastel', 'neon'
            //아래와같이 직접 정의하여 사용할 수도 있음
            colors :['#FF4B4B','#FF7272','#FF9999','#FFC0C0','#FFE7E7'],
                   //rotation : Math.PI*90/180//기울기
          });
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: <code>odf.Chart</code> - chart Style

| Param | Type | Description |
| --- | --- | --- |
| options | [<code>odf\_styleOption\_chart</code>](#odf_styleOption_chart) | chart Style 생성을 위한 옵션 |

<a name="StyleFactory.produceRegularShape"></a>

### StyleFactory.produceRegularShape(options) ⇒ <code>odf.RegularShape</code>
RegularShape Style 생성
```javascript
  let regularShapeStyle = odf.StyleFactory.produceRegularShape( {
      image : {
        regularShape : {
          fill: {
              color: [0, 0, 0, 0.2]
              //채우기 색
          },//채우기
          stroke: {//윤곽선
              color: [132, 229, 252, 0.95],//테두리 색
              width: 2,//굵기
              //lineDash:[4, 1]//점선 설정
          },
          points: 5,
          radius: 10,
          radius2: 4,
          angle: 0
        },
  });
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: <code>odf.RegularShape</code> - regularShape Style

| Param | Type | Description |
| --- | --- | --- |
| options | [<code>odf\_styleOption\_regularShape</code>](#odf_styleOption_regularShape) | chart Style 생성을 위한 옵션 |

<a name="StyleFactory.produce"></a>

### StyleFactory.produce(options) ⇒ <code>odf.Style</code>
스타일 객체 생성

   ```javascript
   //점 스타일 생성 예제
   let pointCircleStyle = odf.StyleFactory.produce({
     image : {
       circle : {
         radius:50,//크기
         fill:{
           color:'gray'//채우기 색
          },//채우기
          stroke: {//윤곽선
            color:'red',//테두리 색
            width:10,//굵기
            lineDash:[4, 8]//점선 설정
          },
          snapToPixel : false //true : sharp, false : blur
        }
      },
      text : {
        text : '텍스트 내용', //텍스트 내용
        font : 'bold 20px Courier New' //폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
      }
    });
    //생성된 스타일을 JSON형태로 변환
    pointCircleStyle.getJSON();
```

```javascript
    //심볼 스타일 생성 예제
    let pointIconStyle = odf.StyleFactory.produce({
      image : {
        icon : {
          anchor:[70, 0.7], //아이콘 위치 조정 값 [x조정값, y조정값] ※size 옵션과 함께 사용 (기본값)[0.5,0.5]
          anchorOrigin:'bottom-right', //아이콘 위치 조정 기준점※(기본값)'bottom-right'
          anchorXUnits:'pixels', //아이콘 x 위치 설정 단위  ※anchor 값의 첫번째 요소의 단위('pixels' 또는 'fraction')
                                 //※ 'pixels' : 픽셀단위로 위치 조정. size의 절반 값이 중심 위치
                                 //※ 'fraction'(기본값) :  백분율로 위치조정. 0.5가 중심 위치
          anchorYUnits:'fraction', //아이콘 y 위치 설정 단위  ※anchor 값의 두번째 요소의 단위('pixels' 또는 'fraction'(기본값))
          color:'blue', //이미지 색상
          offset:[20,20], //offsetOrigin으로 부터 x축, y축 좌표위치 이동
          offsetOrigin:'top-left', //offset의 기준점
          opacity:0.5, //투명도
          scale:0.5, //크기를 정해진 값의 n배로 셋팅
          snapToPixel:false,  //true : sharp, false : blur
          rotateWithView:false, //지도가 회전할때 텍스트도 적절하게 회전할지 여부
          rotation:30*Math.PI/180, //시계방향으로 회전 (단위:라디안)
          size:[100, 100],  //이미지가 그려지는 도형 크기 ※ 그림을 그린 도화지의 크기
          src:'images/sample.png' //이미지 경로
        }
      }
    });
    //생성된 스타일을 JSON형태로 변환
    pointIconStyle.getJSON();
```
```javascript
    //선 스타일 생성 예제
    let lineStyle = odf.StyleFactory.produce({
      stroke : {
        color:'red',
        lineCap : 'butt',//선의 끝부분 모양('butt'(네모지게-선이 원래 길이보다 조금 일찍 끝남) / 'round' (둥글게) / 'square'(네모지게))
        lineJoin : 'miter',//('bevel' (꺾이는 부분을 지붕모양으로 )/ 'round' (둥글게)/ 'miter'(뾰족하게))
        //lineDash : [10],//점선의 간격 크기
        width:20
      },
      text :{
        text:'텍스트 내용',//텍스트 내용
        //offsetX : 0,//기준점으로부터 텍스트 x좌표 위치 이동
        //offsetY : 0,//기준점으로부터 텍스트 Y좌표 위치 이동
        //rotation : (Math.PI*270/180), //회전
        //textAlign : 'left',//텍스트 수평정렬
        //textBaseline : 'middle',//텍스트 수직정렬
        font : 'bold 20px Courier New',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
        fill : {color:'red'},
        stroke : {//text 안의 stroke는 width/lineCap/lineJoin/lineDash/lineDashOffset/miterLimit 옵션 적용  x
          color:'blue',
        },
        padding : [10,5,5,5],//text와 background영역 사이의 여백 //placement :'line' 일 경우 미적용
        backgroundStroke : {color:'black'},//placement :'line' 일경우 미적용
        backgroundFill : {color:'white'},//placement :'line' 일경우 미적용
        placement :'line',//텍스트를 나열하는 위치를 line을 따라 나타나게 할지, 특정 point에 나타나게 할지
        maxAngle : 90*Math.PI/180,//placement :'line' 일경우 적용
        overflow : false,//placement :'line' 일경우 적용//텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부
        scale : 2, //텍스트 크기를 정해진 값의 n배로 셋팅
        rotateWithView: true//지도가 회전할때 텍스트도 적절하게 회전할지 여부
      }
    });
    //생성된 스타일을 JSON형태로 변환
    lineStyle.getJSON();
 ```
 ```javascript
    //면 스타일 생성 예제
    let polygonStyle = odf.StyleFactory.produce({
      fill : {
        color:'gray',
      },
      stroke : {
        color:'red',
        lineCap : 'butt',//선의 끝부분 모양('butt'(네모지게-선이 원래 길이보다 조금 일찍 끝남) / 'round' (둥글게) / 'square'(네모지게))
        lineJoin : 'miter',//('bevel' (꺾이는 부분을 지붕모양으로 )/ 'round' (둥글게)/ 'miter'(뾰족하게))
        //lineDash : [10],//점선의 간격 크기
        width:20
      },
      text :{
        text:'텍스트 내용',//텍스트 내용
        //offsetX : 0,//기준점으로부터 텍스트 x좌표 위치 이동
        //offsetY : 0,//기준점으로부터 텍스트 Y좌표 위치 이동
        //rotation : (Math.PI*270/180), //회전
        //textAlign : 'left',//텍스트 수평정렬
        //textBaseline : 'middle',//텍스트 수직정렬
        font : 'bold 20px Courier New',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
        fill : {color:'red'},
        stroke : {//text 안의 stroke는 width/lineCap/lineJoin/lineDash/lineDashOffset/miterLimit 옵션 적용  x
          color:'blue',
        },
        padding : [10,5,5,5],//text와 background영역 사이의 여백 //placement :'line' 일 경우 미적용
        backgroundStroke : {color:'black'},//placement :'line' 일 경우 미적용
        backgroundFill : {color:'white'},//placement :'line' 일 경우 미적용
        placement :'point',//텍스트를 나열하는 위치를 line을 따라 나타나게 할지, 특정 point에 나타나게 할지
        maxAngle : 90*Math.PI/180,//placement :'line' 일경우 적용
        overflow : false,//placement :'line' 일 경우 적용//텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부
        scale : 2, //텍스트 크기를 정해진 값의 n배로 셋팅
        rotateWithView: true//지도가 회전할 때 텍스트도 적절하게 회전할지 여부
      }
    });
    //생성된 스타일을 JSON형태로 변환
    polygonStyle.getJSON();
 ```
 ```javascript
    //면(패턴채우기) 스타일 생성 예제
    let polygonStyle = odf.StyleFactory.produce({

      fillPattern : {
        //1. 기본제공 패턴 이용시 (pattern 또는 image  둘중 하나만 정의)
        // ★ 기본제공 패턴 종류 ★
        // 'hatch', 'cross', 'dot', 'circle', 'square', 'tile',
        // 'woven', 'crosses', 'caps', 'nylon', 'hexagon', 'cemetry',
        // 'sand', 'conglomerate', 'gravel', 'brick', 'dolomite', 'coal',
        // 'breccia', 'clay', 'flooded', 'chaos', 'grass', 'swamp', 'wave',
        // 'vine', 'forest', 'scrub', 'tree', 'pine', 'pines', 'rock', 'rocks'
        pattern : 'hatch',//기본제공 패턴 명 (odf.StyleFactory.getValidFillPatternList() 로 조회 가능)
        patternColor : 'blue', //패턴의 색상
        fill : { //패턴 배경색상
          color : 'yellow'
        },

        //2. 사용자 정의 이미지 이용시  (pattern 또는 image  둘중 하나만 정의)
        // image : {
        //   icon : {
        //     src : "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZCAYAAADE6YVjAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA2ZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo1RjI5MTdFNjgzMTlFQjExQTI4NUU2MEIzOEZDQzE2MyIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDo5MzNFMUU4MzE5ODcxMUVCQjRDOEQyNTdBRjg5QTZDOSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDo5MzNFMUU4MjE5ODcxMUVCQjRDOEQyNTdBRjg5QTZDOSIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M2IChXaW5kb3dzKSI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjY1MjkxN0U2ODMxOUVCMTFBMjg1RTYwQjM4RkNDMTYzIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjVGMjkxN0U2ODMxOUVCMTFBMjg1RTYwQjM4RkNDMTYzIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+1Tz04QAAAJtJREFUeNpi/P//PwMMTFi6mgEJHE4+sdSWgQzAO3kDCp+JgPr/JOLV2AxhYqAeeAPE2bS2JB+IX9HSks1AvAyXJDUseQ/EGfgUUMOSv0D8h9aWiADxdFpbAgJBQBxBa0tAYDIQi9HaElCwTcUmwUJAIyM1bMdryVyLaDi7IDqUbEuoGVyjloxaMmrJqCV4LAkEYjloPUExAAgwADiJIuQ99s0ZAAAAAElFTkSuQmCC"
        //   }
        // },

        //3. 기본제공 패턴 또는 사용자 정의 이미지 이용시 정의
        offset :16,//패턴 위치 이동 (오른쪽 아래 방향)
        scale :1.5,//패턴 크기
        size :10, //패턴 도형의 크기
        spacing :18,//패턴 도형간의 간격
        angle :0,//회전각
      },
      stroke : {
        color:'red',
        lineCap : 'butt',//선의 끝부분 모양('butt'(네모지게-선이 원래 길이보다 조금 일찍 끝남) / 'round' (둥글게) / 'square'(네모지게))
        lineJoin : 'miter',//('bevel' (꺾이는 부분을 지붕모양으로 )/ 'round' (둥글게)/ 'miter'(뾰족하게))
        //lineDash : [10],//점선의 간격 크기
        width:20
      },
    });
    //생성된 스타일을 JSON형태로 변환
    polygonStyle.getJSON();
 ```
 ```javascript
    //스타일 복합 속성 설정 배열 생성 예제
    let styleArray = odf.StyleFactory.produce([
      //스타일 1 : 다각형의 외곽선 스타일
      {
        stroke: {
          color: [132,229,252,0.95],
          lineCap: 'round', //선의 끝부분 모양('butt'(네모지게-선이 원래 길이보다 조금 일찍 끝남) / 'round' (둥글게) / 'square'(네모지게))
          lineJoin: 'round', //('bevel' (꺾이는 부분을 지붕모양으로 )/ 'round' (둥글게)/ 'miter'(뾰족하게))
          width: 5,
        },
      },
      //스타일 2 : 다각형의 무계중심점에 대한 포인트 스타일
      {
        geometry: function(feature) {
          let geometry = feature.getGeometry();
          return geometry.getInteriorPoints();
        },
        image : {
          circle : {
            fill : {
              color : '#CC70B4'
            },
            radius : 10,
            stroke:{
              color : '#CC70B4',
              width: 1
            },
          }
        }
      },
    ]);
    //생성된 스타일 배열을 JSON 형태로 변환
    styleArray.getJSON();
 ```
   ```javascript
    //파이차트 스타일 생성 예제
    var pointChartStyle = odf.StyleFactory.produce({
      image : {
        chart : {
          type : 'pie', //'pie','pie3D','donut','bar'
          data : [1,5,2,3],//고정값
          stroke : {
            color : '#ffffff', //테두리 색상
            width:2, //테두리 두께
          },
          radius : 50,//파이의 크기
          //파이차트에서 사용할 색상
          colors : 'pale' , // 'classic', 'dark', 'pale', 'pastel', 'neon'
          //아래와같이 직접 정의하여 사용할 수도 있음
          //colors :['#FF4B4B','#FF7272','#FF9999','#FFC0C0','#FFE7E7'],
          //rotation : Math.PI*90/180//기울기
        }
      },
    } );
    //생성된 스타일을 JSON형태로 변환
    pointChartStyle.getJSON();
```
   ```javascript
    //별모양 regularShape 스타일 생성 예제
    var starRegularShapeStyle = odf.StyleFactory.produce({
      image : {
        regularShape : {
          fill: {
              color: [0, 0, 0, 0.2]
              //채우기 색
          },//채우기
          stroke: {//윤곽선
              color: [132, 229, 252, 0.95],//테두리 색
              width: 2,//굵기
              //lineDash:[4, 1]//점선 설정
          },
          points: 5,
          radius: 10,
          radius2: 4,
          angle: 0
        },
    } );
    //생성된 스타일을 JSON형태로 변환
    starRegularShapeStyle.getJSON();
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: <code>odf.Style</code> - 파라미터로 넘어온 options의 값으로 스타일 객체 생성

| Param | Type | Description |
| --- | --- | --- |
| options | [<code>odf\_styleOption</code>](#odf_styleOption) \| [<code>Array.&lt;odf\_styleOption&gt;</code>](#odf_styleOption) \| <code>String</code> | 스타일 생성을 위한 option |

<a name="StyleFactory.produceSLD"></a>

### StyleFactory.produceSLD(options) ⇒ <code>SLD</code>
스타일 객체 생성
 ```javascript
//점 스타일 생성
let sld = odf.StyleFactory.produceSLD({
    rules: [
      {
        name: 'My Rule', //룰 이름
        //해당 룰 표현 범위
        scaleDenominator: {
          //min: 100001,
          max: 100001,
          },
          //해당 룰 적용 대상 한정
          //  ★ 기본 비교
          // - filter[0] : 비교연산자 ('==' , '!=' ,  '>' , '<' , '>=', '<=')
          // - filter[1] : 칼럼명
          // - filter[2] : 기준 값
          // ★ like 비교
          // - filter[0] : '*='
          // - filter[1] : 칼럼명
          // - filter[2] : 비교 문자열 (wildCard="*" singleChar="." escape="!")
          //          (ex 1) *_2  => [somthing] + '_2'
          //          (ex 2) *_.   => [somthing] + '_' +[어떤 문자이든 한개의 문자]
          //    ★ null 비교
          //   - filter[0] : 비교연산자 ('==' , '!=')
          //   - filter[1] : 칼럼명
          //   - filter[2] : null
          //    ★ 두개 이상의 조건
          //   - filter[0] : 논리연산자('&&','||')
          //   - filter[1] : 조건1
          //   - filter[2] : 조건2
          //   (ex) filter:['&&',['>=','id','3'],['!=',id,null]]
          filter: ['>', 'id', '20'], //[기본 비교][ , 칼럼명, 기준값]
          //filter: ['*=', 'type', '*_2'], //[like비교] wildCard="*" singleChar="." escape="!"
          //filter: ['!=', 'id', null], //[isnull비교]
          //filter:['&&',['>=','id','3'],['!=',id,null], ...]//[두개 이상의 조건]
        symbolizers: [
          {
            kind: 'Mark',
            // 포인트에 표현될 도형 종류
            // - 'circle' : 원
            // - 'square' : 사각형
            // - 'triangle' : 세모
            // - 'star' : 별 모양
            // - 'cross' :  + 모양
            // - 'x' : x 모양
            wellKnownName: 'Square',

            //포인트에 표현될 도형의 반지름
            radius: 6,
            //포인트에 표현될 도형의 채우기색
            color: '#FF0000',
            //포인트에 표현될 도형의 채우기색 투명도 0~1
            fillOpacity: 0.5,
            //포인트에 표현될 도형의 윤곽선 색
            strokeColor: '#0000FF',
            //포인트에 표현될 도형의 윤곽선 투명도 0~1
            strokeOpacity: 0.7,
            //포인트에 표현될 도형의 윤곽선 두께
            strokeWidth: 3,
            //offset 적용[x이동량,y이동량]
            offset: [0, 20], //pixel 단위로 이동
            //offset 적용 시킬 geometry 타입 칼럼명, 기본값=> 'the_geom'
            offsetGeometry: 'the_geom',
          },
        ],
        },
      ]
});
pointLayer.setSLD(sld);
 ```
 ```javascript
//심볼 스타일 생성
let sld = odf.StyleFactory.produceSLD({
        rules: [
          {
            //룰 이름
            name: 'My Rule',
            symbolizers: [
              {
                kind: 'Icon',
                //심볼 이미지 경로 ★geoserver가 접근할 수 있는 경로★
                image: '이미지경로',
                //투명도
                opacity: '0.8',
                //회전각(도)
                rotate: 0,
                //크기
                size: 30,
                //offset 적용[x이동량,y이동량]
                offset: [0, -2000], //pixel 단위로 이동
                //offset 적용 시킬 geometry 타입 칼럼명, 기본값=> 'the_geom'
                offsetGeometry: 'the_geom',
              },
            ],
          },
        ],
});
pointLayer.setSLD(sld);
 ```
 ```javascript
//선 스타일 생성
let sld = odf.StyleFactory.produceSLD({
        rules: [
          {
            //룰 이름
            name: 'My Rule',
            symbolizers: [
              {
                kind: 'Line',
                //라인 색상
                color: '#338866',
                // 라인의 끝 표현 방식
                //   - 'butt' : (Default) sharp square edge 끝부분을 수직으로 절단
                //   - 'round' : rounded edge 끝부분이 둥근 모양
                //   - 'square' :  slightly elongated square edge 끝부분에 사각형 추가
                cap: 'round',
                // 라인이 꺽이는 부분 표현 방식
                // - 'miter' : (Default) sharp corner 코너가 뾰족    /＼
                // - 'round' : rounded corner 코너가 동글동글
                // - 'bevel' :  diagonal corner 코너의 끝이 잘림 /￣＼
                join: 'round',
                //투명도 0~1
                opacity: 0.7,
                //두께
                width: 3,
                //대시 간격 조절
                dasharray: [16, 10],
                //선의 시작점에서 얼마나 떨어진 곳에서부터 점선을 표시할지
                dashOffset: 16,
              },
            ],
          },
        ],
});
lineLayer.setSLD(sld);
 ```
 ```javascript
//면 스타일 생성
let sld = odf.StyleFactory.produceSLD({
        rules: [
          { //룰 이름
            name: 'My Rule',
            symbolizers: [
              {
                  kind: 'Fill',
                  //채우기색
                  color: '#AAAAAA',
                  //채우기 투명도 0~1
                  fillOpacity: 0.5,
                  //윤곽선색
                  outlineColor: '#338866',
                  //윤곽선 두께
                  outlineWidth: 3,
                  //윤곽선 투명도 0~1
                  outlineOpacity: 1,
                  //윤곽선 대쉬 간격
                  outlineDasharray: [16, 10],
              },
            ],
          },
        ],
});
polygonLayer.setSLD(sld);
 ```
 ```javascript
//면(패턴채우기) 스타일 생성
let sld = odf.StyleFactory.produceSLD({
        rules: [
          { //룰 이름
            name: 'My Rule',
            symbolizers: [
              {
                  kind: 'Fill',
                  //패턴 채우기 이미지
                 graphicFill:{
                   //이미지
                    image: 'http://**************:13002/geoserver/web/wicket/resource/org.geoserver.web.AboutGeoServerPage/img/icons/silk/help-ver-C3812C74BC524179F4CCF5D2DB7B3CBF.png',
                    kind: "Icon",
                    //패턴 크기
                    size: 20,
                    //회전 각도
                    rotate : 90,
                  },
                  //채우기 투명도 0~1
                  fillOpacity: 0.5,
                  //윤곽선색
                  outlineColor: '#338866',
                  //윤곽선 두께
                  outlineWidth: 3,
                  //윤곽선 투명도 0~1
                  outlineOpacity: 1,
                  //윤곽선 대쉬 간격
                  outlineDasharray: [16, 10],
              },
            ],
          },
        ],
});
polygonLayer.setSLD(sld);
 ```
 ```javascript
//라벨 스타일 생성
let sld = odf.StyleFactory.produceSLD({
        rules: [
          {
            //룰 이름
            name: 'My Rule',
            symbolizers: [
              {
                  kind: 'Text',
                  //사용할 폰트
                  font: ['Times'],
                  // 라벨 모양
                  // 	-'normal' : 기본
                  // 	-'italic' : 이탤릭체 - italic체로 디자인된 폰트를 사용
                  // 	-'oblique' : 기본 글씨체를 비스듬하게 보여줌
                  fontStyle: 'normal',
                  // 라벨 두께
                  // -'normal' : 기본
                  // -'bold' : 굵게
                  fontWeight: 'normal',
                  //라벨 텍스트
                  //	{{칼럼명}} => 해당 칼럼 값
                  label: '{{id}} {{저녁인}}',
                  //라벨 크기
                  size: 15,
                  //후광 색상
                  haloColor: '#ffffff',
                  //후광 두께
                  haloWidth: 5,

                  //★★라벨표현방식이 'LinePlacement'일경우 적용 속성 start★★
                    // 레이블이 선의 곡선을 따르도록 할지 여부
                    //   - true : 레이블이 선의 곡선을 따르도록
                    //   - false : 레이블이 선의 곡선을 따르지 않게
                    //followLine: true,
                    //레이블 반복 간격 조절
                    //   - 0 : 라벨 반복 x
                    //	 - 양수 값 :  라인에 따라 라벨을 표시하는 빈도 조정. 값이 클수록 띄엄띄엄 나타남
                    //repeat: 20,
                    //선을 따라 레이어의 변위를 제어. repeat 속성과 함께 사용할 경우, repeat속성보다 작은 값을 설정
                    // maxDisplacement: 15,
                  //★★라벨표현방식이 'LinePlacement'일경우 적용 속성 end★★

               //라벨 표현 방식
                LabelPlacement: [
                  {
                    //라벨표현방식- 기준점에 표현
                     PointPlacement: [
                       {
                         //기준점을 기준으로 라벨이 배치되는 위치 결정
                         AnchorPoint: [{
                            // 라벨이 x 기준점의 어느부분에 표시되는지(0~1)
                            //    - 0(default) :left
                            //    - 0.5 :center
                            //    - 1 : right
                           AnchorPointX: ['0.5']
                            //  라벨이 y 기준점의 어느부분에 표시되는지(0~1)
                            //   - 0(default) :bottom
                            //   - 0.5 :center
                            //   - 1 : top
                            , AnchorPointY: ['0.5']
                         }],
                         //라벨 기준점 좌표 이동(단위:pixel)
                         Displacement: [{
                           //라벨 x 기준점 좌표 이동량 (+ : 왼쪽,- : 오른쪽)
                           DisplacementX: ['0.5']
                           //라벨 y 기준점 좌표 이동량 (+ : 위,- : 아래)
                            , DisplacementY: ['0.5']
                         }],
                         //라벨 회전 각도
                         Rotation: ['0'],
                       },
                    ],
                   // // 라벨표현방식- 선을 따라서 표현
                   // LinePlacement: [
                   //  {
                   //     //라벨이 라인의 위에 위치할지(+), 아래에 위치할지(-)
                   //     PerpendicularOffset: ['10'],
                   //   },
                   //],
                  },
                ],
              },
            ],
          },
        ],
});
pointLayer.setSLD(sld);
 ```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: <code>SLD</code> - 파라미터로 넘어온 options의 값으로 스타일 객체 생성

| Param | Type | Description |
| --- | --- | --- |
| options | [<code>odf\_sld\_option</code>](#odf_sld_option) | 스타일 생성을 위한 option |

<a name="StyleFactory.cvtSLD2Object"></a>

### StyleFactory.cvtSLD2Object(sld) ⇒ <code>Promise</code>
SLD 형식 문자열 -> SLD 객체 생성 옵션

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: <code>Promise</code> - 변환된 SLD 생성 object를 Promise Value로 갖는 Promise

| Param | Type | Description |
| --- | --- | --- |
| sld | <code>String</code> | SLD 형식 문자열 |

<a name="StyleFactory.produceElement"></a>

### StyleFactory.produceElement(options, size, labelFlag) ⇒ <code>HTMLElement</code>
스타일 옵션으로 범례용 element 생성

```javascript
odf.StyleFactory.produceElement({
    image : {
      circle : {
                radius:25,//크기
                fill:{
                  color:[0,0,0,0.2]//채우기 색
                },//채우기
                stroke: {//윤곽선
                  color:[132,229,252,0.95],//테두리 색
                  width:10,//굵기
                  //lineDash:[4, 1]//점선 설정
                },
               // snapToPixel : true //true : sharp, false : blur
            }
    },
         text :{
            text:'욥',//텍스트 내용
            //offsetX : 0,//기준점으로부터 텍스트 x좌표 위치 이동
            //offsetY : 0,//기준점으로부터 텍스트 Y좌표 위치 이동
            //rotation : (Math.PI*270/180), //회전
            //textAlign : 'left',//텍스트 수평정렬
            //textBaseline : 'middle',//텍스트 수직정렬
            font : 'bold 14px Courier New',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
            fill : {color:[0,0,0,0.95]},
            stroke : {//text 안의 stroke는 width/lineCap/lineJoin/lineDash/lineDashOffset/miterLimit 옵션 적용  x
                color:[255,255,255,0.8],
            },
            padding : [0.5,0.5,0.5,0.5],//text와 background영역 사이의 여백 //placement :'line' 일 경우 미적용
            backgroundStroke : {color:'black'},//placement :'line' 일경우 미적용
            backgroundFill : {color:'white'},//placement :'line' 일경우 미적용
            //maxAngle : 90*Math.PI/180,//placement :'line' 일경우 적용
            //overflow : false,//placement :'line' 일경우 적용//텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부
            scale : 1, //텍스트 크기를 정해진 값의 n배로 셋팅
            rotateWithView: true//지도가 회전할때 텍스트도 적절하게 회전할지 여부
        }

  },20,true);
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)

| Param | Type | Description |
| --- | --- | --- |
| options | [<code>odf\_styleOption</code>](#odf_styleOption) \| <code>String</code> | 스타일 생성 옵션(Object/JSON String) |
| size | <code>Number</code> | element 크기(width,height등에 사용). default 10 최소 0 최대 2000 |
| labelFlag | <code>Boolean</code> | 라벨 스타일 적용 여부. true=> 적용, false=>미적용. default false |

<a name="StyleFactory.produceFunction"></a>

### StyleFactory.produceFunction(options, mode) ⇒ <code>function</code>
스타일 Function 생성
```javascript
   //resolution 값을 기준으로 윤곽선 색 변경
   odf.StyleFactory.produceFunction([
     //기본스타일
     {
       seperatorFunc :"default",
       style :{
         fill:{color:[0,0,0,0.5]},//채우기
         stroke: {color:'red',width:3,},//윤곽선
        },
      },
      //resolution 값이 2보다 클때 적용되는 스타일
      {
        seperatorFunc :function(feature,resolution){
          return resolution>2;
        },
        style :{
          fill:{color:[0,0,0,0.5]},//채우기
          stroke: {color:'gray',width:3,},//윤곽선
        },
        priority : 2,
      },
      //resolution 값이 4보다 클때 적용되는 스타일
      {
        seperatorFunc :function(feature,resolution){
          return resolution>4;},
          style :{
            fill:{color:[0,0,0,0.5]},//채우기
            stroke: {color:'blue',width:3,},//윤곽선
          },
          priority : 1,
        }]);
```
```javascript
  //피쳐별로 텍스트 값 다르게 적용
      odf.StyleFactory.produceFunction([
        {
          seperatorFunc :"default",
          style :{
            fill:{color:[0,0,0,0.5]},//채우기
            stroke: {color:'red',width:3,},//윤곽선
            text : {font : 'bold 14px Courier New',fill : {color:'red'},},//텍스트
          },
          callbackFunc : function(style,feature,resolution){
            style.getText().setText(feature.getProperties().속성명);
          },
        }
      ])
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: <code>function</code> - 파라미터로 넘어온 options의 값으로 스타일 함수 생성

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| options | [<code>Array.&lt;odf\_StyleFunctionOption&gt;</code>](#odf_StyleFunctionOption) \| <code>String</code> |  | 스타일 Function 생성을 위한 옵션 배열 |
| mode | <code>String</code> | <code>normal</code> | 'fast'모드 스타일 사용여부 - 'fast' : 스타일 적용 속도 빠름 - 'normal' : 스타일 적용 속도 느림(default) |

<a name="StyleFactory.cvtStyle2Json"></a>

### StyleFactory.cvtStyle2Json(style) ⇒ <code>String</code>
EXTStyle 또는 Style을 JSON 형태로 변환
```javascript
  //생성된 스타일 function을 JSON으로 변경
   let style = odf.StyleFactory.produce({
        fill:{color:[0,0,0,0.5]},//채우기
        stroke: {color:'red',width:3,},//윤곽선
    });
    odf.StyleFactory.cvtStyle2Json(style);
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: <code>String</code> - JSON형태로 변환된 스타일 생성옵션

| Param | Type | Description |
| --- | --- | --- |
| style | <code>odf.Style</code> \| <code>odf.ExtStyle</code> \| <code>function</code> | 변환 대상 style 또는 styleFunction |

<a name="StyleFactory.cvtStyle2Object"></a>

### StyleFactory.cvtStyle2Object(style) ⇒ [<code>Array.&lt;odf\_styleOption&gt;</code>](#odf_styleOption) \| [<code>odf\_styleOption</code>](#odf_styleOption) \| [<code>Array.&lt;odf\_StyleFunctionOption&gt;</code>](#odf_StyleFunctionOption)
EXTStyle 또는 Style을 Object 형태로 변환
```javascript
  //생성된 스타일 function을 JSON으로 변경
   let style = odf.StyleFactory.produce({
        fill:{color:[0,0,0,0.5]},//채우기
        stroke: {color:'red',width:3,},//윤곽선
    });
    odf.StyleFactory.cvtStyle2Object(style);
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: [<code>Array.&lt;odf\_styleOption&gt;</code>](#odf_styleOption) \| [<code>odf\_styleOption</code>](#odf_styleOption) \| [<code>Array.&lt;odf\_StyleFunctionOption&gt;</code>](#odf_StyleFunctionOption) - Object 형태로 변환된 스타일 생성옵션

| Param | Type | Description |
| --- | --- | --- |
| style | <code>odf.Style</code> \| <code>odf.ExtStyle</code> \| <code>function</code> | 변환 대상 style 또는 FunctionStyle |

<a name="StyleFactory.getValidFillPatternList"></a>

### StyleFactory.getValidFillPatternList() ⇒ [<code>Array.&lt;odf\_default\_pattern\_define&gt;</code>](#odf_default_pattern_define)
기본으로 제공하는 패턴명과 이미지 조회
```javascript
      //기본으로 제공하는 패턴명과 이미지 조회
      odf.StyleFactory.getValidFillPatternList();
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)
**Returns**: [<code>Array.&lt;odf\_default\_pattern\_define&gt;</code>](#odf_default_pattern_define) - Object 형태로 변환된 스타일 생성옵션
<a name="StyleFactory.addPattern"></a>

### StyleFactory.addPattern(title, options)
사용자 정의 패턴 추가
```javascript
      //사용자 정의 패턴 추가
      odf.StyleFactory.addPattern ("copy (char pattern)", { char:"©" });
      odf.StyleFactory.addPattern ("bug (fontawesome)", { char:'\uf188', size:12, font:"10px FontAwesome" });
      odf.StyleFactory.addPattern ("smiley (width angle)", { char:'\uf118', size:20, angle:true, font:"15px FontAwesome" });
```

**Kind**: static method of [<code>StyleFactory</code>](#StyleFactory)

| Param | Type | Description |
| --- | --- | --- |
| title | <code>String</code> | 추가할 패턴의 이름 |
| options | <code>Object</code> | 추가할 패턴 옵션 |
| options.char | <code>String</code> | 문자열 패턴 (ex) "©" , '\uf188', '\uf118' |
| options.size | <code>Number</code> | 패턴의 크기 |
| options.font | <code>String</code> | 폰트 크기와 폰트 (ex) "10px FontAwesome" |
| options.angle | <code>String</code> | 회전각 |

<a name="odf_sld_option"></a>

## odf\_sld\_option : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| rules | [<code>Array.&lt;odf\_sld\_rule&gt;</code>](#odf_sld_rule) | 적용 rule 배열  (table) |

<a name="odf_sld_rule"></a>

## odf\_sld\_rule : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| scaleDenominator | <code>Object</code> | 스타일 적용 시킬 최대/최소 축척 제한 (table) |
| scaleDenominator.min | <code>Number</code> | 적용 가능 최소 축척 |
| scaleDenominator.max | <code>Number</code> | 적용 가능 최대 축척 |
| filter | <code>Array</code> | 스타일 적용 대상 도형 제한 ★ 기본 비교 - filter[0] : 비교연산자 ('==' , '!=' ,  '>' , '<' , '>=', '<=') - filter[1] : 칼럼명 - filter[2] : 기준 값 ★ like 비교 - filter[0] : '*=' - filter[1] : 칼럼명 - filter[2] : 비교 문자열 (wildCard="*" singleChar="." escape="!")                   (ex 1) *_2  => [somthing] + '_2'                   (ex 2) *_.   => [somthing] + '_' +[어떤 문자이든 한개의 문자] ★ null 비교   - filter[0] : 비교연산자 ('==' , '!=')   - filter[1] : 칼럼명   - filter[2] : null ★ 두개 이상의 조건   - filter[0] : 논리연산자('&&','||')   - filter[1] : 조건1   - filter[2] : 조건2   (ex) filter:['&&',['>=','id','3'],['!=',id,null]] |
| symbolizers | <code>Array.&lt;odf\_sld\_symbolizer&gt;</code> | 적용 스타일 설정 |
| symbolizers[0]; | [<code>odf\_sld\_mark\_symbolizer</code>](#odf_sld_mark_symbolizer) | 점 스타일 옵션 |
| symbolizers[1]; | [<code>odf\_sld\_icon\_symbolizer</code>](#odf_sld_icon_symbolizer) | 심볼 스타일 옵션 |
| symbolizers[2]; | [<code>odf\_sld\_line\_symbolizer</code>](#odf_sld_line_symbolizer) | 선 스타일 옵션 |
| symbolizers[3]; | [<code>odf\_sld\_fill\_symbolizer</code>](#odf_sld_fill_symbolizer) | 면 스타일 옵션 |
| symbolizers[4]; | [<code>odf\_sld\_text\_symbolizer</code>](#odf_sld_text_symbolizer) | 라벨 스타일 옵션 |

<a name="odf_sld_mark_symbolizer"></a>

## odf\_sld\_mark\_symbolizer : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| kind | <code>String</code> | 'Mark' 점 스타일 (table) |
| wellKnownName | <code>String</code> | 포인트에 표현될 도형 종류 |
| wellKnownName.circle | <code>String</code> | 원 |
| wellKnownName.square | <code>String</code> | 네모 |
| wellKnownName.triangle | <code>String</code> | 세모 |
| wellKnownName.star | <code>String</code> | 별 모양 |
| wellKnownName.cross | <code>String</code> | '+' 모양 |
| wellKnownName.x | <code>String</code> | 'x' 모양 |
| radius | <code>Number</code> | 포인트에 표현될 도형의 반지름 |
| color | <code>String</code> | 포인트에 표현될 도형의 채우기색 (ex)'#FF0000' |
| fillOpacity | <code>Number</code> | 포인트에 표현될 도형의 채우기색 투명도 0~1 |
| strokeColor | <code>Number</code> | 포인트에 표현될 도형의 윤곽선 색 |
| strokeOpacity | <code>Number</code> | 포인트에 표현될 도형의 윤곽선 투명도 0~1 |
| strokeWidth | <code>Number</code> | 포인트에 표현될 도형의 윤곽선 두께 |
| offset | <code>Array.&lt;Number&gt;</code> | 기준 좌표의 x/y축 이동량 (단위 : pixel), [x이동량,y이동량] |
| offsetGeometry | <code>String</code> | 기준되는 geometry 타입의 컬럼명 ※기본값 : 'the_geom' |

<a name="odf_sld_icon_symbolizer"></a>

## odf\_sld\_icon\_symbolizer : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| kind | <code>String</code> | 'Icon' 심볼 스타일 (table) |
| image | <code>String</code> | 심볼 이미지 경로 ※ geoserver가 접근할 수 있는 경로 |
| opacity | <code>Number</code> | 심볼 이미지 투명도 0~1 |
| rotate | <code>Number</code> | 심볼 이미지 회전각(도) |
| size | <code>Number</code> | 심볼 이미지 크기 |
| offset | <code>Array.&lt;Number&gt;</code> | 기준 좌표의 x/y축 이동량 (단위 : pixel), [x이동량,y이동량] |
| offsetGeometry | <code>String</code> | 기준되는 geometry 타입의 컬럼명 ※기본값 : 'the_geom' |

<a name="odf_sld_line_symbolizer"></a>

## odf\_sld\_line\_symbolizer : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| kind | <code>String</code> | 'Line' 라인 스타일 (table) |
| color | <code>String</code> | 라인 색상 (ex)'#338866' |
| cap | <code>String</code> | 라인의 끝 표현 방식 |
| cap.butt | <code>String</code> | (Default) sharp square edge 끝부분을 수직으로 절단 |
| cap.round | <code>String</code> | rounded edge 끝부분이 둥근 모양 |
| cap.square | <code>String</code> | slightly elongated square edge 끝부분에 사각형 추가 |
| join | <code>String</code> | 라인이 꺽이는 부분 표현 방식 |
| join.miter | <code>String</code> | (Default) sharp corner 코너가 뾰족    /＼ |
| join.round | <code>String</code> | rounded corner 코너가 동글동글 |
| size.bevel | <code>String</code> | diagonal corner 코너의 끝이 잘림 /￣＼ |
| opacity | <code>Number</code> | 라인의 투명도 0~1 |
| width | <code>Number</code> | 라인의 두께 |
| dasharray | <code>Array.&lt;Number&gt;</code> | 대시 간격 조절. 선의 길이 대비 공백의 길이 표현 (ex) [16,10] |
| dashOffset | <code>Number</code> | 선의 시작점에서 얼마나 떨어진 곳에서부터 점선을 표시할지. dasharray의 첫번째 요소보다 같거나 작은값 |

<a name="odf_sld_fill_symbolizer"></a>

## odf\_sld\_fill\_symbolizer : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| kind | <code>String</code> | 'Fill' 다각형 스타일 (table) |
| color | <code>String</code> | 채우기 색상 (ex)'#338866' |
| fillOpacity | <code>Number</code> | 채우기색 투명도 0~1 |
| outlineColor | <code>String</code> | 윤곽선 색상 (ex)'#338866' |
| outlineWidth | <code>Number</code> | 윤곽선 두께 |
| outlineOpacity | <code>Number</code> | 윤곽선 투명도 0~1 |
| outlineDasharray | <code>Array.&lt;Number&gt;</code> | 윤곽선 대시 간격 조절. 선의 길이 대비 공백의 길이 표현 (ex) [16,10] |

<a name="odf_sld_text_symbolizer"></a>

## odf\_sld\_text\_symbolizer : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| kind | <code>String</code> | 'Text' 라벨 스타일 (table) |
| font | <code>Array.&lt;String&gt;</code> | 라벨 폰트 (ex) ['Times'] |
| fontStyle | <code>String</code> | 라벨 모양 |
| fontStyle.normal | <code>String</code> | 기본 |
| fontStyle.italic | <code>String</code> | 이탤릭체(italic체로 디자인된 폰트) |
| fontStyle.oblique | <code>String</code> | 기본 글씨체를 비스듬하게 기울여 적용 |
| fontWeight | <code>String</code> | 라벨 두께 |
| fontWeight.normal | <code>String</code> | 기본 |
| fontWeight.bold | <code>String</code> | 굵게 |
| label | <code>String</code> | 표현할 text  (ex) '{{id}} {{name}}', |
| size | <code>Number</code> | 라벨 크기 |
| haloColor | <code>String</code> | 후광 색상 (ex) '#ffffff' |
| haloWidth | <code>Number</code> | 후광 두께 |
| overflow | <code>Boolean</code> | 오버플로우 여부(라벨이 도형 영역을 넘어서는 경우에도 표현 할지 여부)(POLYGON 타입 적용) |
| followLine | <code>Boolean</code> | 레이블이 선의 곡선을 따르도록 할지 여부 ※LabelPlacement 값이 LinePlacement일 경우, 적용 |
| followLine.true | <code>Boolean</code> | 레이블이 선의 곡선을 따르도록 설정 |
| followLine.false | <code>Boolean</code> | 레이블이 선의 곡선을 따르지 않도록 설정 |
| repeat | <code>Number</code> | 레이블 반복 간격 조절 - 0 : 라벨 반복 x - 양수 값 :  라인에 따라 라벨을 표시하는 빈도 조정. 값이 클수록 띄엄띄엄 나타남 ※LabelPlacement 값이 LinePlacement일 경우, 적용 |
| maxDisplacement | <code>Number</code> | 선을 따라 레이어의 변위를 제어. repeat 속성과 함께 사용할 경우, repeat속성보다 작은 값을 설정 ※LabelPlacement 값이 LinePlacement일 경우, 적용 |
| LabelPlacement | <code>Array.&lt;(odf\_sld\_point\_label\|odf\_sld\_line\_label)&gt;</code> | 라벨 표현 방식 |

<a name="odf_sld_point_label"></a>

## odf\_sld\_point\_label : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| PointPlacement | [<code>Array.&lt;odf\_sld\_point\_label\_inner&gt;</code>](#odf_sld_point_label_inner) | 라벨표현방식- 기준점에 표현  (table) |

<a name="odf_sld_point_label_inner"></a>

## odf\_sld\_point\_label\_inner : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| AnchorPoint | [<code>Array.&lt;odf\_sld\_point\_label\_inner\_anchorpoint&gt;</code>](#odf_sld_point_label_inner_anchorpoint) | 기준점을 기준으로 라벨이 배치되는 위치 결정  (table) |
| Displacement | [<code>Array.&lt;odf\_sld\_point\_label\_inner\_displacement&gt;</code>](#odf_sld_point_label_inner_displacement) | 라벨 기준점 좌표 이동(단위:pixel) |
| Rotation | <code>Array.&lt;Number&gt;</code> | 라벨 회전 각도 |

<a name="odf_sld_point_label_inner_anchorpoint"></a>

## odf\_sld\_point\_label\_inner\_anchorpoint : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| AnchorPointX | <code>Array.&lt;Number&gt;</code> | 라벨이 x 기준점의 어느부분에 표시되는지(0~1) AnchorPointX : [0.5]  (table) - 0(default) :left - 0.5 :center - 1 : right |
| AnchorPointY | <code>Array.&lt;Number&gt;</code> | 라벨이 y 기준점의 어느부분에 표시되는지(0~1) (ex)[0.5] - 0(default) :bottom - 0.5 :center - 1 : top |

<a name="odf_sld_point_label_inner_displacement"></a>

## odf\_sld\_point\_label\_inner\_displacement : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| DisplacementX | <code>Array.&lt;Number&gt;</code> | 라벨 x 기준점 좌표 이동량  (table)  - 양수 : 왼쪽 방향 이동  - 음수 : 오른쪽 방향 이동 |
| DisplacementY | <code>Array.&lt;Number&gt;</code> | 라벨 y 기준점 좌표 이동량  - 양수: 위쪽 방향 이동  - 음수 : 아래쪽 방향 이동 |

<a name="odf_sld_line_label"></a>

## odf\_sld\_line\_label : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| LinePlacement | [<code>Array.&lt;odf\_sld\_line\_label\_inner&gt;</code>](#odf_sld_line_label_inner) | 라벨표현방식- 선을 따라서 표현  (table) |

<a name="odf_sld_line_label_inner"></a>

## odf\_sld\_line\_label\_inner : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| PerpendicularOffset | <code>Array.&lt;Number&gt;</code> | 라인 기준 상단에 위치할지 하단에 위치할지  (table) - 양수 : 라벨이 라인의 위에 위치 - 음수 : 라벨이 라인의 아래에 위치 (ex) [10] |

<a name="odf_styleOption_stroke"></a>

## odf\_styleOption\_stroke : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| color | <code>String</code> \| <code>Array.&lt;Number&gt;</code> | 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5] |
| lineCap | <code>String</code> | 선의 끝 모양 |
| lineJoin | <code>String</code> | 선이 꺾이는 부분의 모양 |
| lineDash | <code>Array.&lt;Number&gt;</code> | 점선 |
| lineDashOffset | <code>Number</code> | 윤곽선 Line dash offset. |
| miterLimit | <code>Number</code> | 윤곽선 Miter limit. |
| width | <code>Number</code> | 너비 |

<a name="odf_StyleOption_fill"></a>

## odf\_StyleOption\_fill : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| color | <code>String</code> \| <code>Array.&lt;Number&gt;</code> | 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5] |

<a name="odf_StyleOption_fillPattern_pattern"></a>

## odf\_StyleOption\_fillPattern\_pattern : <code>String</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| hatch | <code>String</code> | 해치(스트라이프) |
| cross | <code>String</code> | 십자형(체크) |
| dot | <code>String</code> | 점(채워진 원) |
| circle | <code>String</code> | 원(빈 원) |
| square | <code>String</code> | 정사각형(빈 사각형) |
| tile | <code>String</code> | 타일(채워진 사각형) |
| woven | <code>String</code> | 직물(─│─│ 모양) |
| crosses | <code>String</code> | 십자가(x 모양) |
| caps | <code>String</code> | 모자(^ 모양) |
| nylon | <code>String</code> | 나일론(┌ ┘ 모양) |
| hexagon | <code>String</code> | 육각형 |
| cemetry | <code>String</code> | 십자가 모양 |
| sand | <code>String</code> | 모래(불규칙한 사각형 점) |
| conglomerate | <code>String</code> | 역암(블규칙 도형) |
| gravel | <code>String</code> | 자갈(블규칙 도형) |
| brick | <code>String</code> | 벽돌 |
| dolomite | <code>String</code> | 백운석(비스듬한 벽돌 모양) |
| coal | <code>String</code> | 석탄(채워진 삼각형) |
| breccia | <code>String</code> | 각력암(빈 삼각형) |
| clay | <code>String</code> | 점토 |
| flooded | <code>String</code> | 침수(─_─_) |
| chaos | <code>String</code> | 혼돈(기하학무늬) |
| grass | <code>String</code> | 잔디 |
| swamp | <code>String</code> | 늪 |
| wave | <code>String</code> | 물결(^^^^) |
| vine | <code>String</code> | 덩굴(│) |
| forest | <code>String</code> | 숲(작은원,큰원) |
| scrub | <code>String</code> | 스크럽(v,o) |
| tree | <code>String</code> | 나무 |
| pine | <code>String</code> | 소나무1 |
| pines | <code>String</code> | 소나무2 |
| rock | <code>String</code> | 돌1 |
| rocks | <code>String</code> | 돌2 |

<a name="odf_StyleOption_fillPattern"></a>

## odf\_StyleOption\_fillPattern : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| image | <code>Object</code> | 패턴 이미지 |
| image.icon | <code>Object</code> | 패턴 이미지 아이콘 |
| opacity | <code>Number</code> | 패턴 투명도 |
| pattern | [<code>odf\_StyleOption\_fillPattern\_pattern</code>](#odf_StyleOption_fillPattern_pattern) | 패턴명 |
| patternColor | <code>String</code> \| <code>Array.&lt;Number&gt;</code> | 패턴색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5] |
| fill | [<code>odf\_StyleOption\_fill</code>](#odf_StyleOption_fill) | 패턴 배경 색상 |
| offset | <code>Number</code> | 패턴 위치 이동 (오른쪽 아래 방향), hash/dot/circle/cross pattern 패턴에서 사용 |
| size | <code>Number</code> | 도형의 크기, hash/dot/circle/cross pattern 패턴에서 사용 |
| spacing | <code>Number</code> | 도형간의 간격, hash/dot/circle/cross pattern 패턴에서 사용 |
| angle | <code>Boolean</code> \| <code>Number</code> | 회전각 - true :45도 - false : 0도 - [Number] : 입력한 각도 |
| scale | <code>Number</code> | 패턴 크기 |

<a name="odf_StyleOption_text"></a>

## odf\_StyleOption\_text : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| text | <code>String</code> | 텍스트 값 |
| textAlign | <code>String</code> | 텍스트 수직정렬 |
| textBaseline | <code>String</code> | 텍스트 수평정렬 |
| font | <code>String</code> | 폰트 크기(필수) 및 글씨체(필수), 두께(옵션) (ex) 'bold 10px sans-serif' |
| placement | <code>String</code> | 텍스트를 타나낼 위치를 점 기준으로 할지, 피쳐의 모양에 따라 나타나게 할지 여부.'line' 또는 'point'(default) |
| overflow | <code>Boolean</code> | 텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부(placement 속성이 'line'일때 적용) |
| maxAngle | <code>Number</code> | 텍스트가 꺾이는 정도를 제한(placement 속성이 'line'일때 적용) (단위:라디안) (ex)Math.PI*270/180 |
| offsetX | <code>Number</code> | 텍스트의 수평 이동(양수일 때 오른쪽으로 이동) |
| offsetY | <code>Number</code> | 텍스트의 수직 이동(양수일 때 아래로 이동) |
| scale | <code>Number</code> | 텍스트 크기를 정해진 값의 n배로 적용 |
| rotateWithView | <code>Boolean</code> | 지도가 회전할때 텍스트도 적절하게 회전할지 여부 |
| rotation | <code>Number</code> | 텍스트 회전 각도(단위:라디안) (ex)Math.PI*270/180 |
| justify | <code>String</code> | 텍스트 상자 내의 텍스트 맞춤 - 'left', 'center', 'right' |
| fill | [<code>odf\_StyleOption\_fill</code>](#odf_StyleOption_fill) | 텍스트 채우기 스타일 옵션 |
| stroke | <code>odf\_StyleOption\_stroke</code> | 텍스트 테두리 스타일 옵션 |
| backgroundFill | [<code>odf\_StyleOption\_fill</code>](#odf_StyleOption_fill) | 텍스트 배경 채우기 스타일 속성 |
| backgroundStroke | <code>odf\_StyleOption\_stroke</code> | 택스트 배경 테두리 스타일 속성 |
| padding | <code>Array.&lt;Number&gt;</code> | text와 background영역 사이의 여백(placement 속성이 'point'일때 적용) (ex)[10,5,5,5] |

<a name="odf_styleOption_icon"></a>

## odf\_styleOption\_icon : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| anchor | <code>Array.&lt;Number&gt;</code> | 아이콘 위치 조정 (ex) [70, 0.7] |
| anchorOrigin | <code>String</code> | 기준점으로부터의 아이콘 위치. (default)'top-left' 또는 'top-right' 또는 'bottom-left' 또는 'bottom-right' 값 중 하나 |
| anchorXUnits | <code>String</code> | 아이콘 x축 위치 조정 단위.(default)'fraction' 또는 'pixels' |
| anchorYUnits | <code>String</code> | 아이콘 y축 위치 조정 단위.(default)'fraction' 또는 'pixels' |
| color | <code>String</code> \| <code>Array.&lt;Number&gt;</code> | 이미지 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5] |
| crossOrigin | <code>String</code> | CORS 관련 셋팅. (default)'anonymous' |
| offset | <code>Array.&lt;Number&gt;</code> | x축 y축 좌표위치 이동 |
| offsetOrigin | <code>&#x27;bottom-left&#x27;</code> \| <code>&#x27;bottom-right&#x27;</code> \| <code>&#x27;top-left&#x27;</code> \| <code>&#x27;top-right&#x27;</code> | 오프셋 원점 |
| opacity | <code>Number</code> | 투명도 |
| scale | <code>Number</code> \| <code>Array.&lt;Number&gt;</code> | 크기를 정해진 값의 n배로 셋팅 |
| snapToPixel | <code>Boolean</code> | true(sharp) 또는 false(blur) |
| rotateWithView | <code>Boolean</code> | 지도가 회전할 때 이미지도 적절하게 회전할지 여부 |
| rotation | <code>Number</code> | 회전 (ex) 30*Math.PI/180 |
| size | <code>Array.&lt;Number&gt;</code> | 자연수 배열 (단위 : 픽셀) |
| imgSize | <code>Array.&lt;Number&gt;</code> | 자연수 배열 (단위 : 픽셀) |
| src | <code>String</code> | 이미지 경로 |
| img | <code>HTMLImageElement</code> \| <code>HTMLCanvasElement</code> \| <code>ImageBitmap</code> | 아이콘의 이미지 객체 |
| displacement | <code>Array.&lt;Number&gt;</code> | 위치이동. 양수 값은 모양을 오른쪽 및 위쪽으로 이동 (기본값 [0,0]) |
| width | <code>Number</code> | 아이콘의 너비(픽셀). `scale`과 함께 이용할 수 없음 |
| height | <code>Number</code> | 아이콘의 높이(픽셀). `scale`과 함께 이용할 수 없음 |
| declutterMode | <code>&quot;declutter&quot;</code> \| <code>&quot;obstacle&quot;</code> \| <code>&quot;none&quot;</code> \| <code>undefined</code> | 디클러터 모드. |

<a name="odf_styleOption_circle"></a>

## odf\_styleOption\_circle : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| fill | <code>odf\_styleOption\_fill</code> | 원 채우기 스타일 |
| stroke | [<code>odf\_styleOption\_stroke</code>](#odf_styleOption_stroke) | 원 윤곽선 스타일 |
| radius | <code>Number</code> | 원의 반지름 |
| snapToPixel | <code>Boolean</code> | true(sharp) 또는 false(blur) |
| displacement | <code>Array.&lt;Number&gt;</code> | 위치이동. 양수 값은 모양을 오른쪽 및 위쪽으로 이동 (기본값 [0,0]) |
| scale | <code>Number</code> \| <code>Array.&lt;Number&gt;</code> | 원의 크기(radius로 정해진 크기의 n배), 배열일 때, [가로  축척, 세로 축척] 형태임 |
| rotation | <code>Number</code> | 원의 회전각도 (scale이 배열형태로 되어 타원형태가 되었을때 의미있음) |
| rotateWithView | <code>Boolean</code> | 뷰와 함께 모양을 회전할지 여부(기본값 false) |
| declutterMode | <code>&quot;declutter&quot;</code> \| <code>&quot;obstacle&quot;</code> \| <code>&quot;none&quot;</code> \| <code>undefined</code> | 디클러터 모드. |

<a name="odf_styleOption_regularShape"></a>

## odf\_styleOption\_regularShape : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| fill | <code>odf\_styleOption\_fill</code> | 채우기 스타일 |
| stroke | [<code>odf\_styleOption\_stroke</code>](#odf_styleOption_stroke) | 윤곽선 스타일 |
| points | <code>Number</code> | 별과 정다각형의 점 수. 다각형의 경우 점의 개수는 변의 개수 |
| radius | <code>Number</code> | 정다각형의 반경 |
| radius1 | <code>Number</code> | 별의 외부 반경 |
| radius2 | <code>Number</code> | 별의 내부 반경 |
| angle | <code>Number</code> | 모양의 각도(라디안). 값이 0이면 모양의 점 중 하나가 위를 향하게 됩니다. 기본값은 0 |
| snapToPixel | <code>Boolean</code> | true를 사용하면 "선명한" 렌더링(흐림 없음)이 가능하고, false를 사용하면 "정확한" 렌더링이 가능합니다. 모양의 위치에 애니메이션이 적용되는 경우 정확성이 중요합니다. 그렇지 않으면 모양이 눈에 띄게 흔들릴 수 있습니다. (기본값 true) - true : 출력 캔버스에 모양을 그릴 때 정수개의 픽셀이 X 및 Y 픽셀 좌표로 사용되는 경우 .  - false 분수를 사용할 수 있는 경우 . |
| rotation | <code>Number</code> | 라디안 단위의 회전입니다(시계 방향으로 양의 회전)(기본값 0) |
| rotateWithView | <code>Boolean</code> | 뷰와 함께 모양을 회전할지 여부(기본값 false) |
| displacement | <code>Array.&lt;Number&gt;</code> | 모양의 변위(픽셀). 양수 값은 모양을 오른쪽 및 위쪽으로 이동 (기본값 [0,0]) |
| scale | <code>Number</code> | 규모. (기본값 1) 2차원 스케일링이 필요하지 않은 경우 반경, 반경1 및 반경2에 대한 적절한 설정을 사용하면 더 나은 결과를 얻을 수 있음 |
| declutterMode | <code>String</code> | 디클러터 모드. "declutter","obstacle" ,"none" |

<a name="odf_styleOption_chart"></a>

## odf\_styleOption\_chart : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| type | <code>String</code> | 차트 타입  - pie => 파이 차트  - pie3D => 3D 파이 차트  - donut => 도넛 모양 차트  - bar => 막대 차트 |
| radius | <code>Number</code> | (pie/pie3D/donut) 차트의 크기 |
| barWidth | <code>Number</code> | (bar) 막대 너비.(단위 px) (기본값 10) |
| barMaxHegiht | <code>Number</code> | (bar) 막대 최대 높이.(단위 px)  (기본값 50) |
| barMinHegiht | <code>Number</code> | (bar) 막대 최소 높이. (단위 px) (기본값 1) |
| barBufferSize | <code>Number</code> | (bar) 막대와 막대 사이의 여백 너비. (단위 px) (기본값 0) |
| datas | <code>Array.&lt;String&gt;</code> | (동적데이터) feature의 속성 명을 정의 (추후 styleFunction의 콜백에서 활용)  ※ (ex) data : ["COL1","COL2","COL3","COL4"] |
| data | <code>Array.&lt;Number&gt;</code> | (정적 데이터) 미리 정의해 놓은 값을 데이터로 이용  ※ (ex) data : [1,5,3,2] |
| colors | <code>String</code> \| <code>Array.&lt;String&gt;</code> | 차트에 적용할 색상  1) 미리 정의되어있는 색상 값 이용  - classic => ["#ffa500","blue","red","green","cyan","magenta","yellow","#0f0"]  - dark => ["#960","#003","#900","#060","#099","#909","#990","#090"]  - pale => ["#fd0","#369","#f64","#3b7","#880","#b5d","#666"]  - pastel => ["#fb4","#79c","#f66","#7d7","#acc","#fdd","#ff9","#b9b"]  - neon => ["#ff0","#0ff","#0f0","#f0f","#f00","#00f"]  ※ (ex) colors : "dark"  2) 미리 정의되어있는 스타일 사용 (헥사 색상의 배열)  ※ (ex) colors : ['#FF4B4B','#FF7272','#FF9999','#FFC0C0','#FFE7E7'] |
| offsetX | <code>Number</code> | 기준점으로부터 텍스트 x좌표 위치 이동 |
| offsetY | <code>Number</code> | 기준점으로부터 텍스트 Y좌표 위치 이동 |
| rotation | <code>Number</code> | 회전값 (단위:라디안) |
| snapToPixel | <code>Number</code> | true(sharp) 또는 false(blur) |

<a name="odf_styleOption_geometryType"></a>

## odf\_styleOption\_geometryType : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| pointcircle | <code>String</code> | 점(원) 형태의 지오메트리 스타일(image/text 속성 적용가능) |
| pointicon | <code>String</code> | 심볼 형태의 지오메트리 스타일(image/text 속성 적용가능) |
| polygon | <code>String</code> | 면 형태의 지오메트리 스타일(fill/stroke/text 속성 적용가능) |
| linestring | <code>String</code> | 선 형태의 지오메트리 스타일 |
| free | <code>String</code> | [default]자유양식 지오메트리 스타일 |

<a name="odf_styleOption"></a>

## odf\_styleOption : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| geometryType | [<code>odf\_styleOption\_geometryType</code>](#odf_styleOption_geometryType) | 스타일을 적용할 지오메트리의 타입(아래 값들중 하나) |
| image | <code>Object</code> | 이미지 스타일  속성 |
| image.icon | [<code>odf\_styleOption\_icon</code>](#odf_styleOption_icon) | 아이콘 스타일 속성 |
| image.circle | [<code>odf\_styleOption\_circle</code>](#odf_styleOption_circle) | 원 스타일 속성 |
| image.chart | [<code>odf\_styleOption\_chart</code>](#odf_styleOption_chart) | 차트 스타일 속성 |
| image.regularShape | [<code>odf\_styleOption\_regularShape</code>](#odf_styleOption_regularShape) | regularShape 스타일 속성 |
| text | <code>odf\_styleOption\_text</code> | 텍스트 스타일 속성 |
| stroke | [<code>odf\_styleOption\_stroke</code>](#odf_styleOption_stroke) | 윤곽선 스타일 속성 |
| fill | <code>odf\_styleOption\_fill</code> | 채우기 스타일 속성 |

<a name="odf_StyleFunctionOption"></a>

## odf\_StyleFunctionOption : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| seperatorFunc | <code>function</code> \| <code>String</code> | 필터링 조건을 검사하는 function(true나 false를 반환) 또는 'default'. |
| style | [<code>odf\_styleOption</code>](#odf_styleOption) | seperatorFunc의 조건이 참일 경우 적용할 스타일 |
| priority | <code>Number</code> | 여러 seperatorFunc에서 참일경우, 스타일 적용 우선순위 |
| callbackFunc | <code>function</code> \| <code>String</code> | 스타일 펑션 내에서 seperatorFunc의 결과가 true일 경우 정의된 스타일을 반환하기 전, 호출되어 스타일을 변경할 수 있는 콜백함수 매개변수로 style, feature, resolution을 전달받아 해당 function 내에서 feature, resolution 값을 이용하여 style을 변경할 수 있음 |

<a name="odf_default_pattern_define"></a>

## odf\_default\_pattern\_define : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| name | <code>String</code> | 패턴명 |
| image | <code>String</code> | 패턴 이미지(base64) |





<a name="LayerInfoControl"></a>

## LayerInfoControl
레이어 정보 조회 클래스

**Kind**: global class
**Summary**: LayerInfoControl 생성자

 ```javascript
 let layerInfoControl = new odf.LayerInfoControl(options)//LayerInfoControl 생성
```

* [LayerInfoControl](#LayerInfoControl)
  * [new LayerInfoControl(options)](#new_LayerInfoControl_new)
  * [.setMap(map)](#LayerInfoControl+setMap)

<a name="new_LayerInfoControl_new"></a>

### new LayerInfoControl(options)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | LayerInfo 객체생성 옵션 |
| options.clickEvt | <code>event</code> | 발생시킬 이벤트 |
| options.callbackFunc | <code>function</code> | 이벤트 성공시 실행시킬 함수 |

<a name="LayerInfoControl+setMap"></a>

### layerInfoControl.setMap(map)
레이어 속성정보 조회 설정

```javascript
let map = new odf.Map(mapContainer, mapOption);
let layerInfoControl = new odf.LayerInfoControl(options)//LayerInfoControl 생성
layerInfoControl.setMap(map);
```

**Kind**: instance method of [<code>LayerInfoControl</code>](#LayerInfoControl)

| Param | Type | Description |
| --- | --- | --- |
| map | <code>Map</code> | LayerInfoControl 객체와 연결할 Map 객체 |


<a name="SwiperControl"></a>

## SwiperControl
지도 스와이퍼 설정 클래스

**Kind**: global class
**Summary**: SwiperControl 생성자

```javascript
//왼쪽에는 일어 배경지도, 오른쪽에는 항공지도 표현
 let swiperControl = new odf.SwiperControl({
   layers : [
     basemapControl.getBaseLayer('eMapJapanese')
     ,basemapControl.getBaseLayer('eMapAIR')],
    size : 200,
    useOriginalLayerFlag : false
 });
```

```javascript
//항공지도를 배경지도로 하여 왼쪽에는 layer1이, 오른쪽에는 layer2와 layer3 표현
 let layer1 = odf.LayerFactory('geoserver',{...});
 let layer2 = odf.LayerFactory('geoserver',{...});
 let layer3 = odf.LayerFactory('geoserver',{...});
 let swiperControl = new odf.SwiperControl({
   layers : [
      [basemapControl.getBaseLayer('eMapAIR'),layer1]
     ,[basemapControl.getBaseLayer('eMapAIR'),layer2,layer3]
     ],
    size : 200,
    useOriginalLayerFlag : false
 });
```

* [SwiperControl](#SwiperControl)
  * [new SwiperControl(options)](#new_SwiperControl_new)
  * [.getConstructorOptions()](#SwiperControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
  * [.setMap(map, createElementFlag)](#SwiperControl+setMap)
  * [.setLayers(layers)](#SwiperControl+setLayers)
  * [.setSliderValue(value)](#SwiperControl+setSliderValue)
  * [.getSliderValue()](#SwiperControl+getSliderValue) ⇒ <code>number</code>
  * [.setSwipeStrictFlag(strictFlag)](#SwiperControl+setSwipeStrictFlag)
  * [.setState(stateFlag)](#SwiperControl+setState)
  * [.getState()](#SwiperControl+getState) ⇒ <code>boolean</code>
  * [.getLayers()](#SwiperControl+getLayers) ⇒ <code>Array.&lt;Array.&lt;Layer&gt;, Array.&lt;Layer&gt;&gt;</code>

<a name="new_SwiperControl_new"></a>

### new SwiperControl(options)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | 스와이퍼 컨트롤 생성에 사용할 옵션 |
| options.layers | <code>Array.&lt;odf.Layer&gt;</code> \| <code>Array.&lt;Array.&lt;odf.Layer&gt;&gt;</code> | 스와이퍼로 나타낼 레이어 배열 |
| options.size | <code>Number</code> | 스와이퍼의 크기. default값 100px 최소 0, 최대 2000 |
| options.useOriginalLayerFlag | <code>Boolean</code> | 기존 사용중이던 레이어를 swiper레이어로 이용 - 그 외 default 값 true |
| options.swipeStrictFlag | <code>Boolean</code> | 베이스레이어만 오른쪽 그리기로 제한. 나머지 레이어는 오른쪽 영역에도 표시됨 |

<a name="SwiperControl+getConstructorOptions"></a>

### swiperControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>
컨트롤 생성 옵션 반환
```javascript
 let swiperControl = new odf.SwiperControl({
   layers : [
     basemapControl.getBaseLayer('eMapJapanese')
     ,basemapControl.getBaseLayer('eMapAIR')],
    size : 200,
    useOriginalLayerFlag : false
 });
 swiperControl.getConstructorOptions();
```

**Kind**: instance method of [<code>SwiperControl</code>](#SwiperControl)
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환
<a name="SwiperControl+setMap"></a>

### swiperControl.setMap(map, createElementFlag)
SwiperControl 객체에 map 객체 연결
```javascript
 //SwiperControl 생성
 let swiperControl = new odf.SwiperControl({
   layers : [
     basemapControl.getBaseLayer('eMapJapanese')
     ,basemapControl.getBaseLayer('eMapAIR')
   ],
    size : 200,
   });
 swiperControl.setMap(map); //SwiperControl 객체에 map 객체 연결
```

**Kind**: instance method of [<code>SwiperControl</code>](#SwiperControl)

| Param | Type | Description |
| --- | --- | --- |
| map | <code>Map</code> | SwiperControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | SwiperControl 버튼 생성 여부 |

<a name="SwiperControl+setLayers"></a>

### swiperControl.setLayers(layers)
SwiperControl과 연결될 레이어 설정
```javascript
 //SwiperControl 생성
 let swiperControl = new odf.SwiperControl({
   layers : [
     basemapControl.getBaseLayer('eMapJapanese')
     ,basemapControl.getBaseLayer('eMapAIR')
    ],
   size : 200,
 });
 swiperControl.setMap(map); //SwiperControl과 객체에 map 객체 연결

 swiperControl.setLayers([
     basemapControl.getBaseLayer('eMapChinese')
     ,basemapControl.getBaseLayer('eMapAIR')
 ]);// 스와이핑으로 나타낼 레이어를 중국어지도와 항공지도로 변경
```

**Kind**: instance method of [<code>SwiperControl</code>](#SwiperControl)

| Param | Type | Description |
| --- | --- | --- |
| layers | <code>Array.&lt;Layer&gt;</code> | 스와이핑으로 나타낼 레이어 배열. 두개의 레이어가 배열로 구성되어있어야. |

<a name="SwiperControl+setSliderValue"></a>

### swiperControl.setSliderValue(value)
SwiperControl의 슬라이더 값 셋팅
```javascript
 //SwiperControl 생성
 let swiperControl = new odf.SwiperControl({
   layers : [
     basemapControl.getBaseLayer('eMapJapanese')
     ,basemapControl.getBaseLayer('eMapAIR')
   ],
    size : 200,
   });
 swiperControl.setMap(map); //SwiperControl과 객체에 map 객체 연결
 swiperControl.setSliderValue(30); //슬라이더 값을 30으로 셋팅(0~100사이의 값)
```

**Kind**: instance method of [<code>SwiperControl</code>](#SwiperControl)

| Param | Type | Description |
| --- | --- | --- |
| value | <code>number</code> | 셋팅할 슬라이더 값 (0~100사이의 숫자) |

<a name="SwiperControl+getSliderValue"></a>

### swiperControl.getSliderValue() ⇒ <code>number</code>
SwiperControl의 슬라이더 값 조회
```javascript
 //SwiperControl 생성
 let swiperControl = new odf.SwiperControl({
   layers : [
     basemapControl.getBaseLayer('eMapJapanese')
     ,basemapControl.getBaseLayer('eMapAIR')
   ],
   size : 200,
 });
 swiperControl.setMap(map); //SwiperControl과 객체에 map 객체 연결
 swiperControl.getSliderValue(); //슬라이더 값 조회

**Kind**: instance method of [<code>SwiperControl</code>](#SwiperControl)
**Returns**: <code>number</code> - value - 슬라이더의 값(0~100사이의 숫자)
<a name="SwiperControl+setSwipeStrictFlag"></a>

### swiperControl.setSwipeStrictFlag(strictFlag)
SwiperControl에 엄격모드 적용
```javascript
 //SwiperControl 생성
 let swiperControl = new odf.SwiperControl({
   layers : [
     basemapControl.getBaseLayer('eMapJapanese')
     ,basemapControl.getBaseLayer('eMapAIR')
   ],
   size : 200,
 });
 swiperControl.setMap(map); //SwiperControl과 객체에 map 객체 연결
 swiperControl.setSwipeStrictFlag(true); //엄격모드 적용

**Kind**: instance method of [<code>SwiperControl</code>](#SwiperControl)

| Param | Type | Description |
| --- | --- | --- |
| strictFlag | <code>boolean</code> | 엄격모드 여부(왼쪽영역의 레이어 중 베이스레이어를 제외한 레이어가 오른쪽 영역으로도 표기(=>false) 여부) |

<a name="SwiperControl+setState"></a>

### swiperControl.setState(stateFlag)
슬라이더 적용 여부 정의
```javascript
 //SwiperControl 생성
 let swiperControl = new odf.SwiperControl({
   layers : [
     basemapControl.getBaseLayer('eMapJapanese')
     ,basemapControl.getBaseLayer('eMapAIR')
   ],
   size : 200,
 });
 swiperControl.setMap(map); //SwiperControl과 객체에 map 객체 연결
 swiperControl.setState(true); //슬라이더 적용
```

**Kind**: instance method of [<code>SwiperControl</code>](#SwiperControl)

| Param | Type | Description |
| --- | --- | --- |
| stateFlag | <code>boolean</code> | 슬라이더 적용 여부(true=>적용/false=>미적용) |

<a name="SwiperControl+getState"></a>

### swiperControl.getState() ⇒ <code>boolean</code>
슬라이더 적용 여부 조회
```javascript
 //SwiperControl 생성
 let swiperControl = new odf.SwiperControl({
   layers : [
     basemapControl.getBaseLayer('eMapJapanese')
     ,basemapControl.getBaseLayer('eMapAIR')
   ],
   size : 200,
 });
 swiperControl.setMap(map); //SwiperControl과 객체에 map 객체 연결
 swiperControl.getState(); //슬라이더 적용여부 조회

**Kind**: instance method of [<code>SwiperControl</code>](#SwiperControl)
**Returns**: <code>boolean</code> - stateFlag - 슬라이더 적용 여부(true=>적용/false=>미적용)
<a name="SwiperControl+getLayers"></a>

### swiperControl.getLayers() ⇒ <code>Array.&lt;Array.&lt;Layer&gt;, Array.&lt;Layer&gt;&gt;</code>
슬라이더 왼쪽 레이어목록/ 오른쪽 레이어 목록 추출
```javascript
 //SwiperControl 생성
 let swiperControl = new odf.SwiperControl({
   layers : [
     basemapControl.getBaseLayer('eMapJapanese')
     ,basemapControl.getBaseLayer('eMapAIR')
   ],
   size : 200,
 });
 swiperControl.setMap(map); //SwiperControl과 객체에 map 객체 연결
 swiperControl.getLayers(); //컨트롤에 셋팅된 레이어 목록 조회

**Kind**: instance method of [<code>SwiperControl</code>](#SwiperControl)
**Returns**: <code>Array.&lt;Array.&lt;Layer&gt;, Array.&lt;Layer&gt;&gt;</code> - layers
 - 배열의 첫번째 요소 : 슬라이더 왼쪽 표현 레이어 목록
 - 배열의 두번째 요소 :  슬라이더 오른쪽 표현 레이어 목록

## Classes

<dl>
<dt><a href="#ColorFactory">ColorFactory</a></dt>
<dd><p>색 생성 클래스</p>
</dd>
</dl>

## Typedefs

<dl>
<dt><a href="#odf_color_type">odf_color_type</a> : <code>Object</code></dt>
<dd></dd>
<dt><a href="#odf_rgba_color">odf_rgba_color</a> : <code>Array</code></dt>
<dd></dd>
<dt><a href="#odf_hex_color">odf_hex_color</a> : <code>String</code></dt>
<dd></dd>
<dt><a href="#odf_graiden_color_info">odf_graiden_color_info</a> : <code>Object</code></dt>
<dd></dd>
</dl>

<a name="ColorFactory"></a>

## ColorFactory
색 생성 클래스

**Kind**: global class

* [ColorFactory](#ColorFactory)
    * [.produce(type, size, Alpha, order)](#ColorFactory.produce) ⇒ [<code>Array.&lt;odf\_rgba\_color&gt;</code>](#odf_rgba_color)
    * [.produceRandomColor(size, alpha)](#ColorFactory.produceRandomColor) ⇒ [<code>Array.&lt;odf\_rgba\_color&gt;</code>](#odf_rgba_color)
    * [.produceGradientColor(fromColor, toColor, size, alpha)](#ColorFactory.produceGradientColor) ⇒ [<code>Array.&lt;odf\_rgba\_color&gt;</code>](#odf_rgba_color)
    * [.produceGradientColorWitPropotion(colors, size, alpha)](#ColorFactory.produceGradientColorWitPropotion) ⇒ [<code>Array.&lt;odf\_rgba\_color&gt;</code>](#odf_rgba_color)
    * [.packColor(color)](#ColorFactory.packColor) ⇒ <code>Array.&lt;number&gt;</code>

<a name="ColorFactory.produce"></a>

### ColorFactory.produce(type, size, Alpha, order) ⇒ [<code>Array.&lt;odf\_rgba\_color&gt;</code>](#odf_rgba_color)
colorArray 생성

```javascript
odf.ColorFactory.produce('red',10);//빨간색 계열로 10개의 색상 값 추출
```

**Kind**: static method of [<code>ColorFactory</code>](#ColorFactory)
**Returns**: [<code>Array.&lt;odf\_rgba\_color&gt;</code>](#odf_rgba_color) - 생성된 색의 배열

| Param | Type | Description |
| --- | --- | --- |
| type | [<code>odf\_color\_type</code>](#odf_color_type) | 생성할 색의 계열 |
| size | <code>number</code> | 생성할 색의 개수 1~ |
| Alpha | <code>number</code> | 투명도 0~1 |
| order | <code>boolean</code> | 색 표현 순서(true => 옅은색에서 진한색 순, false=>진한색 색에서 옅은색 순) |

<a name="ColorFactory.produceRandomColor"></a>

### ColorFactory.produceRandomColor(size, alpha) ⇒ [<code>Array.&lt;odf\_rgba\_color&gt;</code>](#odf_rgba_color)
랜덤 색상 조회

```javascript
odf.ColorFactory.produceRandomColor(10,1);//alpha값이 1(불투명)인 랜덤색상 10개 추출
```

**Kind**: static method of [<code>ColorFactory</code>](#ColorFactory)
**Returns**: [<code>Array.&lt;odf\_rgba\_color&gt;</code>](#odf_rgba_color) - 생성된 색의 배열

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| size | <code>number</code> | <code>1</code> | 생성할 색의 개수 |
| alpha | <code>number</code> |  | rgba의 alpha값. 0이면 투명, 1이면 불투명, 0~1 사이의 값 |

<a name="ColorFactory.produceGradientColor"></a>

### ColorFactory.produceGradientColor(fromColor, toColor, size, alpha) ⇒ [<code>Array.&lt;odf\_rgba\_color&gt;</code>](#odf_rgba_color)
fromColor에서 toColor까지의 색상 추출
```javascript
odf.ColorFactory.getGradientColor([255,100,0],[0,0,0],20,1);//alpha값이 1(불투명)이면서 [255,100,0]에서 [0,0,0]까지의 색상 값을 추출
```

**Kind**: static method of [<code>ColorFactory</code>](#ColorFactory)
**Returns**: [<code>Array.&lt;odf\_rgba\_color&gt;</code>](#odf_rgba_color) - 생성된 색의 배열

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| fromColor | [<code>odf\_rgba\_color</code>](#odf_rgba_color) \| [<code>odf\_hex\_color</code>](#odf_hex_color) |  | 시작 색상(rgb 또는 hex 색상) |
| toColor | [<code>odf\_rgba\_color</code>](#odf_rgba_color) \| [<code>odf\_hex\_color</code>](#odf_hex_color) |  | 종료 색상(rgb 또는 hex 색상) |
| size | <code>number</code> |  | 생성할 색의 개수 |
| alpha | <code>number</code> | <code>1</code> | rgba의 alpha값. 0이면 투명, 1이면 불투명, 0~1 사이의 값 |

<a name="ColorFactory.produceGradientColorWitPropotion"></a>

### ColorFactory.produceGradientColorWitPropotion(colors, size, alpha) ⇒ [<code>Array.&lt;odf\_rgba\_color&gt;</code>](#odf_rgba_color)
비율을 이용한 색상 추출(2개 이상 추출)
```javascript
odf.ColorFactory.getGradientColorWithWeight([
 {color :[0,0,0]   ,propotion:0}, //첫번째 색상 정보
 {color :[255,0,0] ,propotion:0.35489},//두번째 색상 정보
 {color :[0,255,0] ,propotion:1},//세번째 색상 정보
],//색상정보
50,//생성할 색의 개수
1//rgba의 alpha값. 0이면 투명, 1이면 불투명, 0~1 사이의 값
);
```

**Kind**: static method of [<code>ColorFactory</code>](#ColorFactory)
**Returns**: [<code>Array.&lt;odf\_rgba\_color&gt;</code>](#odf_rgba_color) - 생성된 색의 배열

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| colors | [<code>Array.&lt;odf\_graiden\_color\_info&gt;</code>](#odf_graiden_color_info) |  | 비율 정보를 포함한 색상정보 |
| size | <code>number</code> |  | 생성할 색의 개수 (2 이상) |
| alpha | <code>number</code> | <code>1</code> | rgba의 alpha값. 0이면 투명, 1이면 불투명, 0~1 사이의 값 |

<a name="ColorFactory.packColor"></a>

### ColorFactory.packColor(color) ⇒ <code>Array.&lt;number&gt;</code>
모든 유형의 색상 값을 두개의 부동소수점 배열로 묶는다(압축)

**Kind**: static method of [<code>ColorFactory</code>](#ColorFactory)
**Returns**: <code>Array.&lt;number&gt;</code> - 두개의 부동소수점 배열로 압축한 값

| Param | Type | Description |
| --- | --- | --- |
| color | <code>Array.&lt;Number&gt;</code> \| <code>string</code> | rgb/rgba, hex 색상 값 |

<a name="odf_color_type"></a>

## odf\_color\_type : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| 'red' | <code>String</code> | 빨간색 계열(table) |
| 'blue' | <code>String</code> | 파란색 계열 |
| 'yellow' | <code>String</code> | 노란색 계열 |
| 'green' | <code>String</code> | 초록색 계열 |
| 'purple' | <code>String</code> | 보라색 계열 |
| 'brown' | <code>String</code> | 갈색 계열 |
| 'black' | <code>String</code> | 검정색 계열 |
| 'red2blue' | <code>String</code> | 빨간색→파란색, |
| 'red2green' | <code>String</code> | 빨간색→초록색, |
| 'red2purple' | <code>String</code> | 빨간색→보라색, |
| 'red2yellow' | <code>String</code> | 빨간색→노란색, |
| 'yellow2blue' | <code>String</code> | 노란색→파란색, |
| 'yellow2purple' | <code>String</code> | 노란색→보라색, |
| 'yellow2green' | <code>String</code> | 노란색→초록색, |
| 'yellow2brown' | <code>String</code> | 노란색→갈색, |
| 'yellow2black' | <code>String</code> | 노란색→검정색, |
| 'blue2green' | <code>String</code> | 파란색→초록색, |
| 'random' | <code>String</code> | 랜덤 색상 |

<a name="odf_rgba_color"></a>

## odf\_rgba\_color : <code>Array</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| odf_rgba_color[0]; | <code>number</code> | 빨간색 값(0~255의 값) |
| odf_rgba_color[1]; | <code>number</code> | 초록색 값(0~255의 값) |
| odf_rgba_color[2]; | <code>number</code> | 파란색 값(0~255의 값) |
| odf_rgba_color[3]; | <code>number</code> | 투명도(0~1의 값) |

<a name="odf_hex_color"></a>

## odf\_hex\_color : <code>String</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| '#[숫자][숫자][숫자][숫자][숫자][숫자]'; | <code>String</code> | hex 색상(숫자 6자리) |
| '#[숫자][숫자][숫자][숫자][숫자][숫자][숫자][숫자]'; | <code>String</code> | hex 색상(숫자 6자리 +투명도 포함) |

<a name="odf_graiden_color_info"></a>

## odf\_graiden\_color\_info : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| color | [<code>odf\_rgba\_color</code>](#odf_rgba_color) \| [<code>odf\_hex\_color</code>](#odf_hex_color) | 색상 값 |
| propotion | <code>number</code> | 적용할 색상의 비율   - 0~1 사이의 값  - 첫번째 인덱스는 0으로 고정, 마지막 인덱스는 1로 고정  - propotion 값은 이전 propotion 값보다 커야함 |


<a name="ZipControl"></a>

## ZipControl
Server없이 Layer 생성하는 클래스

**Kind**: global class
**Summary**: ZipControl 생성자

 ```javascript
 let options = {
   file: document.getElementById('uploadBtn'),
   encoding: 'UTF-8',
   epsg: '5179',
 };
 let zipControl = new odf.ZipControl(options)//ZipControl 생성
```

* [ZipControl](#ZipControl)
  * [new ZipControl(options)](#new_ZipControl_new)
  * [.setMap(map)](#ZipControl+setMap)

<a name="new_ZipControl_new"></a>

### new ZipControl(options)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | ZipControl 객체생성 옵션 |
| options.file | <code>HTMLObjectElement</code> | zip 객체생성 옵션(File type input HTML) |
| options.encoding | <code>String</code> | Encoding 종류 |
| options.epsg | <code>String</code> | 좌표계 EPSG 종류(EPSG 문자를 제외한 코드 번호) |
| options.getLayer | <code>function</code> | 생성된 Layer 객체를 받을 callback  함수 |
| options.geometryType | <code>Array</code> | 레이어 생성 시 허용할 geometry 타입배열 ['point','linestring','polygon'] 만 허용 |
| options.errorCallback | <code>function</code> | 에러 발생 시 에러 메시지 리턴 받을 함수 |

<a name="ZipControl+setMap"></a>

### zipControl.setMap(map)
ZipControl map 객체 연결

```javascript
 let zipControl = new odf.ZipControl(options)//ZipControl 생성
 zipControl.setMap(map); //ZipControl 객체에 map 객체 연결
```

**Kind**: instance method of [<code>ZipControl</code>](#ZipControl)

| Param | Type | Description |
| --- | --- | --- |
| map | <code>Map</code> | ZipControl 객체와 연결할 Map 객체 |


<a name="Control"></a>

## Control
사용자 정의 컨트롤 클래스

**Kind**: global class
**Summary**: 사용자가 정의한 컨트롤을 생성한다.
```javascript
//서브그룹 컨트롤
      class NewControl extends odf.Control {
        constructor() {
          super({
            setMap: () => {},
            removeMap: () => {},
            clear: () => {},
          });
        }
      }
      //서브그룹 컨트롤일 경우,
      NewControl.subGroup = [
        {
          id: 'new01',
          name: '뉴1',
          click: (evt) => {

            alert('서브그룹 컨트롤 클릭  뉴1');
          },
        },
        {
          id: 'new02',
          name: '뉴2',
          click: (evt) => {
            alert('서브그룹 컨트롤 클릭  뉴2');
          },
        },
      ];
      //controlId와 groupIdx는 서브그룹 컨트롤이든, 단일컨트롤이든 필수 정의
      NewControl.controlId = 'newControl';// (필수값)사용자정의 컨트롤 id
      NewControl.groupIdx = 3;// (필수값)사용자정의 컨트롤 그룹 순번

      //해당 컨트롤을 map 객체에 등록 분할지도 등에 사용하고 싶을 경우 (한번만 호출)
      NewControl.registControl(map, NewControl);
```


   ```javascript
   //단일 컨트롤
      class NewControl extends odf.Control {
        constructor() {
          super({
            setMap: () => {},
            removeMap: () => {},
            clear: () => {},
          });
        }
      }
      //단일 컨트롤이 아닐 경우
      NewControl.click = function () {
        alert('단일컨트롤 클릭');
      };
      //controlId와 groupIdx는 서브그룹 컨트롤이든, 단일컨트롤이든 필수 정의
      NewControl.controlId = 'newControl';// (필수값)사용자정의 컨트롤 id
      NewControl.groupIdx = 3;// (필수값)사용자정의 컨트롤 그룹 순번

      //해당 컨트롤을 map 객체에 등록 분할지도 등에 사용하고 싶을 경우 (한번만 호출)
      NewControl.registControl(map, NewControl);
```

* [Control](#Control)
  * [new Control(setMap, removeMap, clear)](#new_Control_new)
  * _instance_
    * [.getMap()](#Control+getMap) ⇒ <code>Map</code>
    * [.setMap(map)](#Control+setMap)
    * [.getElements()](#Control+getElements) ⇒ <code>Array.&lt;HTMLElement&gt;</code>
    * [.getElement(contentId)](#Control+getElement) ⇒ <code>HTMLElement</code>
    * [.removeMap()](#Control+removeMap)
    * [.clear()](#Control+clear)
  * _static_
    * [.registControl(map, _class)](#Control.registControl)

<a name="new_Control_new"></a>

### new Control(setMap, removeMap, clear)

| Param | Type | Description |
| --- | --- | --- |
| setMap | <code>function</code> | 사용자 정의 컨트롤을 지도에 셋팅한 후 호출되는 callback function |
| removeMap | <code>function</code> | 사용자 정의 컨트롤을 지도에서 삭제한 후 호출되는 callback function |
| clear | <code>function</code> | 다른 컨트롤을 클릭하여 사용자정의 컨트롤이 닫힐 경우 호출되는 callback function |

<a name="Control+getMap"></a>

### control.getMap() ⇒ <code>Map</code>
사용자정의 컨트롤 연결된 map 반환

```javascript
 class NewControl extends odf.Control {
       ...
      }
      ...
 let newControl = new NewControl({}); //사용자정의 컨트롤 생성
 newControl.setMap(map); //사용자정의 컨트롤에 map 객체 연결
 ```

**Kind**: instance method of [<code>Control</code>](#Control)
**Returns**: <code>Map</code> - 사용자정의 컨트롤에 연결된 map 객체
<a name="Control+setMap"></a>

### control.setMap(map)
사용자정의 컨트롤에 map 연결 한 후 생성자의 매개변수로 넘어온 setMap 호출

```javascript
 class NewControl extends odf.Control {
       ...
      }
      ...
 let newControl = new NewControl({}); //사용자정의 컨트롤 생성
 newControl.setMap(map); //사용자정의 컨트롤에 map 객체 연결
 newControl.getMap(map); //사용자정의 컨트롤에 연결된 map 객체 반환
 ```

**Kind**: instance method of [<code>Control</code>](#Control)

| Param | Type | Description |
| --- | --- | --- |
| map | <code>Map</code> | 사용자정의 컨트롤에 연결할 Map 객체 |

<a name="Control+getElements"></a>

### control.getElements() ⇒ <code>Array.&lt;HTMLElement&gt;</code>
서브그룹 컨트롤일 경우, 사용자정의 컨트롤 button Element 배열 반환

```javascript
 class NewControl extends odf.Control {
       ...
      }
      NewControl.subGroup = [
        {
          id: 'new01',
          name: '뉴1',
          click: (evt) => {

            alert('서브그룹 컨트롤 클릭  뉴1');
          },
        },
        {
          id: 'new02',
          name: '뉴2',
          click: (evt) => {
            alert('서브그룹 컨트롤 클릭  뉴2');
          },
        },
      ];
      ...
 let newControl = new NewControl({}); //사용자정의 컨트롤 생성
 newControl.setMap(map); //사용자정의 컨트롤에 map 객체 연결
 //setMap을 호출한 후에 생성되는 function
 newControl.getElements(); //사용자정의 컨트롤에 연결된 map 객체 반환
 ```

**Kind**: instance method of [<code>Control</code>](#Control)
**Returns**: <code>Array.&lt;HTMLElement&gt;</code> - 사용자정의 컨트롤 button Element
<a name="Control+getElement"></a>

### control.getElement(contentId) ⇒ <code>HTMLElement</code>
버튼 elment반환

```javascript
//서브그룹컨트롤
 class NewControl extends odf.Control {
       ...
      }
      NewControl.subGroup = [
        {
          id: 'new01',
          name: '뉴1',
          click: (evt) => {

            alert('서브그룹 컨트롤 클릭  뉴1');
          },
        },
        {
          id: 'new02',
          name: '뉴2',
          click: (evt) => {
            alert('서브그룹 컨트롤 클릭  뉴2');
          },
        },
      ];
      ...
 let newControl = new NewControl({}); //사용자정의 컨트롤 생성
 newControl.setMap(map); //사용자정의 컨트롤에 map 객체 연결
 //setMap을 호출한 후에 생성되는 function
 newControl.getElement('new01'); //new01라는 id를 갖는 버튼 element 반환
 ```

```javascript
//단일 컨트롤
 class NewControl extends odf.Control {
       ...
      }
      ...
 let newControl = new NewControl({}); //사용자정의 컨트롤 생성
 newControl.setMap(map); //사용자정의 컨트롤에 map 객체 연결
 //setMap을 호출한 후에 생성되는 function
 newControl.getElement(); //버튼 element 반환
 ```

**Kind**: instance method of [<code>Control</code>](#Control)
**Returns**: <code>HTMLElement</code> - contentId에 해당하는 button Element

| Param | Type | Description |
| --- | --- | --- |
| contentId | <code>String</code> | 조회할 버튼 id (단일 컨트롤의 경우 undefined) |

<a name="Control+removeMap"></a>

### control.removeMap()
map과 연결 해제 한 후 생성자의 매개변수로 넘어온 removeMap 호출

```javascript
 class NewControl extends odf.Control {
       ...
      }
      ...
 let newControl = new NewControl({}); //사용자정의 컨트롤 생성
 newControl.setMap(map); //사용자정의 컨트롤에 map 객체 연결
 //setMap을 호출한 후에 생성되는 function
 newControl.removeMap(); //생성된 컨트롤 객체를 삭제
 ```

**Kind**: instance method of [<code>Control</code>](#Control)
<a name="Control+clear"></a>

### control.clear()
생성자의 매개변수로 넘어온 clear 함수 호출

```javascript
 class NewControl extends odf.Control {
       ...
      }
      ...
 let newControl = new NewControl({}); //사용자정의 컨트롤 생성
 newControl.setMap(map); //사용자정의 컨트롤에 map 객체 연결
 newControl.clear(); //생성자의 매개변수로 넘어온  clear 함수 호출
 ```

**Kind**: instance method of [<code>Control</code>](#Control)
<a name="Control.registControl"></a>

### Control.registControl(map, _class)
control 등록

**Kind**: static method of [<code>Control</code>](#Control)

| Param | Type | Description |
| --- | --- | --- |
| map | <code>Object</code> | DrawControl 객체생성 옵션 |
| _class | <code>Class</code> | 사용자 정의 컨트롤을 상속받은 클래스 |

**Example**
class NewControl extends odf.Control {
...
}
//controlId와 groupIdx는 서브그룹 컨트롤이든, 단일컨트롤이든 필수 정의
NewControl.controlId = 'newControl';// (필수값)사용자정의 컨트롤 id
NewControl.groupIdx = 3;// (필수값)사용자정의 컨트롤 그룹 순번

      //해당 컨트롤을 map 객체에 등록 분할지도 등에 사용하고 싶을 경우 (한번만 호출)
      NewControl.registControl(map, NewControl);
```

<a name="ScaleControl"></a>

## ScaleControl
축척 표시 설정 클래스

**Kind**: global class
**Summary**: ScaleControl 생성자

 ```javascript
 let scaleControl = new odf.ScaleControl(options);//ScaleControl 생성
```

* [ScaleControl](#ScaleControl)
  * [new ScaleControl(options)](#new_ScaleControl_new)
  * [.getConstructorOptions()](#ScaleControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
  * [.setMap(_map, createElementFlag)](#ScaleControl+setMap)
  * [.removeMap()](#ScaleControl+removeMap)
  * [.setPixelSize()](#ScaleControl+setPixelSize)
  * [.getScaleValue()](#ScaleControl+getScaleValue)
  * [.setScaleValue(scale)](#ScaleControl+setScaleValue)
  * [.getPixelSize(size)](#ScaleControl+getPixelSize)

<a name="new_ScaleControl_new"></a>

### new ScaleControl(options)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | ScaleControl 생성 옵션 |
| options.size | <code>Number</code> | pixelSize 설정 최소 10 최대 2000 |
| options.scaleInput | <code>Boolean</code> | 축척입력창 true/false |

<a name="ScaleControl+getConstructorOptions"></a>

### scaleControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>
컨트롤 생성 옵션 반환
```javascript
 let scaleControl = new odf.ScaleControl();//ScaleControl 생성
 scaleControl.getConstructorOptions();
```

**Kind**: instance method of [<code>ScaleControl</code>](#ScaleControl)
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환
<a name="ScaleControl+setMap"></a>

### scaleControl.setMap(_map, createElementFlag)
ScaleControl map 객체 연결
clear HTMLElement를 만들고, 이벤트 바인딩 시켜, map 객체와 연결

 ```javascript
 let scaleControl = new odf.ScaleControl();//ScaleControl 생성
 scaleControl.setMap(map,createElementFlag); //ScaleControl 객체에 map 객체 연결
```

**Kind**: instance method of [<code>ScaleControl</code>](#ScaleControl)

| Param | Type | Description |
| --- | --- | --- |
| _map | <code>Map</code> | ScaleControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | ScaleControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x |

<a name="ScaleControl+removeMap"></a>

### scaleControl.removeMap()
ScaleControl map 객체 연결 해제

```javascript
 scaleControl.removeMap(); //ScaleControl 객체에 map 연결 해제
```

**Kind**: instance method of [<code>ScaleControl</code>](#ScaleControl)
<a name="ScaleControl+setPixelSize"></a>

### scaleControl.setPixelSize()
ScaleControl의 기준 픽셀 크기 변경

```javascript
 scaleControl.setPixelSize(); //ScaleControl 기준 픽셀 크기 변경
```

**Kind**: instance method of [<code>ScaleControl</code>](#ScaleControl)
<a name="ScaleControl+getScaleValue"></a>

### scaleControl.getScaleValue()
현재 지도 축척 산출 기준에따른 축척 깂 조회
```javascript
 scaleControl.getScaleValue();
```

**Kind**: instance method of [<code>ScaleControl</code>](#ScaleControl)
<a name="ScaleControl+setScaleValue"></a>

### scaleControl.setScaleValue(scale)
현재 지도 축척 산출 기준에따른 축척 깂 조회

```javascript
 scaleControl.setScaleValue(Number); 축척값 ex) 1:5000 -> 5000 입력
```

**Kind**: instance method of [<code>ScaleControl</code>](#ScaleControl)

| Param | Type | Description |
| --- | --- | --- |
| scale | <code>Number</code> | 설정을 원하는 scale 값 (cm단위) |

<a name="ScaleControl+getPixelSize"></a>

### scaleControl.getPixelSize(size)
ScaleControl의 축척 산출 기준 size(단위: pixel) 조회
```javascript
 scaleControl.getPixelSize(size);
```

**Kind**: instance method of [<code>ScaleControl</code>](#ScaleControl)

| Param | Type | Description |
| --- | --- | --- |
| size | <code>Number</code> | 설정을 원하는 pixel 값 (px단위) |


<a name="Popup"></a>

## Popup
팝업 생성 클래스

**Kind**: global class
**Summary**: 팝업 클래스 생성자

  ```javascript
    // String(text) 형태로 HTML을 구성하여 Parameter로 사용할 수 있다.
    let content = '<div><table><thead></thead>'
      + '<tbody><tr><td><button value=move>이동</button></td></tr></tbody>'
      + '</table></div>';
    // document.createElement('div')등의 형태로 html을 생성하여
    // Popup Class의 Parameter로 사용할 수 있다.

    let options = {
      autoPan: true,
      autoPanAnimation: {
        duration: 250
      }
    };
      let popup = new odf.Popup(Content, options);
popup.openOn(map);
let coord = new odf.Coordinate([126 , 37]]);
popup.setPosition(coord);
  ```

* [Popup](#Popup)
  * [new Popup(content, options)](#new_Popup_new)
  * [.openOn(map)](#Popup+openOn)
  * [.setPosition(coord)](#Popup+setPosition)
  * [.setPositioning(position)](#Popup+setPositioning)
  * [.setOffset(position)](#Popup+setOffset)
  * [.getPosition(coord)](#Popup+getPosition)
  * [.isOpen()](#Popup+isOpen) ⇒ <code>Boolean</code>
  * [.close()](#Popup+close)

<a name="new_Popup_new"></a>

### new Popup(content, options)

| Param | Type | Description |
| --- | --- | --- |
| content | <code>String</code> \| <code>HTML</code> | Popup으로 생성할 HTML 내용 |
| options | <code>Object</code> | 팝업 옵션 정보 |
| options.autoPan | <code>Boolean</code> | true : 지도 화면에서 팝업이 잘려나오게 될 경우, 지도를 이동합니다. (기본값 : false) |
| options.autoPanAnimation | <code>number</code> | autoPan 이 ture 여서 지도를 이동하는데 사용되는 애니메이션 입니다. (기본값 : 250, autoPan이 ture일때만 사용가능) |
| options.autoPanMargin | <code>number</code> | autoPan 이 ture 면서 , 팝업과 지도 사이의 여백(픽셀) 지정 (기본값 : 20, autoPan이 ture일때만 사용가능) |

<a name="Popup+openOn"></a>

### popup.openOn(map)
Popup를 map 객체와 연결
```javascript
let map = new odf.Map(...);
odf.event.addListener(map, 'click', (evt) => {
  popup.openOn(map); //openOn() 실행 후 좌표를 지정하지 않았기 때문에 setPosition 함수를 통해 위치를 지정해주어야합니다.
});
```

**Kind**: instance method of [<code>Popup</code>](#Popup)

| Param | Type | Description |
| --- | --- | --- |
| map | <code>Map</code> | Popup 객체와 map 객체 연결 |

<a name="Popup+setPosition"></a>

### popup.setPosition(coord)
Popup의 위치 설정
```javascript
let map = new odf.Map(...);
odf.event.addListener(map, 'click', (evt) => {
  popup.openOn(map);
  let coord = new odf.Coordinate(evt.coordinate);
  popup.setPosition(coord);   //openOn() 실행 후 좌표를 지정해주어야 지도에 표시 됩니다.
});
```

**Kind**: instance method of [<code>Popup</code>](#Popup)

| Param | Type | Description |
| --- | --- | --- |
| coord | <code>Coordinate</code> | 위치 좌표 odf.Coordinate 객체로 생성 |

<a name="Popup+setPositioning"></a>

### popup.setPositioning(position)
Popup의 오버레이가 속성과 관련하여 실제로 배치되는 방식을 정의
```javascript
let map = new odf.Map(...);
odf.event.addListener(map, 'click', (evt) => {
  popup.openOn(map);
  let coord = new odf.Coordinate(evt.coordinate);
  popup.setPosition(coord);   //openOn() 실행 후 좌표를 지정해주어야 지도에 표시 됩니다.
  popup.setPositioning('bottom-left');
});
```

**Kind**: instance method of [<code>Popup</code>](#Popup)

| Param | Type | Description |
| --- | --- | --- |
| position | <code>String</code> | 오버레이가 지도의 해당 지점을 기준으로 배치되는 방식 (가능한 값 : 'bottom-left', 'bottom-center', 'bottom-right', 'center-left', 'center-center', 'center-right', 'top-left', 'top-center', 'top-right' |

<a name="Popup+setOffset"></a>

### popup.setOffset(position)
popup 배치할 때 사용되는 오프셋(픽셀)입니다. 배열의 첫 번째 요소는 수평 오프셋(양수 값은 오버레이를 오른쪽으로 이동) 배열의 두 번째 요소는 수직 오프셋(양수 값은 오버레이를 아래로 이동합니다.)
```javascript
let map = new odf.Map(...);
odf.event.addListener(map, 'click', (evt) => {
  popup.openOn(map);
  let coord = new odf.Coordinate(evt.coordinate);
  popup.setPosition(coord);   //openOn() 실행 후 좌표를 지정해주어야 지도에 표시 됩니다.
  popup.setOffset([0, -10]);
});
```

**Kind**: instance method of [<code>Popup</code>](#Popup)

| Param | Type | Description |
| --- | --- | --- |
| position | <code>String</code> | 오버레이가 지도의 해당 지점을 기준으로 배치되는 방식 (가능한 값 : 'bottom-left', 'bottom-center', 'bottom-right', 'center-left', 'center-center', 'center-right', 'top-left', 'top-center', 'top-right' |

<a name="Popup+getPosition"></a>

### popup.getPosition(coord)
Popup의 위치 가져오기
```javascript
let map = new odf.Map(...);
odf.event.addListener(map, 'click', (evt) => {
  popup.openOn(map);
  let coord = new odf.Coordinate(evt.coordinate);
  popup.setPosition(coord);
  console.dir(popup.getPosition());   //openOn() 현재 팝업이 표시된 위치 가져오기
});
```

**Kind**: instance method of [<code>Popup</code>](#Popup)

| Param | Type | Description |
| --- | --- | --- |
| coord | <code>Coordinate</code> | 위치 좌표 odf.Coordinate 객체로 생성 |

<a name="Popup+isOpen"></a>

### popup.isOpen() ⇒ <code>Boolean</code>
Popup Open 되어 있는지 확인
```javascript
let popup = new odf.Popup(...);
popup.isOpen();
```

**Kind**: instance method of [<code>Popup</code>](#Popup)
**Returns**: <code>Boolean</code> - Popup이 열려있는지 결과에 따라 true/false 값 리턴
<a name="Popup+close"></a>

### popup.close()
열려있는 Popup을 닫는다
```javascript
let popup = new odf.Popup(...);
popup.isOpen();
popup.close();
```

**Kind**: instance method of [<code>Popup</code>](#Popup)

<a name="Projection"></a>

## Projection
좌표 변환 클래스

**Kind**: global class
**Summary**: 좌표 변환 제공 클래스

  ```javascript
    var map = new odf.Map(...);

    //방법 1. map의 projection 가져오기
    var projection = map.getProjection();

    //방법 2. projection 선언하기
    var projection = new odf.Projection({ EPSG: '5179' });
  ```

* [Projection](#Projection)
  * [new Projection(epsgCd)](#new_Projection_new)
  * [.project(point, epsgCd)](#Projection+project) ⇒ <code>Array</code>
  * [.unproject(point, epsgCd)](#Projection+unproject) ⇒ <code>Array</code>
  * [.projectGeom(feature, epsgCd)](#Projection+projectGeom) ⇒ <code>ODF\_Feature</code>
  * [.unprojectGeom(feature, epsgCd)](#Projection+unprojectGeom) ⇒ <code>ODF\_Feature</code>
  * [.projectExtent(extent, epsgCd)](#Projection+projectExtent) ⇒ <code>Array</code>
  * [.unprojectExtent(extent, epsgCd)](#Projection+unprojectExtent) ⇒ <code>Array</code>

<a name="new_Projection_new"></a>

### new Projection(epsgCd)

| Param | Type | Description |
| --- | --- | --- |
| epsgCd | <code>null</code> \| <code>JSON</code> | 사용할 EPSG 코드를 JSON 형태로 입력한다. |

<a name="Projection+project"></a>

### projection.project(point, epsgCd) ⇒ <code>Array</code>
좌표변환 시, 입력 좌표계 -> 지도 좌표계로 변환한다.
```javascript
let map = new odf.Map(...);
let projection = map.getProjection();
projection.project(point, epsgCd);
```

**Kind**: instance method of [<code>Projection</code>](#Projection)
**Returns**: <code>Array</code> - 변경된 좌표값(배열형태)

| Param | Type | Description |
| --- | --- | --- |
| point | <code>Array</code> | 변환하고자 하는 X,Y 좌표(배열형태) |
| epsgCd | <code>String</code> | 입력한 좌표의 좌표계 |

<a name="Projection+unproject"></a>

### projection.unproject(point, epsgCd) ⇒ <code>Array</code>
좌표변환 시, 지도 자표계 -> 입력 좌표계로 변환한다.
```javascript
let map = new odf.Map(...);
let projection = map.getProjection();
projection.unproject(point, epsgCd);
```

**Kind**: instance method of [<code>Projection</code>](#Projection)
**Returns**: <code>Array</code> - 변경된 좌표값(배열형태)

| Param | Type | Description |
| --- | --- | --- |
| point | <code>Array</code> | 변환하고자 하는 X,Y 좌표(배열형태) |
| epsgCd | <code>String</code> | 변경하고자 하는 좌표계 |

<a name="Projection+projectGeom"></a>

### projection.projectGeom(feature, epsgCd) ⇒ <code>ODF\_Feature</code>
도형정보(Geometry) 좌표변환 시, 입력 좌표계 -> 지도 자표계로 변환한다.
```javascript
let map = new odf.Map(...);
let projection = map.getProjection();
projection.projectGeom(feature, epsgCd);
```

**Kind**: instance method of [<code>Projection</code>](#Projection)
**Returns**: <code>ODF\_Feature</code> - 도형정보가 변경 된 ODF_Feature(배열형태)

| Param | Type | Description |
| --- | --- | --- |
| feature | <code>ODF\_Feature</code> | 변환하고자 하는 feature |
| epsgCd | <code>String</code> | 입력한 Feature의 좌표계 |

<a name="Projection+unprojectGeom"></a>

### projection.unprojectGeom(feature, epsgCd) ⇒ <code>ODF\_Feature</code>
도형정보(Geometry) 좌표변환 시, 지도 자표계 -> 입력 좌표계로 변환한다.
```javascript
let map = new odf.Map(...);
let projection = map.getProjection();
projection.unprojectGeom(feature, epsgCd);
```

**Kind**: instance method of [<code>Projection</code>](#Projection)
**Returns**: <code>ODF\_Feature</code> - 도형정보가 변경 된 ODF_Feature(배열형태)

| Param | Type | Description |
| --- | --- | --- |
| feature | <code>ODF\_Feature</code> | 변환하고자 하는 feature |
| epsgCd | <code>String</code> | 변경하고자 하는 좌표계 |

<a name="Projection+projectExtent"></a>

### projection.projectExtent(extent, epsgCd) ⇒ <code>Array</code>
Extent 좌표변환 시, 입력 좌표계 -> 지도 자표계로 변환한다.
```javascript
let map = new odf.Map(...);
let projection = map.getProjection();
projection.projectExtent(extent, epsgCd);
```

**Kind**: instance method of [<code>Projection</code>](#Projection)
**Returns**: <code>Array</code> - 변경된 Extent 값(배열형태)

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>Array</code> | 변환하고자 하는 Extent(배열형태) |
| epsgCd | <code>String</code> | 입력한 Extent의 좌표계 |

<a name="Projection+unprojectExtent"></a>

### projection.unprojectExtent(extent, epsgCd) ⇒ <code>Array</code>
Extent 좌표변환 시, 지도 자표계 -> 입력 좌표계로 변환한다.
```javascript
let map = new odf.Map(...);
let projection = map.getProjection();
projection.unprojectExtent(extent, epsgCd);
```

**Kind**: instance method of [<code>Projection</code>](#Projection)
**Returns**: <code>Array</code> - 변경된 Extent 값(배열형태)

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>Array</code> | 변환하고자 하는 Extent(배열형태) |
| epsgCd | <code>String</code> | 변경하고자 하는 좌표계 |


## Classes

<dl>
<dt><a href="#FeatureFactory">FeatureFactory</a></dt>
<dd><p>Feature 생성을 위한 FeatureFactory 클래스</p>
</dd>
</dl>

## Typedefs

<dl>
<dt><a href="#odf_feature_option">odf_feature_option</a> : <code>Object</code></dt>
<dd></dd>
</dl>

<a name="FeatureFactory"></a>

## FeatureFactory
Feature 생성을 위한 FeatureFactory 클래스

**Kind**: global class

* [FeatureFactory](#FeatureFactory)
  * [.fromGeoJson(GeoJson)](#FeatureFactory.fromGeoJson) ⇒ <code>Feature</code>
  * [.toWKT(feature)](#FeatureFactory.toWKT) ⇒ <code>JSON</code>
  * [.fromWKT(wkt, property)](#FeatureFactory.fromWKT) ⇒ <code>Feature</code>
  * [.produce(param)](#FeatureFactory.produce) ⇒ <code>Feature</code>

<a name="FeatureFactory.fromGeoJson"></a>

### FeatureFactory.fromGeoJson(GeoJson) ⇒ <code>Feature</code>
GeoJson을 이용한 Feature 생성
```javascript
let feature = odf.FeatureFactory.fromGeoJson(geoJson);
```

**Kind**: static method of [<code>FeatureFactory</code>](#FeatureFactory)
**Returns**: <code>Feature</code> - ODF_Feature 형태의 Feature

| Param | Type | Description |
| --- | --- | --- |
| GeoJson | <code>JSON</code> | Feature를 만들기 위한 GeoJson형태의 데이터 |

<a name="FeatureFactory.toWKT"></a>

### FeatureFactory.toWKT(feature) ⇒ <code>JSON</code>
feature 이용하여 WKT 생성
```javascript
let feature = odf.FeatureFactory.toWKT(feature);
```

**Kind**: static method of [<code>FeatureFactory</code>](#FeatureFactory)
**Returns**: <code>JSON</code> - WKT, Property를 담은 JSON 형태 값

| Param | Type | Description |
| --- | --- | --- |
| feature | <code>Feature</code> | WKT를 만들기 위한 ODF_Feature형태의 데이터 |

<a name="FeatureFactory.fromWKT"></a>

### FeatureFactory.fromWKT(wkt, property) ⇒ <code>Feature</code>
WKT 이용하여 feature 생성
```javascript
let feature = odf.FeatureFactory.fromWKT('MULTIPOLYGON(((948365.1609 1934203.3315,948359.501…8484.119 1934281.0984,948365.1609 1934203.3315)))', property);
```

**Kind**: static method of [<code>FeatureFactory</code>](#FeatureFactory)
**Returns**: <code>Feature</code> - ODF_Feature 형태의 Feature

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| wkt | <code>String</code> |  | WKT를 이용하여 Feature를 만들기 위한 String 형태의 WKT 데이터 |
| property | <code>JSON</code> | <code></code> | JSON 형태의 속성값 |

<a name="FeatureFactory.produce"></a>

### FeatureFactory.produce(param) ⇒ <code>Feature</code>
FeatureFactory를 이용한 Feature 생성
```javascript
//점 feature 생성
let pointFeature = odf.FeatureFactory.produce({
  geometryType: 'point',
       coordinates: [1099747.3552171164, 1715412.1323066188],
});
```
```javascript
//multiPoint feature 생성
let mPointFeature = odf.FeatureFactory.produce({
  geometryType: 'multipoint',
       coordinates: [
         [1101336.4199975003,1718354.3477830617],
         [1101355.5078627302,1718010.7662089246],
         [1101031.0141538228,1718182.556995993],
         [1101040.5580864379,1717838.975421856],
      ],
});
```
```javascript
//선 feature 생성
let lineFeature = odf.FeatureFactory.produce({
  geometryType: 'linestring',
       coordinates: [
          [1097339.898215003, 1715974.1804632691],
          [1097492.6011368418, 1715057.9629322367],
          [1099573.178446894, 1715668.7746195917],
          [1100608.6951356127, 1715038.875067007],
          [1101305.4022165018, 1715778.529844663],
        ],
});
```
```javascript
//multiline feature 생성
let mLineFeature = odf.FeatureFactory.produce({
  geometryType: 'multilinestring',
       coordinates: [
        [
          [1097802.7789468267,1717572.789176268],
          [1097621.4442271432,1717505.9816479636],
          [1097535.5488336089,1717315.1029956653],
          [1097549.8647325314,1717071.7327139848],
          [1097821.8668120564,1716947.6615899908],
          [1098093.8688915817,1717033.5569835252],
          [1098198.8521503457,1717367.5946250472],
          [1098031.8333295847,1717477.3498501189],
          [1097745.5153511371,1717472.5778838114],
          [1097630.988159758,1717291.243164128],
          [1097645.3040586805,1717133.7682759818],
          [1097812.3228794415,1717086.048612907],
          [1097974.5697338951,1717210.119736901],
          [1097955.4818686654,1717353.278726125],
          [1097817.094845749,1717310.3310293579],
          [1097840.9546772863,1717233.9795684384]
        ]
        ,[
          [1098590.1533875575,1717768.4397948738],
          [1098337.2391732621,1717749.3519296441],
          [1098284.74754388,1717568.0172099606],
          [1098408.818667874,1717200.5758042862],
          [1098833.523669238,1717114.6804107518],
          [1099105.5257487632,1717319.874961973],
          [1099134.157546608,1717658.6845698026],
          [1098924.1910290797,1717725.492098107],
          [1098509.0299603308,1717653.9126034952],
          [1098370.6429374146,1717458.2619848894],
          [1098518.5738929457,1717281.6992315133],
          [1098809.6638377008,1717286.4711978207],
          [1099014.8583889215,1717525.0695131938],
          [1098876.4713660053,1717630.0527719578],
          [1098628.3291180173,1717534.6134458086],
          [1098614.0132190948,1717381.91052397],
          [1098709.452545244,1717381.91052397],
          [1098823.9797366231,1717510.7536142713]
        ]
      ],
});
```
```javascript
//면 feature 생성
let polygonFeature = odf.FeatureFactory.produce({
  geometryType: 'polygon',
       coordinates: [
          [
            [1099959.7077177984, 1716260.498441717],
            [1100098.0947407146, 1716608.8519821614],
            [1100675.5026639171, 1716794.9586681523],
            [1101190.875025123, 1716561.132319087],
            [1101114.5235642034, 1716308.2181047916],
            [1100661.1867649949, 1715859.6532718902],
            [1099773.6010318075, 1715530.3875966757],
            [1099353.667996751, 1716160.2871492603],
            [1099277.3165358317, 1716584.992150624],
            [1099329.8081652136, 1717028.785017218],
            [1099730.6533350402, 1717119.4523770595],
            [1100083.7788417924, 1717024.0130509103],
            [1100145.8144037894, 1716713.8352409257],
            [1099959.7077177984, 1716260.498441717],
          ],
        ],
});
```
```javascript
//multipolygon 생성
let mPolygonFeature = odf.FeatureFactory.produce({
        geometryType: 'multipolygon',
        coordinates: [
          [
            [
              [1102899.2389631933, 1717024.0130509103],
              [1102999.45025565, 1717133.768275982],
              [1102975.5904241127, 1716985.8373204507],
              [1103142.6092448737, 1716947.661589991],
              [1102985.1343567276, 1716899.9419269164],
              [1103018.5381208798, 1716732.9231061554],
              [1102927.870761038, 1716847.4502975345],
              [1102846.7473338114, 1716728.151139848],
              [1102832.4314348889, 1716866.5381627642],
              [1102698.81637828, 1716966.749455221],
              [1102899.2389631933, 1717024.0130509103],
            ],
          ],
          [
            [
              [1103548.2263810078, 1717004.9251856806],
              [1103572.0862125452, 1717181.4879390565],
              [1103681.8414376169, 1717033.5569835252],
              [1103901.35188776, 1717071.7327139848],
              [1103786.8246963809, 1716938.117657376],
              [1103872.7200899152, 1716842.6783312266],
              [1103724.789134384, 1716880.8540616864],
              [1103610.2619430048, 1716670.8875441581],
              [1103605.4899766974, 1716866.538162764],
              [1103385.9795265542, 1716871.3101290714],
              [1103548.2263810078, 1717004.9251856806],
            ],
          ],
          [
            [
              [1103223.7326721007, 1716346.393835251],
              [1103223.7326721007, 1716494.324790782],
              [1103319.1719982498, 1716351.1658015584],
              [1103424.1552570139, 1716379.7975994032],
              [1103385.9795265542, 1716260.4984417167],
              [1103467.1029537811, 1716198.4628797197],
              [1103323.9439645573, 1716212.778778642],
              [1103228.504638408, 1716088.707654648],
              [1103199.8728405633, 1716212.778778642],
              [1103047.1699187246, 1716260.4984417167],
              [1103223.7326721007, 1716346.393835251],
            ],
          ],
        ],
      });
```
```javascript
//원 feature 생성
let circleFeature = odf.FeatureFactory.produce({
  geometryType: 'circle',
       coordinates: [1099747.3552171164, 1715412.1323066188],
       circleSize : 1000
});
```

**Kind**: static method of [<code>FeatureFactory</code>](#FeatureFactory)
**Returns**: <code>Feature</code> - 생성된 피쳐

| Param | Type | Description |
| --- | --- | --- |
| param | [<code>odf\_feature\_option</code>](#odf_feature_option) \| <code>ol.Feature</code> | Feature 생성 옵션 |

<a name="odf_feature_option"></a>

## odf\_feature\_option : <code>Object</code>
**Kind**: global typedef
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| geometryType | <code>odf\_geometryType</code> | 지오메트리 타입 |
| coordinates | <code>odf\_coordinate</code> \| <code>Array.&lt;odf\_coordinate&gt;</code> \| <code>Array.&lt;Array.&lt;odf\_coordinate&gt;&gt;</code> \| <code>Array.&lt;Array.&lt;Array.&lt;odf\_coordinate&gt;&gt;&gt;</code> | 지오메트리의 좌표정보 |
| circleSize | <code>number</code> | (선택)geometry type이 'circle'일 경우 원의 크기 |
| properties | <code>Object</code> | (선택)feature에서 관리할 정보 |
| style | <code>StyleFunction</code> \| <code>Style</code> | (선택)Tile 레이어 생성 |


<a name="Extent"></a>

## Extent
영역 관련 클래스

**Kind**: global class

* [Extent](#Extent)
  * [.boundingExtent(coordinates)](#Extent.boundingExtent) ⇒ <code>odf\_extent</code>
  * [.buffer(extent, value, opt_extent)](#Extent.buffer)
  * [.containsCoordinate(extent, coordinate)](#Extent.containsCoordinate) ⇒ <code>Boolean</code>
  * [.containsExtent(extent1, extent2)](#Extent.containsExtent) ⇒ <code>Boolean</code>
  * [.containsXY(extent, x, y)](#Extent.containsXY) ⇒ <code>Boolean</code>
  * [.createEmpty()](#Extent.createEmpty) ⇒ <code>odf\_extent</code>
  * [.equals(extent1, extent2)](#Extent.equals) ⇒ <code>Boolean</code>
  * [.extend(extent1, extent2)](#Extent.extend) ⇒ <code>odf\_extent</code>
  * [.getArea(extent)](#Extent.getArea) ⇒ <code>Number</code>
  * [.getBottomLeft(extent)](#Extent.getBottomLeft) ⇒ <code>odf\_coordinate</code>
  * [.getBottomRight(extent)](#Extent.getBottomRight) ⇒ <code>odf\_coordinate</code>
  * [.getCenter(extent)](#Extent.getCenter) ⇒ <code>odf\_coordinate</code>
  * [.getHeight(extent)](#Extent.getHeight) ⇒ <code>Number</code>
  * [.getSize(extent)](#Extent.getSize) ⇒ <code>Array.&lt;Number&gt;</code>
  * [.getTopLeft(extent)](#Extent.getTopLeft) ⇒ <code>odf\_coordinate</code>
  * [.getTopRight(extent)](#Extent.getTopRight) ⇒ <code>odf\_coordinate</code>
  * [.getWidth(extent)](#Extent.getWidth) ⇒ <code>Number</code>
  * [.intersects(extent1, extent2)](#Extent.intersects) ⇒ <code>Boolean</code>
  * [.isEmpty(extent)](#Extent.isEmpty) ⇒ <code>Boolean</code>
  * [.boundingBoxCoordinates(extent)](#Extent.boundingBoxCoordinates) ⇒ <code>Array.&lt;odf\_coordinate&gt;</code>

<a name="Extent.boundingExtent"></a>

### Extent.boundingExtent(coordinates) ⇒ <code>odf\_extent</code>
주어진 모든 좌표를 포함하는 범위 계산
```javascript
      let extent = odf.Extent.boundingExtent([
        [1097802.7789468267,1717572.789176268],
        [1097621.4442271432,1717505.9816479636],
        [1097535.5488336089,1717315.1029956653],
        [1097549.8647325314,1717071.7327139848],
        [1097821.8668120564,1716947.6615899908]
      ]);
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>odf\_extent</code> - 계산된 extent 값

| Param | Type |
| --- | --- |
| coordinates | <code>Array.&lt;odf\_coordinate&gt;</code> |

<a name="Extent.buffer"></a>

### Extent.buffer(extent, value, opt_extent)
extent를 버퍼링
```javascript
      let extent = [10,0,40,30];
      let bufferExtent = []
      odf.Extent.buffer(extent,30,bufferExtent);
      console.log(bufferExtent);//[-20,-30,70,60]
```

**Kind**: static method of [<code>Extent</code>](#Extent)

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>odf\_extent</code> | 확장 대상 extent. opt_extent 매개변수를 입력하지 않으면 extent에 적용됨 |
| value | <code>Number</code> | 버퍼링할 값 |
| opt_extent | <code>odf\_extent</code> | 확장된 extent값을 적용할 배열 |

<a name="Extent.containsCoordinate"></a>

### Extent.containsCoordinate(extent, coordinate) ⇒ <code>Boolean</code>
특정 좌표가 특정 영역에 포함되있는지 여부를 확인
```javascript
let extent = [10,0,40,30];
      console.log(odf.Extent.containsCoordinate(extent,[11,25]));//true
      console.log(odf.Extent.containsCoordinate(extent,[11,40]));//false
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>Boolean</code> - - 포함여부

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>odf\_extent</code> | 검사 대상 영역 |
| coordinate | <code>odf\_coordinate</code> | 검사할 좌표 |

<a name="Extent.containsExtent"></a>

### Extent.containsExtent(extent1, extent2) ⇒ <code>Boolean</code>
extent2 영역이 extent1 영역에 포함되는지 여부 확인
```javascript
let extent = [10,0,40,30];
      console.log(odf.Extent.buffer(extent,[11,25,12,26]));//true
      console.log(odf.Extent.buffer(extent,[11,40,12,42]));//false
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>Boolean</code> - - 포함여부

| Param | Type | Description |
| --- | --- | --- |
| extent1 | <code>odf\_extent</code> | 검사 대상 영역 |
| extent2 | <code>odf\_extent</code> | 검사할 영역 |

<a name="Extent.containsXY"></a>

### Extent.containsXY(extent, x, y) ⇒ <code>Boolean</code>
[x,y] 좌표가 extent 영역에 포함되는지 여부 확인
```javascript
let extent = [10,0,40,30];
      console.log(odf.Extent.containsXY(extent,11,20));//true
      console.log(odf.Extent.containsXY(extent,44,80));//false
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>Boolean</code> - - 포함여부

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>odf\_extent</code> | 검사 대상 영역 |
| x | <code>Number</code> | 검사할 좌표의 x좌표 |
| y | <code>Number</code> | 검사할 좌표의 y좌표 |

<a name="Extent.createEmpty"></a>

### Extent.createEmpty() ⇒ <code>odf\_extent</code>
빈 영역 생성
```javascript
      console.log(odf.Extent.createEmpty());//[Infinity, Infinity, -Infinity, -Infinity]
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>odf\_extent</code> - - 빈 영역
<a name="Extent.equals"></a>

### Extent.equals(extent1, extent2) ⇒ <code>Boolean</code>
extent1과 extent2를 비교
```javascript
let extent1 = [10,0,40,30];
let extent2 = [10,0,40,38];
      console.log(odf.Extent.equals(extent1,extent1));//true
      console.log(odf.Extent.equals(extent1,extent2));//false
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>Boolean</code> - - 비교결과

| Param | Type | Description |
| --- | --- | --- |
| extent1 | <code>odf\_extent</code> | 비교대상 영역 1 |
| extent2 | <code>odf\_extent</code> | 비교대상 영역 2 |

<a name="Extent.extend"></a>

### Extent.extend(extent1, extent2) ⇒ <code>odf\_extent</code>
extent1과 extent2를 모두 포함하는 영역으로 확장. extent1의 값이 변경됨
```javascript
let extent1 = [10,0,40,30];
let extent2 = [10,0,40,38];
      console.log(odf.Extent.extend(extent1,extent2));//[10,0,40,38]
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>odf\_extent</code> - - 확장된 영역

| Param | Type | Description |
| --- | --- | --- |
| extent1 | <code>odf\_extent</code> | 확장 대상 영역 1. 확장된 영역이 덮어쓰기됨 |
| extent2 | <code>odf\_extent</code> | 확장 대상 영역 2 |

<a name="Extent.getArea"></a>

### Extent.getArea(extent) ⇒ <code>Number</code>
영역의 크기 계산
```javascript
let extent = [10,0,40,30];
      console.log(odf.Extent.getArea(extent));//(40-10)*(30-0)=900
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>Number</code> - - 범위의 크기

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>odf\_extent</code> | 크기 산정 대상 영역 |

<a name="Extent.getBottomLeft"></a>

### Extent.getBottomLeft(extent) ⇒ <code>odf\_coordinate</code>
영역의 왼쪽 아래 꼭지점 좌표 조회
```javascript
let extent = [10,0,40,30];
      console.log(odf.Extent.getBottomLeft(extent));//[10, 0]
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>odf\_coordinate</code> - - 영역의 왼쪽 아래 꼭지점 좌표

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>odf\_extent</code> | 대상 영역 |

<a name="Extent.getBottomRight"></a>

### Extent.getBottomRight(extent) ⇒ <code>odf\_coordinate</code>
영역의 오른쪽 아래 꼭지점 좌표 조회
```javascript
let extent = [10,0,40,30];
      console.log(odf.Extent.getBottomRight(extent));//[40, 0]
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>odf\_coordinate</code> - - 영역의 오른쪽 아래 꼭지점 좌표

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>odf\_extent</code> | 대상 영역 |

<a name="Extent.getCenter"></a>

### Extent.getCenter(extent) ⇒ <code>odf\_coordinate</code>
영역의 중심 좌표 조회
```javascript
let extent = [10,0,40,30];
      console.log(odf.Extent.getTopRight(extent));//[25, 15]
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>odf\_coordinate</code> - - 영역의 줌심 좌표

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>odf\_extent</code> | 대상 영역 |

<a name="Extent.getHeight"></a>

### Extent.getHeight(extent) ⇒ <code>Number</code>
영역의 높이 조회
```javascript
let extent = [10,0,40,30];
      console.log(odf.Extent.getHeight(extent));//30
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>Number</code> - - 영역의 높이

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>odf\_extent</code> | 대상 영역 |

<a name="Extent.getSize"></a>

### Extent.getSize(extent) ⇒ <code>Array.&lt;Number&gt;</code>
영역의 높이,너비 조회
```javascript
let extent = [10,0,40,30];
      console.log(odf.Extent.getSize(extent));//[30,30]
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>Array.&lt;Number&gt;</code> - - 영역의 높이,너비

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>odf\_extent</code> | 대상 영역 |

<a name="Extent.getTopLeft"></a>

### Extent.getTopLeft(extent) ⇒ <code>odf\_coordinate</code>
영역의 왼쪽 위 꼭지점 좌표 조회
```javascript
let extent = [10,0,40,30];
      console.log(odf.Extent.getTopLeft(extent));//[10, 30]
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>odf\_coordinate</code> - - 영역의 왼쪽 위 꼭지점 좌표

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>odf\_extent</code> | 대상 영역 |

<a name="Extent.getTopRight"></a>

### Extent.getTopRight(extent) ⇒ <code>odf\_coordinate</code>
영역의 오른쪽 위 꼭지점 좌표 조회
```javascript
let extent = [10,0,40,30];
      console.log(odf.Extent.getTopRight(extent));//[40, 30]
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>odf\_coordinate</code> - - 영역의 오른쪽 위 꼭지점 좌표

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>odf\_extent</code> | 대상 영역 |

<a name="Extent.getWidth"></a>

### Extent.getWidth(extent) ⇒ <code>Number</code>
영역의 너비 조회
```javascript
let extent = [10,0,40,30];
      console.log(odf.Extent.getWidth(extent));//30
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>Number</code> - - 영역의 높이

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>odf\_extent</code> | 대상 영역 |

<a name="Extent.intersects"></a>

### Extent.intersects(extent1, extent2) ⇒ <code>Boolean</code>
extent1 영역과 extent2 영역의 교차 여부 조회
```javascript
let extent1 = [10,0,40,30];
let extent2 = [20,10,50,40];
let extent3 = [60,50,80,70];
      console.log(odf.Extent.intersects(extent1,extent2));//true
      console.log(odf.Extent.intersects(extent1,extent3));//false
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>Boolean</code> - - extent1 영역과 extent2 영역의 교차 여부

| Param | Type | Description |
| --- | --- | --- |
| extent1 | <code>odf\_extent</code> | 대상 영역 1 |
| extent2 | <code>odf\_extent</code> | 대상 영역 2 |

<a name="Extent.isEmpty"></a>

### Extent.isEmpty(extent) ⇒ <code>Boolean</code>
영역이 비어있는지 확인
```javascript
let extent1 = [10,0,40,30];
let extent2 = odf.Extent.createEmpty();
      console.log(odf.Extent.isEmpty(extent1));//false
      console.log(odf.Extent.isEmpty(extent2));//true
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>Boolean</code> - - 비어있는지 여부

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>odf\_extent</code> | 비었는지 확인할 영역 |

<a name="Extent.boundingBoxCoordinates"></a>

### Extent.boundingBoxCoordinates(extent) ⇒ <code>Array.&lt;odf\_coordinate&gt;</code>
해당 영역을 box 형태의 좌표값으로 계산
```javascript
      let boundBoxCoordinates = odf.Extent.boundingExtent([1097802.7789468267,1717572.789176268,1097621.4442271432,1717505.9816479636]]);
```

**Kind**: static method of [<code>Extent</code>](#Extent)
**Returns**: <code>Array.&lt;odf\_coordinate&gt;</code> - 계산된 box 형태의 좌표값

| Param | Type | Description |
| --- | --- | --- |
| extent | <code>odf\_extent</code> | 계산 대상 영역 |


<a name="Easing"></a>

## Easing
애니메이션 효과

**Kind**: global class

* [Easing](#Easing)
  * [.easeIn(percent)](#Easing.easeIn) ⇒ <code>Number</code>
  * [.easeOut(percent)](#Easing.easeOut) ⇒ <code>Number</code>
  * [.inAndOut(percent)](#Easing.inAndOut) ⇒ <code>Number</code>
  * [.upAndDown(percent)](#Easing.upAndDown) ⇒ <code>Number</code>
  * [.linear(percent)](#Easing.linear) ⇒ <code>Number</code>

<a name="Easing.easeIn"></a>

### Easing.easeIn(percent) ⇒ <code>Number</code>
천천히 시작해서 점점 속도를 올림
```javascript
      odf.Easing.easeIn(0.0)//0
      odf.Easing.easeIn(0.1)//0.0010000000000000002
      odf.Easing.easeIn(0.2)//0.008000000000000002
      odf.Easing.easeIn(0.3)//0.02700000000000001
      odf.Easing.easeIn(0.4)//0.06400000000000002
      odf.Easing.easeIn(0.5)//0.125
      odf.Easing.easeIn(0.6)//0.21600000000000008
      odf.Easing.easeIn(0.7)//0.3430000000000001
      odf.Easing.easeIn(0.8)//0.5120000000000001
      odf.Easing.easeIn(0.9)//0.7290000000000001
      odf.Easing.easeIn(1.0)//1
```

**Kind**: static method of [<code>Easing</code>](#Easing)
**Returns**: <code>Number</code> - 0에서 1 사이의 값

| Param | Type | Description |
| --- | --- | --- |
| percent | <code>Number</code> | 0에서 1 사이의 값 |

<a name="Easing.easeOut"></a>

### Easing.easeOut(percent) ⇒ <code>Number</code>
빠르게 시작해서 점점 속도를 내림
```javascript
      odf.Easing.easeOut(0.0) // 0
      odf.Easing.easeOut(0.1) // 0.2709999999999999
      odf.Easing.easeOut(0.2) // 0.4879999999999999
      odf.Easing.easeOut(0.3) // 0.657
      odf.Easing.easeOut(0.4) // 0.784
      odf.Easing.easeOut(0.5) // 0.875
      odf.Easing.easeOut(0.6) // 0.936
      odf.Easing.easeOut(0.7) // 0.973
      odf.Easing.easeOut(0.8) // 0.992
      odf.Easing.easeOut(0.9) // 0.999
      odf.Easing.easeOut(1.0) // 1
```

**Kind**: static method of [<code>Easing</code>](#Easing)
**Returns**: <code>Number</code> - 0에서 1 사이의 값

| Param | Type | Description |
| --- | --- | --- |
| percent | <code>Number</code> | 0에서 1 사이의 값 |

<a name="Easing.inAndOut"></a>

### Easing.inAndOut(percent) ⇒ <code>Number</code>
천천히 시작해서 속도를 올렸다가 속도를 내림
```javascript
      odf.Easing.inAndOut(0.0) // 0
      odf.Easing.inAndOut(0.1) // 0.028000000000000004
      odf.Easing.inAndOut(0.2) // 0.10400000000000002
      odf.Easing.inAndOut(0.3) // 0.21600000000000005
      odf.Easing.inAndOut(0.4) // 0.3520000000000001
      odf.Easing.inAndOut(0.5) // 0.5
      odf.Easing.inAndOut(0.6) // 0.6480000000000001
      odf.Easing.inAndOut(0.7) // 0.784
      odf.Easing.inAndOut(0.8) // 0.8960000000000001
      odf.Easing.inAndOut(0.9) // 0.972
      odf.Easing.inAndOut(1.0) // 1
```

**Kind**: static method of [<code>Easing</code>](#Easing)
**Returns**: <code>Number</code> - 0에서 1 사이의 값

| Param | Type | Description |
| --- | --- | --- |
| percent | <code>Number</code> | 0에서 1 사이의 값 |

<a name="Easing.upAndDown"></a>

### Easing.upAndDown(percent) ⇒ <code>Number</code>
천천히 시작해서 속도를 올렸다가 속도를 내림
```javascript
      odf.Easing.upAndDown(0.0) // 0
      odf.Easing.upAndDown(0.1) // 0.10400000000000002
      odf.Easing.upAndDown(0.2) // 0.3520000000000001
      odf.Easing.upAndDown(0.3) // 0.6480000000000001
      odf.Easing.upAndDown(0.4) // 0.8960000000000001
      odf.Easing.upAndDown(0.5) // 1
      odf.Easing.upAndDown(0.6) // 0.8959999999999998
      odf.Easing.upAndDown(0.7) // 0.6479999999999998
      odf.Easing.upAndDown(0.8) // 0.35199999999999987
      odf.Easing.upAndDown(0.9) // 0.10399999999999987
      odf.Easing.upAndDown(1.0) // 0
```

**Kind**: static method of [<code>Easing</code>](#Easing)
**Returns**: <code>Number</code> - 0에서 1 사이의 값

| Param | Type | Description |
| --- | --- | --- |
| percent | <code>Number</code> | 0에서 1 사이의 값 |

<a name="Easing.linear"></a>

### Easing.linear(percent) ⇒ <code>Number</code>
일정한 속도를 유지
```javascript
      odf.Easing.linear(0.0) // 0
      odf.Easing.linear(0.1) // 0.1
      odf.Easing.linear(0.2) // 0.2
      odf.Easing.linear(0.3) // 0.30000000000000004
      odf.Easing.linear(0.4) // 0.4
      odf.Easing.linear(0.5) // 0.5
      odf.Easing.linear(0.6) // 0.6000000000000001
      odf.Easing.linear(0.7) // 0.7000000000000001
      odf.Easing.linear(0.8) // 0.8
      odf.Easing.linear(0.9) // 0.9
      odf.Easing.linear(1.0) // 1
```

**Kind**: static method of [<code>Easing</code>](#Easing)
**Returns**: <code>Number</code> - 0에서 1 사이의 값

| Param | Type | Description |
| --- | --- | --- |
| percent | <code>Number</code> | 0에서 1 사이의 값 |



<a name="BookmarkControl"></a>

## BookmarkControl
북마크 컨트롤 생성 클래스

**Kind**: global class
**Summary**: BookmarkControl 생성자

```javascript
 let bookmarkControl = new odf.BookmarkControl([
{zoom : 14, center : [36,127], name : '북마크1'},
]);//BookmarkControl 생성
```

* [BookmarkControl](#BookmarkControl)
  * [new BookmarkControl(options)](#new_BookmarkControl_new)
  * [.setMap(_map, createElementFlag)](#BookmarkControl+setMap)
  * [.removeMap()](#BookmarkControl+removeMap)
  * [.getBookmarkList()](#BookmarkControl+getBookmarkList) ⇒ <code>Object</code>
  * [.removeBookmark()](#BookmarkControl+removeBookmark)
  * [.addbookmark([Object])](#BookmarkControl+addbookmark)

<a name="new_BookmarkControl_new"></a>

### new BookmarkControl(options)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Array</code> \| <code>Object</code> | 북마크 초기값 리스트 |
| options.zoom | <code>Number</code> | 북마크 줌레벨 |
| options.center | <code>Coordinate</code> | 북마크 지도 중심 좌표 값 |
| options.name | <code>String</code> | 북마크 명칭 |

<a name="BookmarkControl+setMap"></a>

### bookmarkControl.setMap(_map, createElementFlag)
BookmarkControl map 객체 연결
clear HTMLElement를 만들고, 이벤트 바인딩 시켜, map 객체와 연결

 ```javascript
 let bookmarkControl = new odf.BookmarkControl();//BookmarkControl 생성
 bookmarkControl.setMap(map,createElementFlag); //BookmarkControl 객체에 map 객체 연결
```

**Kind**: instance method of [<code>BookmarkControl</code>](#BookmarkControl)

| Param | Type | Description |
| --- | --- | --- |
| _map | <code>Map</code> | BookmarkControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | BookmarkControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x |

<a name="BookmarkControl+removeMap"></a>

### bookmarkControl.removeMap()
BookmarkControl map 객체 연결 해제

```javascript
 bookmarkControl.removeMap(); //bookmarkControl 객체에 map 연결 해제
```

**Kind**: instance method of [<code>BookmarkControl</code>](#BookmarkControl)
<a name="BookmarkControl+getBookmarkList"></a>

### bookmarkControl.getBookmarkList() ⇒ <code>Object</code>
BookmarkControl 북마크 리스트 조회

```javascript
 bookmarkControl.getBookmarkList();
```

**Kind**: instance method of [<code>BookmarkControl</code>](#BookmarkControl)
**Returns**: <code>Object</code> - 추가된 북마크 리스트
<a name="BookmarkControl+removeBookmark"></a>

### bookmarkControl.removeBookmark()
BookmarkControl 북마크 삭제

```javascript
 bookmarkControl.removeBookmark(); //마지막으로 추가된 북마크 삭제
```

**Kind**: instance method of [<code>BookmarkControl</code>](#BookmarkControl)
<a name="BookmarkControl+addbookmark"></a>

### bookmarkControl.addbookmark([Object])
BookmarkControl 북마크 추가

```javascript
 bookmarkControl.addbookmark(object); //북마크 리스트에 추가
```

**Kind**: instance method of [<code>BookmarkControl</code>](#BookmarkControl)

| Param | Type | Description |
| --- | --- | --- |
| [Object] | <code>Object</code> | 북마크 추가 Object {zoom : 줌레벨 , center : [x,y], name : '명칭'} |


<a name="HomeControl"></a>

## HomeControl
홈 이동 클래스

**Kind**: global class
**Summary**: HomeControl 생성자


 ```javascript
 let homeControl = new odf.HomeControl(options);//HomeControl 생성
```

* [HomeControl](#HomeControl)
  * [new HomeControl(options)](#new_HomeControl_new)
  * [.setMap(_map, createElementFlag)](#HomeControl+setMap)
  * [.removeMap()](#HomeControl+removeMap)
  * [.moveHome()](#HomeControl+moveHome)
  * [.changeOption(options)](#HomeControl+changeOption)

<a name="new_HomeControl_new"></a>

### new HomeControl(options)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | homeControl 생성 옵션 //입력 없을 시 맵 옵션 초기값 |
| options.zoom | <code>Number</code> | 줌 레벨 값 |
| options.resolution | <code>Number</code> | 레졸루션 값 (zoom 보다 우선적용) |
| options.center | <code>Coordinate</code> | 중심점 좌표 |
| options.projection | <code>String</code> | 중심점 좌표계 정보(EPSG:XXXX 형식) |

<a name="HomeControl+setMap"></a>

### homeControl.setMap(_map, createElementFlag)
HomeControl map 객체 연결
clear HTMLElement를 만들고, 이벤트 바인딩 시켜, map 객체와 연결

 ```javascript
 let homeControl = new odf.HomeControl(options);//HomeControl 생성
 homeControl.setMap(map,createElementFlag); //HomeControl 객체에 map 객체 연결
```

**Kind**: instance method of [<code>HomeControl</code>](#HomeControl)

| Param | Type | Description |
| --- | --- | --- |
| _map | <code>Map</code> | HomeControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | HomeControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x |

<a name="HomeControl+removeMap"></a>

### homeControl.removeMap()
HomeControl map 객체 연결 해제

```javascript
 homeControl.removeMap(); //HomeControl 객체에 map 연결 해제
```

**Kind**: instance method of [<code>HomeControl</code>](#HomeControl)
<a name="HomeControl+moveHome"></a>

### homeControl.moveHome()
설정 위치 홈으로 이동

```javascript
 homeControl.moveHome();
```

**Kind**: instance method of [<code>HomeControl</code>](#HomeControl)
<a name="HomeControl+changeOption"></a>

### homeControl.changeOption(options)
옵션 변경

**Kind**: instance method of [<code>HomeControl</code>](#HomeControl)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | 설정을 변경할 obeject |
| options.center | <code>Array</code> | 변경할 지도 중심좌표 |
| options.zoom | <code>Number</code> | 변경할 지도 확대레벨 ```javascript HomeControl.changeOption(options) ``` |

