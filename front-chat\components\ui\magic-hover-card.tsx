"use client";

import React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";
import { MagicCard } from "@/components/magicui/magic-card";

interface MagicHoverCardProps {
  children: React.ReactNode;
  content: React.ReactNode;
  className?: string;
  gradientSize?: number;
  gradientColor?: string;
  gradientOpacity?: number;
  gradientFrom?: string;
  gradientTo?: string;
  side?: "top" | "right" | "bottom" | "left";
  align?: "start" | "center" | "end";
}

export function MagicHoverCard({
  children,
  content,
  className,
  gradientSize = 200,
  gradientColor = "#262626",
  gradientOpacity = 0.8,
  gradientFrom = "#9E7AFF",
  gradientTo = "#FE8BBB",
  side = "top",
  align = "center",
}: MagicHoverCardProps) {
  return (
    <HoverCard openDelay={100} closeDelay={100}>
      <HoverCardTrigger asChild>
        {children}
      </HoverCardTrigger>
      <HoverCardContent
        side={side}
        align={align}
        className={cn("p-0 border-0 z-[100]", className)}
        sideOffset={8}
        alignOffset={-50}
        avoidCollisions={true}
        collisionBoundary={document.body}
      >
        <MagicCard gradientColor={"#D9D9D955"}>

          {content}
        </MagicCard>
      </HoverCardContent>
    </HoverCard>
  );
}

// 스마트 지도 제어 전용 컴포넌트
export function SmartNavigationHoverCard({
  children,
  isEnabled,
}: {
  children: React.ReactNode;
  isEnabled: boolean;
}) {
  const content = (
    <motion.div
      className="space-y-4 w-72"
      initial={{ opacity: 0, y: 8 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1, duration: 0.3, ease: "easeOut" }}
    >
      {/* Header with enhanced navigation icon */}
      <div className="flex items-center gap-3">
        <motion.div
          className={cn(
            "relative p-2.5 rounded-xl transition-all duration-300",
            isEnabled
              ? "bg-gradient-to-br from-emerald-50 to-green-50 text-emerald-600 shadow-sm border border-emerald-100"
              : "bg-gray-50 text-gray-400 border border-gray-200"
          )}
        >
          <motion.div
            className={cn(
              "w-2 h-2 rounded-full absolute -top-1 -right-1",
              isEnabled ? "bg-emerald-500" : "bg-gray-400"
            )}
            animate={{
              scale: isEnabled ? [1, 1.3, 1] : 1,
              opacity: isEnabled ? [0.7, 1, 0.7] : 0.5,
            }}
            transition={{
              duration: 2.5,
              repeat: isEnabled ? Infinity : 0,
              ease: "easeInOut",
            }}
          />
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
            />
          </svg>
        </motion.div>

        <div className="flex-1">
          <h4 className={cn(
            "text-sm font-semibold transition-colors",
            isEnabled ? "text-gray-900" : "text-gray-500"
          )}>
            스마트 지도 제어 {isEnabled ? "활성화" : "비활성화"}
          </h4>
        </div>
      </div>

      {/* Feature highlights */}
      <div className="space-y-2">
        <div className="flex items-center gap-2 text-xs">
          <div className={cn(
            "w-1.5 h-1.5 rounded-full",
            isEnabled ? "bg-emerald-500" : "bg-gray-300"
          )} />
          <span className={cn(
            "transition-colors",
            isEnabled ? "text-gray-700" : "text-gray-400"
          )}>
            자동 지도 위치 이동
          </span>
        </div>
        <div className="flex items-center gap-2 text-xs">
          <div className={cn(
            "w-1.5 h-1.5 rounded-full",
            isEnabled ? "bg-emerald-500" : "bg-gray-300"
          )} />
          <span className={cn(
            "transition-colors",
            isEnabled ? "text-gray-700" : "text-gray-400"
          )}>
            자동 레이어 추가
          </span>
        </div>
      </div>
    </motion.div>
  );

  return (
    <HoverCard openDelay={150} closeDelay={200}>
      <HoverCardTrigger asChild>
        {children}
      </HoverCardTrigger>
      <HoverCardContent
        side="top"
        align="center"
        className="p-0 border-0 bg-transparent shadow-none z-[100]"
        sideOffset={12}
        alignOffset={-50}
        avoidCollisions={true}
        collisionBoundary={typeof document !== 'undefined' ? document.body : undefined}
      >

          <MagicCard
            className={cn(
              "border-0 shadow-xl backdrop-blur-md p-4 overflow-hidden",
              isEnabled
                ? "bg-gradient-to-br from-white/95 to-emerald-50/95"
                : "bg-white/95"
            )}
            gradientSize={120}
            gradientColor={isEnabled ? "#10b981" : "#6b7280"}
            gradientOpacity={isEnabled ? 0.15 : 0.08}
            gradientFrom={isEnabled ? "#10b981" : "#6b7280"}
            gradientTo={isEnabled ? "#059669" : "#9ca3af"}
          >
              {content}
          </MagicCard>
      </HoverCardContent>
    </HoverCard>
  );
}
