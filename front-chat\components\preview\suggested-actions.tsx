'use client';

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ChatRequestOptions, CreateMessage, Message } from 'ai';
import { memo } from 'react';

interface SuggestedActionsProps {
  chatId: string;
  append: (
    message: Message | CreateMessage,
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  selectedModelId?: string;
}

// 모델별 제안 액션 정의
const modelSuggestedActions = {
  '지자체 공간정보 플랫폼 챗봇': [
    {
      title: '지원 데이터',
      label: '어떤 데이터를 지원하나요?',
      action: '어떤 데이터를 지원하나요?',
    },
    {
      title: '플랫폼 목적',
      label: '무엇을 위한 플랫폼인가요?',
      action: '무엇을 위한 플랫폼인가요?',
    },
    {
      title: '지도공간 기능',
      label: '지도공간에서는 어떤 기능을 볼 수 있나요?',
      action: '지도공간에서는 어떤 기능을 볼 수 있나요?',
    },
    {
      title: '업무공간 서비스',
      label: '업무공간에는 어떤 서비스가 있나요?',
      action: '업무공간에는 어떤 서비스가 있나요?',
    },
  ],
  '지도개발 어시스턴트': [
    {
      title: '지도 생성하기',
      label: 'ODF를 활용한 지도 만들기',
      action: 'ODF를 사용해서 지도를 생성하는 방법을 알려주세요.',
    },
    {
      title: '배경지도 컨트롤 추가',
      label: '지도에 배경지도 컨트롤을 추가하는 방법',
      action: '배경지도 컨트롤을 추가한 지도를 보여주세요.',
    },
  ],
};

function PureSuggestedActions({ chatId, append, selectedModelId = '지도개발 어시스턴트' }: SuggestedActionsProps) {
  // 선택된 모델에 따라 제안 액션 가져오기
  const suggestedActions = modelSuggestedActions[selectedModelId as keyof typeof modelSuggestedActions] ||
                           modelSuggestedActions['지도개발 어시스턴트'];

  // 지자체 공간정보 플랫폼 챗봇의 경우 4개 액션을 모두 표시
  const isGovPlatform = selectedModelId === '지자체 공간정보 플랫폼 챗봇';

  return (
    <div className="grid sm:grid-cols-2 gap-2 w-full">
      {suggestedActions.map((suggestedAction, index) => (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ delay: 0.05 * index }}
          key={`suggested-action-${suggestedAction.title}-${index}`}
          className={!isGovPlatform && index > 1 ? 'hidden sm:block' : 'block'}
        >
          <Button
            variant="ghost"
            onClick={async () => {
              // window.history.replaceState({}, '', `/chat/${chatId}`);

              append({
                role: 'user',
                content: suggestedAction.action,
              });
            }}
            className="text-left border rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start"
          >
            <span className="font-medium">{suggestedAction.title}</span>
            <span className="text-muted-foreground">
              {suggestedAction.label}
            </span>
          </Button>
        </motion.div>
      ))}
    </div>
  );
}

export const SuggestedActions = memo(PureSuggestedActions, () => true);
