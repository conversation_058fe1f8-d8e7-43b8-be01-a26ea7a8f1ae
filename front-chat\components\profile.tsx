'use client'

import {signOut, useSession} from "next-auth/react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {ChevronsUpDown, LogOut, LucideLogIn, Settings, User} from "lucide-react";
import {SidebarMenuButton} from "@/components/ui/sidebar";
import {Avatar, AvatarFallback, AvatarImage} from "@/components/ui/avatar";
import * as React from "react";
import {ThemeToggle} from "@/components/theme-toggle";
import Link from "next/link";
import { type User as NextAuthUser } from 'next-auth';

export default function Profile({ user }: { user: NextAuthUser | undefined }) {

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarMenuButton
            size="lg"
            className="group data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={user?.image ?? undefined}
                alt="사용자이미지"
              />
              <AvatarFallback className="bg-muted">
                <User className="h-4 w-4 text-muted-foreground" />
              </AvatarFallback>
            </Avatar>
            {user &&
              <div className="flex flex-col items-start text-left">
                <span className="text-sm font-medium">{user.name}</span>
                <span className="text-xs text-muted-foreground">{user.email}</span>
              </div>
            }
            <ChevronsUpDown className="ml-auto h-4 w-4 transition-transform duration-200"/>
          </SidebarMenuButton>
        </DropdownMenuTrigger>

        <DropdownMenuContent className="w-56" align="end" side="top">
          <DropdownMenuLabel className="flex justify-between items-center font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">{user?.name}</p>
              <p className="text-xs leading-none text-muted-foreground">{user?.email}</p>
            </div>
            <ThemeToggle/>
          </DropdownMenuLabel>
          <DropdownMenuSeparator/>
          <DropdownMenuGroup>
            <DropdownMenuItem className={"gap-2"}>
              <User className="h-4 w-4"/>
              <span>계정</span>
            </DropdownMenuItem>
            <DropdownMenuItem className={"gap-2"}>
              <Settings className="h-4 w-4"/>
              <span>설정</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>

          <DropdownMenuSeparator/>

          <DropdownMenuGroup>
            {user &&
              <DropdownMenuItem
                className="flex w-full text-red-500 gap-2"
                onClick={() => signOut({redirectTo: '/',})}
              >
                <LogOut className="h-4 w-4" /> {"로그아웃"}
              </DropdownMenuItem>
            }

            {/* 로그인 버튼 */}
            {!user &&
              <DropdownMenuItem>
                <Link
                  href={'/login'}
                  className={'flex w-full hover:text-blue-500 gap-2'}
                >
                  <LucideLogIn className="h-4 w-4" />
                  <span className="">로그인</span>
                </Link>
              </DropdownMenuItem>
            }
          </DropdownMenuGroup>

        </DropdownMenuContent>

      </DropdownMenu>

    );
}
