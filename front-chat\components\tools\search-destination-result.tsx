"use client";

import React, { useState, useCallback } from "react";
import {
  MapPin, Navigation, Building2, Copy, ChevronDown, ChevronUp,
  Route, MoreHorizontal, Star, Target
} from "lucide-react";
import { AddressResponse } from "@/types/tools";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { highlightPointOnMap, type UseMapReturn } from "@/lib/map-utils";
import { componentStyles } from "@/lib/design-tokens";
import { CompactResultTrigger } from "./common/compact-result-trigger";
import { getToolDisplayInfo } from "../annotations";

interface SearchDestinationResultProps {
  content: AddressResponse;
  className?: string;
  mapState?: UseMapReturn;
}

// 주소 아이템 타입 정의
interface AddressItem {
  roadAddr: string;
  jibunAddr: string;
  buildName: string;
  buildLo: string;
  buildLa: string;
  parcelLo: string;
  parcelLa: string;
  poiName: string;
  buildGeom?: string;
  geom?: string;
}

export function SearchDestinationResult({
  content,
  className,
  mapState
}: SearchDestinationResultProps) {
  const [showAll, setShowAll] = useState(false);

  let result: AddressResponse;

  try {
    result = content as AddressResponse;
  } catch (e) {
    return null;
  }

  if (!result.result?.jusoList?.length) {
    return (
      <div className={cn(componentStyles.card.error, "p-3", className)}>
        <div className="flex items-center gap-3">
          <div className={cn(componentStyles.iconContainer.sm, "bg-red-100/80 text-red-600 border border-red-200/60")}>
            <Target className="h-3.5 w-3.5" />
          </div>
          <div>
            <p className="font-medium text-red-900 text-sm">목적지를 찾을 수 없습니다</p>
            <p className="text-xs text-red-700">다른 키워드로 다시 검색해보세요</p>
          </div>
        </div>
      </div>
    );
  }

  const addresses = result.result.jusoList;
  const displayAddresses = showAll ? addresses : addresses.slice(0, 3);

  const formatCoordinateForDirections = useCallback((address: AddressItem): string => {
    return `${address.buildLo},${address.buildLa}${address.buildName ? `,name=${address.buildName}` : ''}`;
  }, []);

  const handleCopyAddress = useCallback((address: string) => {
    navigator.clipboard.writeText(address);
    toast.success("주소가 복사되었습니다");
  }, []);

  const handleNavigate = useCallback((address: AddressItem) => {
    if (!mapState) {
      toast.error("지도가 로드되지 않았습니다");
      return;
    }

    const success = highlightPointOnMap(
      mapState,
      parseFloat(address.buildLo),
      parseFloat(address.buildLa),
      address.buildGeom || address.geom,
      16
    );

    if (success) {
      toast.success(`목적지 ${address.roadAddr}로 이동했습니다`);
    } else {
      toast.error("지도 이동 중 오류가 발생했습니다");
    }
  }, [mapState]);

  const toolInfo = getToolDisplayInfo("searchDestination");

  return (
    <CompactResultTrigger
      icon={toolInfo.icon}
      title={toolInfo.label}
      state="result"
      className={className}
      titleExtra={
        <Badge variant="outline" className="text-xs py-0 border bg-red-100/80 text-red-700 border-red-200/60">
          {addresses.length}개 발견
        </Badge>
      }
    >
      <div className="space-y-2">
        {displayAddresses.map((address, index) => (
          <Card key={index} className={cn(componentStyles.card.interactive, "group")}>
            <CardContent className="p-3">
              <div className="flex items-start justify-between gap-3">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <div className={cn(componentStyles.iconContainer.sm, "bg-blue-100/80 text-blue-600 border border-blue-200/60")}>
                      <Target className="h-3 w-3" />
                    </div>
                    {address.buildName && (
                      <span className="font-medium text-sm text-gray-900 truncate">
                        {address.buildName}
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-gray-600 mb-1 leading-relaxed">
                    {address.roadAddr}
                  </p>
                  {address.jibunAddr && address.jibunAddr !== address.roadAddr && (
                    <p className="text-xs text-gray-500">
                      지번: {address.jibunAddr}
                    </p>
                  )}
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleNavigate(address)}
                    className={cn(componentStyles.button.ghost, "h-7 w-7 p-0")}
                  >
                    <Navigation className="h-3 w-3" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        size="sm"
                        variant="ghost"
                        className={cn(componentStyles.button.ghost, "h-7 w-7 p-0")}
                      >
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <DropdownMenuItem onClick={() => handleCopyAddress(address.roadAddr)}>
                        <Copy className="h-3 w-3 mr-2" />
                        도로명주소 복사
                      </DropdownMenuItem>
                      {address.jibunAddr && (
                        <DropdownMenuItem onClick={() => handleCopyAddress(address.jibunAddr)}>
                          <Copy className="h-3 w-3 mr-2" />
                          지번주소 복사
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleCopyAddress(formatCoordinateForDirections(address))}>
                        <Route className="h-3 w-3 mr-2" />
                        좌표 복사
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {addresses.length > 3 && (
          <div className="flex justify-center pt-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAll(!showAll)}
              className={cn(componentStyles.button.ghost, "text-xs")}
            >
              {showAll ? (
                <>
                  <ChevronUp className="h-3 w-3 mr-1" />
                  접기
                </>
              ) : (
                <>
                  <ChevronDown className="h-3 w-3 mr-1" />
                  {addresses.length - 3}개 더 보기
                </>
              )}
            </Button>
          </div>
        )}
      </div>
    </CompactResultTrigger>
  );
}
