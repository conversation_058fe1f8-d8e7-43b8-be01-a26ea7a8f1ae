# 지도 라이브러리 문서

## 기능 : 지도 생성

**설명**: 이 기능은 기본 지도(Map)를 생성합니다.

**입력**:
- `containerId` (string): 지도가 렌더링될 HTML 요소의 ID. 
- `centerX` (Number): 지도의 중심 X 좌표.
- `centerY` (Number): 지도의 중심 Y 좌표.
- `zoom` (number): 초기 줌 레벨.
- `projection` (String) : 지도의 프로젝션 정보를 나타내는 코드입니다. 예를 들어:
    - `EPSG:4326` (WGS84)
    - `EPSG:3857` (Web Mercator)
    - `EPSG:5186` (Korea 2000 / Unified CS)
    - `EPSG:5179` (Korea 2000 / Korea Polyconic)

**출력**:
- 생성된 지도 객체(map).


**코드 예제**:
```javascript
var mapContainer = document.getElementById(containerId);
// 중심 좌표 정의
var coord = new odf.Coordinate(centerX, centerY);
var mapOption = {
    center:coord,
    zoom:zoom,
    projection:projection,
    //배경지도로 사용할 바로e맵 wmts 서비스 경로
    baroEMapURL: 'https://geon-gateway.geon.kr/map/api/map/baroemap',
    //배경지도로 사용할 바로e맵 항공 wmts 서비스 경로
    baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',
    //사용할 배경지도 정의
    basemap:{
        //바로e맵 서비스중, 기본지도와 항공지도 이용
        baroEMap:['eMapBasic','eMapAIR']
    },
};
//지도 객체 생성
var map = new odf.Map(mapContainer, mapOption);
```
