// Define your models here.

export interface Model {
  id: string;
  label: string;
  apiIdentifier: string; // Dify Application ID
  description: string;
  apiKey?: string;
}

export const models: Array<Model> = [
  {
    id: '지자체 공간정보 플랫폼 챗봇',
    label: '지자체 공간정보 플랫폼 챗봇',
    apiIdentifier: 'EIjFYMz0dmL2HxkQJuBifqvF', // Dify Application ID (app- 제거)
    description: '지자체 공간정보 플랫폼 챗봇',
    apiKey: 'app-Hd682MZtRJh95QtTUe5H9aCl', // Dify Assistant API 키
  },
  {
    id: '지도개발 어시스턴트',
    label: '지도개발 어시스턴트',
    apiIdentifier: 'EIjFYMz0dmL2HxkQJuBifqvF', // Dify Application ID (app- 제거)
    description: '지도개발을 위한 문서를 학습한 어시스턴트',
    apiKey: 'app-EIjFYMz0dmL2HxkQJuBifqvF', // Dify Assistant API 키
  },
] as const;

export const DEFAULT_MODEL_NAME: string = '지자체 공간정보 플랫폼 챗봇';

// API 키를 모델 ID로 매핑하는 함수
export function getApiKeyByModelId(modelId: string): string | undefined {
  const model = models.find(m => m.id === modelId);
  return model?.apiKey;
}
