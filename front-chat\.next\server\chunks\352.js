exports.id=352,exports.ids=[352],exports.modules={7456:(e,s,t)=>{"use strict";t.d(s,{Bx:()=>C,Yv:()=>I,CG:()=>R,Cn:()=>E,rQ:()=>$,jj:()=>z,Gh:()=>S,SidebarInset:()=>k,wZ:()=>L,Uj:()=>T,FX:()=>M,q9:()=>P,am:()=>D,Cp:()=>_,Fg:()=>O,SidebarProvider:()=>w,x2:()=>A,cL:()=>y});var a=t(84464),r=t(63185),l=t(3166),n=t(27872),i=t(76799),o=t(72487),d=t(96684),c=t(67334),m=t(79903),x=t(81205),u=t(25883);let h=x.bL;x.l9,x.bm;let p=x.ZL,f=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(x.hJ,{className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s,ref:t}));f.displayName=x.hJ.displayName;let b=(0,n.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),g=r.forwardRef(({side:e="right",className:s,children:t,...r},l)=>(0,a.jsxs)(p,{children:[(0,a.jsx)(f,{}),(0,a.jsxs)(x.UC,{ref:l,className:(0,o.cn)(b({side:e}),s),...r,children:[t,(0,a.jsxs)(x.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));g.displayName=x.UC.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(x.hE,{ref:t,className:(0,o.cn)("text-lg font-semibold text-foreground",e),...s})).displayName=x.hE.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(x.VY,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",e),...s})).displayName=x.VY.displayName;var j=t(33057),N=t(84371);let v=r.createContext(null);function y(){let e=r.useContext(v);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}let w=r.forwardRef(({defaultOpen:e=!0,open:s,onOpenChange:t,className:l,style:n,children:i,...d},c)=>{let m=function(){let[e,s]=r.useState(void 0);return r.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),t=()=>{s(window.innerWidth<768)};return e.addEventListener("change",t),s(window.innerWidth<768),()=>e.removeEventListener("change",t)},[]),!!e}(),[x,u]=r.useState(!1),[h,p]=r.useState(e),f=s??h,b=r.useCallback(e=>{if(t)return t?.("function"==typeof e?e(f):e);p(e),document.cookie=`sidebar:state=${f}; path=/; max-age=604800`},[t,f]),g=r.useCallback(()=>m?u(e=>!e):b(e=>!e),[m,b,u]);r.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),g())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[g]);let j=f?"expanded":"collapsed",y=r.useMemo(()=>({state:j,open:f,setOpen:b,isMobile:m,openMobile:x,setOpenMobile:u,toggleSidebar:g}),[j,f,b,m,x,u,g]);return(0,a.jsx)(v.Provider,{value:y,children:(0,a.jsx)(N.Bc,{delayDuration:0,children:(0,a.jsx)("div",{style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...n},className:(0,o.cn)("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",l),ref:c,...d,children:i})})})});w.displayName="SidebarProvider";let C=r.forwardRef(({side:e="left",variant:s="sidebar",collapsible:t="offcanvas",className:r,children:l,...n},i)=>{let{isMobile:d,state:c,openMobile:m,setOpenMobile:x}=y();return"none"===t?(0,a.jsx)("div",{className:(0,o.cn)("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",r),ref:i,...n,children:l}):d?(0,a.jsx)(h,{open:m,onOpenChange:x,...n,children:(0,a.jsx)(g,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:e,children:(0,a.jsx)("div",{className:"flex h-full w-full flex-col",children:l})})}):(0,a.jsxs)("div",{ref:i,className:"group peer hidden md:block text-sidebar-foreground","data-state":c,"data-collapsible":"collapsed"===c?t:"","data-variant":s,"data-side":e,children:[(0,a.jsx)("div",{className:(0,o.cn)("duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===s||"inset"===s?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),(0,a.jsx)("div",{className:(0,o.cn)("duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===s||"inset"===s?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...n,children:(0,a.jsx)("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:l})})]})});C.displayName="Sidebar";let A=r.forwardRef(({className:e,onClick:s,...t},r)=>{let{toggleSidebar:l}=y();return(0,a.jsxs)(d.$,{ref:r,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:(0,o.cn)("h-7 w-7",e),onClick:e=>{s?.(e),l()},...t,children:[(0,a.jsx)(i.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})});A.displayName="SidebarTrigger",r.forwardRef(({className:e,...s},t)=>{let{toggleSidebar:r}=y();return(0,a.jsx)("button",{ref:t,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:(0,o.cn)("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...s})}).displayName="SidebarRail";let k=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("main",{ref:t,className:(0,o.cn)("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",e),...s}));k.displayName="SidebarInset",r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(c.p,{ref:t,"data-sidebar":"input",className:(0,o.cn)("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",e),...s})).displayName="SidebarInput";let S=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,"data-sidebar":"header",className:(0,o.cn)("flex flex-col gap-2 p-2",e),...s}));S.displayName="SidebarHeader";let R=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,"data-sidebar":"footer",className:(0,o.cn)("flex flex-col gap-2 p-2",e),...s}));R.displayName="SidebarFooter",r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(m.w,{ref:t,"data-sidebar":"separator",className:(0,o.cn)("mx-2 w-auto bg-sidebar-border",e),...s})).displayName="SidebarSeparator";let I=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,"data-sidebar":"content",className:(0,o.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...s}));I.displayName="SidebarContent";let E=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,"data-sidebar":"group",className:(0,o.cn)("relative flex w-full min-w-0 flex-col p-2",e),...s}));E.displayName="SidebarGroup";let z=r.forwardRef(({className:e,asChild:s=!1,...t},r)=>{let n=s?l.DX:"div";return(0,a.jsx)(n,{ref:r,"data-sidebar":"group-label",className:(0,o.cn)("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...t})});z.displayName="SidebarGroupLabel",r.forwardRef(({className:e,asChild:s=!1,...t},r)=>{let n=s?l.DX:"button";return(0,a.jsx)(n,{ref:r,"data-sidebar":"group-action",className:(0,o.cn)("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",e),...t})}).displayName="SidebarGroupAction";let $=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,"data-sidebar":"group-content",className:(0,o.cn)("w-full text-sm",e),...s}));$.displayName="SidebarGroupContent";let L=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("ul",{ref:t,"data-sidebar":"menu",className:(0,o.cn)("flex w-full min-w-0 flex-col gap-1",e),...s}));L.displayName="SidebarMenu";let M=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("li",{ref:t,"data-sidebar":"menu-item",className:(0,o.cn)("group/menu-item relative",e),...s}));M.displayName="SidebarMenuItem";let F=(0,n.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!p-0"}},defaultVariants:{variant:"default",size:"default"}}),T=r.forwardRef(({asChild:e=!1,isActive:s=!1,variant:t="default",size:r="default",tooltip:n,className:i,...d},c)=>{let m=e?l.DX:"button",{isMobile:x,state:u}=y(),h=(0,a.jsx)(m,{ref:c,"data-sidebar":"menu-button","data-size":r,"data-active":s,className:(0,o.cn)(F({variant:t,size:r}),i),...d});return n?("string"==typeof n&&(n={children:n}),(0,a.jsxs)(N.m_,{children:[(0,a.jsx)(N.k$,{asChild:!0,children:h}),(0,a.jsx)(N.ZI,{side:"right",align:"center",hidden:"collapsed"!==u||x,...n})]})):h});T.displayName="SidebarMenuButton",r.forwardRef(({className:e,asChild:s=!1,showOnHover:t=!1,...r},n)=>{let i=s?l.DX:"button";return(0,a.jsx)(i,{ref:n,"data-sidebar":"menu-action",className:(0,o.cn)("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",t&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",e),...r})}).displayName="SidebarMenuAction",r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,"data-sidebar":"menu-badge",className:(0,o.cn)("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",e),...s})).displayName="SidebarMenuBadge",r.forwardRef(({className:e,showIcon:s=!1,...t},l)=>{let n=r.useMemo(()=>`${Math.floor(40*Math.random())+50}%`,[]);return(0,a.jsxs)("div",{ref:l,"data-sidebar":"menu-skeleton",className:(0,o.cn)("rounded-md h-8 flex gap-2 px-2 items-center",e),...t,children:[s&&(0,a.jsx)(j.E,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,a.jsx)(j.E,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":n}})]})}).displayName="SidebarMenuSkeleton";let P=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("ul",{ref:t,"data-sidebar":"menu-sub",className:(0,o.cn)("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...s}));P.displayName="SidebarMenuSub";let O=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("li",{ref:t,className:(0,o.cn)("group/sub-menu-item relative",e),...s}));O.displayName="SidebarMenuSubItem";let D=r.forwardRef(({className:e,asChild:s=!1,showOnHover:t=!1,...r},n)=>{let i=s?l.DX:"button";return(0,a.jsx)(i,{ref:n,"data-sidebar":"menu-sub-action",className:(0,o.cn)("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0","text-sidebar-foreground outline-none ring-sidebar-ring transition-transform","hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2","peer-hover/menu-sub-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-sub-button:top-1","peer-data-[size=default]/menu-sub-button:top-1.5","peer-data-[size=lg]/menu-sub-button:top-2.5","group-data-[collapsible=icon]:hidden",t&&"group-focus-within/sub-menu-item:opacity-100 group-hover/sub-menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-sub-button:text-sidebar-accent-foreground md:opacity-0",e),...r})});D.displayName="SidebarMenuSubAction";let _=r.forwardRef(({asChild:e=!1,size:s="md",isActive:t,className:r,...n},i)=>{let d=e?l.DX:"a";return(0,a.jsx)(d,{ref:i,"data-sidebar":"menu-sub-button","data-size":s,"data-active":t,className:(0,o.cn)("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===s&&"text-xs","md"===s&&"text-sm","group-data-[collapsible=icon]:hidden",r),...n})});_.displayName="SidebarMenuSubButton"},27616:(e,s,t)=>{"use strict";t.d(s,{J:()=>d});var a=t(84464),r=t(63185),l=t(80656),n=t(27872),i=t(72487);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.b,{ref:t,className:(0,i.cn)(o(),e),...s}));d.displayName=l.b.displayName},30075:(e,s,t)=>{"use strict";t.d(s,{E:()=>i});var a=t(84464);t(63185);var r=t(27872),l=t(72487);let n=(0,r.F)("inline-flex items-center rounded-full border px-3 py-1.5 text-xs font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer select-none",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80 hover:scale-105 active:scale-95",secondary:"border-transparent bg-gray-100 text-gray-700 hover:bg-gray-200 hover:scale-105 active:scale-95 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 hover:scale-105 active:scale-95",outline:"text-foreground border-border hover:bg-accent hover:text-accent-foreground hover:scale-105 active:scale-95","ai-active":"border-transparent bg-gradient-to-r from-blue-600 to-indigo-600 text-white border border-blue-500 hover:from-blue-700 hover:to-indigo-700 hover:border-blue-600 hover:scale-105 active:scale-95 shadow-md dark:from-blue-700 dark:to-indigo-700 dark:text-white dark:border-blue-600 dark:hover:from-blue-800 dark:hover:to-indigo-800","nav-active":"border-transparent bg-gradient-to-r from-emerald-600 to-green-600 text-white border border-emerald-500 hover:from-emerald-700 hover:to-green-700 hover:border-emerald-600 hover:scale-105 active:scale-95 shadow-md dark:from-emerald-700 dark:to-green-700 dark:text-white dark:border-emerald-600 dark:hover:from-emerald-800 dark:hover:to-green-800"}},defaultVariants:{variant:"default"}});function i({className:e,variant:s,...t}){return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:s}),e),...t})}},32807:(e,s,t)=>{"use strict";t.d(s,{BK:()=>o,eu:()=>i,q5:()=>d});var a=t(84464),r=t(63185),l=t(46240),n=t(72487);let i=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.bL,{ref:t,className:(0,n.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...s}));i.displayName=l.bL.displayName;let o=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l._V,{ref:t,className:(0,n.cn)("aspect-square h-full w-full",e),...s}));o.displayName=l._V.displayName;let d=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.H4,{ref:t,className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...s}));d.displayName=l.H4.displayName},33057:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(84464),r=t(72487);function l({className:e,...s}){return(0,a.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",e),...s})}},34934:(e,s,t)=>{"use strict";t.d(s,{ChatMap:()=>a});let a=(0,t(35306).registerClientReference)(function(){throw Error("Attempted to call ChatMap() from the server but ChatMap is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\chat-map\\chat-map.tsx","ChatMap")},37011:(e,s,t)=>{"use strict";t.d(s,{AppSidebar:()=>X});var a=t(84464),r=t(63185),l=t(30346),n=t(64451),i=t(31990),o=t(59872),d=t(5786),c=t(51507),m=t(7456),x=t(47495),u=t(64051),h=t(30075),p=t(84371),f=t(72487);t(67334),t(45132);var b=t(21072),g=t(82440),j=t(36298),N=t(68428),v=t(97825),y=t(32807),w=t(30569),C=t(10875),A=t.n(C);function k({user:e}){return(0,a.jsxs)(u.rI,{children:[(0,a.jsx)(u.ty,{asChild:!0,children:(0,a.jsxs)(m.Uj,{size:"lg",className:"group data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,a.jsxs)(y.eu,{className:"h-8 w-8",children:[(0,a.jsx)(y.BK,{src:e?.image??void 0,alt:"사용자이미지"}),(0,a.jsx)(y.q5,{className:"bg-muted",children:(0,a.jsx)(g.A,{className:"h-4 w-4 text-muted-foreground"})})]}),e&&(0,a.jsxs)("div",{className:"flex flex-col items-start text-left",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:e.name}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:e.email})]}),(0,a.jsx)(d.A,{className:"ml-auto h-4 w-4 transition-transform duration-200"})]})}),(0,a.jsxs)(u.SQ,{className:"w-56",align:"end",side:"top",children:[(0,a.jsxs)(u.lp,{className:"flex justify-between items-center font-normal",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium leading-none",children:e?.name}),(0,a.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:e?.email})]}),(0,a.jsx)(w.U,{})]}),(0,a.jsx)(u.mB,{}),(0,a.jsxs)(u.I,{children:[(0,a.jsxs)(u._2,{className:"gap-2",children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"계정"})]}),(0,a.jsxs)(u._2,{className:"gap-2",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"설정"})]})]}),(0,a.jsx)(u.mB,{}),(0,a.jsxs)(u.I,{children:[e&&(0,a.jsxs)(u._2,{className:"flex w-full text-red-500 gap-2",onClick:()=>(0,b.CI)({redirectTo:"/"}),children:[(0,a.jsx)(N.A,{className:"h-4 w-4"})," ","로그아웃"]}),!e&&(0,a.jsx)(u._2,{children:(0,a.jsxs)(A(),{href:"/login",className:"flex w-full hover:text-blue-500 gap-2",children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"",children:"로그인"})]})})]})]})]})}var S=t(90871),R=t(2170),I=t(95796),E=t(89779),z=t(86270),$=t(55703),L=t(41837),M=t(32288),F=t(43597),T=t(30427),P=t(50847),O=t(65363),D=t(46655),_=t(77262);let B=_.bL,W=_.R6,G=_.Ke,Z=(0,r.memo)(({chat:e,isActive:s,onDelete:t,setOpenMobile:r})=>(0,a.jsxs)(m.Fg,{children:[(0,a.jsx)(m.Cp,{asChild:!0,isActive:s,className:"peer/menu-sub-button",children:(0,a.jsx)(A(),{href:`/geon-2d-map/${e.id}`,onClick:()=>r(!1),children:(0,a.jsx)("span",{children:e.title})})}),(0,a.jsxs)(u.rI,{modal:!0,children:[(0,a.jsx)(u.ty,{asChild:!0,children:(0,a.jsxs)(m.am,{showOnHover:!0,children:[(0,a.jsx)(M.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"More"})]})}),(0,a.jsx)(u.SQ,{side:"bottom",align:"end",children:(0,a.jsxs)(u._2,{className:"cursor-pointer text-destructive focus:bg-destructive/15 focus:text-destructive dark:text-red-500",onSelect:()=>t(e.id),children:[(0,a.jsx)(F.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"삭제"})]})})]})]}),(e,s)=>e.isActive===s.isActive);function J({user:e}){let{setOpenMobile:s}=(0,m.cL)(),{id:t}=(0,z.useParams)();(0,z.usePathname)();let{data:l,isLoading:n,mutate:i}=(0,L.Ay)(e?"/api/history":null,f.GO,{fallbackData:[]}),[o,d]=(0,r.useState)(null),[c,x]=(0,r.useState)(!1),u=(0,z.useRouter)(),h=async()=>{let e=fetch(`/api/chat?id=${o}`,{method:"DELETE"});$.oR.promise(e,{loading:"대화 삭제중...",success:()=>(i(e=>{if(e)return e.filter(e=>e.id!==t)}),"대화가 삭제되었습니다."),error:"대화 삭제 실패"}),x(!1),o===t&&u.push("/")};if(!e)return(0,a.jsx)(m.wZ,{children:(0,a.jsxs)(m.FX,{children:[(0,a.jsxs)(m.Uj,{children:[(0,a.jsx)(T.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"대화 목록"})]}),(0,a.jsx)(m.q9,{children:(0,a.jsxs)("div",{className:"text-zinc-500 w-full flex flex-row justify-center items-center text-sm gap-2 mt-2",children:[(0,a.jsx)(P.A,{}),(0,a.jsx)("div",{children:"이전 대화를 저장하고 다시 보려면 로그인하세요!"})]})})]})});if(n)return(0,a.jsx)(m.wZ,{children:(0,a.jsxs)(m.FX,{children:[(0,a.jsxs)(m.Uj,{children:[(0,a.jsx)(T.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"대화 목록"})]}),(0,a.jsx)(m.q9,{children:(0,a.jsx)("div",{className:"flex flex-col",children:[25,80,67,26,72].map(e=>(0,a.jsx)("div",{className:"rounded-md h-8 flex gap-2 px-2 items-center",children:(0,a.jsx)("div",{className:"h-4 rounded-md flex-1 max-w-[--skeleton-width] bg-sidebar-accent-foreground/10",style:{"--skeleton-width":`${e}%`}})},e))})})]})});if(l?.length===0)return(0,a.jsx)(m.wZ,{children:(0,a.jsxs)(m.FX,{children:[(0,a.jsxs)(m.Uj,{children:[(0,a.jsx)(T.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"대화 목록"})]}),(0,a.jsx)(m.q9,{children:(0,a.jsx)(m.Fg,{children:(0,a.jsxs)(m.Cp,{children:[(0,a.jsx)(P.A,{}),(0,a.jsx)("span",{children:"이전 대화가 없습니다."})]})})})]})});let p=e=>{let s=new Date,t=(0,S.k)(s,1),a=(0,R.a)(s,1);return e.reduce((e,s)=>{let r=new Date(s.createdAt);return(0,I.c)(r)?e.today.push(s):(0,E.P)(r)?e.yesterday.push(s):r>t?e.lastWeek.push(s):r>a?e.lastMonth.push(s):e.older.push(s),e},{today:[],yesterday:[],lastWeek:[],lastMonth:[],older:[]})};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.wZ,{children:(0,a.jsx)(B,{defaultOpen:!0,className:"group/collapsible",children:(0,a.jsxs)(m.FX,{children:[(0,a.jsx)(W,{asChild:!0,children:(0,a.jsxs)(m.Uj,{children:[(0,a.jsx)(T.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"대화 목록"}),(0,a.jsx)(O.A,{className:"absolute right-2 h-4 w-4 transition-transform group-data-[state=open]/collapsible:rotate-90"})]})}),(0,a.jsx)(G,{children:(0,a.jsx)(m.q9,{className:"mt-1",children:l&&(()=>{let e=p(l);return(0,a.jsxs)(a.Fragment,{children:[e.today.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"px-2 py-1 text-xs text-sidebar-foreground/50",children:"오늘"}),e.today.map(e=>(0,a.jsx)(Z,{chat:e,isActive:e.id===t,onDelete:e=>{d(e),x(!0)},setOpenMobile:s},e.id))]}),e.yesterday.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"px-2 py-1 text-xs text-sidebar-foreground/50",children:"어제"}),e.yesterday.map(e=>(0,a.jsx)(Z,{chat:e,isActive:e.id===t,onDelete:e=>{d(e),x(!0)},setOpenMobile:s},e.id))]}),e.lastWeek.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"px-2 py-1 text-xs text-sidebar-foreground/50",children:"최근 7일"}),e.lastWeek.map(e=>(0,a.jsx)(Z,{chat:e,isActive:e.id===t,onDelete:e=>{d(e),x(!0)},setOpenMobile:s},e.id))]}),e.lastMonth.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"px-2 py-1 text-xs text-sidebar-foreground/50",children:"최근 30일"}),e.lastMonth.map(e=>(0,a.jsx)(Z,{chat:e,isActive:e.id===t,onDelete:e=>{d(e),x(!0)},setOpenMobile:s},e.id))]}),e.older.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"px-2 py-1 text-xs text-sidebar-foreground/50",children:"오래된 대화"}),e.older.map(e=>(0,a.jsx)(Z,{chat:e,isActive:e.id===t,onDelete:e=>{d(e),x(!0)},setOpenMobile:s},e.id))]})]})})()})})]})})}),(0,a.jsx)(D.Lt,{open:c,onOpenChange:x,children:(0,a.jsxs)(D.EO,{children:[(0,a.jsxs)(D.wd,{children:[(0,a.jsx)(D.r7,{children:"대화 삭제"}),(0,a.jsx)(D.$v,{children:"이 작업은 되돌릴 수 없습니다. 대화 내용이 영구적으로 삭제되며 서버에서도 완전히 제거됩니다."})]}),(0,a.jsxs)(D.ck,{children:[(0,a.jsx)(D.Zr,{children:"취소"}),(0,a.jsx)(D.Rx,{onClick:h,children:"삭제"})]})]})})]})}var U=t(68995),V=t(39642);let Q=[{label:"말로 만드는 지도",url:"/geon-2d-map",icon:x.A,badge:"Beta",disabled:!1},{label:"업무지원(챗봇)",url:"/preview",icon:l.A,badge:"Beta",disabled:!1}];n.A,i.A;let Y=e=>{let[s,t]=r.useState(e);return{version:s,setVersion:t,isLatest:"2.0.0"===s}},q=({selectedModelId:e,className:s})=>{let[t,l]=(0,r.useState)(!1),[n,i]=r.useOptimistic(e);return(0,r.useMemo)(()=>U.Jn.find(e=>e.id===n),[n]),(0,a.jsxs)(u.rI,{children:[(0,a.jsx)(u.ty,{asChild:!0,children:(0,a.jsxs)(m.Uj,{size:"lg",className:"group data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,a.jsx)("div",{className:"flex aspect-square size-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-500 text-white shadow-lg transition-all group-hover:shadow-blue-500/25",children:(0,a.jsx)(o.A,{className:"size-4"})}),(0,a.jsxs)("div",{className:"flex flex-col gap-0.5 leading-none",children:[(0,a.jsx)("span",{className:"font-semibold",children:"말로 만드는 지도"}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:e})]}),(0,a.jsx)(d.A,{className:"ml-auto h-4 w-4 transition-transform duration-200"})]})}),(0,a.jsx)(u.SQ,{className:"w-[--radix-dropdown-menu-trigger-width]",align:"start",children:U.Jn.map(e=>(0,a.jsxs)(u._2,{onSelect:()=>{l(!1),(0,r.startTransition)(()=>{i(e.id),(0,V.q)(e.id)})},className:"gap-4 group/item","data-active":e.id===n,children:[(0,a.jsxs)("div",{className:"flex flex-col gap-1 items-start",children:[e.label,e.description&&(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]}),(0,a.jsx)(c.A,{className:"size-4 ml-auto opacity-0 group-data-[active=true]/item:opacity-100"})]},e.id))})]})};function X({user:e,selectedModelId:s}){let{open:t,setOpen:r}=(0,m.cL)(),{version:l,setVersion:n}=Y("1.0.0");return(0,a.jsx)(p.Bc,{children:(0,a.jsxs)(m.Bx,{collapsible:"icon",className:"border-r border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:[(0,a.jsx)(m.Gh,{className:"border-b border-border/40",children:(0,a.jsx)(m.wZ,{children:(0,a.jsxs)(m.FX,{className:"flex justify-between items-center",children:[(0,a.jsx)(q,{selectedModelId:s}),t&&(0,a.jsx)(m.x2,{})]})})}),(0,a.jsxs)(m.Yv,{children:[(0,a.jsxs)(m.Cn,{children:[(0,a.jsx)(m.jj,{children:"Application"}),(0,a.jsx)(m.rQ,{children:(0,a.jsx)(m.wZ,{children:Q.map(e=>(0,a.jsx)(m.FX,{children:(0,a.jsx)(m.Uj,{asChild:!0,tooltip:e.label,disabled:e.disabled,className:(0,f.cn)("relative transition-colors hover:bg-accent/50",e.disabled&&"opacity-50 cursor-not-allowed"),children:(0,a.jsxs)(A(),{href:e.url,children:[(0,a.jsx)(e.icon,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:e.label}),e.badge&&(0,a.jsx)(h.E,{variant:"default",className:(0,f.cn)("ml-auto","New"===e.badge?"bg-green-500":"bg-blue-500"),children:e.badge})]})})},e.label))})})]}),(0,a.jsxs)(m.Cn,{children:[(0,a.jsx)(m.jj,{children:"Chat"}),(0,a.jsx)(m.rQ,{children:(0,a.jsx)(J,{user:e})})]})]}),(0,a.jsx)(m.CG,{className:"border-t border-border/40",children:(0,a.jsxs)(m.wZ,{children:[!t&&(0,a.jsx)(m.FX,{children:(0,a.jsx)(m.x2,{})}),(0,a.jsx)(m.FX,{children:(0,a.jsx)(k,{user:e})})]})})]})})}},39642:(e,s,t)=>{"use strict";t.d(s,{q:()=>r});var a=t(3824);let r=(0,a.createServerReference)("401f30ce6fdede15f2c53da7aa771517b099ae106a",a.callServer,void 0,a.findSourceMapURL,"saveModelId")},45132:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>x,L3:()=>h,c7:()=>u,lG:()=>o,rr:()=>p,zM:()=>d});var a=t(84464),r=t(63185),l=t(81205),n=t(25883),i=t(72487);let o=l.bL,d=l.l9,c=l.ZL;l.bm;let m=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.hJ,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s}));m.displayName=l.hJ.displayName;let x=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(c,{children:[(0,a.jsx)(m,{}),(0,a.jsxs)(l.UC,{ref:r,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[s,(0,a.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));x.displayName=l.UC.displayName;let u=({className:e,...s})=>(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});u.displayName="DialogHeader";let h=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.hE,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...s}));h.displayName=l.hE.displayName;let p=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.VY,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));p.displayName=l.VY.displayName},47257:()=>{},47841:(e,s,t)=>{"use strict";t.d(s,{j2:()=>m,Y9:()=>c});var a=t(393),r=t(95983);a.xz;let l="tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh",n="https://gsapi.geon.kr/smt";async function i(e,s){let t=new URLSearchParams({crtfckey:l,userId:e,password:s}),a=await fetch(`${n}/login/validation?${t.toString()}`,{method:"POST",headers:{"Content-Type":"application/json",crtfckey:l}}),r=await a.json();if(!a.ok)throw Error("로그인 검증에 실패했습니다.");return r}async function o(e){let s=new URLSearchParams({crtfckey:l,userId:e}),t=await fetch(`${n}/users/id?${s.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",crtfckey:l}}),a=await t.json();if(!t.ok)throw Error("사용자 정보를 가져오는데 실패했습니다.");return a}let d=[(0,r.A)({credentials:{},async authorize({id:e,password:s}){try{let t=process.env.FRONTEND_LOGIN_USER_ID||"admin",r=process.env.FRONTEND_LOGIN_PASSWORD||"password1234";if(e===t&&s===r)return{id:t,name:"GeOn City",email:"@example.com",userId:t,userNm:"GeOn City",emailaddr:"@example.com",userSeCode:"14",userSeCodeNm:"관리자",userImage:null,insttCode:"GEON",insttNm:"GeOn",insttUrl:null,message:"로그인 성공"};if("geonuser"===e){let t=await i(e,s);if(!t.result.isValid)throw new a.xz(t.result.message);let r=await o(e);if(200!==r.code)return new a.xz(r.result.message);return{...r.result,id:r.result.userId,name:r.result.userNm||e,email:r.result.emailaddr||`${r.result.userNm}`}}throw new a.xz("admin 또는 geonuser 계정으로만 로그인할 수 있습니다.")}catch(e){throw console.error("Auth error:",e),e}}})];d.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let s=e();return{id:s.id,name:s.name}}}).filter(e=>"credentials"!==e.id);let{handlers:c,auth:m,signIn:x,signOut:u}=(0,a.Ay)({pages:{signIn:"/login",newUser:"/"},providers:[],callbacks:{authorized({auth:e,request:{nextUrl:s}}){let t=!!e?.user,a=s.pathname.startsWith("/geon-2d-map"),r=s.pathname.startsWith("/login");return"/"===s.pathname||t&&r?Response.redirect(new URL("/geon-2d-map",s)):!!r||!a||t}},providers:d,session:{strategy:"jwt",maxAge:1800},callbacks:{jwt:async({token:e,user:s})=>(s&&(e.id=s.id),e),session:async({session:e,token:s})=>(e.user&&(e.user.id=s.id),e)}})},53423:(e,s,t)=>{Promise.resolve().then(t.bind(t,34934))},56145:(e,s,t)=>{"use strict";t.d(s,{ChatMap:()=>tq});var a=t(84464),r=t(63185),l=t.n(r),n=t(46549),i=t(63906),o=t(98708),d=t(72487);let c=({className:e,...s})=>(0,a.jsx)(o.YZ,{className:(0,d.cn)("flex h-full w-full data-[panel-group-direction=vertical]:flex-col",e),...s}),m=o.Zk,x=({withHandle:e,className:s,...t})=>(0,a.jsx)(o.TW,{className:(0,d.cn)("relative flex w-px items-center justify-center bg-border after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-1 data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:-translate-y-1/2 data-[panel-group-direction=vertical]:after:translate-x-0 [&[data-panel-group-direction=vertical]>div]:rotate-90",s),...t,children:e&&(0,a.jsx)("div",{className:"z-10 flex h-4 w-3 items-center justify-center rounded-sm border bg-border",children:(0,a.jsx)(i.A,{className:"h-2.5 w-2.5"})})});var u=t(96684),h=t(63547),p=t(10875),f=t.n(p);function b({className:e}){return(0,a.jsx)("div",{className:(0,d.cn)("absolute top-4 left-4 z-[10] flex flex-col gap-2 max-w-[280px]",e),children:(0,a.jsx)(u.$,{asChild:!0,variant:"ghost",size:"sm",className:"h-9 flex items-center justify-start w-full bg-background/80 hover:bg-background/90 backdrop-blur-md shadow-sm border transition-all duration-300",children:(0,a.jsxs)(f(),{href:"/geon-2d-map",className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"새 대화"})]})})})}var g=t(29792);let j=g.bL,N=g.l9,v=r.forwardRef(({className:e,align:s="center",sideOffset:t=4,...r},l)=>(0,a.jsx)(g.UC,{ref:l,align:s,sideOffset:t,className:(0,d.cn)("z-50 w-64 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r}));v.displayName=g.UC.displayName;var y=t(14294);let w=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(y.bL,{ref:r,className:(0,d.cn)("relative overflow-hidden",e),...t,children:[(0,a.jsx)(y.LM,{className:"h-full w-full rounded-[inherit]",children:s}),(0,a.jsx)(C,{}),(0,a.jsx)(y.OK,{})]}));w.displayName=y.bL.displayName;let C=r.forwardRef(({className:e,orientation:s="vertical",...t},r)=>(0,a.jsx)(y.VM,{ref:r,orientation:s,className:(0,d.cn)("flex touch-none select-none transition-colors","vertical"===s&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===s&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...t,children:(0,a.jsx)(y.lr,{className:"relative flex-1 rounded-full bg-border"})}));C.displayName=y.VM.displayName;var A=t(47495),k=t(52178),S=t(87119),R=t(17952),I=t(59945),E=t(95709),z=t(11466),$=t(5996);let L=(0,r.createContext)(null);function M({children:e,mapState:s}){let[t,l]=(0,r.useState)("eMapBasic"),n=(0,r.useCallback)(e=>{if(!s?.view?.setBasemap)return void console.error("map.setBasemap is not available or map not ready.");s.view.setBasemap(e),l(e)},[s]);return(0,a.jsx)(L.Provider,{value:{currentBasemap:t,setCurrentBasemap:l,changeBasemap:n},children:e})}function F(){let e=(0,r.useContext)(L);if(!e)throw Error("useBasemap must be used within a BasemapProvider");return e}let T={eMapBasic:{id:"eMapBasic",name:"일반지도",description:"도로, 건물, 지형지물이 포함된 기본 지도입니다.",icon:A.A,color:"#4C6EF5"},eMapAIR:{id:"eMapAIR",name:"항공지도",description:"위성에서 촬영한 실제 지형을 보여주는 항공사진입니다.",icon:k.A,color:"#40C057"},eMapColor:{id:"eMapColor",name:"색각지도",description:"색상 대비를 강조한 특수 목적 지도입니다.",icon:S.A,color:"#FA5252"},eMapWhite:{id:"eMapWhite",name:"백지도",description:"깔끔한 흰색 배경의 심플한 지도입니다.",icon:R.A,color:"#845EF7"}};function P({mapState:e}){let[s,t]=(0,r.useState)(!1),{currentBasemap:l,changeBasemap:n}=F(),i=T[l]||T.eMapBasic,o=e=>{n(e)};return(0,a.jsxs)(j,{openDelay:100,children:[(0,a.jsx)(N,{asChild:!0,children:(0,a.jsxs)(u.$,{variant:"ghost",size:"sm",className:"h-9 flex items-center justify-between w-full bg-background/80 hover:bg-background/90 backdrop-blur-md shadow-sm border transition-all duration-300",onMouseEnter:()=>t(!0),onMouseLeave:()=>t(!1),children:[(0,a.jsxs)(z.P.div,{initial:{scale:1},className:"flex items-center gap-2",children:[(0,a.jsx)(I.A,{className:"h-4 w-4",style:{color:i.color}}),(0,a.jsx)("span",{className:"text-sm font-medium",children:i.name})]}),(0,a.jsx)(z.P.div,{animate:{rotate:180*!!s},transition:{duration:.2},children:(0,a.jsx)(E.A,{className:"h-4 w-4 opacity-40"})})]})}),(0,a.jsx)(v,{className:"w-auto p-0.5 bg-background/90 backdrop-blur-md border shadow-lg rounded-xl",align:"end",side:"left",alignOffset:-40,sideOffset:8,children:(0,a.jsx)(w,{className:"h-auto max-h-[280px]",children:(0,a.jsx)("div",{className:"space-y-0.5 p-1",children:Object.entries(T).map(([e,s])=>{let t=s.icon,r=l===e;return(0,a.jsx)(z.P.div,{initial:!1,whileHover:{scale:.98},transition:{duration:.2},children:(0,a.jsxs)(u.$,{variant:"ghost",size:"sm",className:`w-full py-3 justify-start text-sm relative group transition-all duration-200
                      ${r?"bg-muted":"hover:bg-muted/50"}`,onClick:()=>o(e),children:[(0,a.jsx)($.N,{children:r&&(0,a.jsx)(z.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:-10},className:"absolute left-0 top-0 w-1 h-full rounded-full",style:{backgroundColor:s.color}})}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:`p-1.5 rounded-lg transition-colors duration-200 
                        ${r?"bg-muted-foreground/10":"bg-muted"}`,children:(0,a.jsx)(t,{className:"h-4 w-4",style:{color:s.color}})}),(0,a.jsxs)("div",{className:"flex flex-col items-start gap-0.5",children:[(0,a.jsx)("span",{className:"font-medium",children:s.name}),(0,a.jsx)("span",{className:"text-[11px] text-muted-foreground leading-tight",children:s.description})]})]})]})},e)})})})})]})}var O=t(45132),D=t(30075),_=t(33057),B=t(67334),W=t(92251),G=t(76444),Z=t(51507);let J=W.bL;W.YJ;let U=W.WT,V=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(W.l9,{ref:r,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,(0,a.jsx)(W.In,{asChild:!0,children:(0,a.jsx)(E.A,{className:"h-4 w-4 opacity-50"})})]}));V.displayName=W.l9.displayName;let Q=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(W.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,a.jsx)(G.A,{className:"h-4 w-4"})}));Q.displayName=W.PP.displayName;let Y=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(W.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,a.jsx)(E.A,{className:"h-4 w-4"})}));Y.displayName=W.wn.displayName;let q=r.forwardRef(({className:e,children:s,position:t="popper",...r},l)=>(0,a.jsx)(W.ZL,{children:(0,a.jsxs)(W.UC,{ref:l,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[(0,a.jsx)(Q,{}),(0,a.jsx)(W.LM,{className:(0,d.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(Y,{})]})}));q.displayName=W.UC.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(W.JU,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=W.JU.displayName;let X=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(W.q7,{ref:r,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(W.VF,{children:(0,a.jsx)(Z.A,{className:"h-4 w-4"})})}),(0,a.jsx)(W.p4,{children:s})]}));X.displayName=W.q7.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(W.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=W.wv.displayName;var K=t(84899),H=t(73823),ee=t(94149),es=t(84371),et=t(55703);let ea=e=>({rules:[{name:"Default Point Rule",symbolizers:[{kind:"Mark",wellKnownName:"circle",radius:6,color:"#FF0000",fillOpacity:.8,strokeColor:"#000000",strokeWidth:1,strokeOpacity:1,...e}]}]}),er=e=>({rules:[{name:"Default Line Rule",symbolizers:[{kind:"Line",color:"#0000FF",width:2,opacity:1,cap:"round",join:"round",...e}]}]}),el=e=>({rules:[{name:"Default Polygon Rule",symbolizers:[{kind:"Fill",color:"#AAAAAA",fillOpacity:.5,outlineColor:"#000000",outlineWidth:1,outlineOpacity:1,...e}]}]}),en=e=>({"circle-radius":6,"circle-fill-color":"#FF0000","circle-fill-opacity":.8,"circle-stroke-color":"#000000","circle-stroke-width":1,"circle-stroke-opacity":1,...e}),ei=e=>({"stroke-color":"#0000FF","stroke-width":2,"stroke-opacity":1,...e}),eo=e=>({"fill-color":"#00FF00","fill-opacity":.5,"stroke-color":"#000000","stroke-width":1,"stroke-opacity":1,...e}),ed=null,ec=(e,s,t,a,r=16,l)=>{if(!e?.map)return!1;try{let r=window.odf;if(!r)return!1;let n=ed?e.map.findLayer(ed):null;if(!n){(n=r.LayerFactory.produce("empty",{})).setMap(e.map),ed=n.getODFId();let s=l?.customStyle||{image:{circle:{radius:10,fill:{color:[255,255,255,.4]},stroke:{color:[237,116,116,.82],width:2}}},fill:{color:[255,255,255,.4]},stroke:{color:[237,116,116,.82],width:2}};n.setStyle(r.StyleFactory.produce(s))}l?.clearPrevious!==!1&&n.clear();let i=a;i||(i=`POINT(${s} ${t})`);let o=r.FeatureFactory.fromWKT(i),d=e.map.getView().getProjection().getCode().replace(/[^0-9]/gi,"");if("4326"!==d&&(o=e.map.getProjection().projectGeom(o,"4326")),n.addFeature(o),l?.useZIndexUp){let s=e.map.getMaxZIndex();e.map.setZIndex(ed,s+1)}return o.getGeometry().getType(),a&&(l?.fitPadding,n.fit()),console.log(e.map.getZoom()),e.map.setZoom(e.map.getZoom()>16?e.map.getZoom()-4:e.map.getZoom()),!0}catch(e){return console.error("지도 하이라이트 중 오류:",e),!1}},em=(0,r.createContext)({}),ex=(0,r.createContext)(null),eu=(0,r.createContext)({currentLocation:null,setCurrentLocation:()=>{},originPoint:null,setOriginPoint:()=>{},destinationPoint:null,setDestinationPoint:()=>{}});function eh(e,s){switch(s.type){case"ADD_LAYER":let t=e.findIndex(e=>e.id===s.payload.id);if(t>=0){let a=[...e];return a[t]={...a[t],...s.payload},a}let a=e.length>0?Math.max(...e.map(e=>e.zIndex||0)):0;return[...e,{...s.payload,zIndex:a+1}];case"UPDATE_LAYER":return e.map(e=>e.id===s.payload.id?{...e,...s.payload.updates}:e);case"REMOVE_LAYER":return e.filter(e=>e.id!==s.payload);case"TOGGLE_VISIBILITY":return e.map(e=>e.id===s.payload?{...e,visible:!e.visible,userModified:!0}:e);case"UPDATE_FILTER":return e.map(e=>e.id===s.payload.id?{...e,filter:s.payload.filter}:e);case"UPDATE_STYLE":return e.map(e=>e.id===s.payload.id?{...e,style:s.payload.style}:e);case"UPDATE_Z_INDEX":return e.map(e=>e.id===s.payload.id?{...e,zIndex:s.payload.zIndex}:e);case"REORDER_LAYERS":let{layerIds:r}=s.payload;return e.map(e=>{let s=r.indexOf(e.id);if(s>=0){let t=r.length-s;return e.zIndex!==t?{...e,zIndex:t}:e}return 0!==e.zIndex?{...e,zIndex:0}:e});case"SET_LAYERS":return s.payload;default:return e}}function ep({messages:e,children:s,enableSmartNavigation:t=!0,mapState:l}){let[n,i]=(0,r.useState)({}),[o,d]=(0,r.useReducer)(eh,[]),[c,m]=(0,r.useState)(null),[x,u]=(0,r.useState)(null),[h,p]=(0,r.useState)(null);(0,r.useRef)(new Set);let f=(0,r.useRef)(new Set);(0,r.useRef)([]);let{setCurrentBasemap:b}=F(),g=(0,r.useRef)([]),j=(0,r.useRef)(null),N=(0,r.useCallback)(()=>{if(t&&l&&0!==g.current.length)try{let e=g.current;if(1===e.length){let{address:s}=e[0];ec(l,parseFloat(s.buildLo),parseFloat(s.buildLa),s.buildGeom||s.geom,16,{clearPrevious:!0,useZIndexUp:!0,fitPadding:1e3})&&et.oR.success(`${s.roadAddr}로 자동 이동했습니다`)}else e.map(({address:e})=>({lng:parseFloat(e.buildLo),lat:parseFloat(e.buildLa),geometry:e.buildGeom||e.geom})).forEach((e,s)=>{ec(l,e.lng,e.lat,e.geometry,16,{clearPrevious:0===s,useZIndexUp:!0,fitPadding:1e3})}),et.oR.success(`${e.length}개 위치를 지도에 표시했습니다`);g.current=[]}catch(e){console.error("배치 주소 처리 중 오류:",e),g.current=[]}},[t,l]);(0,r.useCallback)((e,s,a)=>{if(!t||!l)return;let r=`${e}-${a}`;if(!f.current.has(r))try{switch(e){case"searchAddress":if(s.result?.jusoList?.length>0){let e=s.result.jusoList[0];e.buildLo&&e.buildLa&&(g.current.push({address:e,toolCallId:a}),j.current&&clearTimeout(j.current),j.current=setTimeout(()=>{N(),j.current=null},2e3))}break;case"searchOrigin":if(s.result?.jusoList?.length>0){let e=s.result.jusoList[0];if(e.buildLo&&e.buildLa&&l?.map){let s=l.map.getProjection().project([parseFloat(e.buildLo),parseFloat(e.buildLa)],"4326");u({address:e,projectedCoord:s,toolCallId:a})}}break;case"searchDestination":if(s.result?.jusoList?.length>0){let e=s.result.jusoList[0];if(e.buildLo&&e.buildLa&&l?.map){let s=l.map.getProjection().project([parseFloat(e.buildLo),parseFloat(e.buildLa)],"4326");p({address:e,projectedCoord:s,toolCallId:a})}}break;case"searchDirections":s.routes?.length>0&&0===s.routes[0].result_code&&et.oR.success("경로를 지도에 자동으로 표시했습니다");break;case"getLocation":let t=l.map.getProjection();if(s.latitude&&s.longitude){let e=t.project([s.longitude,s.latitude],"4326"),a={latitude:s.latitude,longitude:s.longitude,accuracy:s.accuracy,timestamp:s.timestamp,projectedCoord:e};m(a),l.map.setZoom(14),l.map.setCenter(e)}break;case"getLayer":if("LR0000004299"===s.lyrId||"GIS건물통합정보_서울"===s.name){if(!l?.map)return;let e=new odf.Coordinate(955156.7761,1951925.0984);l.map.setCenter(e),l.map.setZoom(11),et.oR.success(`${s.name||s.id} 레이어를 지도에 추가했습니다`)}else if(s.bbox&&Array.isArray(s.bbox)&&s.bbox.length>=4){let[e,t,a,r]=s.bbox;ec(l,(e+a)/2,(t+r)/2,void 0,12),et.oR.success(`${s.name||s.id} 레이어 영역으로 지도를 이동했습니다`)}else et.oR.success(`${s.name||s.id} 레이어를 지도에 추가했습니다`);break;case"changeBasemap":s.basemap&&l?.view?.setBasemap&&(l.view.setBasemap(s.basemap),b(s.basemap),et.oR.success(`배경지도가 변경되었습니다`));break;case"setMapCenter":if(s.center&&l?.view?.setCenter){let[e,t]=s.center;l.view.setCenter([e,t]),et.oR.success(s.message||`지도 중심점이 이동되었습니다`)}break;case"setMapZoom":if(void 0!==s.zoom&&l?.view?.setZoom){if("relative"===s.zoomType){let e=l.view.getZoom(),t="in"===s.zoomDirection?s.zoom:-s.zoom,a=Math.max(1,Math.min(20,e+t));l.view.setZoom(a)}else l.view.setZoom(s.zoom);et.oR.success(s.message||`지도 확대/축소 레벨이 변경되었습니다`)}break;case"moveMapByDirection":if(void 0!==s.deltaX&&void 0!==s.deltaY&&l?.map?.setCenter&&l?.map?.getCenter){let e=l.map.getCenter(),t=[e[0]+s.deltaX,e[1]+s.deltaY];l.map.setCenter(t),et.oR.success(s.message||`지도가 이동되었습니다`)}}f.current.add(r)}catch(s){console.error(`Error handling smart navigation for ${e}:`,s)}},[t,l]);let v=(0,r.useMemo)(()=>({layers:o,addLayer:e=>d({type:"ADD_LAYER",payload:e}),updateLayer:(e,s)=>d({type:"UPDATE_LAYER",payload:{id:e,updates:s}}),removeLayer:e=>d({type:"REMOVE_LAYER",payload:e}),toggleVisibility:e=>d({type:"TOGGLE_VISIBILITY",payload:e}),updateFilter:(e,s)=>d({type:"UPDATE_FILTER",payload:{id:e,filter:s}}),updateStyle:(e,s)=>d({type:"UPDATE_STYLE",payload:{id:e,style:s}}),updateZIndex:(e,s)=>d({type:"UPDATE_Z_INDEX",payload:{id:e,zIndex:s}}),reorderLayers:e=>d({type:"REORDER_LAYERS",payload:{layerIds:e}}),getLayerById:e=>o.find(s=>s.id===e)}),[o]),y=(0,r.useMemo)(()=>({currentLocation:c,setCurrentLocation:m,originPoint:x,setOriginPoint:u,destinationPoint:h,setDestinationPoint:p}),[c,x,h]);return(0,a.jsx)(em.Provider,{value:n,children:(0,a.jsx)(ex.Provider,{value:v,children:(0,a.jsx)(eu.Provider,{value:y,children:s})})})}function ef(){let e=(0,r.useContext)(ex);if(!e)throw Error("useLayerManager must be used within a ToolInvocationProvider");return e}let eb=e=>{switch(e){case"1":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";case"2":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";case"3":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"}};function eg({mapState:e}){let[s,t]=(0,r.useState)(!1),[l,n]=(0,r.useState)([]),[i,o]=(0,r.useState)(!1),[c,m]=(0,r.useState)(!0),[x,p]=(0,r.useState)(1),[f,b]=(0,r.useState)(0),[g,j]=(0,r.useState)(""),[N,v]=(0,r.useState)("all"),[y,w]=(0,r.useState)(new Set),{addLayer:C}=ef(),A=async s=>{let a=s.lyrId;if(!y.has(a)){w(e=>new Set(e).add(a));try{let r=await fetch("/api/layers/get-layer",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({lyrId:a})});if(!r.ok)throw Error(`API 요청 실패: ${r.status}`);let l=await r.json();if(l&&l.id){let a="LR0000004299"===s.lyrId||"GIS건물통합정보_서울"===s.lyrNm||"GIS건물통합정보_서울"===l.name;if(C({id:l.id,name:l.name||s.lyrNm,type:l.type||"wms",visible:!0,zIndex:1,server:l.server,autoFit:!a,layer:l.layer,service:l.service||"wms",method:l.method||"get",crtfckey:l.crtfckey,geometryType:l.geometryType,style:l.style,opacity:l.opacity||1,toolCallId:`manual-add-${Date.now()}`}),a&&e){let s=new odf.Coordinate(955156.7761,1951925.0984);e.map.setCenter(s),e.map.setZoom(12)}et.oR.success(`${s.lyrNm} 레이어가 지도에 추가되었습니다`),t(!1)}else throw Error("레이어 정보를 가져올 수 없습니다")}catch(e){console.error("레이어 추가 실패:",e),et.oR.error(`${s.lyrNm} 레이어 추가에 실패했습니다`)}finally{w(e=>{let s=new Set(e);return s.delete(a),s})}}},k=(0,r.useCallback)(async(e,s=!1)=>{o(!0);try{let t=new URLSearchParams({userId:"geonuser",holdDataSeCode:"0",pageIndex:e.toString(),pageSize:"20"});g.trim()&&t.append("searchTxt",g.trim()),N&&"all"!==N&&t.append("lyrTySeCode",N);let a=process.env.NEXT_PUBLIC_GEON_API_KEY||"";a&&t.append("crtfckey",a);let r=await fetch(`/api/layers?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error(`API request failed with status ${r.status}`);let l=await r.json();if(200===l.code&&l.result){let e=l.result.list||[];n(t=>{let a=s?e:[...t,...e];return m(20===e.length&&a.length<l.result.pageInfo.totalCount),a}),b(l.result.pageInfo.totalCount)}}catch(e){console.error("Failed to fetch layers:",e)}finally{o(!1)}},[g,N]),S=(0,r.useCallback)(e=>{let{scrollTop:s,scrollHeight:t,clientHeight:a}=e.currentTarget;t-s<=1.5*a&&c&&!i&&p(e=>e+1)},[c,i]),R=(0,r.useCallback)(()=>{n([]),p(1),m(!0),k(1,!0)},[k]),I=(0,r.useCallback)(e=>{v(e),n([]),p(1),m(!0),setTimeout(()=>k(1,!0),0)},[k]);return(0,r.useCallback)(()=>{n([]),p(1),m(!0),k(1,!0)},[k]),(0,a.jsxs)(O.lG,{open:s,onOpenChange:t,children:[(0,a.jsx)(O.zM,{asChild:!0,children:(0,a.jsxs)(u.$,{variant:"ghost",size:"sm",className:"h-9 flex items-center justify-start gap-2 w-full bg-background/80 hover:bg-background/90 backdrop-blur-md shadow-sm border transition-all duration-300",children:[(0,a.jsx)(K.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"레이어 검색"})]})}),(0,a.jsxs)(O.Cf,{className:"max-w-4xl max-h-[80vh] flex flex-col",children:[(0,a.jsx)(O.c7,{children:(0,a.jsxs)(O.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(K.A,{className:"h-5 w-5"}),"레이어 목록",f>0&&(0,a.jsxs)(D.E,{variant:"secondary",className:"ml-2",children:["총 ",f,"개"]})]})}),(0,a.jsxs)("div",{className:"flex gap-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(H.A,{className:(0,d.cn)("absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4",i?"text-blue-500 animate-pulse":"text-muted-foreground")}),(0,a.jsx)(B.p,{placeholder:"레이어 이름으로 검색...",value:g,onChange:e=>j(e.target.value),onKeyDown:e=>"Enter"===e.key&&R(),className:"pl-10"})]}),(0,a.jsxs)(J,{value:N,onValueChange:I,children:[(0,a.jsxs)(V,{className:"w-32",children:[(0,a.jsx)(ee.A,{className:"h-4 w-4 mr-2"}),(0,a.jsx)(U,{placeholder:"타입"})]}),(0,a.jsxs)(q,{children:[(0,a.jsx)(X,{value:"all",children:"전체"}),(0,a.jsx)(X,{value:"1",children:"점"}),(0,a.jsx)(X,{value:"2",children:"선"}),(0,a.jsx)(X,{value:"3",children:"면"})]})]}),(0,a.jsx)(u.$,{onClick:R,disabled:i,children:"검색"})]}),(0,a.jsx)("div",{className:"flex-1 h-[400px] overflow-y-auto",onScroll:S,children:(0,a.jsxs)("div",{className:"space-y-2 pr-4",children:[l.map(e=>(0,a.jsx)("div",{className:"p-3 border rounded-lg hover:bg-accent/50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("h4",{className:"font-medium truncate",children:e.lyrNm}),(0,a.jsx)(D.E,{className:(0,d.cn)("text-xs",eb(e.lyrTySeCode)),children:e.lyrTySeCodeNm})]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground mb-1",children:["ID: ",e.lyrId]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,a.jsx)("span",{children:e.lyrClCodeNm}),(0,a.jsxs)("span",{children:["소유자: ",e.ownerNm]}),(0,a.jsx)("span",{children:new Date(e.registDt).toLocaleDateString()})]})]}),(0,a.jsx)("div",{className:"flex-shrink-0 ml-3",children:(0,a.jsx)(es.EA,{content:"지도에 레이어 추가",children:(0,a.jsx)(u.$,{variant:"outline",size:"sm",onClick:()=>A(e),disabled:y.has(e.lyrId),className:"h-8 w-8 p-0",children:y.has(e.lyrId)?(0,a.jsx)("div",{className:"h-3 w-3 animate-spin rounded-full border border-current border-t-transparent"}):(0,a.jsx)(h.A,{className:"h-3 w-3"})})})})]})},e.lyrId)),i&&(0,a.jsx)("div",{className:"space-y-2",children:Array.from({length:5}).map((e,s)=>(0,a.jsxs)("div",{className:"p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(_.E,{className:"h-4 w-48"}),(0,a.jsx)(_.E,{className:"h-5 w-12"})]}),(0,a.jsx)(_.E,{className:"h-3 w-32 mb-1"}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(_.E,{className:"h-3 w-20"}),(0,a.jsx)(_.E,{className:"h-3 w-16"}),(0,a.jsx)(_.E,{className:"h-3 w-24"})]})]},s))}),!i&&!c&&l.length>0&&(0,a.jsx)("div",{className:"text-center py-4 text-muted-foreground",children:"모든 레이어를 불러왔습니다."}),!i&&0===l.length&&(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"검색 결과가 없습니다."})]})})]})]})}function ej({mapState:e,className:s}){return(0,a.jsxs)("div",{className:(0,d.cn)("absolute top-4 right-4 z-10 flex flex-col gap-2 max-w-[280px]",s),children:[(0,a.jsx)(eg,{mapState:e}),e&&(0,a.jsx)(P,{mapState:e})]})}var eN=t(91097);let ev=eN.bL,ey=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(eN.B8,{ref:t,className:(0,d.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));ey.displayName=eN.B8.displayName;let ew=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(eN.l9,{ref:t,className:(0,d.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));ew.displayName=eN.l9.displayName;let eC=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(eN.UC,{ref:t,className:(0,d.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));eC.displayName=eN.UC.displayName;var eA=t(23292),ek=t(73996),eS=t(66170),eR=t(43162),eI=t(85477),eE=t(51058),ez=t(64714),e$=t(25883);let eL=()=>{let{layers:e}=ef();return e.map(e=>{let{source:s,toolCallId:t,...a}=e;return"geojson"===e.type?{id:a.id,type:a.type,data:a.data,visible:a.visible??!0,service:a.service,opacity:a.opacity,zIndex:a.zIndex,bbox:a.bbox,autoFit:a.autoFit,renderOptions:a.style?{style:a.style}:void 0,dataProjectionCode:a.dataProjectionCode||"EPSG:5186",featureProjectionCode:a.featureProjectionCode||"EPSG:5186"}:"geoserver"===e.type?{id:a.id,type:"geoserver",server:a.server||"",layer:a.layer||"",service:a.service||"wfs",info:a.info||{lyrId:a.id,lyrNm:a.name||"Unknown Layer"},name:a.name,visible:a.visible??!0,opacity:a.opacity,zIndex:a.zIndex,filter:a.filter,bbox:a.bbox,autoFit:a.autoFit,method:a.method,crtfckey:a.crtfckey,projection:a.projection,geometryType:a.geometryType,serviceTy:a.serviceTy,renderOptions:a.style?{style:a.style}:a.renderOptions}:{...a,type:a.type,id:a.id,visible:a.visible??!0,autoFit:a.autoFit,fitDuration:100}})},eM=()=>ef();var eF=t(64261);let eT=eF.bL,eP=eF.l9,eO=r.forwardRef(({className:e,align:s="center",sideOffset:t=4,...r},l)=>(0,a.jsx)(eF.ZL,{children:(0,a.jsx)(eF.UC,{ref:l,align:s,sideOffset:t,className:(0,d.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));eO.displayName=eF.UC.displayName;var eD=t(27616),e_=t(91473);let eB=r.forwardRef(({className:e,...s},t)=>(0,a.jsxs)(e_.bL,{ref:t,className:(0,d.cn)("relative flex w-full touch-none select-none items-center",e),...s,children:[(0,a.jsx)(e_.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,a.jsx)(e_.Q6,{className:"absolute h-full bg-primary"})}),(0,a.jsx)(e_.zi,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));eB.displayName=e_.bL.displayName;var eW=t(48034),eG=t(79903),eZ=t(79),eJ=t(13044),eU=t(62423),eV=t(61091),eQ=t(74884);let eY=e=>{if(!Array.isArray(e))return"Filter";let[s,t,a]=e,r={"==":"=","!=":"≠",">":">","<":"<",">=":"≥","<=":"≤","*=":"포함","&&":"그리고","||":"또는"};if("&&"===s||"||"===s){let e=Array.isArray(t)?eY(t):t,l=Array.isArray(a)?eY(a):a;return`${e} ${r[s]} ${l}`}let l=r[s]||s,n="string"==typeof t?t:String(t),i=null===a?"null":String(a);if("*="===s)return`${n} 포함 "${i}"`;if(null===a)return"=="===s?`${n} 없음`:`${n} 있음`;if("number"==typeof a){if(">"===s)return`${n} > ${i}`;if("<"===s)return`${n} < ${i}`;if(">="===s)return`${n} ≥ ${i}`;if("<="===s)return`${n} ≤ ${i}`}return`${n} ${l} ${i}`};function eq({layer:e,onStyleChange:s}){let[t,l]=(0,r.useState)(!1),n="wms"===e.service,i="wfs"===e.service,o=e.geometryType||"point",d=()=>{let e=c();p(e),console.log("스타일 초기화:",e)},c=()=>{let s=e.renderOptions?.style||e.style;if(s)return s;if(n)switch(o){case"point":case"1":default:break;case"line":case"2":return er();case"polygon":case"3":return el()}else if(i)switch(o){case"point":case"1":default:return en();case"line":case"2":return ei();case"polygon":case"3":return eo()}return ea()},[m,x]=(0,r.useState)(c()),p=e=>{x(e),s(e)},f=e=>{switch(e){case"circle":default:return(0,a.jsx)(eZ.A,{size:16});case"square":return(0,a.jsx)(eJ.A,{size:16});case"triangle":return(0,a.jsx)(eU.A,{size:16});case"star":return(0,a.jsx)(eV.A,{size:16});case"cross":return(0,a.jsx)(h.A,{size:16});case"x":return(0,a.jsx)(e$.A,{size:16})}};return(0,a.jsxs)(eT,{open:t,onOpenChange:l,children:[(0,a.jsx)(eP,{asChild:!0,children:(0,a.jsx)(u.$,{variant:"ghost",size:"icon",children:(0,a.jsx)(eQ.A,{size:16})})}),(0,a.jsx)(eO,{className:"w-96 p-0",side:"left",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-semibold text-base",children:"레이어 스타일"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(D.E,{variant:"outline",className:"text-xs",children:n?"WMS":i?"WFS":"기타"}),(0,a.jsx)(D.E,{variant:"secondary",className:"text-xs",children:(e=>{switch(e){case"point":case"1":return"포인트";case"line":case"2":return"라인";case"polygon":case"3":return"폴리곤";default:return"알 수 없음"}})(o)})]})]}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground font-medium truncate",children:e.name||e.id}),n&&(0,a.jsxs)("div",{className:"space-y-2",children:["rules"in m&&1===m.rules.length&&(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsx)(u.$,{variant:"ghost",size:"sm",className:"flex-1 text-xs text-muted-foreground",onClick:d,children:"초기화"})}),"rules"in m&&m.rules.length>1&&(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(u.$,{variant:"outline",size:"sm",className:"flex-1 text-xs",onClick:()=>{let e=(()=>{switch(o){case"point":case"1":default:return ea();case"line":case"2":return er();case"polygon":case"3":return el()}})();p(e),console.log("단일 스타일로 변경:",e)},children:[(0,a.jsx)(eZ.A,{size:14,className:"mr-1"}),"단일 스타일로 변경"]}),(0,a.jsx)(u.$,{variant:"ghost",size:"sm",className:"flex-1 text-xs text-muted-foreground",onClick:d,children:"초기화"})]})]}),(0,a.jsx)(eG.w,{})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[n&&"rules"in m&&(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto space-y-1",children:(e=>{if(e.rules.length>1)return(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-sm font-medium",children:"유형별 스타일"}),(0,a.jsxs)(D.E,{variant:"secondary",className:"text-xs",children:[e.rules.length,"개 규칙"]})]}),e.rules.map((s,t)=>{let r=s.symbolizers[0],l=s=>{let a={...r,...s};p({...e,rules:e.rules.map((e,s)=>s===t?{...e,symbolizers:[a]}:e)})};return(0,a.jsx)(eW.Zp,{className:"mb-3",children:(0,a.jsxs)(eW.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)("span",{className:"text-sm font-medium",children:s.name})}),s.filter&&(0,a.jsx)(D.E,{variant:"outline",className:"text-xs",children:eY(s.filter)})]}),"Mark"===r.kind&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"색상"}),(0,a.jsx)(B.p,{type:"color",value:r.color,onChange:e=>l({color:e.target.value}),className:"h-8 w-full cursor-pointer"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"크기"}),(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs",children:[r.radius,"px"]})]}),(0,a.jsx)(eB,{value:[r.radius],onValueChange:([e])=>l({radius:e}),min:1,max:20,step:1,className:"w-full"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"투명도"}),(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs",children:[Math.round(100*r.fillOpacity),"%"]})]}),(0,a.jsx)(eB,{value:[r.fillOpacity],onValueChange:([e])=>l({fillOpacity:e}),min:0,max:1,step:.1,className:"w-full"})]}),r.strokeColor&&(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"윤곽선 색상"}),(0,a.jsx)(B.p,{type:"color",value:r.strokeColor,onChange:e=>l({strokeColor:e.target.value}),className:"h-8 w-full cursor-pointer"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"윤곽선 두께"}),(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs",children:[r.strokeWidth||1,"px"]})]}),(0,a.jsx)(eB,{value:[r.strokeWidth||1],onValueChange:([e])=>l({strokeWidth:e}),min:0,max:10,step:1,className:"w-full"})]})]})]}),"Line"===r.kind&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"색상"}),(0,a.jsx)(B.p,{type:"color",value:r.color,onChange:e=>l({color:e.target.value}),className:"h-8 w-full cursor-pointer"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"두께"}),(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs",children:[r.width,"px"]})]}),(0,a.jsx)(eB,{value:[r.width],onValueChange:([e])=>l({width:e}),min:1,max:10,step:1,className:"w-full"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"투명도"}),(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs",children:[Math.round(100*r.opacity),"%"]})]}),(0,a.jsx)(eB,{value:[r.opacity],onValueChange:([e])=>l({opacity:e}),min:0,max:1,step:.1,className:"w-full"})]})]}),"Fill"===r.kind&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"채우기 색상"}),(0,a.jsx)(B.p,{type:"color",value:r.color,onChange:e=>l({color:e.target.value}),className:"h-8 w-full cursor-pointer"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"투명도"}),(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs",children:[Math.round(100*r.fillOpacity),"%"]})]}),(0,a.jsx)(eB,{value:[r.fillOpacity],onValueChange:([e])=>l({fillOpacity:e}),min:0,max:1,step:.1,className:"w-full"})]})]}),r.outlineColor&&(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"윤곽선 색상"}),(0,a.jsx)(B.p,{type:"color",value:r.outlineColor,onChange:e=>l({outlineColor:e.target.value}),className:"h-8 w-full cursor-pointer"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"윤곽선 두께"}),(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs",children:[r.outlineWidth||1,"px"]})]}),(0,a.jsx)(eB,{value:[r.outlineWidth||1],onValueChange:([e])=>l({outlineWidth:e}),min:0,max:10,step:1,className:"w-full"})]})]})]})]})},t)})]});let s=e.rules[0],t=s.symbolizers[0];return"Mark"===t.kind?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(eW.Zp,{children:(0,a.jsx)(eW.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-sm font-medium",children:"심볼 타입"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[f(t.wellKnownName),(0,a.jsx)("span",{className:"text-xs text-muted-foreground capitalize",children:t.wellKnownName})]})]}),(0,a.jsxs)(J,{value:t.wellKnownName,onValueChange:a=>{p({...e,rules:[{...s,symbolizers:[{...t,wellKnownName:a}]}]})},children:[(0,a.jsx)(V,{className:"h-9",children:(0,a.jsx)(U,{})}),(0,a.jsxs)(q,{children:[(0,a.jsx)(X,{value:"circle",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(eZ.A,{size:14}),(0,a.jsx)("span",{children:"원"})]})}),(0,a.jsx)(X,{value:"square",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(eJ.A,{size:14}),(0,a.jsx)("span",{children:"사각형"})]})}),(0,a.jsx)(X,{value:"triangle",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(eU.A,{size:14}),(0,a.jsx)("span",{children:"삼각형"})]})}),(0,a.jsx)(X,{value:"star",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(eV.A,{size:14}),(0,a.jsx)("span",{children:"별"})]})}),(0,a.jsx)(X,{value:"cross",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{size:14}),(0,a.jsx)("span",{children:"십자"})]})}),(0,a.jsx)(X,{value:"x",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(e$.A,{size:14}),(0,a.jsx)("span",{children:"X"})]})})]})]})]})})}),(0,a.jsx)(eW.Zp,{children:(0,a.jsx)(eW.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-sm font-medium",children:"크기"}),(0,a.jsxs)(D.E,{variant:"secondary",children:[t.radius,"px"]})]}),(0,a.jsx)(eB,{value:[t.radius],onValueChange:([a])=>{p({...e,rules:[{...s,symbolizers:[{...t,radius:a}]}]})},min:1,max:20,step:1,className:"w-full"})]})})}),(0,a.jsx)(eW.Zp,{children:(0,a.jsx)(eW.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(eD.J,{className:"text-sm font-medium",children:"채우기"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"색상"}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)(B.p,{type:"color",value:t.color,onChange:a=>{p({...e,rules:[{...s,symbolizers:[{...t,color:a.target.value}]}]})},className:"h-9 w-full cursor-pointer"})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"투명도"}),(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs",children:[Math.round(100*t.fillOpacity),"%"]})]}),(0,a.jsx)(eB,{value:[t.fillOpacity],onValueChange:([a])=>{p({...e,rules:[{...s,symbolizers:[{...t,fillOpacity:a}]}]})},min:0,max:1,step:.1,className:"w-full"})]})]})]})})}),t.strokeColor&&(0,a.jsx)(eW.Zp,{children:(0,a.jsx)(eW.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(eD.J,{className:"text-sm font-medium",children:"윤곽선"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"색상"}),(0,a.jsx)(B.p,{type:"color",value:t.strokeColor,onChange:a=>{p({...e,rules:[{...s,symbolizers:[{...t,strokeColor:a.target.value}]}]})},className:"h-9 w-full cursor-pointer"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"두께"}),(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs",children:[t.strokeWidth||1,"px"]})]}),(0,a.jsx)(eB,{value:[t.strokeWidth||1],onValueChange:([a])=>{p({...e,rules:[{...s,symbolizers:[{...t,strokeWidth:a}]}]})},min:0,max:10,step:1,className:"w-full"})]})]})]})})})]}):"Line"===t.kind?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(eW.Zp,{children:(0,a.jsx)(eW.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(eD.J,{className:"text-sm font-medium",children:"선 스타일"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"색상"}),(0,a.jsx)(B.p,{type:"color",value:t.color,onChange:a=>{p({...e,rules:[{...s,symbolizers:[{...t,color:a.target.value}]}]})},className:"h-9 w-full cursor-pointer"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"투명도"}),(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs",children:[Math.round(100*t.opacity),"%"]})]}),(0,a.jsx)(eB,{value:[t.opacity],onValueChange:([a])=>{p({...e,rules:[{...s,symbolizers:[{...t,opacity:a}]}]})},min:0,max:1,step:.1,className:"w-full"})]})]})]})})}),(0,a.jsx)(eW.Zp,{children:(0,a.jsx)(eW.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-sm font-medium",children:"선 두께"}),(0,a.jsxs)(D.E,{variant:"secondary",children:[t.width,"px"]})]}),(0,a.jsx)(eB,{value:[t.width],onValueChange:([a])=>{p({...e,rules:[{...s,symbolizers:[{...t,width:a}]}]})},min:1,max:10,step:1,className:"w-full"})]})})})]}):"Fill"===t.kind?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(eW.Zp,{children:(0,a.jsx)(eW.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(eD.J,{className:"text-sm font-medium",children:"채우기"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"색상"}),(0,a.jsx)(B.p,{type:"color",value:t.color,onChange:a=>{p({...e,rules:[{...s,symbolizers:[{...t,color:a.target.value}]}]})},className:"h-9 w-full cursor-pointer"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"투명도"}),(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs",children:[Math.round(100*t.fillOpacity),"%"]})]}),(0,a.jsx)(eB,{value:[t.fillOpacity],onValueChange:([a])=>{p({...e,rules:[{...s,symbolizers:[{...t,fillOpacity:a}]}]})},min:0,max:1,step:.1,className:"w-full"})]})]})]})})}),t.outlineColor&&(0,a.jsx)(eW.Zp,{children:(0,a.jsx)(eW.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(eD.J,{className:"text-sm font-medium",children:"윤곽선"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"색상"}),(0,a.jsx)(B.p,{type:"color",value:t.outlineColor,onChange:a=>{p({...e,rules:[{...s,symbolizers:[{...t,outlineColor:a.target.value}]}]})},className:"h-9 w-full cursor-pointer"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eD.J,{className:"text-xs text-muted-foreground",children:"두께"}),(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs",children:[t.outlineWidth||1,"px"]})]}),(0,a.jsx)(eB,{value:[t.outlineWidth||1],onValueChange:([a])=>{p({...e,rules:[{...s,symbolizers:[{...t,outlineWidth:a}]}]})},min:0,max:10,step:1,className:"w-full"})]})]})]})})})]}):null})(m)}),i&&!("rules"in m)&&(0,a.jsx)(eW.Zp,{children:(0,a.jsx)(eW.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:"개발 중"}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"WFS 레이어 스타일 편집기는 추후 구현 예정입니다."})]})})}),!n&&!i&&(0,a.jsx)(eW.Zp,{children:(0,a.jsx)(eW.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:"지원되지 않음"}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"이 레이어 타입은 스타일 편집을 지원하지 않습니다."})]})})})]})]})})]})}var eX=t(10342);function eK({map:e,layers:s}){let t=eM(),r=[...s].sort((e,s)=>{let t=e.zIndex||0;return(s.zIndex||0)-t}),l=e=>{t.toggleVisibility(e)},n=e=>{t.removeLayer(e)},o=(e,s)=>{t.updateStyle(e,s)};return 0===s.length?(0,a.jsx)(w,{className:"w-full rounded-md border p-4",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,a.jsx)(eI.A,{className:"h-12 w-12 text-muted-foreground/50 mb-4"}),(0,a.jsx)("h3",{className:"font-medium mb-2",children:"레이어가 없습니다"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"새로운 레이어를 추가하여 지도를 구성해보세요"})]})}):(0,a.jsx)(w,{className:"w-full rounded-md border p-4",children:(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)(eX.JY,{onDragEnd:e=>{if(console.log("handleDragEnd",e),!e.destination)return;let s=e.source.index,a=e.destination.index;if(s===a)return;let l=Array.from(r),[n]=l.splice(s,1);l.splice(a,0,n);let i=l.map(e=>e.id).filter(Boolean);console.log("Reordering layers:",i),t.reorderLayers(i)},children:(0,a.jsx)(eX.gL,{droppableId:"layers",children:(e,s)=>(0,a.jsxs)("div",{...e.droppableProps,ref:e.innerRef,className:`space-y-2 transition-colors duration-200 ${s.isDraggingOver?"bg-blue-50/50 rounded-lg p-2":""}`,children:[r.map((e,s)=>{let t=e.id||`layer-${s}`,r=e.zIndex||0;return(0,a.jsx)(eX.sx,{draggableId:t,index:s,isDragDisabled:!1,children:(t,d)=>(0,a.jsx)("div",{ref:t.innerRef,...t.draggableProps,className:`rounded-lg bg-card border transition-all duration-200 ${d.isDragging?"shadow-lg scale-105 rotate-2 bg-white border-blue-300":"shadow-sm hover:shadow-md"} ${!e.visible?"opacity-60":""}`,children:(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{...t.dragHandleProps,className:"cursor-grab active:cursor-grabbing p-1 rounded hover:bg-muted/50 transition-colors",children:(0,a.jsx)(i.A,{className:"h-4 w-4 text-muted-foreground"})}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium text-sm",children:e.name||e.id||`Layer ${s+1}`}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.geometryType||"unknown"," • ",e.visible?"표시됨":"숨김"," • z:",r]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(u.$,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:()=>e.id&&l(e.id),title:e.visible?"레이어 숨기기":"레이어 보이기",children:e.visible?(0,a.jsx)(eE.A,{size:14}):(0,a.jsx)(ez.A,{size:14})}),(0,a.jsx)(eq,{layer:e,onStyleChange:s=>e.id&&o(e.id,s)}),(0,a.jsx)(u.$,{variant:"ghost",size:"icon",className:"h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10",onClick:()=>e.id&&n(e.id),title:"레이어 삭제",children:(0,a.jsx)(e$.A,{size:14})})]})]})})})},t)}),e.placeholder]})})})})})}var eH=t(86270),e0=t(69519),e1=t(52105),e2=t.n(e1),e4=t(90980),e3=t(40106),e5=t(86839),e6=t(51043),e8=t(7456),e7=t(40198),e9=t(83886),se=t(42447),ss=t(39822);function st({children:e,className:s,gradientSize:t=200,gradientColor:l="#262626",gradientOpacity:n=.8,gradientFrom:i="#9E7AFF",gradientTo:o="#FE8BBB"}){let c=(0,r.useRef)(null),m=(0,e9.d)(-t),x=(0,e9.d)(-t),u=(0,r.useCallback)(e=>{if(c.current){let{left:s,top:t}=c.current.getBoundingClientRect(),a=e.clientX,r=e.clientY;m.set(a-s),x.set(r-t)}},[m,x]);return(0,r.useCallback)(e=>{e.relatedTarget||(document.removeEventListener("mousemove",u),m.set(-t),x.set(-t))},[u,m,t,x]),(0,r.useCallback)(()=>{document.addEventListener("mousemove",u),m.set(-t),x.set(-t)},[u,m,t,x]),(0,a.jsxs)("div",{ref:c,className:(0,d.cn)("group relative rounded-[inherit]",s),children:[(0,a.jsx)(se.P.div,{className:"pointer-events-none absolute inset-0 rounded-[inherit] bg-border duration-300 group-hover:opacity-100",style:{background:(0,ss.E)`
          radial-gradient(${t}px circle at ${m}px ${x}px,
          ${i}, 
          ${o}, 
          hsl(var(--border)) 100%
          )
          `}}),(0,a.jsx)("div",{className:"absolute inset-px rounded-[inherit] bg-background"}),(0,a.jsx)(se.P.div,{className:"pointer-events-none absolute inset-px rounded-[inherit] opacity-0 transition-opacity duration-300 group-hover:opacity-100",style:{background:(0,ss.E)`
            radial-gradient(${t}px circle at ${m}px ${x}px, ${l}, transparent 100%)
          `,opacity:n}}),(0,a.jsx)("div",{className:"relative",children:e})]})}var sa=t(70312);function sr({children:e,isEnabled:s,isThinking:t=!1,isDisabled:r=!1,disabledReason:l,className:n}){let i=(0,a.jsxs)(z.P.div,{className:"space-y-4 w-72",initial:{opacity:0,y:8},animate:{opacity:1,y:0},transition:{delay:.1,duration:.3,ease:"easeOut"},children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(z.P.div,{className:(0,d.cn)("relative p-2.5 rounded-xl transition-all duration-300",r?"bg-gray-50 text-gray-300 border border-gray-200 opacity-50":s?"bg-gradient-to-br from-blue-50 to-indigo-50 text-blue-600 shadow-sm border border-blue-100":"bg-gray-50 text-gray-400 border border-gray-200"),animate:s&&t&&!r?{scale:[1,1.05,1]}:{},transition:{duration:2,repeat:s&&t&&!r?1/0:0,ease:"easeInOut"},children:[(0,a.jsx)(e5.A,{className:"w-5 h-5"}),(0,a.jsx)(z.P.div,{className:"absolute -top-1 -right-1",animate:{scale:[0,1,0],opacity:[0,1,0]},transition:{duration:1.5,repeat:1/0,ease:"easeInOut"},children:(0,a.jsx)(sa.A,{className:"w-3 h-3 text-blue-500"})})]}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("h4",{className:(0,d.cn)("text-sm font-semibold transition-colors",r?"text-gray-400":s?"text-gray-900":"text-gray-500"),children:["AI 추론 ",r?"지원 안됨":s?"활성화":"비활성화"]})})]}),(0,a.jsx)("div",{className:"space-y-2",children:r?(0,a.jsx)(a.Fragment,{children:l&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-gray-300"}),(0,a.jsx)("span",{className:"text-gray-400",children:l})]})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs",children:[(0,a.jsx)("div",{className:(0,d.cn)("w-1.5 h-1.5 rounded-full",s?"bg-blue-500":"bg-gray-300")}),(0,a.jsx)("span",{className:(0,d.cn)("transition-colors",s?"text-gray-700":"text-gray-400"),children:"추론을 통해 좀 더 정확한 답변을 제공"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs",children:[(0,a.jsx)("div",{className:(0,d.cn)("w-1.5 h-1.5 rounded-full",s?"bg-blue-500":"bg-gray-300")}),(0,a.jsx)("span",{className:(0,d.cn)("transition-colors",s?"text-gray-700":"text-gray-400"),children:"시간이 좀 더 소요될 수 있습니다."})]})]})})]});return(0,a.jsxs)(j,{openDelay:150,closeDelay:200,children:[(0,a.jsx)(N,{asChild:!0,children:e}),(0,a.jsx)(v,{side:"top",align:"center",className:"p-0 border-0 bg-transparent shadow-none z-[100]",sideOffset:12,alignOffset:-50,avoidCollisions:!0,collisionBoundary:"undefined"!=typeof document?document.body:void 0,children:(0,a.jsx)(st,{className:(0,d.cn)("border-0 shadow-xl backdrop-blur-md p-4 overflow-hidden",s?"bg-gradient-to-br from-white/95 to-blue-50/95":"bg-white/95"),gradientSize:120,gradientColor:s?"#3b82f6":"#6b7280",gradientOpacity:s?.15:.08,gradientFrom:s?"#3b82f6":"#6b7280",gradientTo:s?"#6366f1":"#9ca3af",children:i})})]})}var sl=t(68995);let sn=(0,r.memo)(({status:e,fileInputRef:s,enableThinking:t,setEnableThinking:r,enableSmartNavigation:l,setEnableSmartNavigation:n,modelSupportsReasoning:i,selectedModelId:o})=>(0,a.jsx)("div",{className:"flex justify-between items-center px-3 py-2 border-t",children:(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)(sr,{isEnabled:t,isThinking:"streaming"===e,isDisabled:!i,disabledReason:(0,sl.SW)(o),children:(0,a.jsxs)(D.E,{variant:t&&i?"ai-active":"secondary",className:`group relative overflow-hidden ${!i?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,onClick:()=>{i&&r(!t)},children:[(0,a.jsx)(e5.A,{className:`w-3.5 h-3.5 mr-1.5 ${t&&i?"text-white":"text-gray-600"}`}),(0,a.jsx)("span",{className:"relative z-10",children:"AI 추론"}),t&&i&&"streaming"===e&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/10 to-white/5 animate-pulse"})]})})})})}),(e,s)=>e.status===s.status&&e.enableThinking===s.enableThinking&&e.enableSmartNavigation===s.enableSmartNavigation&&e.modelSupportsReasoning===s.modelSupportsReasoning&&e.selectedModelId===s.selectedModelId);sn.displayName="ActionButtons";let si=(0,r.memo)(function({chatId:e,input:s,setInput:t,status:l,stop:n,attachments:i,setAttachments:o,messages:d,setMessages:c,append:m,handleSubmit:x,enableThinking:h,setEnableThinking:p,enableSmartNavigation:f,setEnableSmartNavigation:b,modelSupportsReasoning:g,selectedModelId:j}){let N=(0,r.useRef)(null);(0,eH.useRouter)();let{setOpenMobile:v}=(0,e8.cL)(),{width:y}=(0,e0.lW)(),w=(0,r.useRef)(null),[C,A]=(0,r.useState)([]),[k,S]=(0,e0.Mj)("input",""),R=()=>{N.current&&(N.current.style.height="auto",N.current.style.height=`${N.current.scrollHeight+2}px`)},I=(0,r.useCallback)(e=>{t(e.target.value),R()},[t]),E=(0,r.useCallback)(()=>{window.history.replaceState({},"",`/geon-2d-map/${e}`),x(void 0,{experimental_attachments:i}),o([]),S(""),y&&y>768&&N.current?.focus()},[i,x,o,S,y,e]),z=async e=>{let s=new FormData;s.append("file",e);try{let e=await fetch("/api/files/upload",{method:"POST",body:s});if(e.ok){let{url:s,pathname:t,contentType:a}=await e.json();return{url:s,name:t,contentType:a}}let{error:t}=await e.json();et.oR.error(t)}catch(e){et.oR.error("Failed to upload file, please try again!")}},$=(0,r.useCallback)(async e=>{let s=Array.from(e.target.files||[]);A(s.map(e=>e.name));try{let e=s.map(e=>z(e)),t=(await Promise.all(e)).filter(e=>void 0!==e);o(e=>[...e,...t])}catch(e){console.error("Error uploading files!",e)}finally{A([])}},[o]),L=(0,r.useCallback)(e=>{o(s=>s.filter(s=>s.url!==e))},[o]);return(0,a.jsxs)("div",{className:"relative w-full flex flex-1 flex-col gap-4",children:[(0,a.jsx)("input",{type:"file",className:"fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none",ref:w,multiple:!0,onChange:$,tabIndex:-1}),(i.length>0||C.length>0)&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 p-2 bg-background rounded-lg",children:[i.map(e=>(0,a.jsx)(e6.q,{attachment:e,removeFile:L},e.url)),C.map(e=>(0,a.jsx)(e6.q,{attachment:{url:"",name:e,contentType:""},isUploading:!0},e))]}),(0,a.jsxs)("div",{className:"flex flex-col rounded-2xl border bg-background shadow-sm",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(e7.T,{ref:N,placeholder:"원하는 지역, 레이어 등을 입력해주세요.",value:s,onChange:I,className:"min-h-[48px] max-h-[40vh] overflow-hidden resize-none rounded-2xl !text-sm p-4 pr-16 w-full border-0 focus-visible:ring-0 focus-visible:ring-offset-0",rows:1,onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),"submitted"===l||"streaming"===l?et.oR.error("이전 응답이 완료될 때까지 잠시만 기다려 주세요!"):E())}}),(0,a.jsx)("div",{className:"absolute right-2 top-1/2 -translate-y-1/2",children:"submitted"===l||"streaming"===l?(0,a.jsx)(u.$,{size:"icon",variant:"ghost",className:"h-8 w-8 rounded-full",onClick:e=>{e.preventDefault(),n()},children:(0,a.jsx)(e4.wF,{size:16})}):(0,a.jsx)(u.$,{size:"icon",className:"h-8 w-8 rounded-full",onClick:e=>{x(e)},disabled:0===s.length||C.length>0,children:(0,a.jsx)(e3.A,{size:16})})})]}),(0,a.jsx)(sn,{status:l,fileInputRef:w,enableThinking:h,setEnableThinking:p,enableSmartNavigation:f,setEnableSmartNavigation:b,modelSupportsReasoning:g,selectedModelId:j})]})]})},(e,s)=>e.input===s.input&&e.status===s.status&&!!e2()(e.attachments,s.attachments)&&e.enableThinking===s.enableThinking&&e.enableSmartNavigation===s.enableSmartNavigation&&e.modelSupportsReasoning===s.modelSupportsReasoning&&e.selectedModelId===s.selectedModelId&&!0);var so=t(92802),sd=t(67443),sc=t(38035),sm=t(48134),sx=t(44132),su=t(12901);let sh=({children:e,className:s,shimmerWidth:t=100,...r})=>(0,a.jsx)("span",{style:{"--shiny-width":`${t}px`},className:(0,d.cn)("max-w-md text-neutral-600/70 dark:text-neutral-400/70","animate-shiny-text bg-clip-text bg-no-repeat [background-position:0_0] [background-size:var(--shiny-width)_100%] [transition:background-position_1s_cubic-bezier(.6,.6,0,1)_infinite]","bg-gradient-to-r from-transparent via-black/80 via-50% to-transparent  dark:via-white/80",s),...r,children:e});function sp({reasoning:e,className:s,initialOpen:t=!0,open:l,isReasoning:n}){let i=(0,r.useRef)(null),o=(0,r.useId)(),c=`reasoning-${o}`,[m,x]=(0,r.useState)(t),u=m?c:"",[h,p]=(0,r.useState)(""),[f,b]=(0,r.useState)(!1);return(0,a.jsx)(sx.nD,{type:"single",value:u,onValueChange:e=>x(e===c),className:(0,d.cn)("w-full h-auto",s),collapsible:!0,children:(0,a.jsxs)(sx.As,{value:c,className:"border-b-0 rounded-lg overflow-hidden bg-card",children:[(0,a.jsx)(sx.$m,{className:"px-3 py-0 h-6 min-h-6",children:(0,a.jsx)("div",{className:"flex items-center space-x-2",children:n?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(su.A,{className:`h-4 w-4 text-yellow-500 ${f?"animate-pulse":""}`}),(0,a.jsx)(sh,{className:"text-sm",children:h},h)]}):(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(su.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:h})]})})}),(0,a.jsx)(sx.ub,{className:"px-2 mt-2",children:(0,a.jsx)("div",{ref:i,className:"text-sm text-gray-500 bg-accent/80 max-h-[150px] overflow-y-auto whitespace-pre-wrap px-4 py-2 border-l-2 border-yellow-400 pl-3 ml-1",children:e})})]})})}var sf=t(72949),sb=t(7380),sg=t(57003),sj=t(29987),sN=t(30346),sv=t(75187),sy=t(41521);let sw={card:{base:`
      bg-white/80 backdrop-blur-sm border border-neutral-200/60 
      rounded-xl shadow-md hover:shadow-lg transition-all duration-200
    `,interactive:`
      hover:border-neutral-300/80 hover:-translate-y-0.5 
      cursor-pointer active:scale-[0.98]
    `,success:`
      bg-gradient-to-br from-success-50/90 to-success-100/70 
      border-success-200/60 hover:border-success-300/80
    `,warning:`
      bg-gradient-to-br from-warning-50/90 to-warning-100/70 
      border-warning-200/60 hover:border-warning-300/80
    `,error:`
      bg-gradient-to-br from-error-50/90 to-error-100/70 
      border-error-200/60 hover:border-error-300/80
    `},button:{primary:`
      bg-primary-500 hover:bg-primary-600 text-white 
      shadow-md hover:shadow-lg active:scale-95
      transition-all duration-200 font-medium
    `,secondary:`
      bg-neutral-100 hover:bg-neutral-200 text-neutral-700 
      border border-neutral-300 hover:border-neutral-400
      transition-all duration-200 font-medium
    `,ghost:`
      bg-transparent hover:bg-neutral-100 text-neutral-600 hover:text-neutral-800
      transition-all duration-200 font-medium
    `},badge:{success:`
      bg-success-100 text-success-700 border border-success-200
      font-medium text-xs px-2 py-1 rounded-md
    `,warning:`
      bg-warning-100 text-warning-700 border border-warning-200
      font-medium text-xs px-2 py-1 rounded-md
    `,info:`
      bg-primary-100 text-primary-700 border border-primary-200
      font-medium text-xs px-2 py-1 rounded-md
    `,neutral:`
      bg-neutral-100 text-neutral-700 border border-neutral-200
      font-medium text-xs px-2 py-1 rounded-md
    `},iconContainer:{sm:`
      flex h-6 w-6 items-center justify-center rounded-full
      bg-neutral-100 text-neutral-600
    `,md:`
      flex h-8 w-8 items-center justify-center rounded-full
      bg-neutral-100 text-neutral-600
    `,lg:`
      flex h-10 w-10 items-center justify-center rounded-full
      bg-neutral-100 text-neutral-600
    `}};var sC=t(5786);function sA({icon:e,title:s,state:t,children:l,className:n,initialOpen:i=!1,open:o,titleExtra:c}){let m=(0,r.useId)(),x=`result-${m}`,[u,h]=(0,r.useState)(i),p=function(e){switch(e){case"call":return{color:"bg-orange-500",label:"처리 중"};case"partial-call":return{color:"bg-red-500",label:"진행 중"};case"result":return{color:"bg-green-500",label:"완료"};default:return{color:"bg-gray-500",label:"알 수 없음"}}}(t),f=u?x:"";return(0,a.jsx)("div",{className:(0,d.cn)(sw.card.base,"bg-white/90 border-neutral-200/60",n),children:(0,a.jsx)(sx.nD,{type:"single",collapsible:!0,value:f,onValueChange:e=>h(e===x),className:"w-full",children:(0,a.jsxs)(sx.As,{value:x,className:"border-0",children:[(0,a.jsxs)(sx.$m,{className:"flex items-center gap-3 p-2 pr-4 hover:no-underline [&>svg]:hidden group hover:bg-neutral-50/50 transition-colors min-h-[40px]",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:(0,d.cn)(sw.iconContainer.sm,"bg-neutral-100/80 text-neutral-600 group-hover:bg-neutral-200/80 transition-colors flex-shrink-0"),children:[(0,a.jsx)("div",{className:"group-hover:hidden",children:e}),(0,a.jsx)("div",{className:"hidden group-hover:block",children:(0,a.jsx)(sC.A,{className:"w-4 h-4"})})]}),(0,a.jsx)("span",{className:"text-neutral-900 text-[13px] truncate",children:s}),c&&(0,a.jsx)("div",{className:"flex-shrink-0 ml-1",onClick:e=>e.stopPropagation(),children:c})]}),(0,a.jsx)("div",{className:"flex items-center gap-2 flex-shrink-0",children:(0,a.jsx)("div",{className:(0,d.cn)("w-2.5 h-2.5 rounded-full",p.color),title:p.label})})]}),(0,a.jsx)(sx.ub,{className:"px-3 pb-3",children:(0,a.jsx)("div",{className:"mt-2 rounded-lg bg-neutral-50/60 border border-neutral-200/50 p-3 text-sm",children:l})})]})})})}var sk=t(58154),sS=t(81760),sR=t(31099),sI=t(52990),sE=t(34594),sz=t(36298);let s$=e=>({getLayer:{label:"레이어 조회",icon:(0,a.jsx)(H.A,{className:"h-3 w-3"}),variant:"default"},getLayerInfo:{label:"레이어 정보",icon:(0,a.jsx)(H.A,{className:"h-3 w-3"}),variant:"default"},getLayerAttributes:{label:"속성 조회",icon:(0,a.jsx)(H.A,{className:"h-3 w-3"}),variant:"default"},getLayerList:{label:"레이어 검색",icon:(0,a.jsx)(H.A,{className:"h-3 w-3"}),variant:"default"},searchAddress:{label:"주소 검색",icon:(0,a.jsx)(H.A,{className:"h-3 w-3"}),variant:"default"},searchOrigin:{label:"출발지 검색",icon:(0,a.jsx)(sg.A,{className:"h-3 w-3 text-green-600"}),variant:"default"},searchDestination:{label:"목적지 검색",icon:(0,a.jsx)(sg.A,{className:"h-3 w-3 text-red-600"}),variant:"default"},searchDirections:{label:"경로 탐색",icon:(0,a.jsx)(sk.A,{className:"h-3 w-3"}),variant:"default"},setCenter:{label:"지도 이동",icon:(0,a.jsx)(sg.A,{className:"h-3 w-3"}),variant:"outline"},changeBasemap:{label:"배경지도 변경",icon:(0,a.jsx)(I.A,{className:"h-3 w-3"}),variant:"outline"},highlightGeometry:{label:"영역 강조",icon:(0,a.jsx)(sg.A,{className:"h-3 w-3"}),variant:"outline"},moveMapByDirection:{label:"방향 이동",icon:(0,a.jsx)(sg.A,{className:"h-3 w-3"}),variant:"outline"},setMapZoom:{label:"확대/축소",icon:(0,a.jsx)(sg.A,{className:"h-3 w-3"}),variant:"outline"},createLayerFilter:{label:"필터 적용",icon:(0,a.jsx)(ee.A,{className:"h-3 w-3"}),variant:"secondary"},createVectorStyle:{label:"스타일 생성",icon:(0,a.jsx)(sf.A,{className:"h-3 w-3"}),variant:"secondary"},updateLayerStyle:{label:"스타일 변경",icon:(0,a.jsx)(sS.A,{className:"h-3 w-3"}),variant:"secondary"},removeLayer:{label:"레이어 삭제",icon:(0,a.jsx)(sR.A,{className:"h-3 w-3"}),variant:"secondary"},generateCategoricalStyle:{label:"유형별 스타일",icon:(0,a.jsx)(sI.A,{className:"h-3 w-3"}),variant:"secondary"},performDensityAnalysis:{label:"밀도 분석",icon:(0,a.jsx)(sb.A,{className:"h-3 w-3"}),variant:"outline"},chooseOption:{label:"옵션 선택 제안",icon:(0,a.jsx)(eR.A,{className:"h-3 w-3"}),variant:"outline"},getUserInput:{label:"입력 요청 제안",icon:(0,a.jsx)(eR.A,{className:"h-3 w-3"}),variant:"outline"},confirmWithCheckbox:{label:"확인",icon:(0,a.jsx)(sE.A,{className:"h-3 w-3"}),variant:"outline"},getLocation:{label:"위치 정보 요청",icon:(0,a.jsx)(sg.A,{className:"h-3 w-3"}),variant:"outline"}})[e]||{label:e,icon:(0,a.jsx)(sz.A,{className:"h-3 w-3"}),variant:"secondary"},sL=({annotations:e,isLoading:s})=>{if(!e||0===e.length)return null;let t=[],r=!1,l="",n=!1,i=!1,o=[],d=null,c=null,m=null,x=-1,u=!1;return e.forEach((s,t)=>{"object"==typeof s&&null!==s&&!Array.isArray(s)&&"type"in s&&"agent_completed"===s.type&&e.slice(t+1).some(e=>"object"==typeof e&&null!==e&&!Array.isArray(e)&&"type"in e&&"reasoning"===e.type)&&(x=t,u=!0)}),e.forEach(e=>{"object"==typeof e&&null!==e&&!Array.isArray(e)&&"type"in e&&"tool_call"===e.type&&o.push(e.toolName)}),e.forEach((e,s)=>{if("object"==typeof e&&null!==e&&!Array.isArray(e)&&!("messageIdFromServer"in e)){if(u&&x>=0&&s<=x){"type"in e&&"reasoning"===e.type&&"textDelta"in e&&t.push(e.textDelta);return}"type"in e&&"reasoning"===e.type&&"textDelta"in e?t.push(e.textDelta):"type"in e&&"intent_analyzed"===e.type?(r=!0,l=e.message):"type"in e&&"agent_start"===e.type?n=!0:"type"in e&&"agent_completed"===e.type?(!u||s>x)&&(i=!0):"type"in e&&"evaluation_completed"===e.type?d=e:"type"in e&&"retry_starting"===e.type?c=e:"type"in e&&("retry_completed"===e.type||"retry_limit_reached"===e.type)&&(m=e)}}),(0,a.jsxs)("div",{className:"w-full max-w-4xl mx-auto",children:[!r&&s&&t.length>0&&(0,a.jsxs)("div",{className:"mb-4 px-1 relative",children:[(0,a.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-blue-500 rounded-full"}),(0,a.jsx)("div",{className:"pl-4",children:(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)(sh,{className:"text-sm inline-flex items-center justify-center px-2 py-1 transition ease-out hover:text-neutral-600 hover:duration-300 hover:dark:text-neutral-400",children:"메세지를 이해하고 있어요..."})})})]}),t.length>0&&(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)(sp,{reasoning:t.join(""),className:"my-2",initialOpen:!1,isReasoning:s&&!r})}),r&&l&&(0,a.jsxs)("div",{className:"mb-4 px-1 relative",children:[(0,a.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full"}),(0,a.jsx)("div",{className:"pl-4",children:(0,a.jsx)("p",{className:"text-sm text-gray-700 leading-relaxed font-medium",children:l})})]}),n&&(0,a.jsxs)("div",{className:"mb-4 px-1 relative",children:[(0,a.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-gray-200 rounded-full"}),(0,a.jsxs)("div",{className:"pl-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[!i&&(0,a.jsx)("span",{className:"text-sm font-medium text-gray-800",children:"작업 중"}),!i&&(0,a.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full animate-pulse"})]}),o.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:o.map((e,s)=>{let t=s$(e);return(0,a.jsx)(z.P.div,{initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.5},transition:{duration:.2},children:(0,a.jsxs)(D.E,{variant:t.variant,className:"text-xs px-2 py-1 font-medium flex items-center gap-1",children:[t.icon,t.label]},s)},s)})})]})]}),d&&(0,a.jsxs)("div",{className:"mb-4 px-1 relative",children:[(0,a.jsx)("div",{className:`absolute left-0 top-0 bottom-0 w-1 rounded-full ${d.isCompleted?"bg-green-500":"bg-yellow-500"}`}),(0,a.jsxs)("div",{className:"pl-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[d.isCompleted?(0,a.jsx)(sE.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(sb.A,{className:"h-4 w-4 text-yellow-600"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:d.message})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-600 mt-1",children:[(0,a.jsx)("div",{className:"font-medium mb-1",children:"평가 내용"}),(0,a.jsx)("p",{children:d.reason})]}),(()=>{let e=d.improvementSuggestions;return Array.isArray(e)&&e.length>0?(0,a.jsxs)("div",{className:"text-xs text-gray-600 mt-2",children:[(0,a.jsx)("div",{className:"font-medium mb-1",children:"개선 제안"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-1",children:e.map((e,s)=>(0,a.jsx)("li",{children:String(e)},s))})]}):null})()]})]}),c&&(0,a.jsxs)("div",{className:"mb-4 px-1 relative",children:[(0,a.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-blue-500 rounded-full"}),(0,a.jsxs)("div",{className:"pl-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-sm font-medium text-blue-700",children:c.message})]}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:c.reason})]})]}),m&&(0,a.jsxs)("div",{className:"mb-4 px-1 relative",children:[(0,a.jsx)("div",{className:`absolute left-0 top-0 bottom-0 w-1 rounded-full ${"retry_completed"===m.type&&"success"===m.finalResult?"bg-green-500":"bg-orange-500"}`}),(0,a.jsxs)("div",{className:"pl-4",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)("span",{className:"text-sm font-medium",children:m.message})}),(0,a.jsxs)("div",{className:"text-xs text-gray-600 mt-1",children:["총 ",m.totalIterations,"회 시도 완료"]})]})]}),!s&&!r&&!n&&!i&&t.length>0&&(0,a.jsxs)("div",{className:"mt-3 px-1 relative",children:[(0,a.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-gray-300 rounded-full"}),(0,a.jsx)("div",{className:"pl-4",children:(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"처리 완료"})})]})]})};function sM({toolName:e,state:s,content:t,className:r,initialOpen:l=!1,open:n}){let i="result"===s,o={createLayerFilter:"레이어 필터 적용 완료",getLayer:"레이어 정보 조회 완료",getLayerAttributes:"레이어 속성 조회 완료",getLayerList:"레이어 목록 검색 완료",performDensityAnalysis:"밀도 분석 완료",searchAddress:"주소 검색 완료",searchOrigin:"출발지 검색 완료",searchDestination:"목적지 검색 완료",searchDirections:"경로 탐색 완료",changeBasemap:"배경지도 변경 완료",createVectorStyle:"벡터 스타일 생성 완료",getLocation:"현재 위치 확인 완료",highlightGeometry:"도형 강조 완료",setCenter:"지도 중심 이동 완료",chooseOption:"선택 완료",getUserInput:"정보 입력 완료",confirmWithCheckbox:"확인 완료",moveMapByDirection:"지도 이동 완료",setMapZoom:"지도 확대/축소 완료",generateCategoricalStyle:"유형별 스타일 생성 완료"}[e]||`${e} 실행 결과`,c=["chooseOption","getUserInput","confirmWithCheckbox","getLocation"].includes(e),m={createLayerFilter:(0,a.jsx)(sf.A,{className:"h-4 w-4"}),updateLayerStyle:(0,a.jsx)(eQ.A,{className:"h-4 w-4"}),getLayer:(0,a.jsx)(sf.A,{className:"h-4 w-4"}),getLayerAttributes:(0,a.jsx)(H.A,{className:"h-4 w-4"}),getLayerList:(0,a.jsx)(H.A,{className:"h-4 w-4"}),performDensityAnalysis:(0,a.jsx)(sb.A,{className:"h-4 w-4"}),searchAddress:(0,a.jsx)(H.A,{className:"h-4 w-4"}),searchOrigin:(0,a.jsx)(sg.A,{className:"h-4 w-4 text-green-600"}),searchDestination:(0,a.jsx)(sg.A,{className:"h-4 w-4 text-blue-600"}),searchDirections:(0,a.jsx)(sj.A,{className:"h-4 w-4"}),changeBasemap:(0,a.jsx)(sg.A,{className:"h-4 w-4"}),createVectorStyle:(0,a.jsx)(sb.A,{className:"h-4 w-4"}),getLocation:(0,a.jsx)(sg.A,{className:"h-4 w-4"}),highlightGeometry:(0,a.jsx)(sb.A,{className:"h-4 w-4"}),setCenter:(0,a.jsx)(sg.A,{className:"h-4 w-4"}),chooseOption:(0,a.jsx)(sN.A,{className:"h-4 w-4"}),getUserInput:(0,a.jsx)(sv.A,{className:"h-4 w-4"}),confirmWithCheckbox:(0,a.jsx)(sy.A,{className:"h-4 w-4"}),moveMapByDirection:(0,a.jsx)(sj.A,{className:"h-4 w-4"}),setMapZoom:(0,a.jsx)(sj.A,{className:"h-4 w-4"}),generateCategoricalStyle:(0,a.jsx)(eQ.A,{className:"h-4 w-4"})}[e]||(0,a.jsx)(sb.A,{className:"h-4 w-4"}),x=t;if("string"==typeof t)try{let e=JSON.parse(t);e&&"object"==typeof e&&(x=e)}catch(e){}if(c){let s="";if("object"==typeof x&&null!==x){let t=x;if("chooseOption"===e)s=`선택: ${"option"in t?t.option:JSON.stringify(t)}`;else if("getUserInput"===e)s=`입력: ${"input"in t?t.input:JSON.stringify(t)}`;else if("confirmWithCheckbox"===e)s="confirmed"in t&&!0===t.confirmed?"확인 및 동의 완료":"동의하지 않음";else if("getLocation"===e)if("latitude"in t&&"longitude"in t){let e=Number(t.latitude).toFixed(6),a=Number(t.longitude).toFixed(6),r=t.accuracy?` (정확도: ${Math.round(t.accuracy)}m)`:"";s=`위치: ${e}, ${a}${r}`}else s=JSON.stringify(t);else s=JSON.stringify(t)}else if("string"==typeof x)if("chooseOption"===e){let[e]=x.split("|");s=`${e}`}else s="confirmWithCheckbox"===e?"true"===x.toLowerCase()?"확인 및 동의 완료":"동의하지 않음":x;else s="boolean"==typeof x?"confirmWithCheckbox"===e?!0===x?"확인 및 동의 완료":"동의하지 않음":String(x):JSON.stringify(x);return(0,a.jsxs)(eW.Zp,{className:(0,d.cn)(sw.card.base,"bg-neutral-50/60 border-neutral-200/50"),children:[(0,a.jsx)(eW.aR,{className:"py-3 px-4",children:(0,a.jsxs)(eW.ZB,{className:"text-sm font-medium flex items-center gap-2",children:[(0,a.jsx)("div",{className:(0,d.cn)(sw.iconContainer.sm,"bg-neutral-100/80"),children:m}),(0,a.jsx)("span",{className:"text-neutral-900",children:o})]})}),(0,a.jsx)(eW.Wu,{className:"px-4 pb-3 text-sm text-neutral-700",children:(0,a.jsx)("p",{children:s})})]})}let u=s$(e);return(0,a.jsxs)(sA,{icon:u.icon,title:u.label,state:s,className:r,initialOpen:l,open:n,children:[(0,a.jsx)("div",{className:"text-xs text-neutral-600 mb-2",children:i?"실행 결과":"partial-call"===s?"작업이 부분적으로 완료되었어요":"작업을 처리하는 중이에요"}),(0,a.jsx)("div",{className:"text-neutral-700",children:"object"==typeof x?(0,a.jsx)("pre",{className:"whitespace-pre-wrap text-xs font-mono bg-white/60 p-2 rounded border border-neutral-200/40 overflow-x-auto max-h-32 overflow-y-auto",children:JSON.stringify(x,null,2)}):(0,a.jsx)("div",{className:"bg-white/60 p-2 rounded border border-neutral-200/40 max-h-32 overflow-y-auto",children:(0,a.jsx)("p",{className:"whitespace-pre-wrap text-xs",children:x})})})]})}var sF=t(45869),sT=t(36468),sP=t(32807),sO=t(44810);let sD=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(sO.bL,{ref:t,className:(0,d.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:(0,a.jsx)(sO.C1,{className:(0,d.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(Z.A,{className:"h-4 w-4"})})}));sD.displayName=sO.bL.displayName;let s_={chooseOption:"옵션 선택",getUserInput:"입력 요청",confirmWithCheckbox:"확인 요청",getLocation:"위치 정보 요청"};function sB({invocation:e,onDone:s}){let{toolName:t,toolCallId:l,state:n}=e,i=s_[t]??t;return(0,a.jsxs)(eW.Zp,{className:"w-full shadow-sm border-0",children:[(0,a.jsxs)(eW.aR,{className:"flex px-0 py-2 flex-row items-center gap-3",children:[(0,a.jsx)(sP.eu,{className:"h-6 w-6",children:(0,a.jsx)(sP.q5,{children:"\uD83E\uDD16"})}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:[i," 제안을 준비했어요!"]})]}),(0,a.jsx)(eW.Wu,{className:"p-2",children:(()=>{switch(t){case"chooseOption":{var n;let{message:t,options:r}=e.args||{},i=(n=r?.length||0)<=2?"grid-cols-1 sm:grid-cols-2":n<=4?"grid-cols-2 sm:grid-cols-2":n<=6?"grid-cols-2 sm:grid-cols-3":"grid-cols-2 sm:grid-cols-3 lg:grid-cols-4";return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("p",{className:"text-sm whitespace-pre-wrap break-all mb-2",children:t}),(0,a.jsx)("div",{className:`grid ${i} gap-2`,children:r?.map((e,t)=>{let r=e.includes("|")?e.split("|")[0]:e;return(0,a.jsx)(u.$,{variant:"outline",className:"justify-start gap-2 h-auto hover:bg-primary/10 break-words whitespace-break-spaces transition-transform hover:-translate-y-0.5",onClick:()=>{s({toolCallId:l,result:e})},children:r},`${l}-${t}-${e.substring(0,20)}`)})})]})}case"getUserInput":{let{message:t,default:n}=e.args||{},[i,o]=(0,r.useState)(n??"");return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("p",{className:"text-sm whitespace-pre-wrap break-all mb-2",children:t}),(0,a.jsx)(B.p,{value:i,onChange:e=>o(e.target.value)}),(0,a.jsx)("div",{className:"mt-2 flex gap-2",children:(0,a.jsx)(u.$,{onClick:()=>s({toolCallId:l,result:i}),disabled:""===i.trim(),children:"확인"})})]})}case"confirmWithCheckbox":{let{message:t,label:n}=e.args||{},[i,o]=(0,r.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("p",{className:"text-sm whitespace-pre-wrap break-all mb-2",children:t}),(0,a.jsxs)("label",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(sD,{checked:i,onCheckedChange:o})," ",(0,a.jsx)("span",{children:n})]}),(0,a.jsx)(u.$,{onClick:()=>s({toolCallId:l,result:i}),disabled:!i,children:"확인"})]})}case"getLocation":{let{message:t}=e.args||{},[n,i]=(0,r.useState)(!1),[o,d]=(0,r.useState)(null),c=()=>{if(i(!0),d(null),!navigator.geolocation){d("이 브라우저는 위치 서비스를 지원하지 않습니다."),i(!1);return}navigator.geolocation.getCurrentPosition(e=>{let{latitude:t,longitude:a}=e.coords;i(!1),s({toolCallId:l,result:{latitude:t,longitude:a,accuracy:e.coords.accuracy,timestamp:e.timestamp}})},e=>{i(!1);let s="위치 정보를 가져올 수 없습니다.";switch(e.code){case e.PERMISSION_DENIED:s="위치 접근 권한이 거부되었습니다. 브라우저 설정에서 위치 권한을 허용해주세요.";break;case e.POSITION_UNAVAILABLE:s="위치 정보를 사용할 수 없습니다.";break;case e.TIMEOUT:s="위치 정보 요청 시간이 초과되었습니다."}d(s)},{enableHighAccuracy:!0,timeout:1e4,maximumAge:3e5})};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("p",{className:"text-sm whitespace-pre-wrap break-all mb-2",children:t||"현재 위치 정보를 가져오시겠습니까?"}),o&&(0,a.jsx)("div",{className:"mb-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700",children:o}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(u.$,{onClick:c,disabled:n,className:"flex items-center gap-2",children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"위치 확인 중..."]}):"현재 위치 확인"}),o&&(0,a.jsx)(u.$,{variant:"outline",onClick:()=>{d(null),c()},children:"다시 시도"})]})]})}default:return(0,a.jsx)("pre",{className:"text-sm bg-accent/20 p-2 rounded-md max-h-40 overflow-auto",children:JSON.stringify(e.args??{},null,2)})}})()}),(0,a.jsx)(eW.wL,{className:"p-0"})]})}function sW({invocation:e,addToolResult:s,className:t,initialOpen:n=!0,open:i}){let o=(0,r.useId)(),c=`tool-call-${o}`,m=(0,r.useRef)(null),[x,u]=l().useState(!1),[h,p]=(0,r.useState)(!1),f=h?c:"",{toolName:b,state:g}=e,j=JSON.stringify(e.args??{},null,2),N=(e=>{switch(e){case"call":return{label:"처리 중",className:"border-blue-500 bg-blue-50 text-blue-700",icon:(0,a.jsx)(sF.A,{className:"h-3 w-3 animate-spin"})};case"partial-call":return{label:"진행 중",className:"border-purple-500 bg-purple-50 text-purple-700",icon:(0,a.jsx)(sF.A,{className:"h-3 w-3 animate-spin"})};case"result":return{label:"완료",className:"border-green-500 bg-green-50 text-green-700",icon:(0,a.jsx)(Z.A,{className:"h-3 w-3"})};default:return{label:e,className:"border-gray-300 bg-gray-50 text-gray-700",icon:(0,a.jsx)(sT.A,{className:"h-3 w-3"})}}})(g),v=["chooseOption","getUserInput","confirmWithCheckbox","getLocation"].includes(b),y={createLayerFilter:{name:"레이어 필터 생성",icon:(0,a.jsx)(sf.A,{className:"h-4 w-4"}),description:"지정한 조건으로 레이어에 필터를 적용합니다."},updateLayerStyle:{name:"레이어 스타일 변경",icon:(0,a.jsx)(eQ.A,{className:"h-4 w-4"}),description:"레이어의 색상, 투명도, 크기 등을 변경합니다."},generateCategoricalStyle:{name:"유형별 스타일 생성",icon:(0,a.jsx)(eQ.A,{className:"h-4 w-4"}),description:"속성 값에 따라 다른 색상으로 레이어를 표시합니다."},getLayer:{name:"레이어 정보 조회",icon:(0,a.jsx)(sf.A,{className:"h-4 w-4"}),description:"선택한 레이어의 상세 정보를 가져옵니다."},getLayerAttributes:{name:"레이어 속성 조회",icon:(0,a.jsx)(H.A,{className:"h-4 w-4"}),description:"레이어의 속성 정보를 확인합니다."},getLayerList:{name:"레이어 목록 검색",icon:(0,a.jsx)(H.A,{className:"h-4 w-4"}),description:"입력한 키워드로 레이어 목록을 검색합니다."},performDensityAnalysis:{name:"밀도 분석 수행",icon:(0,a.jsx)(sb.A,{className:"h-4 w-4"}),description:"선택한 레이어에 대한 밀도 분석을 실행합니다."},searchAddress:{name:"주소 검색",icon:(0,a.jsx)(H.A,{className:"h-4 w-4"}),description:"입력한 주소 또는 장소를 검색합니다."},searchDirections:{name:"경로 탐색",icon:(0,a.jsx)(sj.A,{className:"h-4 w-4"}),description:"출발지와 도착지 간의 경로를 탐색합니다."},changeBasemap:{name:"배경지도 변경",icon:(0,a.jsx)(sg.A,{className:"h-4 w-4"}),description:"선택한 배경지도로 변경합니다."},createVectorStyle:{name:"벡터 스타일 생성",icon:(0,a.jsx)(sb.A,{className:"h-4 w-4"}),description:"레이어에 적용할 새로운 벡터 스타일을 생성합니다."},getLocation:{name:"현재 위치 확인",icon:(0,a.jsx)(sg.A,{className:"h-4 w-4"}),description:"현재 사용자의 위치 정보를 가져옵니다."},highlightGeometry:{name:"도형 강조",icon:(0,a.jsx)(sb.A,{className:"h-4 w-4"}),description:"지도 위의 특정 도형을 강조하여 표시합니다."},setCenter:{name:"지도 중심 이동",icon:(0,a.jsx)(sg.A,{className:"h-4 w-4"}),description:"지정한 위치로 지도 중심을 이동합니다."},chooseOption:{name:"옵션 선택",icon:(0,a.jsx)(sf.A,{className:"h-4 w-4"}),description:"제시된 옵션 중 하나를 선택해 주세요."},getUserInput:{name:"정보 입력 요청",icon:(0,a.jsx)(sv.A,{className:"h-4 w-4"}),description:"작업을 계속하려면 추가 정보를 입력해 주세요."},confirmWithCheckbox:{name:"확인 요청",icon:(0,a.jsx)(sy.A,{className:"h-4 w-4"}),description:"내용을 확인하고 동의해 주세요."}},w={name:b,icon:(0,a.jsx)(sb.A,{className:"h-4 w-4"}),description:"작업을 처리하고 있습니다..."},{name:C,icon:A,description:k}=y[b]||w;return v?(0,a.jsx)(sB,{invocation:e,onDone:s}):(0,a.jsx)(z.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:(0,d.cn)("rounded-xl border bg-card shadow-sm",t),children:(0,a.jsx)(sx.nD,{type:"single",collapsible:!0,value:f,onValueChange:e=>p(e===c),className:"w-full",children:(0,a.jsx)(sx.As,{value:c,className:"border-0",children:(0,a.jsxs)("div",{className:"flex items-start gap-3 p-4",children:[(0,a.jsx)("div",{className:"mt-0.5 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-blue-50",children:(0,a.jsx)(sN.A,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,a.jsx)(sx.$m,{className:"group flex items-center gap-2 p-0 text-sm hover:no-underline [&[data-state=open]>svg]:rotate-180",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:(0,d.cn)("flex h-5 w-5 items-center justify-center rounded-full border-2",N.className),children:N.icon}),(0,a.jsx)("span",{className:"font-medium text-foreground text-left",children:C})]})}),(0,a.jsx)(es.Bc,{children:(0,a.jsxs)(es.m_,{children:[(0,a.jsx)(es.k$,{asChild:!0,children:(0,a.jsxs)(D.E,{variant:"outline",className:(0,d.cn)("flex items-center gap-1 text-xs font-normal h-6 px-2 py-0.5",N.className),children:[N.icon,N.label]})}),(0,a.jsx)(es.ZI,{side:"top",children:(0,a.jsx)("p",{className:"text-xs",children:k})})]})})]}),(0,a.jsx)(sx.ub,{className:"mt-3 pl-0 pb-0",children:(0,a.jsx)(z.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},className:"overflow-hidden",children:(0,a.jsxs)("div",{ref:m,className:"mt-2 rounded-lg bg-muted/30 p-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"mt-0.5 flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 text-blue-600",children:A}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"font-medium text-foreground",children:C}),(0,a.jsx)("p",{className:"text-muted-foreground text-sm mt-0.5",children:k})]})]}),(0,a.jsx)("div",{className:"mt-3 rounded-md border bg-background p-3",children:(0,a.jsx)("pre",{className:"whitespace-pre-wrap break-all text-xs",children:j})})]})})})]})]})})})})}var sG=t(71116),sZ=t(5829),sJ=t(32288),sU=t(86730),sV=t(64051);function sQ({content:e,className:s,mapState:t,onDirectionsRequest:l}){let n,[i,o]=(0,r.useState)(!1),[c,m]=(0,r.useState)(null),[x,h]=(0,r.useState)(null);try{n=e}catch(e){return null}let p=(0,r.useCallback)(e=>{let s=parseFloat(e.buildLo),t=parseFloat(e.buildLa),a=e.buildName||e.poiName||e.roadAddr.split(" ").slice(0,2).join(" ");return`${s},${t},name=${a}`},[]),f=(0,r.useCallback)((e,s)=>{let t=p(e),a=p(s);l&&l(t,a),et.oR.success(`${e.buildName||"출발지"}에서 ${s.buildName||"목적지"}까지 경로를 검색합니다`)},[p,l]),b=(0,r.useCallback)(e=>{navigator.clipboard.writeText(e),et.oR.success("주소가 복사되었습니다")},[]),g=(0,r.useCallback)(e=>{if(!t)return void et.oR.error("지도가 로드되지 않았습니다");ec(t,parseFloat(e.buildLo),parseFloat(e.buildLa),e.buildGeom||e.geom,16)?et.oR.success(`${e.roadAddr}로 이동했습니다`):et.oR.error("지도 이동 중 오류가 발생했습니다")},[t]),j=(0,r.useCallback)(e=>{m(e),et.oR.success(`출발지로 설정: ${e.buildName||e.roadAddr}`)},[]),N=(0,r.useCallback)(e=>{h(e),et.oR.success(`목적지로 설정: ${e.buildName||e.roadAddr}`)},[]),v=(0,r.useCallback)(()=>{c&&x&&(f(c,x),m(null),h(null))},[c,x,f]);if(!n.result?.jusoList?.length)return(0,a.jsx)("div",{className:(0,d.cn)("rounded-xl border border-amber-200/60 bg-gradient-to-r from-amber-50/80 to-orange-50/80 backdrop-blur-sm p-3",s),children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex h-7 w-7 items-center justify-center rounded-full bg-amber-100/80",children:(0,a.jsx)(sg.A,{className:"h-3.5 w-3.5 text-amber-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-amber-900 text-sm",children:"검색 결과가 없습니다"}),(0,a.jsx)("p",{className:"text-xs text-amber-700",children:"다른 키워드로 다시 검색해보세요"})]})]})});let y=n.result.jusoList,w=i?y:y.slice(0,3),C=s$("searchAddress");return(0,a.jsx)(sA,{icon:C.icon,title:C.label,state:"result",className:s,titleExtra:(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs py-0 border bg-emerald-100/80 text-emerald-700 border-emerald-200/60",children:[y.length,"개 발견"]}),children:(0,a.jsxs)("div",{className:"space-y-2",children:[(c||x)&&(0,a.jsx)("div",{className:(0,d.cn)(sw.card.base,"p-3 bg-gradient-to-r from-blue-50/95 to-indigo-50/90 border-blue-200/60"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(sk.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("span",{className:"font-medium text-blue-900",children:"길찾기 설정 중"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-1 text-xs text-blue-700",children:[c&&(0,a.jsxs)("span",{className:"bg-blue-100 px-2 py-0.5 rounded",children:["출발: ",c.buildName||c.roadAddr.split(" ").slice(0,2).join(" ")]}),x&&(0,a.jsxs)("span",{className:"bg-blue-100 px-2 py-0.5 rounded",children:["도착: ",x.buildName||x.roadAddr.split(" ").slice(0,2).join(" ")]})]})]})]}),c&&x&&(0,a.jsxs)(u.$,{size:"sm",onClick:v,className:(0,d.cn)(sw.button.primary,"h-8 px-3 text-xs"),children:[(0,a.jsx)(sk.A,{className:"h-3 w-3 mr-1"}),"길찾기 시작"]})]})}),(0,a.jsx)("div",{className:"space-y-2",children:w.map((e,s)=>(0,a.jsx)("div",{className:"group",children:(0,a.jsx)(eW.Zp,{className:(0,d.cn)(sw.card.base,sw.card.interactive,"cursor-pointer bg-white/90 border-neutral-200/60 hover:border-blue-300/70","hover:bg-gradient-to-r hover:from-blue-50/60 hover:to-indigo-50/40","shadow-sm hover:shadow-md transition-all duration-200"),children:(0,a.jsx)(eW.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3 mb-2",children:[(0,a.jsx)("div",{className:(0,d.cn)(sw.iconContainer.sm,"bg-blue-500/10 border border-blue-200/50 mt-0.5 flex-shrink-0"),children:(0,a.jsx)(sg.A,{className:"h-3 w-3 text-blue-600"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"font-semibold text-neutral-900 text-sm leading-tight",children:e.roadAddr}),e.buildName&&(0,a.jsxs)("div",{className:"flex items-center gap-1.5 mt-1",children:[(0,a.jsx)(sG.A,{className:"h-3 w-3 text-blue-600 flex-shrink-0"}),(0,a.jsx)("p",{className:"text-xs text-blue-700 font-medium truncate",children:e.buildName})]})]})]}),(0,a.jsxs)("p",{className:"text-xs text-neutral-600 ml-9 truncate bg-neutral-50/80 px-2 py-1 rounded-md",children:["지번: ",e.jibunAddr]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"flex gap-1",children:(0,a.jsxs)(u.$,{size:"sm",variant:"ghost",className:(0,d.cn)(sw.button.ghost,"h-8 px-2 text-xs hover:bg-blue-100/80 hover:text-blue-700 rounded-lg"),onClick:s=>{s.stopPropagation(),g(e)},children:[(0,a.jsx)(sZ.A,{className:"h-3 w-3 mr-1"}),"보기"]})}),(0,a.jsxs)(sV.rI,{children:[(0,a.jsx)(sV.ty,{asChild:!0,children:(0,a.jsx)(u.$,{size:"sm",variant:"ghost",className:(0,d.cn)(sw.button.ghost,"h-8 w-8 p-0 hover:bg-neutral-100/80 rounded-lg"),children:(0,a.jsx)(sJ.A,{className:"h-3.5 w-3.5"})})}),(0,a.jsxs)(sV.SQ,{align:"end",className:"w-48",children:[(0,a.jsxs)(sV._2,{onClick:()=>j(e),className:"flex items-center gap-2",children:[(0,a.jsx)(eV.A,{className:"h-3.5 w-3.5 text-green-600"}),"출발지로 설정"]}),(0,a.jsxs)(sV._2,{onClick:()=>N(e),className:"flex items-center gap-2",children:[(0,a.jsx)(sg.A,{className:"h-3.5 w-3.5 text-red-600"}),"목적지로 설정"]}),(0,a.jsx)(sV.mB,{}),(0,a.jsxs)(sV._2,{onClick:()=>b(e.roadAddr),className:"flex items-center gap-2",children:[(0,a.jsx)(sU.A,{className:"h-3.5 w-3.5"}),"주소 복사"]})]})]})]})]})})})},s))}),y.length>3&&(0,a.jsx)("div",{children:(0,a.jsx)(u.$,{variant:"ghost",size:"sm",onClick:()=>o(!i),className:(0,d.cn)(sw.button.secondary,"w-full h-9 text-xs font-medium border-dashed border-neutral-300","hover:border-solid hover:border-blue-300 hover:bg-blue-50/60 hover:text-blue-700","transition-all duration-200"),children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(G.A,{className:"h-3.5 w-3.5 mr-2"}),"접기"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E.A,{className:"h-3.5 w-3.5 mr-2"}),y.length-3,"개 더보기"]})})})]})})}var sY=t(75321);function sq({content:e,className:s,mapState:t}){let l,[n,i]=(0,r.useState)(!1);try{l=e}catch(e){return null}if(!l.result?.jusoList?.length)return(0,a.jsx)("div",{className:(0,d.cn)(sw.card.error,"p-3",s),children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:(0,d.cn)(sw.iconContainer.sm,"bg-red-100/80 text-red-600 border border-red-200/60"),children:(0,a.jsx)(sg.A,{className:"h-3.5 w-3.5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-red-900 text-sm",children:"출발지를 찾을 수 없습니다"}),(0,a.jsx)("p",{className:"text-xs text-red-700",children:"다른 키워드로 다시 검색해보세요"})]})]})});let o=l.result.jusoList,c=n?o:o.slice(0,3),m=(0,r.useCallback)(e=>`${e.buildLo},${e.buildLa}${e.buildName?`,name=${e.buildName}`:""}`,[]),x=(0,r.useCallback)(e=>{navigator.clipboard.writeText(e),et.oR.success("주소가 복사되었습니다")},[]),h=(0,r.useCallback)(e=>{if(!t)return void et.oR.error("지도가 로드되지 않았습니다");ec(t,parseFloat(e.buildLo),parseFloat(e.buildLa),e.buildGeom||e.geom,16)?et.oR.success(`출발지 ${e.roadAddr}로 이동했습니다`):et.oR.error("지도 이동 중 오류가 발생했습니다")},[t]),p=s$("searchOrigin");return(0,a.jsx)(sA,{icon:p.icon,title:p.label,state:"result",className:s,titleExtra:(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs py-0 border bg-green-100/80 text-green-700 border-green-200/60",children:[o.length,"개 발견"]}),children:(0,a.jsxs)("div",{className:"space-y-2",children:[c.map((e,s)=>(0,a.jsx)(eW.Zp,{className:(0,d.cn)(sw.card.interactive,"group"),children:(0,a.jsx)(eW.Wu,{className:"p-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("div",{className:(0,d.cn)(sw.iconContainer.sm,"bg-green-100/80 text-green-600 border border-green-200/60"),children:(0,a.jsx)(sY.A,{className:"h-3 w-3"})}),e.buildName&&(0,a.jsx)("span",{className:"font-medium text-sm text-gray-900 truncate",children:e.buildName})]}),(0,a.jsx)("p",{className:"text-xs text-gray-600 mb-1 leading-relaxed",children:e.roadAddr}),e.jibunAddr&&e.jibunAddr!==e.roadAddr&&(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["지번: ",e.jibunAddr]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(u.$,{size:"sm",variant:"ghost",onClick:()=>h(e),className:(0,d.cn)(sw.button.ghost,"h-7 w-7 p-0"),children:(0,a.jsx)(sZ.A,{className:"h-3 w-3"})}),(0,a.jsxs)(sV.rI,{children:[(0,a.jsx)(sV.ty,{asChild:!0,children:(0,a.jsx)(u.$,{size:"sm",variant:"ghost",className:(0,d.cn)(sw.button.ghost,"h-7 w-7 p-0"),children:(0,a.jsx)(sJ.A,{className:"h-3 w-3"})})}),(0,a.jsxs)(sV.SQ,{align:"end",className:"w-48",children:[(0,a.jsxs)(sV._2,{onClick:()=>x(e.roadAddr),children:[(0,a.jsx)(sU.A,{className:"h-3 w-3 mr-2"}),"도로명주소 복사"]}),e.jibunAddr&&(0,a.jsxs)(sV._2,{onClick:()=>x(e.jibunAddr),children:[(0,a.jsx)(sU.A,{className:"h-3 w-3 mr-2"}),"지번주소 복사"]}),(0,a.jsx)(sV.mB,{}),(0,a.jsxs)(sV._2,{onClick:()=>x(m(e)),children:[(0,a.jsx)(sk.A,{className:"h-3 w-3 mr-2"}),"좌표 복사"]})]})]})]})]})})},s)),o.length>3&&(0,a.jsx)("div",{className:"flex justify-center pt-2",children:(0,a.jsx)(u.$,{variant:"ghost",size:"sm",onClick:()=>i(!n),className:(0,d.cn)(sw.button.ghost,"text-xs"),children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(G.A,{className:"h-3 w-3 mr-1"}),"접기"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E.A,{className:"h-3 w-3 mr-1"}),o.length-3,"개 더 보기"]})})})]})})}var sX=t(33265);function sK({content:e,className:s,mapState:t}){let l,[n,i]=(0,r.useState)(!1);try{l=e}catch(e){return null}if(!l.result?.jusoList?.length)return(0,a.jsx)("div",{className:(0,d.cn)(sw.card.error,"p-3",s),children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:(0,d.cn)(sw.iconContainer.sm,"bg-red-100/80 text-red-600 border border-red-200/60"),children:(0,a.jsx)(sX.A,{className:"h-3.5 w-3.5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-red-900 text-sm",children:"목적지를 찾을 수 없습니다"}),(0,a.jsx)("p",{className:"text-xs text-red-700",children:"다른 키워드로 다시 검색해보세요"})]})]})});let o=l.result.jusoList,c=n?o:o.slice(0,3),m=(0,r.useCallback)(e=>`${e.buildLo},${e.buildLa}${e.buildName?`,name=${e.buildName}`:""}`,[]),x=(0,r.useCallback)(e=>{navigator.clipboard.writeText(e),et.oR.success("주소가 복사되었습니다")},[]),h=(0,r.useCallback)(e=>{if(!t)return void et.oR.error("지도가 로드되지 않았습니다");ec(t,parseFloat(e.buildLo),parseFloat(e.buildLa),e.buildGeom||e.geom,16)?et.oR.success(`목적지 ${e.roadAddr}로 이동했습니다`):et.oR.error("지도 이동 중 오류가 발생했습니다")},[t]),p=s$("searchDestination");return(0,a.jsx)(sA,{icon:p.icon,title:p.label,state:"result",className:s,titleExtra:(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs py-0 border bg-red-100/80 text-red-700 border-red-200/60",children:[o.length,"개 발견"]}),children:(0,a.jsxs)("div",{className:"space-y-2",children:[c.map((e,s)=>(0,a.jsx)(eW.Zp,{className:(0,d.cn)(sw.card.interactive,"group"),children:(0,a.jsx)(eW.Wu,{className:"p-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("div",{className:(0,d.cn)(sw.iconContainer.sm,"bg-blue-100/80 text-blue-600 border border-blue-200/60"),children:(0,a.jsx)(sX.A,{className:"h-3 w-3"})}),e.buildName&&(0,a.jsx)("span",{className:"font-medium text-sm text-gray-900 truncate",children:e.buildName})]}),(0,a.jsx)("p",{className:"text-xs text-gray-600 mb-1 leading-relaxed",children:e.roadAddr}),e.jibunAddr&&e.jibunAddr!==e.roadAddr&&(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["지번: ",e.jibunAddr]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(u.$,{size:"sm",variant:"ghost",onClick:()=>h(e),className:(0,d.cn)(sw.button.ghost,"h-7 w-7 p-0"),children:(0,a.jsx)(sZ.A,{className:"h-3 w-3"})}),(0,a.jsxs)(sV.rI,{children:[(0,a.jsx)(sV.ty,{asChild:!0,children:(0,a.jsx)(u.$,{size:"sm",variant:"ghost",className:(0,d.cn)(sw.button.ghost,"h-7 w-7 p-0"),children:(0,a.jsx)(sJ.A,{className:"h-3 w-3"})})}),(0,a.jsxs)(sV.SQ,{align:"end",className:"w-48",children:[(0,a.jsxs)(sV._2,{onClick:()=>x(e.roadAddr),children:[(0,a.jsx)(sU.A,{className:"h-3 w-3 mr-2"}),"도로명주소 복사"]}),e.jibunAddr&&(0,a.jsxs)(sV._2,{onClick:()=>x(e.jibunAddr),children:[(0,a.jsx)(sU.A,{className:"h-3 w-3 mr-2"}),"지번주소 복사"]}),(0,a.jsx)(sV.mB,{}),(0,a.jsxs)(sV._2,{onClick:()=>x(m(e)),children:[(0,a.jsx)(sk.A,{className:"h-3 w-3 mr-2"}),"좌표 복사"]})]})]})]})]})})},s)),o.length>3&&(0,a.jsx)("div",{className:"flex justify-center pt-2",children:(0,a.jsx)(u.$,{variant:"ghost",size:"sm",onClick:()=>i(!n),className:(0,d.cn)(sw.button.ghost,"text-xs"),children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(G.A,{className:"h-3 w-3 mr-1"}),"접기"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E.A,{className:"h-3 w-3 mr-1"}),o.length-3,"개 더 보기"]})})})]})})}var sH=t(95932),s0=t(25161);let s1=e=>e<1e3?`${Math.round(e)}m`:`${(e/1e3).toFixed(1)}km`,s2=e=>{let s=Math.floor(e/3600),t=Math.floor(e%3600/60);return s>0?`${s}시간 ${t}분`:`${t}분`};function s4({content:e,className:s,mapState:t,toolCallId:r}){let l;ef();try{l="string"==typeof e?JSON.parse(e):e}catch(e){return null}if(!l.routes?.length||0!==l.routes[0].result_code){let e=l.routes?.[0]?.result_msg||"다른 출발지나 목적지를 시도해보세요";return(0,a.jsx)("div",{className:(0,d.cn)(sw.card.error,"p-3",s),children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:(0,d.cn)(sw.iconContainer.sm,"bg-red-100/80 text-red-600 border border-red-200/60"),children:(0,a.jsx)(sH.A,{className:"h-3.5 w-3.5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-red-900 text-sm",children:"경로를 찾을 수 없습니다"}),(0,a.jsx)("p",{className:"text-xs text-red-700",children:e})]})]})})}let n=l.routes[0],i=n.summary,o=s$("searchDirections");return(0,a.jsx)(sA,{icon:o.icon,title:o.label,state:"result",className:s,titleExtra:(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(D.E,{variant:"outline",className:"text-xs py-0 border bg-emerald-100/80 text-emerald-700 border-emerald-200/60",children:s1(i.distance)}),(0,a.jsx)(D.E,{variant:"outline",className:"text-xs py-0 border bg-emerald-100/80 text-emerald-700 border-emerald-200/60",children:s2(i.duration)})]}),children:(0,a.jsxs)("div",{className:"space-y-2",children:[i.fare&&(0,a.jsx)("div",{children:(0,a.jsx)(eW.Zp,{className:(0,d.cn)(sw.card.base,"bg-purple-50/60 border-purple-200/50"),children:(0,a.jsx)(eW.Wu,{className:"p-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(s0.A,{className:"h-4 w-4 text-purple-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-purple-900",children:"예상 요금"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 text-sm",children:[(0,a.jsxs)("div",{className:"text-neutral-700",children:["택시: ",(0,a.jsxs)("span",{className:"font-medium",children:[i.fare.taxi.toLocaleString(),"원"]})]}),(0,a.jsxs)("div",{className:"text-neutral-700",children:["통행료: ",(0,a.jsxs)("span",{className:"font-medium",children:[i.fare.toll.toLocaleString(),"원"]})]})]})]})})})}),n.sections&&n.sections.length>0&&(0,a.jsx)("div",{children:(0,a.jsx)(eW.Zp,{className:(0,d.cn)(sw.card.base,"bg-neutral-50/60 border-neutral-200/50"),children:(0,a.jsxs)(eW.Wu,{className:"p-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(sk.A,{className:"h-4 w-4 text-neutral-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-neutral-900",children:"구간 정보"})]}),(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs bg-neutral-100/80 text-neutral-700 border-neutral-200/60",children:[n.sections.length,"개 구간"]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[n.sections.slice(0,3).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs p-2 bg-white/80 rounded border border-neutral-200/50",children:[(0,a.jsxs)("span",{className:"text-neutral-700",children:["구간 ",s+1]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-neutral-600",children:s1(e.distance)}),(0,a.jsx)("span",{className:"text-neutral-600",children:s2(e.duration)})]})]},s)),n.sections.length>3&&(0,a.jsxs)("div",{className:"text-center text-xs text-neutral-500 py-1",children:["+",n.sections.length-3,"개 구간 더"]})]})]})})})]})})}var s3=t(82440),s5=t(80378),s6=t(50847);let s8=e=>{let s={wms:"bg-blue-100/80 text-blue-700 border-blue-200/60",wfs:"bg-emerald-100/80 text-emerald-700 border-emerald-200/60",vector:"bg-purple-100/80 text-purple-700 border-purple-200/60",raster:"bg-orange-100/80 text-orange-700 border-orange-200/60",default:"bg-neutral-100/80 text-neutral-700 border-neutral-200/60"};return s[e]||s.default};function s7({content:e,className:s,mapState:t}){let l;try{l="string"==typeof e?JSON.parse(e):e}catch(e){return null}let n=async e=>{if(!t)return void et.oR.error("지도가 로드되지 않았습니다")},i=e=>{console.log("레이어 정보 보기:",e),et.oR.info(`${e.lyrNm} 레이어 정보를 표시합니다`)};if(200!==l.code||!l.result?.list?.length)return(0,a.jsx)("div",{className:(0,d.cn)(sw.card.warning,"p-3",s),children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:(0,d.cn)(sw.iconContainer.sm,"bg-amber-100/80 text-amber-600 border border-amber-200/60"),children:(0,a.jsx)(H.A,{className:"h-3.5 w-3.5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-amber-900 text-sm",children:"레이어 목록 검색 결과가 없습니다"}),(0,a.jsx)("p",{className:"text-xs text-amber-700",children:"다른 키워드로 다시 검색해보세요"})]})]})});let o=l.result.list,c=l.result.pageInfo.totalCount,[m,x]=(0,r.useState)(o.length<=5),p=m?o:o.slice(0,5),f=s$("getLayerList");return(0,a.jsx)(sA,{icon:f.icon,title:f.label,state:"result",className:s,titleExtra:(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs py-0 border bg-purple-100/80 text-purple-700 border-purple-200/60",children:[c,"개 발견"]}),o.length>5&&(0,a.jsx)(D.E,{variant:"outline",className:"text-xs py-0 border bg-neutral-100/80 text-neutral-700 border-neutral-200/60",children:"상위 5개"})]}),children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"space-y-1.5",children:p.map(e=>(0,a.jsx)("div",{className:"group",children:(0,a.jsx)(eW.Zp,{className:(0,d.cn)(sw.card.base,sw.card.interactive,"bg-white/90 border-neutral-200/60 hover:border-purple-300/60 hover:bg-purple-50/30"),children:(0,a.jsx)(eW.Wu,{className:"p-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between gap-3",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-start gap-2 mb-1",children:[(0,a.jsx)("div",{className:(0,d.cn)(sw.iconContainer.sm,"bg-purple-100/80 text-purple-600 border border-purple-200/60 mt-0.5 flex-shrink-0"),children:(0,a.jsx)(sf.A,{className:"h-2.5 w-2.5"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"font-medium text-neutral-900 text-sm leading-tight truncate",children:e.lyrNm}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-0.5",children:[(0,a.jsx)(D.E,{variant:"outline",className:(0,d.cn)("text-xs border",s8(e.svcTySeCodeNm?.toLowerCase()||"default")),children:e.svcTySeCodeNm}),e.lyrClCodeNm&&(0,a.jsx)(D.E,{variant:"outline",className:"text-xs bg-neutral-100/80 text-neutral-600 border-neutral-200/60",children:e.lyrClCodeNm})]})]})]}),(0,a.jsxs)("div",{className:"ml-7 space-y-0.5",children:[e.lyrDc&&(0,a.jsx)("p",{className:"text-xs text-neutral-600 line-clamp-1",children:e.lyrDc}),(0,a.jsxs)("div",{className:"flex items-center gap-3 text-xs text-neutral-500",children:[e.ownerNm&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(s3.A,{className:"h-2.5 w-2.5"}),(0,a.jsx)("span",{children:e.ownerNm})]}),e.registDt&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(s5.A,{className:"h-2.5 w-2.5"}),(0,a.jsx)("span",{children:new Date(e.registDt).toLocaleDateString()})]})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,a.jsxs)(u.$,{size:"sm",variant:"default",className:(0,d.cn)(sw.button.primary,"h-6 px-2 text-xs"),onClick:s=>{s.stopPropagation(),n(e)},children:[(0,a.jsx)(h.A,{className:"h-2.5 w-2.5 mr-1"}),"추가"]}),(0,a.jsx)(u.$,{size:"sm",variant:"ghost",className:(0,d.cn)(sw.button.ghost,"h-6 w-6 p-0 hover:bg-purple-100/80"),onClick:s=>{s.stopPropagation(),i(e)},children:(0,a.jsx)(s6.A,{className:"h-2.5 w-2.5"})})]})]})})})},e.lyrId))}),o.length>5&&(0,a.jsx)("div",{children:(0,a.jsx)(u.$,{variant:"ghost",size:"sm",onClick:()=>x(!m),className:(0,d.cn)(sw.button.secondary,"w-full h-8 text-xs border-dashed border-neutral-300 hover:border-solid hover:border-purple-300 hover:bg-purple-50/60 hover:text-purple-700"),children:m?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(G.A,{className:"h-3 w-3 mr-1"}),"접기"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E.A,{className:"h-3 w-3 mr-1"}),o.length-5,"개 더보기"]})})}),c>o.length&&(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"text-center p-2 text-xs text-neutral-500 bg-neutral-50/60 rounded-lg border border-neutral-200/50",children:["총 ",c,"개 중 ",o.length,"개 표시"]})})]})})}let s9=e=>{let s={wms:"bg-blue-100/80 text-blue-700 border-blue-200/60",wfs:"bg-emerald-100/80 text-emerald-700 border-emerald-200/60",wmts:"bg-purple-100/80 text-purple-700 border-purple-200/60",xyz:"bg-orange-100/80 text-orange-700 border-orange-200/60",default:"bg-neutral-100/80 text-neutral-700 border-neutral-200/60"};return s[e]||s.default};function te({content:e,className:s,mapState:t}){let r;try{r="string"==typeof e?JSON.parse(e):e}catch(e){return null}if(!r||!r.id)return(0,a.jsx)("div",{className:(0,d.cn)(sw.card.error,"p-3",s),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:(0,d.cn)(sw.iconContainer.sm,"bg-red-100/80 text-red-600 border border-red-200/60"),children:(0,a.jsx)(s6.A,{className:"h-3 w-3"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-red-900 text-sm",children:"레이어 정보를 가져올 수 없습니다"}),(0,a.jsx)("p",{className:"text-xs text-red-700",children:"레이어가 존재하지 않거나 접근할 수 없습니다"})]})]})});let l=s$("getLayerInfo");return(0,a.jsx)(sA,{icon:l.icon,title:l.label,state:"result",className:s,titleExtra:(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[r.service&&(0,a.jsx)(D.E,{variant:"outline",className:(0,d.cn)("text-xs py-0 border",s9(r.service)),children:r.service.toUpperCase()}),(0,a.jsx)(D.E,{variant:"outline",className:(0,d.cn)("text-xs border py-0",r.visible?"bg-emerald-100/80 text-emerald-700 border-emerald-200/60":"bg-neutral-100/80 text-neutral-700 border-neutral-200/60"),children:r.visible?"표시":"숨김"})]}),children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"text-xs text-neutral-600",children:["ID: ",r.info?.lyrId||r.id]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-xs",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(sf.A,{className:"h-3 w-3 text-neutral-500"}),(0,a.jsx)("span",{className:"font-medium text-neutral-700",children:"타입"})]}),(0,a.jsx)(D.E,{variant:"outline",className:"text-xs h-5",children:r.type||"Unknown"})]}),r.service&&(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(s5.A,{className:"h-3 w-3 text-neutral-500"}),(0,a.jsx)("span",{className:"font-medium text-neutral-700",children:"서비스"})]}),(0,a.jsx)(D.E,{variant:"outline",className:(0,d.cn)("text-xs h-5",s9(r.service)),children:r.service.toUpperCase()})]}),void 0!==r.opacity&&(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(eQ.A,{className:"h-3 w-3 text-neutral-500"}),(0,a.jsx)("span",{className:"font-medium text-neutral-700",children:"투명도"})]}),(0,a.jsxs)("p",{className:"text-neutral-600",children:[Math.round(100*r.opacity),"%"]})]}),void 0!==r.zIndex&&(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(sg.A,{className:"h-3 w-3 text-neutral-500"}),(0,a.jsx)("span",{className:"font-medium text-neutral-700",children:"Z-Index"})]}),(0,a.jsx)("p",{className:"text-neutral-600",children:r.zIndex})]}),r.projection&&(0,a.jsxs)("div",{className:"space-y-1 col-span-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(s5.A,{className:"h-3 w-3 text-neutral-500"}),(0,a.jsx)("span",{className:"font-medium text-neutral-700",children:"좌표계"})]}),(0,a.jsx)("p",{className:"text-neutral-600 font-mono text-xs",children:r.projection})]})]}),r.info?.metadata&&(0,a.jsxs)("div",{className:"pt-2 border-t border-neutral-200",children:[(0,a.jsx)("h5",{className:"text-xs font-medium text-neutral-700 mb-2",children:"메타데이터"}),(0,a.jsxs)("div",{className:"space-y-1 text-xs",children:[r.info.metadata.cntntsId&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-neutral-500",children:"콘텐츠 ID"}),(0,a.jsx)("span",{className:"text-neutral-700 font-mono",children:r.info.metadata.cntntsId})]}),r.info.metadata.lyrClCode&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-neutral-500",children:"레이어 분류"}),(0,a.jsx)("span",{className:"text-neutral-700",children:r.info.metadata.lyrClCode})]}),r.info.metadata.lyrTySeCode&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-neutral-500",children:"레이어 유형"}),(0,a.jsx)("span",{className:"text-neutral-700",children:r.info.metadata.lyrTySeCode})]})]})]}),r.info?.description&&(0,a.jsxs)("div",{className:"pt-2 border-t border-neutral-200",children:[(0,a.jsx)("h5",{className:"text-xs font-medium text-neutral-700 mb-1",children:"설명"}),(0,a.jsx)("p",{className:"text-xs text-neutral-600",children:r.info.description})]})]})})}var ts=t(20131);function tt({content:e,className:s}){let t;try{console.log("DensityAnalysisResult content:",e),t="string"==typeof e?JSON.parse(e):e}catch(e){return null}let r=t.features?.length||0;if(t.error||"error"===t.status)return(0,a.jsx)("div",{className:(0,d.cn)("p-4 rounded-lg border border-red-200 bg-red-50",s),children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-red-100 text-red-600",children:(0,a.jsx)(sb.A,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-red-900",children:"밀도 분석을 완료할 수 없습니다"}),(0,a.jsx)("p",{className:"text-sm text-red-700",children:t.error||"레이어 데이터에 문제가 있거나 분석 조건이 올바르지 않습니다"})]})]})});let l=s$("performDensityAnalysis");return(0,a.jsx)(sA,{icon:l.icon,title:l.label,state:"result",className:s,titleExtra:(0,a.jsxs)("span",{className:"text-xs text-neutral-600",children:[r.toLocaleString(),"개 데이터"]}),children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"text-xs text-neutral-600",children:["총 ",(0,a.jsxs)("span",{className:"font-medium",children:[r.toLocaleString(),"개"]})," 데이터 분석이 완료되었습니다."]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)(u.$,{variant:"outline",size:"sm",onClick:()=>{if(!r)return void et.oR.error("다운로드할 분석 결과가 없습니다");try{let e=JSON.stringify(t,null,2),s="data:application/json;charset=utf-8,"+encodeURIComponent(e),a=`density_analysis_${new Date().toISOString().split("T")[0]}.json`,r=document.createElement("a");r.setAttribute("href",s),r.setAttribute("download",a),r.click(),et.oR.success("분석 결과를 다운로드했습니다")}catch(e){et.oR.error("다운로드 중 오류가 발생했습니다")}},className:"h-6 px-3 text-xs flex items-center gap-1",children:[(0,a.jsx)(ts.A,{className:"h-3 w-3"}),"결과 다운로드"]})})]})})}var ta=t(81441);function tr({content:e,className:s}){let t;try{t="string"==typeof e?JSON.parse(e):e}catch(e){t={lyr_id:"",filter:"",description:"Invalid content format"}}let{updateFilter:r}=ef();var l=t.lyr_id;let{getLayerById:n}=ef();n(l);let i=async()=>{try{r(t.lyr_id,t.filter),et.oR.success("필터가 적용되었습니다")}catch(e){console.error("Filter update error:",e),et.oR.error("필터 적용 중 오류가 발생했습니다")}},o=async()=>{try{r(t.lyr_id,""),et.oR.success("필터가 초기화되었습니다")}catch(e){console.error("Filter reset error:",e),et.oR.error("필터 초기화 중 오류가 발생했습니다")}};if(!t.lyr_id||!t.filter)return(0,a.jsx)("div",{className:(0,d.cn)(sw.card.error,"p-3",s),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:(0,d.cn)(sw.iconContainer.sm,"bg-red-100/80 text-red-600 border border-red-200/60"),children:(0,a.jsx)(ee.A,{className:"h-3 w-3"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-red-900 text-sm",children:"필터 적용에 실패했습니다"}),(0,a.jsx)("p",{className:"text-xs text-red-700",children:"필터 조건을 확인하고 다시 시도해주세요"})]})]})});let c=s$("createLayerFilter");return(0,a.jsx)(sA,{icon:c.icon,title:c.label,state:"result",className:s,titleExtra:(0,a.jsx)(D.E,{variant:"outline",className:"text-xs py-0 border bg-emerald-100/80 text-emerald-700 border-emerald-200/60",children:"필터 적용"}),children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"text-xs text-neutral-600",children:["레이어 ID: ",t.lyr_id]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)(ee.A,{className:"h-3 w-3 text-neutral-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-neutral-700",children:"필터 조건"})]}),(0,a.jsx)("div",{className:"p-2 bg-white/60 rounded border border-neutral-200/40",children:(0,a.jsx)("code",{className:"text-xs text-neutral-800 font-mono break-all",children:t.filter})})]}),t.description&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)(sz.A,{className:"h-3 w-3 text-neutral-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-neutral-700",children:"설명"})]}),(0,a.jsx)("p",{className:"text-sm text-neutral-700",children:t.description})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 pt-2",children:[(0,a.jsxs)(u.$,{variant:"outline",size:"sm",onClick:o,className:"flex items-center gap-1 h-6 px-2 text-xs",children:[(0,a.jsx)(ta.A,{className:"h-3 w-3"}),"초기화"]}),(0,a.jsxs)(u.$,{variant:"outline",size:"sm",onClick:i,className:"flex items-center gap-1 h-6 px-2 text-xs",children:[(0,a.jsx)(eE.A,{className:"h-3 w-3"}),"적용"]})]})]})})}let tl=e=>{let s={color:"bg-blue-100/80 text-blue-700 border-blue-200/60",fillOpacity:"bg-emerald-100/80 text-emerald-700 border-emerald-200/60",strokeColor:"bg-purple-100/80 text-purple-700 border-purple-200/60",strokeWidth:"bg-orange-100/80 text-orange-700 border-orange-200/60",radius:"bg-pink-100/80 text-pink-700 border-pink-200/60",symbol:"bg-yellow-100/80 text-yellow-700 border-yellow-200/60",default:"bg-neutral-100/80 text-neutral-700 border-neutral-200/60"};return s[e]||s.default};function tn({content:e,className:s}){let t;try{t="string"==typeof e?JSON.parse(e):e}catch(e){t={layerId:"",styleUpdate:{},description:"Invalid content format",success:!1}}if(!t.success)return(0,a.jsx)("div",{className:(0,d.cn)(sw.card.error,"p-3",s),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:(0,d.cn)(sw.iconContainer.sm,"bg-red-100/80 text-red-600 border border-red-200/60"),children:(0,a.jsx)(eQ.A,{className:"h-3 w-3"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-red-900 text-sm",children:"스타일 변경 실패"}),(0,a.jsx)("p",{className:"text-xs text-red-700",children:t.description||"스타일 변경 중 오류가 발생했습니다."})]})]})});let r=Object.entries(t.styleUpdate),l=r.length>0?r[0][0]:"default",n=s$("updateLayerStyle");return(0,a.jsx)(sA,{icon:n.icon,title:n.label,state:"result",className:s,titleExtra:r.length>0&&(0,a.jsx)(D.E,{variant:"outline",className:(0,d.cn)("text-xs border",tl(l)),children:l}),children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"text-xs text-neutral-600",children:["ID: ",t.layerId]}),(0,a.jsx)("div",{className:"text-xs text-neutral-600",children:t.description}),r.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(sa.A,{className:"h-3 w-3 text-neutral-500"}),(0,a.jsx)("span",{className:"font-medium text-neutral-700 text-xs",children:"변경된 속성"})]}),(0,a.jsx)("div",{className:"text-xs text-neutral-600",children:(e=>{let s=[];return e.color&&s.push(`색상: ${e.color}`),void 0!==e.fillOpacity&&s.push(`투명도: ${(100*e.fillOpacity).toFixed(0)}%`),e.strokeColor&&s.push(`윤곽선: ${e.strokeColor}`),void 0!==e.strokeWidth&&s.push(`윤곽선 두께: ${e.strokeWidth}px`),void 0!==e.radius&&s.push(`크기: ${e.radius}px`),void 0!==e.width&&s.push(`선 두께: ${e.width}px`),e.symbol&&s.push(`심볼: ${{circle:"원형",square:"사각형",triangle:"삼각형",star:"별",cross:"십자",x:"X자"}[e.symbol]||e.symbol}`),s.length>0?s.join(", "):"스타일 속성 변경됨"})(t.styleUpdate)}),(0,a.jsxs)("div",{className:"pt-2 border-t border-neutral-200",children:[(0,a.jsx)("h5",{className:"text-xs font-medium text-neutral-700 mb-2",children:"스타일 속성 상세"}),(0,a.jsx)("div",{className:"space-y-1 text-xs",children:r.map(([e,s])=>(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-neutral-500",children:e}),(0,a.jsx)("span",{className:"text-neutral-700 font-mono",children:String(s)})]},e))})]})]})]})})}function ti({content:e,className:s}){let t;try{t="string"==typeof e?JSON.parse(e):e}catch(e){t={layerId:"",description:"Invalid content format",success:!1}}let r=s$("removeLayer");return t.success?(0,a.jsx)(sA,{icon:r.icon,title:r.label,state:"result",className:s,titleExtra:(0,a.jsx)(D.E,{variant:"outline",className:"text-xs py-0 border bg-orange-100/80 text-orange-700 border-orange-200/60",children:"삭제됨"}),children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-xs text-neutral-600",children:t.description}),(0,a.jsxs)("div",{className:"text-xs text-neutral-500",children:["삭제된 레이어 ID: ",t.layerId]})]})}):(0,a.jsx)(sA,{icon:r.icon,title:r.label,state:"partial-call",className:s,titleExtra:(0,a.jsx)(D.E,{variant:"outline",className:"text-xs py-0 border bg-red-100/80 text-red-700 border-red-200/60",children:"실패"}),children:(0,a.jsx)("div",{className:"text-xs text-red-700",children:t.description||"레이어 삭제 중 오류가 발생했습니다."})})}let to={eMapBasic:{name:"일반지도",icon:A.A,color:"#4C6EF5"},eMapAIR:{name:"항공지도",icon:k.A,color:"#40C057"},eMapColor:{name:"색각지도",icon:S.A,color:"#FA5252"},eMapWhite:{name:"백지도",icon:R.A,color:"#845EF7"}},td=e=>to[e]?.name||e,tc=e=>to[e]?.icon||I.A,tm=e=>to[e]?.color||"#4C6EF5";function tx({content:e,className:s,mapState:t}){let r;try{r="string"==typeof e?JSON.parse(e):e}catch(e){r={error:"Invalid content format"}}console.log("Basemap change result:",r);let n=s$("changeBasemap");if(r.error)return(0,a.jsx)(sA,{icon:n.icon,title:n.label,state:"partial-call",className:s,titleExtra:(0,a.jsx)(D.E,{variant:"outline",className:"text-xs py-0 border bg-red-100/80 text-red-700 border-red-200/60",children:"오류"}),children:(0,a.jsx)("div",{className:"text-xs text-red-700",children:r.error})});let i=r.basemap||r.basemapId||"eMapBasic",o=r.basemapName||td(i),d=tc(i),c=tm(i);return(0,a.jsx)(sA,{icon:n.icon,title:n.label,state:"result",className:s,titleExtra:(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(D.E,{variant:"outline",className:"text-xs py-0 border bg-emerald-100/80 text-emerald-700 border-emerald-200/60",children:o}),(0,a.jsx)("div",{className:"flex h-4 w-4 items-center justify-center rounded-sm",style:{backgroundColor:`${c}20`},children:l().createElement(d,{className:"h-2.5 w-2.5",style:{color:c}})})]}),children:(0,a.jsxs)("div",{className:"text-xs text-neutral-600",children:["배경지도가 ",(0,a.jsx)("span",{className:"font-medium",children:o}),"로 변경되었습니다."]})})}var tu=t(92147),th=t(98379),tp=t(473),tf=t(1771),tb=t(42538),tg=t(97380),tj=t(65363),tN=t(23250),tv=t(7332);let ty=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("table",{ref:t,className:(0,d.cn)("w-full caption-bottom text-sm",e),...s}));ty.displayName="Table";let tw=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("thead",{ref:t,className:(0,d.cn)("[&_tr]:border-b",e),...s}));tw.displayName="TableHeader";let tC=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("tbody",{ref:t,className:(0,d.cn)("[&_tr:last-child]:border-0",e),...s}));tC.displayName="TableBody",r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("tfoot",{ref:t,className:(0,d.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let tA=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("tr",{ref:t,className:(0,d.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));tA.displayName="TableRow";let tk=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("th",{ref:t,className:(0,d.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));tk.displayName="TableHead";let tS=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("td",{ref:t,className:(0,d.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));function tR({columns:e,data:s,searchKey:t,searchPlaceholder:l="검색...",initialPageSize:n=50}){let[i,o]=r.useState([]),[d,c]=r.useState([]),[m,x]=r.useState({}),h=(0,tN.N4)({data:s,columns:e,onSortingChange:o,onColumnFiltersChange:c,getCoreRowModel:(0,tv.HT)(),getPaginationRowModel:(0,tv.kW)(),getSortedRowModel:(0,tv.h5)(),getFilteredRowModel:(0,tv.hM)(),onColumnVisibilityChange:x,initialState:{pagination:{pageSize:n}},state:{sorting:i,columnFilters:d,columnVisibility:m}});return(0,a.jsxs)("div",{className:"flex flex-col h-full overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center py-3 flex-shrink-0 border-b bg-background",children:[t&&(0,a.jsx)(B.p,{placeholder:l,value:h.getColumn(t)?.getFilterValue()??"",onChange:e=>h.getColumn(t)?.setFilterValue(e.target.value),className:"max-w-sm h-8"}),(0,a.jsx)("select",{value:h.getState().pagination.pageSize,onChange:e=>{h.setPageSize(Number(e.target.value))},className:"ml-2 h-8 px-2 border border-input bg-background rounded-md text-sm",children:[10,20,30,50,100].map(e=>(0,a.jsxs)("option",{value:e,children:[e,"개씩 보기"]},e))}),(0,a.jsxs)(sV.rI,{children:[(0,a.jsx)(sV.ty,{asChild:!0,children:(0,a.jsxs)(u.$,{variant:"outline",size:"sm",className:"ml-auto h-8",children:["컬럼 ",(0,a.jsx)(E.A,{className:"ml-2 h-3 w-3"})]})}),(0,a.jsx)(sV.SQ,{align:"end",children:h.getAllColumns().filter(e=>e.getCanHide()).map(e=>(0,a.jsx)(sV.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:s=>e.toggleVisibility(!!s),children:e.id},e.id))})]})]}),(0,a.jsx)("div",{className:"flex-1 min-h-0 border rounded-md overflow-hidden bg-background",children:(0,a.jsxs)(w,{className:"h-full w-full",children:[(0,a.jsx)("div",{style:{minHeight:"400px",maxHeight:"500px",height:"100%"},children:(0,a.jsxs)(ty,{className:"w-full",children:[(0,a.jsx)(tw,{children:h.getHeaderGroups().map(e=>(0,a.jsx)(tA,{className:"hover:bg-transparent border-b",children:e.headers.map(e=>(0,a.jsx)(tk,{className:"sticky top-0 z-10 bg-muted border-r last:border-r-0 min-w-[120px] p-2 h-auto",children:e.isPlaceholder?null:(0,tN.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,a.jsx)(tC,{children:h.getRowModel().rows?.length?h.getRowModel().rows.map(e=>(0,a.jsx)(tA,{className:"hover:bg-muted/30 border-b",children:e.getVisibleCells().map(e=>(0,a.jsx)(tS,{className:"border-r last:border-r-0 min-w-[120px] p-2 text-sm whitespace-nowrap",children:(0,tN.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,a.jsx)(tA,{children:(0,a.jsx)(tS,{colSpan:e.length,className:"h-24 text-center text-muted-foreground",children:"데이터가 없습니다."})})})]})}),(0,a.jsx)(C,{orientation:"vertical"}),(0,a.jsx)(C,{orientation:"horizontal"})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between space-x-2 py-3 flex-shrink-0 border-t bg-background",children:[(0,a.jsxs)("div",{className:"flex-1 text-sm text-muted-foreground",children:["총 ",h.getFilteredRowModel().rows.length,"개 항목 중 ",h.getState().pagination.pageIndex*h.getState().pagination.pageSize+1,"-",Math.min((h.getState().pagination.pageIndex+1)*h.getState().pagination.pageSize,h.getFilteredRowModel().rows.length),"개 표시"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["페이지 ",h.getState().pagination.pageIndex+1," / ",h.getPageCount()]}),(0,a.jsx)(u.$,{variant:"outline",size:"sm",onClick:()=>h.previousPage(),disabled:!h.getCanPreviousPage(),children:"이전"}),(0,a.jsx)(u.$,{variant:"outline",size:"sm",onClick:()=>h.nextPage(),disabled:!h.getCanNextPage(),children:"다음"})]})]})]})}tS.displayName="TableCell",r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("caption",{ref:t,className:(0,d.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption";let tI=e=>{let s=e.toLowerCase();return s.includes("string")||s.includes("text")?(0,a.jsx)(tu.A,{className:"h-3 w-3"}):s.includes("number")||s.includes("int")||s.includes("double")?(0,a.jsx)(th.A,{className:"h-3 w-3"}):s.includes("date")||s.includes("time")?(0,a.jsx)(tp.A,{className:"h-3 w-3"}):s.includes("geometry")||s.includes("point")||s.includes("polygon")?(0,a.jsx)(sg.A,{className:"h-3 w-3"}):(0,a.jsx)(s5.A,{className:"h-3 w-3"})},tE=e=>{let s=e.toLowerCase();return s.includes("string")||s.includes("text")?"bg-blue-100/80 text-blue-700 border-blue-200/60":s.includes("number")||s.includes("int")||s.includes("double")?"bg-green-100/80 text-green-700 border-green-200/60":s.includes("date")||s.includes("time")?"bg-purple-100/80 text-purple-700 border-purple-200/60":s.includes("geometry")||s.includes("point")||s.includes("polygon")?"bg-orange-100/80 text-orange-700 border-orange-200/60":"bg-neutral-100/80 text-neutral-700 border-neutral-200/60"};function tz({isOpen:e,onOpenChange:s,columns:t,data:l,layerInfo:n}){let[i,o]=(0,r.useState)(null),[c,m]=(0,r.useState)(!1),[x,h]=(0,r.useState)(null),[p,f]=(0,r.useState)(null),[b,g]=(0,r.useState)("data"),[j,N]=(0,r.useState)(""),[v,y]=(0,r.useState)(1),[w,C]=(0,r.useState)(50),A=async(e=v,s=w)=>{if(n){m(!0),h(null);try{let{lyrId:t,cntntsId:a,namespace:r,userId:l="geonuser"}=n,i=await fetch("/api/layer/detailed-info",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:l,lyrId:t,namespace:r,cntntsId:a,pageIndex:e,pageSize:s})});if(!i.ok)throw Error(`HTTP error! status: ${i.status}`);let d=await i.json();if(d.error)throw Error(d.error);o(d)}catch(e){console.error("Error fetching detailed info:",e),h(e.message||"상세 정보를 가져오는 중 오류가 발생했습니다.")}finally{m(!1)}}else h("현재는 기본 속성 정보만 표시됩니다. 더 자세한 정보를 보려면 레이어 관리 기능을 이용해주세요.")},k=e=>{y(e),A(e,w)},S=e=>{C(e),y(1),A(1,e)},R=i?Math.ceil(i.totalCount/w):0,I=v<R,E=v>1;return(0,a.jsx)(O.lG,{open:e,onOpenChange:s,children:(0,a.jsxs)(O.Cf,{className:"max-w-7xl w-[95vw] max-h-[90vh] flex flex-col overflow-hidden",children:[(0,a.jsxs)(O.c7,{className:"flex-shrink-0",children:[(0,a.jsxs)(O.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(s5.A,{className:"h-5 w-5"}),"레이어 속성 상세 정보"]}),(0,a.jsx)(O.rr,{children:i?(0,a.jsxs)(a.Fragment,{children:["총 ",i.columns.length,"개의 속성과 ",i.totalCount,"개의 데이터 행이 있습니다. (페이지 ",v,"/",R,")"]}):(0,a.jsxs)(a.Fragment,{children:["기본 정보: ",t.length,"개의 속성과 ",l.length,"개의 데이터 행"]})})]}),(0,a.jsx)("div",{className:"flex-1 flex flex-col min-h-0 mt-4 overflow-hidden",children:(0,a.jsxs)(ev,{value:b,onValueChange:e=>g(e),className:"flex flex-col h-full overflow-hidden",children:[(0,a.jsxs)(ey,{className:"grid w-full grid-cols-2 flex-shrink-0",children:[(0,a.jsxs)(ew,{value:"data",className:"flex items-center gap-2",children:[(0,a.jsx)(tb.A,{className:"h-4 w-4"}),"데이터"]}),(0,a.jsxs)(ew,{value:"attributes",className:"flex items-center gap-2",children:[(0,a.jsx)(K.A,{className:"h-4 w-4"}),"속성 목록"]})]}),(0,a.jsxs)(eC,{value:"data",className:"flex-1 flex flex-col min-h-0 space-y-4 mt-4 overflow-hidden",children:[c&&(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(sF.A,{className:"h-4 w-4 animate-spin"}),(0,a.jsx)("span",{className:"text-sm text-neutral-600",children:"상세 정보를 불러오는 중..."})]})}),x&&(0,a.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-amber-50 border border-amber-200 rounded flex-shrink-0",children:[(0,a.jsx)(s6.A,{className:"h-4 w-4 text-amber-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-amber-900",children:"알림"}),(0,a.jsx)("p",{className:"text-xs text-amber-700",children:x})]})]}),!c&&(0,a.jsxs)("div",{className:"flex-1 min-h-0 overflow-hidden flex flex-col",children:[(0,a.jsx)("div",{className:"flex-1 min-h-0 overflow-hidden",children:(0,a.jsx)(tR,{columns:(i?.columns||t).map(e=>({accessorKey:e.name,header:({column:s})=>(0,a.jsx)(u.$,{variant:"ghost",onClick:()=>s.toggleSorting("asc"===s.getIsSorted()),className:"h-auto p-1 font-medium text-left justify-start w-full",children:(0,a.jsxs)("div",{className:"flex flex-col items-start gap-1 w-full min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 w-full min-w-0",children:[tI(e.type),(0,a.jsx)("span",{className:"text-sm font-medium truncate flex-1 min-w-0",children:e.name}),i&&e.name===i.pkColumnName&&(0,a.jsx)(D.E,{variant:"outline",className:"text-[10px] px-1 py-0 bg-yellow-50 text-yellow-700 border-yellow-200 flex-shrink-0",children:"PK"}),(0,a.jsx)(tf.A,{className:"h-3 w-3 flex-shrink-0 text-muted-foreground"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 w-full min-w-0",children:[(0,a.jsx)(D.E,{variant:"outline",className:(0,d.cn)("text-[10px] px-1 py-0 flex-shrink-0",tE(e.type)),children:e.type}),e.description&&e.description!==e.name&&(0,a.jsx)("span",{className:"text-[11px] text-muted-foreground truncate flex-1 min-w-0",children:e.description})]})]})}),cell:({getValue:e})=>{let s=e(),t=null!=s?String(s):"-";return(0,a.jsx)("div",{className:"max-w-[200px] min-w-0",children:(0,a.jsx)("span",{className:"text-xs block truncate",title:t,children:t})})},minSize:120,maxSize:300})),data:i?.data||l,searchKey:t.length>0?t[0].name:void 0,searchPlaceholder:"데이터 검색..."})}),i&&i.totalCount>0&&(0,a.jsxs)("div",{className:"flex items-center justify-between px-2 py-3 border-t bg-neutral-50/50 flex-shrink-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-neutral-600",children:[(0,a.jsx)("span",{children:"페이지 크기:"}),(0,a.jsxs)(J,{value:w.toString(),onValueChange:e=>S(Number(e)),children:[(0,a.jsx)(V,{className:"w-20 h-8",children:(0,a.jsx)(U,{})}),(0,a.jsxs)(q,{children:[(0,a.jsx)(X,{value:"25",children:"25"}),(0,a.jsx)(X,{value:"50",children:"50"}),(0,a.jsx)(X,{value:"100",children:"100"}),(0,a.jsx)(X,{value:"200",children:"200"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"text-sm text-neutral-600",children:["총 ",i.totalCount,"개 중 ",(v-1)*w+1,"-",Math.min(v*w,i.totalCount),"개 표시"]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(u.$,{variant:"outline",size:"sm",onClick:()=>k(v-1),disabled:!E,className:"h-8 w-8 p-0",children:(0,a.jsx)(tg.A,{className:"h-4 w-4"})}),(0,a.jsxs)("span",{className:"text-sm font-medium px-2",children:[v," / ",R]}),(0,a.jsx)(u.$,{variant:"outline",size:"sm",onClick:()=>k(v+1),disabled:!I,className:"h-8 w-8 p-0",children:(0,a.jsx)(tj.A,{className:"h-4 w-4"})})]})]})]})]}),!i&&!c&&l.length>10&&(0,a.jsx)("div",{className:"text-center text-neutral-500 text-xs py-2 flex-shrink-0",children:"... 및 더 많은 데이터가 있습니다. 상세 정보를 불러오면 더 많은 데이터를 확인할 수 있습니다."})]}),(0,a.jsxs)(eC,{value:"attributes",className:"flex-1 flex flex-col min-h-0 space-y-4 mt-4 overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[(0,a.jsx)(H.A,{className:"h-4 w-4 text-neutral-500"}),(0,a.jsx)(B.p,{placeholder:"속성 이름으로 검색...",value:j,onChange:e=>N(e.target.value),className:"max-w-sm"})]}),(0,a.jsx)("div",{className:"flex-1 min-h-0 overflow-y-auto pr-2",children:(0,a.jsx)("div",{className:"grid gap-3",children:(i?.columns||t).filter(e=>""===j||e.name.toLowerCase().includes(j.toLowerCase())||e.description&&e.description.toLowerCase().includes(j.toLowerCase())).map((e,s)=>(0,a.jsx)(eW.Zp,{className:(0,d.cn)("cursor-pointer transition-all hover:shadow-md flex-shrink-0",p===e.name&&"ring-2 ring-blue-500"),onClick:()=>f(p===e.name?null:e.name),children:(0,a.jsx)(eW.Wu,{className:"p-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3 min-w-0 flex-1",children:[(0,a.jsx)("div",{className:(0,d.cn)("w-3 h-3 rounded-full flex-shrink-0 mt-0.5",tE(e.type).split(" ")[0])}),(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[tI(e.type),(0,a.jsx)("span",{className:"text-sm font-semibold text-neutral-900 truncate flex-1",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[i&&e.name===i.pkColumnName&&(0,a.jsx)(D.E,{variant:"outline",className:"text-xs bg-yellow-100/80 text-yellow-700 border-yellow-200/60",children:"PK"}),e.required&&(0,a.jsx)(D.E,{variant:"outline",className:"text-xs bg-red-100/80 text-red-700 border-red-200/60",children:"필수"}),e.editable&&(0,a.jsx)(D.E,{variant:"outline",className:"text-xs bg-green-100/80 text-green-700 border-green-200/60",children:"편집가능"})]})]}),e.description&&e.description!==e.name&&(0,a.jsx)("p",{className:"text-xs text-neutral-600 mb-2 break-words",children:e.description}),(void 0!==e.minValue||void 0!==e.maxValue)&&(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs bg-blue-100/80 text-blue-700 border-blue-200/60",children:["범위: ",e.minValue??"∞"," ~ ",e.maxValue??"∞"]})})]})]}),(0,a.jsx)(D.E,{variant:"outline",className:(0,d.cn)("text-xs flex-shrink-0",tE(e.type)),children:e.type})]})})},s))})})]})]})})]})})}let t$=e=>({string:"bg-blue-100/80 text-blue-700 border-blue-200/60",text:"bg-blue-100/80 text-blue-700 border-blue-200/60",varchar:"bg-blue-100/80 text-blue-700 border-blue-200/60",integer:"bg-emerald-100/80 text-emerald-700 border-emerald-200/60",int:"bg-emerald-100/80 text-emerald-700 border-emerald-200/60",number:"bg-emerald-100/80 text-emerald-700 border-emerald-200/60",double:"bg-emerald-100/80 text-emerald-700 border-emerald-200/60",float:"bg-emerald-100/80 text-emerald-700 border-emerald-200/60",date:"bg-purple-100/80 text-purple-700 border-purple-200/60",datetime:"bg-purple-100/80 text-purple-700 border-purple-200/60",timestamp:"bg-purple-100/80 text-purple-700 border-purple-200/60",geometry:"bg-orange-100/80 text-orange-700 border-orange-200/60",point:"bg-orange-100/80 text-orange-700 border-orange-200/60",polygon:"bg-orange-100/80 text-orange-700 border-orange-200/60",boolean:"bg-amber-100/80 text-amber-700 border-amber-200/60"})[e.toLowerCase()]||"bg-neutral-100/80 text-neutral-700 border-neutral-200/60",tL=e=>{let s=e.toLowerCase();return s.includes("string")||s.includes("text")||s.includes("varchar")?(0,a.jsx)(tu.A,{className:"h-3 w-3"}):s.includes("number")||s.includes("int")||s.includes("double")||s.includes("float")?(0,a.jsx)(th.A,{className:"h-3 w-3"}):s.includes("date")||s.includes("time")?(0,a.jsx)(tp.A,{className:"h-3 w-3"}):s.includes("geometry")||s.includes("point")||s.includes("polygon")?(0,a.jsx)(sg.A,{className:"h-3 w-3"}):(0,a.jsx)(s5.A,{className:"h-3 w-3"})};function tM({content:e,className:s,invocation:t}){let l,[n,i]=(0,r.useState)(!1);try{l="string"==typeof e?JSON.parse(e):e}catch(e){return(0,a.jsx)(sA,{icon:(0,a.jsx)(s5.A,{className:"h-4 w-4"}),title:"레이어 속성 조회",state:"result",className:s,children:(0,a.jsxs)("div",{className:"text-red-600 text-sm",children:["결과를 파싱하는 중 오류가 발생했습니다: ",e instanceof Error?e.message:"알 수 없는 오류"]})})}if(l.error)return(0,a.jsx)(sA,{icon:(0,a.jsx)(s5.A,{className:"h-4 w-4"}),title:"레이어 속성 조회",state:"result",className:s,children:(0,a.jsx)("div",{className:"text-red-600 text-sm",children:l.error})});let o=(()=>{if(l.layerInfo)return l.layerInfo;if(t?.args){let{userId:e="geonuser",lyrId:s,namespace:a,cntntsId:r}=t.args;if(s&&a&&r)return{lyrId:s,cntntsId:r,namespace:a,userId:e}}return null})();return(0,a.jsxs)(sA,{icon:(0,a.jsx)(s5.A,{className:"h-4 w-4"}),title:"레이어 속성 조회",state:"result",className:s,children:[(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-neutral-700",children:["주요 속성 (",l.columns.length,"개)"]}),(0,a.jsxs)(u.$,{variant:"outline",size:"sm",className:"h-6 text-xs px-2",onClick:()=>i(!0),children:[(0,a.jsx)(eE.A,{className:"h-3 w-3 mr-1"}),"전체 보기"]})]}),(0,a.jsxs)("div",{className:"grid gap-1",children:[l.columns.slice(0,3).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-2 p-1.5 bg-neutral-50/60 rounded border border-neutral-200/40",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1.5 flex-1",children:[tL(e.type),(0,a.jsx)("span",{className:"text-xs font-medium text-neutral-900",children:e.name}),(0,a.jsx)(D.E,{variant:"outline",className:(0,d.cn)("text-xs px-1 py-0",t$(e.type)),children:e.type})]}),e.description&&e.description!==e.name&&(0,a.jsx)("span",{className:"text-xs text-neutral-600 truncate max-w-[100px]",children:e.description})]},s)),l.columns.length>3&&(0,a.jsx)("div",{className:"text-center py-0.5",children:(0,a.jsxs)("span",{className:"text-xs text-neutral-500",children:["... 및 ",l.columns.length-3,"개 속성 더"]})})]})]})}),(0,a.jsx)(tz,{isOpen:n,onOpenChange:i,columns:l.columns,data:l.data,layerInfo:o})]})}let tF=({invocation:e,addToolResult:s,className:t,status:r,mapState:l})=>{if("result"===e.state)switch(e.toolName){case"searchAddress":return(0,a.jsx)(sQ,{content:e.result,className:t,mapState:l,onDirectionsRequest:(e,t)=>{s({toolCallId:`directions-${Date.now()}`,result:JSON.stringify({toolName:"searchDirections",args:{origin:e,destination:t},status:"pending"})})}},e.toolCallId);case"searchOrigin":return(0,a.jsx)(sq,{content:e.result,className:t,mapState:l},e.toolCallId);case"searchDestination":return(0,a.jsx)(sK,{content:e.result,className:t,mapState:l},e.toolCallId);case"searchDirections":return(0,a.jsx)(s4,{content:e.result,className:t,mapState:l,toolCallId:e.toolCallId},e.toolCallId);case"getLayerList":return(0,a.jsx)(s7,{content:e.result,className:t,mapState:l},e.toolCallId);case"getLayer":return(0,a.jsx)(te,{content:e.result,className:t,mapState:l},e.toolCallId);case"getLayerAttributes":return(0,a.jsx)(tM,{content:e.result,invocation:e,className:t,mapState:l},e.toolCallId);case"performDensityAnalysis":return(0,a.jsx)(tt,{content:e.result,className:t},e.toolCallId);case"createLayerFilter":return(0,a.jsx)(tr,{content:e.result,className:t},e.toolCallId);case"updateLayerStyle":return(0,a.jsx)(tn,{content:e.result,className:t},e.toolCallId);case"removeLayer":return(0,a.jsx)(ti,{content:e.result,className:t},e.toolCallId);case"changeBasemap":return(0,a.jsx)(tx,{content:e.result,className:t,mapState:l},e.toolCallId);default:return(0,a.jsx)(sM,{toolName:e.toolName,state:"result",className:t,content:"string"==typeof e.result?e.result:JSON.stringify(e.result??e.args,null,2)},e.toolCallId)}if("call"===e.state||"partial-call"===e.state)return(0,a.jsx)(sW,{invocation:e,addToolResult:s,className:t},e.toolCallId)},tT=(0,r.memo)(function({chatId:e,message:s,vote:t,status:l,setMessages:n,reload:i,isReadonly:o,addToolResult:c,mapState:m}){let[x,h]=(0,r.useState)("view"),[p,f]=(0,r.useState)(null);return(0,a.jsx)($.N,{children:(0,a.jsx)(z.P.div,{className:"w-full mx-auto max-w-3xl px-4 group/message","data-role":s.role,children:(0,a.jsx)("div",{className:(0,d.cn)("flex flex-col gap-4 w-full","user"===s.role?"items-end":"items-start"),children:(0,a.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:["assistant"===s.role&&(0,a.jsx)(sL,{annotations:s.annotations,isLoading:"streaming"===l||"submitted"===l}),s.experimental_attachments&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:s.experimental_attachments.map(e=>(0,a.jsx)(e6.q,{attachment:e},e.url))}),"view"===x&&(0,a.jsxs)("div",{className:"flex flex-row gap-2 items-start",children:["user"===s.role&&!o&&(0,a.jsxs)(es.m_,{children:[(0,a.jsx)(es.k$,{asChild:!0,children:(0,a.jsx)(u.$,{variant:"ghost",className:"px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100",onClick:()=>h("edit"),children:(0,a.jsx)(so.A,{className:"h-4 w-4"})})}),(0,a.jsx)(es.ZI,{children:"메시지 수정"})]}),(0,a.jsxs)("div",{className:(0,d.cn)("flex flex-col gap-4 overflow-hidden",{"bg-primary text-primary-foreground px-1 py-2 rounded-xl ml-auto max-w-[80%] md:max-w-[70%] lg:max-w-[60%] whitespace-pre-wrap break-words":"user"===s.role,"w-full":"user"!==s.role}),children:[s.parts?.map((e,t)=>{switch(e.type){case"text":return(0,a.jsx)(sd.o,{children:e.text},t);case"reasoning":let r=p===`${s.id}-${t}`;return(0,a.jsx)(sp,{reasoning:e.reasoning,className:"my-2",open:r,isReasoning:r},t);case"tool-invocation":return(0,a.jsx)(tF,{invocation:e.toolInvocation,status:l,addToolResult:c,mapState:m},t);default:return null}}),"assistant"===s.role&&function(e){if(!e)return!1;let s=!1,t=!1;return e.forEach(e=>{"object"==typeof e&&null!==e&&!Array.isArray(e)&&("type"in e&&"evaluation_start"===e.type&&(s=!0),"type"in e&&"evaluation_completed"===e.type&&(t=!0))}),s&&!t}(s.annotations)&&(0,a.jsxs)("div",{className:"mt-3 flex items-center gap-2 text-xs text-gray-500",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-gray-400 rounded-full animate-pulse"}),(0,a.jsx)("span",{children:"작업 결과를 평가 중입니다..."})]})]})]}),"edit"===x&&(0,a.jsx)("div",{className:"flex flex-row gap-2 items-start",children:(0,a.jsx)(sc.j,{message:s,setMode:h,setMessages:n,reload:i},s.id)}),!o&&(0,a.jsx)(sm.P,{chatId:e,message:s,vote:t,isLoading:"submitted"===l||"streaming"===l},`action-${s.id}`)]})})})})},(e,s)=>e.status===s.status&&e.message.id===s.message.id&&e.message.content===s.message.content&&!!e2()(e.message.parts,s.message.parts)&&!!e2()(e.message.annotations,s.message.annotations)),tP=()=>{let e=["답변을 준비하고 있습니다...","요청하신 내용을 처리 중입니다...","최적의 답변을 찾고 있습니다...","생각을 정리하고 있습니다..."],[s,t]=l().useState("");return l().useEffect(()=>{t(e[Math.floor(Math.random()*e.length)])},[]),(0,a.jsx)(z.P.div,{className:"w-full max-w-3xl px-4 group/message",initial:{y:5,opacity:0},animate:{y:0,opacity:1},"data-role":"assistant",children:(0,a.jsx)("div",{className:"flex items-center justify-start gap-2.5",children:(0,a.jsx)(sh,{className:"inline-flex items-center justify-center px-2 py-1 transition ease-out",children:(0,a.jsx)("span",{children:s||"처리 중입니다..."})})})})},tO=({setInput:e})=>{let s=[{title:"장소 검색 및 이동",icon:(0,a.jsx)(I.A,{className:"w-4 h-4 text-primary"}),examples:[{label:"위치 이동",command:"웨이버스로 이동해줘"},{label:"길찾기",command:"웨이버스에서 평촌역까지 얼마나 걸려?"},{label:"현재위치에서 길찾기",command:"내 위치에서 구로디지털단지역까지 얼마나걸려?"}]},{title:"지도 제어",icon:(0,a.jsx)(sz.A,{className:"w-4 h-4 text-primary"}),examples:[{label:"지도 확대",command:"지도를 확대해줘"},{label:"지도 축소",command:"지도를 축소해줘"},{label:"위쪽으로 500m 이동",command:"위쪽으로 500m 이동해줘"},{label:"항공지도로 변경",command:"배경지도를 항공지도로 변경해줘"}]},{title:"레이어 제어",icon:(0,a.jsx)(eI.A,{className:"w-4 h-4 text-primary"}),examples:[{label:"레이어 추가",command:"택지개발사업 레이어를 추가해줘"},{label:"단일 스타일 설정",command:"전국에 있는 백년가게를 노란색 별모양으로 보여줄래?"},{label:"유형별 스타일 설정",command:"서울에 있는 약국만 빨간색으로 표시해줘"},{label:"유형별 스타일 설정",command:"서울의 건물을 5층까지 노란색, 10층까지 파란색, 나머지는 빨간색으로 표시해줄래?"}]},{title:"데이터 분석",icon:(0,a.jsx)(ee.A,{className:"w-4 h-4 text-primary"}),examples:[{label:"노후화 건물 분석",command:"서울의 노후화된 건물을 보여줘"},{label:"레이어 밀도 분석",command:"AI 발생농가 지역의 밀집도를 분석해줘"}]}];return(0,a.jsx)(z.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.3},className:"w-full max-w-lg backdrop-blur-sm mx-auto bg-gradient-to-b from-background/10 to-background/80 rounded-xl",children:(0,a.jsx)(eW.Zp,{className:"border-none shadow-none bg-transparent",children:(0,a.jsxs)(eW.Wu,{className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(z.P.div,{className:"flex items-center justify-center gap-4",initial:{scale:.9},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:150},children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(A.A,{size:32,className:"text-emerald-600"}),(0,a.jsx)(z.P.div,{className:"absolute -top-1 -right-1",animate:{rotate:360},transition:{duration:20,repeat:1/0,ease:"linear"},children:(0,a.jsx)(sa.A,{size:16,className:"text-yellow-500"})})]}),(0,a.jsx)("span",{className:"font-bold text-3xl bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent",children:"\xd7"}),(0,a.jsx)(sN.A,{size:32,className:"text-blue-600"})]}),(0,a.jsxs)(z.P.div,{className:"text-center mt-4 space-y-2",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:[(0,a.jsx)("h2",{className:"text-xl font-bold bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 bg-clip-text text-transparent",children:"말로 만드는 지도"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"자연어로 지도를 제어해보세요."})]})]}),(0,a.jsx)(z.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"space-y-4",children:(0,a.jsx)(sx.nD,{type:"single",collapsible:!0,defaultValue:"examples",className:"w-full bg-background/40 rounded-lg border border-border/20",children:(0,a.jsxs)(sx.As,{value:"examples",className:"border-none",children:[(0,a.jsx)(sx.$m,{className:"justify-center gap-2 py-3 hover:no-underline",children:(0,a.jsx)("span",{className:"text-sm font-medium",children:"✨ 기능 둘러보기"})}),(0,a.jsx)(sx.ub,{className:"flex py-4 justify-center pr-5",children:(0,a.jsx)(z.P.div,{className:"space-y-4",variants:{hidden:{opacity:0},show:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"show",children:s.map((s,t)=>(0,a.jsx)(z.P.div,{variants:{hidden:{opacity:0,x:-10},show:{opacity:1,x:0}},className:"space-y-2",children:(0,a.jsxs)(eT,{children:[(0,a.jsx)(eP,{asChild:!0,children:(0,a.jsxs)(u.$,{variant:"ghost",className:"w-full justify-start gap-2 hover:bg-primary/10",children:[s.icon,(0,a.jsx)("span",{children:s.title})]})}),(0,a.jsx)(eO,{className:"w-80 p-4 bg-gradient-to-b from-background/95 to-background/98 backdrop-blur-lg border border-border/40 shadow-lg animate-in fade-in-0 zoom-in-95 duration-200",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"border-b border-border/40 pb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[s.icon,(0,a.jsx)("h4",{className:"font-semibold text-sm bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent",children:s.title})]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground/80",children:"아래 예시 중에서 선택해보세요"})]}),(0,a.jsx)("div",{className:"space-y-2",children:s.examples.map((s,t)=>(0,a.jsxs)(u.$,{variant:"ghost",className:"w-full group justify-between relative overflow-hidden rounded-lg px-0",onClick:()=>e(s.command),children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200"}),(0,a.jsxs)("div",{className:"relative flex flex-col items-start gap-1 p-3 ",children:[(0,a.jsx)("span",{className:"font-medium text-sm text-foreground/90 group-hover:text-primary transition-colors",children:s.label}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground/70 group-hover:text-muted-foreground transition-colors",children:s.command})]}),(0,a.jsx)("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,a.jsx)(tj.A,{className:"w-4 h-4 text-primary"})})]},t))})]})})]})},t))})})]})})}),(0,a.jsx)(z.P.p,{className:"text-xs text-center text-muted-foreground",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:"각 예시를 클릭하면 자동으로 입력됩니다"})]})})},"overview")},tD=(0,r.memo)(function({chatId:e,votes:s,messages:t,setMessages:r,reload:l,isReadonly:n,setInput:i,status:o,error:d,addToolResult:c,mapState:m}){let[x,u]=(0,eA.R)();return(0,a.jsxs)("div",{ref:x,className:"flex flex-col min-w-0 gap-12 flex-1 overflow-y-auto pt-4 styled-scrollbar",children:[0===t.length&&(0,a.jsx)(tO,{setInput:i}),t.map(t=>(0,a.jsx)(tT,{chatId:e,message:t,status:o,vote:s?s.find(e=>e.messageId===t.id):void 0,setMessages:r,reload:l,isReadonly:n,addToolResult:c,mapState:m},t.id)),"submitted"===o&&(0,a.jsx)(tP,{}),(0,a.jsx)("div",{className:"px-4 py-2 text-center text-sm",children:"error"===o&&d&&(0,a.jsxs)("p",{className:"text-red-500",children:["오류가 발생했습니다. 지속적으로 발생되는 경우 새 대화를 시도해주세요.",d.message]})}),(0,a.jsx)("div",{ref:u,className:"shrink-0 min-w-[24px] min-h-[24px]"})]})},(e,s)=>e.status===s.status&&(!e.status||!s.status)&&e.messages.length===s.messages.length&&!!e2()(e.messages,s.messages)&&!!e2()(e.votes,s.votes));var t_=t(37692),tB=t(39642);function tW({selectedModelId:e,onModelSwitchSuggested:s,className:t}){let l="Qwen3-4B"===e,{isHealthy:n,isLoading:i,error:o,lastChecked:c,responseTime:m,refresh:x}=function(e=!0){let[s,t]=(0,r.useState)({isHealthy:!1,isLoading:!0,error:null,lastChecked:null,responseTime:null}),a=(0,r.useCallback)(async()=>{if(!e)return;t(e=>({...e,isLoading:!0,error:null}));let s=Date.now();try{let e=new AbortController,a=setTimeout(()=>e.abort(),5e3),r=await fetch("/api/health",{method:"GET",signal:e.signal,headers:{"Content-Type":"application/json"}});clearTimeout(a);let l=Date.now()-s;if(r.ok){let e=await r.json();t({isHealthy:"healthy"===e.status,isLoading:!1,error:null,lastChecked:new Date,responseTime:e.responseTime||l})}else{let e=await r.json().catch(()=>({}));throw Error(e.error||`HTTP ${r.status}: ${r.statusText}`)}}catch(r){let e=Date.now()-s,a="Unknown error";r instanceof Error&&(a="AbortError"===r.name?"Request timeout":r.message.includes("fetch")?"Network error":r.message),t({isHealthy:!1,isLoading:!1,error:a,lastChecked:new Date,responseTime:e})}},[e]),l=(0,r.useCallback)(()=>{a()},[a]);return{...s,refresh:l}}(l);if(!l)return null;let h=()=>i?"확인 중...":n?"정상":"오프라인";return(0,a.jsxs)("div",{className:(0,d.cn)("flex items-center ml-2 gap-1",t),children:[(0,a.jsx)(es.EA,{content:(()=>{let e=`Qwen3 서버 상태: ${h()}`;return i?e:n&&m?`${e}
응답시간: ${m}ms
마지막 확인: ${c?.toLocaleTimeString()}`:o?`${e}
오류: ${o}
마지막 확인: ${c?.toLocaleTimeString()}

GPT-4.1-nano 모델 사용을 권장합니다.`:e})(),children:(0,a.jsx)(u.$,{variant:"ghost",size:"icon",className:"h-6 w-6",onClick:()=>{x()},disabled:i,children:(0,a.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 rounded-md bg-secondary/20 hover:bg-secondary/30 transition-colors",children:[i?(0,a.jsx)(sF.A,{className:"h-3 w-3 animate-spin"}):n?(0,a.jsx)(sy.A,{className:"h-3 w-3 text-green-500"}):(0,a.jsx)(sT.A,{className:"h-3 w-3 text-red-500"}),(0,a.jsx)("span",{className:(0,d.cn)("text-xs font-medium",i?"text-yellow-600":n?"text-green-600":"text-red-600"),children:h()})]})})}),!n&&!i&&(0,a.jsx)(es.EA,{content:"Qwen3 서버가 오프라인입니다. 클릭하면 GPT-4.1-nano 모델로 변경됩니다.",children:(0,a.jsx)(u.$,{variant:"destructive",size:"sm",className:"h-6 ml-6 px-2 text-xs animate-pulse",onClick:()=>{s?s():(0,r.startTransition)(()=>{(0,tB.q)("gpt-4.1-nano"),et.oR.success("모델이 GPT-4.1-nano로 변경되었습니다."),setTimeout(()=>{window.location.reload()},1e3)})},children:"GPT로 변경"})})]})}function tG({chatId:e,messages:s,isPanelCollapsed:t,togglePanel:l,isPanelMaximized:n,toggleMaximize:i,mapState:o,input:c,setInput:m,handleSubmit:x,stop:h,attachments:p,setAttachments:f,setMessages:b,append:g,isReadonly:j,reload:N,votes:v,status:y,layerConfigs:w,addToolResult:C,error:A,enableThinking:k,setEnableThinking:S,enableSmartNavigation:R,setEnableSmartNavigation:I,modelSupportsReasoning:E,selectedModelId:z}){let[$,L]=(0,r.useState)("chat"),[M,F]=(0,r.useState)(!1),[T,P]=(0,eA.R)();return(0,a.jsxs)("div",{className:(0,d.cn)("flex flex-col h-dvh bg-background","transition-all duration-300 ease-in-out",t?"hidden":"w-auto",n?"border-l border-border":"rounded-l-lg"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between px-4 h-12 border-b border-border bg-secondary/5",children:[(0,a.jsx)("div",{className:"flex items-center gap-3",children:(0,a.jsx)(tW,{selectedModelId:z})}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)(u.$,{variant:"ghost",size:"icon",onClick:i,className:"h-8 w-8",children:n?(0,a.jsx)(ek.A,{className:"h-4 w-4"}):(0,a.jsx)(eS.A,{className:"h-4 w-4"})})})]}),(0,a.jsxs)(ev,{value:$,onValueChange:L,className:"flex-grow flex flex-col min-h-0",children:[(0,a.jsxs)(ey,{className:"flex p-1 gap-1 border-b border-border",children:[(0,a.jsxs)(ew,{value:"chat",className:(0,d.cn)("flex-1 flex items-center justify-center gap-2","text-sm rounded-md transition-all"),children:[(0,a.jsx)(eR.A,{className:"h-4 w-4"}),"AI 대화"]}),(0,a.jsxs)(ew,{value:"toc",className:(0,d.cn)("flex-1 flex items-center justify-center gap-2","text-sm rounded-md transition-all"),children:[(0,a.jsx)(eI.A,{className:"h-4 w-4"}),"레이어"]})]}),(0,a.jsxs)(eC,{value:"chat",className:"flex flex-col h-full overflow-hidden data-[state=inactive]:hidden",children:[(0,a.jsx)(tD,{chatId:e,setInput:m,votes:v,messages:s,setMessages:b,reload:N,isReadonly:j,status:y,error:A,addToolResult:C,mapState:o}),(0,a.jsx)("form",{className:"flex mx-auto px-4 gap-2 w-full md:max-w-3xl",children:!j&&(0,a.jsx)(si,{chatId:e,input:c,setInput:m,handleSubmit:x,status:y,stop:h,attachments:p,setAttachments:f,messages:s,setMessages:b,append:g,enableThinking:k,setEnableThinking:S,enableSmartNavigation:R,setEnableSmartNavigation:I,modelSupportsReasoning:E,selectedModelId:z})}),(0,a.jsx)(t_.$,{className:"py-2"})]}),(0,a.jsx)(eC,{value:"toc",className:"flex flex-col",children:(0,a.jsx)(eK,{map:o?.map,layers:w||[]})})]})]})}var tZ=t(37720);function tJ({position:e,latitude:s,longitude:t,accuracy:l,timestamp:n,onClose:i}){let[o,d]=(0,r.useState)(!1);return(0,a.jsx)(tZ.zD,{position:e,offset:[0,0],autoPan:!0,autoPanAnimation:250,autoPanMargin:20,children:(0,a.jsxs)(eT,{open:o,onOpenChange:d,children:[(0,a.jsx)(eP,{asChild:!0,children:(0,a.jsx)(u.$,{variant:"default",size:"sm",className:"h-8 w-8 p-0 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg ",onClick:()=>d(!0),children:(0,a.jsx)(sg.A,{className:"h-10 w-10 text-white"})})}),(0,a.jsx)(eO,{className:"w-80 p-0 shadow-lg border-0 backdrop-blur-sm",side:"top",align:"center",children:(0,a.jsxs)(eW.Zp,{className:"border-0 shadow-none bg-transparent",children:[(0,a.jsx)(eW.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(eW.ZB,{className:"text-sm font-semibold flex items-center gap-2",children:[(0,a.jsx)("div",{className:"p-1.5 bg-blue-100 rounded-full",children:(0,a.jsx)(sg.A,{className:"h-4 w-4 text-blue-600"})}),"현재 위치"]}),(0,a.jsx)(u.$,{variant:"ghost",size:"sm",onClick:()=>{d(!1),i()},className:"h-6 w-6 p-0 hover:bg-gray-100",children:(0,a.jsx)(e$.A,{className:"h-3 w-3"})})]})}),(0,a.jsxs)(eW.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"GPS 좌표 (WGS84)"}),(0,a.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:()=>{let e=`${s.toFixed(6)}, ${t.toFixed(6)}`;navigator.clipboard.writeText(e),et.oR.success("좌표가 복사되었습니다")},className:"h-6 px-2 text-xs hover:bg-gray-100",children:[(0,a.jsx)(sU.A,{className:"h-3 w-3 mr-1"}),"복사"]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-md p-2 font-mono text-xs",children:[(0,a.jsxs)("div",{children:["위도: ",s.toFixed(6)]}),(0,a.jsxs)("div",{children:["경도: ",t.toFixed(6)]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"투영 좌표"}),(0,a.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:()=>{let s=`${e[0].toFixed(2)}, ${e[1].toFixed(2)}`;navigator.clipboard.writeText(s),et.oR.success("투영 좌표가 복사되었습니다")},className:"h-6 px-2 text-xs hover:bg-gray-100",children:[(0,a.jsx)(sU.A,{className:"h-3 w-3 mr-1"}),"복사"]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-md p-2 font-mono text-xs",children:[(0,a.jsxs)("div",{children:["X: ",e[0].toFixed(2)]}),(0,a.jsxs)("div",{children:["Y: ",e[1].toFixed(2)]})]})]}),l&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"정확도"}),(0,a.jsxs)(D.E,{variant:"outline",className:"text-xs",children:["\xb1",Math.round(l),"m"]})]}),n&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"측정 시간"}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:n?new Date(n).toLocaleString("ko-KR"):null})]}),(0,a.jsx)("div",{className:"pt-2 border-t",children:(0,a.jsxs)(u.$,{variant:"outline",size:"sm",onClick:()=>{et.oR.success("지도 중심 이동 기능은 지도 컨텍스트에서 처리됩니다")},className:"w-full text-xs",children:[(0,a.jsx)(sZ.A,{className:"h-3 w-3 mr-1"}),"이 위치로 지도 중심 이동"]})})]})]})})]})})}function tU({position:e,address:s,onClose:t}){let[l,n]=(0,r.useState)(!1);return(0,a.jsx)(tZ.zD,{position:e,offset:[0,-10],children:(0,a.jsxs)(eT,{open:l,onOpenChange:n,children:[(0,a.jsx)(eP,{asChild:!0,children:(0,a.jsx)(u.$,{variant:"default",size:"sm",className:"h-10 w-10 p-0 rounded-full bg-green-600 hover:bg-green-700 shadow-lg border-2 border-white",onClick:()=>n(!0),children:"출발"})}),(0,a.jsx)(eO,{className:"w-80 p-0 shadow-lg border-0 backdrop-blur-sm",side:"top",align:"center",children:(0,a.jsxs)(eW.Zp,{className:"border-0 shadow-none bg-transparent",children:[(0,a.jsx)(eW.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(eW.ZB,{className:"text-sm font-semibold flex items-center gap-2",children:[(0,a.jsx)("div",{className:"p-1.5 bg-green-100 rounded-full",children:(0,a.jsx)(sY.A,{className:"h-4 w-4 text-green-600"})}),"출발지"]}),(0,a.jsx)(u.$,{variant:"ghost",size:"sm",onClick:()=>{n(!1)},className:"h-6 w-6 p-0 hover:bg-gray-100",children:(0,a.jsx)(e$.A,{className:"h-3 w-3"})})]})}),(0,a.jsxs)(eW.Wu,{className:"space-y-3",children:[s.buildName&&(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"건물명"}),(0,a.jsx)("div",{className:"bg-green-50 rounded-md p-2 text-sm font-medium text-green-800",children:s.buildName})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"도로명주소"}),(0,a.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:()=>{navigator.clipboard.writeText(s.roadAddr),et.oR.success("주소가 복사되었습니다")},className:"h-6 px-2 text-xs hover:bg-gray-100",children:[(0,a.jsx)(sU.A,{className:"h-3 w-3 mr-1"}),"복사"]})]}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-md p-2 text-xs",children:s.roadAddr})]}),s.jibunAddr&&s.jibunAddr!==s.roadAddr&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"지번주소"}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-md p-2 text-xs",children:s.jibunAddr})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"GPS 좌표 (WGS84)"}),(0,a.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:()=>{let e=`${s.buildLo}, ${s.buildLa}`;navigator.clipboard.writeText(e),et.oR.success("좌표가 복사되었습니다")},className:"h-6 px-2 text-xs hover:bg-gray-100",children:[(0,a.jsx)(sU.A,{className:"h-3 w-3 mr-1"}),"복사"]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-md p-2 font-mono text-xs",children:[(0,a.jsxs)("div",{children:["경도: ",s.buildLo]}),(0,a.jsxs)("div",{children:["위도: ",s.buildLa]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"투영 좌표"}),(0,a.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:()=>{let s=`${e[0].toFixed(2)}, ${e[1].toFixed(2)}`;navigator.clipboard.writeText(s),et.oR.success("투영 좌표가 복사되었습니다")},className:"h-6 px-2 text-xs hover:bg-gray-100",children:[(0,a.jsx)(sU.A,{className:"h-3 w-3 mr-1"}),"복사"]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-md p-2 font-mono text-xs",children:[(0,a.jsxs)("div",{children:["X: ",e[0].toFixed(2)]}),(0,a.jsxs)("div",{children:["Y: ",e[1].toFixed(2)]})]})]}),(0,a.jsx)("div",{className:"pt-2 border-t",children:(0,a.jsxs)(D.E,{variant:"outline",className:"w-full justify-center bg-green-50 text-green-700 border-green-200",children:[(0,a.jsx)(sY.A,{className:"h-3 w-3 mr-1"}),"경로 탐색 출발지"]})})]})]})})]})})}function tV({position:e,address:s,onClose:t}){let[l,n]=(0,r.useState)(!1);return(0,a.jsx)(tZ.zD,{position:e,offset:[0,-10],children:(0,a.jsxs)(eT,{open:l,onOpenChange:n,children:[(0,a.jsx)(eP,{asChild:!0,children:(0,a.jsx)(u.$,{variant:"default",size:"sm",className:"h-10 w-10 p-0 rounded-full bg-red-600 hover:bg-red-700 shadow-lg border-2 border-white",onClick:()=>n(!0),children:"도착"})}),(0,a.jsx)(eO,{className:"w-80 p-0 shadow-lg border-0 backdrop-blur-sm",side:"top",align:"center",children:(0,a.jsxs)(eW.Zp,{className:"border-0 shadow-none bg-transparent",children:[(0,a.jsx)(eW.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(eW.ZB,{className:"text-sm font-semibold flex items-center gap-2",children:[(0,a.jsx)("div",{className:"p-1.5 bg-red-100 rounded-full",children:(0,a.jsx)(sX.A,{className:"h-4 w-4 text-red-600"})}),"목적지"]}),(0,a.jsx)(u.$,{variant:"ghost",size:"sm",onClick:()=>{n(!1)},className:"h-6 w-6 p-0 hover:bg-gray-100",children:(0,a.jsx)(e$.A,{className:"h-3 w-3"})})]})}),(0,a.jsxs)(eW.Wu,{className:"space-y-3",children:[s.buildName&&(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"건물명"}),(0,a.jsx)("div",{className:"bg-red-50 rounded-md p-2 text-sm font-medium text-red-800",children:s.buildName})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"도로명주소"}),(0,a.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:()=>{navigator.clipboard.writeText(s.roadAddr),et.oR.success("주소가 복사되었습니다")},className:"h-6 px-2 text-xs hover:bg-gray-100",children:[(0,a.jsx)(sU.A,{className:"h-3 w-3 mr-1"}),"복사"]})]}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-md p-2 text-xs",children:s.roadAddr})]}),s.jibunAddr&&s.jibunAddr!==s.roadAddr&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"지번주소"}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-md p-2 text-xs",children:s.jibunAddr})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"GPS 좌표 (WGS84)"}),(0,a.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:()=>{let e=`${s.buildLo}, ${s.buildLa}`;navigator.clipboard.writeText(e),et.oR.success("좌표가 복사되었습니다")},className:"h-6 px-2 text-xs hover:bg-gray-100",children:[(0,a.jsx)(sU.A,{className:"h-3 w-3 mr-1"}),"복사"]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-md p-2 font-mono text-xs",children:[(0,a.jsxs)("div",{children:["경도: ",s.buildLo]}),(0,a.jsxs)("div",{children:["위도: ",s.buildLa]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"투영 좌표"}),(0,a.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:()=>{let s=`${e[0].toFixed(2)}, ${e[1].toFixed(2)}`;navigator.clipboard.writeText(s),et.oR.success("투영 좌표가 복사되었습니다")},className:"h-6 px-2 text-xs hover:bg-gray-100",children:[(0,a.jsx)(sU.A,{className:"h-3 w-3 mr-1"}),"복사"]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-md p-2 font-mono text-xs",children:[(0,a.jsxs)("div",{children:["X: ",e[0].toFixed(2)]}),(0,a.jsxs)("div",{children:["Y: ",e[1].toFixed(2)]})]})]}),(0,a.jsx)("div",{className:"pt-2 border-t",children:(0,a.jsxs)(D.E,{variant:"outline",className:"w-full justify-center bg-red-50 text-red-700 border-red-200",children:[(0,a.jsx)(sX.A,{className:"h-3 w-3 mr-1"}),"경로 탐색 목적지"]})})]})]})})]})})}t(47257);var tQ=t(41837);function tY({id:e,isReadOnly:s,mapState:t,mapContainerRef:l,messages:n,append:i,reload:o,stop:u,input:h,setInput:p,handleSubmit:f,status:g,error:j,addToolResult:N,setMessages:v,enableThinking:y,setEnableThinking:w,enableSmartNavigation:C,setEnableSmartNavigation:A,modelSupportsReasoning:k,selectedModelId:S,layerConfigsRef:R}){let{mutate:I}=(0,tQ.iX)(),[E,z]=(0,r.useState)(!1),[$,L]=(0,r.useState)(!1),[M,F]=(0,r.useState)(!1);(0,r.useRef)(!0);let{data:T}=(0,tQ.Ay)(`/api/vote?chatId=${e}`,d.GO),[P,O]=(0,r.useState)([]),D=()=>z(!E),_=(0,r.useRef)(null),B=eL();eM();let{currentLocation:W,setCurrentLocation:G,originPoint:Z,setOriginPoint:J,destinationPoint:U,setDestinationPoint:V}=function(){let e=(0,r.useContext)(eu);if(!e)throw Error("useLocation must be used within a ToolInvocationProvider");return e}();return(0,a.jsx)("div",{className:"flex flex-col h-full w-full overflow-hidden",children:(0,a.jsxs)(c,{direction:"horizontal",className:"h-full rounded-lg",children:[(0,a.jsx)(m,{order:1,defaultSize:99,minSize:50,maxSize:100,children:(0,a.jsx)(tZ.KU,{value:t,children:(0,a.jsxs)("div",{className:"relative h-full w-full",children:[(0,a.jsx)(tZ.W6,{containerRef:l,id:"map",className:"absolute inset-0 h-full w-full"}),t.map&&B.length>0&&(0,a.jsx)(tZ.XS,{children:B.map((e,s)=>(0,a.jsx)(tZ.Wd,{...e},e.id||`layer-${s}`))}),(0,a.jsx)(b,{}),t.map&&(0,a.jsx)(ej,{mapState:t}),W&&(0,a.jsx)(tJ,{position:W.projectedCoord,latitude:W.latitude,longitude:W.longitude,accuracy:W.accuracy,timestamp:W.timestamp,onClose:()=>G(null)}),Z&&(0,a.jsx)(tU,{position:Z.projectedCoord,address:Z.address,onClose:()=>J(null)}),U&&(0,a.jsx)(tV,{position:U.projectedCoord,address:U.address,onClose:()=>V(null)})]})})}),(0,a.jsx)(x,{onDoubleClick:D,onDragging:e=>F(e),withHandle:!0}),(0,a.jsx)(m,{ref:_,order:2,defaultSize:1,minSize:1,maxSize:50,collapsible:E,collapsedSize:1,onCollapse:()=>z(!0),onExpand:()=>z(!1),className:(0,d.cn)(!M&&"transition-all duration-200 ease-in-out"),children:(0,a.jsx)(tG,{chatId:e,messages:n,stop:u,reload:o,isPanelCollapsed:E,togglePanel:D,isPanelMaximized:$,toggleMaximize:()=>L(!$),mapState:t,input:h,setInput:p,handleSubmit:f,setMessages:v,setAttachments:O,attachments:P,votes:T,isReadonly:s,append:i,status:g,addToolResult:N,error:j,layerConfigs:B,enableThinking:y,setEnableThinking:w,enableSmartNavigation:C,setEnableSmartNavigation:A,modelSupportsReasoning:k,selectedModelId:S})})]})})}function tq({id:e,initialMessages:s,selectedModelId:t,selectedVisibilityType:l,isReadOnly:i}){let o=(0,r.useRef)(null),d=(0,r.useRef)([]),c=(0,tZ.B$)({containerRef:o,autoInit:!0}),{mutate:m}=(0,tQ.iX)(),x=(0,sl.t5)(t),[u,h]=(0,r.useState)(!1),[p,f]=(0,r.useState)(!0),{messages:b,append:g,reload:j,stop:N,input:v,setInput:y,handleSubmit:w,status:C,error:A,addToolResult:k,setMessages:S}=(0,n.Y_)({initialMessages:s,body:{id:e,modelId:t,layers:d.current,enable_thinking:u,enable_smart_navigation:p},maxSteps:5,sendExtraMessageFields:!0,onFinish:()=>{m("/api/history")}});return(0,a.jsx)(M,{mapState:c,children:(0,a.jsx)(ep,{messages:b,enableSmartNavigation:p,mapState:c,children:(0,a.jsx)(tY,{id:e,isReadOnly:i,mapState:c,mapContainerRef:o,messages:b,append:g,reload:j,stop:N,input:v,setInput:y,handleSubmit:w,status:C,error:A,addToolResult:k,setMessages:S,enableThinking:u,setEnableThinking:h,enableSmartNavigation:p,setEnableSmartNavigation:f,modelSupportsReasoning:x,selectedModelId:t,layerConfigsRef:d})})})}},61671:(e,s,t)=>{Promise.resolve().then(t.bind(t,56145))},66833:(e,s,t)=>{Promise.resolve().then(t.bind(t,37011)),Promise.resolve().then(t.bind(t,7456))},67334:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var a=t(84464),r=t(63185),l=t(72487);let n=r.forwardRef(({className:e,type:s,...t},r)=>(0,a.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));n.displayName="Input"},68995:(e,s,t)=>{"use strict";t.d(s,{Jn:()=>a,SW:()=>n,t5:()=>l});let a=[{id:"Qwen3-14B",label:"Qwen3 14B",apiIdentifier:"Qwen/Qwen2.5-14B",description:"Qwen3 14B 모델입니다.",provider:"geon",capabilities:{reasoning:!1,streaming:!0,tools:!0,vision:!1}},{id:"Qwen3-4B",label:"Qwen3 (추론)",apiIdentifier:"Qwen/Qwen3-4B",description:"Qwen3-4B 추론 모델입니다.",provider:"geon",capabilities:{reasoning:!0,streaming:!0,tools:!0,vision:!1}},{id:"gpt-4.1-nano",label:"GPT 4.1 Nano",apiIdentifier:"gpt-4.1-nano",description:"OpenAI의 GPT 4.1 Nano 모델입니다.",provider:"openai",capabilities:{reasoning:!1,streaming:!0,tools:!0,vision:!1}}];function r(e){return a.find(s=>s.id===e)}function l(e){let s=function(e){let s=r(e);return s?.capabilities}(e);return s?.reasoning??!1}function n(e){let s=r(e);if(s&&!s.capabilities.reasoning)if("gpt-4.1-nano"===s.id)return"GPT 4.1 Nano 에서 지원되지 않습니다.";else return"현재 선택된 모델은 추론 기능을 지원하지 않습니다"}},72516:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,experimental_ppr:()=>d});var a=t(33626),r=t(45346),l=t(82213),n=t(84256),i=t(47841),o=t(75077);let d=!0;async function c({children:e}){let[s,t]=await Promise.all([(0,i.j2)(),(0,r.UL)()]),d=t.get("sidebar:state")?.value!=="true",c=t.get("model-id")?.value,m=o.Jn.find(e=>e.id===c)?.id||o.oQ;return(0,a.jsxs)(n.SidebarProvider,{defaultOpen:d,children:[(0,a.jsx)(l.AppSidebar,{user:s?.user,selectedModelId:m}),(0,a.jsx)(n.SidebarInset,{children:e})]})}},75077:(e,s,t)=>{"use strict";t.d(s,{Bm:()=>i,Jn:()=>a,UT:()=>l,oQ:()=>r,t5:()=>n});let a=[{id:"Qwen3-14B",label:"Qwen3 14B",apiIdentifier:"Qwen/Qwen2.5-14B",description:"Qwen3 14B 모델입니다.",provider:"geon",capabilities:{reasoning:!1,streaming:!0,tools:!0,vision:!1}},{id:"Qwen3-4B",label:"Qwen3 (추론)",apiIdentifier:"Qwen/Qwen3-4B",description:"Qwen3-4B 추론 모델입니다.",provider:"geon",capabilities:{reasoning:!0,streaming:!0,tools:!0,vision:!1}},{id:"gpt-4.1-nano",label:"GPT 4.1 Nano",apiIdentifier:"gpt-4.1-nano",description:"OpenAI의 GPT 4.1 Nano 모델입니다.",provider:"openai",capabilities:{reasoning:!1,streaming:!0,tools:!0,vision:!1}}],r="Qwen3-4B";function l(e){return a.find(s=>s.id===e)}function n(e){let s=function(e){let s=l(e);return s?.capabilities}(e);return s?.reasoning??!1}function i(e){let s=l(e);return s?.provider}},76561:(e,s,t)=>{Promise.resolve().then(t.bind(t,82213)),Promise.resolve().then(t.bind(t,84256))},79903:(e,s,t)=>{"use strict";t.d(s,{w:()=>i});var a=t(84464),r=t(63185),l=t(33353),n=t(72487);let i=r.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...r},i)=>(0,a.jsx)(l.b,{ref:i,decorative:t,orientation:s,className:(0,n.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));i.displayName=l.b.displayName},82213:(e,s,t)=>{"use strict";t.d(s,{AppSidebar:()=>a});let a=(0,t(35306).registerClientReference)(function(){throw Error("Attempted to call AppSidebar() from the server but AppSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\app-sidebar.tsx","AppSidebar")},84256:(e,s,t)=>{"use strict";t.d(s,{SidebarInset:()=>r,SidebarProvider:()=>l});var a=t(35306);(0,a.registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","Sidebar"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarContent"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarFooter"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarGroup"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarGroupAction"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarGroupContent"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarGroupLabel"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarHeader"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarInput");let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarInset");(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarMenu"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarMenuAction"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarMenuBadge"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarMenuButton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarMenuItem"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarMenuSkeleton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarMenuSub"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarMenuSubButton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarMenuSubItem"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubAction() from the server but SidebarMenuSubAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarMenuSubAction");let l=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarProvider");(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarRail"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarSeparator"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","SidebarTrigger"),(0,a.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\ui\\sidebar.tsx","useSidebar")}};