﻿맵픽이란
Mappick은 누구나 손쉽게 공간정보가 포함된 지도를 제작하여 업무에 적용 할 수 있는 SaaS(Software as a Service) GIS 서비스입니다. Mappick을 사용하면 인터넷을 통해 공개된 공공 데이터나, 본인이 가지고 있는 데이터를 이용하여 다양한 테마지도를 만들어 활용할 수 있고 서로 공유할 수 있습니다.

지도제작
데이터를 조회해서 추가: 온/오프 만으로 데이터 추가 및 조회 기본 제공데이터와 기관 데이터를 간편하게 추가
주소가 엑셀로 되어 있다면: 간단하게 엑셀 데이터를 지도에 표시(지오코딩) 지번주소, 도로명주소, 당연히 GIS파일형식(shp) 그대로 업로드
스타일: 적용 색상, 라벨, 필터 등을 사용자가 직접 수정
저장하기: 다음에도 쉽게 찾을 수 있도록 지도화면 이름, 설명을 간단하게 작성한 후 저장하면 끝!

테마지도
관련된 지도를 한꺼번에 순서, 배치가 자유로운 지도 나만의 테마지도 만들기: 스토리, 모바일 등 다양한 테마로 사용자만의 지도 구성
필요한 지도기능을 선택만으로 생성: 각종 지도 관련 위젯도구 제공

공유하기
공유 단위 설정: 지도 전체를 공유할지 레이어(데이터 단위)만 공유할지 선택
공유 대상 선택: 누구에게 공유할지 주소록에서 선택 / 전체(기관전체), 그룹, 특정 개인 옵션
공유는 조심, 또 조심: 공유한 설정이 맞는지 최종 검토하고, 최종 확인 버튼 클릭!

이외에도 맵픽은 다음의 기능을 제공합니다. 실질적로 Mappick은 지도 작성 및 정보 공유를 손쉽게 할 수 있는 SaaS(Software as a Service) GIS 서비스입니다. 이 플랫폼을 이용하면 누구나 간편하게 지도를 만들고 비즈니스나 개인 업무에 활용할 수 있습니다. Mappick은 다음과 같은 핵심 기능을 제공합니다.
지금 사용자님이 주소 데이터 엑셀파일이 있다면 그 파일을 맵핍에 업로드하여 지도에 해당 위치들을 표시할 수 있습니다. 
관리하고 있는 매장이나 공장의 위치가 주소로만 기록되어 있는 데이터에서 어느 매장끼리 가까운지, 또는 멀리 떨어져 있는지가 궁금하시다면 맵픽에 업로드해보세요. 지도 기반의 한 눈에 파악이 가능하도록 변환해 드립니다.
커피샵 창업을 원하시나요? 내 관심 지역 주변에 다른 카페가 어떻게 분포되어 있는지 맵픽에서 확인해보세요. 카페 레이어를 켜시면 어디에 새로운 카페를 창업할 수 있는지 한 눈에 파악됩니다.
이외에도 맵픽을 이용하여 지도상에서 다양한 활동이 가능하오니 로그인하셔서 나의 데이터를 관리하고 분석해보세요.

--------------------------------------
이 플랫폼을 이용하면 누구나 간편하게 지도를 만들고 비즈니스나 개인 업무에 활용할 수 있습니다. Mappick은 다음과 같은 핵심 기능을 제공합니다.

1. 지금 사용자님이 주소 데이터 엑셀파일이 있다면 그 파일을 맵핍에 업로드하여 지도에 해당 위치들을 표시할 수 있습니다. 

2. 관리하고 있는 매장이나 공장의 위치가 주소로만 기록되어 있는 데이터가 있으시면 업로드하여 위치를 확인해보세요
지도에 표시하면 어느 매장끼리 가까운지, 또는 멀리 떨어져 있는지가 알 수 있습니다. 

3. 커피샵 창업을 원하시나요? 내 관심 지역 주변에 다른 카페가 어떻게 분포되어 있는지 맵픽에서 확인해보세요. 카페 레이어를 켜시면 어디에 새로운 카페를 창업할 수 있는지 한 눈에 파악됩니다.

4. 현재 보유하고 있는 주소 혹은 좌표를 가지고 있는 엑셀데이터가 있으시면 맵픽에 업로드해보세요. 지도 기반에서 사용자님의 데이터를 한 눈에 파악이 가능하도록 변환해 드립니다.

이외에도 핫스팟 분석 등의 공간분석 기능 이용하여 지도상에서 다양한 활동이 가능하오니 로그인하셔서 나의 데이터를 관리하고 분석해보세요.

더 필요한 사항이 있으시면 알려주세요.

----------------------------------------
메인메뉴
메뉴는 챗봇의 메뉴와 맵픽의 메뉴로 구성되어 있습니다. 챗봇이 메뉴는 사용자의 이해를 돕기 위하여 자주 질문하는 내용으로 구성되어 있습니다. 맵픽의 메뉴는 맵픽소개, 지도, 맵갤러리, 공지사항으로 구성되어 있습니다.

1. 지도 빌더
지도빌더에서 제공되는 메인 메뉴 기능으로 새지도생성/지도검색/저장/기본정보 메뉴로 구성되어 있다.

1.1.1. 새 지도 생성
 사용자가 새로운 지도를 생성할 수 있다. 

1.1.2. 지도 검색
 사용자가 저장한 지도 목록을 볼 수 있다.

1.1.3. 지도 저장
 현재 지도를 저장한다.

1.1.4. 다른 이름으로 저장
 현재 지도를 다른이름으로 저장한다.

1.1.5. 지도 TOC
 사용자가 TOC에 있는 레이어 목록을 관리하고 조작할 수 있는 기능을 제공한다.

1.2. 지도 기본기능
지도에서 제공되는 지도 기본기능으로 주소검색, 지도제어 도구 영역으로 구성되어 있다.

1.2.2. 주소검색
 주소검색은 통합검색, 도로명, 지번, PNU, 경위도 등으로 원하는 위치를 검색하고 이동하는 기능을 제공한다.

1.2.3. 지도제어 도구
 지도에서 사용 가능한 다양한 기능을 제공한다.

1.3. 레이어 기능
지도에서 제공되는 레이어 기능으로 업로드, TOC, 스타일설정 등으로 구성되어 있다.
1.3.2. SHP 파일 업로드
 사용자가 보유하고 있는 공간정보 파일을 지도에 업로드하여 지도에서 활용 할 수 있다.

1.3.3. 지오코딩 파일 업로드
 사용자가 보유하고 있는 Excel 파일 및 CSV 파일을 지도에 업로드하여 지도에서 활용 할 수 있다.

1.3.4. 지오코딩 결과 알림
 알림영역에서 지오코딩 작업 내역을 확인할 수 있다. 

1.3.5. 지오코딩 결과 관리
 지오코딩이 완료된 파일을 매핑시켜 레이어로 발행할 수 있다.

1.3.6. 웹 레이어 등록
 OGC 표준의 WMS/WFS/WMTS 레이어 주소를 지도에 등록하여 지도에서 활용 할 수 있다. 

1.3.7. DXF 파일 업로드
 사용자가 보유하고 있는 DXF 파일을 지도에 업로드하여 지도에서 활용 할 수 있다.

1.3.8. 레이어 검색 및 레이어 추가
 사용자가 국가공간정보 레이어, 사용자데이터 레이어, 공통데이터 레이어, 행정데이터 레이 어 등에서 레이어를 검색하여 현재 지도 내의 TOC에 추가할 수 있다. 

1.3.9. 레이어 그룹 관리
 레이어 그룹을 추가하여 레이어를 그룹별로 분류하여 레이어를 관리한다.

1.3.10. 레이어 정보
 레이어의 정보를 확인할 수 있다.
 
1.3.11. 스타일 설정
 사용자가 선택한 레이어에 스타일을 적용하여 표출한다.

1.3.12. 팝업 구성
 사용자가 선택한 레이어에 팝업 구성을 설정한다.
 
1.3.13. 속성 설정
 사용자가 선택한 레이어의 속성정보를 설정한다.
 
1.3.14. 속성 테이블
 사용자가 레이어의 속성을 조회 및 편집, 활용한다.

1.3.15. 차트 설정
 사용자가 레이어 속성정보로 차트를 생성한다.
 
1.3.16. 속성 필터
 사용자가 선택한 레이어에 필터를 속성테이블에 적용하여 표출한다.
 
1.3.17. 공간 검색
 사용자가 레이어 속성을 선택한 공간영역으로 속성정보를 필터링 한다.

1.3.18. 객체 편집모드
 사용자가 선택한 레이어의 객체들을 편집할 수 있는 기능입니다.

1.4. 데이터 요약 분석
1.4.1. 공간조인 분석
 공간 및 속성 관계에 따라 속성 레이어의 속성 정보를 결합하는 공간조인 분석을 수행한다.
 
속성 조인 
- 분석 대상 레이어와 비교 대상 레이어의 속성을 기준으로 ROW 병합.
- 분섞 대상 레이어와 속성명이 일치하는 속성의 경우, ‘[속성명]_1’과 같은 형태로 생성됨.
- 분석 대상 레이어의 키 필드 : 분석 대상 레이어의 키 필드 중 병합의 기준이 될 필드
- 비교 대상 레이어의 키 필드 : 비교 대상 레이어의 키 필드 중 병합의 병합의 기준이 될 필드

공간 조인 
- 공간 관계 유형
① [동일함] 옵션은 분석 대상 레이어의 공간정보와 비교 대상 레이어의 공간정보가 정확히 일치해야 조인 조건이 만족된다.
② [교차함] 옵션은 분석 대상 레이어의 공간정보와 비교 대상 레이어의 공간 정보가 교차하면 조인 조건이 만족된다.
③ [완전히 포함함] 옵션은 비교 대상 레이어의 공간정보가 분석 대상 레이어의 공간정보에 완전히 포함되 있어야 조건이 만족된다.
④ [완전히 포함됨] 옵션은 분석 대상 레이어의 공간정보가 비교 대상 레이어의 공간정보에 완전히 포함되 있어야 조건이 만족된다.
⑤ [일정한 거리 내에 있음] 옵션은 비교 대상 레이어의 공간정보가 분석 대상 레이어의 공간정보.
- 공간 조인 유형
① [조인되는 대상만 포함] 옵션 선택 시 선택한 조인 칼럼 값이 일치하는 경우에만 결과 레이어에 나타난다.
② [모든 대상 피처 유지] 옵션 선택 시 선택한 조인 칼럼 값이 일치하지 않는 경우에도 결과 레이어에 나타난다. 

조인 작업 
① 일대일 조인 : 조인 조건을 만족시키는 ROW가 여러 개일 경우, 하나의 ROW만 조인.
② 일대다 조인 : 조인 조건을 만족시키는 ROW가 여러 개일 경우, 여러 개의 ROW를 조인. 합계/최소/최대/평균/표준편차

 
1.4.2. 주변집계 분석
 비교 대상 레이어에서 지정된 거리 내에 있는 분석 대상 레이어를 레이어 유형(점/선/면)에 따라 집계(총건수/총길이/총면적) 분석한다.

최근접 피처 요약 - 측정 방법 : 운전거리/직선거리
- 탐색 반경 : 기준 거리
- 탐색 반경 단위 : 기준거리 단위
- 경계 영역 반환 : 체크시, 결과 레이어에 비교 대상 레이어의 도형으로부터 기준되는 탐색 반경만큼 떨어진 영역 정보를 공간정보로 사용 

합계 필드 
- 분석 결과로 나타나는 요약 정보를 나타낼 필드명
- 입력하지 않으면 요약 정보가 결과 레이어에 나타나지 않음. 비교 대상 레이어의 도형과 일정 거리 내에 있는 도형의 개수/총 길이/ 총 면적 통계 추가 
- 분석대상 레이어의 숫자 필드로 통계를 내린다.
- 결과 레이어에 [통계구분(sum/min/max//avg/std)]_[분석대상 레이어의 필드명]과 같은 이름의 필드로 추가된다. 합계/최소/최대/평균/표준편차
 
1.4.3. 영역 내 집계 분석
 비교 대상 레이어의 폴리곤 경계 내에 있는 분석 대상 레이어를 레이어 유형(점/선/면)에 따라 집계(총건수/총길이/총면적) 분석한다.

합계 필드 
- 분석 결과로 나타나는 요약 정보를 나타낼 필드명
- 입력하지 않으면 요약 정보가 결과 레이어에 나타나지 않음. 비교 대상 레이어의 도형과 일정 거리 내에 있는 도형의 개수/총 길이/ 총 면적

통계 추가 
- 분석대상 레이어의 숫자 필드로 통계를 내린다.
- 결과 레이어에 [통계구분(sum/min/max//avg/std)]_[분석대상 레이어의 필드명]과 같은 이름의 필드로 추가된다. 합계/최소/최대/평균/표준편차

1.4.4. 공간분포 패턴 분석
 레이어의 공간 분포 패턴을 분석하여 요약 유형별로 중심을 추출한다.

요약 유형 - 중심 피처 : 전체 피처들 중 위치적으로 가운데 잇는 피처 추출(평균 중심에서 가장 가까운 피처 추출)
※ 분석 대상 레이어가 라인일 경우, 결과값이 표출되지 않는다.
- 평균 중심 : extent의 중심(x좌표의 평균, y좌표의 평균) [평균 계산 필드]
: 분석 대상 레이어에서 숫자필드. 선택시 해당 필드의 평균을 계산 하여 결과 레이어에 선택한 속성명으로 표출
- 중앙값 중심 : 각 포인트 간의 거리를 측정하여 중심위치를 추출 [중앙값 계산 필드]
: 분석 대상 레이어의 숫자 필드. 선택한 필드의 중앙값을 계산하여 결과 레이어에 선택한 속성명으로 표출
- 타원 : 포인트 간의 표준편차 값을 추출하여 타원을 추출(타원의 중심 점이 표준편차 평균값)
[타원체의 크기(1SD/2SD/3SD)]
: 결과 레이어에 표현할 타원의 크기 

가중치 기준 - 상대적 중요성에 따라 위치에 가중치를 적용하는데 사용하는 필드
- 가중치 기준을 선택하지 않으면 모든 도형의 속성에 동일한 가중치가 부여되어 중심점 추출
- 가중치 기준을 선택하면 선택한 필드의 값에 따라 가중치가 부여되어 중심점 추출(값이 클수록 가중치가 높음) 
 
1.5. 위치찾기 분석
1.5.1. 공간 조건 검색 분석
 비교 레이어를 기준으로 공간검색 유형의 조건을 만족하는 분석 레이어의 객체를 검색한다.

1.5.2. 공간 조건 추출 분석
 비교 레이어를 기준으로 공간검색 유형의 조건을 만족하는 분석 레이어의 객체를 검색 후 교차하는 영역으로 자르고 비교 레이어의 속성을 추가한다.

1.5.3. 중심찾기 분석
 분석 대상 레이어 객체에서 결과 위치 설정에 따라 중심점을 찾는다.

1.6. 공간패턴 분석
1.6.1. 밀도계산
 위치에 있는 포인트를 기반으로 군집 포인트의 개수가 많은 곳을 쉽게 식별할 수 있도록 시각화하는 분석을 수행한다.

1.6.2. 핫스팟
 데이터의 공간 패턴에 통계적으로 유의미한 군집이 있는지를 격자 그리드로 시각화하는 분석을 수행한다.

1.7. 근접도 분석
1.7.1. 버퍼 분석
 분석 대상 레이어 주위에 입력한 거리까지의 영역을 생성하는 작업을 수행한다.

1.8. 데이터 관리 분석
1.8.1. 경계디졸브
 경계 또는 중첩되는 영역을 병합하여 단일 영역으로 생성하는 작업을 수행한다.

1.8.2. 공간 분할 생성
 분석 대상 레이어를 입력한 분할타입으로 영역을 분할하여 생성하는 작업을 수행한다.
 
1.8.3. 레이어 병합
 동일한 유형의 레이어를 하나의 새로운 레이어로 병합하여 생성하는 작업을 수행한다.
 
1.8.4. 레이어 중첩(지우기) 분석
 동일한 유형의 두 레이어의 중첩되는 영역을 제외한 새 레이어로 속성을 포함하여 병합하는 작업을 수행한다.

1.8.5. 레이어 중첩(교차)
 동일한 유형의 두 레이어의 교차되는 영역을 새 레이어로 속성을 포함하여 병합하는 작업 을 수행한다.

1.8.6. 레이어 중첩(유니온)
 동일한 유형의 두 레이어를 새 레이어로 속성을 포함하여 교차되는 영역은 자르기 후 병합 하는 작업을 수행한다.

1.8.7. 클러스터링
 포인트 레이어의 위치 근접도를 분석하여 그룹으로 묶어 포인트수를 시각화하는 작업을 수 행한다.

1.8.8. 면적 계산
 대상 레이어 내 피처들의 실제 면적과 실제 둘레 길이를 측정하는 작업을 수행한다.

1.8.9. 길이 계산
 대상 레이어 내 피처들의 실제 길이를 측정하는 작업을 수행한다.

1.9. 좌표변환
1.9.1. 파일 좌표 변환
 Shape(ZIP), GeoJSON 파일 형태의 파일을 업로드하여 원하는 좌표계로 변환 후 다운로 드하는 기능을 수행한다.

1.9.2. 단일 좌표 변환
 사용자가 입력한 좌표계의 좌표를 원하는 좌표계로 변환하는 작업을 수행한다.

2. 앱빌더
2.1. 구성
앱(새앱, 검색), 상세설정, 미리보기, 저장 등의 메뉴로 구성되어 있다.

2.2. 표준/편집 테마
지도 빌더를 통해 제작된 지도를 표준, 편집 테마를 활용하여 앱의 레이아웃 및 스타일을 설정 하고 위젯들을 선택하여 새로운 앱을 생성할 수 있다.

2.2.1. 앱 만들기 화면
 테마 목록을 조회하고 테마를 선택하여 앱 편집 화면으로 이동한다.

2.2.2. 메인 설정
 메인 패널에서 사용할 사용자 지도를 선택한다. 

2.2.3. 레이아웃 설정
 타이틀 및 제어도구 위치를 설정한다.

2.2.4. 테마 설정
 헤더 및 푸터의 색상 및 타이틀 등 테마를 설정한다.

2.2.5. 위젯 설정
 웹앱의 위젯을 추가하고 저장한다.
 
헤더 영역 위젯 
앱 기본정보 
웹앱에 추가된 지도에 대한 상세정보를 확인할 수 있는 위젯
레이어 영역 위젯
 레이어 검색 레이어를 검색하고 추가할 수 있는 위젯
 레이어 스타일 레이어의 색상 및 라벨 등을 설정할 수 있는 위젯
 속성 레이어의 속성정보를 확인 할 수 있는 위젯
 속성필터 레이어의 속성으로 필터를 적용할 수 있는 위젯

상단 영역 위젯
 북마크 현재 지도의 위치를 저장할 수 있는 위젯
 오버뷰 오버뷰를 표출하는 위젯
 주소 검색 주소를 검색할 수 있는 위젯
 행정경계 표시 현재 지도의 행정경계 위치를 표시하고 다른 행정경계로 이동할 수 있는 위젯
 저장 지도를 PNG 또는 PDF로 저장할 수 있는 위젯
 인쇄 지도를 프린트 할 수 있는 위젯
 피쳐속성폼 위젯을 클릭한 후 지도상에 피쳐를 클릭하면 피쳐정보를 수정 할 수 있는 위젯

툴바 영역 위젯
홈 지도 홈으로 이동하는 위젯
 초기화 지도에 그려진 임시 그래픽을 초기화 하는 위젯
 전체화면 지도를 전체화면으로 확인 할 수 있는 위젯
 이동 이전/다음으로 이동할 수 있는 위젯
 분할지도 2분할/3분할/4분할로 지도를 분할할 수 있는 위젯
 그리기 점/폴리곤/사각형/선/텍스트/곡선/원/버퍼를 지도상에 그릴 수 있는 위젯
 측정 지도상에서 면적/거리/반경/지점을 측정할 수 있는 위젯
 회전/나침반 지도상에서 Alt + Shift + 마우스 왼쪽 클릭시 지도를 회전 할 수 있는 위젯
 배경지도 배경지도를 변경할 수 있는 위젯

하단 영역 위젯 

확대/축소 
지도를 확대/축소 할 수 있는 위젯

4.3. 제품 지원 사항
구분 설명

적용 표준 및 권고안 
• OGC공간정보 서비스 표준 준수·(WMS) 이미지 형태의 지도 제공 서비스 제공 시 해당 표준 준수·(WFS) 지리정보(Feature)에 대한 CRUD 서비스 제공 시 해당 표준 준수
• GeOn-Buider등 웹 UI을 가진 프로그램은 W3C의 웹표준을 준수하여 개발하여 크롬, 엣지등 웹표준을 준수하는 웹브라우저에서구동함

운영 지원 여부 
• 운영 지원은 별도 운영 지원 계약을 통하여 지원
• 유지보수는 유상 유지보수 계약(통산 1년 단위)를 통하여 제공하며 유지보수 제공 및 유지 보수 내용 
• 제공되는 유지보수 내용은 아래 내용을 포함함·프로그램오류에 대한 SW 오류 수정·프로그램의 예측되는 오류를 선점 처리를 위한 예방 유지보수·HW및 SW환경 변화에 따른 SW 적응
 
6. 시스템 백업 및 복원
서비스 내의 안정적 제공을 위해 DB는 1일 1회 자동 백업을 합니다.
백업된 DB는 시스템 상의 중대 결함 발생 시 복구하는 데 사용됩니다.
백업 대상은 백업 당시 DB에 포함된 모든 내용이 백업 됩니다.
DB 백업 원칙은 아래와 같습니다.
- 백업 시스템에 의해 주기적 백업 업무의 자동화
- 데이터량 변화에 따른 백업 주기의 조정 및 관리

7. 로그 관리
시스템의 운영중에 발생하는 로그를 확인하는 방법을 설명합니다.
로그 확인 절차
① WAS 터미널로 SSH 접속 연결한다.
② 아래 명령어를 입력하여 로그를 확인한다.
③ 아래와 같이 로그를 확인 할 수 있다.
 
 
8. 용어 및 약어
용어 설명

API (application programming interface) 
ㅇ API는 응용 프로그램에서 사용할 수 있도록, 운영 체제나 프로그래밍 언어가 제공하는 기능을 제어할 수 있게 만든 인터페이스를 뜻한다.

Bessel 타원체 
ㅇ Bessel 타원체는 1841년에 만들어졌으며 동경좌표계(Tokyo Datum)를 기준으로 한다. 장반경 6377397.155m, 단반경 6356078.963m, 편평율 299.1528128이다.

CSV(comma-separated values) 
ㅇ 몇 가지 필드를 쉼표(,)로 구분한 텍스트 데이터 및 텍스트 파일이다.

Docker 
ㅇ Docker는 애플리케이션을 신속하게 구축, 테스트 및 배포할 수 있는 소프트웨어 플랫폼입니다. Docker는 소프트웨어를 컨테이너라는 표준화된 유닛으로 패키징하며, 이 컨테이너에는 라이브러리, 시스템 도구, 코드, 런타임 등 소프트웨어를 실행하는 데 필요한 모든 것이 포함되어 있습니다. Docker를 사용하면 환경에 구애받지 않고 애플리케이션을 신속하게 배포 및 확장할 수 있으며 코드가 문제없이 실행될 것임을 확신할 수 있습니다

dxf 
ㅇ PC용 캐드 시스템에서 파일교환을 위한 기본도면 파일 형식

GeoJSON 
ㅇ 위치정보를 갖는 점을 기반으로 체계적으로 지형을 표현하기 위해 설계된 개방형 공개 표준 형식

GeoServer 
ㅇ Geoserver(지오서버)는 지리공간 데이터를 공유하고 편집할 수 있는 Java로 개발된 오픈 소스 GIS 소프트웨어 서버이다.
ㅇ 상호운용성을 전제로 개발되었기 때문에, 개방형 표준을 사용하여 다양한 공간 데이터 소스를 서비스할 수 있게 한다.

GIS(Geographic Information System) 
ㅇ 넓게는 지리공간적으로 참조 가능한 모든 형태의 정보를 효과적으로 수집, 저장, 갱신, 조정, 분석, 표현할 수 있도록 설계된 컴퓨터의 하드웨어와 소프트웨어 및 지리적 자료, 인적자원의 통합체를 의미. 좁게는 전 국토의 지리공간정보를 디지털화하여 수치 지도(digital map)로 작성하고 다양한 정보통신기술을 통해 재해, 환경, 시설물, 국토공간 관리와 행정서비스에 활용하고자 하는 첨단정보시스템을 의미

GRS80 타원체 
ㅇ Geodetic Reference System 1980 타원체의 약자로 타원체의 형상, 지구 중심을 원점으로 정한 타원체. 민간 분야의 국제 협력으로 구축되었으므로 고정밀도로 정밀한 WGS84라 불리기도 함(개정을 통하여 WGS84 타원체와의 차이를 단반경 약 0.1mm 정도로 축소하였으므로 실용적으로 동일하게 취급 가능). 장반경 6378137.000m, 단반경 6356752.314m, 편평율 298.257222101임.

Java 
ㅇ 객체 지향 프로그래밍 언어로서 보안성이 뛰어나며 컴파일한 코드는 다른 운영 체제에서 사용할 수 있도록 클래스(class)로 제공된다.
ㅇ 객체 지향 언어인 C+ 언어의 객체 지향적인 장점을 살리면서 분산 환경을 지원하며 더욱 효율적이다.

JavaScript API 
ㅇ 서버 컴포넌트에서 REST API로 구현된 기능을 웹브라우저 클라이언트 Javascript 환경에서 쉽게 사용할 수 있도록 SDK 형태로 개발한 API

Javascript (자바스크립트) 
ㅇ 자바스크립트는 객체 기반의 스크립트 프로그래밍 언어이다. 이 언어는 웹 브라우저 내에서 주로 사용하며, 다른 응용 프로그램의 내장 객체에도 접근할 수 있는 기능을 가지고 있다. 또한 Node.js와 같은 런타임 환경과 같이 서버 프로그래밍에도 사용되고 있다.
 
JSON(JavaScript Object Notation) 
ㅇ JSON은 경량의 DATA교환 형식.
ㅇ 이 형식은 사람이읽고 쓰기에 용이하며, 기계가 분석하고 생성함에도 용이. ㅇ 속성:값 쌍 또는 “키:값 쌍”으로 이루어진 데이터 오브젝트를 전달하기 위해 인간이 읽을 수 있는 텍스트를 사용하는 개방형 표준 포맷이다.

NAS 
ㅇ 컴퓨터 네트워크에 연결된 파일 수준의 컴퓨터 기억장치

OGC(개방형 공간정보 컨소시엄) 
ㅇ 개방형 공간 정보 컨소시엄(Open Geospatial Consortium, OGC)은 19 4년에 기원한 국제 표준화 기구이다. OGC에서 전 세계 50 곳 이상의 상업, 정부, 비영리, 연구 단체들이 지리 공간적 콘텐츠와 서비스, 센서, 웹, 사물 인터넷, GIS 데이터 처리, 데이터 공유를 위한 개방형 표준의 개발 및 구현을 장려하는 콘센서스 프로세스에서 서로 협업한다.

PNU 
ㅇ 필지를 고유하게 식별할 수 있는 값으로 시도 코드 2자리, 시군구 코드 3자리, 읍면동 코드 3자리, 리 2자리, 필지구분 코드 1자리, 본번 코드 4자리, 부번 코드 4자리 총 19자리로 구성되어있다.
ㅇ Point of Interest
ㅇ 관심 지점 ( POI로 약칭)은 누군가가 흥미롭거나 유용하다고 생각할 수 있는 POI 지도상의 특정 장소 또는 위치 지점입니다. 그것은 레스토랑, 호텔 또는 관광 명소일 (point of interest) 수도 있고 ATM, 주유소, 학교 또는 공원과 같은 평범한 장소일 수도 있습니다. 관심 지점은 이벤트와 같이 일시적일 수도 있고 건물이나 도시와 같이 영구적일 수도 있습니다.

PostgreSQL 
ㅇ 확장 가능성 및 표준 준수를 강조하는 객체-관계형 데이터베이스 관리 시스템

Proxy (프록시) 
ㅇ 다른 서버 상의 자원을 찾는 클라이언트로부터 요청을 받아 중계하는 서버

REST API 
ㅇ REST(Representational state transfer) 아키텍처의 제약 조건을 준수하는 API(Application Programming Interface, 애플리케이션 프로그래밍 인터페이스). 자원의 식별, 메시지를 통한 리소스의 조작, 자기 서술적 메시지, 애플리케이션의 상태에 대한 엔진으로서 하이퍼미디어의 특성을 지님.

SHP 
ㅇ 위상관계를 가지지 않는 공간 데이터 형식을 말하며, 파일구조가 단순해서 구조화된 데이터에서는 거의 표준으로 사용되는 GIS 파일형식을 말한다. 배포되는 자원의 형식의 일종. ㅇ ESRI사 제품의 파일포멧으로 도형 및 속장자료의 통합변환이 가능하다.

TIFF 
ㅇ 태그 붙은 화상 파일 형식이라는 뜻으로, 미국의 앨더스사(현재는 어도비 시스템스사에 흡수 합병)와 마이크로소프트사가 공동 개발한 래스터 화상 파일 형식

TM 좌표계 
ㅇ 우리나라는 국토를 네 구역으로 나누어 투영하며 투영된 지도에 N38도/E125도, N38도/E127도, N38도/E129도, N38도/E131도를 서부, 중부, 동부, 동해 원점으로 하여 각각 평면 직각 좌표계를 만들었다. 이 좌표계들을 서쪽에서부터 서부 좌표계, 중부 좌표계, 동부 좌표계, 동해 좌표계라고 하며 이들을 통합하여 지칭하는 것이 TM 좌표계이다.

TOC(Table of contents) 
ㅇ GIS에서 레이어의 목차 또는 지도 레이어의 관리를 의미.

TXT 
ㅇ 문서 파일 형식 중에서 가장 호환성이 높은 파일 형식

URL 
ㅇ 웹 문서의 각종 서비스를 제공하는 서버들에 있는 파일의 위치를 표시하는 표준

UTM (universal transverse mercator)
ㅇ UTM 좌표계(Universal Transverse Mercator Coordinate System)는 전 지구상 점들의 위치를 통일된 체계로 나타내기 위한 격자 좌표 체계의 하나로 1947년에 개발되었다. UTM 좌표계에서는 지구를 경도 6° 간격의 세로 띠로 나누어 횡축 메르카토르 도법으로 그린 뒤, 위도 8° 간격으로 총 60×20 개의 격자로 나누어 각 세로 구역마다 설정된 원점에 대한 종·횡 좌표로 위치를 나타낸다. 지리 좌표계가 극지방으로 갈수록 직사각형이 크게 감소하는 반면 UTM 좌표계는 직사각형 모양을 유지하므로 거리, 면적, 방향 등을 나타내는 데 매우 편리하다는 장점이 있다.

UTM-K 
ㅇ 한국형 UTM(Universal Transverse Mercator) 좌표계로 기존 UTM 좌표계에서 원점 및 가산수치를 다르게 적용

WFS(Web Feature Service) 
ㅇ OGC가 정의한 지리적 피처(Feature)인터페이스 표준이며 피처 요청, 카타로그 조회, 속성 조회 가능. Http로 요청하고 XML, GeoJSON등으로 받음.

WGS84 타원체 
ㅇ World Geodetic System 1984 타원체의 약자로 미국 국방성이 1984년에 군사용으로 구축, 채택하였고 선박, 항공기의 항행용으로 사용. 1996년 미국이 GPS의 민생 이용을 위해 계속적으로 서비스할 것을 표명한 이후로 현재까지 GPS에 사용. 개정된 측량법에서 UTM 도법을 사용하여 투영. 장반경 6378137.000m, 단반경 6356752.314m, 편평율 298.257223563임. 개정을 통하여 GRS80 타원체와의 차이를 단반경 약 0.1mm 정도로 축소하였으므로 실용적으로 동일하게 취급 가능함.

WMS(Web Map Service) 
ㅇ OGC가 정의한 지도이미지 인터페이스 표준이며 지도 요청, 카타로그 조회, 속성 조회 가능. Http로 요청하고 이미지로 받음.
ㅇ 웹 맵 서비스(Web Map Service, WMS)는 GIS(지리 정보 시스템)에서 데이터를 사용하기 위해 맵 서버에서 생성된 지도 이미지를 인터넷상에서 제공하기 위한 표준 프로토콜이다.

WMTS (Web Map Tile Service) 
ㅇ OGC 웹 맵 타일 서비스 구현 표준(WMTS)은 미리 정의 된 콘텐츠, 범위 및 해상도가 있는 타일 이미지를 사용하여 공간적으로 참조된 데이터의 맵 타일에 대한 웹 기반 요청을 만들기 위한 인터페이스 세트를 정의합니다.

XLS 
ㅇ마이크로소프트사에서 개발해 판매하는 스프레드시트 프로그램

XML 
ㅇ W3C에서 여러 특수 목적의 마크업 언어를 만드는 용도에서 권장되는 다목적 마크업 언어이다. ㅇ 가장 친숙하고 흔하게 접할 수 있는 마크업 언어로 HTML이 있다.
ㅇ XML은SGML의 단순화된 부분 집합이지만 , 수많은 종류의 데이터를 기술하는데 적용. ㅇ XML은 주로 다른 시스템, 특히 인터넷에 연결된 시스템끼리 데이터를 쉽게 주고 받을 수 있게 하여 HTML의 한계를 극복할 목적으로 만들어짐.

ZIP 
ㅇ PC로 다운로드한 데이터를 저장할 때 압축을 하거나 압축을 풀어주는 개방 표준

경계 디졸브 (dissolve boundaries) ㅇ 교차하거나 필드 값이 동일한 영역 피처를 병합합니다.
ㅇ 예를 들어 행정구역 레이어가 있는 경우 각 법정동별 시군구코드 속성이 있으면 시군구코드 속성을 사용하여 경계를 디졸브할 수 있다. 인접 시군구코드가 같은 경우에 병합되어 최종 결과는 시군구 경계 레이어이다.

경위도 좌표계 
ㅇ 일반적으로 지리좌표계를 의미하며 2차원 경위도 좌표계는 지구상의 위치를 나타내는 3차원 좌표계인 지구 타원체의 위치를 2차원인 면에 표현. 3차원 경위도 좌표계는 3차원 공간에서 경도, 위도, 고도 세 부분이 서로 직교하는 특징이 있음.

경위도(longitude and latitude) 
ㅇ 경도(longitude)와 위도(latitude)의 것으로 지구 및 천체 표면상에 위치(점)를 나타내기 위한 좌표 표현
 
공간 검색(공간질의) 
ㅇ 위치나 공간관계에 기초하여 지형물을 선정하는 과정으로서, 즉 사용자가 지도상에 위치한 사상의 속성이나 지도의 영상면출력(Display)이나 찾는 것을 가능하게 하는 GIS의 기능이다.

공간 분석 
ㅇ 지도상의 주요 자산의 실제 위치를 확인하는 것으로 다양한 지리정보를 통합 분석하여 이용자의 요청 사항에 해당하는 정보를 생성.

공간 분할 생성 
ㅇ 선택한 공간정보를 다양한 분할 타입으로 (사각형, 육각형, 타원 등) 둘 또는 그 이상으로 나눔

공간 조건 검색 분석 
ㅇ 지정한 일련의 조건을 충족하는 기존 피처를 선택한다.
ㅇ 이러한 조건은 속성 쿼리(비어 있는 필지)와 공간 쿼리(강에서 1.6km이내 필지)를 기반으로 할 수 있다.

공간 조건 추출 분석 
ㅇ 지정한 일련의 조건을 충족하는 새 피처를 생상하는데 사용
ㅇ 이러한 조건은 속성 쿼리(비어 있는 필지)와 공간 쿼리(홍수권 내에 있는 필지)를 기반으로 할 수 있다. 
ㅇ 공간 조건 검색 분석과 공간 조건 추출 분석의 가장 큰 차이점은 공간 조건 추출 분석의 경우 부분 피처를 포함할 수 있다는 것이다.

공간분포 패턴 분석 
ㅇ 포인트 피처의 중심 피처, 평균 중심, 중앙값 중심 또는 타원(방향 분포)를 찾는다. 
ㅇ 중심 피처 : 데이터셋에서 가장 중심에 위치한 피처를 식별한다.
ㅇ 평균 중심 : 피처 집합의 지리적 중심(또는 집중 중심)을 식별한다.
ㅇ 중앙값 중심 : 데이터셋의 피처까지의 전체 유클리드 거리를 최소화하는 위치를 식별한다. 
ㅇ 타원 : 표준편차 타원을 생성하여 지리 피처의 공간 특성을 요약한다. 중심 경향, 분산 및 방향 경향 타원의 크기는 1, 2 또는 3 표준편차로 조정할 수 있다.

공간정보 
ㅇ 지상 · 지하 · 수상 · 수중 등 공간상에 존재하는 자연적 또는 인공적인 객체에 대한 위치정보 및 이와 관련된 공간적 인지 및 의사결정에 필요한 정보이다

공간정보 좌표변환 
ㅇ 공간정보를 생성하기 위한 좌표변환에 관한 것으로 일반적으로 3단계 프로세스를 거침. 1단계에서 좌표를 정의하고 2단계에서 좌표를 변환하고 3단계에서는 좌표변환 과정에서 변환된 좌표의 값이 정상 범위 밖의 오차를 생성했는지 확인하는 과정을 의미

공간조인 분석 
ㅇ 공간 및 속성 관계에 따라 한 레이어나 테이블의 속성을 다른 레이어나 테이블로 전송한다. 필요한 경우 조인된 피처에 대한 통계를 계산할 수 있다.

기하학, 지오메트리 (Geometry) 
ㅇ 공간에 있는 도형의 성질, 즉 대상들의 치수, 모양, 상대적 위치 등을 연구하는 수학의 한 분야 길이 계산 ㅇ레이어 피처의 길이를 측정하는 작업을 수행한다.

데이터 추출 
ㅇ 지정된 관심 영역에 대한 데이터를 선택하여 다운로드하거나 레이어에 추가된다.

도로명주소 
ㅇ 기존 지번주소를 대신하여 도로에 이름을 붙이고, 건물에 번호를 붙여 도로명과 건물번호를 알기 쉽게 표시하는 주소

동기화 
ㅇ 독립된 2개 이상의 주기적인 사건을 적절한 방법으로 결합, 제어함으로써 일정한 위상 관계를 지속시키는 일

레이아웃 
ㅇ 각 구성요소를 공간에 효과적으로 배열하는 일, 또는 그 기술
 
레이어 병합 
ㅇ 지오메트리 유형이 같은 두 피처 레이어를 합쳐 하나의 결과 레이어를 생성

레이어 중첩(교차) 
ㅇ 동일한 유형의 두 레이어의 교차되는 영역을 새 레이어로 속성을 포함하여 병합하는 작업을 수행한다.

레이어 중첩(유니온) 
ㅇ 동일한 유형의 두 레이어를 새 레이어로 속성을 포함하여 교차되는 영역은 자르기 후 병합하는 작업을 수행한다.

레이어 중첩(지우기) 
ㅇ 동일한 유형의 두 레이어의 중첩되는 영역을 제외한 새 레이어로 속성을 포함하여 병합하는 작업을 수행한다.

레이어 (layer) 
ㅇ 서버에서 맵으로서 요청되는 공간정보의 기본 단위 면적 계산 
ㅇ레이어 피처의 면적과 둘레길이를 측정하는 작업을 수행한다.

밀도 계산 (밀도맵 / heat map) 
ㅇ 히트 맵(heat map)은 열을 뜻하는 히트(heat)와 지도를 뜻하는 맵(map)을 결합시킨 단어로, 색상으로 표현할 수 있는 다양한 정보를 일정한 이미지위에 열분포 형태의 비쥬얼한 그래픽으로 출력하는 것이 특징이다.

발행 
ㅇ 공간 데이터를 GIS Server에 레이어로 발행하여 어디에서든 인터넷을 통해 웹, 데스크톱 및 모바일에서 GIS Server 레이어에 접근할 수 있도록 하는 기능

버퍼 
ㅇ 입력 피처 주위의 특정한 거리를 표현하기 위해 버퍼 폴리곤을 생성하는 기능

버퍼 분석 
ㅇ 포인트, 라인 또는 영역 피처에서 지정된 거리를 포함하는 영역을 생성한다.
ㅇ 일반적으로 레이어 중첩과 같은 도구를 사용하여 추가 분석할 수 있는 영역을 생성하는데 사용

범위 내 요약 
ㅇ 분석 레이어의 영역 경계 내에 있는 피처(및 피처의 일부)를 찾는다.
ㅇ 요약할 레이어의 숫자 필드에서 계산할 수 있는 통계는 합계, 최소값, 최대값, 평균, 표준편차가 있음

베이스맵 (basemap) 
ㅇ 지도의 배경 설정을 형성하는 GIS 데이터 및 정형화 된 이미지 모음

스와이프 
ㅇ 수직 스와이프 막대를 좌우로 이동하여 한 웹 맵 내의 서로 다른 두 레이어를 비교할 수 있는 기능

심볼 (Symbol) 
ㅇ 지리적 사상을 나타내는 표현도식으로 점, 선, 면의 특징을 나타내는 도형요소이다. 예를 들어 선의 심볼은 Arc Feature를 나타내고 마크 심볼은 점을, 쉐이드 심볼은 면을, 그리고 텍스트 심볼은 주석을 나타낸다. 심볼의 색상, 크기, 패턴과 그외의 여러 가지 특징으로 정의된다. GIS는 많은 다양한 심볼을 디스플레이 하기 위해 점, 선, 면 등을 갖추고 있다. 지형도상에 표현되는 내용 및 그 표시방법 등을 정하고 지형도의 규격에 대한 통일을 도모할 목적으로 만들어진 형식을 말한다.

썸네일 (thumb nail) 
ㅇ 인터넷에서 페이지 전체의 레이아웃을 검토할 수 있게 페이지 전체를 작게 줄여 화면에 띄운 것을 말한다.

영역 내 집계 분석 
ㅇ 입력 레이어 경계 내에 있는 피처를 찾는다. 그런 다음 포함된 피처에 대한 통계를 계산한다.

웹레이어 (web layer) 
ㅇ 웹 레이어라고도 하는 레이어는 맵과 씬을 생성하는 데 사용되는 공간 데이터의 논리 컬렉션으로, 공간 분석의 기준으로도 사용됩니다. 예를 들어 캠퍼스 건물 컬렉션을 나타내는 건물 레이어에는 건물의 이름, 건물 유형, 건물 크기, 기타 가능한 속성 등과 같은 각 건물의 등록정보를 나타내는 속성 값이 포함됩니다.
 
지도 빌더 
ㅇ 레이어(점, 선, 면)의 스타일 편집 및 등록 관리 하고, 지도 상의 레이어와 각종 분석 기능을 이용하여 하나의 의미 있는 지도(레이어 모음)를 생성하는 도구

앱 빌더 
ㅇ 지도 빌더를 통해 생성된 지도를 다양한 테마(표준, 편집, 탭, 스토리) 및 위젯 도구를 활용하여
새로운 지도앱을 제작하는 도구

주변집계 분석 
ㅇ 직선 거리 또는 이동 모드를 사용하여 입력 레이어에서 피처로부터 지정된 거리 내에 있는 피처를 찾는다. 그런 다음 인근 피처에 대한 통계를 계산한다.

주소정제 
ㅇ 비정형 원본 주소를 표준 주소로 변환

중심 찾기 분석 
ㅇ 멀티포인트, 라인 및 영역 피처의 기하학적 중심을 나타내는 포인트 피처를 생성한다.

지번주소 
ㅇ 땅에 번호를 매겨 주소를 정하는 방식

지오코딩 (geocoding) 
ㅇ 위치의 한 형태를 다른 형태로 변환 . 지오코딩은 보통 “주소”나 “교차로”를 “직접 위치(좌표)”로 번역하는 것을 말한다. 많은 서비스 제공자는 지오코더에 “역( 逆 )지오코딩” 인터페이스를 포함해서 일반적인 위치 번역기로 서비스의 정의를 확장한다. 라우팅 서비스는 보통 다른 것에서는 활용할 수 없는 내부 위치 인코딩을 사용하기 때문에, 지오코더는 서비스 내부에 존재하는 정수의 일부일 뿐이다.

축척 (scale) 
ㅇ 실제의 거리를 일정한 비율로 줄인 정도로, (지도상의 거리/실제 거리)를 지도축척(map scale)이라 한다.

클러스트링 
ㅇ 개체들이 주어졌을 때, 개체들을 몇 개의 비슷한 특성을 가진 데이터들의 집단으로 나누는 과정을 의미. 
ㅇ 맵에 포인트 수가 많은 레이어가 있는 경우 군집을 구성하여 더욱 간편하게 데이터에서 의미 있는 정보를 시각적으로 추출할 수 있다.

타원체 
ㅇ 준거 타원체<측지학> 주축을 중심으로 회전하는 타원에 의해 형성된, 3차원 유클리드 공간에 포함된 기하학적 기준면비고 1 지구에서 타원체는 축이 두개 있으며, 남북축을 기준으로 회전한다. 그 결과 편평 타원체가 생성되며, 초점의 중심은 지구의 명목상의 중심에 위치한다

피처(feature) 
ㅇ 모양, 크기, 위치 등과 같은 도형의 공간정보와 속성 모두를 포함하는 단일 객체

필드 (field) 
ㅇ 소프트웨어에 관련하며, 하나의 레코드 중에서 특정 종류의 데이터를 위해서 사용되는 지정된 영역을 말한다. 예를 들면, 급여 레코드 중의 「 잔여 지급액 」 이 들어 있는 자릿수를 정한 영역 등을 가리킨다.

핫스팟 (hotspot) 
ㅇ 데이터의 공간 패턴에 통계적으로 유의한 군집이 있는지를 격자 형태로 시각화합니다.

행정구역 
ㅇ 정치적으로 하나의 단위를 이루는 국가의 영역을 국가 행정상의 목적에 따라 구획한 행정단위
 

9. FAQ
Question 
(지도빌더) 어떤 종류의 레이어를 지도에 추가할 수 있습니까?
- Shape File(ZIP), 주소, PNU, X/Y좌표가 포함된 엑셀/CSV/TXT 파일, OGC 표준의 WFS/WMS/WMTS 웹레이어 URL, DXF 캐드파일 등
 
(지도빌더) 그리기 및 측정도구 결과는 어떻게 삭제 하나요?
- 지도에 그려진 객체 위에서 마우스 우측 클릭하면 삭제 버튼이 표출되고 삭제 버튼을 클릭하면 삭제됩니다.
 
(지도빌더) 레이어 목록(TOC)에 있는 레이어가 공간분석 레이어에 왜 안올라 오나요?
- 공간분석이 가능한 레이어 유형만 분석대상 및 비교대상 레이어에 검색됩니다. 예 ) 밀도분석 : 점 레이어, 면적계산 : 면 레이어, 공간조인 분석 : 점/선/면 레이어
 
(지도빌더) 지오코딩 결과를 점 레이어와 면 레이어 둘 다 발행하려면 어떻게 하나요?
- 작업 알림창에서 지오코딩 이력을 삭제하지 않은 경우 [+] 버튼을 클릭하여 언제든지 레이어로 발행 할 수 있습니다.
 
(앱빌더) 표준 테마와 편집 테마는 어떻게 다르나요?
- 표준 테마, 편집 테마 대부분 동일한 위젯 도구 사용이 가능하지만 편집 테마에서만 속성 테이블의 편집 기능 사용이 가능합니다.
 
(앱빌더) 앱 빌더로 생성한 앱을 다른곳에서 사용이 가능한가요?
- 앱을 저장하고 미리보기 메뉴를 클릭 후 미리보기창에서 브라우저의 URL을 복사하여 타 시스템의 메뉴 등으로 연결하여 사용 가능합니다.
 
(개발자센터) 폐쇄망에서도 바로e맵, 브이월드 등 외부 연계 API를 사용 할 수 있나요?
- 폐쇄망의 경우 GeOn-API 서버가 외부망에 직접 연결이 가능하거나 별도의 내/외부망 연결이 가능한 Proxy 서버가 필요합니다.
 
(개발자센터) 지도 예제와 위젯 예제의 다른점은 무엇인가요?
- 지도 예제는 웹 환경에서 지도를 제어하는데 필요한 다양한 예제를 제공합니다.
- 위젯 예제는 GeOn-API의 요청 및 응답 결과 처리, 지도 제어, 기능 UI 생성 등을 위젯에서 처리하여 개발자가 좀 더 빠르고 쉽게 개발하도록 다양한 예제를 제공합니다.

1. 메인 화면 - 비로그인
1.1. GnB 
o 개요
맵픽에서 제공되는 메인 화면으로 메뉴는 맵픽이란, 지도, 맵갤러리, 공지사항으로 구성되어 있고 기관이동 및 회원가입을 할 수 있다.

1.1.1. 맵픽이란
¡ 맵픽의 기능과 사용법을 소개하는 화면이다.
o 화면 설명
① 맵픽의 기능을 간단하게 아이콘과 텍스트로 소개한다.

1.1.2. 지도
¡ 맵픽의 기본 지도를 조회한다.
o 화면 설명
① 지도 화면에서 도움말 버튼을 클릭하면 지도 사용자 매뉴얼 파일이 다운로드된다.

1.1.3. 맵 갤러리
¡ 사용자가 소속된 기관의 소속원들이 공개한 지도와 테마지도를 보여준다.
o 화면 설명
① 전체, 지도, 테마지도로 구성되어 있다. ② 콘텐츠를 선택하여 상세정보를 볼 수 있다.
¡ 콘텐츠의 상세정보를 보여준다.
o 화면 설명
① 지도 창으로 해당 콘텐츠를 보여준다. ② 콘텐츠의 상세 설명을 보여준다.

1.1.4. 공지사항
¡ 공지사항의 목록을 보여준다.
o 화면 설명
① 해당 공지사항의 상세정보를 보여준다.
¡ 공지사항의 상세 내용을 보여준다.
o 화면 설명
① 제목, 작성일, 작성자, 내용, 첨부파일을 볼 수 있다.

1.1.5. 로그인
¡ 로그인과 회원가입을 할 수 있는 화면이다.
o 화면 설명
① 아이디, 비밀번호를 입력한다. ② 아이디와 비밀번호 찾기로 이동한다.
¡ 가입했던 이름과 이메일로 아이디를 찾는 화면이다.
o 화면 설명
① 가입했던 이름과 이메일을 입력 후 인증번호 인증을 통해 아이디를 찾는다.
¡ 가입했던 아이디와 이름, 이메일로 비밀번호를 찾는 화면이다.
o 화면 설명
① 가입했던 아이디, 이름, 이메일을 입력 후 인증번호 인증을 통해 비밀번호를 찾는다.

1.1.6. 회원가입
¡ 회원가입을 하는 화면이다.
o 화면 설명
① 개인정보 동의에 관한 필수 약관에 동의 체크박스를 클릭한다.
¡ 가입할 정보를 입력하는 화면이다.
o 화면 설명
① 아이디, 비밀번호, 이름, 이메일을 입력하여 회원가입을 진행한다. ☞ 입력사항 제한
Ÿ 아이디 작성 후 중복확인을 해야한다. Ÿ 비밀번호는 특수문자를 포함한 8-15자로 입력해야 한다.
¡ 회원가입이 완료된 화면이다.
¡ 회원가입 시 자신이 속한 기관설정을 한다.
o 화면 설명
① 기관 명을 입력한다. 
② 기관 도메인을 입력 및 중복확인 후 “설정하기” 버튼 클릭 시 기관 코드가 자동으로 발급된다. ※ 기관 코드 추후 변경 불가
☞ 입력사항 제한
Ÿ 기관 도메인에는 기본 라틴 문자, 숫자, 하이픈(-)만 사용할 수 있습니다.

2. 메인화면 – 로그인
2.1. GnB 
o 개요
로그인 시 제공되는 메인 화면으로 메뉴는 지도, 테마지도, 맵갤러리, 콘텐츠, 기관정보, 소통공간으로 구성되어 있다.

2.1.1. 마이페이지
¡ 내 콘텐츠, 즐겨찾기, 공유받은 콘텐츠, 내 기관 콘텐츠, 개인정보 관리, 관리자 화면, 소속그룹에 관한 화면을 보여준다.
o 화면 설명
① 내가 올린 콘텐츠, 즐겨찾기 한 콘텐츠, 공유받은 콘텐츠, 내 기관이 올린 콘텐츠의 개수를 보여준다. 
② 내 콘텐츠의 목록을 보여주고 더보기 버튼 클릭 시 콘텐츠 화면으로 이동한다.. ③ 내 정보 관리, 비밀번호 변경, 관리자 화면, 회원 탈퇴로 이동한다. ④ 현재 소속된 그룹의 이름을 보여준다.

2.1.2. 내 정보 관리
¡ 사용자의 정보를 관리하는 화면이다.
o 화면 설명
① 내 정보 관리 버튼을 클릭한다. ② 사진 업로드를 통해 사용자의 이미지를 변경할 수 있다. ③ 사용자의 이름을 변경할 수 있다. ☞ 입력사항 제한
Ÿ 사진은 1MB 이하 이미지만 가능합니다. Ÿ 이름은 20자 이내로만 가능합니다.

2.1.3. 비밀번호 변경
¡ 사용자의 비밀번호를 변경하는 화면이다.
o 화면 설명
① 비밀번호 변경 버튼을 클릭한다. 
② 기존 비밀번호와 변경할 비밀번호를 입력하여 비밀번호를 변경한다. ☞ 입력사항 제한
Ÿ 변경할 비밀번호는 특수문자를 포함한 8-15자로 입력해야합니다.

2.1.4. 회원탈퇴
¡ 회원탈퇴를 하는 화면이다.
o 화면 설명
① 회원탈퇴 버튼을 클릭한다. 
② 비밀번호를 입력 후 탈퇴 사유를 선택하여 탈퇴를 진행한다.

2.2. 지도
o 개요
다양한 레이어로 원하는 형태의 지도를 제작할 수 있는 지도 제작 페이지로 사용자가 원하는 대로 지도를 저장하고 불러올 수 있으며 레이어를 이용한 데이터 요약 분석, 위치찾기 분석, 공간패턴 분석, 근접도 분석, 데이터 관리 분석, 좌표변환의 공간분석 기능을 제공한다. 

2.2.1. 지도 제작 화면
¡ 배경지도, 분할지도, 그리기, 측정 도구 등의 기본적인 지도 작업 도구를 이용하여 지도를 컨트롤 할 수 있으며 사용자가 업로드한 레이어나 공유된 레이어를 통해 지도를 제작하고 저장할 수 있는 화면이다.

2.3. 테마지도
o 개요
만들어진 지도를 이용해 다양한 지도 콘텐츠 묶음(테마)를 만들 수 있다. 2.3.1. 테마 지도 제작 화면
¡ 테마를 선택하여 만들어진 여러 지도를 원하는 형식의 테마로 구성할 수 있는 화면이다.

2.4. 맵갤러리
o 개요
사용자가 소속된 기관에서 공유된 지도와 테마지도를 볼 수 있는 화면으로 전체, 지도, 테마지도로 구성되어 있다. 

2.4.1. 맵갤러리
¡ 사용자가 소속된 기관의 소속원들이 공개한 지도와 테마지도를 보여준다.
o 화면 설명
① 전체, 지도, 테마지도로 구성되어 있다. ② 좋아요, 즐겨찾기를 할 수 있으며 돋보기 버튼을 클릭해 상세정보를 볼 수 있다.

2.4.2. 맵갤러리 상세
¡ 콘텐츠의 상세정보를 보여준다.
o 화면 설명
① 좋아요와 즐겨찾기를 할 수 있다. ② 지도 창으로 해당 콘텐츠를 보여준다. ③ 지도에 사용된 레이어의 목록을 나타낸다.

2.5. 콘텐츠
o 개요
콘텐츠를 보여주는 화면으로 내 콘텐츠, 공유받은 콘텐츠, 내 기관 콘텐츠로 구성되어 있다. 

2.5.1. 콘텐츠
¡ 사용자가 만든 콘텐츠와 공유받은 콘텐츠, 기관에 소속된 사용자들이 만든 콘텐츠를 보여준다.
o 화면 설명
① 내 콘텐츠, 공유받은 콘텐츠, 내 기관 콘텐츠로 구성되어 있다. ② 콘텐츠의 상세정보를 볼 수 있다.

2.5.2. 콘텐츠 상세
¡ 콘텐츠의 상세정보를 보여주고 사용자가 만든 콘텐츠일 경우 수정이 가능하다.
o 화면 설명
① 제목을 수정한다. 
② 지도 창으로 해당 콘텐츠를 보여준다. 
③ 상세 설명을 수정할 수 있다. 
④ 콘텐츠에 사용된 레이어와 공유 현황, 맵 갤러리를 보여준다.

2.6. 공유 – 지도, 테마지도
o 개요
지도 및 테마지도 공유 기능으로 사용자/그룹/기관/전체별로 공유할 수 있다. 

2.6.1. 사용자 공유
¡ 기관 내 특정 사용자를 지정하여 생성한 콘텐츠를 공유한다.
o 화면 설명
① 사용자 탭을 선택한다. 
② 특정 사용자를 찾고자 하는 경우 이름을 검색한다. 
③ 공유하고자 하는 사용자를 추가하고 데이터를 선택한다.

2.6.2. 그룹 공유
¡ 사용자가 속한 그룹을 공유대상으로 지정하여 생성한 콘텐츠를 공유한다.
o 화면 설명
① 그룹 탭을 선택한다. 
② 특정 그룹을 찾고자 하는 경우 그룹명을 검색한다. 
③ 공유하고자 하는 그룹을 추가하고 데이터를 선택한다.

2.6.3. 기관 공유
¡ 사용자가 속한 기관을 공유대상으로 지정하여 생성한 콘텐츠를 공유한다.
o 화면 설명
① 기관 탭을 선택한다. 
② 기관 공유 신청 시 모든 기관 사용자들에게 공유되는 것에 대해 동의한다. 
③ “기관 공유하기”버튼을 클릭하여 기관관리자에게 기관 공유를 신청한다.

2.6.4. 전체 공유
¡ 맵픽의 모든 사용자를 공유대상으로 지정하여 생성한 콘텐츠를 공유한다.
o 화면 설명
① 전체 탭을 선택한다. 
② 전체 공유 신청 시 모든 맵픽 사용자들에게 공유되는 것에 대해 동의한다. 
③ “전체 공유하기”버튼을 클릭하여 기관관리자에게 전체 공유를 신청한다.

2.7. 공유 – 레이어
o 개요
레이어 공유 기능으로 사용자/그룹/기관별로 공유할 수 있다. 

2.7.1. 사용자 공유
¡ 기관 내 특정 사용자를 지정하여 생성한 콘텐츠를 공유한다.
o 화면 설명
① 사용자 탭을 선택한다. 
② 특정 사용자를 찾고자 하는 경우 이름을 검색한다. 
③ 공유하고자 하는 사용자를 추가하고 데이터를 선택한다.

2.7.2. 그룹 공유
¡ 사용자가 속한 그룹을 공유대상으로 지정하여 생성한 콘텐츠를 공유한다.
o 화면 설명
① 그룹 탭을 선택한다. 
② 특정 그룹을 찾고자 하는 경우 그룹명을 검색한다. 
③ 공유하고자 하는 그룹을 추가하고 데이터를 선택한다.

2.7.3. 기관 공유
¡ 사용자가 속한 기관을 공유대상으로 지정하여 생성한 콘텐츠를 공유한다.
o 화면 설명
① 기관 탭을 선택한다. 
② 기관 공유 신청 시 모든 기관 사용자들에게 공유되는 것에 대해 동의한다. 
③ “기관 공유하기”버튼을 클릭하여 기관관리자에게 기관 공유를 신청한다.

2.8. 공유 콘텐츠
o 개요
공유받은 콘텐츠를 보여주는 화면으로 기관이나 다른 사용자에게 공유받은 콘텐츠를 보여준다. 

2.8.1. 공유받은 콘텐츠
¡ 공유받은 콘텐츠의 목록을 보여준다.
o 화면 설명
① 공유받은 콘텐츠 버튼을 클릭한다. 
② 콘텐츠를 클릭해 상세정보를 확인한다.

2.9. 기관 정보
o 개요
사용자가 속한 기관과 구성원에 대한 정보를 보여주는 화면으로 구성원과 그룹으로 구성되어 있다. 

2.9.1. 구성원 관리
¡ 사용자가 속한 기관의 구성원 목록을 카드 형식으로 보여준다.
o 화면 설명
① 구성현 현황을 클릭하여 목록을 카드 형식으로 나타낸다.
¡ 사용자가 속한 기관의 구성원 목록을 목록 형식으로 보여준다.
o 화면 설명
① 목록 현황 버튼을 클릭하여 목록으로 나타낸다. 
② 사용자들의 레벨, 활동그룹, 레이어 생성개수, 지도 생성개수 등을 보여준다.

2.9.2. 그룹 관리
¡ 사용자가 속한 기관의 그룹을 보여준다.
o 화면 설명
① 그룹 버튼을 클릭한다. 
② 그룹 클릭 시, 그룹의 상세정보를 보여준다.
¡ 그룹의 상세정보를 보여준다.
o 화면 설명
① 그룹에 속해있는 구성원 이름과 공유 레이어, 공유 지도, 공유 테마지도, 마지막 로그인의 
정보를 보여준다.

포인트집계분석
포인트 집계 분석은 지정된 지역 내에서 포인트 데이터를 기반으로 집계를 수행하는 분석입니다. 이 분석은 포인트 데이터의 속성을 특정 지역으로 그룹화하여 합계, 평균, 빈도 등을 계산할 수 있습니다. 이를 통해 포인트 데이터의 패턴이나 특성을 파악할 수 있습니다. 분석 결과는 지도 상에 시각화되어 포인트의 집계 결과를 쉽게 확인할 수 있습니다.

공간조인분석
공간 조인 분석은 공간 데이터를 기반으로 두 개 이상의 데이터셋을 결합하는 분석 방법입니다. 이 분석은 공간 데이터에 기반하여 다른 데이터셋과 결합하여 추가 정보를 얻을 수 있습니다. 예를 들어, 도로 데이터와 인구 데이터를 공간 조인 분석을 통해 결합하여 도로 주변 인구 수를 분석할 수 있습니다. 이러한 분석은 지리적 위치에 따라 데이터를 통합하고 관찰할 수 있도록 도와줍니다.

영역 내 집계 분석
비교 대상 레이어의 폴리곤 경계 내에 있는 분석 대상 레이어를 레이어 유형(점/선/면)에 따라 집계(총건수/총길이/총면적) 분석한다.

공간분포패턴
레이어의 공간 분포 패턴을 분석하여 요약 유형별로 중심을 추출한다.

주변집계분석
직선 거리 또는 이동 모드를 사용하여 입력 레이어에서 피처로부터 지정된 거리 내에 있는 피처를 찾는다. 그런 다음 인근 피처에 대한 통계를 계산한다.

공간조건검색분석
공간조건검색분석은 비교 레이어를 기준으로 공간 검색 유형의 조건을 만족하는 분석 레이어의 객체를 검색하는 것입니다. 다음은 화면 구성과 조작 방법입니다.

공간조건추출분석
비교 레이어를 기준으로 공간검색 유형의 조건을 만족하는 분석 레이어의 객체를 검색 후 교차하는 영역으로 자르고 비교 레이어의 속성을 추가한다.

중심찾기분석
분석 대상 레이어 객체에서 결과 위치 설정에 따라 중심점을 찾는다.

유사한 위치찾기분석
유사한 위치 찾기 분석은 어떤 공간적 특성이 유사한 지점들을 찾는 분석 방법입니다. 이를 위해 일반적으로 거리나 유사도 지표를 사용하여 지점 사이의 유사성을 측정합니다. 예를 들어, 주어진 한 지점에 대해 유사한 다른 지점들을 찾는 것이 일반적인 사용 사례입니다. 이를 통해 비슷한 특성이 가까이 있는 다른 지점을 찾거나, 유사한 환경의 다른 위치를 찾을 수 있습니다. 이러한 분석은 지리 정보 시스템(GIS) 분야 등에서 활발하게 사용되고 있습니다.

밀도분석
위치에 있는 포인트를 기반으로 군집 포인트의 개수가 많은 곳을 쉽게 식별할 수 있도록 시각화하는 분석을 수행한다.

핫스팟분석
데이터의 공간 패턴에 통계적으로 유의미한 군집이 있는지를 격자 그리드로 시각화하는 분석을 수행한다.

포인트군집분석
포인트군집분석은 공간 데이터에 있는 포인트들을 일정한 기준에 따라 군집화하는 분석 방법입니다. 이를 통해 포인트들의 공간적 패턴이나 분포를 파악할 수 있습니다. 기준으로는 거리나 밀도 등을 사용할 수 있으며, 주요 목적은 비슷한 특성을 가진 포인트들을 함께 그룹화하여 서로 비교하거나 클러스터링된 그룹 간 유사성을 평가하는 것입니다.

버퍼분석
분석 대상 레이어 주위에 입력한 거리까지의 영역을 생성하는 작업을 수행한다.

최근접위치찾기분석
최근접 위치 찾기 분석은 어떤 한 지점을 기준으로 가장 가까운 위치를 찾는 분석 방법입니다. 이를 통해 특정 지점과 가장 가까운 주변 위치를 식별할 수 있습니다. 이 분석을 사용하여 예를 들면, 가장 가까운 상점이나 호텔, 공원 등을 찾을 수 있습니다. 이 방법은 지리적인 거리를 기준으로 가장 가까운 위치를 찾는데 사용됩니다. 

경계디졸브
경계 또는 중첩되는 영역을 병합하여 단일 영역으로 생성하는 작업을 수행한다.

데이터추출
지정된 관심 영역에 대한 데이터를 선택하여 다운로드하거나 레이어에 추가된다.

공간분할생성
분석 대상 레이어를 입력한 분할타입으로 영역을 분할하여 생성하는 작업을 수행한다.

레이어 병합
동일한 유형의 레이어를 하나의 새로운 레이어로 병합하여 생성하는 작업을 수행한다.

레이어중쳡(지우기)
동일한 유형의 두 레이어의 중첩되는 영역을 제외한 새 레이어로 속성을 포함하여 병합하는 작업을 수행한다.

레이어중쳡(교차)
동일한 유형의 두 레이어의 교차되는 영역을 새 레이어로 속성을 포함하여 병합하는 작업 을 수행한다.

레이어중쳡(유니온)
동일한 유형의 두 레이어를 새 레이어로 속성을 포함하여 교차되는 영역은 자르기 후 병합 하는 작업을 수행한다.

클러스터링
포인트 레이어의 위치 근접도를 분석하여 그룹으로 묶어 포인트수를 시각화하는 작업을 수행한다.

면적계산
대상 레이어 내 피처들의 실제 면적과 실제 둘레 길이를 측정하는 작업을 수행한다.

길이계산
대상 레이어 내 피처들의 실제 길이를 측정하는 작업을 수행한다.

파일좌표변환
Shape(ZIP), GeoJSON 파일 형태의 파일을 업로드하여 원하는 좌표계로 변환 후 다운로 드하는 기능을 수행한다.

단일좌표변환
사용자가 입력한 좌표계의 좌표를 원하는 좌표계로 변환하는 작업을 수행한다.

도시 교통 혼잡도 분석
도시 연동화로 수도권 및 지방 대도시 지역의 인구 집중이 증가하면서 광역대도시권이 형성되었지만, 이로 인해 주택, 교통, 환경 등 도시문제가 심화하고 있습니다. 특히 대도시권의 광역교통수요와 시설 공급의 불균형으로 인한 교통 문제는 경제적인 장애로 작용한다. 

직장인구 분석
서울시 직장인구에 대한 핫스팟 분석 결과를 보여준다. 직장인구분석 지도는 서울시에서 어느 지역에 직장인들이 밀집되어 있는지를 적색으로 보여주고 밀집되어 있지 않은지를 청색으로 표시하여 보여준다.

소득인구 분석
서울시의 소득인구에 대한 핫스팟 분석 결과를 보여준다. 소득인구분석 지도는 서울시에서 어느 지역에 소득이 높은 인구가 밀집되어 있는지를 적색으로 보여주고, 그렇지 않은 지역에 대해서는 청색으로 표시하여 보여준다.

지역별 인구 이동 패턴 분석
도시의 중심인 서울대도시권에서 인구, 사업체, 주택 등의 집중으로 인해 통행 및 경제활동 상호작용이 늘어나면서 특히 아침 7시에서 9시 사이에 통근이 집중되는 현상이 심화하고, 이로 인한 과도한 교통혼잡 문제가 발생하고 있다. 이를 해결하기 위해 신도시 개발과 광역교통 네트워크 확장 등 다양한 도시계획 사업을 추진하며 주거 및 업무 공간 재배치를 유도하고자 한다. 이를 위해 도시공간 구조와 통근 패턴 간의 관계 분석이 필요하며, 이 연구에서는 서울시의 공간 구조를 고려하여 서울대도시권의 통근 패턴 분석이 필요하다.

재난 시뮬레이션을 통한 피난 경로 최적화
도시의 복합시설 등 대규모 콤플렉스 개발이 증가되고 실내 동선이 복잡해지고 있는 가운데 방문자는 복잡한 공간의 동선을 기억하고 행동하기에는 어려움이 있으며, 현행 법규상 화재시 출입구와 엘리베이터 및 계단 주변에 대피로를 안내해주는 피난안내도가 있으나 공간의 복잡도가 증가하는 경우 대피로를 이해하고 기억하며 대응하는 데는 쉽지만은 않다. 이를 위한 피난 경로의 최적화가 필요하다.

자원 배치를 위한 최적 위치 분석
치수대책이 취약한 중소규모 하천 상류에서 갑작스런 홍수 발생 시, 홍수파와 토석류로 인한 2차 피해가 우려되며, 홍수저류지 도입으로 이러한 피해를 줄이고자 한다. 홍수저류지의 효과 극대화를 위해 적절한 위치 선정이 중요하다. 주요 보호 대상인 목표지점 결정하고 지형, 지질, 사태 위험도 등 고려하여 설치 후보지를 선정한다. 강우 및 홍수 분석을 통해 최적 위치와 개소수 결정하며, 계획홍수량 이상의 호우 대비를 위해 제한한다. 결정한 홍수저류지가 목표지점 뿐만 아니라 유역 전체 홍수저감에 기여하는지 검토하고, 이로써 홍수저류지는 유역상류의 치수대책 중 하나로 유용하며, 유역분담형 치수 방안 중의 하나로 활용될 수 있다.

지역별 홍수재해 위험도 분석
본 연구는 통합 홍수위험관리를 위해 홍수위험지구 우선순위를 결정하기 위한 방안을 탐구했다. P-S-R 구성 체계를 활용하여 홍수피해 영향을 미치는 요인들을 분류하고, 12개 세부평가지표를 사용하여 홍수위험도를 평가하였다. GIS를 활용하여 지리적 자료를 구축하였고, 지표평균법과 PROMETHEE 방법을 활용하여 홍수위험도를 분석하고 결정했다. 홍수위험도 결과를 기존 연구와 비교하여 홍수위험지구를 선정한다.

소매점 위치 선정을 위한 마켓 분석
최근 경제성장으로 소비 변화가 나타나고 있으며, 인터넷 쇼핑의 편리성으로 소비 수요가 늘어나고 있다. 하지만 대규모점포의 다점포화로 인한 경쟁은 유통기업과 중소유통업체의 어려움을 증가시키고 지역경제 침체와 양극화를 야기하고 있다. 이에 신규 점포와 기존 점포의 위치 선정은 더욱 중요해지고 있다. 서울지역에서는 대형점포와 SSM 입점으로 지역적 갈등과 양극화가 심화되고 있으며, 대규모점포의 효율성 분석을 통해 서울시의 경영성과가 높음을 확인했다.

주택 시장 변동성 분석
도시 토지 투기로 인한 높은 거래가 경제와 환경에 나쁜 영향을 미친다. 정부 기관은 도시 개발 계획을 위해 토지 투기를 통제하기 위한 정책을 도입하고 있으나, 신속한 관심 지역 감지가 어려움이 있다. 본 연구에서는 실시간 데이터를 활용하여 토지 투기의 시공간적 핫스팟을 빠르게 감지하는 프레임워크를 소개, 한국 화성시를 사례로 적용하여 정남, 봉담, 마도, 동탄 등 지역을 높은 거래 가치로 핫스팟으로 식별한다.

공유 E-스쿠터의 위치 최적화
교통수단 과밀도로 인한 대기오염은 도로이동 오염원으로 인식된다. 공유 E-스쿠터는 도시교통체증과 마지막 이동 거리를 위해 유용하지만 부적절한 사용습관과 폐차 과정의 탄소배출로 환경오염 증가. 이에 배터리 과소사용 문제와 장거리 사용을 해결하기 위해 공유 E-스쿠터의 위치 재분배로 탄소배출 감소를 위한 Zero-carbon Supply Chain Network (ZSCN) 최적화 모델 제안한다. 모델은 온실가스 배출량 감소와 비용 절감을 검증하며 유전알고리즘 방법으로 최적화를 수행한다.

공공 와이파이 입지 최적화
최근 모바일 기기와 무선 정보통신 기술의 발달은 유선 기술의 제약을 극복하며 우리 삶을 혁신하고 있다. 하지만 모바일 환경에서의 격차 문제도 빈틈이 있다. 이에 본 연구에서는 모바일 격차를 정량적으로 측정하는 Mobile Divide Index를 개발하여 국내 지역별 격차를 분석하고, 공공 와이파이 AP 위치 최적화를 통한 격차 완화 방안을 제안하였다. 이를 통해 모바일 격차의 현실적 문제를 다루고 솔루션을 검증하였다.

지리적인 범죄 패턴 분석
경찰 112 신고대응체계는 시민의 안전을 보호하는 중요한 역할을 하며, 이에 대한 데이터인 112 신고자료는 상호작용과 패턴을 파악하는 데 큰 가치가 있다. 본 연구에서는 112 신고자료를 빅 데이터 분석의 탐색적 자료 분석(Exploratory Data Analysis)으로 분석했다. Python의 Pandas와 QGIS를 활용하여 변수 간 관계를 탐색하고 지리적 상관성을 확인했다. 이를 통해 귀납적인 패턴을 발견하고 향후 경찰서의 의사결정에 활용할 수 있는 문제를 제시했다. 경찰서에서 범죄와 사회문제 대응을 위해 탐색적 자료 분석을 적극적으로 활용하는 것을 추천한다.

GIS 네트워크 분석을 통한 서울시 노인복지시설 공급 및 입지 특성 연구
이 연구는 서울시에서 GIS 네트워크 분석을 사용하여 노인복지시설의 접근 어려운 지역을 찾고, 클러스터 분석으로 노인복지시설 분포를 조사했다. 노인복지시설 종류별로 분포 차이가 있었으며, 주거시설은 부족한 편이고 의료시설은 외곽에 집중됐다. 접근성 불균형이 존재하며, 인구 밀집지역에서도 낮은 접근성이 문제였다. 수요와 공급을 고려한 군집화 결과, 입지 계획과 접근성 개선이 필요한 지역이 드러났다. 

GIS 기반 로컬상권 형성에 따른 점포집적 특성
성수동의 레스토랑 사례를 통해 지역 상권 형성과 가게 집중의 특성을 조사했다. 2012년엔 뚝섬과 성수역 주변에 집중, 2017년엔 서울숲2길 주변으로 새로운 지역상권 형성, 2022년엔 서울숲2길에서 더 두드러진 집중 확인. 레스토랑 수는 2012년 903개, 2017년 1,157개, 2022년 1,766개로 증가하고 있다. 한식, 술집/치킨 수는 유지되지만 양식, 외국음식, 일식 레스토랑과 감성주점이 크게 증가하였다. 소상업자는 사업시 위치와 업종 선택 시 카페, 양식/외국음식 레스토랑, 감성주점 중심으로 개업이 필요할 것이다.

GIS를 활용한 장애인사회복지시설의 입지 분석
한국의 인구 감소에도 불구하고 장애인 인구는 증가하며 복지 서비스 수요를 고려해야 한다. 이 연구의 목적은 장애인을 위한 복지센터 추가 설치 계획에서 우선 설치 지역 정보를 제공하여 복지 서비스의 불균형을 줄이는 것이다. 장애인 복지센터의 우선 설치 지역은 네트워크 분석을 통해 도출된다. 연구 결과, 많은 지역에서 복지 서비스 부족이 확인되었다.

GIS를 활용한 전기차 급속충전소 최적 입지 선정에 관한 연구
국내 전기차 등록은 증가 추세이며 향후 급증할 것으로 예상되며, 전기차 인프라 확충 필요성이 부각되고 있다. 특히 충전소 수요가 높아 급속충전소의 부족이 두드러지고 있다. 급속충전소를 효과적으로 확충하기 위해 본 연구에서는 GIS 데이터와 공간분석을 활용하여 최적입지를 탐색하였습니다. 현재 18,895개의 완속충전소와 7,946개의 급속충전소가 설치되어 있으며, 사용자 편의성을 고려하여 급속충전소 확충이 더욱 효과적임을 제안한다.

빈집을 활용한 생활SOC 확충방안
이 연구는 빈집을 활용한 생활SOC(사회복지시설)의 확충 방안을 제시합니다. 빈집의 활용을 위해 GIS 기반 입지 결정 지원체계를 구축하고, 경기도 부천시를 사례로 입지 효율성과 형평성을 고려하여 빈집의 입지 우위를 분석한다. 건강지원센터, 국공립어린이집, 노인여가복지시설, 작은도서관으로 빈집 활용을 제한했으며, 결과적으로 빈집의 입지 우위는 시설별로 다르게 나타났다. 복합화된 시설의 입지 우위는 구시가지에 상대적으로 높았고, 이는 생활권 내 복합화 공급의 타당성을 보여준다. 빈집 활용 입지 결정 지원체계의 예시도 제시되었으며, 이 연구는 빈집 활용 방안에 대한 논의를 위한 GIS 기반 입지 결정 지원체계의 유용성을 입증했다.

도심 보행을 위한 GIS 보행 불균형 평가체계
보행 환경의 질을 향상시키기 위해 품격 있는 보행 공간을 조성하는 중요성이 강조된다. 이에 본 연구는 보행 길 평가체계를 개발하여 품격 있는 보행 공간 조성에 기여하고자 한다. 다양한 요인과 공간을 고려하여 기본 체계를 설정하고, 관련 분야의 동향을 살펴보았다. 보행 안전성, 편의성, 건강성, 매력성 등 여러 항목을 종합하여 평가 체계를 설정하고, 불균형 측정 방법을 개발하였다.

제주도 교통량 예측 및 관광지 추천을 위한 관광 데이터 분석
본 논문은 관광객들이 많이 찾는 지역에서의 교통 혼잡 문제를 해결하기 위해, 제주 관광지를 대상으로 인공지능을 활용한 교통량 예측 및 관광지 추천 시스템을 제안한다. 이를 통해 관광객들이 시간대와 지역을 분산시켜 교통 혼잡을 줄이고, 교통이 적은 시간대와 관광객의 선호도를 고려하여 최적의 관광지를 추천함으로써 사용자에게 효율적인 여행 경험을 제공할 수 있다.

클러스터링을 통한 자전거 도로 분석
현재 개인의 건강을 위한 야외 활동이 증가하며 자전거 이용이 늘어나고 있는데, 이로 인한 사고 위험이 증가하고 있다. 이를 방지하기 위해 본 연구는 자전거 도로 상의 위험한 물체를 감지하고, 클러스터링을 통해 도로 상태를 분석한다. 위험한 지점을 알려주어 사고를 예방하는 것이 목표이다. 사고 위험이 있는 물체는 맨홀, 포트홀, 빗물받이 등이며, 위치 데이터를 수집하여 분석하였다. 결과는 지도와 함께 표현되어 도로의 위험 지점과 상태를 확인할 수 있다.

교통 사고 위험 지역 분석
교통사고는 사회적으로 큰 영향을 미치는 문제로, 자동차 등록과 운전면허 소지자 증가로 심각해지고 있다. 기존 노력은 효과적이지 않아, 교통사고 유형별 위험요인을 파악하여 교통안전시설을 효율적으로 개선해야 한다. 이를 위해 교통사고 발생지점을 지도에 시각화하고 교통사고 패턴의 공간군집 여부를 시공간으로 확인하였다. 대구 지역 교통사고 자료를 통해 유형별 위험요인과 기상자료의 영향을 분석하여 교통사고 발생 위험요인을 파악하였다.

도시 공원 및 녹지공간 불균형 분석
본 연구는 대구광역시의 녹지 불균형을 이용적 측면에서 분석하고, 녹지의 서비스 공급량에 기반하여 관리권역을 설정하는 것을 목표로 한다. 대구의 녹지 총량은 48,936.1ha(55.4%)로 녹지점유비율이 7개 광역시 중 2위로 나타났다. 행정구 및 행정동별 녹지의 지니계수는 불균형이 크지 않지만, 인구대비 지니계수는 심각한 불균형을 보여준다. 특히 달서구에서 녹지의 서비스 공급량을 평가한 결과, 약 100m 내에 많은 녹지가 공급되며 100~200m 지역에서도 녹지가 제공되지만 일부 지역은 적은 녹지를 보유한다. 이 결과를 통해 녹지의 관리방향을 설정하였으며, 이는 도시 및 녹지계획에 활용될 것이다.

화재 위험도 분석을 통한 소방서 위치 선정
본 연구는 대규모 화재 위험 대비를 위해 소방시설 확충과 설립을 목적으로 한다. 기존 연구들은 정성적이거나 정량적인 기준이 부족해 이에 대한 연구 부족을 확인하여 진행하였다. 지도데이터와 GIS를 활용하여 지형을 기준으로 하였으며, 인천광역시의 도로네트워크와 지리 데이터를 사용하여 모델을 개발하였다. Double Covering 모델을 통해 시설 수 최소화와 수요 노드 커버 최대화를 추구하며, 최소 및 최대 커버 가능 시간 내에 노드 도달 제약을 고려한다. 이 모델을 활용하여 인천광역시의 소방시설 위치를 선정하는데 활용할 수 있을 것으로 예상된다.

-----------------------------------

1. 도시 교통 혼잡도 분석
   - 교통 흐름 데이터
   - 도로 네트워크 및 신호 데이터
   - 대중교통 이용량 데이터
   - GPS 기록 데이터
   - 교통 사고 통계 데이터

2. 지역별 인구 이동 패턴 분석
   - 이동성 조사 데이터
   - 통신 데이터 (핸드폰 위치 추적 데이터)
   - 대중교통 카드 이용 데이터
   - GPS 기록 데이터
   - 주민등록 데이터

3. 재난 시뮬레이션을 통한 피난 경로 최적화
   - 지형 데이터 (고도, 지형 정보)
   - 인구 분포 데이터
   - 도로 네트워크 데이터
   - 재난 발생 가능성 데이터
   - 대피소 및 시설 위치 데이터

4. 자원 배치를 위한 최적 위치 분석
   - 자원 종류에 따른 수요 데이터
   - 경쟁 업체 현황 데이터
   - 지역별 인프라 및 시설 데이터
   - 인구 밀도 데이터
   - 경제 지표 데이터

5. 지역별 자연재해 위험도 분석
   - 지진, 홍수, 산사태 등 위험 지역 데이터
   - 기후 데이터 (강수량, 온도 등)
   - 지형 데이터 (고도, 지형 정보)
   - 인구 분포 데이터
   - 건물 및 인프라 데이터

6. 소매점 위치 선정을 위한 마켓 분석
   - 경쟁 업체 위치 데이터
   - 인구 밀도 및 소비 패턴 데이터
   - 경제 지표 데이터
   - 지역별 소매 시장 규모 데이터
   - 인구 성향 조사 데이터

7. 여행자 이동 경로 시각화와 분석
   - 여행자 이동 데이터 (GPS 기록 등)
   - 관광명소 위치 데이터
   - 교통 수단 이용 데이터
   - 사진 공유 서비스 데이터 (지리 태그 정보)
   - 여행 리뷰 및 평가 데이터

8. 환경 오염 지역 탐지 및 모니터링
   - 대기 오염 데이터 (미세먼지, 오존 등)
   - 수질 오염 데이터
   - 공장 및 산업 시설 위치 데이터
   - 대기 조성 모니터링 데이터
   - 인구 밀도 및 건물 데이터

9. 주택 시장 변동성 분석
   - 주택 가격 데이터
   - 거래 기록 데이터
   - 경제 지표 데이터
   - 인구 변동 데이터
   - 주택 시장 전망 데이터

10. 농작물 생산량 예측 및 최적 경작지 선정
    - 작물 생육 데이터
    - 기후 데이터 (강수량, 온도 등)
    - 토양 조사 데이터
    - 농지 지형 및 토평도 데이터
    - 시장 수요 및 경기 데이터

11. 도시 인프라 개선을 위한 시설 위치 최적화
   - 기존 인프라 위치 데이터
   - 인구 분포 및 성장 추이 데이터
   - 교통 네트워크 및 혼잡도 데이터
   - 주거 밀도 및 주택 유형 데이터
   - 재난 대비 시설 배치 기준 데이터

12. 지역별 대기 오염 정도 분석
   - 대기 오염 데이터 (미세먼지, 이산화질소 등)
   - 산업 시설 및 교통 데이터
   - 기후 조건 데이터
   - 인구 밀도 및 교통량 데이터
   - 환경 보호 정책 및 규제 데이터

13. 지리적 인구 분포와 사회경제 특성 연관성 분석
   - 인구 밀도 및 인구 통계 데이터
   - 소득 분포 데이터
   - 교육 수준 및 직업 데이터
   - 주거 밀도와 부동산 시장 데이터
   - 병원, 학교 등 시설 접근성 데이터

14. 지역별 건강 지표와 의료 시설 접근성 분석
   - 건강 지표 데이터 (평균 수명, 질병 발생률 등)
   - 의료 시설 위치 및 종류 데이터
   - 인구 연령대 및 건강 습관 데이터
   - 의료 서비스 이용률 데이터
   - 의료 보험 및 건강 관련 정책 데이터

15. 도로 네트워크 최적화를 위한 도로 형태 분석
   - 도로 데이터 (길이, 너비, 도로 종류 등)
   - 교통 흐름 및 혼잡도 데이터
   - 인구 분포 및 이동 패턴 데이터
   - 도로 유지보수 및 개선 기록 데이터
   - 교통 정책 및 규제 데이터

16. 지리적인 범죄 패턴 분석
   - 범죄 발생 데이터 (종류, 위치, 시간 등)
   - 인구 밀도 및 주거 분포 데이터
   - 경찰서 및 치안 시설 위치 데이터
   - 사회경제 지표 및 소득 수준 데이터
   - 범죄 예방 프로그램 및 정책 데이터

17. 자연 보존 지역 선정을 위한 생태학적 분석
   - 생태학적 보호 대상 종 데이터
   - 지형 및 지리 정보 데이터
   - 자연 보전 지역 및 국립공원 데이터
   - 인구 밀도와 인프라 데이터
   - 생태학 및 생물 다양성 보고서 데이터

18. 지역별 주택 가격 예측 및 투자 분석
   - 주택 가격 데이터 및 거래 내역
   - 경제 지표 및 금융 시장 데이터
   - 인구 변동과 이동 패턴 데이터
   - 부동산 시장 동향 분석 데이터
   - 주택 시장 예측 모델 및 리포트 데이터

19. 물류 네트워크 최적화를 위한 창고 위치 분석
   - 창고 위치 및 용량 데이터
   - 물류 흐름 및 공급망 데이터
   - 주변 교통 및 인프라 데이터
   - 재고 관리 및 주문 데이터
   - 경쟁 업체 및 시장 동향 데이터

20. 관광 명소 추천 시스템 구축을 위한 관광 데이터 분석
   - 관광명소 위치 및 평가 데이터
   - 지역별 인기 여행지 데이터
   - 관광객 도착 및 이용 패턴 데이터
   - 문화 이벤트 및 축제 일정 데이터
   - 관광 관련 리뷰 및 소셜 미디어 데이터

21. 지역 사회 경제 발전을 위한 기업 입지 분석
   - 기업 위치 및 종류 데이터
   - 지역별 인프라 및 교통 네트워크 데이터
   - 인구 밀도와 직업 구조 데이터
   - 경제 지표 및 지원 정책 데이터
   - 경쟁 업체 현황 및 시장 점유율 데이터

22. 지역별 학교 및 교육 시설 접근성 분석
   - 학교 및 교육 시설 위치 데이터
   - 학생 인구 및 학업 성적 데이터
   - 교사 인력 및 교육 프로그램 데이터
   - 부모 및 학생 만족도 조사 데이터
   - 교육 정책 및 교육 환경 데이터

23. 해양 보호구역과 어업 활동 분석
   - 해양 보호구역 및 생태계 데이터
   - 어종 분포 및 어업 활동 데이터
   - 어선 이동 경로 및 어획량 데이터
   - 해양 환경 및 생태계 변화 데이터
   - 어업 정책 및 규제 데이터

24. 자전거 도로 인프라 개선을 위한 경로 분석
   - 자전거 도로 및 자전거 도로망 데이터
   - 교통량 및 안전 사고 데이터
   - 주거 밀도 및 자전거 이용률 데이터
   - 자전거 대여소 위치 및 이용 데이터
   - 도시 자전거 정책 및 계획 데이터

25. 에너지 생산 및 사용 패턴 분석
   - 에너지 생산량 및 원천 데이터
   - 에너지 소비 패턴 및 사용량 데이터
   - 기후 조건 및 에너지 수급 데이터
   - 신재생 에너지 시설 및 분포 데이터
   - 에너지 절약 정책 및 지원 데이터

26. 지역별 문화 시설 밀도와 다양성 분석
   - 문화 시설 종류 및 위치 데이터
   - 문화 이벤트 및 전시회 일정 데이터
   - 인구 밀도와 문화 선호도 데이터
   - 문화 시설 이용 및 만족도 조사 데이터
   - 문화 정책 및 지원 데이터

27. 자연 생태계와 도시 개발의 상충 관계 분석
   - 자연 보전 지역 및 생태계 데이터
   - 도시 개발 및 건설 활동 데이터
   - 생물 다양성 및 종 보호 데이터
   - 토양 오염 및 개발 영향 데이터
   - 환경 보호 및 개발 균형 정책 데이터

28. 지역별 식품 배달 서비스 가능성 분석
   - 인구 밀도 및 소비 패턴 데이터
   - 식품 배달 서비스 이용 데이터
   - 경제 지표 및 소득 수준 데이터
   - 주거 밀도 및 교통 네트워크 데이터
   - 식품 배달 시장 동향 및 성장 예측 데이터

29. 인구 노령화와 노인 복지 시설 분포 분석
   - 인구 노령화 및 고령 인구 비율 데이터
   - 노인 복지 시설 위치 및 종류 데이터
   - 건강 및 의료 서비스 이용률 데이터
   - 노인 거주 환경 및 주거 조건 데이터
   - 복지 정책 및 프로그램 데이터

30. 지역별 태양광 발전소 최적 위치 선정
   - 태양광 발전 데이터 및 일사량 정보
   - 지형 및 지리 정보 데이터
   - 인프라 및 전력 수급 및 수요 데이터
   - 재생 에너지 정책 및 규제 데이터
   - 태양광 발전 설치 및 운영 비용 데이터

31. 지도 학습을 통한 토지 사용 분류
   - 토지 사용 데이터 (농경지, 주거지, 상업지 등)
   - 위성 이미지 및 항공 사진 데이터
   - 지형 및 지리 정보 데이터
   - 지역별 용도 변화 및 발전 데이터
   - 지토 분석 및 토지 관리 데이터

32. 교통 사고 위험 지역 분석
   - 교통 사고 발생 데이터
   - 도로 네트워크 및 교통 혼잡도 데이터
   - 운전 습관 및 교통 안전 데이터
   - 인구 밀도 및 교통량 데이터
   - 교통 안전 정책 및 교육 데이터

33. 지역별 소음 및 환경 손상 분석
   - 소음 레벨 데이터
   - 도로, 공장, 시설 등 소음 원인 데이터
   - 주거 밀도 및 건물 형태 데이터
   - 환경 보호 및 규제 데이터
   - 주민 의견 조사 및 불만 데이터

34. 도시 확장과 자연 생태계 보존의 균형 분석
   - 도시 개발 및 확장 데이터
   - 자연 보전 지역 및 생태계 데이터
   - 지형 및 지리 정보 데이터
   - 생물 다양성 및 생태계 변화 데이터
   - 도시 계획 및 환경 보호 정책 데이터

35. 소셜 미디어 데이터를 활용한 지역 감정 분석
   - 소셜 미디어 게시글 및 해시태그 데이터
   - 감정 분류 및 감정 스코어 데이터
   - 지역별 이벤트 및 사건 데이터
   - 주민 만족도 및 감정 조사 데이터
   - 감정 분석 및 트렌드 예측 모델 데이터

36. 지역별 쓰레기 발생량과 재활용 분포 분석
   - 쓰레기 발생량 및 종류 데이터
   - 재활용 시설 위치 및 처리량 데이터
   - 주민 소비 패턴 및 소비 품목 데이터
   - 환경 보호 및 재활용 정책 데이터
   - 재활용 시설 운영 및 성과 데이터

37. 건축물 거주 여건 분석을 통한 주택 개발 계획
   - 건축물 위치 및 주택 종류 데이터
   - 주거 환경 및 주변 시설 데이터
   - 주택 가격 및 시장 동향 데이터
   - 건축물 건축 연도 및 상태 데이터
   - 주택 개발 및 리모델링 정책 데이터

38. 지역별 농작물 생산 변화와 기후 변화 연관성 분석
   - 농작물 생산량 및 수확 기록 데이터
   - 기후 조건 및 기후 변화 데이터
   - 토양 조건 및 토지 이용 데이터
   - 농업 관련 보조금 및 지원 데이터
   - 기후변화 적응 및 농업 정책 데이터

39. 공공 서비스 시설 접근성과 사회적 격차 분석
   - 공공 서비스 시설 위치 및 종류 데이터
   - 주민 밀도 및 소득 수준 데이터
   - 교통 네트워크 및 접근성 데이터
   - 사회적 취약 계층 및 지원 데이터
   - 공공 서비스 혜택 및 이용 데이터

40. 도시 공원 및 녹지공간 최적 위치 선정
   - 공원 및 녹지공간 위치 및 크기 데이터
   - 인구 밀도 및 주거 환경 데이터
   - 주변 시설 및 교통 접근성 데이터
   - 환경 보호 및 녹지정책 데이터
   - 공원 이용량 및 만족도 조사 데이터

41. 지역 복지 시설과 사회적 취약계층 연관성 분석
   - 사회 복지 시설 위치 및 종류 데이터
   - 사회적 취약계층 특성 및 수요 데이터
   - 인구 밀도 및 소득 수준 데이터
   - 복지 프로그램 이용 및 만족도 데이터
   - 사회복지 정책 및 지원 데이터

42. 지역별 수질 오염 분석
   - 수질 데이터 (물질 농도, 오염 물질 등)
   - 하천 및 강 정보 및 유량 데이터
   - 산업 시설 및 폐기물 처리 시설 데이터
   - 환경 보호 및 규제 데이터
   - 수질 개선 프로그램 및 정책 데이터

43. 화재 위험도 분석을 통한 소방서 위치 선정
   - 화재 발생 데이터 및 위험 요인 데이터
   - 주택 및 건물 위치 및 구조 데이터
   - 주민 수 및 인프라 데이터
   - 소방 시설 위치 및 수용력 데이터
   - 소방 안전 교육 및 예방 정책 데이터

44. 지역별 전기차 충전 인프라 설치 최적 위치 분석
   - 전기차 충전소 위치 및 수용력 데이터
   - 교통 네트워크 및 충전 인프라 데이터
   - 인구 밀도 및 전기차 보급 현황 데이터
   - 에너지 수급 및 대기 오염 데이터
   - 전기차 확대 지원 및 정책 데이터

45. 지역 사업 개발을 위한 경쟁 업체 분석
   - 경쟁 업체 위치 및 종류 데이터
   - 시장 점유율 및 판매 데이터
   - 소비자 성향 및 만족도 데이터
   - 경쟁 업체 인프라 및 서비스 데이터
   - 시장 경쟁력 및 전략 데이터

46. 자연 보존과 관광 산업의 균형 유지 분석
   - 자연 보전 지역 및 생태계 데이터
   - 관광 명소 및 인기 지역 데이터
   - 관광객 도착 및 이용 패턴 데이터
   - 환경 보호 및 관광 규제 데이터
   - 지속 가능한 관광 개발 정책 데이터

47. 식품 배달 로봇 경로 최적화 분석
   - 배달 로봇 위치 데이터
   - 주문량 및 배송 대기 시간 데이터
   - 도로 네트워크 및 교통량 데이터
   - 배달 로봇 운영 및 성과 데이터
   - 로봇 배달 서비스 혜택 및 효율성 데이터

48. 지역별 교통 흐름 분석을 통한 교통체증 예측
   - 교통 흐름 및 혼잡도 데이터
   - 교통 네트워크 및 도로 형태 데이터
   - 인구 밀도 및 출퇴근 패턴 데이터
   - 교통 정책 및 개선 사항 데이터
   - 교통 체증 예측 모델 및 시뮬레이션 데이터

49. 지역별 식품 생산과 소비 패턴 분석
   - 농작물 생산량 및 작물 종류 데이터
   - 식품 유통 및 소비 품목 데이터
   - 주민 섭취 습관 및 식생활 데이터
   - 식품 생산자 및 소비자 인프라 데이터
   - 식품 안전 및 건강 관련 정보 데이터

50. 지역별 환경 보호 및 지속 가능한 개발 정책 분석
   - 환경 보호 정책 및 규제 데이터
   - 지속 가능한 개발 계획 및 방침 데이터
   - 에너지 효율 및 재생 에너지 정책 데이터
   - 환경 오염 및 생태계 변화 데이터
   - 환경 보호와 개발 균형 정책 성과 데이터

도시 교통 혼잡도 분석
도시 연동화로 수도권 및 지방 대도시 지역의 인구 집중이 증가하면서 광역대도시권이 형성되었지만, 이로 인해 주택, 교통, 환경 등 도시문제가 심화하고 있습니다. 특히 대도시권의 광역교통수요와 시설 공급의 불균형으로 인한 교통 문제는 경제적인 장애로 작용한다. 

도시 교통 혼잡도 분석
방법론 방법
사회경제적 데이터를 활용하여 교통 혼잡과 관련된 다양한 요인들을 분석해보는 것이 유익할 수 있습니다. 예를 들어, 지역의 소득 수준, 교육 수준, 자동차 보유 비율 등이 교통 혼잡도에 어떤 영향을 미치는지 알아보는 것입니다. 이렇게 수집된 데이터는 시각화 도구를 사용하여 도출된 결과를 쉽게 이해할 수 있도록 표현하고, 이를 통해 교통 혼잡의 주요 원인과 패턴, 그리고 향후 예측하는데 도움이 될 수 있습니다. 이러한 연구 결과는 도시의 교통 계획을 세우고 교통 혼잡 문제를 해결하는 데에 필요한 정책 결정에 중요한 근거를 제공하게 됩니다. 

교통혼잡도 분석 데이터
도로 네트워크 데이터 
(MapPick)
교통 흐름 데이터 
(TS한국교통안전공단, https://www.kotsa.or.kr/portal/contents.do?menuCode=03030200)
격자별 유동인구 데이터 데이터 
(옵션)
버스 정류장 데이터 
(국가공간정보포털, http://data.nsdi.go.kr/dataset/14875) 
지하철 노선 데이터 
(MapPick) 
지하철역 위치 데이터 
(MapPick)
지하철역 승객 승하차 데이터  
(옵션)
주차장 위치, 용량, 이용 빈도 데이터 
(국가공간정보포털, 공간기반 유/무료 주차장 정보, http://data.nsdi.go.kr/dataset/14734)
건물 데이터 데이터 
(국가공간정보포털, http://data.nsdi.go.kr/dataset/20180927ds0012)
토지용도지역 데이터  
(주거지, 상업지, 산업지 분포, 국가공간정보포털, (연속주제)_국토/용도지역, http://data.nsdi.go.kr/dataset/12659)
인구수 데이터 
(국가공간정보포털, 주거인구,http://data.nsdi.go.kr/dataset/14774)
인구밀도 데이터 
(국가공간정보포털, 집계구별 통계자료(인구), http://data.nsdi.go.kr/dataset/20171206ds00009)	
직장 유동인구 데이터 
(국가공간정보포털, 직장인구, http://data.nsdi.go.kr/dataset/14776)

지역별 인구 이동 패턴 분석
방법론 방법
데이터 분석을 위해 통계적 방법을 사용하며, 다양한 인명적 특성별 차이를 확인하기 위해 분산분석이나 회귀분석과 같은 다변량 분석을 진행할 수 있습니다. 또한, 시간적 차이를 분석하기 위해 시계열 분석을 사용할 수도 있습니다. 지역 특성이나 이동 경로를 분석하기 위해서는 지리정보시스템(GIS)을 이용하여 전입 전출 데이터를 이용하여 거시적인 인구 이동을 파악할 수 있습니다. 이를 위해서는 시점별 핫스팟 분석을 통하여 원하는 결과를 도출할 수 있습니다.

지역별 인구 이동 패턴 분석
필요 데이터
1. 공공 행정 데이터: 지역의 행정 구역, 행정 정보 등
2. 인구 이동 통계 데이터: 인구 이동률, 이동 인구의 성별, 연령, 직업 등
3. 고용 및 경제 데이터: 취업률, 실업률, 지역 내 주요 산업, 근로자 수입 등
4. 교육 데이터: 학교 유형, 학생 수, 지역 내 학교 분포 등
5. 부동산 데이터: 주택 구조, 주택 가격, 임대료, 부동산 판매 및 구매 통계 등
6. 건강 및 의료 데이터: 지역 내 병원과 클리닉의 수와 종류 등
7. 소비자 데이터: 소득 수준, 소비 패턴, 생활비 지출 등
8. 사회 서비스 데이터: 지역 내 복지 서비스 제공 정도, 저소득층 지원, 노인 복지 등.

재난 시 피난 경로 최적화 방법론 방법
도시의 복합시설 등 대규모 콤플렉스 개발이 증가되고 실내 동선이 복잡해지고 있는 가운데 방문자는 복잡한 공간의 동선을 기억하고 행동하기에는 어려움이 있으며, 현행 법규상 화재시 출입구와 엘리베이터 및 계단 주변에 대피로를 안내해주는 피난안내도가 있으나 공간의 복잡도가 증가하는 경우 대피로를 이해하고 기억하며 대응하는 데는 쉽지만은 않습니다. 이를 위한 피난 경로의 최적화가 필요합니다. 실용적인 연구는 통계적, 지리적, 사회경제적 분석 결과를 바탕으로 재난 상황에서 최적의 피난 경로를 설계하고 도시 설계에 반영하는 것입니다. 이상적인 피난 경로는 교통이 원활히 흐르면서도 모든 주민이 쉽고 빠르게 접근할 수 있어야 합니다. 또한 재난 시 발생 할 수 있는 다양한 시나리오를 고려한 다양한 탈출 경로를 제공해야 합니다. 이를 위해 컴퓨터 시뮬레이션, 최적화 알고리즘과 같은 현대적 기술을 사용하여 효율적인 피난 경로를 계획하고, 지역 주민들에게 쉽게 전달할 수 있도록 하는 것이 중요합니다.

재난 시 피난 경로 최적화 필요 데이터
1. 재난 발생 시 기록 데이터: 재난의 종류, 발생 위치, 시간, 규모, 영향 범위 
2. 지형지물 데이터: 지역의 지도, 도로 네트워크, 건물 위치, 고도 
3. 인구 통계 데이터: 누가 어디에 있는지, 그들의 연령, 건강 상태, 이동 능력 
4. 피난소와 구조 시설 위치: 피난 시 안전하게 도착해야 하는 곳의 위치 정보.
5. 과거 재난 데이터: 과거에 발생한 비슷한 재난의 경우, 피난 경로
6. 기상 데이터: 특히 홍수, 허리케인 등 기상 조건
7. 교통 인프라정보: 도로, 다리, 터널, 철도 등의 현재 상태 정보.

소매점 위치 선정을 위한 마켓 분석 방법론 방법
최근 경제성장으로 소비 변화가 나타나고 있으며, 인터넷 쇼핑의 편리성으로 소비 수요가 늘어나고 있습니다. 하지만 대규모점포의 다점포화로 인한 경쟁은 유통기업과 중소유통업체의 어려움을 증가시키고 지역경제 침체와 양극화를 야기하고 있습니다. 이에 신규 점포와 기존 점포의 위치 선정은 더욱 중요해지고 있습니다. 데이터 수집 방법은 조사, 인터뷰, 서베이, 공개자료 분석 등 다양합니다. 이때, 주요 고객의 위치, 경쟁사의 위치, 교통 및 접근성, 인구 밀도, 개인소득수준, 구매세력, 부동산 가격 등 다양한 요인들을 고려해야 합니다. 수집된 데이터는 정량적 및 정성적 분석을 통해 분석됩니다. 이때, 공간분석, 다변량 분석, 회귀분석, 예측 모델링 등 다양한 통계적 기법을 사용하여 데이터의 패턴, 트렌드, 연관성을 찾을 수 있습니다. 

소매점 위치 선정을 위한 마켓 분석 필요 데이터
1. 인구 통계 데이터: 인구밀도, 연령 분포, 성비, 소득 수준 등
2. 위치 데이터: 소매점 후보지의 지리적 위치, 교통 접근성, 주변 환경 (상업지구, 주거지구 등)
3. 소비자 행동 데이터: 구매력, 구매 선호도, 구매 트렌드 등
4. 경쟁업체 데이터: 가까운 지역에 위치한 비슷한 종류의 소매점, 그들의 가격, 서비스, 상품, 고객 대상 등
5. 부동산 데이터: 소매점 위치의 임대료, 건물 유형 및 크기, 건물의 소유주 등
6. 교통 데이터: 주차 공간, 대중교통 현황, 교통량, 교통 체증 정보 등
7. 시장 유동인구 데이터: 고객들의 수요, 고객들의 브랜드 인식도
8. 고객 리뷰 데이터: 이전 고객들의 리뷰, 만족도 등
9. 지역 커뮤니티 데이터: 지역 내 이벤트, 축제 등

주택 시장 변동성 분석 방법론 방법
도시 토지 투기로 인한 높은 거래가 경제와 환경에 나쁜 영향을 미치고 있습니다. 정부 기관은 도시 개발 계획을 위해 토지 투기를 통제하기 위한 정책을 도입하고 있으나, 신속한 관심 지역 감지가 어려움이 있습니다. 본 분석에서는 실시간 데이터를 활용하여 토지 투기의 시공간적 핫스팟을 빠르게 감지하는 프레임워크를 소개, 한국 화성시를 사례로 적용하여 정남, 봉담, 마도, 동탄 등 지역을 높은 거래 가치로 핫스팟으로 식별합니다. 상관성, 회귀 분석, 시계열 분석, 가설 검정 등이 포함될 수 있습니다. 이 단계에서는 데이터를 하나 이상의 통계 모델에 대입하여 어떤 요인이 주택 시장의 변동성에 가장 큰 영향을 미치는지를 판단합니다.

주택 시장 변동성 분석 필요 데이터
1. 주택 가격 데이터: 지역별 주택 가격, 주택 유형에 따른 가격, 과거의 주택 실거래 가격 등
2. 인구 데이터: 인구 증가율, 인구이동, 인구 연령 구조, 월 평균 수입 등
3. 주택 정책 데이터: 부동산 정책, 주택 임대료 조정, 주택 로튼 제도 등
4. 이자율 데이터: 중앙 은행 기준금리, 주택담보대출 이자율 등
5. 지역 정보 데이터: 지역의 특성, 지역내의 교육시설, 병원, 공원 등의 편의시설 위치 및 수 등
6. 행정 구역 데이터: 시, 군, 구 등 행정 단위 정보

공유 E-스쿠터의 위치 최적화 방법론 방법
공유 E-스쿠터는 도시교통체증과 마지막 이동 거리를 위해 유용합니다. E-스쿠터의 사용 통계, 도시의 교통 흐름 및 인프라, 사회경제적 변수 등을 포함할 수 있습니다. 이 과정에서는 GPS 트래킹, 앱 사용 데이터, 공공 교통 데이터, 인구 통계 데이터 등을 사용하여 수집할 수 있습니다. 수집된 데이터를 분석하여 패턴과 경향을 찾는 것입니다. 정량적 분석과 더불어, 기계 학습 알고리즘과 GIS(지리정보시스템) 기반 분석이 이용될 수 있습니다. 이를 통해 수요 예측, 최적화 모델 등을 개발할 수 있습니다.

공유 E-스쿠터의 위치 최적화 필요 데이터
1. E-스쿠터 사용 데이터: 스쿠터의 사용량, 사용자의 승하차 위치, 시간대별 사용 패턴 등
2. 지리정보시스템(GIS) 데이터: 도시의 경로, 도로 레이아웃, 교통 체증 지역, 스쿠터 주차 가능 위치, 충전소 위치
3. 날씨 데이터: 기온, 강수량, 날씨 조건 등
4. 도시 데이터: 인구 밀도, 공공 시설 위치, 주거지와 사무실 지역 등
5. 경쟁사 데이터: 경쟁 E -스쿠터 회사의 가격, 서비스, 위치 등
6. E-스쿠터 성능 및 유지보수 데이터: E-스쿠터의 배터리 수명 등.

공공 와이파이 입지 최적화 방법론 방법
최근 모바일 기기와 무선 정보통신 기술의 발달은 유선 기술의 제약을 극복하며 우리 삶을 혁신하고 있습니다. 하지만 모바일 환경에서의 격차 문제도 빈틈이 있습니다. 이에 본 분석에서는 모바일 격차를 정량적으로 측정하는 Mobile Divide Index를 개발하여 국내 지역별 격차를 분석하고, 공공 와이파이 AP 위치 최적화를 통한 격차 완화 방안을 분석할 수 있습니다. machine learning 기법, 회귀분석, 공간 분석, 예측 모델링 등 다양한 통계적 기법을 도입하여 데이터를 분석합니다. 이는 최적의 와이파이 입지를 도출하는 데 사용합니다. 

공공 와이파이 입지 최적화 필요 데이터
1. 공공 와이파이 사용 데이터: 와이파이 사용 시간, 와이파이 사용자 수, 와이파이 데이터 사용량 등
2. 지리 정보 데이터: 인구밀도, 건물 분포, 공공시설 및 도로 배치 등
3. 와이파이 신호 품질 데이터: 신호 도달 범위 등
4. 인구 통계 데이터: 연령, 성별, 직업, 소득 수준 등
5. 기술 데이터: 와이파이 기술의 성능, 스펙, 비용 등

지리적인 범죄 패턴 분석 방법론 방법
경찰 112 신고대응체계는 시민의 안전을 보호하는 중요한 역할을 하며, 이에 대한 데이터인 112 신고자료는 상호작용과 패턴을 파악하는 데 큰 가치가 있습니다. 본 분석에서는 112 신고자료를 빅 데이터 분석의 탐색적 자료 분석(Exploratory Data Analysis)으로 분석할 수 있습니다. 공간 분석, 통계 분석, GIS(Geographic Information System) 도구 등을 사용하여 데이터에서 패턴을 찾고 변수 간 관계를 탐색하고 지리적 상관성을 확인할 수 있습니다. 이를 통해 범죄가 어디서, 언제, 왜 발생하는지에 대한 인사이트를 얻을 수 있습니다. 

지리적인 범죄 패턴 분석 필요 데이터
1. 범죄에 관한 데이터 : 범죄 발생 위치, 범죄 종류, 발생 시각, 범죄 발생 일자, 피해자 성별, 연령 그룹, 범죄자 성별, 연령 그룹 등의 다양한 범수령 정보와 함께 기록된 범죄 데이터.
2. 지리적 데이터 : 구역별, 도시별, 도로별, 지역별 인구수, 환경 특성(예: 산업구조, 임업 지역 비율, 상업 지역 등)
3. 범죄 예방 리소스 또는 관련 인프라 데이터 : 경찰서 위치, 보안 카메라 위치, 보안 요원

GIS 네트워크 분석을 통한 서울시 노인복지시설 공급 및 입지 특성 분석 방법론 방법
이 분석는 서울시에서 GIS 네트워크 분석을 사용하여 서울시내 노인복지시설 공급 부족 문제를 파악하고, 입지 특성을 통해 노인복지시설의 최적 위치를 제안하는 것입니다. 노인 인구 밀도, 현재 노인복지시설의 분포 및 공급 상태, 지역별 인프라, 이동성 등과 같은 다양한 변수에 대한 데이터 수집이 필요합니다. 해당 데이터는 정부 통계데이터, GIS(지리정보시스템) 데이터 등을 활용해 수집합니다. 접근성 불균형이 존재하며, 인구 밀집지역에서도 낮은 접근성이 문제였다. 노인복지 시설의 현재 상태를 분석하고, 노인 인구 밀도와 시설 공급량 등의 변수 간의 상관관계를 분석합니다. 이를 통해 노인복지시설이 부족한 지역, 공급이 과다한 지역 등을 도출합니다. 

GIS 네트워크 분석을 통한 서울시 노인복지시설 공급 및 입지 특성 분석 필요 데이터
1. 서울시 노인복지시설의 위치 정보 (위도, 경도) 
2. 각 노인복지시설의 용도 및 종류 정보 
3. 각 노인복지시설의 사이즈, 용량, 시설 별 서비스 범위 
4. 노인 인구 통계데이터(나이, 성별, 거주지 등)
5. 지역별 노인 가구 수 
6. 서울시 지도 및 도로 네트워크 데이터
7. 노인 복지 시설 이용자 수
8. 공공 교통 수단 및 교통 네트워크 정보(버스, 지하철 노선)

GIS 기반 로컬상권 형성에 따른 점포집적 특성 방법론 방법
성수동의 레스토랑 사례를 통해 지역 상권 형성과 가게 집중의 특성을 조사해 봅시다. 2012년엔 뚝섬과 성수역 주변에 집중, 2017년엔 서울숲2길 주변으로 새로운 지역상권 형성, 2022년엔 서울숲2길에서 더 두드러진 집중 확인. 레스토랑 수는 2012년 903개, 2017년 1,157개, 2022년 1,766개로 증가하고 있습니다. 이 기간 한식, 술집/치킨 수는 유지되지만 양식, 외국음식, 일식 레스토랑과 감성주점이 크게 증가하였습니다. 공간 분석 결과를 바탕으로 상권의 특성을 파악은 소상공인에게 매우 중요한 정보를 제공합니다. 이러한 결과는 점포 밀도, 상점 유형의 다양성, 지역 내/외부의 점포 이동 흐름 등의 데이터를 이용하여 분석할 수 있습니다. 

GIS 기반 로컬상권 형성에 따른 점포집적 특성 필요 데이터
1. 상권정보: 상권의 위치, 종류, 크기, 특성 등
2. 점포 정보: 점포 종류, 점포 크기 
3. 교통 데이터: 도로 네트워크, 교통 편의 시설 위치, 주차 가능 차량 수
4. 인구조사 정보: 인구밀도, 연령 구성, 성별 비율, 소득 수준, 유동인구
5. 경제 정보: 지역의 경제 상황, 소비력, 소비 패턴 등

GIS를 활용한 장애인사회복지시설의 입지 분석 방법론 방법
한국의 인구 감소에도 불구하고 장애인 인구는 증가하며 복지 서비스 수요를 고려해야 합니다. 이 분석의 목적은 장애인을 위한 복지센터 추가 설치 계획에서 우선 설치 지역 정보를 제공하여 복지 서비스의 불균형을 줄이는 것이다. 장애인 복지센터의 우선 설치 지역은 네트워크 분석을 통해 도출될 수 있습니다. 수집된 데이터를 토대로 공간정보 분석(밀도 분석)을 수행합니다. 이를 통해 장애인사회복지시설이 필요한 곳의 우선 순위와 설치 위치를 확정할 수 있습니다.

GIS를 활용한 장애인사회복지시설의 입지 분석 필요 데이터
1. 장애인사회복지시설의 위치 정보 (위도, 경도)
2. 장애인사회복지시설의 용도, 그에 따른 시설 규모 및 설비 정보
3. 장애인사회복지시설을 이용하는 장애인의 수와 그들의 장애 유형, 정도
4. 지리 공간 데이터 (GIS 데이터), 지리적 특성 데이터 (지형, 지상물 등)
5. 인구조사 데이터 (인구 밀도, 연령 구조, 성별, 소득 수준 등)
6. 교통 데이터 (대중교통 위치, 부지 내 주차장 현황 등)
7. 안전 데이터 (장애인사회복지시설 주변의 안전 시설 위치, 안전 사고 발생 데이터 등)
8. 지역의 의료 시설 및 교육

GIS를 활용한 전기차 급속충전소 최적 입지 선정에 관한 분석 방법론 방법
전기차 급속충전소 최적 입지 선정은 상호 상관성 있는 다양한 영향 요인을 고려해야 하기에 다중 기준 결정 분석 (Multi-Criteria Decision Analysis, MCDA)을 사용할 것입니다. 먼저, 입지 선정에 영향을 미치는 요인들 (예: 전기차 보급률, 교통량, 전력 인프라, 주변 시설 등)을 탐색적 데이터 분석으로 파악합니다. 다음으로, 각 요인들의 중요성을 측정하기 위해 전문가나 대상 집단에게 설문을 수행하고, 상대적 중요도는 랭킹, 그리고 가중치를 부여하는 방법으로 결정합니다. 이후, 지리 정보 시스템(GIS)을 이용하여 각 후보지에 대한 위 요인들 간의 상관성을 분석하고, 사용자 정의 함수를 활용해 최적의 입지를 선정합니다. 이 방법론은 복잡하고 상호연관성 있는 요소들을 종합적으로 고려하여 전기차 급속충전소의 최적 입지를 제안하게 됩니다.

GIS를 활용한 전기차 급속충전소 최적 입지 선정에 관한 분석 필요 데이터
1. 지역별 전기차 보급 현황: 전기차 등록 수, 모델별 보급 현황, 제조사별 보급 현황 등
2. 급속 충전소 현황: 충전소 위치, 충전 속도, 충전소 이용률 등
3. 지리 정보: 도로망 정보, 주변 시설 정보, 지역별 인구 밀도 등
4. 교통량 데이터: 주요 도로의 교통량, 교통 흐름 패턴, 교통 체증 지역 등
5. 소비자 행동 패턴: 전기차 소비자의 이동 패턴, 충전 시간 선호도, 충전 지점 선호도 등

빈집을 활용한 생활SOC 확충방안 방법론 방법
이 분석는 빈집을 활용한 생활SOC(사회복지시설)의 확충 방안을 제시합니다. 이 연구는 혼합 방법론을 채용하여 진행될 수 있습니다. 우선 수량적인 방법을 통해 빈집의 수와 위치, 그리고 주변 환경을 파악합니다. 이는 국토 정보 시스템을 이용하거나 행정 데이터를 활용하여 진행할 수 있습니다. 또한, 질적인 방법으로 주민들의 생활 패턴, 빈집의 활용에 대한 의견, 그리고 생활 SOC 구축에 대한 니즈를 파악합니다. 이는 인터뷰, FGI (Focus Group Interview), 설문조사 데이터 등을 통해 추출할 수 있습니다. 이러한 질적, 수량적 분석 결과를 바탕으로 빈집을 활용한 생활 SOC 확충방안을 SWOT 분석하여, 실질적인 방안을 제시합니다. 이를 통해 체계적이며 실용적인 연구 결과를 내놓을 수 있으며, 이는 빈집 문제 해결과 동시에 지역 사회의 생활환경 개선에 기여할 것입니다.

빈집을 활용한 생활SOC 확충방안 필요 데이터
1. 세대별 빈집 현황: 빈집 위치, 빈집 계약 상태 (임대 중, 판매 중 등), 빈집 크기, 구조 등
2. 빈집을 활용할 수 있는 적합한 프로그램 혹은 활동: 노인 장애인 등 특정 가구의 주거 활동, 커뮤니티 활동, 복지 프로그램 등
3. 지역 사회 구성원들의 의견: 빈집 활용에 대한 지역 주민들의 생각, 제안 등
4. 전국 또는 특정 지역의 빈집 재활용 사례: 사례별 프로그램 내용, 참여자 수, 재활용 효과 등

도심 보행 불균형 평가체계 방법론 방법
보행 환경의 질을 향상시키기 위해 품격 있는 보행 공간을 조성하는 중요성이 강조됩니다. 이에 보행 불균형 분석는 보행 길 평가체계를 개발하여 품격 있는 보행 공간 조성에 기여하고자 한다. 도심 보행 불균형 평가체계의 연구 방법론은 주로 수량적 접근법 및 질적 접근법의 혼합을 추천합니다. GIS(지리정보시스템)를 사용하여 도시의 보행로 배치를 파악하고, 보행 가능성과 여가 시설의 접근성을 분석합니다. 또한, 설문조사나 인터뷰를 통해 시민들의 보행 경험을 이해하고, 보행 장애물과 이동성에 관한 사회경제적 변수를 분석하게 됩니다. 이런 과정을 통해 어떤 지역이 보행 불균형을 경험하고 있는지, 그 원인은 무엇인지를 탐색합니다. 

도심 보행을 위한 GIS 보행 불균형 평가체계 필요 데이터
1. 도심 지역의 지리적 데이터 : 지역의 경계, 행정구역, 건물 및 도로의 위치와 크기, 공공시설의 위치 등
2. 보행자 불편 지수 데이터: 보행 위험도, 보행 속도, 보행 환경의 만족도 등
3. 통계 데이터: 인구 분포, 연령별, 성별 분포, 교통 사고 통계 등
4. 보행로 관련 시설 데이터: 보행로 표면 재질, 보행로 폭, 접근성, 조명, 가로수, 장애물 등
5. 교통량 데이터: 보행 도로의 유동인구 데이터

제주도 관광지 추천을 위한 관광 데이터 분석 방법론 방법
본 분석은 관광객들이 많이 찾는 지역에서의 교통 혼잡 문제를 해결하기 위해, 제주 관광지를 대상으로 인공지능을 활용한 교통량 예측 및 관광지 추천 시스템을 제안합니다. 수집한 데이터를 분석하여 의미 있는 정보를 추출합니다. 텍스트 분석, 감성 분석, 네트워크 분석 등을 활용하여 사용자들의 경향, 선호도, 관광지별 만족도 등을 파악합니다. 분석 결과는 인사이트를 제공하며, 방문객들의 행동 패턴, 리뷰의 긍정성, 사용자 사이에서 인기 있는 관광지 등을 파악하여 제주도에서 가장 추천하는 관광지를 결정할 수 있습니다.

제주도 관광지 추천을 위한 관광 데이터 분석 필요 데이터
1. 제주도 내 교통량 데이터: 도로별 교통량, 시간대별 교통량, 교통체증 지역 등
2. 제주도 관광객 데이터: 관광객 수, 관광객 연령대, 관광객 성별, 관광객 국적 등
3. 제주도 관광지 데이터: 관광지 이름, 위치, 주변 교통상황, 입장료, 잠재적 수용인원 등
4. 제주도의 날씨 및 계절 데이터: 기온, 강수량, 계절 정보 등
5. 제주도 숙박, 식당, 쇼핑 등 관광 관련 업소 데이터: 업소 종류, 위치, 시간대별 이용객 수 등
6. 제주도 내 대중교통 데이터

클러스터링을 통한 자전거 도로 분석 방법론 방법
현재 개인의 건강을 위한 야외 활동이 증가하며 자전거 이용이 늘어나고 있는데, 이로 인한 사고 위험이 증가하고 있습니다. 이를 방지하기 위해 본 분석은 자전거 도로 상의 위험한 물체를 감지하고, 클러스터링을 통해 도로 상태를 분석할 수 있습니다. 데이터를 정리, 표준화하여 클러스터링이 가능한 형태로 만든다. 이는 아웃라이어 탐지, 누락된 값 처리, 변환, 스케일링 등을 포함합니다. 수집된 데이터를 처리한 후, 클러스터링 알고리즘을 적용합니다. 클러스터링은 유사한 특성을 가진 데이터 포인트를 그룹으로 만드는 비지도 학습 방법입니다. 방법으로는 K-means, DBSCAN, 계층적 클러스터링등이 있습니다. 적절한 클러스터 수를 선택하기 위해 Elbow 방법 등을 사용할 수 있습니다.

클러스터링을 통한 자전거 도로 분석 필요 데이터
1. 자전거 도로 네트워크 정보: 도로의 위치, 길이, 폭, 결로 양상, 신호 체계 등
2. 자전거 이용자 정보: 이용 시간대, 이용자 연령대, 이용 목적 (출퇴근, 운동, 여가 등)
3. 자전거 사고 데이터: 사고 발생 위치, 사고 유형, 사고 원인 등
4. 기상 데이터: 자전거 이용에 영향을 미칠 수 있는 기후의 변화 or 변동성 (온도, 강수량, 습도, 바람 등)
5. 도시 지리 학적 정보: 고도, 경사도 등 지형 데이터
6. 교통량 데이터: 자동차, 보행자 교통량 등
7. 도심 내 대중교통 정보: 정류장 위치, 노선 정보, 운행 시간 등
8. 문화, 여가 및 편의시설 위치 정보: 공원, 편의점, 대중 목욕탕 등 자전거 이용자들이 자주 방문하는 장소의 위치 정보
9. 인구 밀도 및 유동인구 데이터.

교통 사고 위험 지역 분석 방법론 방법
교통사고는 사회적으로 큰 영향을 미치는 문제로, 자동차 등록과 운전면허 소지자 증가로 심각해지고 있습니다. 교통사고 유형별 위험요인을 파악하여 교통안전시설을 효율적으로 개선해야 한다. 이를 위해 교통사고 발생지점을 지도에 시각화하고 교통사고 패턴의 공간군집 여부를 시공간으로 확인이 필요하다. 지역 교통사고 자료를 통해 유형별 위험요인과 기상자료의 영향을 분석하여 교통사고 발생 위험요인을 파악할 수 있다.

교통 사고 위험 지역 분석 필요 데이터
1. 교통사고 데이터 : 사고 발생 시간, 사고가 발생한 위치, 사고 유형, 사고 당사자의 수, 날씨 상황, 도로 상태
2. 도로 네트워크 및 교통흐름 데이터 : 도로의 종류, 너비, 교통 흐름 정보, 교차로 조건, 도로 형태, 변곡 정도.
3. 기상 데이터 : 사고 발생 시점의 날씨와 기상 조건.
4. 인구 및 가구 분포 데이터 : 지역별 인구 밀도, 차량 보유 가구 비율 등.
5. 지리 정보 : 지형, 고도, 지리적 특성 등.
6. 도시 인프라 및 개발 상황 : 공원, 학교, 쇼핑몰 등의 위치 정보, 주택 개발지역, 산업 지역 등.
7. 교통 안전 시설 : 교통 신호등, 보행자 보호 구역, 횡단보도의 위치 정보.
8. 법규 위반 데이터 : 과속, 신호 위반 등의 법규 위반 사례.
9. 도로 표면 상태 : 도로 표면의 노면상태, 결함, 자기 등.

도시 공원 및 녹지공간 불균형 분석 방법론 방법
분석 대상으로 대구광역시의 녹지 불균형을 이용적 측면에서 분석하고, 녹지의 서비스 공급량에 기반하여 관리권역을 설정하는 것을 가정해보자. 대구의 녹지 총량은 48,936.1ha(55.4%)로 녹지점유비율이 7개 광역시 중 2위로 나타났다. 행정구 및 행정동별 녹지의 지니계수는 불균형이 크지 않지만, 인구대비 지니계수는 심각한 불균형을 보여줍니다. 특히 달서구에서 녹지의 서비스 공급량을 평가한 결과, 약 100m 내에 많은 녹지가 공급되며 100~200m 지역에서도 녹지가 제공되지만 일부 지역은 부족한 녹지를 보유하고 있다. 이와 같은 과정을 거쳐서 관심 도시의 도시 및 녹지 불균을 파악하는데 활용될 수 있다.

도시 공원 및 녹지공간 불균형 분석 필요 데이터
1. 각 도시 공원의 위치 데이터 
2. 공원의 규모(면적), 특히 녹지공간의 규모
3. 각 도시의 인구 수, 인구 밀도
4. 각 도시의 공간적 구조 (건물 밀도, 도로 형태 등)
5. 각 도시의 상세 지역(동/읍/면)별 인구 통계
6. 일반 주민 구성원과 녹지공간을 이용할 수 있는 대상 (어린이, 노인 등) 의 비율
7. 도시계획 데이터, 공원 및 녹지공간의 설정 및 관리 계획
8. 공원의 이용 통계 데이터 (방문객 수 등)
9. 공원 및 녹지공간의 품질 데이터 (환경 오염 수준, 플라스틱 쓰레기 양 등)

화재 위험도 분석을 통한 소방서 위치 선정 방법론 방법
이 분석은 앞으로 발생할 수 있는 화재 위험 대비를 위해 소방시설 확충과 설립 위치를 선정하는 것을 목적으로 합니다. 지도데이터와 GIS를 활용하여 지형을 기준으로 할 수 있으며, 대상 도시의 도로 네트워크와 지리 데이터를 사용하여 모델을 개발할 수 있습니다. 소방서 위치 선정은 분석된 화재 위험도, 인구 밀도, 도로 네트워크, 현재의 소방서 위치 등을 고려하여 최적의 소방서 위치를 결정한다. 이를 위해 수행되는 공간 분석 방법에는 최적 위치 분석, 서비스 지역 분석, 접근성 분석 등이 있습니다. 

화재 위험도 분석을 통한 소방서 위치 선정 필요 데이터
1. 119 화재 발생 데이터: 화재 발생 일자, 시간, 위치, 유형, 규모 등
2. 인구 통계 데이터: 지역별 인구 수, 연령대, 성별 등
3. 건물 데이터: 건물의 크기, 유형, 용도, 건축년도 등
4. 지리 데이터: 화재가 발생한 지역의 지리적 특성
5. 공공 시설 데이터: 소방서 위치, 병원 위치, 학교 위치 등
6. 교통 데이터: 주요 교통로 위치, 도로 조건, 교통량 등
7. 도시 계획 데이터: 주택 단지 및 공장 위치, 도시 개발 계획 등

지도생성
지도를 생성 기능 제공 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view"></div>
</body>
<script>
	/* 맵 타겟 */
	var mapContainer = document.getElementById('map');

	/* 맵 중심점 */
	var coord = new odf.Coordinate(199312.9996,551784.6924);

	/* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};
	/*
		* 배경지도 종류
		eMapBasic - 바로e맵 일반 지도
		eMapColor - 바로e맵 색각 지도
		eMapLowV - 바로e맵 큰글씨 지도
		eMapWhite - 바로e맵 백지도
		eMapEnglish - 바로e맵 영어 지도
		eMapChinese - 바로e맵 중어 지도
		eMapJapanese - 바로e맵 일어 지도
		eMapWhiteEdu - 바로e맵 교육용 백지도
		eMapAIR - 바로e맵  항공지도

		* 프록시 사용
		proxyURL: 'proxy.jsp' 프록시 설정
	 */

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var map = new odf.Map(mapContainer, mapOption);

	//최소 줌 레벨 설정
	map.getView().setMinZoom(8);
	//최대 줌 레벨 설정
	map.getView().setMaxZoom(23);

</script>
</html>
"""

배경지도설정
배경지도 설정 기능 제공 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view"></div>
</body>
<script>
	/* 맵 타겟 */
	var mapContainer = document.getElementById('map');

	/* 맵 중심점 */
	var coord = new odf.Coordinate(199312.9996,551784.6924);

	/* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};
	/*
		* 배경지도 종류
		eMapBasic - 바로e맵 일반 지도
		eMapColor - 바로e맵 색각 지도
		eMapLowV - 바로e맵 큰글씨 지도
		eMapWhite - 바로e맵 백지도
		eMapEnglish - 바로e맵 영어 지도
		eMapChinese - 바로e맵 중어 지도
		eMapJapanese - 바로e맵 일어 지도
		eMapWhiteEdu - 바로e맵 교육용 백지도
		eMapAIR - 바로e맵  항공지도

		* 프록시 사용
		proxyURL: 'proxy.jsp' 프록시 설정
	 */

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var map = new odf.Map(mapContainer, mapOption);

	/* 베이스맵 컨트롤 생성 */
	var basemapControl = new odf.BasemapControl();
	basemapControl.setMap(map);

</script>
</html>
"""

배경지도 최적화
배경지도 변경시 지도 좌표계 변경하여 조금 더 선명한 배경지도를 이용 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view"></div>
	<div>현재 좌표계 : <span id="projection"></span></div>
	<div>
		※ 배경지도 변경시 좌표계가 변경되면서 콘솔로그에 변경된 좌표계 값이 찍힙니다.
		※ 같은 그룹 끼리는 좌표계가 변경되지 않습니다.
	</div>
</body>
<script>
	/* 맵 타겟 */
	var mapContainer = document.getElementById('map');

	/* 맵 중심점 */
	var coord = new odf.Coordinate(199312.9996,551784.6924);

	/* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};
	mapOption.basemap.OSM = true;
	/*
		* 배경지도 종류
		eMapBasic - 바로e맵 일반 지도
		eMapColor - 바로e맵 색각 지도
		eMapLowV - 바로e맵 큰글씨 지도
		eMapWhite - 바로e맵 백지도
		eMapEnglish - 바로e맵 영어 지도
		eMapChinese - 바로e맵 중어 지도
		eMapJapanese - 바로e맵 일어 지도
		eMapWhiteEdu - 바로e맵 교육용 백지도
		eMapAIR - 바로e맵  항공지도

		* 프록시 사용
		proxyURL: 'proxy.jsp' 프록시 설정
	 */

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var map = new odf.Map(mapContainer, {
		...mapOption,
		//배경지도 최적화 on
		optimization: true,
	});

	/* 베이스맵 컨트롤 생성 */
	var basemapControl = new odf.BasemapControl({});
	basemapControl.setMap(map);


	/* wms 레이어 생성 */
	var wmsLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : 'https://geoserver.geon.kr/geoserver',
		layer : 'geonpaas:L100000254',
		service : 'wms',
	});
	wmsLayer.setMap(map);

	var sld = odf.StyleFactory.produceSLD({
		rules : [ {
			name : 'My Rule', /*룰 이름*/
			symbolizers : [ {
				kind : 'Fill',
				/*채우기색
				rgba 값 입력시 자동변환. 네번째 인수 (투명도) 존재시 fillOpacity 보다 우선 적용됨
				 */
				color : '#FF9966',
				/*채우기 투명도 0~1*/
				fillOpacity : 0.7,
				/*윤곽선색
					rgba 값 입력시 자동변환. 네번째 인수 (투명도) 존재시 outlineOpacity보다 우선 적용됨
				 */
				outlineColor : '#338866',
				/*윤곽선 두께*/
				outlineWidth : 2,
			}, ],
		}, ],
	});

	//sld 적용
	wmsLayer.setSLD(sld);


	// wfs 레이어 생성
	// LayerFactory의 produce 함수는 option이 다양하니 개발자지원센터 '지도문서'를 확인하세요
	var wfsLayer = odf.LayerFactory.produce('geoserver'/*레이어를 생성하기위 한 테이터 호출 방법*/, {
		method : 'get',//'post'
		server : 'https://geoserver.geon.kr/geoserver', // 레이어가 발행된 서버 주소
		layer : 'geonpaas:L100000258', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
		service : 'wfs', // 호출하고자 하는 레이여 형태(wms, wfs, wmts)
	}/*레이어 생성을 위한 옵션*/);
	wfsLayer.setMap(map);
	// 해당 layer가 한눈에 보이는 보여주는 extent로 화면 위치 이동 및 줌 레벨 변경
	wfsLayer.fit();


	document.querySelector('#projection').innerText =map.getView().getProjection().getCode();
	odf.event.addListener(map,'change:view',()=>{
		document.querySelector('#projection').innerText =map.getView().getProjection().getCode();
	})

</script>
</html>
"""

사용자 지정 배경지도
사용자 정의 배경지도 그룹/레이어 관리 기능 제공 샘플코드
"""
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view"></div>
	<input type="button" id="setBasemapDefault" class="onoffBtn" onClick="basemapControl.switchBaseLayer('eMapBasic')" value="배경지도 원래대로 변경"/>
	<p id="positionStr">자세한 정보는 Console창을 확인하세요</p>
</body>
<script>
	/* 맵 타겟 */
	var mapContainer = document.getElementById('map');

	/* 맵 중심점 */
	var coord = new odf.Coordinate(199312.9996,551784.6924);

	/* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};
	/*
		* 배경지도 종류
		eMapBasic - 바로e맵 일반 지도
		eMapColor - 바로e맵 색각 지도
		eMapLowV - 바로e맵 큰글씨 지도
		eMapWhite - 바로e맵 백지도
		eMapEnglish - 바로e맵 영어 지도
		eMapChinese - 바로e맵 중어 지도
		eMapJapanese - 바로e맵 일어 지도
		eMapWhiteEdu - 바로e맵 교육용 백지도
		eMapAIR - 바로e맵  항공지도

		* 프록시 사용
		proxyURL: 'proxy.jsp' 프록시 설정
	 */

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var map = new odf.Map(mapContainer, mapOption);

	/* 베이스맵 컨트롤 생성 */
	var basemapControl = new odf.BasemapControl();
	basemapControl.setMap(map);

	//베이스맵에 새로운 그룹 추가
	basemapControl.setGrp('myGrp');
	//베이스맵에 등록된 그룹 제거// 해당 그룹에 속한 베이스레이어가 있다면 같이 제거
	//basemapControl.removeGrp('myGrp');

	//베이스레이어로 추가할 레이어 생성 //베이스그룹이 없으면 추가하여 베이스레이어 생성
	var _wmtsLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		service: 'wmts',
		server: 'https://geoserver.geon.kr/geoserver',
		layer: 'geonpaas:L100000252',
		crtfckey : 'tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh',
	});
	basemapControl.setBaseLayer('myGrp','customBaseLayer','사용자정의지도',_wmtsLayer);
	map.setCenter([237805.02689211597,437223.62942290655]);
	map.setZoom(19);
	//사용자 정의 베이스레이어 제거 //해당 그룹에 베이스레이어가 하나뿐이면 그룹도 함께 삭제
	//basemapControl.removeBaseLayer('temp');

	//베이스맵 컨트롤 리빌드
	basemapControl.rebuildElement();

	console.log("현재배경지도레이어");
	console.log(basemapControl.getPresentBaseLayer());
	console.log("현재배경지도레이어키:"+basemapControl.getPresentBaseLayerKey());
	console.log("배경지도설정가능목록");
	console.log(basemapControl.getSetableBasemapList());
	console.log("배경지도레이어 유무 확인:"+basemapControl.hasBaseLayer(basemapControl.getPresentBaseLayerKey()));
	console.log("배경지도레이어그룹  유무 확인:"+basemapControl.hasGrp('myGrp'));

</script>
</html>
"""

사용자 지정 배경지도(webGl 벡터타일 레이어)
사용자 정의 배경지도 그룹/레이어 관리 기능 제공 샘플코드
"""
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view"></div>
	<input type="button" id="setBasemapDefault" class="onoffBtn" onClick="basemapControl.switchBaseLayer('eMapBasic')" value="배경지도 원래대로 변경"/>
	<p id="positionStr">자세한 정보는 Console창을 확인하세요</p>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = [922176.2193716865, 1908044.99982566];
	var mapOption = {
		center: coord,
		zoom: 16,
		projection: 'EPSG:5179',
    baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',
		basemap: {
      baroEMap:['eMapAIR'],
    },
  };
	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var map = new odf.Map(mapContainer, mapOption);

	/* 베이스맵 컨트롤 생성 */
	var basemapControl = new odf.BasemapControl();
	basemapControl.setMap(map);

	//베이스맵에 새로운 그룹 추가
	basemapControl.setGrp('myGrp');
	//베이스맵에 등록된 그룹 제거// 해당 그룹에 속한 베이스레이어가 있다면 같이 제거
	//basemapControl.removeGrp('myGrp');

	//webFGLVectorTile 레이어
	var vectorTileLayer = odf.LayerFactory.produce('api', {
		service: 'vectortile',
		server: {
			url: '/pbfData/{z}/{x}/{y}.pbf'
		},
		projection: 'EPSG:5179',
		tileGrid: {
			extent: [-200000.0, -28024123.62, 31824123.62, 4000000.0],
			tileSize: 256,
			minZoom: 13,
			maxZoom: 15,
		},
		//webgGLRender 적용여부
		webGLRender: true,
		//webGLRender 사용시 설정
		renderOptions: {
			style: {
				builder: {
					'fill-color': ['get', 'fillColor'],
					'stroke-color': ['get', 'strokeColor'],
					'stroke-width': ['get', 'strokeWidth'],
					'z-index': ['get', 'zIndex'],
					'circle-radius': 5,
					'circle-fill-color': ['get', 'fillColor'],
					'circle-stroke-color': ['get', 'strokeColor'],
					'circle-stroke-width': ['get', 'strokeWidth'],
				},
				//사용자 정의 속성
				attributes: {
					fillColor: {
						//사용자 정의 속성 크기
						size: 2,
						//사용자 정의 속성 정의
						callback: (feature) => {
							let color = '';
							switch (feature.get("layer")) {
								case "vl_scco_ctprvn_virtual":
								case "vl_scco_ctprvn_simple_virtual":
								case "vl_scco_ctprvn_simple_10_virtual":
								case "vl_scco_sig_virtual":
								case "vl_scco_sig_simple_virtual":
								case "vl_scco_sig_simple_10_virtual": color = '#FFFFFFFF'; break;
								case "new_vl_fclty_zone_bndry_0818_virtual": color = '#b4c49b'; break;
								case "new_vl_rodway_bndry_1518_virtual": color = '#f4f3f199'; break;
								case "new_vl_rodway_ctln_1214_virtual": color = '#cfcabe'; break;
								case "new_vl_arrfc_1318_virtual":
								case "new_vl_arrfc_1518_virtual": {
									if (["도로교", "보행교", "철도교", "도로보행교", "도로철도교", "철도보행교", "생태교"].includes(feature.get("면형교통시설종류 구분"))) {
										color = '#eae6d0';
									}
									else if (["도로터널", "공용터널", "철도터널"].includes(feature.get("면형교통시설종류 구분"))) {
										color = '#e1e1e1';
									}
									else if (["고가차도"].includes(feature.get("면형교통시설종류 구분"))) {
										color = '#e2e2e2';
									}
									else if (["지하보도", "지하차도", "육교"].includes(feature.get("면형교통시설종류 구분"))) {
										color = '#eaeaea';
									}
									else {
										color = 'aliceblue';
									}
								} break;
								case "new_vl_rlroad_ctln_rapid_1118_virtual": color = '#ff0000cc'; break;
								case "new_vl_rlroad_ctln_rapid_1118_virtual": color = '#FFF0'; break;
								case "new_vl_rlroad_ctln_basic_1118_virtual": color = '#2b4fffcc'; break;
								case "new_vl_arwfc_1618_virtual": color = '#e1e1e1'; break;
								case "new_vl_buld_1618_virtual": color = '#f3f3f3'; break;
								case "new_vl_sprd_manage_1518_virtual": color = '#ffb80d4d'; break;
								case "new_vl_spbd_buld_1618_virtual": color = '#00000080'; break;
								case "kais_tl_scco_ctprvn_virtual":
								case "kais_tl_scco_sig_virtual": color = '#FFF0'; break;
								case "new_vl_poi_1718_virtual": color = '#00AAFF'; break;
								default: color = '#F00'; break;
							}
							return odf.ColorFactory.packColor(color);
						},
					},
					strokeColor: {
						//사용자 정의 속성 크기
						size: 2,
						//사용자 정의 속성 정의
						callback: (feature) => {
							let color = '';
							switch (feature.get("layer")) {
								case "vl_scco_ctprvn_virtual":
								case "vl_scco_ctprvn_simple_virtual":
								case "vl_scco_ctprvn_simple_10_virtual": color = '#1020dd'; break;
								case "vl_scco_sig_virtual":
								case "vl_scco_sig_simple_virtual":
								case "vl_scco_sig_simple_10_virtual": color = '#43dd10'; break;
								case "new_vl_fclty_zone_bndry_0818_virtual": color = '#b4c49b'; break;
								case "new_vl_rodway_bndry_1518_virtual": color = '#cfcabe'; break;
								case "new_vl_rodway_ctln_1214_virtual": color = '#cfcabe'; break;
								case "new_vl_arrfc_1318_virtual":
								case "new_vl_arrfc_1518_virtual": {
									if (["도로교", "보행교", "철도교", "도로보행교", "도로철도교", "철도보행교", "생태교"].includes(feature.get("면형교통시설종류 구분"))) {
										color = '#d8d2ba';
									}
									else if (["도로터널", "공용터널", "철도터널"].includes(feature.get("면형교통시설종류 구분"))) {
										color = '#e1e1e1';
									}
									else if (["고가차도"].includes(feature.get("면형교통시설종류 구분"))) {
										color = '#c7c4c4';
									}
									else if (["지하보도", "지하차도", "육교"].includes(feature.get("면형교통시설종류 구분"))) {
										color = '#eaeaea';
									}
									else {
										color = '';
									}
								} break;
								case "new_vl_rlroad_ctln_rapid_1118_virtual": color = '#ff0000cc'; break;
								case "new_vl_rlroad_ctln_rapid_1118_virtual": color = '#2b4fffcc'; break;
								case "new_vl_rlroad_ctln_basic_1118_virtual": color = '#b2b2b233'; break;
								case "new_vl_arwfc_1618_virtual": color = '#b2b2b233'; break;
								case "new_vl_buld_1618_virtual": color = '#ddd7d1'; break;
								case "new_vl_sprd_manage_1518_virtual": color = '#ffb80d4d'; break;
								case "new_vl_spbd_buld_1618_virtual": color = '#00000080'; break;
								case "kais_tl_scco_ctprvn_virtual":
								case "kais_tl_scco_sig_virtual": color = '#000'; break;
								case "new_vl_poi_1718_virtual": color = '#00AAFF'; break;
								default: color = '#F00'; break;
							}
							return odf.ColorFactory.packColor(color);
						},
					},
					strokeWidth: {
						//사용자 정의 속성 크기
						size: 1,
						//사용자 정의 속성 정의
						callback: (feature) => {
							let width = 1;
							switch (feature.get("layer")) {
								case "vl_scco_ctprvn_virtual":
								case "vl_scco_ctprvn_simple_virtual":
								case "vl_scco_ctprvn_simple_10_virtual":
								case "vl_scco_sig_virtual":
								case "vl_scco_sig_simple_virtual":
								case "vl_scco_sig_simple_10_virtual": width = 2; break;
								case "new_vl_fclty_zone_bndry_0818_virtual": width = 0.5; break;
								case "new_vl_rodway_bndry_1518_virtual": width = 1.25; break;
								case "new_vl_rodway_ctln_1214_virtual": width = 0.3; break;
								default: width = 1; break;
							}
							return width;
						},
					},
					zIndex: {
						//사용자 정의 속성 크기
						size: 1,
						//사용자 정의 속성 정의
						callback: (feature) => {
							let zIndex = 999;
							switch (feature.get("layer")) {
								case "vl_scco_ctprvn_virtual":
								case "vl_scco_ctprvn_simple_virtual":
								case "vl_scco_ctprvn_simple_10_virtual": zIndex = 1; break;
								case "vl_scco_sig_virtual":
								case "vl_scco_sig_simple_virtual":
								case "vl_scco_sig_simple_10_virtual": zIndex = 2; break;
								case "new_vl_fclty_zone_bndry_0818_virtual": zIndex = 3; break;
								case "new_vl_rodway_bndry_1518_virtual": zIndex = 4; break;
								case "new_vl_rodway_ctln_1214_virtual": zIndex = 5; break;
								case "new_vl_arrfc_1318_virtual":
								case "new_vl_arrfc_1518_virtual": zIndex = 6; break;
								case "new_vl_rlroad_ctln_rapid_1118_virtual": zIndex = 7; break;
								case "new_vl_rlroad_ctln_basic_1118_virtual": zIndex = 7; break;
								case "new_vl_arwfc_1618_virtual": zIndex = 8; break;
								case "new_vl_buld_1618_virtual": zIndex = 11; break;
								case "new_vl_sprd_manage_1518_virtual": zIndex = 9; break;
								case "new_vl_spbd_buld_1618_virtual": zIndex = 10; break;
								case "kais_tl_scco_ctprvn_virtual":
								case "kais_tl_scco_sig_virtual":
								case "new_vl_poi_1718_virtual": zIndex = 20; break;
								default: zIndex = 999; break;
							}
							return zIndex;
						},
					},
				}
			}
		}
	});
	//vectorTileLayer.setMap(map);
	basemapControl.setBaseLayer('myGrp', 'customBaseLayer', '사용자정의벡터타일', vectorTileLayer);

	//사용자 정의 베이스레이어 제거 //해당 그룹에 베이스레이어가 하나뿐이면 그룹도 함께 삭제
	//basemapControl.removeBaseLayer('temp');

	//베이스맵 컨트롤 리빌드
	basemapControl.rebuildElement();

	console.log("현재배경지도레이어");
	console.log(basemapControl.getPresentBaseLayer());
	console.log("현재배경지도레이어키:"+basemapControl.getPresentBaseLayerKey());
	console.log("배경지도설정가능목록");
	console.log(basemapControl.getSetableBasemapList());
	console.log("배경지도레이어 유무 확인:"+basemapControl.hasBaseLayer(basemapControl.getPresentBaseLayerKey()));
	console.log("배경지도레이어그룹  유무 확인:"+basemapControl.hasGrp('myGrp'));

</script>
</html>
"""

축척
지도의 축척정보 조회 기능 제공 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(199312.9996,551784.6924);
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};
	var map = new odf.Map(mapContainer, mapOption);


	/* 줌 컨트롤 생성 */
	var zoomControl = new odf.ZoomControl({
		// 줌 슬라이더 사용여부. true/false
		// ※ 기본값 => false
		zoomSlider : true,
	});
	zoomControl.setMap(map);

	/* 축척 컨트롤 생성 */
	var scaleControl = new odf.ScaleControl({
		// 축척 컨트롤 크기 조정(pixel)
		// ※ 기본값 => 100, 최소 10 최대 2000
		size : 100,

		// 축척 입력 창 사용여부. true/false
		// ※ 기본값 => false
		scaleInput  : false,
	});
	scaleControl.setMap(map);

</script>
</html>
"""

네비게이션
지도의 네비게이션(이전/다음 화면) 기능 제공 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view"></div>
	<div style="margin-top: 15px;">
		<button class="onoffOnlyBtn toggle grp1" onclick="draggableFn(true);">drag 이동 허용</button>
		<button class="onoffOnlyBtn toggle grp1" onclick="draggableFn(false);">drag 이동 막기</button>
		<button class="onoffOnlyBtn toggle grp2" onclick="zoomableFn(true);">wheel 줌 허용</button>
		<button class="onoffOnlyBtn toggle grp2" onclick="zoomableFn(false);">wheel 줌 막기</button>
	</div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(199312.9996,551784.6924);
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};
	var map = new odf.Map(mapContainer, mapOption);


	/* 줌 컨트롤 생성 */
	var zoomControl = new odf.ZoomControl({
		zoomSlider : true,
	});
	zoomControl.setMap(map);

	/* 이전,다음 컨트롤 생성 */
	var moveControl = new odf.MoveControl();
	moveControl.setMap(map);

	/* 마우스 드래그 이동 제한 */
	function draggableFn(bools) {
		map.setDraggable(bools);
	}

	/* 마우스 휠 확대 축소 제한 */
	function zoomableFn(bools) {
		map.setZoomable(bools);
	}
</script>
</html>
"""

인덱스맵
지도를 한 눈에 볼 수 있는 인덱스맵 제공 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(199312.9996,551784.6924);
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};
	var map = new odf.Map(mapContainer, mapOption);

	/* 인덱스맵 컨트롤 생성 */
	var overviewMapControl = new odf.OverviewMapControl();
	overviewMapControl.setMap(map);

</script>
</html>
"""

지도회전
지도 회전 기능 제공 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view"></div>
	<p>Alt + Shift 키를 누른채로 지도를 드래그 해주세요.</p>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(199312.9996,551784.6924);
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};
	var map = new odf.Map(mapContainer, mapOption);


	/* 지도회전 컨트롤 생성  */
	var rotationControl = new odf.RotationControl();
	rotationControl.setMap(map);
</script>
</html>
"""

그리기 도구
점, 선, 면, 곡선, 다각형, 사각형, 버퍼 그리기 도구 기능 제공 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view"></div>
	<div id="evtChk"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(199312.9996,551784.6924);
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};
	var map = new odf.Map(mapContainer, mapOption);


	/* 그리기 도구 컨트롤 생성 */
	var drawControl = new odf.DrawControl({
		/*
		연속 측정 여부.
		 - true : 연속 측정 기능 활성화. ※ 측정 종료는 clean 함수를 통해서 실행
		 - false : (기본값) 연속 측정 기능 비활성화.
		 */
		continuity : false,

		// 측정 옵션 활성화 여부(선 그리기/원그리기 툴에서 활성화)
		measure : false,

		/**drawControl 생성 시 새 레이어 생성 여부
		 - false : 'odf-layer-draw-unique' 라는 id로 생성. drawControl을 여러개 생성해도 레이어를 공유
		 - true :  'odf-layer-draw-xxxx' 라는 id로 생성.
		  */
		createNewLayer : false,
		// 우클릭 편집 기능(미정의시 사용 안함)
		editFeatureMenu: ['modify', 'dragTranslate', 'delete', 'setText'],
		// 생성할 툴 배열
		// 설정하지 않으면 모든 툴 생성
		tools : [
			'text',//텍스트 그리기 툴
			'polygon',//다각형 그리기 툴
			'lineString',//선 그리기 툴
			'box',//사각형 그리기 툴
			'point',//점 그리기 툴
			'circle',//원 그리기 툴
			'curve',//곡선 그리기 툴
			'buffer',//버퍼 그리기 툴
		],

		// 툴팁 메세지 변경
		message : {
			DRAWSTART_POINT : '[수정한 메세지]점을 그리기 위해 지도를 클릭해주세요',
			DRAWSTART_LINESTRING : '[수정한 메세지]선을 그리기 위해 지도를 클릭해주세요',
			/* DRAWSTART_POLYGON: '면을 그리기 위해 지도를 클릭해주세요',
			DRAWSTART_CURVE: '곡선을 그리기 위해 지도를 드래그해주세요',
			DRAWSTART_TEXT: '텍스트를 입력하기 위해 지도를 클릭해주세요.',
			DRAWSTART_BUFFER: '버퍼를 생성하기 위해 레이어를 선택해주세요.',
			DRAWSTART_CIRCLE: '원을 그리기 위해 지도를 클릭해주세요.',
			DRAWSTART_BOX: '사각형을 그리기 위해 지도를 클릭해주세요.',
			DRAWEND_DRAG: '드래그를 멈추면 그리기가 종료됩니다.',
			DRAWEND_DBCLICK: '드래그를 멈추면 그리기가 종료됩니다.', */
		},

		// 그리기 도형 스타일
		style : {
			fill : {
				color : [ 254, 243, 255, 0.6 ]
			},
			stroke : {
				color : [ 103, 87, 197, 0.7 ],
				width : 2
			},
			image : {
				circle : {
					fill : {
						color : [ 254, 243, 255, 0.6 ]
					},
					stroke : {
						color : [ 103, 87, 197, 0.7 ],
						width : 2
					},
					radius : 5,
				},
			},
			text : {
				textAlign : 'left',
				font : '30px sans-serif',
				fill : {
					color : [ 103, 87, 197, 1 ]
				},
				stroke : {
					color : [ 255, 255, 255, 1 ]
				},
			},
		},
		bufferStyle : {
			stroke : {
				color : [ 255, 255, 159, 1 ],
				width : 2
			},
			fill : {
				color : [ 255, 255, 159, 0.2 ],
			},
		}
	});
	// 지도 객체와 연결 (컨트롤 ui 생성)
	drawControl.setMap(map);
	// 지도 객체와 연결 (컨트롤 ui 생성 x)
	//drawControl.setMap(map,false);

	/*그리기 시작시 이벤트*/
	odf.event.addListener(drawControl, 'drawstart', function(feature) {
		//feature는 odf.Feature
		console.log("drawstart");
		document.getElementById('evtChk').innerText = "drawstart";
	});

	/*그리기 종료시 이벤트*/
	odf.event.addListener(drawControl, 'drawend', function(feature) {
		//feature는 odf.Feature
		console.log("drawend");
		document.getElementById('evtChk').innerText += " -> drawend";
	});

	//텍스트 그리기
	//drawControl.drawText();
	//폴리곤 그리기
	//drawControl.drawPolygon();
	//라인 그리기
	//drawControl.drawLineString();
	//점 그리기
	//drawControl.drawPoint();
	//곡선 그리기
	//drawControl.drawCurve();
	//사각형 그리기
	//drawControl.drawBox();
	//원 그리기
	//drawControl.drawCircle();
	//버퍼 그리기
	//drawControl.buffer();


	//그리기 레이어 조회
	var drawLayer = drawControl.findDrawVectorLayer();
	//그리기 인터렉션 삭제 및 그리기 오버레이 삭제, 그리던 도형 삭제
	//drawControl.clear()


	/*그리기 초기화 컨트롤 생성*/
	var clearControl = new odf.ClearControl();
	clearControl.setMap(map);
</script>
</html>
"""

측정 도구
면적/거리/측정 기능 제공 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view"></div>
	<div id="evtChk"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(199312.9996,551784.6924);
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};
	var map = new odf.Map(mapContainer, mapOption);


	/* 측정 도구 컨트롤 생성 */
	var measureControl = new odf.MeasureControl({
		/*
		연속 측정 여부.
		 - true : 연속 측정 기능 활성화. ※ 측정 종료는 clean 함수를 통해서 실행
		 - false : (기본값) 연속 측정 기능 비활성화.
		 */
		continuity : false,

		/*
		거리 측정 표시 옵션
		*/
		displayOption : {
			area : {//면적 측정 표시 옵션
				//면적 측정 소수점 표기 자리수 (소수몇번째자리까지 표출) default : 2
				decimalPoint : 2,
				//면적 측정 단위 변경 미터기준(m -> km 단위로 변경) ex) 100 -> 100㎡ 부터 0.1 ㎢로 표출 default : 100
				transformUnit: 100,
			},
			distance : {//거리 측정 표시 옵션
				//거리 측정 소수점 표기 자리수 (소수몇번째자리까지 표출) default : 2
				decimalPoint : 2,
				//거리 측정 단위 변경 미터기준(m -> km 단위로 변경) ex) 100 -> 100m 부터 0.1 km로 표출 default : 100
				transformUnit: 100,
			},
			round : {// 반경 측정 표시 옵션
				//반경 측정 소수점 표기 자리수 (소수몇번째자리까지 표출) default : 2
				decimalPoint : 2,
				//반경 측정 단위 변경 미터기준(m -> km 단위로 변경) ex) 100 -> 100m 부터 0.1 km로 표출 default : 100
				transformUnit: 100,
			}
		},

		// 생성할 툴 배열
		// 설정하지 않으면 모든 툴 생성
		tools : [
			'distance',// 거리 측정 툴
			'area',// 면적측정 툴
			'round',// 원의 면적측정 툴
			'spot',// 좌표 측정 툴
		],

		// 좌표 측정시 사용할 좌표계 (기본값=> 지도의 좌표계)
		// EPSG:4326 => GPS가 사용하는 좌표계
		spotProjection:'EPSG:4326',

		// 툴팁 메세지
		message : {
			// DRAWSTART: '클릭하여 측정을 시작하세요',
			DRAWEND_POLYGON : '[수정한 메세지]클릭하여 폴리곤을 그리거나, 더블클릭하여 그리기를 종료하세요',
			// DRAWEND_LINE: '클릭하여 라인을 그리거나, 더블클릭하여 그리기를 종료하세요',
		},

		// 측정 도형 스타일
		style : {
			fill : {
				color : [ 254, 243, 255, 0.2 ]
			},
			stroke : {
				color : [ 103, 87, 197, 0.7 ],
				width : 2
			},
			image : {
				circle : {
					fill : {
						color : [ 254, 243, 255, 0.2 ]
					},
					stroke : {
						color : [ 103, 87, 197, 0.7 ],
						width : 2
					},
					radius : 5,
				},
			},
			text : {
				textAlign : 'left',
				font : '30px sans-serif',
				fill : {
					color : [ 103, 87, 197, 1 ]
				},
				stroke : {
					color : [ 255, 255, 255, 1 ]
				},
			},
		}
	});
	measureControl.setMap(map);
	/*그리기/측정 초기화 컨트롤 생성*/
	var clearControl = new odf.ClearControl();
	clearControl.setMap(map);
</script>
</html>
"""

지도 출력/다운로드
지도 출력 및 다운로드(PDF/이미지) 기능 제공 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(199312.9996,551784.6924);
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};
	var map = new odf.Map(mapContainer, mapOption);


	/* 출력 컨트롤 생성 */
	var printControl = new odf.PrintControl();
	printControl.setMap(map);

	/* 저장 컨트롤 생성 */
	var downloadControl = new odf.DownloadControl();
	downloadControl.setMap(map);
</script>
</html>
"""

마우스 좌표 표시/전체화면
사용자의 마우스 좌표 표출 기능과 전체화면 기능 제공 (전체화면 기능은 직접해보기에서 실행해야 정상동작) 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view"></div>
	<div id="coordDiv" style="height: 25px; font-size: 20px;"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(199312.9996,551784.6924);
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};
	var map = new odf.Map(mapContainer, mapOption);


	/*마우스 좌표 컨트롤 생성*/
	var mousePositionControl = new odf.MousePositionControl({
		//특정 element에 표시
		element : document.querySelector('#coordDiv'),
	//   callback: function (position) {
	//     console.log(position);
	//   },
	});
	mousePositionControl.setMap(map);

	var fullScreenControl  = new odf.FullScreenControl();
	fullScreenControl.setMap(map);
</script>
</html>
"""

스와이퍼
지도스와이퍼 기능 제공 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map"></div>
	<div style="margin-top: 15px">
		<input type="button" id="changeStrictMode" class="onoffBtn toggle" onclick="changeStrictMode()" value="strict모드 변경">
	</div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(199312.9996,551784.6924);
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};
	var map = new odf.Map(mapContainer, mapOption);


	var basemapControl = new odf.BasemapControl();
	basemapControl.setMap(map);

	var wfsLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : 'https://geoserver.geon.kr/geoserver',
		layer : 'geonpaas:L100000254',
		service : 'wfs',
	});
	wfsLayer.setMap(map);
	map.setCenter([192396.63847319243, 534166.8213405443]);
	map.setZoom(14);

	//스와이퍼 컨트롤 추가
	var swiperControl = new odf.SwiperControl({
		/**기존 사용중이던 레이어를 swiper레이어로 이용
		 - true : 현재 지도에서 사용중인 레이어를 왼쪽 영영에 표출(기본값)
		 - false : 왼쪽/오른쪽 영역 표출 레이어 직접 지정
		*/
		useOriginalLayerFlag : true,//원본레이어를 왼쪽영역에 나타낼지 여부, 기본값 true

		/** 엄격한 모드 사용 여부
		   ※ useOriginalLayerFlag 값이 true 일경우에만 적용
		 - true : 배경지도를 제외한 레이어를 왼쪽 영역에만 표출
		 - false : 배경지도를 제외한 레이어를 모든 영역에 표출(기본값)
		*/
		swipeStrictFlag : false,

		// 스와이퍼 컨트롤의 슬라이더 너비 (픽셀)
		// default값 100. 최소 0, 최대 2000
		size : 200,

		/**스와이퍼로 나타낼 레이어 배열
		 - [레이어1, 레이어2, ...] : useOriginalLayerFlag가 true일때 이와 같은 양식 적용.
		  						 오른쪽 영역에 표출할 레이어 목록 정의
		 - [[레이어1, 레이어2, ...],[레이어5, 레이어6, ...]] : useOriginalLayerFlag가 false일때 이와 같은 양식 적용.
		 											  [왼쪽 영역에 표출할 레이어 배열, 오른쪽 영역에 표출할 레이어 배열]
		*/
		layers : [ basemapControl.getBaseLayer('eMapAIR') ],
	});
	swiperControl.setMap(map);

	//layers 값 셋팅
	//swiperControl.setLayers([ basemapControl.getBaseLayer('eMapColor') ]);

	//SwiperControl의 슬라이더 값 셋팅
	//swiperControl.setSliderValue(30/*셋팅할 슬라이더 값 (0~100사이의 숫자)*/);

	//SwiperControl의 슬라이더 값 조회
	//console.log(swiperControl.getSliderValue());

	//SwiperControl에 엄격모드 적용
	//swiperControl.setSwipeStrictFlag(true);

	//슬라이더 적용 여부 정의
	//swiperControl.setState(true/*슬라이더 적용 여부(true=>적용/false=>미적용)*/);

	//슬라이더 적용 여부 조회
	//console.log(swiperControl.getState());


	var strictMode = false;
	function changeStrictMode() {
		//SwiperControl에 엄격모드 적용
		swiperControl.setSwipeStrictFlag(strictMode);
		strictMode = !strictMode;
	}
</script>
</html>
"""

분할지도
2/3/4 분할지도 기능 제공 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map"></div>
	<div style="margin-top: 15px">
		<input type="button" class="onoffOnlyBtn toggle grp1" onclick="setConnect(true)" value="동기화">
		<input type="button" class="onoffOnlyBtn toggle grp1" onclick="setConnect(false)" value="비동기화">
		<input type="button" class="onoffOnlyBtn toggle grp2" onclick="setOn('dualMap',true)" value="2분할 열기">
		<input type="button" class="onoffOnlyBtn toggle grp2" onclick="setOn('quadMap',true)" value="4분할 열기">
		<input type="button" class="onoffOnlyBtn toggle grp2" onclick="setOn('dualMap',false)" value="분할 닫기">
	</div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(199312.9996,551784.6924);
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};
	var map = new odf.Map(mapContainer, mapOption);


	/* 배경지도 컨트롤 생성 */
	var basemapControl = new odf.BasemapControl();
	basemapControl.setMap(map);

	/* 줌 컨트롤 생성 */
	var zoomControl = new odf.ZoomControl();
	zoomControl.setMap(map);

	var dmc = new odf.DivideMapControl({
		dualMap : [
			{
				position : 1,
				mapOption : {
					// 해당 분할지도의 basemap 옵션
					// 정의하지 않는 경우, map 객체 생성시 사용한 basemap option 사용
					basemap : {
						baroEMap : [ 'eMapWhite' ]
					},
				},
				// 사용할 컨트롤 지정
				// 정의하지 않는 경우, 기본 값 적용(배경지도 컨트롤만 이용)
				controlOption : {
		             basemap: true,// 기본값 true
		             zoom: false,// 기본값 false
		             clear: false,// 기본값 false
		             download: false,// 기본값 false
		             print: false,// 기본값 false
		             overviewmap: false,// 기본값 false
		             draw: false,// 기본값 false
		             measure: false,// 기본값 false
		             move: false,// 기본값 false
				}
		 	}
		 ],
		 threepleMap: [
			{
				// position: 1, //지정안하면 기본 1
				mapOption: {
					basemap: {
						baroEMap :['eMapWhite']
					},
				},
			},
			{
				// position: 2, //지정안하면 기본 3
				mapOption: {
					basemap:{
						baroEMap : ['eMapColor']
					},
				},
		    }
		],
		quadMap : [
			{
				// position: 1, //지정안하면 기본 1
				mapOption : {
					basemap : {
						baroEMap  : [ 'eMapWhite' ]
					},
				},
			},
			{
				//position: 2, //지정안하면 기본 3
				mapOption : {
					basemap : {
						baroEMap  : [ 'eMapColor' ]
					},
				},
			},
			{
				//position: 4,//지정안하면 기본 4
				mapOption : {
					basemap : {
						baroEMap  : [ 'eMapAIR' ]
					},
				},
				controlOption : {//사용할 컨트롤 지정
					download : true,
				},
			}
		],
		// 분할지도 상세 생성 옵션
		// 정의하지 않으면 기본 값 적용
		config : {
			//분할지도 내 컨트롤 ui 생성 여부, 기본값 true
			createElementFlag : true,
			// 2분할지도 상세 생성 옵션
			dualMap : {
			 /** 2분할지도 분할 유형.
			   * - 'vertical' : 수직 분할 (기본값)
			   * ┌─┬─┐
			   * │1│2│
			   * └─┴─┘
			   * - 'horizonal' : 수평 분할
			   * ┌───┐
			   * │ 1 │
			   * ├───┤
			   * │ 2 │
			   * └───┘
			   */
				divType : 'vertical'//수직 분할 (기본값)
			},
			//3분할지도 상세 생성 옵션
			threepleMap : {
			 /** 3분할지도 분할 유형
			   * - 'vertical' : 수직 분할 (기본값)
			   * ┌─┬─┬─┐
			   * │1│2│3│
			   * └─┴─┴─┘
			   * - 'horizonal' : 수평 분할
			   * ┌───┐
			   * │ 1 │
			   * ├───┤
			   * │ 2 │
			   * ├───┤
			   * │ 3 │
			   * └───┘
			   * - 'complex-01' : 복합형 1
			   * ┌─┬───┐
			   * │ │ 2 │
			   * │1├───┤
			   * │ │ 3 │
			   * └─┴───┘
			   * - 'complex-02' : 복합형 2
			   * ┌─────┐
			   * │  1  │
			   * ├──┬──┤
			   * │2 │ 3│
			   * └──┴──┘
			   * - 'complex-03' : 복합형 3
			   * ┌──┬─┐
			   * │2 │ │
			   * ├──┤1│
			   * │3 │ │
			   * └──┴─┘
			   * - 'complex-04' : 복합형 4
			   * ┌──┬──┐
			   * │ 1│2 │
			   * ├──┴──┤
			   * │  3  │
			   * └─────┘
			   */
				divType : 'vertical'//수직 분할 (기본값)
			},
			//3분할지도 상세 생성 옵션
			quadMap : {
			 /**
			   * - 'complex' : 수직,수평 분할 (기본값)
			   * ┌───┬───┐
			   * │ 1 │ 2 │
			   * ├───┼───┤
			   * │ 3 │ 4 │
			   * └───┴───┘
			   * - 'vertical' : 수직 분할
			   * ┌─┬─┬─┬─┐
			   * │1│2│3│4│
			   * └─┴─┴─┴─┘
			   * - 'horizonal' : 수평 분할
			   * ┌─────┐
			   * │  1  │
			   * ├─────┤
			   * │  2  │
			   * ├─────┤
			   * │  3  │
			   * ├─────┤
			   * │  4  │
			   * └─────┘
			   */
				divType : 'complex'//수직,수평 분할 (기본값)
			}
		}
	});
	dmc.setMap(map);
	map.setResizable(true);
	function setConnect(flag) {
		dmc.setConnect(flag);
	}

	function setOn(key, flag) {
		dmc.setOn(key, flag);
	}
</script>
</html>
"""

분할지도 상세
메인지도의 컨트롤/레이어 복사하여 분할된 지도에 적용하는 기능 제공 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
</head>
<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
<body>
		<div id="map" ></div>
		<div style="margin-top:15px">
			<input type="button" class="onoffOnlyBtn toggle grp1" onclick="setConnect(true)"  value="동기화">
			<input type="button" class="onoffOnlyBtn toggle grp1" onclick="setConnect(false)"  value="비동기화">
		</div>
</body>
<script>

      /* 맵객체1 생성 */
	  var mapContainer = document.getElementById('map');

	  /* 맵 중심점 */
	  var coord = new odf.Coordinate(199312.9996,551784.6924);

	  /* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	  var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};

	  /* 맵객체 생성 */
	  var map = new odf.Map(mapContainer, mapOption);

      var basemapControl = new odf.BasemapControl();
      basemapControl.setMap(map);

      /* 줌 컨트롤 생성 */
  	  var zoomControl = new odf.ZoomControl();
  	  zoomControl.setMap(map);


      /* 이전,다음 컨트롤 생성 */
      var moveControl = new odf.MoveControl();
      moveControl.setMap(map);


      /* 인덱스맵 컨트롤 생성 */
      var overviewMapControl = new odf.OverviewMapControl();
      overviewMapControl.setMap(map);

      /* 그리기 도구 컨트롤 생성 */
      var drawControl = new odf.DrawControl();
      drawControl.setMap(map);
      /* 측정도구 컨트롤 생성 */
      var measureControl = new odf.MeasureControl( );
        measureControl.setMap(map);
      /* 초기화 컨트롤 생성 */
      var clearControl = new odf.ClearControl();
      clearControl.setMap(map);

        /* 출력 컨트롤 생성 */
      var printControl = new odf.PrintControl();
        printControl.setMap(map);

        /* 저장 컨트롤 생성 */
      var downloadControl = new odf.DownloadControl();
        downloadControl.setMap(map);

        /* 전체화면 컨트롤 생성 */
      var fullScreenControl = new odf.FullScreenControl();
      fullScreenControl.setMap(map);

      /* 회전 컨트롤 생성 */
      var rotationControl = new odf.RotationControl();
      rotationControl.setMap(map);

  	var dmc = new odf.DivideMapControl({
        dualMap: [
          {
            position: 1,
            mapOption: {
              //지정안한 map옵션은 mainmap 생성시 사용한 mapoption적용
              basemap:{
            	    baroEMap : [ 'eMapWhite']
          	  } ,
            },
           controlOption: {//사용할 컨트롤 지정
            basemap: false,
            // zoom: false,
            // clear: false,
            // download: false,
            // print: false,
            // overviewmap: false,
            // draw: false,
            // measure: false,
            // move: false,
            },
          },
        ],
      });
      dmc.setMap(map);
      map.setResizable(true);

      /*폴리곤 레이어 추가*/
      var polygon = odf.LayerFactory.produce('geoserver', {
        // 레이어 호출 방법 (ex. geoserver, geojson)
        method: 'get',
        server: 'https://geoserver.geon.kr/geoserver', // 레이어가 발행된 서버 주소 | 호출 API 주소
        layer: 'geonpaas:L100000254', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
        service: 'wms', // 호출하고자 하는 레이여 형태(wms, wfs, wmts)
      });
      polygon.setMap(map);
      polygon.fit();

      var dMap = dmc.getDividMaps().dualMap.filter(function(o){return o.mainMapFlag===false});


      //메인지도의 컨트롤 복사
  	  var  controls = map.getODFControls();
  		controls.forEach((control, key) => {
  		if(key!=='dividemap'){
  			var newControl;
  			var constructorOption = control.getConstructorOptions();

  			if(constructorOption.length==0){
  				newControl = new control.constructor()
  			}
  			else if(constructorOption.length==1){
  				newControl = new control.constructor(constructorOption[0]);
  			}
  			else if(constructorOption.length==2){
  				newControl = new control.constructor(constructorOption[0],constructorOption[1]);
  			}
  			if(newControl){
  				newControl.setMap(dMap[0].map);
  			}
  		}
      });

	  // 레이어 복사
	  var layers = map.getODFLayers().filter(function(layer){
		  if(layer.getODFId()==='odf-layer-draw'||layer.getODFId()==='odf-layer-measure'){
			  return false;
		  }
		  return true;
	  }).map(function(layer){
		  var initalOption = layer.getInitialOption();
		  return odf.LayerFactory.produce(initalOption.type,initalOption.params);
	  })
	  dMap[0].map.switchLayerList(layers);

      function setConnect(flag){
  		dmc.setConnect(flag);
  	 }
    </script>
</html>
"""

사용자 정의 2분할 지도
사용자 정의대로 2분할 지도를 생성하는 기능 제공 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map">
		<div id="map1" style="width: 49.5%; float: left; border: 1px black solid; border-collapse: collapse;"></div>
		<div id="map2" style="width: 49.5%; float: left; border: 1px black solid; border-collapse: collapse;"></div>
	</div>
	<input type="button" id="chageConnectFlag" class="onoffBtn toggle" value="동기화여부 변경" style="margin-top: 15px">
</body>
<script>

	/* 맵객체1 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer1 = document.getElementById('map1');
	var coord = new odf.Coordinate(199312.9996,551784.6924);
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};
	var map1 = new odf.Map(mapContainer1, mapOption);


	/* 배경지도 컨트롤1 생성 */
	var basemapControl1 = new odf.BasemapControl();
	basemapControl1.setMap(map1);
	map1.updateSize();

	/* 줌 컨트롤 생성 */
	var zoomControl = new odf.ZoomControl();
	zoomControl.setMap(map1);

	/* 이전,다음 컨트롤 생성 */
	var moveControl = new odf.MoveControl();
	moveControl.setMap(map1);

	/* 맵객체2 생성 */
	var mapContainer2 = document.getElementById('map2');
	var map2 = new odf.Map(mapContainer2, mapOption);

	/* 배경지도 컨트롤2 생성 */
	var basemapControl2 = new odf.BasemapControl();
	basemapControl2.setMap(map2);


	//동기화 여부 변경
	var connectMapFlag = false;
	document.getElementById('chageConnectFlag').addEventListener('click',
		function(evt) {
			if (connectMapFlag) {
				map2.connectOtherMap(map1, false);
			} else {
				map2.connectOtherMap(map1);
			}
			connectMapFlag = !connectMapFlag;
		}
	);
</script>
</html>
"""

사용자 정의 4분할 지도
사용자 정의대로 4분할 지도를 생성하는 기능 제공 샘플코드
"""
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">
	<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>
</head>
<body>
	<div id="map">
		<div id="map1" style="width: 49.5%; float: left; border: 1px black solid; border-collapse: collapse;"></div>
		<div id="map2" style="width: 49.5%; float: left; border: 1px black solid; border-collapse: collapse;"></div>
		<div id="map3" style="width: 49.5%; float: left; border: 1px black solid; border-collapse: collapse;"></div>
		<div id="map4" style="width: 49.5%; float: left; border: 1px black solid; border-collapse: collapse;"></div>
	</div>
	<input type="button" id="chageConnectFlag" class="onoffBtn toggle" value="동기화여부 변경" style="margin-top: 15px">
</body>
<script>

	/* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var coord = new odf.Coordinate(199312.9996,551784.6924);
	var mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5186',
		//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		baroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',
		baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',

		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
	};

	// 맵객체1 생성
	var mapContainer = document.getElementById('map1');
	var map1 = new odf.Map(mapContainer, mapOption);

	// 맵객체1에 컨트롤 셋팅
	var basemapControl1 = new odf.BasemapControl();
	basemapControl1.setMap(map1);
	map1.updateSize();

	var zoomControl = new odf.ZoomControl();
	zoomControl.setMap(map1);

	var moveControl = new odf.MoveControl();
	moveControl.setMap(map1);

	//  맵객체2 생성
	var mapContainer2 = document.getElementById('map2');
	var map2 = new odf.Map(mapContainer2, mapOption);

	// 맵객체2에 컨트롤 셋팅
	var basemapControl2 = new odf.BasemapControl();
	basemapControl2.setMap(map2);

	/* 맵객체3 생성 */
	var mapContainer3 = document.getElementById('map3');
	var map3 = new odf.Map(mapContainer3, mapOption);

	// 맵객체3에 컨트롤 셋팅
	var basemapControl3 = new odf.BasemapControl();
	basemapControl3.setMap(map3);

	/* 맵객체4 생성 */
	var mapContainer4 = document.getElementById('map4');
	var map4 = new odf.Map(mapContainer4, mapOption);

	// 맵객체4에 컨트롤 셋팅
	var basemapControl4 = new odf.BasemapControl();
	basemapControl4.setMap(map4);


	// 동기화 여부 변경
	var connectMapFlag = false;
	document.getElementById('chageConnectFlag').addEventListener('click',
		function(evt) {
			if (connectMapFlag) {
				map2.connectOtherMap(map1, false);
				map3.connectOtherMap(map1, false);
				map4.connectOtherMap(map1, false);
			} else {
				map2.connectOtherMap(map1);
				map3.connectOtherMap(map1);
				map4.connectOtherMap(map1);
			}
			connectMapFlag = !connectMapFlag;
		}
	);
</script>
</html>
"""

