"use client";

import React, { useState, useCallback } from "react";
import {
  MapPin, Check, Navigation, Building2, Copy, ChevronDown, ChevronUp,
  Route, MoreHorizontal, Star
} from "lucide-react";
import { AddressResponse } from "@/types/tools";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { highlightPointOnMap, type UseMapReturn } from "@/lib/map-utils";
import { componentStyles } from "@/lib/design-tokens";
import { CompactResultTrigger } from "./common/compact-result-trigger";
import { getToolDisplayInfo } from "../annotations";
import { Badge } from "../ui/badge";

interface SearchAddressResultProps {
  content: AddressResponse;
  className?: string;
  mapState?: UseMapReturn;
  onDirectionsRequest?: (origin: string, destination: string) => void;
}

// 주소 아이템 타입 정의
interface AddressItem {
  roadAddr: string;
  jibunAddr: string;
  buildName: string;
  buildLo: string;
  buildLa: string;
  parcelLo: string;
  parcelLa: string;
  poiName: string;
  buildGeom?: string;
  geom?: string;
}

// 애니메이션 제거 - 정적 렌더링만 사용

export function SearchAddressResult({
  content,
  className,
  mapState,
  onDirectionsRequest
}: SearchAddressResultProps) {
  const [showAll, setShowAll] = useState(false);
  const [selectedOrigin, setSelectedOrigin] = useState<AddressItem | null>(null);
  const [selectedDestination, setSelectedDestination] = useState<AddressItem | null>(null);

  let result: AddressResponse;

  try {
    result = content as AddressResponse;
  } catch (e) {
    return null; // 파싱 실패시 기본 UI로 fallback
  }

  // 자동 이동 로직 제거 - 이제 ToolInvocationProvider에서 처리

  // 좌표를 searchDirections 형식으로 변환하는 함수
  const formatCoordinateForDirections = useCallback((address: AddressItem): string => {
    const longitude = parseFloat(address.buildLo);
    const latitude = parseFloat(address.buildLa);
    const name = address.buildName || address.poiName || address.roadAddr.split(' ').slice(0, 2).join(' ');
    return `${longitude},${latitude},name=${name}`;
  }, []);

  // 길찾기 요청 함수
  const handleDirectionsRequest = useCallback((origin: AddressItem, destination: AddressItem) => {
    const originStr = formatCoordinateForDirections(origin);
    const destinationStr = formatCoordinateForDirections(destination);

    if (onDirectionsRequest) {
      onDirectionsRequest(originStr, destinationStr);
    }

    toast.success(`${origin.buildName || '출발지'}에서 ${destination.buildName || '목적지'}까지 경로를 검색합니다`);
  }, [formatCoordinateForDirections, onDirectionsRequest]);

  const handleCopyAddress = useCallback((address: string) => {
    navigator.clipboard.writeText(address);
    toast.success("주소가 복사되었습니다");
  }, []);

  const handleNavigate = useCallback((address: AddressItem) => {
    if (!mapState) {
      toast.error("지도가 로드되지 않았습니다");
      return;
    }

    const success = highlightPointOnMap(
      mapState,
      parseFloat(address.buildLo),
      parseFloat(address.buildLa),
      address.buildGeom || address.geom,
      16
    );

    if (success) {
      toast.success(`${address.roadAddr}로 이동했습니다`);
    } else {
      toast.error("지도 이동 중 오류가 발생했습니다");
    }
  }, [mapState]);

  // 출발지 설정
  const handleSetAsOrigin = useCallback((address: AddressItem) => {
    setSelectedOrigin(address);
    toast.success(`출발지로 설정: ${address.buildName || address.roadAddr}`);
  }, []);

  // 목적지 설정
  const handleSetAsDestination = useCallback((address: AddressItem) => {
    setSelectedDestination(address);
    toast.success(`목적지로 설정: ${address.buildName || address.roadAddr}`);
  }, []);

  // 선택된 출발지와 목적지로 길찾기
  const handleStartDirections = useCallback(() => {
    if (selectedOrigin && selectedDestination) {
      handleDirectionsRequest(selectedOrigin, selectedDestination);
      // 선택 초기화
      setSelectedOrigin(null);
      setSelectedDestination(null);
    }
  }, [selectedOrigin, selectedDestination, handleDirectionsRequest]);

  if (!result.result?.jusoList?.length) {
    return (
      <div className={cn("rounded-xl border border-amber-200/60 bg-gradient-to-r from-amber-50/80 to-orange-50/80 backdrop-blur-sm p-3", className)}>
        <div className="flex items-center gap-3">
          <div className="flex h-7 w-7 items-center justify-center rounded-full bg-amber-100/80">
            <MapPin className="h-3.5 w-3.5 text-amber-600" />
          </div>
          <div>
            <p className="font-medium text-amber-900 text-sm">검색 결과가 없습니다</p>
            <p className="text-xs text-amber-700">다른 키워드로 다시 검색해보세요</p>
          </div>
        </div>
      </div>
    );
  }

  const addresses = result.result.jusoList;
  const displayAddresses = showAll ? addresses : addresses.slice(0, 3);

  const toolInfo = getToolDisplayInfo("searchAddress");

  return (
    <CompactResultTrigger
      icon={toolInfo.icon}
      title={toolInfo.label}
      state="result"
      className={className}
      titleExtra={
        <Badge variant="outline" className="text-xs py-0 border bg-emerald-100/80 text-emerald-700 border-emerald-200/60">
          {addresses.length}개 발견
        </Badge>
      }
    >
      <div className="space-y-2">

      {/* 길찾기 상태 표시 */}
      {(selectedOrigin || selectedDestination) && (
        <div className={cn(
          componentStyles.card.base,
          "p-3 bg-gradient-to-r from-blue-50/95 to-indigo-50/90 border-blue-200/60"
        )}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Route className="h-4 w-4 text-blue-600" />
              <div className="text-sm">
                <span className="font-medium text-blue-900">길찾기 설정 중</span>
                <div className="flex items-center gap-2 mt-1 text-xs text-blue-700">
                  {selectedOrigin && (
                    <span className="bg-blue-100 px-2 py-0.5 rounded">
                      출발: {selectedOrigin.buildName || selectedOrigin.roadAddr.split(' ').slice(0, 2).join(' ')}
                    </span>
                  )}
                  {selectedDestination && (
                    <span className="bg-blue-100 px-2 py-0.5 rounded">
                      도착: {selectedDestination.buildName || selectedDestination.roadAddr.split(' ').slice(0, 2).join(' ')}
                    </span>
                  )}
                </div>
              </div>
            </div>
            {selectedOrigin && selectedDestination && (
              <Button
                size="sm"
                onClick={handleStartDirections}
                className={cn(componentStyles.button.primary, "h-8 px-3 text-xs")}
              >
                <Route className="h-3 w-3 mr-1" />
                길찾기 시작
              </Button>
            )}
          </div>
        </div>
      )}

      {/* 주소 목록 */}
      <div className="space-y-2">
        {displayAddresses.map((address, index) => (
          <div key={index} className="group">
            <Card className={cn(
              componentStyles.card.base,
              componentStyles.card.interactive,
              "cursor-pointer bg-white/90 border-neutral-200/60 hover:border-blue-300/70",
              "hover:bg-gradient-to-r hover:from-blue-50/60 hover:to-indigo-50/40",
              "shadow-sm hover:shadow-md transition-all duration-200"
            )}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between gap-4">
                    <div className="flex-1 min-w-0">
                      {/* 메인 주소 */}
                      <div className="flex items-start gap-3 mb-2">
                        <div className={cn(
                          componentStyles.iconContainer.sm,
                          "bg-blue-500/10 border border-blue-200/50 mt-0.5 flex-shrink-0"
                        )}>
                          <MapPin className="h-3 w-3 text-blue-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-semibold text-neutral-900 text-sm leading-tight">
                            {address.roadAddr}
                          </p>
                          {address.buildName && (
                            <div className="flex items-center gap-1.5 mt-1">
                              <Building2 className="h-3 w-3 text-blue-600 flex-shrink-0" />
                              <p className="text-xs text-blue-700 font-medium truncate">
                                {address.buildName}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* 지번 주소 */}
                      <p className="text-xs text-neutral-600 ml-9 truncate bg-neutral-50/80 px-2 py-1 rounded-md">
                        지번: {address.jibunAddr}
                      </p>
                    </div>

                    {/* 개선된 액션 버튼들 */}
                    <div className="flex items-center gap-2">
                      {/* 항상 보이는 주요 액션 */}
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          className={cn(
                            componentStyles.button.ghost,
                            "h-8 px-2 text-xs hover:bg-blue-100/80 hover:text-blue-700 rounded-lg"
                          )}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleNavigate(address);
                          }}
                        >
                          <Navigation className="h-3 w-3 mr-1" />
                          보기
                        </Button>
                      </div>

                      {/* 드롭다운 메뉴 */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            size="sm"
                            variant="ghost"
                            className={cn(
                              componentStyles.button.ghost,
                              "h-8 w-8 p-0 hover:bg-neutral-100/80 rounded-lg"
                            )}
                          >
                            <MoreHorizontal className="h-3.5 w-3.5" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem
                            onClick={() => handleSetAsOrigin(address)}
                            className="flex items-center gap-2"
                          >
                            <Star className="h-3.5 w-3.5 text-green-600" />
                            출발지로 설정
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleSetAsDestination(address)}
                            className="flex items-center gap-2"
                          >
                            <MapPin className="h-3.5 w-3.5 text-red-600" />
                            목적지로 설정
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleCopyAddress(address.roadAddr)}
                            className="flex items-center gap-2"
                          >
                            <Copy className="h-3.5 w-3.5" />
                            주소 복사
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
      </div>

      {/* 더보기/접기 버튼 */}
      {addresses.length > 3 && (
        <div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAll(!showAll)}
            className={cn(
              componentStyles.button.secondary,
              "w-full h-9 text-xs font-medium border-dashed border-neutral-300",
              "hover:border-solid hover:border-blue-300 hover:bg-blue-50/60 hover:text-blue-700",
              "transition-all duration-200"
            )}
          >
            {showAll ? (
              <>
                <ChevronUp className="h-3.5 w-3.5 mr-2" />
                접기
              </>
            ) : (
              <>
                <ChevronDown className="h-3.5 w-3.5 mr-2" />
                {addresses.length - 3}개 더보기
              </>
            )}
          </Button>
        </div>
      )}
      </div>
    </CompactResultTrigger>
  );
}