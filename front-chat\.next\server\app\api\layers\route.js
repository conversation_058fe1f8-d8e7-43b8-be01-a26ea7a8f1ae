(()=>{var e={};e.id=920,e.ids=[920],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16387:(e,r,t)=>{"use strict";t.d(r,{Jh:()=>s,j5:()=>a,nK:()=>o,rZ:()=>n});let s=()=>{let e=process.env.GEON_API_BASE_URL||"http://121.163.19.101:14090",r=process.env.GEON_API_KEY;return r||console.warn("GEON_API_KEY가 설정되지 않았습니다."),{baseUrl:e,headers:{crtfckey:r||""},auth:{userId:process.env.GEON_API_USER_ID||"geonuser",password:process.env.GEON_API_USER_PASSWORD||"wavus1234!"}}},a=(e,r)=>{let t=r||s();return t.headers.crtfckey&&e.append("crtfckey",t.headers.crtfckey),e},n=e=>({"Content-Type":"application/json",...(e||s()).headers}),o=e=>(e||s()).auth.userId},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},37758:()=>{},39772:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>u});var a=t(81160),n=t(18765),o=t(46332),p=t(28481),i=t(16387);async function u(e){try{let{searchParams:r}=new URL(e.url),t=(0,i.Jh)(),s=new URLSearchParams;s.append("userId",(0,i.nK)(t)),s.append("holdDataSeCode",r.get("holdDataSeCode")||"0"),s.append("pageIndex",r.get("pageIndex")||"1"),s.append("pageSize",r.get("pageSize")||"10");let a=r.get("searchTxt");a&&""!==a.trim()&&s.append("searchTxt",a.trim());let n=r.get("lyrTySeCode");n&&""!==n.trim()&&s.append("lyrTySeCode",n.trim()),(0,i.j5)(s,t);let o=await fetch(`${t.baseUrl}/smt/layer/info/list?${s.toString()}`,{method:"GET",headers:(0,i.rZ)(t)});if(!o.ok){let e=await o.json().catch(()=>({}));return console.error("API request failed with data",e),p.NextResponse.json({error:`API request failed with status ${o.status}`},{status:o.status})}let u=await o.json();if(!u||!u.result)return p.NextResponse.json({error:"레이어 목록 조회 실패: 응답 데이터가 없습니다."},{status:500});return p.NextResponse.json(u)}catch(e){return console.error("Layer list API error:",e),p.NextResponse.json({error:`레이어 목록 조회 실패: ${e.message}`},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/layers/route",pathname:"/api/layers",filename:"route",bundlePath:"app/api/layers/route"},resolvedPagePath:"C:\\chatbot\\front-chat\\app\\api\\layers\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:h}=d;function x(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77926:()=>{},81160:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[332,481],()=>t(39772));module.exports=s})();