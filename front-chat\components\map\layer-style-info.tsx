import { Badge } from "@/components/ui/badge"
import {useState} from "react";
import {ChevronDown, ChevronUp} from "lucide-react";
import {AnimatePresence, motion} from "framer-motion";

type StyleInfo = {
	type: 'wfs' | 'wms'
	style: WMSStyle | WFSStyle | string
}

type WMSStyle = {
	rules: Array<{
		name: string;
		symbolizers: Array<{
			kind: string;
			wellKnownName?: string;
			radius?: number;
			color?: string;
			fillOpacity?: number;
			strokeColor?: string;
			strokeWidth?: number;
			[key: string]: any;
		}>;
	}>;
}

type WFSStyle = {
	[key: string]: any;
}


export function LayerStyleInfo({ styleInfo }: { styleInfo: StyleInfo }) {
	const [isOpen, setIsOpen] = useState(false);
	const { type, style } = styleInfo;

	const styleData = typeof style === 'string' ? JSON.parse(style) : style;

	return (
		<div className="w-full rounded-lg border bg-card text-card-foreground shadow-sm">
			{/* 헤더 - 항상 표시 */}
			<button
				onClick={() => setIsOpen(!isOpen)}
				className="w-full flex items-center justify-between p-4 hover:bg-muted/50 transition-colors"
			>
				<div className="flex items-center gap-3">
					<h3 className="font-semibold">스타일</h3>
					<Badge variant="outline" className="text-xs">
						{type.toUpperCase()}
					</Badge>
				</div>
				{isOpen ? (
					<ChevronUp className="h-4 w-4 text-muted-foreground" />
				) : (
					<ChevronDown className="h-4 w-4 text-muted-foreground" />
				)}
			</button>

			{/* 상세 내용 - 토글 가능 */}
			<AnimatePresence>
				{isOpen && (
					<motion.div
						initial={{ height: 0, opacity: 0 }}
						animate={{ height: "auto", opacity: 1 }}
						exit={{ height: 0, opacity: 0 }}
						transition={{ duration: 0.2 }}
						className="overflow-hidden"
					>
						<div className="p-4 border-t">
							{styleData.rules?.map((rule: any, index: number) => (
								<StyleRule key={index} rule={rule} />
							))}
						</div>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
}

// 개별 스타일 룰 컴포넌트
function ColorSwatch({ color, label }: { color: string; label?: string }) {
	return (
		<div className="flex items-center gap-2">
			<div className="relative group">
				<div
					className="size-6 rounded border border-border shadow-sm"
					style={{ backgroundColor: color }}
				/>
				{/* Tooltip */}
				<div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-2 py-1 bg-popover rounded text-xs
                      opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
					{color}
				</div>
			</div>
			{label && <span className="text-sm text-muted-foreground">{label}</span>}
		</div>
	)
}

function StyleProperty({ name, value }: { name: string; value: any }) {
	// 색상 관련 속성 체크
	const isColorProperty = ['color', 'strokeColor', 'outlineColor'].includes(name);

	if (isColorProperty) {
		return (
			<div className="flex items-center justify-between py-2">
				<span className="text-sm font-medium">{name}</span>
				<ColorSwatch color={value as string} />
			</div>
		);
	}

	// 투명도 관련 속성은 퍼센트로 표시
	const isOpacityProperty = ['fillOpacity', 'outlineOpacity', 'opacity'].includes(name);
	if (isOpacityProperty) {
		const percentage = Math.round((Number(value) * 100));
		return (
			<div className="flex items-center justify-between py-2">
				<span className="text-sm font-medium">{name}</span>
				<span className="text-sm">{percentage}%</span>
			</div>
		);
	}

	return (
		<div className="flex items-center justify-between py-2">
			<span className="text-sm font-medium">{name}</span>
			<code className="px-2 py-0.5 bg-muted rounded text-xs">
				{typeof value === 'string' ? value : JSON.stringify(value)}
			</code>
		</div>
	);
}

function StyleRule({ rule }: { rule: any }) {
	const [isOpen, setIsOpen] = useState(false);

	return (
		<div className="rounded-md border mb-3 last:mb-0">
			<button
				onClick={() => setIsOpen(!isOpen)}
				className="w-full flex items-center justify-between p-3 hover:bg-muted/50 transition-colors"
			>
				<span className="font-medium">{rule.name}</span>
				{isOpen ? (
					<ChevronUp className="h-4 w-4 text-muted-foreground" />
				) : (
					<ChevronDown className="h-4 w-4 text-muted-foreground" />
				)}
			</button>

			<AnimatePresence>
				{isOpen && (
					<motion.div
						initial={{ height: 0, opacity: 0 }}
						animate={{ height: "auto", opacity: 1 }}
						exit={{ height: 0, opacity: 0 }}
						transition={{ duration: 0.2 }}
						className="overflow-hidden"
					>
						{rule.symbolizers?.map((symbolizer: any, symIndex: number) => (
							<div key={symIndex} className="p-4 border-t">
								<div className="space-y-4">
									<Badge variant="secondary">
										{symbolizer.kind}
									</Badge>

									<div className="space-y-2">
										{Object.entries(symbolizer)
											.filter(([key]) => key !== 'kind')
											.map(([key, value]) => (
												<StyleProperty
													key={key}
													name={key}
													value={value}
												/>
											))}
									</div>
								</div>
							</div>
						))}
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
}
