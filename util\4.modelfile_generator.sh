#!/bin/bash

# 인자로 받은 경로를 변수에 저장
base_dir="$1"

# 경로가 비어있지 않고 디렉토리인 경우에만 작업 수행
if [ -d "$base_dir" ]; then
  # 모든 하부 디렉토리를 순회
  for dir in "$base_dir"/*/; do
    # 디렉토리명을 추출 (마지막 슬래시 제거)
    dirname=$(basename "$dir")

    # Modelfile 경로 설정
    modelfile_path="${dir}Modelfile"

    # Modelfile이 이미 존재하면 삭제
    if [ -f "$modelfile_path" ]; then
      rm "$modelfile_path"
    fi

    # llama-3 또는 llama3가 디렉토리명에 포함된 경우의 파일 내용 작성
    if [[ "$dirname" == *"llama-3"* || "$dirname" == *"llama3"* ]]; then
      file_content=$(cat <<'EOF'

FROM "$dirname.gguf"
TEMPLATE """{{ if .System }}<|start_header_id|>system<|end_header_id|>

{{ .System }}<|eot_id|>{{ end }}{{ if .Prompt }}<|start_header_id|>user<|end_header_id|>

{{ .Prompt }}<|eot_id|>{{ end }}<|start_header_id|>assistant<|end_header_id|>

{{ .Response }}<|eot_id|>"""
SYSTEM A chat between a curious user and an artificial intelligence assistant. The assistant gives helpful, detailed, and polite answers to the user's questions.
PARAMETER stop <|start_header_id|>
PARAMETER stop <|end_header_id|>
PARAMETER stop <|eot_id|>
PARAMETER num_keep 24
EOF
      )
      file_content=$(echo "$file_content" | sed "s/\$dirname/$dirname/g")
-- INSERT -- W10: Warning: Changing a readonly file

    # qwen-2 또는 qwen2가 디렉토리명에 포함된 경우의 파일 내용 작성
    elif [[ "$dirname" == *"qwen-2"*|| "$dirname" == *"qwen2"*|| "$dirname" == *"Qwen2"*|| "$dirname" == *"Qwen-2"* ]]; then
      file_content=$(cat <<'EOF'
FROM "$dirname.gguf"

TEMPLATE """{{ if .System }}<|im_start|>system
{{ .System }}<|im_end|>
{{ end }}{{ if .Prompt }}<|im_start|>user
{{ .Prompt }}<|im_end|>
{{ end }}<|im_start|>assistant
{{ .Response }}<|im_end|>"""

PARAMETER stop <|im_start|>
PARAMETER stop <|im_end|>

EOF
      )
      file_content=$(echo "$file_content" | sed "s/\$dirname/$dirname/g")

    # codegemma가 디렉토리명에 포함된 경우의 파일 내용 작성
    elif [[ "$dirname" == *"codegemma"* ]]; then
      file_content=$(cat <<'EOF'
FROM "$dirname.gguf"

TEMPLATE """<start_of_turn>user
{{ if .System }}{{ .System }} {{ end }}{{ .Prompt }}<end_of_turn>
<start_of_turn>model
{{ .Response }}<end_of_turn>
"""
PARAMETER penalize_newline false
PARAMETER repeat_penalty 1
PARAMETER stop <start_of_turn>
PARAMETER stop <end_of_turn>
EOF
      )
      file_content=$(echo "$file_content" | sed "s/\$dirname/$dirname/g")

    # gemma-2 또는 gemma2가 디렉토리명에 포함된 경우의 파일 내용 작성
    elif [[ "$dirname" == *"gemma-2"* || "$dirname" == *"gemma2"* ]]; then
      file_content=$(cat <<'EOF'
FROM "$dirname.gguf"

TEMPLATE """<start_of_turn>user
{{ if .System }}{{ .System }} {{ end }}{{ .Prompt }}<end_of_turn>
<start_of_turn>model
{{ .Response }}<end_of_turn>
"""

PARAMETER stop <start_of_turn>
PARAMETER stop <end_of_turn>

EOF
      )
      file_content=$(echo "$file_content" | sed "s/\$dirname/$dirname/g")

    # gemma-2 또는 gemma2가 디렉토리명에 포함된 경우의 파일 내용 작성
    elif [[ "$dirname" == *"mistral-nemo"* || "$dirname" == *"Mistral-Nemo"* ]]; then
      file_content=$(cat <<'EOF'
FROM "$dirname.gguf"

TEMPLATE """
{{- if .Messages }}
{{- range $index, $_ := .Messages }}
{{- if eq .Role "user" }}
{{- if and (eq (len (slice $.Messages $index)) 1) $.Tools }}[AVAILABLE_TOOLS] {{ $.Tools }}[/AVAILABLE_TOOLS]
{{- end }}[INST] {{ if and $.System (eq (len (slice $.Messages $index)) 1) }}{{ $.System }}

{{ end }}{{ .Content }}[/INST]
{{- else if eq .Role "assistant" }}
{{- if .Content }} {{ .Content }}
{{- else if .ToolCalls }}[TOOL_CALLS] [
{{- range .ToolCalls }}{"name": "{{ .Function.Name }}", "arguments": {{ .Function.Arguments }}}
{{- end }}]
{{- end }}</s>
{{- else if eq .Role "tool" }}[TOOL_RESULTS] {"content": {{ .Content }}} [/TOOL_RESULTS]
{{- end }}
{{- end }}
{{- else }}[INST] {{ if .System }}{{ .System }}

{{ end }}{{ .Prompt }}[/INST]
{{- end }} {{ .Response }}
{{- if .Response }}</s>
{{- end }}
"""

PARAMETER stop [INST]
PARAMETER stop [\INST]

EOF
      )
      file_content=$(echo "$file_content" | sed "s/\$dirname/$dirname/g")

    # 기본 파일 내용 작성
    else
      file_content=$(cat <<'EOF'
FROM "$dirname.gguf"

TEMPLATE """{{- if .System }}
system {{ .System }}
{{- end }}
user
{{ .Prompt }}
assistant
"""

SYSTEM """A chat between a curious user and an artificial intelligence assistant. The assistant gives helpful, detailed responses."""

PARAMETER temperature 0
PARAMETER num_ctx 4096
PARAMETER stop
PARAMETER stop
PARAMETER stop
PARAMETER stop x
EOF
      )
      file_content=$(echo "$file_content" | sed "s/\$dirname/$dirname/g")
    fi

    # 내용을 Modelfile로 저장
    echo "$file_content" > "$modelfile_path"
  done

  echo "모든 Modelfile이 생성되었습니다!"
else
  echo "지정된 경로가 유효하지 않습니다: $base_dir"
  exit 1
fi