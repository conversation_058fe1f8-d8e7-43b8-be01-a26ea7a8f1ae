"use client";

import React, { useState } from "react";
import type { ToolInvocation } from "ai";
import { <PERSON>, Card<PERSON><PERSON>er, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";

interface Props {
  invocation: ToolInvocation;
  onDone: (result: any) => void;
}

// Map internal tool names to user-friendly labels
const TOOL_DISPLAY_NAMES: Record<string, string> = {
  chooseOption: "옵션 선택",
  getUserInput: "입력 요청",
  confirmWithCheckbox: "확인 요청",
  getLocation: "위치 정보 요청",
};

const getBadge = (state: "call" | "partial-call" | "result") => {
  switch (state) {
    case "call":
      return <Badge variant="outline" className="border-yellow-500 text-yellow-700">실행 중</Badge>;
    case "partial-call":
      return <Badge variant="outline" className="border-red-500 text-red-700">진행 중</Badge>;
    case "result":
      return <Badge variant="outline" className="border-green-500 text-green-700">완료</Badge>;
    default:
      return null;
  }
};

export function SuggestionCard({ invocation, onDone }: Props) {
  const { toolName, toolCallId, state } = invocation;
  const friendlyName = TOOL_DISPLAY_NAMES[toolName] ?? toolName;

  const renderInteractive = () => {
    switch (toolName) {
      case "chooseOption": {
        const { message, options } = (invocation.args || {}) as any;

        // 옵션 개수에 따라 그리드 컬럼 수 결정
        const getGridCols = (optionCount: number) => {
          if (optionCount <= 2) return "grid-cols-1 sm:grid-cols-2";
          if (optionCount <= 4) return "grid-cols-2 sm:grid-cols-2";
          if (optionCount <= 6) return "grid-cols-2 sm:grid-cols-3";
          return "grid-cols-2 sm:grid-cols-3 lg:grid-cols-4";
        };

        const gridCols = getGridCols(options?.length || 0);

        return (
          <>
            <p className="text-sm whitespace-pre-wrap break-all mb-2">{message}</p>
            <div className={`grid ${gridCols} gap-2`}>
              {options?.map((opt: string, index: number) => {
                // "레이어명|레이어ID" 형태에서 레이어명만 추출하여 표시
                const displayText = opt.includes('|') ? opt.split('|')[0] : opt;

                return (
                  <Button
                    key={`${toolCallId}-${index}-${opt.substring(0, 20)}`}
                    variant="outline"
                    className="justify-start gap-2 h-auto hover:bg-primary/10 break-words whitespace-break-spaces transition-transform hover:-translate-y-0.5"
                    onClick={() => {
                      onDone({toolCallId, result: opt}); // 전체 문자열 반환
                    }}
                  >
                    {displayText}
                  </Button>
                );
              })}
            </div>
          </>
        );
      }
      case "getUserInput": {
        const { message, default: defaultVal } = (invocation.args || {}) as any;
        const [value, setValue] = useState<string>(defaultVal ?? "");
        return (
          <>
            <p className="text-sm whitespace-pre-wrap break-all mb-2">{message}</p>
            <Input value={value} onChange={(e) => setValue(e.target.value)} />
            <div className="mt-2 flex gap-2">
              <Button onClick={() => onDone({toolCallId, result: value})} disabled={value.trim() === ""}>확인</Button>
            </div>
          </>
        );
      }
      case "confirmWithCheckbox": {
        const { message, label } = (invocation.args || {}) as any;
        const [checked, setChecked] = useState(false);
        return (
          <>
            <p className="text-sm whitespace-pre-wrap break-all mb-2">{message}</p>
            <label className="flex items-center gap-2 mb-2">
              <Checkbox checked={checked} onCheckedChange={setChecked as any} /> <span>{label}</span>
            </label>
            <Button onClick={() => onDone({toolCallId, result: checked})} disabled={!checked}>확인</Button>
          </>
        );
      }
      case "getLocation": {
        const { message } = (invocation.args || {}) as any;
        const [isLoading, setIsLoading] = useState(false);
        const [error, setError] = useState<string | null>(null);

        const handleGetLocation = () => {
          setIsLoading(true);
          setError(null);

          if (!navigator.geolocation) {
            setError("이 브라우저는 위치 서비스를 지원하지 않습니다.");
            setIsLoading(false);
            return;
          }

          navigator.geolocation.getCurrentPosition(
            (position) => {
              const { latitude, longitude } = position.coords;
              setIsLoading(false);
              onDone({
                toolCallId,
                result: {
                  latitude,
                  longitude,
                  accuracy: position.coords.accuracy,
                  timestamp: position.timestamp
                }
              });
            },
            (error) => {
              setIsLoading(false);
              let errorMessage = "위치 정보를 가져올 수 없습니다.";

              switch (error.code) {
                case error.PERMISSION_DENIED:
                  errorMessage = "위치 접근 권한이 거부되었습니다. 브라우저 설정에서 위치 권한을 허용해주세요.";
                  break;
                case error.POSITION_UNAVAILABLE:
                  errorMessage = "위치 정보를 사용할 수 없습니다.";
                  break;
                case error.TIMEOUT:
                  errorMessage = "위치 정보 요청 시간이 초과되었습니다.";
                  break;
              }

              setError(errorMessage);
            },
            {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 300000 // 5분
            }
          );
        };

        return (
          <>
            <p className="text-sm whitespace-pre-wrap break-all mb-2">
              {message || "현재 위치 정보를 가져오시겠습니까?"}
            </p>
            {error && (
              <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                {error}
              </div>
            )}
            <div className="flex gap-2">
              <Button
                onClick={handleGetLocation}
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    위치 확인 중...
                  </>
                ) : (
                  "현재 위치 확인"
                )}
              </Button>
              {error && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setError(null);
                    handleGetLocation();
                  }}
                >
                  다시 시도
                </Button>
              )}
            </div>
          </>
        );
      }
      default:
        return <pre className="text-sm bg-accent/20 p-2 rounded-md max-h-40 overflow-auto">{JSON.stringify(invocation.args ?? {}, null, 2)}</pre>;
    }
  };

  return (
    <Card className="w-full shadow-sm border-0">
      <CardHeader className="flex px-0 py-2 flex-row items-center gap-3">
        <Avatar className="h-6 w-6">
          <AvatarFallback>🤖</AvatarFallback>
        </Avatar>
        <span className="text-sm font-medium">{friendlyName} 제안을 준비했어요!</span>
      </CardHeader>
      <CardContent className="p-2">
        {renderInteractive()}
      </CardContent>
      {/* Footer reserved for future */}
      <CardFooter className="p-0"/>
    </Card>
  );
}
