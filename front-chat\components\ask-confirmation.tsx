"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

interface AskConfirmationProps {
  /** 사용자에게 보여줄 메시지 */
  message: string;
  /** 여러 선택지가 있을 경우 */
  options?: string[];
  /** 선택 시 호출되는 콜백 (선택된 값 전달) */
  onSelect: (value: string) => void;
}

export function AskConfirmation({ message, options, onSelect }: AskConfirmationProps) {
  // 기본 예/아니오 옵션
  const defaultOptions = ["네", "아니요"];
  const renderOptions = options && options.length > 0 ? options : defaultOptions;

  return (
    <Card className="w-full my-2 bg-primary/5 border-primary/20">
      <CardContent className="p-4">
        <div className="space-y-4">
          <div className="text-sm whitespace-pre-wrap">{message}</div>
          <div className="flex gap-2 flex-wrap">
            {renderOptions.map((opt) => (
              <Button
                key={opt}
                variant={opt === "아니요" ? "destructive" : "default"}
                onClick={() => onSelect(opt)}
              >
                {opt}
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
