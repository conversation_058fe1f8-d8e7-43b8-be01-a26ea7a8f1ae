import { AnimatePresence, motion } from 'framer-motion';
import React, { memo, useState, useEffect, useRef } from 'react';
import { usePreview } from '@/lib/hooks/use-preview';
import { CodeEditor } from './code-editor';
import { cn } from '@/lib/utils';
import {
  ChevronLeft,
  ChevronRight,
  Maximize2,
  MinusSquare,
  X,
  Minimize2
} from 'lucide-react';

interface PreviewPanelProps {
  isReadonly?: boolean;
  isVisible?: boolean;
}

interface NavigationState {
  history: string[];
  currentIndex: number;
}

type Tab = 'preview' | 'code' | 'console';

interface ConsoleLog {
  type: 'log' | 'error' | 'warn';
  timestamp: number;
  content: string;
}

const TabButton = memo(({ selected, label, onClick }: { selected: boolean; label: string; onClick: () => void }) => (
  <button
    onClick={onClick}
    className={cn(
      "px-4 py-2 text-sm font-medium transition-colors",
      selected ? "text-foreground border-b-2 border-primary" : "text-muted-foreground hover:text-foreground"
    )}
  >
    {label}
  </button>
));

TabButton.displayName = 'TabButton';

const ConsoleOutput = memo(({ logs }: { logs: ConsoleLog[] }) => (
  <div className="font-mono text-sm p-4 space-y-2 h-full overflow-auto styled-scrollbar">
    {logs.map((log, i) => (
      <div key={i} className={cn("flex items-start gap-2", {
        "text-red-500": log.type === 'error',
        "text-yellow-500": log.type === 'warn'
      })}>
        <span className="text-muted-foreground text-xs">
          {new Date(log.timestamp).toLocaleTimeString()}
        </span>
        <span>{log.content}</span>
      </div>
    ))}
  </div>
));

ConsoleOutput.displayName = 'ConsoleOutput';

// HTML 프리뷰에 console 캡처 코드를 주입하는 함수
const injectConsoleCapture = (html: string) => `
  <!DOCTYPE html>
  <html>
    <head>
      <script>
        const originalConsole = {
          log: console.log,
          error: console.error,
          warn: console.warn
        };
        
        function captureConsole(type, args) {
          window.parent.postMessage({
            type: 'console',
            logType: type,
            content: Array.from(args).map(arg => 
              typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' '),
            timestamp: Date.now()
          }, '*');
        }
        
        console.log = (...args) => {
          originalConsole.log(...args);
          captureConsole('log', args);
        };
        console.error = (...args) => {
          originalConsole.error(...args);
          captureConsole('error', args);
        };
        console.warn = (...args) => {
          originalConsole.warn(...args);
          captureConsole('warn', args);
        };
      </script>
    </head>
    <body>
      ${html}
    </body>
  </html>
`;

function PurePreviewPanel({ isReadonly = false, isVisible = false }: PreviewPanelProps) {
  const { preview, setPreview } = usePreview();
  const [activeTab, setActiveTab] = useState<Tab>('preview');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [consoleLogs, setConsoleLogs] = useState<ConsoleLog[]>([]);
  const [navigation, setNavigation] = useState<NavigationState>({
    history: [],
    currentIndex: -1
  });

  useEffect(() => {
    if (preview.content && !navigation.history.includes(preview.content)) {
      setNavigation(prev => ({
        history: [...prev.history, preview.content],
        currentIndex: prev.history.length
      }));
    }
  }, [preview.content]);

  const handleNavigate = (direction: 'back' | 'forward') => {
    const newIndex = direction === 'back'
      ? navigation.currentIndex - 1
      : navigation.currentIndex + 1;

    if (newIndex >= 0 && newIndex < navigation.history.length) {
      setNavigation(prev => ({ ...prev, currentIndex: newIndex }));
      setPreview(prev => ({
        ...prev,
        content: navigation.history[newIndex]
      }));
    }
  };

  useEffect(() => {
    const handleConsoleMessage = (event: MessageEvent) => {
      if (event.data.type === 'console') {
        setConsoleLogs(prev => [...prev, {
          type: event.data.logType,
          timestamp: event.data.timestamp,
          content: event.data.content
        }]);
      }
    };

    window.addEventListener('message', handleConsoleMessage);
    return () => window.removeEventListener('message', handleConsoleMessage);
  }, []);

  const handleClose = () => {
    setPreview(prev => ({ ...prev, isVisible: false }));
    setIsFullscreen(false);
  };

  const containerClassName = cn(
    "relative border-l border-border/30 overflow-hidden backdrop-blur-sm transition-all duration-300",
    {
      "fixed inset-0 z-50": isFullscreen,
    }
  );

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ width:'100%', x: '100%', opacity: 0 }}
          animate={{
            x: 0,
            opacity: 1
          }}
          exit={{ 
            x: '100%', 
            opacity: 0, 
            transition: { 
              duration: 0.1, 
              ease: "easeOut" 
            } 
          }}
          transition={{
            duration: 0.1,
            ease: "easeOut"
          }}
          className={containerClassName}
        >
          <div className="flex flex-col h-full bg-background/80">
            <div className="flex items-center justify-between px-4 py-2 border-b border-border/30">
              <div className="flex items-center">

                <button
                  onClick={handleClose}
                  className="p-2 hover:bg-muted rounded-md text-muted-foreground hover:text-foreground"
                >
                  <X size={16} />
                </button>

                <div className="ml-2 flex items-center gap-2">
                  <TabButton
                    selected={activeTab === 'preview'}
                    label="Preview"
                    onClick={() => setActiveTab('preview')}
                  />
                  <TabButton
                    selected={activeTab === 'code'}
                    label="Code"
                    onClick={() => setActiveTab('code')}
                  />
                  <TabButton
                    selected={activeTab === 'console'}
                    label="Console"
                    onClick={() => setActiveTab('console')}
                  />
                </div>
              </div>

              <div className="flex items-center gap-2">
                <button
                  onClick={() => handleNavigate('back')}
                  disabled={navigation.currentIndex <= 0}
                  className="p-2 hover:bg-muted rounded-md text-muted-foreground hover:text-foreground disabled:opacity-50"
                >
                  <ChevronLeft size={16} />
                </button>
                <button
                  onClick={() => handleNavigate('forward')}
                  disabled={navigation.currentIndex >= navigation.history.length - 1}
                  className="p-2 hover:bg-muted rounded-md text-muted-foreground hover:text-foreground disabled:opacity-50"
                >
                  <ChevronRight size={16} />
                </button>
                <button
                  onClick={() => setIsFullscreen(prev => !prev)}
                  className="p-2 hover:bg-muted rounded-md text-muted-foreground hover:text-foreground"
                >
                  {isFullscreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
                </button>

              </div>
            </div>

            <div className="flex-1 overflow-auto styled-scrollbar">
              <div className="h-full">
                {activeTab === 'preview' && preview.kind === 'html' && (
                  <iframe
                    srcDoc={injectConsoleCapture(preview.content)}
                    className="w-full h-full border-0 bg-white dark:bg-zinc-900"
                    sandbox="allow-scripts"
                    title="HTML Preview"
                  />
                )}
                {activeTab === 'preview' && preview.kind !== 'html' && (
                  <CodeEditor
                    content={preview.content}
                    language={preview.kind}
                    mode="edit"
                    status="idle"
                  />
                )}
                {activeTab === 'code' && (
                  <CodeEditor
                    content={preview.content}
                    language={preview.kind}
                    mode="edit"
                    status="idle"
                    lineNumbers={true}
                  />
                )}
                {activeTab === 'console' && (
                  <ConsoleOutput logs={consoleLogs} />
                )}
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

export const PreviewPanel = memo(PurePreviewPanel, (prevProps, nextProps) => {
  return prevProps.isVisible === nextProps.isVisible &&
    prevProps.isReadonly === nextProps.isReadonly;
});

PreviewPanel.displayName = 'PreviewPanel';