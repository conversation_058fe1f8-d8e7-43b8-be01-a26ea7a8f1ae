{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28auth%29/login/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\n\nexport const metadata: Metadata = {\n  title: \"로그인\",\n  description: \"말로 만드는 지도 demo 로그인\",\n  keywords: [\"로그인\", \"GeOn\", \"지온파스\", \"인증\"],\n  openGraph: {\n    title: \"로그인\",\n    description: \"말로 만드는 지도 demo 로그인.\",\n    type: \"website\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"로그인\",\n    description: \"말로 만드는 지도 demo 로그인.\",\n  },\n  robots: {\n    index: false,\n    follow: false,\n  },\n};\n\nexport default function LoginLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return children;\n}\n"], "names": [], "mappings": ";;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAO;QAAQ;QAAQ;KAAK;IACvC,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;IACV;AACF;AAEe,SAAS,YAAY,EAClC,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}