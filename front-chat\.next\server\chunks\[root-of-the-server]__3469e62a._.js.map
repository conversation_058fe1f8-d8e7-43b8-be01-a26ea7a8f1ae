{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/api-config.ts"], "sourcesContent": ["/**\n * API 설정 및 인증 정보를 관리하는 유틸리티\n */\n\nexport interface ApiConfig {\n  baseUrl: string;\n  headers: {\n    crtfckey: string;\n  };\n  auth: {\n    userId: string;\n    password: string;\n  };\n}\n\n/**\n * API 요청을 위한 설정을 반환합니다.\n * 프론트엔드 로그인과 별개로 백엔드 API 요청 시에는 항상 geonuser 계정을 사용합니다.\n */\nexport const getApiConfig = (): ApiConfig => {\n  const baseUrl =\n    process.env.GEON_API_BASE_URL || \"http://121.163.19.101:14090\";\n\n  // MCP 서버 자체 API 키 사용 (클라이언트 토큰과 별개)\n  const apiKey = process.env.GEON_API_KEY;\n  if (!apiKey) {\n    console.warn(\"GEON_API_KEY가 설정되지 않았습니다.\");\n  }\n\n  // 백엔드 API 요청용 계정 정보 (환경변수에서 가져오거나 기본값 사용)\n  const apiUserId = process.env.GEON_API_USER_ID || 'geonuser';\n  const apiUserPassword = process.env.GEON_API_USER_PASSWORD || 'wavus1234!';\n\n  return {\n    baseUrl,\n    headers: {\n      crtfckey: apiKey || \"\",\n    },\n    auth: {\n      userId: apiUserId,\n      password: apiUserPassword,\n    },\n  };\n};\n\n/**\n * API 요청 시 사용할 URLSearchParams에 인증 정보를 추가합니다.\n */\nexport const addAuthToParams = (params: URLSearchParams, config?: ApiConfig): URLSearchParams => {\n  const apiConfig = config || getApiConfig();\n  \n  // API 키 추가\n  if (apiConfig.headers.crtfckey) {\n    params.append(\"crtfckey\", apiConfig.headers.crtfckey);\n  }\n  \n  return params;\n};\n\n/**\n * API 요청 시 사용할 헤더를 반환합니다.\n */\nexport const getApiHeaders = (config?: ApiConfig): Record<string, string> => {\n  const apiConfig = config || getApiConfig();\n  \n  return {\n    \"Content-Type\": \"application/json\",\n    ...apiConfig.headers,\n  };\n};\n\n/**\n * 백엔드 API 요청에서 사용할 userId를 반환합니다.\n * 프론트엔드 로그인 계정과 관계없이 항상 geonuser를 사용합니다.\n */\nexport const getApiUserId = (config?: ApiConfig): string => {\n  const apiConfig = config || getApiConfig();\n  return apiConfig.auth.userId;\n};\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;AAiBM,MAAM,eAAe;IAC1B,MAAM,UACJ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;IAEnC,oCAAoC;IACpC,MAAM,SAAS,QAAQ,GAAG,CAAC,YAAY;IACvC,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;IACf;IAEA,0CAA0C;IAC1C,MAAM,YAAY,QAAQ,GAAG,CAAC,gBAAgB,IAAI;IAClD,MAAM,kBAAkB,QAAQ,GAAG,CAAC,sBAAsB,IAAI;IAE9D,OAAO;QACL;QACA,SAAS;YACP,UAAU,UAAU;QACtB;QACA,MAAM;YACJ,QAAQ;YACR,UAAU;QACZ;IACF;AACF;AAKO,MAAM,kBAAkB,CAAC,QAAyB;IACvD,MAAM,YAAY,UAAU;IAE5B,WAAW;IACX,IAAI,UAAU,OAAO,CAAC,QAAQ,EAAE;QAC9B,OAAO,MAAM,CAAC,YAAY,UAAU,OAAO,CAAC,QAAQ;IACtD;IAEA,OAAO;AACT;AAKO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,YAAY,UAAU;IAE5B,OAAO;QACL,gBAAgB;QAChB,GAAG,UAAU,OAAO;IACtB;AACF;AAMO,MAAM,eAAe,CAAC;IAC3B,MAAM,YAAY,UAAU;IAC5B,OAAO,UAAU,IAAI,CAAC,MAAM;AAC9B", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/api/layers/get-layer/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { getLayer } from \"@geon-ai/tools\";\nimport { getApiConfig } from \"@/lib/api-config\";\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { lyrId } = await request.json();\n\n    if (!lyrId) {\n      return NextResponse.json(\n        { error: \"레이어 ID가 필요합니다\" },\n        { status: 400 }\n      );\n    }\n\n    const config = getApiConfig();\n\n    // 디버깅을 위한 로깅\n    console.log(\"=== getLayer API 호출 시작 ===\");\n    console.log(\"lyrId:\", lyrId);\n    console.log(\"userId: admin (AI 대화와 동일)\");\n    console.log(\"insttCode: geonpaas (AI 대화와 동일)\");\n    console.log(\"userSeCode: 14\");\n    console.log(\"API Key:\", config.headers.crtfckey ? \"존재함\" : \"없음\");\n    console.log(\"Base URL:\", config.baseUrl);\n\n    // getLayer 도구 실행 (AI 대화와 동일한 파라미터 사용)\n    const result = await getLayer.execute({\n      userId: \"admin\",\n      insttCode: \"geonpaas\",\n      userSeCode: \"14\",\n      lyrId: lyrId\n    }, {\n      abortSignal: new AbortController().signal,\n      toolCallId: `api-layer-add-${lyrId}-${Date.now()}`,\n      messages: []\n    });\n\n    return NextResponse.json(result);\n  } catch (error) {\n    console.error(\"=== 레이어 조회 실패 ===\");\n    console.error(\"Error:\", error);\n    console.error(\"Stack:\", error instanceof Error ? error.stack : \"No stack\");\n\n    return NextResponse.json(\n      { error: \"레이어 조회에 실패했습니다\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEpC,IAAI,CAAC,OAAO;YACV,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgB,GACzB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;QAE1B,aAAa;QACb,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,UAAU;QACtB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,YAAY,OAAO,OAAO,CAAC,QAAQ,GAAG,QAAQ;QAC1D,QAAQ,GAAG,CAAC,aAAa,OAAO,OAAO;QAEvC,sCAAsC;QACtC,MAAM,SAAS,MAAM,iPAAA,CAAA,WAAQ,CAAC,OAAO,CAAC;YACpC,QAAQ;YACR,WAAW;YACX,YAAY;YACZ,OAAO;QACT,GAAG;YACD,aAAa,IAAI,kBAAkB,MAAM;YACzC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,IAAI;YAClD,UAAU,EAAE;QACd;QAEA,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC,UAAU;QACxB,QAAQ,KAAK,CAAC,UAAU,iBAAiB,QAAQ,MAAM,KAAK,GAAG;QAE/D,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiB,GAC1B;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}