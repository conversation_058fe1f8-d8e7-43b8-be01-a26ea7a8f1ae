# 배경지도 컨트롤 (BasemapControl)

## 배경지도를 설정하고 변경할 수 있는 컨트롤입니다. 다양한 유형의 배경지도를 지원하며, 사용자가 쉽게 전환할 수 있도록 합니다.

### [배경지도 생성, 베이스맵, 베이스맵 컨트롤, basemapControl, 배경지도 설정]

#### - setMap(map): 컨트롤을 지도에 추가 - switchBaseLayer(layerKey): 특정 배경지도로 전환 - getSetableBasemapList(): 설정 가능한 배경지도 목록 조회

#####
```javascript
//배경지도 컨트롤 생성
var basemapControl = new odf.BasemapControl();
basemapControl.setMap(map);

//특정 배경지도(색각지도)로 전환
basemapControl.switchBaseLayer('eMapColor');

//설정 가능한 배경지도 목록 조회
var basemapList = basemapControl.getSetableBasemapList();
```


# 줌 컨트롤 (ZoomControl)

## 지도의 확대 및 축소 기능을 제공하는 컨트롤입니다. 사용자가 지도의 상세 수준을 쉽게 조절할 수 있게 합니다.

### [줌 컨트롤, 지도 확대, 지도 축소, 줌 레벨 조정]

#### - setMap(map): 컨트롤을 지도에 추가 - zoomIn(): 지도 확대 - zoomOut(): 지도 축소

#####
```javascript
//줌 컨트롤 생성
var zoomControl = new odf.ZoomControl();
zoomControl.setMap(map);

//지도 확대
zoomControl.zoomIn();

//지도 축소
zoomControl.zoomOut();
```

# 오버뷰 컨트롤 (OverviewMapControl)

## 현재 보고 있는 지도 영역의 전체적인 위치를 작은 지도로 보여주는 컨트롤입니다.

### [오버뷰 컨트롤, 미니맵, 전체 지도 보기, 위치 컨텍스트]

#### - setMap(map): 컨트롤을 지도에 추가

#####
```javascript
//overviewMapControl 생성
var overviewMapControl = new odf.OverviewMapControl();
overviewMapControl.setMap(map);
```

# 축척 컨트롤 (ScaleControl)

## 지도의 현재 축척을 표시하는 컨트롤입니다. 사용자에게 지도의 거리 정보를 제공합니다.

### [축척, 지도 축척, 거리 표시, 스케일 바]

#### - setMap(map): 컨트롤을 지도에 추가 - setScaleValue(scale): 축척 설정 (1:5000 -> 5000 입력)

#####
```javascript
var scaleControl = new odf.ScaleControl();
scaleControl.setMap(map);
```

# 이전/다음 화면 이동 컨트롤 (MoveControl)

## 이전에 본 지도 위치로 이동하거나 다음 위치로 이동할 수 있는 기능을 제공하는 컨트롤입니다.

### [이전/다음 화면 이동, 지도 네비게이션, 위치 기억, 브라우징 히스토리]

#### - setMap(map): 컨트롤을 지도에 추가 - back(): 이전 위치로 이동 - forward(): 다음 위치로 이동

#####
```javascript
var moveControl = new odf.MoveControl();
moveControl.setMap(map);

//이전 위치로 이동
moveControl.back();

//다음 위치로 이동
moveControl.forward();
```

# 마우스 좌표 컨트롤 (MousePositionControl)
## 마우스 커서의 현재 위치 좌표를 실시간으로 표시해주는 컨트롤입니다.
### [마우스 좌표 표시, 위치 정보, 좌표계, 실시간 위치]
#### - setMap(map): 컨트롤을 지도에 추가
#####
```html
<div id="coordDiv"></div>
```
```javascript
var mousePositionControl = new odf.MousePositionControl({
  element: document.querySelector('#coordDiv'), // 마우스 좌표를 표시할 element 영역
  callback: function (position) {
    console.log(position);
  },
});
mousePositionControl.setMap(map);
```

# 그리기 도구 컨트롤 (DrawControl)

## 지도 위에 다양한 도형이나 마커를 그릴 수 있는 도구를 제공하는 컨트롤입니다.

### [그리기 도구, 지도 마킹, 도형 그리기, 사용자 정의 오버레이, 그리기, 그리기, 그리기, 그리기]

#### - setMap(map): 컨트롤을 지도에 추가 - setDrawingMode(mode): 그리기 모드 설정 (점, 선, 다각형 등) - getFeatures(): 그려진 객체 목록 가져오기 그리기

#####
```javascript 
//그리기 컨트롤 
var drawControl = new odf.DrawControl();
drawControl.setMap(map);

```


# 레이어 추가 (odf.LayerFactory.produce)

## 지도 위에 레이어를 추가할 수 있습니다.

### [레이어 추가, WMS, 지도 레이어, 데이터 시각화]

#### - produce(): 레이어 생성 - setMap(map): 레이어를 지도에 추가 - fit(): 레이어에 맞춰 지도 뷰 조정

#####
```javascript
// layer 정보: - Wmappickadmin:L100002910 : 지하철역 - Wh2jung:L100002896 : 버거킹
// WMS 레이어 생성
var wmsLayer = odf.LayerFactory.produce('geoserver', {
    method: 'get',
    server: 'https://geoserver.geon.kr/geoserver',
    layer: 'geonpaas:L100000254',
    service: 'wms',
    tiled: false,
});
``` 
// 레이어를 지도에 추가
wmsLayer.setMap(map);

// 레이어가 한눈에 보이는 extent로 화면 위치 이동 및 줌 레벨 변경
wmsLayer.fit();

# 스와이퍼 컨트롤 (SwiperControl)

## 기본 지도를 생성한 후 스와이퍼 컨트롤(SwiperControl)을 추가합니다. 이 컨트롤은 두 개의 지도 레이어를 나란히 비교할 수 있게 해줍니다.

### [스와이퍼, 지도 비교, 레이어 비교, swiperControl]

#### - setMap(map): 컨트롤을 지도에 추가

##### 사용 예시
```javascript
// 배경지도 컨트롤에서 바로e맵 항공지도 레이어 조회, basemapControl이 위에 선언되어 있어야 합니다.
var rightLayer = basemapControl.getBaseLayer(basemapKey);
//스와이퍼 컨트롤 생성(원본 레이어를 왼쪽 영역에 표출, 정의한 레이어는 오른쪽 영역에 표출)
var swiperControl  = new odf.SwiperControl({
    layers : [rightLayer]
});
//생성한 스와이퍼 컨트롤을 지도 객체에 추가
swiperControl.setMap(map);trol.setMap(map);
```

# 분할지도 컨트롤 (DivideMapControl)

## 기본 지도를 생성한 후 분할지도 컨트롤(DivideMapControl)을 추가합니다. 이 컨트롤은 화면을 여러 개의 지도 영역으로 나누어 다양한 배경지도를 동시에 표시할 수 있게 해줍니다.

### [분할지도, 멀티뷰, 다중 지도, divideMapControl]

#### - setMap(map): 컨트롤을 지도에 추가

##### 사용 예시
```javascript
//분할지도 컨트롤 생성
var divideMapControl = new odf.DivideMapControl({
    //2분할지도 정의
    dualMap : [
        {
            mapOption : {
                basemap : {baroEMap : ['eMapAIR']},
            },
        },
    ],
    //3분할지도 정의
    threepleMap: [
        {mapOption: {
                basemap: {baroEMap:['eMapColor']},
            },
        },
        { mapOption: {
                basemap:{baroEMap: ['eMapWhite']},
            },
        },
    ],
    //4분할지도 정의
    quadMap: [
        {
            mapOption: {
                basemap: {baroEMap : ['eMapAIR']},
            },
        },
        {
            mapOption: {
                basemap: {baroEMap : ['eMapColor']} ,
            },
        },
        {
            mapOption: {
                basemap: {baroEMap : ['eMapWhite']} ,
            },
        },
    ],
});
//생성한 분할지도 컨트롤을 지도 객체에 추가
divideMapControl.setMap(map);
```
# 측정도구 (MeasureControl)

## 측정도구 컨트롤을 생성하는 기능입니다.

### [측정도구, 면적, 거리, 반경, MeasureControl]

#### -setMap(map): 컨트롤을 지도에 추가

// 측정 도구 컨트롤 생성 start
  var measureControl = new odf.MeasureControl({
		continuity : false,
		//거리 측정 표시 옵션

		displayOption : {
			area : {//면적 측정 표시 옵션
				//면적 측정 소수점 표기 자리수 (소수몇번째자리까지 표출) default : 2
				decimalPoint : 2,
				//면적 측정 단위 변경 제곱미터기준(㎡ -> ㎢ 단위로 변경) ex) 1000000 -> 1000000㎡ 부터 1 ㎢로 표출 default : 1000000
				transformUnit: 1000000,
			},
			distance : {//거리 측정 표시 옵션
				//거리 측정 소수점 표기 자리수 (소수몇번째자리까지 표출) default : 2
				decimalPoint : 2,
				//거리 측정 단위 변경 미터기준(m -> km 단위로 변경) ex) 1000 -> 1000m 부터 1 km로 표출 default : 1000
				transformUnit: 1000,
			},
			round : {// 반경 측정 표시 옵션
				//반경 측정 소수점 표기 자리수 (소수몇번째자리까지 표출) default : 2
				decimalPoint : 2,
				//반경 측정 단위 변경 미터기준(m -> km 단위로 변경) ex) 1000 -> 1000m 부터 1 km로 표출 default : 1000
				transformUnit: 1000,
			}
		},

		// 생성할 툴 배열
		// 설정하지 않으면 모든 툴 생성
		tools : [
			'distance',// 거리 측정 툴
			'area',// 면적측정 툴
			'round',// 원의 면적측정 툴
			'spot',// 좌표 측정 툴
		],

		// 좌표 측정시 사용할 좌표계 (기본값=> 지도의 좌표계)
		// EPSG:4326 => GPS가 사용하는 좌표계
		spotProjection:'EPSG:4326',

		// 툴팁 메세지
		message : {
			// DRAWSTART: '클릭하여 측정을 시작하세요',
			DRAWEND_POLYGON : '[수정한 메세지]클릭하여 폴리곤을 그리거나, 더블클릭하여 그리기를 종료하세요',
			// DRAWEND_LINE: '클릭하여 라인을 그리거나, 더블클릭하여 그리기를 종료하세요',
		},

		// 측정 도형 스타일
		style : {
			fill : {
				color : [ 254, 243, 255, 0.2 ]
			},
			stroke : {
				color : [ 103, 87, 197, 0.7 ],
				width : 2
			},
			image : {
				circle : {
					fill : {
						color : [ 254, 243, 255, 0.2 ]
					},
					stroke : {
						color : [ 103, 87, 197, 0.7 ],
						width : 2
					},
					radius : 5,
				},
			},
			text : {
				textAlign : 'left',
				font : '30px sans-serif',
				fill : {
					color : [ 103, 87, 197, 1 ]
				},
				stroke : {
					color : [ 255, 255, 255, 1 ]
				},
			},
		}
	});
	measureControl.setMap(map);
  // 측정 도구 컨트롤 생성 end


