import { NextResponse } from "next/server";
import { auth } from "./app/(auth)/auth";

export const config = {
    matcher: [
        /*
         * Match all paths except for:
         * 1. /api routes
         * 2. /_next (Next.js internals)
         * 3. /_static (inside /public)
         * 4. all root files inside /public (e.g. /favicon.ico)
         */
        "/((?!api/|_next/|_static/|_vercel|[\\w-]+\\.\\w+).*)",
    ],
};

export default auth((req) => {
    const url = req.nextUrl;
    const pathname = url.pathname;
    const session = req.auth;

    // 테스트용 상세 로깅
    if (session) {
        console.log(`[MIDDLEWARE] Session details:`, {
            userId: session.user?.id,
            userEmail: session.user?.email,
            expires: session.expires
        });
    }

    // 테스트용: URL 파라미터로 가짜 사용자 시뮬레이션
    const testUser = url.searchParams.get('testUser');
    if (testUser && process.env.NODE_ENV === 'development') {
        console.log(`[MIDDLEWARE] TEST MODE: Simulating user ${testUser}`);
        // 가짜 세션 객체 생성
        const fakeSession = {
            user: { id: testUser },
            expires: new Date(Date.now() + 30 * 60 * 1000).toISOString()
        };
        // @ts-ignore - 테스트용
        req.auth = fakeSession;
        console.log(`[MIDDLEWARE] TEST MODE: Created fake session for ${testUser}`);
    }

    // 루트 경로 접근 시 geon-2d-map으로 리다이렉트
    if (pathname === '/') {
        console.log(`[MIDDLEWARE] Root path, redirecting to /geon-2d-map`);
        return NextResponse.redirect(new URL('/geon-2d-map', req.url));
    }

    // 로그인 페이지 처리
    if (pathname.startsWith('/login')) {
        console.log(`[MIDDLEWARE] Login page access`);
        // 유효한 세션이 있는 경우 (admin 또는 geonuser) geon-2d-map으로 리다이렉트
        if (session && (session.user?.id === 'geonuser' || session.user?.id === 'admin')) {
            console.log(`[MIDDLEWARE] Valid session for ${session.user?.id}, redirecting to geon-2d-map`);
            return NextResponse.redirect(new URL('/geon-2d-map', req.url));
        }
        // 그 외의 경우 (세션 없음, 또는 잘못된 사용자) 로그인 페이지 접근 허용
        console.log(`[MIDDLEWARE] Allowing login page access - Session: ${session ? 'exists but invalid user' : 'none'}`);
        return NextResponse.next();
    }

    // geon-2d-map 경로에 대한 특별한 처리
    if (pathname.startsWith('/geon-2d-map')) {
        console.log(`[MIDDLEWARE] geon-2d-map access attempt`);
        // 세션이 없는 경우
        if (!session) {
            console.log('[MIDDLEWARE] No session found, redirecting to login');
            return NextResponse.redirect(new URL('/login?callbackUrl=' + encodeURIComponent(req.url), req.url));
        }

        // 허용되지 않은 사용자인 경우 (admin 또는 geonuser가 아닌 경우)
        if (session.user?.id !== 'geonuser' && session.user?.id !== 'admin') {
            console.log(`[MIDDLEWARE] Invalid user: ${session.user?.id}, clearing cookies and redirecting to login`);
            // 로그아웃 처리를 위해 세션 쿠키 삭제
            const response = NextResponse.redirect(new URL('/login?callbackUrl=' + encodeURIComponent(req.url), req.url));
            // NextAuth v5에서는 authjs.session-token 사용
            response.cookies.delete('authjs.session-token');
            response.cookies.delete('__Secure-authjs.session-token');
            console.log(`[MIDDLEWARE] Cookies cleared, redirecting to login`);
            return response;
        }

        console.log(`[MIDDLEWARE] Valid session for user: ${session.user?.id}, allowing access`);
    }

    console.log(`[MIDDLEWARE] Allowing request to proceed`);
    return NextResponse.next();
});
