const CHUNK_PUBLIC_PATH = "server/app/(auth)/api/auth/[...nextauth]/route.js";
const runtime = require("../../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/1e20b_next_b1172d18._.js");
runtime.loadChunk("server/chunks/9c5b9_@auth_core_62d763ba._.js");
runtime.loadChunk("server/chunks/96a70_jose_dist_webapi_b110c212._.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_b169931f._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__ca9141c3._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/(auth)/api/auth/[...nextauth]/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/(auth)/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/(auth)/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
