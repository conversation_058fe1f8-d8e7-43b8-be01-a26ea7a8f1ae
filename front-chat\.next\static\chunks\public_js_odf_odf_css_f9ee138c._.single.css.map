{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/public/js/odf/odf.css"], "sourcesContent": ["/*!\r\n OpenLayers version: v8.2.0\r\n App version: odf_webpack 2.0.0\r\n Branch: master\r\n Commit ID: 86ec6ee5\r\n Build Time: 2024. 10. 21. 오후 2:42:48\r\n */\r\n\r\n@charset \"UTF-8\";\r\n.odf-map{\r\n  width:100%;\r\n  height : 100%;\r\n}\r\n.odf-control{\r\n  pointer-events: auto;\r\n}\r\n\r\n\r\n/*odf-control*/\r\n.odf-control.area-toolbar {\r\n  position: absolute;\r\n  z-index: 9999999;\r\n  right: 0.5em;\r\n  top: 0.5em;\r\n  width: 30px;\r\n  height: 100%;\r\n}\r\n.subGrp {\r\n  position: relative;\r\n}\r\n.subGrpArea {\r\n  position: absolute;\r\n  right: 35px;\r\n  top: 0px;\r\n  padding: 2px;\r\n  background-color: #e6e6ea;\r\n  border-radius: 5px;\r\n  width: max-content;\r\n}\r\n.subGrpArea.on {\r\n  display: block;\r\n}\r\n.subGrpArea.off {\r\n  display: none;\r\n}\r\n.subGrpArea ul {\r\n  margin: 0 0 0 0;\r\n  padding: 0;\r\n  display: inline-block;\r\n}\r\n.subGrpArea ul li {\r\n  list-style: none;\r\n  list-style-position: inside;\r\n  display: inline-block;\r\n}\r\n.subGrpArea ul li button span {\r\n  vertical-align: middle;\r\n}\r\n\r\n.odf-control-btn {\r\n  width: 25px;\r\n  height: 25px;\r\n  background-color: white;\r\n  border: #e6e6ea 1px solid;\r\n  border-radius: 5px;\r\n  font-weight: bold;\r\n  background-repeat: no-repeat;\r\n  background-position: center center;\r\n  font-size: 0;\r\n}\r\n.ol-overviewmap.right-down {\r\n  left: 40px !important;\r\n  bottom: -200px !important;\r\n}\r\n.ol-overviewmap.left-up {\r\n  left: -200px;\r\n  bottom: -30px !important;\r\n}\r\n.ol-overviewmap.left-down {\r\n  top: 0px !important;\r\n  left: -200px;\r\n  right: 35px;\r\n}\r\n.ol-overviewmap.right-up {\r\n  left: 40px !important;\r\n  bottom: -30px !important;\r\n}\r\n.odf-control.area-toolbar div.subToolbarGrp {\r\n  padding-bottom: 3.5px;\r\n  padding-top: 3.5px;\r\n  padding-left: 0px;\r\n  padding-right: 0px;\r\n}\r\n\r\n.subToolbarGrp {\r\n  /* width: 30px; */\r\n  text-align: center;\r\n  background-color: #d3d3d6b8;\r\n  border-radius: 5px;\r\n  margin-bottom: 3px;\r\n}\r\n\r\n.odf-draw-overay {\r\n  background-color: #d3d3d6b8;\r\n  border-radius: 5px;\r\n  padding: 3px;\r\n  vertical-align: middle;\r\n}\r\n\r\n.odf-draw-overay input {\r\n  width: 100px;\r\n  margin-right: 3px;\r\n}\r\n.odf-draw-overay button {\r\n  background-color: white;\r\n  border: #e6e6ea 1px solid;\r\n  border-radius: 5px;\r\n  font-weight: bold;\r\n}\r\n\r\n.ol-overviewmap:not(.odf-inner) .ol-overviewmap-map {\r\n  position: absolute;\r\n  z-index: 9999999;\r\n  right: 0.5em;\r\n  top: 0.5em;\r\n  width: 200px;\r\n}\r\n\r\n.odf-control-overviewmap {\r\n  position: relative;\r\n}\r\n.odf-control-overviewmap .ol-overviewmap.ol-control {\r\n  position: absolute;\r\n  background-color: #ffffffd2;\r\n  padding: 4px;\r\n  border-radius: 5px;\r\n  width: fit-content;\r\n}\r\n\r\n.ol-overviewmap-map {\r\n  background-color: white;\r\n}\r\n\r\n.ol-overlay-container {\r\n  will-change: left, right, top, bottom;\r\n}\r\n\r\n.ol-overviewmap .ol-overviewmap-map {\r\n  border: 1px solid #7b98bc;\r\n  height: 190px;\r\n  margin: 2px;\r\n  width: 190px;\r\n}\r\n.ol-overviewmap:not(.ol-collapsed) button {\r\n  bottom: 1px;\r\n  left: 2px;\r\n  position: absolute;\r\n}\r\n.ol-overviewmap.ol-collapsed .ol-overviewmap-map,\r\n.ol-overviewmap.ol-uncollapsible button {\r\n  display: none;\r\n}\r\n.ol-overviewmap:not(.ol-collapsed) {\r\n  background: rgba(255, 255, 255, 0.8);\r\n}\r\n.ol-overviewmap-box {\r\n  border: 2px dotted rgba(0, 60, 136, 0.7);\r\n}\r\n.ol-overviewmap .ol-overviewmap-box:hover {\r\n  cursor: move;\r\n}\r\n\r\n.ol-overviewmap-box {\r\n  border: 2px solid red;\r\n}\r\n\r\n.ol-selectable {\r\n  -webkit-touch-callout: default;\r\n  -webkit-user-select: auto;\r\n  -moz-user-select: auto;\r\n  -ms-user-select: auto;\r\n  user-select: auto;\r\n}\r\n/*zoom slider*/\r\n.slider {\r\n  height: 100px;\r\n  margin: 2px;\r\n}\r\n.slider-btn {\r\n  height: 20px;\r\n  background-color: #e6e6ea;\r\n  text-align: center;\r\n}\r\n\r\n/*배경지도 subGrp bnt*/\r\n.btn-control-basemap {\r\n  background-color: #e6e6ea;\r\n  background-image: url(images/btn-control-basemap.png);\r\n}\r\n.btn-control-basemap:hover {\r\n  background-image: url(images/btn-control-basemap-h.png);\r\n}\r\n\r\n.btn-control-bookmark {\r\n  background-image: url(images/btn-control-bookmark.png);\r\n}\r\n.btn-control-bookmark:hover {\r\n  background-image: url(images/btn-control-bookmark-h.png);\r\n}\r\n\r\n.btn-control-home {\r\n  background-image: url(images/btn-control-home.png);\r\n}\r\n\r\n.btn-control-home:hover {\r\n  background-image: url(images/btn-control-home-h.png);\r\n}\r\n\r\n#subGrp-basemap-area button.odf-control-btn {\r\n  font-size: 14px;\r\n  width: auto;\r\n}\r\n#subGrp-basemap-area button.odf-control-btn:hover {\r\n  color: #36b3e6;\r\n  width: auto;\r\n}\r\n/*move*/\r\n.btn-control-pre-move {\r\n  background-image: url(images/btn-control-pre-move.png);\r\n}\r\n.btn-control-pre-move:hover {\r\n  background-image: url(images/btn-control-pre-move-h.png);\r\n}\r\n.btn-control-after-move {\r\n  background-image: url(images/btn-control-after-move.png);\r\n}\r\n.btn-control-after-move:hover {\r\n  background-image: url(images/btn-control-after-move-h.png);\r\n}\r\n/*줌인/줌아웃*/\r\n.btn-control-zoom-in {\r\n  background-image: url(images/btn-control-zoom-in.png);\r\n}\r\n.btn-control-zoom-in:hover {\r\n  background-image: url(images/btn-control-zoom-in-h.png);\r\n}\r\n.btn-control-zoom-out {\r\n  background-image: url(images/btn-control-zoom-out.png);\r\n}\r\n.btn-control-zoom-out:hover {\r\n  background-image: url(images/btn-control-zoom-out-h.png);\r\n}\r\n/*그리기 툴 박스*/\r\n.btn-control-draw {\r\n  background-image: url(images/btn-control-draw.png);\r\n}\r\n.btn-control-draw:hover {\r\n  background-image: url(images/btn-control-draw-h.png);\r\n}\r\n/*텍스트*/\r\n.btn-control-draw-text {\r\n  background-image: url(images/btn-control-draw-text.png);\r\n}\r\n.btn-control-draw-text:hover {\r\n  background-image: url(images/btn-control-draw-text-h.png);\r\n}\r\n/*폴리곤*/\r\n.btn-control-draw-polygon {\r\n  background-image: url(images/btn-control-draw-polygon.png);\r\n  background-size: contain;\r\n}\r\n.btn-control-draw-polygon:hover {\r\n  background-image: url(images/btn-control-draw-polygon-h.png);\r\n  background-size: contain;\r\n}\r\n/*라인*/\r\n.btn-control-draw-lineString {\r\n  background-image: url(images/btn-control-draw-lineString.png);\r\n}\r\n.btn-control-draw-lineString:hover {\r\n  background-image: url(images/btn-control-draw-lineString-h.png);\r\n}\r\n/*점*/\r\n.btn-control-draw-point {\r\n  background-image: url(images/btn-control-draw-point.png);\r\n}\r\n.btn-control-draw-point:hover {\r\n  background-image: url(images/btn-control-draw-point-h.png);\r\n}\r\n/*원형*/\r\n.btn-control-draw-circle {\r\n  background-image: url(images/btn-control-draw-circle.png);\r\n  background-size: contain;\r\n}\r\n.btn-control-draw-circle:hover {\r\n  background-image: url(images/btn-control-draw-circle-h.png);\r\n  background-size: contain;\r\n}\r\n/*사각형*/\r\n.btn-control-draw-box {\r\n  background-image: url(images/btn-control-draw-box.png);\r\n}\r\n.btn-control-draw-box:hover {\r\n  background-image: url(images/btn-control-draw-box-h.png);\r\n}\r\n/*곡선*/\r\n.btn-control-draw-curve {\r\n  background-image: url(images/btn-control-draw-curve.png);\r\n}\r\n.btn-control-draw-curve:hover {\r\n  background-image: url(images/btn-control-draw-curve-h.png);\r\n}\r\n/*버퍼*/\r\n.btn-control-draw-buffer {\r\n  background-image: url(images/btn-control-draw-buffer.png);\r\n}\r\n.btn-control-draw-buffer:hover {\r\n  background-image: url(images/btn-control-draw-buffer-h.png);\r\n}\r\n/*출력*/\r\n.btn-control-print {\r\n  background-image: url(images/btn-control-print.png);\r\n}\r\n.btn-control-print:hover {\r\n  background-image: url(images/btn-control-print-h.png);\r\n}\r\n/*저장*/\r\n.btn-control-download-img {\r\n  background-image: url(images/btn-control-download.png);\r\n}\r\n.btn-control-download:hover {\r\n  background-image: url(images/btn-control-download-h.png);\r\n}\r\n/*저장 (pdf)*/\r\n.btn-control-download-pdf {\r\n  background-image: url(images/btn-control-download-pdf.png);\r\n}\r\n.btn-control-download-pdf:hover {\r\n  background-image: url(images/btn-control-download-pdf-h.png);\r\n}\r\n.btn-control-overviewmap {\r\n  background-image: url(images/btn-control-overviewmap.png);\r\n}\r\n.btn-control-overviewmap:hover {\r\n  background-image: url(images/btn-control-overviewmap-h.png);\r\n}\r\n/*측정*/\r\n.btn-control-measure {\r\n  background-image: url(images/btn-control-measure.png);\r\n}\r\n.btn-control-measure:hover {\r\n  background-image: url(images/btn-control-measure-h.png);\r\n}\r\n/*거리*/\r\n.btn-control-measure-distance {\r\n  background-image: url(images/btn-control-measure-distance.png);\r\n}\r\n.btn-control-measure-distance:hover {\r\n  background-image: url(images/btn-control-measure-distance-h.png);\r\n}\r\n/*면적*/\r\n.btn-control-measure-area {\r\n  background-image: url(images/btn-control-measure-area.png);\r\n}\r\n.btn-control-measure-area:hover {\r\n  background-image: url(images/btn-control-measure-area-h.png);\r\n}\r\n/*반경*/\r\n.btn-control-measure-round {\r\n  background-image: url(images/btn-control-measure-round.png);\r\n}\r\n.btn-control-measure-round:hover {\r\n  background-image: url(images/btn-control-measure-round-h.png);\r\n}\r\n/*좌표측정*/\r\n.btn-control-measure-spot {\r\n  background-image: url(images/btn-control-measure-spot.png);\r\n}\r\n.btn-control-measure-spot:hover {\r\n  background-image: url(images/btn-control-measure-spot-h.png);\r\n}\r\n\r\n/*초기화*/\r\n.btn-control-clear {\r\n  background-image: url(images/btn-control-clear.png);\r\n}\r\n.btn-control-clear:hover {\r\n  background-image: url(images/btn-control-clear-h.png);\r\n}\r\n/*rotate*/\r\n.btn-control-rotate{\r\n  position: relative;\r\n}\r\n.btn-control-rotate span {\r\n  background-size: contain;\r\n  position: absolute;\r\n  width: 25px;\r\n  height: 25px;\r\n  left: 0px;\r\n  top: 0px;\r\n  background-image: url(images/btn-control-rotation.png);\r\n  background-repeat: no-repeat;\r\n  background-position: center center;\r\n}\r\n.btn-control-rotate span:hover {\r\n  background-image: url(images/btn-control-rotation-h.png);\r\n}\r\n/*fullscreen*/\r\n.btn-control-fullscreen.on {\r\n  background-image: url(images/btn-control-fullscreen-on.png);\r\n}\r\n.btn-control-fullscreen.on:hover {\r\n  background-image: url(images/btn-control-fullscreen-on-h.png);\r\n}\r\n.btn-control-fullscreen.off {\r\n  background-image: url(images/btn-control-fullscreen-off.png);\r\n}\r\n.btn-control-fullscreen.off:hover {\r\n  background-image: url(images/btn-control-fullscreen-off-h.png);\r\n}\r\n/*스와이퍼*/\r\n.btn-control-swiper {\r\n  background-image: url(images/btn-control-swiper.png);\r\n  background-size: contain;\r\n}\r\n.btn-control-swiper:hover {\r\n  background-image: url(images/btn-control-swiper-h.png);\r\n}\r\n.btn-control-swipe div.subGrpArea {\r\n  background-color: white;\r\n}\r\n\r\n/*분할지도(DivideMapControl) css*/\r\n.odf-dividemap-container{\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n/*분할지도 컨트롤 아이콘*/\r\n.btn-control-dividemap {\r\n  background-image: url(images/btn-control-dividemap.png);\r\n  background-size: contain;\r\n}\r\n.btn-control-dividemap:hover {\r\n  background-image: url(images/btn-control-dividemap-h.png);\r\n  background-size: contain;\r\n}\r\n.btn-control-dividemap-dualMap {\r\n  background-image: url(images/btn-control-dividemap-dualMap.png);\r\n  background-size: contain;\r\n}\r\n.btn-control-dividemap-dualMap:hover {\r\n  background-image: url(images/btn-control-dividemap-dualMap-h.png);\r\n  background-size: contain;\r\n}\r\n.btn-control-dividemap-threepleMap {\r\n  background-image: url(images/btn-control-dividemap-threepleMap.png);\r\n  background-size: contain;\r\n}\r\n.btn-control-dividemap-threepleMap:hover {\r\n  background-image: url(images/btn-control-dividemap-threepleMap-h.png);\r\n  background-size: contain;\r\n}\r\n.btn-control-dividemap-quadMap {\r\n  background-image: url(images/btn-control-dividemap-quadMap.png);\r\n  background-size: contain;\r\n}\r\n.btn-control-dividemap-quadMap:hover {\r\n  background-image: url(images/btn-control-dividemap-quadMap-h.png);\r\n  background-size: contain;\r\n}\r\n\r\n\r\n/*분할지도 지도별 영역*/\r\n.dualMap div.odf-map,\r\n.threepleMap div.odf-map,\r\n.quadMap div.odf-map {\r\n  width: 100% !important;\r\n  height: 100% !important;\r\n}\r\n.mainMap:not(.on2):not(.on4):not(.on3) {\r\n  width: 100% !important;\r\n  height: 100% !important;\r\n}\r\n\r\n\r\n/*dualMap(2분할 css)*/\r\n.dualMap:not(.mainMap):not(.on2) {\r\n  display: none;\r\n}\r\n/*vertical*/\r\n.dualMap.on2.dualMap-vertical {\r\n  display: block;\r\n  border-collapse: collapse;\r\n  width: 50% !important;\r\n  height: 100% !important;\r\n}\r\n.dualMap.on2.dualMap-vertical.d-2-1 {\r\n  float: left;\r\n}\r\n.dualMap.on2.dualMap-vertical.d-2-2 {\r\n  float: right;\r\n}\r\n/*horizonal*/\r\n.dualMap.on2.dualMap-horizonal {\r\n  display: block;\r\n  border-collapse: collapse;\r\n  width: 100% !important;\r\n  height: 50% !important;\r\n}\r\n\r\n\r\n/*treepleMap(3분할 css)*/\r\n.threepleMap:not(.mainMap):not(.on3) {\r\n  display: none;\r\n}\r\n/*vertical*/\r\n.threepleMap.on3.threepleMap-vertical {\r\n  display: block;\r\n  border-collapse: collapse;\r\n  float: left;\r\n  width: 33.33333% !important;\r\n  height: 100% !important;\r\n}\r\n/*horizonal*/\r\n.threepleMap.on3.threepleMap-horizonal {\r\n  display: block;\r\n  border-collapse: collapse;\r\n  width: 100% !important;\r\n  height: 33.3% !important;\r\n}\r\n/*complex*/\r\n.threepleMap.on3.threepleMap-complex-01,\r\n.threepleMap.on3.threepleMap-complex-02,\r\n.threepleMap.on3.threepleMap-complex-03,\r\n.threepleMap.on3.threepleMap-complex-04  {\r\n  display: block;\r\n  border-collapse: collapse;\r\n}\r\n/*complex-01*/\r\n.threepleMap.on3.threepleMap-complex-01 {\r\n  width: 50% !important;\r\n  float : left;\r\n}\r\n.threepleMap.on3.threepleMap-complex-01.d-3-1 {\r\n  height : 100% !important;\r\n}\r\n.threepleMap.on3.threepleMap-complex-01.d-3-2 ,\r\n.threepleMap.on3.threepleMap-complex-01.d-3-3{\r\n  height : 50% !important;\r\n}\r\n/*complex-02*/\r\n.threepleMap.on3.threepleMap-complex-02 {\r\n  height: 50% !important;\r\n  float : left;\r\n}\r\n.threepleMap.on3.threepleMap-complex-02.d-3-1 {\r\n  width : 100% !important;\r\n}\r\n.threepleMap.on3.threepleMap-complex-02.d-3-2 ,\r\n.threepleMap.on3.threepleMap-complex-02.d-3-3{\r\n  width : 50% !important;\r\n}\r\n/*complex-03*/\r\n.threepleMap.on3.threepleMap-complex-03 {\r\n  width: 50% !important;\r\n  float : right;\r\n}\r\n.threepleMap.on3.threepleMap-complex-03.d-3-2 ,\r\n.threepleMap.on3.threepleMap-complex-03.d-3-3{\r\n  height : 50% !important;\r\n}\r\n.threepleMap.on3.threepleMap-complex-03.d-3-1 {\r\n  height : 100% !important;\r\n}\r\n/*complex-04*/\r\n.threepleMap.on3.threepleMap-complex-04 {\r\n  height: 50% !important;\r\n  float : left;\r\n}\r\n.threepleMap.on3.threepleMap-complex-04.d-3-1 ,\r\n.threepleMap.on3.threepleMap-complex-04.d-3-2{\r\n  width : 50% !important;\r\n}\r\n.threepleMap.on3.threepleMap-complex-04.d-3-3 {\r\n  width : 100% !important;\r\n}\r\n\r\n\r\n/*quadMap(4분할 css)*/\r\n.quadMap:not(.mainMap):not(.on4) {\r\n  display: none;\r\n}\r\n/*horizonal*/\r\n.quadMap.on4.quadMap-horizonal {\r\n  display: block;\r\n  border-collapse: collapse;\r\n  width: 100% !important;\r\n  height: 25% !important;\r\n}\r\n/*vertical*/\r\n.quadMap.on4.quadMap-vertical {\r\n  display: block;\r\n  border-collapse: collapse;\r\n  float: left;\r\n  width: 25% !important;\r\n  height: 100% !important;\r\n}\r\n/*complex*/\r\n.quadMap.on4.quadMap-complex {\r\n  display: block;\r\n  border-collapse: collapse;\r\n  float: left;\r\n  width: 50% !important;\r\n  height: 50% !important;\r\n}\r\n\r\n\r\n\r\n\r\n/*zoom slider*/\r\n.h-slider {\r\n  width: 100px;\r\n  margin: 2px;\r\n  text-align: justify !important;\r\n  padding-right: 20px;\r\n}\r\n.h-slider-btn {\r\n  width: 20px;\r\n  background-color: #e6e6ea;\r\n}\r\n\r\n/* #layerTOC {\r\n  background-color: #808080;\r\n  width: 100px;\r\n  height: 100px;\r\n  position: absolute;\r\n  z-index: 99999;\r\n} */\r\n\r\n#positionStr {\r\n  float: right;\r\n  font-size: 15px;\r\n  font-weight: bold;\r\n}\r\n/* TOC(레이어) 파트 추가 */\r\n.layer-Aside {\r\n  position: absolute;\r\n  left: -220px;\r\n  top: 140px;\r\n  width: 300px;\r\n  height: 100%;\r\n  z-index: 99999;\r\n  -webkit-box-sizing: border-box;\r\n  -moz-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  -webkit-transition: all 0.3s;\r\n  -moz-transition: all 0.3s;\r\n  -ms-transition: all 0.3s;\r\n  -o-transition: all 0.3s;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.layer-Aside.openAside {\r\n  left: 80px;\r\n}\r\n\r\n.layer-Aside {\r\n  z-index: 1000;\r\n  border-right: 1px solid #c8c8c8;\r\n  background-color: #fff;\r\n}\r\n\r\n.area-layerTitle {\r\n  position: relative;\r\n  padding: 0 15px;\r\n  background-color: #36b3e6;\r\n  border-bottom: 1px solid #c8c8c8;\r\n}\r\n\r\n.area-layerTitle p {\r\n  display: inline-block;\r\n  line-height: 42px;\r\n  color: #fff;\r\n  font-size: 15px;\r\n  font-weight: bold;\r\n  letter-spacing: -0.5px;\r\n}\r\n\r\n.asideBtnGrp {\r\n  position: absolute;\r\n  right: 15px;\r\n  top: 50%;\r\n  margin-top: -13px;\r\n}\r\n\r\n.asideBtnGrp button {\r\n  vertical-align: top; /* margin-left: 2px; */\r\n  width: 25px;\r\n  height: 26px;\r\n  background-repeat: no-repeat;\r\n  background-position: center center;\r\n  text-indent: -9999px;\r\n}\r\n\r\n.area-asideScroll {\r\n  overflow: auto;\r\n  height: calc(100% - 43px);\r\n}\r\n\r\n.asideList {\r\n  padding: 5px 8px;\r\n}\r\n\r\n.asideList li {\r\n  position: relative;\r\n  margin-bottom: 5px;\r\n  padding: 15px 5px 0;\r\n  height: 60px;\r\n  -webkit-box-sizing: border-box;\r\n  -moz-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  background-color: #fff;\r\n  border: 1px solid #e7e7e7;\r\n}\r\n\r\n.asideList li.selected {\r\n  background-color: #f5f8fc;\r\n}\r\n\r\n.sortable li {\r\n  cursor: move;\r\n}\r\n\r\n.btn-typeGrp {\r\n  width: calc(100% - 95px);\r\n  height: 28px;\r\n  text-align: left;\r\n}\r\n\r\n.btn-typeGrp .textArea {\r\n  display: inline-block;\r\n  width: calc(100% - 40px);\r\n  line-height: 28px;\r\n  vertical-align: middle;\r\n  color: #555;\r\n  font-size: 13px;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n  -ms-text-overflow: ellipsis;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.asideListBtnGrp {\r\n  display: inline-block;\r\n  position: absolute;\r\n  right: 20px;\r\n  top: 50%;\r\n  margin-top: -10px;\r\n  height: 20px;\r\n}\r\n\r\n.asideListBtnGrp button {\r\n  float: left;\r\n  width: 25px;\r\n  height: 100%;\r\n  background-repeat: no-repeat;\r\n  background-position: center center;\r\n  text-indent: -9999px;\r\n}\r\n\r\n.menu-select {\r\n  position: absolute;\r\n  z-index: 1010;\r\n  top: 140px;\r\n  left: 0;\r\n  width: 80px;\r\n  height: 100%;\r\n  background: #123874;\r\n}\r\n\r\n.menu-select li {\r\n  height: 27px;\r\n  /* padding-top: 53px; */\r\n  border-bottom: 1px solid #1a58a0;\r\n  text-align: center;\r\n  color: #7aa1df;\r\n  cursor: pointer;\r\n  font-size: 10px;\r\n}\r\n\r\n.menu-select li:hover,\r\n.menu-select li.selected {\r\n  color: #fff;\r\n}\r\n.draggableMarker:hover {\r\n  cursor: move;\r\n}\r\n\r\n.areaServerDiv {\r\n  position: absolute;\r\n  z-index: 99999;\r\n  top: 10px;\r\n  left: 10px;\r\n}\r\n\r\n.areaJsonDiv {\r\n  position: absolute;\r\n  z-index: 99999;\r\n  top: 10px;\r\n  left: 10px;\r\n  /* display: none; */\r\n}\r\n.areaControl {\r\n  width: 150px;\r\n  float: left;\r\n}\r\n.adrressDiv {\r\n  position: absolute;\r\n  z-index: 99999;\r\n  top: 145px;\r\n}\r\n\r\n.adrressDiv span {\r\n  float: left;\r\n  background: white;\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.adrressDiv div {\r\n  width: 35px;\r\n  float: left;\r\n  text-align: center;\r\n  border: 3px solid rgba(255, 0, 0, 0.7);\r\n  background: gray;\r\n}\r\n\r\n.subToolbarGrp.subToolbarGrp_scale {\r\n  position: absolute;\r\n  right: 10px;\r\n  bottom: 15px;\r\n}\r\n.btn-control-scale {\r\n  width: 100%;\r\n  height: 17px;\r\n}\r\n.btn-control-scale-value {\r\n  font-size: 12px !important;\r\n  border: 0px;\r\n}\r\n.btn-control-scale-input{\r\n  width:100%;\r\n}\r\n#odf-control-scale-input{\r\n  width : 48px;\r\n}\r\n\r\n.img-test{\r\n  background-image: url(images/test.png);\r\n  width: 100px;\r\n  height: 50px;\r\n  border : solid 2px;\r\n\r\n}\r\n\r\n.ol-attribution.ol-logo-only,\r\n.ol-attribution.ol-uncollapsible {\r\n  max-width: calc(100% - 3em);\r\n}\r\n\r\n.ol-attribution.ol-uncollapsible {\r\n  bottom: 0;\r\n  right: 0;\r\n  border-radius: 4px 0 0;\r\n}\r\n\r\n.ol-attribution.ol-control {\r\n  text-align: right;\r\n  bottom: .5em;\r\n  right: .5em;\r\n  max-width: calc(100% - 1.3em);\r\n  display: flex;\r\n  flex-flow: row-reverse;\r\n  align-items: center;\r\n  position: absolute;\r\n  background-color: rgba(128, 128, 128, 0.25);\r\n  border-radius: 4px;\r\n  line-height: normal;\r\n}\r\n\r\n.ol-attribution:not(.ol-collapsed) {\r\n  background: rgba(255, 255, 255, 0.75);\r\n  ;\r\n}\r\n\r\n.ol-attribution button:not(:disabled) {\r\n  flex-shrink: 0;\r\n  cursor: pointer;\r\n  display: block;\r\n  margin: 1px;\r\n  padding: 0;\r\n  color: #666666;\r\n  font-weight: bold;\r\n  text-decoration: none;\r\n  font-size: inherit;\r\n  text-align: center;\r\n  height: 1.375em;\r\n  width: 1.375em;\r\n  line-height: .4em;\r\n  background-color: #ffffff;\r\n  border: none;\r\n  border-radius: 2px;\r\n  text-transform: none;\r\n}\r\n\r\n.ol-attribution.ol-uncollapsible button {\r\n  display: none;\r\n}\r\n\r\n\r\n.ol-attribution ul {\r\n  margin: 0;\r\n  padding: 1px .5em;\r\n  color: #333333;\r\n  text-shadow: 0 0 2px #ffffff;\r\n  font-size: 12px;\r\n}\r\n\r\n.ol-attribution li {\r\n  display: inline;\r\n  list-style: none;\r\n}\r\n"], "names": [], "mappings": "AASA;;;;;AAIA;;;;AAMA;;;;;;;;;AAQA;;;;AAGA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;AAWA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;AAOA;;;;;;;AAQA;;;;;;;AAOA;;;;;AAIA;;;;;;;AAOA;;;;;;;;AAQA;;;;AAGA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;;;;AAMA;;;;;;AAKA;;;;AAIA;;;;AAMA;;;;AAIA;;;;AAIA;;;;;;;;AAQA;;;;;AAIA;;;;;;AAOA;;;;;AAIA;;;;AAIA;;;;AAGA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAIA;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAIA;;;;;AAIA;;;;;AAKA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAIA;;;;;AAIA;;;;;AAKA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAKA;;;;AAGA;;;;AAIA;;;;AAGA;;;;;;;;;;;;AAWA;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAKA;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAOA;;;;;AAaA;;;;AAIA;;;;;;;AAMA;;;;AAGA;;;;AAIA;;;;;;;AASA;;;;AAIA;;;;;;;;AAQA;;;;;;;AAOA;;;;;AAQA;;;;;AAIA;;;;AAGA;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAKA;;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAIA;;;;AAIA;;;;AAMA;;;;AAIA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;AAYA;;;;;;;AAMA;;;;;AAaA;;;;;;AAMA;;;;;;;;;;;;;;;;;AAiBA;;;;AAIA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;AASA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;AAYA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;;;;AAcA;;;;;AAIA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAIA;;;;;;;AAQA;;;;AAKA;;;;;;AAMA;;;;;;;;;;;;;;AAcA;;;;AAKA;;;;;;;;;;;;;;;;;;;;AAoBA;;;;AAKA;;;;;;;;AAQA"}}]}