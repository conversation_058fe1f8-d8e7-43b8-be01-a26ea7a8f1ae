import { auth } from "@/app/(auth)/auth";
import { getMapById, updateMapView, deleteMapById, saveMap } from "@/lib/db/queries";

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const session = await auth();
  const id = (await params).id

  if (!session?.user) {
    return Response.json("Unauthorized!", { status: 401 });
  }

  if (!session.user.id) {
    return Response.json("User ID is not defined", { status: 401 });
  }

  try {
    const map = await getMapById({ 
      id: id, 
      userId: session.user.id
    });
    return Response.json(map);
  } catch (error: any) {
    if (error.message === "Forbidden") {
      return Response.json("Forbidden", { status: 403 });
    }
    return Response.json("Failed to fetch map", { status: 500 });
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const session = await auth();
  const id = (await params).id
  
  if (!session?.user) {
    return Response.json("Unauthorized!", { status: 401 });
  }
  if (!session.user.id) {
    return Response.json("User ID is not defined", { status: 401 });
  }

  try {
    const { mapInfo, view } = await request.json();

    // MapInfo 업데이트
    if (mapInfo) {
      await saveMap({
        id: mapInfo.id || id,  // id fallback 추가
        name: mapInfo.name,
        layers: mapInfo.layers || [],
        userId: session.user.id
      });
    }

    // MapView 업데이트
    if (view) {
      await updateMapView({
        mapId: id,
        userId: session.user.id,
        view: view
      });
    }

    // 업데이트된 맵 정보 조회하여 반환
    const updatedMap = await getMapById({ 
      id: id, 
      userId: session.user.id
    });

    return Response.json(updatedMap);
  } catch (error) {
    console.error('Failed to update map:', error);
    return Response.json({
      success: false,
      error: "Failed to update map"
    }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const session = await auth();
  const id = (await params).id

  if (!session?.user) {
    return Response.json("Unauthorized!", { status: 401 });
  }

  if (!session.user.id) {
    return Response.json("User ID is not defined", { status: 401 });
  }

  try {
    await deleteMapById({
      id: id,
      userId: session.user.id
    });
    return Response.json({ success: true });
  } catch (error: any) {
    if (error.message === "Forbidden") {
      return Response.json("Forbidden", { status: 403 });
    }
    return Response.json("Failed to delete map", { status: 500 });
  }
}