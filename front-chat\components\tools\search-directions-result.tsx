"use client";

import React from "react";
import { Navigation, MapPin, Clock, Route, Car, AlertTriangle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { componentStyles } from "@/lib/design-tokens";
import { CompactResultTrigger } from "./common/compact-result-trigger";
import { getToolDisplayInfo } from "../annotations";
import { useLayerManager } from "@/providers/tool-invocation-provider";
import { UseMapReturn } from "@geon-map/odf";

// 제공된 타입 정의 사용
export interface DirectionsResponse {
  trans_id: string;
  routes: Array<{
    result_code: number;
    result_msg: string;
    summary: {
      distance: number;
      duration: number;
      fare: {
        taxi: number;
        toll: number;
      };
    };
    sections?: Array<{
      distance: number;
      duration: number;
      roads: Array<{
        name: string;
        distance: number;
        duration: number;
        traffic_speed: number;
        traffic_state: number;
        vertexes: number[];
      }>;
    }>;
  }>;
}

interface SearchDirectionsResultProps {
  content: DirectionsResponse | string;
  className?: string;
  mapState?: UseMapReturn;
  toolCallId?: string;
}

// 애니메이션 제거 - 정적 렌더링만 사용

const formatDistance = (meters: number): string => {
  if (meters < 1000) {
    return `${Math.round(meters)}m`;
  }
  return `${(meters / 1000).toFixed(1)}km`;
};

const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (hours > 0) {
    return `${hours}시간 ${minutes}분`;
  }
  return `${minutes}분`;
};

export function SearchDirectionsResult({ content, className, mapState, toolCallId }: SearchDirectionsResultProps) {
  let result: DirectionsResponse;
  const layerManager = useLayerManager();

  try {
    result = typeof content === 'string' ? JSON.parse(content) : content;
  } catch (e) {
    return null;
  }

  const handleToggleRouteLayer = () => {
    if (!layerManager || !toolCallId) {
      toast.error("레이어 관리자를 사용할 수 없습니다");
      return;
    }

    const routeLayerId = `route-${toolCallId}`;
    const routeLayer = layerManager.getLayerById(routeLayerId);

    if (routeLayer) {
      layerManager.toggleVisibility(routeLayerId);
      toast.success(routeLayer.visible ? "경로를 지도에서 숨겼습니다" : "경로를 지도에 표시했습니다");
    } else {
      toast.error("경로 레이어를 찾을 수 없습니다");
    }
  };

  // routes[0]에서 result_code와 result_msg 확인
  if (!result.routes?.length || result.routes[0].result_code !== 0) {
    const errorMsg = result.routes?.[0]?.result_msg || "다른 출발지나 목적지를 시도해보세요";
    return (
      <div className={cn(componentStyles.card.error, "p-3", className)}>
        <div className="flex items-center gap-3">
          <div className={cn(componentStyles.iconContainer.sm, "bg-red-100/80 text-red-600 border border-red-200/60")}>
            <AlertTriangle className="h-3.5 w-3.5" />
          </div>
          <div>
            <p className="font-medium text-red-900 text-sm">경로를 찾을 수 없습니다</p>
            <p className="text-xs text-red-700">
              {errorMsg}
            </p>
          </div>
        </div>
      </div>
    );
  }

  const route = result.routes[0];
  const routeSummary = route.summary;

  const toolInfo = getToolDisplayInfo("searchDirections");

  return (
    <CompactResultTrigger
      icon={toolInfo.icon}
      title={toolInfo.label}
      state="result"
      className={className}
      titleExtra={
        <div className="flex items-center gap-1">
          <Badge variant="outline" className="text-xs py-0 border bg-emerald-100/80 text-emerald-700 border-emerald-200/60">
            {formatDistance(routeSummary.distance)}
          </Badge>
          <Badge variant="outline" className="text-xs py-0 border bg-emerald-100/80 text-emerald-700 border-emerald-200/60">
            {formatDuration(routeSummary.duration)}
          </Badge>
        </div>
      }
    >
      <div className="space-y-2">

        {/* 요금 정보 */}
        {routeSummary.fare && (
          <div>
            <Card className={cn(componentStyles.card.base, "bg-purple-50/60 border-purple-200/50")}>
              <CardContent className="p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Car className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium text-purple-900">예상 요금</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm">
                    <div className="text-neutral-700">
                      택시: <span className="font-medium">{routeSummary.fare.taxi.toLocaleString()}원</span>
                    </div>
                    <div className="text-neutral-700">
                      통행료: <span className="font-medium">{routeSummary.fare.toll.toLocaleString()}원</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 구간별 상세 정보 */}
        {route.sections && route.sections.length > 0 && (
          <div>
            <Card className={cn(componentStyles.card.base, "bg-neutral-50/60 border-neutral-200/50")}>
              <CardContent className="p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Route className="h-4 w-4 text-neutral-600" />
                    <span className="text-sm font-medium text-neutral-900">구간 정보</span>
                  </div>
                  <Badge variant="outline" className="text-xs bg-neutral-100/80 text-neutral-700 border-neutral-200/60">
                    {route.sections.length}개 구간
                  </Badge>
                </div>
                <div className="space-y-1">
                  {route.sections.slice(0, 3).map((section: any, index: number) => (
                    <div key={index} className="flex items-center justify-between text-xs p-2 bg-white/80 rounded border border-neutral-200/50">
                      <span className="text-neutral-700">구간 {index + 1}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-neutral-600">{formatDistance(section.distance)}</span>
                        <span className="text-neutral-600">{formatDuration(section.duration)}</span>
                      </div>
                    </div>
                  ))}
                  {route.sections.length > 3 && (
                    <div className="text-center text-xs text-neutral-500 py-1">
                      +{route.sections.length - 3}개 구간 더
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}


      </div>
    </CompactResultTrigger>
  );
}
