{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/api/health/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nconst HEALTH_CHECK_URL = 'http://121.163.19.104:8005/health';\nconst TIMEOUT_DURATION = 5000; // 5초 타임아웃\n\nexport async function GET(request: NextRequest) {\n  const startTime = Date.now();\n  try {\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_DURATION);\n\n    \n    const response = await fetch(HEALTH_CHECK_URL, {\n      method: 'GET',\n      signal: controller.signal,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    clearTimeout(timeoutId);\n    const responseTime = Date.now() - startTime;\n\n    if (response.ok) {\n      // 서버에서 받은 응답을 그대로 전달하거나, 간단한 상태 정보만 전달\n      const data = await response.text();\n      \n      return NextResponse.json({\n        status: 'healthy',\n        responseTime,\n        timestamp: new Date().toISOString(),\n        data: data || 'OK'\n      });\n    } else {\n      return NextResponse.json({\n        status: 'unhealthy',\n        responseTime,\n        timestamp: new Date().toISOString(),\n        error: `HTTP ${response.status}: ${response.statusText}`\n      }, { status: response.status });\n    }\n  } catch (error) {\n    const responseTime = Date.now() - startTime;\n    let errorMessage = 'Unknown error';\n    \n    if (error instanceof Error) {\n      if (error.name === 'AbortError') {\n        errorMessage = 'Request timeout';\n      } else if (error.message.includes('fetch')) {\n        errorMessage = 'Network error - Server may be offline';\n      } else {\n        errorMessage = error.message;\n      }\n    }\n\n    return NextResponse.json({\n      status: 'unhealthy',\n      responseTime,\n      timestamp: new Date().toISOString(),\n      error: errorMessage\n    }, { status: 503 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,mBAAmB;AACzB,MAAM,mBAAmB,MAAM,UAAU;AAElC,eAAe,IAAI,OAAoB;IAC5C,MAAM,YAAY,KAAK,GAAG;IAC1B,IAAI;QACF,MAAM,aAAa,IAAI;QACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI;QAGvD,MAAM,WAAW,MAAM,MAAM,kBAAkB;YAC7C,QAAQ;YACR,QAAQ,WAAW,MAAM;YACzB,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,aAAa;QACb,MAAM,eAAe,KAAK,GAAG,KAAK;QAElC,IAAI,SAAS,EAAE,EAAE;YACf,uCAAuC;YACvC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,QAAQ;gBACR;gBACA,WAAW,IAAI,OAAO,WAAW;gBACjC,MAAM,QAAQ;YAChB;QACF,OAAO;YACL,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,QAAQ;gBACR;gBACA,WAAW,IAAI,OAAO,WAAW;gBACjC,OAAO,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YAC1D,GAAG;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,MAAM,eAAe,KAAK,GAAG,KAAK;QAClC,IAAI,eAAe;QAEnB,IAAI,iBAAiB,OAAO;YAC1B,IAAI,MAAM,IAAI,KAAK,cAAc;gBAC/B,eAAe;YACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;gBAC1C,eAAe;YACjB,OAAO;gBACL,eAAe,MAAM,OAAO;YAC9B;QACF;QAEA,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR;YACA,WAAW,IAAI,OAAO,WAAW;YACjC,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}