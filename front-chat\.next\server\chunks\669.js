"use strict";exports.id=669,exports.ids=[669],exports.modules={70669:(u,e,C)=>{C.d(e,{Jl:()=>_,OV:()=>g,OW:()=>b,_w:()=>$,g$:()=>y,pr:()=>m,qc:()=>x,x6:()=>w});var r=C(88668),t=C(25613),s=C(61478),o=Object.defineProperty,n=Object.defineProperties,i=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,B=(u,e,C)=>e in u?o(u,e,{enumerable:!0,configurable:!0,writable:!0,value:C}):u[e]=C,d=(u,e)=>{for(var C in e||(e={}))l.call(e,C)&&B(u,C,e[C]);if(a)for(var C of a(e))c.call(e,C)&&B(u,C,e[C]);return u},D=(u,e)=>n(u,i(e)),p=(u,e,C)=>new Promise((r,t)=>{var s=u=>{try{n(C.next(u))}catch(u){t(u)}},o=u=>{try{n(C.throw(u))}catch(u){t(u)}},n=u=>u.done?r(u.value):Promise.resolve(u.value).then(s,o);n((C=C.apply(u,e)).next())}),m=(0,r.z6)({description:"주소나 건물명으로 위치를 검색합니다. keyword 를 통해 x,y 경위도 좌표, 지번주소, 도로명주소와 같은 위치정보를 얻을 수 있습니다.",parameters:t.z.object({keyword:t.z.string(),showMultipleResults:t.z.boolean().optional().default(!1),targetSrid:t.z.number().optional().default(4326),countPerPage:t.z.number().min(1).max(20).optional().default(10),currentPage:t.z.number().min(1).optional().default(1)}),execute:u=>p(null,[u],function*({keyword:u,showMultipleResults:e,targetSrid:C,countPerPage:r,currentPage:t}){var s,o;try{let s=process.env.GEON_API_KEY,o=process.env.GEON_API_BASE_URL;if(!s||!o)throw Error("GEON environment variable is not set");let n=new URLSearchParams({crtfckey:s,keyword:u,showMultipleResults:String(e),targetSrid:String(C),countPerPage:String(r),currentPage:String(t)}),i=yield fetch(`${o}/addrgeo/address/int?${n}`,{headers:{Accept:"application/json",crtfckey:s}});if(!i.ok)throw Error(`API \uC694\uCCAD \uC2E4\uD328: ${i.status} ${i.statusText}`);return yield i.json()}catch(e){console.error(e);let u="주소 검색 실패";return(null==(s=e.response)?void 0:s.status)===401?u="유효하지 않은 API 키":(null==(o=e.response)?void 0:o.status)===429&&(u="사용량 초과"),u}})}),A={eMapBasic:"바로e맵 일반 지도",eMapAIR:"바로e맵 항공지도",eMapColor:"바로e맵 색각 지도",eMapWhite:"바로e맵 백지도"};(0,r.z6)({description:`\uC0AC\uC6A9 \uAC00\uB2A5\uD55C \uBC30\uACBD\uC9C0\uB3C4 \uBAA9\uB85D\uC744 \uC870\uD68C\uD569\uB2C8\uB2E4.
  - \uBC18\uD658\uAC12\uC740 {\uC9C0\uB3C4\uCF54\uB4DC: \uC9C0\uB3C4\uC774\uB984} \uD615\uC2DD\uC758 \uAC1D\uCCB4\uC785\uB2C8\uB2E4.
  - \uC9C0\uB3C4\uCF54\uB4DC\uB294 changeBasemap \uB3C4\uAD6C\uC5D0\uC11C \uC0AC\uC6A9\uB429\uB2C8\uB2E4.
  \uC608\uC2DC \uBC18\uD658\uAC12:
  {
    "eMapBasic": "\uBC14\uB85Ce\uB9F5 \uC77C\uBC18 \uC9C0\uB3C4",
    "eMapAIR": "\uBC14\uB85Ce\uB9F5 \uD56D\uACF5\uC9C0\uB3C4"
  }`,parameters:t.z.object({}),execute:()=>p(null,null,function*(){return A})}),(0,r.z6)({description:`\uD604\uC7AC \uC9C0\uB3C4\uC758 \uBC30\uACBD\uC9C0\uB3C4\uB97C \uBCC0\uACBD\uD569\uB2C8\uB2E4.
  - \uBC30\uACBD\uC9C0\uB3C4 \uC774\uB984\uC740 searchBasemapList \uB3C4\uAD6C\uB85C \uC870\uD68C\uD55C \uBAA9\uB85D\uC758 \uAC12\uACFC \uC77C\uCE58\uD574\uC57C \uD569\uB2C8\uB2E4.
  - \uC77C\uCE58\uD558\uB294 \uBC30\uACBD\uC9C0\uB3C4\uAC00 \uC5C6\uC73C\uBA74 \uC624\uB958\uB97C \uBC18\uD658\uD569\uB2C8\uB2E4.
  \uC608\uC2DC:
  - \uC785\uB825: "\uBC14\uB85Ce\uB9F5 \uC77C\uBC18 \uC9C0\uB3C4"
  - \uBC18\uD658: { basemap: "eMapBasic" }`,parameters:t.z.object({baseMapName:t.z.string().min(1).describe("변경할 배경지도의 이름 (searchBasemapList의 반환값 중 하나)")}),execute:u=>p(null,[u],function*({baseMapName:u}){let e=(u=>{for(let[e,C]of Object.entries(A))if(C.includes(u))return e;return null})(u);return e?{basemap:e}:{error:"요청하신 배경지도를 찾을 수 없습니다"}})}),(0,r.z6)({description:`\uC9C0\uB3C4\uC758 \uC911\uC2EC\uC744 \uC8FC\uC5B4\uC9C4 \uACBD\uC704\uB3C4 \uC88C\uD45C\uB85C \uC774\uB3D9\uC2DC\uD0B5\uB2C8\uB2E4.
  - \uC88C\uD45C\uACC4\uB294 WGS84(EPSG:4326)\uB97C \uC0AC\uC6A9\uD569\uB2C8\uB2E4.
  \uC608\uC2DC:
  - \uC785\uB825: x=127.0, y=37.5
  - \uBC18\uD658: { x: 127.0, y: 37.5 }`,parameters:t.z.object({x:t.z.number().min(-180).max(180).describe("경도 좌표 (-180 ~ 180)"),y:t.z.number().min(-90).max(90).describe("위도 좌표 (-90 ~ 90)")}),execute:u=>p(null,[u],function*({x:u,y:e}){return{x:u,y:e}})}),(0,r.z6)({description:`\uC9C0\uB3C4\uC758 \uC911\uC2EC\uC744 \uC8FC\uC5B4\uC9C4 geometry\uB85C \uC774\uB3D9\uC2DC\uD0A4\uACE0 \uD574\uB2F9 \uC601\uC5ED\uC744 \uAC15\uC870 \uD45C\uC2DC\uD569\uB2C8\uB2E4.
  - geometry\uB294 Well Known Text(WKT) \uD615\uC2DD\uC774\uC5B4\uC57C \uD569\uB2C8\uB2E4.
  - \uC88C\uD45C\uACC4\uB294 WGS84(EPSG:4326)\uB97C \uC0AC\uC6A9\uD569\uB2C8\uB2E4.
  \uC608\uC2DC:
  - \uC785\uB825: 
    roadAddr: "\uC11C\uC6B8\uD2B9\uBCC4\uC2DC \uAC15\uB0A8\uAD6C \uD14C\uD5E4\uB780\uB85C 231"
    jibunAddr: "\uC11C\uC6B8\uD2B9\uBCC4\uC2DC \uAC15\uB0A8\uAD6C \uC5ED\uC0BC\uB3D9 702-28"
    geom: "POINT(127.0368 37.5018)"`,parameters:t.z.object({roadAddr:t.z.string().min(1).describe("도로명주소"),jibunAddr:t.z.string().min(1).describe("지번주소"),geom:t.z.string().min(1).describe("WKT(Well Known Text) 형식의 geometry")}),execute:u=>p(null,[u],function*({roadAddr:u,jibunAddr:e,geom:C}){return{roadAddr:u,jibunAddr:e,geom:C}})}),(0,r.z6)({description:"하나의 출발지에서 하나의 목적지까지로의 경로에 대한 상세 정보를 제공합니다.",parameters:t.z.object({origin:t.z.string().describe('출발지 좌표 (예: "127.111202,37.394912" 또는 "127.111202,37.394912,name=판교역")'),destination:t.z.string().describe('목적지 좌표 (예: "127.111202,37.394912" 또는 "127.111202,37.394912,name=판교역")'),waypoints:t.z.string().optional().describe("경유지 좌표 (최대 5개, | 로 구분)"),priority:t.z.enum(["RECOMMEND","TIME","DISTANCE"]).optional().default("RECOMMEND").describe("경로 탐색 우선순위"),avoid:t.z.string().optional().describe("회피 옵션 (ferries, toll, motorway, schoolzone, uturn)"),roadevent:t.z.number().optional().default(0).describe("도로 통제 정보 반영 (0: 전체, 1: 출발/도착지 제외, 2: 미반영)"),alternatives:t.z.boolean().optional().default(!1).describe("대안 경로 제공 여부"),road_details:t.z.boolean().optional().default(!1).describe("상세 도로 정보 제공 여부"),car_type:t.z.number().optional().default(1).describe("차종"),car_fuel:t.z.enum(["GASOLINE","DIESEL","LPG"]).optional().default("GASOLINE").describe("유종"),car_hipass:t.z.boolean().optional().default(!1).describe("하이패스 장착 여부"),summary:t.z.boolean().optional().default(!1).describe("요약 정보 제공 여부")}),execute:u=>p(null,[u],function*({origin:u,destination:e,waypoints:C,priority:r,avoid:t,roadevent:s,alternatives:o,road_details:n,car_type:i,car_fuel:a,car_hipass:l,summary:c}){try{let B=process.env.KAKAO_REST_API_KEY;if(!B)throw Error("KAKAO_REST_API_KEY is not set");let d=new URLSearchParams({origin:u,destination:e,priority:r,car_fuel:a,car_hipass:String(l),alternatives:String(o),road_details:String(n)});C&&d.append("waypoints",C),t&&d.append("avoid",t),s&&d.append("roadevent",String(s)),i&&d.append("car_type",String(i)),c&&d.append("summary",String(c));let D=yield fetch(`https://apis-navi.kakaomobility.com/v1/directions?${d}`,{headers:{Authorization:`KakaoAK ${B}`}});if(!D.ok)throw Error(`API \uC694\uCCAD \uC2E4\uD328: ${D.status} ${D.statusText}`);let p=yield D.json();return{trans_id:p.trans_id,routes:p.routes,result_code:p.result_code,result_msg:p.result_msg,summary:p.summary,origin:p.origin,destination:p.destination,waypoints:p.waypoints,priority:p.priority,bound:p.bound,fare:p.fare,distance:p.summary.distance,duration:p.summary.duration,sections:p.sections}}catch(u){return`\uACBD\uB85C \uAC80\uC0C9 \uC2E4\uD328: ${u}`}})}),(0,r.z6)({description:"사용자에게 확인을 요청합니다.",parameters:t.z.object({message:t.z.string().describe("The message to ask for confirmation.")})});var y=(0,r.z6)({description:"사용자의 위치 정보를 얻습니다.",parameters:t.z.object({location:t.z.string().describe("The location to search for addresses.")})});(0,r.z6)({description:"사용자에게 여러 선택지 중 하나를 고르도록 요청합니다. 결과는 options 배열의 값 중 하나입니다.",parameters:t.z.object({message:t.z.string().describe("사용자에게 보여줄 안내 문구"),options:t.z.array(t.z.string()).min(1).describe("사용자가 선택할 수 있는 옵션 문자열 배열")})});var b=(0,r.z6)({description:"사용자에게 자유 입력을 받아옵니다. 예: 레이어 이름 입력, 주석 입력 등.",parameters:t.z.object({message:t.z.string().describe("입력 필드 상단에 표시할 안내 문구"),default:t.z.string().optional().describe("기본 입력값(선택)").default("")})}),g=(0,r.z6)({description:"체크박스 확인 방식으로 사용자의 확정을 받습니다. 예: 이용 약관 동의, 민감 데이터 확인 등.",parameters:t.z.object({message:t.z.string().describe("사용자에게 보여줄 문구"),label:t.z.string().describe("체크박스 옆에 표시할 라벨. 사용자가 체크해야 확인이 완료됩니다.")})}),E={EXTERNAL:"MPD013"},z=u=>{let e=`${process.env.GEON_API_BASE_URL}/map/api/map/`;return"heatmap"===u||"cluster"===u?`${e}wfs`:"group"===u?`${e}wms`:`${e}${u}`},f=(u,e,C,r)=>{let t=u.result.svcTySeCode;return"MPD011"===e&&"04"===C?"heatmap":"MPD011"===e&&"06"===C?"cluster":"MPD013"===e&&"06"===C&&"5"===r?"group":"6"===r?"geoImage":"M"===t?"wms":"T"===t?"wmts":"wfs"},h=u=>{switch(u){case"1":return"point";case"2":return"line";case"3":return"polygon";case"4":return"geoTiff";default:return}},I=u=>{let e=JSON.parse(u.result.mapUrlParamtr||"{}"),C=u.result.symbolCndCn?JSON.parse(u.result.symbolCndCn):void 0;return{layerConfig:{params:D(d({},e),{projection:`EPSG:${u.result.cntmSeCode}`})},style:C,layerInfo:{lyrId:u.result.lyrId,lyrNm:u.result.lyrNm,description:u.result.lyrDc}}},v=u=>{let e=()=>Math.floor(255*Math.random()),C=`rgb(${e()}, ${e()}, ${e()})`;return{styleObject:[{seperatorFunc:"default",style:{geometryType:"free",image:"point"===u?{circle:{radius:5,fill:{color:C},stroke:{color:"#000000",width:1}}}:{},fill:"polygon"===u?{color:C}:void 0,stroke:{color:"line"===u?C:"#000000",width:1}}}],serviceType:"vector"}},$=(0,r.z6)({description:`\uC9C0\uB3C4\uC5D0 \uB808\uC774\uC5B4\uB97C \uCD94\uAC00\uD558\uAE30 \uC704\uD55C \uBAA8\uB4E0 \uC124\uC815\uC744 \uC900\uBE44\uD569\uB2C8\uB2E4.
  - \uB808\uC774\uC5B4 ID\uB97C \uC0AC\uC6A9\uD558\uC5EC \uB808\uC774\uC5B4 \uC815\uBCF4\uB97C \uC870\uD68C\uD558\uACE0 \uC124\uC815\uC744 \uC0DD\uC131\uD569\uB2C8\uB2E4.`,parameters:t.z.object({userId:t.z.string().describe("사용자 ID"),insttCode:t.z.string().describe("기관 코드"),userSeCode:t.z.string().describe("사용자 구분 코드"),lyrId:t.z.string().describe("레이어 ID")}),execute(u){return p(this,arguments,function*({userId:u,insttCode:e,userSeCode:C,lyrId:r}){var t,s,o;try{let n=new URLSearchParams({crtfckey:process.env.GEON_API_KEY||"",lyrId:r,sessionInsttCode:e,sessionUserSeCode:C,sessionUserId:u}),i=yield fetch(`${process.env.GEON_API_BASE_URL}/smt/layer/info/select?${n.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",crtfckey:process.env.GEON_API_KEY||""}});if(!i.ok)throw Error(`API request failed with status ${i.status}`);let a=yield i.json();if(!a||!a.result)return{error:`${r} < \uB808\uC774\uC5B4\uB97C \uCC3E\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.`};let l=new URLSearchParams({crtfckey:process.env.GEON_API_KEY||"",cntntsId:a.result.cntntsId||""}),c=yield fetch(`${process.env.GEON_API_BASE_URL}/map/layer/cn/select?${l.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",crtfckey:process.env.GEON_API_KEY||""}});if(!c.ok)throw Error(`API request failed with status ${c.status}`);let B=yield c.json();if(!B||!B.result)return{error:"레이어 컨텐츠 정보를 찾을 수 없습니다."};let{lyrClCode:p,lyrClSeCode:m,lyrTySeCode:A,svcTySeCode:y}=a.result,b=f(a,p,m,A),g=h(A);if(p===E.EXTERNAL&&"11"===m){let u=I(a);return D(d({id:a.result.lyrId,name:a.result.lyrNm,type:"api",visible:!0,zIndex:0,info:u.layerInfo},u.layerConfig.params),{style:u.style})}let $={id:a.result.lyrId,name:a.result.lyrNm,type:"geoImage"===b?"geoImage":"geoserver",visible:"N"!==a.result.onOffAt,zIndex:0,server:z(b),layer:`${B.result.namespace}:${a.result.cntntsId}`,service:b,bbox:!1,method:"wmts"===b?"get":"post",crtfckey:process.env.GEON_API_KEY,projection:`EPSG:${a.result.cntmSeCode}`,geometryType:g,info:{lyrId:a.result.lyrId,lyrNm:a.result.lyrNm,description:a.result.lyrDc,metadata:{cntntsId:a.result.cntntsId,jobClCode:a.result.jobClCode,lyrClCode:p,lyrTySeCode:A,namespace:B.result.namespace}},namespace:B.result.namespace,matrixSet:"wmts"===b?`EPSG:${a.result.cntmSeCode}`:void 0};if("geoImage"===b){let u=yield fetch(`${process.env.GEON_API_BASE_URL}/smt/layer/geoImage?fileName=${encodeURIComponent(a.result.mapUrl)}&lyrId=${r}&crtfckey=${process.env.GEON_API_KEY}`,{method:"GET",headers:{"Content-Type":"application/json"}}),e=yield u.json(),C=null==(t=a.result.mapUrl.split(".").pop())?void 0:t.toLowerCase(),n=`data:image/${C};base64,${null==(s=null==e?void 0:e.result)?void 0:s.base64}`;$=D(d(D(d({},$),{type:"geoImage",url:n}),a.result.mapUrlParamtr&&JSON.parse(a.result.mapUrlParamtr)),{opacity:a.result.symbolCndCn&&(null==(o=JSON.parse(a.result.symbolCndCn))?void 0:o.opacity)})}let w=a.result.symbolCndCn?JSON.parse(a.result.symbolCndCn):void 0;return w?(["vector","cluster"].includes(w.serviceType)||"image"===w.serviceType?$.style=w.styleObject:"heatmap"===w.serviceType&&($=d(d({},$),w.styleObject)),w.opacity&&($.opacity=w.opacity)):"wfs"===b&&["1","2","3"].includes(A)?$.style=v(g):"wms"===b&&["1","2","3"].includes(A),"5"!==A&&"4"!==A&&"0"!==A&&"6"!==A&&("MPD013"!==p||"11"!==m)&&($.filter=a.result.flterCndCn,$.dynmFlterCndCn=a.result.dynmFlterCndCn),$}catch(u){return{error:`\uB808\uC774\uC5B4 \uC124\uC815 \uC0DD\uC131 \uC2E4\uD328: ${u.message}`}}})}});(0,r.z6)({description:`\uB808\uC774\uC5B4 \uBAA9\uB85D\uC744 \uAC80\uC0C9\uD569\uB2C8\uB2E4.
  - \uB808\uC774\uC5B4 \uC774\uB984\uC73C\uB85C \uAC80\uC0C9\uD558\uAC70\uB098 \uC804\uCCB4 \uBAA9\uB85D\uC744 \uC870\uD68C\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.
  - \uB370\uC774\uD130 \uAD6C\uBD84\uC5D0 \uB530\uB77C \uD544\uD130\uB9C1\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.
  Args:
        user_id: \uC0AC\uC6A9\uC790 ID (\uC608: "user_id")
        layer_name: \uAC80\uC0C9\uD560 \uB808\uC774\uC5B4 \uC774\uB984 \uB610\uB294 \uD0A4\uC6CC\uB4DC (\uC608: "\uAC74\uBB3C", "\uD589\uC815\uAD6C\uC5ED")
        hold_data_se_code: \uB370\uC774\uD130 \uAD6C\uBD84 ("0": \uC804\uCCB4, "1": \uC0AC\uC6A9\uC790, "2": \uACF5\uC720, "9": \uAD6D\uAC00)
  `,parameters:t.z.object({userId:t.z.string().describe("사용자 ID"),layerName:t.z.string().optional().describe("레이어 이름으로 검색 (선택)"),holdDataSeCode:t.z.string().optional().describe("데이터 구분 코드 (선택)")}),execute(u){return p(this,arguments,function*({userId:u,layerName:e="",holdDataSeCode:C="0"}){try{let r=process.env.GEON_API_BASE_URL,t=new URLSearchParams({crtfckey:process.env.GEON_API_KEY||"",userId:u,searchTxt:e,holdDataSeCode:C,pageIndex:"1",pageSize:"15"}),s=yield fetch(`${r}/smt/layer/info/list?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",crtfckey:process.env.GEON_API_KEY||""}});if(!s.ok)throw Error(`API request failed with status ${s.status}`);let o=yield s.json();if(!o||!o.result)return{error:"레이어 목록 조회 실패: 응답 데이터가 없습니다."};return o}catch(u){return{error:`\uB808\uC774\uC5B4 \uBAA9\uB85D \uC870\uD68C \uC2E4\uD328: ${u.message}`}}})}});var w=(0,r.z6)({description:`\uB808\uC774\uC5B4\uC758 \uC18D\uC131 \uC815\uBCF4\uB97C \uC870\uD68C\uD569\uB2C8\uB2E4.
  - \uD544\uD130 \uC0DD\uC131 \uC2DC \uC774 \uB3C4\uAD6C\uB85C \uC870\uD68C\uD55C properties\uC758 \uD544\uB4DC\uBA85\uACFC \uB370\uC774\uD130\uB97C \uCC38\uACE0\uD558\uC138\uC694.
  - \uCD5C\uB300 3\uAC1C\uC758 \uB370\uC774\uD130\uC14B\uC744 \uBC18\uD658\uD569\uB2C8\uB2E4.

  Args:
    lyr_id: \uB808\uC774\uC5B4 ID
    namespace: \uB808\uC774\uC5B4\uC758 \uB124\uC784\uC2A4\uD398\uC774\uC2A4 (\uC608: 'Wgeontest4')
    cntnts_id: \uCEE8\uD150\uCE20 ID (\uC608: 'L100004830')
            \uC8FC\uC758: LR\uB85C \uC2DC\uC791\uD558\uB294 ID\uB294 \uB808\uC774\uC5B4ID(lyrId)\uC774\uBBC0\uB85C \uC0AC\uC6A9\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
  
  \uC8FC\uC758\uC0AC\uD56D:
  - cntntsId\uAC00 'LR'\uB85C \uC2DC\uC791\uD558\uBA74 \uC548\uB429\uB2C8\uB2E4 (\uB808\uC774\uC5B4ID\uAC00 \uC544\uB2CC \uCEE8\uD150\uCE20ID\uB97C \uC0AC\uC6A9\uD574\uC57C \uD568)
  
  \uBC18\uD658 \uC815\uBCF4:
  - columns: \uCEEC\uB7FC \uC815\uBCF4 (\uB370\uC774\uD130 \uD0C0\uC785, \uC81C\uC57D\uC870\uAC74 \uB4F1)
  - data: \uC2E4\uC81C \uB370\uC774\uD130 \uC0D8\uD50C (\uCD5C\uB300 3\uAC1C)`,parameters:t.z.object({userId:t.z.string().describe("사용자 ID"),lyrId:t.z.string().min(1).describe("레이어 ID"),namespace:t.z.string().min(1).describe("레이어의 네임스페이스 (예: Wgeontest4)"),cntntsId:t.z.string().min(1).describe("컨텐츠 ID (LR로 시작하면 안됨)")}),execute:u=>p(null,[u],function*({userId:u,lyrId:e,namespace:C,cntntsId:r}){try{let t=process.env.GEON_API_KEY,s=process.env.GEON_API_BASE_URL;if(!t||!s)throw Error("API 키 또는 기본 URL이 설정되지 않았습니다.");let o=yield fetch(`${s}/builder/layer/column/select?crtfckey=${t}&lyrId=${e}&userId=${u}`,{method:"GET",headers:{"Content-Type":"application/json",crtfckey:t}});if(!o.ok)throw Error(`\uCEEC\uB7FC \uC815\uBCF4 \uC870\uD68C \uC2E4\uD328: ${o.status} ${o.statusText}`);let n=yield o.json();console.log(JSON.stringify({typeName:`${C}:${r}`,pageIndex:1,pageSize:3}));let i=yield fetch(`${s}/builder/layer/attributes/select?crtfckey=${t}`,{method:"POST",headers:{"Content-Type":"application/json",crtfckey:t},body:JSON.stringify({typeName:`${C}:${r}`,pageIndex:1,pageSize:3})});if(!i.ok)throw Error(`\uC18D\uC131 \uB370\uC774\uD130 \uC870\uD68C \uC2E4\uD328: ${i.status} ${i.statusText}`);let a=yield i.json();if(!n.code||200!==n.code||!a.code||200!==a.code)return{error:"레이어 정보를 찾을 수 없습니다."};return{columns:n.result.map(u=>({name:u.columnNm,description:u.columnNcm,type:u.dataTy,editable:"Y"===u.editPosblAt,required:"Y"===u.esntlAt,minValue:u.mummLt,maxValue:u.mxmmLt,groupCode:u.cmmnGroupCode,groupName:u.cmmnGroupCodeNm})),data:a.result.features.map(u=>u.properties)}}catch(u){return console.error("Error fetching layer information:",u),{error:`\uB808\uC774\uC5B4 \uC18D\uC131 \uC870\uD68C \uC911 \uC624\uB958\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4: ${u.message}`}}})});(0,r.z6)({description:`\uB808\uC774\uC5B4\uC758 \uC18D\uC131\uC815\uBCF4\uB97C \uAE30\uBC18\uC73C\uB85C CQL \uD544\uD130\uB97C \uC0DD\uC131\uD569\uB2C8\uB2E4.
  
  \uD544\uB4DC \uC120\uD0DD \uAC00\uC774\uB4DC:
  - getLayerAttributes \uB3C4\uAD6C\uB85C \uC870\uD68C\uD55C properties\uC758 \uD544\uB4DC\uBA85\uC744 \uC0AC\uC6A9\uD558\uC138\uC694
  - \uD544\uB4DC\uBA85\uC740 \uC815\uD655\uD788 \uC77C\uCE58\uD574\uC57C \uD569\uB2C8\uB2E4 (\uB300\uC18C\uBB38\uC790 \uAD6C\uBD84)
  
  \uC5F0\uC0B0\uC790 \uC0AC\uC6A9 \uAC00\uC774\uB4DC:
  - \uD14D\uC2A4\uD2B8: LIKE (\uBD80\uBD84\uC77C\uCE58), = (\uC644\uC804\uC77C\uCE58)
  - \uC22B\uC790: >, >=, <, <=, =
  - \uBAA9\uB85D: IN
  
  Args:
    lyr_id: \uD544\uD130 \uC801\uC6A9\uD560 \uB808\uC774\uC5B4 ID
    field_name: properties\uC5D0\uC11C \uD655\uC778\uB41C \uC815\uD655\uD55C \uD544\uB4DC\uBA85
    operator: \uC5F0\uC0B0\uC790 (=, >, <, >=, <=, LIKE, IN)
    value: \uD544\uD130 \uAC12
    filter_type: "exact" \uB610\uB294 "partial"
     `,parameters:t.z.object({lyrId:t.z.string().min(1).describe("필터를 적용할 레이어 ID"),fieldName:t.z.string().min(1).describe("필터링할 필드명 (properties의 key)"),operator:t.z.enum(["=",">","<",">=","<=","LIKE","IN"]).describe("비교 연산자"),value:t.z.string().min(1).describe("필터 값"),filterType:t.z.enum(["exact","partial"]).optional().default("exact").describe("필터 타입")}),execute:u=>p(null,[u],function*({lyrId:u,fieldName:e,operator:C,value:r,filterType:t="exact"}){try{let t;if(e=e.trim().replace(/['"]/g,""),"LIKE"===C.toUpperCase())t=`"${e}" LIKE '%${r}%'`;else if("IN"===C.toUpperCase()){let u=r.split(",").map(u=>u.trim()).map(u=>`'${u}'`).join(",");t=`"${e}" IN (${u})`}else{let u=!isNaN(Number(r));t=`"${e}"${C}${u?r:`'${r}'`}`}return{lyr_id:u,filter:t,description:`\uD544\uB4DC '${e}'\uC5D0 \uB300\uD574 ${C} '${r}' \uC870\uAC74\uC73C\uB85C \uD544\uD130\uB9C1\uD569\uB2C8\uB2E4.`}}catch(u){return{error:`\uD544\uD130 \uC0DD\uC131 \uC2E4\uD328: ${u.message}`}}})}),t.z.enum(["point","line","polygon"]),t.z.enum(["wms","wfs"]),(0,r.z6)({description:`
    \uC790\uC5F0\uC5B4 \uC694\uCCAD\uC744 \uBD84\uC11D\uD558\uC5EC OpenLayers Flat \uBCA1\uD130 \uC2A4\uD0C0\uC77C\uC744 \uC0DD\uC131\uD569\uB2C8\uB2E4.
    Point", "LineString", "Polygon", "Icon" \uD0C0\uC785\uC744 \uC0AC\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.
  `,parameters:t.z.object({lyrId:t.z.string().describe("스타일이 적용될 레이어 ID"),styleRequest:t.z.string().describe("자연어 스타일 요청"),iconRequest:t.z.string().describe(`
      \uC544\uC774\uCF58 \uC2A4\uD0C0\uC77C \uC694\uCCAD
      \uC608\uC2DC: 
      '\uC2A4\uD0C0\uBC85\uC2A4': '\uC2A4\uD0C0\uBC85\uC2A4 \uB85C\uACE0, \uB179\uC0C9 \uC6D0 \uC548\uC5D0 \uAE34\uBA38\uB9AC\uC758 \uC655\uAD00\uC744 \uC4F4 \uD558\uC580\uC0C9 \uC778\uC5B4 \uD615\uC0C1\uC774 \uBCF4\uC774\uB294 \uBAA8\uC2B5'
      '\uCE74\uD398': '\uAC08\uC0C9 \uCEE4\uD53C \uCEF5 \uBAA8\uC591\uC5D0 \uAE40\uC774 \uC62C\uB77C\uC624\uB294 \uBAA8\uC2B5'
      '\uC2DD\uB2F9': '\uD3EC\uD06C\uC640 \uB098\uC774\uD504\uAC00 \uAD50\uCC28\uB41C \uD615\uD0DC\uC758 \uC2DD\uB2F9 \uC544\uC774\uCF58'
      '\uBCD1\uC6D0': '\uD770\uC0C9 \uBC30\uACBD\uC5D0 \uBE68\uAC04\uC0C9 \uC2ED\uC790\uAC00 \uC758\uB8CC \uAE30\uD638'
      '\uAE30\uBCF8': '\uAC74\uBB3C \uBAA8\uC591'
      `),geometryType:t.z.enum(["Icon","Point","LineString","Polygon"]).describe("도형 타입")}),execute:u=>p(null,[u],function*({lyrId:u,styleRequest:e,iconRequest:C,geometryType:o}){try{let n=t.z.object({"text-value":t.z.string().optional().describe("텍스트 내용"),"text-font":t.z.string().optional().describe("폰트 스타일 (CSS 형식)"),"text-fill-color":t.z.string().optional().describe("텍스트 색상"),"text-stroke-color":t.z.string().optional().describe("텍스트 테두리 색상"),"text-stroke-width":t.z.number().optional().describe("텍스트 테두리 두께"),"text-scale":t.z.number().optional().describe("텍스트 크기 배율"),"text-offset-x":t.z.number().optional().describe("텍스트 X축 오프셋"),"text-offset-y":t.z.number().optional().describe("텍스트 Y축 오프셋"),"text-rotation":t.z.number().optional().describe("텍스트 회전각도 (라디안)"),"text-background-fill-color":t.z.string().optional().describe("텍스트 배경 색상"),"text-padding":t.z.array(t.z.number()).length(4).optional().describe("텍스트 패딩 [상,우,하,좌]")}).optional(),i=t.z.object({"z-index":t.z.number().optional().describe("스타일 z-index")}),a={Point:t.z.object({style:t.z.array(t.z.union([i.extend({"circle-radius":t.z.number().optional(),"circle-fill-color":t.z.string().optional(),"circle-stroke-color":t.z.string().optional(),"circle-stroke-width":t.z.number().optional(),"circle-scale":t.z.number().optional()}),n])).describe("점 스타일 배열")}),LineString:t.z.object({style:t.z.array(t.z.union([i.extend({"stroke-color":t.z.string().optional(),"stroke-width":t.z.number().optional(),"stroke-line-cap":t.z.enum(["butt","round","square"]).optional(),"stroke-line-dash":t.z.array(t.z.number()).optional(),"stroke-line-join":t.z.enum(["bevel","round","miter"]).optional()}),n])).describe("선 스타일 배열")}),Polygon:t.z.object({style:t.z.array(t.z.union([i.extend({"fill-color":t.z.string().optional(),"fill-pattern-src":t.z.string().optional(),"stroke-color":t.z.string().optional(),"stroke-width":t.z.number().optional()}),n])).describe("폴리곤 스타일 배열")}),Icon:t.z.object({style:t.z.array(t.z.union([i.extend({"icon-src":t.z.string(),"icon-scale":t.z.number().optional(),"icon-anchor":t.z.array(t.z.number()).length(2).optional(),"icon-color":t.z.string().optional(),"icon-rotation":t.z.number().optional(),"icon-opacity":t.z.number().optional()}),n])).describe("아이콘 스타일 배열")})},l=yield(0,r.pY)({model:(0,s.g)("qwen2.5:32b"),prompt:`Generate a style array for a ${o} layer with possible text labels based on this request: ${e}
                Consider including appropriate text styling if the request implies labels are needed.
                For Icon geometry types, do not include any text styles or labels
                `,schema:a[o]});if("Icon"===o){let e=`
        Design a simple, clean SVG icon for a "${C}" at 32x32 pixels resolution.

        Follow these specifications:
        - Use minimal details but make it clearly recognizable
        - Include appropriate colors for the subject
        - Ensure shapes are well-defined with clean outlines
        - Optimize for small size display
        - Use only vector elements (no bitmap/raster)
        - No text elements in the icon

        For a "\uC2A4\uD0C0\uBC85\uC2A4" (Starbucks) icon:
        - Include the circular green logo with simplified mermaid/siren silhouette
        - Use the iconic Starbucks green (#00704A)

        For a generic "\uCE74\uD398" (cafe) icon:
        - Include a simple coffee cup with steam
        - Use warm brown tones (#8B4513 or similar)

        Output only valid SVG code without comments, XML declarations or any additional text.
        `,t=(yield(0,r.Df)({model:(0,s.g)("qwen2.5:32b"),prompt:e})).text.replace(/```xml|```svg|```/g,"").replace(/<\?xml[^>]*\?>/g,"").replace(/<!DOCTYPE[^>]*>/g,"").trim();t.includes("<svg")||(t=`<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">${t}</svg>`),t.includes("width=")&&t.includes("height=")||(t=t.replace("<svg",'<svg width="32" height="32"')),t.includes("viewBox=")||(t=t.replace("<svg",'<svg viewBox="0 0 32 32"')),t.includes("xmlns=")||(t=t.replace("<svg",'<svg xmlns="http://www.w3.org/2000/svg"')),console.log("정제된 SVG 코드:",t);let n=Buffer.from(t).toString("base64"),i=`data:image/svg+xml;base64,${n}`,a=yield l.toJsonResponse().json();return a.style=a.style.map(u=>(u["icon-src"]&&(u["icon-src"]=i),u)),{lyrId:u,geometryType:o,style:a.style}}let c=yield l.toJsonResponse().json();return{lyrId:u,geometryType:o,style:c.style}}catch(u){throw Error(`\uC2A4\uD0C0\uC77C \uC0DD\uC131 \uC911 \uC624\uB958\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4: ${u.message}`)}})}),t.z.object({image:t.z.object({circle:t.z.object({radius:t.z.number(),fill:t.z.object({color:t.z.tuple([t.z.number(),t.z.number(),t.z.number(),t.z.number()])}),stroke:t.z.object({color:t.z.tuple([t.z.number(),t.z.number(),t.z.number(),t.z.number()]),width:t.z.number(),lineDash:t.z.array(t.z.number()).optional()}),scale:t.z.number().optional()}).optional(),icon:t.z.object({src:t.z.string(),scale:t.z.number(),rotation:t.z.number(),opacity:t.z.number()}).optional()})});var _=(0,r.z6)({description:`\uD558\uB098\uC758 \uCD9C\uBC1C\uC9C0\uC5D0\uC11C \uD558\uB098\uC758 \uBAA9\uC801\uC9C0\uAE4C\uC9C0\uC758 \uACBD\uB85C\uC5D0 \uB300\uD55C \uC0C1\uC138 \uC815\uBCF4\uB97C \uC81C\uACF5\uD569\uB2C8\uB2E4.
    \uACBD\uC720\uC9C0\uB294 \uCD5C\uB300 5\uAC1C\uAE4C\uC9C0 \uCD94\uAC00\uD560 \uC218 \uC788\uC73C\uBA70, \uBAA8\uB4E0 \uACBD\uC720\uC9C0\uB97C \uD3EC\uD568\uD55C \uACBD\uB85C\uC758 \uCD1D \uAC70\uB9AC\uB294 1,500km \uBBF8\uB9CC\uC774\uC5B4\uC57C \uD569\uB2C8\uB2E4.`,parameters:t.z.object({origin:t.z.string().min(1).describe("출발지 좌표 (예: ${X좌표},${Y좌표},name=${출발지명} 또는 ${X좌표},${Y좌표})"),destination:t.z.string().min(1).describe("목적지 좌표 (예: ${X좌표},${Y좌표},name=${목적지명} 또는 ${X좌표},${Y좌표})"),waypoints:t.z.string().optional().describe('경유지 좌표 (최대 5개, | 로 구분, 예: "127.1,37.4|127.2,37.5")'),priority:t.z.enum(["RECOMMEND","TIME","DISTANCE"]).default("RECOMMEND").describe("경로 탐색 우선순위"),avoid:t.z.string().optional().describe("회피 옵션 (ferries, toll, motorway, schoolzone, uturn, 쉼표로 구분)"),roadevent:t.z.number().int().default(0).describe("도로 통제 정보 반영 (0: 전체, 1: 출발/도착지 제외, 2: 미반영)"),alternatives:t.z.boolean().default(!1).describe("대안 경로 제공 여부"),roadDetails:t.z.boolean().default(!1).describe("상세 도로 정보 제공 여부"),carType:t.z.number().int().default(1).describe("차종"),carFuel:t.z.enum(["GASOLINE","DIESEL","LPG"]).default("GASOLINE").describe("유종"),carHipass:t.z.boolean().default(!1).describe("하이패스 장착 여부"),summary:t.z.boolean().default(!1).describe("요약 정보 제공 여부")}),execute:u=>p(null,[u],function*({origin:u,destination:e,waypoints:C,priority:r,avoid:t,roadevent:s,alternatives:o,roadDetails:n,carType:i,carFuel:a,carHipass:l,summary:c}){let B={origin:u,destination:e,priority:r,car_fuel:a,car_hipass:l,alternatives:o,road_details:n};console.log("전달받은 길찾기 파라미터:",B),C&&(B.waypoints=C),t&&(B.avoid=t),s&&(B.roadevent=s),i&&(B.car_type=i),c&&(B.summary=c);let d={Authorization:`KakaoAK ${process.env.KAKAO_REST_API_KEY}`};try{let u=yield fetch(`https://apis-navi.kakaomobility.com/v1/directions?${new URLSearchParams(B)}`,{method:"GET",headers:d}),e=yield u.json();return console.log(e),{trans_id:e.trans_id,routes:e.routes||[],result_code:e.result_code,result_msg:e.result_msg,summary:e.summary||{},origin:e.origin||{},destination:e.destination||{},waypoints:e.waypoints||[],priority:e.priority,bound:e.bound||{},fare:e.fare||{},distance:e.distance,duration:e.duration,sections:e.sections||[]}}catch(u){return console.error(`Error making request: ${u.message}`),{error:`\uACBD\uB85C \uD0D0\uC0C9 \uC2E4\uD328: ${u.message}`}}})}),x=(0,r.z6)({description:`\uD2B9\uC815 \uC601\uC5ED \uB0B4\uC5D0\uC11C \uD3EC\uC778\uD2B8 \uB370\uC774\uD130\uC758 \uBC00\uB3C4\uB97C \uACC4\uC0B0\uD569\uB2C8\uB2E4.
    - \uC778\uAD6C \uBC00\uB3C4, \uBC94\uC8C4 \uBC1C\uC0DD\uB960 \uB4F1 \uD3EC\uC778\uD2B8 \uAE30\uBC18 \uB370\uC774\uD130\uC758 \uBD84\uD3EC\uB97C \uBD84\uC11D\uD558\uB294 \uB370 \uC0AC\uC6A9\uB429\uB2C8\uB2E4.
    Args:
      userId: \uC0AC\uC6A9\uC790 ID (\uC608: "geonuser")
      trgetTypeName: \uBD84\uC11D \uB300\uC0C1 \uB808\uC774\uC5B4 ID (\uC608: "geonpaas:L200000007")
      lyrNm: \uACB0\uACFC \uB808\uC774\uC5B4 \uC774\uB984 (\uC120\uD0DD, \uAE30\uBCF8\uAC12: "\uBC00\uB3C4 \uBD84\uC11D \uACB0\uACFC")
    `,parameters:t.z.object({userId:t.z.string().describe("사용자 ID"),trgetTypeName:t.z.string().describe("분석 대상 레이어 ID"),lyrNm:t.z.string().optional().describe("결과 레이어 이름 (선택)")}),execute(u){return p(this,arguments,function*({userId:u,trgetTypeName:e,lyrNm:C}){try{let r=new URLSearchParams({opertUsrId:u,trgetCode:"102",trgetTypeName:e,densityOption:"simple",lyrNm:C||"밀도 분석 결과",direct:"true"}),t=yield fetch(`https://gsapi.geon.kr/analysis/anals/pttrn/density?${r}`,{method:"POST",headers:{"Content-Type":"application/json",crtfckey:process.env.GEON_API_KEY||""}}),s=yield t.json();if(!t.ok)throw Error(`API \uC694\uCCAD \uC2E4\uD328: \uC0C1\uD0DC \uCF54\uB4DC ${t.status} ${t.statusText}`);return s.result}catch(u){return{error:`\uBC00\uB3C4 \uBD84\uC11D \uC2E4\uD328: ${u.message}`}}})}});(0,r.z6)({description:`\uC8FC\uC5B4\uC9C4 \uB3C4\uD615 \uC8FC\uC704\uC5D0 \uC9C0\uC815\uB41C \uAC70\uB9AC\uB9CC\uD07C\uC758 \uBC84\uD37C\uB97C \uC0DD\uC131\uD569\uB2C8\uB2E4.
    - \uD2B9\uC815 \uC9C0\uC5ED \uC8FC\uBCC0\uC758 \uC601\uD5A5\uC744 \uBD84\uC11D\uD558\uAC70\uB098, \uC9C0\uC815\uB41C \uAC70\uB9AC \uB0B4\uC758 \uAC1D\uCCB4\uB97C \uC2DD\uBCC4\uD558\uB294 \uB370 \uC0AC\uC6A9\uB429\uB2C8\uB2E4.
    Args:
      userId: \uC0AC\uC6A9\uC790 ID (\uC608: "geonuser")
      trgetTypeName: \uBD84\uC11D \uB300\uC0C1 \uB808\uC774\uC5B4 ID (\uC608: "geonpaas:L200000007")
      bufferDistance: \uBC84\uD37C \uAC70\uB9AC (\uBBF8\uD130 \uB2E8\uC704, \uC608: 500)
      lyrNm: \uACB0\uACFC \uB808\uC774\uC5B4 \uC774\uB984 (\uC120\uD0DD, \uAE30\uBCF8\uAC12: "\uBC84\uD37C \uBD84\uC11D \uACB0\uACFC")
    `,parameters:t.z.object({userId:t.z.string().describe("사용자 ID"),trgetTypeName:t.z.string().describe("분석 대상 레이어 ID"),bufferDistance:t.z.number().describe("버퍼 거리 (미터)"),lyrNm:t.z.string().optional().describe("결과 레이어 이름 (선택)")}),execute(u){return p(this,arguments,function*({userId:u,trgetTypeName:e,bufferDistance:C,lyrNm:r}){try{new URLSearchParams({crtfckey:process.env.GEON_API_KEY||"",opertUsrId:u,trgetCode:"102",trgetTypeName:e,bufferDistance:C.toString(),lyrNm:r||"버퍼 분석 결과",direct:"true"});let t=yield fetch("https://gsapi.geon.kr/analysis/anals/proximity/buffer",{method:"POST",headers:{"Content-Type":"application/json",crtfckey:process.env.GEON_API_KEY||""},body:JSON.stringify({})});if(!t.ok)throw Error(`API \uC694\uCCAD \uC2E4\uD328: \uC0C1\uD0DC \uCF54\uB4DC ${t.status}`);return(yield t.json()).result}catch(u){return{error:`\uBC84\uD37C \uBD84\uC11D \uC2E4\uD328: ${u.message}`}}})}})}};