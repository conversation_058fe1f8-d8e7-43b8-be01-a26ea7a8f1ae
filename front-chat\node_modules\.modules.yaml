hoistPattern:
  - '*'
hoistedDependencies:
  '@ai-sdk/ui-utils@1.2.11(zod@3.24.1)':
    '@ai-sdk/ui-utils': private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@auth/core@0.37.4':
    '@auth/core': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@codemirror/autocomplete@6.18.4':
    '@codemirror/autocomplete': private
  '@codemirror/commands@6.7.1':
    '@codemirror/commands': private
  '@codemirror/lang-css@6.3.1':
    '@codemirror/lang-css': private
  '@codemirror/language@6.10.7':
    '@codemirror/language': private
  '@codemirror/lint@6.8.4':
    '@codemirror/lint': private
  '@codemirror/search@6.5.8':
    '@codemirror/search': private
  '@esbuild-kit/core-utils@3.3.2':
    '@esbuild-kit/core-utils': private
  '@esbuild-kit/esm-loader@2.6.5':
    '@esbuild-kit/esm-loader': private
  '@esbuild/aix-ppc64@0.19.12':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.19.12':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.19.12':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.19.12':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.19.12':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.19.12':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.19.12':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.19.12':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.19.12':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.19.12':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.19.12':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.19.12':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.19.12':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.19.12':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.19.12':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.19.12':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.19.12':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-x64@0.19.12':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.23.1':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.19.12':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.19.12':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.19.12':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.19.12':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.19.12':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.4.1(eslint@9.17.0(jiti@1.21.6))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.19.1':
    '@eslint/config-array': private
  '@eslint/core@0.9.1':
    '@eslint/core': private
  '@eslint/eslintrc@3.2.0':
    '@eslint/eslintrc': private
  '@eslint/js@9.17.0':
    '@eslint/js': private
  '@eslint/object-schema@2.1.5':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.2.4':
    '@eslint/plugin-kit': private
  '@fastify/busboy@2.1.1':
    '@fastify/busboy': private
  '@floating-ui/core@1.6.8':
    '@floating-ui/core': private
  '@floating-ui/dom@1.6.12':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.8':
    '@floating-ui/utils': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.1':
    '@humanwhocodes/retry': private
  '@img/sharp-darwin-arm64@0.34.2':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.34.2':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.1.0':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.1.0':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.1.0':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.1.0':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-ppc64@1.1.0':
    '@img/sharp-libvips-linux-ppc64': private
  '@img/sharp-libvips-linux-s390x@1.1.0':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.1.0':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.34.2':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.34.2':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.34.2':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.34.2':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.34.2':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.34.2':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.34.2':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-arm64@0.34.2':
    '@img/sharp-win32-arm64': private
  '@img/sharp-win32-ia32@0.34.2':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.34.2':
    '@img/sharp-win32-x64': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@lezer/common@1.2.3':
    '@lezer/common': private
  '@lezer/css@1.1.9':
    '@lezer/css': private
  '@lezer/highlight@1.2.1':
    '@lezer/highlight': private
  '@lezer/html@1.3.10':
    '@lezer/html': private
  '@lezer/javascript@1.4.21':
    '@lezer/javascript': private
  '@lezer/lr@1.4.2':
    '@lezer/lr': private
  '@lezer/python@1.1.15':
    '@lezer/python': private
  '@marijn/find-cluster-break@1.0.2':
    '@marijn/find-cluster-break': private
  '@neondatabase/serverless@0.7.2':
    '@neondatabase/serverless': private
  '@next/env@15.3.3':
    '@next/env': private
  '@next/eslint-plugin-next@15.1.0':
    '@next/eslint-plugin-next': private
  '@next/swc-darwin-arm64@15.3.3':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.3.3':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.3.3':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.3.3':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.3.3':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.3.3':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.3.3':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.3.3':
    '@next/swc-win32-x64-msvc': private
  '@noble/hashes@1.6.1':
    '@noble/hashes': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  '@panva/hkdf@1.2.1':
    '@panva/hkdf': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@radix-ui/number@1.1.0':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.1':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.1(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collection@1.1.1(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.1(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.1(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.2(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.1(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.1(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.3(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popper@1.2.1(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.3(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.2(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.0.1(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.1(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-use-callback-ref@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-escape-keydown@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.1.1(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.0':
    '@radix-ui/rect': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.10.4':
    '@rushstack/eslint-patch': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@tanstack/table-core@8.21.3':
    '@tanstack/table-core': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/diff-match-patch@1.0.36':
    '@types/diff-match-patch': private
  '@types/estree-jsx@1.0.5':
    '@types/estree-jsx': private
  '@types/estree@1.0.6':
    '@types/estree': private
  '@types/hast@3.0.4':
    '@types/hast': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/katex@0.16.7':
    '@types/katex': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/ms@0.7.34':
    '@types/ms': private
  '@types/prismjs@1.26.5':
    '@types/prismjs': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@types/use-sync-external-store@0.0.6':
    '@types/use-sync-external-store': private
  '@typescript-eslint/eslint-plugin@8.18.0(@typescript-eslint/parser@8.18.0(eslint@9.17.0(jiti@1.21.6))(typescript@5.7.2))(eslint@9.17.0(jiti@1.21.6))(typescript@5.7.2)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.18.0(eslint@9.17.0(jiti@1.21.6))(typescript@5.7.2)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/scope-manager@8.18.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@8.18.0(eslint@9.17.0(jiti@1.21.6))(typescript@5.7.2)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.18.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.18.0(typescript@5.7.2)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.18.0(eslint@9.17.0(jiti@1.21.6))(typescript@5.7.2)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.18.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.2.1':
    '@ungap/structured-clone': private
  '@vercel/postgres@0.8.0':
    '@vercel/postgres': private
  acorn-jsx@5.3.2(acorn@8.14.0):
    acorn-jsx: private
  acorn@8.14.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.4:
    aria-hidden: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.1:
    array-buffer-byte-length: private
  array-includes@3.1.8:
    array-includes: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.5:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.2:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  async-retry@1.3.3:
    async-retry: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.2:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  buffer-from@1.1.2:
    buffer-from: private
  bufferutil@4.0.8:
    bufferutil: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.1:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.2:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  caniuse-lite@1.0.30001688:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@1.1.4:
    character-entities-legacy: private
  character-entities@1.2.4:
    character-entities: private
  character-reference-invalid@1.1.4:
    character-reference-invalid: private
  chokidar@3.6.0:
    chokidar: private
  client-only@0.0.1:
    client-only: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  commander@4.1.1:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  crelt@1.0.6:
    crelt: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-box-model@1.2.1:
    css-box-model: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-view-buffer@1.0.1:
    data-view-buffer: private
  data-view-byte-length@1.0.1:
    data-view-byte-length: private
  data-view-byte-offset@1.0.0:
    data-view-byte-offset: private
  debug@4.4.0:
    debug: private
  decode-named-character-reference@1.0.2:
    decode-named-character-reference: private
  deep-is@0.1.4:
    deep-is: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  dequal@2.0.3:
    dequal: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  devlop@1.1.0:
    devlop: private
  didyoumean@1.2.2:
    didyoumean: private
  diff-match-patch@1.0.5:
    diff-match-patch: private
  dlv@1.1.3:
    dlv: private
  doctrine@2.1.0:
    doctrine: private
  dunder-proto@1.0.0:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  embla-carousel-reactive-utils@8.5.1(embla-carousel@8.5.1):
    embla-carousel-reactive-utils: private
  embla-carousel@8.5.1:
    embla-carousel: private
  emoji-regex@9.2.2:
    emoji-regex: private
  enhanced-resolve@5.17.1:
    enhanced-resolve: private
  es-abstract@1.23.5:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.0:
    es-iterator-helpers: private
  es-object-atoms@1.0.0:
    es-object-atoms: private
  es-set-tostringtag@2.0.3:
    es-set-tostringtag: private
  es-shim-unscopables@1.0.2:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild-register@3.6.0(esbuild@0.19.12):
    esbuild-register: private
  esbuild@0.19.12:
    esbuild: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-import-resolver-typescript@3.7.0(eslint-plugin-import@2.31.0)(eslint@9.17.0(jiti@1.21.6)):
    eslint-import-resolver-typescript: private
  eslint-module-utils@2.12.0(@typescript-eslint/parser@8.18.0(eslint@9.17.0(jiti@1.21.6))(typescript@5.7.2))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.7.0)(eslint@9.17.0(jiti@1.21.6)):
    eslint-module-utils: private
  eslint-plugin-import@2.31.0(@typescript-eslint/parser@8.18.0(eslint@9.17.0(jiti@1.21.6))(typescript@5.7.2))(eslint-import-resolver-typescript@3.7.0)(eslint@9.17.0(jiti@1.21.6)):
    eslint-plugin-import: private
  eslint-plugin-jsx-a11y@6.10.2(eslint@9.17.0(jiti@1.21.6)):
    eslint-plugin-jsx-a11y: private
  eslint-plugin-react-hooks@5.1.0(eslint@9.17.0(jiti@1.21.6)):
    eslint-plugin-react-hooks: private
  eslint-plugin-react@7.37.2(eslint@9.17.0(jiti@1.21.6)):
    eslint-plugin-react: private
  eslint-scope@8.2.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: private
  espree@10.3.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-util-is-identifier-name@3.0.0:
    estree-util-is-identifier-name: private
  esutils@2.0.3:
    esutils: private
  extend@3.0.2:
    extend: private
  fast-glob@3.3.2:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.17.1:
    fastq: private
  fault@1.0.4:
    fault: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.2:
    flatted: private
  for-each@0.3.3:
    for-each: private
  foreground-child@3.3.0:
    foreground-child: private
  format@0.2.2:
    format: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.7:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  get-intrinsic@1.2.6:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-symbol-description@1.0.2:
    get-symbol-description: private
  get-tsconfig@4.8.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  globals@14.0.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-bigints@1.0.2:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hast-util-parse-selector@2.2.5:
    hast-util-parse-selector: private
  hast-util-to-jsx-runtime@2.3.2:
    hast-util-to-jsx-runtime: private
  hast-util-whitespace@3.0.0:
    hast-util-whitespace: private
  hastscript@6.0.0:
    hastscript: private
  highlight.js@10.7.3:
    highlight.js: private
  highlightjs-vue@1.0.0:
    highlightjs-vue: private
  html-url-attributes@3.0.1:
    html-url-attributes: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.0:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inline-style-parser@0.2.4:
    inline-style-parser: private
  internal-slot@1.1.0:
    internal-slot: private
  is-alphabetical@1.0.4:
    is-alphabetical: private
  is-alphanumerical@1.0.4:
    is-alphanumerical: private
  is-array-buffer@3.0.4:
    is-array-buffer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-async-function@2.0.0:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.1:
    is-boolean-object: private
  is-buffer@2.0.5:
    is-buffer: private
  is-bun-module@1.3.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.0:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-decimal@1.0.4:
    is-decimal: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.0:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.0.10:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-hexadecimal@1.0.4:
    is-hexadecimal: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.0:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.3:
    is-shared-array-buffer: private
  is-string@1.1.0:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.13:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.0:
    is-weakref: private
  is-weakset@2.0.3:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  iterator.prototype@1.1.4:
    iterator.prototype: private
  jackspeak@3.4.3:
    jackspeak: private
  jiti@1.21.6:
    jiti: private
  jose@5.9.6:
    jose: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@1.0.2:
    json5: private
  jsondiffpatch@0.6.0:
    jsondiffpatch: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  katex@0.16.15:
    katex: private
  keyv@4.5.4:
    keyv: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.merge@4.6.2:
    lodash.merge: private
  longest-streak@3.1.0:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  lowlight@1.20.0:
    lowlight: private
  lru-cache@10.4.3:
    lru-cache: private
  markdown-table@3.0.4:
    markdown-table: private
  math-intrinsics@1.0.0:
    math-intrinsics: private
  mdast-util-find-and-replace@3.0.1:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.0.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.0.0:
    mdast-util-gfm: private
  mdast-util-math@3.0.0:
    mdast-util-math: private
  mdast-util-mdx-expression@2.0.1:
    mdast-util-mdx-expression: private
  mdast-util-mdx-jsx@3.1.3:
    mdast-util-mdx-jsx: private
  mdast-util-mdxjs-esm@2.0.1:
    mdast-util-mdxjs-esm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  merge2@1.4.1:
    merge2: private
  micromark-core-commonmark@2.0.2:
    micromark-core-commonmark: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.0:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-extension-math@3.1.0:
    micromark-extension-math: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.0.3:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.1:
    micromark-util-types: private
  micromark@4.0.1:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  motion-dom@12.17.3:
    motion-dom: private
  motion-utils@12.12.1:
    motion-utils: private
  ms@2.1.3:
    ms: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.8:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  node-gyp-build@4.8.4:
    node-gyp-build: private
  normalize-path@3.0.0:
    normalize-path: private
  oauth4webapi@3.1.4:
    oauth4webapi: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.3:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.5:
    object.assign: private
  object.entries@1.1.8:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.0:
    object.values: private
  obuf@1.1.2:
    obuf: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  parse-entities@2.0.0:
    parse-entities: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  pg-cloudflare@1.1.1:
    pg-cloudflare: private
  pg-connection-string@2.7.0:
    pg-connection-string: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-numeric@1.0.2:
    pg-numeric: private
  pg-pool@3.7.0(pg@8.13.1):
    pg-pool: private
  pg-protocol@1.7.0:
    pg-protocol: private
  pg-types@4.0.2:
    pg-types: private
  pgpass@1.0.5:
    pgpass: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.6:
    pirates: private
  possible-typed-array-names@1.0.0:
    possible-typed-array-names: private
  postcss-import@15.1.0(postcss@8.4.49):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.4.49):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.4.49):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.4.49):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  postgres-range@1.1.4:
    postgres-range: private
  preact-render-to-string@6.5.11(preact@10.24.3):
    preact-render-to-string: private
  preact@10.24.3:
    preact: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prismjs@1.29.0:
    prismjs: private
  prop-types@15.8.1:
    prop-types: private
  property-information@6.5.0:
    property-information: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  raf-schd@4.0.3:
    raf-schd: private
  react-is@16.13.1:
    react-is: private
  react-redux@9.2.0(@types/react@19.0.1)(react@19.0.0)(redux@5.0.1):
    react-redux: private
  react-remove-scroll-bar@2.3.8(@types/react@19.0.1)(react@19.0.0):
    react-remove-scroll-bar: private
  react-remove-scroll@2.6.0(@types/react@19.0.1)(react@19.0.0):
    react-remove-scroll: private
  react-style-singleton@2.2.3(@types/react@19.0.1)(react@19.0.0):
    react-style-singleton: private
  read-cache@1.0.0:
    read-cache: private
  readdirp@3.6.0:
    readdirp: private
  redux@5.0.1:
    redux: private
  reflect.getprototypeof@1.0.8:
    reflect.getprototypeof: private
  refractor@3.6.0:
    refractor: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regexp.prototype.flags@1.5.3:
    regexp.prototype.flags: private
  remark-parse@11.0.0:
    remark-parse: private
  remark-rehype@11.1.1:
    remark-rehype: private
  remark-stringify@11.0.0:
    remark-stringify: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.9:
    resolve: private
  retry@0.13.1:
    retry: private
  reusify@1.0.4:
    reusify: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  scheduler@0.25.0:
    scheduler: private
  secure-json-parse@2.7.0:
    secure-json-parse: private
  semver@6.3.1:
    semver: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  sharp@0.34.2:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  split2@4.2.0:
    split2: private
  stable-hash@0.0.4:
    stable-hash: private
  streamsearch@1.1.0:
    streamsearch: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.11:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  style-mod@4.1.2:
    style-mod: private
  style-to-object@1.0.8:
    style-to-object: private
  styled-jsx@5.1.6(react@19.0.0):
    styled-jsx: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  tapable@2.2.1:
    tapable: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  throttleit@2.1.0:
    throttleit: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  to-regex-range@5.0.1:
    to-regex-range: private
  trim-lines@3.0.1:
    trim-lines: private
  trough@2.2.0:
    trough: private
  ts-api-utils@1.4.3(typescript@5.7.2):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  typed-array-buffer@1.0.2:
    typed-array-buffer: private
  typed-array-byte-length@1.0.1:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.3:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  unbox-primitive@1.0.2:
    unbox-primitive: private
  undici-types@6.19.8:
    undici-types: private
  undici@5.28.4:
    undici: private
  unified@11.0.5:
    unified: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-position@5.0.0:
    unist-util-position: private
  unist-util-remove-position@5.0.0:
    unist-util-remove-position: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  uri-js@4.4.1:
    uri-js: private
  use-callback-ref@1.3.2(@types/react@19.0.1)(react@19.0.0):
    use-callback-ref: private
  use-sidecar@1.1.3(@types/react@19.0.1)(react@19.0.0):
    use-sidecar: private
  use-sync-external-store@1.4.0(react@19.0.0):
    use-sync-external-store: private
  utf-8-validate@6.0.3:
    utf-8-validate: private
  util-deprecate@1.0.2:
    util-deprecate: private
  vfile-message@4.0.2:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  w3c-keyname@2.2.8:
    w3c-keyname: private
  which-boxed-primitive@1.1.0:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.16:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  ws@8.14.2(bufferutil@4.0.8)(utf-8-validate@6.0.3):
    ws: private
  xtend@4.0.2:
    xtend: private
  yaml@2.6.1:
    yaml: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zod-to-json-schema@3.24.1(zod@3.24.1):
    zod-to-json-schema: private
  zwitch@2.0.4:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.4.1
pendingBuilds: []
prunedAt: Thu, 24 Jul 2025 02:39:11 GMT
publicHoistPattern: []
registries:
  default: https://nexus.geon.kr/repository/npm-group/
skipped:
  - '@emnapi/runtime@1.4.3'
  - '@esbuild/aix-ppc64@0.19.12'
  - '@esbuild/aix-ppc64@0.23.1'
  - '@esbuild/android-arm64@0.18.20'
  - '@esbuild/android-arm64@0.19.12'
  - '@esbuild/android-arm64@0.23.1'
  - '@esbuild/android-arm@0.18.20'
  - '@esbuild/android-arm@0.19.12'
  - '@esbuild/android-arm@0.23.1'
  - '@esbuild/android-x64@0.18.20'
  - '@esbuild/android-x64@0.19.12'
  - '@esbuild/android-x64@0.23.1'
  - '@esbuild/darwin-arm64@0.18.20'
  - '@esbuild/darwin-arm64@0.19.12'
  - '@esbuild/darwin-arm64@0.23.1'
  - '@esbuild/darwin-x64@0.18.20'
  - '@esbuild/darwin-x64@0.19.12'
  - '@esbuild/darwin-x64@0.23.1'
  - '@esbuild/freebsd-arm64@0.18.20'
  - '@esbuild/freebsd-arm64@0.19.12'
  - '@esbuild/freebsd-arm64@0.23.1'
  - '@esbuild/freebsd-x64@0.18.20'
  - '@esbuild/freebsd-x64@0.19.12'
  - '@esbuild/freebsd-x64@0.23.1'
  - '@esbuild/linux-arm64@0.18.20'
  - '@esbuild/linux-arm64@0.19.12'
  - '@esbuild/linux-arm64@0.23.1'
  - '@esbuild/linux-arm@0.18.20'
  - '@esbuild/linux-arm@0.19.12'
  - '@esbuild/linux-arm@0.23.1'
  - '@esbuild/linux-ia32@0.18.20'
  - '@esbuild/linux-ia32@0.19.12'
  - '@esbuild/linux-ia32@0.23.1'
  - '@esbuild/linux-loong64@0.18.20'
  - '@esbuild/linux-loong64@0.19.12'
  - '@esbuild/linux-loong64@0.23.1'
  - '@esbuild/linux-mips64el@0.18.20'
  - '@esbuild/linux-mips64el@0.19.12'
  - '@esbuild/linux-mips64el@0.23.1'
  - '@esbuild/linux-ppc64@0.18.20'
  - '@esbuild/linux-ppc64@0.19.12'
  - '@esbuild/linux-ppc64@0.23.1'
  - '@esbuild/linux-riscv64@0.18.20'
  - '@esbuild/linux-riscv64@0.19.12'
  - '@esbuild/linux-riscv64@0.23.1'
  - '@esbuild/linux-s390x@0.18.20'
  - '@esbuild/linux-s390x@0.19.12'
  - '@esbuild/linux-s390x@0.23.1'
  - '@esbuild/linux-x64@0.18.20'
  - '@esbuild/linux-x64@0.19.12'
  - '@esbuild/linux-x64@0.23.1'
  - '@esbuild/netbsd-x64@0.18.20'
  - '@esbuild/netbsd-x64@0.19.12'
  - '@esbuild/netbsd-x64@0.23.1'
  - '@esbuild/openbsd-arm64@0.23.1'
  - '@esbuild/openbsd-x64@0.18.20'
  - '@esbuild/openbsd-x64@0.19.12'
  - '@esbuild/openbsd-x64@0.23.1'
  - '@esbuild/sunos-x64@0.18.20'
  - '@esbuild/sunos-x64@0.19.12'
  - '@esbuild/sunos-x64@0.23.1'
  - '@esbuild/win32-arm64@0.18.20'
  - '@esbuild/win32-arm64@0.19.12'
  - '@esbuild/win32-arm64@0.23.1'
  - '@esbuild/win32-ia32@0.18.20'
  - '@esbuild/win32-ia32@0.19.12'
  - '@esbuild/win32-ia32@0.23.1'
  - '@img/sharp-darwin-arm64@0.34.2'
  - '@img/sharp-darwin-x64@0.34.2'
  - '@img/sharp-libvips-darwin-arm64@1.1.0'
  - '@img/sharp-libvips-darwin-x64@1.1.0'
  - '@img/sharp-libvips-linux-arm64@1.1.0'
  - '@img/sharp-libvips-linux-arm@1.1.0'
  - '@img/sharp-libvips-linux-ppc64@1.1.0'
  - '@img/sharp-libvips-linux-s390x@1.1.0'
  - '@img/sharp-libvips-linux-x64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.1.0'
  - '@img/sharp-linux-arm64@0.34.2'
  - '@img/sharp-linux-arm@0.34.2'
  - '@img/sharp-linux-s390x@0.34.2'
  - '@img/sharp-linux-x64@0.34.2'
  - '@img/sharp-linuxmusl-arm64@0.34.2'
  - '@img/sharp-linuxmusl-x64@0.34.2'
  - '@img/sharp-wasm32@0.34.2'
  - '@img/sharp-win32-arm64@0.34.2'
  - '@img/sharp-win32-ia32@0.34.2'
  - '@next/swc-darwin-arm64@15.3.3'
  - '@next/swc-darwin-x64@15.3.3'
  - '@next/swc-linux-arm64-gnu@15.3.3'
  - '@next/swc-linux-arm64-musl@15.3.3'
  - '@next/swc-linux-x64-gnu@15.3.3'
  - '@next/swc-linux-x64-musl@15.3.3'
  - '@next/swc-win32-arm64-msvc@15.3.3'
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\chatbot\front-chat\node_modules\.pnpm
virtualStoreDirMaxLength: 60
