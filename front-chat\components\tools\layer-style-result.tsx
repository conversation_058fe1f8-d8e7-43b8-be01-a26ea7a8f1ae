"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON>, In<PERSON>, Eye, Settings, Hash, Layers3 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { cn } from "@/lib/utils";
import { componentStyles } from "@/lib/design-tokens";
import { CompactResultTrigger } from "./common/compact-result-trigger";
import { getToolDisplayInfo } from "../annotations";

interface LayerStyleResponse {
  layerId: string;
  styleUpdate: any;
  description: string;
  success: boolean;
}

interface LayerStyleResultProps {
  content: LayerStyleResponse | string;
  className?: string;
}

const getStyleTypeColor = (styleKey: string) => {
  const colors = {
    'color': 'bg-blue-100/80 text-blue-700 border-blue-200/60',
    'fillOpacity': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',
    'strokeColor': 'bg-purple-100/80 text-purple-700 border-purple-200/60',
    'strokeWidth': 'bg-orange-100/80 text-orange-700 border-orange-200/60',
    'radius': 'bg-pink-100/80 text-pink-700 border-pink-200/60',
    'symbol': 'bg-yellow-100/80 text-yellow-700 border-yellow-200/60',
    'default': 'bg-neutral-100/80 text-neutral-700 border-neutral-200/60'
  };
  return colors[styleKey as keyof typeof colors] || colors.default;
};

export function LayerStyleResult({ content, className }: LayerStyleResultProps) {
  let result: LayerStyleResponse;

  try {
    result = typeof content === 'string' ? JSON.parse(content) : content;
  } catch (e) {
    result = { 
      layerId: '', 
      styleUpdate: {}, 
      description: 'Invalid content format', 
      success: false 
    };
  }

  if (!result.success) {
    return (
      <div className={cn(componentStyles.card.error, "p-3", className)}>
        <div className="flex items-center gap-2">
          <div className={cn(componentStyles.iconContainer.sm, "bg-red-100/80 text-red-600 border border-red-200/60")}>
            <Palette className="h-3 w-3" />
          </div>
          <div>
            <p className="font-medium text-red-900 text-sm">스타일 변경 실패</p>
            <p className="text-xs text-red-700">
              {result.description || '스타일 변경 중 오류가 발생했습니다.'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // 스타일 속성을 사용자 친화적으로 표시
  const getStyleDescription = (styleUpdate: any) => {
    const descriptions = [];

    if (styleUpdate.color) {
      descriptions.push(`색상: ${styleUpdate.color}`);
    }
    if (styleUpdate.fillOpacity !== undefined) {
      descriptions.push(`투명도: ${(styleUpdate.fillOpacity * 100).toFixed(0)}%`);
    }
    if (styleUpdate.strokeColor) {
      descriptions.push(`윤곽선: ${styleUpdate.strokeColor}`);
    }
    if (styleUpdate.strokeWidth !== undefined) {
      descriptions.push(`윤곽선 두께: ${styleUpdate.strokeWidth}px`);
    }
    if (styleUpdate.radius !== undefined) {
      descriptions.push(`크기: ${styleUpdate.radius}px`);
    }
    if (styleUpdate.width !== undefined) {
      descriptions.push(`선 두께: ${styleUpdate.width}px`);
    }
    if (styleUpdate.symbol) {
      const symbolNames: Record<string, string> = {
        circle: '원형',
        square: '사각형',
        triangle: '삼각형',
        star: '별',
        cross: '십자',
        x: 'X자'
      };
      descriptions.push(`심볼: ${symbolNames[styleUpdate.symbol] || styleUpdate.symbol}`);
    }

    return descriptions.length > 0 ? descriptions.join(', ') : '스타일 속성 변경됨';
  };

  const styleUpdateEntries = Object.entries(result.styleUpdate);
  const primaryStyleKey = styleUpdateEntries.length > 0 ? styleUpdateEntries[0][0] : 'default';

  const toolInfo = getToolDisplayInfo("updateLayerStyle");

  return (
    <CompactResultTrigger
      icon={toolInfo.icon}
      title={toolInfo.label}
      state="result"
      className={className}
      titleExtra={
        styleUpdateEntries.length > 0 && (
          <Badge variant="outline" className={cn("text-xs border", getStyleTypeColor(primaryStyleKey))}>
            {primaryStyleKey}
          </Badge>
        )
      }
    >
      {/* 스타일 변경 정보 */}
      <div className="space-y-2">
        <div className="text-xs text-neutral-600">
          ID: {result.layerId}
        </div>

        <div className="text-xs text-neutral-600">
          {result.description}
        </div>

        {/* 변경된 스타일 속성 */}
        {styleUpdateEntries.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-1">
              <Sparkles className="h-3 w-3 text-neutral-500" />
              <span className="font-medium text-neutral-700 text-xs">변경된 속성</span>
            </div>
            <div className="text-xs text-neutral-600">
              {getStyleDescription(result.styleUpdate)}
            </div>

            {/* 스타일 속성 상세 정보 */}
            <div className="pt-2 border-t border-neutral-200">
              <h5 className="text-xs font-medium text-neutral-700 mb-2">스타일 속성 상세</h5>
              <div className="space-y-1 text-xs">
                {styleUpdateEntries.map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-neutral-500">{key}</span>
                    <span className="text-neutral-700 font-mono">{String(value)}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </CompactResultTrigger>
  );
}