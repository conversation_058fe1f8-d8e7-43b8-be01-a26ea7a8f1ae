# DrawControl
그리기 도구 클래스

**Kind**: global class  
**Summary**: 지도에 그릴수 있는 다양한 그리기 도구를 제공

```javascript
// DrawControl 클래스 생성자 옵션
let drawControl = new odf.DrawControl({
        //style : {} //그리기 피쳐 odf 스타일 옵션,
        //bufferStyle : {} //버퍼 도형 odf 스타일 옵션,
        //createNewLayer : true //drawControl 생성 시 새로운 레이어 생성 여부
        //message : { //그리기 도구 시작시 툴팁 메세지
          //DRAWSATRT_POINT : '점 그리기 측정입니다.',
          //DRAWSTART_LINESTRING : '',
          //DRAWSTART_POLYGON : '',
          //DRAWSTART_CURVE : '' ,
          //DRAWSTART_TEXT : '',
          //DRAWSTART_BUFFER : '',
          //DRAWSTART_CIRCLE : '',
          //DRAWSTART_BOX : '',
          //DRAWEND_DRAG : '',
          //DRAWEND_DBCLICK : '',
        //},
        //measure : false, 측정 옵션 활성화 여부, (선, 원형만 측정 가능)
        //continuity : false, 연속 측정 여부  (기본값 false),
        //rightClickDelete : false, 우클릭 삭제 기능 활성화 여부,
        //tools : ['text', 'polygon','lineString','point','circle','curve','box','square','buffer'],
});
```  

* [DrawControl](#DrawControl)
    * [new DrawControl(options)](#new_DrawControl_new)
    * [.getConstructorOptions()](#DrawControl+getConstructorOptions) ⇒ <code>Array.&lt;Object&gt;</code>
    * [.setMap(_map, createElementFlag)](#DrawControl+setMap)
    * [.removeMap()](#DrawControl+removeMap)
    * [.draw(type)](#DrawControl+draw)
    * [.drawText()](#DrawControl+drawText)
    * [.drawPolygon()](#DrawControl+drawPolygon)
    * [.drawLineString()](#DrawControl+drawLineString)
    * [.drawPoint()](#DrawControl+drawPoint)
    * [.drawCurve()](#DrawControl+drawCurve)
    * [.drawBox()](#DrawControl+drawBox)
    * [.drawSquare()](#DrawControl+drawSquare)
    * [.drawCircle()](#DrawControl+drawCircle)
    * [.findDrawVectorLayer()](#DrawControl+findDrawVectorLayer) ⇒ <code>Layer</code>
    * [.clear()](#DrawControl+clear)
    * [.removeToolTip()](#DrawControl+removeToolTip)
    * [.setStyle(styleObject)](#DrawControl+setStyle)
    * [.getActiveType()](#DrawControl+getActiveType) ⇒ <code>String</code>

<a name="new_DrawControl_new"></a>

#### new DrawControl(options)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | DrawControl 객체생성 옵션 |
| options.style | <code>odf\_styleOption</code> | 그리기 도형 style 설정 옵션 |
| options.bufferStyle | <code>odf\_styleOption</code> | 버퍼 그리기 도형 style 설정 옵션 |
| options.createNewLayer | <code>boolean</code> | drawControl 생성 시 마다 새 레이어 생성 여부 |
| options.message | <code>Object</code> | 툴팁 메세지 |
| options.message.DRAWSTART_POINT | <code>String</code> | 점 그리기 시작 안내 메세지 (기본값) '점을 그리기 위해 지도를 클릭해주세요' |
| options.message.DRAWSTART_LINESTRING | <code>String</code> | 선 그리기 시작 안내 메세지 (기본값) '선을 그리기 위해 지도를 클릭해주세요' |
| options.message.DRAWSTART_POLYGON | <code>String</code> | 면 그리기 시작 안내 메세지 (기본값) '면을 그리기 위해 지도를 클릭해주세요' |
| options.message.DRAWSTART_CURVE | <code>String</code> | 곡선 그리기 시작 안내 메세지 (기본값) '곡선을 그리기 위해 지도를 드래그해주세요' |
| options.message.DRAWSTART_TEXT | <code>String</code> | 텍스트 그리기 시작 안내 메세지 (기본값) '텍스트를 입력하기 위해 지도를 클릭해주세요.' |
| options.message.DRAWSTART_BUFFER | <code>String</code> | 버퍼 그리기 시작 안내 메세지 (기본값) '버퍼를 생성하기 위해 레이어를 선택해주세요.' |
| options.message.DRAWSTART_CIRCLE | <code>String</code> | 원 그리기 시작 안내 메세지 (기본값) '원을 그리기 위해 지도를 클릭해주세요.' |
| options.message.DRAWSTART_BOX | <code>String</code> | 사각형 그리기 시작 안내 메세지 (기본값) '사각형을 그리기 위해 지도를 클릭해주세요.' |
| options.message.DRAWEND_DRAG | <code>String</code> | 그리기 종료 안내 메세지(드래그) (기본값) '드래그를 멈추면 그리기가 종료됩니다.' |
| options.message.DRAWEND_DBCLICK | <code>String</code> | 그리기 종료 안내 메세지(더블클릭) (기본값) '더블클릭시 그리기가 종료됩니다.' |
| options.measure | <code>Boolean</code> | 측정 옵션 활성화 여부(기본값 false) |
| options.continuity | <code>Boolean</code> | 연속 그리기 여부 (기본값 false) |
| options.rightClickDelete | <code>Boolean</code> | 우클릭 삭제 기능 활성화 여부 (기본값 false) |
| options.tools | <code>Array.&lt;String&gt;</code> | 생성할 툴 배열 |
| options.tools.text | <code>String</code> | 텍스트 그리기 툴 |
| options.tools.polygon | <code>String</code> | 다각형 그리기 툴 |
| options.tools.lineString | <code>String</code> | 선 그리기 툴 |
| options.tools.box | <code>String</code> | 사각형 그리기 툴 |
| options.tools.square | <code>String</code> | 정사각형 그리기 툴 |
| options.tools.point | <code>String</code> | 점 그리기 툴 |
| options.tools.circle | <code>String</code> | 원 그리기 툴 |
| options.tools.curve | <code>String</code> | 곡선 그리기 툴 |
| options.tools.buffer | <code>String</code> | 버퍼 그리기 툴 |

<a name="DrawControl+getConstructorOptions"></a>

#### drawControl.getConstructorOptions() ⇒ <code>Array.&lt;Object&gt;</code>

```javascript
// 컨트롤 생성 옵션 반환
 let drawControl = new odf.DrawControl();
 drawControl.getConstructorOptions();
```

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  
**Returns**: <code>Array.&lt;Object&gt;</code> - 컨트롤 생성 옵션 반환  
<a name="DrawControl+setMap"></a>

#### drawControl.setMap(_map, createElementFlag)

```javascript
// DrawControl map 객체 연결, 그리기 도구 활성화
 let drawControl = new odf.DrawControl();
 drawControl.setMap(map, createElementFlag); //DrawControl 객체에 map 객체 연결
 ```

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)

| Param | Type | Description |
| --- | --- | --- |
| _map | <code>Map</code> | DrawControl 객체와 연결할 Map 객체 |
| createElementFlag | <code>Boolean</code> | DrawControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x |

<a name="DrawControl+removeMap"></a>

#### drawControl.removeMap()
```javascript
// 그리기 도구 제거, 비활성화
drawControl.removeMap();
```
**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  
<a name="DrawControl+draw"></a>

### drawControl.draw(type)
```javascript
// DrawControl 그리기 예시
drawControl.draw('text');
```
**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  

| Param | Type | Description |
| --- | --- | --- |
| type | <code>String</code> | 그리기 유형  - 'text' : 텍스트  - 'polygon' : 다각형  - 'lineString' : 선  - 'box' : 사각형  - 'square' : 정사각형  - 'point' : 점  - 'circle' : 원  - 'curve' : 곡선  - 'buffer' : 버퍼 |

<a name="DrawControl+drawText"></a>

### drawControl.drawText()
```javascript
// DrawControl 텍스트 그리기
drawControl.drawText();
```

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  
<a name="DrawControl+drawPolygon"></a>

#### drawControl.drawPolygon()

```javascript
/// DrawControl 도형 그리기
drawControl.drawPolygon();
```
**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  
<a name="DrawControl+drawLineString"></a>

### drawControl.drawLineString()
        
```javascript
/// DrawControl 선 그리기
drawControl.drawLineString();
```
**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  
<a name="DrawControl+drawPoint"></a>

### drawControl.drawPoint()
```javascript
// DrawControl 점 그리기
drawControl.drawPoint();
```
**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  
<a name="DrawControl+drawCurve"></a>

### drawControl.drawCurve()
```javascript
// DrawControl 곡선 그리기
drawControl.drawCurve();
```
**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  
<a name="DrawControl+drawBox"></a>

### drawControl.drawBox()
```javascript
// DrawControl 사각형 그리기
drawControl.drawBox();
```
**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  
<a name="DrawControl+drawSquare"></a>

### drawControl.drawSquare()
```javascript
// DrawControl 정사각형 그리기
drawControl.drawSquare();
```
**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  
<a name="DrawControl+drawCircle"></a>

### drawControl.drawCircle()
```javascript
// DrawControl 원 그리기
drawControl.drawCircle();
```
**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  
<a name="DrawControl+findDrawVectorLayer"></a>

### drawControl.findDrawVectorLayer() ⇒ <code>Layer</code>
```javascript
// 그리기 도구로 그린 피쳐가 있는 벡터레이어가 있는지 확인,없을 경우 벡터레이어 생성
drawControl.findDrawVectorLayer();
```

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  
**Returns**: <code>Layer</code> - 그리기 레이어 반환  
<a name="DrawControl+clear"></a>

#### drawControl.clear()
```javascript
// 그리기 인터렉션 삭제 및 그리기 오버레이 삭제, 그리던 도형 삭제
drawControl.clear();
```

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  
<a name="DrawControl+removeToolTip"></a>

#### drawControl.removeToolTip()
```javascript
// DrawControl 툴팁 삭제 함수
drawControl.removeHelpTooltip();
```
**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  
<a name="DrawControl+setStyle"></a>

#### drawControl.setStyle(styleObject)

```javascript
// DrawControl 스타일 변경 함수
drawControl.setStyle({
             fill : {
          color : [1,10,100,0.4]
      },
      stroke : {
          color : [1,10,100,0.4],
          width : 3,
      },
      point : { // 점 스타일
         icon : {
              width : 20, // scale 옵션과 함께 사용할 수 없습니다.
              height: 20, // scale 옵션과 함께 사용할 수 없습니다.
              scale: 1,
              color: 'red',
              opacity: 1,
              img: HTMLImageElement // src 옵션과 함께 사용할 수 없습니다.
              src: 'images/icon.png' // img 옵션과 함께 사용할 수 없습니다.
         }
      },
      text : {
          text: '',
          fill: { color: [1, 10, 100, 1] },
          stroke: { color: [1, 10, 100, 1] },
          font: '20px sans-serif',
      },
     bufferStyle : {
          stroke: {
              color: [67, 116, 217, 1],
              width: 2,
            },
            fill: { color: [178, 204, 255, 0.3] },
      },

})
```
**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  

| Param | Type | Description |
| --- | --- | --- |
| styleObject | <code>Object</code> | 스타일을 생성할 Object |

<a name="DrawControl+getActiveType"></a>

#### drawControl.getActiveType() ⇒ <code>String</code>

```javascript
// 어떤 그리기가 활성화 상태인지 조회
  let drawControl = new odf.DrawControl();
  drawControl.getActiveType();//어떤 그리기가 활성화 상태인지
```

**Kind**: instance method of [<code>DrawControl</code>](#DrawControl)  
**Returns**: <code>String</code> - 어떤 그리기가 활성화 상태인지
- 'none' : 그리기 활성화상태 x
- 'text' : 텍스트 그리기 활성화 상태
- 'polygon' : 다각형 그리기 활성화 상태
- 'lineString' : 선 그리기 활성화 상태
- 'point' : 점 그리기 활성화 상태
- 'circle' : 원 그리기 활성화 상태
- 'box' : 사각형 그리기 활성화 상태
- 'square' : 정사각형 그리기 활성화 상태
- 'curve' : 곡선 그리기 활성화 상태
- 'buffer' : 버퍼 그리기 활성화 상태  
