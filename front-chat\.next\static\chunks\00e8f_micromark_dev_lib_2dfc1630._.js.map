{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/micromark%404.0.1/node_modules/micromark/dev/lib/initialize/content.js"], "sourcesContent": ["/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {InitialConstruct} */\nexport const content = {tokenize: initializeContent}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Initializer}\n *   Content.\n */\nfunction initializeContent(effects) {\n  const contentStart = effects.attempt(\n    this.parser.constructs.contentInitial,\n    afterContentStartConstruct,\n    paragraphInitial\n  )\n  /** @type {Token} */\n  let previous\n\n  return contentStart\n\n  /** @type {State} */\n  function afterContentStartConstruct(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, contentStart, types.linePrefix)\n  }\n\n  /** @type {State} */\n  function paragraphInitial(code) {\n    assert(\n      code !== codes.eof && !markdownLineEnding(code),\n      'expected anything other than a line ending or EOF'\n    )\n    effects.enter(types.paragraph)\n    return lineStart(code)\n  }\n\n  /** @type {State} */\n  function lineStart(code) {\n    const token = effects.enter(types.chunkText, {\n      contentType: constants.contentTypeText,\n      previous\n    })\n\n    if (previous) {\n      previous.next = token\n    }\n\n    previous = token\n\n    return data(code)\n  }\n\n  /** @type {State} */\n  function data(code) {\n    if (code === codes.eof) {\n      effects.exit(types.chunkText)\n      effects.exit(types.paragraph)\n      effects.consume(code)\n      return\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.consume(code)\n      effects.exit(types.chunkText)\n      return lineStart\n    }\n\n    // Data.\n    effects.consume(code)\n    return data\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;AAED;AACA;AACA;AACA;AAAA;AAAA;;;;;AAGO,MAAM,UAAU;IAAC,UAAU;AAAiB;AAEnD;;;;;CAKC,GACD,SAAS,kBAAkB,OAAO;IAChC,MAAM,eAAe,QAAQ,OAAO,CAClC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,cAAc,EACrC,4BACA;IAEF,kBAAkB,GAClB,IAAI;IAEJ,OAAO;;IAEP,kBAAkB,GAClB,SAAS,2BAA2B,IAAI;QACtC,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,SAAS,sOAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,4OAAA,CAAA,qBAAkB,AAAD,EAAE,OACzC;QAGF,IAAI,SAAS,sOAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,QAAQ,OAAO,CAAC;YAChB;QACF;QAEA,QAAQ,KAAK,CAAC,sOAAA,CAAA,QAAK,CAAC,UAAU;QAC9B,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,UAAU;QAC7B,OAAO,CAAA,GAAA,0OAAA,CAAA,eAAY,AAAD,EAAE,SAAS,cAAc,sOAAA,CAAA,QAAK,CAAC,UAAU;IAC7D;IAEA,kBAAkB,GAClB,SAAS,iBAAiB,IAAI;QAC5B,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,SAAS,sOAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAC,CAAA,GAAA,4OAAA,CAAA,qBAAkB,AAAD,EAAE,OAC1C;QAEF,QAAQ,KAAK,CAAC,sOAAA,CAAA,QAAK,CAAC,SAAS;QAC7B,OAAO,UAAU;IACnB;IAEA,kBAAkB,GAClB,SAAS,UAAU,IAAI;QACrB,MAAM,QAAQ,QAAQ,KAAK,CAAC,sOAAA,CAAA,QAAK,CAAC,SAAS,EAAE;YAC3C,aAAa,0OAAA,CAAA,YAAS,CAAC,eAAe;YACtC;QACF;QAEA,IAAI,UAAU;YACZ,SAAS,IAAI,GAAG;QAClB;QAEA,WAAW;QAEX,OAAO,KAAK;IACd;IAEA,kBAAkB,GAClB,SAAS,KAAK,IAAI;QAChB,IAAI,SAAS,sOAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,QAAQ,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,SAAS;YAC5B,QAAQ,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,SAAS;YAC5B,QAAQ,OAAO,CAAC;YAChB;QACF;QAEA,IAAI,CAAA,GAAA,4OAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,SAAS;YAC5B,OAAO;QACT;QAEA,QAAQ;QACR,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/micromark%404.0.1/node_modules/micromark/dev/lib/initialize/document.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   ContainerState,\n *   InitialConstruct,\n *   Initializer,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @typedef {[Construct, ContainerState]} StackItem\n *   Construct and its state.\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {splice} from 'micromark-util-chunked'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {InitialConstruct} */\nexport const document = {tokenize: initializeDocument}\n\n/** @type {Construct} */\nconst containerConstruct = {tokenize: tokenizeContainer}\n\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */\nfunction initializeDocument(effects) {\n  const self = this\n  /** @type {Array<StackItem>} */\n  const stack = []\n  let continued = 0\n  /** @type {TokenizeContext | undefined} */\n  let childFlow\n  /** @type {Token | undefined} */\n  let childToken\n  /** @type {number} */\n  let lineStartOffset\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    // First we iterate through the open blocks, starting with the root\n    // document, and descending through last children down to the last open\n    // block.\n    // Each block imposes a condition that the line must satisfy if the block is\n    // to remain open.\n    // For example, a block quote requires a `>` character.\n    // A paragraph requires a non-blank line.\n    // In this phase we may match all or just some of the open blocks.\n    // But we cannot close unmatched blocks yet, because we may have a lazy\n    // continuation line.\n    if (continued < stack.length) {\n      const item = stack[continued]\n      self.containerState = item[1]\n      assert(\n        item[0].continuation,\n        'expected `continuation` to be defined on container construct'\n      )\n      return effects.attempt(\n        item[0].continuation,\n        documentContinue,\n        checkNewContainers\n      )(code)\n    }\n\n    // Done.\n    return checkNewContainers(code)\n  }\n\n  /** @type {State} */\n  function documentContinue(code) {\n    assert(\n      self.containerState,\n      'expected `containerState` to be defined after continuation'\n    )\n\n    continued++\n\n    // Note: this field is called `_closeFlow` but it also closes containers.\n    // Perhaps a good idea to rename it but it’s already used in the wild by\n    // extensions.\n    if (self.containerState._closeFlow) {\n      self.containerState._closeFlow = undefined\n\n      if (childFlow) {\n        closeFlow()\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when dealing with lazy lines in `writeToChild`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the flow chunk.\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === types.chunkFlow\n        ) {\n          point = self.events[indexBeforeFlow][1].end\n          break\n        }\n      }\n\n      assert(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      let index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = {...point}\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      splice(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n\n      return checkNewContainers(code)\n    }\n\n    return start(code)\n  }\n\n  /** @type {State} */\n  function checkNewContainers(code) {\n    // Next, after consuming the continuation markers for existing blocks, we\n    // look for new block starts (e.g. `>` for a block quote).\n    // If we encounter a new block start, we close any blocks unmatched in\n    // step 1 before creating the new block as a child of the last matched\n    // block.\n    if (continued === stack.length) {\n      // No need to `check` whether there’s a container, of `exitContainers`\n      // would be moot.\n      // We can instead immediately `attempt` to parse one.\n      if (!childFlow) {\n        return documentContinued(code)\n      }\n\n      // If we have concrete content, such as block HTML or fenced code,\n      // we can’t have containers “pierce” into them, so we can immediately\n      // start.\n      if (childFlow.currentConstruct && childFlow.currentConstruct.concrete) {\n        return flowStart(code)\n      }\n\n      // If we do have flow, it could still be a blank line,\n      // but we’d be interrupting it w/ a new container if there’s a current\n      // construct.\n      // To do: next major: remove `_gfmTableDynamicInterruptHack` (no longer\n      // needed in micromark-extension-gfm-table@1.0.6).\n      self.interrupt = Boolean(\n        childFlow.currentConstruct && !childFlow._gfmTableDynamicInterruptHack\n      )\n    }\n\n    // Check if there is a new container.\n    self.containerState = {}\n    return effects.check(\n      containerConstruct,\n      thereIsANewContainer,\n      thereIsNoNewContainer\n    )(code)\n  }\n\n  /** @type {State} */\n  function thereIsANewContainer(code) {\n    if (childFlow) closeFlow()\n    exitContainers(continued)\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function thereIsNoNewContainer(code) {\n    self.parser.lazy[self.now().line] = continued !== stack.length\n    lineStartOffset = self.now().offset\n    return flowStart(code)\n  }\n\n  /** @type {State} */\n  function documentContinued(code) {\n    // Try new containers.\n    self.containerState = {}\n    return effects.attempt(\n      containerConstruct,\n      containerContinue,\n      flowStart\n    )(code)\n  }\n\n  /** @type {State} */\n  function containerContinue(code) {\n    assert(\n      self.currentConstruct,\n      'expected `currentConstruct` to be defined on tokenizer'\n    )\n    assert(\n      self.containerState,\n      'expected `containerState` to be defined on tokenizer'\n    )\n    continued++\n    stack.push([self.currentConstruct, self.containerState])\n    // Try another.\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function flowStart(code) {\n    if (code === codes.eof) {\n      if (childFlow) closeFlow()\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    childFlow = childFlow || self.parser.flow(self.now())\n    effects.enter(types.chunkFlow, {\n      _tokenizer: childFlow,\n      contentType: constants.contentTypeFlow,\n      previous: childToken\n    })\n\n    return flowContinue(code)\n  }\n\n  /** @type {State} */\n  function flowContinue(code) {\n    if (code === codes.eof) {\n      writeToChild(effects.exit(types.chunkFlow), true)\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.consume(code)\n      writeToChild(effects.exit(types.chunkFlow))\n      // Get ready for the next line.\n      continued = 0\n      self.interrupt = undefined\n      return start\n    }\n\n    effects.consume(code)\n    return flowContinue\n  }\n\n  /**\n   * @param {Token} token\n   *   Token.\n   * @param {boolean | undefined} [endOfFile]\n   *   Whether the token is at the end of the file (default: `false`).\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function writeToChild(token, endOfFile) {\n    assert(childFlow, 'expected `childFlow` to be defined when continuing')\n    const stream = self.sliceStream(token)\n    if (endOfFile) stream.push(null)\n    token.previous = childToken\n    if (childToken) childToken.next = token\n    childToken = token\n    childFlow.defineSkip(token.start)\n    childFlow.write(stream)\n\n    // Alright, so we just added a lazy line:\n    //\n    // ```markdown\n    // > a\n    // b.\n    //\n    // Or:\n    //\n    // > ~~~c\n    // d\n    //\n    // Or:\n    //\n    // > | e |\n    // f\n    // ```\n    //\n    // The construct in the second example (fenced code) does not accept lazy\n    // lines, so it marked itself as done at the end of its first line, and\n    // then the content construct parses `d`.\n    // Most constructs in markdown match on the first line: if the first line\n    // forms a construct, a non-lazy line can’t “unmake” it.\n    //\n    // The construct in the third example is potentially a GFM table, and\n    // those are *weird*.\n    // It *could* be a table, from the first line, if the following line\n    // matches a condition.\n    // In this case, that second line is lazy, which “unmakes” the first line\n    // and turns the whole into one content block.\n    //\n    // We’ve now parsed the non-lazy and the lazy line, and can figure out\n    // whether the lazy line started a new flow block.\n    // If it did, we exit the current containers between the two flow blocks.\n    if (self.parser.lazy[token.start.line]) {\n      let index = childFlow.events.length\n\n      while (index--) {\n        if (\n          // The token starts before the line ending…\n          childFlow.events[index][1].start.offset < lineStartOffset &&\n          // …and either is not ended yet…\n          (!childFlow.events[index][1].end ||\n            // …or ends after it.\n            childFlow.events[index][1].end.offset > lineStartOffset)\n        ) {\n          // Exit: there’s still something open, which means it’s a lazy line\n          // part of something.\n          return\n        }\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when closing flow in `documentContinue`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {boolean | undefined} */\n      let seen\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the previous chunk (the one before the lazy line).\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === types.chunkFlow\n        ) {\n          if (seen) {\n            point = self.events[indexBeforeFlow][1].end\n            break\n          }\n\n          seen = true\n        }\n      }\n\n      assert(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = {...point}\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      splice(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n    }\n  }\n\n  /**\n   * @param {number} size\n   *   Size.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function exitContainers(size) {\n    let index = stack.length\n\n    // Exit open containers.\n    while (index-- > size) {\n      const entry = stack[index]\n      self.containerState = entry[1]\n      assert(\n        entry[0].exit,\n        'expected `exit` to be defined on container construct'\n      )\n      entry[0].exit.call(self, effects)\n    }\n\n    stack.length = size\n  }\n\n  function closeFlow() {\n    assert(\n      self.containerState,\n      'expected `containerState` to be defined when closing flow'\n    )\n    assert(childFlow, 'expected `childFlow` to be defined when closing it')\n    childFlow.write([codes.eof])\n    childToken = undefined\n    childFlow = undefined\n    self.containerState._closeFlow = undefined\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n *   Tokenizer.\n */\nfunction tokenizeContainer(effects, ok, nok) {\n  // Always populated by defaults.\n  assert(\n    this.parser.constructs.disable.null,\n    'expected `disable.null` to be populated'\n  )\n  return factorySpace(\n    effects,\n    effects.attempt(this.parser.constructs.document, ok, nok),\n    types.linePrefix,\n    this.parser.constructs.disable.null.includes('codeIndented')\n      ? undefined\n      : constants.tabSize\n  )\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;CAYC,GAED;;;CAGC;;;AAED;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;;;AAGO,MAAM,WAAW;IAAC,UAAU;AAAkB;AAErD,sBAAsB,GACtB,MAAM,qBAAqB;IAAC,UAAU;AAAiB;AAEvD;;;;;CAKC,GACD,SAAS,mBAAmB,OAAO;IACjC,MAAM,OAAO,IAAI;IACjB,6BAA6B,GAC7B,MAAM,QAAQ,EAAE;IAChB,IAAI,YAAY;IAChB,wCAAwC,GACxC,IAAI;IACJ,8BAA8B,GAC9B,IAAI;IACJ,mBAAmB,GACnB,IAAI;IAEJ,OAAO;;IAEP,kBAAkB,GAClB,SAAS,MAAM,IAAI;QACjB,mEAAmE;QACnE,uEAAuE;QACvE,SAAS;QACT,4EAA4E;QAC5E,kBAAkB;QAClB,uDAAuD;QACvD,yCAAyC;QACzC,kEAAkE;QAClE,uEAAuE;QACvE,qBAAqB;QACrB,IAAI,YAAY,MAAM,MAAM,EAAE;YAC5B,MAAM,OAAO,KAAK,CAAC,UAAU;YAC7B,KAAK,cAAc,GAAG,IAAI,CAAC,EAAE;YAC7B,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,IAAI,CAAC,EAAE,CAAC,YAAY,EACpB;YAEF,OAAO,QAAQ,OAAO,CACpB,IAAI,CAAC,EAAE,CAAC,YAAY,EACpB,kBACA,oBACA;QACJ;QAEA,QAAQ;QACR,OAAO,mBAAmB;IAC5B;IAEA,kBAAkB,GAClB,SAAS,iBAAiB,IAAI;QAC5B,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,KAAK,cAAc,EACnB;QAGF;QAEA,yEAAyE;QACzE,wEAAwE;QACxE,cAAc;QACd,IAAI,KAAK,cAAc,CAAC,UAAU,EAAE;YAClC,KAAK,cAAc,CAAC,UAAU,GAAG;YAEjC,IAAI,WAAW;gBACb;YACF;YAEA,kEAAkE;YAClE,4DAA4D;YAC5D,MAAM,mBAAmB,KAAK,MAAM,CAAC,MAAM;YAC3C,IAAI,kBAAkB;YACtB,8BAA8B,GAC9B,IAAI;YAEJ,uBAAuB;YACvB,MAAO,kBAAmB;gBACxB,IACE,KAAK,MAAM,CAAC,gBAAgB,CAAC,EAAE,KAAK,UACpC,KAAK,MAAM,CAAC,gBAAgB,CAAC,EAAE,CAAC,IAAI,KAAK,sOAAA,CAAA,QAAK,CAAC,SAAS,EACxD;oBACA,QAAQ,KAAK,MAAM,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG;oBAC3C;gBACF;YACF;YAEA,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,OAAO;YAEd,eAAe;YAEf,iBAAiB;YACjB,IAAI,QAAQ;YAEZ,MAAO,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAE;gBACjC,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,GAAG;oBAAC,GAAG,KAAK;gBAAA;gBACrC;YACF;YAEA,4DAA4D;YAC5D,CAAA,GAAA,wOAAA,CAAA,SAAM,AAAD,EACH,KAAK,MAAM,EACX,kBAAkB,GAClB,GACA,KAAK,MAAM,CAAC,KAAK,CAAC;YAGpB,+BAA+B;YAC/B,KAAK,MAAM,CAAC,MAAM,GAAG;YAErB,OAAO,mBAAmB;QAC5B;QAEA,OAAO,MAAM;IACf;IAEA,kBAAkB,GAClB,SAAS,mBAAmB,IAAI;QAC9B,yEAAyE;QACzE,0DAA0D;QAC1D,sEAAsE;QACtE,sEAAsE;QACtE,SAAS;QACT,IAAI,cAAc,MAAM,MAAM,EAAE;YAC9B,sEAAsE;YACtE,iBAAiB;YACjB,qDAAqD;YACrD,IAAI,CAAC,WAAW;gBACd,OAAO,kBAAkB;YAC3B;YAEA,kEAAkE;YAClE,qEAAqE;YACrE,SAAS;YACT,IAAI,UAAU,gBAAgB,IAAI,UAAU,gBAAgB,CAAC,QAAQ,EAAE;gBACrE,OAAO,UAAU;YACnB;YAEA,sDAAsD;YACtD,sEAAsE;YACtE,aAAa;YACb,uEAAuE;YACvE,kDAAkD;YAClD,KAAK,SAAS,GAAG,QACf,UAAU,gBAAgB,IAAI,CAAC,UAAU,6BAA6B;QAE1E;QAEA,qCAAqC;QACrC,KAAK,cAAc,GAAG,CAAC;QACvB,OAAO,QAAQ,KAAK,CAClB,oBACA,sBACA,uBACA;IACJ;IAEA,kBAAkB,GAClB,SAAS,qBAAqB,IAAI;QAChC,IAAI,WAAW;QACf,eAAe;QACf,OAAO,kBAAkB;IAC3B;IAEA,kBAAkB,GAClB,SAAS,sBAAsB,IAAI;QACjC,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,cAAc,MAAM,MAAM;QAC9D,kBAAkB,KAAK,GAAG,GAAG,MAAM;QACnC,OAAO,UAAU;IACnB;IAEA,kBAAkB,GAClB,SAAS,kBAAkB,IAAI;QAC7B,sBAAsB;QACtB,KAAK,cAAc,GAAG,CAAC;QACvB,OAAO,QAAQ,OAAO,CACpB,oBACA,mBACA,WACA;IACJ;IAEA,kBAAkB,GAClB,SAAS,kBAAkB,IAAI;QAC7B,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,KAAK,gBAAgB,EACrB;QAEF,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,KAAK,cAAc,EACnB;QAEF;QACA,MAAM,IAAI,CAAC;YAAC,KAAK,gBAAgB;YAAE,KAAK,cAAc;SAAC;QACvD,eAAe;QACf,OAAO,kBAAkB;IAC3B;IAEA,kBAAkB,GAClB,SAAS,UAAU,IAAI;QACrB,IAAI,SAAS,sOAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,IAAI,WAAW;YACf,eAAe;YACf,QAAQ,OAAO,CAAC;YAChB;QACF;QAEA,YAAY,aAAa,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG;QAClD,QAAQ,KAAK,CAAC,sOAAA,CAAA,QAAK,CAAC,SAAS,EAAE;YAC7B,YAAY;YACZ,aAAa,0OAAA,CAAA,YAAS,CAAC,eAAe;YACtC,UAAU;QACZ;QAEA,OAAO,aAAa;IACtB;IAEA,kBAAkB,GAClB,SAAS,aAAa,IAAI;QACxB,IAAI,SAAS,sOAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,aAAa,QAAQ,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,SAAS,GAAG;YAC5C,eAAe;YACf,QAAQ,OAAO,CAAC;YAChB;QACF;QAEA,IAAI,CAAA,GAAA,4OAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,QAAQ,OAAO,CAAC;YAChB,aAAa,QAAQ,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,SAAS;YACzC,+BAA+B;YAC/B,YAAY;YACZ,KAAK,SAAS,GAAG;YACjB,OAAO;QACT;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;GAOC,GACD,SAAS,aAAa,KAAK,EAAE,SAAS;QACpC,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,WAAW;QAClB,MAAM,SAAS,KAAK,WAAW,CAAC;QAChC,IAAI,WAAW,OAAO,IAAI,CAAC;QAC3B,MAAM,QAAQ,GAAG;QACjB,IAAI,YAAY,WAAW,IAAI,GAAG;QAClC,aAAa;QACb,UAAU,UAAU,CAAC,MAAM,KAAK;QAChC,UAAU,KAAK,CAAC;QAEhB,yCAAyC;QACzC,EAAE;QACF,cAAc;QACd,MAAM;QACN,KAAK;QACL,EAAE;QACF,MAAM;QACN,EAAE;QACF,SAAS;QACT,IAAI;QACJ,EAAE;QACF,MAAM;QACN,EAAE;QACF,UAAU;QACV,IAAI;QACJ,MAAM;QACN,EAAE;QACF,yEAAyE;QACzE,uEAAuE;QACvE,yCAAyC;QACzC,yEAAyE;QACzE,wDAAwD;QACxD,EAAE;QACF,qEAAqE;QACrE,qBAAqB;QACrB,oEAAoE;QACpE,uBAAuB;QACvB,yEAAyE;QACzE,8CAA8C;QAC9C,EAAE;QACF,sEAAsE;QACtE,kDAAkD;QAClD,yEAAyE;QACzE,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;YACtC,IAAI,QAAQ,UAAU,MAAM,CAAC,MAAM;YAEnC,MAAO,QAAS;gBACd,IACE,2CAA2C;gBAC3C,UAAU,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,mBAC1C,gCAAgC;gBAChC,CAAC,CAAC,UAAU,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAC9B,qBAAqB;gBACrB,UAAU,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,eAAe,GACzD;oBACA,mEAAmE;oBACnE,qBAAqB;oBACrB;gBACF;YACF;YAEA,kEAAkE;YAClE,qDAAqD;YACrD,MAAM,mBAAmB,KAAK,MAAM,CAAC,MAAM;YAC3C,IAAI,kBAAkB;YACtB,gCAAgC,GAChC,IAAI;YACJ,8BAA8B,GAC9B,IAAI;YAEJ,0DAA0D;YAC1D,MAAO,kBAAmB;gBACxB,IACE,KAAK,MAAM,CAAC,gBAAgB,CAAC,EAAE,KAAK,UACpC,KAAK,MAAM,CAAC,gBAAgB,CAAC,EAAE,CAAC,IAAI,KAAK,sOAAA,CAAA,QAAK,CAAC,SAAS,EACxD;oBACA,IAAI,MAAM;wBACR,QAAQ,KAAK,MAAM,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG;wBAC3C;oBACF;oBAEA,OAAO;gBACT;YACF;YAEA,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,OAAO;YAEd,eAAe;YAEf,iBAAiB;YACjB,QAAQ;YAER,MAAO,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAE;gBACjC,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,GAAG;oBAAC,GAAG,KAAK;gBAAA;gBACrC;YACF;YAEA,4DAA4D;YAC5D,CAAA,GAAA,wOAAA,CAAA,SAAM,AAAD,EACH,KAAK,MAAM,EACX,kBAAkB,GAClB,GACA,KAAK,MAAM,CAAC,KAAK,CAAC;YAGpB,+BAA+B;YAC/B,KAAK,MAAM,CAAC,MAAM,GAAG;QACvB;IACF;IAEA;;;;;GAKC,GACD,SAAS,eAAe,IAAI;QAC1B,IAAI,QAAQ,MAAM,MAAM;QAExB,wBAAwB;QACxB,MAAO,UAAU,KAAM;YACrB,MAAM,QAAQ,KAAK,CAAC,MAAM;YAC1B,KAAK,cAAc,GAAG,KAAK,CAAC,EAAE;YAC9B,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,KAAK,CAAC,EAAE,CAAC,IAAI,EACb;YAEF,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;QAC3B;QAEA,MAAM,MAAM,GAAG;IACjB;IAEA,SAAS;QACP,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,KAAK,cAAc,EACnB;QAEF,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,WAAW;QAClB,UAAU,KAAK,CAAC;YAAC,sOAAA,CAAA,QAAK,CAAC,GAAG;SAAC;QAC3B,aAAa;QACb,YAAY;QACZ,KAAK,cAAc,CAAC,UAAU,GAAG;IACnC;AACF;AAEA;;;;;CAKC,GACD,SAAS,kBAAkB,OAAO,EAAE,EAAE,EAAE,GAAG;IACzC,gCAAgC;IAChC,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EACnC;IAEF,OAAO,CAAA,GAAA,0OAAA,CAAA,eAAY,AAAD,EAChB,SACA,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,MACrD,sOAAA,CAAA,QAAK,CAAC,UAAU,EAChB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,kBACzC,YACA,0OAAA,CAAA,YAAS,CAAC,OAAO;AAEzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/micromark%404.0.1/node_modules/micromark/dev/lib/initialize/flow.js"], "sourcesContent": ["/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {blankLine, content} from 'micromark-core-commonmark'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {InitialConstruct} */\nexport const flow = {tokenize: initializeFlow}\n\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */\nfunction initializeFlow(effects) {\n  const self = this\n  const initial = effects.attempt(\n    // Try to parse a blank line.\n    blankLine,\n    atBlankEnding,\n    // Try to parse initial flow (essentially, only code).\n    effects.attempt(\n      this.parser.constructs.flowInitial,\n      afterConstruct,\n      factorySpace(\n        effects,\n        effects.attempt(\n          this.parser.constructs.flow,\n          afterConstruct,\n          effects.attempt(content, afterConstruct)\n        ),\n        types.linePrefix\n      )\n    )\n  )\n\n  return initial\n\n  /** @type {State} */\n  function atBlankEnding(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEndingBlank)\n    effects.consume(code)\n    effects.exit(types.lineEndingBlank)\n    self.currentConstruct = undefined\n    return initial\n  }\n\n  /** @type {State} */\n  function afterConstruct(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    self.currentConstruct = undefined\n    return initial\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AAED;AACA;AAAA;AACA;AACA;AACA;AAAA;;;;;;AAGO,MAAM,OAAO;IAAC,UAAU;AAAc;AAE7C;;;;;CAKC,GACD,SAAS,eAAe,OAAO;IAC7B,MAAM,OAAO,IAAI;IACjB,MAAM,UAAU,QAAQ,OAAO,CAC7B,6BAA6B;IAC7B,6PAAA,CAAA,YAAS,EACT,eACA,sDAAsD;IACtD,QAAQ,OAAO,CACb,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,EAClC,gBACA,CAAA,GAAA,0OAAA,CAAA,eAAY,AAAD,EACT,SACA,QAAQ,OAAO,CACb,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,EAC3B,gBACA,QAAQ,OAAO,CAAC,uPAAA,CAAA,UAAO,EAAE,kBAE3B,sOAAA,CAAA,QAAK,CAAC,UAAU;IAKtB,OAAO;;IAEP,kBAAkB,GAClB,SAAS,cAAc,IAAI;QACzB,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,SAAS,sOAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,4OAAA,CAAA,qBAAkB,AAAD,EAAE,OACzC;QAGF,IAAI,SAAS,sOAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,QAAQ,OAAO,CAAC;YAChB;QACF;QAEA,QAAQ,KAAK,CAAC,sOAAA,CAAA,QAAK,CAAC,eAAe;QACnC,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,eAAe;QAClC,KAAK,gBAAgB,GAAG;QACxB,OAAO;IACT;IAEA,kBAAkB,GAClB,SAAS,eAAe,IAAI;QAC1B,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,SAAS,sOAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,4OAAA,CAAA,qBAAkB,AAAD,EAAE,OACzC;QAGF,IAAI,SAAS,sOAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,QAAQ,OAAO,CAAC;YAChB;QACF;QAEA,QAAQ,KAAK,CAAC,sOAAA,CAAA,QAAK,CAAC,UAAU;QAC9B,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,UAAU;QAC7B,KAAK,gBAAgB,GAAG;QACxB,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/micromark%404.0.1/node_modules/micromark/dev/lib/initialize/text.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   InitialConstruct,\n *   Initializer,\n *   Resolver,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\nexport const resolver = {resolveAll: createResolver()}\nexport const string = initializeFactory('string')\nexport const text = initializeFactory('text')\n\n/**\n * @param {'string' | 'text'} field\n *   Field.\n * @returns {InitialConstruct}\n *   Construct.\n */\nfunction initializeFactory(field) {\n  return {\n    resolveAll: createResolver(\n      field === 'text' ? resolveAllLineSuffixes : undefined\n    ),\n    tokenize: initializeText\n  }\n\n  /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Initializer}\n   */\n  function initializeText(effects) {\n    const self = this\n    const constructs = this.parser.constructs[field]\n    const text = effects.attempt(constructs, start, notText)\n\n    return start\n\n    /** @type {State} */\n    function start(code) {\n      return atBreak(code) ? text(code) : notText(code)\n    }\n\n    /** @type {State} */\n    function notText(code) {\n      if (code === codes.eof) {\n        effects.consume(code)\n        return\n      }\n\n      effects.enter(types.data)\n      effects.consume(code)\n      return data\n    }\n\n    /** @type {State} */\n    function data(code) {\n      if (atBreak(code)) {\n        effects.exit(types.data)\n        return text(code)\n      }\n\n      // Data.\n      effects.consume(code)\n      return data\n    }\n\n    /**\n     * @param {Code} code\n     *   Code.\n     * @returns {boolean}\n     *   Whether the code is a break.\n     */\n    function atBreak(code) {\n      if (code === codes.eof) {\n        return true\n      }\n\n      const list = constructs[code]\n      let index = -1\n\n      if (list) {\n        // Always populated by defaults.\n        assert(Array.isArray(list), 'expected `disable.null` to be populated')\n\n        while (++index < list.length) {\n          const item = list[index]\n          if (!item.previous || item.previous.call(self, self.previous)) {\n            return true\n          }\n        }\n      }\n\n      return false\n    }\n  }\n}\n\n/**\n * @param {Resolver | undefined} [extraResolver]\n *   Resolver.\n * @returns {Resolver}\n *   Resolver.\n */\nfunction createResolver(extraResolver) {\n  return resolveAllText\n\n  /** @type {Resolver} */\n  function resolveAllText(events, context) {\n    let index = -1\n    /** @type {number | undefined} */\n    let enter\n\n    // A rather boring computation (to merge adjacent `data` events) which\n    // improves mm performance by 29%.\n    while (++index <= events.length) {\n      if (enter === undefined) {\n        if (events[index] && events[index][1].type === types.data) {\n          enter = index\n          index++\n        }\n      } else if (!events[index] || events[index][1].type !== types.data) {\n        // Don’t do anything if there is one data token.\n        if (index !== enter + 2) {\n          events[enter][1].end = events[index - 1][1].end\n          events.splice(enter + 2, index - enter - 2)\n          index = enter + 2\n        }\n\n        enter = undefined\n      }\n    }\n\n    return extraResolver ? extraResolver(events, context) : events\n  }\n}\n\n/**\n * A rather ugly set of instructions which again looks at chunks in the input\n * stream.\n * The reason to do this here is that it is *much* faster to parse in reverse.\n * And that we can’t hook into `null` to split the line suffix before an EOF.\n * To do: figure out if we can make this into a clean utility, or even in core.\n * As it will be useful for GFMs literal autolink extension (and maybe even\n * tables?)\n *\n * @type {Resolver}\n */\nfunction resolveAllLineSuffixes(events, context) {\n  let eventIndex = 0 // Skip first.\n\n  while (++eventIndex <= events.length) {\n    if (\n      (eventIndex === events.length ||\n        events[eventIndex][1].type === types.lineEnding) &&\n      events[eventIndex - 1][1].type === types.data\n    ) {\n      const data = events[eventIndex - 1][1]\n      const chunks = context.sliceStream(data)\n      let index = chunks.length\n      let bufferIndex = -1\n      let size = 0\n      /** @type {boolean | undefined} */\n      let tabs\n\n      while (index--) {\n        const chunk = chunks[index]\n\n        if (typeof chunk === 'string') {\n          bufferIndex = chunk.length\n\n          while (chunk.charCodeAt(bufferIndex - 1) === codes.space) {\n            size++\n            bufferIndex--\n          }\n\n          if (bufferIndex) break\n          bufferIndex = -1\n        }\n        // Number\n        else if (chunk === codes.horizontalTab) {\n          tabs = true\n          size++\n        } else if (chunk === codes.virtualSpace) {\n          // Empty\n        } else {\n          // Replacement character, exit.\n          index++\n          break\n        }\n      }\n\n      if (size) {\n        const token = {\n          type:\n            eventIndex === events.length ||\n            tabs ||\n            size < constants.hardBreakPrefixSizeMin\n              ? types.lineSuffix\n              : types.hardBreakTrailing,\n          start: {\n            _bufferIndex: index\n              ? bufferIndex\n              : data.start._bufferIndex + bufferIndex,\n            _index: data.start._index + index,\n            line: data.end.line,\n            column: data.end.column - size,\n            offset: data.end.offset - size\n          },\n          end: {...data.end}\n        }\n\n        data.end = {...token.start}\n\n        if (data.start.offset === data.end.offset) {\n          Object.assign(data, token)\n        } else {\n          events.splice(\n            eventIndex,\n            0,\n            ['enter', token, context],\n            ['exit', token, context]\n          )\n          eventIndex += 2\n        }\n      }\n\n      eventIndex++\n    }\n  }\n\n  return events\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;;;AAED;AACA;AAAA;AAAA;;;AAEO,MAAM,WAAW;IAAC,YAAY;AAAgB;AAC9C,MAAM,SAAS,kBAAkB;AACjC,MAAM,OAAO,kBAAkB;AAEtC;;;;;CAKC,GACD,SAAS,kBAAkB,KAAK;IAC9B,OAAO;QACL,YAAY,eACV,UAAU,SAAS,yBAAyB;QAE9C,UAAU;IACZ;;IAEA;;;;GAIC,GACD,SAAS,eAAe,OAAO;QAC7B,MAAM,OAAO,IAAI;QACjB,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM;QAChD,MAAM,OAAO,QAAQ,OAAO,CAAC,YAAY,OAAO;QAEhD,OAAO;;QAEP,kBAAkB,GAClB,SAAS,MAAM,IAAI;YACjB,OAAO,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;QAC9C;QAEA,kBAAkB,GAClB,SAAS,QAAQ,IAAI;YACnB,IAAI,SAAS,sOAAA,CAAA,QAAK,CAAC,GAAG,EAAE;gBACtB,QAAQ,OAAO,CAAC;gBAChB;YACF;YAEA,QAAQ,KAAK,CAAC,sOAAA,CAAA,QAAK,CAAC,IAAI;YACxB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,kBAAkB,GAClB,SAAS,KAAK,IAAI;YAChB,IAAI,QAAQ,OAAO;gBACjB,QAAQ,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,IAAI;gBACvB,OAAO,KAAK;YACd;YAEA,QAAQ;YACR,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA;;;;;KAKC,GACD,SAAS,QAAQ,IAAI;YACnB,IAAI,SAAS,sOAAA,CAAA,QAAK,CAAC,GAAG,EAAE;gBACtB,OAAO;YACT;YAEA,MAAM,OAAO,UAAU,CAAC,KAAK;YAC7B,IAAI,QAAQ,CAAC;YAEb,IAAI,MAAM;gBACR,gCAAgC;gBAChC,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO;gBAE5B,MAAO,EAAE,QAAQ,KAAK,MAAM,CAAE;oBAC5B,MAAM,OAAO,IAAI,CAAC,MAAM;oBACxB,IAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ,GAAG;wBAC7D,OAAO;oBACT;gBACF;YACF;YAEA,OAAO;QACT;IACF;AACF;AAEA;;;;;CAKC,GACD,SAAS,eAAe,aAAa;IACnC,OAAO;;IAEP,qBAAqB,GACrB,SAAS,eAAe,MAAM,EAAE,OAAO;QACrC,IAAI,QAAQ,CAAC;QACb,+BAA+B,GAC/B,IAAI;QAEJ,sEAAsE;QACtE,kCAAkC;QAClC,MAAO,EAAE,SAAS,OAAO,MAAM,CAAE;YAC/B,IAAI,UAAU,WAAW;gBACvB,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,sOAAA,CAAA,QAAK,CAAC,IAAI,EAAE;oBACzD,QAAQ;oBACR;gBACF;YACF,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,sOAAA,CAAA,QAAK,CAAC,IAAI,EAAE;gBACjE,gDAAgD;gBAChD,IAAI,UAAU,QAAQ,GAAG;oBACvB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,GAAG;oBAC/C,OAAO,MAAM,CAAC,QAAQ,GAAG,QAAQ,QAAQ;oBACzC,QAAQ,QAAQ;gBAClB;gBAEA,QAAQ;YACV;QACF;QAEA,OAAO,gBAAgB,cAAc,QAAQ,WAAW;IAC1D;AACF;AAEA;;;;;;;;;;CAUC,GACD,SAAS,uBAAuB,MAAM,EAAE,OAAO;IAC7C,IAAI,aAAa,EAAE,cAAc;;IAEjC,MAAO,EAAE,cAAc,OAAO,MAAM,CAAE;QACpC,IACE,CAAC,eAAe,OAAO,MAAM,IAC3B,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,KAAK,sOAAA,CAAA,QAAK,CAAC,UAAU,KACjD,MAAM,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,sOAAA,CAAA,QAAK,CAAC,IAAI,EAC7C;YACA,MAAM,OAAO,MAAM,CAAC,aAAa,EAAE,CAAC,EAAE;YACtC,MAAM,SAAS,QAAQ,WAAW,CAAC;YACnC,IAAI,QAAQ,OAAO,MAAM;YACzB,IAAI,cAAc,CAAC;YACnB,IAAI,OAAO;YACX,gCAAgC,GAChC,IAAI;YAEJ,MAAO,QAAS;gBACd,MAAM,QAAQ,MAAM,CAAC,MAAM;gBAE3B,IAAI,OAAO,UAAU,UAAU;oBAC7B,cAAc,MAAM,MAAM;oBAE1B,MAAO,MAAM,UAAU,CAAC,cAAc,OAAO,sOAAA,CAAA,QAAK,CAAC,KAAK,CAAE;wBACxD;wBACA;oBACF;oBAEA,IAAI,aAAa;oBACjB,cAAc,CAAC;gBACjB,OAEK,IAAI,UAAU,sOAAA,CAAA,QAAK,CAAC,aAAa,EAAE;oBACtC,OAAO;oBACP;gBACF,OAAO,IAAI,UAAU,sOAAA,CAAA,QAAK,CAAC,YAAY,EAAE;gBACvC,QAAQ;gBACV,OAAO;oBACL,+BAA+B;oBAC/B;oBACA;gBACF;YACF;YAEA,IAAI,MAAM;gBACR,MAAM,QAAQ;oBACZ,MACE,eAAe,OAAO,MAAM,IAC5B,QACA,OAAO,0OAAA,CAAA,YAAS,CAAC,sBAAsB,GACnC,sOAAA,CAAA,QAAK,CAAC,UAAU,GAChB,sOAAA,CAAA,QAAK,CAAC,iBAAiB;oBAC7B,OAAO;wBACL,cAAc,QACV,cACA,KAAK,KAAK,CAAC,YAAY,GAAG;wBAC9B,QAAQ,KAAK,KAAK,CAAC,MAAM,GAAG;wBAC5B,MAAM,KAAK,GAAG,CAAC,IAAI;wBACnB,QAAQ,KAAK,GAAG,CAAC,MAAM,GAAG;wBAC1B,QAAQ,KAAK,GAAG,CAAC,MAAM,GAAG;oBAC5B;oBACA,KAAK;wBAAC,GAAG,KAAK,GAAG;oBAAA;gBACnB;gBAEA,KAAK,GAAG,GAAG;oBAAC,GAAG,MAAM,KAAK;gBAAA;gBAE1B,IAAI,KAAK,KAAK,CAAC,MAAM,KAAK,KAAK,GAAG,CAAC,MAAM,EAAE;oBACzC,OAAO,MAAM,CAAC,MAAM;gBACtB,OAAO;oBACL,OAAO,MAAM,CACX,YACA,GACA;wBAAC;wBAAS;wBAAO;qBAAQ,EACzB;wBAAC;wBAAQ;wBAAO;qBAAQ;oBAE1B,cAAc;gBAChB;YACF;YAEA;QACF;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/micromark%404.0.1/node_modules/micromark/dev/lib/constructs.js"], "sourcesContent": ["/**\n * @import {Extension} from 'micromark-util-types'\n */\n\nimport {\n  attention,\n  autolink,\n  blockQuote,\n  characterEscape,\n  characterReference,\n  codeFenced,\n  codeIndented,\n  codeText,\n  definition,\n  hardBreakEscape,\n  headingAtx,\n  htmlFlow,\n  htmlText,\n  labelEnd,\n  labelStartImage,\n  labelStartLink,\n  lineEnding,\n  list,\n  setextUnderline,\n  thematicBreak\n} from 'micromark-core-commonmark'\nimport {codes} from 'micromark-util-symbol'\nimport {resolver as resolveText} from './initialize/text.js'\n\n/** @satisfies {Extension['document']} */\nexport const document = {\n  [codes.asterisk]: list,\n  [codes.plusSign]: list,\n  [codes.dash]: list,\n  [codes.digit0]: list,\n  [codes.digit1]: list,\n  [codes.digit2]: list,\n  [codes.digit3]: list,\n  [codes.digit4]: list,\n  [codes.digit5]: list,\n  [codes.digit6]: list,\n  [codes.digit7]: list,\n  [codes.digit8]: list,\n  [codes.digit9]: list,\n  [codes.greaterThan]: blockQuote\n}\n\n/** @satisfies {Extension['contentInitial']} */\nexport const contentInitial = {\n  [codes.leftSquareBracket]: definition\n}\n\n/** @satisfies {Extension['flowInitial']} */\nexport const flowInitial = {\n  [codes.horizontalTab]: codeIndented,\n  [codes.virtualSpace]: codeIndented,\n  [codes.space]: codeIndented\n}\n\n/** @satisfies {Extension['flow']} */\nexport const flow = {\n  [codes.numberSign]: headingAtx,\n  [codes.asterisk]: thematicBreak,\n  [codes.dash]: [setextUnderline, thematicBreak],\n  [codes.lessThan]: htmlFlow,\n  [codes.equalsTo]: setextUnderline,\n  [codes.underscore]: thematicBreak,\n  [codes.graveAccent]: codeFenced,\n  [codes.tilde]: codeFenced\n}\n\n/** @satisfies {Extension['string']} */\nexport const string = {\n  [codes.ampersand]: characterReference,\n  [codes.backslash]: characterEscape\n}\n\n/** @satisfies {Extension['text']} */\nexport const text = {\n  [codes.carriageReturn]: lineEnding,\n  [codes.lineFeed]: lineEnding,\n  [codes.carriageReturnLineFeed]: lineEnding,\n  [codes.exclamationMark]: labelStartImage,\n  [codes.ampersand]: characterReference,\n  [codes.asterisk]: attention,\n  [codes.lessThan]: [autolink, htmlText],\n  [codes.leftSquareBracket]: labelStartLink,\n  [codes.backslash]: [hardBreakEscape, characterEscape],\n  [codes.rightSquareBracket]: labelEnd,\n  [codes.underscore]: attention,\n  [codes.graveAccent]: codeText\n}\n\n/** @satisfies {Extension['insideSpan']} */\nexport const insideSpan = {null: [attention, resolveText]}\n\n/** @satisfies {Extension['attentionMarkers']} */\nexport const attentionMarkers = {null: [codes.asterisk, codes.underscore]}\n\n/** @satisfies {Extension['disable']} */\nexport const disable = {null: []}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;AAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AACA;;;;AAGO,MAAM,WAAW;IACtB,CAAC,sOAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE,oPAAA,CAAA,OAAI;IACtB,CAAC,sOAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE,oPAAA,CAAA,OAAI;IACtB,CAAC,sOAAA,CAAA,QAAK,CAAC,IAAI,CAAC,EAAE,oPAAA,CAAA,OAAI;IAClB,CAAC,sOAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,oPAAA,CAAA,OAAI;IACpB,CAAC,sOAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,oPAAA,CAAA,OAAI;IACpB,CAAC,sOAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,oPAAA,CAAA,OAAI;IACpB,CAAC,sOAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,oPAAA,CAAA,OAAI;IACpB,CAAC,sOAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,oPAAA,CAAA,OAAI;IACpB,CAAC,sOAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,oPAAA,CAAA,OAAI;IACpB,CAAC,sOAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,oPAAA,CAAA,OAAI;IACpB,CAAC,sOAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,oPAAA,CAAA,OAAI;IACpB,CAAC,sOAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,oPAAA,CAAA,OAAI;IACpB,CAAC,sOAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,oPAAA,CAAA,OAAI;IACpB,CAAC,sOAAA,CAAA,QAAK,CAAC,WAAW,CAAC,EAAE,8PAAA,CAAA,aAAU;AACjC;AAGO,MAAM,iBAAiB;IAC5B,CAAC,sOAAA,CAAA,QAAK,CAAC,iBAAiB,CAAC,EAAE,0PAAA,CAAA,aAAU;AACvC;AAGO,MAAM,cAAc;IACzB,CAAC,sOAAA,CAAA,QAAK,CAAC,aAAa,CAAC,EAAE,gQAAA,CAAA,eAAY;IACnC,CAAC,sOAAA,CAAA,QAAK,CAAC,YAAY,CAAC,EAAE,gQAAA,CAAA,eAAY;IAClC,CAAC,sOAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAE,gQAAA,CAAA,eAAY;AAC7B;AAGO,MAAM,OAAO;IAClB,CAAC,sOAAA,CAAA,QAAK,CAAC,UAAU,CAAC,EAAE,8PAAA,CAAA,aAAU;IAC9B,CAAC,sOAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE,iQAAA,CAAA,gBAAa;IAC/B,CAAC,sOAAA,CAAA,QAAK,CAAC,IAAI,CAAC,EAAE;QAAC,mQAAA,CAAA,kBAAe;QAAE,iQAAA,CAAA,gBAAa;KAAC;IAC9C,CAAC,sOAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE,4PAAA,CAAA,WAAQ;IAC1B,CAAC,sOAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE,mQAAA,CAAA,kBAAe;IACjC,CAAC,sOAAA,CAAA,QAAK,CAAC,UAAU,CAAC,EAAE,iQAAA,CAAA,gBAAa;IACjC,CAAC,sOAAA,CAAA,QAAK,CAAC,WAAW,CAAC,EAAE,8PAAA,CAAA,aAAU;IAC/B,CAAC,sOAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAE,8PAAA,CAAA,aAAU;AAC3B;AAGO,MAAM,SAAS;IACpB,CAAC,sOAAA,CAAA,QAAK,CAAC,SAAS,CAAC,EAAE,sQAAA,CAAA,qBAAkB;IACrC,CAAC,sOAAA,CAAA,QAAK,CAAC,SAAS,CAAC,EAAE,mQAAA,CAAA,kBAAe;AACpC;AAGO,MAAM,OAAO;IAClB,CAAC,sOAAA,CAAA,QAAK,CAAC,cAAc,CAAC,EAAE,8PAAA,CAAA,aAAU;IAClC,CAAC,sOAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE,8PAAA,CAAA,aAAU;IAC5B,CAAC,sOAAA,CAAA,QAAK,CAAC,sBAAsB,CAAC,EAAE,8PAAA,CAAA,aAAU;IAC1C,CAAC,sOAAA,CAAA,QAAK,CAAC,eAAe,CAAC,EAAE,uQAAA,CAAA,kBAAe;IACxC,CAAC,sOAAA,CAAA,QAAK,CAAC,SAAS,CAAC,EAAE,sQAAA,CAAA,qBAAkB;IACrC,CAAC,sOAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE,yPAAA,CAAA,YAAS;IAC3B,CAAC,sOAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE;QAAC,wPAAA,CAAA,WAAQ;QAAE,4PAAA,CAAA,WAAQ;KAAC;IACtC,CAAC,sOAAA,CAAA,QAAK,CAAC,iBAAiB,CAAC,EAAE,sQAAA,CAAA,iBAAc;IACzC,CAAC,sOAAA,CAAA,QAAK,CAAC,SAAS,CAAC,EAAE;QAAC,uQAAA,CAAA,kBAAe;QAAE,mQAAA,CAAA,kBAAe;KAAC;IACrD,CAAC,sOAAA,CAAA,QAAK,CAAC,kBAAkB,CAAC,EAAE,4PAAA,CAAA,WAAQ;IACpC,CAAC,sOAAA,CAAA,QAAK,CAAC,UAAU,CAAC,EAAE,yPAAA,CAAA,YAAS;IAC7B,CAAC,sOAAA,CAAA,QAAK,CAAC,WAAW,CAAC,EAAE,4PAAA,CAAA,WAAQ;AAC/B;AAGO,MAAM,aAAa;IAAC,MAAM;QAAC,yPAAA,CAAA,YAAS;QAAE,sNAAA,CAAA,WAAW;KAAC;AAAA;AAGlD,MAAM,mBAAmB;IAAC,MAAM;QAAC,sOAAA,CAAA,QAAK,CAAC,QAAQ;QAAE,sOAAA,CAAA,QAAK,CAAC,UAAU;KAAC;AAAA;AAGlE,MAAM,UAAU;IAAC,MAAM,EAAE;AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/micromark%404.0.1/node_modules/micromark/dev/lib/create-tokenizer.js"], "sourcesContent": ["/**\n * @import {\n *   Chunk,\n *   Code,\n *   ConstructRecord,\n *   Construct,\n *   Effects,\n *   InitialConstruct,\n *   ParseContext,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @callback Restore\n *   Restore the state.\n * @returns {undefined}\n *   Nothing.\n *\n * @typedef Info\n *   Info.\n * @property {Restore} restore\n *   Restore.\n * @property {number} from\n *   From.\n *\n * @callback ReturnHandle\n *   Handle a successful run.\n * @param {Construct} construct\n *   Construct.\n * @param {Info} info\n *   Info.\n * @returns {undefined}\n *   Nothing.\n */\n\nimport createDebug from 'debug'\nimport {ok as assert} from 'devlop'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {push, splice} from 'micromark-util-chunked'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes, values} from 'micromark-util-symbol'\n\nconst debug = createDebug('micromark')\n\n/**\n * Create a tokenizer.\n * Tokenizers deal with one type of data (e.g., containers, flow, text).\n * The parser is the object dealing with it all.\n * `initialize` works like other constructs, except that only its `tokenize`\n * function is used, in which case it doesn’t receive an `ok` or `nok`.\n * `from` can be given to set the point before the first character, although\n * when further lines are indented, they must be set with `defineSkip`.\n *\n * @param {ParseContext} parser\n *   Parser.\n * @param {InitialConstruct} initialize\n *   Construct.\n * @param {Omit<Point, '_bufferIndex' | '_index'> | undefined} [from]\n *   Point (optional).\n * @returns {TokenizeContext}\n *   Context.\n */\nexport function createTokenizer(parser, initialize, from) {\n  /** @type {Point} */\n  let point = {\n    _bufferIndex: -1,\n    _index: 0,\n    line: (from && from.line) || 1,\n    column: (from && from.column) || 1,\n    offset: (from && from.offset) || 0\n  }\n  /** @type {Record<string, number>} */\n  const columnStart = {}\n  /** @type {Array<Construct>} */\n  const resolveAllConstructs = []\n  /** @type {Array<Chunk>} */\n  let chunks = []\n  /** @type {Array<Token>} */\n  let stack = []\n  /** @type {boolean | undefined} */\n  let consumed = true\n\n  /**\n   * Tools used for tokenizing.\n   *\n   * @type {Effects}\n   */\n  const effects = {\n    attempt: constructFactory(onsuccessfulconstruct),\n    check: constructFactory(onsuccessfulcheck),\n    consume,\n    enter,\n    exit,\n    interrupt: constructFactory(onsuccessfulcheck, {interrupt: true})\n  }\n\n  /**\n   * State and tools for resolving and serializing.\n   *\n   * @type {TokenizeContext}\n   */\n  const context = {\n    code: codes.eof,\n    containerState: {},\n    defineSkip,\n    events: [],\n    now,\n    parser,\n    previous: codes.eof,\n    sliceSerialize,\n    sliceStream,\n    write\n  }\n\n  /**\n   * The state function.\n   *\n   * @type {State | undefined}\n   */\n  let state = initialize.tokenize.call(context, effects)\n\n  /**\n   * Track which character we expect to be consumed, to catch bugs.\n   *\n   * @type {Code}\n   */\n  let expectedCode\n\n  if (initialize.resolveAll) {\n    resolveAllConstructs.push(initialize)\n  }\n\n  return context\n\n  /** @type {TokenizeContext['write']} */\n  function write(slice) {\n    chunks = push(chunks, slice)\n\n    main()\n\n    // Exit if we’re not done, resolve might change stuff.\n    if (chunks[chunks.length - 1] !== codes.eof) {\n      return []\n    }\n\n    addResult(initialize, 0)\n\n    // Otherwise, resolve, and exit.\n    context.events = resolveAll(resolveAllConstructs, context.events, context)\n\n    return context.events\n  }\n\n  //\n  // Tools.\n  //\n\n  /** @type {TokenizeContext['sliceSerialize']} */\n  function sliceSerialize(token, expandTabs) {\n    return serializeChunks(sliceStream(token), expandTabs)\n  }\n\n  /** @type {TokenizeContext['sliceStream']} */\n  function sliceStream(token) {\n    return sliceChunks(chunks, token)\n  }\n\n  /** @type {TokenizeContext['now']} */\n  function now() {\n    // This is a hot path, so we clone manually instead of `Object.assign({}, point)`\n    const {_bufferIndex, _index, line, column, offset} = point\n    return {_bufferIndex, _index, line, column, offset}\n  }\n\n  /** @type {TokenizeContext['defineSkip']} */\n  function defineSkip(value) {\n    columnStart[value.line] = value.column\n    accountForPotentialSkip()\n    debug('position: define skip: `%j`', point)\n  }\n\n  //\n  // State management.\n  //\n\n  /**\n   * Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n   * `consume`).\n   * Here is where we walk through the chunks, which either include strings of\n   * several characters, or numerical character codes.\n   * The reason to do this in a loop instead of a call is so the stack can\n   * drain.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function main() {\n    /** @type {number} */\n    let chunkIndex\n\n    while (point._index < chunks.length) {\n      const chunk = chunks[point._index]\n\n      // If we’re in a buffer chunk, loop through it.\n      if (typeof chunk === 'string') {\n        chunkIndex = point._index\n\n        if (point._bufferIndex < 0) {\n          point._bufferIndex = 0\n        }\n\n        while (\n          point._index === chunkIndex &&\n          point._bufferIndex < chunk.length\n        ) {\n          go(chunk.charCodeAt(point._bufferIndex))\n        }\n      } else {\n        go(chunk)\n      }\n    }\n  }\n\n  /**\n   * Deal with one code.\n   *\n   * @param {Code} code\n   *   Code.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function go(code) {\n    assert(consumed === true, 'expected character to be consumed')\n    consumed = undefined\n    debug('main: passing `%s` to %s', code, state && state.name)\n    expectedCode = code\n    assert(typeof state === 'function', 'expected state')\n    state = state(code)\n  }\n\n  /** @type {Effects['consume']} */\n  function consume(code) {\n    assert(code === expectedCode, 'expected given code to equal expected code')\n\n    debug('consume: `%s`', code)\n\n    assert(\n      consumed === undefined,\n      'expected code to not have been consumed: this might be because `return x(code)` instead of `return x` was used'\n    )\n    assert(\n      code === null\n        ? context.events.length === 0 ||\n            context.events[context.events.length - 1][0] === 'exit'\n        : context.events[context.events.length - 1][0] === 'enter',\n      'expected last token to be open'\n    )\n\n    if (markdownLineEnding(code)) {\n      point.line++\n      point.column = 1\n      point.offset += code === codes.carriageReturnLineFeed ? 2 : 1\n      accountForPotentialSkip()\n      debug('position: after eol: `%j`', point)\n    } else if (code !== codes.virtualSpace) {\n      point.column++\n      point.offset++\n    }\n\n    // Not in a string chunk.\n    if (point._bufferIndex < 0) {\n      point._index++\n    } else {\n      point._bufferIndex++\n\n      // At end of string chunk.\n      if (\n        point._bufferIndex ===\n        // Points w/ non-negative `_bufferIndex` reference\n        // strings.\n        /** @type {string} */ (chunks[point._index]).length\n      ) {\n        point._bufferIndex = -1\n        point._index++\n      }\n    }\n\n    // Expose the previous character.\n    context.previous = code\n\n    // Mark as consumed.\n    consumed = true\n  }\n\n  /** @type {Effects['enter']} */\n  function enter(type, fields) {\n    /** @type {Token} */\n    // @ts-expect-error Patch instead of assign required fields to help GC.\n    const token = fields || {}\n    token.type = type\n    token.start = now()\n\n    assert(typeof type === 'string', 'expected string type')\n    assert(type.length > 0, 'expected non-empty string')\n    debug('enter: `%s`', type)\n\n    context.events.push(['enter', token, context])\n\n    stack.push(token)\n\n    return token\n  }\n\n  /** @type {Effects['exit']} */\n  function exit(type) {\n    assert(typeof type === 'string', 'expected string type')\n    assert(type.length > 0, 'expected non-empty string')\n\n    const token = stack.pop()\n    assert(token, 'cannot close w/o open tokens')\n    token.end = now()\n\n    assert(type === token.type, 'expected exit token to match current token')\n\n    assert(\n      !(\n        token.start._index === token.end._index &&\n        token.start._bufferIndex === token.end._bufferIndex\n      ),\n      'expected non-empty token (`' + type + '`)'\n    )\n\n    debug('exit: `%s`', token.type)\n    context.events.push(['exit', token, context])\n\n    return token\n  }\n\n  /**\n   * Use results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulconstruct(construct, info) {\n    addResult(construct, info.from)\n  }\n\n  /**\n   * Discard results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulcheck(_, info) {\n    info.restore()\n  }\n\n  /**\n   * Factory to attempt/check/interrupt.\n   *\n   * @param {ReturnHandle} onreturn\n   *   Callback.\n   * @param {{interrupt?: boolean | undefined} | undefined} [fields]\n   *   Fields.\n   */\n  function constructFactory(onreturn, fields) {\n    return hook\n\n    /**\n     * Handle either an object mapping codes to constructs, a list of\n     * constructs, or a single construct.\n     *\n     * @param {Array<Construct> | ConstructRecord | Construct} constructs\n     *   Constructs.\n     * @param {State} returnState\n     *   State.\n     * @param {State | undefined} [bogusState]\n     *   State.\n     * @returns {State}\n     *   State.\n     */\n    function hook(constructs, returnState, bogusState) {\n      /** @type {ReadonlyArray<Construct>} */\n      let listOfConstructs\n      /** @type {number} */\n      let constructIndex\n      /** @type {Construct} */\n      let currentConstruct\n      /** @type {Info} */\n      let info\n\n      return Array.isArray(constructs)\n        ? /* c8 ignore next 1 */\n          handleListOfConstructs(constructs)\n        : 'tokenize' in constructs\n          ? // Looks like a construct.\n            handleListOfConstructs([/** @type {Construct} */ (constructs)])\n          : handleMapOfConstructs(constructs)\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ConstructRecord} map\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleMapOfConstructs(map) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          const left = code !== null && map[code]\n          const all = code !== null && map.null\n          const list = [\n            // To do: add more extension tests.\n            /* c8 ignore next 2 */\n            ...(Array.isArray(left) ? left : left ? [left] : []),\n            ...(Array.isArray(all) ? all : all ? [all] : [])\n          ]\n\n          return handleListOfConstructs(list)(code)\n        }\n      }\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ReadonlyArray<Construct>} list\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleListOfConstructs(list) {\n        listOfConstructs = list\n        constructIndex = 0\n\n        if (list.length === 0) {\n          assert(bogusState, 'expected `bogusState` to be given')\n          return bogusState\n        }\n\n        return handleConstruct(list[constructIndex])\n      }\n\n      /**\n       * Handle a single construct.\n       *\n       * @param {Construct} construct\n       *   Construct.\n       * @returns {State}\n       *   State.\n       */\n      function handleConstruct(construct) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          // To do: not needed to store if there is no bogus state, probably?\n          // Currently doesn’t work because `inspect` in document does a check\n          // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n          // by not storing.\n          info = store()\n          currentConstruct = construct\n\n          if (!construct.partial) {\n            context.currentConstruct = construct\n          }\n\n          // Always populated by defaults.\n          assert(\n            context.parser.constructs.disable.null,\n            'expected `disable.null` to be populated'\n          )\n\n          if (\n            construct.name &&\n            context.parser.constructs.disable.null.includes(construct.name)\n          ) {\n            return nok(code)\n          }\n\n          return construct.tokenize.call(\n            // If we do have fields, create an object w/ `context` as its\n            // prototype.\n            // This allows a “live binding”, which is needed for `interrupt`.\n            fields ? Object.assign(Object.create(context), fields) : context,\n            effects,\n            ok,\n            nok\n          )(code)\n        }\n      }\n\n      /** @type {State} */\n      function ok(code) {\n        assert(code === expectedCode, 'expected code')\n        consumed = true\n        onreturn(currentConstruct, info)\n        return returnState\n      }\n\n      /** @type {State} */\n      function nok(code) {\n        assert(code === expectedCode, 'expected code')\n        consumed = true\n        info.restore()\n\n        if (++constructIndex < listOfConstructs.length) {\n          return handleConstruct(listOfConstructs[constructIndex])\n        }\n\n        return bogusState\n      }\n    }\n  }\n\n  /**\n   * @param {Construct} construct\n   *   Construct.\n   * @param {number} from\n   *   From.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function addResult(construct, from) {\n    if (construct.resolveAll && !resolveAllConstructs.includes(construct)) {\n      resolveAllConstructs.push(construct)\n    }\n\n    if (construct.resolve) {\n      splice(\n        context.events,\n        from,\n        context.events.length - from,\n        construct.resolve(context.events.slice(from), context)\n      )\n    }\n\n    if (construct.resolveTo) {\n      context.events = construct.resolveTo(context.events, context)\n    }\n\n    assert(\n      construct.partial ||\n        context.events.length === 0 ||\n        context.events[context.events.length - 1][0] === 'exit',\n      'expected last token to end'\n    )\n  }\n\n  /**\n   * Store state.\n   *\n   * @returns {Info}\n   *   Info.\n   */\n  function store() {\n    const startPoint = now()\n    const startPrevious = context.previous\n    const startCurrentConstruct = context.currentConstruct\n    const startEventsIndex = context.events.length\n    const startStack = Array.from(stack)\n\n    return {from: startEventsIndex, restore}\n\n    /**\n     * Restore state.\n     *\n     * @returns {undefined}\n     *   Nothing.\n     */\n    function restore() {\n      point = startPoint\n      context.previous = startPrevious\n      context.currentConstruct = startCurrentConstruct\n      context.events.length = startEventsIndex\n      stack = startStack\n      accountForPotentialSkip()\n      debug('position: restore: `%j`', point)\n    }\n  }\n\n  /**\n   * Move the current point a bit forward in the line when it’s on a column\n   * skip.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function accountForPotentialSkip() {\n    if (point.line in columnStart && point.column < 2) {\n      point.column = columnStart[point.line]\n      point.offset += columnStart[point.line] - 1\n    }\n  }\n}\n\n/**\n * Get the chunks from a slice of chunks in the range of a token.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {Pick<Token, 'end' | 'start'>} token\n *   Token.\n * @returns {Array<Chunk>}\n *   Chunks.\n */\nfunction sliceChunks(chunks, token) {\n  const startIndex = token.start._index\n  const startBufferIndex = token.start._bufferIndex\n  const endIndex = token.end._index\n  const endBufferIndex = token.end._bufferIndex\n  /** @type {Array<Chunk>} */\n  let view\n\n  if (startIndex === endIndex) {\n    assert(endBufferIndex > -1, 'expected non-negative end buffer index')\n    assert(startBufferIndex > -1, 'expected non-negative start buffer index')\n    // @ts-expect-error `_bufferIndex` is used on string chunks.\n    view = [chunks[startIndex].slice(startBufferIndex, endBufferIndex)]\n  } else {\n    view = chunks.slice(startIndex, endIndex)\n\n    if (startBufferIndex > -1) {\n      const head = view[0]\n      if (typeof head === 'string') {\n        view[0] = head.slice(startBufferIndex)\n      } else {\n        assert(startBufferIndex === 0, 'expected `startBufferIndex` to be `0`')\n        view.shift()\n      }\n    }\n\n    if (endBufferIndex > 0) {\n      // @ts-expect-error `_bufferIndex` is used on string chunks.\n      view.push(chunks[endIndex].slice(0, endBufferIndex))\n    }\n  }\n\n  return view\n}\n\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {boolean | undefined} [expandTabs=false]\n *   Whether to expand tabs (default: `false`).\n * @returns {string}\n *   Result.\n */\nfunction serializeChunks(chunks, expandTabs) {\n  let index = -1\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {boolean | undefined} */\n  let atTab\n\n  while (++index < chunks.length) {\n    const chunk = chunks[index]\n    /** @type {string} */\n    let value\n\n    if (typeof chunk === 'string') {\n      value = chunk\n    } else\n      switch (chunk) {\n        case codes.carriageReturn: {\n          value = values.cr\n\n          break\n        }\n\n        case codes.lineFeed: {\n          value = values.lf\n\n          break\n        }\n\n        case codes.carriageReturnLineFeed: {\n          value = values.cr + values.lf\n\n          break\n        }\n\n        case codes.horizontalTab: {\n          value = expandTabs ? values.space : values.ht\n\n          break\n        }\n\n        case codes.virtualSpace: {\n          if (!expandTabs && atTab) continue\n          value = values.space\n\n          break\n        }\n\n        default: {\n          assert(typeof chunk === 'number', 'expected number')\n          // Currently only replacement character.\n          value = String.fromCharCode(chunk)\n        }\n      }\n\n    atTab = chunk === codes.horizontalTab\n    result.push(value)\n  }\n\n  return result.join('')\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC,GAED;;;;;;;;;;;;;;;;;;;;;CAqBC;;;AAED;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;AAEA,MAAM,QAAQ,CAAA,GAAA,4LAAA,CAAA,UAAW,AAAD,EAAE;AAoBnB,SAAS,gBAAgB,MAAM,EAAE,UAAU,EAAE,IAAI;IACtD,kBAAkB,GAClB,IAAI,QAAQ;QACV,cAAc,CAAC;QACf,QAAQ;QACR,MAAM,AAAC,QAAQ,KAAK,IAAI,IAAK;QAC7B,QAAQ,AAAC,QAAQ,KAAK,MAAM,IAAK;QACjC,QAAQ,AAAC,QAAQ,KAAK,MAAM,IAAK;IACnC;IACA,mCAAmC,GACnC,MAAM,cAAc,CAAC;IACrB,6BAA6B,GAC7B,MAAM,uBAAuB,EAAE;IAC/B,yBAAyB,GACzB,IAAI,SAAS,EAAE;IACf,yBAAyB,GACzB,IAAI,QAAQ,EAAE;IACd,gCAAgC,GAChC,IAAI,WAAW;IAEf;;;;GAIC,GACD,MAAM,UAAU;QACd,SAAS,iBAAiB;QAC1B,OAAO,iBAAiB;QACxB;QACA;QACA;QACA,WAAW,iBAAiB,mBAAmB;YAAC,WAAW;QAAI;IACjE;IAEA;;;;GAIC,GACD,MAAM,UAAU;QACd,MAAM,sOAAA,CAAA,QAAK,CAAC,GAAG;QACf,gBAAgB,CAAC;QACjB;QACA,QAAQ,EAAE;QACV;QACA;QACA,UAAU,sOAAA,CAAA,QAAK,CAAC,GAAG;QACnB;QACA;QACA;IACF;IAEA;;;;GAIC,GACD,IAAI,QAAQ,WAAW,QAAQ,CAAC,IAAI,CAAC,SAAS;IAE9C;;;;GAIC,GACD,IAAI;IAEJ,IAAI,WAAW,UAAU,EAAE;QACzB,qBAAqB,IAAI,CAAC;IAC5B;IAEA,OAAO;;IAEP,qCAAqC,GACrC,SAAS,MAAM,KAAK;QAClB,SAAS,CAAA,GAAA,wOAAA,CAAA,OAAI,AAAD,EAAE,QAAQ;QAEtB;QAEA,sDAAsD;QACtD,IAAI,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,KAAK,sOAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YAC3C,OAAO,EAAE;QACX;QAEA,UAAU,YAAY;QAEtB,gCAAgC;QAChC,QAAQ,MAAM,GAAG,CAAA,GAAA,+OAAA,CAAA,aAAU,AAAD,EAAE,sBAAsB,QAAQ,MAAM,EAAE;QAElE,OAAO,QAAQ,MAAM;IACvB;IAEA,EAAE;IACF,SAAS;IACT,EAAE;IAEF,8CAA8C,GAC9C,SAAS,eAAe,KAAK,EAAE,UAAU;QACvC,OAAO,gBAAgB,YAAY,QAAQ;IAC7C;IAEA,2CAA2C,GAC3C,SAAS,YAAY,KAAK;QACxB,OAAO,YAAY,QAAQ;IAC7B;IAEA,mCAAmC,GACnC,SAAS;QACP,iFAAiF;QACjF,MAAM,EAAC,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAC,GAAG;QACrD,OAAO;YAAC;YAAc;YAAQ;YAAM;YAAQ;QAAM;IACpD;IAEA,0CAA0C,GAC1C,SAAS,WAAW,KAAK;QACvB,WAAW,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,MAAM;QACtC;QACA,MAAM,+BAA+B;IACvC;IAEA,EAAE;IACF,oBAAoB;IACpB,EAAE;IAEF;;;;;;;;;;GAUC,GACD,SAAS;QACP,mBAAmB,GACnB,IAAI;QAEJ,MAAO,MAAM,MAAM,GAAG,OAAO,MAAM,CAAE;YACnC,MAAM,QAAQ,MAAM,CAAC,MAAM,MAAM,CAAC;YAElC,+CAA+C;YAC/C,IAAI,OAAO,UAAU,UAAU;gBAC7B,aAAa,MAAM,MAAM;gBAEzB,IAAI,MAAM,YAAY,GAAG,GAAG;oBAC1B,MAAM,YAAY,GAAG;gBACvB;gBAEA,MACE,MAAM,MAAM,KAAK,cACjB,MAAM,YAAY,GAAG,MAAM,MAAM,CACjC;oBACA,GAAG,MAAM,UAAU,CAAC,MAAM,YAAY;gBACxC;YACF,OAAO;gBACL,GAAG;YACL;QACF;IACF;IAEA;;;;;;;GAOC,GACD,SAAS,GAAG,IAAI;QACd,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,aAAa,MAAM;QAC1B,WAAW;QACX,MAAM,4BAA4B,MAAM,SAAS,MAAM,IAAI;QAC3D,eAAe;QACf,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,OAAO,UAAU,YAAY;QACpC,QAAQ,MAAM;IAChB;IAEA,+BAA+B,GAC/B,SAAS,QAAQ,IAAI;QACnB,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,SAAS,cAAc;QAE9B,MAAM,iBAAiB;QAEvB,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,aAAa,WACb;QAEF,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,SAAS,OACL,QAAQ,MAAM,CAAC,MAAM,KAAK,KACxB,QAAQ,MAAM,CAAC,QAAQ,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,SACnD,QAAQ,MAAM,CAAC,QAAQ,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,SACrD;QAGF,IAAI,CAAA,GAAA,4OAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,MAAM,IAAI;YACV,MAAM,MAAM,GAAG;YACf,MAAM,MAAM,IAAI,SAAS,sOAAA,CAAA,QAAK,CAAC,sBAAsB,GAAG,IAAI;YAC5D;YACA,MAAM,6BAA6B;QACrC,OAAO,IAAI,SAAS,sOAAA,CAAA,QAAK,CAAC,YAAY,EAAE;YACtC,MAAM,MAAM;YACZ,MAAM,MAAM;QACd;QAEA,yBAAyB;QACzB,IAAI,MAAM,YAAY,GAAG,GAAG;YAC1B,MAAM,MAAM;QACd,OAAO;YACL,MAAM,YAAY;YAElB,0BAA0B;YAC1B,IACE,MAAM,YAAY,KAClB,kDAAkD;YAClD,WAAW;YACX,mBAAmB,GAAG,AAAC,MAAM,CAAC,MAAM,MAAM,CAAC,CAAE,MAAM,EACnD;gBACA,MAAM,YAAY,GAAG,CAAC;gBACtB,MAAM,MAAM;YACd;QACF;QAEA,iCAAiC;QACjC,QAAQ,QAAQ,GAAG;QAEnB,oBAAoB;QACpB,WAAW;IACb;IAEA,6BAA6B,GAC7B,SAAS,MAAM,IAAI,EAAE,MAAM;QACzB,kBAAkB,GAClB,uEAAuE;QACvE,MAAM,QAAQ,UAAU,CAAC;QACzB,MAAM,IAAI,GAAG;QACb,MAAM,KAAK,GAAG;QAEd,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,OAAO,SAAS,UAAU;QACjC,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,KAAK,MAAM,GAAG,GAAG;QACxB,MAAM,eAAe;QAErB,QAAQ,MAAM,CAAC,IAAI,CAAC;YAAC;YAAS;YAAO;SAAQ;QAE7C,MAAM,IAAI,CAAC;QAEX,OAAO;IACT;IAEA,4BAA4B,GAC5B,SAAS,KAAK,IAAI;QAChB,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,OAAO,SAAS,UAAU;QACjC,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,KAAK,MAAM,GAAG,GAAG;QAExB,MAAM,QAAQ,MAAM,GAAG;QACvB,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,OAAO;QACd,MAAM,GAAG,GAAG;QAEZ,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,SAAS,MAAM,IAAI,EAAE;QAE5B,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,CAAC,CACC,MAAM,KAAK,CAAC,MAAM,KAAK,MAAM,GAAG,CAAC,MAAM,IACvC,MAAM,KAAK,CAAC,YAAY,KAAK,MAAM,GAAG,CAAC,YAAY,AACrD,GACA,gCAAgC,OAAO;QAGzC,MAAM,cAAc,MAAM,IAAI;QAC9B,QAAQ,MAAM,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAO;SAAQ;QAE5C,OAAO;IACT;IAEA;;;;GAIC,GACD,SAAS,sBAAsB,SAAS,EAAE,IAAI;QAC5C,UAAU,WAAW,KAAK,IAAI;IAChC;IAEA;;;;GAIC,GACD,SAAS,kBAAkB,CAAC,EAAE,IAAI;QAChC,KAAK,OAAO;IACd;IAEA;;;;;;;GAOC,GACD,SAAS,iBAAiB,QAAQ,EAAE,MAAM;QACxC,OAAO;;QAEP;;;;;;;;;;;;KAYC,GACD,SAAS,KAAK,UAAU,EAAE,WAAW,EAAE,UAAU;YAC/C,qCAAqC,GACrC,IAAI;YACJ,mBAAmB,GACnB,IAAI;YACJ,sBAAsB,GACtB,IAAI;YACJ,iBAAiB,GACjB,IAAI;YAEJ,OAAO,MAAM,OAAO,CAAC,cACjB,oBAAoB,GACpB,uBAAuB,cACvB,cAAc,aAEZ,uBAAuB;gBAA2B;aAAY,IAC9D,sBAAsB;;YAE5B;;;;;;;OAOC,GACD,SAAS,sBAAsB,GAAG;gBAChC,OAAO;;gBAEP,kBAAkB,GAClB,SAAS,MAAM,IAAI;oBACjB,MAAM,OAAO,SAAS,QAAQ,GAAG,CAAC,KAAK;oBACvC,MAAM,MAAM,SAAS,QAAQ,IAAI,IAAI;oBACrC,MAAM,OAAO;wBACX,mCAAmC;wBACnC,oBAAoB,MAChB,MAAM,OAAO,CAAC,QAAQ,OAAO,OAAO;4BAAC;yBAAK,GAAG,EAAE;2BAC/C,MAAM,OAAO,CAAC,OAAO,MAAM,MAAM;4BAAC;yBAAI,GAAG,EAAE;qBAChD;oBAED,OAAO,uBAAuB,MAAM;gBACtC;YACF;YAEA;;;;;;;OAOC,GACD,SAAS,uBAAuB,IAAI;gBAClC,mBAAmB;gBACnB,iBAAiB;gBAEjB,IAAI,KAAK,MAAM,KAAK,GAAG;oBACrB,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,YAAY;oBACnB,OAAO;gBACT;gBAEA,OAAO,gBAAgB,IAAI,CAAC,eAAe;YAC7C;YAEA;;;;;;;OAOC,GACD,SAAS,gBAAgB,SAAS;gBAChC,OAAO;;gBAEP,kBAAkB,GAClB,SAAS,MAAM,IAAI;oBACjB,mEAAmE;oBACnE,oEAAoE;oBACpE,uEAAuE;oBACvE,kBAAkB;oBAClB,OAAO;oBACP,mBAAmB;oBAEnB,IAAI,CAAC,UAAU,OAAO,EAAE;wBACtB,QAAQ,gBAAgB,GAAG;oBAC7B;oBAEA,gCAAgC;oBAChC,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,QAAQ,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EACtC;oBAGF,IACE,UAAU,IAAI,IACd,QAAQ,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,GAC9D;wBACA,OAAO,IAAI;oBACb;oBAEA,OAAO,UAAU,QAAQ,CAAC,IAAI,CAC5B,6DAA6D;oBAC7D,aAAa;oBACb,iEAAiE;oBACjE,SAAS,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,UAAU,UAAU,SACzD,SACA,IACA,KACA;gBACJ;YACF;YAEA,kBAAkB,GAClB,SAAS,GAAG,IAAI;gBACd,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,SAAS,cAAc;gBAC9B,WAAW;gBACX,SAAS,kBAAkB;gBAC3B,OAAO;YACT;YAEA,kBAAkB,GAClB,SAAS,IAAI,IAAI;gBACf,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,SAAS,cAAc;gBAC9B,WAAW;gBACX,KAAK,OAAO;gBAEZ,IAAI,EAAE,iBAAiB,iBAAiB,MAAM,EAAE;oBAC9C,OAAO,gBAAgB,gBAAgB,CAAC,eAAe;gBACzD;gBAEA,OAAO;YACT;QACF;IACF;IAEA;;;;;;;GAOC,GACD,SAAS,UAAU,SAAS,EAAE,IAAI;QAChC,IAAI,UAAU,UAAU,IAAI,CAAC,qBAAqB,QAAQ,CAAC,YAAY;YACrE,qBAAqB,IAAI,CAAC;QAC5B;QAEA,IAAI,UAAU,OAAO,EAAE;YACrB,CAAA,GAAA,wOAAA,CAAA,SAAM,AAAD,EACH,QAAQ,MAAM,EACd,MACA,QAAQ,MAAM,CAAC,MAAM,GAAG,MACxB,UAAU,OAAO,CAAC,QAAQ,MAAM,CAAC,KAAK,CAAC,OAAO;QAElD;QAEA,IAAI,UAAU,SAAS,EAAE;YACvB,QAAQ,MAAM,GAAG,UAAU,SAAS,CAAC,QAAQ,MAAM,EAAE;QACvD;QAEA,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EACH,UAAU,OAAO,IACf,QAAQ,MAAM,CAAC,MAAM,KAAK,KAC1B,QAAQ,MAAM,CAAC,QAAQ,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,QACnD;IAEJ;IAEA;;;;;GAKC,GACD,SAAS;QACP,MAAM,aAAa;QACnB,MAAM,gBAAgB,QAAQ,QAAQ;QACtC,MAAM,wBAAwB,QAAQ,gBAAgB;QACtD,MAAM,mBAAmB,QAAQ,MAAM,CAAC,MAAM;QAC9C,MAAM,aAAa,MAAM,IAAI,CAAC;QAE9B,OAAO;YAAC,MAAM;YAAkB;QAAO;;QAEvC;;;;;KAKC,GACD,SAAS;YACP,QAAQ;YACR,QAAQ,QAAQ,GAAG;YACnB,QAAQ,gBAAgB,GAAG;YAC3B,QAAQ,MAAM,CAAC,MAAM,GAAG;YACxB,QAAQ;YACR;YACA,MAAM,2BAA2B;QACnC;IACF;IAEA;;;;;;GAMC,GACD,SAAS;QACP,IAAI,MAAM,IAAI,IAAI,eAAe,MAAM,MAAM,GAAG,GAAG;YACjD,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,IAAI,CAAC;YACtC,MAAM,MAAM,IAAI,WAAW,CAAC,MAAM,IAAI,CAAC,GAAG;QAC5C;IACF;AACF;AAEA;;;;;;;;;CASC,GACD,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,MAAM,aAAa,MAAM,KAAK,CAAC,MAAM;IACrC,MAAM,mBAAmB,MAAM,KAAK,CAAC,YAAY;IACjD,MAAM,WAAW,MAAM,GAAG,CAAC,MAAM;IACjC,MAAM,iBAAiB,MAAM,GAAG,CAAC,YAAY;IAC7C,yBAAyB,GACzB,IAAI;IAEJ,IAAI,eAAe,UAAU;QAC3B,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,iBAAiB,CAAC,GAAG;QAC5B,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,mBAAmB,CAAC,GAAG;QAC9B,4DAA4D;QAC5D,OAAO;YAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,kBAAkB;SAAgB;IACrE,OAAO;QACL,OAAO,OAAO,KAAK,CAAC,YAAY;QAEhC,IAAI,mBAAmB,CAAC,GAAG;YACzB,MAAM,OAAO,IAAI,CAAC,EAAE;YACpB,IAAI,OAAO,SAAS,UAAU;gBAC5B,IAAI,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC;YACvB,OAAO;gBACL,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,qBAAqB,GAAG;gBAC/B,KAAK,KAAK;YACZ;QACF;QAEA,IAAI,iBAAiB,GAAG;YACtB,4DAA4D;YAC5D,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG;QACtC;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;;;CASC,GACD,SAAS,gBAAgB,MAAM,EAAE,UAAU;IACzC,IAAI,QAAQ,CAAC;IACb,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,gCAAgC,GAChC,IAAI;IAEJ,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,MAAM,QAAQ,MAAM,CAAC,MAAM;QAC3B,mBAAmB,GACnB,IAAI;QAEJ,IAAI,OAAO,UAAU,UAAU;YAC7B,QAAQ;QACV,OACE,OAAQ;YACN,KAAK,sOAAA,CAAA,QAAK,CAAC,cAAc;gBAAE;oBACzB,QAAQ,uOAAA,CAAA,SAAM,CAAC,EAAE;oBAEjB;gBACF;YAEA,KAAK,sOAAA,CAAA,QAAK,CAAC,QAAQ;gBAAE;oBACnB,QAAQ,uOAAA,CAAA,SAAM,CAAC,EAAE;oBAEjB;gBACF;YAEA,KAAK,sOAAA,CAAA,QAAK,CAAC,sBAAsB;gBAAE;oBACjC,QAAQ,uOAAA,CAAA,SAAM,CAAC,EAAE,GAAG,uOAAA,CAAA,SAAM,CAAC,EAAE;oBAE7B;gBACF;YAEA,KAAK,sOAAA,CAAA,QAAK,CAAC,aAAa;gBAAE;oBACxB,QAAQ,aAAa,uOAAA,CAAA,SAAM,CAAC,KAAK,GAAG,uOAAA,CAAA,SAAM,CAAC,EAAE;oBAE7C;gBACF;YAEA,KAAK,sOAAA,CAAA,QAAK,CAAC,YAAY;gBAAE;oBACvB,IAAI,CAAC,cAAc,OAAO;oBAC1B,QAAQ,uOAAA,CAAA,SAAM,CAAC,KAAK;oBAEpB;gBACF;YAEA;gBAAS;oBACP,CAAA,GAAA,kMAAA,CAAA,KAAM,AAAD,EAAE,OAAO,UAAU,UAAU;oBAClC,wCAAwC;oBACxC,QAAQ,OAAO,YAAY,CAAC;gBAC9B;QACF;QAEF,QAAQ,UAAU,sOAAA,CAAA,QAAK,CAAC,aAAa;QACrC,OAAO,IAAI,CAAC;IACd;IAEA,OAAO,OAAO,IAAI,CAAC;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/micromark%404.0.1/node_modules/micromark/dev/lib/parse.js"], "sourcesContent": ["/**\n * @import {\n *   Create,\n *   FullNormalizedExtension,\n *   InitialConstruct,\n *   ParseContext,\n *   ParseOptions\n * } from 'micromark-util-types'\n */\n\nimport {combineExtensions} from 'micromark-util-combine-extensions'\nimport {content} from './initialize/content.js'\nimport {document} from './initialize/document.js'\nimport {flow} from './initialize/flow.js'\nimport {string, text} from './initialize/text.js'\nimport * as defaultConstructs from './constructs.js'\nimport {createTokenizer} from './create-tokenizer.js'\n\n/**\n * @param {ParseOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ParseContext}\n *   Parser.\n */\nexport function parse(options) {\n  const settings = options || {}\n  const constructs = /** @type {FullNormalizedExtension} */ (\n    combineExtensions([defaultConstructs, ...(settings.extensions || [])])\n  )\n\n  /** @type {ParseContext} */\n  const parser = {\n    constructs,\n    content: create(content),\n    defined: [],\n    document: create(document),\n    flow: create(flow),\n    lazy: {},\n    string: create(string),\n    text: create(text)\n  }\n\n  return parser\n\n  /**\n   * @param {InitialConstruct} initial\n   *   Construct to start with.\n   * @returns {Create}\n   *   Create a tokenizer.\n   */\n  function create(initial) {\n    return creator\n    /** @type {Create} */\n    function creator(from) {\n      return createTokenizer(parser, initial, from)\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAQO,SAAS,MAAM,OAAO;IAC3B,MAAM,WAAW,WAAW,CAAC;IAC7B,MAAM,aACJ,CAAA,GAAA,6PAAA,CAAA,oBAAiB,AAAD,EAAE;QAAC;WAAuB,SAAS,UAAU,IAAI,EAAE;KAAE;IAGvE,yBAAyB,GACzB,MAAM,SAAS;QACb;QACA,SAAS,OAAO,yNAAA,CAAA,UAAO;QACvB,SAAS,EAAE;QACX,UAAU,OAAO,0NAAA,CAAA,WAAQ;QACzB,MAAM,OAAO,sNAAA,CAAA,OAAI;QACjB,MAAM,CAAC;QACP,QAAQ,OAAO,sNAAA,CAAA,SAAM;QACrB,MAAM,OAAO,sNAAA,CAAA,OAAI;IACnB;IAEA,OAAO;;IAEP;;;;;GAKC,GACD,SAAS,OAAO,OAAO;QACrB,OAAO;;QACP,mBAAmB,GACnB,SAAS,QAAQ,IAAI;YACnB,OAAO,CAAA,GAAA,uNAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,SAAS;QAC1C;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/micromark%404.0.1/node_modules/micromark/dev/lib/postprocess.js"], "sourcesContent": ["/**\n * @import {Event} from 'micromark-util-types'\n */\n\nimport {subtokenize} from 'micromark-util-subtokenize'\n\n/**\n * @param {Array<Event>} events\n *   Events.\n * @returns {Array<Event>}\n *   Events.\n */\nexport function postprocess(events) {\n  while (!subtokenize(events)) {\n    // Empty\n  }\n\n  return events\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;;AAQO,SAAS,YAAY,MAAM;IAChC,MAAO,CAAC,CAAA,GAAA,gQAAA,CAAA,cAAW,AAAD,EAAE,QAAS;IAC3B,QAAQ;IACV;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/micromark%404.0.1/node_modules/micromark/dev/lib/preprocess.js"], "sourcesContent": ["/**\n * @import {Chunk, Code, Encoding, Value} from 'micromark-util-types'\n */\n\n/**\n * @callback Preprocessor\n *   Preprocess a value.\n * @param {Value} value\n *   Value.\n * @param {Encoding | null | undefined} [encoding]\n *   Encoding when `value` is a typed array (optional).\n * @param {boolean | null | undefined} [end=false]\n *   Whether this is the last chunk (default: `false`).\n * @returns {Array<Chunk>}\n *   Chunks.\n */\n\nimport {codes, constants} from 'micromark-util-symbol'\n\nconst search = /[\\0\\t\\n\\r]/g\n\n/**\n * @returns {Preprocessor}\n *   Preprocess a value.\n */\nexport function preprocess() {\n  let column = 1\n  let buffer = ''\n  /** @type {boolean | undefined} */\n  let start = true\n  /** @type {boolean | undefined} */\n  let atCarriageReturn\n\n  return preprocessor\n\n  /** @type {Preprocessor} */\n  // eslint-disable-next-line complexity\n  function preprocessor(value, encoding, end) {\n    /** @type {Array<Chunk>} */\n    const chunks = []\n    /** @type {RegExpMatchArray | null} */\n    let match\n    /** @type {number} */\n    let next\n    /** @type {number} */\n    let startPosition\n    /** @type {number} */\n    let endPosition\n    /** @type {Code} */\n    let code\n\n    value =\n      buffer +\n      (typeof value === 'string'\n        ? value.toString()\n        : new TextDecoder(encoding || undefined).decode(value))\n\n    startPosition = 0\n    buffer = ''\n\n    if (start) {\n      // To do: `markdown-rs` actually parses BOMs (byte order mark).\n      if (value.charCodeAt(0) === codes.byteOrderMarker) {\n        startPosition++\n      }\n\n      start = undefined\n    }\n\n    while (startPosition < value.length) {\n      search.lastIndex = startPosition\n      match = search.exec(value)\n      endPosition =\n        match && match.index !== undefined ? match.index : value.length\n      code = value.charCodeAt(endPosition)\n\n      if (!match) {\n        buffer = value.slice(startPosition)\n        break\n      }\n\n      if (\n        code === codes.lf &&\n        startPosition === endPosition &&\n        atCarriageReturn\n      ) {\n        chunks.push(codes.carriageReturnLineFeed)\n        atCarriageReturn = undefined\n      } else {\n        if (atCarriageReturn) {\n          chunks.push(codes.carriageReturn)\n          atCarriageReturn = undefined\n        }\n\n        if (startPosition < endPosition) {\n          chunks.push(value.slice(startPosition, endPosition))\n          column += endPosition - startPosition\n        }\n\n        switch (code) {\n          case codes.nul: {\n            chunks.push(codes.replacementCharacter)\n            column++\n\n            break\n          }\n\n          case codes.ht: {\n            next = Math.ceil(column / constants.tabSize) * constants.tabSize\n            chunks.push(codes.horizontalTab)\n            while (column++ < next) chunks.push(codes.virtualSpace)\n\n            break\n          }\n\n          case codes.lf: {\n            chunks.push(codes.lineFeed)\n            column = 1\n\n            break\n          }\n\n          default: {\n            atCarriageReturn = true\n            column = 1\n          }\n        }\n      }\n\n      startPosition = endPosition + 1\n    }\n\n    if (end) {\n      if (atCarriageReturn) chunks.push(codes.carriageReturn)\n      if (buffer) chunks.push(buffer)\n      chunks.push(codes.eof)\n    }\n\n    return chunks\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;;;;;;CAWC;;;AAED;AAAA;;AAEA,MAAM,SAAS;AAMR,SAAS;IACd,IAAI,SAAS;IACb,IAAI,SAAS;IACb,gCAAgC,GAChC,IAAI,QAAQ;IACZ,gCAAgC,GAChC,IAAI;IAEJ,OAAO;;IAEP,yBAAyB,GACzB,sCAAsC;IACtC,SAAS,aAAa,KAAK,EAAE,QAAQ,EAAE,GAAG;QACxC,yBAAyB,GACzB,MAAM,SAAS,EAAE;QACjB,oCAAoC,GACpC,IAAI;QACJ,mBAAmB,GACnB,IAAI;QACJ,mBAAmB,GACnB,IAAI;QACJ,mBAAmB,GACnB,IAAI;QACJ,iBAAiB,GACjB,IAAI;QAEJ,QACE,SACA,CAAC,OAAO,UAAU,WACd,MAAM,QAAQ,KACd,IAAI,YAAY,YAAY,WAAW,MAAM,CAAC,MAAM;QAE1D,gBAAgB;QAChB,SAAS;QAET,IAAI,OAAO;YACT,+DAA+D;YAC/D,IAAI,MAAM,UAAU,CAAC,OAAO,sOAAA,CAAA,QAAK,CAAC,eAAe,EAAE;gBACjD;YACF;YAEA,QAAQ;QACV;QAEA,MAAO,gBAAgB,MAAM,MAAM,CAAE;YACnC,OAAO,SAAS,GAAG;YACnB,QAAQ,OAAO,IAAI,CAAC;YACpB,cACE,SAAS,MAAM,KAAK,KAAK,YAAY,MAAM,KAAK,GAAG,MAAM,MAAM;YACjE,OAAO,MAAM,UAAU,CAAC;YAExB,IAAI,CAAC,OAAO;gBACV,SAAS,MAAM,KAAK,CAAC;gBACrB;YACF;YAEA,IACE,SAAS,sOAAA,CAAA,QAAK,CAAC,EAAE,IACjB,kBAAkB,eAClB,kBACA;gBACA,OAAO,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,sBAAsB;gBACxC,mBAAmB;YACrB,OAAO;gBACL,IAAI,kBAAkB;oBACpB,OAAO,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,cAAc;oBAChC,mBAAmB;gBACrB;gBAEA,IAAI,gBAAgB,aAAa;oBAC/B,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,eAAe;oBACvC,UAAU,cAAc;gBAC1B;gBAEA,OAAQ;oBACN,KAAK,sOAAA,CAAA,QAAK,CAAC,GAAG;wBAAE;4BACd,OAAO,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,oBAAoB;4BACtC;4BAEA;wBACF;oBAEA,KAAK,sOAAA,CAAA,QAAK,CAAC,EAAE;wBAAE;4BACb,OAAO,KAAK,IAAI,CAAC,SAAS,0OAAA,CAAA,YAAS,CAAC,OAAO,IAAI,0OAAA,CAAA,YAAS,CAAC,OAAO;4BAChE,OAAO,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,aAAa;4BAC/B,MAAO,WAAW,KAAM,OAAO,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,YAAY;4BAEtD;wBACF;oBAEA,KAAK,sOAAA,CAAA,QAAK,CAAC,EAAE;wBAAE;4BACb,OAAO,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,QAAQ;4BAC1B,SAAS;4BAET;wBACF;oBAEA;wBAAS;4BACP,mBAAmB;4BACnB,SAAS;wBACX;gBACF;YACF;YAEA,gBAAgB,cAAc;QAChC;QAEA,IAAI,KAAK;YACP,IAAI,kBAAkB,OAAO,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,cAAc;YACtD,IAAI,QAAQ,OAAO,IAAI,CAAC;YACxB,OAAO,IAAI,CAAC,sOAAA,CAAA,QAAK,CAAC,GAAG;QACvB;QAEA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}]}