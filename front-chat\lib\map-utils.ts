//@ts-nocheck
import { LayerConfig, LayerInfo, UseMapReturn } from "@geon-map/odf";

// 하이라이트 레이어 관리를 위한 전역 변수
let HIGHLIGHT_LAYER_ID: string | null = null;

/**
 * 지도에 포인트를 하이라이트하고 이동하는 함수 (레거시 패턴 적용)
 */
export const highlightPointOnMap = (
  mapState: UseMapReturn,
  longitude: number,
  latitude: number,
  geometry?: string,
  zoomLevel: number = 16,
  options?: {
    clearPrevious?: boolean;
    useZIndexUp?: boolean;
    customStyle?: any;
    fitPadding?: number;
  }
): boolean => {
  if (!mapState?.map) {
    return false;
  }

  try {
    const odf = (window as any).odf;
    if (!odf) {
      return false;
    }

    // 하이라이트 레이어 재사용 또는 생성 (레거시 패턴)
    let highlightLayer = HIGHLIGHT_LAYER_ID
      ? mapState.map.findLayer(HIGHLIGHT_LAYER_ID)
      : null;

    if (!highlightLayer) {
      highlightLayer = odf.LayerFactory.produce("empty", {});
      highlightLayer.setMap(mapState.map);
      HIGHLIGHT_LAYER_ID = highlightLayer.getODFId();

      // 기본 스타일 적용
      const defaultStyle = options?.customStyle || {
        image: {
          circle: {
            radius: 10,
            fill: { color: [255, 255, 255, 0.4] },
            stroke: { color: [237, 116, 116, 0.82], width: 2 },
          },
        },
        fill: { color: [255, 255, 255, 0.4] },
        stroke: { color: [237, 116, 116, 0.82], width: 2 },
      };
      highlightLayer.setStyle(odf.StyleFactory.produce(defaultStyle));
    }

    // 기존 하이라이트 제거 (옵션)
    if (options?.clearPrevious !== false) {
      highlightLayer.clear();
    }

    // 지오메트리가 있는 경우 사용, 없으면 포인트 생성
    let wkt = geometry;
    if (!wkt) {
      wkt = `POINT(${longitude} ${latitude})`;
    }

    // WKT에서 피처 생성
    let feature = odf.FeatureFactory.fromWKT(wkt);

    // 좌표계 변환 처리 (레거시 패턴)
    const mapProjectionCode = mapState.map.getView().getProjection().getCode();
    const targetSrid = mapProjectionCode.replace(/[^0-9]/gi, "");
    if (targetSrid !== "4326") {
      feature = mapState.map.getProjection().projectGeom(feature, "4326");
    }

    highlightLayer.addFeature(feature);

    // Z-Index 관리 (레거시 패턴)
    if (options?.useZIndexUp) {
      const maxZIndex = mapState.map.getMaxZIndex();
      mapState.map.setZIndex(HIGHLIGHT_LAYER_ID, maxZIndex + 1);
    }

    // 지오메트리 타입별 줌 레벨 조정 (레거시 패턴 개선)
    const geometryType = feature.getGeometry().getType();
    if (geometry) {
      // 복잡한 지오메트리는 fit 사용
      const padding = options?.fitPadding || 1000;
      highlightLayer.fit();
    }
    console.log(mapState.map.getZoom());
    mapState.map.setZoom(
      mapState.map.getZoom() > 16 ? mapState.map.getZoom() - 4 :  mapState.map.getZoom()
    ); //바로 e맵 영상 최대 레벨 19

    return true;
  } catch (error) {
    console.error("지도 하이라이트 중 오류:", error);
    return false;
  }
};

/**
 * 지도에 경로를 표시하는 함수 (길찾기 예시 참고하여 개선)
 */
export const showRouteOnMap = (
  mapState: UseMapReturn,
  origin: { x: number; y: number },
  destination: { x: number; y: number },
  waypoints?: Array<{ x: number; y: number }>,
  routeData?: any // 실제 경로 데이터 (sections 포함)
): boolean => {
  if (!mapState?.map) {
    return false;
  }

  try {
    const odf = (window as any).odf;
    if (!odf) {
      return false;
    }

    // 하이라이트 레이어 재사용 또는 생성
    let highlightLayer = HIGHLIGHT_LAYER_ID
      ? mapState.map.findLayer(HIGHLIGHT_LAYER_ID)
      : null;

    if (!highlightLayer) {
      highlightLayer = odf.LayerFactory.produce("empty", {});
      highlightLayer.setMap(mapState.map);
      HIGHLIGHT_LAYER_ID = highlightLayer.getODFId();
    }

    // 기존 하이라이트 제거
    highlightLayer.clear();

    const projection = mapState.map.getProjection();

    // 실제 경로 라인 그리기 (제공된 예제 참고)
    if (
      routeData &&
      routeData.routes &&
      routeData.routes[0] &&
      routeData.routes[0].sections
    ) {
      const coordinates: number[][] = [];

      // 첫 번째 섹션의 모든 도로를 순회
      routeData.routes[0].sections.forEach((section: any) => {
        if (section.roads) {
          section.roads.forEach((router: { vertexes: any[] }) => {
            router.vertexes.forEach((vertex, index) => {
              if (index % 2 === 0) {
                coordinates.push(
                  projection.project(
                    [router.vertexes[index], router.vertexes[index + 1]],
                    "4326"
                  )
                );
              }
            });
          });
        }
      });

      // GeoJSON LineString으로 경로 표시
      if (coordinates.length > 1) {
        const lineWKT = `LINESTRING(${coordinates
          .map((coord) => `${coord[0]} ${coord[1]}`)
          .join(", ")})`;
        let lineFeature = odf.FeatureFactory.fromWKT(lineWKT);
        highlightLayer.addFeature(lineFeature);
      }
    }

    // 출발지 표시 (파란색 원)
    const originWKT = `POINT(${origin.x} ${origin.y})`;
    let originFeature = odf.FeatureFactory.fromWKT(originWKT);
    originFeature = projection.projectGeom(originFeature, "4326");
    highlightLayer.addFeature(originFeature);

    // 도착지 표시 (빨간색 원)
    const destWKT = `POINT(${destination.x} ${destination.y})`;
    let destFeature = odf.FeatureFactory.fromWKT(destWKT);
    destFeature = projection.projectGeom(destFeature, "4326");
    highlightLayer.addFeature(destFeature);

    // 경유지가 있는 경우 표시
    if (waypoints && waypoints.length > 0) {
      waypoints.forEach((waypoint) => {
        const waypointWKT = `POINT(${waypoint.x} ${waypoint.y})`;
        let waypointFeature = odf.FeatureFactory.fromWKT(waypointWKT);
        waypointFeature = projection.projectGeom(waypointFeature, "4326");
        highlightLayer.addFeature(waypointFeature);
      });
    }

    // 전체 경로가 보이도록 fit
    highlightLayer.fit();

    return true;
  } catch (error) {
    console.error("경로 표시 중 오류:", error);
    return false;
  }
};

/**
 * 지도에 레이어를 추가하는 함수
 */
export const addLayerToMap = async (
  mapState: UseMapReturn,
  layerInfo: LayerInfo,
  layerConfig: LayerConfig
): Promise<boolean> => {
  if (!mapState?.layer) {
    return false;
  }

  try {
    const addedLayer = await mapState.layer.add(layerInfo, layerConfig);
    return !!addedLayer;
  } catch (error) {
    console.error("레이어 추가 중 오류:", error);
    return false;
  }
};

/**
 * 거리에 따른 적절한 줌 레벨 계산
 */
export const calculateZoomLevel = (distance: number): number => {
  if (distance > 50000) return 8;
  if (distance > 20000) return 10;
  if (distance > 5000) return 12;
  if (distance > 1000) return 14;
  return 16;
};

/**
 * 지도 하이라이트 레이어 초기화 (레거시 패턴 적용)
 */
export const clearMapHighlights = (mapState: UseMapReturn): boolean => {
  if (!mapState?.map) {
    return false;
  }

  try {
    if (HIGHLIGHT_LAYER_ID) {
      const highlightLayer = mapState.map.findLayer(HIGHLIGHT_LAYER_ID);
      if (highlightLayer) {
        highlightLayer.clear();
      }
    }
    return true;
  } catch (error) {
    console.error("하이라이트 초기화 중 오류:", error);
    return false;
  }
};

/**
 * 하이라이트 레이어 완전 제거 (레거시 패턴)
 */
export const removeHighlightLayer = (mapState: UseMapReturn): boolean => {
  if (!mapState?.map) {
    return false;
  }

  try {
    if (HIGHLIGHT_LAYER_ID) {
      const highlightLayer = mapState.map.findLayer(HIGHLIGHT_LAYER_ID);
      if (highlightLayer) {
        highlightLayer.removeMap(mapState.map);
        HIGHLIGHT_LAYER_ID = null;
      }
    }
    return true;
  } catch (error) {
    console.error("하이라이트 레이어 제거 중 오류:", error);
    return false;
  }
};

// UseMapReturn은 @geon-map/odf에서 import하여 재export
export type { UseMapReturn };
