# 지도 라이브러리 문서

## 기능 : 분할지도 컨트롤 생성

**설명**: 기본 지도를 생성한 후 분할지도 컨트롤(DivideMapControl)을 추가합니다. 
바로e맵 색각지도, 백지도, 항공지도, 기본 지도를 활용하여 분할지도의 배경 지도를 설정합니다. 

**입력**:
- `map` (Map): 지도 객체

**출력**:
- 생성된 분할지도 컨트롤 객체(divideMapControl).


**코드 예제**:
```javascript
//분할지도 컨트롤 생성
var divideMapControl = new odf.DivideMapControl({
  //2분할지도 정의
  dualMap : [
    {
      mapOption : {
        basemap : {baroEMap : ['eMapAIR']},
      },
    },
  ],
  //3분할지도 정의
  threepleMap: [
    {mapOption: {
        basemap: {baroEMap:['eMapColor']},
      },
    },
    { mapOption: {
        basemap:{baroEMap: ['eMapWhite']},
      },
    },
  ],
  //4분할지도 정의
  quadMap: [
    {
      mapOption: {
        basemap: {baroEMap : ['eMapAIR']},
      },
    },
    {
      mapOption: {
        basemap: {baroEMap : ['eMapColor']} ,
      },
    },
    {
      mapOption: {
        basemap: {baroEMap : ['eMapWhite']} ,
      },
    },
  ],
});
//생성한 분할지도 컨트롤을 지도 객체에 추가
divideMapControl.setMap(map);
```
