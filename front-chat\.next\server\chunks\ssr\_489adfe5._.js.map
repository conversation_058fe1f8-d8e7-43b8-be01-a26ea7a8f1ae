{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/hooks/use-mobile.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,8SAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,uVAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,uVAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qXACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,uVAAC,4QAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,4QAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Sheet = SheetPrimitive.Root\r\n\r\nconst SheetTrigger = SheetPrimitive.Trigger\r\n\r\nconst SheetClose = SheetPrimitive.Close\r\n\r\nconst SheetPortal = SheetPrimitive.Portal\r\n\r\nconst SheetOverlay = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n))\r\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\r\n\r\nconst sheetVariants = cva(\r\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n  {\r\n    variants: {\r\n      side: {\r\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\r\n        bottom:\r\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\r\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\r\n        right:\r\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      side: \"right\",\r\n    },\r\n  }\r\n)\r\n\r\ninterface SheetContentProps\r\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\r\n    VariantProps<typeof sheetVariants> {}\r\n\r\nconst SheetContent = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Content>,\r\n  SheetContentProps\r\n>(({ side = \"right\", className, children, ...props }, ref) => (\r\n  <SheetPortal>\r\n    <SheetOverlay />\r\n    <SheetPrimitive.Content\r\n      ref={ref}\r\n      className={cn(sheetVariants({ side }), className)}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </SheetPrimitive.Close>\r\n    </SheetPrimitive.Content>\r\n  </SheetPortal>\r\n))\r\nSheetContent.displayName = SheetPrimitive.Content.displayName\r\n\r\nconst SheetHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-2 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nSheetHeader.displayName = \"SheetHeader\"\r\n\r\nconst SheetFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nSheetFooter.displayName = \"SheetFooter\"\r\n\r\nconst SheetTitle = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSheetTitle.displayName = SheetPrimitive.Title.displayName\r\n\r\nconst SheetDescription = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSheetDescription.displayName = SheetPrimitive.Description.displayName\r\n\r\nexport {\r\n  Sheet,\r\n  SheetPortal,\r\n  SheetOverlay,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,QAAQ,+QAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,+QAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,+QAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,+QAAA,CAAA,SAAqB;AAEzC,MAAM,6BAAe,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,+QAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,aAAa,WAAW,GAAG,+QAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,uVAAC;;0BACC,uVAAC;;;;;0BACD,uVAAC,+QAAA,CAAA,UAAsB;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;oBAER;kCACD,uVAAC,+QAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,uVAAC,gRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,uVAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,+QAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,uVAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,uVAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,+QAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,+QAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,+QAAA,CAAA,cAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,+QAAA,CAAA,cAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,uVAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { VariantProps, cva } from \"class-variance-authority\";\r\nimport { PanelLeft } from \"lucide-react\";\r\n\r\nimport { useIsMobile } from \"@/lib/hooks/use-mobile\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar:state\";\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\r\nconst SIDEBAR_WIDTH = \"16rem\";\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\";\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\";\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\";\r\n\r\ntype SidebarContext = {\r\n  state: \"expanded\" | \"collapsed\";\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n  openMobile: boolean;\r\n  setOpenMobile: (open: boolean) => void;\r\n  isMobile: boolean;\r\n  toggleSidebar: () => void;\r\n};\r\n\r\nconst SidebarContext = React.createContext<SidebarContext | null>(null);\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext);\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\");\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\nconst SidebarProvider = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & {\r\n    defaultOpen?: boolean;\r\n    open?: boolean;\r\n    onOpenChange?: (open: boolean) => void;\r\n  }\r\n>(\r\n  (\r\n    {\r\n      defaultOpen = true,\r\n      open: openProp,\r\n      onOpenChange: setOpenProp,\r\n      className,\r\n      style,\r\n      children,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const isMobile = useIsMobile();\r\n    const [openMobile, setOpenMobile] = React.useState(false);\r\n\r\n    // This is the internal state of the sidebar.\r\n    // We use openProp and setOpenProp for control from outside the component.\r\n    const [_open, _setOpen] = React.useState(defaultOpen);\r\n    const open = openProp ?? _open;\r\n    const setOpen = React.useCallback(\r\n      (value: boolean | ((value: boolean) => boolean)) => {\r\n        if (setOpenProp) {\r\n          return setOpenProp?.(\r\n            typeof value === \"function\" ? value(open) : value\r\n          );\r\n        }\r\n\r\n        _setOpen(value);\r\n        // This sets the cookie to keep the sidebar state.\r\n        document.cookie = `${SIDEBAR_COOKIE_NAME}=${open}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\r\n      },\r\n      [setOpenProp, open]\r\n    );\r\n\r\n    // Helper to toggle the sidebar.\r\n    const toggleSidebar = React.useCallback(() => {\r\n      return isMobile\r\n        ? setOpenMobile((open) => !open)\r\n        : setOpen((open) => !open);\r\n    }, [isMobile, setOpen, setOpenMobile]);\r\n\r\n    // Adds a keyboard shortcut to toggle the sidebar.\r\n    React.useEffect(() => {\r\n      const handleKeyDown = (event: KeyboardEvent) => {\r\n        if (\r\n          event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n          (event.metaKey || event.ctrlKey)\r\n        ) {\r\n          event.preventDefault();\r\n          toggleSidebar();\r\n        }\r\n      };\r\n\r\n      window.addEventListener(\"keydown\", handleKeyDown);\r\n      return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n    }, [toggleSidebar]);\r\n\r\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n    // This makes it easier to style the sidebar with Tailwind classes.\r\n    const state = open ? \"expanded\" : \"collapsed\";\r\n\r\n    const contextValue = React.useMemo<SidebarContext>(\r\n      () => ({\r\n        state,\r\n        open,\r\n        setOpen,\r\n        isMobile,\r\n        openMobile,\r\n        setOpenMobile,\r\n        toggleSidebar,\r\n      }),\r\n      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n    );\r\n\r\n    return (\r\n      <SidebarContext.Provider value={contextValue}>\r\n        <TooltipProvider delayDuration={0}>\r\n          <div\r\n            style={\r\n              {\r\n                \"--sidebar-width\": SIDEBAR_WIDTH,\r\n                \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n                ...style,\r\n              } as React.CSSProperties\r\n            }\r\n            className={cn(\r\n              \"group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar\",\r\n              className\r\n            )}\r\n            ref={ref}\r\n            {...props}\r\n          >\r\n            {children}\r\n          </div>\r\n        </TooltipProvider>\r\n      </SidebarContext.Provider>\r\n    );\r\n  }\r\n);\r\nSidebarProvider.displayName = \"SidebarProvider\";\r\n\r\nconst Sidebar = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & {\r\n    side?: \"left\" | \"right\";\r\n    variant?: \"sidebar\" | \"floating\" | \"inset\";\r\n    collapsible?: \"offcanvas\" | \"icon\" | \"none\";\r\n  }\r\n>(\r\n  (\r\n    {\r\n      side = \"left\",\r\n      variant = \"sidebar\",\r\n      collapsible = \"offcanvas\",\r\n      className,\r\n      children,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\r\n\r\n    if (collapsible === \"none\") {\r\n      return (\r\n        <div\r\n          className={cn(\r\n            \"flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground\",\r\n            className\r\n          )}\r\n          ref={ref}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      );\r\n    }\r\n\r\n    if (isMobile) {\r\n      return (\r\n        <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n          <SheetContent\r\n            data-sidebar=\"sidebar\"\r\n            data-mobile=\"true\"\r\n            className=\"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden\"\r\n            style={\r\n              {\r\n                \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n              } as React.CSSProperties\r\n            }\r\n            side={side}\r\n          >\r\n            <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n          </SheetContent>\r\n        </Sheet>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <div\r\n        ref={ref}\r\n        className=\"group peer hidden md:block text-sidebar-foreground\"\r\n        data-state={state}\r\n        data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n        data-variant={variant}\r\n        data-side={side}\r\n      >\r\n        {/* This is what handles the sidebar gap on desktop */}\r\n        <div\r\n          className={cn(\r\n            \"duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear\",\r\n            \"group-data-[collapsible=offcanvas]:w-0\",\r\n            \"group-data-[side=right]:rotate-180\",\r\n            variant === \"floating\" || variant === \"inset\"\r\n              ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]\"\r\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon]\"\r\n          )}\r\n        />\r\n        <div\r\n          className={cn(\r\n            \"duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex\",\r\n            side === \"left\"\r\n              ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n              : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n            // Adjust the padding for floating and inset variants.\r\n            variant === \"floating\" || variant === \"inset\"\r\n              ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]\"\r\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          <div\r\n            data-sidebar=\"sidebar\"\r\n            className=\"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow\"\r\n          >\r\n            {children}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n);\r\nSidebar.displayName = \"Sidebar\";\r\n\r\nconst SidebarTrigger = React.forwardRef<\r\n  React.ElementRef<typeof Button>,\r\n  React.ComponentProps<typeof Button>\r\n>(({ className, onClick, ...props }, ref) => {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <Button\r\n      ref={ref}\r\n      data-sidebar=\"trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"h-7 w-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event);\r\n        toggleSidebar();\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeft />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  );\r\n});\r\nSidebarTrigger.displayName = \"SidebarTrigger\";\r\n\r\nconst SidebarRail = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\">\r\n>(({ className, ...props }, ref) => {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <button\r\n      ref={ref}\r\n      data-sidebar=\"rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex\",\r\n        \"[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nSidebarRail.displayName = \"SidebarRail\";\r\n\r\nconst SidebarInset = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"main\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <main\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative flex min-h-svh flex-1 flex-col bg-background\",\r\n        \"peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nSidebarInset.displayName = \"SidebarInset\";\r\n\r\nconst SidebarInput = React.forwardRef<\r\n  React.ElementRef<typeof Input>,\r\n  React.ComponentProps<typeof Input>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <Input\r\n      ref={ref}\r\n      data-sidebar=\"input\"\r\n      className={cn(\r\n        \"h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nSidebarInput.displayName = \"SidebarInput\";\r\n\r\nconst SidebarHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nSidebarHeader.displayName = \"SidebarHeader\";\r\n\r\nconst SidebarFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nSidebarFooter.displayName = \"SidebarFooter\";\r\n\r\nconst SidebarSeparator = React.forwardRef<\r\n  React.ElementRef<typeof Separator>,\r\n  React.ComponentProps<typeof Separator>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <Separator\r\n      ref={ref}\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"mx-2 w-auto bg-sidebar-border\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nSidebarSeparator.displayName = \"SidebarSeparator\";\r\n\r\nconst SidebarContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nSidebarContent.displayName = \"SidebarContent\";\r\n\r\nconst SidebarGroup = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nSidebarGroup.displayName = \"SidebarGroup\";\r\n\r\nconst SidebarGroupLabel = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & { asChild?: boolean }\r\n>(({ className, asChild = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"div\";\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nSidebarGroupLabel.displayName = \"SidebarGroupLabel\";\r\n\r\nconst SidebarGroupAction = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\"> & { asChild?: boolean }\r\n>(({ className, asChild = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 after:md:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nSidebarGroupAction.displayName = \"SidebarGroupAction\";\r\n\r\nconst SidebarGroupContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    data-sidebar=\"group-content\"\r\n    className={cn(\"w-full text-sm\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSidebarGroupContent.displayName = \"SidebarGroupContent\";\r\n\r\nconst SidebarMenu = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.ComponentProps<\"ul\">\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    ref={ref}\r\n    data-sidebar=\"menu\"\r\n    className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSidebarMenu.displayName = \"SidebarMenu\";\r\n\r\nconst SidebarMenuItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.ComponentProps<\"li\">\r\n>(({ className, ...props }, ref) => (\r\n  <li\r\n    ref={ref}\r\n    data-sidebar=\"menu-item\"\r\n    className={cn(\"group/menu-item relative\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSidebarMenuItem.displayName = \"SidebarMenuItem\";\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:!p-0\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nconst SidebarMenuButton = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\"> & {\r\n    asChild?: boolean;\r\n    isActive?: boolean;\r\n    tooltip?: string | React.ComponentProps<typeof TooltipContent>;\r\n  } & VariantProps<typeof sidebarMenuButtonVariants>\r\n>(\r\n  (\r\n    {\r\n      asChild = false,\r\n      isActive = false,\r\n      variant = \"default\",\r\n      size = \"default\",\r\n      tooltip,\r\n      className,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    const { isMobile, state } = useSidebar();\r\n\r\n    const button = (\r\n      <Comp\r\n        ref={ref}\r\n        data-sidebar=\"menu-button\"\r\n        data-size={size}\r\n        data-active={isActive}\r\n        className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n        {...props}\r\n      />\r\n    );\r\n\r\n    if (!tooltip) {\r\n      return button;\r\n    }\r\n\r\n    if (typeof tooltip === \"string\") {\r\n      tooltip = {\r\n        children: tooltip,\r\n      };\r\n    }\r\n\r\n    return (\r\n      <Tooltip>\r\n        <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n        <TooltipContent\r\n          side=\"right\"\r\n          align=\"center\"\r\n          hidden={state !== \"collapsed\" || isMobile}\r\n          {...tooltip}\r\n        />\r\n      </Tooltip>\r\n    );\r\n  }\r\n);\r\nSidebarMenuButton.displayName = \"SidebarMenuButton\";\r\n\r\nconst SidebarMenuAction = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\"> & {\r\n    asChild?: boolean;\r\n    showOnHover?: boolean;\r\n  }\r\n>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 after:md:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nSidebarMenuAction.displayName = \"SidebarMenuAction\";\r\n\r\nconst SidebarMenuBadge = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    data-sidebar=\"menu-badge\"\r\n    className={cn(\r\n      \"absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none\",\r\n      \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n      \"peer-data-[size=sm]/menu-button:top-1\",\r\n      \"peer-data-[size=default]/menu-button:top-1.5\",\r\n      \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n      \"group-data-[collapsible=icon]:hidden\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nSidebarMenuBadge.displayName = \"SidebarMenuBadge\";\r\n\r\nconst SidebarMenuSkeleton = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & {\r\n    showIcon?: boolean;\r\n  }\r\n>(({ className, showIcon = false, ...props }, ref) => {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`;\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"rounded-md h-8 flex gap-2 px-2 items-center\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 flex-1 max-w-[--skeleton-width]\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n});\r\nSidebarMenuSkeleton.displayName = \"SidebarMenuSkeleton\";\r\n\r\nconst SidebarMenuSub = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.ComponentProps<\"ul\">\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    ref={ref}\r\n    data-sidebar=\"menu-sub\"\r\n    className={cn(\r\n      \"mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5\",\r\n      \"group-data-[collapsible=icon]:hidden\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nSidebarMenuSub.displayName = \"SidebarMenuSub\";\r\n\r\nconst SidebarMenuSubItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.ComponentProps<\"li\">\r\n>(({ className, ...props }, ref) => (\r\n  <li\r\n    ref={ref}\r\n    className={cn(\"group/sub-menu-item relative\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSidebarMenuSubItem.displayName = \"SidebarMenuSubItem\";\r\n\r\nconst SidebarMenuSubAction = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\"> & {\r\n    asChild?: boolean;\r\n    showOnHover?: boolean;\r\n  }\r\n>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"menu-sub-action\"\r\n      className={cn(\r\n        \"absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0\",\r\n        \"text-sidebar-foreground outline-none ring-sidebar-ring transition-transform\",\r\n        \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2\",\r\n        \"peer-hover/menu-sub-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile\r\n        \"after:absolute after:-inset-2 after:md:hidden\",\r\n        // Size variants\r\n        \"peer-data-[size=sm]/menu-sub-button:top-1\",\r\n        \"peer-data-[size=default]/menu-sub-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-sub-button:top-2.5\",\r\n        // Collapsible state\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        // Hover visibility\r\n        showOnHover &&\r\n          \"group-focus-within/sub-menu-item:opacity-100 group-hover/sub-menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-sub-button:text-sidebar-accent-foreground md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nSidebarMenuSubAction.displayName = \"SidebarMenuSubAction\";\r\n\r\nconst SidebarMenuSubButton = React.forwardRef<\r\n  HTMLAnchorElement,\r\n  React.ComponentProps<\"a\"> & {\r\n    asChild?: boolean;\r\n    size?: \"sm\" | \"md\";\r\n    isActive?: boolean;\r\n  }\r\n>(({ asChild = false, size = \"md\", isActive, className, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"a\";\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nSidebarMenuSubButton.displayName = \"SidebarMenuSubButton\";\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarMenuSubAction,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;AAqBA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,8SAAA,CAAA,gBAAmB,AAAD,EAAyB;AAElE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,MAAM,gCAAkB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAQrC,CACE,EACE,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,IAAI,aAAa;YACf,OAAO,cACL,OAAO,UAAU,aAAa,MAAM,QAAQ;QAEhD;QAEA,SAAS;QACT,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,KAAK,kBAAkB,EAAE,wBAAwB;IAC/F,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,8SAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WACH,cAAc,CAAC,OAAS,CAAC,QACzB,QAAQ,CAAC,OAAS,CAAC;IACzB,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,8SAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,8SAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,uVAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,uVAAC,4HAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,uVAAC;gBACC,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;gBAEF,KAAK;gBACJ,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEF,gBAAgB,WAAW,GAAG;AAE9B,MAAM,wBAAU,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAQ7B,CACE,EACE,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,uVAAC;YACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAEF,KAAK;YACJ,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,uVAAC,0HAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,uVAAC,0HAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;0BAEN,cAAA,uVAAC;oBAAI,WAAU;8BAA+B;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,uVAAC;QACC,KAAK;QACL,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;;0BAGX,uVAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iGACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,yFACA;;;;;;0BAGR,uVAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,kGACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,uVAAC;oBACC,gBAAa;oBACb,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEF,QAAQ,WAAW,GAAG;AAEtB,MAAM,+BAAiB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,uVAAC,2HAAA,CAAA,SAAM;QACL,KAAK;QACL,gBAAa;QACb,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,WAAW;QACzB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,uVAAC,oSAAA,CAAA,YAAS;;;;;0BACV,uVAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,4BAAc,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mPACA,8EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,6BAAe,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA,gRACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,6BAAe,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,uVAAC,0HAAA,CAAA,QAAK;QACJ,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,8BAAgB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,8BAAgB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,uVAAC,8HAAA,CAAA,YAAS;QACR,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AACA,iBAAiB,WAAW,GAAG;AAE/B,MAAM,+BAAiB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,6BAAe,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,kCAAoB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sOACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,mCAAqB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,mBAAmB,WAAW,GAAG;AAEjC,MAAM,oCAAsB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGb,oBAAoB,WAAW,GAAG;AAElC,MAAM,4BAAc,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAA4B,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,kCAAoB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAQvC,CACE,EACE,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OACJ,EACD;IAEA,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,uVAAC,4HAAA,CAAA,UAAO;;0BACN,uVAAC,4HAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,uVAAC,4HAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;AAEF,kBAAkB,WAAW,GAAG;AAEhC,MAAM,kCAAoB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAMvC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,cAAc,KAAK,EAAE,GAAG,OAAO,EAAE;IAChE,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG;AAE/B,MAAM,oCAAsB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAKzC,CAAC,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,GAAG,OAAO,EAAE;IAC5C,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,8SAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,uVAAC,6HAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,uVAAC,6HAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AACA,oBAAoB,WAAW,GAAG;AAElC,MAAM,+BAAiB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,mCAAqB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGb,mBAAmB,WAAW,GAAG;AAEjC,MAAM,qCAAuB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAM1C,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,cAAc,KAAK,EAAE,GAAG,OAAO,EAAE;IAChE,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8FACA,+EACA,qFACA,6FACA,iDAAiD;QACjD,iDACA,gBAAgB;QAChB,6CACA,oDACA,+CACA,oBAAoB;QACpB,wCACA,mBAAmB;QACnB,eACE,wMACF;QAED,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG;AAEnC,MAAM,qCAAuB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAO1C,CAAC,EAAE,UAAU,KAAK,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAClE,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+eACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n))\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n))\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n))\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n))\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,mRAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,mRAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,mRAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,mRAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,mRAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,mRAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,uVAAC,mRAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,uVAAC,0SAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,mRAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,mRAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,mRAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,uVAAC,mRAAA,CAAA,SAA4B;kBAC3B,cAAA,uVAAC,mRAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,mRAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,uVAAC,mRAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,mRAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,uVAAC,mRAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,uVAAC;gBAAK,WAAU;0BACd,cAAA,uVAAC,mRAAA,CAAA,gBAAmC;8BAClC,cAAA,uVAAC,wRAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,mRAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,uVAAC,mRAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,uVAAC;gBAAK,WAAU;0BACd,cAAA,uVAAC,mRAAA,CAAA,gBAAmC;8BAClC,cAAA,uVAAC,0RAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,mRAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,uVAAC,mRAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,mRAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,mRAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,mRAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,uVAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-3 py-1.5 text-xs font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer select-none\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80 hover:scale-105 active:scale-95\",\r\n        secondary:\r\n          \"border-transparent bg-gray-100 text-gray-700 hover:bg-gray-200 hover:scale-105 active:scale-95 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 hover:scale-105 active:scale-95\",\r\n        outline: \"text-foreground border-border hover:bg-accent hover:text-accent-foreground hover:scale-105 active:scale-95\",\r\n        \"ai-active\":\r\n          \"border-transparent bg-gradient-to-r from-blue-600 to-indigo-600 text-white border border-blue-500 hover:from-blue-700 hover:to-indigo-700 hover:border-blue-600 hover:scale-105 active:scale-95 shadow-md dark:from-blue-700 dark:to-indigo-700 dark:text-white dark:border-blue-600 dark:hover:from-blue-800 dark:hover:to-indigo-800\",\r\n        \"nav-active\":\r\n          \"border-transparent bg-gradient-to-r from-emerald-600 to-green-600 text-white border border-emerald-500 hover:from-emerald-700 hover:to-green-700 hover:border-emerald-600 hover:scale-105 active:scale-95 shadow-md dark:from-emerald-700 dark:to-green-700 dark:text-white dark:border-emerald-600 dark:hover:from-emerald-800 dark:hover:to-green-800\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,2MACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,aACE;YACF,cACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,uVAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 1214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,+QAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,+QAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,+QAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,+QAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,uVAAC;;0BACC,uVAAC;;;;;0BACD,uVAAC,+QAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,uVAAC,+QAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,uVAAC,gRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,uVAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,uVAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,uVAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,+QAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,+QAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,+QAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,+QAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1346, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAvatar.displayName = AvatarPrimitive.Root.displayName\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,+QAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,+QAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,+QAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,+QAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,+QAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,+QAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1397, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/icons.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport * as React from 'react'\r\n\r\nimport { cn } from '@/lib/utils'\r\n\r\nfunction IconNextChat({\r\n  className,\r\n  inverted,\r\n  ...props\r\n}: React.ComponentProps<'svg'> & { inverted?: boolean }) {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <svg\r\n      viewBox=\"0 0 17 17\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <defs>\r\n        <linearGradient\r\n          id={`gradient-${id}-1`}\r\n          x1=\"10.6889\"\r\n          y1=\"10.3556\"\r\n          x2=\"13.8445\"\r\n          y2=\"14.2667\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor={inverted ? 'white' : 'black'} />\r\n          <stop\r\n            offset={1}\r\n            stopColor={inverted ? 'white' : 'black'}\r\n            stopOpacity={0}\r\n          />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id={`gradient-${id}-2`}\r\n          x1=\"11.7555\"\r\n          y1=\"4.8\"\r\n          x2=\"11.7376\"\r\n          y2=\"9.50002\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor={inverted ? 'white' : 'black'} />\r\n          <stop\r\n            offset={1}\r\n            stopColor={inverted ? 'white' : 'black'}\r\n            stopOpacity={0}\r\n          />\r\n        </linearGradient>\r\n      </defs>\r\n      <path\r\n        d=\"M1 16L2.58314 11.2506C1.83084 9.74642 1.63835 8.02363 2.04013 6.39052C2.4419 4.75741 3.41171 3.32057 4.776 2.33712C6.1403 1.35367 7.81003 0.887808 9.4864 1.02289C11.1628 1.15798 12.7364 1.8852 13.9256 3.07442C15.1148 4.26363 15.842 5.83723 15.9771 7.5136C16.1122 9.18997 15.6463 10.8597 14.6629 12.224C13.6794 13.5883 12.2426 14.5581 10.6095 14.9599C8.97637 15.3616 7.25358 15.1692 5.74942 14.4169L1 16Z\"\r\n        fill={inverted ? 'black' : 'white'}\r\n        stroke={inverted ? 'black' : 'white'}\r\n        strokeWidth={2}\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <mask\r\n        id=\"mask0_91_2047\"\r\n        style={{ maskType: 'alpha' }}\r\n        maskUnits=\"userSpaceOnUse\"\r\n        x={1}\r\n        y={0}\r\n        width={16}\r\n        height={16}\r\n      >\r\n        <circle cx={9} cy={8} r={8} fill={inverted ? 'black' : 'white'} />\r\n      </mask>\r\n      <g mask=\"url(#mask0_91_2047)\">\r\n        <circle cx={9} cy={8} r={8} fill={inverted ? 'black' : 'white'} />\r\n        <path\r\n          d=\"M14.2896 14.0018L7.146 4.8H5.80005V11.1973H6.87681V6.16743L13.4444 14.6529C13.7407 14.4545 14.0231 14.2369 14.2896 14.0018Z\"\r\n          fill={`url(#gradient-${id}-1)`}\r\n        />\r\n        <rect\r\n          x=\"11.2222\"\r\n          y=\"4.8\"\r\n          width=\"1.06667\"\r\n          height=\"6.4\"\r\n          fill={`url(#gradient-${id}-2)`}\r\n        />\r\n      </g>\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconOpenAI({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      fill=\"currentColor\"\r\n      viewBox=\"0 0 24 24\"\r\n      role=\"img\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <title>OpenAI icon</title>\r\n      <path d=\"M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconVercel({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      aria-label=\"Vercel logomark\"\r\n      role=\"img\"\r\n      viewBox=\"0 0 74 64\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path\r\n        d=\"M37.5896 0.25L74.5396 64.25H0.639648L37.5896 0.25Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconGitHub({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      role=\"img\"\r\n      viewBox=\"0 0 24 24\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <title>GitHub</title>\r\n      <path d=\"M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconSeparator({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      fill=\"none\"\r\n      shapeRendering=\"geometricPrecision\"\r\n      stroke=\"currentColor\"\r\n      strokeLinecap=\"round\"\r\n      strokeLinejoin=\"round\"\r\n      strokeWidth=\"1\"\r\n      viewBox=\"0 0 24 24\"\r\n      aria-hidden=\"true\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M16.88 3.549L7.12 20.451\"></path>\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconArrowDown({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"m205.66 149.66-72 72a8 8 0 0 1-11.32 0l-72-72a8 8 0 0 1 11.32-11.32L120 196.69V40a8 8 0 0 1 16 0v156.69l58.34-58.35a8 8 0 0 1 11.32 11.32Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconArrowRight({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"m221.66 133.66-72 72a8 8 0 0 1-11.32-11.32L196.69 136H40a8 8 0 0 1 0-16h156.69l-58.35-58.34a8 8 0 0 1 11.32-11.32l72 72a8 8 0 0 1 0 11.32Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconUser({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M230.92 212c-15.23-26.33-38.7-45.21-66.09-54.16a72 72 0 1 0-73.66 0c-27.39 8.94-50.86 27.82-66.09 54.16a8 8 0 1 0 13.85 8c18.84-32.56 52.14-52 89.07-52s70.23 19.44 89.07 52a8 8 0 1 0 13.85-8ZM72 96a56 56 0 1 1 56 56 56.06 56.06 0 0 1-56-56Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconPlus({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M224 128a8 8 0 0 1-8 8h-80v80a8 8 0 0 1-16 0v-80H40a8 8 0 0 1 0-16h80V40a8 8 0 0 1 16 0v80h80a8 8 0 0 1 8 8Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconArrowElbow({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M200 32v144a8 8 0 0 1-8 8H67.31l34.35 34.34a8 8 0 0 1-11.32 11.32l-48-48a8 8 0 0 1 0-11.32l48-48a8 8 0 0 1 11.32 11.32L67.31 168H184V32a8 8 0 0 1 16 0Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconSpinner({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4 animate-spin', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M232 128a104 104 0 0 1-208 0c0-41 23.81-78.36 60.66-95.27a8 8 0 0 1 6.68 14.54C60.15 61.59 40 93.27 40 128a88 88 0 0 0 176 0c0-34.73-20.15-66.41-51.34-80.73a8 8 0 0 1 6.68-14.54C208.19 49.64 232 87 232 128Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconMessage({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M216 48H40a16 16 0 0 0-16 16v160a15.84 15.84 0 0 0 9.25 14.5A16.05 16.05 0 0 0 40 240a15.89 15.89 0 0 0 10.25-3.78.69.69 0 0 0 .13-.11L82.5 208H216a16 16 0 0 0 16-16V64a16 16 0 0 0-16-16ZM40 224Zm176-32H82.5a16 16 0 0 0-10.3 3.75l-.12.11L40 224V64h176Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconTrash({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M216 48h-40v-8a24 24 0 0 0-24-24h-48a24 24 0 0 0-24 24v8H40a8 8 0 0 0 0 16h8v144a16 16 0 0 0 16 16h128a16 16 0 0 0 16-16V64h8a8 8 0 0 0 0-16ZM96 40a8 8 0 0 1 8-8h48a8 8 0 0 1 8 8v8H96Zm96 168H64V64h128Zm-80-104v64a8 8 0 0 1-16 0v-64a8 8 0 0 1 16 0Zm48 0v64a8 8 0 0 1-16 0v-64a8 8 0 0 1 16 0Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconRefresh({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M197.67 186.37a8 8 0 0 1 0 11.29C196.58 198.73 170.82 224 128 224c-37.39 0-64.53-22.4-80-39.85V208a8 8 0 0 1-16 0v-48a8 8 0 0 1 8-8h48a8 8 0 0 1 0 16H55.44C67.76 183.35 93 208 128 208c36 0 58.14-21.46 58.36-21.68a8 8 0 0 1 11.31.05ZM216 40a8 8 0 0 0-8 8v23.85C192.53 54.4 165.39 32 128 32c-42.82 0-68.58 25.27-69.66 26.34a8 8 0 0 0 11.3 11.34C69.86 69.46 92 48 128 48c35 0 60.24 24.65 72.56 40H168a8 8 0 0 0 0 16h48a8 8 0 0 0 8-8V48a8 8 0 0 0-8-8Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconStop({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M128 24a104 104 0 1 0 104 104A104.11 104.11 0 0 0 128 24Zm0 192a88 88 0 1 1 88-88 88.1 88.1 0 0 1-88 88Zm24-120h-48a8 8 0 0 0-8 8v48a8 8 0 0 0 8 8h48a8 8 0 0 0 8-8v-48a8 8 0 0 0-8-8Zm-8 48h-32v-32h32Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconSidebar({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M216 40H40a16 16 0 0 0-16 16v144a16 16 0 0 0 16 16h176a16 16 0 0 0 16-16V56a16 16 0 0 0-16-16ZM40 56h40v144H40Zm176 144H96V56h120v144Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconMoon({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M233.54 142.23a8 8 0 0 0-8-2 88.08 88.08 0 0 1-109.8-109.8 8 8 0 0 0-10-10 104.84 104.84 0 0 0-52.91 37A104 104 0 0 0 136 224a103.09 103.09 0 0 0 62.52-20.88 104.84 104.84 0 0 0 37-52.91 8 8 0 0 0-1.98-7.98Zm-44.64 48.11A88 88 0 0 1 65.66 67.11a89 89 0 0 1 31.4-26A106 106 0 0 0 96 56a104.11 104.11 0 0 0 104 104 106 106 0 0 0 14.92-1.06 89 89 0 0 1-26.02 31.4Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconSun({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M120 40V16a8 8 0 0 1 16 0v24a8 8 0 0 1-16 0Zm72 88a64 64 0 1 1-64-64 64.07 64.07 0 0 1 64 64Zm-16 0a48 48 0 1 0-48 48 48.05 48.05 0 0 0 48-48ZM58.34 69.66a8 8 0 0 0 11.32-11.32l-16-16a8 8 0 0 0-11.32 11.32Zm0 116.68-16 16a8 8 0 0 0 11.32 11.32l16-16a8 8 0 0 0-11.32-11.32ZM192 72a8 8 0 0 0 5.66-2.34l16-16a8 8 0 0 0-11.32-11.32l-16 16A8 8 0 0 0 192 72Zm5.66 114.34a8 8 0 0 0-11.32 11.32l16 16a8 8 0 0 0 11.32-11.32ZM48 128a8 8 0 0 0-8-8H16a8 8 0 0 0 0 16h24a8 8 0 0 0 8-8Zm80 80a8 8 0 0 0-8 8v24a8 8 0 0 0 16 0v-24a8 8 0 0 0-8-8Zm112-88h-24a8 8 0 0 0 0 16h24a8 8 0 0 0 0-16Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconCopy({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M216 32H88a8 8 0 0 0-8 8v40H40a8 8 0 0 0-8 8v128a8 8 0 0 0 8 8h128a8 8 0 0 0 8-8v-40h40a8 8 0 0 0 8-8V40a8 8 0 0 0-8-8Zm-56 176H48V96h112Zm48-48h-32V88a8 8 0 0 0-8-8H96V48h112Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconCheck({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"m229.66 77.66-128 128a8 8 0 0 1-11.32 0l-56-56a8 8 0 0 1 11.32-11.32L96 188.69 218.34 66.34a8 8 0 0 1 11.32 11.32Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconDownload({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M224 152v56a16 16 0 0 1-16 16H48a16 16 0 0 1-16-16v-56a8 8 0 0 1 16 0v56h160v-56a8 8 0 0 1 16 0Zm-101.66 5.66a8 8 0 0 0 11.32 0l40-40a8 8 0 0 0-11.32-11.32L136 132.69V40a8 8 0 0 0-16 0v92.69l-26.34-26.35a8 8 0 0 0-11.32 11.32Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconClose({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M205.66 194.34a8 8 0 0 1-11.32 11.32L128 139.31l-66.34 66.35a8 8 0 0 1-11.32-11.32L116.69 128 50.34 61.66a8 8 0 0 1 11.32-11.32L128 116.69l66.34-66.35a8 8 0 0 1 11.32 11.32L139.31 128Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconEdit({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      fill=\"none\"\r\n      viewBox=\"0 0 24 24\"\r\n      strokeWidth={1.5}\r\n      stroke=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n        d=\"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10\"\r\n      />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconShare({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      viewBox=\"0 0 256 256\"\r\n      {...props}\r\n    >\r\n      <path d=\"m237.66 106.35-80-80A8 8 0 0 0 144 32v40.35c-25.94 2.22-54.59 14.92-78.16 34.91-28.38 24.08-46.05 55.11-49.76 87.37a12 12 0 0 0 20.68 9.58c11-11.71 50.14-48.74 107.24-52V192a8 8 0 0 0 13.66 5.65l80-80a8 8 0 0 0 0-11.3ZM160 172.69V144a8 8 0 0 0-8-8c-28.08 0-55.43 7.33-81.29 21.8a196.17 196.17 0 0 0-36.57 26.52c5.8-23.84 20.42-46.51 42.05-64.86C99.41 99.77 127.75 88 152 88a8 8 0 0 0 8-8V51.32L220.69 112Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconUsers({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      viewBox=\"0 0 256 256\"\r\n      {...props}\r\n    >\r\n      <path d=\"M117.25 157.92a60 60 0 1 0-66.5 0 95.83 95.83 0 0 0-47.22 37.71 8 8 0 1 0 13.4 8.74 80 80 0 0 1 134.14 0 8 8 0 0 0 13.4-8.74 95.83 95.83 0 0 0-47.22-37.71ZM40 108a44 44 0 1 1 44 44 44.05 44.05 0 0 1-44-44Zm210.14 98.7a8 8 0 0 1-11.07-2.33A79.83 79.83 0 0 0 172 168a8 8 0 0 1 0-16 44 44 0 1 0-16.34-84.87 8 8 0 1 1-5.94-14.85 60 60 0 0 1 55.53 105.64 95.83 95.83 0 0 1 47.22 37.71 8 8 0 0 1-2.33 11.07Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconExternalLink({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      viewBox=\"0 0 256 256\"\r\n      {...props}\r\n    >\r\n      <path d=\"M224 104a8 8 0 0 1-16 0V59.32l-66.33 66.34a8 8 0 0 1-11.32-11.32L196.68 48H152a8 8 0 0 1 0-16h64a8 8 0 0 1 8 8Zm-40 24a8 8 0 0 0-8 8v72H48V80h72a8 8 0 0 0 0-16H48a16 16 0 0 0-16 16v128a16 16 0 0 0 16 16h128a16 16 0 0 0 16-16v-72a8 8 0 0 0-8-8Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconChevronUpDown({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      viewBox=\"0 0 256 256\"\r\n      {...props}\r\n    >\r\n      <path d=\"M181.66 170.34a8 8 0 0 1 0 11.32l-48 48a8 8 0 0 1-11.32 0l-48-48a8 8 0 0 1 11.32-11.32L128 212.69l42.34-42.35a8 8 0 0 1 11.32 0Zm-96-84.68L128 43.31l42.34 42.35a8 8 0 0 0 11.32-11.32l-48-48a8 8 0 0 0-11.32 0l-48 48a8 8 0 0 0 11.32 11.32Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nexport const StopIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: \"currentcolor\" }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M3 3H13V13H3V3Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const ThumbUpIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M6.89531 2.23972C6.72984 2.12153 6.5 2.23981 6.5 2.44315V5.25001C6.5 6.21651 5.7165 7.00001 4.75 7.00001H2.5V13.5H12.1884C12.762 13.5 13.262 13.1096 13.4011 12.5532L14.4011 8.55318C14.5984 7.76425 14.0017 7.00001 13.1884 7.00001H9.25H8.5V6.25001V3.51458C8.5 3.43384 8.46101 3.35807 8.39531 3.31114L6.89531 2.23972ZM5 2.44315C5 1.01975 6.6089 0.191779 7.76717 1.01912L9.26717 2.09054C9.72706 2.41904 10 2.94941 10 3.51458V5.50001H13.1884C14.9775 5.50001 16.2903 7.18133 15.8563 8.91698L14.8563 12.917C14.5503 14.1412 13.4503 15 12.1884 15H1.75H1V14.25V6.25001V5.50001H1.75H4.75C4.88807 5.50001 5 5.38808 5 5.25001V2.44315Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport const ThumbDownIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M6.89531 13.7603C6.72984 13.8785 6.5 13.7602 6.5 13.5569V10.75C6.5 9.7835 5.7165 9 4.75 9H2.5V2.5H12.1884C12.762 2.5 13.262 2.89037 13.4011 3.44683L14.4011 7.44683C14.5984 8.23576 14.0017 9 13.1884 9H9.25H8.5V9.75V12.4854C8.5 12.5662 8.46101 12.6419 8.39531 12.6889L6.89531 13.7603ZM5 13.5569C5 14.9803 6.6089 15.8082 7.76717 14.9809L9.26717 13.9095C9.72706 13.581 10 13.0506 10 12.4854V10.5H13.1884C14.9775 10.5 16.2903 8.81868 15.8563 7.08303L14.8563 3.08303C14.5503 1.85882 13.4503 1 12.1884 1H1.75H1V1.75V9.75V10.5H1.75H4.75C4.88807 10.5 5 10.6119 5 10.75V13.5569Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\n\r\nexport {\r\n  IconEdit,\r\n  IconNextChat,\r\n  IconOpenAI,\r\n  IconVercel,\r\n  IconGitHub,\r\n  IconSeparator,\r\n  IconArrowDown,\r\n  IconArrowRight,\r\n  IconUser,\r\n  IconPlus,\r\n  IconArrowElbow,\r\n  IconSpinner,\r\n  IconMessage,\r\n  IconTrash,\r\n  IconRefresh,\r\n  IconStop,\r\n  IconSidebar,\r\n  IconMoon,\r\n  IconSun,\r\n  IconCopy,\r\n  IconCheck,\r\n  IconDownload,\r\n  IconClose,\r\n  IconShare,\r\n  IconUsers,\r\n  IconExternalLink,\r\n  IconChevronUpDown\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AAEA;AAJA;;;;AAMA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,MAAM,KAAK,CAAA,GAAA,8SAAA,CAAA,QAAW,AAAD;IAErB,qBACE,uVAAC;QACC,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;;0BAET,uVAAC;;kCACC,uVAAC;wBACC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC;wBACtB,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,eAAc;;0CAEd,uVAAC;gCAAK,WAAW,WAAW,UAAU;;;;;;0CACtC,uVAAC;gCACC,QAAQ;gCACR,WAAW,WAAW,UAAU;gCAChC,aAAa;;;;;;;;;;;;kCAGjB,uVAAC;wBACC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC;wBACtB,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,eAAc;;0CAEd,uVAAC;gCAAK,WAAW,WAAW,UAAU;;;;;;0CACtC,uVAAC;gCACC,QAAQ;gCACR,WAAW,WAAW,UAAU;gCAChC,aAAa;;;;;;;;;;;;;;;;;;0BAInB,uVAAC;gBACC,GAAE;gBACF,MAAM,WAAW,UAAU;gBAC3B,QAAQ,WAAW,UAAU;gBAC7B,aAAa;gBACb,eAAc;gBACd,gBAAe;;;;;;0BAEjB,uVAAC;gBACC,IAAG;gBACH,OAAO;oBAAE,UAAU;gBAAQ;gBAC3B,WAAU;gBACV,GAAG;gBACH,GAAG;gBACH,OAAO;gBACP,QAAQ;0BAER,cAAA,uVAAC;oBAAO,IAAI;oBAAG,IAAI;oBAAG,GAAG;oBAAG,MAAM,WAAW,UAAU;;;;;;;;;;;0BAEzD,uVAAC;gBAAE,MAAK;;kCACN,uVAAC;wBAAO,IAAI;wBAAG,IAAI;wBAAG,GAAG;wBAAG,MAAM,WAAW,UAAU;;;;;;kCACvD,uVAAC;wBACC,GAAE;wBACF,MAAM,CAAC,cAAc,EAAE,GAAG,GAAG,CAAC;;;;;;kCAEhC,uVAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,MAAM,CAAC,cAAc,EAAE,GAAG,GAAG,CAAC;;;;;;;;;;;;;;;;;;AAKxC;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,uVAAC;QACC,MAAK;QACL,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;;0BAET,uVAAC;0BAAM;;;;;;0BACP,uVAAC;gBAAK,GAAE;;;;;;;;;;;;AAGd;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,uVAAC;QACC,cAAW;QACX,MAAK;QACL,SAAQ;QACR,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,uVAAC;QACC,MAAK;QACL,SAAQ;QACR,OAAM;QACN,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;;0BAET,uVAAC;0BAAM;;;;;;0BACP,uVAAC;gBAAK,GAAE;;;;;;;;;;;;AAGd;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,uVAAC;QACC,MAAK;QACL,gBAAe;QACf,QAAO;QACP,eAAc;QACd,gBAAe;QACf,aAAY;QACZ,SAAQ;QACR,eAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAoC;IACnE,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,uVAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,uVAAC;QACC,OAAM;QACN,MAAK;QACL,SAAQ;QACR,aAAa;QACb,QAAO;QACP,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,uVAAC;YACC,eAAc;YACd,gBAAe;YACf,GAAE;;;;;;;;;;;AAIV;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,uVAAC;QACC,OAAM;QACN,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAQ;QACP,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,uVAAC;QACC,OAAM;QACN,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAQ;QACP,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,uVAAC;QACC,OAAM;QACN,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAQ;QACP,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,uVAAC;QACC,OAAM;QACN,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAQ;QACP,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEO,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAqB;IACvD,qBACE,uVAAC;QACC,QAAQ;QACR,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,uVAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AAEO,MAAM,cAAc,CAAC,EAAE,OAAO,EAAE,EAAqB,iBAC1D,uVAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,uVAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAKJ,MAAM,gBAAgB,CAAC,EAAE,OAAO,EAAE,EAAqB,iBAC5D,uVAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,uVAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK", "debugId": null}}, {"offset": {"line": 2222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport * as React from 'react'\r\nimport { useTheme } from 'next-themes'\r\nimport { cn } from '@/lib/utils'\r\nimport { Button } from '@/components/ui/button'\r\nimport { IconMoon, IconSun } from '@/components/ui/icons'\r\n\r\ninterface ThemeToggleProps {\r\n  className?: string\r\n}\r\n\r\nexport function ThemeToggle({ className }: ThemeToggleProps) {\r\n  const { setTheme, theme } = useTheme()\r\n  const [mounted, setMounted] = React.useState(false)\r\n  const [_, startTransition] = React.useTransition()\r\n\r\n  // hydration 에러 수정. mounted 코드를 제거하려면 컴포넌트 조건부 렌더링 수정필요\r\n  React.useEffect(() => {\r\n    setMounted(true)\r\n  }, [])\r\n\r\n  if (!mounted) {\r\n    return <Button variant=\"ghost\" size=\"icon\" className={cn(className)} />\r\n  }\r\n\r\n  return (\r\n    <Button\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(className)}\r\n      onClick={() => {\r\n        startTransition(() => {\r\n          setTheme(theme === 'light' ? 'dark' : 'light')\r\n        })\r\n      }}\r\n    >\r\n      {theme === 'dark' ? (\r\n        <IconMoon className=\"transition-all\" />\r\n      ) : (\r\n        <IconSun className=\"transition-all\" />\r\n      )}\r\n      <span className=\"sr-only\">테마 변경</span>\r\n    </Button>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAYO,SAAS,YAAY,EAAE,SAAS,EAAoB;IACzD,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,yPAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAc,AAAD,EAAE;IAC7C,MAAM,CAAC,GAAG,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,gBAAmB,AAAD;IAE/C,uDAAuD;IACvD,CAAA,GAAA,8SAAA,CAAA,YAAe,AAAD,EAAE;QACd,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBAAO,uVAAC,2HAAA,CAAA,SAAM;YAAC,SAAQ;YAAQ,MAAK;YAAO,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE;;;;;;IAC3D;IAEA,qBACE,uVAAC,2HAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS;YACP,gBAAgB;gBACd,SAAS,UAAU,UAAU,SAAS;YACxC;QACF;;YAEC,UAAU,uBACT,uVAAC,0HAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;qCAEpB,uVAAC,0HAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;0BAErB,uVAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 2301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/profile.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport {signOut, useSession} from \"next-auth/react\"\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {ChevronsUpDown, LogOut, LucideLogIn, Settings, User} from \"lucide-react\";\r\nimport {SidebarMenuButton} from \"@/components/ui/sidebar\";\r\nimport {Avatar, AvatarFallback, AvatarImage} from \"@/components/ui/avatar\";\r\nimport * as React from \"react\";\r\nimport {ThemeToggle} from \"@/components/theme-toggle\";\r\nimport Link from \"next/link\";\r\nimport { type User as NextAuthUser } from 'next-auth';\r\n\r\nexport default function Profile({ user }: { user: NextAuthUser | undefined }) {\r\n\r\n    return (\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <SidebarMenuButton\r\n            size=\"lg\"\r\n            className=\"group data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\"\r\n          >\r\n            <Avatar className=\"h-8 w-8\">\r\n              <AvatarImage\r\n                src={user?.image ?? undefined}\r\n                alt=\"사용자이미지\"\r\n              />\r\n              <AvatarFallback className=\"bg-muted\">\r\n                <User className=\"h-4 w-4 text-muted-foreground\" />\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            {user &&\r\n              <div className=\"flex flex-col items-start text-left\">\r\n                <span className=\"text-sm font-medium\">{user.name}</span>\r\n                <span className=\"text-xs text-muted-foreground\">{user.email}</span>\r\n              </div>\r\n            }\r\n            <ChevronsUpDown className=\"ml-auto h-4 w-4 transition-transform duration-200\"/>\r\n          </SidebarMenuButton>\r\n        </DropdownMenuTrigger>\r\n\r\n        <DropdownMenuContent className=\"w-56\" align=\"end\" side=\"top\">\r\n          <DropdownMenuLabel className=\"flex justify-between items-center font-normal\">\r\n            <div className=\"flex flex-col space-y-1\">\r\n              <p className=\"text-sm font-medium leading-none\">{user?.name}</p>\r\n              <p className=\"text-xs leading-none text-muted-foreground\">{user?.email}</p>\r\n            </div>\r\n            <ThemeToggle/>\r\n          </DropdownMenuLabel>\r\n          <DropdownMenuSeparator/>\r\n          <DropdownMenuGroup>\r\n            <DropdownMenuItem className={\"gap-2\"}>\r\n              <User className=\"h-4 w-4\"/>\r\n              <span>계정</span>\r\n            </DropdownMenuItem>\r\n            <DropdownMenuItem className={\"gap-2\"}>\r\n              <Settings className=\"h-4 w-4\"/>\r\n              <span>설정</span>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuGroup>\r\n\r\n          <DropdownMenuSeparator/>\r\n\r\n          <DropdownMenuGroup>\r\n            {user &&\r\n              <DropdownMenuItem\r\n                className=\"flex w-full text-red-500 gap-2\"\r\n                onClick={() => signOut({redirectTo: '/',})}\r\n              >\r\n                <LogOut className=\"h-4 w-4\" /> {\"로그아웃\"}\r\n              </DropdownMenuItem>\r\n            }\r\n\r\n            {/* 로그인 버튼 */}\r\n            {!user &&\r\n              <DropdownMenuItem>\r\n                <Link\r\n                  href={'/login'}\r\n                  className={'flex w-full hover:text-blue-500 gap-2'}\r\n                >\r\n                  <LucideLogIn className=\"h-4 w-4\" />\r\n                  <span className=\"\">로그인</span>\r\n                </Link>\r\n              </DropdownMenuItem>\r\n            }\r\n          </DropdownMenuGroup>\r\n\r\n        </DropdownMenuContent>\r\n\r\n      </DropdownMenu>\r\n\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AAjBA;;;;;;;;;AAoBe,SAAS,QAAQ,EAAE,IAAI,EAAsC;IAExE,qBACE,uVAAC,qIAAA,CAAA,eAAY;;0BACX,uVAAC,qIAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,uVAAC,4HAAA,CAAA,oBAAiB;oBAChB,MAAK;oBACL,WAAU;;sCAEV,uVAAC,2HAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,uVAAC,2HAAA,CAAA,cAAW;oCACV,KAAK,MAAM,SAAS;oCACpB,KAAI;;;;;;8CAEN,uVAAC,2HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACxB,cAAA,uVAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAGnB,sBACC,uVAAC;4BAAI,WAAU;;8CACb,uVAAC;oCAAK,WAAU;8CAAuB,KAAK,IAAI;;;;;;8CAChD,uVAAC;oCAAK,WAAU;8CAAiC,KAAK,KAAK;;;;;;;;;;;;sCAG/D,uVAAC,kTAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAI9B,uVAAC,qIAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAO,OAAM;gBAAM,MAAK;;kCACrD,uVAAC,qIAAA,CAAA,oBAAiB;wBAAC,WAAU;;0CAC3B,uVAAC;gCAAI,WAAU;;kDACb,uVAAC;wCAAE,WAAU;kDAAoC,MAAM;;;;;;kDACvD,uVAAC;wCAAE,WAAU;kDAA8C,MAAM;;;;;;;;;;;;0CAEnE,uVAAC,8HAAA,CAAA,cAAW;;;;;;;;;;;kCAEd,uVAAC,qIAAA,CAAA,wBAAqB;;;;;kCACtB,uVAAC,qIAAA,CAAA,oBAAiB;;0CAChB,uVAAC,qIAAA,CAAA,mBAAgB;gCAAC,WAAW;;kDAC3B,uVAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,uVAAC;kDAAK;;;;;;;;;;;;0CAER,uVAAC,qIAAA,CAAA,mBAAgB;gCAAC,WAAW;;kDAC3B,uVAAC,8RAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,uVAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAIV,uVAAC,qIAAA,CAAA,wBAAqB;;;;;kCAEtB,uVAAC,qIAAA,CAAA,oBAAiB;;4BACf,sBACC,uVAAC,qIAAA,CAAA,mBAAgB;gCACf,WAAU;gCACV,SAAS,IAAM,CAAA,GAAA,iPAAA,CAAA,UAAO,AAAD,EAAE;wCAAC,YAAY;oCAAI;;kDAExC,uVAAC,8RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;oCAAE;;;;;;;4BAKnC,CAAC,sBACA,uVAAC,qIAAA,CAAA,mBAAgB;0CACf,cAAA,uVAAC,qQAAA,CAAA,UAAI;oCACH,MAAM;oCACN,WAAW;;sDAEX,uVAAC,kSAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,uVAAC;4CAAK,WAAU;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrC", "debugId": null}}, {"offset": {"line": 2595, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nconst AlertDialog = AlertDialogPrimitive.Root\r\n\r\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\r\n\r\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\r\n\r\nconst AlertDialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n))\r\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\r\n\r\nconst AlertDialogContent = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPortal>\r\n    <AlertDialogOverlay />\r\n    <AlertDialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </AlertDialogPortal>\r\n))\r\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\r\n\r\nconst AlertDialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-2 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\r\n\r\nconst AlertDialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\r\n\r\nconst AlertDialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\r\n\r\nconst AlertDialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogDescription.displayName =\r\n  AlertDialogPrimitive.Description.displayName\r\n\r\nconst AlertDialogAction = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Action\r\n    ref={ref}\r\n    className={cn(buttonVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\r\n\r\nconst AlertDialogCancel = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Cancel\r\n    ref={ref}\r\n    className={cn(\r\n      buttonVariants({ variant: \"outline\" }),\r\n      \"mt-2 sm:mt-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,kRAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,kRAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,kRAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,kRAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,mBAAmB,WAAW,GAAG,kRAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;;0BACC,uVAAC;;;;;0BACD,uVAAC,kRAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,kRAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,uVAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,uVAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,kRAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,kRAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,kRAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,kRAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,kRAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kRAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,kRAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kRAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2722, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\r\n\r\nconst Collapsible = CollapsiblePrimitive.Root\r\n\r\nconst CollapsibleTrigger = CollapsiblePrimitive.CollapsibleTrigger\r\n\r\nconst CollapsibleContent = CollapsiblePrimitive.CollapsibleContent\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;AAIA,MAAM,cAAc,2QAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,2QAAA,CAAA,qBAAuC;AAElE,MAAM,qBAAqB,2QAAA,CAAA,qBAAuC", "debugId": null}}, {"offset": {"line": 2740, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/sidebar-history.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { isToday, isYesterday, subMonths, subWeeks } from \"date-fns\";\r\nimport Link from \"next/link\";\r\nimport { useParams, usePathname, useRouter } from \"next/navigation\";\r\nimport { type User } from \"next-auth\";\r\nimport { memo, useEffect, useState } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport useS<PERSON> from \"swr\";\r\n\r\nimport {\r\n  ChevronRight,\r\n  History,\r\n  InfoIcon,\r\n  MoreHorizontalIcon,\r\n  TrashIcon,\r\n} from \"lucide-react\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  SidebarGroup,\r\n  SidebarGroupContent,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubAction,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\nimport { Chat } from \"@/lib/db/schema\";\r\nimport { cn, fetcher } from \"@/lib/utils\";\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"./ui/collapsible\";\r\n\r\ntype GroupedChats = {\r\n  today: Chat[];\r\n  yesterday: Chat[];\r\n  lastWeek: Chat[];\r\n  lastMonth: Chat[];\r\n  older: Chat[];\r\n};\r\n\r\nconst PureChatItem = ({\r\n  chat,\r\n  isActive,\r\n  onDelete,\r\n  setOpenMobile,\r\n}: {\r\n  chat: Chat;\r\n  isActive: boolean;\r\n  onDelete: (chatId: string) => void;\r\n  setOpenMobile: (open: boolean) => void;\r\n}) => (\r\n  <SidebarMenuSubItem>\r\n    <SidebarMenuSubButton\r\n      asChild\r\n      isActive={isActive}\r\n      className=\"peer/menu-sub-button\"\r\n    >\r\n      <Link\r\n        href={`/geon-2d-map/${chat.id}`}\r\n        onClick={() => setOpenMobile(false)}\r\n      >\r\n        <span>{chat.title}</span>\r\n      </Link>\r\n    </SidebarMenuSubButton>\r\n    <DropdownMenu modal={true}>\r\n      <DropdownMenuTrigger asChild>\r\n        <SidebarMenuSubAction showOnHover>\r\n          <MoreHorizontalIcon />\r\n          <span className=\"sr-only\">More</span>\r\n        </SidebarMenuSubAction>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent side=\"bottom\" align=\"end\">\r\n        <DropdownMenuItem\r\n          className=\"cursor-pointer text-destructive focus:bg-destructive/15 focus:text-destructive dark:text-red-500\"\r\n          onSelect={() => onDelete(chat.id)}\r\n        >\r\n          <TrashIcon className=\"mr-2 h-4 w-4\" />\r\n          <span>삭제</span>\r\n        </DropdownMenuItem>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  </SidebarMenuSubItem>\r\n);\r\n\r\nexport const ChatItem = memo(PureChatItem, (prevProps, nextProps) => {\r\n  if (prevProps.isActive !== nextProps.isActive) return false;\r\n  return true;\r\n});\r\n\r\nexport function SidebarHistory({ user }: { user: User | undefined }) {\r\n  const { setOpenMobile } = useSidebar();\r\n  const { id } = useParams();\r\n  const pathname = usePathname();\r\n  const {\r\n    data: history,\r\n    isLoading,\r\n    mutate,\r\n  } = useSWR<Array<Chat>>(user ? \"/api/history\" : null, fetcher, {\r\n    fallbackData: [],\r\n  });\r\n\r\n  useEffect(() => {\r\n    mutate();\r\n  }, [pathname, mutate]);\r\n\r\n  const [deleteId, setDeleteId] = useState<string | null>(null);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n  const router = useRouter();\r\n  const handleDelete = async () => {\r\n    const deletePromise = fetch(`/api/chat?id=${deleteId}`, {\r\n      method: \"DELETE\",\r\n    });\r\n\r\n    toast.promise(deletePromise, {\r\n      loading: \"대화 삭제중...\",\r\n      success: () => {\r\n        mutate((history) => {\r\n          if (history) {\r\n            return history.filter((h) => h.id !== id);\r\n          }\r\n        });\r\n        return \"대화가 삭제되었습니다.\";\r\n      },\r\n      error: \"대화 삭제 실패\",\r\n    });\r\n\r\n    setShowDeleteDialog(false);\r\n\r\n    if (deleteId === id) {\r\n      router.push(\"/\");\r\n    }\r\n  };\r\n\r\n  if (!user) {\r\n    return (\r\n      <SidebarMenu>\r\n        <SidebarMenuItem>\r\n          <SidebarMenuButton>\r\n            <History className=\"mr-2 h-4 w-4\" />\r\n            <span>대화 목록</span>\r\n          </SidebarMenuButton>\r\n          <SidebarMenuSub>\r\n            <div className=\"text-zinc-500 w-full flex flex-row justify-center items-center text-sm gap-2 mt-2\">\r\n              <InfoIcon />\r\n              <div>이전 대화를 저장하고 다시 보려면 로그인하세요!</div>\r\n            </div>\r\n          </SidebarMenuSub>\r\n        </SidebarMenuItem>\r\n      </SidebarMenu>\r\n    );\r\n  }\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <SidebarMenu>\r\n        <SidebarMenuItem>\r\n          <SidebarMenuButton>\r\n            <History className=\"mr-2 h-4 w-4\" />\r\n            <span>대화 목록</span>\r\n          </SidebarMenuButton>\r\n          <SidebarMenuSub>\r\n            <div className=\"flex flex-col\">\r\n              {[25, 80, 67, 26, 72].map((item) => (\r\n                <div\r\n                  key={item}\r\n                  className=\"rounded-md h-8 flex gap-2 px-2 items-center\"\r\n                >\r\n                  <div\r\n                    className=\"h-4 rounded-md flex-1 max-w-[--skeleton-width] bg-sidebar-accent-foreground/10\"\r\n                    style={\r\n                      {\r\n                        \"--skeleton-width\": `${item}%`,\r\n                      } as React.CSSProperties\r\n                    }\r\n                  />\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </SidebarMenuSub>\r\n        </SidebarMenuItem>\r\n      </SidebarMenu>\r\n    );\r\n  }\r\n\r\n  if (history?.length === 0) {\r\n    return (\r\n      <SidebarMenu>\r\n        <SidebarMenuItem>\r\n          <SidebarMenuButton>\r\n            <History className=\"mr-2 h-4 w-4\" />\r\n            <span>대화 목록</span>\r\n          </SidebarMenuButton>\r\n          <SidebarMenuSub>\r\n            <SidebarMenuSubItem>\r\n              <SidebarMenuSubButton>\r\n                <InfoIcon />\r\n                <span>이전 대화가 없습니다.</span>\r\n              </SidebarMenuSubButton>\r\n            </SidebarMenuSubItem>\r\n          </SidebarMenuSub>\r\n        </SidebarMenuItem>\r\n      </SidebarMenu>\r\n    );\r\n  }\r\n\r\n  const groupChatsByDate = (chats: Chat[]): GroupedChats => {\r\n    const now = new Date();\r\n    const oneWeekAgo = subWeeks(now, 1);\r\n    const oneMonthAgo = subMonths(now, 1);\r\n\r\n    return chats.reduce(\r\n      (groups, chat) => {\r\n        const chatDate = new Date(chat.createdAt);\r\n\r\n        if (isToday(chatDate)) {\r\n          groups.today.push(chat);\r\n        } else if (isYesterday(chatDate)) {\r\n          groups.yesterday.push(chat);\r\n        } else if (chatDate > oneWeekAgo) {\r\n          groups.lastWeek.push(chat);\r\n        } else if (chatDate > oneMonthAgo) {\r\n          groups.lastMonth.push(chat);\r\n        } else {\r\n          groups.older.push(chat);\r\n        }\r\n\r\n        return groups;\r\n      },\r\n      {\r\n        today: [],\r\n        yesterday: [],\r\n        lastWeek: [],\r\n        lastMonth: [],\r\n        older: [],\r\n      } as GroupedChats\r\n    );\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <SidebarMenu>\r\n        <Collapsible defaultOpen className=\"group/collapsible\">\r\n          <SidebarMenuItem>\r\n            <CollapsibleTrigger asChild>\r\n              <SidebarMenuButton>\r\n                <History className=\"mr-2 h-4 w-4\" />\r\n                <span>대화 목록</span>\r\n                <ChevronRight className=\"absolute right-2 h-4 w-4 transition-transform group-data-[state=open]/collapsible:rotate-90\" />\r\n              </SidebarMenuButton>\r\n            </CollapsibleTrigger>\r\n\r\n            <CollapsibleContent>\r\n              <SidebarMenuSub className=\"mt-1\">\r\n                {history &&\r\n                  (() => {\r\n                    const groupedChats = groupChatsByDate(history);\r\n\r\n                    return (\r\n                      <>\r\n                        {groupedChats.today.length > 0 && (\r\n                          <>\r\n                            <div className=\"px-2 py-1 text-xs text-sidebar-foreground/50\">\r\n                              오늘\r\n                            </div>\r\n                            {groupedChats.today.map((chat) => (\r\n                              <ChatItem\r\n                                key={chat.id}\r\n                                chat={chat}\r\n                                isActive={chat.id === id}\r\n                                onDelete={(chatId) => {\r\n                                  setDeleteId(chatId);\r\n                                  setShowDeleteDialog(true);\r\n                                }}\r\n                                setOpenMobile={setOpenMobile}\r\n                              />\r\n                            ))}\r\n                          </>\r\n                        )}\r\n\r\n                        {groupedChats.yesterday.length > 0 && (\r\n                          <>\r\n                            <div className=\"px-2 py-1 text-xs text-sidebar-foreground/50\">\r\n                              어제\r\n                            </div>\r\n                            {groupedChats.yesterday.map((chat) => (\r\n                              <ChatItem\r\n                                key={chat.id}\r\n                                chat={chat}\r\n                                isActive={chat.id === id}\r\n                                onDelete={(chatId) => {\r\n                                  setDeleteId(chatId);\r\n                                  setShowDeleteDialog(true);\r\n                                }}\r\n                                setOpenMobile={setOpenMobile}\r\n                              />\r\n                            ))}\r\n                          </>\r\n                        )}\r\n\r\n                        {groupedChats.lastWeek.length > 0 && (\r\n                          <>\r\n                            <div className=\"px-2 py-1 text-xs text-sidebar-foreground/50\">\r\n                              최근 7일\r\n                            </div>\r\n                            {groupedChats.lastWeek.map((chat) => (\r\n                              <ChatItem\r\n                                key={chat.id}\r\n                                chat={chat}\r\n                                isActive={chat.id === id}\r\n                                onDelete={(chatId) => {\r\n                                  setDeleteId(chatId);\r\n                                  setShowDeleteDialog(true);\r\n                                }}\r\n                                setOpenMobile={setOpenMobile}\r\n                              />\r\n                            ))}\r\n                          </>\r\n                        )}\r\n\r\n                        {groupedChats.lastMonth.length > 0 && (\r\n                          <>\r\n                            <div className=\"px-2 py-1 text-xs text-sidebar-foreground/50\">\r\n                              최근 30일\r\n                            </div>\r\n                            {groupedChats.lastMonth.map((chat) => (\r\n                              <ChatItem\r\n                                key={chat.id}\r\n                                chat={chat}\r\n                                isActive={chat.id === id}\r\n                                onDelete={(chatId) => {\r\n                                  setDeleteId(chatId);\r\n                                  setShowDeleteDialog(true);\r\n                                }}\r\n                                setOpenMobile={setOpenMobile}\r\n                              />\r\n                            ))}\r\n                          </>\r\n                        )}\r\n\r\n                        {groupedChats.older.length > 0 && (\r\n                          <>\r\n                            <div className=\"px-2 py-1 text-xs text-sidebar-foreground/50\">\r\n                              오래된 대화\r\n                            </div>\r\n                            {groupedChats.older.map((chat) => (\r\n                              <ChatItem\r\n                                key={chat.id}\r\n                                chat={chat}\r\n                                isActive={chat.id === id}\r\n                                onDelete={(chatId) => {\r\n                                  setDeleteId(chatId);\r\n                                  setShowDeleteDialog(true);\r\n                                }}\r\n                                setOpenMobile={setOpenMobile}\r\n                              />\r\n                            ))}\r\n                          </>\r\n                        )}\r\n                      </>\r\n                    );\r\n                  })()}\r\n              </SidebarMenuSub>\r\n            </CollapsibleContent>\r\n          </SidebarMenuItem>\r\n        </Collapsible>\r\n      </SidebarMenu>\r\n\r\n      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>대화 삭제</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              이 작업은 되돌릴 수 없습니다. 대화 내용이 영구적으로 삭제되며\r\n              서버에서도 완전히 제거됩니다.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>취소</AlertDialogCancel>\r\n            <AlertDialogAction onClick={handleDelete}>삭제</AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAOA;AAUA;AAMA;AAcA;AACA;AAhDA;;;;;;;;;;;;;;AA8DA,MAAM,eAAe,CAAC,EACpB,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,aAAa,EAMd,iBACC,uVAAC,4HAAA,CAAA,qBAAkB;;0BACjB,uVAAC,4HAAA,CAAA,uBAAoB;gBACnB,OAAO;gBACP,UAAU;gBACV,WAAU;0BAEV,cAAA,uVAAC,qQAAA,CAAA,UAAI;oBACH,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;oBAC/B,SAAS,IAAM,cAAc;8BAE7B,cAAA,uVAAC;kCAAM,KAAK,KAAK;;;;;;;;;;;;;;;;0BAGrB,uVAAC,qIAAA,CAAA,eAAY;gBAAC,OAAO;;kCACnB,uVAAC,qIAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,uVAAC,4HAAA,CAAA,uBAAoB;4BAAC,WAAW;;8CAC/B,uVAAC,wSAAA,CAAA,qBAAkB;;;;;8CACnB,uVAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;kCAG9B,uVAAC,qIAAA,CAAA,sBAAmB;wBAAC,MAAK;wBAAS,OAAM;kCACvC,cAAA,uVAAC,qIAAA,CAAA,mBAAgB;4BACf,WAAU;4BACV,UAAU,IAAM,SAAS,KAAK,EAAE;;8CAEhC,uVAAC,4RAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,uVAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOT,MAAM,yBAAW,CAAA,GAAA,8SAAA,CAAA,OAAI,AAAD,EAAE,cAAc,CAAC,WAAW;IACrD,IAAI,UAAU,QAAQ,KAAK,UAAU,QAAQ,EAAE,OAAO;IACtD,OAAO;AACT;AAEO,SAAS,eAAe,EAAE,IAAI,EAA8B;IACjE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,2OAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,2OAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EACJ,MAAM,OAAO,EACb,SAAS,EACT,MAAM,EACP,GAAG,CAAA,GAAA,mOAAA,CAAA,UAAM,AAAD,EAAe,OAAO,iBAAiB,MAAM,4GAAA,CAAA,UAAO,EAAE;QAC7D,cAAc,EAAE;IAClB;IAEA,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAU;KAAO;IAErB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,SAAS,CAAA,GAAA,2OAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe;QACnB,MAAM,gBAAgB,MAAM,CAAC,aAAa,EAAE,UAAU,EAAE;YACtD,QAAQ;QACV;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,eAAe;YAC3B,SAAS;YACT,SAAS;gBACP,OAAO,CAAC;oBACN,IAAI,SAAS;wBACX,OAAO,QAAQ,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBACxC;gBACF;gBACA,OAAO;YACT;YACA,OAAO;QACT;QAEA,oBAAoB;QAEpB,IAAI,aAAa,IAAI;YACnB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,uVAAC,4HAAA,CAAA,cAAW;sBACV,cAAA,uVAAC,4HAAA,CAAA,kBAAe;;kCACd,uVAAC,4HAAA,CAAA,oBAAiB;;0CAChB,uVAAC,4RAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,uVAAC;0CAAK;;;;;;;;;;;;kCAER,uVAAC,4HAAA,CAAA,iBAAc;kCACb,cAAA,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,0RAAA,CAAA,WAAQ;;;;;8CACT,uVAAC;8CAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMjB;IAEA,IAAI,WAAW;QACb,qBACE,uVAAC,4HAAA,CAAA,cAAW;sBACV,cAAA,uVAAC,4HAAA,CAAA,kBAAe;;kCACd,uVAAC,4HAAA,CAAA,oBAAiB;;0CAChB,uVAAC,4RAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,uVAAC;0CAAK;;;;;;;;;;;;kCAER,uVAAC,4HAAA,CAAA,iBAAc;kCACb,cAAA,uVAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAI;gCAAI;gCAAI;gCAAI;6BAAG,CAAC,GAAG,CAAC,CAAC,qBACzB,uVAAC;oCAEC,WAAU;8CAEV,cAAA,uVAAC;wCACC,WAAU;wCACV,OACE;4CACE,oBAAoB,GAAG,KAAK,CAAC,CAAC;wCAChC;;;;;;mCARC;;;;;;;;;;;;;;;;;;;;;;;;;;IAkBrB;IAEA,IAAI,SAAS,WAAW,GAAG;QACzB,qBACE,uVAAC,4HAAA,CAAA,cAAW;sBACV,cAAA,uVAAC,4HAAA,CAAA,kBAAe;;kCACd,uVAAC,4HAAA,CAAA,oBAAiB;;0CAChB,uVAAC,4RAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,uVAAC;0CAAK;;;;;;;;;;;;kCAER,uVAAC,4HAAA,CAAA,iBAAc;kCACb,cAAA,uVAAC,4HAAA,CAAA,qBAAkB;sCACjB,cAAA,uVAAC,4HAAA,CAAA,uBAAoB;;kDACnB,uVAAC,0RAAA,CAAA,WAAQ;;;;;kDACT,uVAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOpB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,MAAM,IAAI;QAChB,MAAM,aAAa,CAAA,GAAA,+LAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACjC,MAAM,cAAc,CAAA,GAAA,gMAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAEnC,OAAO,MAAM,MAAM,CACjB,CAAC,QAAQ;YACP,MAAM,WAAW,IAAI,KAAK,KAAK,SAAS;YAExC,IAAI,CAAA,GAAA,8LAAA,CAAA,UAAO,AAAD,EAAE,WAAW;gBACrB,OAAO,KAAK,CAAC,IAAI,CAAC;YACpB,OAAO,IAAI,CAAA,GAAA,kMAAA,CAAA,cAAW,AAAD,EAAE,WAAW;gBAChC,OAAO,SAAS,CAAC,IAAI,CAAC;YACxB,OAAO,IAAI,WAAW,YAAY;gBAChC,OAAO,QAAQ,CAAC,IAAI,CAAC;YACvB,OAAO,IAAI,WAAW,aAAa;gBACjC,OAAO,SAAS,CAAC,IAAI,CAAC;YACxB,OAAO;gBACL,OAAO,KAAK,CAAC,IAAI,CAAC;YACpB;YAEA,OAAO;QACT,GACA;YACE,OAAO,EAAE;YACT,WAAW,EAAE;YACb,UAAU,EAAE;YACZ,WAAW,EAAE;YACb,OAAO,EAAE;QACX;IAEJ;IAEA,qBACE;;0BACE,uVAAC,4HAAA,CAAA,cAAW;0BACV,cAAA,uVAAC,gIAAA,CAAA,cAAW;oBAAC,WAAW;oBAAC,WAAU;8BACjC,cAAA,uVAAC,4HAAA,CAAA,kBAAe;;0CACd,uVAAC,gIAAA,CAAA,qBAAkB;gCAAC,OAAO;0CACzB,cAAA,uVAAC,4HAAA,CAAA,oBAAiB;;sDAChB,uVAAC,4RAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,uVAAC;sDAAK;;;;;;sDACN,uVAAC,0SAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAI5B,uVAAC,gIAAA,CAAA,qBAAkB;0CACjB,cAAA,uVAAC,4HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,WACC,CAAC;wCACC,MAAM,eAAe,iBAAiB;wCAEtC,qBACE;;gDACG,aAAa,KAAK,CAAC,MAAM,GAAG,mBAC3B;;sEACE,uVAAC;4DAAI,WAAU;sEAA+C;;;;;;wDAG7D,aAAa,KAAK,CAAC,GAAG,CAAC,CAAC,qBACvB,uVAAC;gEAEC,MAAM;gEACN,UAAU,KAAK,EAAE,KAAK;gEACtB,UAAU,CAAC;oEACT,YAAY;oEACZ,oBAAoB;gEACtB;gEACA,eAAe;+DAPV,KAAK,EAAE;;;;;;;gDAanB,aAAa,SAAS,CAAC,MAAM,GAAG,mBAC/B;;sEACE,uVAAC;4DAAI,WAAU;sEAA+C;;;;;;wDAG7D,aAAa,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC3B,uVAAC;gEAEC,MAAM;gEACN,UAAU,KAAK,EAAE,KAAK;gEACtB,UAAU,CAAC;oEACT,YAAY;oEACZ,oBAAoB;gEACtB;gEACA,eAAe;+DAPV,KAAK,EAAE;;;;;;;gDAanB,aAAa,QAAQ,CAAC,MAAM,GAAG,mBAC9B;;sEACE,uVAAC;4DAAI,WAAU;sEAA+C;;;;;;wDAG7D,aAAa,QAAQ,CAAC,GAAG,CAAC,CAAC,qBAC1B,uVAAC;gEAEC,MAAM;gEACN,UAAU,KAAK,EAAE,KAAK;gEACtB,UAAU,CAAC;oEACT,YAAY;oEACZ,oBAAoB;gEACtB;gEACA,eAAe;+DAPV,KAAK,EAAE;;;;;;;gDAanB,aAAa,SAAS,CAAC,MAAM,GAAG,mBAC/B;;sEACE,uVAAC;4DAAI,WAAU;sEAA+C;;;;;;wDAG7D,aAAa,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC3B,uVAAC;gEAEC,MAAM;gEACN,UAAU,KAAK,EAAE,KAAK;gEACtB,UAAU,CAAC;oEACT,YAAY;oEACZ,oBAAoB;gEACtB;gEACA,eAAe;+DAPV,KAAK,EAAE;;;;;;;gDAanB,aAAa,KAAK,CAAC,MAAM,GAAG,mBAC3B;;sEACE,uVAAC;4DAAI,WAAU;sEAA+C;;;;;;wDAG7D,aAAa,KAAK,CAAC,GAAG,CAAC,CAAC,qBACvB,uVAAC;gEAEC,MAAM;gEACN,UAAU,KAAK,EAAE,KAAK;gEACtB,UAAU,CAAC;oEACT,YAAY;oEACZ,oBAAoB;gEACtB;gEACA,eAAe;+DAPV,KAAK,EAAE;;;;;;;;;oCAc1B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOb,uVAAC,oIAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,uVAAC,oIAAA,CAAA,qBAAkB;;sCACjB,uVAAC,oIAAA,CAAA,oBAAiB;;8CAChB,uVAAC,oIAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,uVAAC,oIAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAK1B,uVAAC,oIAAA,CAAA,oBAAiB;;8CAChB,uVAAC,oIAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,uVAAC,oIAAA,CAAA,oBAAiB;oCAAC,SAAS;8CAAc;;;;;;;;;;;;;;;;;;;;;;;;;AAMtD", "debugId": null}}, {"offset": {"line": 3432, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/ai/models.ts"], "sourcesContent": ["// Define your models here.\r\n\r\nexport interface ModelCapabilities {\r\n  reasoning: boolean;\r\n  streaming: boolean;\r\n  tools: boolean;\r\n  vision: boolean;\r\n}\r\n\r\nexport interface Model {\r\n  id: string;\r\n  label: string;\r\n  apiIdentifier: string;\r\n  description: string;\r\n  provider: \"geon\" | \"openai\" | \"dify\";\r\n  capabilities: ModelCapabilities;\r\n}\r\n\r\nexport const models: Array<Model> = [\r\n  {\r\n    id: \"Qwen3-14B\",\r\n    label: \"Qwen3 14B\",\r\n    apiIdentifier: \"Qwen/Qwen2.5-14B\",\r\n    description: \"Qwen3 14B 모델입니다.\",\r\n    provider: \"geon\",\r\n    capabilities: {\r\n      reasoning: false,\r\n      streaming: true,\r\n      tools: true,\r\n      vision: false,\r\n    },\r\n  },\r\n  {\r\n    id: \"Qwen3-4B\",\r\n    label: \"Qwen3 (추론)\",\r\n    apiIdentifier: \"Qwen/Qwen3-4B\",\r\n    description: \"Qwen3-4B 추론 모델입니다.\",\r\n    provider: \"geon\",\r\n    capabilities: {\r\n      reasoning: true,\r\n      streaming: true,\r\n      tools: true,\r\n      vision: false,\r\n    },\r\n  },\r\n  // {\r\n  //   id: 'A.X-4-Light',\r\n  //   label: 'A.X-4 Light',\r\n  //   apiIdentifier: 'A.X-4-Light-awq',\r\n  //   description: 'A.X-4 Light 모델입니다.',\r\n  //   provider: 'geon',\r\n  //   capabilities: {\r\n  //     reasoning: false,\r\n  //     streaming: true,\r\n  //     tools: true,\r\n  //     vision: false,\r\n  //   },\r\n  // },\r\n  {\r\n    id: \"gpt-4.1-nano\",\r\n    label: \"GPT 4.1 Nano\",\r\n    apiIdentifier: \"gpt-4.1-nano\",\r\n    description: \"OpenAI의 GPT 4.1 Nano 모델입니다.\",\r\n    provider: \"openai\",\r\n    capabilities: {\r\n      reasoning: false,\r\n      streaming: true,\r\n      tools: true,\r\n      vision: false,\r\n    },\r\n  },\r\n] as const;\r\n\r\nexport const DEFAULT_MODEL_NAME: string = \"Qwen3-4B\";\r\n\r\n// 모델 유틸리티 함수들\r\nexport function getModelById(modelId: string): Model | undefined {\r\n  return models.find((model) => model.id === modelId);\r\n}\r\n\r\nexport function getModelCapabilities(\r\n  modelId: string\r\n): ModelCapabilities | undefined {\r\n  const model = getModelById(modelId);\r\n  return model?.capabilities;\r\n}\r\n\r\nexport function supportsReasoning(modelId: string): boolean {\r\n  const capabilities = getModelCapabilities(modelId);\r\n  return capabilities?.reasoning ?? false;\r\n}\r\n\r\nexport function supportsStreaming(modelId: string): boolean {\r\n  const capabilities = getModelCapabilities(modelId);\r\n  return capabilities?.streaming ?? false;\r\n}\r\n\r\nexport function supportsTools(modelId: string): boolean {\r\n  const capabilities = getModelCapabilities(modelId);\r\n  return capabilities?.tools ?? false;\r\n}\r\n\r\nexport function supportsVision(modelId: string): boolean {\r\n  const capabilities = getModelCapabilities(modelId);\r\n  return capabilities?.vision ?? false;\r\n}\r\n\r\nexport function getModelProvider(\r\n  modelId: string\r\n): \"geon\" | \"openai\" | \"dify\" | undefined {\r\n  const model = getModelById(modelId);\r\n  return model?.provider;\r\n}\r\n\r\nexport function getReasoningDisabledMessage(\r\n  modelId: string\r\n): string | undefined {\r\n  const model = getModelById(modelId);\r\n  if (!model || model.capabilities.reasoning) {\r\n    return undefined;\r\n  }\r\n\r\n  // 모델별 맞춤 메시지\r\n  switch (model.id) {\r\n    case \"gpt-4.1-nano\":\r\n      return \"GPT 4.1 Nano 에서 지원되지 않습니다.\";\r\n    default:\r\n      return \"현재 선택된 모델은 추론 기능을 지원하지 않습니다\";\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;;;;;;;;AAkBpB,MAAM,SAAuB;IAClC;QACE,IAAI;QACJ,OAAO;QACP,eAAe;QACf,aAAa;QACb,UAAU;QACV,cAAc;YACZ,WAAW;YACX,WAAW;YACX,OAAO;YACP,QAAQ;QACV;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,eAAe;QACf,aAAa;QACb,UAAU;QACV,cAAc;YACZ,WAAW;YACX,WAAW;YACX,OAAO;YACP,QAAQ;QACV;IACF;IACA,IAAI;IACJ,uBAAuB;IACvB,0BAA0B;IAC1B,sCAAsC;IACtC,uCAAuC;IACvC,sBAAsB;IACtB,oBAAoB;IACpB,wBAAwB;IACxB,uBAAuB;IACvB,mBAAmB;IACnB,qBAAqB;IACrB,OAAO;IACP,KAAK;IACL;QACE,IAAI;QACJ,OAAO;QACP,eAAe;QACf,aAAa;QACb,UAAU;QACV,cAAc;YACZ,WAAW;YACX,WAAW;YACX,OAAO;YACP,QAAQ;QACV;IACF;CACD;AAEM,MAAM,qBAA6B;AAGnC,SAAS,aAAa,OAAe;IAC1C,OAAO,OAAO,IAAI,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK;AAC7C;AAEO,SAAS,qBACd,OAAe;IAEf,MAAM,QAAQ,aAAa;IAC3B,OAAO,OAAO;AAChB;AAEO,SAAS,kBAAkB,OAAe;IAC/C,MAAM,eAAe,qBAAqB;IAC1C,OAAO,cAAc,aAAa;AACpC;AAEO,SAAS,kBAAkB,OAAe;IAC/C,MAAM,eAAe,qBAAqB;IAC1C,OAAO,cAAc,aAAa;AACpC;AAEO,SAAS,cAAc,OAAe;IAC3C,MAAM,eAAe,qBAAqB;IAC1C,OAAO,cAAc,SAAS;AAChC;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,eAAe,qBAAqB;IAC1C,OAAO,cAAc,UAAU;AACjC;AAEO,SAAS,iBACd,OAAe;IAEf,MAAM,QAAQ,aAAa;IAC3B,OAAO,OAAO;AAChB;AAEO,SAAS,4BACd,OAAe;IAEf,MAAM,QAAQ,aAAa;IAC3B,IAAI,CAAC,SAAS,MAAM,YAAY,CAAC,SAAS,EAAE;QAC1C,OAAO;IACT;IAEA,aAAa;IACb,OAAQ,MAAM,EAAE;QACd,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 3546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { geon } from '@/lib/ai';\r\nimport { openai } from '@ai-sdk/openai';\r\nimport { VisibilityType } from '@/components/visibility-selector';\r\nimport { deleteMessagesByChatIdAfterTimestamp, getMessageById, updateChatVisiblityById } from '@/lib/db/queries';\r\nimport { CoreUserMessage, generateText } from 'ai';\r\nimport { cookies } from 'next/headers';\r\n\r\nexport async function saveModelId(model: string) {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set('model-id', model);\r\n}\r\nexport async function saveDevModelId(model: string) {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set('dev-model-id', model);\r\n}\r\n\r\nexport async function generateTitleFromUserMessage({\r\n  message,\r\n}: {\r\n  message: CoreUserMessage;\r\n}) {\r\n  const { text: title } = await generateText({\r\n    model: openai('gpt-4o-mini'),\r\n    system: `\\n\r\n    - you will generate a short title based on the first message a user begins a conversation with\r\n    - ensure it is not more than 80 characters long\r\n    - the title should be a summary of the user's message\r\n    - do not use quotes or colons`,\r\n    prompt: JSON.stringify(message),\r\n  });\r\n\r\n  return title;\r\n}\r\n\r\nexport async function deleteTrailingMessages({ id }: { id: string }) {\r\n  const [message] = await getMessageById({ id });\r\n\r\n  await deleteMessagesByChatIdAfterTimestamp({\r\n    chatId: message.chatId,\r\n    timestamp: message.createdAt,\r\n  });\r\n}\r\n\r\nexport async function updateChatVisibility({\r\n  chatId,\r\n  visibility,\r\n}: {\r\n  chatId: string;\r\n  visibility: VisibilityType;\r\n}) {\r\n  await updateChatVisiblityById({ chatId, visibility });\r\n}\r\n"], "names": [], "mappings": ";;;;;;IASsB,cAAA,WAAA,GAAA,CAAA,GAAA,+TAAA,CAAA,wBAAA,EAAA,8CAAA,+TAAA,CAAA,aAAA,EAAA,KAAA,GAAA,+TAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 3559, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { type User as NextAuthUser } from \"next-auth\";\r\nimport {\r\n  ChevronsUpDown,\r\n  GalleryVerticalEnd,\r\n  Bell,\r\n  Bookmark,\r\n  Search,\r\n  Check,\r\n  Code2,\r\n  Bot,\r\n} from \"lucide-react\";\r\nimport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\nimport {\r\n  EarthIcon,\r\n  MapIcon,\r\n} from \"lucide-react\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { TooltipProvider } from \"@/components/ui/tooltip\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  Dialog<PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>eader,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n} from \"@/components/ui/dialog\";\r\nimport Profile from \"@/components/profile\";\r\nimport Link from \"next/link\";\r\nimport { SidebarHistory } from \"./sidebar-history\";\r\nimport { Collapsible } from \"@radix-ui/react-collapsible\";\r\nimport { startTransition, useMemo, useState } from \"react\";\r\nimport { models } from \"@/lib/ai/models\";\r\nimport { saveModelId } from \"@/app/(map)/actions\";\r\n\r\nconst VERSIONS = [\"1.0.0\", \"1.1.0\", \"2.0.0\"] as const;\r\ntype Version = (typeof VERSIONS)[number];\r\n\r\nconst APPLICATION_ITEMS = [\r\n  {\r\n    label: \"말로 만드는 지도\",\r\n    url: \"/geon-2d-map\",\r\n    icon: EarthIcon,\r\n    badge: \"Beta\",\r\n    disabled: false,\r\n  },\r\n  {\r\n    label: \"업무지원(챗봇)\",\r\n    url: \"/preview\",\r\n    icon: Bot,\r\n    badge: \"Beta\",\r\n    disabled: false,\r\n  },\r\n];\r\n\r\nconst CHAT_ITEMS = [\r\n  // {icon: History, label: \"최근 대화\", disabled: true},\r\n  { icon: Bookmark, label: \"북마크\", disabled: true },\r\n  { icon: Bell, label: \"알림\", count: 3, disabled: true },\r\n];\r\n\r\n// Custom hooks\r\nconst useVersionControl = (initialVersion: Version) => {\r\n  const [version, setVersion] = React.useState<Version>(initialVersion);\r\n  const isLatest = version === \"2.0.0\";\r\n\r\n  return { version, setVersion, isLatest };\r\n};\r\n\r\n// Sub-components\r\nconst ModelSelector = ({\r\n  selectedModelId,\r\n  className,\r\n}: {\r\n  selectedModelId: string;\r\n  className?: string;\r\n}) => {\r\n  const [open, setOpen] = useState(false);\r\n  const [optimisticModelId, setOptimisticModelId] =\r\n    React.useOptimistic(selectedModelId);\r\n\r\n  const selectModel = useMemo(\r\n    () => models.find((model) => model.id === optimisticModelId),\r\n    [optimisticModelId]\r\n  );\r\n\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <SidebarMenuButton\r\n          size=\"lg\"\r\n          className=\"group data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\"\r\n        >\r\n          <div className=\"flex aspect-square size-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-500 text-white shadow-lg transition-all group-hover:shadow-blue-500/25\">\r\n            <GalleryVerticalEnd className=\"size-4\" />\r\n          </div>\r\n          <div className=\"flex flex-col gap-0.5 leading-none\">\r\n            <span className=\"font-semibold\">말로 만드는 지도</span>\r\n            <span className=\"text-xs text-muted-foreground\">\r\n              {selectedModelId}\r\n            </span>\r\n          </div>\r\n          <ChevronsUpDown className=\"ml-auto h-4 w-4 transition-transform duration-200\" />\r\n        </SidebarMenuButton>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent\r\n        className=\"w-[--radix-dropdown-menu-trigger-width]\"\r\n        align=\"start\"\r\n      >\r\n        {models.map((model) => (\r\n          <DropdownMenuItem\r\n            key={model.id}\r\n            onSelect={() => {\r\n              setOpen(false);\r\n\r\n              startTransition(() => {\r\n                setOptimisticModelId(model.id);\r\n                saveModelId(model.id);\r\n              });\r\n            }}\r\n            className=\"gap-4 group/item\"\r\n            data-active={model.id === optimisticModelId}\r\n            // disabled={model.label.includes(\"Llama\")}\r\n          >\r\n            <div className=\"flex flex-col gap-1 items-start\">\r\n              {model.label}\r\n              {model.description && (\r\n                <div className=\"text-xs text-muted-foreground\">\r\n                  {model.description}\r\n                </div>\r\n              )}\r\n            </div>\r\n            <Check className=\"size-4 ml-auto opacity-0 group-data-[active=true]/item:opacity-100\" />\r\n          </DropdownMenuItem>\r\n        ))}\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n};\r\n\r\nconst SearchDialog = ({\r\n  open,\r\n  onOpenChange,\r\n  searchQuery,\r\n  setSearchQuery,\r\n}: {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  searchQuery: string;\r\n  setSearchQuery: (query: string) => void;\r\n}) => (\r\n  <Dialog open={open} onOpenChange={onOpenChange}>\r\n    <DialogContent className=\"sm:max-w-[425px]\">\r\n      <DialogHeader>\r\n        <DialogTitle>빠른 검색</DialogTitle>\r\n      </DialogHeader>\r\n      <div className=\"relative\">\r\n        <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\r\n        <Input\r\n          placeholder=\"검색어를 입력하세요...\"\r\n          className=\"pl-8\"\r\n          value={searchQuery}\r\n          onChange={(e) => setSearchQuery(e.target.value)}\r\n          autoFocus\r\n        />\r\n      </div>\r\n    </DialogContent>\r\n  </Dialog>\r\n);\r\n\r\n// Main component\r\nexport function AppSidebar({\r\n  user,\r\n  selectedModelId,\r\n}: {\r\n  user: NextAuthUser | undefined;\r\n  selectedModelId: string;\r\n}) {\r\n  const { open, setOpen } = useSidebar();\r\n  const { version, setVersion } = useVersionControl(\"1.0.0\");\r\n\r\n  return (\r\n    <TooltipProvider>\r\n      <Sidebar\r\n        collapsible=\"icon\"\r\n        className=\"border-r border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\"\r\n      >\r\n        <SidebarHeader className=\"border-b border-border/40\">\r\n          <SidebarMenu>\r\n            <SidebarMenuItem className=\"flex justify-between items-center\">\r\n              <ModelSelector selectedModelId={selectedModelId} />\r\n              {open && <SidebarTrigger />}\r\n            </SidebarMenuItem>\r\n          </SidebarMenu>\r\n        </SidebarHeader>\r\n\r\n        <SidebarContent>\r\n          <SidebarGroup>\r\n            <SidebarGroupLabel>Application</SidebarGroupLabel>\r\n            <SidebarGroupContent>\r\n              <SidebarMenu>\r\n                {APPLICATION_ITEMS.map((item) => (\r\n                  <SidebarMenuItem key={item.label}>\r\n                    <SidebarMenuButton\r\n                      asChild\r\n                      tooltip={item.label}\r\n                      disabled={item.disabled}\r\n                      className={cn(\r\n                        \"relative transition-colors hover:bg-accent/50\",\r\n                        item.disabled && \"opacity-50 cursor-not-allowed\"\r\n                      )}\r\n                    >\r\n                      <Link href={item.url}>\r\n                        <item.icon className=\"mr-2 h-4 w-4\" />\r\n                        <span>{item.label}</span>\r\n                        {item.badge && (\r\n                          <Badge\r\n                            variant=\"default\"\r\n                            className={cn(\r\n                              \"ml-auto\",\r\n                              item.badge === \"New\"\r\n                                ? \"bg-green-500\"\r\n                                : \"bg-blue-500\"\r\n                            )}\r\n                          >\r\n                            {item.badge}\r\n                          </Badge>\r\n                        )}\r\n                      </Link>\r\n                    </SidebarMenuButton>\r\n                  </SidebarMenuItem>\r\n                ))}\r\n              </SidebarMenu>\r\n            </SidebarGroupContent>\r\n          </SidebarGroup>\r\n\r\n          {/* <SidebarGroup>\r\n            <SidebarGroupLabel>Map</SidebarGroupLabel>\r\n            <SidebarGroupContent>\r\n              <SidebarMap user={user} />\r\n            </SidebarGroupContent>\r\n          </SidebarGroup> */}\r\n\r\n          <SidebarGroup>\r\n            <SidebarGroupLabel>Chat</SidebarGroupLabel>\r\n            <SidebarGroupContent>\r\n              <SidebarHistory user={user} />\r\n            </SidebarGroupContent>\r\n          </SidebarGroup>\r\n          \r\n        </SidebarContent>\r\n\r\n        <SidebarFooter className=\"border-t border-border/40\">\r\n          <SidebarMenu>\r\n            {!open && (\r\n              <SidebarMenuItem>\r\n                <SidebarTrigger />\r\n              </SidebarMenuItem>\r\n            )}\r\n            <SidebarMenuItem>\r\n              <Profile user={user} />\r\n            </SidebarMenuItem>\r\n          </SidebarMenu>\r\n        </SidebarFooter>\r\n      </Sidebar>\r\n    </TooltipProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAcA;AAIA;AAMA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AAGA;AACA;AAtDA;;;;;;;;;;;;;;;;;;AAwDA,MAAM,WAAW;IAAC;IAAS;IAAS;CAAQ;AAG5C,MAAM,oBAAoB;IACxB;QACE,OAAO;QACP,KAAK;QACL,MAAM,4RAAA,CAAA,YAAS;QACf,OAAO;QACP,UAAU;IACZ;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM,oRAAA,CAAA,MAAG;QACT,OAAO;QACP,UAAU;IACZ;CACD;AAED,MAAM,aAAa;IACjB,mDAAmD;IACnD;QAAE,MAAM,8RAAA,CAAA,WAAQ;QAAE,OAAO;QAAO,UAAU;IAAK;IAC/C;QAAE,MAAM,sRAAA,CAAA,OAAI;QAAE,OAAO;QAAM,OAAO;QAAG,UAAU;IAAK;CACrD;AAED,eAAe;AACf,MAAM,oBAAoB,CAAC;IACzB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAc,AAAD,EAAW;IACtD,MAAM,WAAW,YAAY;IAE7B,OAAO;QAAE;QAAS;QAAY;IAAS;AACzC;AAEA,iBAAiB;AACjB,MAAM,gBAAgB,CAAC,EACrB,eAAe,EACf,SAAS,EAIV;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,mBAAmB,qBAAqB,GAC7C,CAAA,GAAA,8SAAA,CAAA,gBAAmB,AAAD,EAAE;IAEtB,MAAM,cAAc,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EACxB,IAAM,mHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK,oBAC1C;QAAC;KAAkB;IAGrB,qBACE,uVAAC,qIAAA,CAAA,eAAY;;0BACX,uVAAC,qIAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,uVAAC,4HAAA,CAAA,oBAAiB;oBAChB,MAAK;oBACL,WAAU;;sCAEV,uVAAC;4BAAI,WAAU;sCACb,cAAA,uVAAC,0TAAA,CAAA,qBAAkB;gCAAC,WAAU;;;;;;;;;;;sCAEhC,uVAAC;4BAAI,WAAU;;8CACb,uVAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,uVAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;sCAGL,uVAAC,kTAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAG9B,uVAAC,qIAAA,CAAA,sBAAmB;gBAClB,WAAU;gBACV,OAAM;0BAEL,mHAAA,CAAA,SAAM,CAAC,GAAG,CAAC,CAAC,sBACX,uVAAC,qIAAA,CAAA,mBAAgB;wBAEf,UAAU;4BACR,QAAQ;4BAER,CAAA,GAAA,8SAAA,CAAA,kBAAe,AAAD,EAAE;gCACd,qBAAqB,MAAM,EAAE;gCAC7B,CAAA,GAAA,sJAAA,CAAA,cAAW,AAAD,EAAE,MAAM,EAAE;4BACtB;wBACF;wBACA,WAAU;wBACV,eAAa,MAAM,EAAE,KAAK;;0CAG1B,uVAAC;gCAAI,WAAU;;oCACZ,MAAM,KAAK;oCACX,MAAM,WAAW,kBAChB,uVAAC;wCAAI,WAAU;kDACZ,MAAM,WAAW;;;;;;;;;;;;0CAIxB,uVAAC,wRAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;uBArBZ,MAAM,EAAE;;;;;;;;;;;;;;;;AA2BzB;AAEA,MAAM,eAAe,CAAC,EACpB,IAAI,EACJ,YAAY,EACZ,WAAW,EACX,cAAc,EAMf,iBACC,uVAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,uVAAC,2HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,uVAAC,2HAAA,CAAA,eAAY;8BACX,cAAA,uVAAC,2HAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAEf,uVAAC;oBAAI,WAAU;;sCACb,uVAAC,0RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,uVAAC,0HAAA,CAAA,QAAK;4BACJ,aAAY;4BACZ,WAAU;4BACV,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,SAAS;;;;;;;;;;;;;;;;;;;;;;;AAQZ,SAAS,WAAW,EACzB,IAAI,EACJ,eAAe,EAIhB;IACC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,kBAAkB;IAElD,qBACE,uVAAC,4HAAA,CAAA,kBAAe;kBACd,cAAA,uVAAC,4HAAA,CAAA,UAAO;YACN,aAAY;YACZ,WAAU;;8BAEV,uVAAC,4HAAA,CAAA,gBAAa;oBAAC,WAAU;8BACvB,cAAA,uVAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,uVAAC,4HAAA,CAAA,kBAAe;4BAAC,WAAU;;8CACzB,uVAAC;oCAAc,iBAAiB;;;;;;gCAC/B,sBAAQ,uVAAC,4HAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;8BAK9B,uVAAC,4HAAA,CAAA,iBAAc;;sCACb,uVAAC,4HAAA,CAAA,eAAY;;8CACX,uVAAC,4HAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,uVAAC,4HAAA,CAAA,sBAAmB;8CAClB,cAAA,uVAAC,4HAAA,CAAA,cAAW;kDACT,kBAAkB,GAAG,CAAC,CAAC,qBACtB,uVAAC,4HAAA,CAAA,kBAAe;0DACd,cAAA,uVAAC,4HAAA,CAAA,oBAAiB;oDAChB,OAAO;oDACP,SAAS,KAAK,KAAK;oDACnB,UAAU,KAAK,QAAQ;oDACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iDACA,KAAK,QAAQ,IAAI;8DAGnB,cAAA,uVAAC,qQAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,GAAG;;0EAClB,uVAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;0EACrB,uVAAC;0EAAM,KAAK,KAAK;;;;;;4DAChB,KAAK,KAAK,kBACT,uVAAC,0HAAA,CAAA,QAAK;gEACJ,SAAQ;gEACR,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,WACA,KAAK,KAAK,KAAK,QACX,iBACA;0EAGL,KAAK,KAAK;;;;;;;;;;;;;;;;;+CAvBC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;sCAyCxC,uVAAC,4HAAA,CAAA,eAAY;;8CACX,uVAAC,4HAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,uVAAC,4HAAA,CAAA,sBAAmB;8CAClB,cAAA,uVAAC,iIAAA,CAAA,iBAAc;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;8BAM5B,uVAAC,4HAAA,CAAA,gBAAa;oBAAC,WAAU;8BACvB,cAAA,uVAAC,4HAAA,CAAA,cAAW;;4BACT,CAAC,sBACA,uVAAC,4HAAA,CAAA,kBAAe;0CACd,cAAA,uVAAC,4HAAA,CAAA,iBAAc;;;;;;;;;;0CAGnB,uVAAC,4HAAA,CAAA,kBAAe;0CACd,cAAA,uVAAC,sHAAA,CAAA,UAAO;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B", "debugId": null}}]}