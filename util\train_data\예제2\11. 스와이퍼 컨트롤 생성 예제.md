# 지도 라이브러리 문서

## 기능 : 스와이퍼 컨트롤 생성

**설명**: 기본 지도를 생성한 후 스와이퍼 컨트롤(SwiperControl)을 추가합니다.

**입력**:
- `map` (Map): 지도 객체
- `basemapControl` (BasemapControl): 배경지도 컨트롤 객체
- `basemapKey` (String) 배경지도 키. 예를 들어:
    - `eMapBasic` 바로e맵 기본 지도
    - `eMapColor` 바로e맵 색각 지도
    - `eMapLowV` 바로e맵 큰 글씨 지도
    - `eMapWhite` 바로e맵 백지도
    - `eMapEnglish` 바로e맵 영어 지도
    - `eMapChinese` 바로e맵 중국어 지도
    - `eMapKorean` 바로e맵 한글 지도
    - `eMapWhiteEdu` 바로e맵 교육용 백지도
    - `eMapAIR` 바로e맵 항공(영상) 지도
    - `vWorldBase` vWorld 기본 지도
    - `vWorldWhite` vWorld 백지도
    - `vWorldMidnight` vWorld 야간 지도
    - `vWorldHybrid` vWorld 하이브리드 지도
    - `vWorldSatellite` vWorld 영상(항공) 지도
    - `kakaoBase` 카카오(다음) 기본 지도
    - `kakaoSkyview` 카카오(다음) 항공 지도
    - `OSM` OSM 기본 지도


**출력**:
- 생성된 스와이퍼 컨트롤 객체(swiperControl).


**코드 예제**:
```javascript
// 배경지도 컨트롤에서 바로e맵 항공지도 레이어 조회 
var rightLayer = basemapControl.getBaseLayer(basemapKey);
//스와이퍼 컨트롤 생성(원본 레이어를 왼쪽 영역에 표출, 정의한 레이어는 오른쪽 영역에 표출)
var swiperControl  = new odf.SwiperControl({
    layers : [rightLayer]
});
//생성한 스와이퍼 컨트롤을 지도 객체에 추가
swiperControl.setMap(map);
```
