"use strict";exports.id=929,exports.ids=[929],exports.modules={61478:(e,t,n)=>{n.d(t,{g:()=>J,m:()=>Z});var a=n(87113),r=n(86746),o=n(25613),s=Object.defineProperty,i=Object.defineProperties,l=Object.getOwnPropertyDescriptors,u=Object.getOwnPropertySymbols,p=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,d=(e,t,n)=>t in e?s(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,m=(e,t)=>{for(var n in t||(t={}))p.call(t,n)&&d(e,n,t[n]);if(u)for(var n of u(t))c.call(t,n)&&d(e,n,t[n]);return e},g=(e,t)=>i(e,l(t)),h=(e,t)=>{var n={};for(var a in e)p.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&u)for(var a of u(e))0>t.indexOf(a)&&c.call(e,a)&&(n[a]=e[a]);return n},f=(e,t,n)=>new Promise((a,r)=>{var o=e=>{try{i(n.next(e))}catch(e){r(e)}},s=e=>{try{i(n.throw(e))}catch(e){r(e)}},i=e=>e.done?a(e.value):Promise.resolve(e.value).then(o,s);i((n=n.apply(e,t)).next())});function y(e){switch(e){case"stop":return"stop";case"length":return"length";case"content_filter":return"content-filter";case"function_call":case"tool_calls":return"tool-calls";default:return"unknown"}}var v=o.z.object({error:o.z.object({message:o.z.string(),type:o.z.string().nullish(),param:o.z.any().nullish(),code:o.z.union([o.z.string(),o.z.number()]).nullish()})}),b=(0,r.sl)({errorSchema:v,errorToMessage:e=>e.error.message}),_=e=>t=>f(null,[t],function*({response:t}){let n=(0,r.PX)(t);if(null==t.body)throw new a.Tt({});return{responseHeaders:n,value:t.body.pipeThrough(new TextDecoderStream).pipeThrough((0,r.JA)()).pipeThrough(new TransformStream({transform({event:t,data:n},a){if("[DONE]"!==n)try{let t=JSON.parse(n);t.content&&(t.content=decodeURIComponent(t.content)),a.enqueue((0,r.N8)({text:JSON.stringify(t),schema:e}))}catch(e){console.error("Error parsing or decoding data:",e)}}}))}});function w({id:e,created:t}){return{id:null!=e?e:void 0,timestamp:null!=t?new Date(1e3*t):void 0}}var x=class{constructor(e,t,n){this.specificationVersion="v1",this.defaultObjectGenerationMode="json",this.modelId=e,this.settings=t,this.config=n}get provider(){return this.config.provider}getArgs({mode:e,prompt:t,maxTokens:n,temperature:o,topP:s,topK:i,frequencyPenalty:l,presencePenalty:u,stopSequences:p,responseFormat:c,seed:d,providerMetadata:h}){var f,y,v,b,_,w;let x=e.type,k=[];null!=i&&k.push({type:"unsupported-setting",setting:"topK"}),null!=c&&"json"===c.type&&null!=c.schema&&k.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format schema is not supported"});let z={model:this.modelId,max_tokens:n,temperature:o,top_p:s,frequency_penalty:l,presence_penalty:u,stop:p,seed:d,max_completion_tokens:null!=(y=null==(f=null==h?void 0:h.geon)?void 0:f.maxCompletionTokens)?y:void 0,store:null!=(b=null==(v=null==h?void 0:h.geon)?void 0:v.store)?b:void 0,metadata:null!=(w=null==(_=null==h?void 0:h.geon)?void 0:_.metadata)?w:void 0,response_format:(null==c?void 0:c.type)==="json"?{type:"json_object"}:void 0,messages:function(e){var t;let n=[];for(let{role:o,content:s}of e)switch(o){case"system":n.push({role:"system",content:s});break;case"user":if(s.length>0&&(null==(t=s[0])?void 0:t.type)==="text"){n.push({role:"user",content:s[0].text});break}n.push({role:"user",content:s.map((e,t)=>{var n,o,s,i;switch(e.type){case"text":return{type:"text",text:e.text};case"image":return{type:"image_url",image_url:{url:e.image instanceof URL?e.image.toString():`data:${null!=(n=e.mimeType)?n:"image/jpeg"};base64,${(0,r.n_)(e.image)}`,detail:null==(s=null==(o=e.providerMetadata)?void 0:o.openai)?void 0:s.imageDetail}};case"file":if(e.data instanceof URL)throw new a.b8({functionality:"'File content parts with URL data' functionality not supported."});switch(e.mimeType){case"audio/wav":return{type:"input_audio",input_audio:{data:e.data,format:"wav"}};case"audio/mp3":case"audio/mpeg":return{type:"input_audio",input_audio:{data:e.data,format:"mp3"}};case"application/pdf":return{type:"file",file:{filename:null!=(i=e.filename)?i:`part-${t}.pdf`,file_data:`data:application/pdf;base64,${e.data}`}};default:throw new a.b8({functionality:`File content part type ${e.mimeType} in user messages`})}}})});break;case"assistant":{let e="",t=[];for(let n of s)switch(n.type){case"text":e+=n.text;break;case"tool-call":t.push({id:n.toolCallId,type:"function",function:{name:n.toolName,arguments:JSON.stringify(n.args)}})}n.push({role:"assistant",content:e,tool_calls:t.length>0?t:void 0});break}case"tool":for(let e of s)n.push({role:"tool",name:e.toolName,content:"object"==typeof e.result?JSON.stringify(e.result):`${e.result}`});break;default:throw Error(`Unsupported role: ${o}`)}return n}(t)};switch(x){case"regular":{let{tools:t,tool_choice:n,toolWarnings:r}=function(e,t){var n;let r=(null==(n=e.tools)?void 0:n.length)?e.tools:void 0,o=[];if(null==r)return{tools:void 0,tool_choice:void 0,toolWarnings:o};let s=[];for(let e of r)"provider-defined"===e.type?o.push({type:"unsupported-tool",tool:e}):s.push({type:"function",function:{name:e.name,description:e.description,parameters:e.parameters,strict:!!t||void 0}});let i=e.toolChoice;if(null==i)return{tools:s,tool_choice:void 0,toolWarnings:o};let l=i.type;switch(l){case"auto":case"none":return{tools:s,tool_choice:l,toolWarnings:o};case"required":return{tools:s,tool_choice:"any",toolWarnings:o};case"tool":return{tools:s.filter(e=>e.function.name===i.toolName),tool_choice:"any",toolWarnings:o};default:throw new a.b8({functionality:`Unsupported tool choice type: ${l}`})}}(e,!1);return{args:g(m({},z),{tools:t,tool_choice:n}),warnings:[...k,...r]}}case"object-json":return{args:g(m({},z),{response_format:{type:"json_object"}}),warnings:k};case"object-tool":return{args:g(m({},z),{tool_choice:"any",tools:[{type:"function",function:e.tool}]}),warnings:k};default:throw Error(`Unsupported type: ${x}`)}}doGenerate(e){return f(this,null,function*(){var t,n,a,o,s,i,l,u,p,c,d,f,v,_,x,k,T,S,I,j,E,R,C,N,A,M;let{args:O,warnings:P}=this.getArgs(e),D=g(m({},O),{stream:!1}),{responseHeaders:q,value:$}=yield(0,r.GU)({url:`${this.config.baseURL}/chat/completions`,headers:(0,r.m2)(this.config.headers(),e.headers),body:D,failedResponseHandler:b,successfulResponseHandler:(0,r.cV)(z),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:U}=D,F=h(D,["messages"]);if($.choices&&Array.isArray($.choices)&&$.choices.length>0){let e=$.choices[0],m=null==(t=$.usage)?void 0:t.completion_tokens_details,g=null==(n=$.usage)?void 0:n.prompt_tokens_details,h={openai:{}};return(null==m?void 0:m.reasoning_tokens)!=null&&h.openai&&(h.openai.reasoningTokens=null==m?void 0:m.reasoning_tokens),(null==m?void 0:m.accepted_prediction_tokens)!=null&&h.openai&&(h.openai.acceptedPredictionTokens=null==m?void 0:m.accepted_prediction_tokens),(null==m?void 0:m.rejected_prediction_tokens)!=null&&h.openai&&(h.openai.rejectedPredictionTokens=null==m?void 0:m.rejected_prediction_tokens),(null==g?void 0:g.cached_tokens)!=null&&h.openai&&(h.openai.cachedPromptTokens=null==g?void 0:g.cached_tokens),{text:null!=(o=null==(a=null==e?void 0:e.message)?void 0:a.content)?o:"",toolCalls:(null==(s=null==e?void 0:e.message)?void 0:s.function_call)?[{toolCallType:"function",toolCallId:(0,r.$C)(),toolName:null!=(u=null==(l=null==(i=null==e?void 0:e.message)?void 0:i.function_call)?void 0:l.name)?u:"unknown_tool",args:null!=(d=null==(c=null==(p=null==e?void 0:e.message)?void 0:p.function_call)?void 0:c.arguments)?d:""}]:null==(v=null==(f=null==e?void 0:e.message)?void 0:f.tool_calls)?void 0:v.map(e=>{var t,n;return{toolCallType:"function",toolCallId:null!=(t=e.id)?t:(0,r.$C)(),toolName:null!=(n=e.function.name)?n:"unknown_tool",args:e.function.arguments}}),finishReason:y(null!=(_=null==e?void 0:e.finish_reason)?_:""),usage:{promptTokens:null!=(k=null==(x=$.usage)?void 0:x.prompt_tokens)?k:NaN,completionTokens:null!=(S=null==(T=$.usage)?void 0:T.completion_tokens)?S:NaN},rawCall:{rawPrompt:U,rawSettings:F},rawResponse:{headers:q},request:{body:JSON.stringify(D)},response:w($),providerMetadata:h}}let Z=null==(j=null==(I=$.message)?void 0:I.tool_calls)?void 0:j.map(e=>{var t,n;return{args:JSON.stringify(e.function.arguments),toolCallId:null!=(t=e.id)?t:(0,r.$C)(),toolCallType:"function",toolName:null!=(n=e.function.name)?n:"unknown_tool"}}),J=null!=(R=null==(E=$.message)?void 0:E.content)?R:"",L=J.match(/<think>(.*?)<\/think>/);return g(m({finishReason:y($.done_reason),rawCall:{rawPrompt:U,rawSettings:F},rawResponse:{headers:q},request:{body:JSON.stringify(D)},text:J.replace(/<think>.*?<\/think>/,"").trim()},L&&{reasoning:null==(C=L[1])?void 0:C.trim()}),{reasoning:null!=(M=null==(A=null==(N=$.choices)?void 0:N[0])?void 0:A.message.reasoning_content)?M:void 0,toolCalls:Z,usage:{completionTokens:$.eval_count||0,promptTokens:$.prompt_eval_count||0},warnings:P})})}doStream(e){return f(this,null,function*(){let t,{args:n,warnings:o}=this.getArgs(e),{responseHeaders:s,value:i}=yield(0,r.GU)({url:`${this.config.baseURL}/chat/completions`,headers:(0,r.m2)(this.config.headers(),e.headers),body:g(m(m({},n),"object"==typeof n.metadata&&null!==n.metadata?n.metadata:{}),{stream:!0}),failedResponseHandler:b,successfulResponseHandler:_(T),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:l}=n,u=h(n,["messages"]),p=[],c="unknown",d={promptTokens:void 0,completionTokens:void 0},f=!0,v=!1;return{stream:i.pipeThrough(new TransformStream({transform(e,n){var o,s,i,l,u,g,h,b,_,x,k,z,T,S,I,j,E,R,C,N,A,M,O,P,D,q,$,U,F,Z;if(!e.success){c="error",n.enqueue({type:"error",error:e.error});return}let J=e.value;if("error"in J){c="error",n.enqueue({type:"error",error:J.error});return}if(J.choices&&Array.isArray(J.choices)&&J.choices.length>0){let e=J.choices[0];if((null==e?void 0:e.finish_reason)!=null&&(c=y(e.finish_reason)),(null==e?void 0:e.delta)==null)return;let t=e.delta;null!=t.reasoning_content&&n.enqueue({type:"reasoning",textDelta:t.reasoning_content}),null!=t.content&&n.enqueue({type:"text-delta",textDelta:t.content});let d=null!=t.function_call?[{type:"function",id:(0,r.$C)(),function:t.function_call,index:0}]:t.tool_calls;if(null!=d)for(let t of d){let c=t.index||e.index;if(null==p[c]){if("function"!==t.type)throw new a.xn({data:t,message:"Expected 'function' type."});if(null==t.id)throw new a.xn({data:t,message:"Expected 'id' to be a string."});if((null==(o=t.function)?void 0:o.name)==null)throw new a.xn({data:t,message:"Expected 'function.name' to be a string."});p[c]={id:t.id,type:"function",function:{name:t.function.name,arguments:null!=(s=t.function.arguments)?s:""},hasFinished:!1};let e=p[c];(null==(i=e.function)?void 0:i.name)!=null&&(null==(l=e.function)?void 0:l.arguments)!=null&&(e.function.arguments.length>0&&n.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:e.id,toolName:e.function.name,argsTextDelta:e.function.arguments}),(0,r.v0)(e.function.arguments)&&(n.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(u=e.id)?u:(0,r.$C)(),toolName:e.function.name,args:e.function.arguments}),e.hasFinished=!0));continue}let d=p[c];(null==(g=t.function)?void 0:g.arguments)!=null&&(d.function.arguments+=null!=(b=null==(h=t.function)?void 0:h.arguments)?b:""),n.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:d.id,toolName:d.function.name,argsTextDelta:null!=(_=t.function.arguments)?_:""}),(null==(x=d.function)?void 0:x.name)!=null&&(null==(k=d.function)?void 0:k.arguments)!=null&&(0,r.v0)(d.function.arguments)&&(n.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(z=d.id)?z:(0,r.$C)(),toolName:d.function.name,args:d.function.arguments}),d.hasFinished=!0)}return}if(f&&(f=!1,n.enqueue(m({type:"response-metadata"},w(J)))),null!=J.sources&&n.enqueue({type:"tool-call",toolCallType:"function",toolCallId:(0,r.$C)(),toolName:"knowledgeBase",args:JSON.stringify(J.sources)}),null!=J.done_reason&&"stop"!=(c=y(J.done_reason))&&n.enqueue(m({type:"finish",finishReason:c,usage:{promptTokens:null!=(T=d.promptTokens)?T:NaN,completionTokens:null!=(S=d.completionTokens)?S:NaN}},null!=t?{providerMetadata:t}:{})),null!=J.message){let e=null!=(I=J.message.content)?I:"";if(v||e.includes("<think>")){v=!0,e.includes("</think>")&&(v=!1);let t=e.replace("<think>","").replace("</think>","");n.enqueue({type:"reasoning",textDelta:t})}else n.enqueue({type:"text-delta",textDelta:e});if(null!=J.message.tool_calls){for(let e of J.message.tool_calls)if((null==(j=e.function)?void 0:j.name)!=null&&(null==(E=e.function)?void 0:E.arguments)!=null){let t="object"==typeof e.function.arguments?JSON.stringify(e.function.arguments):(0,r.v0)(e.function.arguments)?e.function.arguments:null;n.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(R=e.id)?R:(0,r.$C)(),toolName:e.function.name,args:t})}}}if(null!=J.content&&n.enqueue({type:"text-delta",textDelta:J.content}),null!=J.tool_calls)for(let e of J.tool_calls){let t=e.index;if(null==t)throw new a.xn({data:e,message:"Expected 'index' to be a number."});if(null==p[t]){if("function"!==e.type)throw new a.xn({data:e,message:"Expected 'function' type."});if(null==e.id)throw new a.xn({data:e,message:"Expected 'id' to be a string."});if((null==(C=e.function)?void 0:C.name)==null)throw new a.xn({data:e,message:"Expected 'function.name' to be a string."});p[t]={id:e.id,type:"function",hasFinished:!1,function:{name:e.function.name,arguments:null!=(N=e.function.arguments)?N:""}};let o=p[t];(null==(A=o.function)?void 0:A.name)!=null&&(null==(M=o.function)?void 0:M.arguments)!=null&&(o.function.arguments.length>0&&n.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:o.id,toolName:o.function.name,argsTextDelta:o.function.arguments}),(0,r.v0)(o.function.arguments)&&n.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(O=o.id)?O:(0,r.$C)(),toolName:o.function.name,args:o.function.arguments}));continue}let o=p[t];(null==(P=e.function)?void 0:P.arguments)!=null&&(o.function.arguments+=null!=(q=null==(D=e.function)?void 0:D.arguments)?q:""),n.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:o.id,toolName:o.function.name,argsTextDelta:null!=($=e.function.arguments)?$:""}),(null==(U=o.function)?void 0:U.name)!=null&&(null==(F=o.function)?void 0:F.arguments)!=null&&(0,r.v0)(o.function.arguments)&&n.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(Z=o.id)?Z:(0,r.$C)(),toolName:o.function.name,args:o.function.arguments})}},flush(e){var n,a;e.enqueue(m({type:"finish",finishReason:c,usage:{promptTokens:null!=(n=d.promptTokens)?n:NaN,completionTokens:null!=(a=d.completionTokens)?a:NaN}},null!=t?{providerMetadata:t}:{}))}})),rawCall:{rawPrompt:l,rawSettings:u},rawResponse:{headers:s},warnings:o}})}},k=o.z.object({prompt_tokens:o.z.number().nullish(),completion_tokens:o.z.number().nullish(),prompt_tokens_details:o.z.object({cached_tokens:o.z.number().nullish()}).nullish(),completion_tokens_details:o.z.object({reasoning_tokens:o.z.number().nullish(),accepted_prediction_tokens:o.z.number().nullish(),rejected_prediction_tokens:o.z.number().nullish()}).nullish()}).nullish(),z=o.z.object({id:o.z.string().nullish(),created:o.z.number().nullish(),content:o.z.string().nullish(),tool_calls:o.z.array(o.z.object({index:o.z.number().nullish(),id:o.z.string().nullish(),type:o.z.string().nullish(),function:o.z.object({name:o.z.string().nullish(),arguments:o.z.string().nullish()})})).nullish(),message:o.z.object({role:o.z.enum(["assistant"]).nullish(),content:o.z.string().nullish(),function_call:o.z.object({name:o.z.string().optional(),arguments:o.z.string().optional()}).nullish(),tool_calls:o.z.array(o.z.object({index:o.z.number().nullish(),id:o.z.string().nullish(),type:o.z.literal("function").optional(),function:o.z.object({name:o.z.string().nullish(),arguments:o.z.any().nullish()})})).nullish()}).nullish(),model:o.z.string(),done:o.z.literal(!0).nullish(),done_reason:o.z.string().optional().nullable(),eval_count:o.z.number().nullish(),eval_duration:o.z.number().nullish(),prompt_eval_count:o.z.number().optional(),prompt_eval_duration:o.z.number().optional(),load_duration:o.z.number().optional(),finish_reason:o.z.string().nullable().optional(),total_duration:o.z.number().nullish(),choices:o.z.array(o.z.object({message:o.z.object({role:o.z.literal("assistant").nullish(),content:o.z.string().nullish(),reasoning_content:o.z.string().nullish(),function_call:o.z.object({arguments:o.z.string(),name:o.z.string()}).nullish(),tool_calls:o.z.array(o.z.object({id:o.z.string().nullish(),type:o.z.literal("function"),function:o.z.object({name:o.z.string(),arguments:o.z.string()})})).nullish()}),index:o.z.number(),logprobs:o.z.object({content:o.z.array(o.z.object({token:o.z.string(),logprob:o.z.number(),top_logprobs:o.z.array(o.z.object({token:o.z.string(),logprob:o.z.number()}))})).nullable()}).nullish(),finish_reason:o.z.string().nullish()})).nullish(),usage:k}),T=o.z.union([o.z.object({id:o.z.string().nullish(),created:o.z.number().nullish(),model:o.z.string().nullish(),sources:o.z.array(o.z.object({source:o.z.object({id:o.z.string(),user_id:o.z.string(),name:o.z.string(),description:o.z.string(),data:o.z.any().optional(),meta:o.z.any().nullable(),access_control:o.z.any().nullable(),created_at:o.z.number(),updated_at:o.z.number(),user:o.z.object({id:o.z.string(),name:o.z.string(),email:o.z.string(),role:o.z.string(),profile_image_url:o.z.string()}),files:o.z.array(o.z.object({id:o.z.string(),meta:o.z.object({name:o.z.string(),content_type:o.z.string(),size:o.z.number(),collection_name:o.z.string()}),created_at:o.z.number(),updated_at:o.z.number()})),type:o.z.string()}),document:o.z.array(o.z.string()),metadata:o.z.array(o.z.object({created_by:o.z.string(),embedding_config:o.z.string(),file_id:o.z.string(),hash:o.z.string(),name:o.z.string(),source:o.z.string(),start_index:o.z.number(),score:o.z.number()})),distances:o.z.array(o.z.number())})).optional(),message:o.z.object({role:o.z.enum(["assistant"]).nullish(),content:o.z.string().nullish(),function_call:o.z.object({name:o.z.string().optional(),arguments:o.z.string().optional()}).nullish(),tool_calls:o.z.array(o.z.object({index:o.z.number().nullish(),id:o.z.string().nullish(),type:o.z.literal("function").optional(),function:o.z.object({name:o.z.string().nullish(),arguments:o.z.any().nullish()})})).nullish()}).nullish(),content:o.z.string().nullish(),tool_calls:o.z.array(o.z.object({index:o.z.number().nullish(),id:o.z.string().nullish(),type:o.z.string().nullish(),function:o.z.object({name:o.z.string().nullish(),arguments:o.z.string().nullish()})})).nullish(),choices:o.z.array(o.z.object({delta:o.z.object({role:o.z.enum(["assistant"]).nullish(),content:o.z.string().nullish(),reasoning_content:o.z.string().nullish(),function_call:o.z.object({name:o.z.string().optional(),arguments:o.z.string().optional()}).nullish(),tool_calls:o.z.array(o.z.object({index:o.z.number().nullish(),id:o.z.string().nullish(),type:o.z.literal("function").optional(),function:o.z.object({name:o.z.string().nullish(),arguments:o.z.string().nullish()})})).nullish()}).nullish(),logprobs:o.z.object({content:o.z.array(o.z.object({token:o.z.string(),logprob:o.z.number(),top_logprobs:o.z.array(o.z.object({token:o.z.string(),logprob:o.z.number()}))})).nullable()}).nullish(),finish_reason:o.z.string().nullable().optional(),index:o.z.number()})).nullish(),usage:k,finish_reason:o.z.string().nullable().optional(),done_reason:o.z.string().nullable().optional(),done:o.z.boolean().nullable().optional(),detail:o.z.any().nullish()}),v]),S=o.z.object({include:o.z.array(o.z.string()).nullish(),language:o.z.string().nullish(),prompt:o.z.string().nullish(),temperature:o.z.number().min(0).max(1).nullish().default(0),timestampGranularities:o.z.array(o.z.enum(["word","segment"])).nullish().default(["segment"])}),I={afrikaans:"af",arabic:"ar",armenian:"hy",azerbaijani:"az",belarusian:"be",bosnian:"bs",bulgarian:"bg",catalan:"ca",chinese:"zh",croatian:"hr",czech:"cs",danish:"da",dutch:"nl",english:"en",estonian:"et",finnish:"fi",french:"fr",galician:"gl",german:"de",greek:"el",hebrew:"he",hindi:"hi",hungarian:"hu",icelandic:"is",indonesian:"id",italian:"it",japanese:"ja",kannada:"kn",kazakh:"kk",korean:"ko",latvian:"lv",lithuanian:"lt",macedonian:"mk",malay:"ms",marathi:"mr",maori:"mi",nepali:"ne",norwegian:"no",persian:"fa",polish:"pl",portuguese:"pt",romanian:"ro",russian:"ru",serbian:"sr",slovak:"sk",slovenian:"sl",spanish:"es",swahili:"sw",swedish:"sv",tagalog:"tl",tamil:"ta",thai:"th",turkish:"tr",ukrainian:"uk",urdu:"ur",vietnamese:"vi",welsh:"cy"},j=class{constructor(e,t){this.modelId=e,this.config=t,this.specificationVersion="v1"}get provider(){return this.config.provider}getArgs({audio:e,mediaType:t,providerOptions:n}){var a,o,s,i,l;let u=(0,r.xI)({provider:"geon",providerOptions:n,schema:S}),p=new FormData,c=e instanceof Uint8Array?new Blob([e]):new Blob([(0,r.Z9)(e)]);if(p.append("model",this.modelId),p.append("file",new File([c],"audio",{type:t})),u){let e={include:null!=(a=u.include)?a:void 0,language:null!=(o=u.language)?o:void 0,prompt:null!=(s=u.prompt)?s:void 0,temperature:null!=(i=u.temperature)?i:void 0,timestamp_granularities:null!=(l=u.timestampGranularities)?l:void 0};for(let t in e){let n=e[t];void 0!==n&&p.append(t,String(n))}}return{formData:p,warnings:[]}}doGenerate(e){return f(this,null,function*(){var t,n,a,o,s,i;let l=null!=(a=null==(n=null==(t=this.config._internal)?void 0:t.currentDate)?void 0:n.call(t))?a:new Date,{formData:u,warnings:p}=this.getArgs(e),{value:c,responseHeaders:d,rawValue:m}=yield(0,r.S)({url:this.config.url({path:"/audio/transcriptions",modelId:this.modelId}),headers:(0,r.m2)(this.config.headers(),e.headers),formData:u,failedResponseHandler:b,successfulResponseHandler:(0,r.cV)(E),abortSignal:e.abortSignal,fetch:this.config.fetch}),g=null!=c.language&&c.language in I?I[c.language]:void 0;return{text:c.text,segments:null!=(s=null==(o=c.words)?void 0:o.map(e=>({text:e.word,startSecond:e.start,endSecond:e.end})))?s:[],language:g,durationInSeconds:null!=(i=c.duration)?i:void 0,warnings:p,response:{timestamp:l,modelId:this.modelId,headers:d,body:m}}})}},E=o.z.object({text:o.z.string(),language:o.z.string().nullish(),duration:o.z.number().nullish(),words:o.z.array(o.z.object({word:o.z.string(),start:o.z.number(),end:o.z.number()})).nullish()});function R({finishReason:e,hasToolCalls:t}){switch(e){case void 0:case null:return t?"tool-calls":"stop";case"max_output_tokens":return"length";case"content_filter":return"content-filter";default:return t?"tool-calls":"unknown"}}var C=class{constructor(e,t){this.specificationVersion="v1",this.defaultObjectGenerationMode="json",this.supportsStructuredOutputs=!0,this.modelId=e,this.config=t}get provider(){return this.config.provider}getArgs({mode:e,maxTokens:t,temperature:n,stopSequences:o,topP:s,topK:i,presencePenalty:l,frequencyPenalty:u,seed:p,prompt:c,providerMetadata:d,responseFormat:h}){var f,y,v,b;let _=[],w=(b=this.modelId).startsWith("o")?b.startsWith("o1-mini")||b.startsWith("o1-preview")?{isReasoningModel:!0,systemMessageMode:"remove",requiredAutoTruncation:!1}:{isReasoningModel:!0,systemMessageMode:"developer",requiredAutoTruncation:!1}:{isReasoningModel:!1,systemMessageMode:"remove",requiredAutoTruncation:!1},x=e.type;null!=i&&_.push({type:"unsupported-setting",setting:"topK"}),null!=p&&_.push({type:"unsupported-setting",setting:"seed"}),null!=l&&_.push({type:"unsupported-setting",setting:"presencePenalty"}),null!=u&&_.push({type:"unsupported-setting",setting:"frequencyPenalty"}),null!=o&&_.push({type:"unsupported-setting",setting:"stopSequences"});let{messages:k,warnings:z}=function({prompt:e,systemMessageMode:t}){let n=[],o=[];for(let{role:s,content:i}of e)switch(s){case"system":switch(t){case"system":n.push({role:"system",content:i});break;case"developer":n.push({role:"developer",content:i});break;case"remove":o.push({type:"other",message:"system messages are removed for this model"});break;default:throw Error(`Unsupported system message mode: ${t}`)}break;case"user":n.push({role:"user",content:i.map((e,t)=>{var n,o,s,i;switch(e.type){case"text":return{type:"input_text",text:e.text};case"image":return{type:"input_image",image_url:e.image instanceof URL?e.image.toString():`data:${null!=(n=e.mimeType)?n:"image/jpeg"};base64,${(0,r.n_)(e.image)}`,detail:null==(s=null==(o=e.providerMetadata)?void 0:o.openai)?void 0:s.imageDetail};case"file":if(e.data instanceof URL)throw new a.b8({functionality:"File URLs in user messages"});if("application/pdf"===e.mimeType)return{type:"input_file",filename:null!=(i=e.filename)?i:`part-${t}.pdf`,file_data:`data:application/pdf;base64,${e.data}`};throw new a.b8({functionality:"Only PDF files are supported in user messages"})}})});break;case"assistant":for(let e of i)switch(e.type){case"text":n.push({role:"assistant",content:[{type:"output_text",text:e.text}]});break;case"tool-call":n.push({type:"function_call",call_id:e.toolCallId,name:e.toolName,arguments:JSON.stringify(e.args)})}break;case"tool":for(let e of i)n.push({type:"function_call_output",call_id:e.toolCallId,output:JSON.stringify(e.result)});break;default:throw Error(`Unsupported role: ${s}`)}return{messages:n,warnings:o}}({prompt:c,systemMessageMode:w.systemMessageMode});_.push(...z);let T=(0,r.xI)({provider:"geon",providerOptions:d,schema:F}),S=null==(f=null==T?void 0:T.strictSchemas)||f,I=m(m(g(m({model:this.modelId,input:k,temperature:n,top_p:s,max_output_tokens:t},(null==h?void 0:h.type)==="json"&&{text:{format:null!=h.schema?{type:"json_schema",strict:S,name:null!=(y=h.name)?y:"response",description:h.description,schema:h.schema}:{type:"json_object"}}}),{metadata:null==T?void 0:T.metadata,parallel_tool_calls:null==T?void 0:T.parallelToolCalls,previous_response_id:null==T?void 0:T.previousResponseId,store:null==T?void 0:T.store,user:null==T?void 0:T.user,instructions:null==T?void 0:T.instructions}),w.isReasoningModel&&(null==T?void 0:T.reasoningEffort)!=null&&{reasoning:{effort:null==T?void 0:T.reasoningEffort}}),w.requiredAutoTruncation&&{truncation:"auto"});switch(w.isReasoningModel&&(null!=I.temperature&&(I.temperature=void 0,_.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for reasoning models"})),null!=I.top_p&&(I.top_p=void 0,_.push({type:"unsupported-setting",setting:"topP",details:"topP is not supported for reasoning models"}))),x){case"regular":{let{tools:t,tool_choice:n,toolWarnings:r}=function({mode:e,strict:t}){var n;let r=(null==(n=e.tools)?void 0:n.length)?e.tools:void 0,o=[];if(null==r)return{tools:void 0,tool_choice:void 0,toolWarnings:o};let s=e.toolChoice,i=[];for(let e of r)switch(e.type){case"function":i.push({type:"function",name:e.name,description:e.description,parameters:e.parameters,strict:!!t||void 0});break;case"provider-defined":"openai.web_search_preview"===e.id?i.push({type:"web_search_preview",search_context_size:e.args.searchContextSize,user_location:e.args.userLocation}):o.push({type:"unsupported-tool",tool:e});break;default:o.push({type:"unsupported-tool",tool:e})}if(null==s)return{tools:i,tool_choice:void 0,toolWarnings:o};let l=s.type;switch(l){case"auto":case"none":case"required":return{tools:i,tool_choice:l,toolWarnings:o};case"tool":if("web_search_preview"===s.toolName)return{tools:i,tool_choice:{type:"web_search_preview"},toolWarnings:o};return{tools:i,tool_choice:{type:"function",name:s.toolName},toolWarnings:o};default:throw new a.b8({functionality:`Unsupported tool choice type: ${l}`})}}({mode:e,strict:S});return{args:g(m({},I),{tools:t,tool_choice:n}),warnings:[..._,...r]}}case"object-json":return{args:g(m({},I),{text:{format:null!=e.schema?{type:"json_schema",strict:S,name:null!=(v=e.name)?v:"response",description:e.description,schema:e.schema}:{type:"json_object"}}}),warnings:_};case"object-tool":return{args:g(m({},I),{tool_choice:{type:"function",name:e.tool.name},tools:[{type:"function",name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters,strict:S}]}),warnings:_};default:throw Error(`Unsupported type: ${x}`)}}doGenerate(e){return f(this,null,function*(){var t,n,a,s,i;let{args:l,warnings:u}=this.getArgs(e),{responseHeaders:p,value:c,rawValue:d}=yield(0,r.GU)({url:this.config.url({path:"/responses",modelId:this.modelId}),headers:(0,r.m2)(this.config.headers(),e.headers),body:l,failedResponseHandler:b,successfulResponseHandler:(0,r.cV)(o.z.object({id:o.z.string(),created_at:o.z.number(),model:o.z.string(),output:o.z.array(o.z.discriminatedUnion("type",[o.z.object({type:o.z.literal("message"),role:o.z.literal("assistant"),content:o.z.array(o.z.object({type:o.z.literal("output_text"),text:o.z.string(),annotations:o.z.array(o.z.object({type:o.z.literal("url_citation"),start_index:o.z.number(),end_index:o.z.number(),url:o.z.string(),title:o.z.string()}))}))}),o.z.object({type:o.z.literal("function_call"),call_id:o.z.string(),name:o.z.string(),arguments:o.z.string()}),o.z.object({type:o.z.literal("web_search_call")}),o.z.object({type:o.z.literal("computer_call")}),o.z.object({type:o.z.literal("reasoning")})])),incomplete_details:o.z.object({reason:o.z.string()}).nullable(),usage:N})),abortSignal:e.abortSignal,fetch:this.config.fetch}),m=c.output.filter(e=>"message"===e.type).flatMap(e=>e.content).filter(e=>"output_text"===e.type),g=c.output.filter(e=>"function_call"===e.type).map(e=>({toolCallType:"function",toolCallId:e.call_id,toolName:e.name,args:e.arguments}));return{text:m.map(e=>e.text).join("\n"),sources:m.flatMap(e=>e.annotations.map(e=>{var t,n,a;return{sourceType:"url",id:null!=(a=null==(n=(t=this.config).generateId)?void 0:n.call(t))?a:(0,r.$C)(),url:e.url,title:e.title}})),finishReason:R({finishReason:null==(t=c.incomplete_details)?void 0:t.reason,hasToolCalls:g.length>0}),toolCalls:g.length>0?g:void 0,usage:{promptTokens:c.usage.input_tokens,completionTokens:c.usage.output_tokens},rawCall:{rawPrompt:void 0,rawSettings:{}},rawResponse:{headers:p,body:d},request:{body:JSON.stringify(l)},response:{id:c.id,timestamp:new Date(1e3*c.created_at),modelId:c.model},providerMetadata:{geon:{responseId:c.id,cachedPromptTokens:null!=(a=null==(n=c.usage.input_tokens_details)?void 0:n.cached_tokens)?a:null,reasoningTokens:null!=(i=null==(s=c.usage.output_tokens_details)?void 0:s.reasoning_tokens)?i:null}},warnings:u}})}doStream(e){return f(this,null,function*(){let{args:t,warnings:n}=this.getArgs(e);console.log(t);let{responseHeaders:a,value:o}=yield(0,r.GU)({url:this.config.url({path:"/responses",modelId:this.modelId}),headers:(0,r.m2)(this.config.headers(),e.headers),body:g(m({},t),{stream:!0}),failedResponseHandler:b,successfulResponseHandler:(0,r.Ds)(U),abortSignal:e.abortSignal,fetch:this.config.fetch}),s=this,i="unknown",l=NaN,u=NaN,p=null,c=null,d=null,h={},f=!1;return{stream:o.pipeThrough(new TransformStream({transform(e,t){var n,a,o,m,g,y,v,b,_;if(!e.success){i="error",t.enqueue({type:"error",error:e.error});return}let w=e.value;if("response.output_item.added"===w.type)"function_call"===w.item.type&&(h[w.output_index]={toolName:w.item.name,toolCallId:w.item.call_id},t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:w.item.call_id,toolName:w.item.name,argsTextDelta:w.item.arguments}));else if("response.function_call_arguments.delta"===w.type){let e=h[w.output_index];null!=e&&t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:e.toolCallId,toolName:e.toolName,argsTextDelta:w.delta})}else{"response.created"===w.type?(d=w.response.id,t.enqueue({type:"response-metadata",id:w.response.id,timestamp:new Date(1e3*w.response.created_at),modelId:w.response.model})):"response.output_text.delta"===w.type?t.enqueue({type:"text-delta",textDelta:w.delta}):"response.output_item.done"===w.type&&"function_call"===w.item.type?(h[w.output_index]=void 0,f=!0,t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:w.item.call_id,toolName:w.item.name,args:w.item.arguments})):"response.completed"===(_=w).type||"response.incomplete"===_.type?(i=R({finishReason:null==(n=w.response.incomplete_details)?void 0:n.reason,hasToolCalls:f}),l=w.response.usage.input_tokens,u=w.response.usage.output_tokens,p=null!=(o=null==(a=w.response.usage.input_tokens_details)?void 0:a.cached_tokens)?o:p,c=null!=(g=null==(m=w.response.usage.output_tokens_details)?void 0:m.reasoning_tokens)?g:c):"response.output_text.annotation.added"===w.type&&t.enqueue({type:"source",source:{sourceType:"url",id:null!=(b=null==(v=(y=s.config).generateId)?void 0:v.call(y))?b:(0,r.$C)(),url:w.annotation.url,title:w.annotation.title}})}},flush(e){e.enqueue({type:"finish",finishReason:i,usage:{promptTokens:l,completionTokens:u},providerMetadata:{geon:{responseId:d,cachedPromptTokens:p,reasoningTokens:c}}})}})),rawCall:{rawPrompt:void 0,rawSettings:{}},rawResponse:{headers:a},request:{body:JSON.stringify(t)},warnings:n}})}},N=o.z.object({input_tokens:o.z.number(),input_tokens_details:o.z.object({cached_tokens:o.z.number().nullish()}).nullish(),output_tokens:o.z.number(),output_tokens_details:o.z.object({reasoning_tokens:o.z.number().nullish()}).nullish()}),A=o.z.object({type:o.z.literal("response.output_text.delta"),delta:o.z.string()}),M=o.z.object({type:o.z.enum(["response.completed","response.incomplete"]),response:o.z.object({incomplete_details:o.z.object({reason:o.z.string()}).nullish(),usage:N})}),O=o.z.object({type:o.z.literal("response.created"),response:o.z.object({id:o.z.string(),created_at:o.z.number(),model:o.z.string()})}),P=o.z.object({type:o.z.literal("response.output_item.done"),output_index:o.z.number(),item:o.z.discriminatedUnion("type",[o.z.object({type:o.z.literal("message")}),o.z.object({type:o.z.literal("function_call"),id:o.z.string(),call_id:o.z.string(),name:o.z.string(),arguments:o.z.string(),status:o.z.literal("completed")})])}),D=o.z.object({type:o.z.literal("response.function_call_arguments.delta"),item_id:o.z.string(),output_index:o.z.number(),delta:o.z.string()}),q=o.z.object({type:o.z.literal("response.output_item.added"),output_index:o.z.number(),item:o.z.discriminatedUnion("type",[o.z.object({type:o.z.literal("message")}),o.z.object({type:o.z.literal("function_call"),id:o.z.string(),call_id:o.z.string(),name:o.z.string(),arguments:o.z.string()})])}),$=o.z.object({type:o.z.literal("response.output_text.annotation.added"),annotation:o.z.object({type:o.z.literal("url_citation"),url:o.z.string(),title:o.z.string()})}),U=o.z.union([A,M,O,P,D,q,$,o.z.object({type:o.z.string()}).passthrough()]),F=o.z.object({metadata:o.z.any().nullish(),parallelToolCalls:o.z.boolean().nullish(),previousResponseId:o.z.string().nullish(),store:o.z.boolean().nullish(),user:o.z.string().nullish(),reasoningEffort:o.z.string().nullish(),strictSchemas:o.z.boolean().nullish(),instructions:o.z.string().nullish()});function Z(e={}){var t;let n=null!=(t=(0,r.ae)(e.baseURL))?t:"http://**************:8081/v1",a="geon",o=()=>m({Authorization:`Bearer ${(0,r.WL)({apiKey:e.apiKey,environmentVariableName:"GEON_AI_API_KEY",description:"Geon"})}`},e.headers),s=(t,a={})=>{var s;return new x(t,a,{provider:"geon.chat",baseURL:n,headers:o,generateId:null!=(s=e.generateId)?s:r.$C,fetch:e.fetch})},i=t=>new j(t,{provider:`${a}.transcription`,url:({path:e})=>`${n}${e}`,headers:o,fetch:e.fetch}),l=function(e,t){if(new.target)throw Error("The Geon model function cannot be called with the new keyword.");return s(e,t)};return l.languageModel=s,l.chat=s,l.beta={threads:{runs:{create:e=>f(null,null,function*(){return fetch(`${n}/threads/runs`,{method:"POST",headers:m({"Content-Type":"application/json",Accept:"application/json"},o()),body:JSON.stringify({query:e.query,assistantId:e.assistantId,threadId:e.threadId,user:e.user||"<EMAIL>",difyType:e.difyType||"chat",stream:void 0!==e.stream&&e.stream})})}),stream:e=>f(null,null,function*(){var t;let a=(null!=(t=e.generateId)?t:r.$C)(),s=yield fetch(`${n}/threads/runs`,{method:"POST",headers:m({"Content-Type":"application/json",Accept:"text/event-stream"},o()),body:JSON.stringify({query:e.query,assistantId:e.assistantId,threadId:e.threadId,user:e.user||"<EMAIL>",difyType:e.difyType||"chat",stream:!0})});if(!s.ok)throw Error(`Dify API \uD638\uCD9C \uC2E4\uD328: ${s.status} ${s.statusText}`);let i=yield function(e,t){return f(this,null,function*(){let n=e.body;if(!n)throw Error("응답 본문이 없습니다");return new ReadableStream({start(e){return f(this,null,function*(){var a,r,o,s,i;let l=!1,u=new TextDecoder,p="",c=n.getReader();try{for(;;){let{done:n,value:d}=yield c.read();if(n)break;let m=(p+=u.decode(d,{stream:!0})).split("\n\n");for(let n of(p=m.pop()||"",m)){if(!n.trim())continue;let u=n.match(/^event:\s*(.+)$/m),p=n.match(/^data:\s*(.+)$/m);if(!u||!p)continue;let c=(null==(a=u[1])?void 0:a.trim())||"",d=(null==(r=p[1])?void 0:r.trim())||"";try{let n=JSON.parse(d);if("thread.message.created"===c)l||(e.enqueue({event:"thread.message.created",data:{id:n.id||t,role:"assistant",content:[{type:"text",text:{value:""}}]}}),l=!0);else if("thread.message.delta"===c){let t=null==(s=null==(o=n.delta)?void 0:o.content)?void 0:s[0];(null==t?void 0:t.type)==="text"&&(null==(i=t.text)?void 0:i.value)!=null&&e.enqueue({event:"thread.message.delta",data:{delta:{content:[{type:"text",text:{value:t.text.value}}]}}})}else"thread.run.completed"===c?e.enqueue({event:"thread.run.completed",data:n}):"thread.run.requires_action"===c&&e.enqueue({event:"thread.run.requires_action",data:n})}catch(e){console.error("JSON 파싱 오류:",e,d)}}}e.close()}catch(t){console.error("스트림 처리 오류:",t),e.error(t)}})}})})}(s,a);return{threadId:e.threadId||"",messageId:a,stream:i}})}}},l.transcription=i,l.transcriptionModel=i,l.responses=t=>new C(t,{provider:`${a}.responses`,url:({path:e})=>`${n}${e}`,headers:o,fetch:e.fetch}),l}var J=Z()},88668:(e,t,n)=>{let a;n.d(t,{eK:()=>nt,$o:()=>no,k7:()=>nw,TO:()=>t9,Kn:()=>nZ,E2:()=>tf,ke:()=>ew,pY:()=>tY,Df:()=>nf,dF:()=>nN,gM:()=>nq,z6:()=>nV,ae:()=>nU});var r,o,s,i,l,u,p,c,d,m,g,h,f,y,v,b,_=n(86746);let w=Symbol("Let zodToJsonSchema decide on which parser to use"),x={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref"},k=e=>"string"==typeof e?{...x,name:e}:{...x,...e},z=e=>{let t=k(e),n=void 0!==t.name?[...t.basePath,t.definitionPath,t.name]:t.basePath;return{...t,currentPath:n,propertyPath:void 0,seen:new Map(Object.entries(t.definitions).map(([e,n])=>[n._def,{def:n._def,path:[...t.basePath,t.definitionPath,e],jsonSchema:void 0}]))}};var T=n(25613);function S(e,t,n,a){a?.errorMessages&&n&&(e.errorMessage={...e.errorMessage,[t]:n})}function I(e,t,n,a,r){e[t]=n,S(e,t,a,r)}function j(e,t){return Y(e.type._def,t)}let E=(e,t)=>Y(e.innerType._def,t),R=(e,t)=>{let n={type:"integer",format:"unix-time"};if("openApi3"===t.target)return n;for(let a of e.checks)switch(a.kind){case"min":I(n,"minimum",a.value,a.message,t);break;case"max":I(n,"maximum",a.value,a.message,t)}return n},C=e=>(!("type"in e)||"string"!==e.type)&&"allOf"in e,N={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>(void 0===a&&(a=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),a),ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function A(e,t){let n={type:"string"};if(e.checks)for(let a of e.checks)switch(a.kind){case"min":I(n,"minLength","number"==typeof n.minLength?Math.max(n.minLength,a.value):a.value,a.message,t);break;case"max":I(n,"maxLength","number"==typeof n.maxLength?Math.min(n.maxLength,a.value):a.value,a.message,t);break;case"email":switch(t.emailStrategy){case"format:email":P(n,"email",a.message,t);break;case"format:idn-email":P(n,"idn-email",a.message,t);break;case"pattern:zod":D(n,N.email,a.message,t)}break;case"url":P(n,"uri",a.message,t);break;case"uuid":P(n,"uuid",a.message,t);break;case"regex":D(n,a.regex,a.message,t);break;case"cuid":D(n,N.cuid,a.message,t);break;case"cuid2":D(n,N.cuid2,a.message,t);break;case"startsWith":D(n,RegExp(`^${M(a.value,t)}`),a.message,t);break;case"endsWith":D(n,RegExp(`${M(a.value,t)}$`),a.message,t);break;case"datetime":P(n,"date-time",a.message,t);break;case"date":P(n,"date",a.message,t);break;case"time":P(n,"time",a.message,t);break;case"duration":P(n,"duration",a.message,t);break;case"length":I(n,"minLength","number"==typeof n.minLength?Math.max(n.minLength,a.value):a.value,a.message,t),I(n,"maxLength","number"==typeof n.maxLength?Math.min(n.maxLength,a.value):a.value,a.message,t);break;case"includes":D(n,RegExp(M(a.value,t)),a.message,t);break;case"ip":"v6"!==a.version&&P(n,"ipv4",a.message,t),"v4"!==a.version&&P(n,"ipv6",a.message,t);break;case"base64url":D(n,N.base64url,a.message,t);break;case"jwt":D(n,N.jwt,a.message,t);break;case"cidr":"v6"!==a.version&&D(n,N.ipv4Cidr,a.message,t),"v4"!==a.version&&D(n,N.ipv6Cidr,a.message,t);break;case"emoji":D(n,N.emoji(),a.message,t);break;case"ulid":D(n,N.ulid,a.message,t);break;case"base64":switch(t.base64Strategy){case"format:binary":P(n,"binary",a.message,t);break;case"contentEncoding:base64":I(n,"contentEncoding","base64",a.message,t);break;case"pattern:zod":D(n,N.base64,a.message,t)}break;case"nanoid":D(n,N.nanoid,a.message,t)}return n}function M(e,t){return"escape"===t.patternStrategy?function(e){let t="";for(let n=0;n<e.length;n++)O.has(e[n])||(t+="\\"),t+=e[n];return t}(e):e}let O=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function P(e,t,n,a){e.format||e.anyOf?.some(e=>e.format)?(e.anyOf||(e.anyOf=[]),e.format&&(e.anyOf.push({format:e.format,...e.errorMessage&&a.errorMessages&&{errorMessage:{format:e.errorMessage.format}}}),delete e.format,e.errorMessage&&(delete e.errorMessage.format,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.anyOf.push({format:t,...n&&a.errorMessages&&{errorMessage:{format:n}}})):I(e,"format",t,n,a)}function D(e,t,n,a){e.pattern||e.allOf?.some(e=>e.pattern)?(e.allOf||(e.allOf=[]),e.pattern&&(e.allOf.push({pattern:e.pattern,...e.errorMessage&&a.errorMessages&&{errorMessage:{pattern:e.errorMessage.pattern}}}),delete e.pattern,e.errorMessage&&(delete e.errorMessage.pattern,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.allOf.push({pattern:q(t,a),...n&&a.errorMessages&&{errorMessage:{pattern:n}}})):I(e,"pattern",q(t,a),n,a)}function q(e,t){if(!t.applyRegexFlags||!e.flags)return e.source;let n={i:e.flags.includes("i"),m:e.flags.includes("m"),s:e.flags.includes("s")},a=n.i?e.source.toLowerCase():e.source,r="",o=!1,s=!1,i=!1;for(let e=0;e<a.length;e++){if(o){r+=a[e],o=!1;continue}if(n.i){if(s){if(a[e].match(/[a-z]/)){i?(r+=a[e],r+=`${a[e-2]}-${a[e]}`.toUpperCase(),i=!1):"-"===a[e+1]&&a[e+2]?.match(/[a-z]/)?(r+=a[e],i=!0):r+=`${a[e]}${a[e].toUpperCase()}`;continue}}else if(a[e].match(/[a-z]/)){r+=`[${a[e]}${a[e].toUpperCase()}]`;continue}}if(n.m){if("^"===a[e]){r+=`(^|(?<=[\r
]))`;continue}else if("$"===a[e]){r+=`($|(?=[\r
]))`;continue}}if(n.s&&"."===a[e]){r+=s?`${a[e]}\r
`:`[${a[e]}\r
]`;continue}r+=a[e],"\\"===a[e]?o=!0:s&&"]"===a[e]?s=!1:s||"["!==a[e]||(s=!0)}try{new RegExp(r)}catch{return console.warn(`Could not convert regex pattern at ${t.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),e.source}return r}function $(e,t){if("openAi"===t.target&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),"openApi3"===t.target&&e.keyType?._def.typeName===T.kY.ZodEnum)return{type:"object",required:e.keyType._def.values,properties:e.keyType._def.values.reduce((n,a)=>({...n,[a]:Y(e.valueType._def,{...t,currentPath:[...t.currentPath,"properties",a]})??{}}),{}),additionalProperties:!1};let n={type:"object",additionalProperties:Y(e.valueType._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??{}};if("openApi3"===t.target)return n;if(e.keyType?._def.typeName===T.kY.ZodString&&e.keyType._def.checks?.length){let{type:a,...r}=A(e.keyType._def,t);return{...n,propertyNames:r}}if(e.keyType?._def.typeName===T.kY.ZodEnum)return{...n,propertyNames:{enum:e.keyType._def.values}};if(e.keyType?._def.typeName===T.kY.ZodBranded&&e.keyType._def.type._def.typeName===T.kY.ZodString&&e.keyType._def.type._def.checks?.length){let{type:a,...r}=j(e.keyType._def,t);return{...n,propertyNames:r}}return n}let U={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"},F=(e,t)=>{let n=(e.options instanceof Map?Array.from(e.options.values()):e.options).map((e,n)=>Y(e._def,{...t,currentPath:[...t.currentPath,"anyOf",`${n}`]})).filter(e=>!!e&&(!t.strictUnions||"object"==typeof e&&Object.keys(e).length>0));return n.length?{anyOf:n}:void 0},Z=(e,t)=>{if(t.currentPath.toString()===t.propertyPath?.toString())return Y(e.innerType._def,t);let n=Y(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","1"]});return n?{anyOf:[{not:{}},n]}:{}},J=(e,t)=>{if("input"===t.pipeStrategy)return Y(e.in._def,t);if("output"===t.pipeStrategy)return Y(e.out._def,t);let n=Y(e.in._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),a=Y(e.out._def,{...t,currentPath:[...t.currentPath,"allOf",n?"1":"0"]});return{allOf:[n,a].filter(e=>void 0!==e)}},L=(e,t)=>Y(e.innerType._def,t);function Y(e,t,n=!1){let a=t.seen.get(e);if(t.override){let r=t.override?.(e,t,a,n);if(r!==w)return r}if(a&&!n){let e=B(a,t);if(void 0!==e)return e}let r={def:e,path:t.currentPath,jsonSchema:void 0};t.seen.set(e,r);let o=G(e,e.typeName,t);return o&&K(e,t,o),r.jsonSchema=o,o}let B=(e,t)=>{switch(t.$refStrategy){case"root":return{$ref:e.path.join("/")};case"relative":return{$ref:V(t.currentPath,e.path)};case"none":case"seen":if(e.path.length<t.currentPath.length&&e.path.every((e,n)=>t.currentPath[n]===e))return console.warn(`Recursive reference detected at ${t.currentPath.join("/")}! Defaulting to any`),{};return"seen"===t.$refStrategy?{}:void 0}},V=(e,t)=>{let n=0;for(;n<e.length&&n<t.length&&e[n]===t[n];n++);return[(e.length-n).toString(),...t.slice(n)].join("/")},G=(e,t,n)=>{switch(t){case T.kY.ZodString:return A(e,n);case T.kY.ZodNumber:return function(e,t){let n={type:"number"};if(!e.checks)return n;for(let a of e.checks)switch(a.kind){case"int":n.type="integer",S(n,"type",a.message,t);break;case"min":"jsonSchema7"===t.target?a.inclusive?I(n,"minimum",a.value,a.message,t):I(n,"exclusiveMinimum",a.value,a.message,t):(a.inclusive||(n.exclusiveMinimum=!0),I(n,"minimum",a.value,a.message,t));break;case"max":"jsonSchema7"===t.target?a.inclusive?I(n,"maximum",a.value,a.message,t):I(n,"exclusiveMaximum",a.value,a.message,t):(a.inclusive||(n.exclusiveMaximum=!0),I(n,"maximum",a.value,a.message,t));break;case"multipleOf":I(n,"multipleOf",a.value,a.message,t)}return n}(e,n);case T.kY.ZodObject:return function(e,t){let n="openAi"===t.target,a={type:"object",...Object.entries(e.shape()).reduce((e,[a,r])=>{if(void 0===r||void 0===r._def)return e;let o=r.isOptional();o&&n&&(r instanceof T.Ii&&(r=r._def.innerType),r.isNullable()||(r=r.nullable()),o=!1);let s=Y(r._def,{...t,currentPath:[...t.currentPath,"properties",a],propertyPath:[...t.currentPath,"properties",a]});return void 0===s?e:{properties:{...e.properties,[a]:s},required:o?e.required:[...e.required,a]}},{properties:{},required:[]}),additionalProperties:"strict"===t.removeAdditionalStrategy?"ZodNever"===e.catchall._def.typeName?"strict"!==e.unknownKeys:Y(e.catchall._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??!0:"ZodNever"===e.catchall._def.typeName?"passthrough"===e.unknownKeys:Y(e.catchall._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??!0};return a.required.length||delete a.required,a}(e,n);case T.kY.ZodBigInt:return function(e,t){let n={type:"integer",format:"int64"};if(!e.checks)return n;for(let a of e.checks)switch(a.kind){case"min":"jsonSchema7"===t.target?a.inclusive?I(n,"minimum",a.value,a.message,t):I(n,"exclusiveMinimum",a.value,a.message,t):(a.inclusive||(n.exclusiveMinimum=!0),I(n,"minimum",a.value,a.message,t));break;case"max":"jsonSchema7"===t.target?a.inclusive?I(n,"maximum",a.value,a.message,t):I(n,"exclusiveMaximum",a.value,a.message,t):(a.inclusive||(n.exclusiveMaximum=!0),I(n,"maximum",a.value,a.message,t));break;case"multipleOf":I(n,"multipleOf",a.value,a.message,t)}return n}(e,n);case T.kY.ZodBoolean:return{type:"boolean"};case T.kY.ZodDate:return function e(t,n,a){let r=a??n.dateStrategy;if(Array.isArray(r))return{anyOf:r.map((a,r)=>e(t,n,a))};switch(r){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":return R(t,n)}}(e,n);case T.kY.ZodUndefined:return{not:{}};case T.kY.ZodNull:return"openApi3"===n.target?{enum:["null"],nullable:!0}:{type:"null"};case T.kY.ZodArray:return function(e,t){let n={type:"array"};return e.type?._def&&e.type?._def?.typeName!==T.kY.ZodAny&&(n.items=Y(e.type._def,{...t,currentPath:[...t.currentPath,"items"]})),e.minLength&&I(n,"minItems",e.minLength.value,e.minLength.message,t),e.maxLength&&I(n,"maxItems",e.maxLength.value,e.maxLength.message,t),e.exactLength&&(I(n,"minItems",e.exactLength.value,e.exactLength.message,t),I(n,"maxItems",e.exactLength.value,e.exactLength.message,t)),n}(e,n);case T.kY.ZodUnion:case T.kY.ZodDiscriminatedUnion:return function(e,t){if("openApi3"===t.target)return F(e,t);let n=e.options instanceof Map?Array.from(e.options.values()):e.options;if(n.every(e=>e._def.typeName in U&&(!e._def.checks||!e._def.checks.length))){let e=n.reduce((e,t)=>{let n=U[t._def.typeName];return n&&!e.includes(n)?[...e,n]:e},[]);return{type:e.length>1?e:e[0]}}if(n.every(e=>"ZodLiteral"===e._def.typeName&&!e.description)){let e=n.reduce((e,t)=>{let n=typeof t._def.value;switch(n){case"string":case"number":case"boolean":return[...e,n];case"bigint":return[...e,"integer"];case"object":if(null===t._def.value)return[...e,"null"];default:return e}},[]);if(e.length===n.length){let t=e.filter((e,t,n)=>n.indexOf(e)===t);return{type:t.length>1?t:t[0],enum:n.reduce((e,t)=>e.includes(t._def.value)?e:[...e,t._def.value],[])}}}else if(n.every(e=>"ZodEnum"===e._def.typeName))return{type:"string",enum:n.reduce((e,t)=>[...e,...t._def.values.filter(t=>!e.includes(t))],[])};return F(e,t)}(e,n);case T.kY.ZodIntersection:return function(e,t){let n=[Y(e.left._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),Y(e.right._def,{...t,currentPath:[...t.currentPath,"allOf","1"]})].filter(e=>!!e),a="jsonSchema2019-09"===t.target?{unevaluatedProperties:!1}:void 0,r=[];return n.forEach(e=>{if(C(e))r.push(...e.allOf),void 0===e.unevaluatedProperties&&(a=void 0);else{let t=e;if("additionalProperties"in e&&!1===e.additionalProperties){let{additionalProperties:n,...a}=e;t=a}else a=void 0;r.push(t)}}),r.length?{allOf:r,...a}:void 0}(e,n);case T.kY.ZodTuple:return function(e,t){return e.rest?{type:"array",minItems:e.items.length,items:e.items.map((e,n)=>Y(e._def,{...t,currentPath:[...t.currentPath,"items",`${n}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[]),additionalItems:Y(e.rest._def,{...t,currentPath:[...t.currentPath,"additionalItems"]})}:{type:"array",minItems:e.items.length,maxItems:e.items.length,items:e.items.map((e,n)=>Y(e._def,{...t,currentPath:[...t.currentPath,"items",`${n}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[])}}(e,n);case T.kY.ZodRecord:return $(e,n);case T.kY.ZodLiteral:return function(e,t){let n=typeof e.value;return"bigint"!==n&&"number"!==n&&"boolean"!==n&&"string"!==n?{type:Array.isArray(e.value)?"array":"object"}:"openApi3"===t.target?{type:"bigint"===n?"integer":n,enum:[e.value]}:{type:"bigint"===n?"integer":n,const:e.value}}(e,n);case T.kY.ZodEnum:return{type:"string",enum:Array.from(e.values)};case T.kY.ZodNativeEnum:return function(e){let t=e.values,n=Object.keys(e.values).filter(e=>"number"!=typeof t[t[e]]).map(e=>t[e]),a=Array.from(new Set(n.map(e=>typeof e)));return{type:1===a.length?"string"===a[0]?"string":"number":["string","number"],enum:n}}(e);case T.kY.ZodNullable:return function(e,t){if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(e.innerType._def.typeName)&&(!e.innerType._def.checks||!e.innerType._def.checks.length))return"openApi3"===t.target?{type:U[e.innerType._def.typeName],nullable:!0}:{type:[U[e.innerType._def.typeName],"null"]};if("openApi3"===t.target){let n=Y(e.innerType._def,{...t,currentPath:[...t.currentPath]});return n&&"$ref"in n?{allOf:[n],nullable:!0}:n&&{...n,nullable:!0}}let n=Y(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","0"]});return n&&{anyOf:[n,{type:"null"}]}}(e,n);case T.kY.ZodOptional:return Z(e,n);case T.kY.ZodMap:return function(e,t){return"record"===t.mapStrategy?$(e,t):{type:"array",maxItems:125,items:{type:"array",items:[Y(e.keyType._def,{...t,currentPath:[...t.currentPath,"items","items","0"]})||{},Y(e.valueType._def,{...t,currentPath:[...t.currentPath,"items","items","1"]})||{}],minItems:2,maxItems:2}}}(e,n);case T.kY.ZodSet:return function(e,t){let n={type:"array",uniqueItems:!0,items:Y(e.valueType._def,{...t,currentPath:[...t.currentPath,"items"]})};return e.minSize&&I(n,"minItems",e.minSize.value,e.minSize.message,t),e.maxSize&&I(n,"maxItems",e.maxSize.value,e.maxSize.message,t),n}(e,n);case T.kY.ZodLazy:return Y(e.getter()._def,n);case T.kY.ZodPromise:return Y(e.type._def,n);case T.kY.ZodNaN:case T.kY.ZodNever:return{not:{}};case T.kY.ZodEffects:return function(e,t){return"input"===t.effectStrategy?Y(e.schema._def,t):{}}(e,n);case T.kY.ZodAny:case T.kY.ZodUnknown:return{};case T.kY.ZodDefault:return function(e,t){return{...Y(e.innerType._def,t),default:e.defaultValue()}}(e,n);case T.kY.ZodBranded:return j(e,n);case T.kY.ZodReadonly:return L(e,n);case T.kY.ZodCatch:return E(e,n);case T.kY.ZodPipeline:return J(e,n);case T.kY.ZodFunction:case T.kY.ZodVoid:case T.kY.ZodSymbol:default:return}},K=(e,t,n)=>(e.description&&(n.description=e.description,t.markdownDescription&&(n.markdownDescription=e.description)),n),W=(e,t)=>{let n=z(t),a="object"==typeof t&&t.definitions?Object.entries(t.definitions).reduce((e,[t,a])=>({...e,[t]:Y(a._def,{...n,currentPath:[...n.basePath,n.definitionPath,t]},!0)??{}}),{}):void 0,r="string"==typeof t?t:t?.nameStrategy==="title"?void 0:t?.name,o=Y(e._def,void 0===r?n:{...n,currentPath:[...n.basePath,n.definitionPath,r]},!1)??{},s="object"==typeof t&&void 0!==t.name&&"title"===t.nameStrategy?t.name:void 0;void 0!==s&&(o.title=s);let i=void 0===r?a?{...o,[n.definitionPath]:a}:o:{$ref:[..."relative"===n.$refStrategy?[]:n.basePath,n.definitionPath,r].join("/"),[n.definitionPath]:{...a,[r]:o}};return"jsonSchema7"===n.target?i.$schema="http://json-schema.org/draft-07/schema#":("jsonSchema2019-09"===n.target||"openAi"===n.target)&&(i.$schema="https://json-schema.org/draft/2019-09/schema#"),"openAi"===n.target&&("anyOf"in i||"oneOf"in i||"allOf"in i||"type"in i&&Array.isArray(i.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),i};var H={code:"0",name:"text",parse:e=>{if("string"!=typeof e)throw Error('"text" parts expect a string value.');return{type:"text",value:e}}},X={code:"3",name:"error",parse:e=>{if("string"!=typeof e)throw Error('"error" parts expect a string value.');return{type:"error",value:e}}},Q={code:"4",name:"assistant_message",parse:e=>{if(null==e||"object"!=typeof e||!("id"in e)||!("role"in e)||!("content"in e)||"string"!=typeof e.id||"string"!=typeof e.role||"assistant"!==e.role||!Array.isArray(e.content)||!e.content.every(e=>null!=e&&"object"==typeof e&&"type"in e&&"text"===e.type&&"text"in e&&null!=e.text&&"object"==typeof e.text&&"value"in e.text&&"string"==typeof e.text.value))throw Error('"assistant_message" parts expect an object with an "id", "role", and "content" property.');return{type:"assistant_message",value:e}}},ee={code:"5",name:"assistant_control_data",parse:e=>{if(null==e||"object"!=typeof e||!("threadId"in e)||!("messageId"in e)||"string"!=typeof e.threadId||"string"!=typeof e.messageId)throw Error('"assistant_control_data" parts expect an object with a "threadId" and "messageId" property.');return{type:"assistant_control_data",value:{threadId:e.threadId,messageId:e.messageId}}}},et={code:"6",name:"data_message",parse:e=>{if(null==e||"object"!=typeof e||!("role"in e)||!("data"in e)||"string"!=typeof e.role||"data"!==e.role)throw Error('"data_message" parts expect an object with a "role" and "data" property.');return{type:"data_message",value:e}}},en=[H,X,Q,ee,et],ea={[H.code]:H,[X.code]:X,[Q.code]:Q,[ee.code]:ee,[et.code]:et};H.name,H.code,X.name,X.code,Q.name,Q.code,ee.name,ee.code,et.name,et.code;var er=en.map(e=>e.code);function eo(e){if(void 0===e)return{value:void 0,state:"undefined-input"};let t=(0,_.N8)({text:e});return t.success?{value:t.value,state:"successful-parse"}:(t=(0,_.N8)({text:function(e){let t=["ROOT"],n=-1,a=null;function r(e,r,o){switch(e){case'"':n=r,t.pop(),t.push(o),t.push("INSIDE_STRING");break;case"f":case"t":case"n":n=r,a=r,t.pop(),t.push(o),t.push("INSIDE_LITERAL");break;case"-":t.pop(),t.push(o),t.push("INSIDE_NUMBER");break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n=r,t.pop(),t.push(o),t.push("INSIDE_NUMBER");break;case"{":n=r,t.pop(),t.push(o),t.push("INSIDE_OBJECT_START");break;case"[":n=r,t.pop(),t.push(o),t.push("INSIDE_ARRAY_START")}}function o(e,a){switch(e){case",":t.pop(),t.push("INSIDE_OBJECT_AFTER_COMMA");break;case"}":n=a,t.pop()}}function s(e,a){switch(e){case",":t.pop(),t.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":n=a,t.pop()}}for(let i=0;i<e.length;i++){let l=e[i];switch(t[t.length-1]){case"ROOT":r(l,i,"FINISH");break;case"INSIDE_OBJECT_START":switch(l){case'"':t.pop(),t.push("INSIDE_OBJECT_KEY");break;case"}":n=i,t.pop()}break;case"INSIDE_OBJECT_AFTER_COMMA":'"'===l&&(t.pop(),t.push("INSIDE_OBJECT_KEY"));break;case"INSIDE_OBJECT_KEY":'"'===l&&(t.pop(),t.push("INSIDE_OBJECT_AFTER_KEY"));break;case"INSIDE_OBJECT_AFTER_KEY":":"===l&&(t.pop(),t.push("INSIDE_OBJECT_BEFORE_VALUE"));break;case"INSIDE_OBJECT_BEFORE_VALUE":r(l,i,"INSIDE_OBJECT_AFTER_VALUE");break;case"INSIDE_OBJECT_AFTER_VALUE":o(l,i);break;case"INSIDE_STRING":switch(l){case'"':t.pop(),n=i;break;case"\\":t.push("INSIDE_STRING_ESCAPE");break;default:n=i}break;case"INSIDE_ARRAY_START":"]"===l?(n=i,t.pop()):(n=i,r(l,i,"INSIDE_ARRAY_AFTER_VALUE"));break;case"INSIDE_ARRAY_AFTER_VALUE":switch(l){case",":t.pop(),t.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":n=i,t.pop();break;default:n=i}break;case"INSIDE_ARRAY_AFTER_COMMA":r(l,i,"INSIDE_ARRAY_AFTER_VALUE");break;case"INSIDE_STRING_ESCAPE":t.pop(),n=i;break;case"INSIDE_NUMBER":switch(l){case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n=i;break;case"e":case"E":case"-":case".":break;case",":t.pop(),"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&s(l,i),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]&&o(l,i);break;case"}":t.pop(),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]&&o(l,i);break;case"]":t.pop(),"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&s(l,i);break;default:t.pop()}break;case"INSIDE_LITERAL":{let r=e.substring(a,i+1);"false".startsWith(r)||"true".startsWith(r)||"null".startsWith(r)?n=i:(t.pop(),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]?o(l,i):"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&s(l,i))}}}let i=e.slice(0,n+1);for(let n=t.length-1;n>=0;n--)switch(t[n]){case"INSIDE_STRING":i+='"';break;case"INSIDE_OBJECT_KEY":case"INSIDE_OBJECT_AFTER_KEY":case"INSIDE_OBJECT_AFTER_COMMA":case"INSIDE_OBJECT_START":case"INSIDE_OBJECT_BEFORE_VALUE":case"INSIDE_OBJECT_AFTER_VALUE":i+="}";break;case"INSIDE_ARRAY_START":case"INSIDE_ARRAY_AFTER_COMMA":case"INSIDE_ARRAY_AFTER_VALUE":i+="]";break;case"INSIDE_LITERAL":{let t=e.substring(a,e.length);"true".startsWith(t)?i+="true".slice(t.length):"false".startsWith(t)?i+="false".slice(t.length):"null".startsWith(t)&&(i+="null".slice(t.length))}}return i}(e)})).success?{value:t.value,state:"repaired-parse"}:{value:void 0,state:"failed-parse"}}var es=[{code:"0",name:"text",parse:e=>{if("string"!=typeof e)throw Error('"text" parts expect a string value.');return{type:"text",value:e}}},{code:"2",name:"data",parse:e=>{if(!Array.isArray(e))throw Error('"data" parts expect an array value.');return{type:"data",value:e}}},{code:"3",name:"error",parse:e=>{if("string"!=typeof e)throw Error('"error" parts expect a string value.');return{type:"error",value:e}}},{code:"8",name:"message_annotations",parse:e=>{if(!Array.isArray(e))throw Error('"message_annotations" parts expect an array value.');return{type:"message_annotations",value:e}}},{code:"9",name:"tool_call",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("toolName"in e)||"string"!=typeof e.toolName||!("args"in e)||"object"!=typeof e.args)throw Error('"tool_call" parts expect an object with a "toolCallId", "toolName", and "args" property.');return{type:"tool_call",value:e}}},{code:"a",name:"tool_result",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("result"in e))throw Error('"tool_result" parts expect an object with a "toolCallId" and a "result" property.');return{type:"tool_result",value:e}}},{code:"b",name:"tool_call_streaming_start",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("toolName"in e)||"string"!=typeof e.toolName)throw Error('"tool_call_streaming_start" parts expect an object with a "toolCallId" and "toolName" property.');return{type:"tool_call_streaming_start",value:e}}},{code:"c",name:"tool_call_delta",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("argsTextDelta"in e)||"string"!=typeof e.argsTextDelta)throw Error('"tool_call_delta" parts expect an object with a "toolCallId" and "argsTextDelta" property.');return{type:"tool_call_delta",value:e}}},{code:"d",name:"finish_message",parse:e=>{if(null==e||"object"!=typeof e||!("finishReason"in e)||"string"!=typeof e.finishReason)throw Error('"finish_message" parts expect an object with a "finishReason" property.');let t={finishReason:e.finishReason};return"usage"in e&&null!=e.usage&&"object"==typeof e.usage&&"promptTokens"in e.usage&&"completionTokens"in e.usage&&(t.usage={promptTokens:"number"==typeof e.usage.promptTokens?e.usage.promptTokens:Number.NaN,completionTokens:"number"==typeof e.usage.completionTokens?e.usage.completionTokens:Number.NaN}),{type:"finish_message",value:t}}},{code:"e",name:"finish_step",parse:e=>{if(null==e||"object"!=typeof e||!("finishReason"in e)||"string"!=typeof e.finishReason)throw Error('"finish_step" parts expect an object with a "finishReason" property.');let t={finishReason:e.finishReason,isContinued:!1};return"usage"in e&&null!=e.usage&&"object"==typeof e.usage&&"promptTokens"in e.usage&&"completionTokens"in e.usage&&(t.usage={promptTokens:"number"==typeof e.usage.promptTokens?e.usage.promptTokens:Number.NaN,completionTokens:"number"==typeof e.usage.completionTokens?e.usage.completionTokens:Number.NaN}),"isContinued"in e&&"boolean"==typeof e.isContinued&&(t.isContinued=e.isContinued),{type:"finish_step",value:t}}},{code:"f",name:"start_step",parse:e=>{if(null==e||"object"!=typeof e||!("messageId"in e)||"string"!=typeof e.messageId)throw Error('"start_step" parts expect an object with an "id" property.');return{type:"start_step",value:{messageId:e.messageId}}}},{code:"g",name:"reasoning",parse:e=>{if("string"!=typeof e)throw Error('"reasoning" parts expect a string value.');return{type:"reasoning",value:e}}},{code:"h",name:"source",parse:e=>{if(null==e||"object"!=typeof e)throw Error('"source" parts expect a Source object.');return{type:"source",value:e}}},{code:"i",name:"redacted_reasoning",parse:e=>{if(null==e||"object"!=typeof e||!("data"in e)||"string"!=typeof e.data)throw Error('"redacted_reasoning" parts expect an object with a "data" property.');return{type:"redacted_reasoning",value:{data:e.data}}}},{code:"j",name:"reasoning_signature",parse:e=>{if(null==e||"object"!=typeof e||!("signature"in e)||"string"!=typeof e.signature)throw Error('"reasoning_signature" parts expect an object with a "signature" property.');return{type:"reasoning_signature",value:{signature:e.signature}}}},{code:"k",name:"file",parse:e=>{if(null==e||"object"!=typeof e||!("data"in e)||"string"!=typeof e.data||!("mimeType"in e)||"string"!=typeof e.mimeType)throw Error('"file" parts expect an object with a "data" and "mimeType" property.');return{type:"file",value:e}}}],ei=Object.fromEntries(es.map(e=>[e.code,e]));Object.fromEntries(es.map(e=>[e.name,e.code]));var el=es.map(e=>e.code),eu=e=>{let t=e.indexOf(":");if(-1===t)throw Error("Failed to parse stream string. No separator found.");let n=e.slice(0,t);if(!el.includes(n))throw Error(`Failed to parse stream string. Invalid code ${n}.`);let a=JSON.parse(e.slice(t+1));return ei[n].parse(a)};function ep(e,t){let n=es.find(t=>t.name===e);if(!n)throw Error(`Invalid stream part type: ${e}`);return`${n.code}:${JSON.stringify(t)}
`}async function ec({stream:e,onTextPart:t,onReasoningPart:n,onReasoningSignaturePart:a,onRedactedReasoningPart:r,onSourcePart:o,onFilePart:s,onDataPart:i,onErrorPart:l,onToolCallStreamingStartPart:u,onToolCallDeltaPart:p,onToolCallPart:c,onToolResultPart:d,onMessageAnnotationsPart:m,onFinishMessagePart:g,onFinishStepPart:h,onStartStepPart:f}){let y=e.getReader(),v=new TextDecoder,b=[],_=0;for(;;){let{value:e}=await y.read();if(e&&(b.push(e),_+=e.length,10!==e[e.length-1]))continue;if(0===b.length)break;let w=function(e,t){let n=new Uint8Array(t),a=0;for(let t of e)n.set(t,a),a+=t.length;return e.length=0,n}(b,_);for(let{type:e,value:y}of(_=0,v.decode(w,{stream:!0}).split("\n").filter(e=>""!==e).map(eu)))switch(e){case"text":await (null==t?void 0:t(y));break;case"reasoning":await (null==n?void 0:n(y));break;case"reasoning_signature":await (null==a?void 0:a(y));break;case"redacted_reasoning":await (null==r?void 0:r(y));break;case"file":await (null==s?void 0:s(y));break;case"source":await (null==o?void 0:o(y));break;case"data":await (null==i?void 0:i(y));break;case"error":await (null==l?void 0:l(y));break;case"message_annotations":await (null==m?void 0:m(y));break;case"tool_call_streaming_start":await (null==u?void 0:u(y));break;case"tool_call_delta":await (null==p?void 0:p(y));break;case"tool_call":await (null==c?void 0:c(y));break;case"tool_result":await (null==d?void 0:d(y));break;case"finish_message":await (null==g?void 0:g(y));break;case"finish_step":await (null==h?void 0:h(y));break;case"start_step":await (null==f?void 0:f(y));break;default:throw Error(`Unknown stream part type: ${e}`)}}}async function ed({stream:e,onTextPart:t}){let n=e.pipeThrough(new TextDecoderStream).getReader();for(;;){let{done:e,value:a}=await n.read();if(e)break;await t(a)}}var em=Symbol.for("vercel.ai.schema");function eg(e){return"object"==typeof e&&null!==e&&em in e&&!0===e[em]&&"jsonSchema"in e&&"validate"in e?e:function(e,{validate:t}={}){return{[em]:!0,_type:void 0,[_.eu]:!0,jsonSchema:e,validate:t}}(W(e,{$refStrategy:"none",target:"jsonSchema7"}),{validate:t=>{let n=e.safeParse(t);return n.success?{success:!0,value:n.data}:{success:!1,error:n.error}}})}var eh=n(87113),ef=n(94645),ey=n(80281),ev=Object.defineProperty,eb=(e,t)=>{for(var n in t)ev(e,n,{get:t[n],enumerable:!0})};function e_(e,{contentType:t,dataStreamVersion:n}){let a=new Headers(null!=e?e:{});return a.has("Content-Type")||a.set("Content-Type",t),void 0!==n&&a.set("X-Vercel-AI-Data-Stream",n),a}function ew({status:e,statusText:t,headers:n,execute:a,onError:r}){return new Response((function({execute:e,onError:t=()=>"An error occurred."}){let n,a=[],r=new ReadableStream({start(e){n=e}});function o(e){try{n.enqueue(e)}catch(e){}}try{let n=e({write(e){o(e)},writeData(e){o(ep("data",[e]))},writeMessageAnnotation(e){o(ep("message_annotations",[e]))},writeSource(e){o(ep("source",e))},merge(e){a.push((async()=>{let t=e.getReader();for(;;){let{done:e,value:n}=await t.read();if(e)break;o(n)}})().catch(e=>{o(ep("error",t(e)))}))},onError:t});n&&a.push(n.catch(e=>{o(ep("error",t(e)))}))}catch(e){o(ep("error",t(e)))}return new Promise(async e=>{for(;a.length>0;)await a.shift();e()}).finally(()=>{try{n.close()}catch(e){}}),r})({execute:a,onError:r}).pipeThrough(new TextEncoderStream),{status:e,statusText:t,headers:e_(n,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}function ex(e,{contentType:t,dataStreamVersion:n}){let a={};if(null!=e)for(let[t,n]of Object.entries(e))a[t]=n;return null==a["Content-Type"]&&(a["Content-Type"]=t),void 0!==n&&(a["X-Vercel-AI-Data-Stream"]=n),a}function ek({response:e,status:t,statusText:n,headers:a,stream:r}){e.writeHead(null!=t?t:200,n,a);let o=r.getReader();(async()=>{try{for(;;){let{done:t,value:n}=await o.read();if(t)break;e.write(n)}}catch(e){throw e}finally{e.end()}})()}var ez=class extends eh.bD{constructor(){super({name:"AI_UnsupportedModelVersionError",message:'Unsupported model version. AI SDK 4 only supports models that implement specification version "v1". Please upgrade to AI SDK 5 to use this model.'})}},eT="AI_InvalidArgumentError",eS=`vercel.ai.error.${eT}`,eI=Symbol.for(eS),ej=class extends eh.bD{constructor({parameter:e,value:t,message:n}){super({name:eT,message:`Invalid argument for parameter ${e}: ${n}`}),this[r]=!0,this.parameter=e,this.value=t}static isInstance(e){return eh.bD.hasMarker(e,eS)}};r=eI;var eE="AI_RetryError",eR=`vercel.ai.error.${eE}`,eC=Symbol.for(eR),eN=class extends eh.bD{constructor({message:e,reason:t,errors:n}){super({name:eE,message:e}),this[o]=!0,this.reason=t,this.errors=n,this.lastError=n[n.length-1]}static isInstance(e){return eh.bD.hasMarker(e,eR)}};o=eC;var eA=({maxRetries:e=2,initialDelayInMs:t=2e3,backoffFactor:n=2}={})=>async a=>eM(a,{maxRetries:e,delayInMs:t,backoffFactor:n});async function eM(e,{maxRetries:t,delayInMs:n,backoffFactor:a},r=[]){try{return await e()}catch(l){if((0,_.zf)(l)||0===t)throw l;let o=(0,_.u1)(l),s=[...r,l],i=s.length;if(i>t)throw new eN({message:`Failed after ${i} attempts. Last error: ${o}`,reason:"maxRetriesExceeded",errors:s});if(l instanceof Error&&eh.hL.isInstance(l)&&!0===l.isRetryable&&i<=t)return await (0,_.cb)(n),eM(e,{maxRetries:t,delayInMs:a*n,backoffFactor:a},s);if(1===i)throw l;throw new eN({message:`Failed after ${i} attempts with non-retryable error: '${o}'`,reason:"errorNotRetryable",errors:s})}}function eO({maxRetries:e}){if(null!=e){if(!Number.isInteger(e))throw new ej({parameter:"maxRetries",value:e,message:"maxRetries must be an integer"});if(e<0)throw new ej({parameter:"maxRetries",value:e,message:"maxRetries must be >= 0"})}let t=null!=e?e:2;return{maxRetries:t,retry:eA({maxRetries:t})}}function eP({operationId:e,telemetry:t}){return{"operation.name":`${e}${(null==t?void 0:t.functionId)!=null?` ${t.functionId}`:""}`,"resource.name":null==t?void 0:t.functionId,"ai.operationId":e,"ai.telemetry.functionId":null==t?void 0:t.functionId}}function eD({model:e,settings:t,telemetry:n,headers:a}){var r;return{"ai.model.provider":e.provider,"ai.model.id":e.modelId,...Object.entries(t).reduce((e,[t,n])=>(e[`ai.settings.${t}`]=n,e),{}),...Object.entries(null!=(r=null==n?void 0:n.metadata)?r:{}).reduce((e,[t,n])=>(e[`ai.telemetry.metadata.${t}`]=n,e),{}),...Object.entries(null!=a?a:{}).reduce((e,[t,n])=>(void 0!==n&&(e[`ai.request.headers.${t}`]=n),e),{})}}var eq={startSpan:()=>e$,startActiveSpan:(e,t,n,a)=>"function"==typeof t?t(e$):"function"==typeof n?n(e$):"function"==typeof a?a(e$):void 0},e$={spanContext:()=>eU,setAttribute(){return this},setAttributes(){return this},addEvent(){return this},addLink(){return this},addLinks(){return this},setStatus(){return this},updateName(){return this},end(){return this},isRecording:()=>!1,recordException(){return this}},eU={traceId:"",spanId:"",traceFlags:0};function eF({isEnabled:e=!1,tracer:t}={}){return e?t||ef.u.getTracer("ai"):eq}function eZ({name:e,tracer:t,attributes:n,fn:a,endWhenDone:r=!0}){return t.startActiveSpan(e,{attributes:n},async e=>{try{let t=await a(e);return r&&e.end(),t}catch(t){try{eJ(e,t)}finally{e.end()}throw t}})}function eJ(e,t){t instanceof Error?(e.recordException({name:t.name,message:t.message,stack:t.stack}),e.setStatus({code:ey.s.ERROR,message:t.message})):e.setStatus({code:ey.s.ERROR})}function eL({telemetry:e,attributes:t}){return(null==e?void 0:e.isEnabled)!==!0?{}:Object.entries(t).reduce((t,[n,a])=>{if(void 0===a)return t;if("object"==typeof a&&"input"in a&&"function"==typeof a.input){if((null==e?void 0:e.recordInputs)===!1)return t;let r=a.input();return void 0===r?t:{...t,[n]:r}}if("object"==typeof a&&"output"in a&&"function"==typeof a.output){if((null==e?void 0:e.recordOutputs)===!1)return t;let r=a.output();return void 0===r?t:{...t,[n]:r}}return{...t,[n]:a}},{})}var eY="AI_NoImageGeneratedError",eB=`vercel.ai.error.${eY}`,eV=Symbol.for(eB);eh.bD,s=eV;var eG=class{constructor({data:e,mimeType:t}){let n=e instanceof Uint8Array;this.base64Data=n?void 0:e,this.uint8ArrayData=n?e:void 0,this.mimeType=t}get base64(){return null==this.base64Data&&(this.base64Data=(0,_.n_)(this.uint8ArrayData)),this.base64Data}get uint8Array(){return null==this.uint8ArrayData&&(this.uint8ArrayData=(0,_.Z9)(this.base64Data)),this.uint8ArrayData}},eK=class extends eG{constructor(e){super(e),this.type="file"}},eW=[{mimeType:"image/gif",bytesPrefix:[71,73,70],base64Prefix:"R0lG"},{mimeType:"image/png",bytesPrefix:[137,80,78,71],base64Prefix:"iVBORw"},{mimeType:"image/jpeg",bytesPrefix:[255,216],base64Prefix:"/9j/"},{mimeType:"image/webp",bytesPrefix:[82,73,70,70],base64Prefix:"UklGRg"},{mimeType:"image/bmp",bytesPrefix:[66,77],base64Prefix:"Qk"},{mimeType:"image/tiff",bytesPrefix:[73,73,42,0],base64Prefix:"SUkqAA"},{mimeType:"image/tiff",bytesPrefix:[77,77,0,42],base64Prefix:"TU0AKg"},{mimeType:"image/avif",bytesPrefix:[0,0,0,32,102,116,121,112,97,118,105,102],base64Prefix:"AAAAIGZ0eXBhdmlm"},{mimeType:"image/heic",bytesPrefix:[0,0,0,32,102,116,121,112,104,101,105,99],base64Prefix:"AAAAIGZ0eXBoZWlj"}],eH=e=>{let t="string"==typeof e?(0,_.Z9)(e):e,n=(127&t[6])<<21|(127&t[7])<<14|(127&t[8])<<7|127&t[9];return t.slice(n+10)},eX="AI_NoObjectGeneratedError",eQ=`vercel.ai.error.${eX}`,e0=Symbol.for(eQ),e1=class extends eh.bD{constructor({message:e="No object generated.",cause:t,text:n,response:a,usage:r,finishReason:o}){super({name:eX,message:e,cause:t}),this[i]=!0,this.text=n,this.response=a,this.usage=r,this.finishReason=o}static isInstance(e){return eh.bD.hasMarker(e,eQ)}};i=e0;var e4="AI_DownloadError",e2=`vercel.ai.error.${e4}`,e9=Symbol.for(e2),e6=class extends eh.bD{constructor({url:e,statusCode:t,statusText:n,cause:a,message:r=null==a?`Failed to download ${e}: ${t} ${n}`:`Failed to download ${e}: ${a}`}){super({name:e4,message:r,cause:a}),this[l]=!0,this.url=e,this.statusCode=t,this.statusText=n}static isInstance(e){return eh.bD.hasMarker(e,e2)}};async function e8({url:e}){var t;let n=e.toString();try{let e=await fetch(n);if(!e.ok)throw new e6({url:n,statusCode:e.status,statusText:e.statusText});return{data:new Uint8Array(await e.arrayBuffer()),mimeType:null!=(t=e.headers.get("content-type"))?t:void 0}}catch(e){if(e6.isInstance(e))throw e;throw new e6({url:n,cause:e})}}l=e9;var e3="AI_InvalidDataContentError",e7=`vercel.ai.error.${e3}`,e5=Symbol.for(e7),te=class extends eh.bD{constructor({content:e,cause:t,message:n=`Invalid data content. Expected a base64 string, Uint8Array, ArrayBuffer, or Buffer, but got ${typeof e}.`}){super({name:e3,message:n,cause:t}),this[u]=!0,this.content=e}static isInstance(e){return eh.bD.hasMarker(e,e7)}};u=e5;var tt=T.z.union([T.z.string(),T.z.instanceof(Uint8Array),T.z.instanceof(ArrayBuffer),T.z.custom(e=>{var t,n;return null!=(n=null==(t=globalThis.Buffer)?void 0:t.isBuffer(e))&&n},{message:"Must be a Buffer"})]);function tn(e){return"string"==typeof e?e:e instanceof ArrayBuffer?(0,_.n_)(new Uint8Array(e)):(0,_.n_)(e)}function ta(e){if(e instanceof Uint8Array)return e;if("string"==typeof e)try{return(0,_.Z9)(e)}catch(t){throw new te({message:"Invalid data content. Content string is not a base64-encoded media.",content:e,cause:t})}if(e instanceof ArrayBuffer)return new Uint8Array(e);throw new te({content:e})}var tr="AI_InvalidMessageRoleError",to=`vercel.ai.error.${tr}`,ts=Symbol.for(to),ti=class extends eh.bD{constructor({role:e,message:t=`Invalid message role: '${e}'. Must be one of: "system", "user", "assistant", "tool".`}){super({name:tr,message:t}),this[p]=!0,this.role=e}static isInstance(e){return eh.bD.hasMarker(e,to)}};async function tl({prompt:e,modelSupportsImageUrls:t=!0,modelSupportsUrl:n=()=>!1,downloadImplementation:a=e8}){let r=await tu(e.messages,a,t,n);return[...null!=e.system?[{role:"system",content:e.system}]:[],...e.messages.map(e=>(function(e,t){var n,a,r,o,s,i;let l=e.role;switch(l){case"system":return{role:"system",content:e.content,providerMetadata:null!=(n=e.providerOptions)?n:e.experimental_providerMetadata};case"user":if("string"==typeof e.content)return{role:"user",content:[{type:"text",text:e.content}],providerMetadata:null!=(a=e.providerOptions)?a:e.experimental_providerMetadata};return{role:"user",content:e.content.map(e=>(function(e,t){var n,a,r,o;let s,i,l;if("text"===e.type)return{type:"text",text:e.text,providerMetadata:null!=(n=e.providerOptions)?n:e.experimental_providerMetadata};let u=e.mimeType,p=e.type;switch(p){case"image":s=e.image;break;case"file":s=e.data;break;default:throw Error(`Unsupported part type: ${p}`)}try{i="string"==typeof s?new URL(s):s}catch(e){i=s}if(i instanceof URL)if("data:"===i.protocol){let{mimeType:e,base64Content:t}=function(e){try{let[t,n]=e.split(",");return{mimeType:t.split(";")[0].split(":")[1],base64Content:n}}catch(e){return{mimeType:void 0,base64Content:void 0}}}(i.toString());if(null==e||null==t)throw Error(`Invalid data URL format in part ${p}`);u=e,l=ta(t)}else{let e=t[i.toString()];e?(l=e.data,null!=u||(u=e.mimeType)):l=i}else l=ta(i);switch(p){case"image":return l instanceof Uint8Array&&(u=null!=(a=function({data:e,signatures:t}){let n="string"==typeof e&&e.startsWith("SUQz")||"string"!=typeof e&&e.length>10&&73===e[0]&&68===e[1]&&51===e[2]?eH(e):e;for(let e of t)if("string"==typeof n?n.startsWith(e.base64Prefix):n.length>=e.bytesPrefix.length&&e.bytesPrefix.every((e,t)=>n[t]===e))return e.mimeType}({data:l,signatures:eW}))?a:u),{type:"image",image:l,mimeType:u,providerMetadata:null!=(r=e.providerOptions)?r:e.experimental_providerMetadata};case"file":if(null==u)throw Error("Mime type is missing for file part");return{type:"file",data:l instanceof Uint8Array?tn(l):l,filename:e.filename,mimeType:u,providerMetadata:null!=(o=e.providerOptions)?o:e.experimental_providerMetadata}}})(e,t)).filter(e=>"text"!==e.type||""!==e.text),providerMetadata:null!=(r=e.providerOptions)?r:e.experimental_providerMetadata};case"assistant":if("string"==typeof e.content)return{role:"assistant",content:[{type:"text",text:e.content}],providerMetadata:null!=(o=e.providerOptions)?o:e.experimental_providerMetadata};return{role:"assistant",content:e.content.filter(e=>"text"!==e.type||""!==e.text).map(e=>{var t;let n=null!=(t=e.providerOptions)?t:e.experimental_providerMetadata;switch(e.type){case"file":return{type:"file",data:e.data instanceof URL?e.data:tn(e.data),filename:e.filename,mimeType:e.mimeType,providerMetadata:n};case"reasoning":return{type:"reasoning",text:e.text,signature:e.signature,providerMetadata:n};case"redacted-reasoning":return{type:"redacted-reasoning",data:e.data,providerMetadata:n};case"text":return{type:"text",text:e.text,providerMetadata:n};case"tool-call":return{type:"tool-call",toolCallId:e.toolCallId,toolName:e.toolName,args:e.args,providerMetadata:n}}}),providerMetadata:null!=(s=e.providerOptions)?s:e.experimental_providerMetadata};case"tool":return{role:"tool",content:e.content.map(e=>{var t;return{type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,result:e.result,content:e.experimental_content,isError:e.isError,providerMetadata:null!=(t=e.providerOptions)?t:e.experimental_providerMetadata}}),providerMetadata:null!=(i=e.providerOptions)?i:e.experimental_providerMetadata};default:throw new ti({role:l})}})(e,r))]}async function tu(e,t,n,a){let r=e.filter(e=>"user"===e.role).map(e=>e.content).filter(e=>Array.isArray(e)).flat().filter(e=>"image"===e.type||"file"===e.type).filter(e=>"image"!==e.type||!0!==n).map(e=>"image"===e.type?e.image:e.data).map(e=>"string"==typeof e&&(e.startsWith("http:")||e.startsWith("https:"))?new URL(e):e).filter(e=>e instanceof URL).filter(e=>!a(e));return Object.fromEntries((await Promise.all(r.map(async e=>({url:e,data:await t({url:e})})))).map(({url:e,data:t})=>[e.toString(),t]))}function tp({maxTokens:e,temperature:t,topP:n,topK:a,presencePenalty:r,frequencyPenalty:o,stopSequences:s,seed:i}){if(null!=e){if(!Number.isInteger(e))throw new ej({parameter:"maxTokens",value:e,message:"maxTokens must be an integer"});if(e<1)throw new ej({parameter:"maxTokens",value:e,message:"maxTokens must be >= 1"})}if(null!=t&&"number"!=typeof t)throw new ej({parameter:"temperature",value:t,message:"temperature must be a number"});if(null!=n&&"number"!=typeof n)throw new ej({parameter:"topP",value:n,message:"topP must be a number"});if(null!=a&&"number"!=typeof a)throw new ej({parameter:"topK",value:a,message:"topK must be a number"});if(null!=r&&"number"!=typeof r)throw new ej({parameter:"presencePenalty",value:r,message:"presencePenalty must be a number"});if(null!=o&&"number"!=typeof o)throw new ej({parameter:"frequencyPenalty",value:o,message:"frequencyPenalty must be a number"});if(null!=i&&!Number.isInteger(i))throw new ej({parameter:"seed",value:i,message:"seed must be an integer"});return{maxTokens:e,temperature:null!=t?t:0,topP:n,topK:a,presencePenalty:r,frequencyPenalty:o,stopSequences:null!=s&&s.length>0?s:void 0,seed:i}}function tc(e){var t,n,a;let r=[];for(let o of e){let e;try{e=new URL(o.url)}catch(e){throw Error(`Invalid URL: ${o.url}`)}switch(e.protocol){case"http:":case"https:":if(null==(t=o.contentType)?void 0:t.startsWith("image/"))r.push({type:"image",image:e});else{if(!o.contentType)throw Error("If the attachment is not an image, it must specify a content type");r.push({type:"file",data:e,mimeType:o.contentType})}break;case"data:":{let e,t,s;try{[e,t]=o.url.split(","),s=e.split(";")[0].split(":")[1]}catch(e){throw Error(`Error processing data URL: ${o.url}`)}if(null==s||null==t)throw Error(`Invalid data URL format: ${o.url}`);if(null==(n=o.contentType)?void 0:n.startsWith("image/"))r.push({type:"image",image:ta(t)});else if(null==(a=o.contentType)?void 0:a.startsWith("text/"))r.push({type:"text",text:function(e){try{return new TextDecoder().decode(e)}catch(e){throw Error("Error decoding Uint8Array to text")}}(ta(t))});else{if(!o.contentType)throw Error("If the attachment is not an image or text, it must specify a content type");r.push({type:"file",data:t,mimeType:o.contentType})}break}default:throw Error(`Unsupported URL protocol: ${e.protocol}`)}}return r}p=ts;var td="AI_MessageConversionError",tm=`vercel.ai.error.${td}`,tg=Symbol.for(tm),th=class extends eh.bD{constructor({originalMessage:e,message:t}){super({name:td,message:t}),this[c]=!0,this.originalMessage=e}static isInstance(e){return eh.bD.hasMarker(e,tm)}};function tf(e,t){var n,a;let r=null!=(n=null==t?void 0:t.tools)?n:{},o=[];for(let t=0;t<e.length;t++){let n=e[t],s=t===e.length-1,{role:i,content:l,experimental_attachments:u}=n;switch(i){case"system":o.push({role:"system",content:l});break;case"user":if(null==n.parts)o.push({role:"user",content:u?[{type:"text",text:l},...tc(u)]:l});else{let e=n.parts.filter(e=>"text"===e.type).map(e=>({type:"text",text:e.text}));o.push({role:"user",content:u?[...e,...tc(u)]:e})}break;case"assistant":{if(null!=n.parts){let e=function(){let e=[];for(let t of i)switch(t.type){case"file":case"text":e.push(t);break;case"reasoning":for(let n of t.details)switch(n.type){case"text":e.push({type:"reasoning",text:n.text,signature:n.signature});break;case"redacted":e.push({type:"redacted-reasoning",data:n.data})}break;case"tool-invocation":e.push({type:"tool-call",toolCallId:t.toolInvocation.toolCallId,toolName:t.toolInvocation.toolName,args:t.toolInvocation.args});break;default:throw Error(`Unsupported part: ${t}`)}o.push({role:"assistant",content:e});let a=i.filter(e=>"tool-invocation"===e.type).map(e=>e.toolInvocation);a.length>0&&o.push({role:"tool",content:a.map(e=>{if(!("result"in e))throw new th({originalMessage:n,message:"ToolInvocation must have a result: "+JSON.stringify(e)});let{toolCallId:t,toolName:a,result:o}=e,s=r[a];return(null==s?void 0:s.experimental_toToolResultContent)!=null?{type:"tool-result",toolCallId:t,toolName:a,result:s.experimental_toToolResultContent(o),experimental_content:s.experimental_toToolResultContent(o)}:{type:"tool-result",toolCallId:t,toolName:a,result:o}})}),i=[],s=!1,t++},t=0,s=!1,i=[];for(let r of n.parts)switch(r.type){case"text":s&&e(),i.push(r);break;case"file":case"reasoning":i.push(r);break;case"tool-invocation":(null!=(a=r.toolInvocation.step)?a:0)!==t&&e(),i.push(r),s=!0}e();break}let e=n.toolInvocations;if(null==e||0===e.length){o.push({role:"assistant",content:l});break}let t=e.reduce((e,t)=>{var n;return Math.max(e,null!=(n=t.step)?n:0)},0);for(let a=0;a<=t;a++){let t=e.filter(e=>{var t;return(null!=(t=e.step)?t:0)===a});0!==t.length&&(o.push({role:"assistant",content:[...s&&l&&0===a?[{type:"text",text:l}]:[],...t.map(({toolCallId:e,toolName:t,args:n})=>({type:"tool-call",toolCallId:e,toolName:t,args:n}))]}),o.push({role:"tool",content:t.map(e=>{if(!("result"in e))throw new th({originalMessage:n,message:"ToolInvocation must have a result: "+JSON.stringify(e)});let{toolCallId:t,toolName:a,result:o}=e,s=r[a];return(null==s?void 0:s.experimental_toToolResultContent)!=null?{type:"tool-result",toolCallId:t,toolName:a,result:s.experimental_toToolResultContent(o),experimental_content:s.experimental_toToolResultContent(o)}:{type:"tool-result",toolCallId:t,toolName:a,result:o}})}))}l&&!s&&o.push({role:"assistant",content:l});break}case"data":break;default:throw new th({originalMessage:n,message:`Unsupported role: ${i}`})}}return o}c=tg;var ty=T.z.lazy(()=>T.z.union([T.z.null(),T.z.string(),T.z.number(),T.z.boolean(),T.z.record(T.z.string(),ty),T.z.array(ty)])),tv=T.z.record(T.z.string(),T.z.record(T.z.string(),ty)),tb=T.z.array(T.z.union([T.z.object({type:T.z.literal("text"),text:T.z.string()}),T.z.object({type:T.z.literal("image"),data:T.z.string(),mimeType:T.z.string().optional()})])),t_=T.z.object({type:T.z.literal("text"),text:T.z.string(),providerOptions:tv.optional(),experimental_providerMetadata:tv.optional()}),tw=T.z.object({type:T.z.literal("image"),image:T.z.union([tt,T.z.instanceof(URL)]),mimeType:T.z.string().optional(),providerOptions:tv.optional(),experimental_providerMetadata:tv.optional()}),tx=T.z.object({type:T.z.literal("file"),data:T.z.union([tt,T.z.instanceof(URL)]),filename:T.z.string().optional(),mimeType:T.z.string(),providerOptions:tv.optional(),experimental_providerMetadata:tv.optional()}),tk=T.z.object({type:T.z.literal("reasoning"),text:T.z.string(),providerOptions:tv.optional(),experimental_providerMetadata:tv.optional()}),tz=T.z.object({type:T.z.literal("redacted-reasoning"),data:T.z.string(),providerOptions:tv.optional(),experimental_providerMetadata:tv.optional()}),tT=T.z.object({type:T.z.literal("tool-call"),toolCallId:T.z.string(),toolName:T.z.string(),args:T.z.unknown(),providerOptions:tv.optional(),experimental_providerMetadata:tv.optional()}),tS=T.z.object({type:T.z.literal("tool-result"),toolCallId:T.z.string(),toolName:T.z.string(),result:T.z.unknown(),content:tb.optional(),isError:T.z.boolean().optional(),providerOptions:tv.optional(),experimental_providerMetadata:tv.optional()}),tI=T.z.object({role:T.z.literal("system"),content:T.z.string(),providerOptions:tv.optional(),experimental_providerMetadata:tv.optional()}),tj=T.z.object({role:T.z.literal("user"),content:T.z.union([T.z.string(),T.z.array(T.z.union([t_,tw,tx]))]),providerOptions:tv.optional(),experimental_providerMetadata:tv.optional()}),tE=T.z.object({role:T.z.literal("assistant"),content:T.z.union([T.z.string(),T.z.array(T.z.union([t_,tx,tk,tz,tT]))]),providerOptions:tv.optional(),experimental_providerMetadata:tv.optional()}),tR=T.z.object({role:T.z.literal("tool"),content:T.z.array(tS),providerOptions:tv.optional(),experimental_providerMetadata:tv.optional()}),tC=T.z.union([tI,tj,tE,tR]);function tN({prompt:e,tools:t}){if(null==e.prompt&&null==e.messages)throw new eh.M3({prompt:e,message:"prompt or messages must be defined"});if(null!=e.prompt&&null!=e.messages)throw new eh.M3({prompt:e,message:"prompt and messages cannot be defined at the same time"});if(null!=e.system&&"string"!=typeof e.system)throw new eh.M3({prompt:e,message:"system must be a string"});if(null!=e.prompt){if("string"!=typeof e.prompt)throw new eh.M3({prompt:e,message:"prompt must be a string"});return{type:"prompt",system:e.system,messages:[{role:"user",content:e.prompt}]}}if(null!=e.messages){let n="ui-messages"===function(e){if(!Array.isArray(e))throw new eh.M3({prompt:e,message:`messages must be an array of CoreMessage or UIMessage
Received non-array value: ${JSON.stringify(e)}`,cause:e});if(0===e.length)return"messages";let t=e.map(tA);if(t.some(e=>"has-ui-specific-parts"===e))return"ui-messages";let n=t.findIndex(e=>"has-core-specific-parts"!==e&&"message"!==e);if(-1===n)return"messages";throw new eh.M3({prompt:e,message:`messages must be an array of CoreMessage or UIMessage
Received message of type: "${t[n]}" at index ${n}
messages[${n}]: ${JSON.stringify(e[n])}`,cause:e})}(e.messages)?tf(e.messages,{tools:t}):e.messages;if(0===n.length)throw new eh.M3({prompt:e,message:"messages must not be empty"});let a=(0,_.ZZ)({value:n,schema:T.z.array(tC)});if(!a.success)throw new eh.M3({prompt:e,message:`message must be a CoreMessage or a UI message
Validation error: ${a.error.message}`,cause:a.error});return{type:"messages",messages:n,system:e.system}}throw Error("unreachable")}function tA(e){return"object"==typeof e&&null!==e&&("function"===e.role||"data"===e.role||"toolInvocations"in e||"parts"in e||"experimental_attachments"in e)?"has-ui-specific-parts":"object"==typeof e&&null!==e&&"content"in e&&(Array.isArray(e.content)||"experimental_providerMetadata"in e||"providerOptions"in e)?"has-core-specific-parts":"object"==typeof e&&null!==e&&"role"in e&&"content"in e&&"string"==typeof e.content&&["system","user","assistant","tool"].includes(e.role)?"message":"other"}function tM({promptTokens:e,completionTokens:t}){return{promptTokens:e,completionTokens:t,totalTokens:e+t}}function tO(e,t){return{promptTokens:e.promptTokens+t.promptTokens,completionTokens:e.completionTokens+t.completionTokens,totalTokens:e.totalTokens+t.totalTokens}}function tP({prompt:e,schema:t,schemaPrefix:n=null!=t?"JSON schema:":void 0,schemaSuffix:a=null!=t?"You MUST answer with a JSON object that matches the JSON schema above.":"You MUST answer with JSON."}){return[null!=e&&e.length>0?e:void 0,null!=e&&e.length>0?"":void 0,n,null!=t?JSON.stringify(t):void 0,a].filter(e=>null!=e).join("\n")}function tD(e){let t=e.pipeThrough(new TransformStream);return t[Symbol.asyncIterator]=()=>{let e=t.getReader();return{async next(){let{done:t,value:n}=await e.read();return t?{done:!0,value:void 0}:{done:!1,value:n}}}},t}var tq={type:"no-schema",jsonSchema:void 0,validatePartialResult:({value:e,textDelta:t})=>({success:!0,value:{partial:e,textDelta:t}}),validateFinalResult:(e,t)=>void 0===e?{success:!1,error:new e1({message:"No object generated: response did not match schema.",text:t.text,response:t.response,usage:t.usage,finishReason:t.finishReason})}:{success:!0,value:e},createElementStream(){throw new eh.b8({functionality:"element streams in no-schema mode"})}},t$=e=>({type:"object",jsonSchema:e.jsonSchema,validatePartialResult:({value:e,textDelta:t})=>({success:!0,value:{partial:e,textDelta:t}}),validateFinalResult:t=>(0,_.ZZ)({value:t,schema:e}),createElementStream(){throw new eh.b8({functionality:"element streams in object mode"})}}),tU=e=>{let{$schema:t,...n}=e.jsonSchema;return{type:"enum",jsonSchema:{$schema:"http://json-schema.org/draft-07/schema#",type:"object",properties:{elements:{type:"array",items:n}},required:["elements"],additionalProperties:!1},validatePartialResult({value:t,latestObject:n,isFirstDelta:a,isFinalDelta:r}){var o;if(!(0,eh.k9)(t)||!(0,eh.cj)(t.elements))return{success:!1,error:new eh.iM({value:t,cause:"value must be an object that contains an array of elements"})};let s=t.elements,i=[];for(let t=0;t<s.length;t++){let n=s[t],a=(0,_.ZZ)({value:n,schema:e});if(t!==s.length-1||r){if(!a.success)return a;i.push(a.value)}}let l=null!=(o=null==n?void 0:n.length)?o:0,u="";return a&&(u+="["),l>0&&(u+=","),u+=i.slice(l).map(e=>JSON.stringify(e)).join(","),r&&(u+="]"),{success:!0,value:{partial:i,textDelta:u}}},validateFinalResult(t){if(!(0,eh.k9)(t)||!(0,eh.cj)(t.elements))return{success:!1,error:new eh.iM({value:t,cause:"value must be an object that contains an array of elements"})};let n=t.elements;for(let t of n){let n=(0,_.ZZ)({value:t,schema:e});if(!n.success)return n}return{success:!0,value:n}},createElementStream(e){let t=0;return tD(e.pipeThrough(new TransformStream({transform(e,n){switch(e.type){case"object":{let a=e.object;for(;t<a.length;t++)n.enqueue(a[t]);break}case"text-delta":case"finish":case"error":break;default:throw Error(`Unsupported chunk type: ${e}`)}}})))}}},tF=e=>({type:"enum",jsonSchema:{$schema:"http://json-schema.org/draft-07/schema#",type:"object",properties:{result:{type:"string",enum:e}},required:["result"],additionalProperties:!1},validateFinalResult(t){if(!(0,eh.k9)(t)||"string"!=typeof t.result)return{success:!1,error:new eh.iM({value:t,cause:'value must be an object that contains a string in the "result" property.'})};let n=t.result;return e.includes(n)?{success:!0,value:n}:{success:!1,error:new eh.iM({value:t,cause:"value must be a string in the enum"})}},validatePartialResult(){throw new eh.b8({functionality:"partial results in enum mode"})},createElementStream(){throw new eh.b8({functionality:"element streams in enum mode"})}});function tZ(e){return JSON.stringify(e.map(e=>({...e,content:"string"==typeof e.content?e.content:e.content.map(tJ)})))}function tJ(e){return"image"===e.type?{...e,image:e.image instanceof Uint8Array?tn(e.image):e.image}:e}var tL=(0,_.hK)({prefix:"aiobj",size:24});async function tY({model:e,enum:t,schema:n,schemaName:a,schemaDescription:r,mode:o,output:s="object",system:i,prompt:l,messages:u,maxRetries:p,abortSignal:c,headers:d,experimental_repairText:m,experimental_telemetry:g,experimental_providerMetadata:h,providerOptions:f=h,_internal:{generateId:y=tL,currentDate:v=()=>new Date}={},...b}){if("string"==typeof e||"v1"!==e.specificationVersion)throw new ez;!function({output:e,mode:t,schema:n,schemaName:a,schemaDescription:r,enumValues:o}){if(null!=e&&"object"!==e&&"array"!==e&&"enum"!==e&&"no-schema"!==e)throw new ej({parameter:"output",value:e,message:"Invalid output type."});if("no-schema"===e){if("auto"===t||"tool"===t)throw new ej({parameter:"mode",value:t,message:'Mode must be "json" for no-schema output.'});if(null!=n)throw new ej({parameter:"schema",value:n,message:"Schema is not supported for no-schema output."});if(null!=r)throw new ej({parameter:"schemaDescription",value:r,message:"Schema description is not supported for no-schema output."});if(null!=a)throw new ej({parameter:"schemaName",value:a,message:"Schema name is not supported for no-schema output."});if(null!=o)throw new ej({parameter:"enumValues",value:o,message:"Enum values are not supported for no-schema output."})}if("object"===e){if(null==n)throw new ej({parameter:"schema",value:n,message:"Schema is required for object output."});if(null!=o)throw new ej({parameter:"enumValues",value:o,message:"Enum values are not supported for object output."})}if("array"===e){if(null==n)throw new ej({parameter:"schema",value:n,message:"Element schema is required for array output."});if(null!=o)throw new ej({parameter:"enumValues",value:o,message:"Enum values are not supported for array output."})}if("enum"===e){if(null!=n)throw new ej({parameter:"schema",value:n,message:"Schema is not supported for enum output."});if(null!=r)throw new ej({parameter:"schemaDescription",value:r,message:"Schema description is not supported for enum output."});if(null!=a)throw new ej({parameter:"schemaName",value:a,message:"Schema name is not supported for enum output."});if(null==o)throw new ej({parameter:"enumValues",value:o,message:"Enum values are required for enum output."});for(let e of o)if("string"!=typeof e)throw new ej({parameter:"enumValues",value:e,message:"Enum values must be strings."})}}({output:s,mode:o,schema:n,schemaName:a,schemaDescription:r,enumValues:t});let{maxRetries:w,retry:x}=eO({maxRetries:p}),k=function({output:e,schema:t,enumValues:n}){switch(e){case"object":return t$(eg(t));case"array":return tU(eg(t));case"enum":return tF(n);case"no-schema":return tq;default:throw Error(`Unsupported output: ${e}`)}}({output:s,schema:n,enumValues:t});"no-schema"===k.type&&void 0===o&&(o="json");let z=eD({model:e,telemetry:g,headers:d,settings:{...b,maxRetries:w}}),T=eF(g);return eZ({name:"ai.generateObject",attributes:eL({telemetry:g,attributes:{...eP({operationId:"ai.generateObject",telemetry:g}),...z,"ai.prompt":{input:()=>JSON.stringify({system:i,prompt:l,messages:u})},"ai.schema":null!=k.jsonSchema?{input:()=>JSON.stringify(k.jsonSchema)}:void 0,"ai.schema.name":a,"ai.schema.description":r,"ai.settings.output":k.type,"ai.settings.mode":o}}),tracer:T,fn:async t=>{var n,s,p,h;let w,S,I,j,E,R,C,N,A,M;switch(("auto"===o||null==o)&&(o=e.defaultObjectGenerationMode),o){case"json":{let t=tN({prompt:{system:null==k.jsonSchema?tP({prompt:i}):e.supportsStructuredOutputs?i:tP({prompt:i,schema:k.jsonSchema}),prompt:l,messages:u},tools:void 0}),p=await tl({prompt:t,modelSupportsImageUrls:e.supportsImageUrls,modelSupportsUrl:null==(n=e.supportsUrl)?void 0:n.bind(e)}),m=await x(()=>eZ({name:"ai.generateObject.doGenerate",attributes:eL({telemetry:g,attributes:{...eP({operationId:"ai.generateObject.doGenerate",telemetry:g}),...z,"ai.prompt.format":{input:()=>t.type},"ai.prompt.messages":{input:()=>JSON.stringify(p)},"ai.settings.mode":o,"gen_ai.system":e.provider,"gen_ai.request.model":e.modelId,"gen_ai.request.frequency_penalty":b.frequencyPenalty,"gen_ai.request.max_tokens":b.maxTokens,"gen_ai.request.presence_penalty":b.presencePenalty,"gen_ai.request.temperature":b.temperature,"gen_ai.request.top_k":b.topK,"gen_ai.request.top_p":b.topP}}),tracer:T,fn:async n=>{var o,s,i,l,u,m;let h=await e.doGenerate({mode:{type:"object-json",schema:k.jsonSchema,name:a,description:r},...tp(b),inputFormat:t.type,prompt:p,providerMetadata:f,abortSignal:c,headers:d}),_={id:null!=(s=null==(o=h.response)?void 0:o.id)?s:y(),timestamp:null!=(l=null==(i=h.response)?void 0:i.timestamp)?l:v(),modelId:null!=(m=null==(u=h.response)?void 0:u.modelId)?m:e.modelId};if(void 0===h.text)throw new e1({message:"No object generated: the model did not return a response.",response:_,usage:tM(h.usage),finishReason:h.finishReason});return n.setAttributes(eL({telemetry:g,attributes:{"ai.response.finishReason":h.finishReason,"ai.response.object":{output:()=>h.text},"ai.response.id":_.id,"ai.response.model":_.modelId,"ai.response.timestamp":_.timestamp.toISOString(),"ai.response.providerMetadata":JSON.stringify(h.providerMetadata),"ai.usage.promptTokens":h.usage.promptTokens,"ai.usage.completionTokens":h.usage.completionTokens,"gen_ai.response.finish_reasons":[h.finishReason],"gen_ai.response.id":_.id,"gen_ai.response.model":_.modelId,"gen_ai.usage.prompt_tokens":h.usage.promptTokens,"gen_ai.usage.completion_tokens":h.usage.completionTokens}})),{...h,objectText:h.text,responseData:_}}}));w=m.objectText,S=m.finishReason,I=m.usage,j=m.warnings,E=m.rawResponse,N=m.logprobs,A=m.providerMetadata,C=null!=(s=m.request)?s:{},R=m.responseData;break}case"tool":{let t=tN({prompt:{system:i,prompt:l,messages:u},tools:void 0}),n=await tl({prompt:t,modelSupportsImageUrls:e.supportsImageUrls,modelSupportsUrl:null==(p=e.supportsUrl)?void 0:p.bind(e)}),s=t.type,m=await x(()=>eZ({name:"ai.generateObject.doGenerate",attributes:eL({telemetry:g,attributes:{...eP({operationId:"ai.generateObject.doGenerate",telemetry:g}),...z,"ai.prompt.format":{input:()=>s},"ai.prompt.messages":{input:()=>tZ(n)},"ai.settings.mode":o,"gen_ai.system":e.provider,"gen_ai.request.model":e.modelId,"gen_ai.request.frequency_penalty":b.frequencyPenalty,"gen_ai.request.max_tokens":b.maxTokens,"gen_ai.request.presence_penalty":b.presencePenalty,"gen_ai.request.temperature":b.temperature,"gen_ai.request.top_k":b.topK,"gen_ai.request.top_p":b.topP}}),tracer:T,fn:async t=>{var o,i,l,u,p,m,h,_;let w=await e.doGenerate({mode:{type:"object-tool",tool:{type:"function",name:null!=a?a:"json",description:null!=r?r:"Respond with a JSON object.",parameters:k.jsonSchema}},...tp(b),inputFormat:s,prompt:n,providerMetadata:f,abortSignal:c,headers:d}),x=null==(i=null==(o=w.toolCalls)?void 0:o[0])?void 0:i.args,z={id:null!=(u=null==(l=w.response)?void 0:l.id)?u:y(),timestamp:null!=(m=null==(p=w.response)?void 0:p.timestamp)?m:v(),modelId:null!=(_=null==(h=w.response)?void 0:h.modelId)?_:e.modelId};if(void 0===x)throw new e1({message:"No object generated: the tool was not called.",response:z,usage:tM(w.usage),finishReason:w.finishReason});return t.setAttributes(eL({telemetry:g,attributes:{"ai.response.finishReason":w.finishReason,"ai.response.object":{output:()=>x},"ai.response.id":z.id,"ai.response.model":z.modelId,"ai.response.timestamp":z.timestamp.toISOString(),"ai.response.providerMetadata":JSON.stringify(w.providerMetadata),"ai.usage.promptTokens":w.usage.promptTokens,"ai.usage.completionTokens":w.usage.completionTokens,"gen_ai.response.finish_reasons":[w.finishReason],"gen_ai.response.id":z.id,"gen_ai.response.model":z.modelId,"gen_ai.usage.input_tokens":w.usage.promptTokens,"gen_ai.usage.output_tokens":w.usage.completionTokens}})),{...w,objectText:x,responseData:z}}}));w=m.objectText,S=m.finishReason,I=m.usage,j=m.warnings,E=m.rawResponse,N=m.logprobs,A=m.providerMetadata,C=null!=(h=m.request)?h:{},R=m.responseData;break}case void 0:throw Error("Model does not have a default object generation mode.");default:{let e=o;throw Error(`Unsupported mode: ${e}`)}}function O(e){let t=(0,_.N8)({text:e});if(!t.success)throw new e1({message:"No object generated: could not parse the response.",cause:t.error,text:e,response:R,usage:tM(I),finishReason:S});let n=k.validateFinalResult(t.value,{text:e,response:R,usage:tM(I)});if(!n.success)throw new e1({message:"No object generated: response did not match schema.",cause:n.error,text:e,response:R,usage:tM(I),finishReason:S});return n.value}try{M=O(w)}catch(e){if(null!=m&&e1.isInstance(e)&&(eh.u6.isInstance(e.cause)||eh.iM.isInstance(e.cause))){let t=await m({text:w,error:e.cause});if(null===t)throw e;M=O(t)}else throw e}return t.setAttributes(eL({telemetry:g,attributes:{"ai.response.finishReason":S,"ai.response.object":{output:()=>JSON.stringify(M)},"ai.usage.promptTokens":I.promptTokens,"ai.usage.completionTokens":I.completionTokens}})),new tB({object:M,finishReason:S,usage:tM(I),warnings:j,request:C,response:{...R,headers:null==E?void 0:E.headers,body:null==E?void 0:E.body},logprobs:N,providerMetadata:A})}})}var tB=class{constructor(e){this.object=e.object,this.finishReason=e.finishReason,this.usage=e.usage,this.warnings=e.warnings,this.providerMetadata=e.providerMetadata,this.experimental_providerMetadata=e.providerMetadata,this.response=e.response,this.request=e.request,this.logprobs=e.logprobs}toJsonResponse(e){var t;return new Response(JSON.stringify(this.object),{status:null!=(t=null==e?void 0:e.status)?t:200,headers:e_(null==e?void 0:e.headers,{contentType:"application/json; charset=utf-8"})})}},tV=class{constructor(){this.status={type:"pending"},this._resolve=void 0,this._reject=void 0}get value(){return this.promise||(this.promise=new Promise((e,t)=>{"resolved"===this.status.type?e(this.status.value):"rejected"===this.status.type&&t(this.status.error),this._resolve=e,this._reject=t})),this.promise}resolve(e){var t;this.status={type:"resolved",value:e},this.promise&&(null==(t=this._resolve)||t.call(this,e))}reject(e){var t;this.status={type:"rejected",error:e},this.promise&&(null==(t=this._reject)||t.call(this,e))}};function tG(){let e,t;return{promise:new Promise((n,a)=>{e=n,t=a}),resolve:e,reject:t}}function tK(){let e=[],t=null,n=!1,a=tG(),r=async()=>{if(n&&0===e.length){null==t||t.close();return}if(0===e.length)return a=tG(),await a.promise,r();try{let{value:a,done:o}=await e[0].read();o?(e.shift(),e.length>0?await r():n&&(null==t||t.close())):null==t||t.enqueue(a)}catch(a){null==t||t.error(a),e.shift(),n&&0===e.length&&(null==t||t.close())}};return{stream:new ReadableStream({start(e){t=e},pull:r,async cancel(){for(let t of e)await t.cancel();e=[],n=!0}}),addStream:t=>{if(n)throw Error("Cannot add inner stream: outer stream is closed");e.push(t.getReader()),a.resolve()},close:()=>{n=!0,a.resolve(),0===e.length&&(null==t||t.close())},terminate:()=>{n=!0,a.resolve(),e.forEach(e=>e.cancel()),e=[],null==t||t.close()}}}function tW(){var e,t;return null!=(t=null==(e=null==globalThis?void 0:globalThis.performance)?void 0:e.now())?t:Date.now()}(0,_.hK)({prefix:"aiobj",size:24});var tH="AI_NoOutputSpecifiedError",tX=`vercel.ai.error.${tH}`,tQ=Symbol.for(tX),t0=class extends eh.bD{constructor({message:e="No output specified."}={}){super({name:tH,message:e}),this[d]=!0}static isInstance(e){return eh.bD.hasMarker(e,tX)}};d=tQ;var t1="AI_ToolExecutionError",t4=`vercel.ai.error.${t1}`,t2=Symbol.for(t4),t9=class extends eh.bD{constructor({toolArgs:e,toolName:t,toolCallId:n,cause:a,message:r=`Error executing tool ${t}: ${(0,eh.u1)(a)}`}){super({name:t1,message:r,cause:a}),this[m]=!0,this.toolArgs=e,this.toolName=t,this.toolCallId=n}static isInstance(e){return eh.bD.hasMarker(e,t4)}};function t6({tools:e,toolChoice:t,activeTools:n}){return null!=e&&Object.keys(e).length>0?{tools:(null!=n?Object.entries(e).filter(([e])=>n.includes(e)):Object.entries(e)).map(([e,t])=>{let n=t.type;switch(n){case void 0:case"function":return{type:"function",name:e,description:t.description,parameters:eg(t.parameters).jsonSchema};case"provider-defined":return{type:"provider-defined",name:e,id:t.id,args:t.args};default:throw Error(`Unsupported tool type: ${n}`)}}),toolChoice:null==t?{type:"auto"}:"string"==typeof t?{type:t}:{type:"tool",toolName:t.toolName}}:{tools:void 0,toolChoice:void 0}}m=t2;var t8=/^([\s\S]*?)(\s+)(\S*)$/;function t3(e){let t=e.match(t8);return t?{prefix:t[1],whitespace:t[2],suffix:t[3]}:void 0}var t7="AI_InvalidToolArgumentsError",t5=`vercel.ai.error.${t7}`,ne=Symbol.for(t5),nt=class extends eh.bD{constructor({toolArgs:e,toolName:t,cause:n,message:a=`Invalid arguments for tool ${t}: ${(0,eh.u1)(n)}`}){super({name:t7,message:a,cause:n}),this[g]=!0,this.toolArgs=e,this.toolName=t}static isInstance(e){return eh.bD.hasMarker(e,t5)}};g=ne;var nn="AI_NoSuchToolError",na=`vercel.ai.error.${nn}`,nr=Symbol.for(na),no=class extends eh.bD{constructor({toolName:e,availableTools:t,message:n=`Model tried to call unavailable tool '${e}'. ${void 0===t?"No tools are available.":`Available tools: ${t.join(", ")}.`}`}){super({name:nn,message:n}),this[h]=!0,this.toolName=e,this.availableTools=t}static isInstance(e){return eh.bD.hasMarker(e,na)}};h=nr;var ns="AI_ToolCallRepairError",ni=`vercel.ai.error.${ns}`,nl=Symbol.for(ni),nu=class extends eh.bD{constructor({cause:e,originalError:t,message:n=`Error repairing tool call: ${(0,eh.u1)(e)}`}){super({name:ns,message:n,cause:e}),this[f]=!0,this.originalError=t}static isInstance(e){return eh.bD.hasMarker(e,ni)}};async function np({toolCall:e,tools:t,repairToolCall:n,system:a,messages:r}){if(null==t)throw new no({toolName:e.toolName});try{return await nc({toolCall:e,tools:t})}catch(s){if(null==n||!(no.isInstance(s)||nt.isInstance(s)))throw s;let o=null;try{o=await n({toolCall:e,tools:t,parameterSchema:({toolName:e})=>eg(t[e].parameters).jsonSchema,system:a,messages:r,error:s})}catch(e){throw new nu({cause:e,originalError:s})}if(null==o)throw s;return await nc({toolCall:o,tools:t})}}async function nc({toolCall:e,tools:t}){let n=e.toolName,a=t[n];if(null==a)throw new no({toolName:e.toolName,availableTools:Object.keys(t)});let r=eg(a.parameters),o=""===e.args.trim()?(0,_.ZZ)({value:{},schema:r}):(0,_.N8)({text:e.args,schema:r});if(!1===o.success)throw new nt({toolName:n,toolArgs:e.args,cause:o.error});return{type:"tool-call",toolCallId:e.toolCallId,toolName:n,args:o.value}}function nd(e){let t=e.filter(e=>"text"===e.type).map(e=>e.text).join("");return t.length>0?t:void 0}function nm({text:e="",files:t,reasoning:n,tools:a,toolCalls:r,toolResults:o,messageId:s,generateMessageId:i}){let l=[],u=[];return n.length>0&&u.push(...n.map(e=>"text"===e.type?{...e,type:"reasoning"}:{...e,type:"redacted-reasoning"})),t.length>0&&u.push(...t.map(e=>({type:"file",data:e.base64,mimeType:e.mimeType}))),e.length>0&&u.push({type:"text",text:e}),r.length>0&&u.push(...r),u.length>0&&l.push({role:"assistant",content:u,id:s}),o.length>0&&l.push({role:"tool",id:i(),content:o.map(e=>{let t=a[e.toolName];return(null==t?void 0:t.experimental_toToolResultContent)!=null?{type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,result:t.experimental_toToolResultContent(e.result),experimental_content:t.experimental_toToolResultContent(e.result)}:{type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,result:e.result}})}),l}f=nl;var ng=(0,_.hK)({prefix:"aitxt",size:24}),nh=(0,_.hK)({prefix:"msg",size:24});async function nf({model:e,tools:t,toolChoice:n,system:a,prompt:r,messages:o,maxRetries:s,abortSignal:i,headers:l,maxSteps:u=1,experimental_generateMessageId:p=nh,experimental_output:c,experimental_continueSteps:d=!1,experimental_telemetry:m,experimental_providerMetadata:g,providerOptions:h=g,experimental_activeTools:f,experimental_prepareStep:y,experimental_repairToolCall:v,_internal:{generateId:b=ng,currentDate:_=()=>new Date}={},onStepFinish:w,...x}){var k;if("string"==typeof e||"v1"!==e.specificationVersion)throw new ez;if(u<1)throw new ej({parameter:"maxSteps",value:u,message:"maxSteps must be at least 1"});let{maxRetries:z,retry:T}=eO({maxRetries:s}),S=eD({model:e,telemetry:m,headers:l,settings:{...x,maxRetries:z}}),I=tN({prompt:{system:null!=(k=null==c?void 0:c.injectIntoSystemPrompt({system:a,model:e}))?k:a,prompt:r,messages:o},tools:t}),j=eF(m);return eZ({name:"ai.generateText",attributes:eL({telemetry:m,attributes:{...eP({operationId:"ai.generateText",telemetry:m}),...S,"ai.model.provider":e.provider,"ai.model.id":e.modelId,"ai.prompt":{input:()=>JSON.stringify({system:a,prompt:r,messages:o})},"ai.settings.maxSteps":u}}),tracer:j,fn:async r=>{var o,s,g,k,z,E,R,C,N,A,M,O,P,D;let q,$=tp(x),U=[],F=[],Z=[],J=0,L=[],Y="",B=[],V=[],G={completionTokens:0,promptTokens:0,totalTokens:0},K="initial";do{let r=0===J?I.type:"messages",O=[...I.messages,...L],P=await (null==y?void 0:y({model:e,steps:V,maxSteps:u,stepNumber:J})),D=null!=(o=null==P?void 0:P.toolChoice)?o:n,W=null!=(s=null==P?void 0:P.experimental_activeTools)?s:f,H=null!=(g=null==P?void 0:P.model)?g:e,X=await tl({prompt:{type:r,system:I.system,messages:O},modelSupportsImageUrls:H.supportsImageUrls,modelSupportsUrl:null==(k=H.supportsUrl)?void 0:k.bind(H)}),Q={type:"regular",...t6({tools:t,toolChoice:D,activeTools:W})};q=await T(()=>eZ({name:"ai.generateText.doGenerate",attributes:eL({telemetry:m,attributes:{...eP({operationId:"ai.generateText.doGenerate",telemetry:m}),...S,"ai.model.provider":H.provider,"ai.model.id":H.modelId,"ai.prompt.format":{input:()=>r},"ai.prompt.messages":{input:()=>tZ(X)},"ai.prompt.tools":{input:()=>{var e;return null==(e=Q.tools)?void 0:e.map(e=>JSON.stringify(e))}},"ai.prompt.toolChoice":{input:()=>null!=Q.toolChoice?JSON.stringify(Q.toolChoice):void 0},"gen_ai.system":H.provider,"gen_ai.request.model":H.modelId,"gen_ai.request.frequency_penalty":x.frequencyPenalty,"gen_ai.request.max_tokens":x.maxTokens,"gen_ai.request.presence_penalty":x.presencePenalty,"gen_ai.request.stop_sequences":x.stopSequences,"gen_ai.request.temperature":x.temperature,"gen_ai.request.top_k":x.topK,"gen_ai.request.top_p":x.topP}}),tracer:j,fn:async t=>{var n,a,o,s,u,p;let d=await H.doGenerate({mode:Q,...$,inputFormat:r,responseFormat:null==c?void 0:c.responseFormat({model:e}),prompt:X,providerMetadata:h,abortSignal:i,headers:l}),g={id:null!=(a=null==(n=d.response)?void 0:n.id)?a:b(),timestamp:null!=(s=null==(o=d.response)?void 0:o.timestamp)?s:_(),modelId:null!=(p=null==(u=d.response)?void 0:u.modelId)?p:H.modelId};return t.setAttributes(eL({telemetry:m,attributes:{"ai.response.finishReason":d.finishReason,"ai.response.text":{output:()=>d.text},"ai.response.toolCalls":{output:()=>JSON.stringify(d.toolCalls)},"ai.response.id":g.id,"ai.response.model":g.modelId,"ai.response.timestamp":g.timestamp.toISOString(),"ai.response.providerMetadata":JSON.stringify(d.providerMetadata),"ai.usage.promptTokens":d.usage.promptTokens,"ai.usage.completionTokens":d.usage.completionTokens,"gen_ai.response.finish_reasons":[d.finishReason],"gen_ai.response.id":g.id,"gen_ai.response.model":g.modelId,"gen_ai.usage.input_tokens":d.usage.promptTokens,"gen_ai.usage.output_tokens":d.usage.completionTokens}})),{...d,response:g}}})),U=await Promise.all((null!=(z=q.toolCalls)?z:[]).map(e=>np({toolCall:e,tools:t,repairToolCall:v,system:a,messages:O}))),F=null==t?[]:await ny({toolCalls:U,tools:t,tracer:j,telemetry:m,messages:O,abortSignal:i});let ee=tM(q.usage);G=tO(G,ee);let et="done";++J<u&&(d&&"length"===q.finishReason&&0===U.length?et="continue":U.length>0&&F.length===U.length&&(et="tool-result"));let en=null!=(E=q.text)?E:"",ea="continue"===K&&Y.trimEnd()!==Y?en.trimStart():en,er="continue"===et?function(e){let t=t3(e);return t?t.prefix+t.whitespace:e}(ea):ea;if(Y="continue"===et||"continue"===K?Y+er:er,Z=nb(q.reasoning),B.push(...null!=(R=q.sources)?R:[]),"continue"===K){let e=L[L.length-1];"string"==typeof e.content?e.content+=er:e.content.push({text:er,type:"text"})}else L.push(...nm({text:Y,files:n_(q.files),reasoning:nb(q.reasoning),tools:null!=t?t:{},toolCalls:U,toolResults:F,messageId:p(),generateMessageId:p}));let eo={stepType:K,text:er,reasoning:nd(Z),reasoningDetails:Z,files:n_(q.files),sources:null!=(C=q.sources)?C:[],toolCalls:U,toolResults:F,finishReason:q.finishReason,usage:ee,warnings:q.warnings,logprobs:q.logprobs,request:null!=(N=q.request)?N:{},response:{...q.response,headers:null==(A=q.rawResponse)?void 0:A.headers,body:null==(M=q.rawResponse)?void 0:M.body,messages:structuredClone(L)},providerMetadata:q.providerMetadata,experimental_providerMetadata:q.providerMetadata,isContinued:"continue"===et};V.push(eo),await (null==w?void 0:w(eo)),K=et}while("done"!==K);return r.setAttributes(eL({telemetry:m,attributes:{"ai.response.finishReason":q.finishReason,"ai.response.text":{output:()=>q.text},"ai.response.toolCalls":{output:()=>JSON.stringify(q.toolCalls)},"ai.usage.promptTokens":q.usage.promptTokens,"ai.usage.completionTokens":q.usage.completionTokens,"ai.response.providerMetadata":JSON.stringify(q.providerMetadata)}})),new nv({text:Y,files:n_(q.files),reasoning:nd(Z),reasoningDetails:Z,sources:B,outputResolver:()=>{if(null==c)throw new t0;return c.parseOutput({text:Y},{response:q.response,usage:G,finishReason:q.finishReason})},toolCalls:U,toolResults:F,finishReason:q.finishReason,usage:G,warnings:q.warnings,request:null!=(O=q.request)?O:{},response:{...q.response,headers:null==(P=q.rawResponse)?void 0:P.headers,body:null==(D=q.rawResponse)?void 0:D.body,messages:L},logprobs:q.logprobs,steps:V,providerMetadata:q.providerMetadata})}})}async function ny({toolCalls:e,tools:t,tracer:n,telemetry:a,messages:r,abortSignal:o}){return(await Promise.all(e.map(async({toolCallId:e,toolName:s,args:i})=>{let l=t[s];if((null==l?void 0:l.execute)==null)return;let u=await eZ({name:"ai.toolCall",attributes:eL({telemetry:a,attributes:{...eP({operationId:"ai.toolCall",telemetry:a}),"ai.toolCall.name":s,"ai.toolCall.id":e,"ai.toolCall.args":{output:()=>JSON.stringify(i)}}}),tracer:n,fn:async t=>{try{let n=await l.execute(i,{toolCallId:e,messages:r,abortSignal:o});try{t.setAttributes(eL({telemetry:a,attributes:{"ai.toolCall.result":{output:()=>JSON.stringify(n)}}}))}catch(e){}return n}catch(n){throw eJ(t,n),new t9({toolCallId:e,toolName:s,toolArgs:i,cause:n})}}});return{type:"tool-result",toolCallId:e,toolName:s,args:i,result:u}}))).filter(e=>null!=e)}var nv=class{constructor(e){this.text=e.text,this.files=e.files,this.reasoning=e.reasoning,this.reasoningDetails=e.reasoningDetails,this.toolCalls=e.toolCalls,this.toolResults=e.toolResults,this.finishReason=e.finishReason,this.usage=e.usage,this.warnings=e.warnings,this.request=e.request,this.response=e.response,this.steps=e.steps,this.experimental_providerMetadata=e.providerMetadata,this.providerMetadata=e.providerMetadata,this.logprobs=e.logprobs,this.outputResolver=e.outputResolver,this.sources=e.sources}get experimental_output(){return this.outputResolver()}};function nb(e){return null==e?[]:"string"==typeof e?[{type:"text",text:e}]:e}function n_(e){var t;return null!=(t=null==e?void 0:e.map(e=>new eG(e)))?t:[]}var nw={};eb(nw,{object:()=>nR,text:()=>nE});var nx="AI_InvalidStreamPartError",nk=`vercel.ai.error.${nx}`,nz=Symbol.for(nk),nT=class extends eh.bD{constructor({chunk:e,message:t}){super({name:nx,message:t}),this[y]=!0,this.chunk=e}static isInstance(e){return eh.bD.hasMarker(e,nk)}};y=nz;var nS="vercel.ai.error.AI_MCPClientError",nI=Symbol.for(nS),nj=class extends eh.bD{constructor({name:e="MCPClientError",message:t,cause:n}){super({name:e,message:t,cause:n}),this[v]=!0}static isInstance(e){return eh.bD.hasMarker(e,nS)}};v=nI;var nE=()=>({type:"text",responseFormat:()=>({type:"text"}),injectIntoSystemPrompt:({system:e})=>e,parsePartial:({text:e})=>({partial:e}),parseOutput:({text:e})=>e}),nR=({schema:e})=>{let t=eg(e);return{type:"object",responseFormat:({model:e})=>({type:"json",schema:e.supportsStructuredOutputs?t.jsonSchema:void 0}),injectIntoSystemPrompt:({system:e,model:n})=>n.supportsStructuredOutputs?e:tP({prompt:e,schema:t.jsonSchema}),parsePartial({text:e}){let t=eo(e);switch(t.state){case"failed-parse":case"undefined-input":return;case"repaired-parse":case"successful-parse":return{partial:t.value};default:{let e=t.state;throw Error(`Unsupported parse state: ${e}`)}}},parseOutput({text:e},n){let a=(0,_.N8)({text:e});if(!a.success)throw new e1({message:"No object generated: could not parse the response.",cause:a.error,text:e,response:n.response,usage:n.usage,finishReason:n.finishReason});let r=(0,_.ZZ)({value:a.value,schema:t});if(!r.success)throw new e1({message:"No object generated: response did not match schema.",cause:r.error,text:e,response:n.response,usage:n.usage,finishReason:n.finishReason});return r.value}}},nC={word:/\S+\s+/m,line:/\n+/m};function nN({delayInMs:e=10,chunking:t="word",_internal:{delay:n=_.cb}={}}={}){let a;if("function"==typeof t)a=e=>{let n=t(e);if(null==n)return null;if(!n.length)throw Error("Chunking function must return a non-empty string.");if(!e.startsWith(n))throw Error(`Chunking function must return a match that is a prefix of the buffer. Received: "${n}" expected to start with "${e}"`);return n};else{let e="string"==typeof t?nC[t]:t;if(null==e)throw new eh.Di({argument:"chunking",message:`Chunking must be "word" or "line" or a RegExp. Received: ${t}`});a=t=>{let n=e.exec(t);return n?t.slice(0,n.index)+(null==n?void 0:n[0]):null}}return()=>{let t="";return new TransformStream({async transform(r,o){let s;if("text-delta"!==r.type){t.length>0&&(o.enqueue({type:"text-delta",textDelta:t}),t=""),o.enqueue(r);return}for(t+=r.textDelta;null!=(s=a(t));)o.enqueue({type:"text-delta",textDelta:s}),t=t.slice(s.length),await n(e)}})}}function nA(e){return void 0===e?[]:Array.isArray(e)?e:[e]}async function nM({stream:e,onError:t}){let n=e.getReader();try{for(;;){let{done:e}=await n.read();if(e)break}}catch(e){null==t||t(e)}finally{n.releaseLock()}}function nO(e,t){let n,a,r=e.getReader(),o=t.getReader(),s=!1,i=!1;async function l(e){try{null==n&&(n=r.read());let t=await n;n=void 0,t.done?e.close():e.enqueue(t.value)}catch(t){e.error(t)}}async function u(e){try{null==a&&(a=o.read());let t=await a;a=void 0,t.done?e.close():e.enqueue(t.value)}catch(t){e.error(t)}}return new ReadableStream({async pull(e){try{if(s)return void await u(e);if(i)return void await l(e);null==n&&(n=r.read()),null==a&&(a=o.read());let{result:t,reader:p}=await Promise.race([n.then(e=>({result:e,reader:r})),a.then(e=>({result:e,reader:o}))]);t.done||e.enqueue(t.value),p===r?(n=void 0,t.done&&(await u(e),s=!0)):(a=void 0,t.done&&(i=!0,await l(e)))}catch(t){e.error(t)}},cancel(){r.cancel(),o.cancel()}})}var nP=(0,_.hK)({prefix:"aitxt",size:24}),nD=(0,_.hK)({prefix:"msg",size:24});function nq({model:e,tools:t,toolChoice:n,system:a,prompt:r,messages:o,maxRetries:s,abortSignal:i,headers:l,maxSteps:u=1,experimental_generateMessageId:p=nD,experimental_output:c,experimental_continueSteps:d=!1,experimental_telemetry:m,experimental_providerMetadata:g,providerOptions:h=g,experimental_toolCallStreaming:f=!1,toolCallStreaming:y=f,experimental_activeTools:v,experimental_repairToolCall:b,experimental_transform:_,onChunk:w,onError:x,onFinish:k,onStepFinish:z,_internal:{now:T=tW,generateId:S=nP,currentDate:I=()=>new Date}={},...j}){if("string"==typeof e||"v1"!==e.specificationVersion)throw new ez;return new n$({model:e,telemetry:m,headers:l,settings:j,maxRetries:s,abortSignal:i,system:a,prompt:r,messages:o,tools:t,toolChoice:n,toolCallStreaming:y,transforms:nA(_),activeTools:v,repairToolCall:b,maxSteps:u,output:c,continueSteps:d,providerOptions:h,onChunk:w,onError:x,onFinish:k,onStepFinish:z,now:T,currentDate:I,generateId:S,generateMessageId:p})}var n$=class{constructor({model:e,telemetry:t,headers:n,settings:a,maxRetries:r,abortSignal:o,system:s,prompt:i,messages:l,tools:u,toolChoice:p,toolCallStreaming:c,transforms:d,activeTools:m,repairToolCall:g,maxSteps:h,output:f,continueSteps:y,providerOptions:v,now:b,currentDate:w,generateId:x,generateMessageId:k,onChunk:z,onError:T,onFinish:S,onStepFinish:I}){var j;let E,R,C,N;if(this.warningsPromise=new tV,this.usagePromise=new tV,this.finishReasonPromise=new tV,this.providerMetadataPromise=new tV,this.textPromise=new tV,this.reasoningPromise=new tV,this.reasoningDetailsPromise=new tV,this.sourcesPromise=new tV,this.filesPromise=new tV,this.toolCallsPromise=new tV,this.toolResultsPromise=new tV,this.requestPromise=new tV,this.responsePromise=new tV,this.stepsPromise=new tV,h<1)throw new ej({parameter:"maxSteps",value:h,message:"maxSteps must be at least 1"});this.output=f;let A="",M="",O="",P=[],D=[],q=[],$=[],U={id:x(),timestamp:w(),modelId:e.modelId,messages:[]},F=[],Z=[],J="initial",L=[],Y=new TransformStream({async transform(e,t){t.enqueue(e);let{part:n}=e;if(("text-delta"===n.type||"reasoning"===n.type||"source"===n.type||"tool-call"===n.type||"tool-result"===n.type||"tool-call-streaming-start"===n.type||"tool-call-delta"===n.type)&&await (null==z?void 0:z({chunk:n})),"error"===n.type&&await (null==T?void 0:T({error:n.error})),"text-delta"===n.type&&(A+=n.textDelta,M+=n.textDelta,O+=n.textDelta),"reasoning"===n.type&&(null==R?(R={type:"text",text:n.textDelta},P.push(R)):R.text+=n.textDelta),"reasoning-signature"===n.type){if(null==R)throw new eh.bD({name:"InvalidStreamPart",message:"reasoning-signature without reasoning"});R.signature=n.signature,R=void 0}if("redacted-reasoning"===n.type&&P.push({type:"redacted",data:n.data}),"file"===n.type&&D.push(n),"source"===n.type&&($.push(n.source),q.push(n.source)),"tool-call"===n.type&&F.push(n),"tool-result"===n.type&&Z.push(n),"step-finish"===n.type){let e=nm({text:M,files:D,reasoning:P,tools:null!=u?u:{},toolCalls:F,toolResults:Z,messageId:n.messageId,generateMessageId:k}),t=L.length,a="done";t+1<h&&(y&&"length"===n.finishReason&&0===F.length?a="continue":F.length>0&&Z.length===F.length&&(a="tool-result"));let r={stepType:J,text:A,reasoning:nd(P),reasoningDetails:P,files:D,sources:q,toolCalls:F,toolResults:Z,finishReason:n.finishReason,usage:n.usage,warnings:n.warnings,logprobs:n.logprobs,request:n.request,response:{...n.response,messages:[...U.messages,...e]},providerMetadata:n.experimental_providerMetadata,experimental_providerMetadata:n.experimental_providerMetadata,isContinued:n.isContinued};await (null==I?void 0:I(r)),L.push(r),F=[],Z=[],A="",q=[],P=[],D=[],R=void 0,"done"!==a&&(J=a),"continue"!==a&&(U.messages.push(...e),M="")}"finish"===n.type&&(U.id=n.response.id,U.timestamp=n.response.timestamp,U.modelId=n.response.modelId,U.headers=n.response.headers,N=n.usage,C=n.finishReason)},async flush(e){var n;try{if(0===L.length)return;let e=L[L.length-1];Q.warningsPromise.resolve(e.warnings),Q.requestPromise.resolve(e.request),Q.responsePromise.resolve(e.response),Q.toolCallsPromise.resolve(e.toolCalls),Q.toolResultsPromise.resolve(e.toolResults),Q.providerMetadataPromise.resolve(e.experimental_providerMetadata),Q.reasoningPromise.resolve(e.reasoning),Q.reasoningDetailsPromise.resolve(e.reasoningDetails);let a=null!=C?C:"unknown",r=null!=N?N:{completionTokens:NaN,promptTokens:NaN,totalTokens:NaN};Q.finishReasonPromise.resolve(a),Q.usagePromise.resolve(r),Q.textPromise.resolve(O),Q.sourcesPromise.resolve($),Q.filesPromise.resolve(e.files),Q.stepsPromise.resolve(L),await (null==S?void 0:S({finishReason:a,logprobs:void 0,usage:r,text:O,reasoning:e.reasoning,reasoningDetails:e.reasoningDetails,files:e.files,sources:e.sources,toolCalls:e.toolCalls,toolResults:e.toolResults,request:null!=(n=e.request)?n:{},response:e.response,warnings:e.warnings,providerMetadata:e.providerMetadata,experimental_providerMetadata:e.experimental_providerMetadata,steps:L})),E.setAttributes(eL({telemetry:t,attributes:{"ai.response.finishReason":a,"ai.response.text":{output:()=>O},"ai.response.toolCalls":{output:()=>{var t;return(null==(t=e.toolCalls)?void 0:t.length)?JSON.stringify(e.toolCalls):void 0}},"ai.usage.promptTokens":r.promptTokens,"ai.usage.completionTokens":r.completionTokens,"ai.response.providerMetadata":JSON.stringify(e.providerMetadata)}}))}catch(t){e.error(t)}finally{E.end()}}}),B=tK();this.addStream=B.addStream,this.closeStream=B.close;let V=B.stream;for(let e of d)V=V.pipeThrough(e({tools:u,stopStream(){B.terminate()}}));this.baseStream=V.pipeThrough(function(e){if(!e)return new TransformStream({transform(e,t){t.enqueue({part:e,partialOutput:void 0})}});let t="",n="",a="";function r({controller:e,partialOutput:t}){e.enqueue({part:{type:"text-delta",textDelta:n},partialOutput:t}),n=""}return new TransformStream({transform(o,s){if("step-finish"===o.type&&r({controller:s}),"text-delta"!==o.type)return void s.enqueue({part:o,partialOutput:void 0});t+=o.textDelta,n+=o.textDelta;let i=e.parsePartial({text:t});if(null!=i){let e=JSON.stringify(i.partial);e!==a&&(r({controller:s,partialOutput:i.partial}),a=e)}},flush(e){n.length>0&&r({controller:e})}})}(f)).pipeThrough(Y);let{maxRetries:G,retry:K}=eO({maxRetries:r}),W=eF(t),H=eD({model:e,telemetry:t,headers:n,settings:{...a,maxRetries:G}}),X=tN({prompt:{system:null!=(j=null==f?void 0:f.injectIntoSystemPrompt({system:s,model:e}))?j:s,prompt:i,messages:l},tools:u}),Q=this;eZ({name:"ai.streamText",attributes:eL({telemetry:t,attributes:{...eP({operationId:"ai.streamText",telemetry:t}),...H,"ai.prompt":{input:()=>JSON.stringify({system:s,prompt:i,messages:l})},"ai.settings.maxSteps":h}}),tracer:W,endWhenDone:!1,fn:async r=>{async function i({currentStep:r,responseMessages:l,usage:d,stepType:z,previousStepText:T,hasLeadingWhitespace:S,messageId:I}){var j;let E,R,C,N=0===l.length?X.type:"messages",A=[...X.messages,...l],M=await tl({prompt:{type:N,system:X.system,messages:A},modelSupportsImageUrls:e.supportsImageUrls,modelSupportsUrl:null==(j=e.supportsUrl)?void 0:j.bind(e)}),O={type:"regular",...t6({tools:u,toolChoice:p,activeTools:m})},{result:{stream:P,warnings:D,rawResponse:q,request:$},doStreamSpan:U,startTimestampMs:F}=await K(()=>eZ({name:"ai.streamText.doStream",attributes:eL({telemetry:t,attributes:{...eP({operationId:"ai.streamText.doStream",telemetry:t}),...H,"ai.prompt.format":{input:()=>N},"ai.prompt.messages":{input:()=>tZ(M)},"ai.prompt.tools":{input:()=>{var e;return null==(e=O.tools)?void 0:e.map(e=>JSON.stringify(e))}},"ai.prompt.toolChoice":{input:()=>null!=O.toolChoice?JSON.stringify(O.toolChoice):void 0},"gen_ai.system":e.provider,"gen_ai.request.model":e.modelId,"gen_ai.request.frequency_penalty":a.frequencyPenalty,"gen_ai.request.max_tokens":a.maxTokens,"gen_ai.request.presence_penalty":a.presencePenalty,"gen_ai.request.stop_sequences":a.stopSequences,"gen_ai.request.temperature":a.temperature,"gen_ai.request.top_k":a.topK,"gen_ai.request.top_p":a.topP}}),tracer:W,endWhenDone:!1,fn:async t=>({startTimestampMs:b(),doStreamSpan:t,result:await e.doStream({mode:O,...tp(a),inputFormat:N,responseFormat:null==f?void 0:f.responseFormat({model:e}),prompt:M,providerMetadata:v,abortSignal:o,headers:n})})})),Z=function({tools:e,generatorStream:t,toolCallStreaming:n,tracer:a,telemetry:r,system:o,messages:s,abortSignal:i,repairToolCall:l}){let u,p=null,c=new ReadableStream({start(e){p=e}}),d={},m=new Set,g=!1;function h(){g&&0===m.size&&(null!=u&&p.enqueue(u),p.close())}let f=new TransformStream({async transform(t,c){let g=t.type;switch(g){case"text-delta":case"reasoning":case"reasoning-signature":case"redacted-reasoning":case"source":case"response-metadata":case"error":c.enqueue(t);break;case"file":c.enqueue(new eK({data:t.data,mimeType:t.mimeType}));break;case"tool-call-delta":n&&(d[t.toolCallId]||(c.enqueue({type:"tool-call-streaming-start",toolCallId:t.toolCallId,toolName:t.toolName}),d[t.toolCallId]=!0),c.enqueue({type:"tool-call-delta",toolCallId:t.toolCallId,toolName:t.toolName,argsTextDelta:t.argsTextDelta}));break;case"tool-call":try{let n=await np({toolCall:t,tools:e,repairToolCall:l,system:o,messages:s});c.enqueue(n);let u=e[n.toolName];if(null!=u.execute){let e=(0,_.$C)();m.add(e),eZ({name:"ai.toolCall",attributes:eL({telemetry:r,attributes:{...eP({operationId:"ai.toolCall",telemetry:r}),"ai.toolCall.name":n.toolName,"ai.toolCall.id":n.toolCallId,"ai.toolCall.args":{output:()=>JSON.stringify(n.args)}}}),tracer:a,fn:async t=>u.execute(n.args,{toolCallId:n.toolCallId,messages:s,abortSignal:i}).then(a=>{p.enqueue({...n,type:"tool-result",result:a}),m.delete(e),h();try{t.setAttributes(eL({telemetry:r,attributes:{"ai.toolCall.result":{output:()=>JSON.stringify(a)}}}))}catch(e){}},a=>{eJ(t,a),p.enqueue({type:"error",error:new t9({toolCallId:n.toolCallId,toolName:n.toolName,toolArgs:n.args,cause:a})}),m.delete(e),h()})})}}catch(e){p.enqueue({type:"error",error:e})}break;case"finish":u={type:"finish",finishReason:t.finishReason,logprobs:t.logprobs,usage:tM(t.usage),experimental_providerMetadata:t.providerMetadata};break;default:throw Error(`Unhandled chunk type: ${g}`)}},flush(){g=!0,h()}});return new ReadableStream({start:async e=>Promise.all([t.pipeThrough(f).pipeTo(new WritableStream({write(t){e.enqueue(t)},close(){}})),c.pipeTo(new WritableStream({write(t){e.enqueue(t)},close(){e.close()}}))])})}({tools:u,generatorStream:P,toolCallStreaming:c,tracer:W,telemetry:t,system:s,messages:A,repairToolCall:g,abortSignal:o}),J=null!=$?$:{},L=[],Y=[],B=[],V=[],G="unknown",ee={promptTokens:0,completionTokens:0,totalTokens:0},et=!0,en="",ea="continue"===z?T:"",er={id:x(),timestamp:w(),modelId:e.modelId},eo="",es=!1,ei=!0,el=!1;async function eu({controller:e,chunk:t}){e.enqueue(t),en+=t.textDelta,ea+=t.textDelta,es=!0,el=t.textDelta.trimEnd()!==t.textDelta}Q.addStream(Z.pipeThrough(new TransformStream({async transform(e,t){var n,a,r;if(et){let e=b()-F;et=!1,U.addEvent("ai.stream.firstChunk",{"ai.response.msToFirstChunk":e}),U.setAttributes({"ai.response.msToFirstChunk":e}),t.enqueue({type:"step-start",messageId:I,request:J,warnings:null!=D?D:[]})}if("text-delta"===e.type&&0===e.textDelta.length)return;let o=e.type;switch(o){case"text-delta":if(y){let n=ei&&S?e.textDelta.trimStart():e.textDelta;if(0===n.length)break;ei=!1;let a=t3(eo+=n);null!=a&&(eo=a.suffix,await eu({controller:t,chunk:{type:"text-delta",textDelta:a.prefix+a.whitespace}}))}else await eu({controller:t,chunk:e});break;case"reasoning":t.enqueue(e),null==C?(C={type:"text",text:e.textDelta},B.push(C)):C.text+=e.textDelta;break;case"reasoning-signature":if(t.enqueue(e),null==C)throw new nT({chunk:e,message:"reasoning-signature without reasoning"});C.signature=e.signature,C=void 0;break;case"redacted-reasoning":t.enqueue(e),B.push({type:"redacted",data:e.data});break;case"tool-call":t.enqueue(e),L.push(e);break;case"tool-result":t.enqueue(e),Y.push(e);break;case"response-metadata":er={id:null!=(n=e.id)?n:er.id,timestamp:null!=(a=e.timestamp)?a:er.timestamp,modelId:null!=(r=e.modelId)?r:er.modelId};break;case"finish":{ee=e.usage,G=e.finishReason,E=e.experimental_providerMetadata,R=e.logprobs;let t=b()-F;U.addEvent("ai.stream.finish"),U.setAttributes({"ai.response.msToFinish":t,"ai.response.avgCompletionTokensPerSecond":1e3*ee.completionTokens/t});break}case"file":V.push(e),t.enqueue(e);break;case"source":case"tool-call-streaming-start":case"tool-call-delta":t.enqueue(e);break;case"error":t.enqueue(e),G="error";break;default:throw Error(`Unknown chunk type: ${o}`)}},async flush(e){let n=L.length>0?JSON.stringify(L):void 0,a="done";r+1<h&&(y&&"length"===G&&0===L.length?a="continue":L.length>0&&Y.length===L.length&&(a="tool-result")),y&&eo.length>0&&("continue"!==a||"continue"===z&&!es)&&(await eu({controller:e,chunk:{type:"text-delta",textDelta:eo}}),eo="");try{U.setAttributes(eL({telemetry:t,attributes:{"ai.response.finishReason":G,"ai.response.text":{output:()=>en},"ai.response.toolCalls":{output:()=>n},"ai.response.id":er.id,"ai.response.model":er.modelId,"ai.response.timestamp":er.timestamp.toISOString(),"ai.response.providerMetadata":JSON.stringify(E),"ai.usage.promptTokens":ee.promptTokens,"ai.usage.completionTokens":ee.completionTokens,"gen_ai.response.finish_reasons":[G],"gen_ai.response.id":er.id,"gen_ai.response.model":er.modelId,"gen_ai.usage.input_tokens":ee.promptTokens,"gen_ai.usage.output_tokens":ee.completionTokens}}))}catch(e){}finally{U.end()}e.enqueue({type:"step-finish",finishReason:G,usage:ee,providerMetadata:E,experimental_providerMetadata:E,logprobs:R,request:J,response:{...er,headers:null==q?void 0:q.headers},warnings:D,isContinued:"continue"===a,messageId:I});let o=tO(d,ee);if("done"===a)e.enqueue({type:"finish",finishReason:G,usage:o,providerMetadata:E,experimental_providerMetadata:E,logprobs:R,response:{...er,headers:null==q?void 0:q.headers}}),Q.closeStream();else{if("continue"===z){let e=l[l.length-1];"string"==typeof e.content?e.content+=en:e.content.push({text:en,type:"text"})}else l.push(...nm({text:en,files:V,reasoning:B,tools:null!=u?u:{},toolCalls:L,toolResults:Y,messageId:I,generateMessageId:k}));await i({currentStep:r+1,responseMessages:l,usage:o,stepType:a,previousStepText:ea,hasLeadingWhitespace:el,messageId:"continue"===a?I:k()})}}})))}E=r,await i({currentStep:0,responseMessages:[],usage:{promptTokens:0,completionTokens:0,totalTokens:0},previousStepText:"",stepType:"initial",hasLeadingWhitespace:!1,messageId:k()})}}).catch(e=>{Q.addStream(new ReadableStream({start(t){t.enqueue({type:"error",error:e}),t.close()}})),Q.closeStream()})}get warnings(){return this.warningsPromise.value}get usage(){return this.usagePromise.value}get finishReason(){return this.finishReasonPromise.value}get experimental_providerMetadata(){return this.providerMetadataPromise.value}get providerMetadata(){return this.providerMetadataPromise.value}get text(){return this.textPromise.value}get reasoning(){return this.reasoningPromise.value}get reasoningDetails(){return this.reasoningDetailsPromise.value}get sources(){return this.sourcesPromise.value}get files(){return this.filesPromise.value}get toolCalls(){return this.toolCallsPromise.value}get toolResults(){return this.toolResultsPromise.value}get request(){return this.requestPromise.value}get response(){return this.responsePromise.value}get steps(){return this.stepsPromise.value}teeStream(){let[e,t]=this.baseStream.tee();return this.baseStream=t,e}get textStream(){return tD(this.teeStream().pipeThrough(new TransformStream({transform({part:e},t){"text-delta"===e.type&&t.enqueue(e.textDelta)}})))}get fullStream(){return tD(this.teeStream().pipeThrough(new TransformStream({transform({part:e},t){t.enqueue(e)}})))}async consumeStream(e){var t;try{await nM({stream:this.fullStream,onError:null==e?void 0:e.onError})}catch(n){null==(t=null==e?void 0:e.onError)||t.call(e,n)}}get experimental_partialOutputStream(){if(null==this.output)throw new t0;return tD(this.teeStream().pipeThrough(new TransformStream({transform({partialOutput:e},t){null!=e&&t.enqueue(e)}})))}toDataStreamInternal({getErrorMessage:e=()=>"An error occurred.",sendUsage:t=!0,sendReasoning:n=!1,sendSources:a=!1,experimental_sendFinish:r=!0}){return this.fullStream.pipeThrough(new TransformStream({transform:async(o,s)=>{let i=o.type;switch(i){case"text-delta":s.enqueue(ep("text",o.textDelta));break;case"reasoning":n&&s.enqueue(ep("reasoning",o.textDelta));break;case"redacted-reasoning":n&&s.enqueue(ep("redacted_reasoning",{data:o.data}));break;case"reasoning-signature":n&&s.enqueue(ep("reasoning_signature",{signature:o.signature}));break;case"file":s.enqueue(ep("file",{mimeType:o.mimeType,data:o.base64}));break;case"source":a&&s.enqueue(ep("source",o.source));break;case"tool-call-streaming-start":s.enqueue(ep("tool_call_streaming_start",{toolCallId:o.toolCallId,toolName:o.toolName}));break;case"tool-call-delta":s.enqueue(ep("tool_call_delta",{toolCallId:o.toolCallId,argsTextDelta:o.argsTextDelta}));break;case"tool-call":s.enqueue(ep("tool_call",{toolCallId:o.toolCallId,toolName:o.toolName,args:o.args}));break;case"tool-result":s.enqueue(ep("tool_result",{toolCallId:o.toolCallId,result:o.result}));break;case"error":s.enqueue(ep("error",e(o.error)));break;case"step-start":s.enqueue(ep("start_step",{messageId:o.messageId}));break;case"step-finish":s.enqueue(ep("finish_step",{finishReason:o.finishReason,usage:t?{promptTokens:o.usage.promptTokens,completionTokens:o.usage.completionTokens}:void 0,isContinued:o.isContinued}));break;case"finish":r&&s.enqueue(ep("finish_message",{finishReason:o.finishReason,usage:t?{promptTokens:o.usage.promptTokens,completionTokens:o.usage.completionTokens}:void 0}));break;default:throw Error(`Unknown chunk type: ${i}`)}}}))}pipeDataStreamToResponse(e,{status:t,statusText:n,headers:a,data:r,getErrorMessage:o,sendUsage:s,sendReasoning:i,sendSources:l,experimental_sendFinish:u}={}){ek({response:e,status:t,statusText:n,headers:ex(a,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"}),stream:this.toDataStream({data:r,getErrorMessage:o,sendUsage:s,sendReasoning:i,sendSources:l,experimental_sendFinish:u})})}pipeTextStreamToResponse(e,t){ek({response:e,status:null==t?void 0:t.status,statusText:null==t?void 0:t.statusText,headers:ex(null==t?void 0:t.headers,{contentType:"text/plain; charset=utf-8"}),stream:this.textStream.pipeThrough(new TextEncoderStream)})}toDataStream(e){let t=this.toDataStreamInternal({getErrorMessage:null==e?void 0:e.getErrorMessage,sendUsage:null==e?void 0:e.sendUsage,sendReasoning:null==e?void 0:e.sendReasoning,sendSources:null==e?void 0:e.sendSources,experimental_sendFinish:null==e?void 0:e.experimental_sendFinish}).pipeThrough(new TextEncoderStream);return(null==e?void 0:e.data)?nO(null==e?void 0:e.data.stream,t):t}mergeIntoDataStream(e,t){e.merge(this.toDataStreamInternal({getErrorMessage:e.onError,sendUsage:null==t?void 0:t.sendUsage,sendReasoning:null==t?void 0:t.sendReasoning,sendSources:null==t?void 0:t.sendSources,experimental_sendFinish:null==t?void 0:t.experimental_sendFinish}))}toDataStreamResponse({headers:e,status:t,statusText:n,data:a,getErrorMessage:r,sendUsage:o,sendReasoning:s,sendSources:i,experimental_sendFinish:l}={}){return new Response(this.toDataStream({data:a,getErrorMessage:r,sendUsage:o,sendReasoning:s,sendSources:i,experimental_sendFinish:l}),{status:t,statusText:n,headers:e_(e,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}toTextStreamResponse(e){var t;return new Response(this.textStream.pipeThrough(new TextEncoderStream),{status:null!=(t=null==e?void 0:e.status)?t:200,headers:e_(null==e?void 0:e.headers,{contentType:"text/plain; charset=utf-8"})})}},nU=(eh.bD,eh.bD,({model:e,middleware:t,modelId:n,providerId:a})=>nA(t).reverse().reduce((e,t)=>nF({model:e,middleware:t,modelId:n,providerId:a}),e)),nF=({model:e,middleware:{transformParams:t,wrapGenerate:n,wrapStream:a},modelId:r,providerId:o})=>{var s;async function i({params:e,type:n}){return t?await t({params:e,type:n}):e}return{specificationVersion:"v1",provider:null!=o?o:e.provider,modelId:null!=r?r:e.modelId,defaultObjectGenerationMode:e.defaultObjectGenerationMode,supportsImageUrls:e.supportsImageUrls,supportsUrl:null==(s=e.supportsUrl)?void 0:s.bind(e),supportsStructuredOutputs:e.supportsStructuredOutputs,async doGenerate(t){let a=await i({params:t,type:"generate"}),r=async()=>e.doGenerate(a),o=async()=>e.doStream(a);return n?n({doGenerate:r,doStream:o,params:a,model:e}):r()},async doStream(t){let n=await i({params:t,type:"stream"}),r=async()=>e.doGenerate(n),o=async()=>e.doStream(n);return a?a({doGenerate:r,doStream:o,params:n,model:e}):o()}}};function nZ({messages:e,responseMessages:t,_internal:{currentDate:n=()=>new Date}={}}){var a,r;let o=structuredClone(e);for(let e of t){let t=e.role,s=o[o.length-1],i="assistant"===s.role;switch(t){case"assistant":{let t,l=function(t){return("string"==typeof e.content?[]:e.content.filter(e=>"tool-call"===e.type)).map(e=>({state:"call",step:t,args:e.args,toolCallId:e.toolCallId,toolName:e.toolName}))},u=[{type:"step-start"}],p="";if("string"==typeof e.content)p=e.content,u.push({type:"text",text:e.content});else{let n;for(let a of e.content)switch(a.type){case"text":n=void 0,p+=a.text,u.push({type:"text",text:a.text});break;case"reasoning":null==n&&(n={type:"reasoning",reasoning:"",details:[]},u.push(n)),t=(null!=t?t:"")+a.text,n.reasoning+=a.text,n.details.push({type:"text",text:a.text,signature:a.signature});break;case"redacted-reasoning":null==n&&(n={type:"reasoning",reasoning:"",details:[]},u.push(n)),n.details.push({type:"redacted",data:a.data});break;case"tool-call":break;case"file":if(a.data instanceof URL)throw new eh.bD({name:"InvalidAssistantFileData",message:"File data cannot be a URL"});u.push({type:"file",mimeType:a.mimeType,data:tn(a.data)})}}if(i){let e=null==(r=s.toolInvocations)?void 0:r.reduce((e,t)=>{var n;return Math.max(e,null!=(n=t.step)?n:0)},0);null!=s.parts||(s.parts=[]),s.content=p,s.reasoning=t,s.parts.push(...u),s.toolInvocations=[...null!=(a=s.toolInvocations)?a:[],...l(void 0===e?0:e+1)],l(void 0===e?0:e+1).map(e=>({type:"tool-invocation",toolInvocation:e})).forEach(e=>{s.parts.push(e)})}else o.push({role:"assistant",id:e.id,createdAt:n(),content:p,reasoning:t,toolInvocations:l(0),parts:[...u,...l(0).map(e=>({type:"tool-invocation",toolInvocation:e}))]});break}case"tool":if(null!=s.toolInvocations||(s.toolInvocations=[]),"assistant"!==s.role)throw Error(`Tool result must follow an assistant message: ${s.role}`);for(let t of(null!=s.parts||(s.parts=[]),e.content)){let e=s.toolInvocations.find(e=>e.toolCallId===t.toolCallId),n=s.parts.find(e=>"tool-invocation"===e.type&&e.toolInvocation.toolCallId===t.toolCallId);if(!e)throw Error("Tool call not found in previous message");e.state="result",e.result=t.result,n?n.toolInvocation=e:s.parts.push({type:"tool-invocation",toolInvocation:e})}break;default:throw Error(`Unsupported message role: ${t}`)}}return o}var nJ="AI_NoSuchProviderError",nL=`vercel.ai.error.${nJ}`,nY=Symbol.for(nL),nB=class extends eh.eM{constructor({modelId:e,modelType:t,providerId:n,availableProviders:a,message:r=`No such provider: ${n} (available providers: ${a.join()})`}){super({errorName:nJ,modelId:e,modelType:t,message:r}),this[b]=!0,this.providerId=n,this.availableProviders=a}static isInstance(e){return eh.bD.hasMarker(e,nL)}};b=nY;function nV(e){return e}var nG="2024-11-05",nK=[nG,"2024-10-07"],nW=T.z.object({name:T.z.string(),version:T.z.string()}).passthrough(),nH=T.z.object({_meta:T.z.optional(T.z.object({}).passthrough())}).passthrough(),nX=T.z.object({method:T.z.string(),params:T.z.optional(nH)}),nQ=T.z.object({experimental:T.z.optional(T.z.object({}).passthrough()),logging:T.z.optional(T.z.object({}).passthrough()),prompts:T.z.optional(T.z.object({listChanged:T.z.optional(T.z.boolean())}).passthrough()),resources:T.z.optional(T.z.object({subscribe:T.z.optional(T.z.boolean()),listChanged:T.z.optional(T.z.boolean())}).passthrough()),tools:T.z.optional(T.z.object({listChanged:T.z.optional(T.z.boolean())}).passthrough())}).passthrough(),n0=nH.extend({protocolVersion:T.z.string(),capabilities:nQ,serverInfo:nW,instructions:T.z.optional(T.z.string())}),n1=nH.extend({nextCursor:T.z.optional(T.z.string())}),n4=T.z.object({name:T.z.string(),description:T.z.optional(T.z.string()),inputSchema:T.z.object({type:T.z.literal("object"),properties:T.z.optional(T.z.object({}).passthrough())}).passthrough()}).passthrough(),n2=n1.extend({tools:T.z.array(n4)}),n9=T.z.object({type:T.z.literal("text"),text:T.z.string()}).passthrough(),n6=T.z.object({type:T.z.literal("image"),data:T.z.string().base64(),mimeType:T.z.string()}).passthrough(),n8=T.z.object({uri:T.z.string(),mimeType:T.z.optional(T.z.string())}).passthrough(),n3=n8.extend({text:T.z.string()}),n7=n8.extend({blob:T.z.string().base64()}),n5=T.z.object({type:T.z.literal("resource"),resource:T.z.union([n3,n7])}).passthrough(),ae=nH.extend({content:T.z.array(T.z.union([n9,n6,n5])),isError:T.z.boolean().default(!1).optional()}).or(nH.extend({toolResult:T.z.unknown()})),at=T.z.object({jsonrpc:T.z.literal("2.0"),id:T.z.union([T.z.string(),T.z.number().int()])}).merge(nX).strict(),an=T.z.object({jsonrpc:T.z.literal("2.0"),id:T.z.union([T.z.string(),T.z.number().int()]),result:nH}).strict(),aa=T.z.object({jsonrpc:T.z.literal("2.0"),id:T.z.union([T.z.string(),T.z.number().int()]),error:T.z.object({code:T.z.number().int(),message:T.z.string(),data:T.z.optional(T.z.unknown())})}).strict(),ar=T.z.object({jsonrpc:T.z.literal("2.0")}).merge(T.z.object({method:T.z.string(),params:T.z.optional(nH)})).strict(),ao=T.z.union([at,ar,an,aa]),as=class{constructor({url:e,headers:t}){this.connected=!1,this.url=new URL(e),this.headers=t}async start(){return new Promise((e,t)=>{if(this.connected)return e();this.abortController=new AbortController,(async()=>{var n,a,r;try{let r=new Headers(this.headers);r.set("Accept","text/event-stream");let o=await fetch(this.url.href,{headers:r,signal:null==(n=this.abortController)?void 0:n.signal});if(!o.ok||!o.body){let e=new nj({message:`MCP SSE Transport Error: ${o.status} ${o.statusText}`});return null==(a=this.onerror)||a.call(this,e),t(e)}let s=o.body.pipeThrough(new TextDecoderStream).pipeThrough(createEventSourceParserStream()).getReader(),i=async()=>{var n,a,r;try{for(;;){let{done:t,value:r}=await s.read();if(t){if(this.connected)throw this.connected=!1,new nj({message:"MCP SSE Transport Error: Connection closed unexpectedly"});return}let{event:o,data:i}=r;if("endpoint"===o){if(this.endpoint=new URL(i,this.url),this.endpoint.origin!==this.url.origin)throw new nj({message:`MCP SSE Transport Error: Endpoint origin does not match connection origin: ${this.endpoint.origin}`});this.connected=!0,e()}else if("message"===o)try{let e=ao.parse(JSON.parse(i));null==(n=this.onmessage)||n.call(this,e)}catch(t){let e=new nj({message:"MCP SSE Transport Error: Failed to parse message",cause:t});null==(a=this.onerror)||a.call(this,e)}}}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;null==(r=this.onerror)||r.call(this,e),t(e)}};this.sseConnection={close:()=>s.cancel()},i()}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;null==(r=this.onerror)||r.call(this,e),t(e)}})()})}async close(){var e,t,n;this.connected=!1,null==(e=this.sseConnection)||e.close(),null==(t=this.abortController)||t.abort(),null==(n=this.onclose)||n.call(this)}async send(e){var t,n,a;if(!this.endpoint||!this.connected)throw new nj({message:"MCP SSE Transport Error: Not connected"});try{let a=new Headers(this.headers);a.set("Content-Type","application/json");let r={method:"POST",headers:a,body:JSON.stringify(e),signal:null==(t=this.abortController)?void 0:t.signal},o=await fetch(this.endpoint,r);if(!o.ok){let e=await o.text().catch(()=>null),t=new nj({message:`MCP SSE Transport Error: POSTing to endpoint (HTTP ${o.status}): ${e}`});null==(n=this.onerror)||n.call(this,t);return}}catch(e){null==(a=this.onerror)||a.call(this,e);return}}};function ai(e={}){let t=new TextEncoder,n="";return new TransformStream({async start(){e.onStart&&await e.onStart()},async transform(a,r){r.enqueue(t.encode(a)),n+=a,e.onToken&&await e.onToken(a),e.onText&&"string"==typeof a&&await e.onText(a)},async flush(){e.onCompletion&&await e.onCompletion(n),e.onFinal&&await e.onFinal(n)}})}function al(e,t){return e.pipeThrough(new TransformStream({transform:async(e,t)=>{var n;if("string"==typeof e)return void t.enqueue(e);if("event"in e){"on_chat_model_stream"===e.event&&ad(null==(n=e.data)?void 0:n.chunk,t);return}ad(e,t)}})).pipeThrough(ai(t)).pipeThrough(new TextDecoderStream).pipeThrough(new TransformStream({transform:async(e,t)=>{t.enqueue(ep("text",e))}}))}function au(e,t){return al(e,t).pipeThrough(new TextEncoderStream)}function ap(e,t){var n;let a=al(e,null==t?void 0:t.callbacks).pipeThrough(new TextEncoderStream),r=null==t?void 0:t.data,o=null==t?void 0:t.init;return new Response(r?nO(r.stream,a):a,{status:null!=(n=null==o?void 0:o.status)?n:200,statusText:null==o?void 0:o.statusText,headers:e_(null==o?void 0:o.headers,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}function ac(e,t){t.dataStream.merge(al(e,t.callbacks))}function ad(e,t){if("string"==typeof e.content)t.enqueue(e.content);else for(let n of e.content)"text"===n.type&&t.enqueue(n.text)}function am(e,t){let n,a=(n=!0,e=>(n&&(e=e.trimStart())&&(n=!1),e));return(0,_.NR)(e[Symbol.asyncIterator]()).pipeThrough(new TransformStream({async transform(e,t){t.enqueue(a(e.delta))}})).pipeThrough(ai(t)).pipeThrough(new TextDecoderStream).pipeThrough(new TransformStream({transform:async(e,t)=>{t.enqueue(ep("text",e))}}))}function ag(e,t){return am(e,t).pipeThrough(new TextEncoderStream)}function ah(e,t={}){var n;let{init:a,data:r,callbacks:o}=t,s=am(e,o).pipeThrough(new TextEncoderStream);return new Response(r?nO(r.stream,s):s,{status:null!=(n=null==a?void 0:a.status)?n:200,statusText:null==a?void 0:a.statusText,headers:e_(null==a?void 0:a.headers,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}function af(e,t){t.dataStream.merge(am(e,t.callbacks))}eb({},{mergeIntoDataStream:()=>ac,toDataStream:()=>au,toDataStreamResponse:()=>ap}),eb({},{mergeIntoDataStream:()=>af,toDataStream:()=>ag,toDataStreamResponse:()=>ah})}};