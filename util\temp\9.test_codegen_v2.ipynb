{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import json\n", "import requests\n", "import asyncio\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_openai import ChatOpenAI\n", "from langchain_ollama import ChatOllama\n", "import os\n", "import csv\n", "import pandas as pd\n", "from langchain_core.documents import Document\n", "import json\n", "# from langchain_openai import OpenAIEmbeddings\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "from langchain_community.vectorstores import FAISS\n", "from langchain_text_splitters import MarkdownHeaderTextSplitter\n", "\n", "# 문서 검색기 생성\n", "from langchain_community.retrievers import BM25Retriever\n", "\n", "from dotenv import load_dotenv\n", "load_dotenv('./dot.env')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# format&훈련데이터 load 및 가공"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["#-------------------------------------- vectorize\n", "file_path = './test_kjy_refined_v2.md'\n", "\n", "#-------------------------------------- chat, chatstream, chat~\n", "\n", "with open('./format.txt', 'r') as f:\n", "    format = f.read()\n", "    \n", "# 헤더 구조 정의\n", "headers_to_split_on = [\n", "    (\"#\", \"기능명\"),\n", "    (\"##\", \"설명\"),\n", "    (\"###\", \"키워드\"),\n", "    (\"####\", \"주요매서드\"),\n", "    (\"#####\", \"사용예시\"),\n", "]\n", "markdown_splitter = MarkdownHeaderTextSplitter(\n", "    headers_to_split_on=headers_to_split_on\n", ")\n", "\n", "with open(file_path, \"r\", encoding='utf-8') as f:\n", "    markdown_content = f.read()\n", "\n", "split_docs = markdown_splitter.split_text(markdown_content)\n", "\n", "# 메타데이터 추가 및 문서 구조화\n", "docs = []\n", "for doc in split_docs:\n", "    metadata = doc.metadata.copy()\n", "    metadata['source'] = file_path.split('/')[-1]\n", "\n", "    # 키워드 추출 (있는 경우)\n", "    if '키워드:' in doc.page_content:\n", "        keywords_start = doc.page_content.index('키워드:')\n", "        keywords_end = doc.page_content.index('\\n', keywords_start)\n", "        keywords = doc.page_content[keywords_start:keywords_end].replace('키워드:', '').strip()\n", "        metadata['keywords'] = keywords\n", "\n", "    # 사용 예시 추출 (있는 경우)\n", "    if '사용 예시:' in doc.page_content:\n", "        example_start = doc.page_content.index('사용 예시:')\n", "        example_end = doc.page_content.index('```', example_start + 1)\n", "        example = doc.page_content[example_start:example_end].replace('사용 예시:', '').strip()\n", "        metadata['example'] = example\n", "\n", "    docs.append(Document(page_content=doc.page_content, metadata=metadata))\n", "\n", "# keywords는 langchain에 context로 넣기 위해 string 형식으로 통합\n", "keywords = [i.metadata['키워드'] for i in docs]\n", "keywords_str = ''\n", "for i in keywords:\n", "    keywords_str += f\"\\n\\n{i}\"\n", "\n", "# keywords를 통해 코드를 가지고 오기 위해 code dictionray 생성: {key:code}\n", "dic_code = {}\n", "for i in docs:\n", "   dic_code[i.metadata['키워드']] = '//'+i.metadata['주요매서드'] + \"\\n\" +i.page_content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# chain_retriever"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['[스와이퍼, 지도 비교, 레이어 비교, swiperControl]', '[분할지도, 멀티뷰, 다중 지도, divideMapControl]']\n"]}], "source": ["from langchain_openai import ChatOpenAI\n", "from langchain_ollama import ChatOllama\n", "from langchain.prompts import PromptTemplate\n", "from langchain_core.runnables import RunnablePassthrough\n", "\n", "# llm = ChatOpenAI(model = 'gpt-4o-mini',\n", "#                  temperature= 0.0)\n", "llm = ChatOllama(model = 'llama3.1:8b-instruct-q8_0',\n", "                 temperature=0.0)\n", "\n", "prompt_template_retriever = \"\"\"\n", "Your task is to look at the user's question and Use the context to extract only what matches the user's question.\n", "Output only the content in the context in the same format as the example\n", "\n", "----\n", "context: \n", "{context}\n", "\n", "examples:\n", "question: 배경지도, 줌 컨트롤, 오버뷰 컨트롤, 축척, 이전/다음 화면이동이 추가된 지도를 보고 싶어\n", "\n", ">>>answer:\n", "'[배경지도 생성, 베이스맵, 베이스맵 컨트롤, basemapControl, 배경지도 설정]',\n", " '[줌 컨트롤, 지도 확대, 지도 축소, 줌 레벨 조정]',\n", " '[오버뷰 컨트롤, 미니맵, 전체 지도 보기, 위치 컨텍스트]',\n", " '[축척, 지도 축척, 거리 표시, 스케일 바]',\n", " '[이전/다음 화면 이동, 지도 네비게이션, 위치 기억, 브라우징 히스토리]'\n", "\n", " question: 스와이퍼, 레이어추가, 분할지도, 측정도구가 포함된 지도를 보고싶어\n", "\n", ">>>answer:\n", " '[레이어 추가, WMS, 지도 레이어, 데이터 시각화]',\n", " '[스와이퍼, 지도 비교, 레이어 비교, swiperControl]',\n", " '[분할지도, 멀티뷰, 다중 지도, divideMapControl]',\n", " '[측정도구, 면적, 거리, 반경, MeasureControl]'\n", " ----\n", "\n", " \n", "context:\n", "{context}\n", "\n", "question:\n", "{question}\n", "\n", "--------\n", ">>>Answer:\n", "\"\"\"\n", "\n", "prompt = PromptTemplate(template=prompt_template_retriever,\n", "                        input_variables=['qustion','context'])\n", "\n", "chain_retriever = (\n", "    {\"question\": RunnablePassthrough(),\n", "     \"context\": lambda x: keywords_str\n", "     }\n", "    |prompt\n", "    |llm\n", "    |(lambda x: x.content)\n", "    |(lambda x: x.split(\",\\n \"))\n", "    |(lambda x: [i[1:-1] for i in x])\n", ")\n", "print(chain_retriever.invoke('화면이 나눠지고 양쪽으로 비교할 수 있는 지도를 만들고 싶어'))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["query = \"\"\"배경지도 컨트롤, 줌 컨트롤, 오버뷰 컨트롤, 분할지도 컨트롤, 그리기 도구 컨트롤을 추가한 지도를 생성해줘.\"\"\" \n", "answer = chain_retriever.invoke(query)\n", "code_str = ''\n", "for i in answer: code_str += f'\\n\\n{dic_code[i]}'"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "//배경지도 컨트롤 (BasemapControl)\n", "//배경지도를 설정하고 변경할 수 있는 컨트롤입니다. 다양한 유형의 배경지도를 지원하며, 사용자가 쉽게 전환할 수 있도록 합니다.\n", "//[배경지도 생성, 베이스맵, 베이스맵 컨트롤, basemapControl, 배경지도 설정]\n", "//- setMap(map): 컨트롤을 지도에 추가 - switchBaseLayer(layerKey): 특정 배경지도로 전환 - getSetableBasemapList(): 설정 가능한 배경지도 목록 조회\n", "//배경지도 컨트롤 생성\n", "var basemapControl = new odf.BasemapControl();\n", "basemapControl.setMap(map);  \n", "//특정 배경지도(색각지도)로 전환\n", "basemapControl.switchBaseLayer('eMapColor');  \n", "//설정 가능한 배경지도 목록 조회\n", "var basemapList = basemapControl.getSetableBasemapList();\n", "\n", "//줌 컨트롤 (ZoomControl)\n", "//지도의 확대 및 축소 기능을 제공하는 컨트롤입니다. 사용자가 지도의 상세 수준을 쉽게 조절할 수 있게 합니다.\n", "//[줌 컨트롤, 지도 확대, 지도 축소, 줌 레벨 조정]\n", "//- setMap(map): 컨트롤을 지도에 추가 - zoomIn(): 지도 확대 - zoomOut(): 지도 축소\n", "//줌 컨트롤 생성\n", "var zoomControl = new odf.ZoomControl();\n", "zoomControl.setMap(map);  \n", "//지도 확대\n", "zoomControl.zoomIn();  \n", "//지도 축소\n", "zoomControl.zoomOut();\n", "\n", "//오버뷰 컨트롤 (OverviewMapControl)\n", "//현재 보고 있는 지도 영역의 전체적인 위치를 작은 지도로 보여주는 컨트롤입니다.\n", "//[오버뷰 컨트롤, 미니맵, 전체 지도 보기, 위치 컨텍스트]\n", "//- setMap(map): 컨트롤을 지도에 추가\n", "//overviewMapControl 생성\n", "var overviewMapControl = new odf.OverviewMapControl();\n", "overviewMapControl.setMap(map);\n", "\n", "//분할지도 컨트롤 (DivideMapControl)\n", "//기본 지도를 생성한 후 분할지도 컨트롤(DivideMapControl)을 추가합니다. 이 컨트롤은 화면을 여러 개의 지도 영역으로 나누어 다양한 배경지도를 동시에 표시할 수 있게 해줍니다.\n", "//[분할지도, 멀티뷰, 다중 지도, divideMapControl]\n", "//- setMap(map): 컨트롤을 지도에 추가\n", "//분할지도 컨트롤 생성\n", "var divideMapControl = new odf.DivideMapControl({\n", "//2분할지도 정의\n", "dualMap : [\n", "{\n", "mapOption : {\n", "basemap : {baroEMap : ['eMapAIR']},\n", "},\n", "},\n", "],\n", "//3분할지도 정의\n", "threepleMap: [\n", "{mapOption: {\n", "basemap: {baroEMap:['eMapColor']},\n", "},\n", "},\n", "{ mapOption: {\n", "basemap:{baroEMap: ['eMapWhite']},\n", "},\n", "},\n", "],\n", "//4분할지도 정의\n", "quadMap: [\n", "{\n", "mapOption: {\n", "basemap: {baroEMap : ['eMapAIR']},\n", "},\n", "},\n", "{\n", "mapOption: {\n", "basemap: {baroEMap : ['eMapColor']} ,\n", "},\n", "},\n", "{\n", "mapOption: {\n", "basemap: {baroEMap : ['eMapWhite']} ,\n", "},\n", "},\n", "],\n", "});\n", "//생성한 분할지도 컨트롤을 지도 객체에 추가\n", "divideMapControl.setMap(map);\n", "\n", "//그리기 도구 컨트롤 (DrawControl)\n", "//지도 위에 다양한 도형이나 마커를 그릴 수 있는 도구를 제공하는 컨트롤입니다.\n", "//[그리기 도구, 지도 마킹, 도형 그리기, 사용자 정의 오버레이, 그리기, 그리기, 그리기, 그리기]\n", "//- setMap(map): 컨트롤을 지도에 추가 - setDrawingMode(mode): 그리기 모드 설정 (점, 선, 다각형 등) - getFeatures(): 그려진 객체 목록 가져오기 그리기\n", "//그리기 컨트롤\n", "var drawControl = new odf.DrawControl();\n", "drawControl.setMap(map);\n"]}], "source": ["print(code_str)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 모델 및 프롬프트"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["prompt_template = \"\"\"\n", "당신은 지도마법사의 코드를 작성하는 전문가입니다. 사용자의 question을 바탕으로 코드를 작성하되\n", "아래 format 중 [추가코드 적용 위치]에 context를 적용하여 코드를 작성해주세요\n", "단, 컨트롤 추가 시에는 반드시 setmap 매소드를 사용해주세요\n", "\n", "----\n", "<question>question: \n", "{question}</question>\n", "\n", "----\n", "<context>context:\n", "{context}</context>\n", "\n", "----\n", "<format>format:\n", "{format}</format>\n", "\n", "Answer:\n", "\n", "\"\"\"\n", "\n", "prompt = PromptTemplate(template=prompt_template, \n", "                        input_variables=['question','example','context','format'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# final chain"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def stream_generate_from_open_source(system_prompt,query,model,docs,format):\n", "    # found_data = ''\n", "    # for i, doc in enumerate(docs):\n", "    #     found_data += str(f\"{i + 1}. {doc.page_content} \\n\")\n", "    system_prompt = system_prompt.replace(\"{context}\",docs).replace(\"{question}\",query).replace(\"{format}\",format)\n", "    \n", "    url = 'http://121.163.19.104:11434/api/generate'\n", "    headers = {\n", "        'accept': 'application/json',\n", "        'Content-Type': 'application/json'\n", "    }\n", "    data = {\n", "        'prompt': system_prompt,\n", "        'model': model,\n", "        'stream' : <PERSON><PERSON><PERSON>,\n", "        'options':{\"temperature\": 0.0},\n", "        \"keep_alive\" : '5m'\n", "    }\n", "\n", "    response = requests.post(url, headers=headers, data=json.dumps(data))\n", "    \n", "    return response.json()['response']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 오류났을 때 버전\n", "from tqdm import tqdm\n", "import pandas as pd\n", "# df_results_models = pd.DataFrame(columns = ['query','answer_llm','answer_retriever','model'])\n", "# llm_model = ChatOllama(model = 'llama3.1:70b',\n", "#                 temperature=0.0)\n", "llm_model = 'llama3.1:70b-instruct-q8_0'\n", "for j in tqdm(range(21,25)):\n", "    query = df_queries.loc[j]['queries']\n", "    answer_retriever = chain_retriever.invoke(query) # -> context 생성\n", "    code_str = ''\n", "    for k in answer_retriever: code_str += f'\\n\\n{dic_code[k]}'\n", "    answer = stream_generate_from_open_source(prompt_template,\n", "                                              query,\n", "                                              llm_model,\n", "                                              code_str,\n", "                                              format)\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "mppckbt", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}