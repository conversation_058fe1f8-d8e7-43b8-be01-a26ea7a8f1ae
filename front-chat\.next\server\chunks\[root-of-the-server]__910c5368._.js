module.exports = {

"[project]/.next-internal/server/app/(map)/api/layer/detailed-info/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/app/(map)/api/layer/detailed-info/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/server.js [app-route] (ecmascript)");
;
async function POST(request) {
    try {
        const { userId, lyrId, namespace, cntntsId, pageSize = 50, pageIndex = 1 } = await request.json();
        const apiKey = process.env.GEON_API_KEY;
        const apiBaseUrl = process.env.GEON_API_BASE_URL;
        if (!apiKey || !apiBaseUrl) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: "API 키 또는 기본 URL이 설정되지 않았습니다."
            }, {
                status: 500
            });
        }
        // 1. 컬럼 정보 조회
        const columnResponse = await fetch(`${apiBaseUrl}/builder/layer/column/select?crtfckey=${apiKey}&lyrId=${lyrId}&userId=${userId}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                crtfckey: apiKey
            }
        });
        if (!columnResponse.ok) {
            throw new Error(`컬럼 정보 조회 실패: ${columnResponse.status} ${columnResponse.statusText}`);
        }
        const columnData = await columnResponse.json();
        // 2. 속성 데이터 조회
        const dataResponse = await fetch(`${apiBaseUrl}/builder/layer/attributes/select?crtfckey=${apiKey}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                crtfckey: apiKey
            },
            body: JSON.stringify({
                typeName: `${namespace}:${cntntsId}`,
                pageIndex: pageIndex,
                pageSize: pageSize
            })
        });
        if (!dataResponse.ok) {
            throw new Error(`속성 데이터 조회 실패: ${dataResponse.status} ${dataResponse.statusText}`);
        }
        const attributeData = await dataResponse.json();
        if (!columnData.code || columnData.code !== 200 || !attributeData.code || attributeData.code !== 200) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: "레이어 정보를 찾을 수 없습니다."
            }, {
                status: 404
            });
        }
        // 컬럼 정보와 속성 데이터를 함께 반환
        const result = {
            columns: columnData.result.map((col)=>({
                    name: col.columnNm,
                    description: col.columnNcm,
                    type: col.dataTy,
                    editable: col.editPosblAt === "Y",
                    required: col.esntlAt === "Y",
                    minValue: col.mummLt,
                    maxValue: col.mxmmLt,
                    groupCode: col.cmmnGroupCode,
                    groupName: col.cmmnGroupCodeNm,
                    order: col.columnOrdr,
                    indicator: col.indictAt === "Y"
                })),
            data: attributeData.result.features.map((feature)=>feature.properties),
            totalCount: attributeData.result.pageInfo.totalCount,
            pageInfo: attributeData.result.pageInfo,
            pkColumnName: attributeData.result.pkColumnName
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
    } catch (error) {
        console.error("Error fetching layer detailed information:", error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: `레이어 상세 정보 조회 중 오류가 발생했습니다: ${error.message}`
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__910c5368._.js.map