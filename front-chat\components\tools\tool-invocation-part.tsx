"use client";

import React from "react";
import type { ToolInvocation } from "ai";
import { ToolResult } from "./tool-result";
import { ToolCall } from "./tool-call";
import { SearchAddressResult } from "./search-address-result";
import { SearchOriginResult } from "./search-origin-result";
import { SearchDestinationResult } from "./search-destination-result";
import { SearchDirectionsResult } from "./search-directions-result";
import { LayerListResult } from "./layer-list-result";
import { LayerInfoResult } from "./layer-info-result";
import { DensityAnalysisResult } from "./density-analysis-result";
import { LayerFilterResult } from "./layer-filter-result";
import { LayerStyleResult } from "./layer-style-result";
import { LayerRemoveResult } from "./layer-remove-result";
import { BasemapChangeResult } from "./basemap-change-result";
import { LayerAttributesResult } from "./layer-attributes-result";
import { UseMapReturn } from "@geon-map/odf";


interface ToolInvocationPartProps {
  invocation: ToolInvocation;
  addToolResult: (params: { toolCallId: string; result: string }) => void;
  className?: string;
  status: "submitted" | "streaming" | "ready" | "error";
  mapState?: UseMapReturn;
}

export const ToolInvocationPart = ({
  invocation,
  addToolResult,
  className,
  status,
  mapState,
}: ToolInvocationPartProps) => {
  // 길찾기 요청 처리 함수
  const handleDirectionsRequest = (origin: string, destination: string) => {
    // searchDirections 도구를 호출하는 새로운 도구 결과 추가
    const directionsToolCall = {
      toolCallId: `directions-${Date.now()}`,
      result: JSON.stringify({
        toolName: "searchDirections",
        args: { origin, destination },
        status: "pending"
      })
    };

    addToolResult(directionsToolCall);
  };

  if (invocation.state === "result") {
    // 각 도구별로 전용 결과 컴포넌트 사용
    switch (invocation.toolName) {
      case "searchAddress":
        return (
          <SearchAddressResult
            key={invocation.toolCallId}
            content={invocation.result}
            className={className}
            mapState={mapState}
            onDirectionsRequest={handleDirectionsRequest}
          />
        );

      case "searchOrigin":
        return (
          <SearchOriginResult
            key={invocation.toolCallId}
            content={invocation.result}
            className={className}
            mapState={mapState}
          />
        );

      case "searchDestination":
        return (
          <SearchDestinationResult
            key={invocation.toolCallId}
            content={invocation.result}
            className={className}
            mapState={mapState}
          />
        );

      case "searchDirections":
        return (
          <SearchDirectionsResult
            key={invocation.toolCallId}
            content={invocation.result}
            className={className}
            mapState={mapState}
            toolCallId={invocation.toolCallId}
          />
        );

      case "getLayerList":
        return (
          <LayerListResult
            key={invocation.toolCallId}
            content={invocation.result}
            className={className}
            mapState={mapState}
          />
        );

      case "getLayer":
        return (
          <LayerInfoResult
            key={invocation.toolCallId}
            content={invocation.result}
            className={className}
            mapState={mapState}
          />
        );

      case "getLayerAttributes":
        return (
          <LayerAttributesResult
            key={invocation.toolCallId}
            content={invocation.result}
            invocation={invocation}
            className={className}
            mapState={mapState}
          />
        );

      case "performDensityAnalysis":
        return (
          <DensityAnalysisResult
            key={invocation.toolCallId}
            content={invocation.result}
            className={className}
          />
        );

      case "createLayerFilter":
        return (
          <LayerFilterResult
            key={invocation.toolCallId}
            content={invocation.result}
            className={className}
          />
        );

      case "updateLayerStyle":
        return (
          <LayerStyleResult
            key={invocation.toolCallId}
            content={invocation.result}
            className={className}
          />
        );

      case "removeLayer":
        return (
          <LayerRemoveResult
            key={invocation.toolCallId}
            content={invocation.result}
            className={className}
          />
        );

      case "changeBasemap":
        return (
          <BasemapChangeResult
            key={invocation.toolCallId}
            content={invocation.result}
            className={className}
            mapState={mapState}
          />
        );

      // 기본 ToolResult 컴포넌트 사용
      default:
        return (
          <ToolResult
            key={invocation.toolCallId}
            toolName={invocation.toolName}
            state="result"
            className={className}
            content={
              typeof (invocation as any).result === "string"
                ? ((invocation as any).result as string)
                : JSON.stringify(
                  (invocation as any).result ?? invocation.args,
                  null,
                  2
                )
            }
          />
        );
    }
  }

  if (invocation.state === "call" || invocation.state === "partial-call") {
    return (
      <ToolCall
        key={invocation.toolCallId}
        invocation={invocation}
        addToolResult={addToolResult}
        className={className}
      />
    );
  }
};
