# util 목록
## 0.train_db_generator_kor.ipynb

## 1.test_model_gpu.ipynb

## 1-1.test_model_pc.ipynb
- 1의 local 버전, pc에 ollama 및 사용하고자 하는 모델이 추가되어 있어야 사용 가능

## 2.hf_downloader.py: gpu 서버에서 허깅페이스 공개 오픈소스 llm관련 파일 다운로드
- 실행: python hf_downloader
- 다운로드 타입설정: 레포지토리 단위: repo, 파일 단위: file
- repo 선택 시: local_dir, repo_id 입력
- file 선택 시: local_dir, repo_id, file_name 입력

## 3.move_gguf.sh: 선택 경로 내의 gguf파일을 개별 폴더 단위로 정리

## 4.modelfile_generator.sh: 선택경로 내의 개별 모델 디렉터리를 순회하며 modelfile 생성
## 5.ollama_model_loader: 3,4의 결과로 생성된 .gguf, Modelfile을 이용, ollama model으로 추가 