import {
  InvalidToolArgumentsError,
  type Message,
  NoSuchToolError,
  ToolExecutionError,
  convertToCoreMessages,
  createDataStreamResponse,
  streamText,
} from "ai";

import { difyModel } from "@/lib/ai";
import { models } from "@/lib/ai/dev-models";
import { deleteChatById, getChatById } from "@/lib/db/queries";
import { getMostRecentUserMessage } from "@/lib/utils";

export async function POST(request: Request) {
  const {
    id,
    messages,
    modelId,
    conversationId,
  }: { 
    id: string; 
    messages: Array<Message>; 
    modelId: string;
    conversationId?: string; 
  } = await request.json();

  const model = models.find((model) => model.id === modelId);

  if (!model) {
    return new Response("Model not found", { status: 404 });
  }

  const coreMessages = convertToCoreMessages(messages);
  const userMessage = getMostRecentUserMessage(coreMessages);

  if (!userMessage) {
    return new Response("No user message found", { status: 400 });
  }



  return createDataStreamResponse({
    execute: (dataStream) => {
      const result = streamText({
        model: difyModel(model.id),
        messages: [{ role: "user", content: userMessage.content as string }],
        headers: {
          'user-id': 'geon-user',
          ...(conversationId ? { 'chat-id': conversationId } : {})
        },
        onFinish: async ({ providerMetadata }) => {
          const difyConversationId = providerMetadata?.difyWorkflowData?.conversationId;
          if (difyConversationId) {
            dataStream.writeData({
              type: 'conversation-id',
              id: difyConversationId
            });
          }
        },
      });

      // dataStream에 결과 병합
      result.mergeIntoDataStream(dataStream, {
        sendReasoning: false
      });
    },
    onError: (error) => {
      console.error("Agent processing error:", error);
      if (NoSuchToolError.isInstance(error)) {
        return 'The model tried to call a unknown tool.';
      } else if (InvalidToolArgumentsError.isInstance(error)) {
        return 'The model called a tool with invalid arguments.';
      } else if (ToolExecutionError.isInstance(error)) {
        return 'An error occurred during tool execution.';
      } else {
        return 'An unknown error occurred.';
      }
    },
  });
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get("id");

  if (!id) {
    return new Response("Not Found", { status: 404 });
  }

  try {
    await deleteChatById({ id });
    return new Response("OK");
  } catch (error) {
    console.error(error);
    return new Response("Error", { status: 500 });
  }
}
