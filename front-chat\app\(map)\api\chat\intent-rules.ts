// 의도분석 규칙 및 템플릿 정의

// 헬퍼 함수들
function extractBaseKeyword(context: string): string {
  const input = context.toLowerCase();

  // 주요 키워드 매핑
  const keywordMap = {
    '건물': ['건물', '빌딩', '건축물'],
    '도로': ['도로', '길', '거리'],
    '상점': ['상점', '매장', '점포'],
    '학교': ['학교', '교육기관'],
    '병원': ['병원', '의료기관'],
    '공원': ['공원', '녹지']
  };

  for (const [base, variants] of Object.entries(keywordMap)) {
    if (variants.some(variant => input.includes(variant))) {
      return base;
    }
  }

  // 지역명 제거 후 키워드 추출
  const regions = ['서울', '부산', '대구', '인천', '광주', '대전', '울산', '세종'];
  let cleanInput = input;
  regions.forEach(region => {
    cleanInput = cleanInput.replace(region, '').trim();
  });

  return cleanInput.split(' ')[0] || '레이어';
}

function extractAttributeCondition(context: string): string {
  const input = context.toLowerCase();

  const conditionMap = {
    '노후화된': '건축년도나 노후화',
    '오래된': '건축년도나 사용연한',
    '높은': '높이나 층수',
    '큰': '규모나 면적',
    '작은': '규모나 면적',
    '새로운': '건축년도나 준공년도',
    '최신': '건축년도나 준공년도'
  };

  for (const [pattern, condition] of Object.entries(conditionMap)) {
    if (input.includes(pattern)) {
      return condition;
    }
  }

  return '특정 조건';
}

function extractColorCondition(context: string): string | null {
  const input = context.toLowerCase();
  const colors = ['흰색', '노란색', '빨간색', '파란색', '초록색', '검은색'];

  for (const color of colors) {
    if (input.includes(color)) {
      return color;
    }
  }

  return null;
}

export const INTENT_CATEGORIES = {
  UNSUPPORTED_FEATURE: {
    priority: 1,
    keywords: ["근처", "주변", "실시간", "업로드", "전체목록", "CSV", "파일", "데이터 추가", "내 데이터"],
    description: "지원하지 않는 기능 요청"
  },
  DENSITY_ANALYSIS: {
    priority: 2,
    keywords: ["밀도", "분포", "집중도", "히트맵", "밀집"],
    constraints: "점 타입 레이어에서만 가능",
    description: "공간 밀도 분석"
  },
  LAYER_STYLE: {
    priority: 3,
    keywords: ["색상", "색깔", "스타일", "투명", "굵기", "파란색", "빨간색", "노란색", "초록색"],
    description: "레이어 스타일 변경"
  },
  LAYER_ADD: {
    priority: 4,
    keywords: ["추가", "보여줘", "찾아서", "검색해서"],
    description: "레이어 추가",
    // 속성 기반 요청 패턴 추가
    attributePatterns: [
      "노후화된", "오래된", "낡은", "높은", "큰", "작은", "새로운", "최신",
      "~년도 이후", "~년도 이전", "~이상", "~이하", "~보다"
    ],
    colorPatterns: ["흰색", "노란색", "빨간색", "파란색", "초록색", "검은색"]
  },
  LAYER_REMOVE: {
    priority: 4,
    keywords: ["삭제", "제거", "없애"],
    description: "레이어 삭제"
  },
  LAYER_FILTER: {
    priority: 4,
    keywords: ["필터", "조건", "범위"],
    description: "레이어 필터링"
  },
  LAYER_LIST: {
    priority: 4,
    keywords: ["목록", "리스트", "어떤 레이어"],
    description: "레이어 목록 조회"
  },
  MAP_CONTROL: {
    priority: 5,
    keywords: ["확대", "축소", "이동", "중심", "북쪽", "남쪽", "동쪽", "서쪽"],
    description: "지도 조작 및 제어"
  },
  BASEMAP_CHANGE: {
    priority: 5,
    keywords: ["배경지도", "기본지도", "항공지도", "위성지도"],
    description: "배경지도 변경"
  },
  NAVIGATION: {
    priority: 6,
    keywords: ["찾기", "검색", "길찾기", "경로", "위치", "어디"],
    description: "장소 검색 및 길찾기"
  },
  GENERAL_CONVERSATION: {
    priority: 7,
    keywords: [],
    description: "일반 대화 및 기타"
  }
} as const;

export const MESSAGE_GENERATION_RULES = {
  // 메시지 생성 원칙
  principles: [
    "구체적인 작업 단계 제시",
    "필요한 도구와 순서 명시",
    "제약사항 및 주의사항 포함",
    "내부 프로세스 언급 금지"
  ],
  
  // 카테고리별 메시지 템플릿
  templates: {
    DENSITY_ANALYSIS: (context: string) => 
      `${context}의 밀도분석을 수행하겠습니다. 밀도분석은 점 타입 레이어에서만 가능하므로, 점 타입 레이어를 검색하여 선택한 후 공간적 밀집도를 분석하고 히트맵으로 시각화하겠습니다.`,
    
    LAYER_STYLE: (context: string) => 
      `${context} 스타일을 변경하겠습니다. 현재 지도 상태를 확인하여 대상 레이어가 있는지 확인하고, 없으면 레이어를 검색하여 추가한 후 요청된 스타일을 적용하겠습니다.`,
    
    LAYER_ADD: (context: string, conversationContext?: string) => {
      // 대화 컨텍스트에서 워크플로우 상태 분석
      const hasChooseOptionResult = conversationContext?.includes('chooseOption') ||
                                   conversationContext?.includes('사용자가 선택한') ||
                                   conversationContext?.includes('선택된 레이어');



      // 속성 기반 요청인지 확인
      const attributePatterns = INTENT_CATEGORIES.LAYER_ADD.attributePatterns || [];
      const colorPatterns = INTENT_CATEGORIES.LAYER_ADD.colorPatterns || [];

      const hasAttributePattern = attributePatterns.some(pattern =>
        context.toLowerCase().includes(pattern.toLowerCase())
      );
      const hasColorPattern = colorPatterns.some(pattern =>
        context.toLowerCase().includes(pattern.toLowerCase())
      );

      // 워크플로우 상태별 메시지 생성
      if (hasChooseOptionResult && (hasAttributePattern || hasColorPattern)) {
        // 레이어가 선택된 상태 + 속성 조건이 있는 경우

        const attributeCondition = extractAttributeCondition(context);
        const colorCondition = extractColorCondition(context);

        let message = `선택한 레이어를 추가하고, ${attributeCondition} 기준으로`;

        if (colorCondition) {
          message += ` ${colorCondition}으로 표시해야 합니다.`;
        } else {
          message += ` 필터링하여 표시해야 합니다.`;
        }

        message += ` 정확한 기준을 위해 속성 정보를 조회하고 사용자에게 확인을 요청하세요.`;

        return message;
      }

      if (hasAttributePattern || hasColorPattern) {
        // 속성 기반 요청이지만 레이어가 아직 선택되지 않은 경우
        const baseKeyword = extractBaseKeyword(context);
        const attributeCondition = extractAttributeCondition(context);
        const colorCondition = extractColorCondition(context);

        let message = `${baseKeyword} 관련 레이어를 검색하고, ${attributeCondition} 기준에 따라 필터링하여`;

        if (colorCondition) {
          message += ` ${colorCondition}으로 표시해주세요.`;
        } else {
          message += ` 지도에 표시해주세요.`;
        }

        return message;
      }

      // 일반적인 레이어 추가 요청
      return `${context} 레이어를 검색하여 지도에 추가하겠습니다. 키워드로 관련 레이어를 찾아 사용자가 선택할 수 있도록 목록을 제공하겠습니다.`;
    },
    
    LAYER_REMOVE: (context: string) => 
      `${context} 레이어를 삭제하겠습니다. 현재 지도에서 해당 레이어를 찾아 제거하겠습니다.`,
    
    LAYER_FILTER: (context: string) => 
      `${context} 조건으로 레이어를 필터링하겠습니다. 해당 레이어의 속성 정보를 확인하여 조건에 맞는 데이터만 표시하겠습니다.`,
    
    LAYER_LIST: () => 
      `현재 지도에 추가된 레이어 목록을 조회하겠습니다.`,
    
    MAP_CONTROL: (context: string) => 
      `지도 ${context} 작업을 수행하겠습니다.`,
    
    BASEMAP_CHANGE: (context: string) => 
      `배경지도를 ${context}로 변경하겠습니다. 사용 가능한 배경지도 옵션을 제공하겠습니다.`,
    
    NAVIGATION: (context: string) => 
      `${context} 관련 장소 검색 및 길찾기를 수행하겠습니다.`,
    
    UNSUPPORTED_FEATURE: (context: string) => 
      `${context} 기능은 현재 지원하지 않습니다. 현재 지원하는 기능들을 안내하고 대안을 제시하겠습니다.`,
    
    GENERAL_CONVERSATION: (context: string) => 
      `${context}에 대해 답변드리겠습니다.`
  }
} as const;

// 의도 분류 로직
export function classifyIntent(userInput: string): string {
  const input = userInput.toLowerCase();
  
  // 우선순위 순으로 키워드 매칭
  const categories = Object.entries(INTENT_CATEGORIES)
    .sort(([,a], [,b]) => a.priority - b.priority);
  
  for (const [intent, config] of categories) {
    if (config.keywords.length === 0) continue; // GENERAL_CONVERSATION은 마지막에 처리
    
    const hasKeyword = config.keywords.some(keyword => 
      input.includes(keyword.toLowerCase())
    );
    
    if (hasKeyword) {
      return intent;
    }
  }
  
  return 'GENERAL_CONVERSATION';
}

// 메시지 생성 로직
export function generateMessage(intent: string, userInput: string, conversationContext?: string): string {
  const template = MESSAGE_GENERATION_RULES.templates[intent as keyof typeof MESSAGE_GENERATION_RULES.templates];

  if (typeof template === 'function') {
    return template(userInput, conversationContext);
  }

  return `사용자 요청을 처리하겠습니다: ${userInput}`;
}

// 간소화된 프롬프트 생성
export function createIntentAnalysisPrompt(): string {
  return `
당신은 사용자 요청을 분석하여 다음 중 하나로 분류합니다:

**의도 카테고리:**
- LAYER_*: 레이어 관련 작업 (추가/삭제/스타일/필터)
- MAP_*: 지도 조작 (확대/축소/이동/배경지도)
- NAVIGATION: 장소 검색/길찾기
- DENSITY_ANALYSIS: 밀도 분석 (점 타입 레이어만 가능)
- UNSUPPORTED_FEATURE: 지원 불가 기능
- GENERAL_CONVERSATION: 일반 대화

**지원 불가 키워드:** "근처", "주변", "실시간", "업로드", "전체목록", "CSV", "파일"

**워크플로우 상태 인식:**
- 이전 대화에서 getLayerList, chooseOption 등의 도구 호출 결과를 확인하세요
- 레이어가 이미 선택된 상태인지, 아직 검색 단계인지 파악하세요
- 현재 워크플로우 단계에 맞는 구체적인 다음 작업을 지시하세요

**속성 기반 레이어 요청 식별:**
- 형용사: "노후화된", "높은", "오래된", "새로운", "큰", "작은"
- 색상 지정: "흰색으로", "노란색으로", "빨간색으로"
- 이런 패턴이 감지되면 LAYER_ADD로 분류하고 구체적인 필터링 지시 생성

**응답 형식:** {"intent": "CATEGORY", "message": "작업 지시"}

**메시지 작성 원칙:**
- **레이어 선택 완료 + 속성 조건**: "선택한 레이어를 추가하고, X 기준으로 표시해야 합니다. 속성 정보를 조회하고 사용자에게 확인을 요청하세요."
- **속성 기반 요청 (초기)**: "X 관련 레이어를 검색하고, Y 기준에 따라 필터링하여 Z로 표시해주세요"
- **일반 레이어 요청**: "X 레이어를 검색하여 지도에 추가하겠습니다"
- 구체적인 작업 단계와 도구 사용 계획 제시
- 제약사항 명시 (예: 밀도분석은 점 타입만 가능)

사용자 요청을 분석하여 적절한 의도로 분류하고 다음 에이전트를 위한 명확한 작업 지시를 생성하세요.
  `.trim();
}

// 테스트용 함수 (개발 시에만 사용)
export function testMessageGeneration(userInput: string): string {
  const intent = classifyIntent(userInput);
  return generateMessage(intent, userInput);
}

// 예시 테스트
// console.log(testMessageGeneration("서울의 노후화된 건물을 보여줘"));
// 예상 결과: "건물 관련 레이어를 검색하고, 건축년도나 노후화 기준에 따라 필터링하여 지도에 표시해주세요."

// console.log(testMessageGeneration("서울의 노후화된 건물을 흰색으로 보여줘"));
// 예상 결과: "건물 관련 레이어를 검색하고, 건축년도나 노후화 기준에 따라 필터링하여 흰색으로 표시해주세요."
