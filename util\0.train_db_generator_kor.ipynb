{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 한글 단어 리트리버 튜닝"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# 환경변수 설정\n", "from langchain_openai import OpenAIEmbeddings\n", "from dotenv import load_dotenv\n", "load_dotenv('./dot.env')\n", "import os\n", "import re"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 형태소 분석기\n", "from kiwipiepy import Kiwi\n", "\n", "# markdown 문서 load 시 header 정보가 HTML tag 형식으로 불러들여짐\n", "from langchain_text_splitters import HTMLHeaderTextSplitter\n", "from langchain_text_splitters import MarkdownHeaderTextSplitter\n", "\n", "\n", "# embeding\n", "from langchain.retrievers import EnsembleRetriever\n", "from langchain_community.retrievers import BM25Retriever\n", "from langchain_core.documents import Document\n", "from langchain.vectorstores import FAISS\n", "\n", "# markdown\n", "import markdown\n", "\n", "kiwi = Kiwi()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Markdown 문서 load"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_markdown_file(file_path):\n", "    # if file_path.endswith('.md'):\n", "    with open(file_path, 'r', encoding='utf-8') as file:\n", "        content = file.read()\n", "    return markdown.markdown(content)\n", "doc = ''\n", "li_dir = sorted(os.listdir('../train_data_refined/'))[:-1]\n", "for i in li_dir:\n", "    markdown_content = load_markdown_file(f'../train_data_refined/{i}')\n", "    doc = '\\n'.join([doc, markdown_content])\n", "print(len(doc))\n", "with open('../train_data_refined/5.train-geon-example-double_org.txt') as f:\n", "    file = f.read()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# header\n", "headers_to_split_on = [\n", "    (\"h1\", \"Header 1\"),  # 분할할 헤더 레벨과 해당 레벨의 이름을 지정합니다.\n", "    (\"h2\", \"Header 2\"),\n", "    (\"h3\", \"Header 3\"),\n", "    (\"h4\", \"Header 4\"),\n", "]\n", "\n", "# Markdown 문서를 헤더 레벨에 따라 분할합니다.\n", "markdown_splitter = HTMLHeaderTextSplitter(\n", "    headers_to_split_on=headers_to_split_on\n", ")\n", "guide_splits = markdown_splitter.split_text(doc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# header\n", "headers_to_split_on = [\n", "    (\"#\", \"Header 1\"),  # 분할할 헤더 레벨과 해당 레벨의 이름을 지정합니다.\n", "    (\"##\", \"Header 2\"),\n", "    (\"###\", \"Header 3\"),\n", "]\n", "\n", "# Markdown 문서를 헤더 레벨에 따라 분할합니다.\n", "markdown_splitter = MarkdownHeaderTextSplitter(\n", "    headers_to_split_on=headers_to_split_on\n", ")\n", "code_splits = markdown_splitter.split_text(file)\n", "guide_splits.extend(code_splits)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["code_splits[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#bm25는 반환값을 list로 가지기에 아래 함수도 list 반환값 형식으로\n", "def kiwi_tokenize(text):\n", "    return [token.form for token in kiwi.tokenize(text)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["docs = str(guide_splits)\n", "\n", "# 파일 저장 경로\n", "file_path = './train_db.txt'\n", "\n", "# 문자열 데이터를 텍스트 파일로 저장\n", "with open(file_path, 'w', encoding='utf-8') as file:\n", "    file.write(docs)\n", "\n", "# 텍스트 파일에서 문자열 데이터를 읽기\n", "with open(file_path, 'r', encoding='utf-8') as file:\n", "    document_str = file.read()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class Document:\n", "    def __init__(self, page_content, metadata):\n", "        self.page_content = page_content\n", "        self.metadata = metadata\n", "\n", "    def __repr__(self):\n", "        return f\"Document(page_content={self.page_content}, metadata={self.metadata})\"\n", "# Function to parse the string and create Document objects\n", "def parse_document_string(doc_str):\n", "    pattern = re.compile(\n", "        r\"Document\\(page_content='(.*?)', metadata=\\{(.*?)\\}\\)\"\n", "    )\n", "    documents = []\n", "    matches = pattern.findall(doc_str)\n", "\n", "    for match in matches:\n", "        page_content = match[0]\n", "        metadata_str = match[1]\n", "        \n", "        metadata = {}\n", "        metadata_items = metadata_str.split(', ')\n", "        for item in metadata_items:\n", "            if ': ' in item:\n", "                key, value = item.split(': ', 1)\n", "                key = key.strip(\"'\")\n", "                value = value.strip(\"'\")\n", "                metadata[key] = value\n", "            else:\n", "                print(f\"Skipping invalid metadata item: {item}\")\n", "\n", "        documents.append(Document(page_content, metadata))\n", "\n", "    return documents\n", "\n", "# Parse the document string and create Document objects\n", "guide_splits = parse_document_string(document_str)\n", "\n", "# Print the parsed Document objects\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["guide_splits[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bm25 = BM25Retriever.from_documents(guide_splits)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# bm25 = BM25Retriever.from_documents(guide_splits)\n", "# kiwi_bm25 = BM25Retriever.from_documents(guide_splits, preprocess_func=kiwi_tokenize)\n", "# faiss = FAISS.from_documents(guide_splits, OpenAIEmbeddings()).as_retriever()\n", "# bm25_faiss_73 = EnsembleRetriever(\n", "#     retrievers=[bm25, faiss],  # 사용할 검색 모델의 리스트\n", "#     weights=[0.7, 0.3],  # 각 검색 모델의 결과에 적용할 가중치\n", "#     search_type=\"mmr\",  # 검색 결과의 다양성을 증진시키는 MMR 방식을 사용\n", "# )\n", "# bm25_faiss_37 = EnsembleRetriever(\n", "#     retrievers=[bm25, faiss],  # 사용할 검색 모델의 리스트\n", "#     weights=[0.7, 0.3],  # 각 검색 모델의 결과에 적용할 가중치\n", "#     search_type=\"mmr\",  # 검색 결과의 다양성을 증진시키는 MMR 방식을 사용\n", "# )\n", "# kiwibm25_faiss_73 = EnsembleRetriever(\n", "#     retrievers=[kiwi_bm25, faiss],  # 사용할 검색 모델의 리스트\n", "#     weights=[0.7, 0.3],  # 각 검색 모델의 결과에 적용할 가중치\n", "#     search_type=\"mmr\",  # 검색 결과의 다양성을 증진시키는 MMR 방식을 사용\n", "# )\n", "# kiwibm25_faiss_37 = EnsembleRetriever(\n", "#     retrievers=[kiwi_bm25, faiss],  # 사용할 검색 모델의 리스트\n", "#     weights=[0.3, 0.7],  # 각 검색 모델의 결과에 적용할 가중치\n", "#     search_type=\"mmr\",\n", "#     # 검색 결과의 다양성을 증진시키는 MMR 방식을 사용\n", "# )\n", "\n", "# retrievers = {\n", "#     \"bm25\": bm25,\n", "#     \"kiwi_bm25\": kiwi_bm25,\n", "#     \"faiss\": faiss,\n", "#     \"bm25_faiss_73\": bm25_faiss_73,\n", "#     \"bm25_faiss_37\": bm25_faiss_37,\n", "#     \"kiwi_bm25_faiss_73\": kiwibm25_faiss_73,\n", "#     \"kiwi_bm25_faiss_37\": kiwibm25_faiss_37,\n", "# }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def print_search_results(retrievers, query):\n", "    print(f\"Query: {query}\")\n", "    for name, retriever in retrievers.items():\n", "        print(f\"{name}    \\t: {retriever.invoke(query)}\")\n", "    print(\"===\" * 20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "from langchain import hub\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "# prompt = hub.pull(\"rlm/rag-prompt\")\n", "\n", "# You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. keep the answer concise. When the 코드 in [question], be sure to search only documents with header 1 as 코드.\n", "template = ''' \n", "You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question first. When the 코드 in [question], you must use documents with header 1 as 코드. If the answer to the question is not in the context, answer to the best of your ability based on clear facts.\n", "\n", "Question: {question} \n", "\n", "Context: {context} \n", "\n", "Answer:\n", "'''\n", "prompt = PromptTemplate.from_template(template)\n", "\n", "llm = ChatOpenAI(model = 'gpt-4o',\n", "                 max_tokens = 4000,\n", "                 )\n", "retriever = bm25\n", "\n", "chain = (\n", "    {\"question\": RunnablePassthrough(),\n", "     \"context\": (lambda x: retriever.get_relevant_documents(x)[:10]) \n", "     }\n", "    | prompt\n", "    | llm\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = '배경지도 최적화 코드를 알려줘'\n", "retriever.get_relevant_documents(query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = '배경지도 최적화 코드를 알려줘'\n", "retriever.get_relevant_documents(query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = '서울시청의 좌표를 배경지도 생성 코드에 추가해줘'\n", "print(chain.invoke(query).content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = '배경지도 최적화 코드를 생성하되 특정위치와 도형, 레이어는 제외하고 서울시청의 좌표를 추가하고 포인트로 표시해줘'\n", "print(chain.invoke(query).content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "langchain-kr-lwwSZlnu-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}