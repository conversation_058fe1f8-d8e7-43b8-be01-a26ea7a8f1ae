(()=>{var e={};e.id=922,e.ids=[922],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16387:(e,r,t)=>{"use strict";t.d(r,{Jh:()=>s,j5:()=>o,nK:()=>n,rZ:()=>a});let s=()=>{let e=process.env.GEON_API_BASE_URL||"http://121.163.19.101:14090",r=process.env.GEON_API_KEY;return r||console.warn("GEON_API_KEY가 설정되지 않았습니다."),{baseUrl:e,headers:{crtfckey:r||""},auth:{userId:process.env.GEON_API_USER_ID||"geonuser",password:process.env.GEON_API_USER_PASSWORD||"wavus1234!"}}},o=(e,r)=>{let t=r||s();return t.headers.crtfckey&&e.append("crtfckey",t.headers.crtfckey),e},a=e=>({"Content-Type":"application/json",...(e||s()).headers}),n=e=>(e||s()).auth.userId},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},37758:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55843:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>i,serverHooks:()=>y,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{POST:()=>p});var o=t(81160),a=t(18765),n=t(46332),l=t(28481),c=t(70669),u=t(16387);async function p(e){try{let{lyrId:r}=await e.json();if(!r)return l.NextResponse.json({error:"레이어 ID가 필요합니다"},{status:400});let t=(0,u.Jh)();console.log("=== getLayer API 호출 시작 ==="),console.log("lyrId:",r),console.log("userId: admin (AI 대화와 동일)"),console.log("insttCode: geonpaas (AI 대화와 동일)"),console.log("userSeCode: 14"),console.log("API Key:",t.headers.crtfckey?"존재함":"없음"),console.log("Base URL:",t.baseUrl);let s=await c._w.execute({userId:"admin",insttCode:"geonpaas",userSeCode:"14",lyrId:r},{abortSignal:new AbortController().signal,toolCallId:`api-layer-add-${r}-${Date.now()}`,messages:[]});return l.NextResponse.json(s)}catch(e){return console.error("=== 레이어 조회 실패 ==="),console.error("Error:",e),console.error("Stack:",e instanceof Error?e.stack:"No stack"),l.NextResponse.json({error:"레이어 조회에 실패했습니다"},{status:500})}}let i=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/layers/get-layer/route",pathname:"/api/layers/get-layer",filename:"route",bundlePath:"app/api/layers/get-layer/route"},resolvedPagePath:"C:\\chatbot\\front-chat\\app\\api\\layers\\get-layer\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:g,serverHooks:y}=i;function x(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:g})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77926:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[332,481,746,46,929,669],()=>t(55843));module.exports=s})();