import { notFound } from "next/navigation";

import { auth } from "@/app/(auth)/auth";
import { ChatMap } from "@/components/chat-map/chat-map";
import { getChatById, getMessagesByChatId } from "@/lib/db/queries";
import { convertToUIMessages } from "@/lib/utils";
import { cookies } from "next/headers";
import { DEFAULT_MODEL_NAME, models } from "@/lib/ai/models";
import type { Metadata } from "next";

export async function generateMetadata(props: { params: Promise<any> }): Promise<Metadata> {
  const params = await props.params;
  const { id } = params;

  try {
    const chat = await getChatById({ id });

    if (!chat) {
      return {
        title: "채팅을 찾을 수 없음",
        description: "요청하신 채팅을 찾을 수 없습니다.",
      };
    }

    const title = chat.title || "말로 만드는 지도";

    return {
      title,
      description: `${title} - AI와 대화하며 지도를 탐색하고 분석하세요.`,
      openGraph: {
        title: `${title} | 업무지원(챗봇)`,
        description: `${title} - AI와 대화하며 지도를 탐색하고 분석하세요.`,
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title: `${title} | 업무지원(챗봇)`,
        description: `${title} - AI와 대화하며 지도를 탐색하고 분석하세요.`,
      },
    };
  } catch (error) {
    return {
      title: "말로 만드는 지도",
      description: "AI와 대화하며 지도를 탐색하고 분석하세요.",
    };
  }
}

export default async function Page(props: { params: Promise<any> }) {
	1
	const params = await props.params;
	const { id } = params;
	const chat = await getChatById({ id });

	if (!chat) {
		notFound();
	}

	const session = await auth();

	if (chat.visibility === 'private') {
		if (!session || !session.user) {
			return notFound();
		}

		if (session.user.id !== chat.userId) {
			return notFound();
		}
	}

	const messagesFromDb = await getMessagesByChatId({
		id,
	});

	const cookieStore = await cookies();
	const modelIdFromCookie = cookieStore.get('model-id')?.value;
	const selectedModelId =
		models.find((model) => model.id === modelIdFromCookie)?.id ||
		DEFAULT_MODEL_NAME;

	return (
		<ChatMap
			key={id}
			id={chat.id}
			initialMessages={convertToUIMessages(messagesFromDb)}
			selectedModelId={selectedModelId}
			selectedVisibilityType={chat.visibility}
			isReadOnly={session?.user?.id !== chat.userId}
		/>
	);
}
