{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(auth)/auth.config.ts"], "sourcesContent": ["import {NextAuthConfig, Session, User} from \"next-auth\";\r\n\r\nexport const authConfig = {\r\n\tpages: {\r\n\t\tsignIn: \"/login\",\r\n\t\t// verifyRequest: `/login`,\r\n\t\t// error: \"/login\", // Error code passed in query string as ?error=\r\n\t\tnewUser: \"/\",\r\n\t},\r\n\tproviders: [\r\n\t\t// added later in auth.ts since it requires bcrypt which is only compatible with Node.js\r\n\t\t// while this file is also used in non-Node.js environments\r\n\t],\r\n\tcallbacks: {\r\n\t\tauthorized({ auth, request: { nextUrl } }) {\r\n\t\t\tconst isLoggedIn = !!auth?.user;\r\n\t\t\tconst isOnChat = nextUrl.pathname.startsWith(\"/geon-2d-map\");\r\n\t\t\tconst isOnLogin = nextUrl.pathname.startsWith(\"/login\");\r\n\t\t\tconst isOnRoot = nextUrl.pathname === \"/\";\r\n\t  \r\n\t\t\t// 루트 경로로 접근시 /geon-2d-map으로 리다이렉트\r\n\t\t\tif (isOnRoot) {\r\n\t\t\t  return Response.redirect(new URL(\"/geon-2d-map\", nextUrl));\r\n\t\t\t}\r\n\t  \r\n\t\t\t// 로그인된 상태에서 로그인/회원가입 페이지 접근시 /geon-2d-map으로 리다이렉트\r\n\t\t\tif (isLoggedIn && (isOnLogin)) {\r\n\t\t\t  return Response.redirect(new URL(\"/geon-2d-map\", nextUrl));\r\n\t\t\t}\r\n\t  \r\n\t\t\t// 로그인/회원가입 페이지는 항상 접근 가능\r\n\t\t\tif (isOnLogin) {\r\n\t\t\t  return true;\r\n\t\t\t}\r\n\t  \r\n\t\t\t// /geon-2d-map 페이지는 로그인한 사용자만 접근 가능\r\n\t\t\tif (isOnChat) {\r\n\t\t\t  return isLoggedIn;\r\n\t\t\t}\r\n\t  \r\n\t\t\treturn true;\r\n\t\t  },\r\n\t},\r\n} satisfies NextAuthConfig;\r\n"], "names": [], "mappings": ";;;AAEO,MAAM,aAAa;IACzB,OAAO;QACN,QAAQ;QACR,2BAA2B;QAC3B,mEAAmE;QACnE,SAAS;IACV;IACA,WAAW,EAGV;IACD,WAAW;QACV,YAAW,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;YACxC,MAAM,aAAa,CAAC,CAAC,MAAM;YAC3B,MAAM,WAAW,QAAQ,QAAQ,CAAC,UAAU,CAAC;YAC7C,MAAM,YAAY,QAAQ,QAAQ,CAAC,UAAU,CAAC;YAC9C,MAAM,WAAW,QAAQ,QAAQ,KAAK;YAEtC,kCAAkC;YAClC,IAAI,UAAU;gBACZ,OAAO,SAAS,QAAQ,CAAC,IAAI,IAAI,gBAAgB;YACnD;YAEA,kDAAkD;YAClD,IAAI,cAAe,WAAY;gBAC7B,OAAO,SAAS,QAAQ,CAAC,IAAI,IAAI,gBAAgB;YACnD;YAEA,yBAAyB;YACzB,IAAI,WAAW;gBACb,OAAO;YACT;YAEA,oCAAoC;YACpC,IAAI,UAAU;gBACZ,OAAO;YACT;YAEA,OAAO;QACN;IACH;AACD"}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(auth)/auth.ts"], "sourcesContent": ["import NextAuth, {User, Session, CredentialsSignin} from \"next-auth\";\r\nimport type { Provider } from \"next-auth/providers\";\r\nimport Credentials from \"next-auth/providers/credentials\";\r\nimport { authConfig } from \"./auth.config\";\r\n\r\ninterface ExtendedSession extends Session {\r\n    user: User;\r\n}\r\n\r\ninterface ValidationResponse {\r\n    code: number;\r\n    message: string;\r\n    result: {\r\n        isValid: boolean;\r\n        message: string;\r\n    };\r\n}\r\n\r\ninterface UserResponse {\r\n    code: number;\r\n    message: string;\r\n    result: {\r\n        userId: string;\r\n        userNm: string;\r\n        emailaddr: string | null;\r\n        userSeCode: string;\r\n        userSeCodeNm: string;\r\n        userImage: string | null;\r\n        insttCode: string;\r\n        insttNm: string | null;\r\n        insttUrl: string | null;\r\n        message: string;\r\n    };\r\n}\r\n\r\nclass InvalidLoginError extends CredentialsSignin {\r\n    code = \"Invalid identifier or password\"\r\n}\r\n\r\nconst PRODUCTION = process.env.NODE_ENV === \"production\";\r\nconst API_CERTIFICATE_KEY = 'tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh'\r\nconst SMT_URL = 'https://gsapi.geon.kr/smt'\r\n\r\nasync function validateLogin(userId: string, password: string): Promise<ValidationResponse> {\r\n    const params = new URLSearchParams({\r\n        crtfckey: API_CERTIFICATE_KEY,\r\n        userId,\r\n        password\r\n    });\r\n\r\n    const response = await fetch(`${SMT_URL}/login/validation?${params.toString()}`, {\r\n        method: 'POST',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            'crtfckey': API_CERTIFICATE_KEY\r\n        }\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n        throw new Error('로그인 검증에 실패했습니다.');\r\n    }\r\n\r\n    return data;\r\n}\r\n\r\nasync function getUserInfo(userId: string): Promise<UserResponse> {\r\n    const params = new URLSearchParams({\r\n        crtfckey: API_CERTIFICATE_KEY,\r\n        userId\r\n    });\r\n\r\n    const response = await fetch(`${SMT_URL}/users/id?${params.toString()}`, {\r\n        method: 'GET',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            'crtfckey': API_CERTIFICATE_KEY\r\n        }\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n        throw new Error('사용자 정보를 가져오는데 실패했습니다.');\r\n    }\r\n\r\n    return data;\r\n}\r\n\r\nexport const providers: Provider[] = [\r\n    Credentials({\r\n        credentials: {},\r\n        async authorize({id, password}: any) {\r\n            try {\r\n                // admin 계정으로 프론트엔드 로그인 허용\r\n                const frontendUserId = process.env.FRONTEND_LOGIN_USER_ID || 'admin';\r\n                const frontendPassword = process.env.FRONTEND_LOGIN_PASSWORD || 'password1234';\r\n\r\n                if (id === frontendUserId && password === frontendPassword) {\r\n                    // admin 계정으로 로그인 성공\r\n                    return {\r\n                        id: frontendUserId,\r\n                        name: 'GeOn City',\r\n                        email: '@example.com',\r\n                        userId: frontendUserId,\r\n                        userNm: 'GeOn City',\r\n                        emailaddr: '@example.com',\r\n                        userSeCode: '14',\r\n                        userSeCodeNm: '관리자',\r\n                        userImage: null,\r\n                        insttCode: 'GEON',\r\n                        insttNm: 'GeOn',\r\n                        insttUrl: null,\r\n                        message: '로그인 성공'\r\n                    };\r\n                }\r\n\r\n                // 기존 geonuser 계정도 유지 (호환성을 위해)\r\n                if (id === 'geonuser') {\r\n                    // 1. 로그인 검증\r\n                    const validation = await validateLogin(id, password);\r\n\r\n                    if (!validation.result.isValid) {\r\n                        throw new CredentialsSignin(validation.result.message);\r\n                    }\r\n\r\n                    // 2. 유저 정보 조회\r\n                    const userResponse = await getUserInfo(id);\r\n\r\n                    if (userResponse.code !== 200) {\r\n                        return new CredentialsSignin(userResponse.result.message);\r\n                    }\r\n\r\n                    // 3. 유저 정보 반환\r\n                    return {\r\n                        ...userResponse.result,\r\n                        id: userResponse.result.userId,\r\n                        name: userResponse.result.userNm || id,\r\n                        email: userResponse.result.emailaddr || `${userResponse.result.userNm}`,\r\n                    };\r\n                }\r\n\r\n                // 허용되지 않은 계정\r\n                throw new CredentialsSignin('admin 또는 geonuser 계정으로만 로그인할 수 있습니다.');\r\n            } catch (error) {\r\n                console.error('Auth error:', error);\r\n                throw error;\r\n            }\r\n        },\r\n    })\r\n]\r\n\r\nexport const providerMap = providers\r\n  .map((provider) => {\r\n      if (typeof provider === \"function\") {\r\n          const providerData = provider()\r\n          return { id: providerData.id, name: providerData.name }\r\n      } else {\r\n          return { id: provider.id, name: provider.name }\r\n      }\r\n  })\r\n  .filter((provider) => provider.id !== \"credentials\")\r\n\r\nexport const {\r\n    handlers,\r\n    auth,\r\n    signIn,\r\n    signOut,\r\n} = NextAuth({\r\n    ...authConfig,\r\n    providers,\r\n    session: {\r\n        strategy: \"jwt\",\r\n        maxAge: 30 * 60, // 30분 (30분 * 60초)\r\n    },\r\n    callbacks: {\r\n        async jwt({ token, user }) {\r\n            if (user) {\r\n                token.id = user.id;\r\n            }\r\n            return token;\r\n        },\r\n        async session({session, token,}: {\r\n            session: ExtendedSession;\r\n            token: any;\r\n        }) {\r\n            if (session.user) {\r\n                session.user.id = token.id as string;\r\n            }\r\n            return session;\r\n        },\r\n    }\r\n    // adapter: DrizzleAdapter(db, {\r\n    //     // @ts-ignore GitHub 로그인의 경우 email Null 가능성 존재\r\n    //     usersTable: users,\r\n    //     accountsTable: accounts,\r\n    //     sessionsTable: sessions,\r\n    //     verificationTokensTable: verificationTokens,\r\n    // }) as Adapter,\r\n    // cookies: {\r\n    //     sessionToken: {\r\n    //         name: `${PRODUCTION ? \"__Secure-\" : \"\"}next-auth.session-token`,\r\n    //         options: {\r\n    //             httpOnly: true,\r\n    //             sameSite: \"lax\",\r\n    //             path: \"/\",\r\n    //             secure: PRODUCTION,\r\n    //             // When working on localhost, the cookie domain must be omitted entirely (https://stackoverflow.com/a/1188145)\r\n    //             domain: PRODUCTION\r\n    //               ? `.${process.env.NEXT_PUBLIC_ROOT_DOMAIN}`\r\n    //               : undefined,\r\n    //         },\r\n    //     },\r\n    // },\r\n});\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;;;;AAgCA,MAAM,0BAA0B,4MAAA,CAAA,oBAAiB;IAC7C,OAAO,iCAAgC;AAC3C;AAEA,MAAM,aAAa,oDAAyB;AAC5C,MAAM,sBAAsB;AAC5B,MAAM,UAAU;AAEhB,eAAe,cAAc,MAAc,EAAE,QAAgB;IACzD,MAAM,SAAS,IAAI,gBAAgB;QAC/B,UAAU;QACV;QACA;IACJ;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,kBAAkB,EAAE,OAAO,QAAQ,IAAI,EAAE;QAC7E,QAAQ;QACR,SAAS;YACL,gBAAgB;YAChB,YAAY;QAChB;IACJ;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;QACd,MAAM,IAAI,MAAM;IACpB;IAEA,OAAO;AACX;AAEA,eAAe,YAAY,MAAc;IACrC,MAAM,SAAS,IAAI,gBAAgB;QAC/B,UAAU;QACV;IACJ;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,UAAU,EAAE,OAAO,QAAQ,IAAI,EAAE;QACrE,QAAQ;QACR,SAAS;YACL,gBAAgB;YAChB,YAAY;QAChB;IACJ;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;QACd,MAAM,IAAI,MAAM;IACpB;IAEA,OAAO;AACX;AAEO,MAAM,YAAwB;IACjC,CAAA,GAAA,8NAAA,CAAA,UAAW,AAAD,EAAE;QACR,aAAa,CAAC;QACd,MAAM,WAAU,EAAC,EAAE,EAAE,QAAQ,EAAM;YAC/B,IAAI;gBACA,0BAA0B;gBAC1B,MAAM,iBAAiB,QAAQ,GAAG,CAAC,sBAAsB,IAAI;gBAC7D,MAAM,mBAAmB,QAAQ,GAAG,CAAC,uBAAuB,IAAI;gBAEhE,IAAI,OAAO,kBAAkB,aAAa,kBAAkB;oBACxD,oBAAoB;oBACpB,OAAO;wBACH,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,QAAQ;wBACR,QAAQ;wBACR,WAAW;wBACX,YAAY;wBACZ,cAAc;wBACd,WAAW;wBACX,WAAW;wBACX,SAAS;wBACT,UAAU;wBACV,SAAS;oBACb;gBACJ;gBAEA,+BAA+B;gBAC/B,IAAI,OAAO,YAAY;oBACnB,YAAY;oBACZ,MAAM,aAAa,MAAM,cAAc,IAAI;oBAE3C,IAAI,CAAC,WAAW,MAAM,CAAC,OAAO,EAAE;wBAC5B,MAAM,IAAI,4MAAA,CAAA,oBAAiB,CAAC,WAAW,MAAM,CAAC,OAAO;oBACzD;oBAEA,cAAc;oBACd,MAAM,eAAe,MAAM,YAAY;oBAEvC,IAAI,aAAa,IAAI,KAAK,KAAK;wBAC3B,OAAO,IAAI,4MAAA,CAAA,oBAAiB,CAAC,aAAa,MAAM,CAAC,OAAO;oBAC5D;oBAEA,cAAc;oBACd,OAAO;wBACH,GAAG,aAAa,MAAM;wBACtB,IAAI,aAAa,MAAM,CAAC,MAAM;wBAC9B,MAAM,aAAa,MAAM,CAAC,MAAM,IAAI;wBACpC,OAAO,aAAa,MAAM,CAAC,SAAS,IAAI,GAAG,aAAa,MAAM,CAAC,MAAM,EAAE;oBAC3E;gBACJ;gBAEA,aAAa;gBACb,MAAM,IAAI,4MAAA,CAAA,oBAAiB,CAAC;YAChC,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,eAAe;gBAC7B,MAAM;YACV;QACJ;IACJ;CACH;AAEM,MAAM,cAAc,UACxB,GAAG,CAAC,CAAC;IACF,IAAI,OAAO,aAAa,YAAY;QAChC,MAAM,eAAe;QACrB,OAAO;YAAE,IAAI,aAAa,EAAE;YAAE,MAAM,aAAa,IAAI;QAAC;IAC1D,OAAO;QACH,OAAO;YAAE,IAAI,SAAS,EAAE;YAAE,MAAM,SAAS,IAAI;QAAC;IAClD;AACJ,GACC,MAAM,CAAC,CAAC,WAAa,SAAS,EAAE,KAAK;AAEjC,MAAM,EACT,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,OAAO,EACV,GAAG,CAAA,GAAA,yQAAA,CAAA,UAAQ,AAAD,EAAE;IACT,GAAG,yIAAA,CAAA,aAAU;IACb;IACA,SAAS;QACL,UAAU;QACV,QAAQ,KAAK;IACjB;IACA,WAAW;QACP,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACrB,IAAI,MAAM;gBACN,MAAM,EAAE,GAAG,KAAK,EAAE;YACtB;YACA,OAAO;QACX;QACA,MAAM,SAAQ,EAAC,OAAO,EAAE,KAAK,EAG5B;YACG,IAAI,QAAQ,IAAI,EAAE;gBACd,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;YAC9B;YACA,OAAO;QACX;IACJ;AAuBJ"}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\r\nimport { auth } from \"./app/(auth)/auth\";\r\n\r\nexport const config = {\r\n    matcher: [\r\n        /*\r\n         * Match all paths except for:\r\n         * 1. /api routes\r\n         * 2. /_next (Next.js internals)\r\n         * 3. /_static (inside /public)\r\n         * 4. all root files inside /public (e.g. /favicon.ico)\r\n         */\r\n        \"/((?!api/|_next/|_static/|_vercel|[\\\\w-]+\\\\.\\\\w+).*)\",\r\n    ],\r\n};\r\n\r\nexport default auth((req) => {\r\n    const url = req.nextUrl;\r\n    const pathname = url.pathname;\r\n    const session = req.auth;\r\n\r\n    // 테스트용 상세 로깅\r\n    if (session) {\r\n        console.log(`[MIDDLEWARE] Session details:`, {\r\n            userId: session.user?.id,\r\n            userEmail: session.user?.email,\r\n            expires: session.expires\r\n        });\r\n    }\r\n\r\n    // 테스트용: URL 파라미터로 가짜 사용자 시뮬레이션\r\n    const testUser = url.searchParams.get('testUser');\r\n    if (testUser && process.env.NODE_ENV === 'development') {\r\n        console.log(`[MIDDLEWARE] TEST MODE: Simulating user ${testUser}`);\r\n        // 가짜 세션 객체 생성\r\n        const fakeSession = {\r\n            user: { id: testUser },\r\n            expires: new Date(Date.now() + 30 * 60 * 1000).toISOString()\r\n        };\r\n        // @ts-ignore - 테스트용\r\n        req.auth = fakeSession;\r\n        console.log(`[MIDDLEWARE] TEST MODE: Created fake session for ${testUser}`);\r\n    }\r\n\r\n    // 루트 경로 접근 시 geon-2d-map으로 리다이렉트\r\n    if (pathname === '/') {\r\n        console.log(`[MIDDLEWARE] Root path, redirecting to /geon-2d-map`);\r\n        return NextResponse.redirect(new URL('/geon-2d-map', req.url));\r\n    }\r\n\r\n    // 로그인 페이지 처리\r\n    if (pathname.startsWith('/login')) {\r\n        console.log(`[MIDDLEWARE] Login page access`);\r\n        // 유효한 세션이 있는 경우 (admin 또는 geonuser) geon-2d-map으로 리다이렉트\r\n        if (session && (session.user?.id === 'geonuser' || session.user?.id === 'admin')) {\r\n            console.log(`[MIDDLEWARE] Valid session for ${session.user?.id}, redirecting to geon-2d-map`);\r\n            return NextResponse.redirect(new URL('/geon-2d-map', req.url));\r\n        }\r\n        // 그 외의 경우 (세션 없음, 또는 잘못된 사용자) 로그인 페이지 접근 허용\r\n        console.log(`[MIDDLEWARE] Allowing login page access - Session: ${session ? 'exists but invalid user' : 'none'}`);\r\n        return NextResponse.next();\r\n    }\r\n\r\n    // geon-2d-map 경로에 대한 특별한 처리\r\n    if (pathname.startsWith('/geon-2d-map')) {\r\n        console.log(`[MIDDLEWARE] geon-2d-map access attempt`);\r\n        // 세션이 없는 경우\r\n        if (!session) {\r\n            console.log('[MIDDLEWARE] No session found, redirecting to login');\r\n            return NextResponse.redirect(new URL('/login?callbackUrl=' + encodeURIComponent(req.url), req.url));\r\n        }\r\n\r\n        // 허용되지 않은 사용자인 경우 (admin 또는 geonuser가 아닌 경우)\r\n        if (session.user?.id !== 'geonuser' && session.user?.id !== 'admin') {\r\n            console.log(`[MIDDLEWARE] Invalid user: ${session.user?.id}, clearing cookies and redirecting to login`);\r\n            // 로그아웃 처리를 위해 세션 쿠키 삭제\r\n            const response = NextResponse.redirect(new URL('/login?callbackUrl=' + encodeURIComponent(req.url), req.url));\r\n            // NextAuth v5에서는 authjs.session-token 사용\r\n            response.cookies.delete('authjs.session-token');\r\n            response.cookies.delete('__Secure-authjs.session-token');\r\n            console.log(`[MIDDLEWARE] Cookies cleared, redirecting to login`);\r\n            return response;\r\n        }\r\n\r\n        console.log(`[MIDDLEWARE] Valid session for user: ${session.user?.id}, allowing access`);\r\n    }\r\n\r\n    console.log(`[MIDDLEWARE] Allowing request to proceed`);\r\n    return NextResponse.next();\r\n});\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEO,MAAM,SAAS;IAClB,SAAS;QACL;;;;;;SAMC,GACD;KACH;AACL;uCAEe,CAAA,GAAA,+HAAA,CAAA,OAAI,AAAD,EAAE,CAAC;IACjB,MAAM,MAAM,IAAI,OAAO;IACvB,MAAM,WAAW,IAAI,QAAQ;IAC7B,MAAM,UAAU,IAAI,IAAI;IAExB,aAAa;IACb,IAAI,SAAS;QACT,QAAQ,GAAG,CAAC,CAAC,6BAA6B,CAAC,EAAE;YACzC,QAAQ,QAAQ,IAAI,EAAE;YACtB,WAAW,QAAQ,IAAI,EAAE;YACzB,SAAS,QAAQ,OAAO;QAC5B;IACJ;IAEA,+BAA+B;IAC/B,MAAM,WAAW,IAAI,YAAY,CAAC,GAAG,CAAC;IACtC,IAAI,YAAY,oDAAyB,eAAe;QACpD,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,UAAU;QACjE,cAAc;QACd,MAAM,cAAc;YAChB,MAAM;gBAAE,IAAI;YAAS;YACrB,SAAS,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;QAC9D;QACA,oBAAoB;QACpB,IAAI,IAAI,GAAG;QACX,QAAQ,GAAG,CAAC,CAAC,iDAAiD,EAAE,UAAU;IAC9E;IAEA,iCAAiC;IACjC,IAAI,aAAa,KAAK;QAClB,QAAQ,GAAG,CAAC,CAAC,mDAAmD,CAAC;QACjE,OAAO,sSAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,gBAAgB,IAAI,GAAG;IAChE;IAEA,aAAa;IACb,IAAI,SAAS,UAAU,CAAC,WAAW;QAC/B,QAAQ,GAAG,CAAC,CAAC,8BAA8B,CAAC;QAC5C,wDAAwD;QACxD,IAAI,WAAW,CAAC,QAAQ,IAAI,EAAE,OAAO,cAAc,QAAQ,IAAI,EAAE,OAAO,OAAO,GAAG;YAC9E,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,QAAQ,IAAI,EAAE,GAAG,4BAA4B,CAAC;YAC5F,OAAO,sSAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,gBAAgB,IAAI,GAAG;QAChE;QACA,4CAA4C;QAC5C,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,UAAU,4BAA4B,QAAQ;QAChH,OAAO,sSAAA,CAAA,eAAY,CAAC,IAAI;IAC5B;IAEA,4BAA4B;IAC5B,IAAI,SAAS,UAAU,CAAC,iBAAiB;QACrC,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC;QACrD,YAAY;QACZ,IAAI,CAAC,SAAS;YACV,QAAQ,GAAG,CAAC;YACZ,OAAO,sSAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,wBAAwB,mBAAmB,IAAI,GAAG,GAAG,IAAI,GAAG;QACrG;QAEA,6CAA6C;QAC7C,IAAI,QAAQ,IAAI,EAAE,OAAO,cAAc,QAAQ,IAAI,EAAE,OAAO,SAAS;YACjE,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,QAAQ,IAAI,EAAE,GAAG,2CAA2C,CAAC;YACvG,uBAAuB;YACvB,MAAM,WAAW,sSAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,wBAAwB,mBAAmB,IAAI,GAAG,GAAG,IAAI,GAAG;YAC3G,yCAAyC;YACzC,SAAS,OAAO,CAAC,MAAM,CAAC;YACxB,SAAS,OAAO,CAAC,MAAM,CAAC;YACxB,QAAQ,GAAG,CAAC,CAAC,kDAAkD,CAAC;YAChE,OAAO;QACX;QAEA,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,QAAQ,IAAI,EAAE,GAAG,iBAAiB,CAAC;IAC3F;IAEA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,CAAC;IACtD,OAAO,sSAAA,CAAA,eAAY,CAAC,IAAI;AAC5B"}}]}