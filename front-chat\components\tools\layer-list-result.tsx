"use client";

import React, { useState } from "react";
import { Layers3, Search, Info, Plus, ChevronDown, ChevronUp, Database, User } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { componentStyles } from "@/lib/design-tokens";
import { CompactResultTrigger } from "./common/compact-result-trigger";
import { getToolDisplayInfo } from "../annotations";
import { LayerConfig, LayerInfo, UseMapReturn } from "@geon-map/odf";

// 실제 타입에 맞게 업데이트
interface LayerListItem {
  lyrId: string;
  cntntsId: string;
  jobClCode: string;
  jobClCodeNm: string;
  lyrNm: string;
  lyrClCode: string;
  lyrClCodeNm: string;
  lyrClSeCode: string;
  lyrClSeCodeNm: string;
  lyrPosesnSeCode: string;
  lyrPosesnSeCodeNm: string;
  lyrTySeCode: string;
  lyrTySeCodeNm: string;
  svcTySeCode: string;
  svcTySeCodeNm: string;
  cntmSeCode: string;
  cntmSeCodeNm: string;
  usePblonsipSeCode: string;
  usePblonsipSeCodeNm: string;
  useSttusSeCode: string;
  useSttusSeCodeNm: string;
  holdDataId: string;
  lyrDc: string;
  mapUrl: string;
  mapUrlParamtr: string;
  xyOrdrNrmltAt: string;
  registerId: string;
  registerNm: string;
  registDt: string;
  updusrId: string;
  updusrNm: string;
  updtDt: string;
  userNm: string;
  ownerNm: string;
  ownerId: string;
}

interface LayerListResponse {
  code: number;
  message: string;
  result: {
    pageInfo: {
      pageSize: number;
      pageIndex: number;
      totalCount: number;
    };
    list: LayerListItem[];
  };
}

interface LayerListResultProps {
  content: LayerListResponse | string;
  className?: string;
  mapState?: UseMapReturn;
}

const getLayerTypeColor = (type: string) => {
  const colors = {
    'wms': 'bg-blue-100/80 text-blue-700 border-blue-200/60',
    'wfs': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',
    'vector': 'bg-purple-100/80 text-purple-700 border-purple-200/60',
    'raster': 'bg-orange-100/80 text-orange-700 border-orange-200/60',
    'default': 'bg-neutral-100/80 text-neutral-700 border-neutral-200/60'
  };
  return colors[type as keyof typeof colors] || colors.default;
};

export function LayerListResult({ content, className, mapState }: LayerListResultProps) {
  let result: LayerListResponse;

  try {
    result = typeof content === 'string' ? JSON.parse(content) : content;
  } catch (e) {
    return null;
  }

  const handleAddLayer = async (layer: LayerListItem) => {
    if (!mapState) {
      toast.error("지도가 로드되지 않았습니다");
      return;
    }

    // const success = await addLayerToMap(mapState, layerInfo, layerConfig);

    // if (success) {
    //   toast.success(`${layer.lyrNm} 레이어를 추가했습니다`);
    // } else {
    //   toast.error("레이어 추가에 실패했습니다");
    // }
  };

  const handleViewLayerInfo = (layer: LayerListItem) => {
    console.log('레이어 정보 보기:', layer);
    toast.info(`${layer.lyrNm} 레이어 정보를 표시합니다`);
    // TODO: 레이어 상세 정보 모달 표시
  };

  if (result.code !== 200 || !result.result?.list?.length) {
    return (
      <div className={cn(componentStyles.card.warning, "p-3", className)}>
        <div className="flex items-center gap-3">
          <div className={cn(componentStyles.iconContainer.sm, "bg-amber-100/80 text-amber-600 border border-amber-200/60")}>
            <Search className="h-3.5 w-3.5" />
          </div>
          <div>
            <p className="font-medium text-amber-900 text-sm">레이어 목록 검색 결과가 없습니다</p>
            <p className="text-xs text-amber-700">다른 키워드로 다시 검색해보세요</p>
          </div>
        </div>
      </div>
    );
  }

  const layers = result.result.list;
  const totalCount = result.result.pageInfo.totalCount;
  const [showAll, setShowAll] = useState(layers.length <= 5);
  const displayLayers = showAll ? layers : layers.slice(0, 5);

  const toolInfo = getToolDisplayInfo("getLayerList");

  return (
    <CompactResultTrigger
      icon={toolInfo.icon}
      title={toolInfo.label}
      state="result"
      className={className}
      titleExtra={
        <div className="flex items-center gap-1">
          <Badge variant="outline" className="text-xs py-0 border bg-purple-100/80 text-purple-700 border-purple-200/60">
            {totalCount}개 발견
          </Badge>
          {layers.length > 5 && (
            <Badge variant="outline" className="text-xs py-0 border bg-neutral-100/80 text-neutral-700 border-neutral-200/60">
              상위 5개
            </Badge>
          )}
        </div>
      }
    >
      <div className="space-y-2">

      {/* 레이어 목록 */}
      <div className="space-y-1.5">
        {displayLayers.map((layer) => (
          <div
            key={layer.lyrId}
            className="group"
          >
              <Card className={cn(componentStyles.card.base, componentStyles.card.interactive, "bg-white/90 border-neutral-200/60 hover:border-purple-300/60 hover:bg-purple-50/30")}>
                <CardContent className="p-3">
                  <div className="flex items-center justify-between gap-3">
                    <div className="flex-1 min-w-0">
                      {/* 레이어 정보 */}
                      <div className="flex items-start gap-2 mb-1">
                        <div className={cn(componentStyles.iconContainer.sm, "bg-purple-100/80 text-purple-600 border border-purple-200/60 mt-0.5 flex-shrink-0")}>
                          <Layers3 className="h-2.5 w-2.5" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-neutral-900 text-sm leading-tight truncate">
                            {layer.lyrNm}
                          </p>
                          <div className="flex items-center gap-2 mt-0.5">
                            <Badge variant="outline" className={cn("text-xs border", getLayerTypeColor(layer.svcTySeCodeNm?.toLowerCase() || 'default'))}>
                              {layer.svcTySeCodeNm}
                            </Badge>
                            {layer.lyrClCodeNm && (
                              <Badge variant="outline" className="text-xs bg-neutral-100/80 text-neutral-600 border-neutral-200/60">
                                {layer.lyrClCodeNm}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* 추가 정보 */}
                      <div className="ml-7 space-y-0.5">
                        {layer.lyrDc && (
                          <p className="text-xs text-neutral-600 line-clamp-1">
                            {layer.lyrDc}
                          </p>
                        )}
                        <div className="flex items-center gap-3 text-xs text-neutral-500">
                          {layer.ownerNm && (
                            <div className="flex items-center gap-1">
                              <User className="h-2.5 w-2.5" />
                              <span>{layer.ownerNm}</span>
                            </div>
                          )}
                          {layer.registDt && (
                            <div className="flex items-center gap-1">
                              <Database className="h-2.5 w-2.5" />
                              <span>{new Date(layer.registDt).toLocaleDateString()}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* 컴팩트 액션 버튼들 */}
                    <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        size="sm"
                        variant="default"
                        className={cn(componentStyles.button.primary, "h-6 px-2 text-xs")}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAddLayer(layer);
                        }}
                      >
                        <Plus className="h-2.5 w-2.5 mr-1" />
                        추가
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className={cn(componentStyles.button.ghost, "h-6 w-6 p-0 hover:bg-purple-100/80")}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewLayerInfo(layer);
                        }}
                      >
                        <Info className="h-2.5 w-2.5" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
      </div>

      {/* 더보기/접기 버튼 */}
      {layers.length > 5 && (
        <div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAll(!showAll)}
            className={cn(componentStyles.button.secondary, "w-full h-8 text-xs border-dashed border-neutral-300 hover:border-solid hover:border-purple-300 hover:bg-purple-50/60 hover:text-purple-700")}
          >
            {showAll ? (
              <>
                <ChevronUp className="h-3 w-3 mr-1" />
                접기
              </>
            ) : (
              <>
                <ChevronDown className="h-3 w-3 mr-1" />
                {layers.length - 5}개 더보기
              </>
            )}
          </Button>
        </div>
      )}

      {/* 페이지 정보 */}
      {totalCount > layers.length && (
        <div>
          <div className="text-center p-2 text-xs text-neutral-500 bg-neutral-50/60 rounded-lg border border-neutral-200/50">
            총 {totalCount}개 중 {layers.length}개 표시
          </div>
        </div>
      )}
      </div>
    </CompactResultTrigger>
  );
}
