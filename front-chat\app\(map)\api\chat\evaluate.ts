import { openai } from "@ai-sdk/openai";
import { generateObject } from "ai";
import { z } from "zod";

// 평가 결과 스키마 (단순화)
export const evaluationSchema = z.object({
  isCompleted: z.boolean().describe("사용자의 의도가 충분히 해결되었는지 (도구 호출 여부와 무관)"),
  reason: z.string().describe("완료/미완료 판단 이유"),
  improvementSuggestions: z.array(z.string()).optional().describe("미완료 시 개선 제안사항"),
});

export type EvaluationResult = z.infer<typeof evaluationSchema>;

// 평가자 함수
export async function evaluateAgentResult(
  intentMessage: string,
  agentResponse: string,
  toolCalls: any[]
): Promise<EvaluationResult> {
  try {
    // HIL 도구 호출 확인 (최우선 처리)
    const hilTools = ['chooseOption', 'getUserInput', 'confirmWithCheckbox', 'getLocation'];
    const hasHILTool = toolCalls.some(call => hilTools.includes(call.toolName));

    if (hasHILTool) {
      return {
        isCompleted: true,
        reason: "사용자와의 상호작용을 진행 중이므로 완료로 판단합니다."
      };
    }
    const system = `당신은 AI Agent의 작업 수행 결과를 평가하는 전문가입니다.

      **핵심 평가 원칙:**
      1. 도구 호출 여부와 관계없이 사용자의 의도가 충분히 해결되었는지 판단
      2. "안녕하세요" 같은 간단한 인사는 도구 호출 없이도 완료될 수 있음
      3. 단순하게 "완료" vs "미완료" 두 가지로만 구분

      **완료 판단 기준 (isCompleted: true):**
      - 사용자의 원래 요청이 충분히 해결됨
      - 의도분석에서 제시한 목표가 달성됨
      - 더 이상 추가 작업이 필요하지 않음

      **미완료 판단 기준 (isCompleted: false):**
      - 사용자 요청이 아직 해결되지 않음
      - 필요한 작업이 수행되지 않음
      - 도구 호출 실패나 에러 발생
      - "분석을 시작하겠습니다" 등의 예고만 하고 실제 작업 미수행

      **판단 가이드라인:**
      - 전체 맥락을 종합적으로 고려하여 판단
      - 특정 키워드나 패턴에만 의존하지 말 것
      - 사용자 관점에서 요청이 해결되었는지 중점 평가
      `
      ;

    const prompt = `다음 Agent 수행 결과를 평가하세요:

**의도분석 메시지:**
${intentMessage}

**Agent 응답:**
${agentResponse}

**도구 정보:**
${toolCalls.length > 0 ? 
  toolCalls.map((call) => {
    return `toolName: ${call.toolName}, args: ${JSON.stringify(call.args)}`;
  }).join("\n")
  : "도구 호출 없음"}

**도구 호출 결과:**
${
  toolCalls.length > 0
    ? toolCalls
        .map((call) => {
          return `${JSON.stringify(call.result)}`;
        })
        .join("\n")
    : ""
}

**종합 평가 요청:**

**평가 기준**
1. **의도분석 목표 달성도**: 요청된 작업이 실제로 완료되었는가?
2. **작업 진행 상태 파악**:
   - 준비만 하고 핵심 작업 미수행 → isCompleted: false
   - 예: "분석을 시작하겠습니다" 후 performDensityAnalysis 미호출
   - 예: "스타일을 변경하겠습니다" 후 updateLayerStyle 미호출
3. **에러 및 실패 상황**: 도구 호출 실패나 잘못된 사용 → isCompleted: false

**🚨 핵심 판단 원칙:**
- **HIL 도구 호출 시**: 무조건 isCompleted: true (최우선)
- **작업 예고 후 미수행**: isCompleted: false
- **실제 작업 완료**: isCompleted: true
- **에러 발생**: isCompleted: false
`;


    console.log("=== 평가자 호출 ===");
    console.log("시스템 프롬프트:", system);
    console.log("평가 프롬프트:", prompt);

    const { object: evaluation } = await generateObject({
      model: openai("gpt-4.1-nano"),
      schema: evaluationSchema,
      system,
      prompt,
      temperature: 0
    });

    return evaluation;
  } catch (error) {
    console.error("평가자 실행 실패:", error);

    // 평가자 실패 시 기본값 반환 (미완료로 처리하여 재시도)
    return {
      isCompleted: false,
      reason: "평가자 오류로 인한 기본 판단: 작업이 미완료된 것으로 처리",
      improvementSuggestions: [
        "의도분석 메시지에 따라 필요한 도구를 호출하세요",
        "구체적인 작업을 수행하여 사용자의 요청을 완료하세요"
      ],
    };
  }
}

// 개선 메시지 생성
export function createImprovementMessage(evaluation: EvaluationResult): string {
  const suggestions = evaluation.improvementSuggestions || ["작업을 완료하기 위해 필요한 도구를 호출하세요"];
  const suggestionText = suggestions.join("\n- ");

  return `🔄 이전 시도가 불완전했습니다. 다음 사항을 개선하여 작업을 완료하세요:

**미완료 이유:**
${evaluation.reason}

**개선사항:**
- ${suggestionText}

**중요:** 설명보다는 실제 도구 호출을 통해 작업을 수행하세요.`;
}
