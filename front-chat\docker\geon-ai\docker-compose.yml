services:
  geon-ai:
    build:
      context: ../../
      dockerfile: docker/geon-ai/Dockerfile
    image: harbor.geon.kr/geon-ai/geon-ai
    restart: always
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - AUTH_URL=${AUTH_URL:-https://ai-map.geon.kr}
      - AUTH_SECRET=${NEXTAUTH_SECRET:-26683db73f2d85b3c7ebdae4a4742f04}
      - AUTH_TRUST_HOST=true
    ports:
      - "3000:3000"
    depends_on:
      - postgresql
  postgresql:
    image: postgres:14
    environment:
      POSTGRES_USER: geon
      POSTGRES_PASSWORD: geon
      POSTGRES_DB: geondb
    restart: always
    volumes:
      - ai_db:/var/lib/postgresql/data
    ports:
      - "15432:5432"

volumes:
  ai_db: