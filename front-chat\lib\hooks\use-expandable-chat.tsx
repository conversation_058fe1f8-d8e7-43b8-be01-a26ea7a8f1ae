// lib/hooks/use-expandable-chat.tsx
import React, { createContext, useContext, useState, ReactNode } from 'react';

interface ExpandableChatContextType {
	isOpen: boolean;
	toggleChat: () => void;
}

const ExpandableChatContext = createContext<ExpandableChatContextType | undefined>(undefined);

export const ExpandableChatProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
	const [isOpen, setIsOpen] = useState(false);

	const toggleChat = () => setIsOpen(prev => !prev);

	return (
		<ExpandableChatContext.Provider value={{ isOpen, toggleChat }}>
			{children}
		</ExpandableChatContext.Provider>
	);
};

export const useExpandableChat = () => {
	const context = useContext(ExpandableChatContext);
	if (context === undefined) {
		throw new Error('useExpandableChat must be used within a ExpandableChatProvider');
	}
	return context;
};
