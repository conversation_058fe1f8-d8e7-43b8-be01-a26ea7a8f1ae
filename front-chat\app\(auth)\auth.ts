import NextAuth, {User, Session, CredentialsSignin} from "next-auth";
import type { Provider } from "next-auth/providers";
import Credentials from "next-auth/providers/credentials";
import { authConfig } from "./auth.config";

interface ExtendedSession extends Session {
    user: User;
}

interface ValidationResponse {
    code: number;
    message: string;
    result: {
        isValid: boolean;
        message: string;
    };
}

interface UserResponse {
    code: number;
    message: string;
    result: {
        userId: string;
        userNm: string;
        emailaddr: string | null;
        userSeCode: string;
        userSeCodeNm: string;
        userImage: string | null;
        insttCode: string;
        insttNm: string | null;
        insttUrl: string | null;
        message: string;
    };
}

class InvalidLoginError extends CredentialsSignin {
    code = "Invalid identifier or password"
}

const PRODUCTION = process.env.NODE_ENV === "production";
const API_CERTIFICATE_KEY = 'tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh'
const SMT_URL = 'https://gsapi.geon.kr/smt'

async function validateLogin(userId: string, password: string): Promise<ValidationResponse> {
    const params = new URLSearchParams({
        crtfckey: API_CERTIFICATE_KEY,
        userId,
        password
    });

    const response = await fetch(`${SMT_URL}/login/validation?${params.toString()}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'crtfckey': API_CERTIFICATE_KEY
        }
    });

    const data = await response.json();

    if (!response.ok) {
        throw new Error('로그인 검증에 실패했습니다.');
    }

    return data;
}

async function getUserInfo(userId: string): Promise<UserResponse> {
    const params = new URLSearchParams({
        crtfckey: API_CERTIFICATE_KEY,
        userId
    });

    const response = await fetch(`${SMT_URL}/users/id?${params.toString()}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'crtfckey': API_CERTIFICATE_KEY
        }
    });

    const data = await response.json();

    if (!response.ok) {
        throw new Error('사용자 정보를 가져오는데 실패했습니다.');
    }

    return data;
}

export const providers: Provider[] = [
    Credentials({
        credentials: {},
        async authorize({id, password}: any) {
            try {
                // admin 계정으로 프론트엔드 로그인 허용
                const frontendUserId = process.env.FRONTEND_LOGIN_USER_ID || 'admin';
                const frontendPassword = process.env.FRONTEND_LOGIN_PASSWORD || 'password1234';

                if (id === frontendUserId && password === frontendPassword) {
                    // admin 계정으로 로그인 성공
                    return {
                        id: frontendUserId,
                        name: 'GeOn City',
                        email: '@example.com',
                        userId: frontendUserId,
                        userNm: 'GeOn City',
                        emailaddr: '@example.com',
                        userSeCode: '14',
                        userSeCodeNm: '관리자',
                        userImage: null,
                        insttCode: 'GEON',
                        insttNm: 'GeOn',
                        insttUrl: null,
                        message: '로그인 성공'
                    };
                }

                // 기존 geonuser 계정도 유지 (호환성을 위해)
                if (id === 'geonuser') {
                    // 1. 로그인 검증
                    const validation = await validateLogin(id, password);

                    if (!validation.result.isValid) {
                        throw new CredentialsSignin(validation.result.message);
                    }

                    // 2. 유저 정보 조회
                    const userResponse = await getUserInfo(id);

                    if (userResponse.code !== 200) {
                        return new CredentialsSignin(userResponse.result.message);
                    }

                    // 3. 유저 정보 반환
                    return {
                        ...userResponse.result,
                        id: userResponse.result.userId,
                        name: userResponse.result.userNm || id,
                        email: userResponse.result.emailaddr || `${userResponse.result.userNm}`,
                    };
                }

                // 허용되지 않은 계정
                throw new CredentialsSignin('admin 또는 geonuser 계정으로만 로그인할 수 있습니다.');
            } catch (error) {
                console.error('Auth error:', error);
                throw error;
            }
        },
    })
]

export const providerMap = providers
  .map((provider) => {
      if (typeof provider === "function") {
          const providerData = provider()
          return { id: providerData.id, name: providerData.name }
      } else {
          return { id: provider.id, name: provider.name }
      }
  })
  .filter((provider) => provider.id !== "credentials")

export const {
    handlers,
    auth,
    signIn,
    signOut,
} = NextAuth({
    ...authConfig,
    providers,
    session: {
        strategy: "jwt",
        maxAge: 30 * 60, // 30분 (30분 * 60초)
    },
    callbacks: {
        async jwt({ token, user }) {
            if (user) {
                token.id = user.id;
            }
            return token;
        },
        async session({session, token,}: {
            session: ExtendedSession;
            token: any;
        }) {
            if (session.user) {
                session.user.id = token.id as string;
            }
            return session;
        },
    }
    // adapter: DrizzleAdapter(db, {
    //     // @ts-ignore GitHub 로그인의 경우 email Null 가능성 존재
    //     usersTable: users,
    //     accountsTable: accounts,
    //     sessionsTable: sessions,
    //     verificationTokensTable: verificationTokens,
    // }) as Adapter,
    // cookies: {
    //     sessionToken: {
    //         name: `${PRODUCTION ? "__Secure-" : ""}next-auth.session-token`,
    //         options: {
    //             httpOnly: true,
    //             sameSite: "lax",
    //             path: "/",
    //             secure: PRODUCTION,
    //             // When working on localhost, the cookie domain must be omitted entirely (https://stackoverflow.com/a/1188145)
    //             domain: PRODUCTION
    //               ? `.${process.env.NEXT_PUBLIC_ROOT_DOMAIN}`
    //               : undefined,
    //         },
    //     },
    // },
});
