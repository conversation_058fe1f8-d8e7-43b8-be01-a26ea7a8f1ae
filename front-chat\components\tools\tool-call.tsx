"use client";

import React, { useId, useRef, useState } from "react";
import type { ToolInvocation } from "ai";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import {
  Bot,
  Check,
  Loader2 as Loader,
  Zap,
  MapPin,
  Search,
  Layers3,
  Compass,
  CheckCircle,
  AlertCircle,
  HelpCircle,
  Palette,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { SuggestionCard } from "./suggestion-card";
import { LayerStyleResult } from "./layer-style-result";

interface ToolCallProps {
  invocation: ToolInvocation;
  addToolResult: (params: { toolCallId: string; result: any }) => void;
  className?: string;
  initialOpen?: boolean;
  open?: boolean;
}

/**
 * UI shown while a tool invocation is in progress (state="call" or "partial-call").
 * Very similar to `ToolResult` but with a spinner and different default colours.
 */
export function ToolCall({
  invocation,
  addToolResult,
  className,
  initialOpen = true,
  open,
}: ToolCallProps) {
  // 고유한 ID 생성하여 각 컴포넌트의 value가 중복되지 않도록 함
  const uniqueId = useId();
  const itemValue = `tool-call-${uniqueId}`;
  const contentRef = useRef<HTMLDivElement>(null);

  // 하이드레이션 오류 방지를 위해 초기 상태를 false로 설정하고, 마운트 후에 실제 값으로 업데이트
  const [isMounted, setIsMounted] = React.useState(false);
  const [isOpenInternal, setIsOpenInternal] = useState(false);

  // 마운트 후에만 상태 업데이트
  React.useEffect(() => {
    setIsMounted(true);
    setIsOpenInternal(initialOpen);
  }, []);

  // external open prop 반영
  React.useEffect(() => {
    if (open !== undefined) {
      setIsOpenInternal(open);
    }
  }, [open]);

  const accordionValue = isOpenInternal ? itemValue : "";

  const getStateConfig = (
    st: "call" | "partial-call" | "result"
  ): { label: string; className: string; icon: React.ReactNode } => {
    switch (st) {
      case "call":
        return {
          label: "처리 중",
          className: "border-blue-500 bg-blue-50 text-blue-700",
          icon: <Loader className="h-3 w-3 animate-spin" />,
        };
      case "partial-call":
        return {
          label: "진행 중",
          className: "border-purple-500 bg-purple-50 text-purple-700",
          icon: <Loader className="h-3 w-3 animate-spin" />,
        };
      case "result":
        return {
          label: "완료",
          className: "border-green-500 bg-green-50 text-green-700",
          icon: <Check className="h-3 w-3" />,
        };
      default:
        return {
          label: st,
          className: "border-gray-300 bg-gray-50 text-gray-700",
          icon: <AlertCircle className="h-3 w-3" />,
        };
    }
  };

  const { toolName, state } = invocation;
  const content = JSON.stringify(invocation.args ?? {}, null, 2);

  const stateConfig = getStateConfig(state);

  // Check if this is an HIL (Human-in-the-Loop) component
  const isHILComponent = [
    "chooseOption",
    "getUserInput",
    "confirmWithCheckbox",
    "getLocation"
  ].includes(toolName);

  // Map internal tool names to user-friendly labels and icons
  const TOOL_CONFIG: Record<
    string,
    { name: string; icon: React.ReactNode; description: string }
  > = {
    createLayerFilter: {
      name: "레이어 필터 생성",
      icon: <Layers3 className="h-4 w-4" />,
      description: "지정한 조건으로 레이어에 필터를 적용합니다.",
    },
    updateLayerStyle: {
      name: "레이어 스타일 변경",
      icon: <Palette className="h-4 w-4" />,
      description: "레이어의 색상, 투명도, 크기 등을 변경합니다.",
    },
    generateCategoricalStyle: {
      name: "유형별 스타일 생성",
      icon: <Palette className="h-4 w-4" />,
      description: "속성 값에 따라 다른 색상으로 레이어를 표시합니다.",
    },
    getLayer: {
      name: "레이어 정보 조회",
      icon: <Layers3 className="h-4 w-4" />,
      description: "선택한 레이어의 상세 정보를 가져옵니다.",
    },
    getLayerAttributes: {
      name: "레이어 속성 조회",
      icon: <Search className="h-4 w-4" />,
      description: "레이어의 속성 정보를 확인합니다.",
    },
    getLayerList: {
      name: "레이어 목록 검색",
      icon: <Search className="h-4 w-4" />,
      description: "입력한 키워드로 레이어 목록을 검색합니다.",
    },
    performDensityAnalysis: {
      name: "밀도 분석 수행",
      icon: <Zap className="h-4 w-4" />,
      description: "선택한 레이어에 대한 밀도 분석을 실행합니다.",
    },
    searchAddress: {
      name: "주소 검색",
      icon: <Search className="h-4 w-4" />,
      description: "입력한 주소 또는 장소를 검색합니다.",
    },
    searchDirections: {
      name: "경로 탐색",
      icon: <Compass className="h-4 w-4" />,
      description: "출발지와 도착지 간의 경로를 탐색합니다.",
    },
    changeBasemap: {
      name: "배경지도 변경",
      icon: <MapPin className="h-4 w-4" />,
      description: "선택한 배경지도로 변경합니다.",
    },
    createVectorStyle: {
      name: "벡터 스타일 생성",
      icon: <Zap className="h-4 w-4" />,
      description: "레이어에 적용할 새로운 벡터 스타일을 생성합니다.",
    },
    getLocation: {
      name: "현재 위치 확인",
      icon: <MapPin className="h-4 w-4" />,
      description: "현재 사용자의 위치 정보를 가져옵니다.",
    },
    highlightGeometry: {
      name: "도형 강조",
      icon: <Zap className="h-4 w-4" />,
      description: "지도 위의 특정 도형을 강조하여 표시합니다.",
    },
    setCenter: {
      name: "지도 중심 이동",
      icon: <MapPin className="h-4 w-4" />,
      description: "지정한 위치로 지도 중심을 이동합니다.",
    },
    chooseOption: {
      name: "옵션 선택",
      icon: <Layers3 className="h-4 w-4" />,
      description: "제시된 옵션 중 하나를 선택해 주세요.",
    },
    getUserInput: {
      name: "정보 입력 요청",
      icon: <HelpCircle className="h-4 w-4" />,
      description: "작업을 계속하려면 추가 정보를 입력해 주세요.",
    },
    confirmWithCheckbox: {
      name: "확인 요청",
      icon: <CheckCircle className="h-4 w-4" />,
      description: "내용을 확인하고 동의해 주세요.",
    },
  };

  // Default configuration for unknown tools
  const defaultConfig = {
    name: toolName,
    icon: <Zap className="h-4 w-4" />,
    description: "작업을 처리하고 있습니다...",
  };

  // Get tool configuration
  const toolConfig = TOOL_CONFIG[toolName] || defaultConfig;
  const { name: friendlyName, icon: toolIcon, description } = toolConfig;

  // For HIL components, render a simplified UI
  if (isHILComponent) {
    return (
        <SuggestionCard invocation={invocation} onDone={addToolResult} />
    );
  }

  // For non-HIL components, show the detailed view
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      // transition={{ duration: 0.2 }}
      className={cn("rounded-xl border bg-card shadow-sm", className)}
    >
      <Accordion
        type="single"
        collapsible
        value={accordionValue}
        onValueChange={(value) => setIsOpenInternal(value === itemValue)}
        className="w-full"
      >
        <AccordionItem value={itemValue} className="border-0">
          <div className="flex items-start gap-3 p-4">
            <div className="mt-0.5 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-blue-50">
              <Bot className="h-4 w-4 text-blue-600" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between gap-2">
                <AccordionTrigger className="group flex items-center gap-2 p-0 text-sm hover:no-underline [&[data-state=open]>svg]:rotate-180">
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        "flex h-5 w-5 items-center justify-center rounded-full border-2",
                        stateConfig.className
                      )}
                    >
                      {stateConfig.icon}
                    </div>
                    <span className="font-medium text-foreground text-left">
                      {friendlyName}
                    </span>
                  </div>
                </AccordionTrigger>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge
                        variant="outline"
                        className={cn(
                          "flex items-center gap-1 text-xs font-normal h-6 px-2 py-0.5",
                          stateConfig.className
                        )}
                      >
                        {stateConfig.icon}
                        {stateConfig.label}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent side="top">
                      <p className="text-xs">{description}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <AccordionContent className="mt-3 pl-0 pb-0">
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  className="overflow-hidden"
                >
                  <div
                    ref={contentRef}
                    className="mt-2 rounded-lg bg-muted/30 p-3 text-sm"
                  >
                    <div className="flex items-start gap-3">
                      <div className="mt-0.5 flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 text-blue-600">
                        {toolIcon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-foreground">
                          {friendlyName}
                        </p>
                        <p className="text-muted-foreground text-sm mt-0.5">
                          {description}
                        </p>
                      </div>
                    </div>

                    <div className="mt-3 rounded-md border bg-background p-3">
                      <pre className="whitespace-pre-wrap break-all text-xs">
                        {content}
                      </pre>
                    </div>
                  </div>
                </motion.div>
              </AccordionContent>
            </div>
          </div>
        </AccordionItem>
      </Accordion>
    </motion.div>
  );
}
