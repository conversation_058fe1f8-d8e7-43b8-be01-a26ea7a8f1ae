{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import requests\n", "import asyncio\n", "from pydantic import BaseModel\n", "from fastapi import APIRouter,Query\n", "from langchain.callbacks import AsyncIteratorCallbackHandler\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_openai import ChatOpenAI\n", "from fastapi.responses import StreamingResponse\n", "from langchain.schema import HumanMessage, SystemMessage, AIMessage\n", "from langchain_community.vectorstores import FAISS\n", "from typing import List, AsyncIterable\n", "\n", "import os\n", "import csv\n", "import json\n", "from fastapi import APIRouter\n", "from langchain_community.embeddings.openai import OpenAIEmbeddings\n", "# from langchain_openai import OpenAIEmbeddings\n", "from langchain_community.document_loaders import PDFPlumberLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from fastapi import UploadFile, File, Form\n", "import shutil\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain_community.vectorstores import FAISS\n", "\n", "from langchain.chains.question_answering import load_qa_chain\n", "from langchain.prompts import PromptTemplate\n", "\n", "from dotenv import load_dotenv\n", "load_dotenv('./dot.env')\n", "\n", "router = APIRouter()\n", "class ChatHistory(BaseModel):\n", "    history: List\n", "\n", "#system_prompt\n", "#input_variable은 임의 설정\n", "name = '맵픽'\n", "language = 'korean'\n", "greeting = 'hihi'\n", "answer_length = 20000\n", "\n", "prompt_template = f\"\"\"You're {name}, you are helpful chatbot. You communicate exclusively in {language} language. \n", "            When I interact with you, you'll always start with the greeting: '{greeting}'.\n", "\n", "            You'll provide answers that are {answer_length}, straight to the point.\n", "            If I have a question related to the below context, I'll ask.\n", "\n", "            Here's the context you need to remember :\n", "            Context:\n", "            -----------\n", "            {{context}}\n", "            -----------\n", "\n", "            If a question doesn't pertain to the context, you'll respond with 'I do not have any trained info regarding your question' in {language} language.\n", "            Now, go ahead and answer my question: \n", "            Question: {{question}}\n", "\n", "            Answer in {language}:\"\"\"\n", "\n", "persist_directory = '../trained_db/Lk5521ILdWe9t18yi0zcJKU0teE3/chbt_ahiNhfU_all_embeddings'\n", "\n", "def stream_generate_from_open_source(system_prompt,query,model,docs):\n", "    found_data = ''\n", "    for i, doc in enumerate(docs):\n", "        found_data += str(f\"{i + 1}. {doc.page_content} \\n\")\n", "    system_prompt = system_prompt.replace(\"{context}\",found_data).replace(\"{question}\",query)\n", "    \n", "    url = 'http://121.163.19.104:11434/api/generate'\n", "    headers = {\n", "        'accept': 'application/json',\n", "        'Content-Type': 'application/json'\n", "    }\n", "    data = {\n", "        'prompt': system_prompt,\n", "        'model': model,\n", "        'stream' : <PERSON><PERSON><PERSON>,\n", "        'options':{\"temperature\": 0.0},\n", "        \"keep_alive\" : '5m'\n", "    }\n", "\n", "    response = requests.post(url, headers=headers, data=json.dumps(data))\n", "    \n", "    return response.json()['response']\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 사용가능한 모델 정보 출력"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Available Model List \n", "\n", "gemma2:27b-instruct-fp16\n", "llama3.1:70b-instruct-q8_0\n"]}], "source": ["# ollama server에 등록되어 있는 모델 정보\n", "dic = !curl http://121.163.19.104:11434/api/tags\n", "# dic 형태로 변환\n", "dic = json.loads(dic[5])\n", "# model name 출력\n", "model_list = []\n", "for i in dic['models']:\n", "    model_list.append(i['name'])\n", "print('Available Model List', '\\n')\n", "for i in sorted(model_list):\n", "    print(i)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/envs/mppckbt/lib/python3.9/site-packages/langchain_core/_api/deprecation.py:139: LangChainDeprecationWarning: The class `OpenAIEmbeddings` was deprecated in LangChain 0.0.9 and will be removed in 0.3.0. An updated version of the class exists in the langchain-openai package and should be used instead. To use it run `pip install -U langchain-openai` and import as `from langchain_openai import OpenAIEmbeddings`.\n", "  warn_deprecated(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["줌 생성과 축척을 반영한 배경지도 코드는 다음과 같습니다.\n", "\n", "```html\n", "<html>\n", "    <head>\n", "        <meta charset=\"utf-8\">\n", "        <title>wizard page</title>\n", "        <link href=\"https://developer.geon.kr/js/odf/odf.css\" rel=\"stylesheet\">\n", "        <script type=\"text/javascript\" src=\"https://developer.geon.kr/js/odf/odf.min.js\"></script>\n", "    </head>\n", "    <body>\n", "\n", "        <div id=\"map\" class=\"odf-view\" style=\"height:550px;\"></div>\n", "\n", "        <script>\n", "\n", "            // 지도 생성 start\n", "            var mapContainer = document.getElementById('map');\n", "            // 중심 좌표 정의\n", "            var coord = new odf.Coord<PERSON>(199312.9996, 551784.6924);\n", "\n", "            var mapOption = {\n", "                center:coord,\n", "                zoom:11,\n", "                projection:'EPSG:5186',\n", "                baroEMapURL: 'https://geon-gateway.geon.kr/map/api/map/baroemap',\n", "                baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',\n", "                basemap:{\n", "                    baroEMap:['eMapBasic','eMapAIR']\n", "                },\n", "            };\n", "            var map = new odf.Map(mapContainer, mapOption);\n", "            // 지도 생성 end\n", "\n", "            //배경지도(베이스맵) 컨트롤 생성 start\n", "            var basemapControl = new odf.BasemapControl();\n", "            //배경지도(베이스맵) 컨트롤 생성 end\n", "            //지도에 생성한 배경지도 컨트롤 적용 start\n", "            basemapControl.setMap(map);\n", "            //지도에 생성한 배경지도 컨트롤 적용 end\n", "\n", "            //줌(확대/축소) 컨트롤 생성 start\n", "            var zoomControl = new odf.ZoomControl();\n", "            //줌(확대/축소) 컨트롤 생성 end\n", "            //지도에 생성한 줌(확대/축소) 컨트롤 적용 start\n", "            zoomControl.setMap(map);\n", "            //지도에 생성한 줌(확대/축소) 컨트롤 적용 end\n", "\n", "            //축척 컨트롤 생성 start\n", "            var scaleControl = new odf.ScaleControl();\n", "            //축척 컨트롤 생성 end\n", "            //지도에 생성한 축척 컨트롤 적용 start\n", "            scaleControl.setMap(map);\n", "            //지도에 생성한 축척 컨트롤 적용 end\n", "\n", "        </script>\n", "\n", "    </body>\n", "</html>\n", "```\n", "\n", "이 코드는 줌 생성과 축척을 반영한 배경지도를 표시합니다.\n"]}], "source": ["# 위의 모델명 중 시험해보고 싶은 모델명을 복사하여 아래의 model 변수에 할당해 주시면 됩니다.\n", "\n", "# 질문입력\n", "\n", "embeddings = OpenAIEmbeddings()\n", "\n", "# gpt 기준 v2\n", "file_path = './train_data/org_v2_지도마법사하_240806.txt'\n", "\n", "loader = TextLoader(file_path,encoding='utf-8')\n", "docs = loader.load()\n", "\n", "## 텍스트 분할\n", "# text_splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=20)\n", "# split_docs = text_splitter.split_documents(documents)\n", "\n", "## 임베딩 및 벡터 데이터베이스 생성\n", "# Vectordb = FAISS.from_documents(documents, embeddings)\n", "\n", "## 검색기 생성\n", "# retriever = Vectordb.as_retriever(search_type=\"mmr\")\n", "\n", "# query = \"스와이퍼와 분할지도를 추가한 지도를 생성해줘\"\n", "query = \"줌 생성과 축척을 반영한 배경지도 코드 보여줘\"                                                                                          \n", "\n", "model = 'llama3.1:70b-instruct-q8_0'\n", "\n", "\n", "print(stream_generate_from_open_source(prompt_template,\n", "                                query,\n", "                                model,\n", "                                docs))\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<!DOCTYPE HTML>\n", "<html>\n", "<head>\n", "\t<meta charset=\"utf-8\">\n", "\t<link href=\"https://developer.geon.kr/js/odf/odf.css\" rel=\"stylesheet\">\n", "\t<script type=\"text/javascript\" src=\"https://developer.geon.kr/js/odf/odf.min.js\"></script>\n", "</head>\n", "<body>\n", "\t<div id=\"map\" class=\"odf-view\"></div>\n", "</body>\n", "<script>\n", "\t/* 맵 타겟 */\n", "\tvar mapContainer = document.getElementById('map');\n", "\n", "\t/* 맵 중심점 */\n", "\tvar coord = new odf.Coord<PERSON>(199312.9996,551784.6924);\n", "\n", "\t/* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */\n", "\tvar mapOption = {\n", "\t\tcenter : coord,\n", "\t\tzoom : 11,\n", "\t\tprojection : 'EPSG:5186',\n", "\t\t//proxyURL: 'proxyUrl.jsp',\n", "\t\t//proxyParam: 'url',\n", "\t\tbaroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',\n", "\t\tbaroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',\n", "\n", "console.log(\"현재배경지도레이어\");\n", "\tconsole.log(basemapControl.getPresentBaseLayer());\n", "\tconsole.log(\"현재배경지도레이어키:\"+basemapControl.getPresentBaseLayerKey());\n", "\tconsole.log(\"배경지도설정가능목록\");\n", "\tconsole.log(basemapControl.getSetableBasemapList());\n", "\tconsole.log(\"배경지도레이어 유무 확인:\"+basemapControl.hasBaseLayer(basemapControl.getPresentBaseLayerKey()));\n", "\tconsole.log(\"배경지도레이어그룹  유무 확인:\"+basemapControl.hasGrp('myGrp'));\n", "\n", "</script>\n", "</html>\n"]}], "source": ["# chatgpt를 이용한 답변\n", "model = 'gpt-3.5-turbo-16k'\n", "llm = ChatOpenAI(temperature=0.0, \n", "                    model_name=model, \n", "                    max_tokens=12000, \n", "                    request_timeout=1200)\n", "PROMPT = PromptTemplate(template=prompt_template, input_variables=[\"context\", \"question\"])\n", "chain = load_qa_chain(llm=llm, chain_type=\"stuff\", prompt=PROMPT)\n", "ans = chain({\"input_documents\": docs, \"question\": query}, return_only_outputs=True)\n", "answer = ans['output_text']\n", "print(answer)"]}], "metadata": {"kernelspec": {"display_name": "mppckbt", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}