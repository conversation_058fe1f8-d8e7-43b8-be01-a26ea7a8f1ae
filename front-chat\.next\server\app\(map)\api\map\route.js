(()=>{var e={};e.id=758,e.ids=[758],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28538:(e,t,a)=>{"use strict";a.d(t,{qQ:()=>K,yo:()=>ea,wA:()=>Y,TJ:()=>V,Lz:()=>M,zU:()=>Z,V7:()=>X,kA:()=>B,de:()=>L,mV:()=>W,yd:()=>z,Y0:()=>ee,yM:()=>J,$:()=>H,xZ:()=>et,Ci:()=>G});var r={};a.r(r),a.d(r,{accounts:()=>v,accountsRelations:()=>F,chat:()=>N,chatMap:()=>k,chatRelation:()=>_,map:()=>b,mapAccess:()=>x,mapSession:()=>E,mapView:()=>Q,mapsRelation:()=>R,message:()=>g,sessions:()=>q,sessionsRelations:()=>U,user:()=>w,userRelations:()=>P,verificationTokens:()=>I,vote:()=>A});var s=a(9652),o=a(33094),n=a(32019),i=a(57052),d=a(52363),c=a(4471),l=a(56850),u=a(94485),p=a(10302),m=a(7320),h=a(16077),f=a(56839),y=a(86746);let w=(0,n.cJ)("user",{id:(0,i.Qq)("id").primaryKey().$defaultFn(()=>(0,s.sX)()),name:(0,i.Qq)("name"),username:(0,i.Qq)("username"),gh_username:(0,i.Qq)("gh_username"),email:(0,i.Qq)("email").unique(),emailVerified:(0,d.vE)("emailVerified",{mode:"date"}),image:(0,i.Qq)("image"),createdAt:(0,d.vE)("createdAt",{mode:"date"}).defaultNow().notNull(),updatedAt:(0,d.vE)("updatedAt",{mode:"date"}).notNull().$onUpdate(()=>new Date)}),q=(0,n.cJ)("sessions",{sessionToken:(0,i.Qq)("sessionToken").primaryKey(),userId:(0,i.Qq)("userId").notNull().references(()=>w.id,{onDelete:"cascade",onUpdate:"cascade"}),expires:(0,d.vE)("expires",{mode:"date"}).notNull()},e=>({userIdIdx:(0,c.Pe)().on(e.userId)})),I=(0,n.cJ)("verificationTokens",{identifier:(0,i.Qq)("identifier").notNull(),token:(0,i.Qq)("token").notNull().unique(),expires:(0,d.vE)("expires",{mode:"date"}).notNull()},e=>({compositePk:(0,l.ie)({columns:[e.identifier,e.token]})})),v=(0,n.cJ)("accounts",{userId:(0,i.Qq)("userId").notNull().references(()=>w.id,{onDelete:"cascade",onUpdate:"cascade"}),type:(0,i.Qq)("type").notNull(),provider:(0,i.Qq)("provider").notNull(),providerAccountId:(0,i.Qq)("providerAccountId").notNull(),refresh_token:(0,i.Qq)("refresh_token"),refreshTokenExpiresIn:(0,u.nd)("refresh_token_expires_in"),access_token:(0,i.Qq)("access_token"),expires_at:(0,u.nd)("expires_at"),token_type:(0,i.Qq)("token_type"),scope:(0,i.Qq)("scope"),id_token:(0,i.Qq)("id_token"),session_state:(0,i.Qq)("session_state"),oauth_token_secret:(0,i.Qq)("oauth_token_secret"),oauth_token:(0,i.Qq)("oauth_token")},e=>({userIdIdx:(0,c.Pe)().on(e.userId),compositePk:(0,l.ie)({columns:[e.provider,e.providerAccountId]})})),N=(0,n.cJ)("Chat",{id:(0,i.Qq)("id").primaryKey(),createdAt:(0,d.vE)("createdAt").notNull(),title:(0,i.Qq)("title").notNull().default("New Chat"),userId:(0,i.Qq)("userId").notNull(),visibility:(0,p.yf)("visibility",{enum:["public","private"]}).notNull().default("private")}),g=(0,n.cJ)("Message",{id:(0,m.uR)("id").primaryKey().notNull().defaultRandom(),chatId:(0,i.Qq)("chatId").notNull().references(()=>N.id),role:(0,p.yf)("role").notNull(),content:(0,h.Pq)("content"),parts:(0,h.Pq)("parts"),attachments:(0,h.Pq)("attachments"),createdAt:(0,d.vE)("createdAt").notNull()}),A=(0,n.cJ)("Vote",{chatId:(0,i.Qq)("chatId").notNull().references(()=>N.id),messageId:(0,m.uR)("messageId").notNull().references(()=>g.id),isUpvoted:(0,f.zM)("isUpvoted").notNull()},e=>({pk:(0,l.ie)({columns:[e.chatId,e.messageId]})})),b=(0,n.cJ)("map",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,y.$C)()),name:(0,i.Qq)("name").notNull(),createdBy:(0,i.Qq)("userId").notNull(),createdAt:(0,d.vE)("createdAt").notNull().defaultNow(),updatedAt:(0,d.vE)("updatedAt").notNull().defaultNow(),isPublic:(0,f.zM)("isPublic").default(!1),layers:(0,h.Pq)("layers").notNull(),version:(0,u.nd)("version").notNull().default(1)}),Q=(0,n.cJ)("map_view",{id:(0,i.Qq)("id").notNull().$defaultFn(()=>(0,y.$C)()),mapId:(0,i.Qq)("mapId").notNull().references(()=>b.id,{onDelete:"cascade"}),userId:(0,i.Qq)("userId").notNull(),center:(0,h.Pq)("center").notNull(),zoom:(0,u.nd)("zoom").notNull(),basemap:(0,i.Qq)("basemap").notNull(),updatedAt:(0,d.vE)("updatedAt").notNull().defaultNow()},e=>({compoundKey:(0,l.ie)({columns:[e.mapId,e.userId]}),idIdx:(0,c.Pe)("map_view_id_idx").on(e.id)})),k=(0,n.cJ)("chat_map",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,y.$C)()),chatId:(0,i.Qq)("chatId").notNull().references(()=>N.id,{onDelete:"cascade"}),mapId:(0,i.Qq)("mapId").notNull().references(()=>b.id,{onDelete:"restrict"}),createdAt:(0,d.vE)("createdAt").notNull().defaultNow()}),x=(0,n.cJ)("map_access",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,y.$C)()),mapId:(0,i.Qq)("mapId").notNull().references(()=>b.id,{onDelete:"cascade"}),userId:(0,i.Qq)("userId").notNull(),accessType:(0,i.Qq)("accessType").notNull(),createdAt:(0,d.vE)("createdAt").notNull().defaultNow()}),E=(0,n.cJ)("map_session",{id:(0,i.Qq)("id").primaryKey().notNull().$defaultFn(()=>(0,y.$C)()),mapId:(0,i.Qq)("mapId").notNull().references(()=>b.id,{onDelete:"cascade"}),userId:(0,i.Qq)("userId").notNull(),isActive:(0,f.zM)("isActive").default(!0),lastActiveAt:(0,d.vE)("lastActiveAt").notNull().defaultNow(),syncView:(0,f.zM)("syncView").default(!1),followingUserId:(0,i.Qq)("followingUserId")}),R=(0,o.K1)(b,({many:e})=>({views:e(Q),access:e(x),sessions:e(E),chats:e(k)})),_=(0,o.K1)(N,({many:e})=>({maps:e(k)})),U=(0,o.K1)(q,({one:e})=>({user:e(w,{references:[w.id],fields:[q.userId]})})),F=(0,o.K1)(v,({one:e})=>({user:e(w,{references:[w.id],fields:[v.userId]})})),P=(0,o.K1)(w,({many:e})=>({accounts:e(v),sessions:e(q)}));var $=a(60988),T=a(1770),C=a(7160),D=a(42449),j=a(8738);let O=new D.Pool({connectionString:process.env.POSTGRES_URL}),S=(0,j.f)(O,{schema:r,logger:!0});async function z({id:e,userId:t,title:a}){try{return await S.insert(N).values({id:e,createdAt:new Date,userId:t,title:a})}catch(e){throw console.error("Failed to save chat in database"),e}}async function J({messages:e}){try{return await S.insert(g).values(e)}catch(e){throw console.error("Failed to save messages in database",e),e}}async function K({id:e}){try{return await S.delete(A).where((0,$.eq)(A.chatId,e)),await S.delete(g).where((0,$.eq)(g.chatId,e)),await S.delete(N).where((0,$.eq)(N.id,e))}catch(e){throw console.error("Failed to delete chat by id from database"),e}}async function M({id:e}){try{return await S.select().from(N).where((0,$.eq)(N.userId,e)).orderBy((0,T.i)(N.createdAt))}catch(e){throw console.error("Failed to get chats by user from database"),e}}async function V({id:e}){try{let[t]=await S.select().from(N).where((0,$.eq)(N.id,e));return t}catch(e){throw console.error("Failed to get chat by id from database"),e}}async function B({id:e}){try{return await S.select().from(g).where((0,$.eq)(g.id,e))}catch(e){throw console.error("Failed to get message by id from database"),e}}async function L({id:e}){try{return await S.select().from(g).where((0,$.eq)(g.chatId,e)).orderBy((0,T.Y)(g.createdAt))}catch(e){throw console.error("Failed to get messages by chat id from database",e),e}}async function G({chatId:e,messageId:t,type:a}){try{let[r]=await S.select().from(A).where((0,$.Uo)((0,$.eq)(A.messageId,t)));if(r)return await S.update(A).set({isUpvoted:"up"===a}).where((0,$.Uo)((0,$.eq)(A.messageId,t),(0,$.eq)(A.chatId,e)));return await S.insert(A).values({chatId:e,messageId:t,isUpvoted:"up"===a})}catch(e){throw console.error("Failed to upvote message in database",e),e}}async function W({id:e}){try{return await S.select().from(A).where((0,$.eq)(A.chatId,e))}catch(e){throw console.error("Failed to get votes by chat id from database",e),e}}async function Y({chatId:e,timestamp:t}){try{return await S.delete(g).where((0,$.Uo)((0,$.eq)(g.chatId,e),(0,$.RO)(g.createdAt,t)))}catch(e){throw console.error("Failed to delete messages by id after timestamp from database"),e}}async function H({chatId:e,visibility:t}){try{return await S.update(N).set({visibility:t}).where((0,$.eq)(N.id,e))}catch(e){throw console.error("Failed to update chat visibility in database"),e}}async function X({userId:e}){try{return await S.select({id:b.id,name:b.name,createdAt:b.createdAt,updatedAt:b.updatedAt,layers:b.layers,isPublic:b.isPublic,activeUsers:(0,C.ll)`
        (
          SELECT COUNT(DISTINCT ${Q.userId})
          FROM ${Q}
          WHERE ${Q.mapId} = ${b.id}
          AND ${Q.updatedAt} > NOW() - INTERVAL '5 minutes'
        )
        `.as("activeUsers"),view:{center:Q.center,zoom:Q.zoom,basemap:Q.basemap}}).from(b).leftJoin(x,(0,$.Uo)((0,$.eq)(x.mapId,b.id),(0,$.eq)(x.userId,e))).leftJoin(Q,(0,$.Uo)((0,$.eq)(Q.mapId,b.id),(0,$.eq)(Q.userId,e))).where((0,$.or)((0,$.eq)(b.createdBy,e),(0,$.eq)(x.userId,e))).orderBy((0,T.i)(b.updatedAt))}catch(e){throw e instanceof Error&&console.error("Failed to get maps by user from database:",{message:e.message,stack:e.stack}),e}}async function Z({id:e,userId:t}){try{let[a]=await S.select({id:b.id,name:b.name,createdAt:b.createdAt,updatedAt:b.updatedAt,layers:b.layers,isPublic:b.isPublic,createdBy:b.createdBy,activeUsers:(0,C.ll)`
        (
          SELECT COUNT(DISTINCT ${Q.userId})
          FROM ${Q}
          WHERE ${Q.mapId} = ${b.id}
          AND ${Q.updatedAt} > NOW() - INTERVAL '5 minutes'
        )
        `.as("activeUsers")}).from(b).where((0,$.eq)(b.id,e));if(!a)throw Error("Map not found");if(Array.isArray(a.layers)&&(a.layers=a.layers.map(e=>({...e,style:e.style?"string"==typeof e.style?JSON.parse(e.style):e.style:void 0}))),a.createdBy!==t&&!a.isPublic&&!await er({mapId:e,userId:t}))throw Error("Forbidden");let[r]=await S.select().from(Q).where((0,$.Uo)((0,$.eq)(Q.mapId,e),(0,$.eq)(Q.userId,t)));return{...a,view:r||null}}catch(e){throw console.error("Failed to get map by id from database"),e}}async function ee({id:e,name:t,layers:a,userId:r}){try{if((await S.select().from(b).where((0,$.eq)(b.id,e))).length>0)return await S.update(b).set({name:t,layers:a,updatedAt:new Date}).where((0,$.eq)(b.id,e));return await S.insert(b).values({id:e,name:t,layers:a,createdBy:r,createdAt:new Date,updatedAt:new Date})}catch(e){throw console.error("Failed to save map in database"),e}}async function et({mapId:e,userId:t,view:a}){try{return await S.insert(Q).values({mapId:e,userId:t,center:a.center??{lat:36.5,lng:127.5},zoom:a.zoom??7,basemap:a.basemap??"eMapBasic",updatedAt:new Date}).onConflictDoUpdate({target:[Q.mapId,Q.userId],set:{...a,updatedAt:new Date}})}catch(e){throw console.error("Failed to update map view state"),e}}async function ea({id:e,userId:t}){try{let[a]=await S.select().from(b).where((0,$.eq)(b.id,e));if(!a)throw Error("Map not found");if(a.createdBy!==t)throw Error("Forbidden");return await S.delete(b).where((0,$.eq)(b.id,e))}catch(e){throw console.error("Failed to delete map from database"),e}}async function er({mapId:e,userId:t}){try{return(await S.select().from(x).where((0,$.Uo)((0,$.eq)(x.mapId,e),(0,$.eq)(x.userId,t)))).length>0}catch(e){throw console.error("Failed to check map access"),e}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30779:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>y,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var r={};a.r(r),a.d(r,{GET:()=>l,POST:()=>u});var s=a(81160),o=a(18765),n=a(46332),i=a(47841),d=a(28538),c=a(86746);async function l(){let e=await (0,i.j2)();if(!e?.user)return Response.json("Unauthorized!",{status:401});try{let t=await (0,d.V7)({userId:e.user.id??""});return Response.json(t,{status:200})}catch(e){return Response.json("Failed to fetch maps",{status:500})}}async function u(e){let t=await (0,i.j2)();if(!t?.user)return Response.json("Unauthorized!",{status:401});if(!t.user.id)return Response.json("User ID is required",{status:401});try{let{name:a,map:r}=await e.json(),s=t.user.id,o=(0,c.$C)();await (0,d.Y0)({id:o,name:a,layers:r.mapInfo.layers,userId:s}),await (0,d.xZ)({mapId:o,userId:s,view:r.view});let n=(await (0,d.V7)({userId:s})).find(e=>e.id===o);if(!n)return new Response("Failed to create map",{status:500});return Response.json(n,{status:200})}catch(e){return Response.json("Failed to create map",{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/(map)/api/map/route",pathname:"/api/map",filename:"route",bundlePath:"app/(map)/api/map/route"},resolvedPagePath:"C:\\chatbot\\front-chat\\app\\(map)\\api\\map\\route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:f}=p;function y(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},37758:()=>{},42449:e=>{"use strict";e.exports=require("pg")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47841:(e,t,a)=>{"use strict";a.d(t,{j2:()=>u,Y9:()=>l});var r=a(393),s=a(95983);r.xz;let o="tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh",n="https://gsapi.geon.kr/smt";async function i(e,t){let a=new URLSearchParams({crtfckey:o,userId:e,password:t}),r=await fetch(`${n}/login/validation?${a.toString()}`,{method:"POST",headers:{"Content-Type":"application/json",crtfckey:o}}),s=await r.json();if(!r.ok)throw Error("로그인 검증에 실패했습니다.");return s}async function d(e){let t=new URLSearchParams({crtfckey:o,userId:e}),a=await fetch(`${n}/users/id?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",crtfckey:o}}),r=await a.json();if(!a.ok)throw Error("사용자 정보를 가져오는데 실패했습니다.");return r}let c=[(0,s.A)({credentials:{},async authorize({id:e,password:t}){try{let a=process.env.FRONTEND_LOGIN_USER_ID||"admin",s=process.env.FRONTEND_LOGIN_PASSWORD||"password1234";if(e===a&&t===s)return{id:a,name:"GeOn City",email:"@example.com",userId:a,userNm:"GeOn City",emailaddr:"@example.com",userSeCode:"14",userSeCodeNm:"관리자",userImage:null,insttCode:"GEON",insttNm:"GeOn",insttUrl:null,message:"로그인 성공"};if("geonuser"===e){let a=await i(e,t);if(!a.result.isValid)throw new r.xz(a.result.message);let s=await d(e);if(200!==s.code)return new r.xz(s.result.message);return{...s.result,id:s.result.userId,name:s.result.userNm||e,email:s.result.emailaddr||`${s.result.userNm}`}}throw new r.xz("admin 또는 geonuser 계정으로만 로그인할 수 있습니다.")}catch(e){throw console.error("Auth error:",e),e}}})];c.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"credentials"!==e.id);let{handlers:l,auth:u,signIn:p,signOut:m}=(0,r.Ay)({pages:{signIn:"/login",newUser:"/"},providers:[],callbacks:{authorized({auth:e,request:{nextUrl:t}}){let a=!!e?.user,r=t.pathname.startsWith("/geon-2d-map"),s=t.pathname.startsWith("/login");return"/"===t.pathname||a&&s?Response.redirect(new URL("/geon-2d-map",t)):!!s||!r||a}},providers:c,session:{strategy:"jwt",maxAge:1800},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id),e),session:async({session:e,token:t})=>(e.user&&(e.user.id=t.id),e)}})},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77598:e=>{"use strict";e.exports=require("node:crypto")},77926:()=>{},81160:(e,t,a)=>{"use strict";e.exports=a(44870)}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[332,481,741,115,746,861],()=>a(30779));module.exports=r})();