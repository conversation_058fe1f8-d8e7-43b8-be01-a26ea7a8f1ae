"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ChevronDown } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"

interface ServerDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  searchKey?: string
  searchPlaceholder?: string
  // 서버 사이드 페이지네이션 props
  pageIndex: number
  pageSize: number
  totalCount: number
  onPageChange: (pageIndex: number) => void
  onPageSizeChange: (pageSize: number) => void
  isLoading?: boolean
}

export function ServerDataTable<TData, TValue>({
  columns,
  data,
  searchKey,
  searchPlaceholder = "검색...",
  pageIndex,
  pageSize,
  totalCount,
  onPageChange,
  onPageSizeChange,
  isLoading = false,
}: ServerDataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    manualPagination: true, // 서버 사이드 페이지네이션 활성화
    pageCount: Math.ceil(totalCount / pageSize), // 전체 페이지 수 계산
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      pagination: {
        pageIndex: pageIndex - 1, // 0-based index로 변환
        pageSize,
      },
    },
  })

  // 페이지 변경 핸들러
  const handlePageChange = (newPageIndex: number) => {
    onPageChange(newPageIndex + 1) // 1-based index로 변환
  }

  // 페이지 크기 변경 핸들러
  const handlePageSizeChange = (newPageSize: string) => {
    onPageSizeChange(Number(newPageSize))
  }

  // 페이지네이션 정보 계산
  const totalPages = Math.ceil(totalCount / pageSize)
  const startItem = (pageIndex - 1) * pageSize + 1
  const endItem = Math.min(pageIndex * pageSize, totalCount)

  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* 검색 및 컬럼 선택 - 고정 영역 */}
      <div className="flex items-center py-3 flex-shrink-0 border-b bg-background">
        {searchKey && (
          <Input
            placeholder={searchPlaceholder}
            value={(table.getColumn(searchKey)?.getFilterValue() as string) ?? ""}
            onChange={(event) =>
              table.getColumn(searchKey)?.setFilterValue(event.target.value)
            }
            className="max-w-sm h-8"
          />
        )}

        {/* 페이지 크기 선택 */}
        <Select value={pageSize.toString()} onValueChange={handlePageSizeChange}>
          <SelectTrigger className="ml-2 h-8 w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="25">25개씩 보기</SelectItem>
            <SelectItem value="50">50개씩 보기</SelectItem>
            <SelectItem value="100">100개씩 보기</SelectItem>
            <SelectItem value="200">200개씩 보기</SelectItem>
          </SelectContent>
        </Select>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="ml-auto h-8">
              컬럼 <ChevronDown className="ml-2 h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                )
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* 테이블 컨테이너 - 고정 높이 + 강제 스크롤 */}
      <div className="flex-1 min-h-0 border rounded-md overflow-hidden bg-background">
        <ScrollArea className="h-full w-full">
          <div style={{ minHeight: '400px', maxHeight: '500px', height: '100%' }}>
            <Table className="w-full">
              {/* 고정 헤더 */}
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id} className="hover:bg-transparent border-b">
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        className="sticky top-0 z-10 bg-muted border-r last:border-r-0 min-w-[120px] p-2 h-auto"
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>

              {/* 스크롤 가능한 바디 */}
              <TableBody>
                {isLoading ? (
                  // 스켈레톤 로딩 표시
                  Array.from({ length: pageSize > 20 ? 20 : pageSize }).map((_, index) => (
                    <TableRow key={`skeleton-${index}`} className="border-b">
                      {columns.map((_, colIndex) => {
                        // 다양한 너비의 스켈레톤을 위한 랜덤 너비 클래스
                        const widthClasses = ['w-full', 'w-3/4', 'w-1/2', 'w-2/3', 'w-5/6'];
                        const randomWidth = widthClasses[(index + colIndex) % widthClasses.length];

                        return (
                          <TableCell
                            key={`skeleton-cell-${index}-${colIndex}`}
                            className="border-r last:border-r-0 min-w-[120px] p-2"
                          >
                            <Skeleton className={`h-4 ${randomWidth}`} />
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  ))
                ) : table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow key={row.id} className="hover:bg-muted/30 border-b">
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          key={cell.id}
                          className="border-r last:border-r-0 min-w-[120px] p-2 text-sm whitespace-nowrap"
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center text-muted-foreground"
                    >
                      데이터가 없습니다.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          <ScrollBar orientation="vertical" />
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>

      {/* 서버 사이드 페이지네이션 - 고정 영역 */}
      <div className="flex items-center justify-between space-x-2 py-3 flex-shrink-0 border-t bg-background">
        <div className="flex-1 text-sm text-muted-foreground">
          총 {totalCount.toLocaleString()}개 항목 중 {startItem.toLocaleString()}-{endItem.toLocaleString()}개 표시
        </div>
        <div className="flex items-center space-x-2">
          <div className="text-sm text-muted-foreground">
            페이지 {pageIndex} / {totalPages}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pageIndex - 2)} // 0-based로 변환하여 전달
            disabled={pageIndex <= 1 || isLoading}
          >
            이전
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pageIndex)} // 0-based로 변환하여 전달
            disabled={pageIndex >= totalPages || isLoading}
          >
            다음
          </Button>
        </div>
      </div>
    </div>
  )
}
