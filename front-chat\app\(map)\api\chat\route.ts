import {
  convertToCoreMessages,
  Message,
  Output,
  smoothStream,
  streamObject,
  appendResponseMessages,
  InvalidToolArgumentsError,
  NoSuchToolError,
  ToolExecutionError,
  generateObject,
} from "ai";
import { customModel, openaiModel } from "@/lib/ai";
import { auth } from "@/app/(auth)/auth";
import {
  deleteChatById,
  getChatById,
  saveChat,
  saveMessages,
} from "@/lib/db/queries";
import {
  models,
  getModelById,
  getModelProvider,
  supportsReasoning,
} from "@/lib/ai/models";
import {
  generateUUID,
  getMostRecentUserMessage,
  prunedMessages,
} from "@/lib/utils";
import { generateTitleFromUserMessage } from "../../actions";
import {
  agentConfigs,
  intentAnalyzerAgentConfig,
  IntentResponseSchema,
  AgentName,
  IntentResponseType,
  IntentEnum,
} from "./agents";
import { createDataStreamResponse, streamText } from "ai";
import { LayerProps } from "@geon-map/odf";
import { openai } from "@ai-sdk/openai";
import {
  getApiConfig,
  addAuthToParams,
  getApiHeaders,
  getApiUserId,
} from "@/lib/api-config";
import { evaluateAgentResult } from "./evaluate";
import { executeAgentWithRetry } from "./execute-with-retry";
import { handleAgentWithRetry } from "./retry-handler";

// 지도 상태 정보 생성 유틸리티
const createMapStateInfo = (layers: LayerProps[]) => {
  if (layers.length === 0) {
    return `지도 상태: 빈 지도 (레이어 없음)
- 모든 레이어 요청은 검색 → 추가 과정이 필요합니다.`;
  }

  const layerSummary = layers.map(layer => ({
    name: (layer as any).name || (layer as any).lyrNm || '이름없음',
    id: layer.id,
    type: (layer as any).geometryType || (layer as any).type || '타입불명',
    visible: layer.visible
  }));

  const visibleLayers = layerSummary.filter(l => l.visible);
  const hiddenLayers = layerSummary.filter(l => !l.visible);

  return `지도 상태: ${layers.length}개 레이어 존재

**현재 표시중인 레이어 (${visibleLayers.length}개):**
${visibleLayers.map(l => `- ${l.name} (${l.type}, ID: ${l.id})`).join('\n')}

**숨겨진 레이어 (${hiddenLayers.length}개):**
${hiddenLayers.map(l => `- ${l.name} (${l.type}, ID: ${l.id})`).join('\n')}

**레이어 타입 분포:**
- 점(Point): ${layerSummary.filter(l => l.type === 'point').length}개
- 선(Line): ${layerSummary.filter(l => l.type === 'line').length}개
- 면(Polygon): ${layerSummary.filter(l => l.type === 'polygon').length}개`;
};

// 의도분석 메시지 생성 함수
const createIntentAnalyzerMessages = (
  userMessage: any,
  lastAssistantMsg: any,
  layers: LayerProps[],
  intentAnalyzerAgentConfig: any
) => {
  const messages: Array<{role: "system" | "assistant" | "user"; content: string}> = [
    { role: "system", content: intentAnalyzerAgentConfig.system }
  ];

  // 지도 상태 정보 추가
  const mapStateInfo = createMapStateInfo(layers);
  messages.push({
    role: "system",
    content: `현재 지도 상태 정보:
${mapStateInfo}

이 정보를 바탕으로 사용자 요청을 분석하고 최적의 작업 계획을 수립하세요.`
  });

  // 이전 대화 맥락
  if (lastAssistantMsg) {
    const assistantContent = typeof lastAssistantMsg.content === "string"
      ? lastAssistantMsg.content
      : JSON.stringify(lastAssistantMsg.content);
    messages.push({
      role: "assistant",
      content: assistantContent,
    });
  }

  // 사용자 메시지
  const userContentStr = typeof userMessage.content === "string"
    ? userMessage.content
    : JSON.stringify(userMessage.content);
  messages.push({ role: "user", content: userContentStr });

  return messages;
};

// 실제 사용 가능한 레이어 목록을 조회하는 함수
async function getAvailableLayersContext(): Promise<string> {
  try {
    const config = getApiConfig();

    const params = new URLSearchParams({
      userId: getApiUserId(config),
      holdDataSeCode: "0",
      pageIndex: "1",
      pageSize: "10", // 상위 10개만 조회
    });

    // 인증 정보 추가
    addAuthToParams(params, config);

    const response = await fetch(
      `${config.baseUrl}/smt/layer/info/list?${params.toString()}`,
      {
        method: "GET",
        headers: getApiHeaders(config),
      }
    );

    if (!response.ok) {
      console.warn("Failed to fetch available layers for context");
      return "사용 가능한 레이어 정보를 조회할 수 없습니다.";
    }

    const data = await response.json();

    if (!data || !data.result || !data.result.list) {
      return "사용 가능한 레이어 정보를 조회할 수 없습니다.";
    }

    const layers = data.result.list.slice(0, 5); // 상위 5개만 사용
    const layerInfo = layers
      .map((layer: any) => {
        const typeMap: { [key: string]: string } = {
          "1": "점",
          "2": "선",
          "3": "면",
        };
        const typeName = typeMap[layer.lyrTySeCode] || "기타";
        return `${layer.lyrNm}(${typeName})`;
      })
      .join(", ");

    return `실제 사용 가능한 레이어 예시: ${layerInfo}`;
  } catch (error) {
    console.warn("Error fetching available layers for context:", error);
    return "사용 가능한 레이어 정보를 조회할 수 없습니다.";
  }
}

export const maxDuration = 100;

export async function POST(req: Request) {
  const {
    id,
    messages,
    layers,
    modelId,
    enable_thinking,
    enable_smart_navigation,
  }: {
    id: string;
    messages: Array<Message>;
    layers: LayerProps[];
    modelId?: string;
    enable_thinking: boolean;
    enable_smart_navigation: boolean;
  } = await req.json();

  const session = await auth();

  if (!session || !session.user || !session.user.id) {
    return new Response("Unauthorized", { status: 401 });
  }

  // 모델 선택 및 초기화
  let model = customModel("Qwen/Qwen3-4B");
  let selectedModel = getModelById(modelId || "Qwen3-4B");
  let modelSupportsReasoning = true; // 기본값
  let isNonGeonProvider = false;

  if (selectedModel) {
    const provider = getModelProvider(selectedModel.id);
    modelSupportsReasoning = supportsReasoning(selectedModel.id);

    switch (provider) {
      case "openai":
        model = openaiModel(selectedModel.apiIdentifier);
        isNonGeonProvider = true;
        break;
      case "geon":
        model = customModel(selectedModel.apiIdentifier);
        break;
      case "dify":
        // difyModel 함수가 있다면 사용
        model = customModel(selectedModel.apiIdentifier);
        break;
      default:
        model = customModel(selectedModel.apiIdentifier);
    }
  }

  const coreMessages = convertToCoreMessages(prunedMessages(messages));
  const userMessage = getMostRecentUserMessage(coreMessages);

  if (!userMessage) {
    return new Response("No user message found", { status: 400 });
  }

  const chat = await getChatById({ id });

  if (!chat) {
    const title = await generateTitleFromUserMessage({ message: userMessage });
    await saveChat({ id, userId: session.user.id, title });
  }

  const userMessageId = generateUUID();

  // 사용자 메시지를 AI SDK 패턴에 맞게 저장
  const userMessageToSave = {
    id: userMessageId,
    chatId: id,
    role: userMessage.role,
    content: "", // AI SDK 권장: content는 빈 문자열
    parts: Array.isArray(userMessage.content)
      ? userMessage.content
      : [{ type: "text", text: userMessage.content }], // content를 parts로 변환
    attachments: [],
    createdAt: new Date(),
  };

  await saveMessages({
    messages: [userMessageToSave],
  });

  // createDataStreamResponse를 사용한 Sequential Processing과 Routing
  return createDataStreamResponse({
    execute: async (dataStream) => {
      try {
        // Step 1: Intent Analysis (의도 분석) - 내부 프로세스는 숨김
        const lastAssistantMsg = [...coreMessages]
          .reverse()
          .find((msg) => msg.role === "assistant");

        // 개선된 의도분석 메시지 생성 (지도 상태 정보 포함)
        const intentAnalyzerMessages = createIntentAnalyzerMessages(
          userMessage,
          lastAssistantMsg,
          layers,
          intentAnalyzerAgentConfig
        );

        const { experimental_partialOutputStream } = streamText({
          model,
          temperature: 0,
          messages: intentAnalyzerMessages,
          experimental_output: Output.object({
            schema: IntentResponseSchema,
          }),
          onChunk({ chunk }) {
            if (chunk.type === "reasoning") {
              dataStream.writeMessageAnnotation(chunk);
            }
          },
          // experimental_transform: smoothStream({
          //   delayInMs: 10
          // }),
          ...(isNonGeonProvider
            ? {}
            : {
                providerOptions: {
                  geon: {
                    metadata: {
                      chat_template_kwargs: {
                        enable_thinking:
                          enable_thinking && modelSupportsReasoning,
                      },
                    },
                  },
                },
              }),
        });

        // experimental_partialOutputStream에서 intent 추출
        let intent: any = {
          intent: "GENERAL_CONVERSATION",
          message: "사용자 요청을 처리하고 있습니다...",
        };

        // 사용자 메시지 처리 과정을 보여주면서 동기식으로 출력이 완성될 때까지 기다림
        for await (const partial of experimental_partialOutputStream) {
          if (partial.intent && partial.message) {
            intent = {
              intent: partial.intent,
              message: partial.userMessage,
            };
          }
        }

        // 의도분석 결과와 작업 계획을 사용자에게 표시
        dataStream.writeMessageAnnotation({
          type: "intent_analyzed",
          intent: intent.intent,
          message: intent.message,
        });

        // Step 2: Agent Routing (에이전트 라우팅)
        const agentName = mapIntentToAgent(intent.intent);

        if (!agentName) {
          // General conversation이나 처리할 수 없는 의도인 경우
          const result = streamText({
            model,
            messages: [
              {
                role: "system",
                content:
                  "당신은 친근하고 도움이 되는 지도 AI 어시스턴트입니다. 사용자와 자연스럽게 대화하세요.",
              },
              ...coreMessages,
            ],
            temperature: 0,
            maxSteps: 5,
          });

          result.mergeIntoDataStream(dataStream);
          return;
        }

        // 에이전트 시작 알림 (사용자 친화적 메시지 사용)
        dataStream.writeMessageAnnotation({
          type: "agent_start",
          agent: agentName,
          message: intent.userMessage || `${agentName} 에이전트가 작업을 시작합니다`,
        });

        // 개선된 stateMessage 생성
        const createExecutionMessage = (intent: any, layers: LayerProps[]) => {
          let content = `Current map state:
${JSON.stringify(layers, null, 2)}

의도분석 결과: ${intent.message}`;

          // 조건부 추가 정보
          if (intent.targetLayer) {
            content += `\n\n대상 레이어: ${intent.targetLayer}`;
            content += `\n레이어 존재 여부: ${intent.layerExists ? '존재함' : '존재하지 않음'}`;
          }

          if (intent.requiredActions && intent.requiredActions.length > 0) {
            content += `\n\n필수 작업 단계:`;
            intent.requiredActions.forEach((action: string, index: number) => {
              content += `\n${index + 1}. ${action}`;
            });
          }

          if (intent.styleRequirements) {
            content += `\n\n스타일 요구사항: ${JSON.stringify(intent.styleRequirements)}`;
          }

          content += `\n\n사용자 정보: {\n    userId = '${session.user?.id}'\n    insttCode = 'geonpaas'\n    userSeCode = '14'\n  }`;

          content += `\n\n위 계획에 따라 단계별로 작업을 수행하세요.`;

          return {
            role: "system" as const,
            content
          };
        };

        const stateMessage = createExecutionMessage(intent, layers);

        console.log(
          `[ROUTING] Intent: ${intent.intent} → Agent: ${agentName || "null"}`,
          `${JSON.stringify(stateMessage)}`
        );

        // 새로운 방식
        await handleAgentWithRetry({
          model,
          agentName,
          messages: coreMessages,
          stateMessage,
          intentMessage: intent.message,
          dataStream,
          session,
          enable_smart_navigation,
          isNonGeonProvider,
          chatId: id, // AI 응답을 DB에 저장하기 위한 chatId 전달
        });

        // 작업 완료 시 메시지 저장
      } catch (error) {
        console.error("Agent processing error:", error);
      }
    },
    onError: (error) => {
      console.error("Agent processing error:", error);
      if (NoSuchToolError.isInstance(error)) {
        return "The model tried to call a unknown tool.";
      } else if (InvalidToolArgumentsError.isInstance(error)) {
        return "The model called a tool with invalid arguments.";
      } else if (ToolExecutionError.isInstance(error)) {
        return "An error occurred during tool execution.";
      } else {
        return "An unknown error occurred.";
      }
    },
  });
}

// 의도를 에이전트로 매핑하는 헬퍼 함수
function mapIntentToAgent(
  intent: (typeof IntentEnum)[number]
): AgentName | null {
  const intentToAgentMap: Record<
    (typeof IntentEnum)[number],
    AgentName | null
  > = {
    LAYER_ADD: "layer_agent",
    LAYER_REMOVE: "layer_agent",
    LAYER_STYLE: "layer_agent", // 스타일 변경을 layer_agent가 처리
    LAYER_LIST: "layer_agent", // 레이어 목록 조회를 layer_agent가 처리

    LAYER_FILTER: "layer_agent", // 레이어 필터링 요청도 layer_agent가 처리

    NAVIGATION: "navigation",
    MAP_CONTROL: "map_control", // 지도 컨트롤을 map_control 에이전트가 처리
    BASEMAP_CHANGE: "map_control", // 배경지도 변경 요청을 map_control 에이전트가 처리

    DENSITY_ANALYSIS: "density_analysis", // 밀도 분석 요청을 density_analysis 에이전트가 처리

    GENERAL_CONVERSATION: "default_agent", // 일반 대화는 default_agent로 처리
    UNSURE: "default_agent", // 불확실한 경우도 default_agent로 처리

    UNSUPPORTED_FEATURE: "unsupported_feature", // 지원되지 않는 기능 요청을 전용 에이전트가 처리
  };

  return intentToAgentMap[intent];
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get("id");

  if (!id) {
    return new Response("Not Found", { status: 404 });
  }

  const session = await auth();

  if (!session || !session.user) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    const chat = await getChatById({ id });

    if (chat.userId !== session.user.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    await deleteChatById({ id });

    return new Response("Chat deleted", { status: 200 });
  } catch (error) {
    return new Response("An error occurred while processing your request", {
      status: 500,
    });
  }
}

// deprecated
// const result = streamText({
//   model,
//   messages: [
//     {
//       role: "system",
//       content: agentConfig.system,
//     },
//     stateMessage,
//     ...coreMessages,
//   ],
//   temperature: 0,
//   tools: agentConfig.tools,
//   toolCallStreaming: true,
//   maxSteps: agentConfig.maxSteps || 10,
//   experimental_transform: smoothStream(),
//   experimental_continueSteps: true,
//   experimental_repairToolCall: async ({
//     toolCall,
//     tools,
//     parameterSchema,
//     error,
//   }) => {
//     console.log(
//       `[TOOL_REPAIR] Attempting to repair tool call: ${toolCall.toolName}`
//     );
//     console.log(`[TOOL_REPAIR] Error: ${error.message}`);
//     console.log(`[TOOL_REPAIR] Original args:`, toolCall.args);

//     if (NoSuchToolError.isInstance(error)) {
//       console.log(`[TOOL_REPAIR] Tool not found, cannot repair`);
//       return null; // 존재하지 않는 도구는 수정할 수 없음
//     }
//     const tool = tools[toolCall.toolName as keyof typeof tools];

//     const { object: repairedArgs } = await generateObject({
//       model: openai("gpt-4.1-nano", { structuredOutputs: true }),
//       schema: tool.parameters,
//       prompt: [
//         `The model tried to call the tool "${toolCall.toolName}"` +
//           ` with the following arguments:`,
//         JSON.stringify(toolCall.args),
//         `The tool accepts the following schema:`,
//         JSON.stringify(parameterSchema(toolCall)),
//         "Please fix the arguments.",
//       ].join("\n"),
//     });

//     return { ...toolCall, args: JSON.stringify(repairedArgs) };
//   },
//   ...(isNonGeonProvider
//     ? {}
//     : {
//         providerOptions: {
//           geon: {
//             metadata: {
//               chat_template_kwargs: { enable_thinking: false },
//             },
//           },
//         },
//       }),
//   onStepFinish: ({ toolCalls, text }) => {
//     // 평가를 위한 정보 수집
//     if (toolCalls && toolCalls.length > 0) {
//       allToolCalls.push(...toolCalls);
//     }
//     if (text) {
//       agentResponse += text;
//     }

//     // 도구 호출 정보를 어노테이션으로 전송
//     if (toolCalls && toolCalls.length > 0) {
//       toolCalls.forEach((toolCall) => {
//         dataStream.writeMessageAnnotation({
//           type: "tool_call",
//           toolName: toolCall.toolName,
//           args: toolCall.args,
//           enableSmartNavigation: enable_smart_navigation, // 스마트 네비게이션 상태 전달
//         });
//       });
//     }
//   },
//   onFinish: async ({ response, text, toolCalls }) => {
//     // 최종 정보 수집
//     if (text) {
//       agentResponse += text;
//     }
//     if (toolCalls && toolCalls.length > 0) {
//       allToolCalls.push(...toolCalls);
//     }
//     // 평가자 실행 테스트
//     if (intent?.message) {
//       try {
//         const evaluation = await evaluateAgentResult(
//           intent.message,
//           agentResponse,
//           allToolCalls
//         );
//       } catch (error) {
//         console.error("평가자 실행 실패:", error);
//       }
//     }

//     if (session.user?.id) {
//       try {
//         dataStream.writeMessageAnnotation({
//           type: "agent_completed",
//           agent: agentName,
//           message: "작업이 완료되었습니다.",
//         });

//         // AI SDK 공식 패턴: appendResponseMessages 사용
//         const updatedMessages = appendResponseMessages({
//           messages,
//           responseMessages: response.messages,
//         });

//         // 새로 추가된 메시지들만 저장 (기존 메시지는 이미 저장됨)
//         const newMessages = updatedMessages.slice(messages.length);

//         const messagesToSave = newMessages.map((message) => {
//           const messageId = generateUUID();

//           if (message.role === "assistant") {
//             dataStream.writeMessageAnnotation({
//               messageIdFromServer: messageId,
//             });
//           }

//           return {
//             id: messageId,
//             chatId: id,
//             role: message.role,
//             content: message.content, // 기존 호환성을 위해 유지
//             parts: message.parts || [], // AI SDK parts 배열
//             attachments: message.experimental_attachments || [], // AI SDK attachments
//             createdAt: new Date(),
//           };
//         });

//         if (messagesToSave.length > 0) {
//           await saveMessages({
//             messages: messagesToSave,
//           });
//         }
//       } catch (error) {
//         console.error("Failed to save chat:", error);
//       }
//     }
//   },
// });
// 최종 결과를 데이터 스트림에 병합
// result.mergeIntoDataStream(dataStream, {
//   sendReasoning: true,
// });
