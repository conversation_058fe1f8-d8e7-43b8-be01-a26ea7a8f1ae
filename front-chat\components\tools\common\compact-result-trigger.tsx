"use client";

import React, { useState, useId, useEffect } from "react";
import { cn } from "@/lib/utils";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import { ChevronDown, ChevronsUpDown, Expand } from "lucide-react";
import { componentStyles } from "@/lib/design-tokens";

interface CompactResultTriggerProps {
  /** 도구 아이콘 (기본 상태) */
  icon: React.ReactNode;
  /** 도구 이름 */
  title: string;
  /** 상태 ("call" | "result" | "partial-call") */
  state: "call" | "result" | "partial-call";
  /** 아코디언 내용 */
  children: React.ReactNode;
  /** 추가 CSS 클래스 */
  className?: string;
  /** 초기 열림 상태 */
  initialOpen?: boolean;
  /** 외부에서 제어하는 열림 상태 */
  open?: boolean;
  /** 도구 이름 옆에 표시할 추가 정보 */
  titleExtra?: React.ReactNode;
}

function getStateConfig(state: "call" | "result" | "partial-call") {
  switch (state) {
    case "call":
      return {
        color: "bg-orange-500", // 주황색 원
        label: "처리 중",
      };
    case "partial-call":
      return {
        color: "bg-red-500", // 빨간색 원
        label: "진행 중",
      };
    case "result":
      return {
        color: "bg-green-500", // 초록색 원
        label: "완료",
      };
    default:
      return {
        color: "bg-gray-500", // 회색 원
        label: "알 수 없음",
      };
  }
}

export function CompactResultTrigger({
  icon,
  title,
  state,
  children,
  className,
  initialOpen = false,
  open,
  titleExtra,
}: CompactResultTriggerProps) {
  const uniqueId = useId();
  const itemValue = `result-${uniqueId}`;
  const [isOpenInternal, setIsOpenInternal] = useState(initialOpen);
  const stateConfig = getStateConfig(state);

  useEffect(() => {
    if (open !== undefined) {
      setIsOpenInternal(open);
    }
  }, [open]);

  const accordionValue = isOpenInternal ? itemValue : "";

  return (
    <div className={cn(componentStyles.card.base, "bg-white/90 border-neutral-200/60", className)}>
      <Accordion
        type="single"
        collapsible
        value={accordionValue}
        onValueChange={(val) => setIsOpenInternal(val === itemValue)}
        className="w-full"
      >
        <AccordionItem value={itemValue} className="border-0">
          <AccordionTrigger className="flex items-center gap-3 p-2 pr-4 hover:no-underline [&>svg]:hidden group hover:bg-neutral-50/50 transition-colors min-h-[40px]">
            {/* 왼쪽: 도구 아이콘 + 도구명 */}
            <div className="flex items-center gap-1 flex-1 min-w-0">
              {/* 도구 아이콘 - hover 시 expand로 변경 */}
              <div className={cn(componentStyles.iconContainer.sm, "bg-neutral-100/80 text-neutral-600 group-hover:bg-neutral-200/80 transition-colors flex-shrink-0")}>
                <div className="group-hover:hidden">
                  {icon}
                </div>
                <div className="hidden group-hover:block">
                  <ChevronsUpDown className="w-4 h-4" />
                </div>
              </div>

              {/* 도구 이름 */}
              <span className="text-neutral-900 text-[13px] truncate">
                {title}
              </span>

              {/* 추가 정보 */}
              {titleExtra && (
                <div
                  className="flex-shrink-0 ml-1"
                  onClick={(e) => e.stopPropagation()}
                >
                  {titleExtra}
                </div>
              )}
            </div>

            {/* 오른쪽: 상태 컬러 원만 표시 */}
            <div className="flex items-center gap-2 flex-shrink-0">
              <div
                className={cn("w-2.5 h-2.5 rounded-full", stateConfig.color)}
                title={stateConfig.label}
              />
            </div>
          </AccordionTrigger>

          <AccordionContent className="px-3 pb-3">
            <div className="mt-2 rounded-lg bg-neutral-50/60 border border-neutral-200/50 p-3 text-sm">
              {children}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
