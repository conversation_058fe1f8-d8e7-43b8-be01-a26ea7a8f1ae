const CHUNK_PUBLIC_PATH = "server/app/(map)/api/history/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/1e20b_next_f6e46399._.js");
runtime.loadChunk("server/chunks/9c5b9_@auth_core_62d763ba._.js");
runtime.loadChunk("server/chunks/96a70_jose_dist_webapi_b110c212._.js");
runtime.loadChunk("server/chunks/c60ac_drizzle-orm_8d80448e._.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_896e3ca4._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__6e4d8bbf._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/(map)/api/history/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/(map)/api/history/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/(map)/api/history/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
