﻿맵픽이란
Mappick은 누구나 손쉽게 공간정보가 포함된 지도를 제작하여 업무에 적용 할 수 있는 SaaS(Software as a Service) GIS 서비스입니다. Mappick을 사용하면 인터넷을 통해 공개된 공공 데이터나, 본인이 가지고 있는 데이터를 이용하여 다양한 테마지도를 만들어 활용할 수 있고 서로 공유할 수 있습니다.

지도제작
데이터를 조회해서 추가: 온/오프 만으로 데이터 추가 및 조회 기본 제공데이터와 기관 데이터를 간편하게 추가
주소가 엑셀로 되어 있다면: 간단하게 엑셀 데이터를 지도에 표시(지오코딩) 지번주소, 도로명주소, 당연히 GIS파일형식(shp) 그대로 업로드
스타일: 적용 색상, 라벨, 필터 등을 사용자가 직접 수정
저장하기: 다음에도 쉽게 찾을 수 있도록 지도화면 이름, 설명을 간단하게 작성한 후 저장하면 끝!

테마지도
관련된 지도를 한꺼번에 순서, 배치가 자유로운 지도 나만의 테마지도 만들기: 스토리, 모바일 등 다양한 테마로 사용자만의 지도 구성
필요한 지도기능을 선택만으로 생성: 각종 지도 관련 위젯도구 제공

공유하기
공유 단위 설정: 지도 전체를 공유할지 레이어(데이터 단위)만 공유할지 선택
공유 대상 선택: 누구에게 공유할지 주소록에서 선택 / 전체(기관전체), 그룹, 특정 개인 옵션
공유는 조심, 또 조심: 공유한 설정이 맞는지 최종 검토하고, 최종 확인 버튼 클릭!

이외에도 맵픽은 다음의 기능을 제공합니다. 실질적로 Mappick은 지도 작성 및 정보 공유를 손쉽게 할 수 있는 SaaS(Software as a Service) GIS 서비스입니다. 이 플랫폼을 이용하면 누구나 간편하게 지도를 만들고 비즈니스나 개인 업무에 활용할 수 있습니다. Mappick은 다음과 같은 핵심 기능을 제공합니다.
지금 사용자님이 주소 데이터 엑셀파일이 있다면 그 파일을 맵핍에 업로드하여 지도에 해당 위치들을 표시할 수 있습니다. 
관리하고 있는 매장이나 공장의 위치가 주소로만 기록되어 있는 데이터에서 어느 매장끼리 가까운지, 또는 멀리 떨어져 있는지가 궁금하시다면 맵픽에 업로드해보세요. 지도 기반의 한 눈에 파악이 가능하도록 변환해 드립니다.
커피샵 창업을 원하시나요? 내 관심 지역 주변에 다른 카페가 어떻게 분포되어 있는지 맵픽에서 확인해보세요. 카페 레이어를 켜시면 어디에 새로운 카페를 창업할 수 있는지 한 눈에 파악됩니다.
이외에도 맵픽을 이용하여 지도상에서 다양한 활동이 가능하오니 로그인하셔서 나의 데이터를 관리하고 분석해보세요.

--------------------------------------
이 플랫폼을 이용하면 누구나 간편하게 지도를 만들고 비즈니스나 개인 업무에 활용할 수 있습니다. Mappick은 다음과 같은 핵심 기능을 제공합니다.

1. 지금 사용자님이 주소 데이터 엑셀파일이 있다면 그 파일을 맵핍에 업로드하여 지도에 해당 위치들을 표시할 수 있습니다. 

2. 관리하고 있는 매장이나 공장의 위치가 주소로만 기록되어 있는 데이터가 있으시면 업로드하여 위치를 확인해보세요
지도에 표시하면 어느 매장끼리 가까운지, 또는 멀리 떨어져 있는지가 알 수 있습니다. 

3. 커피샵 창업을 원하시나요? 내 관심 지역 주변에 다른 카페가 어떻게 분포되어 있는지 맵픽에서 확인해보세요. 카페 레이어를 켜시면 어디에 새로운 카페를 창업할 수 있는지 한 눈에 파악됩니다.

4. 현재 보유하고 있는 주소 혹은 좌표를 가지고 있는 엑셀데이터가 있으시면 맵픽에 업로드해보세요. 지도 기반에서 사용자님의 데이터를 한 눈에 파악이 가능하도록 변환해 드립니다.

이외에도 핫스팟 분석 등의 공간분석 기능 이용하여 지도상에서 다양한 활동이 가능하오니 로그인하셔서 나의 데이터를 관리하고 분석해보세요.

더 필요한 사항이 있으시면 알려주세요.

----------------------------------------
메인메뉴
메뉴는 챗봇의 메뉴와 맵픽의 메뉴로 구성되어 있습니다. 챗봇이 메뉴는 사용자의 이해를 돕기 위하여 자주 질문하는 내용으로 구성되어 있습니다. 맵픽의 메뉴는 맵픽소개, 지도, 맵갤러리, 공지사항으로 구성되어 있습니다.

1. 지도 빌더
지도빌더에서 제공되는 메인 메뉴 기능으로 새지도생성/지도검색/저장/기본정보 메뉴로 구성되어 있다.

1.1.1. 새 지도 생성
 사용자가 새로운 지도를 생성할 수 있다. 

1.1.2. 지도 검색
 사용자가 저장한 지도 목록을 볼 수 있다.

1.1.3. 지도 저장
 현재 지도를 저장한다.

1.1.4. 다른 이름으로 저장
 현재 지도를 다른이름으로 저장한다.

1.1.5. 지도 TOC
 사용자가 TOC에 있는 레이어 목록을 관리하고 조작할 수 있는 기능을 제공한다.

1.2. 지도 기본기능
지도에서 제공되는 지도 기본기능으로 주소검색, 지도제어 도구 영역으로 구성되어 있다.

1.2.2. 주소검색
 주소검색은 통합검색, 도로명, 지번, PNU, 경위도 등으로 원하는 위치를 검색하고 이동하는 기능을 제공한다.

1.2.3. 지도제어 도구
 지도에서 사용 가능한 다양한 기능을 제공한다.

1.3. 레이어 기능
지도에서 제공되는 레이어 기능으로 업로드, TOC, 스타일설정 등으로 구성되어 있다.
1.3.2. SHP 파일 업로드
 사용자가 보유하고 있는 공간정보 파일을 지도에 업로드하여 지도에서 활용 할 수 있다.

1.3.3. 지오코딩 파일 업로드
 사용자가 보유하고 있는 Excel 파일 및 CSV 파일을 지도에 업로드하여 지도에서 활용 할 수 있다.

1.3.4. 지오코딩 결과 알림
 알림영역에서 지오코딩 작업 내역을 확인할 수 있다. 

1.3.5. 지오코딩 결과 관리
 지오코딩이 완료된 파일을 매핑시켜 레이어로 발행할 수 있다.

1.3.6. 웹 레이어 등록
 OGC 표준의 WMS/WFS/WMTS 레이어 주소를 지도에 등록하여 지도에서 활용 할 수 있다. 

1.3.7. DXF 파일 업로드
 사용자가 보유하고 있는 DXF 파일을 지도에 업로드하여 지도에서 활용 할 수 있다.

1.3.8. 레이어 검색 및 레이어 추가
 사용자가 국가공간정보 레이어, 사용자데이터 레이어, 공통데이터 레이어, 행정데이터 레이 어 등에서 레이어를 검색하여 현재 지도 내의 TOC에 추가할 수 있다. 

1.3.9. 레이어 그룹 관리
 레이어 그룹을 추가하여 레이어를 그룹별로 분류하여 레이어를 관리한다.

1.3.10. 레이어 정보
 레이어의 정보를 확인할 수 있다.
 
1.3.11. 스타일 설정
 사용자가 선택한 레이어에 스타일을 적용하여 표출한다.

1.3.12. 팝업 구성
 사용자가 선택한 레이어에 팝업 구성을 설정한다.
 
1.3.13. 속성 설정
 사용자가 선택한 레이어의 속성정보를 설정한다.
 
1.3.14. 속성 테이블
 사용자가 레이어의 속성을 조회 및 편집, 활용한다.

1.3.15. 차트 설정
 사용자가 레이어 속성정보로 차트를 생성한다.
 
1.3.16. 속성 필터
 사용자가 선택한 레이어에 필터를 속성테이블에 적용하여 표출한다.
 
1.3.17. 공간 검색
 사용자가 레이어 속성을 선택한 공간영역으로 속성정보를 필터링 한다.

1.3.18. 객체 편집모드
 사용자가 선택한 레이어의 객체들을 편집할 수 있는 기능입니다.

1.4. 데이터 요약 분석
1.4.1. 공간조인 분석
 공간 및 속성 관계에 따라 속성 레이어의 속성 정보를 결합하는 공간조인 분석을 수행한다.
 
속성 조인 
- 분석 대상 레이어와 비교 대상 레이어의 속성을 기준으로 ROW 병합.
- 분섞 대상 레이어와 속성명이 일치하는 속성의 경우, ‘[속성명]_1’과 같은 형태로 생성됨.
- 분석 대상 레이어의 키 필드 : 분석 대상 레이어의 키 필드 중 병합의 기준이 될 필드
- 비교 대상 레이어의 키 필드 : 비교 대상 레이어의 키 필드 중 병합의 병합의 기준이 될 필드

공간 조인 
- 공간 관계 유형
① [동일함] 옵션은 분석 대상 레이어의 공간정보와 비교 대상 레이어의 공간정보가 정확히 일치해야 조인 조건이 만족된다.
② [교차함] 옵션은 분석 대상 레이어의 공간정보와 비교 대상 레이어의 공간 정보가 교차하면 조인 조건이 만족된다.
③ [완전히 포함함] 옵션은 비교 대상 레이어의 공간정보가 분석 대상 레이어의 공간정보에 완전히 포함되 있어야 조건이 만족된다.
④ [완전히 포함됨] 옵션은 분석 대상 레이어의 공간정보가 비교 대상 레이어의 공간정보에 완전히 포함되 있어야 조건이 만족된다.
⑤ [일정한 거리 내에 있음] 옵션은 비교 대상 레이어의 공간정보가 분석 대상 레이어의 공간정보.
- 공간 조인 유형
① [조인되는 대상만 포함] 옵션 선택 시 선택한 조인 칼럼 값이 일치하는 경우에만 결과 레이어에 나타난다.
② [모든 대상 피처 유지] 옵션 선택 시 선택한 조인 칼럼 값이 일치하지 않는 경우에도 결과 레이어에 나타난다. 

조인 작업 
① 일대일 조인 : 조인 조건을 만족시키는 ROW가 여러 개일 경우, 하나의 ROW만 조인.
② 일대다 조인 : 조인 조건을 만족시키는 ROW가 여러 개일 경우, 여러 개의 ROW를 조인. 합계/최소/최대/평균/표준편차

 
1.4.2. 주변집계 분석
 비교 대상 레이어에서 지정된 거리 내에 있는 분석 대상 레이어를 레이어 유형(점/선/면)에 따라 집계(총건수/총길이/총면적) 분석한다.

최근접 피처 요약 - 측정 방법 : 운전거리/직선거리
- 탐색 반경 : 기준 거리
- 탐색 반경 단위 : 기준거리 단위
- 경계 영역 반환 : 체크시, 결과 레이어에 비교 대상 레이어의 도형으로부터 기준되는 탐색 반경만큼 떨어진 영역 정보를 공간정보로 사용 

합계 필드 
- 분석 결과로 나타나는 요약 정보를 나타낼 필드명
- 입력하지 않으면 요약 정보가 결과 레이어에 나타나지 않음. 비교 대상 레이어의 도형과 일정 거리 내에 있는 도형의 개수/총 길이/ 총 면적 통계 추가 
- 분석대상 레이어의 숫자 필드로 통계를 내린다.
- 결과 레이어에 [통계구분(sum/min/max//avg/std)]_[분석대상 레이어의 필드명]과 같은 이름의 필드로 추가된다. 합계/최소/최대/평균/표준편차
 
1.4.3. 영역 내 집계 분석
 비교 대상 레이어의 폴리곤 경계 내에 있는 분석 대상 레이어를 레이어 유형(점/선/면)에 따라 집계(총건수/총길이/총면적) 분석한다.

합계 필드 
- 분석 결과로 나타나는 요약 정보를 나타낼 필드명
- 입력하지 않으면 요약 정보가 결과 레이어에 나타나지 않음. 비교 대상 레이어의 도형과 일정 거리 내에 있는 도형의 개수/총 길이/ 총 면적

통계 추가 
- 분석대상 레이어의 숫자 필드로 통계를 내린다.
- 결과 레이어에 [통계구분(sum/min/max//avg/std)]_[분석대상 레이어의 필드명]과 같은 이름의 필드로 추가된다. 합계/최소/최대/평균/표준편차

1.4.4. 공간분포 패턴 분석
 레이어의 공간 분포 패턴을 분석하여 요약 유형별로 중심을 추출한다.

요약 유형 - 중심 피처 : 전체 피처들 중 위치적으로 가운데 잇는 피처 추출(평균 중심에서 가장 가까운 피처 추출)
※ 분석 대상 레이어가 라인일 경우, 결과값이 표출되지 않는다.
- 평균 중심 : extent의 중심(x좌표의 평균, y좌표의 평균) [평균 계산 필드]
: 분석 대상 레이어에서 숫자필드. 선택시 해당 필드의 평균을 계산 하여 결과 레이어에 선택한 속성명으로 표출
- 중앙값 중심 : 각 포인트 간의 거리를 측정하여 중심위치를 추출 [중앙값 계산 필드]
: 분석 대상 레이어의 숫자 필드. 선택한 필드의 중앙값을 계산하여 결과 레이어에 선택한 속성명으로 표출
- 타원 : 포인트 간의 표준편차 값을 추출하여 타원을 추출(타원의 중심 점이 표준편차 평균값)
[타원체의 크기(1SD/2SD/3SD)]
: 결과 레이어에 표현할 타원의 크기 

가중치 기준 - 상대적 중요성에 따라 위치에 가중치를 적용하는데 사용하는 필드
- 가중치 기준을 선택하지 않으면 모든 도형의 속성에 동일한 가중치가 부여되어 중심점 추출
- 가중치 기준을 선택하면 선택한 필드의 값에 따라 가중치가 부여되어 중심점 추출(값이 클수록 가중치가 높음) 
 
1.5. 위치찾기 분석
1.5.1. 공간 조건 검색 분석
 비교 레이어를 기준으로 공간검색 유형의 조건을 만족하는 분석 레이어의 객체를 검색한다.

1.5.2. 공간 조건 추출 분석
 비교 레이어를 기준으로 공간검색 유형의 조건을 만족하는 분석 레이어의 객체를 검색 후 교차하는 영역으로 자르고 비교 레이어의 속성을 추가한다.

1.5.3. 중심찾기 분석
 분석 대상 레이어 객체에서 결과 위치 설정에 따라 중심점을 찾는다.

1.6. 공간패턴 분석
1.6.1. 밀도계산
 위치에 있는 포인트를 기반으로 군집 포인트의 개수가 많은 곳을 쉽게 식별할 수 있도록 시각화하는 분석을 수행한다.

1.6.2. 핫스팟
 데이터의 공간 패턴에 통계적으로 유의미한 군집이 있는지를 격자 그리드로 시각화하는 분석을 수행한다.

1.7. 근접도 분석
1.7.1. 버퍼 분석
 분석 대상 레이어 주위에 입력한 거리까지의 영역을 생성하는 작업을 수행한다.

1.8. 데이터 관리 분석
1.8.1. 경계디졸브
 경계 또는 중첩되는 영역을 병합하여 단일 영역으로 생성하는 작업을 수행한다.

1.8.2. 공간 분할 생성
 분석 대상 레이어를 입력한 분할타입으로 영역을 분할하여 생성하는 작업을 수행한다.
 
1.8.3. 레이어 병합
 동일한 유형의 레이어를 하나의 새로운 레이어로 병합하여 생성하는 작업을 수행한다.
 
1.8.4. 레이어 중첩(지우기) 분석
 동일한 유형의 두 레이어의 중첩되는 영역을 제외한 새 레이어로 속성을 포함하여 병합하는 작업을 수행한다.

1.8.5. 레이어 중첩(교차)
 동일한 유형의 두 레이어의 교차되는 영역을 새 레이어로 속성을 포함하여 병합하는 작업 을 수행한다.

1.8.6. 레이어 중첩(유니온)
 동일한 유형의 두 레이어를 새 레이어로 속성을 포함하여 교차되는 영역은 자르기 후 병합 하는 작업을 수행한다.

1.8.7. 클러스터링
 포인트 레이어의 위치 근접도를 분석하여 그룹으로 묶어 포인트수를 시각화하는 작업을 수 행한다.

1.8.8. 면적 계산
 대상 레이어 내 피처들의 실제 면적과 실제 둘레 길이를 측정하는 작업을 수행한다.

1.8.9. 길이 계산
 대상 레이어 내 피처들의 실제 길이를 측정하는 작업을 수행한다.

1.9. 좌표변환
1.9.1. 파일 좌표 변환
 Shape(ZIP), GeoJSON 파일 형태의 파일을 업로드하여 원하는 좌표계로 변환 후 다운로 드하는 기능을 수행한다.

1.9.2. 단일 좌표 변환
 사용자가 입력한 좌표계의 좌표를 원하는 좌표계로 변환하는 작업을 수행한다.

2. 앱빌더
2.1. 구성
앱(새앱, 검색), 상세설정, 미리보기, 저장 등의 메뉴로 구성되어 있다.

2.2. 표준/편집 테마
지도 빌더를 통해 제작된 지도를 표준, 편집 테마를 활용하여 앱의 레이아웃 및 스타일을 설정 하고 위젯들을 선택하여 새로운 앱을 생성할 수 있다.

2.2.1. 앱 만들기 화면
 테마 목록을 조회하고 테마를 선택하여 앱 편집 화면으로 이동한다.

2.2.2. 메인 설정
 메인 패널에서 사용할 사용자 지도를 선택한다. 

2.2.3. 레이아웃 설정
 타이틀 및 제어도구 위치를 설정한다.

2.2.4. 테마 설정
 헤더 및 푸터의 색상 및 타이틀 등 테마를 설정한다.

2.2.5. 위젯 설정
 웹앱의 위젯을 추가하고 저장한다.
 
헤더 영역 위젯 
앱 기본정보 
웹앱에 추가된 지도에 대한 상세정보를 확인할 수 있는 위젯
레이어 영역 위젯
 레이어 검색 레이어를 검색하고 추가할 수 있는 위젯
 레이어 스타일 레이어의 색상 및 라벨 등을 설정할 수 있는 위젯
 속성 레이어의 속성정보를 확인 할 수 있는 위젯
 속성필터 레이어의 속성으로 필터를 적용할 수 있는 위젯

상단 영역 위젯
 북마크 현재 지도의 위치를 저장할 수 있는 위젯
 오버뷰 오버뷰를 표출하는 위젯
 주소 검색 주소를 검색할 수 있는 위젯
 행정경계 표시 현재 지도의 행정경계 위치를 표시하고 다른 행정경계로 이동할 수 있는 위젯
 저장 지도를 PNG 또는 PDF로 저장할 수 있는 위젯
 인쇄 지도를 프린트 할 수 있는 위젯
 피쳐속성폼 위젯을 클릭한 후 지도상에 피쳐를 클릭하면 피쳐정보를 수정 할 수 있는 위젯

툴바 영역 위젯
홈 지도 홈으로 이동하는 위젯
 초기화 지도에 그려진 임시 그래픽을 초기화 하는 위젯
 전체화면 지도를 전체화면으로 확인 할 수 있는 위젯
 이동 이전/다음으로 이동할 수 있는 위젯
 분할지도 2분할/3분할/4분할로 지도를 분할할 수 있는 위젯
 그리기 점/폴리곤/사각형/선/텍스트/곡선/원/버퍼를 지도상에 그릴 수 있는 위젯
 측정 지도상에서 면적/거리/반경/지점을 측정할 수 있는 위젯
 회전/나침반 지도상에서 Alt + Shift + 마우스 왼쪽 클릭시 지도를 회전 할 수 있는 위젯
 배경지도 배경지도를 변경할 수 있는 위젯

하단 영역 위젯 

확대/축소 
지도를 확대/축소 할 수 있는 위젯

4.3. 제품 지원 사항
구분 설명

적용 표준 및 권고안 
• OGC공간정보 서비스 표준 준수·(WMS) 이미지 형태의 지도 제공 서비스 제공 시 해당 표준 준수·(WFS) 지리정보(Feature)에 대한 CRUD 서비스 제공 시 해당 표준 준수
• GeOn-Buider등 웹 UI을 가진 프로그램은 W3C의 웹표준을 준수하여 개발하여 크롬, 엣지등 웹표준을 준수하는 웹브라우저에서구동함

운영 지원 여부 
• 운영 지원은 별도 운영 지원 계약을 통하여 지원
• 유지보수는 유상 유지보수 계약(통산 1년 단위)를 통하여 제공하며 유지보수 제공 및 유지 보수 내용 
• 제공되는 유지보수 내용은 아래 내용을 포함함·프로그램오류에 대한 SW 오류 수정·프로그램의 예측되는 오류를 선점 처리를 위한 예방 유지보수·HW및 SW환경 변화에 따른 SW 적응
 
6. 시스템 백업 및 복원
서비스 내의 안정적 제공을 위해 DB는 1일 1회 자동 백업을 합니다.
백업된 DB는 시스템 상의 중대 결함 발생 시 복구하는 데 사용됩니다.
백업 대상은 백업 당시 DB에 포함된 모든 내용이 백업 됩니다.
DB 백업 원칙은 아래와 같습니다.
- 백업 시스템에 의해 주기적 백업 업무의 자동화
- 데이터량 변화에 따른 백업 주기의 조정 및 관리

7. 로그 관리
시스템의 운영중에 발생하는 로그를 확인하는 방법을 설명합니다.
로그 확인 절차
① WAS 터미널로 SSH 접속 연결한다.
② 아래 명령어를 입력하여 로그를 확인한다.
③ 아래와 같이 로그를 확인 할 수 있다.
 
 
8. 용어 및 약어
용어 설명

API (application programming interface) 
ㅇ API는 응용 프로그램에서 사용할 수 있도록, 운영 체제나 프로그래밍 언어가 제공하는 기능을 제어할 수 있게 만든 인터페이스를 뜻한다.

Bessel 타원체 
ㅇ Bessel 타원체는 1841년에 만들어졌으며 동경좌표계(Tokyo Datum)를 기준으로 한다. 장반경 6377397.155m, 단반경 6356078.963m, 편평율 299.1528128이다.

CSV(comma-separated values) 
ㅇ 몇 가지 필드를 쉼표(,)로 구분한 텍스트 데이터 및 텍스트 파일이다.

Docker 
ㅇ Docker는 애플리케이션을 신속하게 구축, 테스트 및 배포할 수 있는 소프트웨어 플랫폼입니다. Docker는 소프트웨어를 컨테이너라는 표준화된 유닛으로 패키징하며, 이 컨테이너에는 라이브러리, 시스템 도구, 코드, 런타임 등 소프트웨어를 실행하는 데 필요한 모든 것이 포함되어 있습니다. Docker를 사용하면 환경에 구애받지 않고 애플리케이션을 신속하게 배포 및 확장할 수 있으며 코드가 문제없이 실행될 것임을 확신할 수 있습니다

dxf 
ㅇ PC용 캐드 시스템에서 파일교환을 위한 기본도면 파일 형식

GeoJSON 
ㅇ 위치정보를 갖는 점을 기반으로 체계적으로 지형을 표현하기 위해 설계된 개방형 공개 표준 형식

GeoServer 
ㅇ Geoserver(지오서버)는 지리공간 데이터를 공유하고 편집할 수 있는 Java로 개발된 오픈 소스 GIS 소프트웨어 서버이다.
ㅇ 상호운용성을 전제로 개발되었기 때문에, 개방형 표준을 사용하여 다양한 공간 데이터 소스를 서비스할 수 있게 한다.

GIS(Geographic Information System) 
ㅇ 넓게는 지리공간적으로 참조 가능한 모든 형태의 정보를 효과적으로 수집, 저장, 갱신, 조정, 분석, 표현할 수 있도록 설계된 컴퓨터의 하드웨어와 소프트웨어 및 지리적 자료, 인적자원의 통합체를 의미. 좁게는 전 국토의 지리공간정보를 디지털화하여 수치 지도(digital map)로 작성하고 다양한 정보통신기술을 통해 재해, 환경, 시설물, 국토공간 관리와 행정서비스에 활용하고자 하는 첨단정보시스템을 의미

GRS80 타원체 
ㅇ Geodetic Reference System 1980 타원체의 약자로 타원체의 형상, 지구 중심을 원점으로 정한 타원체. 민간 분야의 국제 협력으로 구축되었으므로 고정밀도로 정밀한 WGS84라 불리기도 함(개정을 통하여 WGS84 타원체와의 차이를 단반경 약 0.1mm 정도로 축소하였으므로 실용적으로 동일하게 취급 가능). 장반경 6378137.000m, 단반경 6356752.314m, 편평율 298.257222101임.

Java 
ㅇ 객체 지향 프로그래밍 언어로서 보안성이 뛰어나며 컴파일한 코드는 다른 운영 체제에서 사용할 수 있도록 클래스(class)로 제공된다.
ㅇ 객체 지향 언어인 C+ 언어의 객체 지향적인 장점을 살리면서 분산 환경을 지원하며 더욱 효율적이다.

JavaScript API 
ㅇ 서버 컴포넌트에서 REST API로 구현된 기능을 웹브라우저 클라이언트 Javascript 환경에서 쉽게 사용할 수 있도록 SDK 형태로 개발한 API

Javascript (자바스크립트) 
ㅇ 자바스크립트는 객체 기반의 스크립트 프로그래밍 언어이다. 이 언어는 웹 브라우저 내에서 주로 사용하며, 다른 응용 프로그램의 내장 객체에도 접근할 수 있는 기능을 가지고 있다. 또한 Node.js와 같은 런타임 환경과 같이 서버 프로그래밍에도 사용되고 있다.
 
JSON(JavaScript Object Notation) 
ㅇ JSON은 경량의 DATA교환 형식.
ㅇ 이 형식은 사람이읽고 쓰기에 용이하며, 기계가 분석하고 생성함에도 용이. ㅇ 속성:값 쌍 또는 “키:값 쌍”으로 이루어진 데이터 오브젝트를 전달하기 위해 인간이 읽을 수 있는 텍스트를 사용하는 개방형 표준 포맷이다.

NAS 
ㅇ 컴퓨터 네트워크에 연결된 파일 수준의 컴퓨터 기억장치

OGC(개방형 공간정보 컨소시엄) 
ㅇ 개방형 공간 정보 컨소시엄(Open Geospatial Consortium, OGC)은 19 4년에 기원한 국제 표준화 기구이다. OGC에서 전 세계 50 곳 이상의 상업, 정부, 비영리, 연구 단체들이 지리 공간적 콘텐츠와 서비스, 센서, 웹, 사물 인터넷, GIS 데이터 처리, 데이터 공유를 위한 개방형 표준의 개발 및 구현을 장려하는 콘센서스 프로세스에서 서로 협업한다.

PNU 
ㅇ 필지를 고유하게 식별할 수 있는 값으로 시도 코드 2자리, 시군구 코드 3자리, 읍면동 코드 3자리, 리 2자리, 필지구분 코드 1자리, 본번 코드 4자리, 부번 코드 4자리 총 19자리로 구성되어있다.
ㅇ Point of Interest
ㅇ 관심 지점 ( POI로 약칭)은 누군가가 흥미롭거나 유용하다고 생각할 수 있는 POI 지도상의 특정 장소 또는 위치 지점입니다. 그것은 레스토랑, 호텔 또는 관광 명소일 (point of interest) 수도 있고 ATM, 주유소, 학교 또는 공원과 같은 평범한 장소일 수도 있습니다. 관심 지점은 이벤트와 같이 일시적일 수도 있고 건물이나 도시와 같이 영구적일 수도 있습니다.

PostgreSQL 
ㅇ 확장 가능성 및 표준 준수를 강조하는 객체-관계형 데이터베이스 관리 시스템

Proxy (프록시) 
ㅇ 다른 서버 상의 자원을 찾는 클라이언트로부터 요청을 받아 중계하는 서버

REST API 
ㅇ REST(Representational state transfer) 아키텍처의 제약 조건을 준수하는 API(Application Programming Interface, 애플리케이션 프로그래밍 인터페이스). 자원의 식별, 메시지를 통한 리소스의 조작, 자기 서술적 메시지, 애플리케이션의 상태에 대한 엔진으로서 하이퍼미디어의 특성을 지님.

SHP 
ㅇ 위상관계를 가지지 않는 공간 데이터 형식을 말하며, 파일구조가 단순해서 구조화된 데이터에서는 거의 표준으로 사용되는 GIS 파일형식을 말한다. 배포되는 자원의 형식의 일종. ㅇ ESRI사 제품의 파일포멧으로 도형 및 속장자료의 통합변환이 가능하다.

TIFF 
ㅇ 태그 붙은 화상 파일 형식이라는 뜻으로, 미국의 앨더스사(현재는 어도비 시스템스사에 흡수 합병)와 마이크로소프트사가 공동 개발한 래스터 화상 파일 형식

TM 좌표계 
ㅇ 우리나라는 국토를 네 구역으로 나누어 투영하며 투영된 지도에 N38도/E125도, N38도/E127도, N38도/E129도, N38도/E131도를 서부, 중부, 동부, 동해 원점으로 하여 각각 평면 직각 좌표계를 만들었다. 이 좌표계들을 서쪽에서부터 서부 좌표계, 중부 좌표계, 동부 좌표계, 동해 좌표계라고 하며 이들을 통합하여 지칭하는 것이 TM 좌표계이다.

TOC(Table of contents) 
ㅇ GIS에서 레이어의 목차 또는 지도 레이어의 관리를 의미.

TXT 
ㅇ 문서 파일 형식 중에서 가장 호환성이 높은 파일 형식

URL 
ㅇ 웹 문서의 각종 서비스를 제공하는 서버들에 있는 파일의 위치를 표시하는 표준

UTM (universal transverse mercator)
ㅇ UTM 좌표계(Universal Transverse Mercator Coordinate System)는 전 지구상 점들의 위치를 통일된 체계로 나타내기 위한 격자 좌표 체계의 하나로 1947년에 개발되었다. UTM 좌표계에서는 지구를 경도 6° 간격의 세로 띠로 나누어 횡축 메르카토르 도법으로 그린 뒤, 위도 8° 간격으로 총 60×20 개의 격자로 나누어 각 세로 구역마다 설정된 원점에 대한 종·횡 좌표로 위치를 나타낸다. 지리 좌표계가 극지방으로 갈수록 직사각형이 크게 감소하는 반면 UTM 좌표계는 직사각형 모양을 유지하므로 거리, 면적, 방향 등을 나타내는 데 매우 편리하다는 장점이 있다.

UTM-K 
ㅇ 한국형 UTM(Universal Transverse Mercator) 좌표계로 기존 UTM 좌표계에서 원점 및 가산수치를 다르게 적용

WFS(Web Feature Service) 
ㅇ OGC가 정의한 지리적 피처(Feature)인터페이스 표준이며 피처 요청, 카타로그 조회, 속성 조회 가능. Http로 요청하고 XML, GeoJSON등으로 받음.

WGS84 타원체 
ㅇ World Geodetic System 1984 타원체의 약자로 미국 국방성이 1984년에 군사용으로 구축, 채택하였고 선박, 항공기의 항행용으로 사용. 1996년 미국이 GPS의 민생 이용을 위해 계속적으로 서비스할 것을 표명한 이후로 현재까지 GPS에 사용. 개정된 측량법에서 UTM 도법을 사용하여 투영. 장반경 6378137.000m, 단반경 6356752.314m, 편평율 298.257223563임. 개정을 통하여 GRS80 타원체와의 차이를 단반경 약 0.1mm 정도로 축소하였으므로 실용적으로 동일하게 취급 가능함.

WMS(Web Map Service) 
ㅇ OGC가 정의한 지도이미지 인터페이스 표준이며 지도 요청, 카타로그 조회, 속성 조회 가능. Http로 요청하고 이미지로 받음.
ㅇ 웹 맵 서비스(Web Map Service, WMS)는 GIS(지리 정보 시스템)에서 데이터를 사용하기 위해 맵 서버에서 생성된 지도 이미지를 인터넷상에서 제공하기 위한 표준 프로토콜이다.

WMTS (Web Map Tile Service) 
ㅇ OGC 웹 맵 타일 서비스 구현 표준(WMTS)은 미리 정의 된 콘텐츠, 범위 및 해상도가 있는 타일 이미지를 사용하여 공간적으로 참조된 데이터의 맵 타일에 대한 웹 기반 요청을 만들기 위한 인터페이스 세트를 정의합니다.

XLS 
ㅇ마이크로소프트사에서 개발해 판매하는 스프레드시트 프로그램

XML 
ㅇ W3C에서 여러 특수 목적의 마크업 언어를 만드는 용도에서 권장되는 다목적 마크업 언어이다. ㅇ 가장 친숙하고 흔하게 접할 수 있는 마크업 언어로 HTML이 있다.
ㅇ XML은SGML의 단순화된 부분 집합이지만 , 수많은 종류의 데이터를 기술하는데 적용. ㅇ XML은 주로 다른 시스템, 특히 인터넷에 연결된 시스템끼리 데이터를 쉽게 주고 받을 수 있게 하여 HTML의 한계를 극복할 목적으로 만들어짐.

ZIP 
ㅇ PC로 다운로드한 데이터를 저장할 때 압축을 하거나 압축을 풀어주는 개방 표준

경계 디졸브 (dissolve boundaries) ㅇ 교차하거나 필드 값이 동일한 영역 피처를 병합합니다.
ㅇ 예를 들어 행정구역 레이어가 있는 경우 각 법정동별 시군구코드 속성이 있으면 시군구코드 속성을 사용하여 경계를 디졸브할 수 있다. 인접 시군구코드가 같은 경우에 병합되어 최종 결과는 시군구 경계 레이어이다.

경위도 좌표계 
ㅇ 일반적으로 지리좌표계를 의미하며 2차원 경위도 좌표계는 지구상의 위치를 나타내는 3차원 좌표계인 지구 타원체의 위치를 2차원인 면에 표현. 3차원 경위도 좌표계는 3차원 공간에서 경도, 위도, 고도 세 부분이 서로 직교하는 특징이 있음.

경위도(longitude and latitude) 
ㅇ 경도(longitude)와 위도(latitude)의 것으로 지구 및 천체 표면상에 위치(점)를 나타내기 위한 좌표 표현
 
공간 검색(공간질의) 
ㅇ 위치나 공간관계에 기초하여 지형물을 선정하는 과정으로서, 즉 사용자가 지도상에 위치한 사상의 속성이나 지도의 영상면출력(Display)이나 찾는 것을 가능하게 하는 GIS의 기능이다.

공간 분석 
ㅇ 지도상의 주요 자산의 실제 위치를 확인하는 것으로 다양한 지리정보를 통합 분석하여 이용자의 요청 사항에 해당하는 정보를 생성.

공간 분할 생성 
ㅇ 선택한 공간정보를 다양한 분할 타입으로 (사각형, 육각형, 타원 등) 둘 또는 그 이상으로 나눔

공간 조건 검색 분석 
ㅇ 지정한 일련의 조건을 충족하는 기존 피처를 선택한다.
ㅇ 이러한 조건은 속성 쿼리(비어 있는 필지)와 공간 쿼리(강에서 1.6km이내 필지)를 기반으로 할 수 있다.

공간 조건 추출 분석 
ㅇ 지정한 일련의 조건을 충족하는 새 피처를 생상하는데 사용
ㅇ 이러한 조건은 속성 쿼리(비어 있는 필지)와 공간 쿼리(홍수권 내에 있는 필지)를 기반으로 할 수 있다. 
ㅇ 공간 조건 검색 분석과 공간 조건 추출 분석의 가장 큰 차이점은 공간 조건 추출 분석의 경우 부분 피처를 포함할 수 있다는 것이다.

공간분포 패턴 분석 
ㅇ 포인트 피처의 중심 피처, 평균 중심, 중앙값 중심 또는 타원(방향 분포)를 찾는다. 
ㅇ 중심 피처 : 데이터셋에서 가장 중심에 위치한 피처를 식별한다.
ㅇ 평균 중심 : 피처 집합의 지리적 중심(또는 집중 중심)을 식별한다.
ㅇ 중앙값 중심 : 데이터셋의 피처까지의 전체 유클리드 거리를 최소화하는 위치를 식별한다. 
ㅇ 타원 : 표준편차 타원을 생성하여 지리 피처의 공간 특성을 요약한다. 중심 경향, 분산 및 방향 경향 타원의 크기는 1, 2 또는 3 표준편차로 조정할 수 있다.

공간정보 
ㅇ 지상 · 지하 · 수상 · 수중 등 공간상에 존재하는 자연적 또는 인공적인 객체에 대한 위치정보 및 이와 관련된 공간적 인지 및 의사결정에 필요한 정보이다

공간정보 좌표변환 
ㅇ 공간정보를 생성하기 위한 좌표변환에 관한 것으로 일반적으로 3단계 프로세스를 거침. 1단계에서 좌표를 정의하고 2단계에서 좌표를 변환하고 3단계에서는 좌표변환 과정에서 변환된 좌표의 값이 정상 범위 밖의 오차를 생성했는지 확인하는 과정을 의미

공간조인 분석 
ㅇ 공간 및 속성 관계에 따라 한 레이어나 테이블의 속성을 다른 레이어나 테이블로 전송한다. 필요한 경우 조인된 피처에 대한 통계를 계산할 수 있다.

기하학, 지오메트리 (Geometry) 
ㅇ 공간에 있는 도형의 성질, 즉 대상들의 치수, 모양, 상대적 위치 등을 연구하는 수학의 한 분야 길이 계산 ㅇ레이어 피처의 길이를 측정하는 작업을 수행한다.

데이터 추출 
ㅇ 지정된 관심 영역에 대한 데이터를 선택하여 다운로드하거나 레이어에 추가된다.

도로명주소 
ㅇ 기존 지번주소를 대신하여 도로에 이름을 붙이고, 건물에 번호를 붙여 도로명과 건물번호를 알기 쉽게 표시하는 주소

동기화 
ㅇ 독립된 2개 이상의 주기적인 사건을 적절한 방법으로 결합, 제어함으로써 일정한 위상 관계를 지속시키는 일

레이아웃 
ㅇ 각 구성요소를 공간에 효과적으로 배열하는 일, 또는 그 기술
 
레이어 병합 
ㅇ 지오메트리 유형이 같은 두 피처 레이어를 합쳐 하나의 결과 레이어를 생성

레이어 중첩(교차) 
ㅇ 동일한 유형의 두 레이어의 교차되는 영역을 새 레이어로 속성을 포함하여 병합하는 작업을 수행한다.

레이어 중첩(유니온) 
ㅇ 동일한 유형의 두 레이어를 새 레이어로 속성을 포함하여 교차되는 영역은 자르기 후 병합하는 작업을 수행한다.

레이어 중첩(지우기) 
ㅇ 동일한 유형의 두 레이어의 중첩되는 영역을 제외한 새 레이어로 속성을 포함하여 병합하는 작업을 수행한다.

레이어 (layer) 
ㅇ 서버에서 맵으로서 요청되는 공간정보의 기본 단위 면적 계산 
ㅇ레이어 피처의 면적과 둘레길이를 측정하는 작업을 수행한다.

밀도 계산 (밀도맵 / heat map) 
ㅇ 히트 맵(heat map)은 열을 뜻하는 히트(heat)와 지도를 뜻하는 맵(map)을 결합시킨 단어로, 색상으로 표현할 수 있는 다양한 정보를 일정한 이미지위에 열분포 형태의 비쥬얼한 그래픽으로 출력하는 것이 특징이다.

발행 
ㅇ 공간 데이터를 GIS Server에 레이어로 발행하여 어디에서든 인터넷을 통해 웹, 데스크톱 및 모바일에서 GIS Server 레이어에 접근할 수 있도록 하는 기능

버퍼 
ㅇ 입력 피처 주위의 특정한 거리를 표현하기 위해 버퍼 폴리곤을 생성하는 기능

버퍼 분석 
ㅇ 포인트, 라인 또는 영역 피처에서 지정된 거리를 포함하는 영역을 생성한다.
ㅇ 일반적으로 레이어 중첩과 같은 도구를 사용하여 추가 분석할 수 있는 영역을 생성하는데 사용

범위 내 요약 
ㅇ 분석 레이어의 영역 경계 내에 있는 피처(및 피처의 일부)를 찾는다.
ㅇ 요약할 레이어의 숫자 필드에서 계산할 수 있는 통계는 합계, 최소값, 최대값, 평균, 표준편차가 있음

베이스맵 (basemap) 
ㅇ 지도의 배경 설정을 형성하는 GIS 데이터 및 정형화 된 이미지 모음

스와이프 
ㅇ 수직 스와이프 막대를 좌우로 이동하여 한 웹 맵 내의 서로 다른 두 레이어를 비교할 수 있는 기능

심볼 (Symbol) 
ㅇ 지리적 사상을 나타내는 표현도식으로 점, 선, 면의 특징을 나타내는 도형요소이다. 예를 들어 선의 심볼은 Arc Feature를 나타내고 마크 심볼은 점을, 쉐이드 심볼은 면을, 그리고 텍스트 심볼은 주석을 나타낸다. 심볼의 색상, 크기, 패턴과 그외의 여러 가지 특징으로 정의된다. GIS는 많은 다양한 심볼을 디스플레이 하기 위해 점, 선, 면 등을 갖추고 있다. 지형도상에 표현되는 내용 및 그 표시방법 등을 정하고 지형도의 규격에 대한 통일을 도모할 목적으로 만들어진 형식을 말한다.

썸네일 (thumb nail) 
ㅇ 인터넷에서 페이지 전체의 레이아웃을 검토할 수 있게 페이지 전체를 작게 줄여 화면에 띄운 것을 말한다.

영역 내 집계 분석 
ㅇ 입력 레이어 경계 내에 있는 피처를 찾는다. 그런 다음 포함된 피처에 대한 통계를 계산한다.

웹레이어 (web layer) 
ㅇ 웹 레이어라고도 하는 레이어는 맵과 씬을 생성하는 데 사용되는 공간 데이터의 논리 컬렉션으로, 공간 분석의 기준으로도 사용됩니다. 예를 들어 캠퍼스 건물 컬렉션을 나타내는 건물 레이어에는 건물의 이름, 건물 유형, 건물 크기, 기타 가능한 속성 등과 같은 각 건물의 등록정보를 나타내는 속성 값이 포함됩니다.
 
지도 빌더 
ㅇ 레이어(점, 선, 면)의 스타일 편집 및 등록 관리 하고, 지도 상의 레이어와 각종 분석 기능을 이용하여 하나의 의미 있는 지도(레이어 모음)를 생성하는 도구

앱 빌더 
ㅇ 지도 빌더를 통해 생성된 지도를 다양한 테마(표준, 편집, 탭, 스토리) 및 위젯 도구를 활용하여
새로운 지도앱을 제작하는 도구

주변집계 분석 
ㅇ 직선 거리 또는 이동 모드를 사용하여 입력 레이어에서 피처로부터 지정된 거리 내에 있는 피처를 찾는다. 그런 다음 인근 피처에 대한 통계를 계산한다.

주소정제 
ㅇ 비정형 원본 주소를 표준 주소로 변환

중심 찾기 분석 
ㅇ 멀티포인트, 라인 및 영역 피처의 기하학적 중심을 나타내는 포인트 피처를 생성한다.

지번주소 
ㅇ 땅에 번호를 매겨 주소를 정하는 방식

지오코딩 (geocoding) 
ㅇ 위치의 한 형태를 다른 형태로 변환 . 지오코딩은 보통 “주소”나 “교차로”를 “직접 위치(좌표)”로 번역하는 것을 말한다. 많은 서비스 제공자는 지오코더에 “역( 逆 )지오코딩” 인터페이스를 포함해서 일반적인 위치 번역기로 서비스의 정의를 확장한다. 라우팅 서비스는 보통 다른 것에서는 활용할 수 없는 내부 위치 인코딩을 사용하기 때문에, 지오코더는 서비스 내부에 존재하는 정수의 일부일 뿐이다.

축척 (scale) 
ㅇ 실제의 거리를 일정한 비율로 줄인 정도로, (지도상의 거리/실제 거리)를 지도축척(map scale)이라 한다.

클러스트링 
ㅇ 개체들이 주어졌을 때, 개체들을 몇 개의 비슷한 특성을 가진 데이터들의 집단으로 나누는 과정을 의미. 
ㅇ 맵에 포인트 수가 많은 레이어가 있는 경우 군집을 구성하여 더욱 간편하게 데이터에서 의미 있는 정보를 시각적으로 추출할 수 있다.

타원체 
ㅇ 준거 타원체<측지학> 주축을 중심으로 회전하는 타원에 의해 형성된, 3차원 유클리드 공간에 포함된 기하학적 기준면비고 1 지구에서 타원체는 축이 두개 있으며, 남북축을 기준으로 회전한다. 그 결과 편평 타원체가 생성되며, 초점의 중심은 지구의 명목상의 중심에 위치한다

피처(feature) 
ㅇ 모양, 크기, 위치 등과 같은 도형의 공간정보와 속성 모두를 포함하는 단일 객체

필드 (field) 
ㅇ 소프트웨어에 관련하며, 하나의 레코드 중에서 특정 종류의 데이터를 위해서 사용되는 지정된 영역을 말한다. 예를 들면, 급여 레코드 중의 「 잔여 지급액 」 이 들어 있는 자릿수를 정한 영역 등을 가리킨다.

핫스팟 (hotspot) 
ㅇ 데이터의 공간 패턴에 통계적으로 유의한 군집이 있는지를 격자 형태로 시각화합니다.

행정구역 
ㅇ 정치적으로 하나의 단위를 이루는 국가의 영역을 국가 행정상의 목적에 따라 구획한 행정단위
 

9. FAQ
Question 
(지도빌더) 어떤 종류의 레이어를 지도에 추가할 수 있습니까?
- Shape File(ZIP), 주소, PNU, X/Y좌표가 포함된 엑셀/CSV/TXT 파일, OGC 표준의 WFS/WMS/WMTS 웹레이어 URL, DXF 캐드파일 등
 
(지도빌더) 그리기 및 측정도구 결과는 어떻게 삭제 하나요?
- 지도에 그려진 객체 위에서 마우스 우측 클릭하면 삭제 버튼이 표출되고 삭제 버튼을 클릭하면 삭제됩니다.
 
(지도빌더) 레이어 목록(TOC)에 있는 레이어가 공간분석 레이어에 왜 안올라 오나요?
- 공간분석이 가능한 레이어 유형만 분석대상 및 비교대상 레이어에 검색됩니다. 예 ) 밀도분석 : 점 레이어, 면적계산 : 면 레이어, 공간조인 분석 : 점/선/면 레이어
 
(지도빌더) 지오코딩 결과를 점 레이어와 면 레이어 둘 다 발행하려면 어떻게 하나요?
- 작업 알림창에서 지오코딩 이력을 삭제하지 않은 경우 [+] 버튼을 클릭하여 언제든지 레이어로 발행 할 수 있습니다.
 
(앱빌더) 표준 테마와 편집 테마는 어떻게 다르나요?
- 표준 테마, 편집 테마 대부분 동일한 위젯 도구 사용이 가능하지만 편집 테마에서만 속성 테이블의 편집 기능 사용이 가능합니다.
 
(앱빌더) 앱 빌더로 생성한 앱을 다른곳에서 사용이 가능한가요?
- 앱을 저장하고 미리보기 메뉴를 클릭 후 미리보기창에서 브라우저의 URL을 복사하여 타 시스템의 메뉴 등으로 연결하여 사용 가능합니다.
 
(개발자센터) 폐쇄망에서도 바로e맵, 브이월드 등 외부 연계 API를 사용 할 수 있나요?
- 폐쇄망의 경우 GeOn-API 서버가 외부망에 직접 연결이 가능하거나 별도의 내/외부망 연결이 가능한 Proxy 서버가 필요합니다.
 
(개발자센터) 지도 예제와 위젯 예제의 다른점은 무엇인가요?
- 지도 예제는 웹 환경에서 지도를 제어하는데 필요한 다양한 예제를 제공합니다.
- 위젯 예제는 GeOn-API의 요청 및 응답 결과 처리, 지도 제어, 기능 UI 생성 등을 위젯에서 처리하여 개발자가 좀 더 빠르고 쉽게 개발하도록 다양한 예제를 제공합니다.

