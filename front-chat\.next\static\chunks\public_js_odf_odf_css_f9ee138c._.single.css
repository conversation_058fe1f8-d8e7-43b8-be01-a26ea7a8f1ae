/* [project]/public/js/odf/odf.css [app-client] (css) */
.odf-map {
  width: 100%;
  height: 100%;
}

.odf-control {
  pointer-events: auto;
}

.odf-control.area-toolbar {
  z-index: 9999999;
  width: 30px;
  height: 100%;
  position: absolute;
  top: .5em;
  right: .5em;
}

.subGrp {
  position: relative;
}

.subGrpArea {
  background-color: #e6e6ea;
  border-radius: 5px;
  width: max-content;
  padding: 2px;
  position: absolute;
  top: 0;
  right: 35px;
}

.subGrpArea.on {
  display: block;
}

.subGrpArea.off {
  display: none;
}

.subGrpArea ul {
  margin: 0;
  padding: 0;
  display: inline-block;
}

.subGrpArea ul li {
  list-style: inside none;
  display: inline-block;
}

.subGrpArea ul li button span {
  vertical-align: middle;
}

.odf-control-btn {
  background-color: #fff;
  background-position: center;
  background-repeat: no-repeat;
  border: 1px solid #e6e6ea;
  border-radius: 5px;
  width: 25px;
  height: 25px;
  font-size: 0;
  font-weight: bold;
}

.ol-overviewmap.right-down {
  bottom: -200px !important;
  left: 40px !important;
}

.ol-overviewmap.left-up {
  left: -200px;
  bottom: -30px !important;
}

.ol-overviewmap.left-down {
  left: -200px;
  right: 35px;
  top: 0 !important;
}

.ol-overviewmap.right-up {
  bottom: -30px !important;
  left: 40px !important;
}

.odf-control.area-toolbar div.subToolbarGrp {
  padding: 3.5px 0;
}

.subToolbarGrp {
  text-align: center;
  background-color: #d3d3d6b8;
  border-radius: 5px;
  margin-bottom: 3px;
}

.odf-draw-overay {
  vertical-align: middle;
  background-color: #d3d3d6b8;
  border-radius: 5px;
  padding: 3px;
}

.odf-draw-overay input {
  width: 100px;
  margin-right: 3px;
}

.odf-draw-overay button {
  background-color: #fff;
  border: 1px solid #e6e6ea;
  border-radius: 5px;
  font-weight: bold;
}

.ol-overviewmap:not(.odf-inner) .ol-overviewmap-map {
  z-index: 9999999;
  width: 200px;
  position: absolute;
  top: .5em;
  right: .5em;
}

.odf-control-overviewmap {
  position: relative;
}

.odf-control-overviewmap .ol-overviewmap.ol-control {
  background-color: #ffffffd2;
  border-radius: 5px;
  width: fit-content;
  padding: 4px;
  position: absolute;
}

.ol-overviewmap-map {
  background-color: #fff;
}

.ol-overlay-container {
  will-change: left, right, top, bottom;
}

.ol-overviewmap .ol-overviewmap-map {
  border: 1px solid #7b98bc;
  width: 190px;
  height: 190px;
  margin: 2px;
}

.ol-overviewmap:not(.ol-collapsed) button {
  position: absolute;
  bottom: 1px;
  left: 2px;
}

.ol-overviewmap.ol-collapsed .ol-overviewmap-map, .ol-overviewmap.ol-uncollapsible button {
  display: none;
}

.ol-overviewmap:not(.ol-collapsed) {
  background: #fffc;
}

.ol-overviewmap .ol-overviewmap-box:hover {
  cursor: move;
}

.ol-overviewmap-box {
  border: 2px solid red;
}

.ol-selectable {
  -webkit-touch-callout: default;
  -webkit-user-select: auto;
  -moz-user-select: auto;
  -ms-user-select: auto;
  user-select: auto;
}

.slider {
  height: 100px;
  margin: 2px;
}

.slider-btn {
  text-align: center;
  background-color: #e6e6ea;
  height: 20px;
}

.btn-control-basemap {
  background-color: #e6e6ea;
  background-image: url("../media/btn-control-basemap.33b04c6c.png");
}

.btn-control-basemap:hover {
  background-image: url("../media/btn-control-basemap-h.78ffc03a.png");
}

.btn-control-bookmark {
  background-image: url("../media/btn-control-bookmark.e0dff0c2.png");
}

.btn-control-bookmark:hover {
  background-image: url("../media/btn-control-bookmark-h.a1806779.png");
}

.btn-control-home {
  background-image: url("../media/btn-control-home.930d936c.png");
}

.btn-control-home:hover {
  background-image: url("../media/btn-control-home-h.7651ed44.png");
}

#subGrp-basemap-area button.odf-control-btn {
  width: auto;
  font-size: 14px;
}

#subGrp-basemap-area button.odf-control-btn:hover {
  color: #36b3e6;
  width: auto;
}

.btn-control-pre-move {
  background-image: url("../media/btn-control-pre-move.2d689f2e.png");
}

.btn-control-pre-move:hover {
  background-image: url("../media/btn-control-pre-move-h.ab31dffe.png");
}

.btn-control-after-move {
  background-image: url("../media/btn-control-after-move.90c2ea40.png");
}

.btn-control-after-move:hover {
  background-image: url("../media/btn-control-after-move-h.0940b635.png");
}

.btn-control-zoom-in {
  background-image: url("../media/btn-control-zoom-in.07f0c8af.png");
}

.btn-control-zoom-in:hover {
  background-image: url("../media/btn-control-zoom-in-h.fa53054e.png");
}

.btn-control-zoom-out {
  background-image: url("../media/btn-control-zoom-out.45193f28.png");
}

.btn-control-zoom-out:hover {
  background-image: url("../media/btn-control-zoom-out-h.6e904174.png");
}

.btn-control-draw {
  background-image: url("../media/btn-control-draw.f332d61d.png");
}

.btn-control-draw:hover {
  background-image: url("../media/btn-control-draw-h.4b76de09.png");
}

.btn-control-draw-text {
  background-image: url("../media/btn-control-draw-text.414b64b9.png");
}

.btn-control-draw-text:hover {
  background-image: url("../media/btn-control-draw-text-h.da434583.png");
}

.btn-control-draw-polygon {
  background-image: url("../media/btn-control-draw-polygon.eb2cf369.png");
  background-size: contain;
}

.btn-control-draw-polygon:hover {
  background-image: url("../media/btn-control-draw-polygon-h.0587a469.png");
  background-size: contain;
}

.btn-control-draw-lineString {
  background-image: url("../media/btn-control-draw-lineString.1b450227.png");
}

.btn-control-draw-lineString:hover {
  background-image: url("../media/btn-control-draw-lineString-h.2638902f.png");
}

.btn-control-draw-point {
  background-image: url("../media/btn-control-draw-point.89ecd7ef.png");
}

.btn-control-draw-point:hover {
  background-image: url("../media/btn-control-draw-point-h.9dba8f2d.png");
}

.btn-control-draw-circle {
  background-image: url("../media/btn-control-draw-circle.18eefcf7.png");
  background-size: contain;
}

.btn-control-draw-circle:hover {
  background-image: url("../media/btn-control-draw-circle-h.1da9c50e.png");
  background-size: contain;
}

.btn-control-draw-box {
  background-image: url("../media/btn-control-draw-box.35d746e7.png");
}

.btn-control-draw-box:hover {
  background-image: url("../media/btn-control-draw-box-h.7e04287e.png");
}

.btn-control-draw-curve {
  background-image: url("../media/btn-control-draw-curve.0a851869.png");
}

.btn-control-draw-curve:hover {
  background-image: url("../media/btn-control-draw-curve-h.82e7b720.png");
}

.btn-control-draw-buffer {
  background-image: url("../media/btn-control-draw-buffer.d7a2baff.png");
}

.btn-control-draw-buffer:hover {
  background-image: url("../media/btn-control-draw-buffer-h.279290b3.png");
}

.btn-control-print {
  background-image: url("../media/btn-control-print.1c5831a9.png");
}

.btn-control-print:hover {
  background-image: url("../media/btn-control-print-h.7ffc9598.png");
}

.btn-control-download-img {
  background-image: url("../media/btn-control-download.07429ffd.png");
}

.btn-control-download:hover {
  background-image: url("../media/btn-control-download-h.0ffe00f2.png");
}

.btn-control-download-pdf {
  background-image: url("../media/btn-control-download-pdf.4162a1ff.png");
}

.btn-control-download-pdf:hover {
  background-image: url("../media/btn-control-download-pdf-h.c03b40a2.png");
}

.btn-control-overviewmap {
  background-image: url("../media/btn-control-overviewmap.245f0600.png");
}

.btn-control-overviewmap:hover {
  background-image: url("../media/btn-control-overviewmap-h.b5a00614.png");
}

.btn-control-measure {
  background-image: url("../media/btn-control-measure.a969dffd.png");
}

.btn-control-measure:hover {
  background-image: url("../media/btn-control-measure-h.2f614821.png");
}

.btn-control-measure-distance {
  background-image: url("../media/btn-control-measure-distance.e03393fc.png");
}

.btn-control-measure-distance:hover {
  background-image: url("../media/btn-control-measure-distance-h.9818daea.png");
}

.btn-control-measure-area {
  background-image: url("../media/btn-control-measure-area.345b2a47.png");
}

.btn-control-measure-area:hover {
  background-image: url("../media/btn-control-measure-area-h.c75ff75f.png");
}

.btn-control-measure-round {
  background-image: url("../media/btn-control-measure-round.2c882fa0.png");
}

.btn-control-measure-round:hover {
  background-image: url("../media/btn-control-measure-round-h.1d578f94.png");
}

.btn-control-measure-spot {
  background-image: url("../media/btn-control-measure-spot.89ecd7ef.png");
}

.btn-control-measure-spot:hover {
  background-image: url("../media/btn-control-measure-spot-h.9dba8f2d.png");
}

.btn-control-clear {
  background-image: url("../media/btn-control-clear.5a219fd5.png");
}

.btn-control-clear:hover {
  background-image: url("../media/btn-control-clear-h.b1eb47d2.png");
}

.btn-control-rotate {
  position: relative;
}

.btn-control-rotate span {
  background-image: url("../media/btn-control-rotation.5ae930be.png");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  width: 25px;
  height: 25px;
  position: absolute;
  top: 0;
  left: 0;
}

.btn-control-rotate span:hover {
  background-image: url("../media/btn-control-rotation-h.ec92487b.png");
}

.btn-control-fullscreen.on {
  background-image: url("../media/btn-control-fullscreen-on.3ec19a31.png");
}

.btn-control-fullscreen.on:hover {
  background-image: url("../media/btn-control-fullscreen-on-h.1339f414.png");
}

.btn-control-fullscreen.off {
  background-image: url("../media/btn-control-fullscreen-off.f1e7ccda.png");
}

.btn-control-fullscreen.off:hover {
  background-image: url("../media/btn-control-fullscreen-off-h.c326ab91.png");
}

.btn-control-swiper {
  background-image: url("../media/btn-control-swiper.81fd7340.png");
  background-size: contain;
}

.btn-control-swiper:hover {
  background-image: url("../media/btn-control-swiper-h.a627bb67.png");
}

.btn-control-swipe div.subGrpArea {
  background-color: #fff;
}

.odf-dividemap-container {
  width: 100%;
  height: 100%;
}

.btn-control-dividemap {
  background-image: url("../media/btn-control-dividemap.3b9200c2.png");
  background-size: contain;
}

.btn-control-dividemap:hover {
  background-image: url("../media/btn-control-dividemap-h.a7a148e7.png");
  background-size: contain;
}

.btn-control-dividemap-dualMap {
  background-image: url("../media/btn-control-dividemap-dualMap.4f34124a.png");
  background-size: contain;
}

.btn-control-dividemap-dualMap:hover {
  background-image: url("../media/btn-control-dividemap-dualMap-h.5cb8f0fe.png");
  background-size: contain;
}

.btn-control-dividemap-threepleMap {
  background-image: url("../media/btn-control-dividemap-threepleMap.2d8c7863.png");
  background-size: contain;
}

.btn-control-dividemap-threepleMap:hover {
  background-image: url("../media/btn-control-dividemap-threepleMap-h.8ae98a2c.png");
  background-size: contain;
}

.btn-control-dividemap-quadMap {
  background-image: url("../media/btn-control-dividemap-quadMap.b8e45946.png");
  background-size: contain;
}

.btn-control-dividemap-quadMap:hover {
  background-image: url("../media/btn-control-dividemap-quadMap-h.bf0e9694.png");
  background-size: contain;
}

.dualMap div.odf-map, .threepleMap div.odf-map, .quadMap div.odf-map, .mainMap:not(.on2):not(.on4):not(.on3) {
  width: 100% !important;
  height: 100% !important;
}

.dualMap:not(.mainMap):not(.on2) {
  display: none;
}

.dualMap.on2.dualMap-vertical {
  border-collapse: collapse;
  display: block;
  width: 50% !important;
  height: 100% !important;
}

.dualMap.on2.dualMap-vertical.d-2-1 {
  float: left;
}

.dualMap.on2.dualMap-vertical.d-2-2 {
  float: right;
}

.dualMap.on2.dualMap-horizonal {
  border-collapse: collapse;
  display: block;
  width: 100% !important;
  height: 50% !important;
}

.threepleMap:not(.mainMap):not(.on3) {
  display: none;
}

.threepleMap.on3.threepleMap-vertical {
  border-collapse: collapse;
  float: left;
  display: block;
  width: 33.3333% !important;
  height: 100% !important;
}

.threepleMap.on3.threepleMap-horizonal {
  border-collapse: collapse;
  display: block;
  width: 100% !important;
  height: 33.3% !important;
}

.threepleMap.on3.threepleMap-complex-01, .threepleMap.on3.threepleMap-complex-02, .threepleMap.on3.threepleMap-complex-03, .threepleMap.on3.threepleMap-complex-04 {
  border-collapse: collapse;
  display: block;
}

.threepleMap.on3.threepleMap-complex-01 {
  float: left;
  width: 50% !important;
}

.threepleMap.on3.threepleMap-complex-01.d-3-1 {
  height: 100% !important;
}

.threepleMap.on3.threepleMap-complex-01.d-3-2, .threepleMap.on3.threepleMap-complex-01.d-3-3 {
  height: 50% !important;
}

.threepleMap.on3.threepleMap-complex-02 {
  float: left;
  height: 50% !important;
}

.threepleMap.on3.threepleMap-complex-02.d-3-1 {
  width: 100% !important;
}

.threepleMap.on3.threepleMap-complex-02.d-3-2, .threepleMap.on3.threepleMap-complex-02.d-3-3 {
  width: 50% !important;
}

.threepleMap.on3.threepleMap-complex-03 {
  float: right;
  width: 50% !important;
}

.threepleMap.on3.threepleMap-complex-03.d-3-2, .threepleMap.on3.threepleMap-complex-03.d-3-3 {
  height: 50% !important;
}

.threepleMap.on3.threepleMap-complex-03.d-3-1 {
  height: 100% !important;
}

.threepleMap.on3.threepleMap-complex-04 {
  float: left;
  height: 50% !important;
}

.threepleMap.on3.threepleMap-complex-04.d-3-1, .threepleMap.on3.threepleMap-complex-04.d-3-2 {
  width: 50% !important;
}

.threepleMap.on3.threepleMap-complex-04.d-3-3 {
  width: 100% !important;
}

.quadMap:not(.mainMap):not(.on4) {
  display: none;
}

.quadMap.on4.quadMap-horizonal {
  border-collapse: collapse;
  display: block;
  width: 100% !important;
  height: 25% !important;
}

.quadMap.on4.quadMap-vertical {
  border-collapse: collapse;
  float: left;
  display: block;
  width: 25% !important;
  height: 100% !important;
}

.quadMap.on4.quadMap-complex {
  border-collapse: collapse;
  float: left;
  display: block;
  width: 50% !important;
  height: 50% !important;
}

.h-slider {
  width: 100px;
  margin: 2px;
  padding-right: 20px;
  text-align: justify !important;
}

.h-slider-btn {
  background-color: #e6e6ea;
  width: 20px;
}

#positionStr {
  float: right;
  font-size: 15px;
  font-weight: bold;
}

.layer-Aside {
  z-index: 99999;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -o-transition: all .3s;
  width: 300px;
  height: 100%;
  -webkit-transition: all .3s;
  -moz-transition: all .3s;
  -ms-transition: all .3s;
  transition: all .3s;
  position: absolute;
  top: 140px;
  left: -220px;
}

.layer-Aside.openAside {
  left: 80px;
}

.layer-Aside {
  z-index: 1000;
  background-color: #fff;
  border-right: 1px solid #c8c8c8;
}

.area-layerTitle {
  background-color: #36b3e6;
  border-bottom: 1px solid #c8c8c8;
  padding: 0 15px;
  position: relative;
}

.area-layerTitle p {
  color: #fff;
  letter-spacing: -.5px;
  font-size: 15px;
  font-weight: bold;
  line-height: 42px;
  display: inline-block;
}

.asideBtnGrp {
  margin-top: -13px;
  position: absolute;
  top: 50%;
  right: 15px;
}

.asideBtnGrp button {
  vertical-align: top;
  text-indent: -9999px;
  background-position: center;
  background-repeat: no-repeat;
  width: 25px;
  height: 26px;
}

.area-asideScroll {
  height: calc(100% - 43px);
  overflow: auto;
}

.asideList {
  padding: 5px 8px;
}

.asideList li {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #e7e7e7;
  height: 60px;
  margin-bottom: 5px;
  padding: 15px 5px 0;
  position: relative;
}

.asideList li.selected {
  background-color: #f5f8fc;
}

.sortable li {
  cursor: move;
}

.btn-typeGrp {
  text-align: left;
  width: calc(100% - 95px);
  height: 28px;
}

.btn-typeGrp .textArea {
  vertical-align: middle;
  color: #555;
  white-space: nowrap;
  -ms-text-overflow: ellipsis;
  text-overflow: ellipsis;
  width: calc(100% - 40px);
  font-size: 13px;
  line-height: 28px;
  display: inline-block;
  overflow: hidden;
}

.asideListBtnGrp {
  height: 20px;
  margin-top: -10px;
  display: inline-block;
  position: absolute;
  top: 50%;
  right: 20px;
}

.asideListBtnGrp button {
  float: left;
  text-indent: -9999px;
  background-position: center;
  background-repeat: no-repeat;
  width: 25px;
  height: 100%;
}

.menu-select {
  z-index: 1010;
  background: #123874;
  width: 80px;
  height: 100%;
  position: absolute;
  top: 140px;
  left: 0;
}

.menu-select li {
  text-align: center;
  color: #7aa1df;
  cursor: pointer;
  border-bottom: 1px solid #1a58a0;
  height: 27px;
  font-size: 10px;
}

.menu-select li:hover, .menu-select li.selected {
  color: #fff;
}

.draggableMarker:hover {
  cursor: move;
}

.areaServerDiv, .areaJsonDiv {
  z-index: 99999;
  position: absolute;
  top: 10px;
  left: 10px;
}

.areaControl {
  float: left;
  width: 150px;
}

.adrressDiv {
  z-index: 99999;
  position: absolute;
  top: 145px;
}

.adrressDiv span {
  float: left;
  background: #fff;
  font-size: 20px;
  font-weight: bold;
}

.adrressDiv div {
  float: left;
  text-align: center;
  background: gray;
  border: 3px solid #ff0000b3;
  width: 35px;
}

.subToolbarGrp.subToolbarGrp_scale {
  position: absolute;
  bottom: 15px;
  right: 10px;
}

.btn-control-scale {
  width: 100%;
  height: 17px;
}

.btn-control-scale-value {
  border: 0;
  font-size: 12px !important;
}

.btn-control-scale-input {
  width: 100%;
}

#odf-control-scale-input {
  width: 48px;
}

.img-test {
  background-image: url("../media/test.3de982e6.png");
  border: 2px solid;
  width: 100px;
  height: 50px;
}

.ol-attribution.ol-logo-only, .ol-attribution.ol-uncollapsible {
  max-width: calc(100% - 3em);
}

.ol-attribution.ol-uncollapsible {
  border-radius: 4px 0 0;
  bottom: 0;
  right: 0;
}

.ol-attribution.ol-control {
  text-align: right;
  background-color: #80808040;
  border-radius: 4px;
  flex-flow: row-reverse;
  align-items: center;
  max-width: calc(100% - 1.3em);
  line-height: normal;
  display: flex;
  position: absolute;
  bottom: .5em;
  right: .5em;
}

.ol-attribution:not(.ol-collapsed) {
  background: #ffffffbf;
}

.ol-attribution button:not(:disabled) {
  cursor: pointer;
  color: #666;
  font-weight: bold;
  font-size: inherit;
  text-align: center;
  text-transform: none;
  background-color: #fff;
  border: none;
  border-radius: 2px;
  flex-shrink: 0;
  width: 1.375em;
  height: 1.375em;
  margin: 1px;
  padding: 0;
  line-height: .4em;
  text-decoration: none;
  display: block;
}

.ol-attribution.ol-uncollapsible button {
  display: none;
}

.ol-attribution ul {
  color: #333;
  text-shadow: 0 0 2px #fff;
  margin: 0;
  padding: 1px .5em;
  font-size: 12px;
}

.ol-attribution li {
  list-style: none;
  display: inline;
}

/*# sourceMappingURL=public_js_odf_odf_css_f9ee138c._.single.css.map*/