type AgentType = 'layer_add' | 'navigation' | 'search' | 'analysis' | string;

export const getAgentFriendlyMessage = (agentName: string): string => {
  const agentType = agentName.toLowerCase() as AgentType;
  
  const messages: Record<string, string> = {
    'layer_add': '🌍 지도에 새로운 레이어를 추가하고 있어요!',
    'navigation': '📍 길 안내를 시작할게요!',
    'search': '🔍 원하시는 정보를 찾아볼게요!',
    'analysis': '📊 데이터를 분석하고 있어요!',
    'default': '✨ 요청하신 작업을 시작할게요!',
    'intent_analyzer': '사용자 메시지의 의도를 도출하고 있습니다...'
  };

  return messages[agentType] || messages['default'];
};
