"use client";

import { useState, useRef, useEffect, useId } from "react";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent
} from "@/components/ui/accordion";
import { BrainCircuit } from "lucide-react";
import { cn } from "@/lib/utils";
import { AnimatedShinyText } from "./magicui/animated-shiny-text";

const thinkingPhrases = [
  "더 나은 답변을 위해 고민 중이에요...",
  "조금만 기다려 주세요...",
];

const completedPhrases = [
  "생각 완료",
];



interface ReasoningProps {
  reasoning: string;
  className?: string;
  initialOpen?: boolean;
  open?: boolean;
  isReasoning?: boolean;
}

export function Reasoning({ reasoning, className, initialOpen = true, open, isReasoning }: ReasoningProps) {
  const contentRef = useRef<HTMLDivElement>(null);
  const uniqueId = useId();
  const itemValue = `reasoning-${uniqueId}`;
  const [isOpenInternal, setIsOpenInternal] = useState(initialOpen);

  useEffect(() => {
    if (open !== undefined) {
      setIsOpenInternal(open);
    }
  }, [open]);

  const accordionValue = isOpenInternal ? itemValue : "";

  useEffect(() => {
    if (isOpenInternal && contentRef.current) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    }
  }, [reasoning, isOpenInternal]);


  const [currentPhrase, setCurrentPhrase] = useState("");
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isReasoning) {
      // 초기 메시지 설정
      const randomIndex = Math.floor(Math.random() * thinkingPhrases.length);
      setCurrentPhrase(thinkingPhrases[randomIndex]);
      setIsAnimating(true);
      
      // 3초마다 메시지 변경
      const interval = setInterval(() => {
        const newIndex = Math.floor(Math.random() * thinkingPhrases.length);
        setCurrentPhrase(thinkingPhrases[newIndex]);
      }, 3000);
      
      return () => {
        clearInterval(interval);
        setIsAnimating(false);
      };
    } else {
      // 완료 상태일 때는 랜덤한 완료 메시지 표시
      const randomIndex = Math.floor(Math.random() * completedPhrases.length);
      setCurrentPhrase(completedPhrases[randomIndex]);
    }
  }, [isReasoning]);

  return (
    <Accordion 
      type="single" 
      value={accordionValue}
      onValueChange={(val) => setIsOpenInternal(val === itemValue)}
      className={cn("w-full h-auto", className)}
      collapsible
    >
      <AccordionItem value={itemValue} className="border-b-0 rounded-lg overflow-hidden bg-card">
        <AccordionTrigger className="px-3 py-0 h-6 min-h-6">
          <div className="flex items-center space-x-2">
            {isReasoning ? (
              <div className="flex items-center gap-2">
                <BrainCircuit 
                  className={`h-4 w-4 text-yellow-500 ${isAnimating ? 'animate-pulse' : ''}`} 
                />
                <AnimatedShinyText 
                  key={currentPhrase}
                  className="text-sm"
                >
                  {currentPhrase}
                </AnimatedShinyText>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <BrainCircuit 
                  className="h-4 w-4 text-green-500" 
                />
                <span className="text-sm text-muted-foreground">
                  {currentPhrase}
                </span>
              </div>
            )}
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-2 mt-2">
          <div 
            ref={contentRef}
            className="text-sm text-gray-500 bg-accent/80 max-h-[150px] overflow-y-auto whitespace-pre-wrap px-4 py-2 border-l-2 border-yellow-400 pl-3 ml-1"
          >
            {reasoning}
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
