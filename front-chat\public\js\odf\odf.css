/*!
 OpenLayers version: v8.2.0
 App version: odf_webpack 2.0.0
 Branch: master
 Commit ID: 86ec6ee5
 Build Time: 2024. 10. 21. 오후 2:42:48
 */

@charset "UTF-8";
.odf-map{
  width:100%;
  height : 100%;
}
.odf-control{
  pointer-events: auto;
}


/*odf-control*/
.odf-control.area-toolbar {
  position: absolute;
  z-index: 9999999;
  right: 0.5em;
  top: 0.5em;
  width: 30px;
  height: 100%;
}
.subGrp {
  position: relative;
}
.subGrpArea {
  position: absolute;
  right: 35px;
  top: 0px;
  padding: 2px;
  background-color: #e6e6ea;
  border-radius: 5px;
  width: max-content;
}
.subGrpArea.on {
  display: block;
}
.subGrpArea.off {
  display: none;
}
.subGrpArea ul {
  margin: 0 0 0 0;
  padding: 0;
  display: inline-block;
}
.subGrpArea ul li {
  list-style: none;
  list-style-position: inside;
  display: inline-block;
}
.subGrpArea ul li button span {
  vertical-align: middle;
}

.odf-control-btn {
  width: 25px;
  height: 25px;
  background-color: white;
  border: #e6e6ea 1px solid;
  border-radius: 5px;
  font-weight: bold;
  background-repeat: no-repeat;
  background-position: center center;
  font-size: 0;
}
.ol-overviewmap.right-down {
  left: 40px !important;
  bottom: -200px !important;
}
.ol-overviewmap.left-up {
  left: -200px;
  bottom: -30px !important;
}
.ol-overviewmap.left-down {
  top: 0px !important;
  left: -200px;
  right: 35px;
}
.ol-overviewmap.right-up {
  left: 40px !important;
  bottom: -30px !important;
}
.odf-control.area-toolbar div.subToolbarGrp {
  padding-bottom: 3.5px;
  padding-top: 3.5px;
  padding-left: 0px;
  padding-right: 0px;
}

.subToolbarGrp {
  /* width: 30px; */
  text-align: center;
  background-color: #d3d3d6b8;
  border-radius: 5px;
  margin-bottom: 3px;
}

.odf-draw-overay {
  background-color: #d3d3d6b8;
  border-radius: 5px;
  padding: 3px;
  vertical-align: middle;
}

.odf-draw-overay input {
  width: 100px;
  margin-right: 3px;
}
.odf-draw-overay button {
  background-color: white;
  border: #e6e6ea 1px solid;
  border-radius: 5px;
  font-weight: bold;
}

.ol-overviewmap:not(.odf-inner) .ol-overviewmap-map {
  position: absolute;
  z-index: 9999999;
  right: 0.5em;
  top: 0.5em;
  width: 200px;
}

.odf-control-overviewmap {
  position: relative;
}
.odf-control-overviewmap .ol-overviewmap.ol-control {
  position: absolute;
  background-color: #ffffffd2;
  padding: 4px;
  border-radius: 5px;
  width: fit-content;
}

.ol-overviewmap-map {
  background-color: white;
}

.ol-overlay-container {
  will-change: left, right, top, bottom;
}

.ol-overviewmap .ol-overviewmap-map {
  border: 1px solid #7b98bc;
  height: 190px;
  margin: 2px;
  width: 190px;
}
.ol-overviewmap:not(.ol-collapsed) button {
  bottom: 1px;
  left: 2px;
  position: absolute;
}
.ol-overviewmap.ol-collapsed .ol-overviewmap-map,
.ol-overviewmap.ol-uncollapsible button {
  display: none;
}
.ol-overviewmap:not(.ol-collapsed) {
  background: rgba(255, 255, 255, 0.8);
}
.ol-overviewmap-box {
  border: 2px dotted rgba(0, 60, 136, 0.7);
}
.ol-overviewmap .ol-overviewmap-box:hover {
  cursor: move;
}

.ol-overviewmap-box {
  border: 2px solid red;
}

.ol-selectable {
  -webkit-touch-callout: default;
  -webkit-user-select: auto;
  -moz-user-select: auto;
  -ms-user-select: auto;
  user-select: auto;
}
/*zoom slider*/
.slider {
  height: 100px;
  margin: 2px;
}
.slider-btn {
  height: 20px;
  background-color: #e6e6ea;
  text-align: center;
}

/*배경지도 subGrp bnt*/
.btn-control-basemap {
  background-color: #e6e6ea;
  background-image: url(images/btn-control-basemap.png);
}
.btn-control-basemap:hover {
  background-image: url(images/btn-control-basemap-h.png);
}

.btn-control-bookmark {
  background-image: url(images/btn-control-bookmark.png);
}
.btn-control-bookmark:hover {
  background-image: url(images/btn-control-bookmark-h.png);
}

.btn-control-home {
  background-image: url(images/btn-control-home.png);
}

.btn-control-home:hover {
  background-image: url(images/btn-control-home-h.png);
}

#subGrp-basemap-area button.odf-control-btn {
  font-size: 14px;
  width: auto;
}
#subGrp-basemap-area button.odf-control-btn:hover {
  color: #36b3e6;
  width: auto;
}
/*move*/
.btn-control-pre-move {
  background-image: url(images/btn-control-pre-move.png);
}
.btn-control-pre-move:hover {
  background-image: url(images/btn-control-pre-move-h.png);
}
.btn-control-after-move {
  background-image: url(images/btn-control-after-move.png);
}
.btn-control-after-move:hover {
  background-image: url(images/btn-control-after-move-h.png);
}
/*줌인/줌아웃*/
.btn-control-zoom-in {
  background-image: url(images/btn-control-zoom-in.png);
}
.btn-control-zoom-in:hover {
  background-image: url(images/btn-control-zoom-in-h.png);
}
.btn-control-zoom-out {
  background-image: url(images/btn-control-zoom-out.png);
}
.btn-control-zoom-out:hover {
  background-image: url(images/btn-control-zoom-out-h.png);
}
/*그리기 툴 박스*/
.btn-control-draw {
  background-image: url(images/btn-control-draw.png);
}
.btn-control-draw:hover {
  background-image: url(images/btn-control-draw-h.png);
}
/*텍스트*/
.btn-control-draw-text {
  background-image: url(images/btn-control-draw-text.png);
}
.btn-control-draw-text:hover {
  background-image: url(images/btn-control-draw-text-h.png);
}
/*폴리곤*/
.btn-control-draw-polygon {
  background-image: url(images/btn-control-draw-polygon.png);
  background-size: contain;
}
.btn-control-draw-polygon:hover {
  background-image: url(images/btn-control-draw-polygon-h.png);
  background-size: contain;
}
/*라인*/
.btn-control-draw-lineString {
  background-image: url(images/btn-control-draw-lineString.png);
}
.btn-control-draw-lineString:hover {
  background-image: url(images/btn-control-draw-lineString-h.png);
}
/*점*/
.btn-control-draw-point {
  background-image: url(images/btn-control-draw-point.png);
}
.btn-control-draw-point:hover {
  background-image: url(images/btn-control-draw-point-h.png);
}
/*원형*/
.btn-control-draw-circle {
  background-image: url(images/btn-control-draw-circle.png);
  background-size: contain;
}
.btn-control-draw-circle:hover {
  background-image: url(images/btn-control-draw-circle-h.png);
  background-size: contain;
}
/*사각형*/
.btn-control-draw-box {
  background-image: url(images/btn-control-draw-box.png);
}
.btn-control-draw-box:hover {
  background-image: url(images/btn-control-draw-box-h.png);
}
/*곡선*/
.btn-control-draw-curve {
  background-image: url(images/btn-control-draw-curve.png);
}
.btn-control-draw-curve:hover {
  background-image: url(images/btn-control-draw-curve-h.png);
}
/*버퍼*/
.btn-control-draw-buffer {
  background-image: url(images/btn-control-draw-buffer.png);
}
.btn-control-draw-buffer:hover {
  background-image: url(images/btn-control-draw-buffer-h.png);
}
/*출력*/
.btn-control-print {
  background-image: url(images/btn-control-print.png);
}
.btn-control-print:hover {
  background-image: url(images/btn-control-print-h.png);
}
/*저장*/
.btn-control-download-img {
  background-image: url(images/btn-control-download.png);
}
.btn-control-download:hover {
  background-image: url(images/btn-control-download-h.png);
}
/*저장 (pdf)*/
.btn-control-download-pdf {
  background-image: url(images/btn-control-download-pdf.png);
}
.btn-control-download-pdf:hover {
  background-image: url(images/btn-control-download-pdf-h.png);
}
.btn-control-overviewmap {
  background-image: url(images/btn-control-overviewmap.png);
}
.btn-control-overviewmap:hover {
  background-image: url(images/btn-control-overviewmap-h.png);
}
/*측정*/
.btn-control-measure {
  background-image: url(images/btn-control-measure.png);
}
.btn-control-measure:hover {
  background-image: url(images/btn-control-measure-h.png);
}
/*거리*/
.btn-control-measure-distance {
  background-image: url(images/btn-control-measure-distance.png);
}
.btn-control-measure-distance:hover {
  background-image: url(images/btn-control-measure-distance-h.png);
}
/*면적*/
.btn-control-measure-area {
  background-image: url(images/btn-control-measure-area.png);
}
.btn-control-measure-area:hover {
  background-image: url(images/btn-control-measure-area-h.png);
}
/*반경*/
.btn-control-measure-round {
  background-image: url(images/btn-control-measure-round.png);
}
.btn-control-measure-round:hover {
  background-image: url(images/btn-control-measure-round-h.png);
}
/*좌표측정*/
.btn-control-measure-spot {
  background-image: url(images/btn-control-measure-spot.png);
}
.btn-control-measure-spot:hover {
  background-image: url(images/btn-control-measure-spot-h.png);
}

/*초기화*/
.btn-control-clear {
  background-image: url(images/btn-control-clear.png);
}
.btn-control-clear:hover {
  background-image: url(images/btn-control-clear-h.png);
}
/*rotate*/
.btn-control-rotate{
  position: relative;
}
.btn-control-rotate span {
  background-size: contain;
  position: absolute;
  width: 25px;
  height: 25px;
  left: 0px;
  top: 0px;
  background-image: url(images/btn-control-rotation.png);
  background-repeat: no-repeat;
  background-position: center center;
}
.btn-control-rotate span:hover {
  background-image: url(images/btn-control-rotation-h.png);
}
/*fullscreen*/
.btn-control-fullscreen.on {
  background-image: url(images/btn-control-fullscreen-on.png);
}
.btn-control-fullscreen.on:hover {
  background-image: url(images/btn-control-fullscreen-on-h.png);
}
.btn-control-fullscreen.off {
  background-image: url(images/btn-control-fullscreen-off.png);
}
.btn-control-fullscreen.off:hover {
  background-image: url(images/btn-control-fullscreen-off-h.png);
}
/*스와이퍼*/
.btn-control-swiper {
  background-image: url(images/btn-control-swiper.png);
  background-size: contain;
}
.btn-control-swiper:hover {
  background-image: url(images/btn-control-swiper-h.png);
}
.btn-control-swipe div.subGrpArea {
  background-color: white;
}

/*분할지도(DivideMapControl) css*/
.odf-dividemap-container{
  width: 100%;
  height: 100%;
}
/*분할지도 컨트롤 아이콘*/
.btn-control-dividemap {
  background-image: url(images/btn-control-dividemap.png);
  background-size: contain;
}
.btn-control-dividemap:hover {
  background-image: url(images/btn-control-dividemap-h.png);
  background-size: contain;
}
.btn-control-dividemap-dualMap {
  background-image: url(images/btn-control-dividemap-dualMap.png);
  background-size: contain;
}
.btn-control-dividemap-dualMap:hover {
  background-image: url(images/btn-control-dividemap-dualMap-h.png);
  background-size: contain;
}
.btn-control-dividemap-threepleMap {
  background-image: url(images/btn-control-dividemap-threepleMap.png);
  background-size: contain;
}
.btn-control-dividemap-threepleMap:hover {
  background-image: url(images/btn-control-dividemap-threepleMap-h.png);
  background-size: contain;
}
.btn-control-dividemap-quadMap {
  background-image: url(images/btn-control-dividemap-quadMap.png);
  background-size: contain;
}
.btn-control-dividemap-quadMap:hover {
  background-image: url(images/btn-control-dividemap-quadMap-h.png);
  background-size: contain;
}


/*분할지도 지도별 영역*/
.dualMap div.odf-map,
.threepleMap div.odf-map,
.quadMap div.odf-map {
  width: 100% !important;
  height: 100% !important;
}
.mainMap:not(.on2):not(.on4):not(.on3) {
  width: 100% !important;
  height: 100% !important;
}


/*dualMap(2분할 css)*/
.dualMap:not(.mainMap):not(.on2) {
  display: none;
}
/*vertical*/
.dualMap.on2.dualMap-vertical {
  display: block;
  border-collapse: collapse;
  width: 50% !important;
  height: 100% !important;
}
.dualMap.on2.dualMap-vertical.d-2-1 {
  float: left;
}
.dualMap.on2.dualMap-vertical.d-2-2 {
  float: right;
}
/*horizonal*/
.dualMap.on2.dualMap-horizonal {
  display: block;
  border-collapse: collapse;
  width: 100% !important;
  height: 50% !important;
}


/*treepleMap(3분할 css)*/
.threepleMap:not(.mainMap):not(.on3) {
  display: none;
}
/*vertical*/
.threepleMap.on3.threepleMap-vertical {
  display: block;
  border-collapse: collapse;
  float: left;
  width: 33.33333% !important;
  height: 100% !important;
}
/*horizonal*/
.threepleMap.on3.threepleMap-horizonal {
  display: block;
  border-collapse: collapse;
  width: 100% !important;
  height: 33.3% !important;
}
/*complex*/
.threepleMap.on3.threepleMap-complex-01,
.threepleMap.on3.threepleMap-complex-02,
.threepleMap.on3.threepleMap-complex-03,
.threepleMap.on3.threepleMap-complex-04  {
  display: block;
  border-collapse: collapse;
}
/*complex-01*/
.threepleMap.on3.threepleMap-complex-01 {
  width: 50% !important;
  float : left;
}
.threepleMap.on3.threepleMap-complex-01.d-3-1 {
  height : 100% !important;
}
.threepleMap.on3.threepleMap-complex-01.d-3-2 ,
.threepleMap.on3.threepleMap-complex-01.d-3-3{
  height : 50% !important;
}
/*complex-02*/
.threepleMap.on3.threepleMap-complex-02 {
  height: 50% !important;
  float : left;
}
.threepleMap.on3.threepleMap-complex-02.d-3-1 {
  width : 100% !important;
}
.threepleMap.on3.threepleMap-complex-02.d-3-2 ,
.threepleMap.on3.threepleMap-complex-02.d-3-3{
  width : 50% !important;
}
/*complex-03*/
.threepleMap.on3.threepleMap-complex-03 {
  width: 50% !important;
  float : right;
}
.threepleMap.on3.threepleMap-complex-03.d-3-2 ,
.threepleMap.on3.threepleMap-complex-03.d-3-3{
  height : 50% !important;
}
.threepleMap.on3.threepleMap-complex-03.d-3-1 {
  height : 100% !important;
}
/*complex-04*/
.threepleMap.on3.threepleMap-complex-04 {
  height: 50% !important;
  float : left;
}
.threepleMap.on3.threepleMap-complex-04.d-3-1 ,
.threepleMap.on3.threepleMap-complex-04.d-3-2{
  width : 50% !important;
}
.threepleMap.on3.threepleMap-complex-04.d-3-3 {
  width : 100% !important;
}


/*quadMap(4분할 css)*/
.quadMap:not(.mainMap):not(.on4) {
  display: none;
}
/*horizonal*/
.quadMap.on4.quadMap-horizonal {
  display: block;
  border-collapse: collapse;
  width: 100% !important;
  height: 25% !important;
}
/*vertical*/
.quadMap.on4.quadMap-vertical {
  display: block;
  border-collapse: collapse;
  float: left;
  width: 25% !important;
  height: 100% !important;
}
/*complex*/
.quadMap.on4.quadMap-complex {
  display: block;
  border-collapse: collapse;
  float: left;
  width: 50% !important;
  height: 50% !important;
}




/*zoom slider*/
.h-slider {
  width: 100px;
  margin: 2px;
  text-align: justify !important;
  padding-right: 20px;
}
.h-slider-btn {
  width: 20px;
  background-color: #e6e6ea;
}

/* #layerTOC {
  background-color: #808080;
  width: 100px;
  height: 100px;
  position: absolute;
  z-index: 99999;
} */

#positionStr {
  float: right;
  font-size: 15px;
  font-weight: bold;
}
/* TOC(레이어) 파트 추가 */
.layer-Aside {
  position: absolute;
  left: -220px;
  top: 140px;
  width: 300px;
  height: 100%;
  z-index: 99999;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}

.layer-Aside.openAside {
  left: 80px;
}

.layer-Aside {
  z-index: 1000;
  border-right: 1px solid #c8c8c8;
  background-color: #fff;
}

.area-layerTitle {
  position: relative;
  padding: 0 15px;
  background-color: #36b3e6;
  border-bottom: 1px solid #c8c8c8;
}

.area-layerTitle p {
  display: inline-block;
  line-height: 42px;
  color: #fff;
  font-size: 15px;
  font-weight: bold;
  letter-spacing: -0.5px;
}

.asideBtnGrp {
  position: absolute;
  right: 15px;
  top: 50%;
  margin-top: -13px;
}

.asideBtnGrp button {
  vertical-align: top; /* margin-left: 2px; */
  width: 25px;
  height: 26px;
  background-repeat: no-repeat;
  background-position: center center;
  text-indent: -9999px;
}

.area-asideScroll {
  overflow: auto;
  height: calc(100% - 43px);
}

.asideList {
  padding: 5px 8px;
}

.asideList li {
  position: relative;
  margin-bottom: 5px;
  padding: 15px 5px 0;
  height: 60px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #e7e7e7;
}

.asideList li.selected {
  background-color: #f5f8fc;
}

.sortable li {
  cursor: move;
}

.btn-typeGrp {
  width: calc(100% - 95px);
  height: 28px;
  text-align: left;
}

.btn-typeGrp .textArea {
  display: inline-block;
  width: calc(100% - 40px);
  line-height: 28px;
  vertical-align: middle;
  color: #555;
  font-size: 13px;
  overflow: hidden;
  white-space: nowrap;
  -ms-text-overflow: ellipsis;
  text-overflow: ellipsis;
}

.asideListBtnGrp {
  display: inline-block;
  position: absolute;
  right: 20px;
  top: 50%;
  margin-top: -10px;
  height: 20px;
}

.asideListBtnGrp button {
  float: left;
  width: 25px;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center center;
  text-indent: -9999px;
}

.menu-select {
  position: absolute;
  z-index: 1010;
  top: 140px;
  left: 0;
  width: 80px;
  height: 100%;
  background: #123874;
}

.menu-select li {
  height: 27px;
  /* padding-top: 53px; */
  border-bottom: 1px solid #1a58a0;
  text-align: center;
  color: #7aa1df;
  cursor: pointer;
  font-size: 10px;
}

.menu-select li:hover,
.menu-select li.selected {
  color: #fff;
}
.draggableMarker:hover {
  cursor: move;
}

.areaServerDiv {
  position: absolute;
  z-index: 99999;
  top: 10px;
  left: 10px;
}

.areaJsonDiv {
  position: absolute;
  z-index: 99999;
  top: 10px;
  left: 10px;
  /* display: none; */
}
.areaControl {
  width: 150px;
  float: left;
}
.adrressDiv {
  position: absolute;
  z-index: 99999;
  top: 145px;
}

.adrressDiv span {
  float: left;
  background: white;
  font-size: 20px;
  font-weight: bold;
}

.adrressDiv div {
  width: 35px;
  float: left;
  text-align: center;
  border: 3px solid rgba(255, 0, 0, 0.7);
  background: gray;
}

.subToolbarGrp.subToolbarGrp_scale {
  position: absolute;
  right: 10px;
  bottom: 15px;
}
.btn-control-scale {
  width: 100%;
  height: 17px;
}
.btn-control-scale-value {
  font-size: 12px !important;
  border: 0px;
}
.btn-control-scale-input{
  width:100%;
}
#odf-control-scale-input{
  width : 48px;
}

.img-test{
  background-image: url(images/test.png);
  width: 100px;
  height: 50px;
  border : solid 2px;

}

.ol-attribution.ol-logo-only,
.ol-attribution.ol-uncollapsible {
  max-width: calc(100% - 3em);
}

.ol-attribution.ol-uncollapsible {
  bottom: 0;
  right: 0;
  border-radius: 4px 0 0;
}

.ol-attribution.ol-control {
  text-align: right;
  bottom: .5em;
  right: .5em;
  max-width: calc(100% - 1.3em);
  display: flex;
  flex-flow: row-reverse;
  align-items: center;
  position: absolute;
  background-color: rgba(128, 128, 128, 0.25);
  border-radius: 4px;
  line-height: normal;
}

.ol-attribution:not(.ol-collapsed) {
  background: rgba(255, 255, 255, 0.75);
  ;
}

.ol-attribution button:not(:disabled) {
  flex-shrink: 0;
  cursor: pointer;
  display: block;
  margin: 1px;
  padding: 0;
  color: #666666;
  font-weight: bold;
  text-decoration: none;
  font-size: inherit;
  text-align: center;
  height: 1.375em;
  width: 1.375em;
  line-height: .4em;
  background-color: #ffffff;
  border: none;
  border-radius: 2px;
  text-transform: none;
}

.ol-attribution.ol-uncollapsible button {
  display: none;
}


.ol-attribution ul {
  margin: 0;
  padding: 1px .5em;
  color: #333333;
  text-shadow: 0 0 2px #ffffff;
  font-size: 12px;
}

.ol-attribution li {
  display: inline;
  list-style: none;
}
