{"controls": [{"name": "레이어 추가 (odf.LayerFactory.produce)", "description": "지도 위에 레이어를 추가할 수 있습니다.", "tags": ["레이어 추가", "WMS", "지도 레이어", "데이터 시각화"], "methods": ["produce(): 레이어 생성", "setMap(map): 레이어를 지도에 추가", "fit(): 레이어에 맞춰 지도 뷰 조정"], "example": "var wmsLayer = odf.LayerFactory.produce('geoserver', {\n    method: 'get',\n    server: 'https://geoserver.geon.kr/geoserver',\n    layer: 'geonpaas:L100000254',\n    service: 'wms',\n    tiled: false,\n});\nwmsLayer.setMap(map);\nwmsLayer.fit();"}, {"name": "레이어 필터 (odf.Layer.defineQuery)", "description": "레이어에 필터를 설정하여 조회합니다.", "tags": ["레이어 필터", "원하는 정보 조회"], "methods": ["defineQuery(): 질의문 정의(cql 형식)"], "example": "wmsLayer.defineQuery({\ncondition : `\"정제지번PNU\" like '11%'`, // 필터(질의문) 조건 (cql 형식)\n});"}], "layer": [{"소방서": "geonpaas:L100000258", "119 안전센터": "Wgeontest4:L100004784", "부산광역시 병원": "Wgeontest4:L100004775"}]}