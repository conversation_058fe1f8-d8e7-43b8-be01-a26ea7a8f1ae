module.exports = {

"[project]/.next-internal/server/app/(map)/api/chat/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/lib/ai/custom-middleware.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "customMiddleware": (()=>customMiddleware)
});
const customMiddleware = {
    wrapStream: async ({ doStream })=>{
        const { stream, ...rest } = await doStream();
        const transformStream = new TransformStream({
            transform (chunk, controller) {
                controller.enqueue(chunk);
            },
            flush () {}
        });
        return {
            stream: stream.pipeThrough(transformStream),
            ...rest
        };
    }
};
}}),
"[project]/lib/ai/dev-models.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Define your models here.
__turbopack_context__.s({
    "DEFAULT_MODEL_NAME": (()=>DEFAULT_MODEL_NAME),
    "getApiKeyByModelId": (()=>getApiKeyByModelId),
    "models": (()=>models)
});
const models = [
    {
        id: '지자체 공간정보 플랫폼 챗봇',
        label: '지자체 공간정보 플랫폼 챗봇',
        apiIdentifier: 'EIjFYMz0dmL2HxkQJuBifqvF',
        description: '지자체 공간정보 플랫폼 챗봇',
        apiKey: 'app-Hd682MZtRJh95QtTUe5H9aCl'
    },
    {
        id: '지도개발 어시스턴트',
        label: '지도개발 어시스턴트',
        apiIdentifier: 'EIjFYMz0dmL2HxkQJuBifqvF',
        description: '지도개발을 위한 문서를 학습한 어시스턴트',
        apiKey: 'app-EIjFYMz0dmL2HxkQJuBifqvF'
    }
];
const DEFAULT_MODEL_NAME = '지자체 공간정보 플랫폼 챗봇';
function getApiKeyByModelId(modelId) {
    const model = models.find((m)=>m.id === modelId);
    return model?.apiKey;
}
}}),
"[project]/lib/ai/index.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "customModel": (()=>customModel),
    "difyModel": (()=>difyModel),
    "difyProvider": (()=>difyProvider),
    "geon": (()=>geon),
    "geonAX4": (()=>geonAX4),
    "geonQwen25": (()=>geonQwen25),
    "openaiModel": (()=>openaiModel)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/ai@4.3.19_react@19.0.0_zod@3.24.1/node_modules/ai/dist/index.mjs [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$custom$2d$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/ai/custom-middleware.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$dev$2d$models$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/ai/dev-models.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$geon$40$0$2e$1$2e$5_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$geon$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@ai-sdk+geon@0.1.5_react@19.0.0_zod@3.24.1/node_modules/@ai-sdk/geon/dist/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$openai$40$1$2e$3$2e$22_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$openai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@ai-sdk+openai@1.3.22_zod@3.24.1/node_modules/@ai-sdk/openai/dist/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$dify$2d$ai$2d$provider$40$0$2e$1$2e$6$2f$node_modules$2f$dify$2d$ai$2d$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/dify-ai-provider@0.1.6/node_modules/dify-ai-provider/dist/index.mjs [app-route] (ecmascript)");
;
;
;
;
;
;
const geon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$geon$40$0$2e$1$2e$5_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$geon$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createGeon"])({
    baseURL: "http://**************:8005/v1",
    apiKey: "123"
});
const geonQwen25 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$geon$40$0$2e$1$2e$5_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$geon$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createGeon"])({
    baseURL: "http://**************:8002/v1",
    apiKey: "123"
});
const geonAX4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$geon$40$0$2e$1$2e$5_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$geon$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createGeon"])({
    baseURL: "http://**************:8007/v1",
    apiKey: "123"
});
const difyProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$dify$2d$ai$2d$provider$40$0$2e$1$2e$6$2f$node_modules$2f$dify$2d$ai$2d$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createDifyProvider"])({
    baseURL: "https://ai-dify.geon.kr/v1"
});
const customModel = (modelId)=>{
    // 모델 ID에 따라 적절한 Geon 인스턴스 선택
    let geonInstance = geon; // 기본값
    if (modelId.includes('Qwen2.5-14B')) {
        geonInstance = geonQwen25;
    } else if (modelId.includes('A.X-4-Light-awq')) {
        geonInstance = geonAX4;
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["wrapLanguageModel"])({
        model: geonInstance(modelId),
        middleware: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$custom$2d$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customMiddleware"]
    });
};
const difyModel = (modelId)=>{
    const model = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$dev$2d$models$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["models"].find((m)=>m.id === modelId);
    if (!model) {
        throw new Error(`Model not found: ${modelId}`);
    }
    const difyModelInstance = difyProvider(model.apiIdentifier, {
        apiKey: model.apiKey,
        responseMode: "streaming"
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["wrapLanguageModel"])({
        model: difyModelInstance,
        middleware: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$custom$2d$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customMiddleware"]
    });
};
const openaiModel = (modelId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["wrapLanguageModel"])({
        model: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$openai$40$1$2e$3$2e$22_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$openai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["openai"])(modelId),
        middleware: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$custom$2d$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customMiddleware"]
    });
};
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/app/(auth)/auth.config.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authConfig": (()=>authConfig)
});
const authConfig = {
    pages: {
        signIn: "/login",
        // verifyRequest: `/login`,
        // error: "/login", // Error code passed in query string as ?error=
        newUser: "/"
    },
    providers: [],
    callbacks: {
        authorized ({ auth, request: { nextUrl } }) {
            const isLoggedIn = !!auth?.user;
            const isOnChat = nextUrl.pathname.startsWith("/geon-2d-map");
            const isOnLogin = nextUrl.pathname.startsWith("/login");
            const isOnRoot = nextUrl.pathname === "/";
            // 루트 경로로 접근시 /geon-2d-map으로 리다이렉트
            if (isOnRoot) {
                return Response.redirect(new URL("/geon-2d-map", nextUrl));
            }
            // 로그인된 상태에서 로그인/회원가입 페이지 접근시 /geon-2d-map으로 리다이렉트
            if (isLoggedIn && isOnLogin) {
                return Response.redirect(new URL("/geon-2d-map", nextUrl));
            }
            // 로그인/회원가입 페이지는 항상 접근 가능
            if (isOnLogin) {
                return true;
            }
            // /geon-2d-map 페이지는 로그인한 사용자만 접근 가능
            if (isOnChat) {
                return isLoggedIn;
            }
            return true;
        }
    }
};
}}),
"[project]/app/(auth)/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "auth": (()=>auth),
    "handlers": (()=>handlers),
    "providerMap": (()=>providerMap),
    "providers": (()=>providers),
    "signIn": (()=>signIn),
    "signOut": (()=>signOut)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$auth$40$5$2e$0$2e$0$2d$beta$2e$26_nex_d27840fa84cd19414d096cb619ebb0b4$2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next-auth@5.0.0-beta.26_nex_d27840fa84cd19414d096cb619ebb0b4/node_modules/next-auth/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$auth$40$5$2e$0$2e$0$2d$beta$2e$26_nex_d27840fa84cd19414d096cb619ebb0b4$2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next-auth@5.0.0-beta.26_nex_d27840fa84cd19414d096cb619ebb0b4/node_modules/next-auth/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$auth$2b$core$40$0$2e$39$2e$0$2f$node_modules$2f40$auth$2f$core$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/errors.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$auth$40$5$2e$0$2e$0$2d$beta$2e$26_nex_d27840fa84cd19414d096cb619ebb0b4$2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next-auth@5.0.0-beta.26_nex_d27840fa84cd19414d096cb619ebb0b4/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$auth$2b$core$40$0$2e$39$2e$0$2f$node_modules$2f40$auth$2f$core$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$auth$292f$auth$2e$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(auth)/auth.config.ts [app-route] (ecmascript)");
;
;
;
class InvalidLoginError extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$auth$2b$core$40$0$2e$39$2e$0$2f$node_modules$2f40$auth$2f$core$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CredentialsSignin"] {
    code = "Invalid identifier or password";
}
const PRODUCTION = ("TURBOPACK compile-time value", "development") === "production";
const API_CERTIFICATE_KEY = 'tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh';
const SMT_URL = 'https://gsapi.geon.kr/smt';
async function validateLogin(userId, password) {
    const params = new URLSearchParams({
        crtfckey: API_CERTIFICATE_KEY,
        userId,
        password
    });
    const response = await fetch(`${SMT_URL}/login/validation?${params.toString()}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'crtfckey': API_CERTIFICATE_KEY
        }
    });
    const data = await response.json();
    if (!response.ok) {
        throw new Error('로그인 검증에 실패했습니다.');
    }
    return data;
}
async function getUserInfo(userId) {
    const params = new URLSearchParams({
        crtfckey: API_CERTIFICATE_KEY,
        userId
    });
    const response = await fetch(`${SMT_URL}/users/id?${params.toString()}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'crtfckey': API_CERTIFICATE_KEY
        }
    });
    const data = await response.json();
    if (!response.ok) {
        throw new Error('사용자 정보를 가져오는데 실패했습니다.');
    }
    return data;
}
const providers = [
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$auth$2b$core$40$0$2e$39$2e$0$2f$node_modules$2f40$auth$2f$core$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
        credentials: {},
        async authorize ({ id, password }) {
            try {
                // admin 계정으로 프론트엔드 로그인 허용
                const frontendUserId = process.env.FRONTEND_LOGIN_USER_ID || 'admin';
                const frontendPassword = process.env.FRONTEND_LOGIN_PASSWORD || 'password1234';
                if (id === frontendUserId && password === frontendPassword) {
                    // admin 계정으로 로그인 성공
                    return {
                        id: frontendUserId,
                        name: 'GeOn City',
                        email: '@example.com',
                        userId: frontendUserId,
                        userNm: 'GeOn City',
                        emailaddr: '@example.com',
                        userSeCode: '14',
                        userSeCodeNm: '관리자',
                        userImage: null,
                        insttCode: 'GEON',
                        insttNm: 'GeOn',
                        insttUrl: null,
                        message: '로그인 성공'
                    };
                }
                // 기존 geonuser 계정도 유지 (호환성을 위해)
                if (id === 'geonuser') {
                    // 1. 로그인 검증
                    const validation = await validateLogin(id, password);
                    if (!validation.result.isValid) {
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$auth$2b$core$40$0$2e$39$2e$0$2f$node_modules$2f40$auth$2f$core$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CredentialsSignin"](validation.result.message);
                    }
                    // 2. 유저 정보 조회
                    const userResponse = await getUserInfo(id);
                    if (userResponse.code !== 200) {
                        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$auth$2b$core$40$0$2e$39$2e$0$2f$node_modules$2f40$auth$2f$core$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CredentialsSignin"](userResponse.result.message);
                    }
                    // 3. 유저 정보 반환
                    return {
                        ...userResponse.result,
                        id: userResponse.result.userId,
                        name: userResponse.result.userNm || id,
                        email: userResponse.result.emailaddr || `${userResponse.result.userNm}`
                    };
                }
                // 허용되지 않은 계정
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$auth$2b$core$40$0$2e$39$2e$0$2f$node_modules$2f40$auth$2f$core$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CredentialsSignin"]('admin 또는 geonuser 계정으로만 로그인할 수 있습니다.');
            } catch (error) {
                console.error('Auth error:', error);
                throw error;
            }
        }
    })
];
const providerMap = providers.map((provider)=>{
    if (typeof provider === "function") {
        const providerData = provider();
        return {
            id: providerData.id,
            name: providerData.name
        };
    } else {
        return {
            id: provider.id,
            name: provider.name
        };
    }
}).filter((provider)=>provider.id !== "credentials");
const { handlers, auth, signIn, signOut } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$auth$40$5$2e$0$2e$0$2d$beta$2e$26_nex_d27840fa84cd19414d096cb619ebb0b4$2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])({
    ...__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$auth$292f$auth$2e$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authConfig"],
    providers,
    session: {
        strategy: "jwt",
        maxAge: 30 * 60
    },
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.id = user.id;
            }
            return token;
        },
        async session ({ session, token }) {
            if (session.user) {
                session.user.id = token.id;
            }
            return session;
        }
    }
});
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[project]/lib/db/schema.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "accounts": (()=>accounts),
    "accountsRelations": (()=>accountsRelations),
    "chat": (()=>chat),
    "chatMap": (()=>chatMap),
    "chatRelation": (()=>chatRelation),
    "map": (()=>map),
    "mapAccess": (()=>mapAccess),
    "mapSession": (()=>mapSession),
    "mapView": (()=>mapView),
    "mapsRelation": (()=>mapsRelation),
    "message": (()=>message),
    "sessions": (()=>sessions),
    "sessionsRelations": (()=>sessionsRelations),
    "user": (()=>user),
    "userRelations": (()=>userRelations),
    "verificationTokens": (()=>verificationTokens),
    "vote": (()=>vote)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$paralleldrive$2b$cuid2$40$2$2e$2$2e$2$2f$node_modules$2f40$paralleldrive$2f$cuid2$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@paralleldrive+cuid2@2.2.2/node_modules/@paralleldrive/cuid2/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$relations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/drizzle-orm@0.31.4_@neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/relations.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$boolean$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/drizzle-orm@0.31.4_@neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/pg-core/columns/boolean.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$indexes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/drizzle-orm@0.31.4_@neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/pg-core/indexes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/drizzle-orm@0.31.4_@neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/pg-core/columns/integer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$json$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/drizzle-orm@0.31.4_@neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/pg-core/columns/json.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/drizzle-orm@0.31.4_@neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/pg-core/table.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$primary$2d$keys$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/drizzle-orm@0.31.4_@neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/pg-core/primary-keys.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/drizzle-orm@0.31.4_@neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/pg-core/columns/text.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$timestamp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/drizzle-orm@0.31.4_@neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/pg-core/columns/timestamp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/drizzle-orm@0.31.4_@neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/pg-core/columns/uuid.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$varchar$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/drizzle-orm@0.31.4_@neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/pg-core/columns/varchar.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$provider$2d$utils$40$2$2e$2$2e$8_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs [app-route] (ecmascript)");
;
;
;
;
const user = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pgTable"])("user", {
    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("id").primaryKey().$defaultFn(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$paralleldrive$2b$cuid2$40$2$2e$2$2e$2$2f$node_modules$2f40$paralleldrive$2f$cuid2$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createId"])()),
    name: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("name"),
    // if you are using Github OAuth, you can get rid of the username attribute (that is for Twitter OAuth)
    username: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("username"),
    gh_username: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("gh_username"),
    email: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("email").unique(),
    emailVerified: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$timestamp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["timestamp"])("emailVerified", {
        mode: "date"
    }),
    image: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("image"),
    createdAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$timestamp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["timestamp"])("createdAt", {
        mode: "date"
    }).defaultNow().notNull(),
    updatedAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$timestamp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["timestamp"])("updatedAt", {
        mode: "date"
    }).notNull().$onUpdate(()=>new Date())
});
const sessions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pgTable"])("sessions", {
    sessionToken: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("sessionToken").primaryKey(),
    userId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("userId").notNull().references(()=>user.id, {
        onDelete: "cascade",
        onUpdate: "cascade"
    }),
    expires: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$timestamp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["timestamp"])("expires", {
        mode: "date"
    }).notNull()
}, (table)=>{
    return {
        userIdIdx: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$indexes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["index"])().on(table.userId)
    };
});
const verificationTokens = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pgTable"])("verificationTokens", {
    identifier: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("identifier").notNull(),
    token: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("token").notNull().unique(),
    expires: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$timestamp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["timestamp"])("expires", {
        mode: "date"
    }).notNull()
}, (table)=>{
    return {
        compositePk: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$primary$2d$keys$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["primaryKey"])({
            columns: [
                table.identifier,
                table.token
            ]
        })
    };
});
const accounts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pgTable"])("accounts", {
    userId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("userId").notNull().references(()=>user.id, {
        onDelete: "cascade",
        onUpdate: "cascade"
    }),
    type: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("type").notNull(),
    provider: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("provider").notNull(),
    providerAccountId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("providerAccountId").notNull(),
    refresh_token: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("refresh_token"),
    refreshTokenExpiresIn: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])("refresh_token_expires_in"),
    access_token: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("access_token"),
    expires_at: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])("expires_at"),
    token_type: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("token_type"),
    scope: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("scope"),
    id_token: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("id_token"),
    session_state: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("session_state"),
    oauth_token_secret: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("oauth_token_secret"),
    oauth_token: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("oauth_token")
}, (table)=>{
    return {
        userIdIdx: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$indexes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["index"])().on(table.userId),
        compositePk: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$primary$2d$keys$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["primaryKey"])({
            columns: [
                table.provider,
                table.providerAccountId
            ]
        })
    };
});
const chat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pgTable"])("Chat", {
    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("id").primaryKey(),
    createdAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$timestamp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["timestamp"])("createdAt").notNull(),
    title: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("title").notNull().default('New Chat'),
    userId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("userId").notNull(),
    visibility: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$varchar$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["varchar"])("visibility", {
        enum: [
            "public",
            "private"
        ]
    }).notNull().default("private")
});
const message = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pgTable"])("Message", {
    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["uuid"])("id").primaryKey().notNull().defaultRandom(),
    chatId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("chatId").notNull().references(()=>chat.id),
    role: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$varchar$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["varchar"])("role").notNull(),
    content: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$json$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["json"])("content"),
    parts: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$json$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["json"])("parts"),
    attachments: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$json$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["json"])("attachments"),
    createdAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$timestamp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["timestamp"])("createdAt").notNull()
});
const vote = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pgTable"])("Vote", {
    chatId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("chatId").notNull().references(()=>chat.id),
    messageId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["uuid"])("messageId").notNull().references(()=>message.id),
    isUpvoted: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$boolean$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["boolean"])("isUpvoted").notNull()
}, (table)=>{
    return {
        pk: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$primary$2d$keys$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["primaryKey"])({
            columns: [
                table.chatId,
                table.messageId
            ]
        })
    };
});
const map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pgTable"])("map", {
    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("id").primaryKey().notNull().$defaultFn(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$provider$2d$utils$40$2$2e$2$2e$8_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateId"])()),
    name: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("name").notNull(),
    createdBy: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("userId").notNull(),
    createdAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$timestamp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["timestamp"])("createdAt").notNull().defaultNow(),
    updatedAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$timestamp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["timestamp"])("updatedAt").notNull().defaultNow(),
    isPublic: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$boolean$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["boolean"])("isPublic").default(false),
    // 공유되는 핵심 상태
    layers: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$json$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["json"])("layers").notNull(),
    version: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])("version").notNull().default(1)
});
const mapView = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pgTable"])("map_view", {
    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("id").notNull().$defaultFn(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$provider$2d$utils$40$2$2e$2$2e$8_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateId"])()),
    mapId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("mapId").notNull().references(()=>map.id, {
        onDelete: "cascade"
    }),
    userId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("userId").notNull(),
    center: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$json$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["json"])("center").notNull(),
    zoom: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["integer"])("zoom").notNull(),
    basemap: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("basemap").notNull(),
    updatedAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$timestamp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["timestamp"])("updatedAt").notNull().defaultNow()
}, (table)=>{
    return {
        compoundKey: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$primary$2d$keys$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["primaryKey"])({
            columns: [
                table.mapId,
                table.userId
            ]
        }),
        idIdx: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$indexes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["index"])("map_view_id_idx").on(table.id)
    };
});
const chatMap = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pgTable"])("chat_map", {
    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("id").primaryKey().notNull().$defaultFn(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$provider$2d$utils$40$2$2e$2$2e$8_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateId"])()),
    chatId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("chatId").notNull().references(()=>chat.id, {
        onDelete: "cascade"
    }),
    mapId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("mapId").notNull().references(()=>map.id, {
        onDelete: "restrict"
    }),
    createdAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$timestamp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["timestamp"])("createdAt").notNull().defaultNow()
});
const mapAccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pgTable"])("map_access", {
    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("id").primaryKey().notNull().$defaultFn(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$provider$2d$utils$40$2$2e$2$2e$8_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateId"])()),
    mapId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("mapId").notNull().references(()=>map.id, {
        onDelete: "cascade"
    }),
    userId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("userId").notNull(),
    accessType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("accessType").notNull(),
    createdAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$timestamp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["timestamp"])("createdAt").notNull().defaultNow()
});
const mapSession = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$table$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pgTable"])("map_session", {
    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("id").primaryKey().notNull().$defaultFn(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$provider$2d$utils$40$2$2e$2$2e$8_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateId"])()),
    mapId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("mapId").notNull().references(()=>map.id, {
        onDelete: "cascade"
    }),
    userId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("userId").notNull(),
    isActive: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$boolean$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["boolean"])("isActive").default(true),
    lastActiveAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$timestamp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["timestamp"])("lastActiveAt").notNull().defaultNow(),
    // 선택적 view 동기화 설정
    syncView: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$boolean$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["boolean"])("syncView").default(false),
    followingUserId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["text"])("followingUserId")
});
const mapsRelation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$relations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["relations"])(map, ({ many })=>({
        views: many(mapView),
        access: many(mapAccess),
        sessions: many(mapSession),
        chats: many(chatMap)
    }));
const chatRelation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$relations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["relations"])(chat, ({ many })=>({
        maps: many(chatMap)
    }));
const sessionsRelations = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$relations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["relations"])(sessions, ({ one })=>({
        user: one(user, {
            references: [
                user.id
            ],
            fields: [
                sessions.userId
            ]
        })
    }));
const accountsRelations = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$relations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["relations"])(accounts, ({ one })=>({
        user: one(user, {
            references: [
                user.id
            ],
            fields: [
                accounts.userId
            ]
        })
    }));
const userRelations = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$relations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["relations"])(user, ({ many })=>({
        accounts: many(accounts),
        sessions: many(sessions)
    }));
}}),
"[externals]/pg [external] (pg, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("pg", () => require("pg"));

module.exports = mod;
}}),
"[project]/lib/db/index.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/pg [external] (pg, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$node$2d$postgres$2f$driver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/drizzle-orm@0.31.4_@neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/node-postgres/driver.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/db/schema.ts [app-route] (ecmascript)");
;
;
;
const pool = new __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__cjs$29$__["Pool"]({
    connectionString: process.env.POSTGRES_URL
});
const db = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$node$2d$postgres$2f$driver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["drizzle"])(pool, {
    schema: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    logger: true
});
const __TURBOPACK__default__export__ = db;
}}),
"[project]/lib/db/queries.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// db/queries.ts
__turbopack_context__.s({
    "deleteChatById": (()=>deleteChatById),
    "deleteMapById": (()=>deleteMapById),
    "deleteMessagesByChatIdAfterTimestamp": (()=>deleteMessagesByChatIdAfterTimestamp),
    "getChatById": (()=>getChatById),
    "getChatsByUserId": (()=>getChatsByUserId),
    "getMapById": (()=>getMapById),
    "getMapsByUserId": (()=>getMapsByUserId),
    "getMessageById": (()=>getMessageById),
    "getMessagesByChatId": (()=>getMessagesByChatId),
    "getVotesByChatId": (()=>getVotesByChatId),
    "hasMapAccess": (()=>hasMapAccess),
    "saveChat": (()=>saveChat),
    "saveMap": (()=>saveMap),
    "saveMessages": (()=>saveMessages),
    "shareMap": (()=>shareMap),
    "updateChatVisiblityById": (()=>updateChatVisiblityById),
    "updateMapView": (()=>updateMapView),
    "voteMessage": (()=>voteMessage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/db/schema.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$select$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/drizzle-orm@0.31.4_@neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/sql/expressions/select.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/drizzle-orm@0.31.4_@neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/drizzle-orm@0.31.4_@neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/sql/sql.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/db/index.ts [app-route] (ecmascript)");
;
;
;
async function saveChat({ id, userId, title }) {
    try {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chat"]).values({
            id,
            createdAt: new Date(),
            userId,
            title
        });
    } catch (error) {
        console.error('Failed to save chat in database');
        throw error;
    }
}
async function saveMessages({ messages }) {
    try {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["message"]).values(messages);
    } catch (error) {
        console.error('Failed to save messages in database', error);
        throw error;
    }
}
async function deleteChatById({ id }) {
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["vote"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["vote"].chatId, id));
        await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["message"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["message"].chatId, id));
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chat"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chat"].id, id));
    } catch (error) {
        console.error('Failed to delete chat by id from database');
        throw error;
    }
}
async function getChatsByUserId({ id }) {
    try {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chat"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chat"].userId, id)).orderBy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$select$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["desc"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chat"].createdAt));
    } catch (error) {
        console.error("Failed to get chats by user from database");
        throw error;
    }
}
async function getChatById({ id }) {
    try {
        const [selectedChat] = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chat"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chat"].id, id));
        return selectedChat;
    } catch (error) {
        console.error("Failed to get chat by id from database");
        throw error;
    }
}
async function getMessageById({ id }) {
    try {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["message"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["message"].id, id));
    } catch (error) {
        console.error('Failed to get message by id from database');
        throw error;
    }
}
async function getMessagesByChatId({ id }) {
    try {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["message"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["message"].chatId, id)).orderBy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$select$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["asc"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["message"].createdAt));
    } catch (error) {
        console.error('Failed to get messages by chat id from database', error);
        throw error;
    }
}
async function voteMessage({ chatId, messageId, type }) {
    try {
        const [existingVote] = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["vote"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["vote"].messageId, messageId)));
        if (existingVote) {
            return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["vote"]).set({
                isUpvoted: type === 'up'
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["vote"].messageId, messageId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["vote"].chatId, chatId)));
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["vote"]).values({
            chatId,
            messageId,
            isUpvoted: type === 'up'
        });
    } catch (error) {
        console.error('Failed to upvote message in database', error);
        throw error;
    }
}
async function getVotesByChatId({ id }) {
    try {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["vote"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["vote"].chatId, id));
    } catch (error) {
        console.error('Failed to get votes by chat id from database', error);
        throw error;
    }
}
async function deleteMessagesByChatIdAfterTimestamp({ chatId, timestamp }) {
    try {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["message"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["message"].chatId, chatId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["gte"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["message"].createdAt, timestamp)));
    } catch (error) {
        console.error('Failed to delete messages by id after timestamp from database');
        throw error;
    }
}
async function updateChatVisiblityById({ chatId, visibility }) {
    try {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chat"]).set({
            visibility
        }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chat"].id, chatId));
    } catch (error) {
        console.error('Failed to update chat visibility in database');
        throw error;
    }
}
async function getMapsByUserId({ userId }) {
    try {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].select({
            id: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].id,
            name: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].name,
            createdAt: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].createdAt,
            updatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].updatedAt,
            layers: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].layers,
            isPublic: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].isPublic,
            activeUsers: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sql"]`
        (
          SELECT COUNT(DISTINCT ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"].userId})
          FROM ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"]}
          WHERE ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"].mapId} = ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].id}
          AND ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"].updatedAt} > NOW() - INTERVAL '5 minutes'
        )
        `.as("activeUsers"),
            view: {
                center: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"].center,
                zoom: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"].zoom,
                basemap: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"].basemap
            }
        }).from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"]).leftJoin(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapAccess"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapAccess"].mapId, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].id), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapAccess"].userId, userId))).leftJoin(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"].mapId, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].id), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"].userId, userId))).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["or"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].createdBy, userId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapAccess"].userId, userId))).orderBy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$select$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["desc"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].updatedAt));
    } catch (error) {
        if (error instanceof Error) {
            console.error("Failed to get maps by user from database:", {
                message: error.message,
                stack: error.stack
            });
        }
        throw error;
    }
}
async function getMapById({ id, userId }) {
    try {
        const [mapData] = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].select({
            id: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].id,
            name: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].name,
            createdAt: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].createdAt,
            updatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].updatedAt,
            layers: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].layers,
            isPublic: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].isPublic,
            createdBy: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].createdBy,
            activeUsers: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sql"]`
        (
          SELECT COUNT(DISTINCT ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"].userId})
          FROM ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"]}
          WHERE ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"].mapId} = ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].id}
          AND ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"].updatedAt} > NOW() - INTERVAL '5 minutes'
        )
        `.as("activeUsers")
        }).from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].id, id));
        if (!mapData) {
            throw new Error("Map not found");
        }
        // layers 배열의 style 객체 파싱
        if (Array.isArray(mapData.layers)) {
            mapData.layers = mapData.layers.map((layer)=>({
                    ...layer,
                    style: layer.style ? typeof layer.style === 'string' ? JSON.parse(layer.style) : layer.style : undefined
                }));
        }
        // 접근 권한 확인
        if (mapData.createdBy !== userId && !mapData.isPublic && !await hasMapAccess({
            mapId: id,
            userId
        })) {
            throw new Error("Forbidden");
        }
        // 사용자별 뷰 상태 조회
        const [view] = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"].mapId, id), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"].userId, userId)));
        return {
            ...mapData,
            view: view || null
        };
    } catch (error) {
        console.error("Failed to get map by id from database");
        throw error;
    }
}
async function saveMap({ id, name, layers, userId }) {
    try {
        const existingMap = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].id, id));
        if (existingMap.length > 0) {
            return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].update(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"]).set({
                name,
                layers,
                updatedAt: new Date()
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].id, id));
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"]).values({
            id,
            name,
            layers,
            createdBy: userId,
            createdAt: new Date(),
            updatedAt: new Date()
        });
    } catch (error) {
        console.error("Failed to save map in database");
        throw error;
    }
}
async function updateMapView({ mapId, userId, view }) {
    try {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"]).values({
            mapId,
            userId,
            center: view.center ?? {
                lat: 36.5,
                lng: 127.5
            },
            zoom: view.zoom ?? 7,
            basemap: view.basemap ?? "eMapBasic",
            updatedAt: new Date()
        }).onConflictDoUpdate({
            target: [
                __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"].mapId,
                __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapView"].userId
            ],
            set: {
                ...view,
                updatedAt: new Date()
            }
        });
    } catch (error) {
        console.error("Failed to update map view state");
        throw error;
    }
}
async function deleteMapById({ id, userId }) {
    try {
        const [mapData] = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].id, id));
        if (!mapData) {
            throw new Error("Map not found");
        }
        if (mapData.createdBy !== userId) {
            throw new Error("Forbidden");
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].id, id));
    } catch (error) {
        console.error("Failed to delete map from database");
        throw error;
    }
}
async function hasMapAccess({ mapId, userId }) {
    try {
        const access = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapAccess"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapAccess"].mapId, mapId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapAccess"].userId, userId)));
        return access.length > 0;
    } catch (error) {
        console.error("Failed to check map access");
        throw error;
    }
}
async function shareMap({ mapId, userId, targetUserId, accessType = "view" }) {
    try {
        // 공유 권한 확인
        const [mapData] = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].select().from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$drizzle$2d$orm$40$0$2e$31$2e$4_$40$neondat_2c4e83f03bbbbe484e5e453678a61102$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["map"].id, mapId));
        if (!mapData || mapData.createdBy !== userId) {
            throw new Error("Forbidden");
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapAccess"]).values({
            mapId,
            userId: targetUserId,
            accessType,
            createdAt: new Date()
        });
    } catch (error) {
        console.error("Failed to share map");
        throw error;
    }
}
}}),
"[project]/lib/ai/models.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Define your models here.
__turbopack_context__.s({
    "DEFAULT_MODEL_NAME": (()=>DEFAULT_MODEL_NAME),
    "getModelById": (()=>getModelById),
    "getModelCapabilities": (()=>getModelCapabilities),
    "getModelProvider": (()=>getModelProvider),
    "getReasoningDisabledMessage": (()=>getReasoningDisabledMessage),
    "models": (()=>models),
    "supportsReasoning": (()=>supportsReasoning),
    "supportsStreaming": (()=>supportsStreaming),
    "supportsTools": (()=>supportsTools),
    "supportsVision": (()=>supportsVision)
});
const models = [
    {
        id: "Qwen3-14B",
        label: "Qwen3 14B",
        apiIdentifier: "Qwen/Qwen2.5-14B",
        description: "Qwen3 14B 모델입니다.",
        provider: "geon",
        capabilities: {
            reasoning: false,
            streaming: true,
            tools: true,
            vision: false
        }
    },
    {
        id: "Qwen3-4B",
        label: "Qwen3 (추론)",
        apiIdentifier: "Qwen/Qwen3-4B",
        description: "Qwen3-4B 추론 모델입니다.",
        provider: "geon",
        capabilities: {
            reasoning: true,
            streaming: true,
            tools: true,
            vision: false
        }
    },
    // {
    //   id: 'A.X-4-Light',
    //   label: 'A.X-4 Light',
    //   apiIdentifier: 'A.X-4-Light-awq',
    //   description: 'A.X-4 Light 모델입니다.',
    //   provider: 'geon',
    //   capabilities: {
    //     reasoning: false,
    //     streaming: true,
    //     tools: true,
    //     vision: false,
    //   },
    // },
    {
        id: "gpt-4.1-nano",
        label: "GPT 4.1 Nano",
        apiIdentifier: "gpt-4.1-nano",
        description: "OpenAI의 GPT 4.1 Nano 모델입니다.",
        provider: "openai",
        capabilities: {
            reasoning: false,
            streaming: true,
            tools: true,
            vision: false
        }
    }
];
const DEFAULT_MODEL_NAME = "Qwen3-4B";
function getModelById(modelId) {
    return models.find((model)=>model.id === modelId);
}
function getModelCapabilities(modelId) {
    const model = getModelById(modelId);
    return model?.capabilities;
}
function supportsReasoning(modelId) {
    const capabilities = getModelCapabilities(modelId);
    return capabilities?.reasoning ?? false;
}
function supportsStreaming(modelId) {
    const capabilities = getModelCapabilities(modelId);
    return capabilities?.streaming ?? false;
}
function supportsTools(modelId) {
    const capabilities = getModelCapabilities(modelId);
    return capabilities?.tools ?? false;
}
function supportsVision(modelId) {
    const capabilities = getModelCapabilities(modelId);
    return capabilities?.vision ?? false;
}
function getModelProvider(modelId) {
    const model = getModelById(modelId);
    return model?.provider;
}
function getReasoningDisabledMessage(modelId) {
    const model = getModelById(modelId);
    if (!model || model.capabilities.reasoning) {
        return undefined;
    }
    // 모델별 맞춤 메시지
    switch(model.id){
        case "gpt-4.1-nano":
            return "GPT 4.1 Nano 에서 지원되지 않습니다.";
        default:
            return "현재 선택된 모델은 추론 기능을 지원하지 않습니다";
    }
}
}}),
"[project]/lib/utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "V2ChatbotId": (()=>V2ChatbotId),
    "V2ModelId": (()=>V2ModelId),
    "cn": (()=>cn),
    "convertToUIMessages": (()=>convertToUIMessages),
    "defaultChatbotId": (()=>defaultChatbotId),
    "defaultModelId": (()=>defaultModelId),
    "defaultSystemMessage": (()=>defaultSystemMessage),
    "fetcher": (()=>fetcher),
    "formatDate": (()=>formatDate),
    "generateUUID": (()=>generateUUID),
    "getMessageIdFromAnnotations": (()=>getMessageIdFromAnnotations),
    "getMostRecentUserMessage": (()=>getMostRecentUserMessage),
    "prunedMessages": (()=>prunedMessages)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$tailwind$2d$merge$40$2$2e$5$2e$5$2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/tailwind-merge@2.5.5/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-route] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$tailwind$2d$merge$40$2$2e$5$2e$5$2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatDate(input) {
    const date = new Date(input);
    return date.toLocaleDateString('korean', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
    });
}
const fetcher = async (url)=>{
    const res = await fetch(url);
    if (!res.ok) {
        // 401 에러 시 로그인 페이지로 리다이렉트
        if (res.status === 401) {
            // 클라이언트 사이드에서만 리다이렉트 실행
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
        }
        const error = new Error('An error occurred while fetching the data.');
        error.info = await res.json();
        error.status = res.status;
        throw error;
    }
    return res.json();
};
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c)=>{
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : r & 0x3 | 0x8;
        return v.toString(16);
    });
}
function convertToUIMessages(messages) {
    // Vercel AI Chatbot 예제 패턴: parts 기반으로 메시지 변환
    return messages.map((message)=>({
            id: message.id,
            parts: message.parts,
            role: message.role,
            // Note: content will soon be deprecated in @ai-sdk/react
            content: '',
            createdAt: message.createdAt,
            experimental_attachments: message.attachments ?? []
        }));
}
function getMostRecentUserMessage(messages) {
    const userMessages = messages.filter((message)=>message.role === 'user');
    return userMessages.at(-1);
}
function getMessageIdFromAnnotations(message) {
    if (!message.annotations) return message.id;
    const [annotation] = message.annotations;
    if (!annotation) return message.id;
    // @ts-expect-error messageIdFromServer is not defined in MessageAnnotation
    return annotation.messageIdFromServer;
}
const defaultSystemMessage = ``;
const defaultModelId = '2JgxF9fviqd4cezxuT4UAuJCcRP2';
const defaultChatbotId = 'chbt_CRNiaMw';
const V2ModelId = 'Lk5521ILdWe9t18yi0zcJKU0teE3';
const V2ChatbotId = 'chbt_ahiNhfU';
const prunedMessages = (messages)=>{
    // 1. 마지막 4개 메시지만 선택
    const recentMessages = messages.slice(-4);
    console.log('Pruned messages count:', recentMessages.length);
    // 2. Human-in-the-loop 도구 호출 후 사용자가 tool-result 없이 채팅한 경우 처리
    const processedMessages = recentMessages.map((message, index)=>{
        // Assistant 메시지에서 미완료된 tool-invocation 확인 (parts 배열 사용)
        if (message.role === 'assistant' && Array.isArray(message.parts)) {
            const hasIncompleteToolCall = message.parts.some((part)=>part.type === 'tool-invocation' && part.toolInvocation?.state === 'call' && !part.toolInvocation?.result);
            // 미완료된 tool-invocation이 있고, 다음 메시지가 user 메시지인 경우
            if (hasIncompleteToolCall && index < recentMessages.length - 1) {
                const nextMessage = recentMessages[index + 1];
                if (nextMessage?.role === 'user') {
                    // 미완료된 tool-invocation을 제거하고 텍스트 응답만 유지
                    const filteredParts = message.parts.filter((part)=>part.type !== 'tool-invocation' || part.type === 'tool-invocation' && part.toolInvocation?.result);
                    // 텍스트 응답이 없으면 기본 응답 추가
                    if (filteredParts.length === 0 || !filteredParts.some((p)=>p.type === 'text')) {
                        filteredParts.unshift({
                            type: 'text',
                            text: '요청을 처리하고 있습니다. 추가로 도움이 필요한 것이 있나요?'
                        });
                    }
                    return {
                        ...message,
                        parts: filteredParts
                    };
                }
            }
            // 3. 도구 결과에서 불필요한 대용량 필드 제거
            const cleanedParts = message.parts.map((part)=>{
                if (part.type === 'tool-invocation' && part.toolInvocation?.state === 'result') {
                    const toolName = part.toolInvocation?.toolName;
                    const result = part.toolInvocation.result;
                    // searchAddress, searchOrigin, searchDestination: geom, buildGeom 필드 제거
                    if ([
                        'searchAddress',
                        'searchOrigin',
                        'searchDestination'
                    ].includes(toolName)) {
                        if (result && typeof result === 'object' && result.result?.jusoList) {
                            // jusoList 배열의 각 항목에서 geom, buildGeom 필드 제거
                            const cleanedJusoList = result.result.jusoList.map((item)=>{
                                const { geom, buildGeom, ...cleanedItem } = item;
                                return cleanedItem;
                            });
                            return {
                                ...part,
                                toolInvocation: {
                                    ...part.toolInvocation,
                                    result: {
                                        ...result,
                                        result: {
                                            ...result.result,
                                            jusoList: cleanedJusoList
                                        }
                                    }
                                }
                            };
                        }
                    }
                    // searchDirections: sections 필드 제거 (상세 경로 데이터)
                    if (toolName === 'searchDirections') {
                        if (result && typeof result === 'object' && result.routes) {
                            const cleanedRoutes = result.routes.map((route)=>{
                                const { sections, ...cleanedRoute } = route;
                                return cleanedRoute;
                            });
                            return {
                                ...part,
                                toolInvocation: {
                                    ...part.toolInvocation,
                                    result: {
                                        ...result,
                                        routes: cleanedRoutes
                                    }
                                }
                            };
                        }
                    }
                    // densityAnalysis: features 필드 제거 (GeoJSON 형상 데이터)
                    if (toolName === 'performDensityAnalysis') {
                        if (result && typeof result === 'object') {
                            const { features, ...cleanedResult } = result;
                            return {
                                ...part,
                                toolInvocation: {
                                    ...part.toolInvocation,
                                    result: {
                                        ...cleanedResult,
                                        // features 배열 크기만 유지 (실제 데이터는 제거)
                                        featuresCount: Array.isArray(features) ? features.length : 0
                                    }
                                }
                            };
                        }
                    }
                }
                return part;
            });
            return {
                ...message,
                parts: cleanedParts
            };
        }
        return message;
    });
    return processedMessages;
};
}}),
"[project]/app/(map)/actions.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"406613c6f217a0744b02093d3e5a3c9a71f308ff53":"saveModelId","40764eb81a775d0486660fff2140578c0e1f884c4e":"deleteTrailingMessages","40923c7b07eb27494fa03b60c03123fcf27a6e9f34":"saveDevModelId","40dcef6630873128ac1943388c47e34dd4346a63f0":"updateChatVisibility","40f616456b88cadd42bf5c75cd38fde62390283694":"generateTitleFromUserMessage"},"",""] */ __turbopack_context__.s({
    "deleteTrailingMessages": (()=>deleteTrailingMessages),
    "generateTitleFromUserMessage": (()=>generateTitleFromUserMessage),
    "saveDevModelId": (()=>saveDevModelId),
    "saveModelId": (()=>saveModelId),
    "updateChatVisibility": (()=>updateChatVisibility)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/server/app-render/encryption.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$openai$40$1$2e$3$2e$22_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$openai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@ai-sdk+openai@1.3.22_zod@3.24.1/node_modules/@ai-sdk/openai/dist/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$queries$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/db/queries.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/ai@4.3.19_react@19.0.0_zod@3.24.1/node_modules/ai/dist/index.mjs [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/headers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-route] (ecmascript)");
;
;
;
;
;
;
async function saveModelId(model) {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    cookieStore.set('model-id', model);
}
async function saveDevModelId(model) {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    cookieStore.set('dev-model-id', model);
}
async function generateTitleFromUserMessage({ message }) {
    const { text: title } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["generateText"])({
        model: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$openai$40$1$2e$3$2e$22_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$openai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["openai"])('gpt-4o-mini'),
        system: `\n
    - you will generate a short title based on the first message a user begins a conversation with
    - ensure it is not more than 80 characters long
    - the title should be a summary of the user's message
    - do not use quotes or colons`,
        prompt: JSON.stringify(message)
    });
    return title;
}
async function deleteTrailingMessages({ id }) {
    const [message] = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$queries$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getMessageById"])({
        id
    });
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$queries$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["deleteMessagesByChatIdAfterTimestamp"])({
        chatId: message.chatId,
        timestamp: message.createdAt
    });
}
async function updateChatVisibility({ chatId, visibility }) {
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$queries$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["updateChatVisiblityById"])({
        chatId,
        visibility
    });
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    saveModelId,
    saveDevModelId,
    generateTitleFromUserMessage,
    deleteTrailingMessages,
    updateChatVisibility
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(saveModelId, "406613c6f217a0744b02093d3e5a3c9a71f308ff53", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(saveDevModelId, "40923c7b07eb27494fa03b60c03123fcf27a6e9f34", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(generateTitleFromUserMessage, "40f616456b88cadd42bf5c75cd38fde62390283694", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(deleteTrailingMessages, "40764eb81a775d0486660fff2140578c0e1f884c4e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(updateChatVisibility, "40dcef6630873128ac1943388c47e34dd4346a63f0", null);
}}),
"[project]/app/(map)/api/chat/intent-rules.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// 의도분석 규칙 및 템플릿 정의
// 헬퍼 함수들
__turbopack_context__.s({
    "INTENT_CATEGORIES": (()=>INTENT_CATEGORIES),
    "MESSAGE_GENERATION_RULES": (()=>MESSAGE_GENERATION_RULES),
    "classifyIntent": (()=>classifyIntent),
    "createIntentAnalysisPrompt": (()=>createIntentAnalysisPrompt),
    "generateMessage": (()=>generateMessage),
    "testMessageGeneration": (()=>testMessageGeneration)
});
function extractBaseKeyword(context) {
    const input = context.toLowerCase();
    // 주요 키워드 매핑
    const keywordMap = {
        '건물': [
            '건물',
            '빌딩',
            '건축물'
        ],
        '도로': [
            '도로',
            '길',
            '거리'
        ],
        '상점': [
            '상점',
            '매장',
            '점포'
        ],
        '학교': [
            '학교',
            '교육기관'
        ],
        '병원': [
            '병원',
            '의료기관'
        ],
        '공원': [
            '공원',
            '녹지'
        ]
    };
    for (const [base, variants] of Object.entries(keywordMap)){
        if (variants.some((variant)=>input.includes(variant))) {
            return base;
        }
    }
    // 지역명 제거 후 키워드 추출
    const regions = [
        '서울',
        '부산',
        '대구',
        '인천',
        '광주',
        '대전',
        '울산',
        '세종'
    ];
    let cleanInput = input;
    regions.forEach((region)=>{
        cleanInput = cleanInput.replace(region, '').trim();
    });
    return cleanInput.split(' ')[0] || '레이어';
}
function extractAttributeCondition(context) {
    const input = context.toLowerCase();
    const conditionMap = {
        '노후화된': '건축년도나 노후화',
        '오래된': '건축년도나 사용연한',
        '높은': '높이나 층수',
        '큰': '규모나 면적',
        '작은': '규모나 면적',
        '새로운': '건축년도나 준공년도',
        '최신': '건축년도나 준공년도'
    };
    for (const [pattern, condition] of Object.entries(conditionMap)){
        if (input.includes(pattern)) {
            return condition;
        }
    }
    return '특정 조건';
}
function extractColorCondition(context) {
    const input = context.toLowerCase();
    const colors = [
        '흰색',
        '노란색',
        '빨간색',
        '파란색',
        '초록색',
        '검은색'
    ];
    for (const color of colors){
        if (input.includes(color)) {
            return color;
        }
    }
    return null;
}
const INTENT_CATEGORIES = {
    UNSUPPORTED_FEATURE: {
        priority: 1,
        keywords: [
            "근처",
            "주변",
            "실시간",
            "업로드",
            "전체목록",
            "CSV",
            "파일",
            "데이터 추가",
            "내 데이터"
        ],
        description: "지원하지 않는 기능 요청"
    },
    DENSITY_ANALYSIS: {
        priority: 2,
        keywords: [
            "밀도",
            "분포",
            "집중도",
            "히트맵",
            "밀집"
        ],
        constraints: "점 타입 레이어에서만 가능",
        description: "공간 밀도 분석"
    },
    LAYER_STYLE: {
        priority: 3,
        keywords: [
            "색상",
            "색깔",
            "스타일",
            "투명",
            "굵기",
            "파란색",
            "빨간색",
            "노란색",
            "초록색"
        ],
        description: "레이어 스타일 변경"
    },
    LAYER_ADD: {
        priority: 4,
        keywords: [
            "추가",
            "보여줘",
            "찾아서",
            "검색해서"
        ],
        description: "레이어 추가",
        // 속성 기반 요청 패턴 추가
        attributePatterns: [
            "노후화된",
            "오래된",
            "낡은",
            "높은",
            "큰",
            "작은",
            "새로운",
            "최신",
            "~년도 이후",
            "~년도 이전",
            "~이상",
            "~이하",
            "~보다"
        ],
        colorPatterns: [
            "흰색",
            "노란색",
            "빨간색",
            "파란색",
            "초록색",
            "검은색"
        ]
    },
    LAYER_REMOVE: {
        priority: 4,
        keywords: [
            "삭제",
            "제거",
            "없애"
        ],
        description: "레이어 삭제"
    },
    LAYER_FILTER: {
        priority: 4,
        keywords: [
            "필터",
            "조건",
            "범위"
        ],
        description: "레이어 필터링"
    },
    LAYER_LIST: {
        priority: 4,
        keywords: [
            "목록",
            "리스트",
            "어떤 레이어"
        ],
        description: "레이어 목록 조회"
    },
    MAP_CONTROL: {
        priority: 5,
        keywords: [
            "확대",
            "축소",
            "이동",
            "중심",
            "북쪽",
            "남쪽",
            "동쪽",
            "서쪽"
        ],
        description: "지도 조작 및 제어"
    },
    BASEMAP_CHANGE: {
        priority: 5,
        keywords: [
            "배경지도",
            "기본지도",
            "항공지도",
            "위성지도"
        ],
        description: "배경지도 변경"
    },
    NAVIGATION: {
        priority: 6,
        keywords: [
            "찾기",
            "검색",
            "길찾기",
            "경로",
            "위치",
            "어디"
        ],
        description: "장소 검색 및 길찾기"
    },
    GENERAL_CONVERSATION: {
        priority: 7,
        keywords: [],
        description: "일반 대화 및 기타"
    }
};
const MESSAGE_GENERATION_RULES = {
    // 메시지 생성 원칙
    principles: [
        "구체적인 작업 단계 제시",
        "필요한 도구와 순서 명시",
        "제약사항 및 주의사항 포함",
        "내부 프로세스 언급 금지"
    ],
    // 카테고리별 메시지 템플릿
    templates: {
        DENSITY_ANALYSIS: (context)=>`${context}의 밀도분석을 수행하겠습니다. 밀도분석은 점 타입 레이어에서만 가능하므로, 점 타입 레이어를 검색하여 선택한 후 공간적 밀집도를 분석하고 히트맵으로 시각화하겠습니다.`,
        LAYER_STYLE: (context)=>`${context} 스타일을 변경하겠습니다. 현재 지도 상태를 확인하여 대상 레이어가 있는지 확인하고, 없으면 레이어를 검색하여 추가한 후 요청된 스타일을 적용하겠습니다.`,
        LAYER_ADD: (context, conversationContext)=>{
            // 대화 컨텍스트에서 워크플로우 상태 분석
            const hasChooseOptionResult = conversationContext?.includes('chooseOption') || conversationContext?.includes('사용자가 선택한') || conversationContext?.includes('선택된 레이어');
            // 속성 기반 요청인지 확인
            const attributePatterns = INTENT_CATEGORIES.LAYER_ADD.attributePatterns || [];
            const colorPatterns = INTENT_CATEGORIES.LAYER_ADD.colorPatterns || [];
            const hasAttributePattern = attributePatterns.some((pattern)=>context.toLowerCase().includes(pattern.toLowerCase()));
            const hasColorPattern = colorPatterns.some((pattern)=>context.toLowerCase().includes(pattern.toLowerCase()));
            // 워크플로우 상태별 메시지 생성
            if (hasChooseOptionResult && (hasAttributePattern || hasColorPattern)) {
                // 레이어가 선택된 상태 + 속성 조건이 있는 경우
                const attributeCondition = extractAttributeCondition(context);
                const colorCondition = extractColorCondition(context);
                let message = `선택한 레이어를 추가하고, ${attributeCondition} 기준으로`;
                if (colorCondition) {
                    message += ` ${colorCondition}으로 표시해야 합니다.`;
                } else {
                    message += ` 필터링하여 표시해야 합니다.`;
                }
                message += ` 정확한 기준을 위해 속성 정보를 조회하고 사용자에게 확인을 요청하세요.`;
                return message;
            }
            if (hasAttributePattern || hasColorPattern) {
                // 속성 기반 요청이지만 레이어가 아직 선택되지 않은 경우
                const baseKeyword = extractBaseKeyword(context);
                const attributeCondition = extractAttributeCondition(context);
                const colorCondition = extractColorCondition(context);
                let message = `${baseKeyword} 관련 레이어를 검색하고, ${attributeCondition} 기준에 따라 필터링하여`;
                if (colorCondition) {
                    message += ` ${colorCondition}으로 표시해주세요.`;
                } else {
                    message += ` 지도에 표시해주세요.`;
                }
                return message;
            }
            // 일반적인 레이어 추가 요청
            return `${context} 레이어를 검색하여 지도에 추가하겠습니다. 키워드로 관련 레이어를 찾아 사용자가 선택할 수 있도록 목록을 제공하겠습니다.`;
        },
        LAYER_REMOVE: (context)=>`${context} 레이어를 삭제하겠습니다. 현재 지도에서 해당 레이어를 찾아 제거하겠습니다.`,
        LAYER_FILTER: (context)=>`${context} 조건으로 레이어를 필터링하겠습니다. 해당 레이어의 속성 정보를 확인하여 조건에 맞는 데이터만 표시하겠습니다.`,
        LAYER_LIST: ()=>`현재 지도에 추가된 레이어 목록을 조회하겠습니다.`,
        MAP_CONTROL: (context)=>`지도 ${context} 작업을 수행하겠습니다.`,
        BASEMAP_CHANGE: (context)=>`배경지도를 ${context}로 변경하겠습니다. 사용 가능한 배경지도 옵션을 제공하겠습니다.`,
        NAVIGATION: (context)=>`${context} 관련 장소 검색 및 길찾기를 수행하겠습니다.`,
        UNSUPPORTED_FEATURE: (context)=>`${context} 기능은 현재 지원하지 않습니다. 현재 지원하는 기능들을 안내하고 대안을 제시하겠습니다.`,
        GENERAL_CONVERSATION: (context)=>`${context}에 대해 답변드리겠습니다.`
    }
};
function classifyIntent(userInput) {
    const input = userInput.toLowerCase();
    // 우선순위 순으로 키워드 매칭
    const categories = Object.entries(INTENT_CATEGORIES).sort(([, a], [, b])=>a.priority - b.priority);
    for (const [intent, config] of categories){
        if (config.keywords.length === 0) continue; // GENERAL_CONVERSATION은 마지막에 처리
        const hasKeyword = config.keywords.some((keyword)=>input.includes(keyword.toLowerCase()));
        if (hasKeyword) {
            return intent;
        }
    }
    return 'GENERAL_CONVERSATION';
}
function generateMessage(intent, userInput, conversationContext) {
    const template = MESSAGE_GENERATION_RULES.templates[intent];
    if (typeof template === 'function') {
        return template(userInput, conversationContext);
    }
    return `사용자 요청을 처리하겠습니다: ${userInput}`;
}
function createIntentAnalysisPrompt() {
    return `
당신은 사용자 요청을 분석하여 다음 중 하나로 분류합니다:

**의도 카테고리:**
- LAYER_*: 레이어 관련 작업 (추가/삭제/스타일/필터)
- MAP_*: 지도 조작 (확대/축소/이동/배경지도)
- NAVIGATION: 장소 검색/길찾기
- DENSITY_ANALYSIS: 밀도 분석 (점 타입 레이어만 가능)
- UNSUPPORTED_FEATURE: 지원 불가 기능
- GENERAL_CONVERSATION: 일반 대화

**지원 불가 키워드:** "근처", "주변", "실시간", "업로드", "전체목록", "CSV", "파일"

**워크플로우 상태 인식:**
- 이전 대화에서 getLayerList, chooseOption 등의 도구 호출 결과를 확인하세요
- 레이어가 이미 선택된 상태인지, 아직 검색 단계인지 파악하세요
- 현재 워크플로우 단계에 맞는 구체적인 다음 작업을 지시하세요

**속성 기반 레이어 요청 식별:**
- 형용사: "노후화된", "높은", "오래된", "새로운", "큰", "작은"
- 색상 지정: "흰색으로", "노란색으로", "빨간색으로"
- 이런 패턴이 감지되면 LAYER_ADD로 분류하고 구체적인 필터링 지시 생성

**응답 형식:** {"intent": "CATEGORY", "message": "작업 지시"}

**메시지 작성 원칙:**
- **레이어 선택 완료 + 속성 조건**: "선택한 레이어를 추가하고, X 기준으로 표시해야 합니다. 속성 정보를 조회하고 사용자에게 확인을 요청하세요."
- **속성 기반 요청 (초기)**: "X 관련 레이어를 검색하고, Y 기준에 따라 필터링하여 Z로 표시해주세요"
- **일반 레이어 요청**: "X 레이어를 검색하여 지도에 추가하겠습니다"
- 구체적인 작업 단계와 도구 사용 계획 제시
- 제약사항 명시 (예: 밀도분석은 점 타입만 가능)

사용자 요청을 분석하여 적절한 의도로 분류하고 다음 에이전트를 위한 명확한 작업 지시를 생성하세요.
  `.trim();
}
function testMessageGeneration(userInput) {
    const intent = classifyIntent(userInput);
    return generateMessage(intent, userInput);
} // 예시 테스트
 // console.log(testMessageGeneration("서울의 노후화된 건물을 보여줘"));
 // 예상 결과: "건물 관련 레이어를 검색하고, 건축년도나 노후화 기준에 따라 필터링하여 지도에 표시해주세요."
 // console.log(testMessageGeneration("서울의 노후화된 건물을 흰색으로 보여줘"));
 // 예상 결과: "건물 관련 레이어를 검색하고, 건축년도나 노후화 기준에 따라 필터링하여 흰색으로 표시해주세요."
}}),
"[project]/lib/api-config.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * API 설정 및 인증 정보를 관리하는 유틸리티
 */ __turbopack_context__.s({
    "addAuthToParams": (()=>addAuthToParams),
    "getApiConfig": (()=>getApiConfig),
    "getApiHeaders": (()=>getApiHeaders),
    "getApiUserId": (()=>getApiUserId)
});
const getApiConfig = ()=>{
    const baseUrl = process.env.GEON_API_BASE_URL || "https://gsapi.geon.kr";
    // MCP 서버 자체 API 키 사용 (클라이언트 토큰과 별개)
    const apiKey = process.env.GEON_API_KEY;
    if (!apiKey) {
        console.warn("GEON_API_KEY가 설정되지 않았습니다.");
    }
    // 백엔드 API 요청용 계정 정보 (환경변수에서 가져오거나 기본값 사용)
    const apiUserId = process.env.GEON_API_USER_ID || 'geonuser';
    const apiUserPassword = process.env.GEON_API_USER_PASSWORD || 'wavus1234!';
    return {
        baseUrl,
        headers: {
            crtfckey: apiKey || ""
        },
        auth: {
            userId: apiUserId,
            password: apiUserPassword
        }
    };
};
const addAuthToParams = (params, config)=>{
    const apiConfig = config || getApiConfig();
    // API 키 추가
    if (apiConfig.headers.crtfckey) {
        params.append("crtfckey", apiConfig.headers.crtfckey);
    }
    return params;
};
const getApiHeaders = (config)=>{
    const apiConfig = config || getApiConfig();
    return {
        "Content-Type": "application/json",
        ...apiConfig.headers
    };
};
const getApiUserId = (config)=>{
    const apiConfig = config || getApiConfig();
    return apiConfig.auth.userId;
};
}}),
"[project]/app/(map)/api/chat/tools.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HILTools": (()=>HILTools),
    "changeBasemap": (()=>changeBasemap),
    "chooseOption": (()=>chooseOption),
    "createLayerFilter": (()=>createLayerFilter),
    "default": (()=>__TURBOPACK__default__export__),
    "generateCategoricalStyle": (()=>generateCategoricalStyle),
    "getBasemapList": (()=>getBasemapList),
    "getLayerAttributesCount": (()=>getLayerAttributesCount),
    "getLayerList": (()=>getLayerList),
    "moveMapByDirection": (()=>moveMapByDirection),
    "performDensityAnalysis": (()=>performDensityAnalysis),
    "removeLayer": (()=>removeLayer),
    "searchAddressOptimized": (()=>searchAddressOptimized),
    "searchDestination": (()=>searchDestination),
    "searchDirectionsOptimized": (()=>searchDirectionsOptimized),
    "searchOrigin": (()=>searchOrigin),
    "setMapCenter": (()=>setMapCenter),
    "setMapZoom": (()=>setMapZoom),
    "updateLayerStyle": (()=>updateLayerStyle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@geon-ai+tools@0.0.11_react@19.0.0/node_modules/@geon-ai/tools/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/ai@4.3.19_react@19.0.0_zod@3.24.1/node_modules/ai/dist/index.mjs [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$openai$40$1$2e$3$2e$22_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$openai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@ai-sdk+openai@1.3.22_zod@3.24.1/node_modules/@ai-sdk/openai/dist/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.24.1/node_modules/zod/lib/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api-config.ts [app-route] (ecmascript)");
;
;
;
;
;
// 베이스맵 목록 정의
const baseMap = {
    eMapBasic: "바로e맵 일반지도",
    eMapAIR: "바로e맵 항공지도",
    eMapColor: "바로e맵 색각지도",
    eMapWhite: "바로e맵 백지도"
};
const performDensityAnalysis = {
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["performDensityAnalysis"],
    experimental_toToolResultContent: (result)=>{
        // LLM에게는 텍스트 형태의 요약된 정보만 전달 (대용량 GeoJSON 데이터 제외)
        const featureCount = result.features?.length || 0;
        const analysisType = result.type === "FeatureCollection" ? "공간 밀도 분석" : "밀도 분석";
        const summary = `${analysisType}이 완료되었습니다. 총 ${featureCount}개의 공간 데이터를 분석하여 밀도 분포를 계산했습니다. 분석 결과가 지도에 히트맵 형태로 시각화되었습니다.`;
        return [
            {
                type: "text",
                text: summary
            }
        ];
    }
};
const searchAddressOptimized = {
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["searchAddress"],
    experimental_toToolResultContent: (result)=>{
        // LLM에게는 핵심 정보만 전달 (상세한 메타데이터 제외)
        if (!result.result?.jusoList?.length) {
            return [
                {
                    type: "text",
                    text: "검색 결과가 없습니다. 다른 키워드로 다시 검색해보세요."
                }
            ];
        }
        const addresses = result.result.jusoList;
        const addressCount = addresses.length;
        // 최대 3개까지만 LLM에 전달
        const topAddresses = addresses.slice(0, 3).map((addr)=>({
                roadAddr: addr.roadAddr,
                buildName: addr.buildName || addr.poiName,
                buildLo: addr.buildLo,
                buildLa: addr.buildLa
            }));
        const summary = `주소 검색이 완료되었습니다. 총 ${addressCount}개의 위치를 찾았습니다.`;
        let addressInfo = topAddresses.map((addr, index)=>`${index + 1}. ${addr.roadAddr}${addr.buildName ? ` (${addr.buildName})` : ""} - 좌표: ${addr.buildLo},${addr.buildLa}`).join("\n");
        if (addressCount > 3) {
            addressInfo += `\n... 외 ${addressCount - 3}개 추가 결과`;
        }
        return [
            {
                type: "text",
                text: `${summary}\n\n${addressInfo}`
            }
        ];
    }
};
const searchOrigin = {
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["searchAddress"],
    description: "출발지 위치를 검색합니다. 경로 탐색의 시작점이 되는 주소나 건물명을 검색하여 좌표 정보를 얻습니다.",
    experimental_toToolResultContent: (result)=>{
        if (!result.result?.jusoList?.length) {
            return [
                {
                    type: "text",
                    text: "출발지를 찾을 수 없습니다. 다른 키워드로 다시 검색해보세요."
                }
            ];
        }
        const addresses = result.result.jusoList;
        const addressCount = addresses.length;
        const topAddresses = addresses.slice(0, 3).map((addr)=>({
                roadAddr: addr.roadAddr,
                buildName: addr.buildName || addr.poiName,
                buildLo: addr.buildLo,
                buildLa: addr.buildLa
            }));
        const summary = `출발지 검색이 완료되었습니다. 총 ${addressCount}개의 위치를 찾았습니다.`;
        let addressInfo = topAddresses.map((addr, index)=>`${index + 1}. ${addr.roadAddr}${addr.buildName ? ` (${addr.buildName})` : ""} - 좌표: ${addr.buildLo},${addr.buildLa}`).join("\n");
        if (addressCount > 3) {
            addressInfo += `\n... 외 ${addressCount - 3}개 추가 결과`;
        }
        return [
            {
                type: "text",
                text: `${summary}\n\n${addressInfo}`
            }
        ];
    }
};
const searchDestination = {
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["searchAddress"],
    description: "목적지 위치를 검색합니다. 경로 탐색의 도착점이 되는 주소나 건물명을 검색하여 좌표 정보를 얻습니다.",
    experimental_toToolResultContent: (result)=>{
        if (!result.result?.jusoList?.length) {
            return [
                {
                    type: "text",
                    text: "목적지를 찾을 수 없습니다. 다른 키워드로 다시 검색해보세요."
                }
            ];
        }
        const addresses = result.result.jusoList;
        const addressCount = addresses.length;
        const topAddresses = addresses.slice(0, 3).map((addr)=>({
                roadAddr: addr.roadAddr,
                buildName: addr.buildName || addr.poiName,
                buildLo: addr.buildLo,
                buildLa: addr.buildLa
            }));
        const summary = `목적지 검색이 완료되었습니다. 총 ${addressCount}개의 위치를 찾았습니다.`;
        let addressInfo = topAddresses.map((addr, index)=>`${index + 1}. ${addr.roadAddr}${addr.buildName ? ` (${addr.buildName})` : ""} - 좌표: ${addr.buildLo},${addr.buildLa}`).join("\n");
        if (addressCount > 3) {
            addressInfo += `\n... 외 ${addressCount - 3}개 추가 결과`;
        }
        return [
            {
                type: "text",
                text: `${summary}\n\n${addressInfo}`
            }
        ];
    }
};
const searchDirectionsOptimized = {
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["searchDirections"],
    execute: async (params, options)=>{
        // avoid 파라미터 제거 (API 오류 방지)
        const { avoid, ...cleanParams } = params;
        console.log("searchDirections - avoid 파라미터 제거됨:", avoid);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["searchDirections"].execute(cleanParams, options);
    },
    experimental_toToolResultContent: (result)=>{
        // LLM에게는 핵심 정보만 전달 (상세한 경로 데이터 제외)
        // 에러 처리
        if (result.error) {
            return [
                {
                    type: "text",
                    text: `경로 탐색 실패: ${result.error}`
                }
            ];
        }
        // routes 배열에서 첫 번째 경로의 정보 추출
        if (!result.routes || !result.routes.length) {
            return [
                {
                    type: "text",
                    text: "경로를 찾을 수 없습니다. 출발지와 목적지를 확인해주세요."
                }
            ];
        }
        const route = result.routes[0];
        // result_code로 성공/실패 판단
        if (route.result_code !== 0) {
            return [
                {
                    type: "text",
                    text: `경로 탐색 실패: ${route.result_msg || "알 수 없는 오류가 발생했습니다."}`
                }
            ];
        }
        // 성공한 경우 핵심 정보만 추출
        const summary = route.summary;
        const distance = summary?.distance ? `${(summary.distance / 1000).toFixed(1)}km` : "정보 없음";
        const duration = summary?.duration ? `${Math.floor(summary.duration / 60)}분` : "정보 없음";
        const taxiFare = summary?.fare?.taxi ? `${summary.fare.taxi.toLocaleString()}원` : "정보 없음";
        const tollFare = summary?.fare?.toll ? `${summary.fare.toll.toLocaleString()}원` : "무료";
        // 출발지/목적지 정보
        const originName = result.origin?.name || "출발지";
        const destinationName = result.destination?.name || "목적지";
        const routeInfo = `경로 탐색이 완료되었습니다.

📍 ${originName} → ${destinationName}
🚗 거리: ${distance}
⏱️ 소요시간: ${duration}
💰 예상 택시요금: ${taxiFare}
🛣️ 통행료: ${tollFare}

경로가 지도에 자동으로 표시되었습니다.`;
        return [
            {
                type: "text",
                text: routeInfo
            }
        ];
    }
};
const getBasemapList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["tool"])({
    description: `사용 가능한 배경지도 목록을 조회합니다.
  반환되는 배경지도 목록:
  - eMapBasic: 바로e맵 일반지도 (도로, 건물, 지형지물 포함)
  - eMapAIR: 바로e맵 항공지도 (위성 항공사진)
  - eMapColor: 바로e맵 색각지도 (색상 대비 강화)
  - eMapWhite: 바로e맵 백지도 (깔끔한 흰색 배경)`,
    parameters: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({}),
    execute: async ()=>{
        return {
            basemaps: Object.entries(baseMap).map(([id, name])=>({
                    id,
                    name,
                    displayName: name
                }))
        };
    }
});
const changeBasemap = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["tool"])({
    description: `배경지도를 변경합니다.
  사용 가능한 배경지도 ID:
  - eMapBasic: 일반지도 (기본 지도)
  - eMapAIR: 항공지도 (위성지도)
  - eMapColor: 색각지도 (색상 대비)
  - eMapWhite: 백지도 (심플한 배경)

  키워드 매칭:
  - "위성지도", "항공지도" → eMapAIR
  - "일반지도", "기본지도" → eMapBasic
  - "색각지도", "컬러지도" → eMapColor
  - "백지도", "흰지도" → eMapWhite`,
    parameters: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        basemapId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
            "eMapBasic",
            "eMapAIR",
            "eMapColor",
            "eMapWhite"
        ]).describe("변경할 배경지도의 ID (eMapBasic, eMapAIR, eMapColor, eMapWhite 중 하나)")
    }),
    execute: async ({ basemapId })=>{
        const basemapName = baseMap[basemapId];
        if (!basemapName) {
            return {
                error: `지원하지 않는 배경지도 ID입니다: ${basemapId}`,
                availableBasemaps: Object.keys(baseMap)
            };
        }
        return {
            basemap: basemapId,
            basemapId: basemapId,
            basemapName: basemapName,
            success: true,
            message: `배경지도가 ${basemapName}로 변경되었습니다.`
        };
    }
});
const setMapCenter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["tool"])({
    description: `지도의 중심점을 특정 좌표로 이동합니다.

  사용 예시:
  - 특정 좌표로 이동: "지도를 127.027926, 37.497175로 이동"
  - 상대적 이동: "지도를 동쪽으로 500m 이동", "북쪽으로 1km 이동"
  - 방향별 이동: "위로", "아래로", "왼쪽으로", "오른쪽으로"`,
    parameters: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        longitude: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().describe("경도 (X좌표)"),
        latitude: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().describe("위도 (Y좌표)"),
        moveType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
            "absolute",
            "relative"
        ]).describe("이동 타입: absolute(절대좌표), relative(상대이동)").optional().default("absolute"),
        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("이동에 대한 설명").optional()
    }),
    execute: async ({ longitude, latitude, moveType, description })=>{
        try {
            return {
                success: true,
                center: [
                    longitude,
                    latitude
                ],
                moveType,
                message: description || `지도 중심점이 경도 ${longitude}, 위도 ${latitude}로 이동되었습니다.`
            };
        } catch (error) {
            return {
                success: false,
                error: `지도 중심점 이동 실패: ${error.message}`
            };
        }
    }
});
const setMapZoom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["tool"])({
    description: `지도의 확대/축소 레벨을 조정합니다.

  확대/축소 레벨:
  - 1-5: 국가/대륙 레벨 (매우 넓은 범위)
  - 6-10: 지역/도시 레벨 (넓은 범위)
  - 11-15: 구/동 레벨 (중간 범위)
  - 16-20: 건물/도로 레벨 (상세 범위)

  사용 예시:
  - "지도 확대해줘" → zoomDirection: "in", zoomLevel: 2, zoomType: "relative"
  - "더 넓게 보여줘" → zoomDirection: "out", zoomLevel: 2, zoomType: "relative"
  - "최대한 확대" → zoomLevel: 18, zoomType: "absolute"
  - "전체 보기" → zoomLevel: 2, zoomType: "absolute"
`,
    parameters: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        zoomLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().optional().describe("확대/축소 레벨 또는 변경량 (1-20)"),
        zoomType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
            "absolute",
            "relative"
        ]).describe("확대/축소 타입: absolute(절대레벨), relative(상대변경)").optional().default("relative"),
        zoomDirection: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
            "in",
            "out"
        ]).describe("확대/축소 방향: in(확대), out(축소)"),
        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("확대/축소에 대한 설명").optional()
    }),
    execute: async ({ zoomLevel = 2, zoomType, zoomDirection, description })=>{
        try {
            return {
                success: true,
                zoom: zoomLevel,
                zoomType,
                zoomDirection,
                message: description || `지도 ${zoomDirection === "in" ? "확대" : "축소"} 레벨이 ${zoomType === "relative" ? "변경" : "설정"}되었습니다.`
            };
        } catch (error) {
            return {
                success: false,
                error: `지도 확대/축소 실패: ${error.message}`
            };
        }
    }
});
const moveMapByDirection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["tool"])({
    description: `지도를 특정 방향으로 이동합니다.

  지원하는 방향:
  - north, up, 위, 북쪽: 북쪽으로 이동
  - south, down, 아래, 남쪽: 남쪽으로 이동
  - east, right, 오른쪽, 동쪽: 동쪽으로 이동
  - west, left, 왼쪽, 서쪽: 서쪽으로 이동

  거리 형식:
  - "500m", "1km", "2000m" 등의 형태로 입력
  - 단위가 없으면 미터(m)로 간주
  - 기본값: "500m"

  좌표계 지원:
  - EPSG:5186 (Korea 2000 / Central Belt 2010): 미터 단위 직접 계산
  - 정확한 거리 이동을 위해 투영 좌표계 사용`,
    parameters: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
            "north",
            "south",
            "east",
            "west",
            "up",
            "down",
            "left",
            "right"
        ]).describe("이동 방향"),
        distance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("이동 거리 (예: '500m', '1km', '2000m')").optional().default("500m")
    }),
    execute: async ({ direction, distance })=>{
        try {
            // 거리 문자열 파싱 (예: "500m", "1km", "2000")
            const parseDistance = (distanceStr)=>{
                const match = distanceStr.match(/^(\d+(?:\.\d+)?)\s*(m|km)?$/i);
                if (!match) {
                    throw new Error(`잘못된 거리 형식: ${distanceStr}`);
                }
                const value = parseFloat(match[1]);
                const unit = match[2]?.toLowerCase() || 'm';
                return unit === 'km' ? value * 1000 : value;
            };
            const distanceInMeters = parseDistance(distance);
            console.log(`moveMapByDirection: ${direction}, ${distance} -> ${distanceInMeters}m`);
            // EPSG:5186 좌표계에서는 미터 단위로 직접 계산
            // 이 좌표계는 한국 중부 지역에 최적화된 투영 좌표계로 미터 단위 사용
            let deltaX = 0; // 동서 방향 (X축)
            let deltaY = 0; // 남북 방향 (Y축)
            switch(direction){
                case "north":
                case "up":
                    deltaY = distanceInMeters; // 북쪽으로 이동 (Y 증가)
                    break;
                case "south":
                case "down":
                    deltaY = -distanceInMeters; // 남쪽으로 이동 (Y 감소)
                    break;
                case "east":
                case "right":
                    deltaX = distanceInMeters; // 동쪽으로 이동 (X 증가)
                    break;
                case "west":
                case "left":
                    deltaX = -distanceInMeters; // 서쪽으로 이동 (X 감소)
                    break;
            }
            const directionNames = {
                north: "북쪽",
                south: "남쪽",
                east: "동쪽",
                west: "서쪽",
                up: "위쪽",
                down: "아래쪽",
                left: "왼쪽",
                right: "오른쪽"
            };
            return {
                success: true,
                direction,
                deltaX,
                deltaY,
                distance: distanceInMeters,
                coordinateSystem: "EPSG:5186",
                message: `지도가 ${directionNames[direction]}으로 ${distance} 이동되었습니다.`
            };
        } catch (error) {
            return {
                success: false,
                error: `지도 방향 이동 실패: ${error.message}`
            };
        }
    }
});
const chooseOption = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["tool"])({
    description: "사용자에게 여러 선택지 중 하나를 고르도록 요청합니다. 결과는 options 배열의 값 중 하나입니다.",
    parameters: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        message: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("사용자에게 보여줄 안내 문구"),
        options: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("사용자에게 표시될 텍스트. 반드시 'key|value' 형태로 제공하세요.")).describe("사용자가 선택할 수 있는 옵션 문자열 배열")
    })
});
const getLayerList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["tool"])({
    description: `레이어 목록을 검색합니다.
**레이어 타입 매핑 (lyrTySeCode): 명시하지 않는 경우 반드시 값을 비워둡니다**
`,
    parameters: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        userId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("사용자 ID"),
        layerName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional().describe("레이어 이름"),
        lyrTySeCode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional().describe("레이어 유형 코드 ('1': 점, '2': 선, '3': 면, 비어있는 경우 전체)"),
        holdDataSeCode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional().describe("데이터 구분 코드 ('0': 전체, '1': 사용자, '2': 공유, '9': 국가 (기본값 '0'))"),
        pageIndex: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional().describe("조회할 페이지 인덱스 (기본값 '1')"),
        pageSize: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional().describe("한 페이지당 조회할 레이어 수 (기본값 '10')")
    }),
    async execute ({ userId, layerName = "", lyrTySeCode = "", holdDataSeCode = "0", pageIndex = "1", pageSize = "10" }) {
        try {
            const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiConfig"])();
            const params = new URLSearchParams({
                userId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiUserId"])(config),
                holdDataSeCode,
                pageIndex,
                pageSize
            });
            // layerName이 있을 경우에만 searchTxt 파라미터 추가
            if (layerName && layerName.trim() !== "") {
                params.append("searchTxt", layerName.trim());
            }
            if (lyrTySeCode && lyrTySeCode.trim() !== "") {
                params.append("lyrTySeCode", lyrTySeCode.trim());
            }
            // 인증 정보 추가
            if (config.headers.crtfckey) {
                params.append("crtfckey", config.headers.crtfckey);
            }
            const response = await fetch(`${config.baseUrl}/builder/layer/info/list?${params.toString()}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    ...config.headers
                }
            });
            if (!response.ok) {
                response.json().then((data)=>{
                    console.error("API request failed with data", data);
                });
                throw new Error(`API request failed with status ${response.status}`);
            }
            const data = await response.json();
            if (!data || !data.result) {
                return {
                    error: "레이어 목록 조회 실패: 응답 데이터가 없습니다."
                };
            }
            return data;
        } catch (error) {
            return {
                error: `레이어 목록 조회 실패: ${error.message}`
            };
        }
    }
});
const updateLayerStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["tool"])({
    description: `레이어의 시각적 스타일을 변경합니다. 레이어 타입(점/선/면)에 따라 적절한 스타일이 적용됩니다.

**지원하는 스타일 속성:**
- color: 기본 색상 (hex 코드, 예: "#FF0000") - 모든 타입
- fillOpacity: 채우기 투명도 (0.0-1.0) - 점, 면 타입
- strokeColor: 윤곽선 색상 (hex 코드) - 모든 타입
- strokeWidth: 윤곽선 두께 (픽셀) - 모든 타입
- radius: 점 크기 (픽셀) - 점 타입만
- width: 선 두께 (픽셀) - 선 타입만
- symbol: 심볼 타입 (점 타입만) - "circle", "square", "triangle", "star", "cross", "x"

**레이어 타입별 적용:**
- 점(Point) 레이어: color, fillOpacity, strokeColor, strokeWidth, radius, symbol
- 선(Line) 레이어: color, strokeColor, strokeWidth, width
- 면(Polygon) 레이어: color, fillOpacity, strokeColor(윤곽선), strokeWidth(윤곽선)

**사용 예시:**
- "빨간색으로 바꿔줘" → color: "#FF0000"
- "투명하게 해줘" → fillOpacity: 0.3
- "윤곽선을 두껍게" → strokeWidth: 3
- "크기를 키워줘" → radius: 10 (점) 또는 width: 5 (선)
- "별 모양으로 바꿔줘" → symbol: "star"
- "사각형으로 바꿔줘" → symbol: "square"
- "십자 모양으로 해줘" → symbol: "cross"`,
    parameters: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        layerId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("스타일을 변경할 레이어 ID"),
        color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional().describe("기본 색상 (hex 코드, 예: #FF0000)"),
        fillOpacity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(0).max(1).optional().describe("채우기 투명도 (0.0-1.0)"),
        strokeColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional().describe("윤곽선 색상 (hex 코드)"),
        strokeWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(0).max(20).optional().describe("윤곽선 두께 (픽셀)"),
        radius: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(1).max(50).optional().describe("점 크기 (픽셀, 점 타입만)"),
        width: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(1).max(20).optional().describe("선 두께 (픽셀, 선 타입만)"),
        symbol: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
            "circle",
            "square",
            "triangle",
            "star",
            "cross",
            "x"
        ]).optional().describe("심볼 타입 (점 타입만): circle(원), square(사각형), triangle(삼각형), star(별), cross(십자), x(X자)"),
        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional().describe("변경 사항에 대한 설명")
    }),
    async execute ({ layerId, color, fillOpacity, strokeColor, strokeWidth, radius, width, symbol, description }) {
        try {
            // 스타일 객체 구성
            const styleUpdate = {};
            if (color) styleUpdate.color = color;
            if (fillOpacity !== undefined) styleUpdate.fillOpacity = fillOpacity;
            if (strokeColor) styleUpdate.strokeColor = strokeColor;
            if (strokeWidth !== undefined) styleUpdate.strokeWidth = strokeWidth;
            if (radius !== undefined) styleUpdate.radius = radius;
            if (width !== undefined) styleUpdate.width = width;
            if (symbol) styleUpdate.symbol = symbol;
            return {
                layerId,
                styleUpdate,
                description: description || "레이어 스타일이 업데이트되었습니다.",
                success: true
            };
        } catch (error) {
            return {
                error: `스타일 업데이트 실패: ${error.message}`,
                success: false
            };
        }
    }
});
const generateCategoricalStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["tool"])({
    description: `레이어에 여러 조건별 스타일을 적용합니다. 사용자가 "A는 빨간색, B는 파란색" 같은 요청을 할 때 사용합니다.
attributes 매개변수로 filter 조건에 사용할 수 있는 속성 정보를 제공해야 합니다.
예시: 
`,
    parameters: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        layerId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("스타일을 적용할 레이어 ID"),
        lyrNm: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("레이어이름"),
        attributes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("🚨 필수: 속성명과 설명을 모두 포함한 형태로 제공 🚨 - 형식: 'a17(건폐율), a13(사용승인일자)' 또는 'c1(주소), c2(건물명)' - 절대 'a17'처럼 속성명만 제공하지 마세요"),
        userInstruction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("사용자의 자연어 스타일링 요청 (예: '용산구는 노란색, 강남구는 파란색, 나머지는 회색으로', '벽돌구조이면서 1990년도 이전인 건물은 빨간색으로')")
    }),
    async execute ({ layerId, lyrNm, attributes, userInstruction }) {
        try {
            console.log("generateCategoricalStyle called with:", {
                layerId,
                lyrNm,
                attributes,
                userInstruction
            });
            // generateObject를 사용해서 userInstruction에서 바로 processedStyleRules 생성
            const { object: processedStyleRules } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["generateObject"])({
                model: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$openai$40$1$2e$3$2e$22_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$openai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["openai"])("gpt-4.1-nano", {
                    structuredOutputs: true
                }),
                temperature: 0,
                schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
                    styleRules: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
                        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("규칙 설명"),
                        color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("HEX 색상 코드 (예: #FF0000, #0000FF, #00FF00)"),
                        conditions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
                            attributeName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("속성명 - 반드시 attributes 매개변수에서 제공된 속성명만 사용"),
                            condition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
                                "like",
                                "equal",
                                "greater",
                                "less",
                                "greaterEqual",
                                "lessEqual",
                                "default"
                            ]).describe("조건 타입"),
                            value: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("조건 값")
                        })).describe("조건 배열 (복수 조건 지원). default 조건인 경우 빈 배열"),
                        logicalOperator: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
                            "AND",
                            "OR"
                        ]).describe("복수 조건 간 논리 연산자 (기본값: AND)")
                    }))
                }),
                prompt: `다음 자연어 스타일링 요청을 분석해서 스타일 규칙으로 변환해주세요:

**사용자 요청**: "${userInstruction}"
**레이어명**: "${lyrNm}"
**사용 가능한 속성들**: "${attributes}"

**🚨 중요 제약사항 🚨**:
- **반드시 위의 "사용 가능한 속성들"에서 속성명을 추출하여 사용하세요**
- 속성 형식: "a17(건폐율), a13(사용승인일자)" → 속성명은 "a17", "a13" 사용
- 속성 형식: "c1(주소), c2(건물명)" → 속성명은 "c1", "c2" 사용
- **괄호 안의 설명은 무시하고 괄호 앞의 속성명만 사용하세요**
- 존재하지 않는 가상의 속성명을 절대 만들어내지 마세요

**변환 규칙**:
1. 자연어에서 조건과 색상을 추출하여 스타일 규칙 생성
2. 색상은 HEX 코드로 직접 생성 (예: #FF0000, #0000FF, #00FF00, #FFFF00, #800080, #FFA500, #FFC0CB, #A52A2A, #808080, #000000, #FFFFFF)
3. **속성명 선택 규칙**:
   - 사용자 요청을 분석하여 위의 "사용 가능한 속성들" 목록에서만 적절한 속성을 선택
   - 목록에 없는 속성은 절대 사용하지 마세요
   - **복합 조건**: 여러 속성을 조합하여 사용 가능 (단, 모두 위 목록에 있는 속성만)
4. **🚨 복합 조건 처리 규칙 🚨**:
   - **"그리고", "이면서", "동시에", "또한"**: 하나의 규칙으로 처리, conditions 배열에 여러 조건 포함
   - **"또는", "이거나"**: 하나의 규칙으로 처리, logicalOperator를 "OR"로 설정
   - **"A는 X색, B는 Y색"**: 별개의 규칙들로 분리
   - **단일 조건**: conditions 배열에 하나의 조건만 포함
5. 조건 타입:
   - "like": 텍스트 포함 검색 (기본값, 대부분의 경우)
   - "equal": 정확한 값 매칭 (특별히 정확한 매칭이 필요한 경우만)
   - "greater": 숫자 > 비교 (명시적으로 "초과", "보다 큰" 등이 언급된 경우)
   - "less": 숫자 < 비교 (명시적으로 "미만", "보다 작은" 등이 언급된 경우)
   - "greaterEqual": 숫자 >= 비교 (명시적으로 "이상" 등이 언급된 경우)
   - "lessEqual": 숫자 <= 비교 (명시적으로 "이하" 등이 언급된 경우)
   - "default": 기본/나머지 스타일 (조건 없음)

**변환 예시** (속성명 추출 방법):

**🚨 중요: "그리고", "이면서", "동시에" 등은 하나의 규칙으로 처리! 🚨**

**속성명 추출 방법:**
- 속성 정보: "a17(건폐율), a13(사용승인일자)" → 사용할 속성명: "a17", "a13"
- 속성 정보: "c1(주소), c2(건물명)" → 사용할 속성명: "c1", "c2"

**단일 조건:**
- "서울에 있는 데이터만 파란색으로" (속성: "정제지번주소") →
  [{"description": "서울 지역 - 파란색", "color": "#0000FF", "conditions": [{"attributeName": "정제지번주소", "condition": "like", "value": "서울"}], "logicalOperator": "AND"}]

**복합 조건 (하나의 규칙):**
- "건폐율 50 이상이고 사용승인일자가 1980년 이전인 건물" (속성: "a17(건폐율), a13(사용승인일자)") →
  [{"description": "건폐율 50 이상이고 사용승인일자가 1980년 이전인 건물 - 빨간색", "color": "#FF0000", "conditions": [{"attributeName": "a17", "condition": "greaterEqual", "value": "50"}, {"attributeName": "a13", "condition": "less", "value": "1980"}], "logicalOperator": "AND"}]

**여러 규칙 (별개 조건):**
- "대학은 파란색, 중학교는 빨간색으로" (속성: 교육기관타입) →
  [
    {"description": "대학 - 파란색", "color": "#0000FF", "conditions": [{"attributeName": "교육기관타입", "condition": "like", "value": "대학"}], "logicalOperator": "AND"},
    {"description": "중학교 - 빨간색", "color": "#FF0000", "conditions": [{"attributeName": "교육기관타입", "condition": "like", "value": "중학교"}], "logicalOperator": "AND"}
  ]

- "나머지/기본 스타일" →
  [{"description": "나머지 - 회색", "color": "#808080", "conditions": [], "logicalOperator": "AND"}]

**중요**:
- 특별한 언급이 없으면 condition은 "like" 사용
- **logicalOperator는 항상 포함**: 단일 조건이어도 "AND" 명시, 복합 조건은 "AND" 또는 "OR" 명시
- **항상 default 규칙을 포함**: 사용자가 특정 조건만 언급해도 나머지 데이터를 위한 기본 스타일(회색 #808080)을 자동으로 추가
- 사용자가 "나머지는 X색으로" 명시한 경우에만 해당 색상 사용, 그렇지 않으면 기본 회색 사용
- **다시 한번 강조: 반드시 제공된 속성 목록에서만 속성명을 선택하세요**`
            });
            console.log("Processed style rules:", processedStyleRules.styleRules);
            // logicalOperator가 누락된 경우 기본값 설정
            const normalizedStyleRules = processedStyleRules.styleRules.map((rule)=>({
                    ...rule,
                    logicalOperator: rule.logicalOperator || "AND"
                }));
            // default 규칙이 없으면 자동으로 추가
            const hasDefaultRule = normalizedStyleRules.some((rule)=>rule.conditions.length === 0 || rule.conditions.some((cond)=>cond.condition === 'default'));
            let finalStyleRules = [
                ...normalizedStyleRules
            ];
            if (!hasDefaultRule) {
                finalStyleRules.push({
                    description: "나머지 - 기본 스타일",
                    color: "#808080",
                    conditions: [],
                    logicalOperator: "AND"
                });
                console.log("Added default rule automatically");
            }
            return {
                layerId,
                attributes,
                styleRules: finalStyleRules,
                description: `${finalStyleRules.length}개 유형별 스타일 적용 (${attributes.length}개 속성 활용)`,
                success: true,
                type: 'categorical'
            };
        } catch (error) {
            return {
                error: `유형별 스타일 생성 실패: ${error.message}`,
                success: false
            };
        }
    }
});
const removeLayer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["tool"])({
    description: `지도에서 레이어를 삭제합니다. 삭제된 레이어는 복구할 수 없으므로 신중하게 사용하세요.

**사용 방법:**
- layerId: 삭제할 레이어의 ID를 정확히 입력하세요
- Current map state에서 레이어 ID를 확인할 수 있습니다

**사용 예시:**
- "스타벅스 레이어를 삭제해줘" → layerId: "LR0000001234"
- "이 레이어를 지워줘" → 현재 활성 레이어의 ID 사용
- "모든 레이어를 삭제해줘" → 각 레이어 ID를 순차적으로 삭제`,
    parameters: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        layerId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("삭제할 레이어의 ID"),
        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional().describe("삭제 사유 또는 설명")
    }),
    async execute ({ layerId, description }) {
        try {
            console.log("removeLayer called with layerId:", layerId);
            console.log("description:", description);
            return {
                layerId,
                description: description || "레이어가 삭제되었습니다.",
                success: true
            };
        } catch (error) {
            return {
                error: `레이어 삭제 실패: ${error.message}`,
                success: false
            };
        }
    }
});
const createLayerFilter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["tool"])({
    description: `레이어의 속성정보를 기반으로 CQL 필터를 생성합니다. 단일 조건과 복수 조건을 모두 지원합니다.

  필드 선택 가이드:
  - getLayerAttributes 도구로 조회한 properties의 필드명을 사용하세요
  - 필드명은 정확히 일치해야 합니다 (대소문자 구분)

  연산자 사용 가이드:
  - 텍스트: LIKE (부분일치), = (완전일치)
  - 숫자: >, >=, <, <=, =
  - 목록: IN

  복수 조건 지원:
  - AND: 모든 조건이 참이어야 함
  - OR: 하나 이상의 조건이 참이면 됨
  - 예시: "서울이면서 인구 100만 이상", "강남구 또는 서초구"

  Args:
    lyr_id: 필터 적용할 레이어 ID
    attributes: 사용 가능한 속성 정보 (형식: "a17(건폐율), a13(사용승인일자)")
    user_instruction: 자연어 필터링 요청 (예: "서울이면서 인구 100만 이상", "강남구 또는 서초구")
     `,
    parameters: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        lyrId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().min(1).describe("필터를 적용할 레이어 ID"),
        attributes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("🚨 필수: 속성명과 설명을 모두 포함한 형태로 제공 🚨 - 형식: 'a17(건폐율), a13(사용승인일자)' 또는 'c1(주소), c2(건물명)' - 절대 'a17'처럼 속성명만 제공하지 마세요"),
        userInstruction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("사용자의 자연어 필터링 요청 (예: '서울이면서 인구 100만 이상', '강남구 또는 서초구', '건폐율 50 이상')")
    }),
    execute: async ({ lyrId, attributes, userInstruction })=>{
        try {
            console.log("createLayerFilter called with:", {
                lyrId,
                attributes,
                userInstruction
            });
            // generateObject를 사용해서 userInstruction에서 바로 필터 조건 생성
            const { object: filterConditions } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["generateObject"])({
                model: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$openai$40$1$2e$3$2e$22_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$openai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["openai"])("gpt-4.1-nano", {
                    structuredOutputs: true
                }),
                schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
                    conditions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
                        attributeName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("속성명 - 반드시 attributes 매개변수에서 제공된 속성명만 사용"),
                        operator: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
                            "=",
                            ">",
                            "<",
                            ">=",
                            "<=",
                            "LIKE",
                            "IN"
                        ]).describe("비교 연산자"),
                        value: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("조건 값")
                    })).describe("필터 조건 배열"),
                    logicalOperator: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
                        "AND",
                        "OR"
                    ]).describe("복수 조건 간 논리 연산자 (기본값: AND)")
                }),
                prompt: `다음 자연어 필터링 요청을 분석해서 CQL 필터 조건으로 변환해주세요:

**사용자 요청**: "${userInstruction}"
**사용 가능한 속성들**: "${attributes}"

**🚨 중요 제약사항 🚨**:
- **반드시 위의 "사용 가능한 속성들"에서 속성명을 추출하여 사용하세요**
- 속성 형식: "a17(건폐율), a13(사용승인일자)" → 속성명은 "a17", "a13" 사용
- 속성 형식: "c1(주소), c2(건물명)" → 속성명은 "c1", "c2" 사용
- **괄호 안의 설명은 무시하고 괄호 앞의 속성명만 사용하세요**
- 존재하지 않는 가상의 속성명을 절대 만들어내지 마세요

**변환 규칙**:
1. 자연어에서 조건을 추출하여 필터 조건 생성
2. **속성명 선택 규칙**:
   - 사용자 요청을 분석하여 위의 "사용 가능한 속성들" 목록에서만 적절한 속성을 선택
   - 목록에 없는 속성은 절대 사용하지 마세요
3. **논리 연산자 처리 규칙**:
   - **"그리고", "이면서", "동시에", "또한"**: logicalOperator를 "AND"로 설정
   - **"또는", "이거나"**: logicalOperator를 "OR"로 설정
   - **단일 조건**: logicalOperator를 "AND"로 설정
4. 연산자 타입:
   - "LIKE": 텍스트 포함 검색 (기본값, 대부분의 경우)
   - "=": 정확한 값 매칭 (특별히 정확한 매칭이 필요한 경우만)
   - ">": 숫자 > 비교 (명시적으로 "초과", "보다 큰" 등이 언급된 경우)
   - "<": 숫자 < 비교 (명시적으로 "미만", "보다 작은" 등이 언급된 경우)
   - ">=": 숫자 >= 비교 (명시적으로 "이상" 등이 언급된 경우)
   - "<=": 숫자 <= 비교 (명시적으로 "이하" 등이 언급된 경우)
   - "IN": 목록 값 매칭 (여러 값 중 하나와 일치)

**변환 예시**:
- "서울에 있는 데이터만" (속성: "정제지번주소") →
  {"conditions": [{"attributeName": "정제지번주소", "operator": "LIKE", "value": "서울"}], "logicalOperator": "AND"}

- "건폐율 50 이상이고 사용승인일자가 1980년 이전" (속성: "a17(건폐율), a13(사용승인일자)") →
  {"conditions": [{"attributeName": "a17", "operator": ">=", "value": "50"}, {"attributeName": "a13", "operator": "<", "value": "1980"}], "logicalOperator": "AND"}

- "강남구 또는 서초구" (속성: "구명") →
  {"conditions": [{"attributeName": "구명", "operator": "LIKE", "value": "강남구"}, {"attributeName": "구명", "operator": "LIKE", "value": "서초구"}], "logicalOperator": "OR"}

**중요**:
- 특별한 언급이 없으면 operator는 "LIKE" 사용
- **logicalOperator는 항상 포함**: 단일 조건이어도 "AND" 명시
- **다시 한번 강조: 반드시 제공된 속성 목록에서만 속성명을 선택하세요**`
            });
            console.log("Processed filter conditions:", filterConditions);
            // CQL 필터 문자열 생성
            let filterStr = "";
            if (filterConditions.conditions.length === 0) {
                return {
                    error: "필터 조건이 생성되지 않았습니다."
                };
            }
            if (filterConditions.conditions.length === 1) {
                // 단일 조건
                const condition = filterConditions.conditions[0];
                filterStr = buildCQLCondition(condition);
            } else {
                // 복수 조건
                const conditionStrings = filterConditions.conditions.map((condition)=>buildCQLCondition(condition));
                const operator = filterConditions.logicalOperator || "AND";
                filterStr = conditionStrings.join(` ${operator} `);
            }
            return {
                lyr_id: lyrId,
                filter: filterStr,
                description: `${filterConditions.conditions.length}개 조건으로 필터링: ${userInstruction}`,
                conditions: filterConditions.conditions,
                logicalOperator: filterConditions.logicalOperator
            };
        } catch (error) {
            return {
                error: `필터 생성 실패: ${error.message}`
            };
        }
    }
});
// CQL 조건 문자열 생성 헬퍼 함수
function buildCQLCondition(condition) {
    const { attributeName, operator, value } = condition;
    // 공백과 따옴표 제거
    const cleanFieldName = attributeName.trim().replace(/['"]/g, "");
    if (operator.toUpperCase() === "LIKE") {
        return `${cleanFieldName} LIKE '%${value}%'`;
    } else if (operator.toUpperCase() === "IN") {
        const values = value.split(",").map((v)=>v.trim());
        const valueStr = values.map((v)=>`'${v}'`).join(",");
        return `${cleanFieldName} IN (${valueStr})`;
    } else {
        const isNumeric = !isNaN(Number(value));
        // 연산자 앞뒤에 공백 추가하여 가독성 향상
        return `${cleanFieldName} ${operator} ${isNumeric ? value : `'${value}'`}`;
    }
}
const getLayerAttributesCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["tool"])({
    description: `레이어의 속성 데이터 개수를 조회합니다. 필터 조건을 적용하여 특정 조건에 맞는 데이터의 개수를 확인할 수 있습니다.`,
    parameters: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        typeName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("레이어 타입명 (예: Wgeontest3:L100000249)"),
        attributeFilter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional().describe("속성 필터 내용 (예: a1 like '%종로구%' AND a2 < '1995-02-19')"),
        spatialFilter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional().describe("공간 필터 내용 (예: POLYGON((190000 540000, 200000 540000, 200000 550000, 190000 550000, 190000 540000)))"),
        spatialFilterSrid: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional().describe("공간 필터 SRID (예: 5186)")
    }),
    execute: async ({ typeName, attributeFilter, spatialFilter, spatialFilterSrid })=>{
        try {
            const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiConfig"])();
            const params = new URLSearchParams({
                typeName: typeName
            });
            // 선택적 파라미터들 추가
            if (attributeFilter && attributeFilter.trim() !== "") {
                params.append("attributeFilter", attributeFilter.trim());
            }
            if (spatialFilter && spatialFilter.trim() !== "") {
                params.append("spatialFilter", spatialFilter.trim());
            }
            if (spatialFilterSrid && spatialFilterSrid.trim() !== "") {
                params.append("spatialFilterSrid", spatialFilterSrid.trim());
            }
            // 인증 정보 추가
            if (config.headers.crtfckey) {
                params.append("crtfckey", config.headers.crtfckey);
            }
            const response = await fetch(`${config.baseUrl}/builder/layer/attributes/count?${params.toString()}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    ...config.headers
                }
            });
            if (!response.ok) {
                response.json().then((data)=>{
                    console.error("API request failed with data", data);
                });
                throw new Error(`API request failed with status ${response.status}`);
            }
            const data = await response.json();
            if (!data) {
                return {
                    error: "레이어 속성개수 조회 실패: 응답 데이터가 없습니다."
                };
            }
            return {
                typeName,
                count: data.count || data.result || data,
                attributeFilter,
                spatialFilter,
                spatialFilterSrid,
                description: `레이어 ${typeName}의 속성 데이터 개수: ${data.count || data.result || data}개`
            };
        } catch (error) {
            return {
                error: `레이어 속성개수 조회 실패: ${error.message}`
            };
        }
    }
});
const HILTools = {
    chooseOption,
    getUserInput: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getUserInput"],
    confirmWithCheckbox: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["confirmWithCheckbox"]
};
const __TURBOPACK__default__export__ = HILTools;
}}),
"[project]/app/(map)/api/chat/layer-tool-rules.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 레이어 관련 도구 사용 규칙 - 간소화된 버전
 * 중복 제거 및 계층적 구조로 재구성
 */ __turbopack_context__.s({
    "CORE_LAYER_RULES": (()=>CORE_LAYER_RULES),
    "TOOL_RULES": (()=>TOOL_RULES),
    "WORKFLOWS": (()=>WORKFLOWS),
    "createLayerToolRules": (()=>createLayerToolRules),
    "getLayerToolRulesForAgent": (()=>getLayerToolRulesForAgent)
});
const CORE_LAYER_RULES = {
    // 현재 지도 상태 확인 (최우선)
    MAP_STATE_CHECK: "모든 작업 전에 Current map state에서 대상 레이어 존재 여부 확인",
    // 레이어 ID 형식 및 확보
    LAYER_ID_FORMAT: "LR로 시작하는 10자리",
    LAYER_ID_EXTRACTION: "chooseOption 결과가 '레이어명|레이어ID' 형태면 '|' 뒤의 ID 추출",
    // 중복 방지
    DUPLICATE_PREVENTION: "이미 지도에 있는 레이어는 getLayer 재호출 금지",
    // 작업 순서
    WORK_ORDER: "현재 지도 상태 확인 → 레이어 ID 확보 → 필요시에만 도구 호출",
    // HIL 도구 사용
    HIL_USAGE: "검색 결과 여러개: chooseOption (반드시 '레이어명|레이어ID' 형태), 검색 실패: getUserInput, 중요 작업: confirmWithCheckbox, 사용자 위치 확인: getLocation",
    // 속성 기반 요청 식별 패턴
    ATTRIBUTE_REQUEST_PATTERNS: `
    형용사: "노후화된", "높은", "오래된", "새로운", "큰", "작은", "낡은", "최신"
    필터링: "~만", "~인 것만", "조건에 맞는", "특정 조건"
    색상 지정: "흰색으로", "노란색으로", "빨간색으로", "파란색으로"
    비교: "~보다 높은", "~이상", "~이하", "~년도 이후", "~년도 이전"
    지역 조건: "서울에 있는", "부산에 있는", "강남구의", "용산구만", "경기도에 있는", "~시의", "~구의", "~동의"`
};
const TOOL_RULES = {
    getLayerList: "userId='geonuser', layerName='키워드' lyrTySeCode='' (비워둠)",
    getLayer: "현재 지도 상태 확인 후 중복 방지, 레이어 ID 형식 검증",
    getLayerAttributes: `
    - getLayer 선행 필수, 400 에러 시 getLayer 재시도
    - 속성 기반 요청 시 반드시 호출 (노후화, 높이, 크기 등)
    - 조건부 스타일링 전 필수 단계
    - 속성 컬럼 정보를 바탕으로 사용자 조건 매칭
    - 🚨 지역 조건 시 우선순위: 시도명 > 주소 > 구명 > 동명 > 기타 지역 관련 컬럼`,
    updateLayerStyle: "현재 지도 상태 확인, 유형별 스타일링 시 속성 조회 필수",
    removeLayer: "현재 지도 상태 확인, 중요 작업 시 confirmWithCheckbox 사용",
    getLayerAttributesCount: "속성 필터 적용 시 cql 형태로 생성"
};
const WORKFLOWS = {
    ADD_LAYER: "지도 상태 확인 → ID 확보 → 필요시 검색/선택 → getLayer",
    CHANGE_STYLE: "지도 상태 확인 → 레이어 없으면 추가 → 스타일 적용",
    DELETE_LAYER: "지도 상태 확인 → ID 확인 → 필요시 확인 → removeLayer",
    // 새로운 상세 워크플로우 추가
    LAYER_WITH_ATTRIBUTES: `
    1. getLayerList로 키워드 검색
    2. 검색 결과 처리 (chooseOption/자동선택)
    3. getLayer로 레이어 추가
    4. getLayerAttributes로 속성 정보 조회
    5. 필요시 조건부 스타일링 또는 필터링 적용`,
    CONDITIONAL_STYLING: `
    1. 현재 지도 상태에서 대상 레이어 확인
    2. 레이어가 없으면 LAYER_WITH_ATTRIBUTES 워크플로우 실행
    3. getLayerAttributes로 속성 정보 조회 (필수)
    4. 속성값 기반 조건부 스타일링 적용`,
    ATTRIBUTE_BASED_FILTER: `
    1. 대상 레이어 확보 (없으면 검색/추가)
    2. getLayerAttributes로 속성 컬럼 확인
    3. 사용자 조건에 맞는 속성 컬럼 식별
    4. createLayerFilter 또는 generateCategoricalStyle로 필터링 적용`,
    GET_LAYER_ATTRIBUTES_COUNT: `
    1. 대상 레이어 확보 (없으면 검색/추가)
    2. getLayerAttributesCount로 데이터 개수 조회`
};
function createLayerToolRules() {
    return `
**🚨 레이어 도구 사용 핵심 규칙:**

**최우선 원칙:**
- ${CORE_LAYER_RULES.MAP_STATE_CHECK}
- ${CORE_LAYER_RULES.WORK_ORDER}
- ${CORE_LAYER_RULES.DUPLICATE_PREVENTION}

**레이어 ID 관리:**
- 형식: ${CORE_LAYER_RULES.LAYER_ID_FORMAT}
- 추출: ${CORE_LAYER_RULES.LAYER_ID_EXTRACTION}

**도구별 핵심 규칙:**
- **getLayerList**: ${TOOL_RULES.getLayerList}
- **getLayer**: ${TOOL_RULES.getLayer}
- **getLayerAttributes**: ${TOOL_RULES.getLayerAttributes}
- **getLayerAttributesCount**: ${TOOL_RULES.getLayerAttributesCount}
- **updateLayerStyle**: ${TOOL_RULES.updateLayerStyle}
- **removeLayer**: ${TOOL_RULES.removeLayer}

**🎯 상세 작업 흐름 (필수 준수):**

**기본 레이어 추가**: ${WORKFLOWS.ADD_LAYER}

**속성 정보가 필요한 레이어 작업**: ${WORKFLOWS.LAYER_WITH_ATTRIBUTES}

**조건부 스타일링**: ${WORKFLOWS.CONDITIONAL_STYLING}

**속성 기반 필터링**: ${WORKFLOWS.ATTRIBUTE_BASED_FILTER}

**데이터 개수 조회**: ${WORKFLOWS.GET_LAYER_ATTRIBUTES_COUNT}

**HIL 도구 활용:**
- ${CORE_LAYER_RULES.HIL_USAGE}

**에러 방지 체크리스트:**
- Current map state 확인했는가?
- 레이어 ID가 LR로 시작하는 10자리인가?
- 중복 추가를 방지했는가?
- getLayerList 호출 시 lyrTySeCode를 비워뒀는가?
- 속성 기반 요청 시 getLayerAttributes를 호출했는가?
- HIL 도구를 적절히 사용했는가?
  `.trim();
}
function getLayerToolRulesForAgent(agentType) {
    const baseRules = createLayerToolRules();
    switch(agentType){
        case "unified":
            return baseRules + `
**🎯 통합 레이어 에이전트 특화 규칙:**

**핵심 임무:**
- 레이어 검색, 추가, 삭제, 속성 조회, 필터링, 스타일 변경, 유형별 스타일 적용, 데이터 개수 조회 기능 지원
- 현재 지도 상태 우선 확인으로 효율적 작업 흐름 보장

**기본 실행 원칙:**
- 의도분석에서 제공된 작업 단계를 순서대로 실행
- 각 단계별로 명확한 도구 호출
- 복잡한 판단은 하지 않고 지시사항만 따름
`;
        case "style":
            return baseRules + `

**🎯 스타일링 특화:**
- 스타일 변경이 주 목적
- 조건부 스타일링 시 속성 정보 필수
- generateCategoricalStyle 사용 시 속성값 검증 필수
      `;
        default:
            return baseRules;
    }
}
}}),
"[project]/app/(map)/api/chat/agents.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AGENT_LIMITATIONS": (()=>AGENT_LIMITATIONS),
    "COMMON_SYSTEM_PROMPTS": (()=>COMMON_SYSTEM_PROMPTS),
    "IntentEnum": (()=>IntentEnum),
    "IntentResponseSchema": (()=>IntentResponseSchema),
    "UNSUPPORTED_FEATURES": (()=>UNSUPPORTED_FEATURES),
    "agentConfigs": (()=>agentConfigs),
    "intentAnalyzerAgentConfig": (()=>intentAnalyzerAgentConfig)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@geon-ai+tools@0.0.11_react@19.0.0/node_modules/@geon-ai/tools/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$intent$2d$rules$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(map)/api/chat/intent-rules.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(map)/api/chat/tools.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.24.1/node_modules/zod/lib/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$layer$2d$tool$2d$rules$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(map)/api/chat/layer-tool-rules.ts [app-route] (ecmascript)");
;
;
;
;
;
const UNSUPPORTED_FEATURES = {
    NEARBY_POI_SEARCH: {
        keywords: [
            "근처",
            "주변",
            "이 근처",
            "여기 근처",
            "주위",
            "인근",
            "nearby",
            "around here",
            "close to"
        ],
        description: "주변/근처 POI 검색",
        examples: [
            "여기 근처에 맛집",
            "주변 카페",
            "이 근처 병원"
        ],
        alternative: "구체적인 장소명 검색 (예: '강남역 스타벅스')"
    },
    CATEGORY_LIST_REQUEST: {
        keywords: [
            "전체 목록",
            "모든 ~",
            "~ 목록",
            "~ 리스트"
        ],
        description: "카테고리별 전체 목록 요청",
        examples: [
            "맛집 목록",
            "카페 목록",
            "병원 목록"
        ],
        alternative: "구체적인 브랜드명 검색 (예: '스타벅스', '이디야')",
        exceptions: [
            "레이어 목록",
            "사용가능한 레이어",
            "데이터 종류"
        ]
    },
    REALTIME_INFO: {
        keywords: [
            "실시간",
            "지금",
            "현재",
            "실시간 교통",
            "교통상황",
            "정체",
            "실시간 날씨",
            "현재 날씨",
            "실시간 위치"
        ],
        description: "실시간 정보 요청",
        examples: [
            "실시간 교통상황",
            "지금 날씨",
            "현재 교통정보"
        ],
        alternative: "정적 데이터 기반 길찾기 및 레이어 정보"
    },
    DATA_MANAGEMENT: {
        keywords: [
            "업로드",
            "올리기",
            "파일 추가",
            "편집",
            "수정",
            "삭제",
            "다운로드",
            "저장",
            "내보내기",
            "export",
            "새로 만들기",
            "생성"
        ],
        description: "데이터 관리 기능",
        examples: [
            "데이터 업로드",
            "파일 편집",
            "레이어 생성"
        ],
        alternative: "기본 제공 레이어 활용 및 검색"
    },
    ADVANCED_SPATIAL_ANALYSIS: {
        keywords: [
            "버퍼",
            "반경",
            "buffer",
            "radius",
            "오버레이",
            "overlay",
            "중첩",
            "네트워크 분석",
            "공간분석",
            "spatial analysis"
        ],
        description: "고급 공간분석",
        examples: [
            "500m 버퍼 분석",
            "오버레이 분석",
            "네트워크 분석"
        ],
        alternative: "밀도 분석 기능",
        exceptions: [
            "밀도 분석",
            "밀도"
        ]
    },
    ADVANCED_MAP_FEATURES: {
        keywords: [
            "3D",
            "삼차원",
            "입체",
            "애니메이션",
            "시간대별",
            "temporal",
            "사용자 정의 심볼",
            "커스텀 스타일",
            "회전",
            "돌려줘",
            "각도",
            "기울여",
            "틸트",
            "rotate",
            "tilt"
        ],
        description: "고급 지도 기능",
        examples: [
            "3D 지도",
            "시간대별 애니메이션",
            "커스텀 심볼",
            "지도 회전",
            "지도 기울기"
        ],
        alternative: "기본 2D 지도, 표준 스타일링, 지도 이동 및 확대/축소"
    }
};
const AGENT_LIMITATIONS = {
    navigation: {
        unsupported: [
            UNSUPPORTED_FEATURES.NEARBY_POI_SEARCH,
            UNSUPPORTED_FEATURES.CATEGORY_LIST_REQUEST,
            UNSUPPORTED_FEATURES.REALTIME_INFO
        ],
        supported: [
            "구체적인 장소명 검색",
            "두 지점 간 길찾기",
            "특정 주소 검색"
        ]
    },
    layer_agent: {
        unsupported: [
            UNSUPPORTED_FEATURES.DATA_MANAGEMENT,
            UNSUPPORTED_FEATURES.CATEGORY_LIST_REQUEST,
            UNSUPPORTED_FEATURES.ADVANCED_SPATIAL_ANALYSIS
        ],
        supported: [
            "키워드 기반 레이어 검색",
            "레이어 추가/삭제",
            "레이어 목록 조회",
            "레이어 스타일 변경",
            "조건부 레이어 필터링",
            "속성 기반 필터링"
        ]
    },
    density_analysis: {
        unsupported: [
            UNSUPPORTED_FEATURES.ADVANCED_SPATIAL_ANALYSIS,
            UNSUPPORTED_FEATURES.DATA_MANAGEMENT
        ],
        supported: [
            "포인트 데이터 밀도 분석",
            "히트맵 시각화"
        ]
    },
    basemap: {
        unsupported: [
            UNSUPPORTED_FEATURES.ADVANCED_MAP_FEATURES,
            UNSUPPORTED_FEATURES.DATA_MANAGEMENT
        ],
        supported: [
            "배경지도 변경",
            "기본 지도 스타일 전환"
        ]
    }
};
const COMMON_SYSTEM_PROMPTS = {
    language: `응답은 한국어로 작성하세요.`,
    tone: `친근하고 자연스러운 톤으로 사용자에게 도움을 제공하세요.`,
    interaction: `명확한 지시는 즉시 실행하고, 애매한 경우에만 사용자 입력 도구를 사용하세요. 이미 동의한 작업을 다시 확인하지 마세요.`,
    coreRules: `
🚨🚨🚨 **모든 에이전트 공통 핵심 규칙** 🚨🚨🚨
1. **컨텍스트 유지**: 대화 맥락을 파악하여 지시사항을 준수하세요.
2. **정확한 파라미터**: 키워드가 없으면 빈 값으로 설정
`
};
const IntentEnum = [
    "LAYER_ADD",
    "LAYER_REMOVE",
    "LAYER_STYLE",
    "LAYER_FILTER",
    "LAYER_LIST",
    "LAYER_COUNT",
    "NAVIGATION",
    "MAP_CONTROL",
    "BASEMAP_CHANGE",
    "DENSITY_ANALYSIS",
    "GENERAL_CONVERSATION",
    "UNSUPPORTED_FEATURE",
    "UNSURE"
];
const IntentResponseSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
    intent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum(IntentEnum).describe("분석된 사용자의 핵심 의도 카테고리"),
    message: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("다음 에이전트에게 전달할 구체적인 작업 지시 메시지 (시스템 내부 통신용)"),
    userMessage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("사용자에게 표시할 친화적인 작업 설명 메시지 (도구명 노출 없이 작업 흐름만 설명)"),
    targetLayer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional().describe("대상 레이어명 (있는 경우)"),
    layerExists: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].boolean().optional().describe("대상 레이어가 현재 지도에 존재하는지"),
    requiredActions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string()).describe("필요한 작업 단계들"),
    styleRequirements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional(),
        shape: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional(),
        size: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional()
    }).optional().describe("스타일 요구사항")
});
const intentAnalyzerAgentConfig = {
    tools: {},
    system: `
    ${COMMON_SYSTEM_PROMPTS.language}

    당신은 지도 서비스의 의도분석 전문가입니다.

    **핵심 역할:**
    1. 사용자 요청의 정확한 의도 파악
    2. 현재 지도 상태 기반 작업 계획 수립
    3. 다음 에이전트를 위한 구체적 지시사항 생성

    **분석 단계:**
    1. **현재 상황 파악**: 지도에 있는 레이어들 확인
    2. **요청 분석**: 사용자가 원하는 것이 무엇인지 정확히 파악
    3. **작업 계획**: 현재 상태에서 목표까지의 구체적 단계 수립

    **상황별 분석 예시:**

    **케이스 1: 레이어가 이미 존재하는 경우**
    사용자: "스타벅스를 빨간색으로 바꿔줘"
    현재 지도: 스타벅스 레이어 존재
    → intent: "LAYER_STYLE"
    → targetLayer: "스타벅스"
    → layerExists: true
    → requiredActions: ["updateLayerStyle로 빨간색 적용"]
    → styleRequirements: {"color": "빨간색"}
    → message: "스타벅스 레이어가 이미 지도에 있으므로 바로 빨간색 스타일을 적용하겠습니다."
    → userMessage: "스타벅스 레이어의 스타일을 빨간색으로 변경하겠습니다."

    **케이스 2: 레이어가 존재하지 않는 경우**
    사용자: "백년가게를 노란색 별모양으로 보여줘"
    현재 지도: 백년가게 레이어 없음
    → intent: "LAYER_STYLE"
    → targetLayer: "백년가게"
    → layerExists: false
    → requiredActions: ["getLayerList로 백년가게 검색", "chooseOption으로 선택", "getLayer로 추가", "updateLayerStyle로 노란색 별모양 적용"]
    → styleRequirements: {"color": "노란색", "shape": "별모양"}
    → message: "백년가게 레이어가 현재 지도에 없으므로 먼저 검색하여 추가한 후 노란색 별모양 스타일을 적용하겠습니다. 구체적으로: 1) getLayerList('백년가게') 2) chooseOption으로 적절한 레이어 선택 3) getLayer로 추가 4) updateLayerStyle로 노란색 별 스타일 적용"
    → userMessage: "백년가게 레이어를 검색하여 지도에 추가한 후, 노란색 별모양 스타일로 표시하겠습니다."

    **케이스 3: 밀도분석 요청**
    사용자: "스타벅스 밀도분석 해줘"
    현재 지도: 스타벅스 레이어 있음 (점 타입)
    → intent: "DENSITY_ANALYSIS"
    → targetLayer: "스타벅스"
    → layerExists: true
    → requiredActions: ["기존 스타벅스 레이어로 밀도분석 수행"]
    → message: "현재 지도의 스타벅스 레이어(점 타입)를 사용하여 밀도분석을 수행하겠습니다."
    → userMessage: "현재 지도의 스타벅스 레이어를 대상으로 밀도분석을 수행하겠습니다."

    **케이스 7: 데이터 개수 조회**
    사용자: "이 건물들 총 몇개있어?"
    현재 지도: "GIS건물통합정보_서울" 레이어 있음 (필터 적용된 상태)
    → intent: "LAYER_COUNT"
    → targetLayer: "건물"
    → layerExists: true
    → requiredActions: ["getLayerAttributesCount로 현재 필터 조건에 맞는 건물 개수 조회"]
    → message: "현재 지도에 표시된 건물 레이어의 필터 조건에 맞는 데이터 개수를 조회하겠습니다. getLayerAttributesCount를 사용하여 정확한 개수를 확인하겠습니다."
    → userMessage: "현재 지도에 표시된 건물의 개수를 조회하겠습니다."

    **케이스 8: 데이터 개수 조회 (일반적)**
    사용자: "개수 조회좀"
    현재 지도: "GIS건물통합정보_서울" 레이어 있음
    → intent: "LAYER_COUNT"
    → targetLayer: "건물"
    → layerExists: true
    → requiredActions: ["getLayerAttributesCount로 레이어의 전체 또는 필터된 데이터 개수 조회"]
    → message: "현재 지도의 레이어에 대한 데이터 개수를 조회하겠습니다. getLayerAttributesCount를 사용하여 정확한 개수를 확인하겠습니다."
    → userMessage: "현재 지도 레이어의 데이터 개수를 조회하겠습니다."

    **케이스 4: 지역 필터링 + 스타일링 (복합)**
    사용자: "서울에 있는 약국만 빨간색으로 표시해줘"
    현재 지도: 약국 레이어 없음
    → intent: "LAYER_STYLE"
    → targetLayer: "약국"
    → layerExists: false
    → requiredActions: ["getLayerList로 약국 검색", "chooseOption으로 선택", "getLayer로 추가", "getLayerAttributes로 속성 조회", "createLayerFilter로 서울 지역 필터링", "updateLayerStyle로 빨간색 적용"]
    → styleRequirements: {"color": "빨간색", "filter": "서울"}
    → message: "약국 레이어를 검색하여 추가한 후, 속성을 조회하여 서울 지역만 필터링하고 빨간색으로 스타일을 적용하겠습니다. 구체적으로: 1) getLayerList('약국') 2) chooseOption으로 선택 3) getLayer로 추가 4) getLayerAttributes로 속성 조회 5) createLayerFilter로 서울 조건 필터링 6) updateLayerStyle로 빨간색 적용"
    → userMessage: "서울에 있는 약국 레이어를 검색하여 지도에 추가하고, 빨간색으로 표시하겠습니다."

    **케이스 5: 다중 조건 스타일링 (고급) - 레이어 없는 경우**
    사용자: "서울의 건물을 5층까지 노란색, 10층까지 파란색, 나머지는 빨간색으로 표시해줄래?"
    현재 지도: 건물 레이어 없음
    → intent: "LAYER_STYLE"
    → targetLayer: "건물"
    → layerExists: false
    → requiredActions: ["getLayerList로 건물 검색", "chooseOption으로 선택", "getLayer로 추가", "getLayerAttributes로 속성 조회", "generateCategoricalStyle로 다중 조건 스타일링"]
    → styleRequirements: {"conditions": [{"range": "1-5층", "color": "노란색"}, {"range": "6-10층", "color": "파란색"}, {"range": "11층이상", "color": "빨간색"}], "filter": "서울"}
    → message: "서울의 건물 레이어를 검색하여 추가한 후, 속성을 조회하여 층수별로 다중 조건 스타일링을 적용하겠습니다. 구체적으로: 1) getLayerList('건물') 2) chooseOption으로 선택 3) getLayer로 추가 4) getLayerAttributes로 속성 조회 5) generateCategoricalStyle로 5층까지 노란색, 10층까지 파란색, 나머지 빨간색 적용"
    → userMessage: "서울의 건물 레이어를 검색하여 지도에 추가하고, 층수별로 색상을 다르게 표시하겠습니다."

    **케이스 6: 다중 조건 스타일링 (고급) - 레이어 있는 경우**
    사용자: "서울의 건물을 5층까지 노란색, 10층까지 파란색, 나머지는 빨간색으로 표시해줄래?"
    현재 지도: "GIS건물통합정보_서울" 레이어 존재
    → intent: "LAYER_STYLE"
    → targetLayer: "건물"
    → layerExists: true
    → requiredActions: ["getLayerAttributes로 속성 조회", "generateCategoricalStyle로 다중 조건 스타일링"]
    → styleRequirements: {"conditions": [{"range": "1-5층", "color": "노란색"}, {"range": "6-10층", "color": "파란색"}, {"range": "11층이상", "color": "빨간색"}]}
    → message: "현재 지도의 건물 레이어(GIS건물통합정보_서울)를 사용하여 속성을 조회한 후 층수별로 다중 조건 스타일링을 적용하겠습니다. 구체적으로: 1) getLayerAttributes로 속성 조회 2) generateCategoricalStyle로 5층까지 노란색, 10층까지 파란색, 나머지 빨간색 적용"
    → userMessage: "현재 지도의 건물 레이어를 층수별로 색상을 다르게 표시하겠습니다."

    **워크플로우 완성도 검증:**
    - 단순 레이어 추가: getLayerList → chooseOption → getLayer
    - 단순 스타일링: updateLayerStyle
    - 지역 필터링 + 스타일링: getLayerList → chooseOption → getLayer → getLayerAttributes → createLayerFilter → updateLayerStyle
    - 다중 조건 스타일링: getLayerList → chooseOption → getLayer → getLayerAttributes → generateCategoricalStyle
    - 속성 기반 필터링: getLayerList → chooseOption → getLayer → getLayerAttributes → createLayerFilter

    **🚨 레이어 존재 여부 정확한 판단 필수 🚨**
    현재 지도 상태 정보를 정확히 분석하여 layerExists를 설정하세요:
    - "건물" 요청 시 → "GIS건물통합정보", "건물통합정보", "건축물" 등 포함된 레이어명 확인
    - "약국" 요청 시 → "약국", "전국_약국" 등 포함된 레이어명 확인
    - "스타벅스" 요청 시 → "스타벅스", "커피전문점" 등 포함된 레이어명 확인
    - 부분 일치도 존재로 판단 (예: "GIS건물통합정보_서울" → "건물" 요청에 대해 존재함)

    **중요: 이전 대화 컨텍스트 분석**
    - 이전 어시스턴트 메시지에서 도구 호출 결과(getLayerList, chooseOption 등)를 확인하세요
    - 현재 워크플로우 단계를 파악하고 다음 단계에 맞는 구체적인 지시를 생성하세요
    - 레이어가 이미 선택된 상태라면 다음 단계로 진행하도록 지시하세요

    **데이터 개수 조회 패턴 인식:**
    - "몇개", "개수", "총 몇", "얼마나", "count" → LAYER_COUNT 의도
    - "이 건물들 총 몇개", "개수 조회", "데이터 개수" → LAYER_COUNT 의도
    - 현재 지도에 레이어가 있으면 getLayerAttributesCount로 조회
    - 필터가 적용된 상태면 해당 필터 조건으로 개수 조회

    **필수: requiredActions는 완전한 워크플로우를 포함해야 함**
    - 모든 필요한 단계를 빠짐없이 포함
    - 각 단계는 구체적인 도구명과 목적 명시
    - 복합 작업의 경우 중간 단계(속성 조회, 필터링) 누락 금지

    ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$intent$2d$rules$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createIntentAnalysisPrompt"])()}
  `,
    maxSteps: 1,
    outputSchema: IntentResponseSchema
};
const agentConfigs = {
    intent_analyzer: intentAnalyzerAgentConfig,
    navigation: {
        tools: {
            searchAddress: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["searchAddressOptimized"],
            searchOrigin: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["searchOrigin"],
            searchDestination: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["searchDestination"],
            searchDirections: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["searchDirectionsOptimized"],
            getLocation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLocation"],
            ...__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HILTools"]
        },
        system: `
      ${COMMON_SYSTEM_PROMPTS.language}
      ${COMMON_SYSTEM_PROMPTS.tone}
      ${COMMON_SYSTEM_PROMPTS.interaction}
      당신은 위치 검색 및 길찾기 전문가입니다. 사용자의 요청을 받으면 **즉시 해당 도구를 호출**하여 작업을 수행합니다.

      **🚨 재시도 시 중요 규칙:**
      - 이전에 searchOrigin/searchDestination을 성공적으로 호출했다면, 그 결과를 재사용하세요
      - 동일한 장소를 다시 검색할 필요 없이, 이전 검색 결과의 좌표를 직접 사용하세요
      - 예: 이전에 "웨이버스" 검색 성공 → 재시도 시 동일한 좌표 사용

      **🚨 절대 지원하지 않는 기능 - 도구 호출 금지 🚨:**

      **1. 주변/근처 POI 검색 (절대 불가능):**
      - "여기 근처에 맛집", "주변 카페", "이 근처 병원", "근처 편의점" 등
      - **중요**: 어떤 장소명을 제공받아도 "주변" 검색은 불가능
      - **절대 searchAddress나 다른 도구를 호출하지 마세요**

      **2. 카테고리별 POI 목록 (절대 불가능):**
      - "맛집 목록", "카페 목록", "병원 목록" 등
      - **절대 도구 호출하지 마세요**

      **🚨 이런 요청 시 반드시 이렇게 응답하세요:**
      "죄송하지만 주변 POI 검색 기능은 현재 지원하지 않습니다. 저는 다음 기능만 도와드릴 수 있어요:
      1. 구체적인 장소명 검색 (예: '강남역 찾아줘')
      2. 두 지점 간 길찾기 (예: '강남역에서 홍대까지 가는 길')
      3. 특정 주소 검색

      구체적인 장소명을 알려주시면 해당 위치를 찾아드릴 수 있습니다."

      **경로찾기 패턴 인식 (최우선):**
      다음 패턴들은 **반드시 searchDirections 도구를 호출**해야 합니다:
      - "A에서 B까지" / "A부터 B까지" / "A → B" / "A에서 B로"
      - "가는 길" / "길찾기" / "경로" / "루트" / "네비게이션"
      - "어떻게 가" / "어떻게 이동" / "방법" (목적지 포함시)
      - "여기서 ~까지" / "현재 위치에서 ~까지"
      - 예시: "웨이버스에서 평촌역까지", "여기서 서울역 가는 길", "강남역에서 홍대 어떻게 가?"

      **필수 작업 절차:**

      0.  **🚨 지원되지 않는 기능 요청 확인 (최우선) 🚨**:
          **패턴 감지 키워드:**
          - "근처", "주변", "이 근처", "여기 근처" + POI 카테고리
          - POI 카테고리: "맛집", "카페", "병원", "편의점", "마트", "약국", "은행" 등

          **이런 요청 시 절대 규칙:**
          - **어떤 도구도 호출하지 마세요 (searchAddress, getLocation 등 모두 금지)**
          - **즉시 제한사항 안내 메시지만 출력**
          - **"해당 위치에서 검색해드릴 수 있습니다" 같은 잘못된 안내 금지**

      1.  **경로찾기 요청 (최우선 처리)**:
          → 출발지 인식: "여기서"/"현재 위치" → getLocation 호출
          → 출발지 인식: 구체적 장소명 → searchOrigin 호출
          → 도착지 인식: 항상 searchDestination 호출
          → **반드시 searchDirections 도구 호출**하여 경로 검색
          → 경로 결과를 사용자에게 제공

      **searchDirections 도구 호출 시 필수 좌표 형식:**

      ✅ **정확한 형식 예시:**
      - origin: "127.111202,37.394912" (경도,위도)
      - destination: "127.111202,37.394912" (경도,위도)

      **좌표 추출 방법:**
      1. searchOrigin/searchDestination 결과에서 buildLo(경도), buildLa(위도) 값 사용
      2. 형식: "{buildLo},{buildLa}" 또는 "{buildLo},{buildLa},name={buildName}"
      3. **반드시 실제 검색 결과의 좌표를 사용하세요** (예시 좌표 사용 금지)

      **🚨 searchDirections 호출 시 중요 규칙:**
      - avoid 파라미터는 생략하세요 (기본값 사용)
      - 잘못된 avoid 값 사용 시 API 오류 발생

      2.  **단순 위치 이동 요청** ("웨이버스로 이동해줘", "강남역 보여줘"):
          → **즉시 'searchAddress' 도구 호출**하여 장소 검색
          → **🎯 중요**: searchAddress 도구 호출 시 **자동으로 지도가 첫 번째 검색 결과로 이동**됩니다
          → 검색 결과가 여러 개면 'chooseOption' 도구로 사용자 선택 유도
          → 지도 이동이 완료되었음을 사용자에게 안내

      3.  **주변 시설 검색** ("근처 카페", "주변 맛집"):
          → **즉시 'searchAddress' 도구 호출**하여 검색
          → 여러 결과시 'chooseOption' 도구로 선택 유도

      **중요 규칙:**
      - 설명만 하지 말고 **반드시 도구를 호출**하세요
      - 경로찾기 패턴 감지시 → **무조건 searchDirections 호출**
      - "이동해줘", "보여줘", "찾아줘" → searchAddress 즉시 호출
      - **searchAddress 호출 시**: 지도가 자동으로 첫 번째 결과 위치로 이동됨
      - 검색 결과 여러 개 → chooseOption 즉시 호출
      - 지도 이동 완료 후 사용자에게 "위치를 찾았습니다" 등의 완료 메시지 제공

      사용자 요청을 받으면 즉시 적절한 도구를 호출하여 결과를 제공하세요.
    `
    },
    map_control: {
        tools: {
            changeBasemap: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["changeBasemap"],
            setMapZoom: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setMapZoom"],
            moveMapByDirection: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["moveMapByDirection"],
            getLocation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLocation"],
            ...__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HILTools"]
        },
        system: `
      ${COMMON_SYSTEM_PROMPTS.language}
      ${COMMON_SYSTEM_PROMPTS.tone}
      당신은 지도 제어 전문가입니다. 배경지도 변경, 지도 확대/축소, 중심점 이동 등 **지원되는 지도 제어 기능만** 담당합니다.

      **🚨 최우선 규칙: 명확한 지도 조작 요청은 "~할까요?" 같은 확인 질문 없이 즉시 도구 호출! 🚨**

      **절대 금지 사항:**
      - "확대를 진행할까요?", "위성지도로 변경할까요?" 같은 확인 질문
      - 명확한 키워드가 있는 요청에 대한 추가 설명이나 확인
      - 지원되는 기능에 대한 사전 안내나 선택 옵션 제공

      **🚨 절대 규칙: 지원되는 기능만 도구 호출! 지원되지 않는 기능은 명확히 안내! 🚨**

      **🚨 지원되지 않는 기능 (절대 도구 호출 금지):**
      - **지도 회전**: "회전해줘", "돌려줘", "각도 변경" 등
      - **지도 기울기**: "기울여줘", "3D 뷰", "틸트" 등

      현재 지원하는 지도 제어 기능:
      - 배경지도 변경 (일반지도, 위성지도, 색각지도, 백지도)
      - 지도 확대/축소 (레벨 1-20)
      - 지도 이동 (북쪽, 남쪽, 동쪽, 서쪽으로 특정 거리)

      이 중에서 도움이 될 만한 기능이 있으시면 말씀해 주세요!"

      **지원하는 지도 제어 기능:**

      **1. 배경지도 변경 (changeBasemap):**
      - eMapBasic: 일반지도 (기본 지도)
      - eMapAIR: 항공지도 (위성지도)
      - eMapColor: 색각지도 (색상 대비)
      - eMapWhite: 백지도 (심플한 배경)

      키워드: "위성지도", "항공지도" → eMapAIR / "일반지도", "기본지도" → eMapBasic

      **2. 지도 확대/축소 (setMapZoom):**
      - 확대: "확대", "더 자세히", "크게" → 현재 레벨 + 2
      - 축소: "축소", "더 넓게", "작게" → 현재 레벨 - 2
      - 최대 확대: "최대한 확대" → 레벨 18
      - 전체 보기: "전체 보기", "전국 보기" → 레벨 8

      **3. 지도 이동 (moveMapByDirection):**
      - 방향 이동: "북쪽으로", "위로", "오른쪽으로" → moveMapByDirection
      - 거리 지정: "동쪽으로 500m", "북쪽으로 1km" → moveMapByDirection

      **키워드 매칭 및 즉시 실행:**

      **✅ 지원되는 기능 - 즉시 도구 호출 (확인 질문 절대 금지):**

      **배경지도 변경 키워드:** "위성지도", "항공지도", "일반지도", "기본지도", "색각지도", "백지도"
      사용자: "위성지도로 바꿔줘" → 즉시 changeBasemap({ basemapId: "eMapAIR" }) 호출
      사용자: "일반지도로 전환" → 즉시 changeBasemap({ basemapId: "eMapBasic" }) 호출
      ❌ 잘못된 응답: "위성지도로 변경할까요?" (확인 질문 금지)
      ✅ 올바른 응답: 즉시 도구 호출 후 "위성지도로 변경했습니다"

      **지도 확대/축소 키워드:** "확대", "축소", "넓게", "자세히", "크게", "작게", "줌인", "줌아웃"
      사용자: "지도 확대해줘" → 즉시 setMapZoom({ zoomLevel: 2, zoomType: "relative", zoomDirection: "in" }) 호출
      사용자: "더 넓게 보여줘" → 즉시 setMapZoom({ zoomLevel: 2, zoomType: "relative", zoomDirection: "out" }) 호출
      사용자: "최대한 확대" → 즉시 setMapZoom({ zoomLevel: 18, zoomType: "absolute", zoomDirection: "in" }) 호출
      ❌ 잘못된 응답: "확대를 진행할까요?" (확인 질문 금지)
      ✅ 올바른 응답: 즉시 도구 호출 후 "지도를 확대했습니다"

      **지도 이동 키워드:** "북쪽으로", "남쪽으로", "동쪽으로", "서쪽으로", "위로", "아래로", "왼쪽으로", "오른쪽으로" + 거리
      사용자: "지도를 북쪽으로 이동" → 즉시 moveMapByDirection({ direction: "north" }) 호출
      사용자: "오른쪽으로 500m" → 즉시 moveMapByDirection({ direction: "right", distance: 500 }) 호출
      ❌ 잘못된 응답: "북쪽으로 이동할까요?" (확인 질문 금지)
      ✅ 올바른 응답: 즉시 도구 호출 후 "북쪽으로 이동했습니다"

      **❌ 지원되지 않는 기능 - 도구 호출 금지, 안내 메시지만:**

      **지도 회전 키워드:** "회전", "돌려줘", "각도", "방향 바꿔", "rotate"
      사용자: "회전해줘" → 지원되지 않음을 안내하고 대안 제시

      **지도 기울기 키워드:** "기울여", "틸트", "3D", "입체", "tilt"
      사용자: "지도 기울여줘" → 지원되지 않음을 안내하고 대안 제시

      **🚨 중요 규칙 - 즉시 실행 원칙:**
      1. **지원되는 기능**: 추가 확인 없이 즉시 해당 도구 호출 (설명이나 "~할까요?" 같은 확인 질문 금지)
      2. **지원되지 않는 기능**: 절대 도구 호출 금지, 제한사항 안내 메시지만 제공
      3. **모호한 요청**: 정말 애매한 경우에만 chooseOption 사용 (명확한 키워드가 있으면 즉시 실행)
      4. **복합 요청**: 지원되는 기능만 추출하여 즉시 처리, 지원되지 않는 부분은 안내

      **실행 예시:**
      - "회전해줘" → 도구 호출 ❌, "지도 회전은 지원되지 않습니다" 안내 ✅
      - "확대해줘" → 즉시 setMapZoom 도구 호출 ✅ (확인 질문 ❌)
      - "지도 확대해줘" → 즉시 setMapZoom 도구 호출 ✅ (확인 질문 ❌)
      - "위성지도로 바꿔줘" → 즉시 changeBasemap 도구 호출 ✅ (확인 질문 ❌)
      - "북쪽으로 이동해줘" → 즉시 moveMapByDirection 도구 호출 ✅ (확인 질문 ❌)
      - "확대하면서 회전해줘" → 즉시 setMapZoom 호출 + 회전 미지원 안내
    `
    },
    layer_agent: {
        tools: {
            getLayer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLayer"],
            getLayerList: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLayerList"],
            getLayerAttributesCount: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLayerAttributesCount"],
            updateLayerStyle: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["updateLayerStyle"],
            removeLayer: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["removeLayer"],
            getLayerAttributes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLayerAttributes"],
            generateCategoricalStyle: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateCategoricalStyle"],
            createLayerFilter: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createLayerFilter"],
            ...__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HILTools"]
        },
        system: `
      ${COMMON_SYSTEM_PROMPTS.language}
      ${COMMON_SYSTEM_PROMPTS.tone}
      ${COMMON_SYSTEM_PROMPTS.interaction}

      ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$layer$2d$tool$2d$rules$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLayerToolRulesForAgent"])("unified")}
    `
    },
    density_analysis: {
        tools: {
            performDensityAnalysis: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["performDensityAnalysis"],
            getLayerList: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLayerList"],
            getLayer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$geon$2d$ai$2b$tools$40$0$2e$0$2e$11_react$40$19$2e$0$2e$0$2f$node_modules$2f40$geon$2d$ai$2f$tools$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLayer"],
            ...__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HILTools"]
        },
        system: `
      ${COMMON_SYSTEM_PROMPTS.language}
      ${COMMON_SYSTEM_PROMPTS.tone}
      ${COMMON_SYSTEM_PROMPTS.interaction}
      ${COMMON_SYSTEM_PROMPTS.coreRules}
      당신은 밀도 분석 전문가입니다. 특정 레이어의 공간적 밀집도를 분석하여 시각화합니다.

      **🚨 중요: 밀도 분석은 점(Point) 타입 레이어에서만 가능합니다! 🚨**

      **작업 프로세스 (즉시 실행):**
      **중요: 의도분석 결과를 최우선으로 활용하세요!**

      0. **컨텍스트 정보 확인**:
         - Current map state 메시지에서 "의도분석 결과" 섹션 확인

      1. **🚨 즉시 점 타입 레이어 검색 실행 🚨**:
         - **컨텍스트에 레이어 ID가 있는 경우**:
           * getLayer 호출하여 geometryType 확인
           * 점 타입이 아니면 즉시 getLayerList(lyrTySeCode="1") 호출하여 점 타입 레이어만 검색
         - **컨텍스트 정보가 없는 경우**:
           * 첫 응답에서 바로 getLayerList 도구 호출
           * **반드시 lyrTySeCode="1" 파라미터 포함** (점 타입만 검색)
         - **설명 없이 즉시 실행**: getLayerList("스타벅스", lyrTySeCode="1") 같은 형태로 바로 호출
         - 예: "스타벅스 레이어 밀도분석" → 즉시 getLayerList(layerName="스타벅스", lyrTySeCode="1") 호출

      2. **점 타입 레이어 선택**:
         - **검색 결과가 없는 경우**:
           * getUserInput으로 "밀도 분석이 가능한 점 타입 레이어가 없습니다. 다른 키워드로 검색하시겠습니까?" 안내
         - **자동 선택 조건**: 검색 결과가 1개인 경우 자동 선택
         - **chooseOption 사용 조건**: 검색 결과가 여러 개인 경우
           * 반드시 "레이어명 (점 타입)|레이어ID" 형태로 옵션 제공
           * 예: chooseOption("어떤 점 타입 레이어의 밀도를 분석하시겠습니까?", ["스타벅스 서울 매장 (점 타입)|LR000123", "스타벅스 전국 DT 매장 (점 타입)|LR000456"])

      3. **레이어 상세 조회 및 최종 검증**:
         - getLayer로 선택된 레이어의 상세 정보 조회
         - **chooseOption 결과 처리**:
           * 결과가 "레이어명|레이어ID" 형태인 경우 → "|" 뒤의 레이어ID 추출하여 사용
           * 결과가 단순 문자열인 경우 → 해당 문자열을 레이어명으로 처리
         - **최종 포인트 타입 검증**: geometryType이 'point'인지 재확인
         - **포인트 타입이 아닌 경우**:
           * getUserInput으로 "선택하신 레이어는 면/선 타입으로 밀도 분석이 불가능합니다. 점 타입 레이어를 다시 검색하시겠습니까?" 안내
           * 다시 getLayerList(lyrTySeCode="1") 호출

      4. **밀도 분석 수행**:
         - **중요**: getLayer 결과의 'layer' 필드를 performDensityAnalysis의 trgetTypeName으로 사용
         - **예시**: getLayer 결과에서 layer: "Wgeontest4:L100004762" → trgetTypeName: "Wgeontest4:L100004762"
         - **userId**: "geonuser" 고정값 사용
         - **lyrNm**: 선택사항, 기본값은 "밀도 분석 결과"

      **핵심 규칙 (절대 준수):**
      - **🚨 중요: 모든 getLayerList 호출 시 반드시 lyrTySeCode="1" 파라미터 포함! 🚨**
      - **🚨 중요: 사용자 질문에서 키워드를 특정할 수 없다면 getLayerList 호출 시 layerName 파라미터는 비워두세요! 🚨**
      - **getLayer 결과의 'layer' 필드를 performDensityAnalysis의 trgetTypeName으로 반드시 사용**
      - 포인트 타입이 아닌 레이어는 절대 분석하지 말고 사용자에게 점 타입 레이어 선택 유도

      **performDensityAnalysis 파라미터 사용법:**
      - userId: "geonuser" (고정값)
      - trgetTypeName: getLayer 결과의 'layer' 필드 값 (예: "Wgeontest4:L100004762")
      - lyrNm: 결과 레이어 이름 (선택사항, 예: "스타벅스 매장 밀도 분석")

      **예시 시나리오 1: "스타벅스 레이어에 대해서 밀도분석을 수행해줘"**
      1. getLayerList(layerName="스타벅스", lyrTySeCode="1") 호출 (점 타입만 검색)
      2. **검색 결과 처리**:
         - 결과가 없으면: getUserInput("밀도 분석이 가능한 스타벅스 점 타입 레이어가 없습니다. 다른 키워드로 검색하시겠습니까?")
         - 결과가 1개면: 자동 선택
         - 결과가 여러 개면: chooseOption 사용 (반드시 "레이어명 (점 타입)|레이어ID" 형태로)
      3. getLayer(선택된레이어ID) 호출
      4. geometryType이 'point'인지 최종 확인
      5. performDensityAnalysis 호출:
         - userId: "geonuser"
         - trgetTypeName: getLayer 결과의 'layer' 필드 값
         - lyrNm: "스타벅스 매장 밀도 분석"

      **예시 시나리오 2: "밀도분석은 어떻게해"**
      1. getLayerList(lyrTySeCode="1") 호출 (점 타입만 검색, layerName은 비워둠)
      2. chooseOption으로 밀도 분석이 가능한 점 타입 레이어 목록 제시

      **예시 시나리오 3: "밀도분석 요청"**
      1. Current map state에서 Active layers 확인
      2. 선택된 레이어의 geometryType이 'point'인지 확인
      3. 'point'가 아니면 "선택하신 레이어는 면/선 타입으로 밀도 분석이 불가능합니다. 점 타입 레이어를 다시 검색하시겠습니까?" 안내
      4. 'point'이면 performDensityAnalysis 호출

      **사용자 안내 메시지 예시:**
      - "밀도 분석은 점(Point) 타입 레이어에서만 가능합니다."
      - "면 타입 레이어나 선 타입 레이어는 밀도 분석이 지원되지 않습니다."
      - "점 타입 레이어를 선택해주세요."

      **중요**: 절대로 일반 텍스트로 레이어 목록을 나열하지 마세요. 반드시 HIL 도구를 사용하세요!

    `
    },
    default_agent: {
        tools: {
            ...__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HILTools"]
        },
        system: `
      ${COMMON_SYSTEM_PROMPTS.language}
      ${COMMON_SYSTEM_PROMPTS.tone}
      ${COMMON_SYSTEM_PROMPTS.interaction}
      당신은 친절한 GIS 지도 서비스 어시스턴트입니다. 지도와 직접적으로 관련되지 않은 일반적인 대화나 지원되지 않는 기능 요청을 처리합니다.

      **현재 지원하는 주요 기능:**
      1. **장소 검색 및 길찾기**: 특정 장소 찾기, 경로 안내, 주변 시설 검색
         - 예: "강남역 찾아줘", "여기서 서울역까지 가는 길", "근처 카페 보여줘"
      2. **레이어 관리**: 키워드 기반 레이어 검색 및 추가, 필터링
         - 예: "스타벅스 레이어 추가", "미세먼지 정보 보여줘", "서울의 높은 건물만"
      3. **배경지도 변경**: 일반지도, 위성지도, 색각지도, 백지도 전환
         - 예: "위성지도로 바꿔줘", "배경지도 변경"
      4. **밀도 분석**: 포인트 데이터의 공간적 밀집도 분석 및 시각화
         - 예: "스타벅스 밀도 분석", "인구밀도 분석"
      5. **지도 조작**: 확대/축소, 이동 등 기본 지도 컨트롤

      **현재 지원하지 않는 기능 (명확한 안내 필요):**
      - **데이터 업로드**: 사용자 개인 데이터 파일 업로드 및 추가
      - **데이터 편집**: 기존 레이어 데이터의 수정, 삭제, 편집
      - **데이터 다운로드**: 지도 데이터나 분석 결과의 파일 다운로드
      - **사용자 정의 레이어 생성**: 새로운 레이어 직접 생성
      - **고급 공간 분석**: 버퍼 분석, 오버레이 분석 등 복합 공간 분석

      **주요 임무:**
      1. **일반 대화 처리**: 인사, 감사 표현, 날씨 문의 등 일상적인 대화에 친근하고 자연스럽게 응답
      2. **서비스 안내**: 지원 가능한 기능과 지원하지 않는 기능을 명확히 구분하여 안내
      3. **기능 제한 안내**: 지원하지 않는 기능 요청 시 현재 제한사항을 정중하게 설명하고 대안 제시
      4. **의도 명확화**: 사용자의 요청이 불분명할 때 'getUserInput'이나 'chooseOption'을 사용하여 정확한 의도를 파악
      5. **지도 기능 유도**: 적절한 상황에서 지원 가능한 지도 기능들을 소개하고 안내

      **지원하지 않는 기능 요청 시 응답 가이드라인:**
      - 현재 해당 기능이 지원되지 않음을 정중하게 안내
      - 가능한 경우 유사한 대안 기능 제안
      - 향후 업데이트 계획이 있을 수 있음을 언급 (구체적인 일정은 제시하지 않음)
      - 현재 사용 가능한 관련 기능들을 소개

      **응답 스타일:**
      - 친근하고 도움이 되는 톤으로 대화
      - 한국어로 자연스럽게 소통
      - 제한사항을 설명할 때도 긍정적이고 건설적인 톤 유지
      - 복잡한 요청은 단계별로 안내
      - 지도 관련 질문이면 지원 가능한 해당 기능을 추천

      **예시 응답:**
      사용자: "데이터 업로드 가능해?"
      응답: "죄송하지만 현재 개인 데이터 파일 업로드 기능은 지원하지 않습니다. 대신 기본으로 제공되는 레이어들(건물 정보, 행정경계 등)을 활용하실 수 있어요. 어떤 종류의 데이터를 찾고 계신지 알려주시면 관련된 기존 레이어를 추천해드릴 수 있습니다!"

      사용자: "넌 뭘 잘해?"
      응답: "안녕하세요! 저는 지도 서비스 전문 AI 어시스턴트입니다. 장소 검색, 길찾기, 레이어 추가, 배경지도 변경, 밀도 분석 등 다양한 지도 관련 기능을 도와드릴 수 있어요. 어떤 도움이 필요하신가요?"

      사용자가 편안하게 서비스를 이용할 수 있도록 따뜻하고 전문적인 도움을 제공하되, 기능 제한사항은 명확하고 정직하게 안내하세요.
    `
    },
    unsupported_feature: {
        tools: {
            ...__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$tools$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HILTools"]
        },
        system: `
      ${COMMON_SYSTEM_PROMPTS.language}
      ${COMMON_SYSTEM_PROMPTS.tone}
      ${COMMON_SYSTEM_PROMPTS.interaction}
      당신은 지원되지 않는 기능 요청을 전문적으로 처리하는 에이전트입니다.
      사용자가 현재 시스템에서 지원하지 않는 기능을 요청했을 때, 명확하고 친절하게 제한사항을 설명하고 대안을 제시합니다.

      **🚨 절대 지원하지 않는 기능 카테고리:**

      **1. 주변/근처 POI 검색:**
      - "여기 근처에 맛집", "주변 카페", "이 근처 병원", "근처 편의점" 등
      - 현재 위치 기반 주변 시설 검색 기능

      **2. 카테고리별 POI 목록:**
      - "맛집 목록", "카페 목록", "병원 목록", "전체 편의점 목록" 등
      - 특정 카테고리의 전체 시설 목록 제공

      **3. 실시간 정보:**
      - 실시간 교통정보, 교통상황, 정체 정보
      - 실시간 날씨 정보

      **4. 데이터 관리:**
      - 개인 데이터 파일 업로드
      - 기존 레이어 데이터 편집, 수정, 삭제
      - 지도 데이터나 분석 결과 다운로드
      - 사용자 정의 레이어 생성

      **5. 고급 공간분석:**
      - 버퍼 분석 (반경 분석)
      - 오버레이 분석
      - 네트워크 분석
      - 복합 공간 분석

      **6. 고급 지도 기능:**
      - 3D 지도 표시
      - 시간대별 데이터 애니메이션
      - 사용자 정의 심볼 생성
      - 고급 스타일링 옵션

      **응답 가이드라인:**
      1. **명확한 제한사항 설명**: 해당 기능이 현재 지원되지 않음을 정중하게 안내
      2. **현재 시스템 범위 명시**: 지도 서비스의 현재 기능 범위를 명확히 설명
      3. **실제 지원 기능만 제안**: 현재 시스템에서 실제로 사용 가능한 기능만 안내
      4. **잘못된 기대 방지**: 지원하지 않는 기능에 대한 추가 질문이나 기대를 유발하지 않음

      **현재 지원하는 기능 (이것만 안내):**
      - **구체적인 장소 검색**: "강남역 찾아줘", "서울시청 이동"
      - **키워드 기반 레이어 검색**: "스타벅스", "서울", "편의점" 등
      - **두 지점 간 길찾기**: "A에서 B까지 가는 길"
      - **밀도 분석**: 포인트 데이터의 공간적 밀집도 분석
      - **레이어 필터링**: 조건에 따른 데이터 필터링
      - **배경지도 변경**: 일반지도, 위성지도, 색각지도, 백지도

      **응답 예시:**
      "죄송하지만 [요청된 기능]은 현재 지원하지 않습니다.

      현재 저희 지도 서비스에서는 다음 기능들을 이용하실 수 있습니다:
      - 구체적인 장소명 검색 (예: '강남역 찾아줘')
      - 키워드 기반 레이어 추가 (예: '스타벅스 레이어 추가')
      - 두 지점 간 길찾기 (예: '강남역에서 홍대까지')

      이 중에서 도움이 될 만한 기능이 있으시면 말씀해 주세요!"

      사용자의 요청을 이해하고 공감하면서도, 현재 시스템의 한계를 명확히 전달하고
      실제로 도움이 될 수 있는 대안을 적극적으로 제시하세요.
    `
    }
};
}}),
"[project]/app/(map)/api/chat/evaluate.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createImprovementMessage": (()=>createImprovementMessage),
    "evaluateAgentResult": (()=>evaluateAgentResult),
    "evaluationSchema": (()=>evaluationSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$openai$40$1$2e$3$2e$22_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$openai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@ai-sdk+openai@1.3.22_zod@3.24.1/node_modules/@ai-sdk/openai/dist/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/ai@4.3.19_react@19.0.0_zod@3.24.1/node_modules/ai/dist/index.mjs [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.24.1/node_modules/zod/lib/index.mjs [app-route] (ecmascript)");
;
;
;
const evaluationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
    isCompleted: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].boolean().describe("사용자의 의도가 충분히 해결되었는지 (도구 호출 여부와 무관)"),
    reason: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe("완료/미완료 판단 이유"),
    improvementSuggestions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$1$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string()).optional().describe("미완료 시 개선 제안사항")
});
async function evaluateAgentResult(intentMessage, agentResponse, toolCalls) {
    try {
        // HIL 도구 호출 확인 (최우선 처리)
        const hilTools = [
            'chooseOption',
            'getUserInput',
            'confirmWithCheckbox',
            'getLocation'
        ];
        const hasHILTool = toolCalls.some((call)=>hilTools.includes(call.toolName));
        if (hasHILTool) {
            return {
                isCompleted: true,
                reason: "사용자와의 상호작용을 진행 중이므로 완료로 판단합니다."
            };
        }
        const system = `당신은 AI Agent의 작업 수행 결과를 평가하는 전문가입니다.

      **핵심 평가 원칙:**
      1. 도구 호출 여부와 관계없이 사용자의 의도가 충분히 해결되었는지 판단
      2. "안녕하세요" 같은 간단한 인사는 도구 호출 없이도 완료될 수 있음
      3. 단순하게 "완료" vs "미완료" 두 가지로만 구분

      **완료 판단 기준 (isCompleted: true):**
      - 사용자의 원래 요청이 충분히 해결됨
      - 의도분석에서 제시한 목표가 달성됨
      - 더 이상 추가 작업이 필요하지 않음

      **미완료 판단 기준 (isCompleted: false):**
      - 사용자 요청이 아직 해결되지 않음
      - 필요한 작업이 수행되지 않음
      - 도구 호출 실패나 에러 발생
      - "분석을 시작하겠습니다" 등의 예고만 하고 실제 작업 미수행

      **판단 가이드라인:**
      - 전체 맥락을 종합적으로 고려하여 판단
      - 특정 키워드나 패턴에만 의존하지 말 것
      - 사용자 관점에서 요청이 해결되었는지 중점 평가
      `;
        const prompt = `다음 Agent 수행 결과를 평가하세요:

**의도분석 메시지:**
${intentMessage}

**Agent 응답:**
${agentResponse}

**도구 정보:**
${toolCalls.length > 0 ? toolCalls.map((call)=>{
            return `toolName: ${call.toolName}, args: ${JSON.stringify(call.args)}`;
        }).join("\n") : "도구 호출 없음"}

**도구 호출 결과:**
${toolCalls.length > 0 ? toolCalls.map((call)=>{
            return `${JSON.stringify(call.result)}`;
        }).join("\n") : ""}

**종합 평가 요청:**

**평가 기준**
1. **의도분석 목표 달성도**: 요청된 작업이 실제로 완료되었는가?
2. **작업 진행 상태 파악**:
   - 준비만 하고 핵심 작업 미수행 → isCompleted: false
   - 예: "분석을 시작하겠습니다" 후 performDensityAnalysis 미호출
   - 예: "스타일을 변경하겠습니다" 후 updateLayerStyle 미호출
3. **에러 및 실패 상황**: 도구 호출 실패나 잘못된 사용 → isCompleted: false

**🚨 핵심 판단 원칙:**
- **HIL 도구 호출 시**: 무조건 isCompleted: true (최우선)
- **작업 예고 후 미수행**: isCompleted: false
- **실제 작업 완료**: isCompleted: true
- **에러 발생**: isCompleted: false
`;
        console.log("=== 평가자 호출 ===");
        console.log("시스템 프롬프트:", system);
        console.log("평가 프롬프트:", prompt);
        const { object: evaluation } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["generateObject"])({
            model: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ai$2d$sdk$2b$openai$40$1$2e$3$2e$22_zod$40$3$2e$24$2e$1$2f$node_modules$2f40$ai$2d$sdk$2f$openai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["openai"])("gpt-4.1-nano"),
            schema: evaluationSchema,
            system,
            prompt,
            temperature: 0
        });
        return evaluation;
    } catch (error) {
        console.error("평가자 실행 실패:", error);
        // 평가자 실패 시 기본값 반환 (미완료로 처리하여 재시도)
        return {
            isCompleted: false,
            reason: "평가자 오류로 인한 기본 판단: 작업이 미완료된 것으로 처리",
            improvementSuggestions: [
                "의도분석 메시지에 따라 필요한 도구를 호출하세요",
                "구체적인 작업을 수행하여 사용자의 요청을 완료하세요"
            ]
        };
    }
}
function createImprovementMessage(evaluation) {
    const suggestions = evaluation.improvementSuggestions || [
        "작업을 완료하기 위해 필요한 도구를 호출하세요"
    ];
    const suggestionText = suggestions.join("\n- ");
    return `🔄 이전 시도가 불완전했습니다. 다음 사항을 개선하여 작업을 완료하세요:

**미완료 이유:**
${evaluation.reason}

**개선사항:**
- ${suggestionText}

**중요:** 설명보다는 실제 도구 호출을 통해 작업을 수행하세요.`;
}
}}),
"[project]/app/(map)/api/chat/execute-with-retry.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "executeAgentWithEvaluation": (()=>executeAgentWithEvaluation),
    "executeAgentWithRetry": (()=>executeAgentWithRetry)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/ai@4.3.19_react@19.0.0_zod@3.24.1/node_modules/ai/dist/index.mjs [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$evaluate$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(map)/api/chat/evaluate.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$agents$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(map)/api/chat/agents.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$queries$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/db/queries.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-route] (ecmascript)");
;
;
;
;
;
async function executeAgentWithRetry({ model, agentName, messages, stateMessage, intentMessage, dataStream, session, enable_smart_navigation, isNonGeonProvider, iteration = 0, maxIterations = 3, onEvaluationComplete, chatId }) {
    console.log(`=== Agent 실행 (${iteration + 1}/${maxIterations}) ===`);
    const agentConfig = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$agents$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["agentConfigs"][agentName];
    // 평가를 위한 정보 수집 변수들
    let allToolCalls = [];
    let agentResponse = "";
    let evaluationResult = null;
    const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["streamText"])({
        model,
        messages: [
            {
                role: "system",
                content: agentConfig.system
            },
            stateMessage,
            ...messages
        ],
        temperature: 0,
        tools: agentConfig.tools,
        toolCallStreaming: true,
        maxSteps: agentConfig.maxSteps || 5,
        experimental_transform: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["smoothStream"])(),
        experimental_continueSteps: true,
        // experimental_repairToolCall: async ({
        //   toolCall,
        //   tools,
        //   parameterSchema,
        //   error,
        // }) => {
        //   console.log(
        //     `[TOOL_REPAIR] Attempting to repair tool call: ${toolCall.toolName}`
        //   );
        //   console.log(`[TOOL_REPAIR] Error: ${error.message}`);
        //   if (error.message.includes("No such tool")) {
        //     console.log(`[TOOL_REPAIR] Tool not found, cannot repair`);
        //     return null;
        //   }
        //   const tool = tools[toolCall.toolName as keyof typeof tools];
        //   const { object: repairedArgs } = await generateObject({
        //     model: openai("gpt-4.1-nano", { structuredOutputs: true }),
        //     schema: tool.parameters,
        //     prompt: [
        //       `The model tried to call the tool "${toolCall.toolName}" with the following arguments:`,
        //       JSON.stringify(toolCall.args),
        //       `The tool accepts the following schema:`,
        //       JSON.stringify(parameterSchema(toolCall)),
        //       "Please fix the arguments.",
        //     ].join("\n"),
        //   });
        //   return { ...toolCall, args: JSON.stringify(repairedArgs) };
        // },
        ...isNonGeonProvider ? {} : {
            providerOptions: {
                geon: {
                    metadata: {
                        chat_template_kwargs: {
                            enable_thinking: false
                        }
                    }
                }
            }
        },
        onStepFinish: ({ toolCalls, text, toolResults })=>{
            // 평가를 위한 정보 수집
            if (toolCalls && toolCalls.length > 0) {
                allToolCalls.push(...toolCalls);
            }
            // 도구 결과도 수집 (다음 시도에서 참조할 수 있도록)
            if (toolResults && toolResults.length > 0) {
                // toolResults를 allToolCalls에 매핑하여 저장
                toolResults.forEach((result, index)=>{
                    const toolCallIndex = allToolCalls.length - toolResults.length + index;
                    if (allToolCalls[toolCallIndex]) {
                        const toolCall = allToolCalls[toolCallIndex];
                        const tool = agentConfig.tools[toolCall.toolName];
                        // toToolResultContent가 있는 도구는 프루닝된 결과를, 없는 도구는 전체 결과를 저장
                        if (tool && "experimental_toToolResultContent" in tool && tool.experimental_toToolResultContent) {
                            // 프루닝된 결과 저장 (평가자용)
                            toolCall.result = tool.experimental_toToolResultContent(result.result);
                        } else {
                            // 전체 결과 저장
                            toolCall.result = result.result;
                        }
                    }
                });
            }
            // 도구 호출 정보를 어노테이션으로 전송
            if (toolCalls && toolCalls.length > 0) {
                toolCalls.forEach((toolCall)=>{
                    dataStream.writeMessageAnnotation({
                        type: "tool_call",
                        toolName: toolCall.toolName,
                        args: toolCall.args,
                        enableSmartNavigation: enable_smart_navigation
                    });
                });
            }
        },
        onFinish: async ({ text, toolCalls, response })=>{
            // 최종 정보 수집
            if (text) {
                agentResponse += text;
            }
            if (toolCalls && toolCalls.length > 0) {
                allToolCalls.push(...toolCalls);
            }
            // 평가자 실행 시작 알림
            dataStream.writeMessageAnnotation({
                type: "evaluation_start",
                iteration: iteration + 1,
                maxIterations,
                message: "작업 결과를 평가하고 있습니다..."
            });
            // 평가자 실행
            try {
                evaluationResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$evaluate$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["evaluateAgentResult"])(intentMessage, agentResponse, allToolCalls);
                // 평가 완료 어노테이션
                const evaluationMessage = evaluationResult.isCompleted ? "작업이 성공적으로 완료되었습니다" : "작업이 미완료되어 계속 진행합니다";
                dataStream.writeMessageAnnotation({
                    type: "evaluation_completed",
                    iteration: iteration + 1,
                    maxIterations,
                    isCompleted: evaluationResult.isCompleted,
                    shouldContinue: !evaluationResult.isCompleted,
                    message: evaluationMessage,
                    reason: evaluationResult.reason,
                    improvementSuggestions: evaluationResult.improvementSuggestions
                });
                // 콜백으로 평가 결과와 도구 호출 정보 전달
                if (onEvaluationComplete) {
                    onEvaluationComplete({
                        ...evaluationResult,
                        toolCalls: allToolCalls,
                        agentResponse: agentResponse
                    });
                }
            } catch (error) {
                console.error("평가자 실행 실패:", error);
                evaluationResult = {
                    isCompleted: false,
                    reason: "평가자 실행 중 오류가 발생하여 미완료로 처리",
                    improvementSuggestions: [
                        "평가자 오류로 인한 재시도가 필요합니다"
                    ]
                };
            }
            // AI SDK 4 공식 패턴: response.messages를 사용하여 tool calls와 results 포함하여 저장
            if (session.user?.id && chatId && response.messages && response.messages.length > 0) {
                try {
                    // AI SDK의 appendResponseMessages를 사용하여 기존 메시지에 응답 메시지들을 추가
                    const updatedMessages = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["appendResponseMessages"])({
                        messages,
                        responseMessages: response.messages
                    });
                    // 새로 추가된 메시지들만 저장 (기존 메시지는 이미 저장됨)
                    const newMessages = updatedMessages.slice(messages.length);
                    const messagesToSave = newMessages.map((message)=>{
                        const messageId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateUUID"])();
                        if (message.role === "assistant") {
                            dataStream.writeMessageAnnotation({
                                messageIdFromServer: messageId
                            });
                        }
                        return {
                            id: messageId,
                            chatId,
                            role: message.role,
                            content: message.content,
                            parts: message.parts || [],
                            attachments: message.experimental_attachments || [],
                            createdAt: new Date()
                        };
                    });
                    if (messagesToSave.length > 0) {
                        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$queries$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["saveMessages"])({
                            messages: messagesToSave
                        });
                    }
                } catch (error) {
                    console.error("메시지 저장 실패:", error);
                }
            }
            // 작업 완료 메시지 전송
            if (session.user?.id) {
                try {
                    dataStream.writeMessageAnnotation({
                        type: "agent_completed",
                        agent: agentName,
                        message: evaluationResult?.isCompleted ? "작업이 완료되었습니다." : "작업이 진행 중입니다.",
                        finalEvaluation: evaluationResult,
                        iteration: iteration + 1,
                        maxIterations
                    });
                } catch (error) {
                    console.error("메시지 저장 실패:", error);
                }
            }
        }
    });
    return result;
}
async function executeAgentWithEvaluation({ model, agentName, messages, stateMessage, intentMessage, dataStream, session, enable_smart_navigation, isNonGeonProvider, iteration = 0, maxIterations = 3, chatId }) {
    let evaluationResult = null;
    // Agent 실행
    const result = await executeAgentWithRetry({
        model,
        agentName,
        messages,
        stateMessage,
        intentMessage,
        dataStream,
        session,
        enable_smart_navigation,
        isNonGeonProvider,
        iteration,
        maxIterations,
        chatId
    });
    // 스트림을 데이터스트림에 병합하고 완료 대기
    await new Promise((resolve)=>{
        result.mergeIntoDataStream(dataStream, {
            sendReasoning: true
        });
        // onFinish에서 평가 결과가 설정될 때까지 대기
        const checkEvaluation = ()=>{
            // 여기서 평가 결과를 가져오는 로직 필요
            // 현재는 간단히 완료로 처리
            setTimeout(()=>{
                resolve();
            }, 1000);
        };
        checkEvaluation();
    });
    return {
        streamResult: result,
        evaluationResult: evaluationResult
    };
}
}}),
"[project]/app/(map)/api/chat/retry-handler.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "handleAgentWithRetry": (()=>handleAgentWithRetry)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$execute$2d$with$2d$retry$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(map)/api/chat/execute-with-retry.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$evaluate$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(map)/api/chat/evaluate.ts [app-route] (ecmascript)");
;
;
async function handleAgentWithRetry({ model, agentName, messages, stateMessage, intentMessage, dataStream, session, enable_smart_navigation, isNonGeonProvider, maxIterations = 3, chatId }) {
    let currentMessages = [
        ...messages
    ];
    let iteration = 0; // 0부터 시작: 첫 시도는 0, 재시도는 1, 2, ...
    let previousToolCalls = []; // 이전 시도의 도구 호출 결과 저장
    while(iteration < maxIterations){
        console.log(`=== 재시도 핸들러: ${iteration + 1}/${maxIterations} ===`);
        let evaluationResult = null;
        let streamCompleted = false;
        // Agent 실행 (콜백으로 평가 결과 수집)
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$execute$2d$with$2d$retry$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeAgentWithRetry"])({
            model,
            agentName,
            messages: currentMessages,
            stateMessage,
            intentMessage,
            dataStream,
            session,
            enable_smart_navigation,
            isNonGeonProvider,
            iteration,
            maxIterations,
            chatId,
            onEvaluationComplete: (evaluation)=>{
                evaluationResult = evaluation;
                // 도구 호출 정보 저장 (다음 재시도에서 사용)
                if (evaluation.toolCalls) {
                    previousToolCalls = evaluation.toolCalls;
                }
                streamCompleted = true;
            }
        });
        // 스트림을 데이터스트림에 병합
        result.mergeIntoDataStream(dataStream, {
            sendReasoning: true
        });
        // 평가 완료까지 대기
        while(!streamCompleted){
            await new Promise((resolve)=>setTimeout(resolve, 100));
        }
        // 완료 여부 판단 (단순화된 기준)
        if (evaluationResult?.isCompleted) {
            console.log("=== 작업 완료 ===");
            // 최종 완료 어노테이션 (첫 번째 시도에서 성공한 경우는 제외)
            if (iteration > 0) {
                dataStream.writeMessageAnnotation({
                    type: "retry_completed",
                    totalIterations: iteration + 1,
                    maxIterations,
                    finalResult: "success",
                    message: "모든 작업이 성공적으로 완료되었습니다"
                });
            }
            break;
        }
        // 재시도 준비
        if (iteration < maxIterations - 1) {
            console.log("=== 재시도 준비 중 ===");
            // 개선 메시지를 시스템 메시지로 추가
            const improvementMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$evaluate$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createImprovementMessage"])(evaluationResult);
            currentMessages = [
                ...currentMessages,
                {
                    role: "system",
                    content: improvementMessage
                }
            ];
            // 이전 도구 호출 결과를 assistant 메시지로 추가 (컨텍스트 유지)
            if (previousToolCalls.length > 0) {
                const toolCallsContext = previousToolCalls.map((toolCall)=>{
                    const args = JSON.stringify(toolCall.args);
                    const result = toolCall.result ? JSON.stringify(toolCall.result).substring(0, 200) + '...' : '결과 없음';
                    return `${toolCall.toolName}(${args}) → ${result}`;
                }).join('\n');
                currentMessages = [
                    ...currentMessages,
                    {
                        role: "assistant",
                        content: `이전 시도에서 수행한 작업 결과:\n${toolCallsContext}\n\n위 결과를 참고하여 재시도하세요.`
                    }
                ];
            }
            // 재시도 시작 알림
            dataStream.writeMessageAnnotation({
                type: "retry_starting",
                iteration: iteration + 2,
                maxIterations,
                message: `${iteration + 2}번째 시도를 시작합니다`,
                reason: evaluationResult.reason,
                improvementSuggestions: evaluationResult.improvementSuggestions || []
            });
            // iteration 증가: 작업이 미완료이므로 다음 시도 진행
            iteration++;
        } else {
            console.log("=== 최대 재시도 횟수 도달 ===");
            // 최대 재시도 도달 어노테이션
            dataStream.writeMessageAnnotation({
                type: "retry_limit_reached",
                totalIterations: maxIterations,
                maxIterations,
                message: `⏰ 최대 재시도 횟수(${maxIterations}회)에 도달했습니다`,
                finalEvaluation: evaluationResult
            });
            break;
        }
    }
    return "완료";
}
}}),
"[project]/app/(map)/api/chat/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DELETE": (()=>DELETE),
    "POST": (()=>POST),
    "maxDuration": (()=>maxDuration)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/ai@4.3.19_react@19.0.0_zod@3.24.1/node_modules/ai/dist/index.mjs [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/ai/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$auth$292f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(auth)/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$queries$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/db/queries.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$models$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/ai/models.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$actions$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(map)/actions.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$agents$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(map)/api/chat/agents.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api-config.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$retry$2d$handler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(map)/api/chat/retry-handler.ts [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
// 지도 상태 정보 생성 유틸리티
const createMapStateInfo = (layers)=>{
    if (layers.length === 0) {
        return `지도 상태: 빈 지도 (레이어 없음)
- 모든 레이어 요청은 검색 → 추가 과정이 필요합니다.`;
    }
    const layerSummary = layers.map((layer)=>({
            name: layer.name || layer.lyrNm || '이름없음',
            id: layer.id,
            type: layer.geometryType || layer.type || '타입불명',
            visible: layer.visible
        }));
    const visibleLayers = layerSummary.filter((l)=>l.visible);
    const hiddenLayers = layerSummary.filter((l)=>!l.visible);
    return `지도 상태: ${layers.length}개 레이어 존재

**현재 표시중인 레이어 (${visibleLayers.length}개):**
${visibleLayers.map((l)=>`- ${l.name} (${l.type}, ID: ${l.id})`).join('\n')}

**숨겨진 레이어 (${hiddenLayers.length}개):**
${hiddenLayers.map((l)=>`- ${l.name} (${l.type}, ID: ${l.id})`).join('\n')}

**레이어 타입 분포:**
- 점(Point): ${layerSummary.filter((l)=>l.type === 'point').length}개
- 선(Line): ${layerSummary.filter((l)=>l.type === 'line').length}개
- 면(Polygon): ${layerSummary.filter((l)=>l.type === 'polygon').length}개`;
};
// 의도분석 메시지 생성 함수
const createIntentAnalyzerMessages = (userMessage, lastAssistantMsg, layers, intentAnalyzerAgentConfig)=>{
    const messages = [
        {
            role: "system",
            content: intentAnalyzerAgentConfig.system
        }
    ];
    // 지도 상태 정보 추가
    const mapStateInfo = createMapStateInfo(layers);
    messages.push({
        role: "system",
        content: `현재 지도 상태 정보:
${mapStateInfo}

이 정보를 바탕으로 사용자 요청을 분석하고 최적의 작업 계획을 수립하세요.`
    });
    // 이전 대화 맥락
    if (lastAssistantMsg) {
        const assistantContent = typeof lastAssistantMsg.content === "string" ? lastAssistantMsg.content : JSON.stringify(lastAssistantMsg.content);
        messages.push({
            role: "assistant",
            content: assistantContent
        });
    }
    // 사용자 메시지
    const userContentStr = typeof userMessage.content === "string" ? userMessage.content : JSON.stringify(userMessage.content);
    messages.push({
        role: "user",
        content: userContentStr
    });
    return messages;
};
// 실제 사용 가능한 레이어 목록을 조회하는 함수
async function getAvailableLayersContext() {
    try {
        const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiConfig"])();
        const params = new URLSearchParams({
            userId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiUserId"])(config),
            holdDataSeCode: "0",
            pageIndex: "1",
            pageSize: "10"
        });
        // 인증 정보 추가
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addAuthToParams"])(params, config);
        const response = await fetch(`${config.baseUrl}/smt/layer/info/list?${params.toString()}`, {
            method: "GET",
            headers: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiHeaders"])(config)
        });
        if (!response.ok) {
            console.warn("Failed to fetch available layers for context");
            return "사용 가능한 레이어 정보를 조회할 수 없습니다.";
        }
        const data = await response.json();
        if (!data || !data.result || !data.result.list) {
            return "사용 가능한 레이어 정보를 조회할 수 없습니다.";
        }
        const layers = data.result.list.slice(0, 5); // 상위 5개만 사용
        const layerInfo = layers.map((layer)=>{
            const typeMap = {
                "1": "점",
                "2": "선",
                "3": "면"
            };
            const typeName = typeMap[layer.lyrTySeCode] || "기타";
            return `${layer.lyrNm}(${typeName})`;
        }).join(", ");
        return `실제 사용 가능한 레이어 예시: ${layerInfo}`;
    } catch (error) {
        console.warn("Error fetching available layers for context:", error);
        return "사용 가능한 레이어 정보를 조회할 수 없습니다.";
    }
}
const maxDuration = 100;
async function POST(req) {
    const { id, messages, layers, modelId, enable_thinking, enable_smart_navigation } = await req.json();
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$auth$292f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["auth"])();
    if (!session || !session.user || !session.user.id) {
        return new Response("Unauthorized", {
            status: 401
        });
    }
    // 모델 선택 및 초기화
    let model = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customModel"])("Qwen/Qwen3-4B");
    let selectedModel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$models$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getModelById"])(modelId || "Qwen3-4B");
    let modelSupportsReasoning = true; // 기본값
    let isNonGeonProvider = false;
    if (selectedModel) {
        const provider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$models$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getModelProvider"])(selectedModel.id);
        modelSupportsReasoning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$models$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supportsReasoning"])(selectedModel.id);
        switch(provider){
            case "openai":
                model = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["openaiModel"])(selectedModel.apiIdentifier);
                isNonGeonProvider = true;
                break;
            case "geon":
                model = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customModel"])(selectedModel.apiIdentifier);
                break;
            case "dify":
                // difyModel 함수가 있다면 사용
                model = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customModel"])(selectedModel.apiIdentifier);
                break;
            default:
                model = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customModel"])(selectedModel.apiIdentifier);
        }
    }
    const coreMessages = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["convertToCoreMessages"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prunedMessages"])(messages));
    const userMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getMostRecentUserMessage"])(coreMessages);
    if (!userMessage) {
        return new Response("No user message found", {
            status: 400
        });
    }
    const chat = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$queries$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getChatById"])({
        id
    });
    if (!chat) {
        const title = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$actions$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateTitleFromUserMessage"])({
            message: userMessage
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$queries$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["saveChat"])({
            id,
            userId: session.user.id,
            title
        });
    }
    const userMessageId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateUUID"])();
    // 사용자 메시지를 AI SDK 패턴에 맞게 저장
    const userMessageToSave = {
        id: userMessageId,
        chatId: id,
        role: userMessage.role,
        content: "",
        parts: Array.isArray(userMessage.content) ? userMessage.content : [
            {
                type: "text",
                text: userMessage.content
            }
        ],
        attachments: [],
        createdAt: new Date()
    };
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$queries$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["saveMessages"])({
        messages: [
            userMessageToSave
        ]
    });
    // createDataStreamResponse를 사용한 Sequential Processing과 Routing
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createDataStreamResponse"])({
        execute: async (dataStream)=>{
            try {
                // Step 1: Intent Analysis (의도 분석) - 내부 프로세스는 숨김
                const lastAssistantMsg = [
                    ...coreMessages
                ].reverse().find((msg)=>msg.role === "assistant");
                // 개선된 의도분석 메시지 생성 (지도 상태 정보 포함)
                const intentAnalyzerMessages = createIntentAnalyzerMessages(userMessage, lastAssistantMsg, layers, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$agents$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["intentAnalyzerAgentConfig"]);
                const { experimental_partialOutputStream } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["streamText"])({
                    model,
                    temperature: 0,
                    messages: intentAnalyzerMessages,
                    experimental_output: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Output"].object({
                        schema: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$agents$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IntentResponseSchema"]
                    }),
                    onChunk ({ chunk }) {
                        if (chunk.type === "reasoning") {
                            dataStream.writeMessageAnnotation(chunk);
                        }
                    },
                    // experimental_transform: smoothStream({
                    //   delayInMs: 10
                    // }),
                    ...isNonGeonProvider ? {} : {
                        providerOptions: {
                            geon: {
                                metadata: {
                                    chat_template_kwargs: {
                                        enable_thinking: enable_thinking && modelSupportsReasoning
                                    }
                                }
                            }
                        }
                    }
                });
                // experimental_partialOutputStream에서 intent 추출
                let intent = {
                    intent: "GENERAL_CONVERSATION",
                    message: "사용자 요청을 처리하고 있습니다..."
                };
                // 사용자 메시지 처리 과정을 보여주면서 동기식으로 출력이 완성될 때까지 기다림
                for await (const partial of experimental_partialOutputStream){
                    if (partial.intent && partial.message) {
                        intent = {
                            intent: partial.intent,
                            message: partial.message,
                            userMessage: partial.userMessage,
                            requiredActions: partial.requiredActions,
                            targetLayer: partial.targetLayer,
                            layerExists: partial.layerExists,
                            styleRequirements: partial.styleRequirements
                        };
                    }
                }
                // 의도분석 결과와 작업 계획을 사용자에게 표시
                dataStream.writeMessageAnnotation({
                    type: "intent_analyzed",
                    intent: intent.intent,
                    message: intent.userMessage
                });
                // Step 2: Agent Routing (에이전트 라우팅)
                const agentName = mapIntentToAgent(intent.intent);
                if (!agentName) {
                    // General conversation이나 처리할 수 없는 의도인 경우
                    const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["streamText"])({
                        model,
                        messages: [
                            {
                                role: "system",
                                content: "당신은 친근하고 도움이 되는 지도 AI 어시스턴트입니다. 사용자와 자연스럽게 대화하세요."
                            },
                            ...coreMessages
                        ],
                        temperature: 0,
                        maxSteps: 5
                    });
                    result.mergeIntoDataStream(dataStream);
                    return;
                }
                // 에이전트 시작 알림 (사용자 친화적 메시지 사용)
                dataStream.writeMessageAnnotation({
                    type: "agent_start",
                    agent: agentName,
                    message: intent.userMessage || `${agentName} 에이전트가 작업을 시작합니다`
                });
                // 개선된 stateMessage 생성
                const createExecutionMessage = (intent, layers)=>{
                    let content = `Current map state: ${JSON.stringify(layers, null, 2)}\n\n의도분석 결과: ${intent.message}`;
                    // 조건부 추가 정보
                    if (intent.targetLayer) {
                        content += `\n\n대상 레이어: ${intent.targetLayer}`;
                        content += `\n레이어 존재 여부: ${intent.layerExists ? '존재함' : '존재하지 않음'}`;
                    }
                    if (intent.requiredActions && intent.requiredActions.length > 0) {
                        content += `\n\n필수 작업 단계:`;
                        intent.requiredActions.forEach((action, index)=>{
                            content += `\n${index + 1}. ${action}`;
                        });
                    }
                    if (intent.styleRequirements) {
                        content += `\n\n스타일 요구사항: ${JSON.stringify(intent.styleRequirements)}`;
                    }
                    content += `\n\n사용자 정보: {\n    userId = '${session.user?.id}'\n    insttCode = 'geonpaas'\n    userSeCode = '14'\n  }`;
                    content += `\n\n위 계획에 따라 단계별로 작업을 수행하세요.`;
                    return {
                        role: "system",
                        content
                    };
                };
                const stateMessage = createExecutionMessage(intent, layers);
                console.log(`[ROUTING] Intent: ${intent.intent} → Agent: ${agentName || "null"}`, `${JSON.stringify(stateMessage)}`);
                // 새로운 방식
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$map$292f$api$2f$chat$2f$retry$2d$handler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleAgentWithRetry"])({
                    model,
                    agentName,
                    messages: coreMessages,
                    stateMessage,
                    intentMessage: intent.message,
                    dataStream,
                    session,
                    enable_smart_navigation,
                    isNonGeonProvider,
                    chatId: id
                });
            // 작업 완료 시 메시지 저장
            } catch (error) {
                console.error("Agent processing error:", error);
            }
        },
        onError: (error)=>{
            console.error("Agent processing error:", error);
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["NoSuchToolError"].isInstance(error)) {
                return "The model tried to call a unknown tool.";
            } else if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["InvalidToolArgumentsError"].isInstance(error)) {
                return "The model called a tool with invalid arguments.";
            } else if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ai$40$4$2e$3$2e$19_react$40$19$2e$0$2e$0_zod$40$3$2e$24$2e$1$2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ToolExecutionError"].isInstance(error)) {
                return "An error occurred during tool execution.";
            } else {
                return "An unknown error occurred.";
            }
        }
    });
}
// 의도를 에이전트로 매핑하는 헬퍼 함수
function mapIntentToAgent(intent) {
    const intentToAgentMap = {
        LAYER_ADD: "layer_agent",
        LAYER_REMOVE: "layer_agent",
        LAYER_STYLE: "layer_agent",
        LAYER_LIST: "layer_agent",
        LAYER_COUNT: "layer_agent",
        LAYER_FILTER: "layer_agent",
        NAVIGATION: "navigation",
        MAP_CONTROL: "map_control",
        BASEMAP_CHANGE: "map_control",
        DENSITY_ANALYSIS: "density_analysis",
        GENERAL_CONVERSATION: "default_agent",
        UNSURE: "default_agent",
        UNSUPPORTED_FEATURE: "unsupported_feature"
    };
    return intentToAgentMap[intent];
}
async function DELETE(request) {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    if (!id) {
        return new Response("Not Found", {
            status: 404
        });
    }
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$auth$292f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["auth"])();
    if (!session || !session.user) {
        return new Response("Unauthorized", {
            status: 401
        });
    }
    try {
        const chat = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$queries$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getChatById"])({
            id
        });
        if (chat.userId !== session.user.id) {
            return new Response("Unauthorized", {
                status: 401
            });
        }
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2f$queries$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["deleteChatById"])({
            id
        });
        return new Response("Chat deleted", {
            status: 200
        });
    } catch (error) {
        return new Response("An error occurred while processing your request", {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e18b3c30._.js.map