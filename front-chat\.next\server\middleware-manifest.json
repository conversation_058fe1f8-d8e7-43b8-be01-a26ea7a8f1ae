{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_c50ec21a._.js", "server/edge/chunks/9c5b9_@auth_core_16e273d6._.js", "server/edge/chunks/96a70_jose_dist_webapi_68ebb33a._.js", "server/edge/chunks/node_modules__pnpm_60fd9d8f._.js", "server/edge/chunks/[root-of-the-server]__b9a0598e._.js", "server/edge/chunks/edge-wrapper_22e6e254.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/|_next\\/|_static\\/|_vercel|[\\w-]+\\.\\w+).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/|_next/|_static/|_vercel|[\\w-]+\\.\\w+).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RzLJEFYUW/CFqaruOjruBpAlZrrsX4lJc3SEBXoIqAE=", "__NEXT_PREVIEW_MODE_ID": "51de03911f609e095f272fb6ca1ee653", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "609648916bc9fabe8bec40322b0557d33cd45ec4e2916cc0d3dd10a8d47a741a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "de118224c344dcc65c46f0a406b7514c3b909625e56cd762dd7c48101c9a02aa"}}}, "sortedMiddleware": ["/"], "functions": {}}