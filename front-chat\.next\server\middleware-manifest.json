{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_c50ec21a._.js", "server/edge/chunks/9c5b9_@auth_core_16e273d6._.js", "server/edge/chunks/96a70_jose_dist_webapi_68ebb33a._.js", "server/edge/chunks/node_modules__pnpm_60fd9d8f._.js", "server/edge/chunks/[root-of-the-server]__b9a0598e._.js", "server/edge/chunks/edge-wrapper_22e6e254.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/|_next\\/|_static\\/|_vercel|[\\w-]+\\.\\w+).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/|_next/|_static/|_vercel|[\\w-]+\\.\\w+).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "cTUG/ZvvyahVZd/zBHHEOAaq3Xu/bKxqLZoLpyon+/Y=", "__NEXT_PREVIEW_MODE_ID": "edbb613f1c44d1b69bf24b0229f352d1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6faaeb0e96f75308e401640b3b4c88a202ea9b8d0e8db604f3cdfa7cf087ad2e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4b0ada1493cb6492901db4245834d7176d1c654ff4482201106711a175ef7112"}}}, "sortedMiddleware": ["/"], "functions": {}}