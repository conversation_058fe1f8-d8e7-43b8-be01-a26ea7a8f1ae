{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/errors/ai-sdk-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/errors/api-call-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/errors/empty-response-body-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/errors/get-error-message.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/errors/invalid-argument-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/errors/invalid-prompt-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/errors/invalid-response-data-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/errors/json-parse-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/errors/load-api-key-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/errors/load-setting-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/errors/no-content-generated-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/errors/no-such-model-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/errors/too-many-embedding-values-for-call-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/errors/type-validation-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/errors/unsupported-functionality-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider%401.1.3/node_modules/%40ai-sdk/provider/src/json-value/is-json.ts"], "sourcesContent": ["/**\n * Symbol used for identifying AI SDK Error instances.\n * Enables checking if an error is an instance of AISDKError across package versions.\n */\nconst marker = 'vercel.ai.error';\nconst symbol = Symbol.for(marker);\n\n/**\n * Custom error class for AI SDK related errors.\n * @extends Error\n */\nexport class AISDKError extends Error {\n  private readonly [symbol] = true; // used in isInstance\n\n  /**\n   * The underlying cause of the error, if any.\n   */\n  readonly cause?: unknown;\n\n  /**\n   * Creates an AI SDK Error.\n   *\n   * @param {Object} params - The parameters for creating the error.\n   * @param {string} params.name - The name of the error.\n   * @param {string} params.message - The error message.\n   * @param {unknown} [params.cause] - The underlying cause of the error.\n   */\n  constructor({\n    name,\n    message,\n    cause,\n  }: {\n    name: string;\n    message: string;\n    cause?: unknown;\n  }) {\n    super(message);\n\n    this.name = name;\n    this.cause = cause;\n  }\n\n  /**\n   * Checks if the given error is an AI SDK Error.\n   * @param {unknown} error - The error to check.\n   * @returns {boolean} True if the error is an AI SDK Error, false otherwise.\n   */\n  static isInstance(error: unknown): error is AISDKError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  protected static hasMarker(error: unknown, marker: string): boolean {\n    const markerSymbol = Symbol.for(marker);\n    return (\n      error != null &&\n      typeof error === 'object' &&\n      markerSymbol in error &&\n      typeof error[markerSymbol] === 'boolean' &&\n      error[markerSymbol] === true\n    );\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_APICallError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class APICallError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly url: string;\n  readonly requestBodyValues: unknown;\n  readonly statusCode?: number;\n\n  readonly responseHeaders?: Record<string, string>;\n  readonly responseBody?: string;\n\n  readonly isRetryable: boolean;\n  readonly data?: unknown;\n\n  constructor({\n    message,\n    url,\n    requestBodyValues,\n    statusCode,\n    responseHeaders,\n    responseBody,\n    cause,\n    isRetryable = statusCode != null &&\n      (statusCode === 408 || // request timeout\n        statusCode === 409 || // conflict\n        statusCode === 429 || // too many requests\n        statusCode >= 500), // server error\n    data,\n  }: {\n    message: string;\n    url: string;\n    requestBodyValues: unknown;\n    statusCode?: number;\n    responseHeaders?: Record<string, string>;\n    responseBody?: string;\n    cause?: unknown;\n    isRetryable?: boolean;\n    data?: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.url = url;\n    this.requestBodyValues = requestBodyValues;\n    this.statusCode = statusCode;\n    this.responseHeaders = responseHeaders;\n    this.responseBody = responseBody;\n    this.isRetryable = isRetryable;\n    this.data = data;\n  }\n\n  static isInstance(error: unknown): error is APICallError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_EmptyResponseBodyError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class EmptyResponseBodyError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message = 'Empty response body' }: { message?: string } = {}) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is EmptyResponseBodyError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidArgumentError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * A function argument is invalid.\n */\nexport class InvalidArgumentError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly argument: string;\n\n  constructor({\n    message,\n    cause,\n    argument,\n  }: {\n    argument: string;\n    message: string;\n    cause?: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.argument = argument;\n  }\n\n  static isInstance(error: unknown): error is InvalidArgumentError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidPromptError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * A prompt is invalid. This error should be thrown by providers when they cannot\n * process a prompt.\n */\nexport class InvalidPromptError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly prompt: unknown;\n\n  constructor({\n    prompt,\n    message,\n    cause,\n  }: {\n    prompt: unknown;\n    message: string;\n    cause?: unknown;\n  }) {\n    super({ name, message: `Invalid prompt: ${message}`, cause });\n\n    this.prompt = prompt;\n  }\n\n  static isInstance(error: unknown): error is InvalidPromptError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidResponseDataError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * Server returned a response with invalid data content.\n * This should be thrown by providers when they cannot parse the response from the API.\n */\nexport class InvalidResponseDataError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly data: unknown;\n\n  constructor({\n    data,\n    message = `Invalid response data: ${JSON.stringify(data)}.`,\n  }: {\n    data: unknown;\n    message?: string;\n  }) {\n    super({ name, message });\n\n    this.data = data;\n  }\n\n  static isInstance(error: unknown): error is InvalidResponseDataError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\nimport { getErrorMessage } from './get-error-message';\n\nconst name = 'AI_JSONParseError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n// TODO v5: rename to ParseError\nexport class JSONParseError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly text: string;\n\n  constructor({ text, cause }: { text: string; cause: unknown }) {\n    super({\n      name,\n      message:\n        `JSON parsing failed: ` +\n        `Text: ${text}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n      cause,\n    });\n\n    this.text = text;\n  }\n\n  static isInstance(error: unknown): error is JSONParseError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_LoadAPIKeyError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class LoadAPIKeyError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message }: { message: string }) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is LoadAPIKeyError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_LoadSettingError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class LoadSettingError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message }: { message: string }) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is LoadSettingError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_NoContentGeneratedError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\nThrown when the AI provider fails to generate any content.\n */\nexport class NoContentGeneratedError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({\n    message = 'No content generated.',\n  }: { message?: string } = {}) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is NoContentGeneratedError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_NoSuchModelError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class NoSuchModelError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly modelId: string;\n  readonly modelType: 'languageModel' | 'textEmbeddingModel' | 'imageModel';\n\n  constructor({\n    errorName = name,\n    modelId,\n    modelType,\n    message = `No such ${modelType}: ${modelId}`,\n  }: {\n    errorName?: string;\n    modelId: string;\n    modelType: 'languageModel' | 'textEmbeddingModel' | 'imageModel';\n    message?: string;\n  }) {\n    super({ name: errorName, message });\n\n    this.modelId = modelId;\n    this.modelType = modelType;\n  }\n\n  static isInstance(error: unknown): error is NoSuchModelError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_TooManyEmbeddingValuesForCallError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class TooManyEmbeddingValuesForCallError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly provider: string;\n  readonly modelId: string;\n  readonly maxEmbeddingsPerCall: number;\n  readonly values: Array<unknown>;\n\n  constructor(options: {\n    provider: string;\n    modelId: string;\n    maxEmbeddingsPerCall: number;\n    values: Array<unknown>;\n  }) {\n    super({\n      name,\n      message:\n        `Too many values for a single embedding call. ` +\n        `The ${options.provider} model \"${options.modelId}\" can only embed up to ` +\n        `${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`,\n    });\n\n    this.provider = options.provider;\n    this.modelId = options.modelId;\n    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;\n    this.values = options.values;\n  }\n\n  static isInstance(\n    error: unknown,\n  ): error is TooManyEmbeddingValuesForCallError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\nimport { getErrorMessage } from './get-error-message';\n\nconst name = 'AI_TypeValidationError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class TypeValidationError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly value: unknown;\n\n  constructor({ value, cause }: { value: unknown; cause: unknown }) {\n    super({\n      name,\n      message:\n        `Type validation failed: ` +\n        `Value: ${JSON.stringify(value)}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n      cause,\n    });\n\n    this.value = value;\n  }\n\n  static isInstance(error: unknown): error is TypeValidationError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * Wraps an error into a TypeValidationError.\n   * If the cause is already a TypeValidationError with the same value, it returns the cause.\n   * Otherwise, it creates a new TypeValidationError.\n   *\n   * @param {Object} params - The parameters for wrapping the error.\n   * @param {unknown} params.value - The value that failed validation.\n   * @param {unknown} params.cause - The original error or cause of the validation failure.\n   * @returns {TypeValidationError} A TypeValidationError instance.\n   */\n  static wrap({\n    value,\n    cause,\n  }: {\n    value: unknown;\n    cause: unknown;\n  }): TypeValidationError {\n    return TypeValidationError.isInstance(cause) && cause.value === value\n      ? cause\n      : new TypeValidationError({ value, cause });\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_UnsupportedFunctionalityError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class UnsupportedFunctionalityError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly functionality: string;\n\n  constructor({\n    functionality,\n    message = `'${functionality}' functionality not supported.`,\n  }: {\n    functionality: string;\n    message?: string;\n  }) {\n    super({ name, message });\n    this.functionality = functionality;\n  }\n\n  static isInstance(error: unknown): error is UnsupportedFunctionalityError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { JSONArray, JSONObject, JSONValue } from './json-value';\n\nexport function isJSONValue(value: unknown): value is JSONValue {\n  if (\n    value === null ||\n    typeof value === 'string' ||\n    typeof value === 'number' ||\n    typeof value === 'boolean'\n  ) {\n    return true;\n  }\n\n  if (Array.isArray(value)) {\n    return value.every(isJSONValue);\n  }\n\n  if (typeof value === 'object') {\n    return Object.entries(value).every(\n      ([key, val]) => typeof key === 'string' && isJSONValue(val),\n    );\n  }\n\n  return false;\n}\n\nexport function isJSONArray(value: unknown): value is JSONArray {\n  return Array.isArray(value) && value.every(isJSONValue);\n}\n\nexport function isJSONObject(value: unknown): value is JSONObject {\n  return (\n    value != null &&\n    typeof value === 'object' &&\n    Object.entries(value).every(\n      ([key, val]) => typeof key === 'string' && isJSONValue(val),\n    )\n  );\n}\n"], "names": ["name", "marker", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,SAAS;AACf,IAAM,SAAS,OAAO,GAAA,CAAI,MAAM;AALhC,IAAA;AAWO,IAAM,cAAN,MAAM,oBAAmB,MAAM;IAAA;;;;;;;GAAA,GAgBpC,YAAY,EACV,MAAAA,MAAAA,EACA,OAAA,EACA,KAAA,EACF,CAIG;QACD,KAAA,CAAM,OAAO;QAxBf,IAAA,CAAkB,GAAA,GAAU;QA0B1B,IAAA,CAAK,IAAA,GAAOA;QACZ,IAAA,CAAK,KAAA,GAAQ;IACf;IAAA;;;;GAAA,GAOA,OAAO,WAAW,KAAA,EAAqC;QACrD,OAAO,YAAW,SAAA,CAAU,OAAO,MAAM;IAC3C;IAEA,OAAiB,UAAU,KAAA,EAAgBC,QAAAA,EAAyB;QAClE,MAAM,eAAe,OAAO,GAAA,CAAIA,QAAM;QACtC,OACE,SAAS,QACT,OAAO,UAAU,YACjB,gBAAgB,SAChB,OAAO,KAAA,CAAM,YAAY,CAAA,KAAM,aAC/B,KAAA,CAAM,YAAY,CAAA,KAAM;IAE5B;AACF;AAjDoB,KAAA;AADb,IAAM,aAAN;;ACTP,IAAM,OAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmB,IAAI,EAAA;AACtC,IAAMC,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,eAAN,cAA2B,WAAW;IAa3C,YAAY,EACV,OAAA,EACA,GAAA,EACA,iBAAA,EACA,UAAA,EACA,eAAA,EACA,YAAA,EACA,KAAA,EACA,cAAc,cAAc,QAAA,CACzB,eAAe,OAAA,kBAAA;IACd,eAAe,OAAA,WAAA;IACf,eAAe,OAAA,oBAAA;IACf,cAAc,GAAA,CAAA,EAAA,eAAA;IAClB,IAAA,EACF,CAUG;QACD,KAAA,CAAM;YAAE;YAAM;YAAS;QAAM,CAAC;QArChC,IAAA,CAAkBA,IAAAA,GAAU;QAuC1B,IAAA,CAAK,GAAA,GAAM;QACX,IAAA,CAAK,iBAAA,GAAoB;QACzB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,eAAA,GAAkB;QACvB,IAAA,CAAK,YAAA,GAAe;QACpB,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,IAAA,GAAO;IACd;IAEA,OAAO,WAAW,KAAA,EAAuC;QACvD,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AAnDoBE,MAAAD;;ACLpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,yBAAN,cAAqC,WAAW;IAAA,qBAAA;IAGrD,YAAY,EAAE,UAAU,qBAAA,CAAsB,CAAA,GAA0B,CAAC,CAAA,CAAG;QAC1E,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QAHzB,IAAA,CAAkBG,IAAAA,GAAU;IAI5B;IAEA,OAAO,WAAW,KAAA,EAAiD;QACjE,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AAToBE,MAAAD;;ACPb,SAAS,gBAAgB,KAAA,EAA4B;IAC1D,IAAI,SAAS,MAAM;QACjB,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAA;IACf;IAEA,OAAO,KAAK,SAAA,CAAU,KAAK;AAC7B;;ACZA,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AASO,IAAM,uBAAN,cAAmC,WAAW;IAKnD,YAAY,EACV,OAAA,EACA,KAAA,EACA,QAAA,EACF,CAIG;QACD,KAAA,CAAM;YAAE,MAAAH;YAAM;YAAS;QAAM,CAAC;QAbhC,IAAA,CAAkBG,IAAAA,GAAU;QAe1B,IAAA,CAAK,QAAA,GAAW;IAClB;IAEA,OAAO,WAAW,KAAA,EAA+C;QAC/D,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AArBoBE,MAAAD;;ACRpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAUO,IAAM,qBAAN,cAAiC,WAAW;IAKjD,YAAY,EACV,MAAA,EACA,OAAA,EACA,KAAA,EACF,CAIG;QACD,KAAA,CAAM;YAAE,MAAAH;YAAM,SAAS,CAAA,gBAAA,EAAmB,OAAO,EAAA;YAAI;QAAM,CAAC;QAb9D,IAAA,CAAkBG,IAAAA,GAAU;QAe1B,IAAA,CAAK,MAAA,GAAS;IAChB;IAEA,OAAO,WAAW,KAAA,EAA6C;QAC7D,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AArBoBE,MAAAD;;ACTpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAUO,IAAM,2BAAN,cAAuC,WAAW;IAKvD,YAAY,EACV,IAAA,EACA,UAAU,CAAA,uBAAA,EAA0B,KAAK,SAAA,CAAU,IAAI,CAAC,CAAA,CAAA,CAAA,EAC1D,CAGG;QACD,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QAXzB,IAAA,CAAkBG,IAAAA,GAAU;QAa1B,IAAA,CAAK,IAAA,GAAO;IACd;IAEA,OAAO,WAAW,KAAA,EAAmD;QACnE,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AAnBoBE,MAAAD;;ACRpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AALhC,IAAAE;AAQO,IAAM,iBAAN,cAA6B,WAAW;IAK7C,YAAY,EAAE,IAAA,EAAM,KAAA,CAAM,CAAA,CAAqC;QAC7D,KAAA,CAAM;YACJ,MAAAH;YACA,SACE,CAAA,2BAAA,EACS,IAAI,CAAA;eAAA,EACK,gBAAgB,KAAK,CAAC,EAAA;YAC1C;QACF,CAAC;QAZH,IAAA,CAAkBG,IAAAA,GAAU;QAc1B,IAAA,CAAK,IAAA,GAAO;IACd;IAEA,OAAO,WAAW,KAAA,EAAyC;QACzD,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AApBoBE,MAAAD;;ACPpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,kBAAN,cAA8B,WAAW;IAAA,qBAAA;IAG9C,YAAY,EAAE,OAAA,CAAQ,CAAA,CAAwB;QAC5C,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QAHzB,IAAA,CAAkBG,IAAAA,GAAU;IAI5B;IAEA,OAAO,WAAW,KAAA,EAA0C;QAC1D,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AAToBE,MAAAD;;ACLpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,mBAAN,cAA+B,WAAW;IAAA,qBAAA;IAG/C,YAAY,EAAE,OAAA,CAAQ,CAAA,CAAwB;QAC5C,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QAHzB,IAAA,CAAkBG,IAAAA,GAAU;IAI5B;IAEA,OAAO,WAAW,KAAA,EAA2C;QAC3D,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AAToBE,MAAAD;;ACLpB,IAAME,QAAO;AACb,IAAMC,WAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,WAAS,OAAO,GAAA,CAAID,QAAM;AAJhC,IAAAE;AASO,IAAM,0BAAN,cAAsC,WAAW;IAAA,qBAAA;IAGtD,YAAY,EACV,UAAU,uBAAA,EACZ,GAA0B,CAAC,CAAA,CAAG;QAC5B,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QALzB,IAAA,CAAkBG,KAAAA,GAAU;IAM5B;IAEA,OAAO,WAAW,KAAA,EAAkD;QAClE,OAAO,WAAW,SAAA,CAAU,OAAOF,QAAM;IAC3C;AACF;AAXoBE,OAAAD;;ACRpB,IAAME,SAAO;AACb,IAAMC,WAAS,CAAA,gBAAA,EAAmBD,MAAI,EAAA;AACtC,IAAME,WAAS,OAAO,GAAA,CAAID,QAAM;AAJhC,IAAAE;AAMO,IAAM,mBAAN,cAA+B,WAAW;IAM/C,YAAY,EACV,YAAYH,MAAAA,EACZ,OAAA,EACA,SAAA,EACA,UAAU,CAAA,QAAA,EAAW,SAAS,CAAA,EAAA,EAAK,OAAO,EAAA,EAC5C,CAKG;QACD,KAAA,CAAM;YAAE,MAAM;YAAW;QAAQ,CAAC;QAhBpC,IAAA,CAAkBG,KAAAA,GAAU;QAkB1B,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,SAAA,GAAY;IACnB;IAEA,OAAO,WAAW,KAAA,EAA2C;QAC3D,OAAO,WAAW,SAAA,CAAU,OAAOF,QAAM;IAC3C;AACF;AAzBoBE,OAAAD;;ACLpB,IAAME,SAAO;AACb,IAAMC,WAAS,CAAA,gBAAA,EAAmBD,MAAI,EAAA;AACtC,IAAME,WAAS,OAAO,GAAA,CAAID,QAAM;AAJhC,IAAAE;AAMO,IAAM,qCAAN,cAAiD,WAAW;IAQjE,YAAY,OAAA,CAKT;QACD,KAAA,CAAM;YACJ,MAAAH;YACA,SACE,CAAA,iDAAA,EACO,QAAQ,QAAQ,CAAA,QAAA,EAAW,QAAQ,OAAO,CAAA,uBAAA,EAC9C,QAAQ,oBAAoB,CAAA,sBAAA,EAAyB,QAAQ,MAAA,CAAO,MAAM,CAAA,sBAAA,CAAA;QACjF,CAAC;QAnBH,IAAA,CAAkBG,KAAAA,GAAU;QAqB1B,IAAA,CAAK,QAAA,GAAW,QAAQ,QAAA;QACxB,IAAA,CAAK,OAAA,GAAU,QAAQ,OAAA;QACvB,IAAA,CAAK,oBAAA,GAAuB,QAAQ,oBAAA;QACpC,IAAA,CAAK,MAAA,GAAS,QAAQ,MAAA;IACxB;IAEA,OAAO,WACL,KAAA,EAC6C;QAC7C,OAAO,WAAW,SAAA,CAAU,OAAOF,QAAM;IAC3C;AACF;AAhCoBE,OAAAD;;ACJpB,IAAME,SAAO;AACb,IAAMC,WAAS,CAAA,gBAAA,EAAmBD,MAAI,EAAA;AACtC,IAAME,WAAS,OAAO,GAAA,CAAID,QAAM;AALhC,IAAAE;AAOO,IAAM,uBAAN,MAAM,6BAA4B,WAAW;IAKlD,YAAY,EAAE,KAAA,EAAO,KAAA,CAAM,CAAA,CAAuC;QAChE,KAAA,CAAM;YACJ,MAAAH;YACA,SACE,CAAA,+BAAA,EACU,KAAK,SAAA,CAAU,KAAK,CAAC,CAAA;eAAA,EACb,gBAAgB,KAAK,CAAC,EAAA;YAC1C;QACF,CAAC;QAZH,IAAA,CAAkBG,KAAAA,GAAU;QAc1B,IAAA,CAAK,KAAA,GAAQ;IACf;IAEA,OAAO,WAAW,KAAA,EAA8C;QAC9D,OAAO,WAAW,SAAA,CAAU,OAAOF,QAAM;IAC3C;IAAA;;;;;;;;;GAAA,GAYA,OAAO,KAAK,EACV,KAAA,EACA,KAAA,EACF,EAGwB;QACtB,OAAO,qBAAoB,UAAA,CAAW,KAAK,KAAK,MAAM,KAAA,KAAU,QAC5D,QACA,IAAI,qBAAoB;YAAE;YAAO;QAAM,CAAC;IAC9C;AACF;AA1CoBE,OAAAD;AADb,IAAM,sBAAN;;ACLP,IAAME,SAAO;AACb,IAAMC,WAAS,CAAA,gBAAA,EAAmBD,MAAI,EAAA;AACtC,IAAME,WAAS,OAAO,GAAA,CAAID,QAAM;AAJhC,IAAAE;AAMO,IAAM,gCAAN,cAA4C,WAAW;IAK5D,YAAY,EACV,aAAA,EACA,UAAU,CAAA,CAAA,EAAI,aAAa,CAAA,8BAAA,CAAA,EAC7B,CAGG;QACD,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QAXzB,IAAA,CAAkBG,KAAAA,GAAU;QAY1B,IAAA,CAAK,aAAA,GAAgB;IACvB;IAEA,OAAO,WAAW,KAAA,EAAwD;QACxE,OAAO,WAAW,SAAA,CAAU,OAAOF,QAAM;IAC3C;AACF;AAlBoBE,OAAAD;;ACLb,SAAS,YAAY,KAAA,EAAoC;IAC9D,IACE,UAAU,QACV,OAAO,UAAU,YACjB,OAAO,UAAU,YACjB,OAAO,UAAU,WACjB;QACA,OAAO;IACT;IAEA,IAAI,MAAM,OAAA,CAAQ,KAAK,GAAG;QACxB,OAAO,MAAM,KAAA,CAAM,WAAW;IAChC;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,OAAO,OAAA,CAAQ,KAAK,EAAE,KAAA,CAC3B,CAAC,CAAC,KAAK,GAAG,CAAA,GAAM,OAAO,QAAQ,YAAY,YAAY,GAAG;IAE9D;IAEA,OAAO;AACT;AAEO,SAAS,YAAY,KAAA,EAAoC;IAC9D,OAAO,MAAM,OAAA,CAAQ,KAAK,KAAK,MAAM,KAAA,CAAM,WAAW;AACxD;AAEO,SAAS,aAAa,KAAA,EAAqC;IAChE,OACE,SAAS,QACT,OAAO,UAAU,YACjB,OAAO,OAAA,CAAQ,KAAK,EAAE,KAAA,CACpB,CAAC,CAAC,KAAK,GAAG,CAAA,GAAM,OAAO,QAAQ,YAAY,YAAY,GAAG;AAGhE", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "debugId": null}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/nanoid%403.3.8/node_modules/nanoid/non-secure/index.js"], "sourcesContent": ["// This alphabet uses `A-Za-z0-9_-` symbols.\n// The order of characters is optimized for better gzip and brotli compression.\n// References to the same file (works both for gzip and brotli):\n// `'use`, `andom`, and `rict'`\n// References to the brotli default dictionary:\n// `-26T`, `1983`, `40px`, `75px`, `bush`, `jack`, `mind`, `very`, and `wolf`\nlet urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\n\nlet customAlphabet = (alphabet, defaultSize = 21) => {\n  return (size = defaultSize) => {\n    let id = ''\n    // A compact alternative for `for (var i = 0; i < step; i++)`.\n    let i = size | 0\n    while (i--) {\n      // `| 0` is more compact and faster than `Math.floor()`.\n      id += alphabet[(Math.random() * alphabet.length) | 0]\n    }\n    return id\n  }\n}\n\nlet nanoid = (size = 21) => {\n  let id = ''\n  // A compact alternative for `for (var i = 0; i < step; i++)`.\n  let i = size | 0\n  while (i--) {\n    // `| 0` is more compact and faster than `Math.floor()`.\n    id += urlAlphabet[(Math.random() * 64) | 0]\n  }\n  return id\n}\n\nexport { nanoid, customAlphabet }\n"], "names": [], "mappings": "AAAA,4CAA4C;AAC5C,+EAA+E;AAC/E,gEAAgE;AAChE,+BAA+B;AAC/B,+CAA+C;AAC/C,6EAA6E;;;;;AAC7E,IAAI,cACF;AAEF,IAAI,iBAAiB,CAAC,UAAU,cAAc,EAAE;IAC9C,OAAO,CAAC,OAAO,WAAW;QACxB,IAAI,KAAK;QACT,8DAA8D;QAC9D,IAAI,IAAI,OAAO;QACf,MAAO,IAAK;YACV,wDAAwD;YACxD,MAAM,QAAQ,CAAC,AAAC,KAAK,MAAM,KAAK,SAAS,MAAM,GAAI,EAAE;QACvD;QACA,OAAO;IACT;AACF;AAEA,IAAI,SAAS,CAAC,OAAO,EAAE;IACrB,IAAI,KAAK;IACT,8DAA8D;IAC9D,IAAI,IAAI,OAAO;IACf,MAAO,IAAK;QACV,wDAAwD;QACxD,MAAM,WAAW,CAAC,AAAC,KAAK,MAAM,KAAK,KAAM,EAAE;IAC7C;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/secure-json-parse%402.7.0/node_modules/secure-json-parse/index.js"], "sourcesContent": ["'use strict'\n\nconst hasBuffer = typeof Buffer !== 'undefined'\nconst suspectProtoRx = /\"(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])(?:p|\\\\u0070)(?:r|\\\\u0072)(?:o|\\\\u006[Ff])(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])\"\\s*:/\nconst suspectConstructorRx = /\"(?:c|\\\\u0063)(?:o|\\\\u006[Ff])(?:n|\\\\u006[Ee])(?:s|\\\\u0073)(?:t|\\\\u0074)(?:r|\\\\u0072)(?:u|\\\\u0075)(?:c|\\\\u0063)(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:r|\\\\u0072)\"\\s*:/\n\nfunction _parse (text, reviver, options) {\n  // Normalize arguments\n  if (options == null) {\n    if (reviver !== null && typeof reviver === 'object') {\n      options = reviver\n      reviver = undefined\n    }\n  }\n\n  if (hasBuffer && Buffer.isBuffer(text)) {\n    text = text.toString()\n  }\n\n  // BOM checker\n  if (text && text.charCodeAt(0) === 0xFEFF) {\n    text = text.slice(1)\n  }\n\n  // Parse normally, allowing exceptions\n  const obj = JSON.parse(text, reviver)\n\n  // Ignore null and non-objects\n  if (obj === null || typeof obj !== 'object') {\n    return obj\n  }\n\n  const protoAction = (options && options.protoAction) || 'error'\n  const constructorAction = (options && options.constructorAction) || 'error'\n\n  // options: 'error' (default) / 'remove' / 'ignore'\n  if (protoAction === 'ignore' && constructorAction === 'ignore') {\n    return obj\n  }\n\n  if (protoAction !== 'ignore' && constructorAction !== 'ignore') {\n    if (suspectProtoRx.test(text) === false && suspectConstructorRx.test(text) === false) {\n      return obj\n    }\n  } else if (protoAction !== 'ignore' && constructorAction === 'ignore') {\n    if (suspectProtoRx.test(text) === false) {\n      return obj\n    }\n  } else {\n    if (suspectConstructorRx.test(text) === false) {\n      return obj\n    }\n  }\n\n  // Scan result for proto keys\n  return filter(obj, { protoAction, constructorAction, safe: options && options.safe })\n}\n\nfunction filter (obj, { protoAction = 'error', constructorAction = 'error', safe } = {}) {\n  let next = [obj]\n\n  while (next.length) {\n    const nodes = next\n    next = []\n\n    for (const node of nodes) {\n      if (protoAction !== 'ignore' && Object.prototype.hasOwnProperty.call(node, '__proto__')) { // Avoid calling node.hasOwnProperty directly\n        if (safe === true) {\n          return null\n        } else if (protoAction === 'error') {\n          throw new SyntaxError('Object contains forbidden prototype property')\n        }\n\n        delete node.__proto__ // eslint-disable-line no-proto\n      }\n\n      if (constructorAction !== 'ignore' &&\n          Object.prototype.hasOwnProperty.call(node, 'constructor') &&\n          Object.prototype.hasOwnProperty.call(node.constructor, 'prototype')) { // Avoid calling node.hasOwnProperty directly\n        if (safe === true) {\n          return null\n        } else if (constructorAction === 'error') {\n          throw new SyntaxError('Object contains forbidden prototype property')\n        }\n\n        delete node.constructor\n      }\n\n      for (const key in node) {\n        const value = node[key]\n        if (value && typeof value === 'object') {\n          next.push(value)\n        }\n      }\n    }\n  }\n  return obj\n}\n\nfunction parse (text, reviver, options) {\n  const stackTraceLimit = Error.stackTraceLimit\n  Error.stackTraceLimit = 0\n  try {\n    return _parse(text, reviver, options)\n  } finally {\n    Error.stackTraceLimit = stackTraceLimit\n  }\n}\n\nfunction safeParse (text, reviver) {\n  const stackTraceLimit = Error.stackTraceLimit\n  Error.stackTraceLimit = 0\n  try {\n    return _parse(text, reviver, { safe: true })\n  } catch (_e) {\n    return null\n  } finally {\n    Error.stackTraceLimit = stackTraceLimit\n  }\n}\n\nmodule.exports = parse\nmodule.exports.default = parse\nmodule.exports.parse = parse\nmodule.exports.safeParse = safeParse\nmodule.exports.scan = filter\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,YAAY,OAAO,WAAW;AACpC,MAAM,iBAAiB;AACvB,MAAM,uBAAuB;AAE7B,SAAS,OAAQ,IAAI,EAAE,OAAO,EAAE,OAAO;IACrC,sBAAsB;IACtB,IAAI,WAAW,MAAM;QACnB,IAAI,YAAY,QAAQ,OAAO,YAAY,UAAU;YACnD,UAAU;YACV,UAAU;QACZ;IACF;IAEA,IAAI,aAAa,OAAO,QAAQ,CAAC,OAAO;QACtC,OAAO,KAAK,QAAQ;IACtB;IAEA,cAAc;IACd,IAAI,QAAQ,KAAK,UAAU,CAAC,OAAO,QAAQ;QACzC,OAAO,KAAK,KAAK,CAAC;IACpB;IAEA,sCAAsC;IACtC,MAAM,MAAM,KAAK,KAAK,CAAC,MAAM;IAE7B,8BAA8B;IAC9B,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;QAC3C,OAAO;IACT;IAEA,MAAM,cAAc,AAAC,WAAW,QAAQ,WAAW,IAAK;IACxD,MAAM,oBAAoB,AAAC,WAAW,QAAQ,iBAAiB,IAAK;IAEpE,mDAAmD;IACnD,IAAI,gBAAgB,YAAY,sBAAsB,UAAU;QAC9D,OAAO;IACT;IAEA,IAAI,gBAAgB,YAAY,sBAAsB,UAAU;QAC9D,IAAI,eAAe,IAAI,CAAC,UAAU,SAAS,qBAAqB,IAAI,CAAC,UAAU,OAAO;YACpF,OAAO;QACT;IACF,OAAO,IAAI,gBAAgB,YAAY,sBAAsB,UAAU;QACrE,IAAI,eAAe,IAAI,CAAC,UAAU,OAAO;YACvC,OAAO;QACT;IACF,OAAO;QACL,IAAI,qBAAqB,IAAI,CAAC,UAAU,OAAO;YAC7C,OAAO;QACT;IACF;IAEA,6BAA6B;IAC7B,OAAO,OAAO,KAAK;QAAE;QAAa;QAAmB,MAAM,WAAW,QAAQ,IAAI;IAAC;AACrF;AAEA,SAAS,OAAQ,GAAG,EAAE,EAAE,cAAc,OAAO,EAAE,oBAAoB,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACrF,IAAI,OAAO;QAAC;KAAI;IAEhB,MAAO,KAAK,MAAM,CAAE;QAClB,MAAM,QAAQ;QACd,OAAO,EAAE;QAET,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,gBAAgB,YAAY,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,cAAc;gBACvF,IAAI,SAAS,MAAM;oBACjB,OAAO;gBACT,OAAO,IAAI,gBAAgB,SAAS;oBAClC,MAAM,IAAI,YAAY;gBACxB;gBAEA,OAAO,KAAK,SAAS,CAAC,+BAA+B;;YACvD;YAEA,IAAI,sBAAsB,YACtB,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,kBAC3C,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE,cAAc;gBACvE,IAAI,SAAS,MAAM;oBACjB,OAAO;gBACT,OAAO,IAAI,sBAAsB,SAAS;oBACxC,MAAM,IAAI,YAAY;gBACxB;gBAEA,OAAO,KAAK,WAAW;YACzB;YAEA,IAAK,MAAM,OAAO,KAAM;gBACtB,MAAM,QAAQ,IAAI,CAAC,IAAI;gBACvB,IAAI,SAAS,OAAO,UAAU,UAAU;oBACtC,KAAK,IAAI,CAAC;gBACZ;YACF;QACF;IACF;IACA,OAAO;AACT;AAEA,SAAS,MAAO,IAAI,EAAE,OAAO,EAAE,OAAO;IACpC,MAAM,kBAAkB,MAAM,eAAe;IAC7C,MAAM,eAAe,GAAG;IACxB,IAAI;QACF,OAAO,OAAO,MAAM,SAAS;IAC/B,SAAU;QACR,MAAM,eAAe,GAAG;IAC1B;AACF;AAEA,SAAS,UAAW,IAAI,EAAE,OAAO;IAC/B,MAAM,kBAAkB,MAAM,eAAe;IAC7C,MAAM,eAAe,GAAG;IACxB,IAAI;QACF,OAAO,OAAO,MAAM,SAAS;YAAE,MAAM;QAAK;IAC5C,EAAE,OAAO,IAAI;QACX,OAAO;IACT,SAAU;QACR,MAAM,eAAe,GAAG;IAC1B;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,CAAC,OAAO,GAAG;AACzB,OAAO,OAAO,CAAC,KAAK,GAAG;AACvB,OAAO,OAAO,CAAC,SAAS,GAAG;AAC3B,OAAO,OAAO,CAAC,IAAI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/combine-headers.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/convert-async-iterator-to-readable-stream.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/delay.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/event-source-parser-stream.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/extract-response-headers.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/generate-id.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/get-error-message.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/get-from-api.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/remove-undefined-entries.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/is-abort-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/load-api-key.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/load-optional-setting.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/load-setting.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/parse-json.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/validate-types.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/validator.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/parse-provider-options.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/post-to-api.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/resolve.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/response-handler.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/uint8-utils.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bprovider-utils%402.2.8_zod%403.24.1/node_modules/%40ai-sdk/provider-utils/src/without-trailing-slash.ts"], "sourcesContent": ["export function combineHeaders(\n  ...headers: Array<Record<string, string | undefined> | undefined>\n): Record<string, string | undefined> {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...(currentHeaders ?? {}),\n    }),\n    {},\n  ) as Record<string, string | undefined>;\n}\n", "/**\n * Converts an AsyncIterator to a ReadableStream.\n *\n * @template T - The type of elements produced by the AsyncIterator.\n * @param { <T>} iterator - The AsyncIterator to convert.\n * @returns {ReadableStream<T>} - A ReadableStream that provides the same data as the AsyncIterator.\n */\nexport function convertAsyncIteratorToReadableStream<T>(\n  iterator: AsyncIterator<T>,\n): ReadableStream<T> {\n  return new ReadableStream<T>({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await iterator.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {},\n  });\n}\n", "/**\n * Creates a Promise that resolves after a specified delay\n * @param delayInMs - The delay duration in milliseconds. If null or undefined, resolves immediately.\n * @returns A Promise that resolves after the specified delay\n */\nexport async function delay(delayInMs?: number | null): Promise<void> {\n  return delayInMs == null\n    ? Promise.resolve()\n    : new Promise(resolve => setTimeout(resolve, delayInMs));\n}\n", "export type EventSourceChunk = {\n  event: string | undefined;\n  data: string;\n  id?: string;\n  retry?: number;\n};\n\nexport function createEventSourceParserStream() {\n  let buffer = '';\n  let event: string | undefined = undefined;\n  let data: string[] = [];\n  let lastEventId: string | undefined = undefined;\n  let retry: number | undefined = undefined;\n\n  function parseLine(\n    line: string,\n    controller: TransformStreamDefaultController<EventSourceChunk>,\n  ) {\n    // Empty line means dispatch the event\n    if (line === '') {\n      dispatchEvent(controller);\n      return;\n    }\n\n    // Comments start with colon\n    if (line.startsWith(':')) {\n      return;\n    }\n\n    // Field parsing\n    const colonIndex = line.indexOf(':');\n    if (colonIndex === -1) {\n      // field with no value\n      handleField(line, '');\n      return;\n    }\n\n    const field = line.slice(0, colonIndex);\n    // If there's a space after the colon, it should be ignored\n    const valueStart = colonIndex + 1;\n    const value =\n      valueStart < line.length && line[valueStart] === ' '\n        ? line.slice(valueStart + 1)\n        : line.slice(valueStart);\n\n    handleField(field, value);\n  }\n\n  function dispatchEvent(\n    controller: TransformStreamDefaultController<EventSourceChunk>,\n  ) {\n    if (data.length > 0) {\n      controller.enqueue({\n        event,\n        data: data.join('\\n'),\n        id: lastEventId,\n        retry,\n      });\n\n      // Reset data but keep lastEventId as per spec\n      data = [];\n      event = undefined;\n      retry = undefined;\n    }\n  }\n\n  function handleField(field: string, value: string) {\n    switch (field) {\n      case 'event':\n        event = value;\n        break;\n      case 'data':\n        data.push(value);\n        break;\n      case 'id':\n        lastEventId = value;\n        break;\n      case 'retry':\n        const parsedRetry = parseInt(value, 10);\n        if (!isNaN(parsedRetry)) {\n          retry = parsedRetry;\n        }\n        break;\n    }\n  }\n\n  return new TransformStream<string, EventSourceChunk>({\n    transform(chunk, controller) {\n      const { lines, incompleteLine } = splitLines(buffer, chunk);\n\n      buffer = incompleteLine;\n\n      // using for loop for performance\n      for (let i = 0; i < lines.length; i++) {\n        parseLine(lines[i], controller);\n      }\n    },\n\n    flush(controller) {\n      parseLine(buffer, controller);\n      dispatchEvent(controller);\n    },\n  });\n}\n\n// performance: send in already scanned buffer separately, do not scan again\nfunction splitLines(buffer: string, chunk: string) {\n  const lines: Array<string> = [];\n  let currentLine = buffer;\n\n  // using for loop for performance\n  for (let i = 0; i < chunk.length; ) {\n    const char = chunk[i++];\n\n    // order is performance-optimized\n    if (char === '\\n') {\n      // Standalone LF\n      lines.push(currentLine);\n      currentLine = '';\n    } else if (char === '\\r') {\n      lines.push(currentLine);\n      currentLine = '';\n      if (chunk[i] === '\\n') {\n        i++; // CRLF case: Skip the LF character\n      }\n    } else {\n      currentLine += char;\n    }\n  }\n\n  return { lines, incompleteLine: currentLine };\n}\n", "/**\nExtracts the headers from a response object and returns them as a key-value object.\n\n@param response - The response object to extract headers from.\n@returns The headers as a key-value object.\n*/\nexport function extractResponseHeaders(\n  response: Response,\n): Record<string, string> {\n  const headers: Record<string, string> = {};\n  response.headers.forEach((value, key) => {\n    headers[key] = value;\n  });\n  return headers;\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\nimport { customAlphabet } from 'nanoid/non-secure';\n\n/**\nCreates an ID generator.\nThe total length of the ID is the sum of the prefix, separator, and random part length.\nNon-secure.\n\n@param alphabet - The alphabet to use for the ID. Default: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.\n@param prefix - The prefix of the ID to generate. Default: ''.\n@param separator - The separator between the prefix and the random part of the ID. Default: '-'.\n@param size - The size of the random part of the ID to generate. Default: 16.\n */\n// TODO 5.0 breaking change: change the return type to IDGenerator\nexport const createIdGenerator = ({\n  prefix,\n  size: defaultSize = 16,\n  alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',\n  separator = '-',\n}: {\n  prefix?: string;\n  separator?: string;\n  size?: number;\n  alphabet?: string;\n} = {}): ((size?: number) => string) => {\n  const generator = customAlphabet(alphabet, defaultSize);\n\n  if (prefix == null) {\n    return generator;\n  }\n\n  // check that the prefix is not part of the alphabet (otherwise prefix checking can fail randomly)\n  if (alphabet.includes(separator)) {\n    throw new InvalidArgumentError({\n      argument: 'separator',\n      message: `The separator \"${separator}\" must not be part of the alphabet \"${alphabet}\".`,\n    });\n  }\n\n  return size => `${prefix}${separator}${generator(size)}`;\n};\n\n/**\nA function that generates an ID.\n */\nexport type IDGenerator = () => string;\n\n/**\nGenerates a 16-character random string to use for IDs. Not secure.\n\n@param size - The size of the ID to generate. Default: 16.\n */\nexport const generateId = createIdGenerator();\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { FetchFunction } from './fetch-function';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\nimport { ResponseHandler } from './response-handler';\nimport { isAbortError } from './is-abort-error';\nimport { extractResponseHeaders } from './extract-response-headers';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => globalThis.fetch;\n\nexport const getFromApi = async <T>({\n  url,\n  headers = {},\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'GET',\n      headers: removeUndefinedEntries(headers),\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: {},\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: {},\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: {},\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: {},\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n\n    if (error instanceof TypeError && error.message === 'fetch failed') {\n      const cause = (error as any).cause;\n      if (cause != null) {\n        throw new APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          isRetryable: true,\n          requestBodyValues: {},\n        });\n      }\n    }\n\n    throw error;\n  }\n};\n", "/**\n * Removes entries from a record where the value is null or undefined.\n * @param record - The input object whose entries may be null or undefined.\n * @returns A new object containing only entries with non-null and non-undefined values.\n */\nexport function removeUndefinedEntries<T>(\n  record: Record<string, T | undefined>,\n): Record<string, T> {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null),\n  ) as Record<string, T>;\n}\n", "export function isAbortError(error: unknown): error is Error {\n  return (\n    error instanceof Error &&\n    (error.name === 'AbortError' || error.name === 'TimeoutError')\n  );\n}\n", "import { LoadAPIKeyError } from '@ai-sdk/provider';\n\nexport function loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = 'apiKey',\n  description,\n}: {\n  apiKey: string | undefined;\n  environmentVariableName: string;\n  apiKeyParameterName?: string;\n  description: string;\n}): string {\n  if (typeof apiKey === 'string') {\n    return apiKey;\n  }\n\n  if (apiKey != null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`,\n    });\n  }\n\n  apiKey = process.env[environmentVariableName];\n\n  if (apiKey == null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof apiKey !== 'string') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return apiKey;\n}\n", "/**\n * Loads an optional `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @returns The setting value.\n */\nexport function loadOptionalSetting({\n  settingValue,\n  environmentVariableName,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n}): string | undefined {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null || typeof process === 'undefined') {\n    return undefined;\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null || typeof settingValue !== 'string') {\n    return undefined;\n  }\n\n  return settingValue;\n}\n", "import { LoadSettingError } from '@ai-sdk/provider';\n\n/**\n * Loads a `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @param settingName - The setting name.\n * @param description - The description of the setting.\n * @returns The setting value.\n */\nexport function loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n  settingName: string;\n  description: string;\n}): string {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null) {\n    throw new LoadSettingError({\n      message: `${description} setting must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter. ` +\n        `Environment variables is not supported in this environment.`,\n    });\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null) {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter ` +\n        `or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof settingValue !== 'string') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting must be a string. ` +\n        `The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return settingValue;\n}\n", "import {\n  JSONParseError,\n  <PERSON>SO<PERSON>V<PERSON><PERSON>,\n  TypeValidationError,\n} from '@ai-sdk/provider';\nimport SecureJSON from 'secure-json-parse';\nimport { ZodSchema } from 'zod';\nimport { safeValidateTypes, validateTypes } from './validate-types';\nimport { Validator } from './validator';\n\n/**\n * Parses a JSON string into an unknown object.\n *\n * @param text - The JSON string to parse.\n * @returns {JSONValue} - The parsed JSON object.\n */\nexport function parseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): JSONValue;\n/**\n * Parses a JSON string into a strongly-typed object using the provided schema.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns {T} - The parsed object.\n */\nexport function parseJSON<T>(options: {\n  text: string;\n  schema: ZodSchema<T> | Validator<T>;\n}): T;\nexport function parseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: ZodSchema<T> | Validator<T>;\n}): T {\n  try {\n    const value = SecureJSON.parse(text);\n\n    if (schema == null) {\n      return value;\n    }\n\n    return validateTypes({ value, schema });\n  } catch (error) {\n    if (\n      JSONParseError.isInstance(error) ||\n      TypeValidationError.isInstance(error)\n    ) {\n      throw error;\n    }\n\n    throw new JSONParseError({ text, cause: error });\n  }\n}\n\nexport type ParseResult<T> =\n  | { success: true; value: T; rawValue: unknown }\n  | { success: false; error: JSONParseError | TypeValidationError };\n\n/**\n * Safely parses a JSON string and returns the result as an object of type `unknown`.\n *\n * @param text - The JSON string to parse.\n * @returns {object} Either an object with `success: true` and the parsed data, or an object with `success: false` and the error that occurred.\n */\nexport function safeParseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): ParseResult<JSONValue>;\n/**\n * Safely parses a JSON string into a strongly-typed object, using a provided schema to validate the object.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport function safeParseJSON<T>(options: {\n  text: string;\n  schema: ZodSchema<T> | Validator<T>;\n}): ParseResult<T>;\nexport function safeParseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: ZodSchema<T> | Validator<T>;\n}): ParseResult<T> {\n  try {\n    const value = SecureJSON.parse(text);\n\n    if (schema == null) {\n      return { success: true, value: value as T, rawValue: value };\n    }\n\n    const validationResult = safeValidateTypes({ value, schema });\n\n    return validationResult.success\n      ? { ...validationResult, rawValue: value }\n      : validationResult;\n  } catch (error) {\n    return {\n      success: false,\n      error: JSONParseError.isInstance(error)\n        ? error\n        : new JSONParseError({ text, cause: error }),\n    };\n  }\n}\n\nexport function isParsableJson(input: string): boolean {\n  try {\n    SecureJSON.parse(input);\n    return true;\n  } catch {\n    return false;\n  }\n}\n", "import { TypeValidationError } from '@ai-sdk/provider';\nimport { z } from 'zod';\nimport { Validator, asValidator } from './validator';\n\n/**\n * Validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns {T} - The typed object.\n */\nexport function validateTypes<T>({\n  value,\n  schema: inputSchema,\n}: {\n  value: unknown;\n  schema: z.Schema<T, z.ZodTypeDef, any> | Validator<T>;\n}): T {\n  const result = safeValidateTypes({ value, schema: inputSchema });\n\n  if (!result.success) {\n    throw TypeValidationError.wrap({ value, cause: result.error });\n  }\n\n  return result.value;\n}\n\n/**\n * Safely validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The JSON object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport function safeValidateTypes<T>({\n  value,\n  schema,\n}: {\n  value: unknown;\n  schema: z.Schema<T, z.ZodTypeDef, any> | Validator<T>;\n}):\n  | { success: true; value: T }\n  | { success: false; error: TypeValidationError } {\n  const validator = asValidator(schema);\n\n  try {\n    if (validator.validate == null) {\n      return { success: true, value: value as T };\n    }\n\n    const result = validator.validate(value);\n\n    if (result.success) {\n      return result;\n    }\n\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: result.error }),\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: error }),\n    };\n  }\n}\n", "import { z } from 'zod';\n\n/**\n * Used to mark validator functions so we can support both Zod and custom schemas.\n */\nexport const validatorSymbol = Symbol.for('vercel.ai.validator');\n\nexport type ValidationResult<OBJECT> =\n  | { success: true; value: OBJECT }\n  | { success: false; error: Error };\n\nexport type Validator<OBJECT = unknown> = {\n  /**\n   * Used to mark validator functions so we can support both Zod and custom schemas.\n   */\n  [validatorSymbol]: true;\n\n  /**\n   * Optional. Validates that the structure of a value matches this schema,\n   * and returns a typed version of the value if it does.\n   */\n  readonly validate?: (value: unknown) => ValidationResult<OBJECT>;\n};\n\n/**\n * Create a validator.\n *\n * @param validate A validation function for the schema.\n */\nexport function validator<OBJECT>(\n  validate?: undefined | ((value: unknown) => ValidationResult<OBJECT>),\n): Validator<OBJECT> {\n  return { [validatorSymbol]: true, validate };\n}\n\nexport function isValidator(value: unknown): value is Validator {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    validatorSymbol in value &&\n    value[validatorSymbol] === true &&\n    'validate' in value\n  );\n}\n\nexport function asValidator<OBJECT>(\n  value: Validator<OBJECT> | z.Schema<OBJECT, z.ZodTypeDef, any>,\n): Validator<OBJECT> {\n  return isValidator(value) ? value : zodValidator(value);\n}\n\nexport function zodValidator<OBJECT>(\n  zodSchema: z.Schema<OBJECT, z.ZodTypeDef, any>,\n): Validator<OBJECT> {\n  return validator(value => {\n    const result = zodSchema.safeParse(value);\n    return result.success\n      ? { success: true, value: result.data }\n      : { success: false, error: result.error };\n  });\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\nimport { safeValidateTypes } from './validate-types';\nimport { z } from 'zod';\n\nexport function parseProviderOptions<T>({\n  provider,\n  providerOptions,\n  schema,\n}: {\n  provider: string;\n  providerOptions: Record<string, unknown> | undefined;\n  schema: z.ZodSchema<T>;\n}): T | undefined {\n  if (providerOptions?.[provider] == null) {\n    return undefined;\n  }\n\n  const parsedProviderOptions = safeValidateTypes({\n    value: providerOptions[provider],\n    schema,\n  });\n\n  if (!parsedProviderOptions.success) {\n    throw new InvalidArgumentError({\n      argument: 'providerOptions',\n      message: `invalid ${provider} provider options`,\n      cause: parsedProviderOptions.error,\n    });\n  }\n\n  return parsedProviderOptions.value;\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { FetchFunction } from './fetch-function';\nimport { isAbortError } from './is-abort-error';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\nimport { ResponseHandler } from './response-handler';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => globalThis.fetch;\n\nexport const postJsonToApi = async <T>({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: unknown;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) =>\n  postToApi({\n    url,\n    headers: {\n      'Content-Type': 'application/json',\n      ...headers,\n    },\n    body: {\n      content: JSON.stringify(body),\n      values: body,\n    },\n    failedResponse<PERSON>and<PERSON>,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postFormDataToApi = async <T>({\n  url,\n  headers,\n  formData,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  formData: FormData;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) =>\n  postToApi({\n    url,\n    headers,\n    body: {\n      content: formData,\n      values: Object.fromEntries((formData as any).entries()),\n    },\n    failedResponseHandler,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postToApi = async <T>({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: {\n    content: string | FormData | Uint8Array;\n    values: unknown;\n  };\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'POST',\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values,\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values,\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values,\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values,\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n\n    // unwrap original error when fetch failed (for easier debugging):\n    if (error instanceof TypeError && error.message === 'fetch failed') {\n      const cause = (error as any).cause;\n\n      if (cause != null) {\n        // Failed to connect to server:\n        throw new APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          requestBodyValues: body.values,\n          isRetryable: true, // retry when network error\n        });\n      }\n    }\n\n    throw error;\n  }\n};\n", "export type Resolvable<T> =\n  | T // Raw value\n  | Promise<T> // Promise of value\n  | (() => T) // Function returning value\n  | (() => Promise<T>); // Function returning promise of value\n\n/**\n * Resolves a value that could be a raw value, a Promise, a function returning a value,\n * or a function returning a Promise.\n */\nexport async function resolve<T>(value: Resolvable<T>): Promise<T> {\n  // If it's a function, call it to get the value/promise\n  if (typeof value === 'function') {\n    value = (value as Function)();\n  }\n\n  // Otherwise just resolve whatever we got (value or promise)\n  return Promise.resolve(value as T);\n}\n", "import { APICallError, EmptyResponseBodyError } from '@ai-sdk/provider';\nimport { ZodSchema } from 'zod';\nimport {\n  createEventSourceParserStream,\n  EventSourceChunk,\n} from './event-source-parser-stream';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { parseJSON, ParseResult, safeParseJSON } from './parse-json';\n\nexport type ResponseHandler<RETURN_TYPE> = (options: {\n  url: string;\n  requestBodyValues: unknown;\n  response: Response;\n}) => PromiseLike<{\n  value: RETURN_TYPE;\n  rawValue?: unknown;\n  responseHeaders?: Record<string, string>;\n}>;\n\nexport const createJsonErrorResponseHandler =\n  <T>({\n    errorSchema,\n    errorToMessage,\n    isRetryable,\n  }: {\n    errorSchema: ZodSchema<T>;\n    errorToMessage: (error: T) => string;\n    isRetryable?: (response: Response, error?: T) => boolean;\n  }): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n    const responseHeaders = extractResponseHeaders(response);\n\n    // Some providers return an empty response body for some errors:\n    if (responseBody.trim() === '') {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n\n    // resilient parsing in case the response is not JSON or does not match the schema:\n    try {\n      const parsedError = parseJSON({\n        text: responseBody,\n        schema: errorSchema,\n      });\n\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: errorToMessage(parsedError),\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          data: parsedError,\n          isRetryable: isRetryable?.(response, parsedError),\n        }),\n      };\n    } catch (parseError) {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n  };\n\nexport const createEventSourceResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    return {\n      responseHeaders,\n      value: response.body\n        .pipeThrough(new TextDecoderStream())\n        .pipeThrough(createEventSourceParserStream())\n        .pipeThrough(\n          new TransformStream<EventSourceChunk, ParseResult<T>>({\n            transform({ data }, controller) {\n              // ignore the 'DONE' event that e.g. OpenAI sends:\n              if (data === '[DONE]') {\n                return;\n              }\n\n              controller.enqueue(\n                safeParseJSON({\n                  text: data,\n                  schema: chunkSchema,\n                }),\n              );\n            },\n          }),\n        ),\n    };\n  };\n\nexport const createJsonStreamResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    let buffer = '';\n\n    return {\n      responseHeaders,\n      value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n        new TransformStream<string, ParseResult<T>>({\n          transform(chunkText, controller) {\n            if (chunkText.endsWith('\\n')) {\n              controller.enqueue(\n                safeParseJSON({\n                  text: buffer + chunkText,\n                  schema: chunkSchema,\n                }),\n              );\n              buffer = '';\n            } else {\n              buffer += chunkText;\n            }\n          },\n        }),\n      ),\n    };\n  };\n\nexport const createJsonResponseHandler =\n  <T>(responseSchema: ZodSchema<T>): ResponseHandler<T> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n\n    const parsedResult = safeParseJSON({\n      text: responseBody,\n      schema: responseSchema,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!parsedResult.success) {\n      throw new APICallError({\n        message: 'Invalid JSON response',\n        cause: parsedResult.error,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        url,\n        requestBodyValues,\n      });\n    }\n\n    return {\n      responseHeaders,\n      value: parsedResult.value,\n      rawValue: parsedResult.rawValue,\n    };\n  };\n\nexport const createBinaryResponseHandler =\n  (): ResponseHandler<Uint8Array> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.body) {\n      throw new APICallError({\n        message: 'Response body is empty',\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody: undefined,\n      });\n    }\n\n    try {\n      const buffer = await response.arrayBuffer();\n      return {\n        responseHeaders,\n        value: new Uint8Array(buffer),\n      };\n    } catch (error) {\n      throw new APICallError({\n        message: 'Failed to read response as array buffer',\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody: undefined,\n        cause: error,\n      });\n    }\n  };\n\nexport const createStatusCodeErrorResponseHandler =\n  (): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseHeaders = extractResponseHeaders(response);\n    const responseBody = await response.text();\n\n    return {\n      responseHeaders,\n      value: new APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues: requestBodyValues as Record<string, unknown>,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n      }),\n    };\n  };\n", "// btoa and atob need to be invoked as a function call, not as a method call.\n// Otherwise CloudFlare will throw a\n// \"TypeError: Illegal invocation: function called with incorrect this reference\"\nconst { btoa, atob } = globalThis;\n\nexport function convertBase64ToUint8Array(base64String: string) {\n  const base64Url = base64String.replace(/-/g, '+').replace(/_/g, '/');\n  const latin1string = atob(base64Url);\n  return Uint8Array.from(latin1string, byte => byte.codePointAt(0)!);\n}\n\nexport function convertUint8ArrayToBase64(array: Uint8Array): string {\n  let latin1string = '';\n\n  // Note: regular for loop to support older JavaScript versions that\n  // do not support for..of on Uint8Array\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n\n  return btoa(latin1string);\n}\n", "export function withoutTrailingSlash(url: string | undefined) {\n  return url?.replace(/\\/$/, '');\n}\n"], "names": ["resolve", "TypeValidationError", "validator", "TypeValidationError", "InvalidArgumentError", "InvalidArgumentError", "APICallError", "getOriginalFetch", "APICallError", "APICallError", "APICallError"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AKAA,SAAS,4BAA4B;AACrC,SAAS,sBAAsB;AQI/B,OAAO,gBAAgB;AbLhB,SAAS,eAAA,GACX,OAAA,EACiC;IACpC,OAAO,QAAQ,MAAA,CACb,CAAC,iBAAiB,iBAAA,CAAoB;YACpC,GAAG,eAAA;YACH,GAAI,kBAAA,OAAA,iBAAkB,CAAC,CAAA;QACzB,CAAA,GACA,CAAC;AAEL;;ACHO,SAAS,qCACd,QAAA,EACmB;IACnB,OAAO,IAAI,eAAkB;QAAA;;;;;KAAA,GAO3B,MAAM,MAAK,UAAA,EAAY;YACrB,IAAI;gBACF,MAAM,EAAE,KAAA,EAAO,IAAA,CAAK,CAAA,GAAI,MAAM,SAAS,IAAA,CAAK;gBAC5C,IAAI,MAAM;oBACR,WAAW,KAAA,CAAM;gBACnB,OAAO;oBACL,WAAW,OAAA,CAAQ,KAAK;gBAC1B;YACF,EAAA,OAAS,OAAO;gBACd,WAAW,KAAA,CAAM,KAAK;YACxB;QACF;QAAA;;KAAA,GAIA,SAAS,EAAC;IACZ,CAAC;AACH;;AC7BA,eAAsB,MAAM,SAAA,EAA0C;IACpE,OAAO,aAAa,OAChB,QAAQ,OAAA,CAAQ,IAChB,IAAI,QAAQ,CAAAA,WAAW,WAAWA,UAAS,SAAS,CAAC;AAC3D;;ACFO,SAAS,gCAAgC;IAC9C,IAAI,SAAS;IACb,IAAI,QAA4B,KAAA;IAChC,IAAI,OAAiB,CAAC,CAAA;IACtB,IAAI,cAAkC,KAAA;IACtC,IAAI,QAA4B,KAAA;IAEhC,SAAS,UACP,IAAA,EACA,UAAA,EACA;QAEA,IAAI,SAAS,IAAI;YACf,cAAc,UAAU;YACxB;QACF;QAGA,IAAI,KAAK,UAAA,CAAW,GAAG,GAAG;YACxB;QACF;QAGA,MAAM,aAAa,KAAK,OAAA,CAAQ,GAAG;QACnC,IAAI,eAAe,CAAA,GAAI;YAErB,YAAY,MAAM,EAAE;YACpB;QACF;QAEA,MAAM,QAAQ,KAAK,KAAA,CAAM,GAAG,UAAU;QAEtC,MAAM,aAAa,aAAa;QAChC,MAAM,QACJ,aAAa,KAAK,MAAA,IAAU,IAAA,CAAK,UAAU,CAAA,KAAM,MAC7C,KAAK,KAAA,CAAM,aAAa,CAAC,IACzB,KAAK,KAAA,CAAM,UAAU;QAE3B,YAAY,OAAO,KAAK;IAC1B;IAEA,SAAS,cACP,UAAA,EACA;QACA,IAAI,KAAK,MAAA,GAAS,GAAG;YACnB,WAAW,OAAA,CAAQ;gBACjB;gBACA,MAAM,KAAK,IAAA,CAAK,IAAI;gBACpB,IAAI;gBACJ;YACF,CAAC;YAGD,OAAO,CAAC,CAAA;YACR,QAAQ,KAAA;YACR,QAAQ,KAAA;QACV;IACF;IAEA,SAAS,YAAY,KAAA,EAAe,KAAA,EAAe;QACjD,OAAQ,OAAO;YACb,KAAK;gBACH,QAAQ;gBACR;YACF,KAAK;gBACH,KAAK,IAAA,CAAK,KAAK;gBACf;YACF,KAAK;gBACH,cAAc;gBACd;YACF,KAAK;gBACH,MAAM,cAAc,SAAS,OAAO,EAAE;gBACtC,IAAI,CAAC,MAAM,WAAW,GAAG;oBACvB,QAAQ;gBACV;gBACA;QACJ;IACF;IAEA,OAAO,IAAI,gBAA0C;QACnD,WAAU,KAAA,EAAO,UAAA,EAAY;YAC3B,MAAM,EAAE,KAAA,EAAO,cAAA,CAAe,CAAA,GAAI,WAAW,QAAQ,KAAK;YAE1D,SAAS;YAGT,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAK;gBACrC,UAAU,KAAA,CAAM,CAAC,CAAA,EAAG,UAAU;YAChC;QACF;QAEA,OAAM,UAAA,EAAY;YAChB,UAAU,QAAQ,UAAU;YAC5B,cAAc,UAAU;QAC1B;IACF,CAAC;AACH;AAGA,SAAS,WAAW,MAAA,EAAgB,KAAA,EAAe;IACjD,MAAM,QAAuB,CAAC,CAAA;IAC9B,IAAI,cAAc;IAGlB,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAU;QAClC,MAAM,OAAO,KAAA,CAAM,GAAG,CAAA;QAGtB,IAAI,SAAS,MAAM;YAEjB,MAAM,IAAA,CAAK,WAAW;YACtB,cAAc;QAChB,OAAA,IAAW,SAAS,MAAM;YACxB,MAAM,IAAA,CAAK,WAAW;YACtB,cAAc;YACd,IAAI,KAAA,CAAM,CAAC,CAAA,KAAM,MAAM;gBACrB;YACF;QACF,OAAO;YACL,eAAe;QACjB;IACF;IAEA,OAAO;QAAE;QAAO,gBAAgB;IAAY;AAC9C;;AC7HO,SAAS,uBACd,QAAA,EACwB;IACxB,MAAM,UAAkC,CAAC;IACzC,SAAS,OAAA,CAAQ,OAAA,CAAQ,CAAC,OAAO,QAAQ;QACvC,OAAA,CAAQ,GAAG,CAAA,GAAI;IACjB,CAAC;IACD,OAAO;AACT;;;ACAO,IAAM,oBAAoB,CAAC,EAChC,MAAA,EACA,MAAM,cAAc,EAAA,EACpB,WAAW,gEAAA,EACX,YAAY,GAAA,EACd,GAKI,CAAC,CAAA,KAAmC;IACtC,MAAM,aAAY,0NAAA,EAAe,UAAU,WAAW;IAEtD,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IAGA,IAAI,SAAS,QAAA,CAAS,SAAS,GAAG;QAChC,MAAM,oOAAI,uBAAA,CAAqB;YAC7B,UAAU;YACV,SAAS,CAAA,eAAA,EAAkB,SAAS,CAAA,oCAAA,EAAuC,QAAQ,CAAA,EAAA,CAAA;QACrF,CAAC;IACH;IAEA,OAAO,CAAA,OAAQ,GAAG,MAAM,GAAG,SAAS,GAAG,UAAU,IAAI,CAAC,EAAA;AACxD;AAYO,IAAM,aAAa,kBAAkB;;ACpDrC,SAAS,gBAAgB,KAAA,EAA4B;IAC1D,IAAI,SAAS,MAAM;QACjB,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAA;IACf;IAEA,OAAO,KAAK,SAAA,CAAU,KAAK;AAC7B;;;AETO,SAAS,uBACd,MAAA,EACmB;IACnB,OAAO,OAAO,WAAA,CACZ,OAAO,OAAA,CAAQ,MAAM,EAAE,MAAA,CAAO,CAAC,CAAC,MAAM,KAAK,CAAA,GAAM,SAAS,IAAI;AAElE;;ACXO,SAAS,aAAa,KAAA,EAAgC;IAC3D,OACE,iBAAiB,SAAA,CAChB,MAAM,IAAA,KAAS,gBAAgB,MAAM,IAAA,KAAS,cAAA;AAEnD;;AFGA,IAAM,mBAAmB,IAAM,WAAW,KAAA;AAEnC,IAAM,aAAa,OAAU,EAClC,GAAA,EACA,UAAU,CAAC,CAAA,EACX,yBAAA,EACA,qBAAA,EACA,WAAA,EACA,QAAQ,iBAAiB,CAAA,EAC3B,KAOM;IACJ,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS,uBAAuB,OAAO;YACvC,QAAQ;QACV,CAAC;QAED,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,CAAC,SAAS,EAAA,EAAI;YAChB,IAAI;YAKJ,IAAI;gBACF,mBAAmB,MAAM,sBAAsB;oBAC7C;oBACA;oBACA,mBAAmB,CAAC;gBACtB,CAAC;YACH,EAAA,OAAS,OAAO;gBACd,IAAI,aAAa,KAAK,qOAAK,eAAA,CAAa,UAAA,CAAW,KAAK,GAAG;oBACzD,MAAM;gBACR;gBAEA,MAAM,oOAAI,eAAA,CAAa;oBACrB,SAAS;oBACT,OAAO;oBACP,YAAY,SAAS,MAAA;oBACrB;oBACA;oBACA,mBAAmB,CAAC;gBACtB,CAAC;YACH;YAEA,MAAM,iBAAiB,KAAA;QACzB;QAEA,IAAI;YACF,OAAO,MAAM,0BAA0B;gBACrC;gBACA;gBACA,mBAAmB,CAAC;YACtB,CAAC;QACH,EAAA,OAAS,OAAO;YACd,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,aAAa,KAAK,qOAAK,eAAA,CAAa,UAAA,CAAW,KAAK,GAAG;oBACzD,MAAM;gBACR;YACF;YAEA,MAAM,oOAAI,eAAA,CAAa;gBACrB,SAAS;gBACT,OAAO;gBACP,YAAY,SAAS,MAAA;gBACrB;gBACA;gBACA,mBAAmB,CAAC;YACtB,CAAC;QACH;IACF,EAAA,OAAS,OAAO;QACd,IAAI,aAAa,KAAK,GAAG;YACvB,MAAM;QACR;QAEA,IAAI,iBAAiB,aAAa,MAAM,OAAA,KAAY,gBAAgB;YAClE,MAAM,QAAS,MAAc,KAAA;YAC7B,IAAI,SAAS,MAAM;gBACjB,MAAM,oOAAI,eAAA,CAAa;oBACrB,SAAS,CAAA,uBAAA,EAA0B,MAAM,OAAO,EAAA;oBAChD;oBACA;oBACA,aAAa;oBACb,mBAAmB,CAAC;gBACtB,CAAC;YACH;QACF;QAEA,MAAM;IACR;AACF;;AGxGO,SAAS,WAAW,EACzB,MAAA,EACA,uBAAA,EACA,sBAAsB,QAAA,EACtB,WAAA,EACF,EAKW;IACT,IAAI,OAAO,WAAW,UAAU;QAC9B,OAAO;IACT;IAEA,IAAI,UAAU,MAAM;QAClB,MAAM,oOAAI,kBAAA,CAAgB;YACxB,SAAS,GAAG,WAAW,CAAA,0BAAA,CAAA;QACzB,CAAC;IACH;IAEA,IAAI,OAAO,YAAY,aAAa;QAClC,MAAM,oOAAI,kBAAA,CAAgB;YACxB,SAAS,GAAG,WAAW,CAAA,wCAAA,EAA2C,mBAAmB,CAAA,wEAAA,CAAA;QACvF,CAAC;IACH;IAEA,SAAS,QAAQ,GAAA,CAAI,uBAAuB,CAAA;IAE5C,IAAI,UAAU,MAAM;QAClB,MAAM,oOAAI,kBAAA,CAAgB;YACxB,SAAS,GAAG,WAAW,CAAA,wCAAA,EAA2C,mBAAmB,CAAA,mBAAA,EAAsB,uBAAuB,CAAA,sBAAA,CAAA;QACpI,CAAC;IACH;IAEA,IAAI,OAAO,WAAW,UAAU;QAC9B,MAAM,IAAI,kPAAA,CAAgB;YACxB,SAAS,GAAG,WAAW,CAAA,4CAAA,EAA+C,uBAAuB,CAAA,sCAAA,CAAA;QAC/F,CAAC;IACH;IAEA,OAAO;AACT;;ACrCO,SAAS,oBAAoB,EAClC,YAAA,EACA,uBAAA,EACF,EAGuB;IACrB,IAAI,OAAO,iBAAiB,UAAU;QACpC,OAAO;IACT;IAEA,IAAI,gBAAgB,QAAQ,OAAO,YAAY,aAAa;QAC1D,OAAO,KAAA;IACT;IAEA,eAAe,QAAQ,GAAA,CAAI,uBAAuB,CAAA;IAElD,IAAI,gBAAgB,QAAQ,OAAO,iBAAiB,UAAU;QAC5D,OAAO,KAAA;IACT;IAEA,OAAO;AACT;;AClBO,SAAS,YAAY,EAC1B,YAAA,EACA,uBAAA,EACA,WAAA,EACA,WAAA,EACF,EAKW;IACT,IAAI,OAAO,iBAAiB,UAAU;QACpC,OAAO;IACT;IAEA,IAAI,gBAAgB,MAAM;QACxB,MAAM,oOAAI,mBAAA,CAAiB;YACzB,SAAS,GAAG,WAAW,CAAA,0BAAA,CAAA;QACzB,CAAC;IACH;IAEA,IAAI,OAAO,YAAY,aAAa;QAClC,MAAM,oOAAI,mBAAA,CAAiB;YACzB,SACE,GAAG,WAAW,CAAA,wCAAA,EACQ,WAAW,CAAA,wEAAA,CAAA;QAErC,CAAC;IACH;IAEA,eAAe,QAAQ,GAAA,CAAI,uBAAuB,CAAA;IAElD,IAAI,gBAAgB,MAAM;QACxB,MAAM,oOAAI,mBAAA,CAAiB;YACzB,SACE,GAAG,WAAW,CAAA,wCAAA,EACQ,WAAW,CAAA,mBAAA,EACvB,uBAAuB,CAAA,sBAAA,CAAA;QACrC,CAAC;IACH;IAEA,IAAI,OAAO,iBAAiB,UAAU;QACpC,MAAM,IAAI,mPAAA,CAAiB;YACzB,SACE,GAAG,WAAW,CAAA,4CAAA,EACM,uBAAuB,CAAA,sCAAA,CAAA;QAC/C,CAAC;IACH;IAEA,OAAO;AACT;;;;;AGxDO,IAAM,kBAAkB,OAAO,GAAA,CAAI,qBAAqB;AAwBxD,SAAS,UACd,QAAA,EACmB;IACnB,OAAO;QAAE,CAAC,eAAe,CAAA,EAAG;QAAM;IAAS;AAC7C;AAEO,SAAS,YAAY,KAAA,EAAoC;IAC9D,OACE,OAAO,UAAU,YACjB,UAAU,QACV,mBAAmB,SACnB,KAAA,CAAM,eAAe,CAAA,KAAM,QAC3B,cAAc;AAElB;AAEO,SAAS,YACd,KAAA,EACmB;IACnB,OAAO,YAAY,KAAK,IAAI,QAAQ,aAAa,KAAK;AACxD;AAEO,SAAS,aACd,SAAA,EACmB;IACnB,OAAO,UAAU,CAAA,UAAS;QACxB,MAAM,SAAS,UAAU,SAAA,CAAU,KAAK;QACxC,OAAO,OAAO,OAAA,GACV;YAAE,SAAS;YAAM,OAAO,OAAO,IAAA;QAAK,IACpC;YAAE,SAAS;YAAO,OAAO,OAAO,KAAA;QAAM;IAC5C,CAAC;AACH;;AD/CO,SAAS,cAAiB,EAC/B,KAAA,EACA,QAAQ,WAAA,EACV,EAGM;IACJ,MAAM,SAAS,kBAAkB;QAAE;QAAO,QAAQ;IAAY,CAAC;IAE/D,IAAI,CAAC,OAAO,OAAA,EAAS;QACnB,MAAM,sPAAA,CAAoB,IAAA,CAAK;YAAE;YAAO,OAAO,OAAO,KAAA;QAAM,CAAC;IAC/D;IAEA,OAAO,OAAO,KAAA;AAChB;AAWO,SAAS,kBAAqB,EACnC,KAAA,EACA,MAAA,EACF,EAKmD;IACjD,MAAME,aAAY,YAAY,MAAM;IAEpC,IAAI;QACF,IAAIA,WAAU,QAAA,IAAY,MAAM;YAC9B,OAAO;gBAAE,SAAS;gBAAM;YAAkB;QAC5C;QAEA,MAAM,SAASA,WAAU,QAAA,CAAS,KAAK;QAEvC,IAAI,OAAO,OAAA,EAAS;YAClB,OAAO;QACT;QAEA,OAAO;YACL,SAAS;YACT,uOAAO,sBAAA,CAAoB,IAAA,CAAK;gBAAE;gBAAO,OAAO,OAAO,KAAA;YAAM,CAAC;QAChE;IACF,EAAA,OAAS,OAAO;QACd,OAAO;YACL,SAAS;YACT,uOAAO,sBAAA,CAAoB,IAAA,CAAK;gBAAE;gBAAO,OAAO;YAAM,CAAC;QACzD;IACF;AACF;;ADtCO,SAAS,UAAa,EAC3B,IAAA,EACA,MAAA,EACF,EAGM;IACJ,IAAI;QACF,MAAM,8NAAQ,WAAA,CAAW,KAAA,CAAM,IAAI;QAEnC,IAAI,UAAU,MAAM;YAClB,OAAO;QACT;QAEA,OAAO,cAAc;YAAE;YAAO;QAAO,CAAC;IACxC,EAAA,OAAS,OAAO;QACd,oOACE,iBAAA,CAAe,UAAA,CAAW,KAAK,KAC/BC,sPAAAA,CAAoB,UAAA,CAAW,KAAK,GACpC;YACA,MAAM;QACR;QAEA,MAAM,oOAAI,iBAAA,CAAe;YAAE;YAAM,OAAO;QAAM,CAAC;IACjD;AACF;AA4BO,SAAS,cAAiB,EAC/B,IAAA,EACA,MAAA,EACF,EAGmB;IACjB,IAAI;QACF,MAAM,+NAAQ,UAAA,CAAW,KAAA,CAAM,IAAI;QAEnC,IAAI,UAAU,MAAM;YAClB,OAAO;gBAAE,SAAS;gBAAM;gBAAmB,UAAU;YAAM;QAC7D;QAEA,MAAM,mBAAmB,kBAAkB;YAAE;YAAO;QAAO,CAAC;QAE5D,OAAO,iBAAiB,OAAA,GACpB;YAAE,GAAG,gBAAA;YAAkB,UAAU;QAAM,IACvC;IACN,EAAA,OAAS,OAAO;QACd,OAAO;YACL,SAAS;YACT,uOAAO,iBAAA,CAAe,UAAA,CAAW,KAAK,IAClC,QACA,oOAAI,iBAAA,CAAe;gBAAE;gBAAM,OAAO;YAAM,CAAC;QAC/C;IACF;AACF;AAEO,SAAS,eAAe,KAAA,EAAwB;IACrD,IAAI;QACF,sNAAA,CAAA,UAAA,CAAW,KAAA,CAAM,KAAK;QACtB,OAAO;IACT,EAAA,OAAQ,GAAA;QACN,OAAO;IACT;AACF;;AGrHO,SAAS,qBAAwB,EACtC,QAAA,EACA,eAAA,EACA,MAAA,EACF,EAIkB;IAChB,IAAA,CAAI,mBAAA,OAAA,KAAA,IAAA,eAAA,CAAkB,SAAA,KAAa,MAAM;QACvC,OAAO,KAAA;IACT;IAEA,MAAM,wBAAwB,kBAAkB;QAC9C,OAAO,eAAA,CAAgB,QAAQ,CAAA;QAC/B;IACF,CAAC;IAED,IAAI,CAAC,sBAAsB,OAAA,EAAS;QAClC,MAAM,IAAIE,uPAAAA,CAAqB;YAC7B,UAAU;YACV,SAAS,CAAA,QAAA,EAAW,QAAQ,CAAA,iBAAA,CAAA;YAC5B,OAAO,sBAAsB,KAAA;QAC/B,CAAC;IACH;IAEA,OAAO,sBAAsB,KAAA;AAC/B;;ACvBA,IAAME,oBAAmB,IAAM,WAAW,KAAA;AAEnC,IAAM,gBAAgB,OAAU,EACrC,GAAA,EACA,OAAA,EACA,IAAA,EACA,qBAAA,EACA,yBAAA,EACA,WAAA,EACA,KAAA,EACF,GASE,UAAU;QACR;QACA,SAAS;YACP,gBAAgB;YAChB,GAAG,OAAA;QACL;QACA,MAAM;YACJ,SAAS,KAAK,SAAA,CAAU,IAAI;YAC5B,QAAQ;QACV;QACA;QACA;QACA;QACA;IACF,CAAC;AAEI,IAAM,oBAAoB,OAAU,EACzC,GAAA,EACA,OAAA,EACA,QAAA,EACA,qBAAA,EACA,yBAAA,EACA,WAAA,EACA,KAAA,EACF,GASE,UAAU;QACR;QACA;QACA,MAAM;YACJ,SAAS;YACT,QAAQ,OAAO,WAAA,CAAa,SAAiB,OAAA,CAAQ,CAAC;QACxD;QACA;QACA;QACA;QACA;IACF,CAAC;AAEI,IAAM,YAAY,OAAU,EACjC,GAAA,EACA,UAAU,CAAC,CAAA,EACX,IAAA,EACA,yBAAA,EACA,qBAAA,EACA,WAAA,EACA,QAAQA,kBAAiB,CAAA,EAC3B,KAWM;IACJ,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS,uBAAuB,OAAO;YACvC,MAAM,KAAK,OAAA;YACX,QAAQ;QACV,CAAC;QAED,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,CAAC,SAAS,EAAA,EAAI;YAChB,IAAI;YAKJ,IAAI;gBACF,mBAAmB,MAAM,sBAAsB;oBAC7C;oBACA;oBACA,mBAAmB,KAAK,MAAA;gBAC1B,CAAC;YACH,EAAA,OAAS,OAAO;gBACd,IAAI,aAAa,KAAK,qOAAKC,eAAAA,CAAa,UAAA,CAAW,KAAK,GAAG;oBACzD,MAAM;gBACR;gBAEA,MAAM,oOAAIA,eAAAA,CAAa;oBACrB,SAAS;oBACT,OAAO;oBACP,YAAY,SAAS,MAAA;oBACrB;oBACA;oBACA,mBAAmB,KAAK,MAAA;gBAC1B,CAAC;YACH;YAEA,MAAM,iBAAiB,KAAA;QACzB;QAEA,IAAI;YACF,OAAO,MAAM,0BAA0B;gBACrC;gBACA;gBACA,mBAAmB,KAAK,MAAA;YAC1B,CAAC;QACH,EAAA,OAAS,OAAO;YACd,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,aAAa,KAAK,qOAAKA,eAAAA,CAAa,UAAA,CAAW,KAAK,GAAG;oBACzD,MAAM;gBACR;YACF;YAEA,MAAM,oOAAIA,eAAAA,CAAa;gBACrB,SAAS;gBACT,OAAO;gBACP,YAAY,SAAS,MAAA;gBACrB;gBACA;gBACA,mBAAmB,KAAK,MAAA;YAC1B,CAAC;QACH;IACF,EAAA,OAAS,OAAO;QACd,IAAI,aAAa,KAAK,GAAG;YACvB,MAAM;QACR;QAGA,IAAI,iBAAiB,aAAa,MAAM,OAAA,KAAY,gBAAgB;YAClE,MAAM,QAAS,MAAc,KAAA;YAE7B,IAAI,SAAS,MAAM;gBAEjB,MAAM,oOAAIA,eAAAA,CAAa;oBACrB,SAAS,CAAA,uBAAA,EAA0B,MAAM,OAAO,EAAA;oBAChD;oBACA;oBACA,mBAAmB,KAAK,MAAA;oBACxB,aAAa;gBACf,CAAC;YACH;QACF;QAEA,MAAM;IACR;AACF;;ACxKA,eAAsB,QAAW,KAAA,EAAkC;IAEjE,IAAI,OAAO,UAAU,YAAY;QAC/B,QAAS,MAAmB;IAC9B;IAGA,OAAO,QAAQ,OAAA,CAAQ,KAAU;AACnC;;ACCO,IAAM,iCACX,CAAI,EACF,WAAA,EACA,cAAA,EACA,WAAA,EACF,GAKA,OAAO,EAAE,QAAA,EAAU,GAAA,EAAK,iBAAA,CAAkB,CAAA,KAAM;QAC9C,MAAM,eAAe,MAAM,SAAS,IAAA,CAAK;QACzC,MAAM,kBAAkB,uBAAuB,QAAQ;QAGvD,IAAI,aAAa,IAAA,CAAK,MAAM,IAAI;YAC9B,OAAO;gBACL;gBACA,OAAO,IAAIE,+OAAAA,CAAa;oBACtB,SAAS,SAAS,UAAA;oBAClB;oBACA;oBACA,YAAY,SAAS,MAAA;oBACrB;oBACA;oBACA,aAAa,eAAA,OAAA,KAAA,IAAA,YAAc;gBAC7B,CAAC;YACH;QACF;QAGA,IAAI;YACF,MAAM,cAAc,UAAU;gBAC5B,MAAM;gBACN,QAAQ;YACV,CAAC;YAED,OAAO;gBACL;gBACA,OAAO,oOAAIA,eAAAA,CAAa;oBACtB,SAAS,eAAe,WAAW;oBACnC;oBACA;oBACA,YAAY,SAAS,MAAA;oBACrB;oBACA;oBACA,MAAM;oBACN,aAAa,eAAA,OAAA,KAAA,IAAA,YAAc,UAAU;gBACvC,CAAC;YACH;QACF,EAAA,OAAS,YAAY;YACnB,OAAO;gBACL;gBACA,OAAO,oOAAIA,eAAAA,CAAa;oBACtB,SAAS,SAAS,UAAA;oBAClB;oBACA;oBACA,YAAY,SAAS,MAAA;oBACrB;oBACA;oBACA,aAAa,eAAA,OAAA,KAAA,IAAA,YAAc;gBAC7B,CAAC;YACH;QACF;IACF;AAEK,IAAM,mCACX,CACE,cAEF,OAAO,EAAE,QAAA,CAAS,CAAA,KAA8B;QAC9C,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,SAAS,IAAA,IAAQ,MAAM;YACzB,MAAM,oOAAI,yBAAA,CAAuB,CAAC,CAAC;QACrC;QAEA,OAAO;YACL;YACA,OAAO,SAAS,IAAA,CACb,WAAA,CAAY,IAAI,kBAAkB,CAAC,EACnC,WAAA,CAAY,8BAA8B,CAAC,EAC3C,WAAA,CACC,IAAI,gBAAkD;gBACpD,WAAU,EAAE,IAAA,CAAK,CAAA,EAAG,UAAA,EAAY;oBAE9B,IAAI,SAAS,UAAU;wBACrB;oBACF;oBAEA,WAAW,OAAA,CACT,cAAc;wBACZ,MAAM;wBACN,QAAQ;oBACV,CAAC;gBAEL;YACF,CAAC;QAEP;IACF;AAEK,IAAM,kCACX,CACE,cAEF,OAAO,EAAE,QAAA,CAAS,CAAA,KAA8B;QAC9C,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,SAAS,IAAA,IAAQ,MAAM;YACzB,MAAM,mOAAI,0BAAA,CAAuB,CAAC,CAAC;QACrC;QAEA,IAAI,SAAS;QAEb,OAAO;YACL;YACA,OAAO,SAAS,IAAA,CAAK,WAAA,CAAY,IAAI,kBAAkB,CAAC,EAAE,WAAA,CACxD,IAAI,gBAAwC;gBAC1C,WAAU,SAAA,EAAW,UAAA,EAAY;oBAC/B,IAAI,UAAU,QAAA,CAAS,IAAI,GAAG;wBAC5B,WAAW,OAAA,CACT,cAAc;4BACZ,MAAM,SAAS;4BACf,QAAQ;wBACV,CAAC;wBAEH,SAAS;oBACX,OAAO;wBACL,UAAU;oBACZ;gBACF;YACF,CAAC;QAEL;IACF;AAEK,IAAM,4BACX,CAAI,iBACJ,OAAO,EAAE,QAAA,EAAU,GAAA,EAAK,iBAAA,CAAkB,CAAA,KAAM;QAC9C,MAAM,eAAe,MAAM,SAAS,IAAA,CAAK;QAEzC,MAAM,eAAe,cAAc;YACjC,MAAM;YACN,QAAQ;QACV,CAAC;QAED,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,CAAC,aAAa,OAAA,EAAS;YACzB,MAAM,oOAAIA,eAAAA,CAAa;gBACrB,SAAS;gBACT,OAAO,aAAa,KAAA;gBACpB,YAAY,SAAS,MAAA;gBACrB;gBACA;gBACA;gBACA;YACF,CAAC;QACH;QAEA,OAAO;YACL;YACA,OAAO,aAAa,KAAA;YACpB,UAAU,aAAa,QAAA;QACzB;IACF;AAEK,IAAM,8BACX,IACA,OAAO,EAAE,QAAA,EAAU,GAAA,EAAK,iBAAA,CAAkB,CAAA,KAAM;QAC9C,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,CAAC,SAAS,IAAA,EAAM;YAClB,MAAM,oOAAIA,eAAAA,CAAa;gBACrB,SAAS;gBACT;gBACA;gBACA,YAAY,SAAS,MAAA;gBACrB;gBACA,cAAc,KAAA;YAChB,CAAC;QACH;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,SAAS,WAAA,CAAY;YAC1C,OAAO;gBACL;gBACA,OAAO,IAAI,WAAW,MAAM;YAC9B;QACF,EAAA,OAAS,OAAO;YACd,MAAM,oOAAIA,eAAAA,CAAa;gBACrB,SAAS;gBACT;gBACA;gBACA,YAAY,SAAS,MAAA;gBACrB;gBACA,cAAc,KAAA;gBACd,OAAO;YACT,CAAC;QACH;IACF;AAEK,IAAM,uCACX,IACA,OAAO,EAAE,QAAA,EAAU,GAAA,EAAK,iBAAA,CAAkB,CAAA,KAAM;QAC9C,MAAM,kBAAkB,uBAAuB,QAAQ;QACvD,MAAM,eAAe,MAAM,SAAS,IAAA,CAAK;QAEzC,OAAO;YACL;YACA,OAAO,oOAAIA,eAAAA,CAAa;gBACtB,SAAS,SAAS,UAAA;gBAClB;gBACA;gBACA,YAAY,SAAS,MAAA;gBACrB;gBACA;YACF,CAAC;QACH;IACF;;AC5OF,IAAM,EAAE,IAAA,EAAM,IAAA,CAAK,CAAA,GAAI;AAEhB,SAAS,0BAA0B,YAAA,EAAsB;IAC9D,MAAM,YAAY,aAAa,OAAA,CAAQ,MAAM,GAAG,EAAE,OAAA,CAAQ,MAAM,GAAG;IACnE,MAAM,eAAe,KAAK,SAAS;IACnC,OAAO,WAAW,IAAA,CAAK,cAAc,CAAA,OAAQ,KAAK,WAAA,CAAY,CAAC,CAAE;AACnE;AAEO,SAAS,0BAA0B,KAAA,EAA2B;IACnE,IAAI,eAAe;IAInB,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAK;QACrC,gBAAgB,OAAO,aAAA,CAAc,KAAA,CAAM,CAAC,CAAC;IAC/C;IAEA,OAAO,KAAK,YAAY;AAC1B;;ACrBO,SAAS,qBAAqB,GAAA,EAAyB;IAC5D,OAAO,OAAA,OAAA,KAAA,IAAA,IAAK,OAAA,CAAQ,OAAO;AAC7B", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21], "debugId": null}}, {"offset": {"line": 1349, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/index.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/assistant-stream-parts.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/process-chat-response.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/duplicated/usage.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/parse-partial-json.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/fix-json.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/data-stream-parts.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/process-data-stream.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/process-chat-text-response.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/process-text-stream.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/call-chat-api.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/call-completion-api.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/data-url.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/extract-max-tool-invocation-step.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/get-message-parts.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/fill-message-parts.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/is-deep-equal-data.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/prepare-attachments-for-request.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/process-assistant-stream.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/schema.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/zod-schema.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/should-resubmit-messages.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bui-utils%401.2.11_zod%403.24.1/node_modules/%40ai-sdk/ui-utils/src/update-tool-call-result.ts"], "sourcesContent": ["export * from './types';\n\nexport { generateId } from '@ai-sdk/provider-utils';\n\n// Export stream data utilities for custom stream implementations,\n// both on the client and server side.\n// NOTE: this is experimental / internal and may change without notice\nexport {\n  formatAssistantStreamPart,\n  parseAssistantStreamPart,\n} from './assistant-stream-parts';\nexport type {\n  AssistantStreamPart,\n  AssistantStreamString,\n} from './assistant-stream-parts';\nexport { callChatApi } from './call-chat-api';\nexport { callCompletionApi } from './call-completion-api';\nexport { formatDataStreamPart, parseDataStreamPart } from './data-stream-parts';\nexport type { DataStreamPart, DataStreamString } from './data-stream-parts';\nexport { getTextFromDataUrl } from './data-url';\nexport type { DeepPartial } from './deep-partial';\nexport { extractMaxToolInvocationStep } from './extract-max-tool-invocation-step';\nexport { fillMessageParts } from './fill-message-parts';\nexport { getMessageParts } from './get-message-parts';\nexport { isDeepEqualData } from './is-deep-equal-data';\nexport { parsePartialJson } from './parse-partial-json';\nexport { prepareAttachmentsForRequest } from './prepare-attachments-for-request';\nexport { processAssistantStream } from './process-assistant-stream';\nexport { processDataStream } from './process-data-stream';\nexport { processTextStream } from './process-text-stream';\nexport { asSchema, jsonSchema } from './schema';\nexport type { Schema } from './schema';\nexport {\n  isAssistantMessageWithCompletedToolCalls,\n  shouldResubmitMessages,\n} from './should-resubmit-messages';\nexport { updateToolCallResult } from './update-tool-call-result';\nexport { zodSchema } from './zod-schema';\n", "import { AssistantMessage, DataMessage, JSONValue } from './types';\n\nexport type AssistantStreamString =\n  `${(typeof StreamStringPrefixes)[keyof typeof StreamStringPrefixes]}:${string}\\n`;\n\nexport interface AssistantStreamPart<\n  CODE extends string,\n  NAME extends string,\n  TYPE,\n> {\n  code: CODE;\n  name: NAME;\n  parse: (value: JSONValue) => { type: NAME; value: TYPE };\n}\n\nconst textStreamPart: AssistantStreamPart<'0', 'text', string> = {\n  code: '0',\n  name: 'text',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: 'text', value };\n  },\n};\n\nconst errorStreamPart: AssistantStreamPart<'3', 'error', string> = {\n  code: '3',\n  name: 'error',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: 'error', value };\n  },\n};\n\nconst assistantMessageStreamPart: AssistantStreamPart<\n  '4',\n  'assistant_message',\n  AssistantMessage\n> = {\n  code: '4',\n  name: 'assistant_message',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('id' in value) ||\n      !('role' in value) ||\n      !('content' in value) ||\n      typeof value.id !== 'string' ||\n      typeof value.role !== 'string' ||\n      value.role !== 'assistant' ||\n      !Array.isArray(value.content) ||\n      !value.content.every(\n        item =>\n          item != null &&\n          typeof item === 'object' &&\n          'type' in item &&\n          item.type === 'text' &&\n          'text' in item &&\n          item.text != null &&\n          typeof item.text === 'object' &&\n          'value' in item.text &&\n          typeof item.text.value === 'string',\n      )\n    ) {\n      throw new Error(\n        '\"assistant_message\" parts expect an object with an \"id\", \"role\", and \"content\" property.',\n      );\n    }\n\n    return {\n      type: 'assistant_message',\n      value: value as AssistantMessage,\n    };\n  },\n};\n\nconst assistantControlDataStreamPart: AssistantStreamPart<\n  '5',\n  'assistant_control_data',\n  {\n    threadId: string;\n    messageId: string;\n  }\n> = {\n  code: '5',\n  name: 'assistant_control_data',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('threadId' in value) ||\n      !('messageId' in value) ||\n      typeof value.threadId !== 'string' ||\n      typeof value.messageId !== 'string'\n    ) {\n      throw new Error(\n        '\"assistant_control_data\" parts expect an object with a \"threadId\" and \"messageId\" property.',\n      );\n    }\n\n    return {\n      type: 'assistant_control_data',\n      value: {\n        threadId: value.threadId,\n        messageId: value.messageId,\n      },\n    };\n  },\n};\n\nconst dataMessageStreamPart: AssistantStreamPart<\n  '6',\n  'data_message',\n  DataMessage\n> = {\n  code: '6',\n  name: 'data_message',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('role' in value) ||\n      !('data' in value) ||\n      typeof value.role !== 'string' ||\n      value.role !== 'data'\n    ) {\n      throw new Error(\n        '\"data_message\" parts expect an object with a \"role\" and \"data\" property.',\n      );\n    }\n\n    return {\n      type: 'data_message',\n      value: value as DataMessage,\n    };\n  },\n};\n\nconst assistantStreamParts = [\n  textStreamPart,\n  errorStreamPart,\n  assistantMessageStreamPart,\n  assistantControlDataStreamPart,\n  dataMessageStreamPart,\n] as const;\n\ntype AssistantStreamParts =\n  | typeof textStreamPart\n  | typeof errorStreamPart\n  | typeof assistantMessageStreamPart\n  | typeof assistantControlDataStreamPart\n  | typeof dataMessageStreamPart;\n\ntype AssistantStreamPartValueType = {\n  [P in AssistantStreamParts as P['name']]: ReturnType<P['parse']>['value'];\n};\n\nexport type AssistantStreamPartType =\n  | ReturnType<typeof textStreamPart.parse>\n  | ReturnType<typeof errorStreamPart.parse>\n  | ReturnType<typeof assistantMessageStreamPart.parse>\n  | ReturnType<typeof assistantControlDataStreamPart.parse>\n  | ReturnType<typeof dataMessageStreamPart.parse>;\n\nexport const assistantStreamPartsByCode = {\n  [textStreamPart.code]: textStreamPart,\n  [errorStreamPart.code]: errorStreamPart,\n  [assistantMessageStreamPart.code]: assistantMessageStreamPart,\n  [assistantControlDataStreamPart.code]: assistantControlDataStreamPart,\n  [dataMessageStreamPart.code]: dataMessageStreamPart,\n} as const;\n\nexport const StreamStringPrefixes = {\n  [textStreamPart.name]: textStreamPart.code,\n  [errorStreamPart.name]: errorStreamPart.code,\n  [assistantMessageStreamPart.name]: assistantMessageStreamPart.code,\n  [assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,\n  [dataMessageStreamPart.name]: dataMessageStreamPart.code,\n} as const;\n\nexport const validCodes = assistantStreamParts.map(part => part.code);\n\nexport const parseAssistantStreamPart = (\n  line: string,\n): AssistantStreamPartType => {\n  const firstSeparatorIndex = line.indexOf(':');\n\n  if (firstSeparatorIndex === -1) {\n    throw new Error('Failed to parse stream string. No separator found.');\n  }\n\n  const prefix = line.slice(0, firstSeparatorIndex);\n\n  if (!validCodes.includes(prefix as keyof typeof assistantStreamPartsByCode)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n\n  const code = prefix as keyof typeof assistantStreamPartsByCode;\n\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue: JSONValue = JSON.parse(textValue);\n\n  return assistantStreamPartsByCode[code].parse(jsonValue);\n};\n\nexport function formatAssistantStreamPart<\n  T extends keyof AssistantStreamPartValueType,\n>(type: T, value: AssistantStreamPartValueType[T]): AssistantStreamString {\n  const streamPart = assistantStreamParts.find(part => part.name === type);\n\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n\n  return `${streamPart.code}:${JSON.stringify(value)}\\n`;\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\nimport { generateId as generateIdFunction } from '@ai-sdk/provider-utils';\nimport {\n  calculateLanguageModelUsage,\n  LanguageModelUsage,\n} from './duplicated/usage';\nimport { parsePartialJson } from './parse-partial-json';\nimport { processDataStream } from './process-data-stream';\nimport type {\n  JSONValue,\n  ReasoningUIPart,\n  TextUIPart,\n  ToolInvocation,\n  ToolInvocationUIPart,\n  UIMessage,\n  UseChatOptions,\n} from './types';\n\nexport async function processChatResponse({\n  stream,\n  update,\n  onToolCall,\n  onFinish,\n  generateId = generateIdFunction,\n  getCurrentDate = () => new Date(),\n  lastMessage,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  update: (options: {\n    message: UIMessage;\n    data: JSONValue[] | undefined;\n    replaceLastMessage: boolean;\n  }) => void;\n  onToolCall?: UseChatOptions['onToolCall'];\n  onFinish?: (options: {\n    message: UIMessage | undefined;\n    finishReason: LanguageModelV1FinishReason;\n    usage: LanguageModelUsage;\n  }) => void;\n  generateId?: () => string;\n  getCurrentDate?: () => Date;\n  lastMessage: UIMessage | undefined;\n}) {\n  const replaceLastMessage = lastMessage?.role === 'assistant';\n  let step = replaceLastMessage\n    ? 1 +\n      // find max step in existing tool invocations:\n      (lastMessage.toolInvocations?.reduce((max, toolInvocation) => {\n        return Math.max(max, toolInvocation.step ?? 0);\n      }, 0) ?? 0)\n    : 0;\n\n  const message: UIMessage = replaceLastMessage\n    ? structuredClone(lastMessage)\n    : {\n        id: generateId(),\n        createdAt: getCurrentDate(),\n        role: 'assistant',\n        content: '',\n        parts: [],\n      };\n\n  let currentTextPart: TextUIPart | undefined = undefined;\n  let currentReasoningPart: ReasoningUIPart | undefined = undefined;\n  let currentReasoningTextDetail:\n    | { type: 'text'; text: string; signature?: string }\n    | undefined = undefined;\n\n  function updateToolInvocationPart(\n    toolCallId: string,\n    invocation: ToolInvocation,\n  ) {\n    const part = message.parts.find(\n      part =>\n        part.type === 'tool-invocation' &&\n        part.toolInvocation.toolCallId === toolCallId,\n    ) as ToolInvocationUIPart | undefined;\n\n    if (part != null) {\n      part.toolInvocation = invocation;\n    } else {\n      message.parts.push({\n        type: 'tool-invocation',\n        toolInvocation: invocation,\n      });\n    }\n  }\n\n  const data: JSONValue[] = [];\n\n  // keep list of current message annotations for message\n  let messageAnnotations: JSONValue[] | undefined = replaceLastMessage\n    ? lastMessage?.annotations\n    : undefined;\n\n  // keep track of partial tool calls\n  const partialToolCalls: Record<\n    string,\n    { text: string; step: number; index: number; toolName: string }\n  > = {};\n\n  let usage: LanguageModelUsage = {\n    completionTokens: NaN,\n    promptTokens: NaN,\n    totalTokens: NaN,\n  };\n  let finishReason: LanguageModelV1FinishReason = 'unknown';\n\n  function execUpdate() {\n    // make a copy of the data array to ensure UI is updated (SWR)\n    const copiedData = [...data];\n\n    // keeps the currentMessage up to date with the latest annotations,\n    // even if annotations preceded the message creation\n    if (messageAnnotations?.length) {\n      message.annotations = messageAnnotations;\n    }\n\n    const copiedMessage = {\n      // deep copy the message to ensure that deep changes (msg attachments) are updated\n      // with SolidJS. SolidJS uses referential integration of sub-objects to detect changes.\n      ...structuredClone(message),\n      // add a revision id to ensure that the message is updated with SWR. SWR uses a\n      // hashing approach by default to detect changes, but it only works for shallow\n      // changes. This is why we need to add a revision id to ensure that the message\n      // is updated with SWR (without it, the changes get stuck in SWR and are not\n      // forwarded to rendering):\n      revisionId: generateId(),\n    } as UIMessage;\n\n    update({\n      message: copiedMessage,\n      data: copiedData,\n      replaceLastMessage,\n    });\n  }\n\n  await processDataStream({\n    stream,\n    onTextPart(value) {\n      if (currentTextPart == null) {\n        currentTextPart = {\n          type: 'text',\n          text: value,\n        };\n        message.parts.push(currentTextPart);\n      } else {\n        currentTextPart.text += value;\n      }\n\n      message.content += value;\n      execUpdate();\n    },\n    onReasoningPart(value) {\n      if (currentReasoningTextDetail == null) {\n        currentReasoningTextDetail = { type: 'text', text: value };\n        if (currentReasoningPart != null) {\n          currentReasoningPart.details.push(currentReasoningTextDetail);\n        }\n      } else {\n        currentReasoningTextDetail.text += value;\n      }\n\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: 'reasoning',\n          reasoning: value,\n          details: [currentReasoningTextDetail],\n        };\n        message.parts.push(currentReasoningPart);\n      } else {\n        currentReasoningPart.reasoning += value;\n      }\n\n      message.reasoning = (message.reasoning ?? '') + value;\n\n      execUpdate();\n    },\n    onReasoningSignaturePart(value) {\n      if (currentReasoningTextDetail != null) {\n        currentReasoningTextDetail.signature = value.signature;\n      }\n    },\n    onRedactedReasoningPart(value) {\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: 'reasoning',\n          reasoning: '',\n          details: [],\n        };\n        message.parts.push(currentReasoningPart);\n      }\n\n      currentReasoningPart.details.push({\n        type: 'redacted',\n        data: value.data,\n      });\n\n      currentReasoningTextDetail = undefined;\n\n      execUpdate();\n    },\n    onFilePart(value) {\n      message.parts.push({\n        type: 'file',\n        mimeType: value.mimeType,\n        data: value.data,\n      });\n\n      execUpdate();\n    },\n    onSourcePart(value) {\n      message.parts.push({\n        type: 'source',\n        source: value,\n      });\n\n      execUpdate();\n    },\n    onToolCallStreamingStartPart(value) {\n      if (message.toolInvocations == null) {\n        message.toolInvocations = [];\n      }\n\n      // add the partial tool call to the map\n      partialToolCalls[value.toolCallId] = {\n        text: '',\n        step,\n        toolName: value.toolName,\n        index: message.toolInvocations.length,\n      };\n\n      const invocation = {\n        state: 'partial-call',\n        step,\n        toolCallId: value.toolCallId,\n        toolName: value.toolName,\n        args: undefined,\n      } as const;\n\n      message.toolInvocations.push(invocation);\n\n      updateToolInvocationPart(value.toolCallId, invocation);\n\n      execUpdate();\n    },\n    onToolCallDeltaPart(value) {\n      const partialToolCall = partialToolCalls[value.toolCallId];\n\n      partialToolCall.text += value.argsTextDelta;\n\n      const { value: partialArgs } = parsePartialJson(partialToolCall.text);\n\n      const invocation = {\n        state: 'partial-call',\n        step: partialToolCall.step,\n        toolCallId: value.toolCallId,\n        toolName: partialToolCall.toolName,\n        args: partialArgs,\n      } as const;\n\n      message.toolInvocations![partialToolCall.index] = invocation;\n\n      updateToolInvocationPart(value.toolCallId, invocation);\n\n      execUpdate();\n    },\n    async onToolCallPart(value) {\n      const invocation = {\n        state: 'call',\n        step,\n        ...value,\n      } as const;\n\n      if (partialToolCalls[value.toolCallId] != null) {\n        // change the partial tool call to a full tool call\n        message.toolInvocations![partialToolCalls[value.toolCallId].index] =\n          invocation;\n      } else {\n        if (message.toolInvocations == null) {\n          message.toolInvocations = [];\n        }\n\n        message.toolInvocations.push(invocation);\n      }\n\n      updateToolInvocationPart(value.toolCallId, invocation);\n\n      execUpdate();\n\n      // invoke the onToolCall callback if it exists. This is blocking.\n      // In the future we should make this non-blocking, which\n      // requires additional state management for error handling etc.\n      if (onToolCall) {\n        const result = await onToolCall({ toolCall: value });\n        if (result != null) {\n          const invocation = {\n            state: 'result',\n            step,\n            ...value,\n            result,\n          } as const;\n\n          // store the result in the tool invocation\n          message.toolInvocations![message.toolInvocations!.length - 1] =\n            invocation;\n\n          updateToolInvocationPart(value.toolCallId, invocation);\n\n          execUpdate();\n        }\n      }\n    },\n    onToolResultPart(value) {\n      const toolInvocations = message.toolInvocations;\n\n      if (toolInvocations == null) {\n        throw new Error('tool_result must be preceded by a tool_call');\n      }\n\n      // find if there is any tool invocation with the same toolCallId\n      // and replace it with the result\n      const toolInvocationIndex = toolInvocations.findIndex(\n        invocation => invocation.toolCallId === value.toolCallId,\n      );\n\n      if (toolInvocationIndex === -1) {\n        throw new Error(\n          'tool_result must be preceded by a tool_call with the same toolCallId',\n        );\n      }\n\n      const invocation = {\n        ...toolInvocations[toolInvocationIndex],\n        state: 'result' as const,\n        ...value,\n      } as const;\n\n      toolInvocations[toolInvocationIndex] = invocation;\n\n      updateToolInvocationPart(value.toolCallId, invocation);\n\n      execUpdate();\n    },\n    onDataPart(value) {\n      data.push(...value);\n      execUpdate();\n    },\n    onMessageAnnotationsPart(value) {\n      if (messageAnnotations == null) {\n        messageAnnotations = [...value];\n      } else {\n        messageAnnotations.push(...value);\n      }\n\n      execUpdate();\n    },\n    onFinishStepPart(value) {\n      step += 1;\n\n      // reset the current text and reasoning parts\n      currentTextPart = value.isContinued ? currentTextPart : undefined;\n      currentReasoningPart = undefined;\n      currentReasoningTextDetail = undefined;\n    },\n    onStartStepPart(value) {\n      // keep message id stable when we are updating an existing message:\n      if (!replaceLastMessage) {\n        message.id = value.messageId;\n      }\n\n      // add a step boundary part to the message\n      message.parts.push({ type: 'step-start' });\n      execUpdate();\n    },\n    onFinishMessagePart(value) {\n      finishReason = value.finishReason;\n      if (value.usage != null) {\n        usage = calculateLanguageModelUsage(value.usage);\n      }\n    },\n    onErrorPart(error) {\n      throw new Error(error);\n    },\n  });\n\n  onFinish?.({ message, finishReason, usage });\n}\n", "/**\nRepresents the number of tokens used in a prompt and completion.\n */\nexport type LanguageModelUsage = {\n  /**\nThe number of tokens used in the prompt.\n   */\n  promptTokens: number;\n\n  /**\nThe number of tokens used in the completion.\n */\n  completionTokens: number;\n\n  /**\nThe total number of tokens used (promptTokens + completionTokens).\n   */\n  totalTokens: number;\n};\n\n/**\nRepresents the number of tokens used in an embedding.\n */\nexport type EmbeddingModelUsage = {\n  /**\nThe number of tokens used in the embedding.\n   */\n  tokens: number;\n};\n\nexport function calculateLanguageModelUsage({\n  promptTokens,\n  completionTokens,\n}: {\n  promptTokens: number;\n  completionTokens: number;\n}): LanguageModelUsage {\n  return {\n    promptTokens,\n    completionTokens,\n    totalTokens: promptTokens + completionTokens,\n  };\n}\n", "import { JSONValue } from '@ai-sdk/provider';\nimport { safeParseJSON } from '@ai-sdk/provider-utils';\nimport { fixJson } from './fix-json';\n\nexport function parsePartialJson(jsonText: string | undefined): {\n  value: JSONValue | undefined;\n  state:\n    | 'undefined-input'\n    | 'successful-parse'\n    | 'repaired-parse'\n    | 'failed-parse';\n} {\n  if (jsonText === undefined) {\n    return { value: undefined, state: 'undefined-input' };\n  }\n\n  let result = safeParseJSON({ text: jsonText });\n\n  if (result.success) {\n    return { value: result.value, state: 'successful-parse' };\n  }\n\n  result = safeParseJSON({ text: fixJson(jsonText) });\n\n  if (result.success) {\n    return { value: result.value, state: 'repaired-parse' };\n  }\n\n  return { value: undefined, state: 'failed-parse' };\n}\n", "type State =\n  | 'ROOT'\n  | 'FINISH'\n  | 'INSIDE_STRING'\n  | 'INSIDE_STRING_ESCAPE'\n  | 'INSIDE_LITERAL'\n  | 'INSIDE_NUMBER'\n  | 'INSIDE_OBJECT_START'\n  | 'INSIDE_OBJECT_KEY'\n  | 'INSIDE_OBJECT_AFTER_KEY'\n  | 'INSIDE_OBJECT_BEFORE_VALUE'\n  | 'INSIDE_OBJECT_AFTER_VALUE'\n  | 'INSIDE_OBJECT_AFTER_COMMA'\n  | 'INSIDE_ARRAY_START'\n  | 'INSIDE_ARRAY_AFTER_VALUE'\n  | 'INSIDE_ARRAY_AFTER_COMMA';\n\n// Implemented as a scanner with additional fixing\n// that performs a single linear time scan pass over the partial JSON.\n//\n// The states should ideally match relevant states from the JSON spec:\n// https://www.json.org/json-en.html\n//\n// Please note that invalid JSON is not considered/covered, because it\n// is assumed that the resulting JSON will be processed by a standard\n// JSON parser that will detect any invalid JSON.\nexport function fixJson(input: string): string {\n  const stack: State[] = ['ROOT'];\n  let lastValidIndex = -1;\n  let literalStart: number | null = null;\n\n  function processValueStart(char: string, i: number, swapState: State) {\n    {\n      switch (char) {\n        case '\"': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_STRING');\n          break;\n        }\n\n        case 'f':\n        case 't':\n        case 'n': {\n          lastValidIndex = i;\n          literalStart = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_LITERAL');\n          break;\n        }\n\n        case '-': {\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_NUMBER');\n          break;\n        }\n        case '0':\n        case '1':\n        case '2':\n        case '3':\n        case '4':\n        case '5':\n        case '6':\n        case '7':\n        case '8':\n        case '9': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_NUMBER');\n          break;\n        }\n\n        case '{': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_OBJECT_START');\n          break;\n        }\n\n        case '[': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_ARRAY_START');\n          break;\n        }\n      }\n    }\n  }\n\n  function processAfterObjectValue(char: string, i: number) {\n    switch (char) {\n      case ',': {\n        stack.pop();\n        stack.push('INSIDE_OBJECT_AFTER_COMMA');\n        break;\n      }\n      case '}': {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n\n  function processAfterArrayValue(char: string, i: number) {\n    switch (char) {\n      case ',': {\n        stack.pop();\n        stack.push('INSIDE_ARRAY_AFTER_COMMA');\n        break;\n      }\n      case ']': {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n\n  for (let i = 0; i < input.length; i++) {\n    const char = input[i];\n    const currentState = stack[stack.length - 1];\n\n    switch (currentState) {\n      case 'ROOT':\n        processValueStart(char, i, 'FINISH');\n        break;\n\n      case 'INSIDE_OBJECT_START': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_KEY');\n            break;\n          }\n          case '}': {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_AFTER_COMMA': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_KEY');\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_KEY': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_AFTER_KEY');\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_AFTER_KEY': {\n        switch (char) {\n          case ':': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_BEFORE_VALUE');\n\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_BEFORE_VALUE': {\n        processValueStart(char, i, 'INSIDE_OBJECT_AFTER_VALUE');\n        break;\n      }\n\n      case 'INSIDE_OBJECT_AFTER_VALUE': {\n        processAfterObjectValue(char, i);\n        break;\n      }\n\n      case 'INSIDE_STRING': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            lastValidIndex = i;\n            break;\n          }\n\n          case '\\\\': {\n            stack.push('INSIDE_STRING_ESCAPE');\n            break;\n          }\n\n          default: {\n            lastValidIndex = i;\n          }\n        }\n\n        break;\n      }\n\n      case 'INSIDE_ARRAY_START': {\n        switch (char) {\n          case ']': {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n\n          default: {\n            lastValidIndex = i;\n            processValueStart(char, i, 'INSIDE_ARRAY_AFTER_VALUE');\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_ARRAY_AFTER_VALUE': {\n        switch (char) {\n          case ',': {\n            stack.pop();\n            stack.push('INSIDE_ARRAY_AFTER_COMMA');\n            break;\n          }\n\n          case ']': {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n\n          default: {\n            lastValidIndex = i;\n            break;\n          }\n        }\n\n        break;\n      }\n\n      case 'INSIDE_ARRAY_AFTER_COMMA': {\n        processValueStart(char, i, 'INSIDE_ARRAY_AFTER_VALUE');\n        break;\n      }\n\n      case 'INSIDE_STRING_ESCAPE': {\n        stack.pop();\n        lastValidIndex = i;\n\n        break;\n      }\n\n      case 'INSIDE_NUMBER': {\n        switch (char) {\n          case '0':\n          case '1':\n          case '2':\n          case '3':\n          case '4':\n          case '5':\n          case '6':\n          case '7':\n          case '8':\n          case '9': {\n            lastValidIndex = i;\n            break;\n          }\n\n          case 'e':\n          case 'E':\n          case '-':\n          case '.': {\n            break;\n          }\n\n          case ',': {\n            stack.pop();\n\n            if (stack[stack.length - 1] === 'INSIDE_ARRAY_AFTER_VALUE') {\n              processAfterArrayValue(char, i);\n            }\n\n            if (stack[stack.length - 1] === 'INSIDE_OBJECT_AFTER_VALUE') {\n              processAfterObjectValue(char, i);\n            }\n\n            break;\n          }\n\n          case '}': {\n            stack.pop();\n\n            if (stack[stack.length - 1] === 'INSIDE_OBJECT_AFTER_VALUE') {\n              processAfterObjectValue(char, i);\n            }\n\n            break;\n          }\n\n          case ']': {\n            stack.pop();\n\n            if (stack[stack.length - 1] === 'INSIDE_ARRAY_AFTER_VALUE') {\n              processAfterArrayValue(char, i);\n            }\n\n            break;\n          }\n\n          default: {\n            stack.pop();\n            break;\n          }\n        }\n\n        break;\n      }\n\n      case 'INSIDE_LITERAL': {\n        const partialLiteral = input.substring(literalStart!, i + 1);\n\n        if (\n          !'false'.startsWith(partialLiteral) &&\n          !'true'.startsWith(partialLiteral) &&\n          !'null'.startsWith(partialLiteral)\n        ) {\n          stack.pop();\n\n          if (stack[stack.length - 1] === 'INSIDE_OBJECT_AFTER_VALUE') {\n            processAfterObjectValue(char, i);\n          } else if (stack[stack.length - 1] === 'INSIDE_ARRAY_AFTER_VALUE') {\n            processAfterArrayValue(char, i);\n          }\n        } else {\n          lastValidIndex = i;\n        }\n\n        break;\n      }\n    }\n  }\n\n  let result = input.slice(0, lastValidIndex + 1);\n\n  for (let i = stack.length - 1; i >= 0; i--) {\n    const state = stack[i];\n\n    switch (state) {\n      case 'INSIDE_STRING': {\n        result += '\"';\n        break;\n      }\n\n      case 'INSIDE_OBJECT_KEY':\n      case 'INSIDE_OBJECT_AFTER_KEY':\n      case 'INSIDE_OBJECT_AFTER_COMMA':\n      case 'INSIDE_OBJECT_START':\n      case 'INSIDE_OBJECT_BEFORE_VALUE':\n      case 'INSIDE_OBJECT_AFTER_VALUE': {\n        result += '}';\n        break;\n      }\n\n      case 'INSIDE_ARRAY_START':\n      case 'INSIDE_ARRAY_AFTER_COMMA':\n      case 'INSIDE_ARRAY_AFTER_VALUE': {\n        result += ']';\n        break;\n      }\n\n      case 'INSIDE_LITERAL': {\n        const partialLiteral = input.substring(literalStart!, input.length);\n\n        if ('true'.startsWith(partialLiteral)) {\n          result += 'true'.slice(partialLiteral.length);\n        } else if ('false'.startsWith(partialLiteral)) {\n          result += 'false'.slice(partialLiteral.length);\n        } else if ('null'.startsWith(partialLiteral)) {\n          result += 'null'.slice(partialLiteral.length);\n        }\n      }\n    }\n  }\n\n  return result;\n}\n", "import {\n  LanguageModelV1FinishReason,\n  LanguageModelV1Source,\n} from '@ai-sdk/provider';\nimport { Tool<PERSON>all, ToolResult } from '@ai-sdk/provider-utils';\nimport { JSONValue } from './types';\n\nexport type DataStreamString =\n  `${(typeof DataStreamStringPrefixes)[keyof typeof DataStreamStringPrefixes]}:${string}\\n`;\n\nexport interface DataStreamPart<\n  CODE extends string,\n  NAME extends string,\n  TYPE,\n> {\n  code: CODE;\n  name: NAME;\n  parse: (value: JSONValue) => { type: NAME; value: TYPE };\n}\n\nconst textStreamPart: DataStreamPart<'0', 'text', string> = {\n  code: '0',\n  name: 'text',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: 'text', value };\n  },\n};\n\nconst dataStreamPart: DataStreamPart<'2', 'data', Array<JSONValue>> = {\n  code: '2',\n  name: 'data',\n  parse: (value: JSONValue) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"data\" parts expect an array value.');\n    }\n\n    return { type: 'data', value };\n  },\n};\n\nconst errorStreamPart: DataStreamPart<'3', 'error', string> = {\n  code: '3',\n  name: 'error',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: 'error', value };\n  },\n};\n\nconst messageAnnotationsStreamPart: DataStreamPart<\n  '8',\n  'message_annotations',\n  Array<JSONValue>\n> = {\n  code: '8',\n  name: 'message_annotations',\n  parse: (value: JSONValue) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"message_annotations\" parts expect an array value.');\n    }\n\n    return { type: 'message_annotations', value };\n  },\n};\n\nconst toolCallStreamPart: DataStreamPart<\n  '9',\n  'tool_call',\n  ToolCall<string, any>\n> = {\n  code: '9',\n  name: 'tool_call',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('toolName' in value) ||\n      typeof value.toolName !== 'string' ||\n      !('args' in value) ||\n      typeof value.args !== 'object'\n    ) {\n      throw new Error(\n        '\"tool_call\" parts expect an object with a \"toolCallId\", \"toolName\", and \"args\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_call',\n      value: value as unknown as ToolCall<string, any>,\n    };\n  },\n};\n\nconst toolResultStreamPart: DataStreamPart<\n  'a',\n  'tool_result',\n  Omit<ToolResult<string, any, any>, 'args' | 'toolName'>\n> = {\n  code: 'a',\n  name: 'tool_result',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('result' in value)\n    ) {\n      throw new Error(\n        '\"tool_result\" parts expect an object with a \"toolCallId\" and a \"result\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_result',\n      value: value as unknown as Omit<\n        ToolResult<string, any, any>,\n        'args' | 'toolName'\n      >,\n    };\n  },\n};\n\nconst toolCallStreamingStartStreamPart: DataStreamPart<\n  'b',\n  'tool_call_streaming_start',\n  { toolCallId: string; toolName: string }\n> = {\n  code: 'b',\n  name: 'tool_call_streaming_start',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('toolName' in value) ||\n      typeof value.toolName !== 'string'\n    ) {\n      throw new Error(\n        '\"tool_call_streaming_start\" parts expect an object with a \"toolCallId\" and \"toolName\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_call_streaming_start',\n      value: value as unknown as { toolCallId: string; toolName: string },\n    };\n  },\n};\n\nconst toolCallDeltaStreamPart: DataStreamPart<\n  'c',\n  'tool_call_delta',\n  { toolCallId: string; argsTextDelta: string }\n> = {\n  code: 'c',\n  name: 'tool_call_delta',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('argsTextDelta' in value) ||\n      typeof value.argsTextDelta !== 'string'\n    ) {\n      throw new Error(\n        '\"tool_call_delta\" parts expect an object with a \"toolCallId\" and \"argsTextDelta\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_call_delta',\n      value: value as unknown as {\n        toolCallId: string;\n        argsTextDelta: string;\n      },\n    };\n  },\n};\n\nconst finishMessageStreamPart: DataStreamPart<\n  'd',\n  'finish_message',\n  {\n    finishReason: LanguageModelV1FinishReason;\n    // TODO v5 remove usage from finish event (only on step-finish)\n    usage?: {\n      promptTokens: number;\n      completionTokens: number;\n    };\n  }\n> = {\n  code: 'd',\n  name: 'finish_message',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('finishReason' in value) ||\n      typeof value.finishReason !== 'string'\n    ) {\n      throw new Error(\n        '\"finish_message\" parts expect an object with a \"finishReason\" property.',\n      );\n    }\n\n    const result: {\n      finishReason: LanguageModelV1FinishReason;\n      usage?: {\n        promptTokens: number;\n        completionTokens: number;\n      };\n    } = {\n      finishReason: value.finishReason as LanguageModelV1FinishReason,\n    };\n\n    if (\n      'usage' in value &&\n      value.usage != null &&\n      typeof value.usage === 'object' &&\n      'promptTokens' in value.usage &&\n      'completionTokens' in value.usage\n    ) {\n      result.usage = {\n        promptTokens:\n          typeof value.usage.promptTokens === 'number'\n            ? value.usage.promptTokens\n            : Number.NaN,\n        completionTokens:\n          typeof value.usage.completionTokens === 'number'\n            ? value.usage.completionTokens\n            : Number.NaN,\n      };\n    }\n\n    return {\n      type: 'finish_message',\n      value: result,\n    };\n  },\n};\n\nconst finishStepStreamPart: DataStreamPart<\n  'e',\n  'finish_step',\n  {\n    isContinued: boolean;\n    finishReason: LanguageModelV1FinishReason;\n    usage?: {\n      promptTokens: number;\n      completionTokens: number;\n    };\n  }\n> = {\n  code: 'e',\n  name: 'finish_step',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('finishReason' in value) ||\n      typeof value.finishReason !== 'string'\n    ) {\n      throw new Error(\n        '\"finish_step\" parts expect an object with a \"finishReason\" property.',\n      );\n    }\n\n    const result: {\n      isContinued: boolean;\n      finishReason: LanguageModelV1FinishReason;\n      usage?: {\n        promptTokens: number;\n        completionTokens: number;\n      };\n    } = {\n      finishReason: value.finishReason as LanguageModelV1FinishReason,\n      isContinued: false,\n    };\n\n    if (\n      'usage' in value &&\n      value.usage != null &&\n      typeof value.usage === 'object' &&\n      'promptTokens' in value.usage &&\n      'completionTokens' in value.usage\n    ) {\n      result.usage = {\n        promptTokens:\n          typeof value.usage.promptTokens === 'number'\n            ? value.usage.promptTokens\n            : Number.NaN,\n        completionTokens:\n          typeof value.usage.completionTokens === 'number'\n            ? value.usage.completionTokens\n            : Number.NaN,\n      };\n    }\n\n    if ('isContinued' in value && typeof value.isContinued === 'boolean') {\n      result.isContinued = value.isContinued;\n    }\n\n    return {\n      type: 'finish_step',\n      value: result,\n    };\n  },\n};\n\nconst startStepStreamPart: DataStreamPart<\n  'f',\n  'start_step',\n  {\n    messageId: string;\n  }\n> = {\n  code: 'f',\n  name: 'start_step',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('messageId' in value) ||\n      typeof value.messageId !== 'string'\n    ) {\n      throw new Error(\n        '\"start_step\" parts expect an object with an \"id\" property.',\n      );\n    }\n\n    return {\n      type: 'start_step',\n      value: {\n        messageId: value.messageId,\n      },\n    };\n  },\n};\n\nconst reasoningStreamPart: DataStreamPart<'g', 'reasoning', string> = {\n  code: 'g',\n  name: 'reasoning',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"reasoning\" parts expect a string value.');\n    }\n    return { type: 'reasoning', value };\n  },\n};\n\nconst sourcePart: DataStreamPart<'h', 'source', LanguageModelV1Source> = {\n  code: 'h',\n  name: 'source',\n  parse: (value: JSONValue) => {\n    if (value == null || typeof value !== 'object') {\n      throw new Error('\"source\" parts expect a Source object.');\n    }\n\n    return {\n      type: 'source',\n      value: value as LanguageModelV1Source,\n    };\n  },\n};\n\nconst redactedReasoningStreamPart: DataStreamPart<\n  'i',\n  'redacted_reasoning',\n  { data: string }\n> = {\n  code: 'i',\n  name: 'redacted_reasoning',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('data' in value) ||\n      typeof value.data !== 'string'\n    ) {\n      throw new Error(\n        '\"redacted_reasoning\" parts expect an object with a \"data\" property.',\n      );\n    }\n    return { type: 'redacted_reasoning', value: { data: value.data } };\n  },\n};\n\nconst reasoningSignatureStreamPart: DataStreamPart<\n  'j',\n  'reasoning_signature',\n  { signature: string }\n> = {\n  code: 'j',\n  name: 'reasoning_signature',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('signature' in value) ||\n      typeof value.signature !== 'string'\n    ) {\n      throw new Error(\n        '\"reasoning_signature\" parts expect an object with a \"signature\" property.',\n      );\n    }\n    return {\n      type: 'reasoning_signature',\n      value: { signature: value.signature },\n    };\n  },\n};\n\nconst fileStreamPart: DataStreamPart<\n  'k',\n  'file',\n  {\n    data: string; // base64 encoded data\n    mimeType: string;\n  }\n> = {\n  code: 'k',\n  name: 'file',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('data' in value) ||\n      typeof value.data !== 'string' ||\n      !('mimeType' in value) ||\n      typeof value.mimeType !== 'string'\n    ) {\n      throw new Error(\n        '\"file\" parts expect an object with a \"data\" and \"mimeType\" property.',\n      );\n    }\n    return { type: 'file', value: value as { data: string; mimeType: string } };\n  },\n};\n\nconst dataStreamParts = [\n  textStreamPart,\n  dataStreamPart,\n  errorStreamPart,\n  messageAnnotationsStreamPart,\n  toolCallStreamPart,\n  toolResultStreamPart,\n  toolCallStreamingStartStreamPart,\n  toolCallDeltaStreamPart,\n  finishMessageStreamPart,\n  finishStepStreamPart,\n  startStepStreamPart,\n  reasoningStreamPart,\n  sourcePart,\n  redactedReasoningStreamPart,\n  reasoningSignatureStreamPart,\n  fileStreamPart,\n] as const;\n\nexport const dataStreamPartsByCode = Object.fromEntries(\n  dataStreamParts.map(part => [part.code, part]),\n) as {\n  [K in (typeof dataStreamParts)[number]['code']]: (typeof dataStreamParts)[number];\n};\n\ntype DataStreamParts = (typeof dataStreamParts)[number];\n\n/**\n * Maps the type of a stream part to its value type.\n */\ntype DataStreamPartValueType = {\n  [P in DataStreamParts as P['name']]: ReturnType<P['parse']>['value'];\n};\n\nexport type DataStreamPartType = ReturnType<DataStreamParts['parse']>;\n\n/**\n * The map of prefixes for data in the stream\n *\n * - 0: Text from the LLM response\n * - 1: (OpenAI) function_call responses\n * - 2: custom JSON added by the user using `Data`\n * - 6: (OpenAI) tool_call responses\n *\n * Example:\n * ```\n * 0:Vercel\n * 0:'s\n * 0: AI\n * 0: AI\n * 0: SDK\n * 0: is great\n * 0:!\n * 2: { \"someJson\": \"value\" }\n * 1: {\"function_call\": {\"name\": \"get_current_weather\", \"arguments\": \"{\\\\n\\\\\"location\\\\\": \\\\\"Charlottesville, Virginia\\\\\",\\\\n\\\\\"format\\\\\": \\\\\"celsius\\\\\"\\\\n}\"}}\n * 6: {\"tool_call\": {\"id\": \"tool_0\", \"type\": \"function\", \"function\": {\"name\": \"get_current_weather\", \"arguments\": \"{\\\\n\\\\\"location\\\\\": \\\\\"Charlottesville, Virginia\\\\\",\\\\n\\\\\"format\\\\\": \\\\\"celsius\\\\\"\\\\n}\"}}}\n *```\n */\nexport const DataStreamStringPrefixes = Object.fromEntries(\n  dataStreamParts.map(part => [part.name, part.code]),\n) as {\n  [K in DataStreamParts['name']]: (typeof dataStreamParts)[number]['code'];\n};\n\nexport const validCodes = dataStreamParts.map(part => part.code);\n\n/**\nParses a stream part from a string.\n\n@param line The string to parse.\n@returns The parsed stream part.\n@throws An error if the string cannot be parsed.\n */\nexport const parseDataStreamPart = (line: string): DataStreamPartType => {\n  const firstSeparatorIndex = line.indexOf(':');\n\n  if (firstSeparatorIndex === -1) {\n    throw new Error('Failed to parse stream string. No separator found.');\n  }\n\n  const prefix = line.slice(0, firstSeparatorIndex);\n\n  if (!validCodes.includes(prefix as keyof typeof dataStreamPartsByCode)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n\n  const code = prefix as keyof typeof dataStreamPartsByCode;\n\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue: JSONValue = JSON.parse(textValue);\n\n  return dataStreamPartsByCode[code].parse(jsonValue);\n};\n\n/**\nPrepends a string with a prefix from the `StreamChunkPrefixes`, JSON-ifies it,\nand appends a new line.\n\nIt ensures type-safety for the part type and value.\n */\nexport function formatDataStreamPart<T extends keyof DataStreamPartValueType>(\n  type: T,\n  value: DataStreamPartValueType[T],\n): DataStreamString {\n  const streamPart = dataStreamParts.find(part => part.name === type);\n\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n\n  return `${streamPart.code}:${JSON.stringify(value)}\\n`;\n}\n", "import { DataStreamPartType, parseDataStreamPart } from './data-stream-parts';\n\nconst NEWLINE = '\\n'.charCodeAt(0);\n\n// concatenates all the chunks into a single Uint8Array\nfunction concatChunks(chunks: Uint8Array[], totalLength: number) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n\n  return concatenatedChunks;\n}\n\nexport async function processDataStream({\n  stream,\n  onTextPart,\n  onReasoningPart,\n  onReasoningSignaturePart,\n  onRedactedReasoningPart,\n  onSourcePart,\n  onFilePart,\n  onDataPart,\n  onErrorPart,\n  onToolCallStreamingStartPart,\n  onToolCallDeltaPart,\n  onToolCallPart,\n  onToolResultPart,\n  onMessageAnnotationsPart,\n  onFinishMessagePart,\n  onFinishStepPart,\n  onStartStepPart,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  onTextPart?: (\n    streamPart: (DataStreamPartType & { type: 'text' })['value'],\n  ) => Promise<void> | void;\n  onReasoningPart?: (\n    streamPart: (DataStreamPartType & { type: 'reasoning' })['value'],\n  ) => Promise<void> | void;\n  onReasoningSignaturePart?: (\n    streamPart: (DataStreamPartType & { type: 'reasoning_signature' })['value'],\n  ) => Promise<void> | void;\n  onRedactedReasoningPart?: (\n    streamPart: (DataStreamPartType & { type: 'redacted_reasoning' })['value'],\n  ) => Promise<void> | void;\n  onFilePart?: (\n    streamPart: (DataStreamPartType & { type: 'file' })['value'],\n  ) => Promise<void> | void;\n  onSourcePart?: (\n    streamPart: (DataStreamPartType & { type: 'source' })['value'],\n  ) => Promise<void> | void;\n  onDataPart?: (\n    streamPart: (DataStreamPartType & { type: 'data' })['value'],\n  ) => Promise<void> | void;\n  onErrorPart?: (\n    streamPart: (DataStreamPartType & { type: 'error' })['value'],\n  ) => Promise<void> | void;\n  onToolCallStreamingStartPart?: (\n    streamPart: (DataStreamPartType & {\n      type: 'tool_call_streaming_start';\n    })['value'],\n  ) => Promise<void> | void;\n  onToolCallDeltaPart?: (\n    streamPart: (DataStreamPartType & { type: 'tool_call_delta' })['value'],\n  ) => Promise<void> | void;\n  onToolCallPart?: (\n    streamPart: (DataStreamPartType & { type: 'tool_call' })['value'],\n  ) => Promise<void> | void;\n  onToolResultPart?: (\n    streamPart: (DataStreamPartType & { type: 'tool_result' })['value'],\n  ) => Promise<void> | void;\n  onMessageAnnotationsPart?: (\n    streamPart: (DataStreamPartType & {\n      type: 'message_annotations';\n    })['value'],\n  ) => Promise<void> | void;\n  onFinishMessagePart?: (\n    streamPart: (DataStreamPartType & { type: 'finish_message' })['value'],\n  ) => Promise<void> | void;\n  onFinishStepPart?: (\n    streamPart: (DataStreamPartType & { type: 'finish_step' })['value'],\n  ) => Promise<void> | void;\n  onStartStepPart?: (\n    streamPart: (DataStreamPartType & { type: 'start_step' })['value'],\n  ) => Promise<void> | void;\n}): Promise<void> {\n  // implementation note: this slightly more complex algorithm is required\n  // to pass the tests in the edge environment.\n\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks: Uint8Array[] = [];\n  let totalLength = 0;\n\n  while (true) {\n    const { value } = await reader.read();\n\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        // if the last character is not a newline, we have not read the whole JSON value\n        continue;\n      }\n    }\n\n    if (chunks.length === 0) {\n      break; // we have reached the end of the stream\n    }\n\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n\n    const streamParts = decoder\n      .decode(concatenatedChunks, { stream: true })\n      .split('\\n')\n      .filter(line => line !== '') // splitting leaves an empty string at the end\n      .map(parseDataStreamPart);\n\n    for (const { type, value } of streamParts) {\n      switch (type) {\n        case 'text':\n          await onTextPart?.(value);\n          break;\n        case 'reasoning':\n          await onReasoningPart?.(value);\n          break;\n        case 'reasoning_signature':\n          await onReasoningSignaturePart?.(value);\n          break;\n        case 'redacted_reasoning':\n          await onRedactedReasoningPart?.(value);\n          break;\n        case 'file':\n          await onFilePart?.(value);\n          break;\n        case 'source':\n          await onSourcePart?.(value);\n          break;\n        case 'data':\n          await onDataPart?.(value);\n          break;\n        case 'error':\n          await onErrorPart?.(value);\n          break;\n        case 'message_annotations':\n          await onMessageAnnotationsPart?.(value);\n          break;\n        case 'tool_call_streaming_start':\n          await onToolCallStreamingStartPart?.(value);\n          break;\n        case 'tool_call_delta':\n          await onToolCallDeltaPart?.(value);\n          break;\n        case 'tool_call':\n          await onToolCallPart?.(value);\n          break;\n        case 'tool_result':\n          await onToolResultPart?.(value);\n          break;\n        case 'finish_message':\n          await onFinishMessagePart?.(value);\n          break;\n        case 'finish_step':\n          await onFinishStepPart?.(value);\n          break;\n        case 'start_step':\n          await onStartStepPart?.(value);\n          break;\n        default: {\n          const exhaustiveCheck: never = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n", "import { JSONValue } from '@ai-sdk/provider';\nimport { generateId as generateIdFunction } from '@ai-sdk/provider-utils';\nimport { processTextStream } from './process-text-stream';\nimport { TextUIPart, UIMessage, UseChatOptions } from './types';\n\nexport async function processChatTextResponse({\n  stream,\n  update,\n  onFinish,\n  getCurrentDate = () => new Date(),\n  generateId = generateIdFunction,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  update: (options: {\n    message: UIMessage;\n    data: JSONValue[] | undefined;\n    replaceLastMessage: boolean;\n  }) => void;\n  onFinish: UseChatOptions['onFinish'];\n  getCurrentDate?: () => Date;\n  generateId?: () => string;\n}) {\n  const textPart: TextUIPart = { type: 'text', text: '' };\n\n  const resultMessage: UIMessage = {\n    id: generateId(),\n    createdAt: getCurrentDate(),\n    role: 'assistant' as const,\n    content: '',\n    parts: [textPart],\n  };\n\n  await processTextStream({\n    stream,\n    onTextPart: chunk => {\n      resultMessage.content += chunk;\n      textPart.text += chunk;\n\n      // note: creating a new message object is required for Solid.js streaming\n      update({\n        message: { ...resultMessage },\n        data: [],\n        replaceLastMessage: false,\n      });\n    },\n  });\n\n  // in text mode, we don't have usage information or finish reason:\n  onFinish?.(resultMessage, {\n    usage: { completionTokens: NaN, promptTokens: NaN, totalTokens: NaN },\n    finishReason: 'unknown',\n  });\n}\n", "export async function processTextStream({\n  stream,\n  onTextPart,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  onTextPart: (chunk: string) => Promise<void> | void;\n}): Promise<void> {\n  const reader = stream.pipeThrough(new TextDecoderStream()).getReader();\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) {\n      break;\n    }\n    await onTextPart(value);\n  }\n}\n", "import { processChatResponse } from './process-chat-response';\nimport { processChatTextResponse } from './process-chat-text-response';\nimport { IdGenerator, JSONValue, UIMessage, UseChatOptions } from './types';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => fetch;\n\nexport async function callChatApi({\n  api,\n  body,\n  streamProtocol = 'data',\n  credentials,\n  headers,\n  abortController,\n  restoreMessagesOnFailure,\n  onResponse,\n  onUpdate,\n  onFinish,\n  onToolCall,\n  generateId,\n  fetch = getOriginalFetch(),\n  lastMessage,\n  requestType = 'generate',\n}: {\n  api: string;\n  body: Record<string, any>;\n  streamProtocol: 'data' | 'text' | undefined;\n  credentials: RequestCredentials | undefined;\n  headers: HeadersInit | undefined;\n  abortController: (() => AbortController | null) | undefined;\n  restoreMessagesOnFailure: () => void;\n  onResponse: ((response: Response) => void | Promise<void>) | undefined;\n  onUpdate: (options: {\n    message: UIMessage;\n    data: JSONValue[] | undefined;\n    replaceLastMessage: boolean;\n  }) => void;\n  onFinish: UseChatOptions['onFinish'];\n  onToolCall: UseChatOptions['onToolCall'];\n  generateId: IdGenerator;\n  fetch: ReturnType<typeof getOriginalFetch> | undefined;\n  lastMessage: UIMessage | undefined;\n  requestType?: 'generate' | 'resume';\n}) {\n  const request =\n    requestType === 'resume'\n      ? fetch(`${api}?chatId=${body.id}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n            ...headers,\n          },\n          signal: abortController?.()?.signal,\n          credentials,\n        })\n      : fetch(api, {\n          method: 'POST',\n          body: JSON.stringify(body),\n          headers: {\n            'Content-Type': 'application/json',\n            ...headers,\n          },\n          signal: abortController?.()?.signal,\n          credentials,\n        });\n\n  const response = await request.catch(err => {\n    restoreMessagesOnFailure();\n    throw err;\n  });\n\n  if (onResponse) {\n    try {\n      await onResponse(response);\n    } catch (err) {\n      throw err;\n    }\n  }\n\n  if (!response.ok) {\n    restoreMessagesOnFailure();\n    throw new Error(\n      (await response.text()) ?? 'Failed to fetch the chat response.',\n    );\n  }\n\n  if (!response.body) {\n    throw new Error('The response body is empty.');\n  }\n\n  switch (streamProtocol) {\n    case 'text': {\n      await processChatTextResponse({\n        stream: response.body,\n        update: onUpdate,\n        onFinish,\n        generateId,\n      });\n      return;\n    }\n\n    case 'data': {\n      await processChatResponse({\n        stream: response.body,\n        update: onUpdate,\n        lastMessage,\n        onToolCall,\n        onFinish({ message, finishReason, usage }) {\n          if (onFinish && message != null) {\n            onFinish(message, { usage, finishReason });\n          }\n        },\n        generateId,\n      });\n      return;\n    }\n\n    default: {\n      const exhaustiveCheck: never = streamProtocol;\n      throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n    }\n  }\n}\n", "import { processTextStream } from './process-text-stream';\nimport { processDataStream } from './process-data-stream';\nimport { JSONValue } from './types';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => fetch;\n\nexport async function callCompletionApi({\n  api,\n  prompt,\n  credentials,\n  headers,\n  body,\n  streamProtocol = 'data',\n  setCompletion,\n  setLoading,\n  setError,\n  setAbortController,\n  onResponse,\n  onFinish,\n  onError,\n  onData,\n  fetch = getOriginalFetch(),\n}: {\n  api: string;\n  prompt: string;\n  credentials: RequestCredentials | undefined;\n  headers: HeadersInit | undefined;\n  body: Record<string, any>;\n  streamProtocol: 'data' | 'text' | undefined;\n  setCompletion: (completion: string) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: Error | undefined) => void;\n  setAbortController: (abortController: AbortController | null) => void;\n  onResponse: ((response: Response) => void | Promise<void>) | undefined;\n  onFinish: ((prompt: string, completion: string) => void) | undefined;\n  onError: ((error: Error) => void) | undefined;\n  onData: ((data: JSONValue[]) => void) | undefined;\n  fetch: ReturnType<typeof getOriginalFetch> | undefined;\n}) {\n  try {\n    setLoading(true);\n    setError(undefined);\n\n    const abortController = new AbortController();\n    setAbortController(abortController);\n\n    // Empty the completion immediately.\n    setCompletion('');\n\n    const response = await fetch(api, {\n      method: 'POST',\n      body: JSON.stringify({\n        prompt,\n        ...body,\n      }),\n      credentials,\n      headers: {\n        'Content-Type': 'application/json',\n        ...headers,\n      },\n      signal: abortController.signal,\n    }).catch(err => {\n      throw err;\n    });\n\n    if (onResponse) {\n      try {\n        await onResponse(response);\n      } catch (err) {\n        throw err;\n      }\n    }\n\n    if (!response.ok) {\n      throw new Error(\n        (await response.text()) ?? 'Failed to fetch the chat response.',\n      );\n    }\n\n    if (!response.body) {\n      throw new Error('The response body is empty.');\n    }\n\n    let result = '';\n\n    switch (streamProtocol) {\n      case 'text': {\n        await processTextStream({\n          stream: response.body,\n          onTextPart: chunk => {\n            result += chunk;\n            setCompletion(result);\n          },\n        });\n        break;\n      }\n      case 'data': {\n        await processDataStream({\n          stream: response.body,\n          onTextPart(value) {\n            result += value;\n            setCompletion(result);\n          },\n          onDataPart(value) {\n            onData?.(value);\n          },\n          onErrorPart(value) {\n            throw new Error(value);\n          },\n        });\n        break;\n      }\n      default: {\n        const exhaustiveCheck: never = streamProtocol;\n        throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n      }\n    }\n\n    if (onFinish) {\n      onFinish(prompt, result);\n    }\n\n    setAbortController(null);\n    return result;\n  } catch (err) {\n    // Ignore abort errors as they are expected.\n    if ((err as any).name === 'AbortError') {\n      setAbortController(null);\n      return null;\n    }\n\n    if (err instanceof Error) {\n      if (onError) {\n        onError(err);\n      }\n    }\n\n    setError(err as Error);\n  } finally {\n    setLoading(false);\n  }\n}\n", "/**\n * Converts a data URL of type text/* to a text string.\n */\nexport function getTextFromDataUrl(dataUrl: string): string {\n  const [header, base64Content] = dataUrl.split(',');\n  const mimeType = header.split(';')[0].split(':')[1];\n\n  if (mimeType == null || base64Content == null) {\n    throw new Error('Invalid data URL format');\n  }\n\n  try {\n    return window.atob(base64Content);\n  } catch (error) {\n    throw new Error(`Error decoding data URL`);\n  }\n}\n", "import { ToolInvocation } from './types';\n\nexport function extractMaxToolInvocationStep(\n  toolInvocations: ToolInvocation[] | undefined,\n): number | undefined {\n  return toolInvocations?.reduce((max, toolInvocation) => {\n    return Math.max(max, toolInvocation.step ?? 0);\n  }, 0);\n}\n", "import {\n  CreateMessage,\n  FileUIPart,\n  Message,\n  ReasoningUIPart,\n  SourceUIPart,\n  StepStartUIPart,\n  TextUIPart,\n  ToolInvocationUIPart,\n  UIMessage,\n} from './types';\n\nexport function getMessageParts(\n  message: Message | CreateMessage | UIMessage,\n): (\n  | TextUIPart\n  | ReasoningUIPart\n  | ToolInvocationUIPart\n  | SourceUIPart\n  | FileUIPart\n  | StepStartUIPart\n)[] {\n  return (\n    message.parts ?? [\n      ...(message.toolInvocations\n        ? message.toolInvocations.map(toolInvocation => ({\n            type: 'tool-invocation' as const,\n            toolInvocation,\n          }))\n        : []),\n      ...(message.reasoning\n        ? [\n            {\n              type: 'reasoning' as const,\n              reasoning: message.reasoning,\n              details: [{ type: 'text' as const, text: message.reasoning }],\n            },\n          ]\n        : []),\n      ...(message.content\n        ? [{ type: 'text' as const, text: message.content }]\n        : []),\n    ]\n  );\n}\n", "import { getMessageParts } from './get-message-parts';\nimport { Message, UIMessage } from './types';\n\nexport function fillMessageParts(messages: Message[]): UIMessage[] {\n  return messages.map(message => ({\n    ...message,\n    parts: getMessageParts(message),\n  }));\n}\n", "/**\n * Performs a deep-equal comparison of two parsed JSON objects.\n *\n * @param {any} obj1 - The first object to compare.\n * @param {any} obj2 - The second object to compare.\n * @returns {boolean} - Returns true if the two objects are deeply equal, false otherwise.\n */\nexport function isDeepEqualData(obj1: any, obj2: any): boolean {\n  // Check for strict equality first\n  if (obj1 === obj2) return true;\n\n  // Check if either is null or undefined\n  if (obj1 == null || obj2 == null) return false;\n\n  // Check if both are objects\n  if (typeof obj1 !== 'object' && typeof obj2 !== 'object')\n    return obj1 === obj2;\n\n  // If they are not strictly equal, they both need to be Objects\n  if (obj1.constructor !== obj2.constructor) return false;\n\n  // Special handling for Date objects\n  if (obj1 instanceof Date && obj2 instanceof Date) {\n    return obj1.getTime() === obj2.getTime();\n  }\n\n  // Handle arrays: compare length and then perform a recursive deep comparison on each item\n  if (Array.isArray(obj1)) {\n    if (obj1.length !== obj2.length) return false;\n    for (let i = 0; i < obj1.length; i++) {\n      if (!isDeepEqualData(obj1[i], obj2[i])) return false;\n    }\n    return true; // All array elements matched\n  }\n\n  // Compare the set of keys in each object\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n  if (keys1.length !== keys2.length) return false;\n\n  // Check each key-value pair recursively\n  for (const key of keys1) {\n    if (!keys2.includes(key)) return false;\n    if (!isDeepEqualData(obj1[key], obj2[key])) return false;\n  }\n\n  return true; // All keys and values matched\n}\n", "import { Attachment } from './types';\n\nexport async function prepareAttachmentsForRequest(\n  attachmentsFromOptions: FileList | Array<Attachment> | undefined,\n) {\n  if (!attachmentsFromOptions) {\n    return [];\n  }\n\n  // https://github.com/vercel/ai/pull/6045\n  // React-native doesn't have a FileList\n  // global variable, so we need to check for it\n  if (\n    globalThis.FileList &&\n    attachmentsFromOptions instanceof globalThis.FileList\n  ) {\n    return Promise.all(\n      Array.from(attachmentsFromOptions).map(async attachment => {\n        const { name, type } = attachment;\n\n        const dataUrl = await new Promise<string>((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = readerEvent => {\n            resolve(readerEvent.target?.result as string);\n          };\n          reader.onerror = error => reject(error);\n          reader.readAsDataURL(attachment);\n        });\n\n        return {\n          name,\n          contentType: type,\n          url: dataUrl,\n        };\n      }),\n    );\n  }\n\n  if (Array.isArray(attachmentsFromOptions)) {\n    return attachmentsFromOptions;\n  }\n\n  throw new Error('Invalid attachments type');\n}\n", "import {\n  AssistantStreamPartType,\n  parseAssistantStreamPart,\n} from './assistant-stream-parts';\n\nconst NEWLINE = '\\n'.charCodeAt(0);\n\n// concatenates all the chunks into a single Uint8Array\nfunction concatChunks(chunks: Uint8Array[], totalLength: number) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n\n  return concatenatedChunks;\n}\n\nexport async function processAssistantStream({\n  stream,\n  onTextPart,\n  onErrorPart,\n  onAssistantMessagePart,\n  onAssistantControlDataPart,\n  onDataMessagePart,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  onTextPart?: (\n    streamPart: (AssistantStreamPartType & { type: 'text' })['value'],\n  ) => Promise<void> | void;\n  onErrorPart?: (\n    streamPart: (AssistantStreamPartType & { type: 'error' })['value'],\n  ) => Promise<void> | void;\n  onAssistantMessagePart?: (\n    streamPart: (AssistantStreamPartType & {\n      type: 'assistant_message';\n    })['value'],\n  ) => Promise<void> | void;\n  onAssistantControlDataPart?: (\n    streamPart: (AssistantStreamPartType & {\n      type: 'assistant_control_data';\n    })['value'],\n  ) => Promise<void> | void;\n  onDataMessagePart?: (\n    streamPart: (AssistantStreamPartType & { type: 'data_message' })['value'],\n  ) => Promise<void> | void;\n}): Promise<void> {\n  // implementation note: this slightly more complex algorithm is required\n  // to pass the tests in the edge environment.\n\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks: Uint8Array[] = [];\n  let totalLength = 0;\n\n  while (true) {\n    const { value } = await reader.read();\n\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        // if the last character is not a newline, we have not read the whole JSON value\n        continue;\n      }\n    }\n\n    if (chunks.length === 0) {\n      break; // we have reached the end of the stream\n    }\n\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n\n    const streamParts = decoder\n      .decode(concatenatedChunks, { stream: true })\n      .split('\\n')\n      .filter(line => line !== '')\n      .map(parseAssistantStreamPart);\n\n    for (const { type, value } of streamParts) {\n      switch (type) {\n        case 'text':\n          await onTextPart?.(value);\n          break;\n        case 'error':\n          await onErrorPart?.(value);\n          break;\n        case 'assistant_message':\n          await onAssistantMessagePart?.(value);\n          break;\n        case 'assistant_control_data':\n          await onAssistantControlDataPart?.(value);\n          break;\n        case 'data_message':\n          await onDataMessagePart?.(value);\n          break;\n        default: {\n          const exhaustiveCheck: never = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n", "import { Validator, validatorSymbol } from '@ai-sdk/provider-utils';\nimport { JSONSchema7 } from 'json-schema';\nimport { z } from 'zod';\nimport { zodSchema } from './zod-schema';\n\n/**\n * Used to mark schemas so we can support both Zod and custom schemas.\n */\nconst schemaSymbol = Symbol.for('vercel.ai.schema');\n\nexport type Schema<OBJECT = unknown> = Validator<OBJECT> & {\n  /**\n   * Used to mark schemas so we can support both Zod and custom schemas.\n   */\n  [schemaSymbol]: true;\n\n  /**\n   * Schema type for inference.\n   */\n  _type: OBJECT;\n\n  /**\n   * The JSON Schema for the schema. It is passed to the providers.\n   */\n  readonly jsonSchema: JSONSchema7;\n};\n\n/**\n * Create a schema using a JSON Schema.\n *\n * @param jsonSchema The JSON Schema for the schema.\n * @param options.validate Optional. A validation function for the schema.\n */\nexport function jsonSchema<OBJECT = unknown>(\n  jsonSchema: JSONSchema7,\n  {\n    validate,\n  }: {\n    validate?: (\n      value: unknown,\n    ) => { success: true; value: OBJECT } | { success: false; error: Error };\n  } = {},\n): Schema<OBJECT> {\n  return {\n    [schemaSymbol]: true,\n    _type: undefined as OBJECT, // should never be used directly\n    [validatorSymbol]: true,\n    jsonSchema,\n    validate,\n  };\n}\n\nfunction isSchema(value: unknown): value is Schema {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    schemaSymbol in value &&\n    value[schemaSymbol] === true &&\n    'jsonSchema' in value &&\n    'validate' in value\n  );\n}\n\nexport function asSchema<OBJECT>(\n  schema: z.Schema<OBJECT, z.ZodTypeDef, any> | Schema<OBJECT>,\n): Schema<OBJECT> {\n  return isSchema(schema) ? schema : zodSchema(schema);\n}\n", "import { JSONSchema7 } from 'json-schema';\nimport { z } from 'zod';\nimport zodToJsonSchema from 'zod-to-json-schema';\nimport { jsonSchema, Schema } from './schema';\n\nexport function zodSchema<OBJECT>(\n  zodSchema: z.Schema<OBJECT, z.ZodTypeDef, any>,\n  options?: {\n    /**\n     * Enables support for references in the schema.\n     * This is required for recursive schemas, e.g. with `z.lazy`.\n     * However, not all language models and providers support such references.\n     * Defaults to `false`.\n     */\n    useReferences?: boolean;\n  },\n): Schema<OBJECT> {\n  // default to no references (to support openapi conversion for google)\n  const useReferences = options?.useReferences ?? false;\n\n  return jsonSchema(\n    zodToJsonSchema(zodSchema, {\n      $refStrategy: useReferences ? 'root' : 'none',\n      target: 'jsonSchema7', // note: openai mode breaks various gemini conversions\n    }) as JSONSchema7,\n    {\n      validate: value => {\n        const result = zodSchema.safeParse(value);\n        return result.success\n          ? { success: true, value: result.data }\n          : { success: false, error: result.error };\n      },\n    },\n  );\n}\n", "import { extractMaxToolInvocationStep } from './extract-max-tool-invocation-step';\nimport { UIMessage } from './types';\n\nexport function shouldResubmitMessages({\n  originalMaxToolInvocationStep,\n  originalMessageCount,\n  maxSteps,\n  messages,\n}: {\n  originalMaxToolInvocationStep: number | undefined;\n  originalMessageCount: number;\n  maxSteps: number;\n  messages: UIMessage[];\n}) {\n  const lastMessage = messages[messages.length - 1];\n  return (\n    // check if the feature is enabled:\n    maxSteps > 1 &&\n    // ensure there is a last message:\n    lastMessage != null &&\n    // ensure we actually have new steps (to prevent infinite loops in case of errors):\n    (messages.length > originalMessageCount ||\n      extractMaxToolInvocationStep(lastMessage.toolInvocations) !==\n        originalMaxToolInvocationStep) &&\n    // check that next step is possible:\n    isAssistantMessageWithCompletedToolCalls(lastMessage) &&\n    // limit the number of automatic steps:\n    (extractMaxToolInvocationStep(lastMessage.toolInvocations) ?? 0) < maxSteps\n  );\n}\n\n/**\nCheck if the message is an assistant message with completed tool calls.\nThe last step of the message must have at least one tool invocation and\nall tool invocations must have a result.\n */\nexport function isAssistantMessageWithCompletedToolCalls(\n  message: UIMessage,\n): message is UIMessage & {\n  role: 'assistant';\n} {\n  if (message.role !== 'assistant') {\n    return false;\n  }\n\n  const lastStepStartIndex = message.parts.reduce((lastIndex, part, index) => {\n    return part.type === 'step-start' ? index : lastIndex;\n  }, -1);\n\n  const lastStepToolInvocations = message.parts\n    .slice(lastStepStartIndex + 1)\n    .filter(part => part.type === 'tool-invocation');\n\n  return (\n    lastStepToolInvocations.length > 0 &&\n    lastStepToolInvocations.every(part => 'result' in part.toolInvocation)\n  );\n}\n", "import { ToolInvocationUIPart, UIMessage } from './types';\n\n/**\n * Updates the result of a specific tool invocation in the last message of the given messages array.\n *\n * @param {object} params - The parameters object.\n * @param {UIMessage[]} params.messages - An array of messages, from which the last one is updated.\n * @param {string} params.toolCallId - The unique identifier for the tool invocation to update.\n * @param {unknown} params.toolResult - The result object to attach to the tool invocation.\n * @returns {void} This function does not return anything.\n */\nexport function updateToolCallResult({\n  messages,\n  toolCallId,\n  toolResult: result,\n}: {\n  messages: UIMessage[];\n  toolCallId: string;\n  toolResult: unknown;\n}) {\n  const lastMessage = messages[messages.length - 1];\n\n  const invocationPart = lastMessage.parts.find(\n    (part): part is ToolInvocationUIPart =>\n      part.type === 'tool-invocation' &&\n      part.toolInvocation.toolCallId === toolCallId,\n  );\n\n  if (invocationPart == null) {\n    return;\n  }\n\n  const toolResult = {\n    ...invocationPart.toolInvocation,\n    state: 'result' as const,\n    result,\n  };\n\n  invocationPart.toolInvocation = toolResult;\n\n  lastMessage.toolInvocations = lastMessage.toolInvocations?.map(\n    toolInvocation =>\n      toolInvocation.toolCallId === toolCallId ? toolResult : toolInvocation,\n  );\n}\n"], "names": ["textStreamPart", "errorStreamPart", "validCodes", "value", "generateId", "_a", "part", "invocation", "generateIdFunction", "generateId", "generateIdFunction", "generateId", "fetch", "getOriginalFetch", "fetch", "NEWLINE", "concatChunks", "value", "zodSchema", "jsonSchema"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AECA,SAAS,cAAc,0BAA0B;;AkBCjD,OAAO,qBAAqB;;;;AnBa5B,IAAM,iBAA2D;IAC/D,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,IAAI,MAAM,qCAAqC;QACvD;QACA,OAAO;YAAE,MAAM;YAAQ;QAAM;IAC/B;AACF;AAEA,IAAM,kBAA6D;IACjE,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,IAAI,MAAM,sCAAsC;QACxD;QACA,OAAO;YAAE,MAAM;YAAS;QAAM;IAChC;AACF;AAEA,IAAM,6BAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,QAAQ,KAAA,KACV,CAAA,CAAE,UAAU,KAAA,KACZ,CAAA,CAAE,aAAa,KAAA,KACf,OAAO,MAAM,EAAA,KAAO,YACpB,OAAO,MAAM,IAAA,KAAS,YACtB,MAAM,IAAA,KAAS,eACf,CAAC,MAAM,OAAA,CAAQ,MAAM,OAAO,KAC5B,CAAC,MAAM,OAAA,CAAQ,KAAA,CACb,CAAA,OACE,QAAQ,QACR,OAAO,SAAS,YAChB,UAAU,QACV,KAAK,IAAA,KAAS,UACd,UAAU,QACV,KAAK,IAAA,IAAQ,QACb,OAAO,KAAK,IAAA,KAAS,YACrB,WAAW,KAAK,IAAA,IAChB,OAAO,KAAK,IAAA,CAAK,KAAA,KAAU,WAE/B;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN;QACF;IACF;AACF;AAEA,IAAM,iCAOF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,cAAc,KAAA,KAChB,CAAA,CAAE,eAAe,KAAA,KACjB,OAAO,MAAM,QAAA,KAAa,YAC1B,OAAO,MAAM,SAAA,KAAc,UAC3B;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN,OAAO;gBACL,UAAU,MAAM,QAAA;gBAChB,WAAW,MAAM,SAAA;YACnB;QACF;IACF;AACF;AAEA,IAAM,wBAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,UAAU,KAAA,KACZ,CAAA,CAAE,UAAU,KAAA,KACZ,OAAO,MAAM,IAAA,KAAS,YACtB,MAAM,IAAA,KAAS,QACf;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN;QACF;IACF;AACF;AAEA,IAAM,uBAAuB;IAC3B;IACA;IACA;IACA;IACA;CACF;AAoBO,IAAM,6BAA6B;IACxC,CAAC,eAAe,IAAI,CAAA,EAAG;IACvB,CAAC,gBAAgB,IAAI,CAAA,EAAG;IACxB,CAAC,2BAA2B,IAAI,CAAA,EAAG;IACnC,CAAC,+BAA+B,IAAI,CAAA,EAAG;IACvC,CAAC,sBAAsB,IAAI,CAAA,EAAG;AAChC;AAEO,IAAM,uBAAuB;IAClC,CAAC,eAAe,IAAI,CAAA,EAAG,eAAe,IAAA;IACtC,CAAC,gBAAgB,IAAI,CAAA,EAAG,gBAAgB,IAAA;IACxC,CAAC,2BAA2B,IAAI,CAAA,EAAG,2BAA2B,IAAA;IAC9D,CAAC,+BAA+B,IAAI,CAAA,EAAG,+BAA+B,IAAA;IACtE,CAAC,sBAAsB,IAAI,CAAA,EAAG,sBAAsB,IAAA;AACtD;AAEO,IAAM,aAAa,qBAAqB,GAAA,CAAI,CAAA,OAAQ,KAAK,IAAI;AAE7D,IAAM,2BAA2B,CACtC,SAC4B;IAC5B,MAAM,sBAAsB,KAAK,OAAA,CAAQ,GAAG;IAE5C,IAAI,wBAAwB,CAAA,GAAI;QAC9B,MAAM,IAAI,MAAM,oDAAoD;IACtE;IAEA,MAAM,SAAS,KAAK,KAAA,CAAM,GAAG,mBAAmB;IAEhD,IAAI,CAAC,WAAW,QAAA,CAAS,MAAiD,GAAG;QAC3E,MAAM,IAAI,MAAM,CAAA,4CAAA,EAA+C,MAAM,CAAA,CAAA,CAAG;IAC1E;IAEA,MAAM,OAAO;IAEb,MAAM,YAAY,KAAK,KAAA,CAAM,sBAAsB,CAAC;IACpD,MAAM,YAAuB,KAAK,KAAA,CAAM,SAAS;IAEjD,OAAO,0BAAA,CAA2B,IAAI,CAAA,CAAE,KAAA,CAAM,SAAS;AACzD;AAEO,SAAS,0BAEd,IAAA,EAAS,KAAA,EAA+D;IACxE,MAAM,aAAa,qBAAqB,IAAA,CAAK,CAAA,OAAQ,KAAK,IAAA,KAAS,IAAI;IAEvE,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,MAAM,CAAA,0BAAA,EAA6B,IAAI,EAAE;IACrD;IAEA,OAAO,GAAG,WAAW,IAAI,CAAA,CAAA,EAAI,KAAK,SAAA,CAAU,KAAK,CAAC,CAAA;AAAA,CAAA;AACpD;;;AE7LO,SAAS,4BAA4B,EAC1C,YAAA,EACA,gBAAA,EACF,EAGuB;IACrB,OAAO;QACL;QACA;QACA,aAAa,eAAe;IAC9B;AACF;;;AEhBO,SAAS,QAAQ,KAAA,EAAuB;IAC7C,MAAM,QAAiB;QAAC,MAAM;KAAA;IAC9B,IAAI,iBAAiB,CAAA;IACrB,IAAI,eAA8B;IAElC,SAAS,kBAAkB,IAAA,EAAc,CAAA,EAAW,SAAA,EAAkB;QACpE;YACE,OAAQ,MAAM;gBACZ,KAAK;oBAAK;wBACR,iBAAiB;wBACjB,MAAM,GAAA,CAAI;wBACV,MAAM,IAAA,CAAK,SAAS;wBACpB,MAAM,IAAA,CAAK,eAAe;wBAC1B;oBACF;gBAEA,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAK;wBACR,iBAAiB;wBACjB,eAAe;wBACf,MAAM,GAAA,CAAI;wBACV,MAAM,IAAA,CAAK,SAAS;wBACpB,MAAM,IAAA,CAAK,gBAAgB;wBAC3B;oBACF;gBAEA,KAAK;oBAAK;wBACR,MAAM,GAAA,CAAI;wBACV,MAAM,IAAA,CAAK,SAAS;wBACpB,MAAM,IAAA,CAAK,eAAe;wBAC1B;oBACF;gBACA,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAK;wBACR,iBAAiB;wBACjB,MAAM,GAAA,CAAI;wBACV,MAAM,IAAA,CAAK,SAAS;wBACpB,MAAM,IAAA,CAAK,eAAe;wBAC1B;oBACF;gBAEA,KAAK;oBAAK;wBACR,iBAAiB;wBACjB,MAAM,GAAA,CAAI;wBACV,MAAM,IAAA,CAAK,SAAS;wBACpB,MAAM,IAAA,CAAK,qBAAqB;wBAChC;oBACF;gBAEA,KAAK;oBAAK;wBACR,iBAAiB;wBACjB,MAAM,GAAA,CAAI;wBACV,MAAM,IAAA,CAAK,SAAS;wBACpB,MAAM,IAAA,CAAK,oBAAoB;wBAC/B;oBACF;YACF;QACF;IACF;IAEA,SAAS,wBAAwB,IAAA,EAAc,CAAA,EAAW;QACxD,OAAQ,MAAM;YACZ,KAAK;gBAAK;oBACR,MAAM,GAAA,CAAI;oBACV,MAAM,IAAA,CAAK,2BAA2B;oBACtC;gBACF;YACA,KAAK;gBAAK;oBACR,iBAAiB;oBACjB,MAAM,GAAA,CAAI;oBACV;gBACF;QACF;IACF;IAEA,SAAS,uBAAuB,IAAA,EAAc,CAAA,EAAW;QACvD,OAAQ,MAAM;YACZ,KAAK;gBAAK;oBACR,MAAM,GAAA,CAAI;oBACV,MAAM,IAAA,CAAK,0BAA0B;oBACrC;gBACF;YACA,KAAK;gBAAK;oBACR,iBAAiB;oBACjB,MAAM,GAAA,CAAI;oBACV;gBACF;QACF;IACF;IAEA,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAK;QACrC,MAAM,OAAO,KAAA,CAAM,CAAC,CAAA;QACpB,MAAM,eAAe,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA;QAE3C,OAAQ,cAAc;YACpB,KAAK;gBACH,kBAAkB,MAAM,GAAG,QAAQ;gBACnC;YAEF,KAAK;gBAAuB;oBAC1B,OAAQ,MAAM;wBACZ,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCACV,MAAM,IAAA,CAAK,mBAAmB;gCAC9B;4BACF;wBACA,KAAK;4BAAK;gCACR,iBAAiB;gCACjB,MAAM,GAAA,CAAI;gCACV;4BACF;oBACF;oBACA;gBACF;YAEA,KAAK;gBAA6B;oBAChC,OAAQ,MAAM;wBACZ,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCACV,MAAM,IAAA,CAAK,mBAAmB;gCAC9B;4BACF;oBACF;oBACA;gBACF;YAEA,KAAK;gBAAqB;oBACxB,OAAQ,MAAM;wBACZ,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCACV,MAAM,IAAA,CAAK,yBAAyB;gCACpC;4BACF;oBACF;oBACA;gBACF;YAEA,KAAK;gBAA2B;oBAC9B,OAAQ,MAAM;wBACZ,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCACV,MAAM,IAAA,CAAK,4BAA4B;gCAEvC;4BACF;oBACF;oBACA;gBACF;YAEA,KAAK;gBAA8B;oBACjC,kBAAkB,MAAM,GAAG,2BAA2B;oBACtD;gBACF;YAEA,KAAK;gBAA6B;oBAChC,wBAAwB,MAAM,CAAC;oBAC/B;gBACF;YAEA,KAAK;gBAAiB;oBACpB,OAAQ,MAAM;wBACZ,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCACV,iBAAiB;gCACjB;4BACF;wBAEA,KAAK;4BAAM;gCACT,MAAM,IAAA,CAAK,sBAAsB;gCACjC;4BACF;wBAEA;4BAAS;gCACP,iBAAiB;4BACnB;oBACF;oBAEA;gBACF;YAEA,KAAK;gBAAsB;oBACzB,OAAQ,MAAM;wBACZ,KAAK;4BAAK;gCACR,iBAAiB;gCACjB,MAAM,GAAA,CAAI;gCACV;4BACF;wBAEA;4BAAS;gCACP,iBAAiB;gCACjB,kBAAkB,MAAM,GAAG,0BAA0B;gCACrD;4BACF;oBACF;oBACA;gBACF;YAEA,KAAK;gBAA4B;oBAC/B,OAAQ,MAAM;wBACZ,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCACV,MAAM,IAAA,CAAK,0BAA0B;gCACrC;4BACF;wBAEA,KAAK;4BAAK;gCACR,iBAAiB;gCACjB,MAAM,GAAA,CAAI;gCACV;4BACF;wBAEA;4BAAS;gCACP,iBAAiB;gCACjB;4BACF;oBACF;oBAEA;gBACF;YAEA,KAAK;gBAA4B;oBAC/B,kBAAkB,MAAM,GAAG,0BAA0B;oBACrD;gBACF;YAEA,KAAK;gBAAwB;oBAC3B,MAAM,GAAA,CAAI;oBACV,iBAAiB;oBAEjB;gBACF;YAEA,KAAK;gBAAiB;oBACpB,OAAQ,MAAM;wBACZ,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;4BAAK;gCACR,iBAAiB;gCACjB;4BACF;wBAEA,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;4BAAK;gCACR;4BACF;wBAEA,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCAEV,IAAI,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAM,4BAA4B;oCAC1D,uBAAuB,MAAM,CAAC;gCAChC;gCAEA,IAAI,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAM,6BAA6B;oCAC3D,wBAAwB,MAAM,CAAC;gCACjC;gCAEA;4BACF;wBAEA,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCAEV,IAAI,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAM,6BAA6B;oCAC3D,wBAAwB,MAAM,CAAC;gCACjC;gCAEA;4BACF;wBAEA,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCAEV,IAAI,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAM,4BAA4B;oCAC1D,uBAAuB,MAAM,CAAC;gCAChC;gCAEA;4BACF;wBAEA;4BAAS;gCACP,MAAM,GAAA,CAAI;gCACV;4BACF;oBACF;oBAEA;gBACF;YAEA,KAAK;gBAAkB;oBACrB,MAAM,iBAAiB,MAAM,SAAA,CAAU,cAAe,IAAI,CAAC;oBAE3D,IACE,CAAC,QAAQ,UAAA,CAAW,cAAc,KAClC,CAAC,OAAO,UAAA,CAAW,cAAc,KACjC,CAAC,OAAO,UAAA,CAAW,cAAc,GACjC;wBACA,MAAM,GAAA,CAAI;wBAEV,IAAI,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAM,6BAA6B;4BAC3D,wBAAwB,MAAM,CAAC;wBACjC,OAAA,IAAW,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAM,4BAA4B;4BACjE,uBAAuB,MAAM,CAAC;wBAChC;oBACF,OAAO;wBACL,iBAAiB;oBACnB;oBAEA;gBACF;QACF;IACF;IAEA,IAAI,SAAS,MAAM,KAAA,CAAM,GAAG,iBAAiB,CAAC;IAE9C,IAAA,IAAS,IAAI,MAAM,MAAA,GAAS,GAAG,KAAK,GAAG,IAAK;QAC1C,MAAM,QAAQ,KAAA,CAAM,CAAC,CAAA;QAErB,OAAQ,OAAO;YACb,KAAK;gBAAiB;oBACpB,UAAU;oBACV;gBACF;YAEA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAA6B;oBAChC,UAAU;oBACV;gBACF;YAEA,KAAK;YACL,KAAK;YACL,KAAK;gBAA4B;oBAC/B,UAAU;oBACV;gBACF;YAEA,KAAK;gBAAkB;oBACrB,MAAM,iBAAiB,MAAM,SAAA,CAAU,cAAe,MAAM,MAAM;oBAElE,IAAI,OAAO,UAAA,CAAW,cAAc,GAAG;wBACrC,UAAU,OAAO,KAAA,CAAM,eAAe,MAAM;oBAC9C,OAAA,IAAW,QAAQ,UAAA,CAAW,cAAc,GAAG;wBAC7C,UAAU,QAAQ,KAAA,CAAM,eAAe,MAAM;oBAC/C,OAAA,IAAW,OAAO,UAAA,CAAW,cAAc,GAAG;wBAC5C,UAAU,OAAO,KAAA,CAAM,eAAe,MAAM;oBAC9C;gBACF;QACF;IACF;IAEA,OAAO;AACT;;AD5YO,SAAS,iBAAiB,QAAA,EAO/B;IACA,IAAI,aAAa,KAAA,GAAW;QAC1B,OAAO;YAAE,OAAO,KAAA;YAAW,OAAO;QAAkB;IACtD;IAEA,IAAI,mRAAS,gBAAA,EAAc;QAAE,MAAM;IAAS,CAAC;IAE7C,IAAI,OAAO,OAAA,EAAS;QAClB,OAAO;YAAE,OAAO,OAAO,KAAA;YAAO,OAAO;QAAmB;IAC1D;IAEA,mRAAS,gBAAA,EAAc;QAAE,MAAM,QAAQ,QAAQ;IAAE,CAAC;IAElD,IAAI,OAAO,OAAA,EAAS;QAClB,OAAO;YAAE,OAAO,OAAO,KAAA;YAAO,OAAO;QAAiB;IACxD;IAEA,OAAO;QAAE,OAAO,KAAA;QAAW,OAAO;IAAe;AACnD;;AETA,IAAMA,kBAAsD;IAC1D,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,IAAI,MAAM,qCAAqC;QACvD;QACA,OAAO;YAAE,MAAM;YAAQ;QAAM;IAC/B;AACF;AAEA,IAAM,iBAAgE;IACpE,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,CAAC,MAAM,OAAA,CAAQ,KAAK,GAAG;YACzB,MAAM,IAAI,MAAM,qCAAqC;QACvD;QAEA,OAAO;YAAE,MAAM;YAAQ;QAAM;IAC/B;AACF;AAEA,IAAMC,mBAAwD;IAC5D,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,IAAI,MAAM,sCAAsC;QACxD;QACA,OAAO;YAAE,MAAM;YAAS;QAAM;IAChC;AACF;AAEA,IAAM,+BAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,CAAC,MAAM,OAAA,CAAQ,KAAK,GAAG;YACzB,MAAM,IAAI,MAAM,oDAAoD;QACtE;QAEA,OAAO;YAAE,MAAM;YAAuB;QAAM;IAC9C;AACF;AAEA,IAAM,qBAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,gBAAgB,KAAA,KAClB,OAAO,MAAM,UAAA,KAAe,YAC5B,CAAA,CAAE,cAAc,KAAA,KAChB,OAAO,MAAM,QAAA,KAAa,YAC1B,CAAA,CAAE,UAAU,KAAA,KACZ,OAAO,MAAM,IAAA,KAAS,UACtB;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN;QACF;IACF;AACF;AAEA,IAAM,uBAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,gBAAgB,KAAA,KAClB,OAAO,MAAM,UAAA,KAAe,YAC5B,CAAA,CAAE,YAAY,KAAA,GACd;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN;QAIF;IACF;AACF;AAEA,IAAM,mCAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,gBAAgB,KAAA,KAClB,OAAO,MAAM,UAAA,KAAe,YAC5B,CAAA,CAAE,cAAc,KAAA,KAChB,OAAO,MAAM,QAAA,KAAa,UAC1B;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN;QACF;IACF;AACF;AAEA,IAAM,0BAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,gBAAgB,KAAA,KAClB,OAAO,MAAM,UAAA,KAAe,YAC5B,CAAA,CAAE,mBAAmB,KAAA,KACrB,OAAO,MAAM,aAAA,KAAkB,UAC/B;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN;QAIF;IACF;AACF;AAEA,IAAM,0BAWF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,kBAAkB,KAAA,KACpB,OAAO,MAAM,YAAA,KAAiB,UAC9B;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,MAAM,SAMF;YACF,cAAc,MAAM,YAAA;QACtB;QAEA,IACE,WAAW,SACX,MAAM,KAAA,IAAS,QACf,OAAO,MAAM,KAAA,KAAU,YACvB,kBAAkB,MAAM,KAAA,IACxB,sBAAsB,MAAM,KAAA,EAC5B;YACA,OAAO,KAAA,GAAQ;gBACb,cACE,OAAO,MAAM,KAAA,CAAM,YAAA,KAAiB,WAChC,MAAM,KAAA,CAAM,YAAA,GACZ,OAAO,GAAA;gBACb,kBACE,OAAO,MAAM,KAAA,CAAM,gBAAA,KAAqB,WACpC,MAAM,KAAA,CAAM,gBAAA,GACZ,OAAO,GAAA;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;QACT;IACF;AACF;AAEA,IAAM,uBAWF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,kBAAkB,KAAA,KACpB,OAAO,MAAM,YAAA,KAAiB,UAC9B;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,MAAM,SAOF;YACF,cAAc,MAAM,YAAA;YACpB,aAAa;QACf;QAEA,IACE,WAAW,SACX,MAAM,KAAA,IAAS,QACf,OAAO,MAAM,KAAA,KAAU,YACvB,kBAAkB,MAAM,KAAA,IACxB,sBAAsB,MAAM,KAAA,EAC5B;YACA,OAAO,KAAA,GAAQ;gBACb,cACE,OAAO,MAAM,KAAA,CAAM,YAAA,KAAiB,WAChC,MAAM,KAAA,CAAM,YAAA,GACZ,OAAO,GAAA;gBACb,kBACE,OAAO,MAAM,KAAA,CAAM,gBAAA,KAAqB,WACpC,MAAM,KAAA,CAAM,gBAAA,GACZ,OAAO,GAAA;YACf;QACF;QAEA,IAAI,iBAAiB,SAAS,OAAO,MAAM,WAAA,KAAgB,WAAW;YACpE,OAAO,WAAA,GAAc,MAAM,WAAA;QAC7B;QAEA,OAAO;YACL,MAAM;YACN,OAAO;QACT;IACF;AACF;AAEA,IAAM,sBAMF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,eAAe,KAAA,KACjB,OAAO,MAAM,SAAA,KAAc,UAC3B;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN,OAAO;gBACL,WAAW,MAAM,SAAA;YACnB;QACF;IACF;AACF;AAEA,IAAM,sBAAgE;IACpE,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,IAAI,MAAM,0CAA0C;QAC5D;QACA,OAAO;YAAE,MAAM;YAAa;QAAM;IACpC;AACF;AAEA,IAAM,aAAmE;IACvE,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,SAAS,QAAQ,OAAO,UAAU,UAAU;YAC9C,MAAM,IAAI,MAAM,wCAAwC;QAC1D;QAEA,OAAO;YACL,MAAM;YACN;QACF;IACF;AACF;AAEA,IAAM,8BAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,UAAU,KAAA,KACZ,OAAO,MAAM,IAAA,KAAS,UACtB;YACA,MAAM,IAAI,MACR;QAEJ;QACA,OAAO;YAAE,MAAM;YAAsB,OAAO;gBAAE,MAAM,MAAM,IAAA;YAAK;QAAE;IACnE;AACF;AAEA,IAAM,+BAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,eAAe,KAAA,KACjB,OAAO,MAAM,SAAA,KAAc,UAC3B;YACA,MAAM,IAAI,MACR;QAEJ;QACA,OAAO;YACL,MAAM;YACN,OAAO;gBAAE,WAAW,MAAM,SAAA;YAAU;QACtC;IACF;AACF;AAEA,IAAM,iBAOF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,UAAU,KAAA,KACZ,OAAO,MAAM,IAAA,KAAS,YACtB,CAAA,CAAE,cAAc,KAAA,KAChB,OAAO,MAAM,QAAA,KAAa,UAC1B;YACA,MAAM,IAAI,MACR;QAEJ;QACA,OAAO;YAAE,MAAM;YAAQ;QAAmD;IAC5E;AACF;AAEA,IAAM,kBAAkB;IACtBD;IACA;IACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAEO,IAAM,wBAAwB,OAAO,WAAA,CAC1C,gBAAgB,GAAA,CAAI,CAAA,OAAQ;QAAC,KAAK,IAAA;QAAM,IAAI;KAAC;AAsCxC,IAAM,2BAA2B,OAAO,WAAA,CAC7C,gBAAgB,GAAA,CAAI,CAAA,OAAQ;QAAC,KAAK,IAAA;QAAM,KAAK,IAAI;KAAC;AAK7C,IAAMC,cAAa,gBAAgB,GAAA,CAAI,CAAA,OAAQ,KAAK,IAAI;AASxD,IAAM,sBAAsB,CAAC,SAAqC;IACvE,MAAM,sBAAsB,KAAK,OAAA,CAAQ,GAAG;IAE5C,IAAI,wBAAwB,CAAA,GAAI;QAC9B,MAAM,IAAI,MAAM,oDAAoD;IACtE;IAEA,MAAM,SAAS,KAAK,KAAA,CAAM,GAAG,mBAAmB;IAEhD,IAAI,CAACA,YAAW,QAAA,CAAS,MAA4C,GAAG;QACtE,MAAM,IAAI,MAAM,CAAA,4CAAA,EAA+C,MAAM,CAAA,CAAA,CAAG;IAC1E;IAEA,MAAM,OAAO;IAEb,MAAM,YAAY,KAAK,KAAA,CAAM,sBAAsB,CAAC;IACpD,MAAM,YAAuB,KAAK,KAAA,CAAM,SAAS;IAEjD,OAAO,qBAAA,CAAsB,IAAI,CAAA,CAAE,KAAA,CAAM,SAAS;AACpD;AAQO,SAAS,qBACd,IAAA,EACA,KAAA,EACkB;IAClB,MAAM,aAAa,gBAAgB,IAAA,CAAK,CAAA,OAAQ,KAAK,IAAA,KAAS,IAAI;IAElE,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,MAAM,CAAA,0BAAA,EAA6B,IAAI,EAAE;IACrD;IAEA,OAAO,GAAG,WAAW,IAAI,CAAA,CAAA,EAAI,KAAK,SAAA,CAAU,KAAK,CAAC,CAAA;AAAA,CAAA;AACpD;;AC9iBA,IAAM,UAAU,KAAK,UAAA,CAAW,CAAC;AAGjC,SAAS,aAAa,MAAA,EAAsB,WAAA,EAAqB;IAC/D,MAAM,qBAAqB,IAAI,WAAW,WAAW;IAErD,IAAI,SAAS;IACb,KAAA,MAAW,SAAS,OAAQ;QAC1B,mBAAmB,GAAA,CAAI,OAAO,MAAM;QACpC,UAAU,MAAM,MAAA;IAClB;IACA,OAAO,MAAA,GAAS;IAEhB,OAAO;AACT;AAEA,eAAsB,kBAAkB,EACtC,MAAA,EACA,UAAA,EACA,eAAA,EACA,wBAAA,EACA,uBAAA,EACA,YAAA,EACA,UAAA,EACA,UAAA,EACA,WAAA,EACA,4BAAA,EACA,mBAAA,EACA,cAAA,EACA,gBAAA,EACA,wBAAA,EACA,mBAAA,EACA,gBAAA,EACA,eAAA,EACF,EAsDkB;IAIhB,MAAM,SAAS,OAAO,SAAA,CAAU;IAChC,MAAM,UAAU,IAAI,YAAY;IAChC,MAAM,SAAuB,CAAC,CAAA;IAC9B,IAAI,cAAc;IAElB,MAAO,KAAM;QACX,MAAM,EAAE,KAAA,CAAM,CAAA,GAAI,MAAM,OAAO,IAAA,CAAK;QAEpC,IAAI,OAAO;YACT,OAAO,IAAA,CAAK,KAAK;YACjB,eAAe,MAAM,MAAA;YACrB,IAAI,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAM,SAAS;gBAEvC;YACF;QACF;QAEA,IAAI,OAAO,MAAA,KAAW,GAAG;YACvB;QACF;QAEA,MAAM,qBAAqB,aAAa,QAAQ,WAAW;QAC3D,cAAc;QAEd,MAAM,cAAc,QACjB,MAAA,CAAO,oBAAoB;YAAE,QAAQ;QAAK,CAAC,EAC3C,KAAA,CAAM,IAAI,EACV,MAAA,CAAO,CAAA,OAAQ,SAAS,EAAE,EAC1B,GAAA,CAAI,mBAAmB;QAE1B,KAAA,MAAW,EAAE,IAAA,EAAM,OAAAC,MAAAA,CAAM,CAAA,IAAK,YAAa;YACzC,OAAQ,MAAM;gBACZ,KAAK;oBACH,MAAA,CAAM,cAAA,OAAA,KAAA,IAAA,WAAaA,OAAAA;oBACnB;gBACF,KAAK;oBACH,MAAA,CAAM,mBAAA,OAAA,KAAA,IAAA,gBAAkBA,OAAAA;oBACxB;gBACF,KAAK;oBACH,MAAA,CAAM,4BAAA,OAAA,KAAA,IAAA,yBAA2BA,OAAAA;oBACjC;gBACF,KAAK;oBACH,MAAA,CAAM,2BAAA,OAAA,KAAA,IAAA,wBAA0BA,OAAAA;oBAChC;gBACF,KAAK;oBACH,MAAA,CAAM,cAAA,OAAA,KAAA,IAAA,WAAaA,OAAAA;oBACnB;gBACF,KAAK;oBACH,MAAA,CAAM,gBAAA,OAAA,KAAA,IAAA,aAAeA,OAAAA;oBACrB;gBACF,KAAK;oBACH,MAAA,CAAM,cAAA,OAAA,KAAA,IAAA,WAAaA,OAAAA;oBACnB;gBACF,KAAK;oBACH,MAAA,CAAM,eAAA,OAAA,KAAA,IAAA,YAAcA,OAAAA;oBACpB;gBACF,KAAK;oBACH,MAAA,CAAM,4BAAA,OAAA,KAAA,IAAA,yBAA2BA,OAAAA;oBACjC;gBACF,KAAK;oBACH,MAAA,CAAM,gCAAA,OAAA,KAAA,IAAA,6BAA+BA,OAAAA;oBACrC;gBACF,KAAK;oBACH,MAAA,CAAM,uBAAA,OAAA,KAAA,IAAA,oBAAsBA,OAAAA;oBAC5B;gBACF,KAAK;oBACH,MAAA,CAAM,kBAAA,OAAA,KAAA,IAAA,eAAiBA,OAAAA;oBACvB;gBACF,KAAK;oBACH,MAAA,CAAM,oBAAA,OAAA,KAAA,IAAA,iBAAmBA,OAAAA;oBACzB;gBACF,KAAK;oBACH,MAAA,CAAM,uBAAA,OAAA,KAAA,IAAA,oBAAsBA,OAAAA;oBAC5B;gBACF,KAAK;oBACH,MAAA,CAAM,oBAAA,OAAA,KAAA,IAAA,iBAAmBA,OAAAA;oBACzB;gBACF,KAAK;oBACH,MAAA,CAAM,mBAAA,OAAA,KAAA,IAAA,gBAAkBA,OAAAA;oBACxB;gBACF;oBAAS;wBACP,MAAM,kBAAyB;wBAC/B,MAAM,IAAI,MAAM,CAAA,0BAAA,EAA6B,eAAe,EAAE;oBAChE;YACF;QACF;IACF;AACF;;ALnKA,eAAsB,oBAAoB,EACxC,MAAA,EACA,MAAA,EACA,UAAA,EACA,QAAA,EACA,YAAAC,oRAAa,aAAA,EACb,iBAAiB,IAAM,aAAA,GAAA,IAAI,KAAK,CAAA,EAChC,WAAA,EACF,EAgBG;IA1CH,IAAA,IAAA;IA2CE,MAAM,qBAAA,CAAqB,eAAA,OAAA,KAAA,IAAA,YAAa,IAAA,MAAS;IACjD,IAAI,OAAO,qBACP,IAAA,8CAAA;IAAA,CAAA,CAEC,KAAA,CAAA,KAAA,YAAY,eAAA,KAAZ,OAAA,KAAA,IAAA,GAA6B,MAAA,CAAO,CAAC,KAAK,mBAAmB;QA/CpE,IAAAC;QAgDQ,OAAO,KAAK,GAAA,CAAI,KAAA,CAAKA,MAAA,eAAe,IAAA,KAAf,OAAAA,MAAuB,CAAC;IAC/C,GAAG,EAAA,KAFF,OAAA,KAEQ,CAAA,IACT;IAEJ,MAAM,UAAqB,qBACvB,gBAAgB,WAAW,IAC3B;QACE,IAAID,YAAW;QACf,WAAW,eAAe;QAC1B,MAAM;QACN,SAAS;QACT,OAAO,CAAC,CAAA;IACV;IAEJ,IAAI,kBAA0C,KAAA;IAC9C,IAAI,uBAAoD,KAAA;IACxD,IAAI,6BAEY,KAAA;IAEhB,SAAS,yBACP,UAAA,EACA,UAAA,EACA;QACA,MAAM,OAAO,QAAQ,KAAA,CAAM,IAAA,CACzB,CAAAE,QACEA,MAAK,IAAA,KAAS,qBACdA,MAAK,cAAA,CAAe,UAAA,KAAe;QAGvC,IAAI,QAAQ,MAAM;YAChB,KAAK,cAAA,GAAiB;QACxB,OAAO;YACL,QAAQ,KAAA,CAAM,IAAA,CAAK;gBACjB,MAAM;gBACN,gBAAgB;YAClB,CAAC;QACH;IACF;IAEA,MAAM,OAAoB,CAAC,CAAA;IAG3B,IAAI,qBAA8C,qBAC9C,eAAA,OAAA,KAAA,IAAA,YAAa,WAAA,GACb,KAAA;IAGJ,MAAM,mBAGF,CAAC;IAEL,IAAI,QAA4B;QAC9B,kBAAkB;QAClB,cAAc;QACd,aAAa;IACf;IACA,IAAI,eAA4C;IAEhD,SAAS,aAAa;QAEpB,MAAM,aAAa,CAAC;eAAG,IAAI;SAAA;QAI3B,IAAI,sBAAA,OAAA,KAAA,IAAA,mBAAoB,MAAA,EAAQ;YAC9B,QAAQ,WAAA,GAAc;QACxB;QAEA,MAAM,gBAAgB;YAAA,kFAAA;YAAA,uFAAA;YAGpB,GAAG,gBAAgB,OAAO,CAAA;YAAA,+EAAA;YAAA,+EAAA;YAAA,+EAAA;YAAA,4EAAA;YAAA,2BAAA;YAM1B,YAAYF,YAAW;QACzB;QAEA,OAAO;YACL,SAAS;YACT,MAAM;YACN;QACF,CAAC;IACH;IAEA,MAAM,kBAAkB;QACtB;QACA,YAAW,KAAA,EAAO;YAChB,IAAI,mBAAmB,MAAM;gBAC3B,kBAAkB;oBAChB,MAAM;oBACN,MAAM;gBACR;gBACA,QAAQ,KAAA,CAAM,IAAA,CAAK,eAAe;YACpC,OAAO;gBACL,gBAAgB,IAAA,IAAQ;YAC1B;YAEA,QAAQ,OAAA,IAAW;YACnB,WAAW;QACb;QACA,iBAAgB,KAAA,EAAO;YAzJ3B,IAAAC;YA0JM,IAAI,8BAA8B,MAAM;gBACtC,6BAA6B;oBAAE,MAAM;oBAAQ,MAAM;gBAAM;gBACzD,IAAI,wBAAwB,MAAM;oBAChC,qBAAqB,OAAA,CAAQ,IAAA,CAAK,0BAA0B;gBAC9D;YACF,OAAO;gBACL,2BAA2B,IAAA,IAAQ;YACrC;YAEA,IAAI,wBAAwB,MAAM;gBAChC,uBAAuB;oBACrB,MAAM;oBACN,WAAW;oBACX,SAAS;wBAAC,0BAA0B;qBAAA;gBACtC;gBACA,QAAQ,KAAA,CAAM,IAAA,CAAK,oBAAoB;YACzC,OAAO;gBACL,qBAAqB,SAAA,IAAa;YACpC;YAEA,QAAQ,SAAA,GAAA,CAAA,CAAaA,MAAA,QAAQ,SAAA,KAAR,OAAAA,MAAqB,EAAA,IAAM;YAEhD,WAAW;QACb;QACA,0BAAyB,KAAA,EAAO;YAC9B,IAAI,8BAA8B,MAAM;gBACtC,2BAA2B,SAAA,GAAY,MAAM,SAAA;YAC/C;QACF;QACA,yBAAwB,KAAA,EAAO;YAC7B,IAAI,wBAAwB,MAAM;gBAChC,uBAAuB;oBACrB,MAAM;oBACN,WAAW;oBACX,SAAS,CAAC,CAAA;gBACZ;gBACA,QAAQ,KAAA,CAAM,IAAA,CAAK,oBAAoB;YACzC;YAEA,qBAAqB,OAAA,CAAQ,IAAA,CAAK;gBAChC,MAAM;gBACN,MAAM,MAAM,IAAA;YACd,CAAC;YAED,6BAA6B,KAAA;YAE7B,WAAW;QACb;QACA,YAAW,KAAA,EAAO;YAChB,QAAQ,KAAA,CAAM,IAAA,CAAK;gBACjB,MAAM;gBACN,UAAU,MAAM,QAAA;gBAChB,MAAM,MAAM,IAAA;YACd,CAAC;YAED,WAAW;QACb;QACA,cAAa,KAAA,EAAO;YAClB,QAAQ,KAAA,CAAM,IAAA,CAAK;gBACjB,MAAM;gBACN,QAAQ;YACV,CAAC;YAED,WAAW;QACb;QACA,8BAA6B,KAAA,EAAO;YAClC,IAAI,QAAQ,eAAA,IAAmB,MAAM;gBACnC,QAAQ,eAAA,GAAkB,CAAC,CAAA;YAC7B;YAGA,gBAAA,CAAiB,MAAM,UAAU,CAAA,GAAI;gBACnC,MAAM;gBACN;gBACA,UAAU,MAAM,QAAA;gBAChB,OAAO,QAAQ,eAAA,CAAgB,MAAA;YACjC;YAEA,MAAM,aAAa;gBACjB,OAAO;gBACP;gBACA,YAAY,MAAM,UAAA;gBAClB,UAAU,MAAM,QAAA;gBAChB,MAAM,KAAA;YACR;YAEA,QAAQ,eAAA,CAAgB,IAAA,CAAK,UAAU;YAEvC,yBAAyB,MAAM,UAAA,EAAY,UAAU;YAErD,WAAW;QACb;QACA,qBAAoB,KAAA,EAAO;YACzB,MAAM,kBAAkB,gBAAA,CAAiB,MAAM,UAAU,CAAA;YAEzD,gBAAgB,IAAA,IAAQ,MAAM,aAAA;YAE9B,MAAM,EAAE,OAAO,WAAA,CAAY,CAAA,GAAI,iBAAiB,gBAAgB,IAAI;YAEpE,MAAM,aAAa;gBACjB,OAAO;gBACP,MAAM,gBAAgB,IAAA;gBACtB,YAAY,MAAM,UAAA;gBAClB,UAAU,gBAAgB,QAAA;gBAC1B,MAAM;YACR;YAEA,QAAQ,eAAA,CAAiB,gBAAgB,KAAK,CAAA,GAAI;YAElD,yBAAyB,MAAM,UAAA,EAAY,UAAU;YAErD,WAAW;QACb;QACA,MAAM,gBAAe,KAAA,EAAO;YAC1B,MAAM,aAAa;gBACjB,OAAO;gBACP;gBACA,GAAG,KAAA;YACL;YAEA,IAAI,gBAAA,CAAiB,MAAM,UAAU,CAAA,IAAK,MAAM;gBAE9C,QAAQ,eAAA,CAAiB,gBAAA,CAAiB,MAAM,UAAU,CAAA,CAAE,KAAK,CAAA,GAC/D;YACJ,OAAO;gBACL,IAAI,QAAQ,eAAA,IAAmB,MAAM;oBACnC,QAAQ,eAAA,GAAkB,CAAC,CAAA;gBAC7B;gBAEA,QAAQ,eAAA,CAAgB,IAAA,CAAK,UAAU;YACzC;YAEA,yBAAyB,MAAM,UAAA,EAAY,UAAU;YAErD,WAAW;YAKX,IAAI,YAAY;gBACd,MAAM,SAAS,MAAM,WAAW;oBAAE,UAAU;gBAAM,CAAC;gBACnD,IAAI,UAAU,MAAM;oBAClB,MAAME,cAAa;wBACjB,OAAO;wBACP;wBACA,GAAG,KAAA;wBACH;oBACF;oBAGA,QAAQ,eAAA,CAAiB,QAAQ,eAAA,CAAiB,MAAA,GAAS,CAAC,CAAA,GAC1DA;oBAEF,yBAAyB,MAAM,UAAA,EAAYA,WAAU;oBAErD,WAAW;gBACb;YACF;QACF;QACA,kBAAiB,KAAA,EAAO;YACtB,MAAM,kBAAkB,QAAQ,eAAA;YAEhC,IAAI,mBAAmB,MAAM;gBAC3B,MAAM,IAAI,MAAM,6CAA6C;YAC/D;YAIA,MAAM,sBAAsB,gBAAgB,SAAA,CAC1C,CAAAA,cAAcA,YAAW,UAAA,KAAe,MAAM,UAAA;YAGhD,IAAI,wBAAwB,CAAA,GAAI;gBAC9B,MAAM,IAAI,MACR;YAEJ;YAEA,MAAM,aAAa;gBACjB,GAAG,eAAA,CAAgB,mBAAmB,CAAA;gBACtC,OAAO;gBACP,GAAG,KAAA;YACL;YAEA,eAAA,CAAgB,mBAAmB,CAAA,GAAI;YAEvC,yBAAyB,MAAM,UAAA,EAAY,UAAU;YAErD,WAAW;QACb;QACA,YAAW,KAAA,EAAO;YAChB,KAAK,IAAA,CAAK,GAAG,KAAK;YAClB,WAAW;QACb;QACA,0BAAyB,KAAA,EAAO;YAC9B,IAAI,sBAAsB,MAAM;gBAC9B,qBAAqB,CAAC;uBAAG,KAAK;iBAAA;YAChC,OAAO;gBACL,mBAAmB,IAAA,CAAK,GAAG,KAAK;YAClC;YAEA,WAAW;QACb;QACA,kBAAiB,KAAA,EAAO;YACtB,QAAQ;YAGR,kBAAkB,MAAM,WAAA,GAAc,kBAAkB,KAAA;YACxD,uBAAuB,KAAA;YACvB,6BAA6B,KAAA;QAC/B;QACA,iBAAgB,KAAA,EAAO;YAErB,IAAI,CAAC,oBAAoB;gBACvB,QAAQ,EAAA,GAAK,MAAM,SAAA;YACrB;YAGA,QAAQ,KAAA,CAAM,IAAA,CAAK;gBAAE,MAAM;YAAa,CAAC;YACzC,WAAW;QACb;QACA,qBAAoB,KAAA,EAAO;YACzB,eAAe,MAAM,YAAA;YACrB,IAAI,MAAM,KAAA,IAAS,MAAM;gBACvB,QAAQ,4BAA4B,MAAM,KAAK;YACjD;QACF;QACA,aAAY,KAAA,EAAO;YACjB,MAAM,IAAI,MAAM,KAAK;QACvB;IACF,CAAC;IAED,YAAA,OAAA,KAAA,IAAA,SAAW;QAAE;QAAS;QAAc;IAAM;AAC5C;;;AOnYA,eAAsB,kBAAkB,EACtC,MAAA,EACA,UAAA,EACF,EAGkB;IAChB,MAAM,SAAS,OAAO,WAAA,CAAY,IAAI,kBAAkB,CAAC,EAAE,SAAA,CAAU;IACrE,MAAO,KAAM;QACX,MAAM,EAAE,IAAA,EAAM,KAAA,CAAM,CAAA,GAAI,MAAM,OAAO,IAAA,CAAK;QAC1C,IAAI,MAAM;YACR;QACF;QACA,MAAM,WAAW,KAAK;IACxB;AACF;;ADVA,eAAsB,wBAAwB,EAC5C,MAAA,EACA,MAAA,EACA,QAAA,EACA,iBAAiB,IAAM,aAAA,GAAA,IAAI,KAAK,CAAA,EAChC,YAAAE,mRAAaC,cAAAA,EACf,EAUG;IACD,MAAM,WAAuB;QAAE,MAAM;QAAQ,MAAM;IAAG;IAEtD,MAAM,gBAA2B;QAC/B,IAAID,YAAW;QACf,WAAW,eAAe;QAC1B,MAAM;QACN,SAAS;QACT,OAAO;YAAC,QAAQ;SAAA;IAClB;IAEA,MAAM,kBAAkB;QACtB;QACA,YAAY,CAAA,UAAS;YACnB,cAAc,OAAA,IAAW;YACzB,SAAS,IAAA,IAAQ;YAGjB,OAAO;gBACL,SAAS;oBAAE,GAAG,aAAA;gBAAc;gBAC5B,MAAM,CAAC,CAAA;gBACP,oBAAoB;YACtB,CAAC;QACH;IACF,CAAC;IAGD,YAAA,OAAA,KAAA,IAAA,SAAW,eAAe;QACxB,OAAO;YAAE,kBAAkB;YAAK,cAAc;YAAK,aAAa;QAAI;QACpE,cAAc;IAChB;AACF;;AE/CA,IAAM,mBAAmB,IAAM;AAE/B,eAAsB,YAAY,EAChC,GAAA,EACA,IAAA,EACA,iBAAiB,MAAA,EACjB,WAAA,EACA,OAAA,EACA,eAAA,EACA,wBAAA,EACA,UAAA,EACA,QAAA,EACA,QAAA,EACA,UAAA,EACA,YAAAE,WAAAA,EACA,OAAAC,SAAQ,iBAAiB,CAAA,EACzB,WAAA,EACA,cAAc,UAAA,EAChB,EAoBG;IA3CH,IAAA,IAAA,IAAA;IA4CE,MAAM,UACJ,gBAAgB,WACZA,OAAM,GAAG,GAAG,CAAA,QAAA,EAAW,KAAK,EAAE,EAAA,EAAI;QAChC,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,GAAG,OAAA;QACL;QACA,QAAA,CAAQ,KAAA,mBAAA,OAAA,KAAA,IAAA,iBAAA,KAAA,OAAA,KAAA,IAAA,GAAqB,MAAA;QAC7B;IACF,CAAC,IACDA,OAAM,KAAK;QACT,QAAQ;QACR,MAAM,KAAK,SAAA,CAAU,IAAI;QACzB,SAAS;YACP,gBAAgB;YAChB,GAAG,OAAA;QACL;QACA,QAAA,CAAQ,KAAA,mBAAA,OAAA,KAAA,IAAA,iBAAA,KAAA,OAAA,KAAA,IAAA,GAAqB,MAAA;QAC7B;IACF,CAAC;IAEP,MAAM,WAAW,MAAM,QAAQ,KAAA,CAAM,CAAA,QAAO;QAC1C,yBAAyB;QACzB,MAAM;IACR,CAAC;IAED,IAAI,YAAY;QACd,IAAI;YACF,MAAM,WAAW,QAAQ;QAC3B,EAAA,OAAS,KAAK;YACZ,MAAM;QACR;IACF;IAEA,IAAI,CAAC,SAAS,EAAA,EAAI;QAChB,yBAAyB;QACzB,MAAM,IAAI,MAAA,CACP,KAAA,MAAM,SAAS,IAAA,CAAK,CAAA,KAApB,OAAA,KAA0B;IAE/B;IAEA,IAAI,CAAC,SAAS,IAAA,EAAM;QAClB,MAAM,IAAI,MAAM,6BAA6B;IAC/C;IAEA,OAAQ,gBAAgB;QACtB,KAAK;YAAQ;gBACX,MAAM,wBAAwB;oBAC5B,QAAQ,SAAS,IAAA;oBACjB,QAAQ;oBACR;oBACA,YAAAD;gBACF,CAAC;gBACD;YACF;QAEA,KAAK;YAAQ;gBACX,MAAM,oBAAoB;oBACxB,QAAQ,SAAS,IAAA;oBACjB,QAAQ;oBACR;oBACA;oBACA,UAAS,EAAE,OAAA,EAAS,YAAA,EAAc,KAAA,CAAM,CAAA,EAAG;wBACzC,IAAI,YAAY,WAAW,MAAM;4BAC/B,SAAS,SAAS;gCAAE;gCAAO;4BAAa,CAAC;wBAC3C;oBACF;oBACA,YAAAA;gBACF,CAAC;gBACD;YACF;QAEA;YAAS;gBACP,MAAM,kBAAyB;gBAC/B,MAAM,IAAI,MAAM,CAAA,yBAAA,EAA4B,eAAe,EAAE;YAC/D;IACF;AACF;;ACrHA,IAAME,oBAAmB,IAAM;AAE/B,eAAsB,kBAAkB,EACtC,GAAA,EACA,MAAA,EACA,WAAA,EACA,OAAA,EACA,IAAA,EACA,iBAAiB,MAAA,EACjB,aAAA,EACA,UAAA,EACA,QAAA,EACA,kBAAA,EACA,UAAA,EACA,QAAA,EACA,OAAA,EACA,MAAA,EACA,OAAAC,SAAQD,kBAAiB,CAAA,EAC3B,EAgBG;IAvCH,IAAA;IAwCE,IAAI;QACF,WAAW,IAAI;QACf,SAAS,KAAA,CAAS;QAElB,MAAM,kBAAkB,IAAI,gBAAgB;QAC5C,mBAAmB,eAAe;QAGlC,cAAc,EAAE;QAEhB,MAAM,WAAW,MAAMC,OAAM,KAAK;YAChC,QAAQ;YACR,MAAM,KAAK,SAAA,CAAU;gBACnB;gBACA,GAAG,IAAA;YACL,CAAC;YACD;YACA,SAAS;gBACP,gBAAgB;gBAChB,GAAG,OAAA;YACL;YACA,QAAQ,gBAAgB,MAAA;QAC1B,CAAC,EAAE,KAAA,CAAM,CAAA,QAAO;YACd,MAAM;QACR,CAAC;QAED,IAAI,YAAY;YACd,IAAI;gBACF,MAAM,WAAW,QAAQ;YAC3B,EAAA,OAAS,KAAK;gBACZ,MAAM;YACR;QACF;QAEA,IAAI,CAAC,SAAS,EAAA,EAAI;YAChB,MAAM,IAAI,MAAA,CACP,KAAA,MAAM,SAAS,IAAA,CAAK,CAAA,KAApB,OAAA,KAA0B;QAE/B;QAEA,IAAI,CAAC,SAAS,IAAA,EAAM;YAClB,MAAM,IAAI,MAAM,6BAA6B;QAC/C;QAEA,IAAI,SAAS;QAEb,OAAQ,gBAAgB;YACtB,KAAK;gBAAQ;oBACX,MAAM,kBAAkB;wBACtB,QAAQ,SAAS,IAAA;wBACjB,YAAY,CAAA,UAAS;4BACnB,UAAU;4BACV,cAAc,MAAM;wBACtB;oBACF,CAAC;oBACD;gBACF;YACA,KAAK;gBAAQ;oBACX,MAAM,kBAAkB;wBACtB,QAAQ,SAAS,IAAA;wBACjB,YAAW,KAAA,EAAO;4BAChB,UAAU;4BACV,cAAc,MAAM;wBACtB;wBACA,YAAW,KAAA,EAAO;4BAChB,UAAA,OAAA,KAAA,IAAA,OAAS;wBACX;wBACA,aAAY,KAAA,EAAO;4BACjB,MAAM,IAAI,MAAM,KAAK;wBACvB;oBACF,CAAC;oBACD;gBACF;YACA;gBAAS;oBACP,MAAM,kBAAyB;oBAC/B,MAAM,IAAI,MAAM,CAAA,yBAAA,EAA4B,eAAe,EAAE;gBAC/D;QACF;QAEA,IAAI,UAAU;YACZ,SAAS,QAAQ,MAAM;QACzB;QAEA,mBAAmB,IAAI;QACvB,OAAO;IACT,EAAA,OAAS,KAAK;QAEZ,IAAK,IAAY,IAAA,KAAS,cAAc;YACtC,mBAAmB,IAAI;YACvB,OAAO;QACT;QAEA,IAAI,eAAe,OAAO;YACxB,IAAI,SAAS;gBACX,QAAQ,GAAG;YACb;QACF;QAEA,SAAS,GAAY;IACvB,SAAE;QACA,WAAW,KAAK;IAClB;AACF;;AC3IO,SAAS,mBAAmB,OAAA,EAAyB;IAC1D,MAAM,CAAC,QAAQ,aAAa,CAAA,GAAI,QAAQ,KAAA,CAAM,GAAG;IACjD,MAAM,WAAW,OAAO,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,CAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA;IAElD,IAAI,YAAY,QAAQ,iBAAiB,MAAM;QAC7C,MAAM,IAAI,MAAM,yBAAyB;IAC3C;IAEA,IAAI;QACF,OAAO,OAAO,IAAA,CAAK,aAAa;IAClC,EAAA,OAAS,OAAO;QACd,MAAM,IAAI,MAAM,CAAA,uBAAA,CAAyB;IAC3C;AACF;;ACdO,SAAS,6BACd,eAAA,EACoB;IACpB,OAAO,mBAAA,OAAA,KAAA,IAAA,gBAAiB,MAAA,CAAO,CAAC,KAAK,mBAAmB;QAL1D,IAAA;QAMI,OAAO,KAAK,GAAA,CAAI,KAAA,CAAK,KAAA,eAAe,IAAA,KAAf,OAAA,KAAuB,CAAC;IAC/C,GAAG;AACL;;ACIO,SAAS,gBACd,OAAA,EAQE;IArBJ,IAAA;IAsBE,OAAA,CACE,KAAA,QAAQ,KAAA,KAAR,OAAA,KAAiB;WACX,QAAQ,eAAA,GACR,QAAQ,eAAA,CAAgB,GAAA,CAAI,CAAA,iBAAA,CAAmB;gBAC7C,MAAM;gBACN;YACF,CAAA,CAAE,IACF,CAAC,CAAA;WACD,QAAQ,SAAA,GACR;YACE;gBACE,MAAM;gBACN,WAAW,QAAQ,SAAA;gBACnB,SAAS;oBAAC;wBAAE,MAAM;wBAAiB,MAAM,QAAQ,SAAA;oBAAU,CAAC;iBAAA;YAC9D;SACF,GACA,CAAC,CAAA;WACD,QAAQ,OAAA,GACR;YAAC;gBAAE,MAAM;gBAAiB,MAAM,QAAQ,OAAA;YAAQ,CAAC;SAAA,GACjD,CAAC,CAAA;KACP;AAEJ;;ACzCO,SAAS,iBAAiB,QAAA,EAAkC;IACjE,OAAO,SAAS,GAAA,CAAI,CAAA,UAAA,CAAY;YAC9B,GAAG,OAAA;YACH,OAAO,gBAAgB,OAAO;QAChC,CAAA,CAAE;AACJ;;ACDO,SAAS,gBAAgB,IAAA,EAAW,IAAA,EAAoB;IAE7D,IAAI,SAAS,MAAM,OAAO;IAG1B,IAAI,QAAQ,QAAQ,QAAQ,MAAM,OAAO;IAGzC,IAAI,OAAO,SAAS,YAAY,OAAO,SAAS,UAC9C,OAAO,SAAS;IAGlB,IAAI,KAAK,WAAA,KAAgB,KAAK,WAAA,EAAa,OAAO;IAGlD,IAAI,gBAAgB,QAAQ,gBAAgB,MAAM;QAChD,OAAO,KAAK,OAAA,CAAQ,MAAM,KAAK,OAAA,CAAQ;IACzC;IAGA,IAAI,MAAM,OAAA,CAAQ,IAAI,GAAG;QACvB,IAAI,KAAK,MAAA,KAAW,KAAK,MAAA,EAAQ,OAAO;QACxC,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,MAAA,EAAQ,IAAK;YACpC,IAAI,CAAC,gBAAgB,IAAA,CAAK,CAAC,CAAA,EAAG,IAAA,CAAK,CAAC,CAAC,GAAG,OAAO;QACjD;QACA,OAAO;IACT;IAGA,MAAM,QAAQ,OAAO,IAAA,CAAK,IAAI;IAC9B,MAAM,QAAQ,OAAO,IAAA,CAAK,IAAI;IAC9B,IAAI,MAAM,MAAA,KAAW,MAAM,MAAA,EAAQ,OAAO;IAG1C,KAAA,MAAW,OAAO,MAAO;QACvB,IAAI,CAAC,MAAM,QAAA,CAAS,GAAG,GAAG,OAAO;QACjC,IAAI,CAAC,gBAAgB,IAAA,CAAK,GAAG,CAAA,EAAG,IAAA,CAAK,GAAG,CAAC,GAAG,OAAO;IACrD;IAEA,OAAO;AACT;;AC7CA,eAAsB,6BACpB,sBAAA,EACA;IACA,IAAI,CAAC,wBAAwB;QAC3B,OAAO,CAAC,CAAA;IACV;IAKA,IACE,WAAW,QAAA,IACX,kCAAkC,WAAW,QAAA,EAC7C;QACA,OAAO,QAAQ,GAAA,CACb,MAAM,IAAA,CAAK,sBAAsB,EAAE,GAAA,CAAI,OAAM,eAAc;YACzD,MAAM,EAAE,IAAA,EAAM,IAAA,CAAK,CAAA,GAAI;YAEvB,MAAM,UAAU,MAAM,IAAI,QAAgB,CAAC,SAAS,WAAW;gBAC7D,MAAM,SAAS,IAAI,WAAW;gBAC9B,OAAO,MAAA,GAAS,CAAA,gBAAe;oBAtBzC,IAAA;oBAuBY,QAAA,CAAQ,KAAA,YAAY,MAAA,KAAZ,OAAA,KAAA,IAAA,GAAoB,MAAgB;gBAC9C;gBACA,OAAO,OAAA,GAAU,CAAA,QAAS,OAAO,KAAK;gBACtC,OAAO,aAAA,CAAc,UAAU;YACjC,CAAC;YAED,OAAO;gBACL;gBACA,aAAa;gBACb,KAAK;YACP;QACF,CAAC;IAEL;IAEA,IAAI,MAAM,OAAA,CAAQ,sBAAsB,GAAG;QACzC,OAAO;IACT;IAEA,MAAM,IAAI,MAAM,0BAA0B;AAC5C;;ACtCA,IAAMC,WAAU,KAAK,UAAA,CAAW,CAAC;AAGjC,SAASC,cAAa,MAAA,EAAsB,WAAA,EAAqB;IAC/D,MAAM,qBAAqB,IAAI,WAAW,WAAW;IAErD,IAAI,SAAS;IACb,KAAA,MAAW,SAAS,OAAQ;QAC1B,mBAAmB,GAAA,CAAI,OAAO,MAAM;QACpC,UAAU,MAAM,MAAA;IAClB;IACA,OAAO,MAAA,GAAS;IAEhB,OAAO;AACT;AAEA,eAAsB,uBAAuB,EAC3C,MAAA,EACA,UAAA,EACA,WAAA,EACA,sBAAA,EACA,0BAAA,EACA,iBAAA,EACF,EAqBkB;IAIhB,MAAM,SAAS,OAAO,SAAA,CAAU;IAChC,MAAM,UAAU,IAAI,YAAY;IAChC,MAAM,SAAuB,CAAC,CAAA;IAC9B,IAAI,cAAc;IAElB,MAAO,KAAM;QACX,MAAM,EAAE,KAAA,CAAM,CAAA,GAAI,MAAM,OAAO,IAAA,CAAK;QAEpC,IAAI,OAAO;YACT,OAAO,IAAA,CAAK,KAAK;YACjB,eAAe,MAAM,MAAA;YACrB,IAAI,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAMD,UAAS;gBAEvC;YACF;QACF;QAEA,IAAI,OAAO,MAAA,KAAW,GAAG;YACvB;QACF;QAEA,MAAM,qBAAqBC,cAAa,QAAQ,WAAW;QAC3D,cAAc;QAEd,MAAM,cAAc,QACjB,MAAA,CAAO,oBAAoB;YAAE,QAAQ;QAAK,CAAC,EAC3C,KAAA,CAAM,IAAI,EACV,MAAA,CAAO,CAAA,OAAQ,SAAS,EAAE,EAC1B,GAAA,CAAI,wBAAwB;QAE/B,KAAA,MAAW,EAAE,IAAA,EAAM,OAAAC,MAAAA,CAAM,CAAA,IAAK,YAAa;YACzC,OAAQ,MAAM;gBACZ,KAAK;oBACH,MAAA,CAAM,cAAA,OAAA,KAAA,IAAA,WAAaA,OAAAA;oBACnB;gBACF,KAAK;oBACH,MAAA,CAAM,eAAA,OAAA,KAAA,IAAA,YAAcA,OAAAA;oBACpB;gBACF,KAAK;oBACH,MAAA,CAAM,0BAAA,OAAA,KAAA,IAAA,uBAAyBA,OAAAA;oBAC/B;gBACF,KAAK;oBACH,MAAA,CAAM,8BAAA,OAAA,KAAA,IAAA,2BAA6BA,OAAAA;oBACnC;gBACF,KAAK;oBACH,MAAA,CAAM,qBAAA,OAAA,KAAA,IAAA,kBAAoBA,OAAAA;oBAC1B;gBACF;oBAAS;wBACP,MAAM,kBAAyB;wBAC/B,MAAM,IAAI,MAAM,CAAA,0BAAA,EAA6B,eAAe,EAAE;oBAChE;YACF;QACF;IACF;AACF;;;AEtGO,SAAS,UACdC,UAAAA,EACA,OAAA,EASgB;IAhBlB,IAAA;IAkBE,MAAM,gBAAA,CAAgB,KAAA,WAAA,OAAA,KAAA,IAAA,QAAS,aAAA,KAAT,OAAA,KAA0B;IAEhD,OAAO,WACL,iSAAA,EAAgBA,YAAW;QACzB,cAAc,gBAAgB,SAAS;QACvC,QAAQ;IACV,CAAC,GACD;QACE,UAAU,CAAA,UAAS;YACjB,MAAM,SAASA,WAAU,SAAA,CAAU,KAAK;YACxC,OAAO,OAAO,OAAA,GACV;gBAAE,SAAS;gBAAM,OAAO,OAAO,IAAA;YAAK,IACpC;gBAAE,SAAS;gBAAO,OAAO,OAAO,KAAA;YAAM;QAC5C;IACF;AAEJ;;AD1BA,IAAM,eAAe,OAAO,GAAA,CAAI,kBAAkB;AAyB3C,SAAS,WACdC,WAAAA,EACA,EACE,QAAA,EACF,GAII,CAAC,CAAA,EACW;IAChB,OAAO;QACL,CAAC,YAAY,CAAA,EAAG;QAChB,OAAO,KAAA;QAAA,gCAAA;QACP,uQAAC,kBAAe,CAAA,EAAG;QACnB,YAAAA;QACA;IACF;AACF;AAEA,SAAS,SAAS,KAAA,EAAiC;IACjD,OACE,OAAO,UAAU,YACjB,UAAU,QACV,gBAAgB,SAChB,KAAA,CAAM,YAAY,CAAA,KAAM,QACxB,gBAAgB,SAChB,cAAc;AAElB;AAEO,SAAS,SACd,MAAA,EACgB;IAChB,OAAO,SAAS,MAAM,IAAI,SAAS,UAAU,MAAM;AACrD;;AEhEO,SAAS,uBAAuB,EACrC,6BAAA,EACA,oBAAA,EACA,QAAA,EACA,QAAA,EACF,EAKG;IAbH,IAAA;IAcE,MAAM,cAAc,QAAA,CAAS,SAAS,MAAA,GAAS,CAAC,CAAA;IAChD,OAAA,mCAAA;IAEE,WAAW,KAAA,kCAAA;IAEX,eAAe,QAAA,mFAAA;IAAA,CAEd,SAAS,MAAA,GAAS,wBACjB,6BAA6B,YAAY,eAAe,MACtD,6BAAA,KAAA,oCAAA;IAEJ,yCAAyC,WAAW,KAAA,uCAAA;IAAA,CAAA,CAEnD,KAAA,6BAA6B,YAAY,eAAe,CAAA,KAAxD,OAAA,KAA6D,CAAA,IAAK;AAEvE;AAOO,SAAS,yCACd,OAAA,EAGA;IACA,IAAI,QAAQ,IAAA,KAAS,aAAa;QAChC,OAAO;IACT;IAEA,MAAM,qBAAqB,QAAQ,KAAA,CAAM,MAAA,CAAO,CAAC,WAAW,MAAM,UAAU;QAC1E,OAAO,KAAK,IAAA,KAAS,eAAe,QAAQ;IAC9C,GAAG,CAAA,CAAE;IAEL,MAAM,0BAA0B,QAAQ,KAAA,CACrC,KAAA,CAAM,qBAAqB,CAAC,EAC5B,MAAA,CAAO,CAAA,OAAQ,KAAK,IAAA,KAAS,iBAAiB;IAEjD,OACE,wBAAwB,MAAA,GAAS,KACjC,wBAAwB,KAAA,CAAM,CAAA,OAAQ,YAAY,KAAK,cAAc;AAEzE;;AC9CO,SAAS,qBAAqB,EACnC,QAAA,EACA,UAAA,EACA,YAAY,MAAA,EACd,EAIG;IAnBH,IAAA;IAoBE,MAAM,cAAc,QAAA,CAAS,SAAS,MAAA,GAAS,CAAC,CAAA;IAEhD,MAAM,iBAAiB,YAAY,KAAA,CAAM,IAAA,CACvC,CAAC,OACC,KAAK,IAAA,KAAS,qBACd,KAAK,cAAA,CAAe,UAAA,KAAe;IAGvC,IAAI,kBAAkB,MAAM;QAC1B;IACF;IAEA,MAAM,aAAa;QACjB,GAAG,eAAe,cAAA;QAClB,OAAO;QACP;IACF;IAEA,eAAe,cAAA,GAAiB;IAEhC,YAAY,eAAA,GAAA,CAAkB,KAAA,YAAY,eAAA,KAAZ,OAAA,KAAA,IAAA,GAA6B,GAAA,CACzD,CAAA,iBACE,eAAe,UAAA,KAAe,aAAa,aAAa;AAE9D", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22], "debugId": null}}, {"offset": {"line": 3026, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/%40geon-ai%2Btools%400.0.11_react%4019.0.0/node_modules/%40geon-ai/tools/src/addr.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40geon-ai%2Btools%400.0.11_react%4019.0.0/node_modules/%40geon-ai/tools/src/map.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40geon-ai%2Btools%400.0.11_react%4019.0.0/node_modules/%40geon-ai/tools/src/directions.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40geon-ai%2Btools%400.0.11_react%4019.0.0/node_modules/%40geon-ai/tools/src/hil.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40geon-ai%2Btools%400.0.11_react%4019.0.0/node_modules/%40geon-ai/tools/src/layer.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40geon-ai%2Btools%400.0.11_react%4019.0.0/node_modules/%40geon-ai/tools/src/analysis.ts"], "sourcesContent": ["import { tool } from 'ai'\nimport { z } from 'zod'\n\ninterface AddressResponse {\n  code: number;\n  message: string;\n  result: {\n    common: {\n      totalCount: string;\n      currentPage: number;\n      countPerPage: number;\n      searchAddressGbn: string;\n      gbn: string;\n      clOk: string;\n      trOk: string;\n      clCd: string;\n      trCd: string;\n      clMessage: string;\n      trMessage: string;\n    };\n    jusoList: Array<{\n      roadAddr: string;\n      jibunAddr: string;\n      buildName: string;\n      buildLo: string;\n      buildLa: string;\n      parcelLo: string;\n      parcelLa: string;\n      poiName: string;\n      buildGeom?: string;\n      geom?: string;\n    }>;\n  };\n}\n\n// export const searchSido = tool({\n//   description: '시도 단위 검색',\n//   parameters: z.object({\n//     location: z.string().describe('시도명'),\n//   }),\n//   execute: async ({ location }) => {\n//     try {\n//       const apiKey = process.env.GEON_API_KEY;\n//       const apiBaseUrl = process.env.GEON_API_BASE_URL;\n\n//       if (!apiKey || !apiBaseUrl) {\n//         throw new Error(\"GEON environment variable is not set\");\n//       }\n\n//       const response = await fetch(\n//         `${apiBaseUrl}/addrgeo/administ/ctpv/list?crtfckey=${apiKey}&retGeom=true`\n//       );\n\n//       if (!response.ok) {\n//         throw new Error(`API 요청 실패: ${response.status} ${response.statusText}`);\n//       }\n\n//       const sidoList = await response.json();\n//       const matchingSido = sidoList.find((sido: any) => sido.korNm.includes(location));\n//       return matchingSido || null;\n//     } catch (error: any) {\n//       return `시도 검색 실패: ${error.message}`;\n//     }\n//   }\n// });\n\n// export const searchSgg = tool({\n//   description: '시군구 단위 검색',\n//   parameters: z.object({\n//     location: z.string().describe('시군구명'),\n//     sidoCode: z.string().describe('시도 코드'),\n//   }),\n//   execute: async ({ location, sidoCode }) => {\n//     try {\n//       const apiKey = process.env.GEON_API_KEY;\n//       const apiBaseUrl = process.env.GEON_API_BASE_URL;\n\n//       if (!apiKey || !apiBaseUrl) {\n//         throw new Error(\"GEON environment variable is not set\");\n//       }\n\n//       const response = await fetch(\n//         `${apiBaseUrl}/addrgeo/administ/sgg/list?crtfckey=${apiKey}&ctprvnCd=${sidoCode}&retGeom=true`\n//       );\n\n//       if (!response.ok) {\n//         throw new Error(`API 요청 실패: ${response.status} ${response.statusText}`);\n//       }\n\n//       const sggList = await response.json();\n//       const matchingSgg = sggList.find((sgg: any) => sgg.korNm.includes(location));\n//       return matchingSgg || null;\n//     } catch (error: any) {\n//       return `시군구 검색 실패: ${error.message}`;\n//     }\n//   }\n// });\n\n// export const searchEmd = tool({\n//   description: '읍면동 단위 검색',\n//   parameters: z.object({\n//     location: z.string().describe('읍면동명'),\n//     sggCode: z.string().describe('시군구 코드'),\n//   }),\n//   execute: async ({ location, sggCode }) => {\n//     try {\n//       const apiKey = process.env.GEON_API_KEY;\n//       const apiBaseUrl = process.env.GEON_API_BASE_URL;\n\n//       if (!apiKey || !apiBaseUrl) {\n//         throw new Error(\"GEON environment variable is not set\");\n//       }\n\n//       const response = await fetch(\n//         `${apiBaseUrl}/addrgeo/administ/emd/list?crtfckey=${apiKey}&emdCd=${sggCode}`\n//       );\n\n//       if (!response.ok) {\n//         throw new Error(`API 요청 실패: ${response.status} ${response.statusText}`);\n//       }\n\n//       const emdList = await response.json();\n//       const matchingEmd = emdList.find((emd: any) => emd.korNm.includes(location));\n//       return matchingEmd || null;\n//     } catch (error: any) {\n//       return `읍면동 검색 실패: ${error.message}`;\n//     }\n//   }\n// });\n\n// export const searchLi = tool({\n//   description: '리 단위 검색',\n//   parameters: z.object({\n//     location: z.string().describe('리명'),\n//     emdCode: z.string().describe('읍면동 코드'),\n//   }),\n//   execute: async ({ location, emdCode }) => {\n//     try {\n//       const apiKey = process.env.GEON_API_KEY;\n//       const apiBaseUrl = process.env.GEON_API_BASE_URL;\n\n//       if (!apiKey || !apiBaseUrl) {\n//         throw new Error(\"GEON environment variable is not set\");\n//       }\n\n//       const response = await fetch(\n//         `${apiBaseUrl}/addrgeo/administ/li/list?crtfckey=${apiKey}&liCd=${emdCode}`\n//       );\n\n//       if (!response.ok) {\n//         throw new Error(`API 요청 실패: ${response.status} ${response.statusText}`);\n//       }\n\n//       const liList = await response.json();\n//       const matchingLi = liList.find((li: any) => li.korNm.includes(location));\n//       return matchingLi || null;\n//     } catch (error: any) {\n//       return `리 검색 실패: ${error.message}`;\n//     }\n//   }\n// });\n\nexport const searchAddress = tool({\n  description: '주소나 건물명으로 위치를 검색합니다. keyword 를 통해 x,y 경위도 좌표, 지번주소, 도로명주소와 같은 위치정보를 얻을 수 있습니다.',\n  parameters: z.object({\n    keyword: z.string(),\n    showMultipleResults: z.boolean().optional().default(false),\n    targetSrid: z.number().optional().default(4326),\n    countPerPage: z.number().min(1).max(20).optional().default(10),\n    currentPage: z.number().min(1).optional().default(1),\n  }),\n  execute: async ({ keyword, showMultipleResults, targetSrid, countPerPage, currentPage }) => {\n    try {\n      const apiKey = process.env.GEON_API_KEY;\n      const apiBaseUrl = process.env.GEON_API_BASE_URL;\n\n      if (!apiKey || !apiBaseUrl) {\n        throw new Error(\"GEON environment variable is not set\");\n      }\n\n      const searchParams = new URLSearchParams({\n        crtfckey: apiKey,\n        keyword,\n        showMultipleResults: String(showMultipleResults),\n        targetSrid: String(targetSrid),\n        countPerPage: String(countPerPage),\n        currentPage: String(currentPage),\n      });\n\n      const response = await fetch(\n        `${apiBaseUrl}/addrgeo/address/int?${searchParams}`,\n        {\n          headers: {\n            Accept: \"application/json\",\n            crtfckey: apiKey,\n          },\n        }\n      );\n\n      if (!response.ok) {\n        throw new Error(`API 요청 실패: ${response.status} ${response.statusText}`);\n      }\n\n      const data: AddressResponse = await response.json() as AddressResponse; \n      \n      return data;\n    } catch (error: any) {\n      console.error(error)\n      let errorMessage = '주소 검색 실패';\n      \n      if (error.response?.status === 401) {\n        errorMessage = '유효하지 않은 API 키';\n      } else if (error.response?.status === 429) {\n        errorMessage = '사용량 초과';\n      }\n      \n      return errorMessage;\n    }\n  }\n});\n", "import { tool } from \"ai\";\nimport { z } from \"zod\";\n\n// 사용 가능한 배경지도 목록\nconst baseMap = {\n  eMapBasic: \"바로e맵 일반 지도\",\n  eMapAIR: \"바로e맵 항공지도\",\n  eMapColor: \"바로e맵 색각 지도\",\n  eMapWhite: \"바로e맵 백지도\",\n} as const;\n/**\nexport const searchUserMapList = tool({\n  description: `사용자가 생성한 모든 지도 목록을 조회합니다.\n  - 사용자 ID가 유효하지 않으면 빈 목록을 반환합니다.\n  - 지도 목록은 생성일자 기준으로 정렬됩니다.`,\n  parameters: z.object({\n    userId: z.string().min(1).describe('조회할 사용자의 고유 ID'),\n  }),\n  execute: async ({ userId }) => {\n    // TODO: Implement actual API call\n    return { message: `${userId}의 지도 목록 조회` };\n  }\n});\n\nexport const searchMapInfo = tool({\n  description: `특정 지도의 상세 정보를 조회합니다.\n  - 지도 ID가 유효하지 않으면 null을 반환합니다.\n  - 반환되는 정보: 지도 이름, 생성자, 생성일자, 마지막 수정일자, 배경지도 종류`,\n  parameters: z.object({\n    userMapId: z.string().min(1).describe('조회할 지도의 고유 ID'),\n  }),\n  execute: async ({ userMapId }) => {\n    // TODO: Implement actual API call\n    return { message: `지도 ${userMapId} 정보 조회` };\n  }\n});\n\nexport const createMap = tool({\n  description: `새로운 지도를 생성합니다.\n  - 동일한 사용자가 같은 이름의 지도를 생성할 수 없습니다.\n  - 기본 배경지도는 'eMapBasic'으로 설정됩니다.\n  - 반환값에는 생성된 지도의 ID가 포함됩니다.`,\n  parameters: z.object({\n    userMapName: z.string().min(1).max(100).describe('생성할 지도의 이름 (1-100자)'),\n    userId: z.string().min(1).describe('지도를 생성할 사용자의 고유 ID'),\n  }),\n  execute: async ({ userMapName, userId }) => {\n    // TODO: Implement actual API call\n    return { message: `${userId}의 ${userMapName} 지도 생성` };\n  }\n});\n\nexport const modifyMap = tool({\n  description: `기존 지도의 정보를 수정합니다.\n  - 지도 ID가 유효하지 않으면 오류를 반환합니다.\n  - 수정 가능한 정보: 지도 이름, 배경지도 종류\n  - 수정된 정보는 즉시 반영됩니다.`,\n  parameters: z.object({\n    userMapId: z.string().min(1).describe('수정할 지도의 고유 ID'),\n    mapInfo: z.object({\n      mapName: z.string().min(1).max(100).optional().describe('변경할 지도 이름 (1-100자)'),\n      baseMapType: z.enum(['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite']).optional().describe('변경할 배경지도 종류'),\n    }).describe('수정할 지도 정보'),\n  }),\n  execute: async ({ userMapId, mapInfo }) => {\n    // TODO: Implement actual API call\n    return { message: `지도 ${userMapId} 정보 수정` };\n  }\n});\n\nexport const saveMap = tool({\n  description: `현재 지도의 상태를 저장합니다.\n  - 저장되는 정보: 지도 중심점, 확대/축소 레벨, 배경지도 종류\n  - 이미 존재하는 지도는 덮어쓰기됩니다.\n  - 저장 성공 시 저장된 지도의 ID를 반환합니다.`,\n  parameters: z.object({\n    userMapName: z.string().min(1).max(100).describe('저장할 지도의 이름 (1-100자)'),\n    userId: z.string().min(1).describe('지도를 저장할 사용자의 고유 ID'),\n  }),\n  execute: async ({ userMapName, userId }) => {\n    // TODO: Implement actual API call\n    return { message: `${userId}의 ${userMapName} 지도 저장` };\n  }\n});\n*/\n\nexport const getBasemapList = tool({\n  description: `사용 가능한 배경지도 목록을 조회합니다.\n  - 반환값은 {지도코드: 지도이름} 형식의 객체입니다.\n  - 지도코드는 changeBasemap 도구에서 사용됩니다.\n  예시 반환값:\n  {\n    \"eMapBasic\": \"바로e맵 일반 지도\",\n    \"eMapAIR\": \"바로e맵 항공지도\"\n  }`,\n  parameters: z.object({}),\n  execute: async () => {\n    return baseMap;\n  },\n});\n\nexport const changeBasemap = tool({\n  description: `현재 지도의 배경지도를 변경합니다.\n  - 배경지도 이름은 searchBasemapList 도구로 조회한 목록의 값과 일치해야 합니다.\n  - 일치하는 배경지도가 없으면 오류를 반환합니다.\n  예시:\n  - 입력: \"바로e맵 일반 지도\"\n  - 반환: { basemap: \"eMapBasic\" }`,\n  parameters: z.object({\n    baseMapName: z\n      .string()\n      .min(1)\n      .describe(\"변경할 배경지도의 이름 (searchBasemapList의 반환값 중 하나)\"),\n  }),\n  execute: async ({ baseMapName }) => {\n    const findKeyByValue = (targetValue: string): string | null => {\n      for (const [key, value] of Object.entries(baseMap)) {\n        if (value.includes(targetValue)) {\n          return key;\n        }\n      }\n      return null;\n    };\n\n    const basemap = findKeyByValue(baseMapName);\n    if (!basemap) {\n      return { error: \"요청하신 배경지도를 찾을 수 없습니다\" };\n    }\n\n    return { basemap };\n  },\n});\n\nexport const setCenter = tool({\n  description: `지도의 중심을 주어진 경위도 좌표로 이동시킵니다.\n  - 좌표계는 WGS84(EPSG:4326)를 사용합니다.\n  예시:\n  - 입력: x=127.0, y=37.5\n  - 반환: { x: 127.0, y: 37.5 }`,\n  parameters: z.object({\n    x: z.number().min(-180).max(180).describe(\"경도 좌표 (-180 ~ 180)\"),\n    y: z.number().min(-90).max(90).describe(\"위도 좌표 (-90 ~ 90)\"),\n  }),\n  execute: async ({ x, y }) => {\n    return { x, y };\n  },\n});\n\nexport const highlightGeometry = tool({\n  description: `지도의 중심을 주어진 geometry로 이동시키고 해당 영역을 강조 표시합니다.\n  - geometry는 Well Known Text(WKT) 형식이어야 합니다.\n  - 좌표계는 WGS84(EPSG:4326)를 사용합니다.\n  예시:\n  - 입력: \n    roadAddr: \"서울특별시 강남구 테헤란로 231\"\n    jibunAddr: \"서울특별시 강남구 역삼동 702-28\"\n    geom: \"POINT(127.0368 37.5018)\"`,\n  parameters: z.object({\n    roadAddr: z.string().min(1).describe(\"도로명주소\"),\n    jibunAddr: z.string().min(1).describe(\"지번주소\"),\n    geom: z.string().min(1).describe(\"WKT(Well Known Text) 형식의 geometry\"),\n  }),\n  execute: async ({ roadAddr, jibunAddr, geom }) => {\n    return { roadAddr, jibunAddr, geom };\n  },\n});\n", "import { tool } from 'ai'\nimport { z } from 'zod'\n\n// Kakao Directions API response types\ninterface KakaoDirectionsResponse {\n  trans_id: string;\n  routes: Array<{\n    result_code: number;\n    result_msg: string;\n    summary: {\n      distance: number;\n      duration: number;\n      fare: {\n        taxi: number;\n        toll: number;\n      };\n    };\n  }>;\n  result_code: number;\n  result_msg: string;\n  summary: {\n    distance: number;\n    duration: number;\n  };\n  origin: {\n    name?: string;\n    x: number;\n    y: number;\n  };\n  destination: {\n    name?: string;\n    x: number;\n    y: number;\n  };\n  waypoints?: Array<{\n    name?: string;\n    x: number;\n    y: number;\n  }>;\n  priority?: string;\n  bound: {\n    min_x: number;\n    min_y: number;\n    max_x: number;\n    max_y: number;\n  };\n  fare?: {\n    taxi: number;\n    toll: number;\n  };\n  sections: Array<{\n    distance: number;\n    duration: number;\n    roads: Array<{\n      name: string;\n      distance: number;\n      duration: number;\n      traffic_speed: number;\n      traffic_state: number;\n    }>;\n  }>;\n}\n\nexport const getDirections = tool({\n  description: '하나의 출발지에서 하나의 목적지까지로의 경로에 대한 상세 정보를 제공합니다.',\n  parameters: z.object({\n    origin: z.string().describe('출발지 좌표 (예: \"127.111202,37.394912\" 또는 \"127.111202,37.394912,name=판교역\")'),\n    destination: z.string().describe('목적지 좌표 (예: \"127.111202,37.394912\" 또는 \"127.111202,37.394912,name=판교역\")'),\n    waypoints: z.string().optional().describe('경유지 좌표 (최대 5개, | 로 구분)'),\n    priority: z.enum(['RECOMMEND', 'TIME', 'DISTANCE']).optional().default('RECOMMEND').describe('경로 탐색 우선순위'),\n    avoid: z.string().optional().describe('회피 옵션 (ferries, toll, motorway, schoolzone, uturn)'),\n    roadevent: z.number().optional().default(0).describe('도로 통제 정보 반영 (0: 전체, 1: 출발/도착지 제외, 2: 미반영)'),\n    alternatives: z.boolean().optional().default(false).describe('대안 경로 제공 여부'),\n    road_details: z.boolean().optional().default(false).describe('상세 도로 정보 제공 여부'),\n    car_type: z.number().optional().default(1).describe('차종'),\n    car_fuel: z.enum(['GASOLINE', 'DIESEL', 'LPG']).optional().default('GASOLINE').describe('유종'),\n    car_hipass: z.boolean().optional().default(false).describe('하이패스 장착 여부'),\n    summary: z.boolean().optional().default(false).describe('요약 정보 제공 여부'),\n  }),\n  execute: async ({ origin, destination, waypoints, priority, avoid, roadevent, alternatives, road_details, car_type, car_fuel, car_hipass, summary }) => {\n    try {\n      const apiKey = process.env.KAKAO_REST_API_KEY;\n      if (!apiKey) {\n        throw new Error(\"KAKAO_REST_API_KEY is not set\");\n      }\n\n      const url = 'https://apis-navi.kakaomobility.com/v1/directions';\n      const params = new URLSearchParams({\n        origin,\n        destination,\n        priority,\n        car_fuel,\n        car_hipass: String(car_hipass),\n        alternatives: String(alternatives),\n        road_details: String(road_details),\n      });\n\n      if (waypoints) params.append('waypoints', waypoints);\n      if (avoid) params.append('avoid', avoid);\n      if (roadevent) params.append('roadevent', String(roadevent));\n      if (car_type) params.append('car_type', String(car_type));\n      if (summary) params.append('summary', String(summary));\n\n      const response = await fetch(`${url}?${params}`, {\n        headers: {\n          Authorization: `KakaoAK ${apiKey}`,\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`API 요청 실패: ${response.status} ${response.statusText}`);\n      }\n\n      const result = await response.json() as KakaoDirectionsResponse;\n      return {\n        trans_id: result.trans_id,\n        routes: result.routes,\n        result_code: result.result_code,\n        result_msg: result.result_msg,\n        summary: result.summary,\n        origin: result.origin,\n        destination: result.destination,\n        waypoints: result.waypoints,\n        priority: result.priority,\n        bound: result.bound,\n        fare: result.fare,\n        distance: result.summary.distance,\n        duration: result.summary.duration,\n        sections: result.sections,\n      };\n    } catch (error) {\n      return `경로 검색 실패: ${error}`;\n    }\n  }\n});\n", "// hil.ts (Human In the Loop)\n\nimport { tool } from \"ai\";\nimport { z } from \"zod\";\n\nexport const askForConfirmation= tool({\n    description: '사용자에게 확인을 요청합니다.',\n    parameters: z.object({\n      message: z.string().describe('The message to ask for confirmation.'),\n    }),\n  })\n\nexport const getLocation= tool({\n    description: '사용자의 위치 정보를 얻습니다.',\n    parameters: z.object({\n      location: z.string().describe('The location to search for addresses.'),\n    }),\n})\n\nexport const chooseOption = tool({\n  description:\n    \"사용자에게 여러 선택지 중 하나를 고르도록 요청합니다. 결과는 options 배열의 값 중 하나입니다.\",\n  parameters: z.object({\n    message: z.string().describe(\"사용자에게 보여줄 안내 문구\"),\n    options: z\n      .array(z.string())\n      .min(1)\n      .describe(\"사용자가 선택할 수 있는 옵션 문자열 배열\"),\n  }),\n});\n\nexport const getUserInput = tool({\n  description:\n    \"사용자에게 자유 입력을 받아옵니다. 예: 레이어 이름 입력, 주석 입력 등.\",\n  parameters: z.object({\n    message: z.string().describe(\"입력 필드 상단에 표시할 안내 문구\"),\n    default: z.string().optional().describe(\"기본 입력값(선택)\").default(\"\"),\n  }),\n});\n\nexport const confirmWithCheckbox = tool({\n  description:\n    \"체크박스 확인 방식으로 사용자의 확정을 받습니다. 예: 이용 약관 동의, 민감 데이터 확인 등.\",\n  parameters: z.object({\n    message: z.string().describe(\"사용자에게 보여줄 문구\"),\n    label: z\n      .string()\n      .describe(\"체크박스 옆에 표시할 라벨. 사용자가 체크해야 확인이 완료됩니다.\"),\n  }),\n});\n", "import { generateObject, generateText, tool } from \"ai\";\nimport { geon } from \"@ai-sdk/geon\";\nimport { z } from \"zod\";\n\n// 레이어 분류 코드 상수\nconst LayerClassCode = {\n  ANALYSIS: \"MPD011\",\n  EXTERNAL: \"MPD013\",\n} as const;\n\n// 서비스 타입 정의\ntype ServiceType =\n  | \"wms\"\n  | \"wfs\"\n  | \"wmts\"\n  | \"group\"\n  | \"cluster\"\n  | \"heatmap\"\n  | \"geoImage\";\n\nconst getServerUrl = (serviceType: ServiceType): string => {\n  const baseUrl = `${process.env.GEON_API_BASE_URL}/map/api/map/`;\n  if (serviceType === \"heatmap\" || serviceType === \"cluster\") {\n    return `${baseUrl}wfs`;\n  } else if (serviceType === \"group\") {\n    return `${baseUrl}wms`;\n  }\n  return `${baseUrl}${serviceType}`;\n};\n\ntype LayerResult = {\n  id: string;\n  name: string;\n  type: string;\n  visible: boolean;\n  zIndex: number;\n  server?: string;\n  layer?: string;\n  service?: string;\n  bbox?: boolean;\n  method?: string;\n  crtfckey?: string;\n  projection?: string;\n  geometryType?: string;\n  info: {\n    lyrId: string;\n    lyrNm: string;\n    description?: string;\n    metadata: {\n      cntntsId: string;\n      jobClCode?: string;\n      lyrClCode: string;\n      lyrTySeCode: string;\n      namespace?: string;\n    };\n  };\n  namespace?: string;\n  style?: any;\n  matrixSet?: string;\n  opacity?: number;\n  filter?: string;\n  dynmFlterCndCn?: string;\n};\n\nconst determineServiceType = (\n  layerInfo: any,\n  lyrClCode: string,\n  lyrClSeCode: string,\n  lyrTySeCode: string\n): ServiceType => {\n  const svcTySeCode = layerInfo.result.svcTySeCode;\n  if (lyrClCode === \"MPD011\" && lyrClSeCode === \"04\") return \"heatmap\";\n  if (lyrClCode === \"MPD011\" && lyrClSeCode === \"06\") return \"cluster\";\n  if (lyrClCode === \"MPD013\" && lyrClSeCode === \"06\" && lyrTySeCode === \"5\")\n    return \"group\";\n  if (lyrTySeCode === \"6\") return \"geoImage\";\n  return svcTySeCode === \"M\" ? \"wms\" : svcTySeCode === \"T\" ? \"wmts\" : \"wfs\";\n};\n\nconst getGeometryType = (lyrTySeCode: string): string | undefined => {\n  switch (lyrTySeCode) {\n    case \"1\":\n      return \"point\";\n    case \"2\":\n      return \"line\";\n    case \"3\":\n      return \"polygon\";\n    case \"4\":\n      return \"geoTiff\";\n    default:\n      return undefined;\n  }\n};\n\nconst createExternalApiLayerConfig = (layerInfo: any) => {\n  const param = JSON.parse(layerInfo.result.mapUrlParamtr || \"{}\");\n  const style = layerInfo.result.symbolCndCn\n    ? JSON.parse(layerInfo.result.symbolCndCn)\n    : undefined;\n  return {\n    layerConfig: {\n      params: {\n        ...param,\n        projection: `EPSG:${layerInfo.result.cntmSeCode}`,\n      },\n    },\n    style,\n    layerInfo: {\n      lyrId: layerInfo.result.lyrId,\n      lyrNm: layerInfo.result.lyrNm,\n      description: layerInfo.result.lyrDc,\n    },\n  };\n};\n\nconst generateRandomStyle = (geometryType: string): any => {\n  const randomColor = () => Math.floor(Math.random() * 255);\n  const color = `rgb(${randomColor()}, ${randomColor()}, ${randomColor()})`;\n  return {\n    styleObject: [\n      {\n        seperatorFunc: \"default\",\n        style: {\n          geometryType: \"free\",\n          image:\n            geometryType === \"point\"\n              ? {\n                  circle: {\n                    radius: 5,\n                    fill: { color },\n                    stroke: { color: \"#000000\", width: 1 },\n                  },\n                }\n              : {},\n          fill: geometryType === \"polygon\" ? { color } : undefined,\n          stroke: {\n            color: geometryType === \"line\" ? color : \"#000000\",\n            width: 1,\n          },\n        },\n      },\n    ],\n    serviceType: \"vector\",\n  };\n};\n\nexport const getLayer = tool({\n  description: `지도에 레이어를 추가하기 위한 모든 설정을 준비합니다.\n  - 레이어 ID를 사용하여 레이어 정보를 조회하고 설정을 생성합니다.`,\n  parameters: z.object({\n    userId: z.string().describe(\"사용자 ID\"),\n    insttCode: z.string().describe(\"기관 코드\"),\n    userSeCode: z.string().describe(\"사용자 구분 코드\"),\n    lyrId: z.string().describe(\"레이어 ID\"),\n  }),\n  async execute({ userId, insttCode, userSeCode, lyrId }) {\n    try {\n      // 1. 레이어 기본 정보 조회\n      const params = new URLSearchParams({\n        crtfckey: process.env.GEON_API_KEY || \"\",\n        lyrId,\n        sessionInsttCode: insttCode,\n        sessionUserSeCode: userSeCode,\n        sessionUserId: userId,\n      });\n\n      const response = await fetch(\n        `${process.env.GEON_API_BASE_URL}/smt/layer/info/select?${params.toString()}`,\n        {\n          method: \"GET\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            crtfckey: process.env.GEON_API_KEY || \"\",\n          },\n        }\n      );\n\n      if (!response.ok) {\n        throw new Error(`API request failed with status ${response.status}`);\n      }\n\n      const layerInfo = (await response.json()) as any;\n\n      if (!layerInfo || !layerInfo.result) {\n        return { error: `${lyrId} < 레이어를 찾을 수 없습니다.` };\n      }\n\n      // 2. 레이어 컨텐츠 정보 조회\n      const contentParams = new URLSearchParams({\n        crtfckey: process.env.GEON_API_KEY || \"\",\n        cntntsId: layerInfo.result.cntntsId || \"\",\n      });\n\n      const responseContent = await fetch(\n        `${process.env.GEON_API_BASE_URL}/map/layer/cn/select?${contentParams.toString()}`,\n        {\n          method: \"GET\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            crtfckey: process.env.GEON_API_KEY || \"\",\n          },\n        }\n      );\n\n      if (!responseContent.ok) {\n        throw new Error(\n          `API request failed with status ${responseContent.status}`\n        );\n      }\n\n      const layerContent = (await responseContent.json()) as any;\n\n      if (!layerContent || !layerContent.result) {\n        return { error: \"레이어 컨텐츠 정보를 찾을 수 없습니다.\" };\n      }\n\n      const { lyrClCode, lyrClSeCode, lyrTySeCode, svcTySeCode } =\n        layerInfo.result;\n\n      // 3. 서비스 타입 결정\n      const serviceType = determineServiceType(\n        layerInfo,\n        lyrClCode,\n        lyrClSeCode,\n        lyrTySeCode\n      );\n      const geometryType = getGeometryType(lyrTySeCode);\n\n      // 4. 외부 API 레이어 처리 (MPD013, lyrClSeCode: 11)\n      if (lyrClCode === LayerClassCode.EXTERNAL && lyrClSeCode === \"11\") {\n        const externalConfig = createExternalApiLayerConfig(layerInfo);\n        return {\n          id: layerInfo.result.lyrId,\n          name: layerInfo.result.lyrNm,\n          type: \"api\",\n          visible: true,\n          zIndex: 0,\n          info: externalConfig.layerInfo,\n          ...externalConfig.layerConfig.params,\n          style: externalConfig.style,\n        } satisfies LayerResult;\n      }\n\n      // 5. 기본 레이어 설정\n      let layerConfig: LayerResult = {\n        id: layerInfo.result.lyrId,\n        name: layerInfo.result.lyrNm,\n        type: serviceType === \"geoImage\" ? \"geoImage\" : \"geoserver\",\n        visible: layerInfo.result.onOffAt !== \"N\",\n        zIndex: 0,\n        server: getServerUrl(serviceType),\n        layer: `${layerContent.result.namespace}:${layerInfo.result.cntntsId}`,\n        service: serviceType,\n        bbox: false,\n        method: serviceType === \"wmts\" ? \"get\" : \"post\",\n        crtfckey: process.env.GEON_API_KEY,\n        projection: `EPSG:${layerInfo.result.cntmSeCode}`,\n        geometryType,\n        info: {\n          lyrId: layerInfo.result.lyrId,\n          lyrNm: layerInfo.result.lyrNm,\n          description: layerInfo.result.lyrDc,\n          metadata: {\n            cntntsId: layerInfo.result.cntntsId,\n            jobClCode: layerInfo.result.jobClCode,\n            lyrClCode,\n            lyrTySeCode,\n            namespace: layerContent.result.namespace,\n          },\n        },\n        namespace: layerContent.result.namespace,\n        matrixSet:\n          serviceType === \"wmts\"\n            ? `EPSG:${layerInfo.result.cntmSeCode}`\n            : undefined,\n      };\n\n      // 6. GeoImage 처리\n      if (serviceType === \"geoImage\") {\n        const geoImgResponse = await fetch(\n          `${process.env.GEON_API_BASE_URL}/smt/layer/geoImage?fileName=${encodeURIComponent(layerInfo.result.mapUrl)}&lyrId=${lyrId}&crtfckey=${process.env.GEON_API_KEY}`,\n          { method: \"GET\", headers: { \"Content-Type\": \"application/json\" } }\n        );\n        const geoImgResult = (await geoImgResponse.json()) as any;\n        const format = layerInfo.result.mapUrl.split(\".\").pop()?.toLowerCase();\n        const base64 = `data:image/${format};base64,${geoImgResult?.result?.base64}`;\n\n        layerConfig = {\n          ...layerConfig,\n          type: \"geoImage\",\n          url: base64,\n          ...(layerInfo.result.mapUrlParamtr &&\n            JSON.parse(layerInfo.result.mapUrlParamtr)),\n          opacity:\n            layerInfo.result.symbolCndCn &&\n            JSON.parse(layerInfo.result.symbolCndCn)?.opacity,\n        };\n      }\n\n      // 7. 스타일 처리\n      let style = layerInfo.result.symbolCndCn\n        ? JSON.parse(layerInfo.result.symbolCndCn)\n        : undefined;\n      if (style) {\n        if ([\"vector\", \"cluster\"].includes(style.serviceType)) {\n          layerConfig.style = style.styleObject; // For vector/cluster, use styleObject directly\n        } else if (style.serviceType === \"image\") {\n          layerConfig.style = style.styleObject; // For WMS, SLD style\n        } else if (style.serviceType === \"heatmap\") {\n          layerConfig = { ...layerConfig, ...style.styleObject }; // Heatmap-specific props\n        }\n        if (style.opacity) layerConfig.opacity = style.opacity;\n      } else if (\n        serviceType === \"wfs\" &&\n        [\"1\", \"2\", \"3\"].includes(lyrTySeCode)\n      ) {\n        layerConfig.style = generateRandomStyle(geometryType!); // Random style for WFS without style\n      } else if (\n        serviceType === \"wms\" &&\n        [\"1\", \"2\", \"3\"].includes(lyrTySeCode)\n      ) {\n        // Default WMS random style could be added here if needed\n      }\n\n      // 8. 필터 처리\n      if (\n        lyrTySeCode !== \"5\" &&\n        lyrTySeCode !== \"4\" &&\n        lyrTySeCode !== \"0\" &&\n        lyrTySeCode !== \"6\" &&\n        !(lyrClCode === \"MPD013\" && lyrClSeCode === \"11\")\n      ) {\n        layerConfig.filter = layerInfo.result.flterCndCn;\n        layerConfig.dynmFlterCndCn = layerInfo.result.dynmFlterCndCn;\n      }\n\n      return layerConfig;\n    } catch (error: any) {\n      return { error: `레이어 설정 생성 실패: ${error.message}` };\n    }\n  },\n});\n\n// 레이어 목록 아이템 응답 타입\ninterface LayerListItem {\n  lyrId: string; // 레이어ID\n  cntntsId: string; // 콘텐츠일련번호\n  jobClCode: string; // 업무구분코드\n  jobClCodeNm: string; // 업무구분코드명\n  lyrNm: string; // 레이어명\n  lyrClCode: string; // 레이어분류코드\n  lyrClCodeNm: string; // 레이어분류코드명\n  lyrClSeCode: string; // 레이어분류구분코드\n  lyrClSeCodeNm: string; // 레이어분류구분코드명\n  lyrPosesnSeCode: string; // 레이어소유구분코드\n  lyrPosesnSeCodeNm: string; // 레이어소유구분코드명\n  lyrTySeCode: string; // 레이어유형구분코드\n  lyrTySeCodeNm: string; // 레이어유형구분코드명\n  svcTySeCode: string; // 서비스유형구분코드\n  svcTySeCodeNm: string; // 서비스유형구분코드명\n  cntmSeCode: string; // 좌표계구분코드\n  cntmSeCodeNm: string; // 좌표계구분코드명\n  usePblonsipSeCode: string; // 사용공유구분코드\n  usePblonsipSeCodeNm: string; // 사용공유구분코드명\n  useSttusSeCode: string; // 사용상태구분코드\n  useSttusSeCodeNm: string; // 사용상태구분코드명\n  holdDataId: string; // 보유데이터ID\n  lyrDc: string; // 레이어설명\n  mapUrl: string; // 지도url\n  mapUrlParamtr: string; // 지도url파라미터\n  xyOrdrNrmltAt: string; // XY순서정상여부\n  registerId: string; // 등록자ID\n  registerNm: string; // 등록자명\n  registDt: string; // 등록일자\n  updusrId: string; // 수정자ID\n  updusrNm: string; // 수정자명\n  updtDt: string; // 수정일자\n  userNm: string; // 사용자명\n  ownerNm: string; // 소유자 명\n  ownerId: string; // 소유자ID\n}\n\n// 레이어 목록 응답 타입\ninterface LayerListResponse {\n  code: number;\n  message: string;\n  result: {\n    pageInfo: {\n      pageSize: number;\n      pageIndex: number;\n      totalCount: number;\n    };\n    list: LayerListItem[];\n  };\n}\n\ninterface LayerAttributesResponse {\n  code: number;\n  message: string;\n  result: {\n    pageInfo: {\n      pageSize: number;\n      pageIndex: number;\n      totalCount: number;\n    };\n    features: Record<string, any>[];\n    pkColumnName: string;\n  };\n}\n\ninterface LayerColumnInfo {\n  lyrId: string;\n  columnOrdr: number;\n  columnNm: string;\n  columnNcm: string;\n  dataTy: string;\n  indictAt: string;\n  editPosblAt: string;\n  esntlAt: string;\n  mummLt?: number;\n  mxmmLt?: number;\n  cmmnGroupCode: string | null;\n  cmmnGroupCodeNm: string | null;\n  useAt: string;\n  registerId: string;\n  registDt: string;\n  updusrId: string;\n  updtDt: string;\n}\n\ninterface LayerColumnResponse {\n  code: number;\n  message: string;\n  result: LayerColumnInfo[];\n}\n\nexport const getLayerList = tool({\n  description: `레이어 목록을 검색합니다.\n  - 레이어 이름으로 검색하거나 전체 목록을 조회할 수 있습니다.\n  - 데이터 구분에 따라 필터링할 수 있습니다.\n  Args:\n        user_id: 사용자 ID (예: \"user_id\")\n        layer_name: 검색할 레이어 이름 또는 키워드 (예: \"건물\", \"행정구역\")\n        hold_data_se_code: 데이터 구분 (\"0\": 전체, \"1\": 사용자, \"2\": 공유, \"9\": 국가)\n  `,\n  parameters: z.object({\n    userId: z.string().describe(\"사용자 ID\"),\n    layerName: z.string().optional().describe(\"레이어 이름으로 검색 (선택)\"),\n    holdDataSeCode: z.string().optional().describe(\"데이터 구분 코드 (선택)\"),\n  }),\n  async execute({ userId, layerName = \"\", holdDataSeCode = \"0\" }) {\n    try {\n      const apiBaseUrl = process.env.GEON_API_BASE_URL;\n      const params = new URLSearchParams({\n        crtfckey: process.env.GEON_API_KEY || \"\",\n        userId,\n        searchTxt: layerName,\n        holdDataSeCode,\n        pageIndex: \"1\",\n        pageSize: \"15\",\n      });\n\n      const response = await fetch(\n        `${apiBaseUrl}/smt/layer/info/list?${params.toString()}`,\n        {\n          method: \"GET\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            crtfckey: process.env.GEON_API_KEY || \"\",\n          },\n        }\n      );\n\n      if (!response.ok) {\n        throw new Error(`API request failed with status ${response.status}`);\n      }\n\n      const data = (await response.json()) as LayerListResponse;\n\n      if (!data || !data.result) {\n        return { error: \"레이어 목록 조회 실패: 응답 데이터가 없습니다.\" };\n      }\n\n      return data;\n    } catch (error: any) {\n      return { error: `레이어 목록 조회 실패: ${error.message}` };\n    }\n  },\n});\n\nexport const getLayerAttributes = tool({\n  description: `레이어의 속성 정보를 조회합니다.\n  - 필터 생성 시 이 도구로 조회한 properties의 필드명과 데이터를 참고하세요.\n  - 최대 3개의 데이터셋을 반환합니다.\n\n  Args:\n    lyr_id: 레이어 ID\n    namespace: 레이어의 네임스페이스 (예: 'Wgeontest4')\n    cntnts_id: 컨텐츠 ID (예: 'L100004830')\n            주의: LR로 시작하는 ID는 레이어ID(lyrId)이므로 사용할 수 없습니다.\n  \n  주의사항:\n  - cntntsId가 'LR'로 시작하면 안됩니다 (레이어ID가 아닌 컨텐츠ID를 사용해야 함)\n  \n  반환 정보:\n  - columns: 컬럼 정보 (데이터 타입, 제약조건 등)\n  - data: 실제 데이터 샘플 (최대 3개)`,\n  parameters: z.object({\n    userId: z.string().describe(\"사용자 ID\"),\n    lyrId: z.string().min(1).describe(\"레이어 ID\"),\n    namespace: z\n      .string()\n      .min(1)\n      .describe(\"레이어의 네임스페이스 (예: Wgeontest4)\"),\n    cntntsId: z.string().min(1).describe(\"컨텐츠 ID (LR로 시작하면 안됨)\"),\n  }),\n  execute: async ({ userId, lyrId, namespace, cntntsId }) => {\n    try {\n      const apiKey = process.env.GEON_API_KEY;\n      const apiBaseUrl = process.env.GEON_API_BASE_URL;\n      if (!apiKey || !apiBaseUrl) {\n        throw new Error(\"API 키 또는 기본 URL이 설정되지 않았습니다.\");\n      }\n\n      // 1. 컬럼 정보 조회\n      const columnResponse = await fetch(\n        `${apiBaseUrl}/builder/layer/column/select?crtfckey=${apiKey}&lyrId=${lyrId}&userId=${userId}`,\n        {\n          method: \"GET\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            crtfckey: apiKey,\n          },\n        }\n      );\n\n      if (!columnResponse.ok) {\n        throw new Error(\n          `컬럼 정보 조회 실패: ${columnResponse.status} ${columnResponse.statusText}`\n        );\n      }\n\n      const columnData = (await columnResponse.json()) as LayerColumnResponse;\n\n      console.log(JSON.stringify({\n        typeName: `${namespace}:${cntntsId}`,\n        pageIndex: 1,\n        pageSize: 3,\n      }))\n\n      // 2. 속성 데이터 조회\n      const dataResponse = await fetch(\n        `${apiBaseUrl}/builder/layer/attributes/select?crtfckey=${apiKey}`,\n        {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            crtfckey: apiKey,\n          },\n          body: JSON.stringify({\n            typeName: `${namespace}:${cntntsId}`,\n            pageIndex: 1,\n            pageSize: 3,\n          }),\n        }\n      );\n\n      if (!dataResponse.ok) {\n        throw new Error(\n          `속성 데이터 조회 실패: ${dataResponse.status} ${dataResponse.statusText}`\n        );\n      }\n\n      const attributeData =\n        (await dataResponse.json()) as LayerAttributesResponse;\n\n      if (\n        !columnData.code ||\n        columnData.code !== 200 ||\n        !attributeData.code ||\n        attributeData.code !== 200\n      ) {\n        return { error: \"레이어 정보를 찾을 수 없습니다.\" };\n      }\n\n      // 컬럼 정보와 속성 데이터를 함께 반환\n      return {\n        columns: columnData.result.map((col) => ({\n          name: col.columnNm,\n          description: col.columnNcm,\n          type: col.dataTy,\n          editable: col.editPosblAt === \"Y\",\n          required: col.esntlAt === \"Y\",\n          minValue: col.mummLt,\n          maxValue: col.mxmmLt,\n          groupCode: col.cmmnGroupCode,\n          groupName: col.cmmnGroupCodeNm,\n        })),\n        data: attributeData.result.features.map(\n          (feature) => feature.properties\n        ),\n      };\n    } catch (error: any) {\n      console.error(\"Error fetching layer information:\", error);\n      return {\n        error: `레이어 속성 조회 중 오류가 발생했습니다: ${error.message}`,\n      };\n    }\n  },\n});\n\n// 레이어 정보 응답 타입 정의\ninterface LayerInfoResponse {\n  code: number;\n  message: string;\n  result: {\n    lyrId: string; // 레이어ID\n    cntntsId?: string; // 콘텐츠일련번호\n    jobClCode: string; // 업무구분코드\n    jobClCodeNm: string; // 업무구분코드명\n    lyrNm: string; // 레이어명\n    lyrClCode: string; // 레이어분류코드\n    lyrClCodeNm: string; // 레이어분류코드명\n    lyrClSeCode: string; // 레이어분류구분코드\n    lyrClSeCodeNm: string; // 레이어분류구분코드명\n    lyrPosesnSeCode: string; // 레이어소유구분코드\n    lyrPosesnSeCodeNm: string; // 레이어소유구분코드명\n    lyrTySeCode: string; // 레이어유형구분코드\n    lyrTySeCodeNm: string; // 레이어유형구분코드명\n    svcTySeCode?: string; // 서비스유형구분코드\n    svcTySeCodeNm?: string; // 서비스유형구분코드명\n    cntmSeCode: string; // 좌표계구분코드\n    cntmSeCodeNm: string; // 좌표계구분코드명\n    usePblonsipSeCode: string; // 사용공유구분코드\n    usePblonsipSeCodeNm: string; // 사용공유구분코드명\n    useSttusSeCode: string; // 사용상태구분코드\n    useSttusSeCodeNm: string; // 사용상태구분코드명\n    holdDataId?: string; // 보유데이터ID\n    lyrDc?: string; // 레이어설명\n    mapUrl?: string; // 지도url\n    mapUrlParamtr?: string; // 지도url파라미터\n    xyOrdrNrmltAt?: string; // XY순서정상여부\n    registerId: string; // 등록자ID\n    registerNm?: string; // 등록자명\n    ownerId: string; // 소유자 ID\n    registDt: string; // 등록일자\n    updusrId?: string; // 수정자ID\n    updusrNm?: string; // 수정자명\n    updtDt?: string; // 수정일자\n    lyrNcm?: string; // 레이어별칭\n    insttCode?: string; // 기관코드\n    tableLayer?: boolean; // 테이블 레이어 여부\n    symbolCndCn?: string; // 심볼 조건\n  };\n}\n\n// 레이어 컨텐츠 응답 타입 정의\ninterface LayerContentResponse {\n  code: number;\n  message: string;\n  result: {\n    apiUseSn: string; // API사용일련번호\n    cntntsId: string; // 콘텐츠아이디\n    lyrNm: string; // 레이어명칭\n    lyrClCode: string; // 레이어분류코드\n    lyrClSeCode: string; // 레이어분류구분코드\n    lyrPosesnSeCode: string; // 레이어소유구분코드\n    lyrTySeCode: string; // 레이어유형구분코드\n    cntmSeCode: string; // 좌표계구분코드\n    lyrOpertSpcNm: string; // 레이어작업공간명\n    opertUsrId: string; // 작업사용자아이디\n    registDt: string; // 등록일시\n    updtDt: string; // 수정일시\n    namespace: string; // 네임스페이스\n  };\n}\n\nexport const createLayerFilter = tool({\n  description: `레이어의 속성정보를 기반으로 CQL 필터를 생성합니다.\n  \n  필드 선택 가이드:\n  - getLayerAttributes 도구로 조회한 properties의 필드명을 사용하세요\n  - 필드명은 정확히 일치해야 합니다 (대소문자 구분)\n  \n  연산자 사용 가이드:\n  - 텍스트: LIKE (부분일치), = (완전일치)\n  - 숫자: >, >=, <, <=, =\n  - 목록: IN\n  \n  Args:\n    lyr_id: 필터 적용할 레이어 ID\n    field_name: properties에서 확인된 정확한 필드명\n    operator: 연산자 (=, >, <, >=, <=, LIKE, IN)\n    value: 필터 값\n    filter_type: \"exact\" 또는 \"partial\"\n     `,\n  parameters: z.object({\n    lyrId: z.string().min(1).describe(\"필터를 적용할 레이어 ID\"),\n    fieldName: z.string().min(1).describe(\"필터링할 필드명 (properties의 key)\"),\n    operator: z\n      .enum([\"=\", \">\", \"<\", \">=\", \"<=\", \"LIKE\", \"IN\"])\n      .describe(\"비교 연산자\"),\n    value: z.string().min(1).describe(\"필터 값\"),\n    filterType: z\n      .enum([\"exact\", \"partial\"])\n      .optional()\n      .default(\"exact\")\n      .describe(\"필터 타입\"),\n  }),\n  execute: async ({\n    lyrId,\n    fieldName,\n    operator,\n    value,\n    filterType = \"exact\",\n  }) => {\n    try {\n      // 공백과 따옴표 제거\n      fieldName = fieldName.trim().replace(/['\"]/g, \"\");\n\n      let filterStr;\n      if (operator.toUpperCase() === \"LIKE\") {\n        filterStr = `\"${fieldName}\" LIKE '%${value}%'`;\n      } else if (operator.toUpperCase() === \"IN\") {\n        const values = value.split(\",\").map((v) => v.trim());\n        const valueStr = values.map((v) => `'${v}'`).join(\",\");\n        filterStr = `\"${fieldName}\" IN (${valueStr})`;\n      } else {\n        const isNumeric = !isNaN(Number(value));\n        filterStr = `\"${fieldName}\"${operator}${isNumeric ? value : `'${value}'`}`;\n      }\n\n      return {\n        lyr_id: lyrId,\n        filter: filterStr,\n        description: `필드 '${fieldName}'에 대해 ${operator} '${value}' 조건으로 필터링합니다.`,\n      };\n    } catch (error: any) {\n      return { error: `필터 생성 실패: ${error.message}` };\n    }\n  },\n});\n\nconst GeometryType = z.enum([\"point\", \"line\", \"polygon\"]);\nconst ServiceType = z.enum([\"wms\", \"wfs\"]);\n\nexport const createVectorStyle = tool({\n  description: `\n    자연어 요청을 분석하여 OpenLayers Flat 벡터 스타일을 생성합니다.\n    Point\", \"LineString\", \"Polygon\", \"Icon\" 타입을 사용할 수 있습니다.\n  `,\n  parameters: z.object({\n    lyrId: z.string().describe(\"스타일이 적용될 레이어 ID\"),\n    styleRequest: z.string().describe(\"자연어 스타일 요청\"),\n    iconRequest: z.string().describe(`\n      아이콘 스타일 요청\n      예시: \n      '스타벅스': '스타벅스 로고, 녹색 원 안에 긴머리의 왕관을 쓴 하얀색 인어 형상이 보이는 모습'\n      '카페': '갈색 커피 컵 모양에 김이 올라오는 모습'\n      '식당': '포크와 나이프가 교차된 형태의 식당 아이콘'\n      '병원': '흰색 배경에 빨간색 십자가 의료 기호'\n      '기본': '건물 모양'\n      `),\n    geometryType: z\n      .enum([\"Icon\", \"Point\", \"LineString\", \"Polygon\"])\n      .describe(\"도형 타입\"),\n  }),\n  execute: async ({ lyrId, styleRequest, iconRequest, geometryType }) => {\n    try {\n      // 기본 텍스트 스타일 스키마\n      const textStyle = z\n        .object({\n          \"text-value\": z.string().optional().describe(\"텍스트 내용\"),\n          \"text-font\": z.string().optional().describe(\"폰트 스타일 (CSS 형식)\"),\n          \"text-fill-color\": z.string().optional().describe(\"텍스트 색상\"),\n          \"text-stroke-color\": z\n            .string()\n            .optional()\n            .describe(\"텍스트 테두리 색상\"),\n          \"text-stroke-width\": z\n            .number()\n            .optional()\n            .describe(\"텍스트 테두리 두께\"),\n          \"text-scale\": z.number().optional().describe(\"텍스트 크기 배율\"),\n          \"text-offset-x\": z.number().optional().describe(\"텍스트 X축 오프셋\"),\n          \"text-offset-y\": z.number().optional().describe(\"텍스트 Y축 오프셋\"),\n          \"text-rotation\": z\n            .number()\n            .optional()\n            .describe(\"텍스트 회전각도 (라디안)\"),\n          \"text-background-fill-color\": z\n            .string()\n            .optional()\n            .describe(\"텍스트 배경 색상\"),\n          \"text-padding\": z\n            .array(z.number())\n            .length(4)\n            .optional()\n            .describe(\"텍스트 패딩 [상,우,하,좌]\"),\n        })\n        .optional();\n\n      // 기본 스타일 속성\n      const baseStyle = z.object({\n        \"z-index\": z.number().optional().describe(\"스타일 z-index\"),\n      });\n\n      // 도형 타입별 스키마 정의\n      const styleSchemas = {\n        Point: z.object({\n          style: z\n            .array(\n              z.union([\n                baseStyle.extend({\n                  \"circle-radius\": z.number().optional(),\n                  \"circle-fill-color\": z.string().optional(),\n                  \"circle-stroke-color\": z.string().optional(),\n                  \"circle-stroke-width\": z.number().optional(),\n                  \"circle-scale\": z.number().optional(),\n                }),\n                textStyle,\n              ])\n            )\n            .describe(\"점 스타일 배열\"),\n        }),\n        LineString: z.object({\n          style: z\n            .array(\n              z.union([\n                baseStyle.extend({\n                  \"stroke-color\": z.string().optional(),\n                  \"stroke-width\": z.number().optional(),\n                  \"stroke-line-cap\": z\n                    .enum([\"butt\", \"round\", \"square\"])\n                    .optional(),\n                  \"stroke-line-dash\": z.array(z.number()).optional(),\n                  \"stroke-line-join\": z\n                    .enum([\"bevel\", \"round\", \"miter\"])\n                    .optional(),\n                }),\n                textStyle,\n              ])\n            )\n            .describe(\"선 스타일 배열\"),\n        }),\n        Polygon: z.object({\n          style: z\n            .array(\n              z.union([\n                baseStyle.extend({\n                  \"fill-color\": z.string().optional(),\n                  \"fill-pattern-src\": z.string().optional(),\n                  \"stroke-color\": z.string().optional(),\n                  \"stroke-width\": z.number().optional(),\n                }),\n                textStyle,\n              ])\n            )\n            .describe(\"폴리곤 스타일 배열\"),\n        }),\n        Icon: z.object({\n          style: z\n            .array(\n              z.union([\n                baseStyle.extend({\n                  \"icon-src\": z.string(),\n                  \"icon-scale\": z.number().optional(),\n                  \"icon-anchor\": z.array(z.number()).length(2).optional(),\n                  \"icon-color\": z.string().optional(),\n                  \"icon-rotation\": z.number().optional(),\n                  \"icon-opacity\": z.number().optional(),\n                }),\n                textStyle,\n              ])\n            )\n            .describe(\"아이콘 스타일 배열\"),\n        }),\n      };\n\n      // LLM을 통한 스타일 생성\n      const result = await generateObject({\n        model: geon(\"qwen2.5:32b\"),\n        // system: `You are a style generator for OpenLayers maps that creates Flat style configurations.\n        //         Use valid color formats (rgb, rgba, hex).\n        //         Consider common mapping conventions and visual hierarchy.\n        //         Text styles should be readable and properly positioned, but only include text styles for Point, LineString, and Polygon geometry types.\n        //         For Icon geometry types, do not include any text styles or labels.\n        //         `,\n        prompt: `Generate a style array for a ${geometryType} layer with possible text labels based on this request: ${styleRequest}\n                Consider including appropriate text styling if the request implies labels are needed.\n                For Icon geometry types, do not include any text styles or labels\n                `,\n        schema: styleSchemas[geometryType],\n      });\n\n      if (geometryType === \"Icon\") {\n        // SVG 생성 프롬프트\n        const svgPrompt = `\n        Design a simple, clean SVG icon for a \"${iconRequest}\" at 32x32 pixels resolution.\n\n        Follow these specifications:\n        - Use minimal details but make it clearly recognizable\n        - Include appropriate colors for the subject\n        - Ensure shapes are well-defined with clean outlines\n        - Optimize for small size display\n        - Use only vector elements (no bitmap/raster)\n        - No text elements in the icon\n\n        For a \"스타벅스\" (Starbucks) icon:\n        - Include the circular green logo with simplified mermaid/siren silhouette\n        - Use the iconic Starbucks green (#00704A)\n\n        For a generic \"카페\" (cafe) icon:\n        - Include a simple coffee cup with steam\n        - Use warm brown tones (#8B4513 or similar)\n\n        Output only valid SVG code without comments, XML declarations or any additional text.\n        `;\n        // SVG 코드 생성\n        const svgResponse = await generateText({\n          model: geon(\"qwen2.5:32b\"),\n          prompt: svgPrompt,\n        });\n\n        let svgCode = svgResponse.text\n          .replace(/```xml|```svg|```/g, \"\") // 마크다운 코드 블록 제거\n          .replace(/<\\?xml[^>]*\\?>/g, \"\") // XML 선언 제거\n          .replace(/<!DOCTYPE[^>]*>/g, \"\") // DOCTYPE 제거\n          .trim();\n\n        // SVG 태그가 없는 경우 감싸기\n        if (!svgCode.includes(\"<svg\")) {\n          svgCode = `<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"32\" viewBox=\"0 0 32 32\">${svgCode}</svg>`;\n        }\n\n        // 크기가 명시되지 않은 경우 추가\n        if (!svgCode.includes(\"width=\") || !svgCode.includes(\"height=\")) {\n          svgCode = svgCode.replace(\"<svg\", '<svg width=\"32\" height=\"32\"');\n        }\n\n        // 뷰포트가 없는 경우 추가\n        if (!svgCode.includes(\"viewBox=\")) {\n          svgCode = svgCode.replace(\"<svg\", '<svg viewBox=\"0 0 32 32\"');\n        }\n\n        // xmlns 속성이 없는 경우 추가\n        if (!svgCode.includes(\"xmlns=\")) {\n          svgCode = svgCode.replace(\n            \"<svg\",\n            '<svg xmlns=\"http://www.w3.org/2000/svg\"'\n          );\n        }\n\n        console.log(\"정제된 SVG 코드:\", svgCode);\n\n        // SVG 코드를 데이터 URI로 변환\n        const svgBase64 = Buffer.from(svgCode).toString(\"base64\");\n        const dataUri = `data:image/svg+xml;base64,${svgBase64}`;\n\n        // 생성된 데이터 URI로 스타일 업데이트\n        const json = (await result.toJsonResponse().json()) as any;\n        json.style = json.style.map((style: any) => {\n          if (style[\"icon-src\"]) {\n            style[\"icon-src\"] = dataUri;\n          }\n          return style;\n        });\n\n        return {\n          lyrId,\n          geometryType,\n          style: json.style,\n        };\n      }\n\n      const json = (await result.toJsonResponse().json()) as any;\n\n      return {\n        lyrId,\n        geometryType,\n        style: json.style,\n      };\n    } catch (error: any) {\n      throw new Error(`스타일 생성 중 오류가 발생했습니다: ${error.message}`);\n    }\n  },\n});\n\n// WFS 스타일 스키마\nconst wfsStyleSchema = z.object({\n  image: z.object({\n    circle: z\n      .object({\n        radius: z.number(),\n        fill: z.object({\n          color: z.tuple([z.number(), z.number(), z.number(), z.number()]),\n        }),\n        stroke: z.object({\n          color: z.tuple([z.number(), z.number(), z.number(), z.number()]),\n          width: z.number(),\n          lineDash: z.array(z.number()).optional(),\n        }),\n        scale: z.number().optional(),\n      })\n      .optional(),\n    icon: z\n      .object({\n        src: z.string(),\n        scale: z.number(),\n        rotation: z.number(),\n        opacity: z.number(),\n      })\n      .optional(),\n  }),\n  // 나머지 line, polygon 등의 스키마도 추가\n});\n\nexport const searchDirections = tool({\n  description: `하나의 출발지에서 하나의 목적지까지의 경로에 대한 상세 정보를 제공합니다.\n    경유지는 최대 5개까지 추가할 수 있으며, 모든 경유지를 포함한 경로의 총 거리는 1,500km 미만이어야 합니다.`,\n  parameters: z.object({\n    origin: z\n      .string()\n      .min(1)\n      .describe(\n        '출발지 좌표 (예: ${X좌표},${Y좌표},name=${출발지명} 또는 ${X좌표},${Y좌표})'\n      ),\n    destination: z\n      .string()\n      .min(1)\n      .describe(\n        '목적지 좌표 (예: ${X좌표},${Y좌표},name=${목적지명} 또는 ${X좌표},${Y좌표})'\n      ),\n    waypoints: z\n      .string()\n      .optional()\n      .describe(\n        '경유지 좌표 (최대 5개, | 로 구분, 예: \"127.1,37.4|127.2,37.5\")'\n      ),\n    priority: z\n      .enum([\"RECOMMEND\", \"TIME\", \"DISTANCE\"])\n      .default(\"RECOMMEND\")\n      .describe(\"경로 탐색 우선순위\"),\n    avoid: z\n      .string()\n      .optional()\n      .describe(\n        \"회피 옵션 (ferries, toll, motorway, schoolzone, uturn, 쉼표로 구분)\"\n      ),\n    roadevent: z\n      .number()\n      .int()\n      .default(0)\n      .describe(\n        \"도로 통제 정보 반영 (0: 전체, 1: 출발/도착지 제외, 2: 미반영)\"\n      ),\n    alternatives: z.boolean().default(false).describe(\"대안 경로 제공 여부\"),\n    roadDetails: z\n      .boolean()\n      .default(false)\n      .describe(\"상세 도로 정보 제공 여부\"),\n    carType: z.number().int().default(1).describe(\"차종\"),\n    carFuel: z\n      .enum([\"GASOLINE\", \"DIESEL\", \"LPG\"])\n      .default(\"GASOLINE\")\n      .describe(\"유종\"),\n    carHipass: z.boolean().default(false).describe(\"하이패스 장착 여부\"),\n    summary: z.boolean().default(false).describe(\"요약 정보 제공 여부\"),\n  }),\n  execute: async ({\n    origin,\n    destination,\n    waypoints,\n    priority,\n    avoid,\n    roadevent,\n    alternatives,\n    roadDetails,\n    carType,\n    carFuel,\n    carHipass,\n    summary,\n  }) => {\n    const url = \"https://apis-navi.kakaomobility.com/v1/directions\";\n    const params: Record<string, any> = {\n      origin,\n      destination,\n      priority,\n      car_fuel: carFuel,\n      car_hipass: carHipass,\n      alternatives,\n      road_details: roadDetails,\n    };\n\n\n    console.log(\"전달받은 길찾기 파라미터:\", params);\n\n    if (waypoints) params.waypoints = waypoints;\n    if (avoid) params.avoid = avoid;\n    if (roadevent) params.roadevent = roadevent;\n    if (carType) params.car_type = carType;\n    if (summary) params.summary = summary;\n\n    const headers = {\n      Authorization: `KakaoAK ${process.env.KAKAO_REST_API_KEY}`,\n    };\n\n    try {\n      const response = (await fetch(`${url}?${new URLSearchParams(params)}`, {\n        method: \"GET\",\n        headers,\n      })) as any;\n\n      const result = (await response.json()) as any;\n      console.log(result);\n\n      return {\n        trans_id: result.trans_id,\n        routes: result.routes || [],\n        result_code: result.result_code,\n        result_msg: result.result_msg,\n        summary: result.summary || {},\n        origin: result.origin || {},\n        destination: result.destination || {},\n        waypoints: result.waypoints || [],\n        priority: result.priority,\n        bound: result.bound || {},\n        fare: result.fare || {},\n        distance: result.distance,\n        duration: result.duration,\n        sections: result.sections || [],\n      };\n    } catch (error: any) {\n      console.error(`Error making request: ${error.message}`);\n      return { error: `경로 탐색 실패: ${error.message}` };\n    }\n  },\n});\n", "import { tool } from \"ai\";\nimport { z } from \"zod\";\n\nexport const performDensityAnalysis = tool({\n    description: `특정 영역 내에서 포인트 데이터의 밀도를 계산합니다.\n    - 인구 밀도, 범죄 발생률 등 포인트 기반 데이터의 분포를 분석하는 데 사용됩니다.\n    Args:\n      userId: 사용자 ID (예: \"geonuser\")\n      trgetTypeName: 분석 대상 레이어 ID (예: \"geonpaas:L200000007\")\n      lyrNm: 결과 레이어 이름 (선택, 기본값: \"밀도 분석 결과\")\n    `,\n    parameters: z.object({\n      userId: z.string().describe('사용자 ID'),\n      trgetTypeName: z.string().describe('분석 대상 레이어 ID'),\n      lyrNm: z.string().optional().describe('결과 레이어 이름 (선택)'),\n    }),\n    async execute({ userId, trgetTypeName, lyrNm }) {\n      try {\n        const apiBaseUrl = 'https://gsapi.geon.kr/analysis';\n\n        const params = new URLSearchParams({\n          opertUsrId: userId,\n          trgetCode: '102',\n          trgetTypeName,\n          densityOption: 'simple',\n          lyrNm: lyrNm || '밀도 분석 결과',\n          direct: 'true',\n        });\n\n        const response = await fetch(`${apiBaseUrl}/anals/pttrn/density?${params}`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            crtfckey: process.env.GEON_API_KEY || '',\n          }\n        });\n  \n        const data: any = await response.json();\n\n        if (!response.ok) {\n          throw new Error(`API 요청 실패: 상태 코드 ${response.status} ${response.statusText}`);\n        }\n  \n        return data.result; // 결과 반환\n      } catch (error: any) {\n        return { error: `밀도 분석 실패: ${error.message}` };\n      }\n    },\n  });\n  \n  export const performBufferAnalysis = tool({\n    description: `주어진 도형 주위에 지정된 거리만큼의 버퍼를 생성합니다.\n    - 특정 지역 주변의 영향을 분석하거나, 지정된 거리 내의 객체를 식별하는 데 사용됩니다.\n    Args:\n      userId: 사용자 ID (예: \"geonuser\")\n      trgetTypeName: 분석 대상 레이어 ID (예: \"geonpaas:L200000007\")\n      bufferDistance: 버퍼 거리 (미터 단위, 예: 500)\n      lyrNm: 결과 레이어 이름 (선택, 기본값: \"버퍼 분석 결과\")\n    `,\n    parameters: z.object({\n      userId: z.string().describe('사용자 ID'),\n      trgetTypeName: z.string().describe('분석 대상 레이어 ID'),\n      bufferDistance: z.number().describe('버퍼 거리 (미터)'),\n      lyrNm: z.string().optional().describe('결과 레이어 이름 (선택)'),\n    }),\n    async execute({ userId, trgetTypeName, bufferDistance, lyrNm }) {\n      try {\n        const apiBaseUrl = 'https://gsapi.geon.kr/analysis'; // API 문서의 서버 URL\n        const params = new URLSearchParams({\n          crtfckey: process.env.GEON_API_KEY || '', // 환경 변수에서 인증키 가져오기\n          opertUsrId: userId,\n          trgetCode: '102', // typeName을 사용하는 경우 기본값\n          trgetTypeName,\n          bufferDistance: bufferDistance.toString(),\n          lyrNm: lyrNm || '버퍼 분석 결과',\n          direct: 'true', // 동기 요청으로 즉시 결과 반환\n        });\n  \n        const response = await fetch(`${apiBaseUrl}/anals/proximity/buffer`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            crtfckey: process.env.GEON_API_KEY || '',\n          },\n          body: JSON.stringify({}), // 필요 시 추가 데이터 포함\n        });\n  \n        if (!response.ok) {\n          throw new Error(`API 요청 실패: 상태 코드 ${response.status}`);\n        }\n  \n        const data: any = await response.json();\n        return data.result; // 결과 반환\n      } catch (error: any) {\n        return { error: `버퍼 분석 실패: ${error.message}` };\n      }\n    },\n  });\n"], "names": ["tool", "z", "tool", "z", "tool", "z", "tool", "z", "tool", "z", "tool", "z", "tool", "z", "json", "tool", "z", "tool", "z"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,YAAY;AACrB,SAAS,SAAS;AIAlB,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AJiKd,IAAM,qQAAgB,OAAA,EAAK;IAChC,aAAa;IACb,oMAAY,IAAA,CAAE,MAAA,CAAO;QACnB,gMAAS,KAAA,CAAE,MAAA,CAAO;QAClB,6MAAqB,IAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS,EAAE,OAAA,CAAQ,KAAK;QACzD,oMAAY,IAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,OAAA,CAAQ,IAAI;QAC9C,cAAc,4LAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC,EAAE,GAAA,CAAI,EAAE,EAAE,QAAA,CAAS,EAAE,OAAA,CAAQ,EAAE;QAC7D,qMAAa,IAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS,EAAE,OAAA,CAAQ,CAAC;IACrD,CAAC;IACD,SAAS,CAAO,KAA4E,QAAA,MAAA;YAA5E;SAAA,EAA4E,UAA5E,EAAE,OAAA,EAAS,mBAAA,EAAqB,UAAA,EAAY,YAAA,EAAc,WAAA,CAAY,CAAA,EAAM;YA3K9F,IAAA,IAAA;YA4KI,IAAI;gBACF,MAAM,SAAS,QAAQ,GAAA,CAAI,YAAA;gBAC3B,MAAM,aAAa,QAAQ,GAAA,CAAI,iBAAA;gBAE/B,IAAI,CAAC,UAAU,CAAC,YAAY;oBAC1B,MAAM,IAAI,MAAM,sCAAsC;gBACxD;gBAEA,MAAM,eAAe,IAAI,gBAAgB;oBACvC,UAAU;oBACV;oBACA,qBAAqB,OAAO,mBAAmB;oBAC/C,YAAY,OAAO,UAAU;oBAC7B,cAAc,OAAO,YAAY;oBACjC,aAAa,OAAO,WAAW;gBACjC,CAAC;gBAED,MAAM,WAAW,MAAM,MACrB,GAAG,UAAU,CAAA,qBAAA,EAAwB,YAAY,EAAA,EACjD;oBACE,SAAS;wBACP,QAAQ;wBACR,UAAU;oBACZ;gBACF;gBAGF,IAAI,CAAC,SAAS,EAAA,EAAI;oBAChB,MAAM,IAAI,MAAM,CAAA,+BAAA,EAAc,SAAS,MAAM,CAAA,CAAA,EAAI,SAAS,UAAU,EAAE;gBACxE;gBAEA,MAAM,OAAwB,MAAM,SAAS,IAAA,CAAK;gBAElD,OAAO;YACT,EAAA,OAAS,OAAY;gBACnB,QAAQ,KAAA,CAAM,KAAK;gBACnB,IAAI,eAAe;gBAEnB,IAAA,CAAA,CAAI,KAAA,MAAM,QAAA,KAAN,OAAA,KAAA,IAAA,GAAgB,MAAA,MAAW,KAAK;oBAClC,eAAe;gBACjB,OAAA,IAAA,CAAA,CAAW,KAAA,MAAM,QAAA,KAAN,OAAA,KAAA,IAAA,GAAgB,MAAA,MAAW,KAAK;oBACzC,eAAe;gBACjB;gBAEA,OAAO;YACT;QACF;AACF,CAAC;;;ACvND,IAAM,UAAU;IACd,WAAW;IACX,SAAS;IACT,WAAW;IACX,WAAW;AACb;AA6EO,IAAM,sQAAiBE,OAAAA,EAAK;IACjC,aAAa,CAAA;;;;;;;GAAA,CAAA;IAQb,oMAAYC,IAAAA,CAAE,MAAA,CAAO,CAAC,CAAC;IACvB,SAAS,IAAY,QAAA,MAAA,MAAA;YACnB,OAAO;QACT;AACF,CAAC;AAEM,IAAM,qQAAgBD,OAAAA,EAAK;IAChC,aAAa,CAAA;;;;;0CAAA,CAAA;IAMb,oMAAYC,IAAAA,CAAE,MAAA,CAAO;QACnB,qMAAaA,IAAAA,CACV,MAAA,CAAO,EACP,GAAA,CAAI,CAAC,EACL,QAAA,CAAS,iIAA4C;IAC1D,CAAC;IACD,SAAS,CAAO,KAAoB,QAAA,MAAA;YAApB;SAAA,EAAoB,UAApB,EAAE,WAAA,CAAY,CAAA,EAAM;YAClC,MAAM,iBAAiB,CAAC,gBAAuC;gBAC7D,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,OAAO,EAAG;oBAClD,IAAI,MAAM,QAAA,CAAS,WAAW,GAAG;wBAC/B,OAAO;oBACT;gBACF;gBACA,OAAO;YACT;YAEA,MAAM,UAAU,eAAe,WAAW;YAC1C,IAAI,CAAC,SAAS;gBACZ,OAAO;oBAAE,OAAO;gBAAuB;YACzC;YAEA,OAAO;gBAAE;YAAQ;QACnB;AACF,CAAC;AAEM,IAAM,iQAAYD,OAAAA,EAAK;IAC5B,aAAa,CAAA;;;;uCAAA,CAAA;IAKb,oMAAYC,IAAAA,CAAE,MAAA,CAAO;QACnB,2LAAGA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAA,GAAI,EAAE,GAAA,CAAI,GAAG,EAAE,QAAA,CAAS,wCAAoB;QAC9D,2LAAGA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAA,EAAG,EAAE,GAAA,CAAI,EAAE,EAAE,QAAA,CAAS,sCAAkB;IAC5D,CAAC;IACD,SAAS,CAAO,KAAa,QAAA,MAAA;YAAb;SAAA,EAAa,UAAb,EAAE,CAAA,EAAG,CAAA,CAAE,CAAA,EAAM;YAC3B,OAAO;gBAAE;gBAAG;YAAE;QAChB;AACF,CAAC;AAEM,IAAM,yQAAoBD,OAAAA,EAAK;IACpC,aAAa,CAAA;;;;;;;mCAAA,CAAA;IAQb,oMAAYC,IAAAA,CAAE,MAAA,CAAO;QACnB,iMAAUA,KAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS,gCAAO;QAC5C,mMAAWA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS,0BAAM;QAC5C,8LAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS,kDAAmC;IACtE,CAAC;IACD,SAAS,CAAO,KAAkC,QAAA,MAAA;YAAlC;SAAA,EAAkC,UAAlC,EAAE,QAAA,EAAU,SAAA,EAAW,IAAA,CAAK,CAAA,EAAM;YAChD,OAAO;gBAAE;gBAAU;gBAAW;YAAK;QACrC;AACF,CAAC;;;ACtGM,IAAM,iBAAgBG,2PAAAA,EAAK;IAChC,aAAa;IACb,mMAAYC,KAAAA,CAAE,MAAA,CAAO;QACnB,gMAAQA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,8HAAuE;QACnG,qMAAaA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,8HAAuE;QACxG,mMAAWA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS,+EAAwB;QAClE,kMAAUA,IAAAA,CAAE,IAAA,CAAK;YAAC;YAAa;YAAQ,UAAU;SAAC,EAAE,QAAA,CAAS,EAAE,OAAA,CAAQ,WAAW,EAAE,QAAA,CAAS,oDAAY;QACzG,+LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS,wEAAoD;QAC1F,mMAAWA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,OAAA,CAAQ,CAAC,EAAE,QAAA,CAAS,+IAA2C;QAChG,sMAAcA,IAAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS,EAAE,OAAA,CAAQ,KAAK,EAAE,QAAA,CAAS,qDAAa;QAC1E,sMAAcA,IAAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS,EAAE,OAAA,CAAQ,KAAK,EAAE,QAAA,CAAS,kEAAgB;QAC7E,kMAAUA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,OAAA,CAAQ,CAAC,EAAE,QAAA,CAAS,cAAI;QACxD,kMAAUA,IAAAA,CAAE,IAAA,CAAK;YAAC;YAAY;YAAU,KAAK;SAAC,EAAE,QAAA,CAAS,EAAE,OAAA,CAAQ,UAAU,EAAE,QAAA,CAAS,cAAI;QAC5F,oMAAYA,IAAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS,EAAE,OAAA,CAAQ,KAAK,EAAE,QAAA,CAAS,oDAAY;QACvE,iMAASA,IAAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS,EAAE,OAAA,CAAQ,KAAK,EAAE,QAAA,CAAS,qDAAa;IACvE,CAAC;IACD,SAAS,CAAO,KAAwI,QAAA,MAAA;YAAxI;SAAA,EAAwI,UAAxI,EAAE,MAAA,EAAQ,WAAA,EAAa,SAAA,EAAW,QAAA,EAAU,KAAA,EAAO,SAAA,EAAW,YAAA,EAAc,YAAA,EAAc,QAAA,EAAU,QAAA,EAAU,UAAA,EAAY,OAAA,CAAQ,CAAA,EAAM;YACtJ,IAAI;gBACF,MAAM,SAAS,QAAQ,GAAA,CAAI,kBAAA;gBAC3B,IAAI,CAAC,QAAQ;oBACX,MAAM,IAAI,MAAM,+BAA+B;gBACjD;gBAEA,MAAM,MAAM;gBACZ,MAAM,SAAS,IAAI,gBAAgB;oBACjC;oBACA;oBACA;oBACA;oBACA,YAAY,OAAO,UAAU;oBAC7B,cAAc,OAAO,YAAY;oBACjC,cAAc,OAAO,YAAY;gBACnC,CAAC;gBAED,IAAI,UAAW,CAAA,OAAO,MAAA,CAAO,aAAa,SAAS;gBACnD,IAAI,MAAO,CAAA,OAAO,MAAA,CAAO,SAAS,KAAK;gBACvC,IAAI,UAAW,CAAA,OAAO,MAAA,CAAO,aAAa,OAAO,SAAS,CAAC;gBAC3D,IAAI,SAAU,CAAA,OAAO,MAAA,CAAO,YAAY,OAAO,QAAQ,CAAC;gBACxD,IAAI,QAAS,CAAA,OAAO,MAAA,CAAO,WAAW,OAAO,OAAO,CAAC;gBAErD,MAAM,WAAW,MAAM,MAAM,GAAG,GAAG,CAAA,CAAA,EAAI,MAAM,EAAA,EAAI;oBAC/C,SAAS;wBACP,eAAe,CAAA,QAAA,EAAW,MAAM,EAAA;oBAClC;gBACF,CAAC;gBAED,IAAI,CAAC,SAAS,EAAA,EAAI;oBAChB,MAAM,IAAI,MAAM,CAAA,+BAAA,EAAc,SAAS,MAAM,CAAA,CAAA,EAAI,SAAS,UAAU,EAAE;gBACxE;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAA,CAAK;gBACnC,OAAO;oBACL,UAAU,OAAO,QAAA;oBACjB,QAAQ,OAAO,MAAA;oBACf,aAAa,OAAO,WAAA;oBACpB,YAAY,OAAO,UAAA;oBACnB,SAAS,OAAO,OAAA;oBAChB,QAAQ,OAAO,MAAA;oBACf,aAAa,OAAO,WAAA;oBACpB,WAAW,OAAO,SAAA;oBAClB,UAAU,OAAO,QAAA;oBACjB,OAAO,OAAO,KAAA;oBACd,MAAM,OAAO,IAAA;oBACb,UAAU,OAAO,OAAA,CAAQ,QAAA;oBACzB,UAAU,OAAO,OAAA,CAAQ,QAAA;oBACzB,UAAU,OAAO,QAAA;gBACnB;YACF,EAAA,OAAS,OAAO;gBACd,OAAO,CAAA,wCAAA,EAAa,KAAK,EAAA;YAC3B;QACF;AACF,CAAC;;;ACjIM,IAAM,0QAAoBC,OAAAA,EAAK;IAClC,aAAa;IACb,mMAAYC,KAAAA,CAAE,MAAA,CAAO;QACnB,iMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,sCAAsC;IACrE,CAAC;AACH,CAAC;AAEI,IAAM,cAAaD,4PAAAA,EAAK;IAC3B,aAAa;IACb,oMAAYC,IAAAA,CAAE,MAAA,CAAO;QACnB,kMAAUA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,uCAAuC;IACvE,CAAC;AACL,CAAC;AAEM,IAAM,oQAAeD,OAAAA,EAAK;IAC/B,aACE;IACF,oMAAYC,IAAAA,CAAE,MAAA,CAAO;QACnB,iMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,6EAAiB;QAC9C,iMAASA,IAAAA,CACN,KAAA,yLAAMA,IAAAA,CAAE,MAAA,CAAO,CAAC,EAChB,GAAA,CAAI,CAAC,EACL,QAAA,CAAS,8GAAyB;IACvC,CAAC;AACH,CAAC;AAEM,IAAM,oQAAeD,OAAAA,EAAK;IAC/B,aACE;IACF,mMAAYC,KAAAA,CAAE,MAAA,CAAO;QACnB,iMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,2FAAqB;QAClD,iMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS,+CAAY,EAAE,OAAA,CAAQ,EAAE;IAClE,CAAC;AACH,CAAC;AAEM,IAAM,uBAAsBD,2PAAAA,EAAK;IACtC,aACE;IACF,oMAAYC,IAAAA,CAAE,MAAA,CAAO;QACnB,iMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,gEAAc;QAC3C,+LAAOA,IAAAA,CACJ,MAAA,CAAO,EACP,QAAA,CAAS,6KAAsC;IACpD,CAAC;AACH,CAAC;;;;AC5CD,IAAM,iBAAiB;IACrB,UAAU;IACV,UAAU;AACZ;AAYA,IAAM,eAAe,CAAC,gBAAqC;IACzD,MAAM,UAAU,GAAG,QAAQ,GAAA,CAAI,iBAAiB,CAAA,aAAA,CAAA;IAChD,IAAI,gBAAgB,aAAa,gBAAgB,WAAW;QAC1D,OAAO,GAAG,OAAO,CAAA,GAAA,CAAA;IACnB,OAAA,IAAW,gBAAgB,SAAS;QAClC,OAAO,GAAG,OAAO,CAAA,GAAA,CAAA;IACnB;IACA,OAAO,GAAG,OAAO,GAAG,WAAW,EAAA;AACjC;AAoCA,IAAM,uBAAuB,CAC3B,WACA,WACA,aACA,gBACgB;IAChB,MAAM,cAAc,UAAU,MAAA,CAAO,WAAA;IACrC,IAAI,cAAc,YAAY,gBAAgB,KAAM,CAAA,OAAO;IAC3D,IAAI,cAAc,YAAY,gBAAgB,KAAM,CAAA,OAAO;IAC3D,IAAI,cAAc,YAAY,gBAAgB,QAAQ,gBAAgB,KACpE,OAAO;IACT,IAAI,gBAAgB,IAAK,CAAA,OAAO;IAChC,OAAO,gBAAgB,MAAM,QAAQ,gBAAgB,MAAM,SAAS;AACtE;AAEA,IAAM,kBAAkB,CAAC,gBAA4C;IACnE,OAAQ,aAAa;QACnB,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,KAAA;IACX;AACF;AAEA,IAAM,+BAA+B,CAAC,cAAmB;IACvD,MAAM,QAAQ,KAAK,KAAA,CAAM,UAAU,MAAA,CAAO,aAAA,IAAiB,IAAI;IAC/D,MAAM,QAAQ,UAAU,MAAA,CAAO,WAAA,GAC3B,KAAK,KAAA,CAAM,UAAU,MAAA,CAAO,WAAW,IACvC,KAAA;IACJ,OAAO;QACL,aAAa;YACX,QAAQ,cAAA,eAAA,CAAA,GACH,QADG;gBAEN,YAAY,CAAA,KAAA,EAAQ,UAAU,MAAA,CAAO,UAAU,EAAA;YACjD;QACF;QACA;QACA,WAAW;YACT,OAAO,UAAU,MAAA,CAAO,KAAA;YACxB,OAAO,UAAU,MAAA,CAAO,KAAA;YACxB,aAAa,UAAU,MAAA,CAAO,KAAA;QAChC;IACF;AACF;AAEA,IAAM,sBAAsB,CAAC,iBAA8B;IACzD,MAAM,cAAc,IAAM,KAAK,KAAA,CAAM,KAAK,MAAA,CAAO,IAAI,GAAG;IACxD,MAAM,QAAQ,CAAA,IAAA,EAAO,YAAY,CAAC,CAAA,EAAA,EAAK,YAAY,CAAC,CAAA,EAAA,EAAK,YAAY,CAAC,CAAA,CAAA,CAAA;IACtE,OAAO;QACL,aAAa;YACX;gBACE,eAAe;gBACf,OAAO;oBACL,cAAc;oBACd,OACE,iBAAiB,UACb;wBACE,QAAQ;4BACN,QAAQ;4BACR,MAAM;gCAAE;4BAAM;4BACd,QAAQ;gCAAE,OAAO;gCAAW,OAAO;4BAAE;wBACvC;oBACF,IACA,CAAC;oBACP,MAAM,iBAAiB,YAAY;wBAAE;oBAAM,IAAI,KAAA;oBAC/C,QAAQ;wBACN,OAAO,iBAAiB,SAAS,QAAQ;wBACzC,OAAO;oBACT;gBACF;YACF;SACF;QACA,aAAa;IACf;AACF;AAEO,IAAM,WAAWG,4PAAAA,EAAK;IAC3B,aAAa,CAAA;0KAAA,CAAA;IAEb,oMAAYC,IAAAA,CAAE,MAAA,CAAO;QACnB,gMAAQA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,uBAAQ;QACpC,mMAAWA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,2BAAO;QACtC,oMAAYA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,8CAAW;QAC3C,OAAOA,4LAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,uBAAQ;IACrC,CAAC;IACK,SAAQ,EAAA,EAA0C;QAAA,OAAA,QAAA,IAAA,EAAA,WAAA,UAA1C,EAAE,MAAA,EAAQ,SAAA,EAAW,UAAA,EAAY,KAAA,CAAM,CAAA,EAAG;YA3J1D,IAAA,IAAA,IAAA;YA4JI,IAAI;gBAEF,MAAM,SAAS,IAAI,gBAAgB;oBACjC,UAAU,QAAQ,GAAA,CAAI,YAAA,IAAgB;oBACtC;oBACA,kBAAkB;oBAClB,mBAAmB;oBACnB,eAAe;gBACjB,CAAC;gBAED,MAAM,WAAW,MAAM,MACrB,GAAG,QAAQ,GAAA,CAAI,iBAAiB,CAAA,uBAAA,EAA0B,OAAO,QAAA,CAAS,CAAC,EAAA,EAC3E;oBACE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,UAAU,QAAQ,GAAA,CAAI,YAAA,IAAgB;oBACxC;gBACF;gBAGF,IAAI,CAAC,SAAS,EAAA,EAAI;oBAChB,MAAM,IAAI,MAAM,CAAA,+BAAA,EAAkC,SAAS,MAAM,EAAE;gBACrE;gBAEA,MAAM,YAAa,MAAM,SAAS,IAAA,CAAK;gBAEvC,IAAI,CAAC,aAAa,CAAC,UAAU,MAAA,EAAQ;oBACnC,OAAO;wBAAE,OAAO,GAAG,KAAK,CAAA,yEAAA,CAAA;oBAAqB;gBAC/C;gBAGA,MAAM,gBAAgB,IAAI,gBAAgB;oBACxC,UAAU,QAAQ,GAAA,CAAI,YAAA,IAAgB;oBACtC,UAAU,UAAU,MAAA,CAAO,QAAA,IAAY;gBACzC,CAAC;gBAED,MAAM,kBAAkB,MAAM,MAC5B,GAAG,QAAQ,GAAA,CAAI,iBAAiB,CAAA,qBAAA,EAAwB,cAAc,QAAA,CAAS,CAAC,EAAA,EAChF;oBACE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,UAAU,QAAQ,GAAA,CAAI,YAAA,IAAgB;oBACxC;gBACF;gBAGF,IAAI,CAAC,gBAAgB,EAAA,EAAI;oBACvB,MAAM,IAAI,MACR,CAAA,+BAAA,EAAkC,gBAAgB,MAAM,EAAA;gBAE5D;gBAEA,MAAM,eAAgB,MAAM,gBAAgB,IAAA,CAAK;gBAEjD,IAAI,CAAC,gBAAgB,CAAC,aAAa,MAAA,EAAQ;oBACzC,OAAO;wBAAE,OAAO;oBAAyB;gBAC3C;gBAEA,MAAM,EAAE,SAAA,EAAW,WAAA,EAAa,WAAA,EAAa,WAAA,CAAY,CAAA,GACvD,UAAU,MAAA;gBAGZ,MAAM,cAAc,qBAClB,WACA,WACA,aACA;gBAEF,MAAM,eAAe,gBAAgB,WAAW;gBAGhD,IAAI,cAAc,eAAe,QAAA,IAAY,gBAAgB,MAAM;oBACjE,MAAM,iBAAiB,6BAA6B,SAAS;oBAC7D,OAAO,cAAA,eAAA;wBACL,IAAI,UAAU,MAAA,CAAO,KAAA;wBACrB,MAAM,UAAU,MAAA,CAAO,KAAA;wBACvB,MAAM;wBACN,SAAS;wBACT,QAAQ;wBACR,MAAM,eAAe,SAAA;oBAAA,GAClB,eAAe,WAAA,CAAY,MAAA,GAPzB;wBAQL,OAAO,eAAe,KAAA;oBACxB;gBACF;gBAGA,IAAI,cAA2B;oBAC7B,IAAI,UAAU,MAAA,CAAO,KAAA;oBACrB,MAAM,UAAU,MAAA,CAAO,KAAA;oBACvB,MAAM,gBAAgB,aAAa,aAAa;oBAChD,SAAS,UAAU,MAAA,CAAO,OAAA,KAAY;oBACtC,QAAQ;oBACR,QAAQ,aAAa,WAAW;oBAChC,OAAO,GAAG,aAAa,MAAA,CAAO,SAAS,CAAA,CAAA,EAAI,UAAU,MAAA,CAAO,QAAQ,EAAA;oBACpE,SAAS;oBACT,MAAM;oBACN,QAAQ,gBAAgB,SAAS,QAAQ;oBACzC,UAAU,QAAQ,GAAA,CAAI,YAAA;oBACtB,YAAY,CAAA,KAAA,EAAQ,UAAU,MAAA,CAAO,UAAU,EAAA;oBAC/C;oBACA,MAAM;wBACJ,OAAO,UAAU,MAAA,CAAO,KAAA;wBACxB,OAAO,UAAU,MAAA,CAAO,KAAA;wBACxB,aAAa,UAAU,MAAA,CAAO,KAAA;wBAC9B,UAAU;4BACR,UAAU,UAAU,MAAA,CAAO,QAAA;4BAC3B,WAAW,UAAU,MAAA,CAAO,SAAA;4BAC5B;4BACA;4BACA,WAAW,aAAa,MAAA,CAAO,SAAA;wBACjC;oBACF;oBACA,WAAW,aAAa,MAAA,CAAO,SAAA;oBAC/B,WACE,gBAAgB,SACZ,CAAA,KAAA,EAAQ,UAAU,MAAA,CAAO,UAAU,EAAA,GACnC,KAAA;gBACR;gBAGA,IAAI,gBAAgB,YAAY;oBAC9B,MAAM,iBAAiB,MAAM,MAC3B,GAAG,QAAQ,GAAA,CAAI,iBAAiB,CAAA,6BAAA,EAAgC,mBAAmB,UAAU,MAAA,CAAO,MAAM,CAAC,CAAA,OAAA,EAAU,KAAK,CAAA,UAAA,EAAa,QAAQ,GAAA,CAAI,YAAY,EAAA,EAC/J;wBAAE,QAAQ;wBAAO,SAAS;4BAAE,gBAAgB;wBAAmB;oBAAE;oBAEnE,MAAM,eAAgB,MAAM,eAAe,IAAA,CAAK;oBAChD,MAAM,SAAA,CAAS,KAAA,UAAU,MAAA,CAAO,MAAA,CAAO,KAAA,CAAM,GAAG,EAAE,GAAA,CAAI,CAAA,KAAvC,OAAA,KAAA,IAAA,GAA0C,WAAA;oBACzD,MAAM,SAAS,CAAA,WAAA,EAAc,MAAM,CAAA,QAAA,EAAA,CAAW,KAAA,gBAAA,OAAA,KAAA,IAAA,aAAc,MAAA,KAAd,OAAA,KAAA,IAAA,GAAsB,MAAM,EAAA;oBAE1E,cAAc,cAAA,eAAA,cAAA,eAAA,CAAA,GACT,cADS;wBAEZ,MAAM;wBACN,KAAK;oBAAA,IACD,UAAU,MAAA,CAAO,aAAA,IACnB,KAAK,KAAA,CAAM,UAAU,MAAA,CAAO,aAAa,IAL/B;wBAMZ,SACE,UAAU,MAAA,CAAO,WAAA,IAAA,CAAA,CACjB,KAAA,KAAK,KAAA,CAAM,UAAU,MAAA,CAAO,WAAW,CAAA,KAAvC,OAAA,KAAA,IAAA,GAA0C,OAAA;oBAC9C;gBACF;gBAGA,IAAI,QAAQ,UAAU,MAAA,CAAO,WAAA,GACzB,KAAK,KAAA,CAAM,UAAU,MAAA,CAAO,WAAW,IACvC,KAAA;gBACJ,IAAI,OAAO;oBACT,IAAI;wBAAC;wBAAU,SAAS;qBAAA,CAAE,QAAA,CAAS,MAAM,WAAW,GAAG;wBACrD,YAAY,KAAA,GAAQ,MAAM,WAAA;oBAC5B,OAAA,IAAW,MAAM,WAAA,KAAgB,SAAS;wBACxC,YAAY,KAAA,GAAQ,MAAM,WAAA;oBAC5B,OAAA,IAAW,MAAM,WAAA,KAAgB,WAAW;wBAC1C,cAAc,eAAA,eAAA,CAAA,GAAK,cAAgB,MAAM,WAAA;oBAC3C;oBACA,IAAI,MAAM,OAAA,CAAS,CAAA,YAAY,OAAA,GAAU,MAAM,OAAA;gBACjD,OAAA,IACE,gBAAgB,SAChB;oBAAC;oBAAK;oBAAK,GAAG;iBAAA,CAAE,QAAA,CAAS,WAAW,GACpC;oBACA,YAAY,KAAA,GAAQ,oBAAoB,YAAa;gBACvD,OAAA,IACE,gBAAgB,SAChB;oBAAC;oBAAK;oBAAK,GAAG;iBAAA,CAAE,QAAA,CAAS,WAAW,GACpC,CAEF;gBAGA,IACE,gBAAgB,OAChB,gBAAgB,OAChB,gBAAgB,OAChB,gBAAgB,OAChB,CAAA,CAAE,cAAc,YAAY,gBAAgB,IAAA,GAC5C;oBACA,YAAY,MAAA,GAAS,UAAU,MAAA,CAAO,UAAA;oBACtC,YAAY,cAAA,GAAiB,UAAU,MAAA,CAAO,cAAA;gBAChD;gBAEA,OAAO;YACT,EAAA,OAAS,OAAY;gBACnB,OAAO;oBAAE,OAAO,CAAA,2DAAA,EAAiB,MAAM,OAAO,EAAA;gBAAG;YACnD;QACF;IAAA;AACF,CAAC;AA+FM,IAAM,oQAAeD,OAAAA,EAAK;IAC/B,aAAa,CAAA;;;;;;;EAAA,CAAA;IAQb,oMAAYC,IAAAA,CAAE,MAAA,CAAO;QACnB,gMAAQA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,uBAAQ;QACpC,mMAAWA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS,yEAAkB;QAC5D,wMAAgBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS,6DAAgB;IACjE,CAAC;IACK,SAAQ,EAAA,EAAkD;QAAA,OAAA,QAAA,IAAA,EAAA,WAAA,UAAlD,EAAE,MAAA,EAAQ,YAAY,EAAA,EAAI,iBAAiB,GAAA,CAAI,CAAA,EAAG;YAC9D,IAAI;gBACF,MAAM,aAAa,QAAQ,GAAA,CAAI,iBAAA;gBAC/B,MAAM,SAAS,IAAI,gBAAgB;oBACjC,UAAU,QAAQ,GAAA,CAAI,YAAA,IAAgB;oBACtC;oBACA,WAAW;oBACX;oBACA,WAAW;oBACX,UAAU;gBACZ,CAAC;gBAED,MAAM,WAAW,MAAM,MACrB,GAAG,UAAU,CAAA,qBAAA,EAAwB,OAAO,QAAA,CAAS,CAAC,EAAA,EACtD;oBACE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,UAAU,QAAQ,GAAA,CAAI,YAAA,IAAgB;oBACxC;gBACF;gBAGF,IAAI,CAAC,SAAS,EAAA,EAAI;oBAChB,MAAM,IAAI,MAAM,CAAA,+BAAA,EAAkC,SAAS,MAAM,EAAE;gBACrE;gBAEA,MAAM,OAAQ,MAAM,SAAS,IAAA,CAAK;gBAElC,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAA,EAAQ;oBACzB,OAAO;wBAAE,OAAO;oBAA8B;gBAChD;gBAEA,OAAO;YACT,EAAA,OAAS,OAAY;gBACnB,OAAO;oBAAE,OAAO,CAAA,2DAAA,EAAiB,MAAM,OAAO,EAAA;gBAAG;YACnD;QACF;IAAA;AACF,CAAC;AAEM,IAAM,qBAAqBD,4PAAAA,EAAK;IACrC,aAAa,CAAA;;;;;;;;;;;;;;;6EAAA,CAAA;IAgBb,oMAAYC,IAAAA,CAAE,MAAA,CAAO;QACnB,gMAAQA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,uBAAQ;QACpC,OAAOA,4LAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS,uBAAQ;QAC1C,mMAAWA,IAAAA,CACR,MAAA,CAAO,EACP,GAAA,CAAI,CAAC,EACL,QAAA,CAAS,oFAA6B;QACzC,kMAAUA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS,wEAAsB;IAC7D,CAAC;IACD,SAAS,CAAO,KAA2C,QAAA,MAAA;YAA3C;SAAA,EAA2C,UAA3C,EAAE,MAAA,EAAQ,KAAA,EAAO,SAAA,EAAW,QAAA,CAAS,CAAA,EAAM;YACzD,IAAI;gBACF,MAAM,SAAS,QAAQ,GAAA,CAAI,YAAA;gBAC3B,MAAM,aAAa,QAAQ,GAAA,CAAI,iBAAA;gBAC/B,IAAI,CAAC,UAAU,CAAC,YAAY;oBAC1B,MAAM,IAAI,MAAM,yGAA8B;gBAChD;gBAGA,MAAM,iBAAiB,MAAM,MAC3B,GAAG,UAAU,CAAA,sCAAA,EAAyC,MAAM,CAAA,OAAA,EAAU,KAAK,CAAA,QAAA,EAAW,MAAM,EAAA,EAC5F;oBACE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,UAAU;oBACZ;gBACF;gBAGF,IAAI,CAAC,eAAe,EAAA,EAAI;oBACtB,MAAM,IAAI,MACR,CAAA,qDAAA,EAAgB,eAAe,MAAM,CAAA,CAAA,EAAI,eAAe,UAAU,EAAA;gBAEtE;gBAEA,MAAM,aAAc,MAAM,eAAe,IAAA,CAAK;gBAE9C,QAAQ,GAAA,CAAI,KAAK,SAAA,CAAU;oBACzB,UAAU,GAAG,SAAS,CAAA,CAAA,EAAI,QAAQ,EAAA;oBAClC,WAAW;oBACX,UAAU;gBACZ,CAAC,CAAC;gBAGF,MAAM,eAAe,MAAM,MACzB,GAAG,UAAU,CAAA,0CAAA,EAA6C,MAAM,EAAA,EAChE;oBACE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,UAAU;oBACZ;oBACA,MAAM,KAAK,SAAA,CAAU;wBACnB,UAAU,GAAG,SAAS,CAAA,CAAA,EAAI,QAAQ,EAAA;wBAClC,WAAW;wBACX,UAAU;oBACZ,CAAC;gBACH;gBAGF,IAAI,CAAC,aAAa,EAAA,EAAI;oBACpB,MAAM,IAAI,MACR,CAAA,2DAAA,EAAiB,aAAa,MAAM,CAAA,CAAA,EAAI,aAAa,UAAU,EAAA;gBAEnE;gBAEA,MAAM,gBACH,MAAM,aAAa,IAAA,CAAK;gBAE3B,IACE,CAAC,WAAW,IAAA,IACZ,WAAW,IAAA,KAAS,OACpB,CAAC,cAAc,IAAA,IACf,cAAc,IAAA,KAAS,KACvB;oBACA,OAAO;wBAAE,OAAO;oBAAqB;gBACvC;gBAGA,OAAO;oBACL,SAAS,WAAW,MAAA,CAAO,GAAA,CAAI,CAAC,MAAA,CAAS;4BACvC,MAAM,IAAI,QAAA;4BACV,aAAa,IAAI,SAAA;4BACjB,MAAM,IAAI,MAAA;4BACV,UAAU,IAAI,WAAA,KAAgB;4BAC9B,UAAU,IAAI,OAAA,KAAY;4BAC1B,UAAU,IAAI,MAAA;4BACd,UAAU,IAAI,MAAA;4BACd,WAAW,IAAI,aAAA;4BACf,WAAW,IAAI,eAAA;wBACjB,CAAA,CAAE;oBACF,MAAM,cAAc,MAAA,CAAO,QAAA,CAAS,GAAA,CAClC,CAAC,UAAY,QAAQ,UAAA;gBAEzB;YACF,EAAA,OAAS,OAAY;gBACnB,QAAQ,KAAA,CAAM,qCAAqC,KAAK;gBACxD,OAAO;oBACL,OAAO,CAAA,6GAAA,EAA2B,MAAM,OAAO,EAAA;gBACjD;YACF;QACF;AACF,CAAC;AAoEM,IAAM,yQAAoBD,OAAAA,EAAK;IACpC,aAAa,CAAA;;;;;;;;;;;;;;;;;KAAA,CAAA;IAkBb,mMAAYC,KAAAA,CAAE,MAAA,CAAO;QACnB,+LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS,6DAAgB;QAClD,kMAAWA,KAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS,oEAA4B;QAClE,kMAAUA,IAAAA,CACP,IAAA,CAAK;YAAC;YAAK;YAAK;YAAK;YAAM;YAAM;YAAQ,IAAI;SAAC,EAC9C,QAAA,CAAS,iCAAQ;QACpB,+LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS,qBAAM;QACxC,oMAAYA,IAAAA,CACT,IAAA,CAAK;YAAC;YAAS,SAAS;SAAC,EACzB,QAAA,CAAS,EACT,OAAA,CAAQ,OAAO,EACf,QAAA,CAAS,2BAAO;IACrB,CAAC;IACD,SAAS,CAAO,KAMV,QAAA,MAAA;YANU;SAAA,EAMV,UANU,EACd,KAAA,EACA,SAAA,EACA,QAAA,EACA,KAAA,EACA,aAAa,OAAA,EACf,EAAM;YACJ,IAAI;gBAEF,YAAY,UAAU,IAAA,CAAK,EAAE,OAAA,CAAQ,SAAS,EAAE;gBAEhD,IAAI;gBACJ,IAAI,SAAS,WAAA,CAAY,MAAM,QAAQ;oBACrC,YAAY,CAAA,CAAA,EAAI,SAAS,CAAA,SAAA,EAAY,KAAK,CAAA,EAAA,CAAA;gBAC5C,OAAA,IAAW,SAAS,WAAA,CAAY,MAAM,MAAM;oBAC1C,MAAM,SAAS,MAAM,KAAA,CAAM,GAAG,EAAE,GAAA,CAAI,CAAC,IAAM,EAAE,IAAA,CAAK,CAAC;oBACnD,MAAM,WAAW,OAAO,GAAA,CAAI,CAAC,IAAM,CAAA,CAAA,EAAI,CAAC,CAAA,CAAA,CAAG,EAAE,IAAA,CAAK,GAAG;oBACrD,YAAY,CAAA,CAAA,EAAI,SAAS,CAAA,MAAA,EAAS,QAAQ,CAAA,CAAA,CAAA;gBAC5C,OAAO;oBACL,MAAM,YAAY,CAAC,MAAM,OAAO,KAAK,CAAC;oBACtC,YAAY,CAAA,CAAA,EAAI,SAAS,CAAA,CAAA,EAAI,QAAQ,GAAG,YAAY,QAAQ,CAAA,CAAA,EAAI,KAAK,CAAA,CAAA,CAAG,EAAA;gBAC1E;gBAEA,OAAO;oBACL,QAAQ;oBACR,QAAQ;oBACR,aAAa,CAAA,cAAA,EAAO,SAAS,CAAA,qBAAA,EAAS,QAAQ,CAAA,EAAA,EAAK,KAAK,CAAA,gEAAA,CAAA;gBAC1D;YACF,EAAA,OAAS,OAAY;gBACnB,OAAO;oBAAE,OAAO,CAAA,wCAAA,EAAa,MAAM,OAAO,EAAA;gBAAG;YAC/C;QACF;AACF,CAAC;AAED,IAAM,uMAAeA,IAAAA,CAAE,IAAA,CAAK;IAAC;IAAS;IAAQ,SAAS;CAAC;AACxD,IAAM,sMAAcA,IAAAA,CAAE,IAAA,CAAK;IAAC;IAAO,KAAK;CAAC;AAElC,IAAM,yQAAoBD,OAAAA,EAAK;IACpC,aAAa,CAAA;;;EAAA,CAAA;IAIb,YAAYC,4LAAAA,CAAE,MAAA,CAAO;QACnB,+LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,mEAAiB;QAC5C,cAAcA,4LAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,oDAAY;QAC9C,qMAAaA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,CAAA;;;;;;;;MAAA,CAQ9B;QACH,sMAAcA,IAAAA,CACX,IAAA,CAAK;YAAC;YAAQ;YAAS;YAAc,SAAS;SAAC,EAC/C,QAAA,CAAS,2BAAO;IACrB,CAAC;IACD,SAAS,CAAO,KAAuD,QAAA,MAAA;YAAvD;SAAA,EAAuD,UAAvD,EAAE,KAAA,EAAO,YAAA,EAAc,WAAA,EAAa,YAAA,CAAa,CAAA,EAAM;YACrE,IAAI;gBAEF,MAAM,oMAAYA,IAAAA,CACf,MAAA,CAAO;oBACN,cAAcA,4LAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS,iCAAQ;oBACrD,qMAAaA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS,oDAAiB;oBAC7D,mBAAmBA,4LAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS,iCAAQ;oBAC1D,6MAAqBA,IAAAA,CAClB,MAAA,CAAO,EACP,QAAA,CAAS,EACT,QAAA,CAAS,oDAAY;oBACxB,4MAAqBA,KAAAA,CAClB,MAAA,CAAO,EACP,QAAA,CAAS,EACT,QAAA,CAAS,oDAAY;oBACxB,sMAAcA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS,8CAAW;oBACxD,yMAAiBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS,+CAAY;oBAC5D,yMAAiBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS,+CAAY;oBAC5D,yMAAiBA,IAAAA,CACd,MAAA,CAAO,EACP,QAAA,CAAS,EACT,QAAA,CAAS,kEAAgB;oBAC5B,sNAA8BA,IAAAA,CAC3B,MAAA,CAAO,EACP,QAAA,CAAS,EACT,QAAA,CAAS,8CAAW;oBACvB,wMAAgBA,IAAAA,CACb,KAAA,yLAAMA,IAAAA,CAAE,MAAA,CAAO,CAAC,EAChB,MAAA,CAAO,CAAC,EACR,QAAA,CAAS,EACT,QAAA,CAAS,+DAAkB;gBAChC,CAAC,EACA,QAAA,CAAS;gBAGZ,MAAM,mMAAYA,KAAAA,CAAE,MAAA,CAAO;oBACzB,mMAAWA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS,4BAAa;gBACzD,CAAC;gBAGD,MAAM,eAAe;oBACnB,+LAAOA,IAAAA,CAAE,MAAA,CAAO;wBACd,+LAAOA,IAAAA,CACJ,KAAA,yLACCA,IAAAA,CAAE,KAAA,CAAM;4BACN,UAAU,MAAA,CAAO;gCACf,yMAAiBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;gCACrC,6MAAqBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;gCACzC,+MAAuBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;gCAC3C,+MAAuBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;gCAC3C,wMAAgBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;4BACtC,CAAC;4BACD;yBACD,GAEF,QAAA,CAAS,wCAAU;oBACxB,CAAC;oBACD,oMAAYA,IAAAA,CAAE,MAAA,CAAO;wBACnB,+LAAOA,IAAAA,CACJ,KAAA,yLACCA,IAAAA,CAAE,KAAA,CAAM;4BACN,UAAU,MAAA,CAAO;gCACf,uMAAgBA,KAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;gCACpC,wMAAgBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;gCACpC,2MAAmBA,IAAAA,CAChB,IAAA,CAAK;oCAAC;oCAAQ;oCAAS,QAAQ;iCAAC,EAChC,QAAA,CAAS;gCACZ,4MAAoBA,IAAAA,CAAE,KAAA,yLAAMA,IAAAA,CAAE,MAAA,CAAO,CAAC,EAAE,QAAA,CAAS;gCACjD,oBAAoBA,4LAAAA,CACjB,IAAA,CAAK;oCAAC;oCAAS;oCAAS,OAAO;iCAAC,EAChC,QAAA,CAAS;4BACd,CAAC;4BACD;yBACD,GAEF,QAAA,CAAS,wCAAU;oBACxB,CAAC;oBACD,iMAASA,IAAAA,CAAE,MAAA,CAAO;wBAChB,+LAAOA,IAAAA,CACJ,KAAA,yLACCA,IAAAA,CAAE,KAAA,CAAM;4BACN,UAAU,MAAA,CAAO;gCACf,sMAAcA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;gCAClC,4MAAoBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;gCACxC,wMAAgBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;gCACpC,wMAAgBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;4BACtC,CAAC;4BACD;yBACD,GAEF,QAAA,CAAS,oDAAY;oBAC1B,CAAC;oBACD,8LAAMA,IAAAA,CAAE,MAAA,CAAO;wBACb,+LAAOA,IAAAA,CACJ,KAAA,yLACCA,IAAAA,CAAE,KAAA,CAAM;4BACN,UAAU,MAAA,CAAO;gCACf,oMAAYA,IAAAA,CAAE,MAAA,CAAO;gCACrB,sMAAcA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;gCAClC,eAAeA,4LAAAA,CAAE,KAAA,yLAAMA,IAAAA,CAAE,MAAA,CAAO,CAAC,EAAE,MAAA,CAAO,CAAC,EAAE,QAAA,CAAS;gCACtD,cAAcA,4LAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;gCAClC,yMAAiBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;gCACrC,wMAAgBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;4BACtC,CAAC;4BACD;yBACD,GAEF,QAAA,CAAS,oDAAY;oBAC1B,CAAC;gBACH;gBAGA,MAAM,SAAS,2PAAM,iBAAA,EAAe;oBAClC,WAAO,yQAAA,EAAK,aAAa;oBAAA,iGAAA;oBAAA,oDAAA;oBAAA,oEAAA;oBAAA,kJAAA;oBAAA,6EAAA;oBAAA,aAAA;oBAOzB,QAAQ,CAAA,6BAAA,EAAgC,YAAY,CAAA,wDAAA,EAA2D,YAAY,CAAA;;;gBAAA,CAAA;oBAI3H,QAAQ,YAAA,CAAa,YAAY,CAAA;gBACnC,CAAC;gBAED,IAAI,iBAAiB,QAAQ;oBAE3B,MAAM,YAAY,CAAA;+CAAA,EACuB,WAAW,CAAA;;;;;;;;;;;;;;;;;;;QAAA,CAAA;oBAqBpD,MAAM,cAAc,2PAAM,eAAA,EAAa;wBACrC,QAAO,4QAAA,EAAK,aAAa;wBACzB,QAAQ;oBACV,CAAC;oBAED,IAAI,UAAU,YAAY,IAAA,CACvB,OAAA,CAAQ,sBAAsB,EAAE,EAChC,OAAA,CAAQ,mBAAmB,EAAE,EAC7B,OAAA,CAAQ,oBAAoB,EAAE,EAC9B,IAAA,CAAK;oBAGR,IAAI,CAAC,QAAQ,QAAA,CAAS,MAAM,GAAG;wBAC7B,UAAU,CAAA,mFAAA,EAAsF,OAAO,CAAA,MAAA,CAAA;oBACzG;oBAGA,IAAI,CAAC,QAAQ,QAAA,CAAS,QAAQ,KAAK,CAAC,QAAQ,QAAA,CAAS,SAAS,GAAG;wBAC/D,UAAU,QAAQ,OAAA,CAAQ,QAAQ,6BAA6B;oBACjE;oBAGA,IAAI,CAAC,QAAQ,QAAA,CAAS,UAAU,GAAG;wBACjC,UAAU,QAAQ,OAAA,CAAQ,QAAQ,0BAA0B;oBAC9D;oBAGA,IAAI,CAAC,QAAQ,QAAA,CAAS,QAAQ,GAAG;wBAC/B,UAAU,QAAQ,OAAA,CAChB,QACA;oBAEJ;oBAEA,QAAQ,GAAA,CAAI,wCAAe,OAAO;oBAGlC,MAAM,YAAY,OAAO,IAAA,CAAK,OAAO,EAAE,QAAA,CAAS,QAAQ;oBACxD,MAAM,UAAU,CAAA,0BAAA,EAA6B,SAAS,EAAA;oBAGtD,MAAMC,QAAQ,MAAM,OAAO,cAAA,CAAe,EAAE,IAAA,CAAK;oBACjDA,MAAK,KAAA,GAAQA,MAAK,KAAA,CAAM,GAAA,CAAI,CAAC,UAAe;wBAC1C,IAAI,KAAA,CAAM,UAAU,CAAA,EAAG;4BACrB,KAAA,CAAM,UAAU,CAAA,GAAI;wBACtB;wBACA,OAAO;oBACT,CAAC;oBAED,OAAO;wBACL;wBACA;wBACA,OAAOA,MAAK,KAAA;oBACd;gBACF;gBAEA,MAAM,OAAQ,MAAM,OAAO,cAAA,CAAe,EAAE,IAAA,CAAK;gBAEjD,OAAO;oBACL;oBACA;oBACA,OAAO,KAAK,KAAA;gBACd;YACF,EAAA,OAAS,OAAY;gBACnB,MAAM,IAAI,MAAM,CAAA,gGAAA,EAAwB,MAAM,OAAO,EAAE;YACzD;QACF;AACF,CAAC;AAGD,IAAM,yMAAiBD,IAAAA,CAAE,MAAA,CAAO;IAC9B,+LAAOA,IAAAA,CAAE,MAAA,CAAO;QACd,gMAAQA,IAAAA,CACL,MAAA,CAAO;YACN,QAAQA,4LAAAA,CAAE,MAAA,CAAO;YACjB,8LAAMA,IAAAA,CAAE,MAAA,CAAO;gBACb,+LAAOA,IAAAA,CAAE,KAAA,CAAM;4MAACA,IAAAA,CAAE,MAAA,CAAO;4MAAGA,IAAAA,CAAE,MAAA,CAAO;4MAAGA,IAAAA,CAAE,MAAA,CAAO;4MAAGA,IAAAA,CAAE,MAAA,CAAO,CAAC;iBAAC;YACjE,CAAC;YACD,gMAAQA,IAAAA,CAAE,MAAA,CAAO;gBACf,+LAAOA,IAAAA,CAAE,KAAA,CAAM;4MAACA,IAAAA,CAAE,MAAA,CAAO;4MAAGA,IAAAA,CAAE,MAAA,CAAO;2MAAGA,KAAAA,CAAE,MAAA,CAAO;4MAAGA,IAAAA,CAAE,MAAA,CAAO,CAAC;iBAAC;gBAC/D,+LAAOA,IAAAA,CAAE,MAAA,CAAO;gBAChB,kMAAUA,IAAAA,CAAE,KAAA,yLAAMA,IAAAA,CAAE,MAAA,CAAO,CAAC,EAAE,QAAA,CAAS;YACzC,CAAC;YACD,OAAOA,4LAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;QAC7B,CAAC,EACA,QAAA,CAAS;QACZ,8LAAMA,IAAAA,CACH,MAAA,CAAO;YACN,6LAAKA,IAAAA,CAAE,MAAA,CAAO;YACd,+LAAOA,IAAAA,CAAE,MAAA,CAAO;YAChB,kMAAUA,IAAAA,CAAE,MAAA,CAAO;YACnB,iMAASA,IAAAA,CAAE,MAAA,CAAO;QACpB,CAAC,EACA,QAAA,CAAS;IACd,CAAC;AAEH,CAAC;AAEM,IAAM,oBAAmBD,2PAAAA,EAAK;IACnC,aAAa,CAAA;4QAAA,CAAA;IAEb,oMAAYC,IAAAA,CAAE,MAAA,CAAO;QACnB,QAAQA,4LAAAA,CACL,MAAA,CAAO,EACP,GAAA,CAAI,CAAC,EACL,QAAA,CACC;QAEJ,qMAAaA,IAAAA,CACV,MAAA,CAAO,EACP,GAAA,CAAI,CAAC,EACL,QAAA,CACC;QAEJ,mMAAWA,IAAAA,CACR,MAAA,CAAO,EACP,QAAA,CAAS,EACT,QAAA,CACC;QAEJ,kMAAUA,IAAAA,CACP,IAAA,CAAK;YAAC;YAAa;YAAQ,UAAU;SAAC,EACtC,OAAA,CAAQ,WAAW,EACnB,QAAA,CAAS,oDAAY;QACxB,+LAAOA,IAAAA,CACJ,MAAA,CAAO,EACP,QAAA,CAAS,EACT,QAAA,CACC;QAEJ,mMAAWA,IAAAA,CACR,MAAA,CAAO,EACP,GAAA,CAAI,EACJ,OAAA,CAAQ,CAAC,EACT,QAAA,CACC;QAEJ,sMAAcA,IAAAA,CAAE,OAAA,CAAQ,EAAE,OAAA,CAAQ,KAAK,EAAE,QAAA,CAAS,qDAAa;QAC/D,qMAAaA,IAAAA,CACV,OAAA,CAAQ,EACR,OAAA,CAAQ,KAAK,EACb,QAAA,CAAS,kEAAgB;QAC5B,iMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,EAAE,OAAA,CAAQ,CAAC,EAAE,QAAA,CAAS,cAAI;QAClD,iMAASA,IAAAA,CACN,IAAA,CAAK;YAAC;YAAY;YAAU,KAAK;SAAC,EAClC,OAAA,CAAQ,UAAU,EAClB,QAAA,CAAS,cAAI;QAChB,mMAAWA,IAAAA,CAAE,OAAA,CAAQ,EAAE,OAAA,CAAQ,KAAK,EAAE,QAAA,CAAS,oDAAY;QAC3D,iMAASA,IAAAA,CAAE,OAAA,CAAQ,EAAE,OAAA,CAAQ,KAAK,EAAE,QAAA,CAAS,qDAAa;IAC5D,CAAC;IACD,SAAS,CAAO,KAaV,QAAA,MAAA;YAbU;SAAA,EAaV,UAbU,EACd,MAAA,EACA,WAAA,EACA,SAAA,EACA,QAAA,EACA,KAAA,EACA,SAAA,EACA,YAAA,EACA,WAAA,EACA,OAAA,EACA,OAAA,EACA,SAAA,EACA,OAAA,EACF,EAAM;YACJ,MAAM,MAAM;YACZ,MAAM,SAA8B;gBAClC;gBACA;gBACA;gBACA,UAAU;gBACV,YAAY;gBACZ;gBACA,cAAc;YAChB;YAGA,QAAQ,GAAA,CAAI,yEAAkB,MAAM;YAEpC,IAAI,UAAW,CAAA,OAAO,SAAA,GAAY;YAClC,IAAI,MAAO,CAAA,OAAO,KAAA,GAAQ;YAC1B,IAAI,UAAW,CAAA,OAAO,SAAA,GAAY;YAClC,IAAI,QAAS,CAAA,OAAO,QAAA,GAAW;YAC/B,IAAI,QAAS,CAAA,OAAO,OAAA,GAAU;YAE9B,MAAM,UAAU;gBACd,eAAe,CAAA,QAAA,EAAW,QAAQ,GAAA,CAAI,kBAAkB,EAAA;YAC1D;YAEA,IAAI;gBACF,MAAM,WAAY,MAAM,MAAM,GAAG,GAAG,CAAA,CAAA,EAAI,IAAI,gBAAgB,MAAM,CAAC,EAAA,EAAI;oBACrE,QAAQ;oBACR;gBACF,CAAC;gBAED,MAAM,SAAU,MAAM,SAAS,IAAA,CAAK;gBACpC,QAAQ,GAAA,CAAI,MAAM;gBAElB,OAAO;oBACL,UAAU,OAAO,QAAA;oBACjB,QAAQ,OAAO,MAAA,IAAU,CAAC,CAAA;oBAC1B,aAAa,OAAO,WAAA;oBACpB,YAAY,OAAO,UAAA;oBACnB,SAAS,OAAO,OAAA,IAAW,CAAC;oBAC5B,QAAQ,OAAO,MAAA,IAAU,CAAC;oBAC1B,aAAa,OAAO,WAAA,IAAe,CAAC;oBACpC,WAAW,OAAO,SAAA,IAAa,CAAC,CAAA;oBAChC,UAAU,OAAO,QAAA;oBACjB,OAAO,OAAO,KAAA,IAAS,CAAC;oBACxB,MAAM,OAAO,IAAA,IAAQ,CAAC;oBACtB,UAAU,OAAO,QAAA;oBACjB,UAAU,OAAO,QAAA;oBACjB,UAAU,OAAO,QAAA,IAAY,CAAC,CAAA;gBAChC;YACF,EAAA,OAAS,OAAY;gBACnB,QAAQ,KAAA,CAAM,CAAA,sBAAA,EAAyB,MAAM,OAAO,EAAE;gBACtD,OAAO;oBAAE,OAAO,CAAA,wCAAA,EAAa,MAAM,OAAO,EAAA;gBAAG;YAC/C;QACF;AACF,CAAC;;;AC9mCM,IAAM,8QAAyBI,OAAAA,EAAK;IACvC,aAAa,CAAA;;;;;;IAAA,CAAA;IAOb,oMAAYC,IAAAA,CAAE,MAAA,CAAO;QACnB,gMAAQA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,uBAAQ;QACpC,uMAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,iDAAc;QACjD,+LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS,6DAAgB;IACxD,CAAC;IACK,SAAQ,EAAA,EAAkC;QAAA,OAAA,QAAA,IAAA,EAAA,WAAA,UAAlC,EAAE,MAAA,EAAQ,aAAA,EAAe,KAAA,CAAM,CAAA,EAAG;YAC9C,IAAI;gBACF,MAAM,aAAa;gBAEnB,MAAM,SAAS,IAAI,gBAAgB;oBACjC,YAAY;oBACZ,WAAW;oBACX;oBACA,eAAe;oBACf,OAAO,SAAS;oBAChB,QAAQ;gBACV,CAAC;gBAED,MAAM,WAAW,MAAM,MAAM,GAAG,UAAU,CAAA,qBAAA,EAAwB,MAAM,EAAA,EAAI;oBAC1E,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,UAAU,QAAQ,GAAA,CAAI,YAAA,IAAgB;oBACxC;gBACF,CAAC;gBAED,MAAM,OAAY,MAAM,SAAS,IAAA,CAAK;gBAEtC,IAAI,CAAC,SAAS,EAAA,EAAI;oBAChB,MAAM,IAAI,MAAM,CAAA,yDAAA,EAAoB,SAAS,MAAM,CAAA,CAAA,EAAI,SAAS,UAAU,EAAE;gBAC9E;gBAEA,OAAO,KAAK,MAAA;YACd,EAAA,OAAS,OAAY;gBACnB,OAAO;oBAAE,OAAO,CAAA,wCAAA,EAAa,MAAM,OAAO,EAAA;gBAAG;YAC/C;QACF;IAAA;AACF,CAAC;AAEM,IAAM,6QAAwBD,OAAAA,EAAK;IACxC,aAAa,CAAA;;;;;;;IAAA,CAAA;IAQb,oMAAYC,IAAAA,CAAE,MAAA,CAAO;QACnB,gMAAQA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,uBAAQ;QACpC,uMAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,iDAAc;QACjD,wMAAgBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,0CAAY;QAChD,+LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS,6DAAgB;IACxD,CAAC;IACK,SAAQ,EAAA,EAAkD;QAAA,OAAA,QAAA,IAAA,EAAA,WAAA,UAAlD,EAAE,MAAA,EAAQ,aAAA,EAAe,cAAA,EAAgB,KAAA,CAAM,CAAA,EAAG;YAC9D,IAAI;gBACF,MAAM,aAAa;gBACnB,MAAM,SAAS,IAAI,gBAAgB;oBACjC,UAAU,QAAQ,GAAA,CAAI,YAAA,IAAgB;oBAAA,mBAAA;oBACtC,YAAY;oBACZ,WAAW;oBAAA,wBAAA;oBACX;oBACA,gBAAgB,eAAe,QAAA,CAAS;oBACxC,OAAO,SAAS;oBAChB,QAAQ;gBACV,CAAC;gBAED,MAAM,WAAW,MAAM,MAAM,GAAG,UAAU,CAAA,uBAAA,CAAA,EAA2B;oBACnE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,UAAU,QAAQ,GAAA,CAAI,YAAA,IAAgB;oBACxC;oBACA,MAAM,KAAK,SAAA,CAAU,CAAC,CAAC;gBACzB,CAAC;gBAED,IAAI,CAAC,SAAS,EAAA,EAAI;oBAChB,MAAM,IAAI,MAAM,CAAA,yDAAA,EAAoB,SAAS,MAAM,EAAE;gBACvD;gBAEA,MAAM,OAAY,MAAM,SAAS,IAAA,CAAK;gBACtC,OAAO,KAAK,MAAA;YACd,EAAA,OAAS,OAAY;gBACnB,OAAO;oBAAE,OAAO,CAAA,wCAAA,EAAa,MAAM,OAAO,EAAA;gBAAG;YAC/C;QACF;IAAA;AACF,CAAC", "ignoreList": [0, 1, 2, 3, 4, 5], "debugId": null}}]}