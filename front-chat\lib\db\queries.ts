// db/queries.ts
import { map, mapView, mapAccess, chat, Message, message, vote } from "@/lib/db/schema";
import { desc, eq, and, or, sql, gte, asc } from "drizzle-orm";
import db from "@/lib/db";
import { Layer, MapView } from "@/types/map";

// Chat Queries (기존)
export async function saveChat({
  id,
  userId,
  title,
}: {
  id: string;
  userId: string;
  title: string;
}) {
  try {
    return await db.insert(chat).values({
      id,
      createdAt: new Date(),
      userId,
      title,
    });
  } catch (error) {
    console.error('Failed to save chat in database');
    throw error;
  }
}

export async function saveMessages({ messages }: { messages: Array<Message> }) {
  try {
    return await db.insert(message).values(messages);
  } catch (error) {
    console.error('Failed to save messages in database', error);
    throw error;
  }
}

export async function deleteChatById({ id }: { id: string }) {
  try {
    await db.delete(vote).where(eq(vote.chatId, id));
    await db.delete(message).where(eq(message.chatId, id));

    return await db.delete(chat).where(eq(chat.id, id));
  } catch (error) {
    console.error('Failed to delete chat by id from database');
    throw error;
  }
}

export async function getChatsByUserId({ id }: { id: string }) {
  try {
    return await db
      .select()
      .from(chat)
      .where(eq(chat.userId, id))
      .orderBy(desc(chat.createdAt));
  } catch (error) {
    console.error("Failed to get chats by user from database");
    throw error;
  }
}

export async function getChatById({ id }: { id: string }) {
  try {
    const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));
    return selectedChat;
  } catch (error) {
    console.error("Failed to get chat by id from database");
    throw error;
  }
}

export async function getMessageById({ id }: { id: string }) {
  try {
    return await db.select().from(message).where(eq(message.id, id));
  } catch (error) {
    console.error('Failed to get message by id from database');
    throw error;
  }
}

export async function getMessagesByChatId({ id }: { id: string }) {
  try {
    return await db
      .select()
      .from(message)
      .where(eq(message.chatId, id))
      .orderBy(asc(message.createdAt));
  } catch (error) {
    console.error('Failed to get messages by chat id from database', error);
    throw error;
  }
}

export async function voteMessage({
  chatId,
  messageId,
  type,
}: {
  chatId: string;
  messageId: string;
  type: 'up' | 'down';
}) {
  try {
    const [existingVote] = await db
      .select()
      .from(vote)
      .where(and(eq(vote.messageId, messageId)));

    if (existingVote) {
      return await db
        .update(vote)
        .set({ isUpvoted: type === 'up' })
        .where(and(eq(vote.messageId, messageId), eq(vote.chatId, chatId)));
    }
    return await db.insert(vote).values({
      chatId,
      messageId,
      isUpvoted: type === 'up',
    });
  } catch (error) {
    console.error('Failed to upvote message in database', error);
    throw error;
  }
}

export async function getVotesByChatId({ id }: { id: string }) {
  try {
    return await db.select().from(vote).where(eq(vote.chatId, id));
  } catch (error) {
    console.error('Failed to get votes by chat id from database', error);
    throw error;
  }
}

export async function deleteMessagesByChatIdAfterTimestamp({
  chatId,
  timestamp,
}: {
  chatId: string;
  timestamp: Date;
}) {
  try {
    return await db
      .delete(message)
      .where(
        and(eq(message.chatId, chatId), gte(message.createdAt, timestamp)),
      );
  } catch (error) {
    console.error(
      'Failed to delete messages by id after timestamp from database',
    );
    throw error;
  }
}

export async function updateChatVisiblityById({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: 'private' | 'public';
}) {
  try {
    return await db.update(chat).set({ visibility }).where(eq(chat.id, chatId));
  } catch (error) {
    console.error('Failed to update chat visibility in database');
    throw error;
  }
}


// Map Queries
export async function getMapsByUserId({ userId }: { userId: string }) {
  try {
    return await db
      .select({
        id: map.id,
        name: map.name,
        createdAt: map.createdAt,
        updatedAt: map.updatedAt,
        layers: map.layers,
        isPublic: map.isPublic,
        activeUsers: sql<number>`
        (
          SELECT COUNT(DISTINCT ${mapView.userId})
          FROM ${mapView}
          WHERE ${mapView.mapId} = ${map.id}
          AND ${mapView.updatedAt} > NOW() - INTERVAL '5 minutes'
        )
        `.as("activeUsers"),
        view: {
          center: mapView.center,
          zoom: mapView.zoom,
          basemap: mapView.basemap,
        },
      })
      .from(map)
      .leftJoin(
        mapAccess,
        and(eq(mapAccess.mapId, map.id), eq(mapAccess.userId, userId))
      )
      .leftJoin(
        mapView,
        and(
          eq(mapView.mapId, map.id),
          eq(mapView.userId, userId)
        )
      )
      .where(or(eq(map.createdBy, userId), eq(mapAccess.userId, userId)))
      .orderBy(desc(map.updatedAt));
  } catch (error) {
    if (error instanceof Error) {
      console.error("Failed to get maps by user from database:", {
        message: error.message,
        stack: error.stack,
      });
    }
    throw error;
  }
}

export async function getMapById({
  id,
  userId,
}: {
  id: string;
  userId: string;
}) {
  try {
    const [mapData] = await db
      .select({
        id: map.id,
        name: map.name,
        createdAt: map.createdAt,
        updatedAt: map.updatedAt,
        layers: map.layers,
        isPublic: map.isPublic,
        createdBy: map.createdBy,
        activeUsers: sql<number>`
        (
          SELECT COUNT(DISTINCT ${mapView.userId})
          FROM ${mapView}
          WHERE ${mapView.mapId} = ${map.id}
          AND ${mapView.updatedAt} > NOW() - INTERVAL '5 minutes'
        )
        `.as("activeUsers"),
      })
      .from(map)
      .where(eq(map.id, id));

    if (!mapData) {
      throw new Error("Map not found");
    }

    // layers 배열의 style 객체 파싱
    if (Array.isArray(mapData.layers)) {
      mapData.layers = mapData.layers.map((layer: Layer) => ({
        ...layer,
        style: layer.style ? (
          typeof layer.style === 'string' ? JSON.parse(layer.style) : layer.style
        ) : undefined
      }));
    }

    // 접근 권한 확인
    if (
      mapData.createdBy !== userId &&
      !mapData.isPublic &&
      !(await hasMapAccess({ mapId: id, userId }))
    ) {
      throw new Error("Forbidden");
    }

    // 사용자별 뷰 상태 조회
    const [view] = await db
      .select()
      .from(mapView)
      .where(and(eq(mapView.mapId, id), eq(mapView.userId, userId)));

    return {
      ...mapData,
      view: view || null,
    };
  } catch (error) {
    console.error("Failed to get map by id from database");
    throw error;
  }
}

export async function saveMap({
  id,
  name,
  layers,
  userId,
}: {
  id: string;
  name: string;
  layers: any[];
  userId: string;
}) {
  try {
    const existingMap = await db.select().from(map).where(eq(map.id, id));

    if (existingMap.length > 0) {
      return await db
        .update(map)
        .set({
          name,
          layers,
          updatedAt: new Date(),
        })
        .where(eq(map.id, id));
    }

    return await db.insert(map).values({
      id,
      name,
      layers,
      createdBy: userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  } catch (error) {
    console.error("Failed to save map in database");
    throw error;
  }
}

export async function updateMapView({
  mapId,
  userId,
  view,
}: {
  mapId: string;
  userId: string;
  view: MapView;
}) {
  try {
    return await db
      .insert(mapView)
      .values({
        mapId,
        userId,
        center: view.center ?? { lat: 36.5, lng: 127.5 },
        zoom: view.zoom ?? 7,
        basemap: view.basemap ?? "eMapBasic",
        updatedAt: new Date(),
      })
      .onConflictDoUpdate({
        target: [mapView.mapId, mapView.userId],
        set: {
          ...view,
          updatedAt: new Date(),
        },
      });
  } catch (error) {
    console.error("Failed to update map view state");
    throw error;
  }
}

export async function deleteMapById({
  id,
  userId,
}: {
  id: string;
  userId: string;
}) {
  try {
    const [mapData] = await db.select().from(map).where(eq(map.id, id));

    if (!mapData) {
      throw new Error("Map not found");
    }

    if (mapData.createdBy !== userId) {
      throw new Error("Forbidden");
    }

    return await db.delete(map).where(eq(map.id, id));
  } catch (error) {
    console.error("Failed to delete map from database");
    throw error;
  }
}

export async function hasMapAccess({
  mapId,
  userId,
}: {
  mapId: string;
  userId: string;
}) {
  try {
    const access = await db
      .select()
      .from(mapAccess)
      .where(and(eq(mapAccess.mapId, mapId), eq(mapAccess.userId, userId)));

    return access.length > 0;
  } catch (error) {
    console.error("Failed to check map access");
    throw error;
  }
}

export async function shareMap({
  mapId,
  userId,
  targetUserId,
  accessType = "view",
}: {
  mapId: string;
  userId: string;
  targetUserId: string;
  accessType?: "view" | "edit";
}) {
  try {
    // 공유 권한 확인
    const [mapData] = await db.select().from(map).where(eq(map.id, mapId));

    if (!mapData || mapData.createdBy !== userId) {
      throw new Error("Forbidden");
    }

    return await db.insert(mapAccess).values({
      mapId,
      userId: targetUserId,
      accessType,
      createdAt: new Date(),
    });
  } catch (error) {
    console.error("Failed to share map");
    throw error;
  }
}
