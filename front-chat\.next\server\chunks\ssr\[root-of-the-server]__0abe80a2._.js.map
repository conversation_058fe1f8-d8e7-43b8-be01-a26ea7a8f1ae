{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28auth%29/auth.config.ts"], "sourcesContent": ["import {NextAuthConfig, Session, User} from \"next-auth\";\r\n\r\nexport const authConfig = {\r\n\tpages: {\r\n\t\tsignIn: \"/login\",\r\n\t\t// verifyRequest: `/login`,\r\n\t\t// error: \"/login\", // Error code passed in query string as ?error=\r\n\t\tnewUser: \"/\",\r\n\t},\r\n\tproviders: [\r\n\t\t// added later in auth.ts since it requires bcrypt which is only compatible with Node.js\r\n\t\t// while this file is also used in non-Node.js environments\r\n\t],\r\n\tcallbacks: {\r\n\t\tauthorized({ auth, request: { nextUrl } }) {\r\n\t\t\tconst isLoggedIn = !!auth?.user;\r\n\t\t\tconst isOnChat = nextUrl.pathname.startsWith(\"/geon-2d-map\");\r\n\t\t\tconst isOnLogin = nextUrl.pathname.startsWith(\"/login\");\r\n\t\t\tconst isOnRoot = nextUrl.pathname === \"/\";\r\n\t  \r\n\t\t\t// 루트 경로로 접근시 /geon-2d-map으로 리다이렉트\r\n\t\t\tif (isOnRoot) {\r\n\t\t\t  return Response.redirect(new URL(\"/geon-2d-map\", nextUrl));\r\n\t\t\t}\r\n\t  \r\n\t\t\t// 로그인된 상태에서 로그인/회원가입 페이지 접근시 /geon-2d-map으로 리다이렉트\r\n\t\t\tif (isLoggedIn && (isOnLogin)) {\r\n\t\t\t  return Response.redirect(new URL(\"/geon-2d-map\", nextUrl));\r\n\t\t\t}\r\n\t  \r\n\t\t\t// 로그인/회원가입 페이지는 항상 접근 가능\r\n\t\t\tif (isOnLogin) {\r\n\t\t\t  return true;\r\n\t\t\t}\r\n\t  \r\n\t\t\t// /geon-2d-map 페이지는 로그인한 사용자만 접근 가능\r\n\t\t\tif (isOnChat) {\r\n\t\t\t  return isLoggedIn;\r\n\t\t\t}\r\n\t  \r\n\t\t\treturn true;\r\n\t\t  },\r\n\t},\r\n} satisfies NextAuthConfig;\r\n"], "names": [], "mappings": ";;;AAEO,MAAM,aAAa;IACzB,OAAO;QACN,QAAQ;QACR,2BAA2B;QAC3B,mEAAmE;QACnE,SAAS;IACV;IACA,WAAW,EAGV;IACD,WAAW;QACV,YAAW,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;YACxC,MAAM,aAAa,CAAC,CAAC,MAAM;YAC3B,MAAM,WAAW,QAAQ,QAAQ,CAAC,UAAU,CAAC;YAC7C,MAAM,YAAY,QAAQ,QAAQ,CAAC,UAAU,CAAC;YAC9C,MAAM,WAAW,QAAQ,QAAQ,KAAK;YAEtC,kCAAkC;YAClC,IAAI,UAAU;gBACZ,OAAO,SAAS,QAAQ,CAAC,IAAI,IAAI,gBAAgB;YACnD;YAEA,kDAAkD;YAClD,IAAI,cAAe,WAAY;gBAC7B,OAAO,SAAS,QAAQ,CAAC,IAAI,IAAI,gBAAgB;YACnD;YAEA,yBAAyB;YACzB,IAAI,WAAW;gBACb,OAAO;YACT;YAEA,oCAAoC;YACpC,IAAI,UAAU;gBACZ,OAAO;YACT;YAEA,OAAO;QACN;IACH;AACD", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28auth%29/auth.ts"], "sourcesContent": ["import NextAuth, {User, Session, CredentialsSignin} from \"next-auth\";\r\nimport type { Provider } from \"next-auth/providers\";\r\nimport Credentials from \"next-auth/providers/credentials\";\r\nimport { authConfig } from \"./auth.config\";\r\n\r\ninterface ExtendedSession extends Session {\r\n    user: User;\r\n}\r\n\r\ninterface ValidationResponse {\r\n    code: number;\r\n    message: string;\r\n    result: {\r\n        isValid: boolean;\r\n        message: string;\r\n    };\r\n}\r\n\r\ninterface UserResponse {\r\n    code: number;\r\n    message: string;\r\n    result: {\r\n        userId: string;\r\n        userNm: string;\r\n        emailaddr: string | null;\r\n        userSeCode: string;\r\n        userSeCodeNm: string;\r\n        userImage: string | null;\r\n        insttCode: string;\r\n        insttNm: string | null;\r\n        insttUrl: string | null;\r\n        message: string;\r\n    };\r\n}\r\n\r\nclass InvalidLoginError extends CredentialsSignin {\r\n    code = \"Invalid identifier or password\"\r\n}\r\n\r\nconst PRODUCTION = process.env.NODE_ENV === \"production\";\r\nconst API_CERTIFICATE_KEY = 'tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh'\r\nconst SMT_URL = 'https://gsapi.geon.kr/smt'\r\n\r\nasync function validateLogin(userId: string, password: string): Promise<ValidationResponse> {\r\n    const params = new URLSearchParams({\r\n        crtfckey: API_CERTIFICATE_KEY,\r\n        userId,\r\n        password\r\n    });\r\n\r\n    const response = await fetch(`${SMT_URL}/login/validation?${params.toString()}`, {\r\n        method: 'POST',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            'crtfckey': API_CERTIFICATE_KEY\r\n        }\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n        throw new Error('로그인 검증에 실패했습니다.');\r\n    }\r\n\r\n    return data;\r\n}\r\n\r\nasync function getUserInfo(userId: string): Promise<UserResponse> {\r\n    const params = new URLSearchParams({\r\n        crtfckey: API_CERTIFICATE_KEY,\r\n        userId\r\n    });\r\n\r\n    const response = await fetch(`${SMT_URL}/users/id?${params.toString()}`, {\r\n        method: 'GET',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            'crtfckey': API_CERTIFICATE_KEY\r\n        }\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n        throw new Error('사용자 정보를 가져오는데 실패했습니다.');\r\n    }\r\n\r\n    return data;\r\n}\r\n\r\nexport const providers: Provider[] = [\r\n    Credentials({\r\n        credentials: {},\r\n        async authorize({id, password}: any) {\r\n            try {\r\n                // admin 계정으로 프론트엔드 로그인 허용\r\n                const frontendUserId = process.env.FRONTEND_LOGIN_USER_ID || 'admin';\r\n                const frontendPassword = process.env.FRONTEND_LOGIN_PASSWORD || 'password1234';\r\n\r\n                if (id === frontendUserId && password === frontendPassword) {\r\n                    // admin 계정으로 로그인 성공\r\n                    return {\r\n                        id: frontendUserId,\r\n                        name: 'GeOn City',\r\n                        email: '@example.com',\r\n                        userId: frontendUserId,\r\n                        userNm: 'GeOn City',\r\n                        emailaddr: '@example.com',\r\n                        userSeCode: '14',\r\n                        userSeCodeNm: '관리자',\r\n                        userImage: null,\r\n                        insttCode: 'GEON',\r\n                        insttNm: 'GeOn',\r\n                        insttUrl: null,\r\n                        message: '로그인 성공'\r\n                    };\r\n                }\r\n\r\n                // 기존 geonuser 계정도 유지 (호환성을 위해)\r\n                if (id === 'geonuser') {\r\n                    // 1. 로그인 검증\r\n                    const validation = await validateLogin(id, password);\r\n\r\n                    if (!validation.result.isValid) {\r\n                        throw new CredentialsSignin(validation.result.message);\r\n                    }\r\n\r\n                    // 2. 유저 정보 조회\r\n                    const userResponse = await getUserInfo(id);\r\n\r\n                    if (userResponse.code !== 200) {\r\n                        return new CredentialsSignin(userResponse.result.message);\r\n                    }\r\n\r\n                    // 3. 유저 정보 반환\r\n                    return {\r\n                        ...userResponse.result,\r\n                        id: userResponse.result.userId,\r\n                        name: userResponse.result.userNm || id,\r\n                        email: userResponse.result.emailaddr || `${userResponse.result.userNm}`,\r\n                    };\r\n                }\r\n\r\n                // 허용되지 않은 계정\r\n                throw new CredentialsSignin('admin 또는 geonuser 계정으로만 로그인할 수 있습니다.');\r\n            } catch (error) {\r\n                console.error('Auth error:', error);\r\n                throw error;\r\n            }\r\n        },\r\n    })\r\n]\r\n\r\nexport const providerMap = providers\r\n  .map((provider) => {\r\n      if (typeof provider === \"function\") {\r\n          const providerData = provider()\r\n          return { id: providerData.id, name: providerData.name }\r\n      } else {\r\n          return { id: provider.id, name: provider.name }\r\n      }\r\n  })\r\n  .filter((provider) => provider.id !== \"credentials\")\r\n\r\nexport const {\r\n    handlers,\r\n    auth,\r\n    signIn,\r\n    signOut,\r\n} = NextAuth({\r\n    ...authConfig,\r\n    providers,\r\n    session: {\r\n        strategy: \"jwt\",\r\n        maxAge: 30 * 60, // 30분 (30분 * 60초)\r\n    },\r\n    callbacks: {\r\n        async jwt({ token, user }) {\r\n            if (user) {\r\n                token.id = user.id;\r\n            }\r\n            return token;\r\n        },\r\n        async session({session, token,}: {\r\n            session: ExtendedSession;\r\n            token: any;\r\n        }) {\r\n            if (session.user) {\r\n                session.user.id = token.id as string;\r\n            }\r\n            return session;\r\n        },\r\n    }\r\n    // adapter: DrizzleAdapter(db, {\r\n    //     // @ts-ignore GitHub 로그인의 경우 email Null 가능성 존재\r\n    //     usersTable: users,\r\n    //     accountsTable: accounts,\r\n    //     sessionsTable: sessions,\r\n    //     verificationTokensTable: verificationTokens,\r\n    // }) as Adapter,\r\n    // cookies: {\r\n    //     sessionToken: {\r\n    //         name: `${PRODUCTION ? \"__Secure-\" : \"\"}next-auth.session-token`,\r\n    //         options: {\r\n    //             httpOnly: true,\r\n    //             sameSite: \"lax\",\r\n    //             path: \"/\",\r\n    //             secure: PRODUCTION,\r\n    //             // When working on localhost, the cookie domain must be omitted entirely (https://stackoverflow.com/a/1188145)\r\n    //             domain: PRODUCTION\r\n    //               ? `.${process.env.NEXT_PUBLIC_ROOT_DOMAIN}`\r\n    //               : undefined,\r\n    //         },\r\n    //     },\r\n    // },\r\n});\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;;;;AAgCA,MAAM,0BAA0B,oMAAA,CAAA,oBAAiB;IAC7C,OAAO,iCAAgC;AAC3C;AAEA,MAAM,aAAa,oDAAyB;AAC5C,MAAM,sBAAsB;AAC5B,MAAM,UAAU;AAEhB,eAAe,cAAc,MAAc,EAAE,QAAgB;IACzD,MAAM,SAAS,IAAI,gBAAgB;QAC/B,UAAU;QACV;QACA;IACJ;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,kBAAkB,EAAE,OAAO,QAAQ,IAAI,EAAE;QAC7E,QAAQ;QACR,SAAS;YACL,gBAAgB;YAChB,YAAY;QAChB;IACJ;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;QACd,MAAM,IAAI,MAAM;IACpB;IAEA,OAAO;AACX;AAEA,eAAe,YAAY,MAAc;IACrC,MAAM,SAAS,IAAI,gBAAgB;QAC/B,UAAU;QACV;IACJ;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,UAAU,EAAE,OAAO,QAAQ,IAAI,EAAE;QACrE,QAAQ;QACR,SAAS;YACL,gBAAgB;YAChB,YAAY;QAChB;IACJ;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;QACd,MAAM,IAAI,MAAM;IACpB;IAEA,OAAO;AACX;AAEO,MAAM,YAAwB;IACjC,CAAA,GAAA,sNAAA,CAAA,UAAW,AAAD,EAAE;QACR,aAAa,CAAC;QACd,MAAM,WAAU,EAAC,EAAE,EAAE,QAAQ,EAAM;YAC/B,IAAI;gBACA,0BAA0B;gBAC1B,MAAM,iBAAiB,QAAQ,GAAG,CAAC,sBAAsB,IAAI;gBAC7D,MAAM,mBAAmB,QAAQ,GAAG,CAAC,uBAAuB,IAAI;gBAEhE,IAAI,OAAO,kBAAkB,aAAa,kBAAkB;oBACxD,oBAAoB;oBACpB,OAAO;wBACH,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,QAAQ;wBACR,QAAQ;wBACR,WAAW;wBACX,YAAY;wBACZ,cAAc;wBACd,WAAW;wBACX,WAAW;wBACX,SAAS;wBACT,UAAU;wBACV,SAAS;oBACb;gBACJ;gBAEA,+BAA+B;gBAC/B,IAAI,OAAO,YAAY;oBACnB,YAAY;oBACZ,MAAM,aAAa,MAAM,cAAc,IAAI;oBAE3C,IAAI,CAAC,WAAW,MAAM,CAAC,OAAO,EAAE;wBAC5B,MAAM,IAAI,oMAAA,CAAA,oBAAiB,CAAC,WAAW,MAAM,CAAC,OAAO;oBACzD;oBAEA,cAAc;oBACd,MAAM,eAAe,MAAM,YAAY;oBAEvC,IAAI,aAAa,IAAI,KAAK,KAAK;wBAC3B,OAAO,IAAI,oMAAA,CAAA,oBAAiB,CAAC,aAAa,MAAM,CAAC,OAAO;oBAC5D;oBAEA,cAAc;oBACd,OAAO;wBACH,GAAG,aAAa,MAAM;wBACtB,IAAI,aAAa,MAAM,CAAC,MAAM;wBAC9B,MAAM,aAAa,MAAM,CAAC,MAAM,IAAI;wBACpC,OAAO,aAAa,MAAM,CAAC,SAAS,IAAI,GAAG,aAAa,MAAM,CAAC,MAAM,EAAE;oBAC3E;gBACJ;gBAEA,aAAa;gBACb,MAAM,IAAI,oMAAA,CAAA,oBAAiB,CAAC;YAChC,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,eAAe;gBAC7B,MAAM;YACV;QACJ;IACJ;CACH;AAEM,MAAM,cAAc,UACxB,GAAG,CAAC,CAAC;IACF,IAAI,OAAO,aAAa,YAAY;QAChC,MAAM,eAAe;QACrB,OAAO;YAAE,IAAI,aAAa,EAAE;YAAE,MAAM,aAAa,IAAI;QAAC;IAC1D,OAAO;QACH,OAAO;YAAE,IAAI,SAAS,EAAE;YAAE,MAAM,SAAS,IAAI;QAAC;IAClD;AACJ,GACC,MAAM,CAAC,CAAC,WAAa,SAAS,EAAE,KAAK;AAEjC,MAAM,EACT,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,OAAO,EACV,GAAG,CAAA,GAAA,iQAAA,CAAA,UAAQ,AAAD,EAAE;IACT,GAAG,iIAAA,CAAA,aAAU;IACb;IACA,SAAS;QACL,UAAU;QACV,QAAQ,KAAK;IACjB;IACA,WAAW;QACP,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACrB,IAAI,MAAM;gBACN,MAAM,EAAE,GAAG,KAAK,EAAE;YACtB;YACA,OAAO;QACX;QACA,MAAM,SAAQ,EAAC,OAAO,EAAE,KAAK,EAG5B;YACG,IAAI,QAAQ,IAAI,EAAE;gBACd,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;YAC9B;YACA,OAAO;QACX;IACJ;AAuBJ", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28auth%29/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { z } from \"zod\";\r\n\r\nimport { signIn } from \"./auth\";\r\n\r\nconst authFormSchema = z.object({\r\n\tid: z.string(),\r\n\temail: z.string().email().nullish(),\r\n\tpassword: z.string().min(6),\r\n});\r\n\r\nexport interface LoginActionState {\r\n\tstatus: \"idle\" | \"in_progress\" | \"success\" | \"failed\" | \"invalid_data\";\r\n}\r\n\r\nexport const login = async (\r\n\t_: LoginActionState,\r\n\tformData: FormData,\r\n): Promise<LoginActionState> => {\r\n\ttry {\r\n\t\tconst validatedData = authFormSchema.parse({\r\n\t\t\tid: formData.get(\"id\"),\r\n\t\t\t// email: formData.get(\"email\"),\r\n\t\t\tpassword: formData.get(\"password\"),\r\n\t\t});\r\n\t\tawait signIn(\"credentials\", {\r\n\t\t\tid: validatedData.id,\r\n\t\t\tpassword: validatedData.password,\r\n\t\t\tredirect: false,\r\n\t\t});\r\n\r\n\t\treturn { status: \"success\" };\r\n\t} catch (error) {\r\n\t\tif (error instanceof z.ZodError) {\r\n\t\t\treturn { status: \"invalid_data\" };\r\n\t\t}\r\n\r\n\t\treturn { status: \"failed\" };\r\n\t}\r\n};\r\n\r\nexport interface RegisterActionState {\r\n\tstatus:\r\n\t\t| \"idle\"\r\n\t\t| \"in_progress\"\r\n\t\t| \"success\"\r\n\t\t| \"failed\"\r\n\t\t| \"user_exists\"\r\n\t\t| \"invalid_data\";\r\n}\r\n\r\n// export const register = async (\r\n// \t_: RegisterActionState,\r\n// \tformData: FormData,\r\n// ): Promise<RegisterActionState> => {\r\n// \ttry {\r\n// \t\tconst validatedData = authFormSchema.parse({\r\n// \t\t\temail: formData.get(\"email\"),\r\n// \t\t\tpassword: formData.get(\"password\"),\r\n// \t\t});\r\n//\r\n// \t\tlet [user] = await getUser(validatedData.email);\r\n//\r\n// \t\tif (user) {\r\n// \t\t\treturn { status: \"user_exists\" } as RegisterActionState;\r\n// \t\t} else {\r\n// \t\t\tawait createUser(validatedData.email, validatedData.password);\r\n// \t\t\tawait signIn(\"credentials\", {\r\n// \t\t\t\temail: validatedData.email,\r\n// \t\t\t\tpassword: validatedData.password,\r\n// \t\t\t\tredirect: false,\r\n// \t\t\t});\r\n//\r\n// \t\t\treturn { status: \"success\" };\r\n// \t\t}\r\n// \t} catch (error) {\r\n// \t\tif (error instanceof z.ZodError) {\r\n// \t\t\treturn { status: \"invalid_data\" };\r\n// \t\t}\r\n//\r\n// \t\treturn { status: \"failed\" };\r\n// \t}\r\n// };\r\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;;;;AAEA,MAAM,iBAAiB,qLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/B,IAAI,qLAAA,CAAA,IAAC,CAAC,MAAM;IACZ,OAAO,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,GAAG,OAAO;IACjC,UAAU,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;AAC1B;AAMO,MAAM,QAAQ,OACpB,GACA;IAEA,IAAI;QACH,MAAM,gBAAgB,eAAe,KAAK,CAAC;YAC1C,IAAI,SAAS,GAAG,CAAC;YACjB,gCAAgC;YAChC,UAAU,SAAS,GAAG,CAAC;QACxB;QACA,MAAM,CAAA,GAAA,uHAAA,CAAA,SAAM,AAAD,EAAE,eAAe;YAC3B,IAAI,cAAc,EAAE;YACpB,UAAU,cAAc,QAAQ;YAChC,UAAU;QACX;QAEA,OAAO;YAAE,QAAQ;QAAU;IAC5B,EAAE,OAAO,OAAO;QACf,IAAI,iBAAiB,qLAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAChC,OAAO;gBAAE,QAAQ;YAAe;QACjC;QAEA,OAAO;YAAE,QAAQ;QAAS;IAC3B;AACD;;;IAxBa;;AAAA,wVAAA;CAoCb,kCAAkC;CAClC,2BAA2B;CAC3B,uBAAuB;CACvB,uCAAuC;CACvC,SAAS;CACT,iDAAiD;CACjD,mCAAmC;CACnC,yCAAyC;CACzC,QAAQ;CACR,EAAE;CACF,qDAAqD;CACrD,EAAE;CACF,gBAAgB;CAChB,8DAA8D;CAC9D,aAAa;CACb,oEAAoE;CACpE,mCAAmC;CACnC,kCAAkC;CAClC,wCAAwC;CACxC,uBAAuB;CACvB,SAAS;CACT,EAAE;CACF,mCAAmC;CACnC,MAAM;CACN,qBAAqB;CACrB,uCAAuC;CACvC,wCAAwC;CACxC,MAAM;CACN,EAAE;CACF,iCAAiC;CACjC,KAAK;CACL,KAAK", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/.next-internal/server/app/%28auth%29/login/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {login as '7f971f4d88258000755a37ed4c4f0db53b8db3294c'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28auth%29/login/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(auth)/login/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(auth)/login/page.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28auth%29/login/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(auth)/login/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(auth)/login/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}