import { OpenAICompatibleChatLanguageModel } from '@ai-sdk/openai-compatible';
import {
  LanguageModelV1,
  NoSuchModelError,
  ProviderV1,
} from '@ai-sdk/provider';
import {
  FetchFunction,
  loadApiKey,
  withoutTrailingSlash,
} from '@ai-sdk/provider-utils';
import {
  GeonChatModelId,
  GeonChatSettings,
} from './geon-chat-settings';
// import { deepSeekMetadataExtractor } from './deepseek-metadata-extractor';

export interface GeonProviderSettings {
  /**
DeepSeek API key.
*/
  apiKey?: string;
  /**
Base URL for the API calls.
*/
  baseURL?: string;
  /**
Custom headers to include in the requests.
*/
  headers?: Record<string, string>;
  /**
Custom fetch implementation. You can use it as a middleware to intercept requests,
or to provide a custom fetch implementation for e.g. testing.
*/
  fetch?: FetchFunction;
}

export interface GeonProvider extends ProviderV1 {
  /**
Creates a DeepSeek model for text generation.
*/
  (
    modelId: GeonChatModelId,
    settings?: GeonChatSettings,
  ): LanguageModelV1;

  /**
Creates a DeepSeek model for text generation.
*/
  languageModel(
    modelId: GeonChatModelId,
    settings?: GeonChatSettings,
  ): LanguageModelV1;

  /**
Creates a DeepSeek chat model for text generation.
*/
  chat(
    modelId: GeonChatModelId,
    settings?: GeonChatSettings,
  ): LanguageModelV1;
}

export function createGeon(
  options: GeonProviderSettings = {},
): GeonProvider {
  const baseURL = withoutTrailingSlash(
    options.baseURL ?? 'http://**************:8081/v1',
  );
  const getHeaders = () => ({
    Authorization: `Bearer ${loadApiKey({
      apiKey: options.apiKey,
      environmentVariableName: 'GEON_AI_API_KEY',
      description: 'Geon API key',
    })}`,
    ...options.headers,
  });

  const createLanguageModel = (
    modelId: GeonChatModelId,
    settings: GeonChatSettings = {},
  ) => {
    return new OpenAICompatibleChatLanguageModel(modelId, settings, {
      provider: `deepseek.chat`,
      url: ({ path }) => `${baseURL}${path}`,
      headers: getHeaders,
      fetch: options.fetch,
      defaultObjectGenerationMode: 'json',
    //   metadataExtractor: deepSeekMetadataExtractor,
    },


);
  };

  const provider = (
    modelId: GeonChatModelId,
    settings?: GeonChatSettings,
  ) => createLanguageModel(modelId, settings);

  provider.languageModel = createLanguageModel;
  provider.chat = createLanguageModel;
  provider.textEmbeddingModel = (modelId: string) => {
    throw new NoSuchModelError({ modelId, modelType: 'textEmbeddingModel' });
  };

  return provider;
}

export const geon = createGeon();