import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>, AlertDialogTitle, AlertDialogAction } from '@/components/ui/alert-dialog';
import { Info, X } from 'lucide-react';
import { Markdown } from '../markdown';

type FileMeta = {
  name: string;
  content_type: string;
  size: number;
  collection_name: string;
}

type File = {
  id: string;
  meta: FileMeta;
  created_at: number;
  updated_at: number;
}

type User = {
  id: string;
  name: string;
  email: string;
  role: string;
  profile_image_url: string;
}

type Source = {
  id: string;
  user_id: string;
  name: string;
  description: string;
  data?: any;
  meta: any | null;
  access_control: any | null;
  created_at: number;
  updated_at: number;
  user: User;
  files: File[];
  type: string;
}

type Metadata = {
  created_by: string;
  embedding_config: string;
  file_id: string;
  hash: string;
  name: string;
  source: string;
  start_index: number;
  score: number;
}

type SourceItem = {
  source: Source;
  document: string[];
  metadata: <PERSON><PERSON><PERSON>[];
  distances: number[];
}

interface SourceCitationProps {
  sources: SourceItem[];
}

const SourceCitation: React.FC<SourceCitationProps> = ({ sources }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedSource, setSelectedSource] = useState<{name: string, contents: {content: string, relevance: number}[]} | null>(null);

  const handleSourceClick = (fileName: string) => {
    const contents = sources.flatMap((sourceItem) => {
      return sourceItem.metadata
        .map((meta, index) => ({
          content: sourceItem.document[index],
          relevance: sourceItem.distances[index] * 100,
          fileName: meta.name
        }))
        .filter(item => item.fileName === fileName);
    });

    setSelectedSource({
      name: fileName,
      contents: contents
    });
    setIsDialogOpen(true);
  };

  const getUniqueFiles = () => {
    const filesMap = new Map<string, { name: string, relevance: number, count: number }>();
    
    sources.forEach(({ metadata, distances }) => {
      metadata.forEach((meta, index) => {
        const fileName = meta.name;
        const currentRelevance = distances[index] * 100;
        
        if (!filesMap.has(fileName)) {
          filesMap.set(fileName, {
            name: fileName,
            relevance: currentRelevance,
            count: 1
          });
        } else {
          const current = filesMap.get(fileName)!;
          filesMap.set(fileName, {
            name: fileName,
            relevance: current.relevance + currentRelevance,
            count: current.count + 1
          });
        }
      });
    });
    
    return Array.from(filesMap.entries()).map(([_, value]) => ({
      name: value.name,
      avgRelevance: value.relevance / value.count
    }));
  };

  const parseContent = (content: string) => {
    try {
      // metadata 부분 제거
      const [mainContent] = content.split(" metadata=");
      // page_content= 와 따옴표 제거
      return mainContent
        .replace(/^page_content=/, '')
        .replace(/^'|'$/g, '');
    } catch (e) {
      return content;
    }
  };

  return (
    <div className="w-full max-w-2xl">
      <div className="flex flex-col gap-2">
        <div className="text-sm flex items-center gap-1">
          <Info size={16} />
          <span>참고 문서</span>
        </div>
        <div className="flex flex-wrap gap-2">
          {getUniqueFiles().map((file) => (
            <button
              key={file.name}
              onClick={() => handleSourceClick(file.name)}
              className="inline-flex items-center gap-2 px-3 py-1 text-sm rounded-full border hover:opacity-80 transition-opacity"
            >
              <span>{file.name}</span>
              <span className="text-xs opacity-70">
                {file.avgRelevance.toFixed(1)}%
              </span>
            </button>
          ))}
        </div>
      </div>

      <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader className="relative">
            <AlertDialogTitle className="pr-8">{selectedSource?.name}</AlertDialogTitle>
            <button
              onClick={() => setIsDialogOpen(false)}
              className="absolute right-0 top-0 p-1 rounded-full hover:opacity-70 transition-opacity"
            >
              <X size={18} />
            </button>
          </AlertDialogHeader>
          <div className="max-h-96 overflow-y-auto">
            {selectedSource?.contents.map((item, index) => (
              <div key={index} className="mt-4">
                <div className="flex items-center gap-2 mb-1">
                  <div className="h-1 flex-grow rounded-full overflow-hidden border">
                    <div 
                      className="h-full transition-all"
                      style={{ 
                        width: `${item.relevance}%`,
                        backgroundColor: `hsl(${item.relevance * 1.2}, 70%, 50%)`
                      }} 
                    />
                  </div>
                  <span className="text-xs opacity-70">{item.relevance.toFixed(1)}%</span>
                </div>
                <div className="border-l-2 pl-4 py-2">
                  <Markdown>{item.content as string}</Markdown>
                </div>
              </div>
            ))}
          </div>
          <AlertDialogAction onClick={() => setIsDialogOpen(false)} className="mt-4">
            닫기
          </AlertDialogAction>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default SourceCitation;