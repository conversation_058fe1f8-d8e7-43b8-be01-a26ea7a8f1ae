"use client";

import { Message as AIMessage } from "ai";
import React, { useEffect } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { memo, useState } from "react";
import { cn } from "@/lib/utils";
import { PencilIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Markdown } from "./markdown";
import { PreviewAttachment } from "./preview-attachment";
import equal from "fast-deep-equal";
import { MessageEditor } from "./message-editor";
import { MessageActions } from "./message-actions";
import { Vote } from "@/lib/db/schema";
import { Reasoning } from "./reasoning";
import { ToolInvocationPart } from "./tools/tool-invocation-part";
import { AnimatedShinyText } from "./magicui/animated-shiny-text";
import { Annotations } from "./annotations";
import { UseMapReturn } from "@geon-map/odf";
import type { JSONValue } from "ai";

// 평가 진행 상태 감지 함수
function isEvaluationInProgress(annotations: JSONValue[] | undefined): boolean {
  if (!annotations) return false;

  let hasEvaluationStart = false;
  let hasEvaluationCompleted = false;

  annotations.forEach((ann: JSONValue) => {
    if (typeof ann === "object" && ann !== null && !Array.isArray(ann)) {
      if ("type" in ann && ann.type === "evaluation_start") {
        hasEvaluationStart = true;
      }
      if ("type" in ann && ann.type === "evaluation_completed") {
        hasEvaluationCompleted = true;
      }
    }
  });

  // evaluation_start가 있고 evaluation_completed가 없을 때만 진행 중으로 판단
  return hasEvaluationStart && !hasEvaluationCompleted;
}

interface MessageProps {
  chatId: string;
  message: AIMessage;
  status: "submitted" | "streaming" | "ready" | "error";
  vote: Vote | undefined;
  setMessages: (
    messages: AIMessage[] | ((messages: AIMessage[]) => AIMessage[])
  ) => void;
  reload: () => Promise<string | null | undefined>;
  isReadonly: boolean;
  addToolResult: (params: { toolCallId: string; result: string }) => void;
  mapState?: UseMapReturn;
}





function PureMessage({
  chatId,
  message,
  vote,
  status,
  setMessages,
  reload,
  isReadonly,
  addToolResult,
  mapState,
}: MessageProps) {
  const [mode, setMode] = useState<"view" | "edit">("view");
  // 각 reasoning 파트의 표시 상태를 관리하는 상태
  const [activeReasoningId, setActiveReasoningId] = useState<string | null>(
    null
  );

  // 필요할 때만 로그를 확인하고, 빌드에는 제거
  // console.log("message", message)

  // 메시지의 파트 타입을 추적하여 reasoning 컴포넌트 상태 관리
  useEffect(() => {
    if (!message.parts || message.parts.length === 0) return;

    // 가장 최근 파트 타입 확인
    const lastPart = message.parts[message.parts.length - 1];

    // 파트의 타입이 reasoning이면 해당 메시지의 ID를 설정
    // 다른 타입이면 null로 설정하여 모든 reasoning 컴포넌트를 닫음
    if (lastPart.type === "reasoning") {
      // 고유한 식별자 생성: 메시지 ID + 마지막 파트 인덱스
      const reasoningId = `${message.id}-${message.parts.length - 1}`;
      setActiveReasoningId(reasoningId);
    } else {
      setActiveReasoningId(null);
    }
  }, [message.parts]);

  return (
    <AnimatePresence>
      <motion.div
        className="w-full mx-auto max-w-3xl px-4 group/message"
        // initial={{ y: 5, opacity: 0 }}
        // animate={{ y: 0, opacity: 1 }}
        data-role={message.role}
      >
        <div
          className={cn(
            "flex flex-col gap-4 w-full",
            message.role === "user" ? "items-end" : "items-start"
          )}
        >
          <div className="flex flex-col gap-4 w-full">
            {message.role === "assistant" && (
              <Annotations
                annotations={message.annotations}
                isLoading={status === "streaming" || status === "submitted"}
              />
            )}
            {message.experimental_attachments && (
              <div className="flex flex-wrap gap-2">
                {message.experimental_attachments.map((attachment) => (
                  <PreviewAttachment
                    key={attachment.url}
                    attachment={attachment}
                  />
                ))}
              </div>
            )}

            {mode === "view" && (
              <div className="flex flex-row gap-2 items-start">
                {message.role === "user" && !isReadonly && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        className="px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100"
                        onClick={() => setMode("edit")}
                      >
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>메시지 수정</TooltipContent>
                  </Tooltip>
                )}

                <div
                  className={cn("flex flex-col gap-4 overflow-hidden", {
                    "bg-primary text-primary-foreground px-1 py-2 rounded-xl ml-auto max-w-[80%] md:max-w-[70%] lg:max-w-[60%] whitespace-pre-wrap break-words":
                      message.role === "user",
                    "w-full": message.role !== "user",
                  })}
                >
                  {message.parts?.map((part, j) => {
                    switch (part.type) {
                      case "text":
                        return <Markdown key={j}>{part.text}</Markdown>;

                      case "reasoning":
                        // 각 reasoning 파트를 고유하게 식별하는 ID 생성
                        const reasoningId = `${message.id}-${j}`;
                        // 현재 활성화된 reasoning ID와 매칭되는지 확인
                        const isReasoningActive =
                          activeReasoningId === reasoningId;

                        return (
                          <Reasoning
                            key={j}
                            reasoning={part.reasoning}
                            className="my-2"
                            open={isReasoningActive}
                            isReasoning={isReasoningActive}
                          />
                        );

                      case "tool-invocation": {
                        return (
                          <ToolInvocationPart
                            key={j}
                            invocation={part.toolInvocation}
                            status={status}
                            addToolResult={addToolResult}
                            mapState={mapState}
                          />
                        );
                      }
                      default:
                        return null;
                    }
                  })}

                  {/* 평가 진행 상태 - 메시지 텍스트 아래에 표시 */}
                  {message.role === "assistant" && isEvaluationInProgress(message.annotations) && (
                    <div className="mt-3 flex items-center gap-2 text-xs text-gray-500">
                      <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-pulse"></div>
                      <span>작업 결과를 평가 중입니다...</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {mode === "edit" && (
              <div className="flex flex-row gap-2 items-start">
                <MessageEditor
                  key={message.id}
                  message={message}
                  setMode={setMode}
                  setMessages={setMessages}
                  reload={reload}
                />
              </div>
            )}

            {!isReadonly && (
              <MessageActions
                key={`action-${message.id}`}
                chatId={chatId}
                message={message}
                vote={vote}
                isLoading={status === "submitted" || status === "streaming"}
              />
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}

export const Message = memo(PureMessage, (prevProps, nextProps) => {
  if (prevProps.status !== nextProps.status) return false;
  if (prevProps.message.id !== nextProps.message.id) return false;
  if (prevProps.message.content !== nextProps.message.content) return false;
  if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;
  if (!equal(prevProps.message.annotations, nextProps.message.annotations))
    return false;
  return true;
});

export const ThinkingMessage = () => {
  const thinkingPhrases = [
    "답변을 준비하고 있습니다...",
    "요청하신 내용을 처리 중입니다...",
    "최적의 답변을 찾고 있습니다...",
    "생각을 정리하고 있습니다...",
  ];

  const [currentPhrase, setCurrentPhrase] = React.useState("");

  React.useEffect(() => {
    setCurrentPhrase(
      thinkingPhrases[Math.floor(Math.random() * thinkingPhrases.length)]
    );
  }, []);
  return (
    <motion.div
      className="w-full max-w-3xl px-4 group/message"
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      data-role="assistant"
    >
      <div className="flex items-center justify-start gap-2.5">
        <AnimatedShinyText className="inline-flex items-center justify-center px-2 py-1 transition ease-out">
          <span>{currentPhrase || "처리 중입니다..."}</span>
        </AnimatedShinyText>
      </div>
    </motion.div>
  );
};
