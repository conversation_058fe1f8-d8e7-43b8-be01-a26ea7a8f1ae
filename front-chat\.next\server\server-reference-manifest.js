self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"406613c6f217a0744b02093d3e5a3c9a71f308ff53\": {\n      \"workers\": {\n        \"app/(map)/geon-2d-map/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(map)/geon-2d-map/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(map)/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(map)/geon-2d-map/page\": \"action-browser\"\n      }\n    },\n    \"40764eb81a775d0486660fff2140578c0e1f884c4e\": {\n      \"workers\": {\n        \"app/(map)/geon-2d-map/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(map)/geon-2d-map/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(map)/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(map)/geon-2d-map/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"RzLJEFYUW/CFqaruOjruBpAlZrrsX4lJc3SEBXoIqAE=\"\n}"