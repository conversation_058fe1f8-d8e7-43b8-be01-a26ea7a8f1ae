self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"40fefd0995466d2cff572450385c7313fe2dde0d08\": {\n      \"workers\": {\n        \"app/(map)/geon-2d-map/[id]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(map)/geon-2d-map/[id]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(map)/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(map)/geon-2d-map/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(map)/geon-2d-map/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(map)/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(map)/geon-2d-map/[id]/page\": \"action-browser\",\n        \"app/(map)/geon-2d-map/page\": \"action-browser\"\n      }\n    },\n    \"40549055c821b122536c42382986c059566a9ef0a2\": {\n      \"workers\": {\n        \"app/(map)/geon-2d-map/[id]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(map)/geon-2d-map/[id]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(map)/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(map)/geon-2d-map/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(map)/geon-2d-map/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(map)/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(map)/geon-2d-map/[id]/page\": \"action-browser\",\n        \"app/(map)/geon-2d-map/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"cTUG/ZvvyahVZd/zBHHEOAaq3Xu/bKxqLZoLpyon+/Y=\"\n}"