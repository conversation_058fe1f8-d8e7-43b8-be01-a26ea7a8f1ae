"use client";

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { DialogDescription } from '@radix-ui/react-dialog';

interface CreateMapDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateMap: (name: string) => Promise<void>;
}

export function CreateMapDialog({ isOpen, onClose, onCreateMap }: CreateMapDialogProps) {
  const [mapName, setMapName] = React.useState("");
  const [isCreating, setIsCreating] = React.useState(false);

  // 다이얼로그가 열릴 때마다 기본값으로 초기화
  React.useEffect(() => {
    if (isOpen) {
      const now = new Date();
      const dateString = `${now.getMonth() + 1}월 ${now.getDate()}일`;
      const timeString = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      setMapName(`새 지도 (${dateString} ${timeString})`);
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!mapName.trim()) {
      toast.error("지도 이름을 입력해주세요.");
      return;
    }

    setIsCreating(true);
    try {
      await onCreateMap(mapName.trim());
      onClose();
    } catch (error) {
      // 에러 처리는 상위 컴포넌트에서 수행
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>새 지도 만들기</DialogTitle>
        </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">지도 이름</Label>
                <Input
                  id="name"
                  value={mapName}
                  onChange={(e) => setMapName(e.target.value)}
                  placeholder="지도 이름을 입력하세요"
                  autoFocus
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" type="button" onClick={onClose}>
                취소
              </Button>
              <Button type="submit" disabled={isCreating}>
                {isCreating ? "생성 중..." : "생성하기"}
              </Button>
            </DialogFooter>
          </form>
      </DialogContent>
    </Dialog>
  );
}