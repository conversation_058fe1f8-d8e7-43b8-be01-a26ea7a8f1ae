"use client";

import { useState } from 'react';
import { Popup } from '@geon-map/odf';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { MapPin, X, Copy, Navigation, ArrowRight } from 'lucide-react';
import { toast } from 'sonner';

interface OriginPopupProps {
  position: [number, number];
  address: {
    roadAddr: string;
    jibunAddr?: string;
    buildName?: string;
    buildLo: string;
    buildLa: string;
  };
  onClose: () => void;
}

export function OriginPopup({
  position,
  address,
  onClose
}: OriginPopupProps) {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);

  const handleCopyAddress = () => {
    navigator.clipboard.writeText(address.roadAddr);
    toast.success("주소가 복사되었습니다");
  };

  const handleCopyCoordinates = () => {
    const coordText = `${address.buildLo}, ${address.buildLa}`;
    navigator.clipboard.writeText(coordText);
    toast.success("좌표가 복사되었습니다");
  };

  const handleCopyProjectedCoordinates = () => {
    const coordText = `${position[0].toFixed(2)}, ${position[1].toFixed(2)}`;
    navigator.clipboard.writeText(coordText);
    toast.success("투영 좌표가 복사되었습니다");
  };

  return (
    <Popup
      position={position}
      offset={[0, -10]}
      // autoPan
      // autoPanAnimation={250}
      // autoPanMargin={20}      
    >
      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="default"
            size="sm"
            className="h-10 w-10 p-0 rounded-full bg-green-600 hover:bg-green-700 shadow-lg border-2 border-white"
            onClick={() => setIsPopoverOpen(true)}
          >
            출발
          </Button>
        </PopoverTrigger>

        <PopoverContent
          className="w-80 p-0 shadow-lg border-0 backdrop-blur-sm"
          side="top"
          align="center"
        >
          <Card className="border-0 shadow-none bg-transparent">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-semibold flex items-center gap-2">
                  <div className="p-1.5 bg-green-100 rounded-full">
                    <ArrowRight className="h-4 w-4 text-green-600" />
                  </div>
                  출발지
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setIsPopoverOpen(false);
                    // onClose();
                  }}
                  className="h-6 w-6 p-0 hover:bg-gray-100"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </CardHeader>

            <CardContent className="space-y-3">
              {/* 건물명 */}
              {address.buildName && (
                <div className="space-y-1">
                  <span className="text-xs font-medium text-gray-600">건물명</span>
                  <div className="bg-green-50 rounded-md p-2 text-sm font-medium text-green-800">
                    {address.buildName}
                  </div>
                </div>
              )}

              {/* 도로명주소 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-gray-600">도로명주소</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopyAddress}
                    className="h-6 px-2 text-xs hover:bg-gray-100"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    복사
                  </Button>
                </div>
                <div className="bg-gray-50 rounded-md p-2 text-xs">
                  {address.roadAddr}
                </div>
              </div>

              {/* 지번주소 */}
              {address.jibunAddr && address.jibunAddr !== address.roadAddr && (
                <div className="space-y-2">
                  <span className="text-xs font-medium text-gray-600">지번주소</span>
                  <div className="bg-gray-50 rounded-md p-2 text-xs">
                    {address.jibunAddr}
                  </div>
                </div>
              )}

              {/* GPS 좌표 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-gray-600">GPS 좌표 (WGS84)</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopyCoordinates}
                    className="h-6 px-2 text-xs hover:bg-gray-100"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    복사
                  </Button>
                </div>
                <div className="bg-gray-50 rounded-md p-2 font-mono text-xs">
                  <div>경도: {address.buildLo}</div>
                  <div>위도: {address.buildLa}</div>
                </div>
              </div>

              {/* 투영 좌표 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-gray-600">투영 좌표</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopyProjectedCoordinates}
                    className="h-6 px-2 text-xs hover:bg-gray-100"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    복사
                  </Button>
                </div>
                <div className="bg-gray-50 rounded-md p-2 font-mono text-xs">
                  <div>X: {position[0].toFixed(2)}</div>
                  <div>Y: {position[1].toFixed(2)}</div>
                </div>
              </div>

              {/* 출발지 표시 */}
              <div className="pt-2 border-t">
                <Badge variant="outline" className="w-full justify-center bg-green-50 text-green-700 border-green-200">
                  <ArrowRight className="h-3 w-3 mr-1" />
                  경로 탐색 출발지
                </Badge>
              </div>
            </CardContent>
          </Card>
        </PopoverContent>
      </Popover>
    </Popup>
  );
}
