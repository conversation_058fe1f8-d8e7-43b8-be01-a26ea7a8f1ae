# 맵픽이란
Mappick은 누구나 손쉽게 공간정보가 포함된 지도를 제작하여 업무에 적용 할 수 있는 SaaS(Software as a Service) GIS 서비스입니다. Mappick을 사용하면 인터넷을 통해 공개된 공공 데이터나, 본인이 가지고 있는 데이터를 이용하여 다양한 테마지도를 만들어 활용할 수 있고 서로 공유할 수 있습니다.

## 지도제작
- 데이터를 조회해서 추가: 온/오프 만으로 데이터 추가 및 조회 기본 제공데이터와 기관 데이터를 간편하게 추가
- 주소가 엑셀로 되어 있다면: 간단하게 엑셀 데이터를 지도에 표시(지오코딩) 지번주소, 도로명주소, 당연히 GIS파일형식(shp) 그대로 업로드
- 스타일: 적용 색상, 라벨, 필터 등을 사용자가 직접 수정
- 저장하기: 다음에도 쉽게 찾을 수 있도록 지도화면 이름, 설명을 간단하게 작성한 후 저장하면 끝!

## 테마지도
- 관련된 지도를 한꺼번에 순서, 배치가 자유로운 지도 나만의 테마지도 만들기: 스토리, 모바일 등 다양한 테마로 사용자만의 지도 구성
- 필요한 지도기능을 선택만으로 생성: 각종 지도 관련 위젯도구 제공

## 공유하기
- 공유 단위 설정: 지도 전체를 공유할지 레이어(데이터 단위)만 공유할지 선택
- 공유 대상 선택: 누구에게 공유할지 주소록에서 선택 / 전체(기관전체), 그룹, 특정 개인 옵션
- 공유는 조심, 또 조심: 공유한 설정이 맞는지 최종 검토하고, 최종 확인 버튼 클릭!

이외에도 맵픽은 다음의 기능을 제공합니다. 실질적으로 Mappick은 지도 작성 및 정보 공유를 손쉽게 할 수 있는 SaaS(Software as a Service) GIS 서비스입니다. 이 플랫폼을 이용하면 누구나 간편하게 지도를 만들고 비즈니스나 개인 업무에 활용할 수 있습니다. Mappick은 다음과 같은 핵심 기능을 제공합니다.
- 지금 사용자님이 주소 데이터 엑셀파일이 있다면 그 파일을 맵핍에 업로드하여 지도에 해당 위치들을 표시할 수 있습니다.
- 관리하고 있는 매장이나 공장의 위치가 주소로만 기록되어 있는 데이터에서 어느 매장끼리 가까운지, 또는 멀리 떨어져 있는지가 궁금하시다면 맵픽에 업로드해보세요. 지도 기반의 한 눈에 파악이 가능하도록 변환해 드립니다.
- 커피샵 창업을 원하시나요? 내 관심 지역 주변에 다른 카페가 어떻게 분포되어 있는지 맵픽에서 확인해보세요. 카페 레이어를 켜시면 어디에 새로운 카페를 창업할 수 있는지 한 눈에 파악됩니다.
- 이외에도 맵픽을 이용하여 지도상에서 다양한 활동이 가능하오니 로그인하셔서 나의 데이터를 관리하고 분석해보세요.

---

이 플랫폼을 이용하면 누구나 간편하게 지도를 만들고 비즈니스나 개인 업무에 활용할 수 있습니다. Mappick은 다음과 같은 핵심 기능을 제공합니다.

1. 지금 사용자님이 주소 데이터 엑셀파일이 있다면 그 파일을 맵핍에 업로드하여 지도에 해당 위치들을 표시할 수 있습니다.

2. 관리하고 있는 매장이나 공장의 위치가 주소로만 기록되어 있는 데이터가 있으시면 업로드하여 위치를 확인해보세요. 지도에 표시하면 어느 매장끼리 가까운지, 또는 멀리 떨어져 있는지가 알 수 있습니다.

3. 카페 창업을 원하시나요? 내 관심 지역 주변에 다른 카페가 어떻게 분포되어 있는지 맵픽에서 확인해보세요. 카페 레이어를 켜시면 어디에 새로운 카페를 창업할 수 있는지 한 눈에 파악됩니다.

4. 현재 보유하고 있는 주소 혹은 좌표를 가지고 있는 엑셀데이터가 있으시면 맵픽에 업로드해보세요. 지도 기반에서 사용자님의 데이터를 한 눈에 파악이 가능하도록 변환해 드립니다.

이외에도 핫스팟 분석 등의 공간분석 기능 이용하여 지도상에서 다양한 활동이 가능하오니 로그인하셔서 나의 데이터를 관리하고 분석해보세요.

더 필요한 사항이 있으시면 알려주세요.

---

# 메인메뉴
메뉴는 챗봇의 메뉴와 맵픽의 메뉴로 구성되어 있습니다. 챗봇이 메뉴는 사용자의 이해를 돕기 위하여 자주 질문하는 내용으로 구성되어 있습니다. 맵픽의 메뉴는 맵픽소개, 지도, 맵갤러리, 공지사항으로 구성되어 있습니다.

## 1. 지도 빌더
지도빌더에서 제공되는 메인 메뉴 기능으로 새지도생성/지도검색/저장/기본정보 메뉴로 구성되어 있다.

### 1.1.1. 새 지도 생성
- 사용자가 새로운 지도를 생성할 수 있다.

### 1.1.2. 지도 검색
- 사용자가 저장한 지도 목록을 볼 수 있다.

### 1.1.3. 지도 저장
- 현재 지도를 저장한다.

### 1.1.4. 다른 이름으로 저장
- 현재 지도를 다른이름으로 저장한다.

### 1.1.5. 지도 TOC
- 사용자가 TOC에 있는 레이어 목록을 관리하고 조작할 수 있는 기능을 제공한다.

## 1.2. 지도 기본기능
지도에서 제공되는 지도 기본기능으로 주소검색, 지도제어 도구 영역으로 구성되어 있다.

### 1.2.2. 주소검색
- 주소검색은 통합검색, 도로명, 지번, PNU, 경위도 등으로 원하는 위치를 검색하고 이동하는 기능을 제공한다.

### 1.2.3. 지도제어 도구
- 지도에서 사용 가능한 다양한 기능을 제공한다.

## 1.3. 레이어 기능
지도에서 제공되는 레이어 기능으로 업로드, TOC, 스타일설정 등으로 구성되어 있다.

### 1.3.2. SHP 파일 업로드
- 사용자가 보유하고 있는 공간정보 파일을 지도에 업로드하여 지도에서 활용 할 수 있다.

### 1.3.3. 지오코딩 파일 업로드
- 사용자가 보유하고 있는 Excel 파일 및 CSV 파일을 지도에 업로드하여 지도에서 활용 할 수 있다.

### 1.3.4. 지오코딩 결과 알림
- 알림영역에서 지오코딩 작업 내역을 확인할 수 있다.

### 1.3.5. 지오코딩 결과 관리
- 지오코딩이 완료된 파일을 매핑시켜 레이어로 발행할 수 있다.

### 1.3.6. 웹 레이어 등록
- OGC 표준의 WMS/WFS/WMTS 레이어 주소를 지도에 등록하여 지도에서 활용 할 수 있다.

### 1.3.7. DXF 파일 업로드
- 사용자가 보유하고 있는 DXF 파일을 지도에 업로드하여 지도에서 활용 할 수 있다.

### 1.3.8. 레이어 검색 및 레이어 추가
- 사용자가 국가공간정보 레이어, 사용자데이터 레이어, 공통데이터 레이어, 행정데이터 레이어 등에서 레이어를 검색하여 현재 지도 내의 TOC에 추가할 수 있다.

### 1.3.9. 레이어 그룹 관리
- 레이어 그룹을 추가하여 레이어를 그룹별로 분류하여 레이어를 관리한다.

### 1.3.10. 레이어 정보
- 레이어의 정보를 확인할 수 있다.

### 1.3.11. 스타일 설정
- 사용자가 선택한 레이어에 스타일을 적용하여 표출한다.

### 1.3.12. 팝업 구성
- 사용자가 선택한 레이어에 팝업 구성을 설정한다.

### 1.3.13. 속성 설정
- 사용자가 선택한 레이어의 속성정보를 설정한다.

### 1.3.14. 속성 테이블
- 사용자가 레이어의 속성을 조회 및 편집, 활용한다.

### 1.3.15. 차트 설정
- 사용자가 레이어 속성정보로 차트를 생성한다.

### 1.3.16. 속성 필터
- 사용자가 선택한 레이어에 필터를 속성테이블에 적용하여 표출한다.

### 1.3.17. 공간 검색
- 사용자가 레이어 속성을 선택한 공간영역으로 속성정보를 필터링 한다.

### 1.3.18. 객체 편집모드
- 사용자가 선택한 레이어의 객체들을 편집할 수 있는 기능입니다.

## 1.4. 데이터 요약 분석
### 1.4.1. 공간조인 분석
- 공간 및 속성 관계에 따라 속성 레이어의 속성 정보를 결합하는 공간조인 분석을 수행한다.

#### 속성 조인
- 분석 대상 레이어와 비교 대상 레이어의 속성을 기준으로 ROW 병합.
- 분석 대상 레이어와 속성명이 일치하는 속성의 경우, ‘[속성명]_1’과 같은 형태로 생성됨.
- 분석 대상 레이어의 키 필드 : 분석 대상 레이어의 키 필드 중 병합의 기준이 될 필드
- 비교 대상 레이어의 키 필드 : 비교 대상 레이어의 키 필드 중 병합의 병합의 기준이 될 필드

#### 공간 조인
- 공간 관계 유형
  1. [동일함] 옵션은 분석 대상 레이어의 공간정보와 비교 대상 레이어의 공간정보가 정확히 일치해야 조인 조건이 만족된다.
  2. [교차함] 옵션은 분석 대상 레이어의 공간정보와 비교 대상 레이어의 공간 정보가 교차하면 조인 조건이 만족된다.
  3. [완전히 포함함] 옵션은 비교 대상 레이어의 공간정보가 분석 대상 레이어의 공간정보에 완전히 포함되 있어야 조건이 만족된다.
  4. [완전히 포함됨] 옵션은 분석 대상 레이어의 공간정보가 비교 대상 레이어의 공간정보에 완전히 포함되 있어야 조건이 만족된다.
  5. [일정한 거리 내에 있음] 옵션은 비교 대상 레이어의 공간정보가 분석 대상 레이어의 공간정보.
- 공간 조인 유형
  1. [조인되는 대상만 포함] 옵션 선택 시 선택한 조인 칼럼 값이 일치하는 경우에만 결과 레이어에 나타난다.
  2. [모든 대상 피처 유지] 옵션 선택 시 선택한 조인 칼럼 값이 일치하지 않는 경우에도 결과 레이어에 나타난다.

#### 조인 작업
  1. 일대일 조인 : 조인 조건을 만족시키는 ROW가 여러 개일 경우, 하나의 ROW만 조인.
  2. 일대다 조인 : 조인 조건을 만족시키는 ROW가 여러 개일 경우, 여러 개의 ROW를 조인. 합계/최소/최대/평균/표준편차

### 1.4.2. 주변집계 분석
- 비교 대상 레이어에서 지정된 거리 내에 있는 분석 대상 레이어를 레이어 유형(점/선/면)에 따라 집계(총건수/총길이/총면적) 분석한다.

#### 최근접 피처 요약
- 측정 방법 : 운전거리/직선거리
- 탐색 반경 : 기준 거리
- 탐색 반경 단위 : 기준거리 단위
- 경계 영역 반환 : 체크시, 결과 레이어에 비교 대상 레이어의 도형으로부터 기준되는 탐색 반경만큼 떨어진 영역 정보를 공간정보로 사용

#### 합계 필드
- 분석 결과로 나타나는 요약 정보를 나타낼 필드명
- 입력하지 않으면 요약 정보가 결과 레이어에 나타나지 않음. 비교 대상 레이어의 도형과 일정 거리 내에 있는 도형의 개수/총 길이/ 총 면적 통계 추가
- 분석대상 레이어의 숫자 필드로 통계를 내린다.
- 결과 레이어에 [통계구분(sum/min/max//avg/std)]_[분석대상 레이어의 필드명]과 같은 이름의 필드로 추가된다. 합계/최소/최대/평균/표준편차

### 1.4.3. 영역 내 집계 분석
- 비교 대상 레이어의 폴리곤 경계 내에 있는 분석 대상 레이어를 레이어 유형(점/선/면)에 따라 집계(총건수/총길이/총면적) 분석한다.

#### 합계 필드
- 분석 결과로 나타나는 요약 정보를 나타낼 필드명
- 입력하지 않으면 요약 정보가 결과 레이어에 나타나지 않음. 비교 대상 레이어의 도형과 일정 거리 내에 있는 도형의 개수/총 길이/ 총 면적

#### 통계 추가
- 분석대상 레이어의 숫자 필드로 통계를 내린다.
- 결과 레이어에 [통계구분(sum/min/max//avg/std)]_[분석대상 레이어의 필드명]과 같은 이름의 필드로 추가된다. 합계/최소/최대/평균/표준편차

### 1.4.4. 공간분포 패턴 분석
- 레이어의 공간 분포 패턴을 분석하여 요약 유형별로 중심을 추출한다.

#### 요약 유형
- 중심 피처 : 전체 피처들 중 위치적으로 가운데 잇는 피처 추출(평균 중심에서 가장 가까운 피처 추출)
  - 분석 대상 레이어가 라인일 경우, 결과값이 표출되지 않는다.
- 평균 중심 : extent의 중심(x좌표의 평균, y좌표의 평균) [평균 계산 필드]
  - 분석 대상 레이어에서 숫자필드. 선택시 해당 필드의 평균을 계산 하여 결과 레이어에 선택한 속성명으로 표출
- 중앙값 중심 : 각 포인트 간의 거리를 측정하여 중심위치를 추출 [중앙값 계산 필드]
  - 분석 대상 레이어의 숫자 필드. 선택한 필드의 중앙값을 계산하여 결과 레이어에 선택한 속성명으로 표출
- 타원 : 포인트 간의 표준편차 값을 추출하여 타원을 추출(타원의 중심 점이 표준편차 평균값)
  - 타원체의 크기(1SD/2SD/3SD)
  - 결과 레이어에 표현할 타원의 크기

#### 가중치 기준
- 상대적 중요성에 따라 위치에 가중치를 적용하는데 사용하는 필드
- 가중치 기준을 선택하지 않으면 모든 도형의 속성에 동일한 가중치가 부여되어 중심점 추출
- 가중치 기준을 선택하면 선택한 필드의 값에 따라 가중치가 부여되어 중심점 추출(값이 클수록 가중치가 높음)

## 1.5. 위치찾기 분석
### 1.5.1. 공간 조건 검색 분석
- 비교 레이어를 기준으로 공간검색 유형의 조건을 만족하는 분석 레이어의 객체를 검색한다.

### 1.5.2. 공간 조건 추출 분석
- 비교 레이어를 기준으로 공간검색 유형의 조건을 만족하는 분석 레이어의 객체를 검색 후 교차하는 영역으로 자르고 비교 레이어의 속성을 추가한다.

### 1.5.3. 중심찾기 분석
- 분석 대상 레이어 객체에서 결과 위치 설정에 따라 중심점을 찾는다.

## 1.6. 공간패턴 분석
### 1.6.1. 밀도계산
- 위치에 있는 포인트를 기반으로 군집 포인트의 개수가 많은 곳을 쉽게 식별할 수 있도록 시각화하는 분석을 수행한다.

### 1.6.2. 핫스팟
- 데이터의 공간 패턴에 통계적으로 유의미한 군집이 있는지를 격자 그리드로 시각화하는 분석을 수행한다.

## 1.7. 근접도 분석
### 1.7.1. 버퍼 분석
- 분석 대상 레이어 주위에 입력한 거리까지의 영역을 생성하는 작업을 수행한다.

## 1.8. 데이터 관리 분석
### 1.8.1. 경계디졸브
- 경계 또는 중첩되는 영역을 병합하여 단일 영역으로 생성하는 작업을 수행한다.

### 1.8.2. 공간 분할 생성
- 분석 대상 레이어를 입력한 분할타입으로 영역을 분할하여 생성하는 작업을 수행한다.

### 1.8.3. 레이어 병합
- 동일한 유형의 레이어를 하나의 새로운 레이어로 병합하여 생성하는 작업을 수행한다.

### 1.8.4. 레이어 중첩(지우기) 분석
- 동일한 유형의 두 레이어의 중첩되는 영역을 제외한 새 레이어로 속성을 포함하여 병합하는 작업을 수행한다.

### 1.8.5. 레이어 중첩(교차)
- 동일한 유형의 두 레이어의 교차되는 영역을 새 레이어로 속성을 포함하여 병합하는 작업을 수행한다.

### 1.8.6. 레이어 중첩(유니온)
- 동일한 유형의 두 레이어를 새 레이어로 속성을 포함하여 교차되는 영역은 자르기 후 병합하는 작업을 수행한다.

### 1.8.7. 클러스터링
- 포인트 레이어의 위치 근접도를 분석하여 그룹으로 묶어 포인트수를 시각화하는 작업을 수행한다.

### 1.8.8. 면적 계산
- 대상 레이어 내 피처들의 실제 면적과 실제 둘레 길이를 측정하는 작업을 수행한다.

### 1.8.9. 길이 계산
- 대상 레이어 내 피처들의 실제 길이를 측정하는 작업을 수행한다.

## 1.9. 좌표변환
### 1.9.1. 파일 좌표 변환
- Shape(ZIP), GeoJSON 파일 형태의 파일을 업로드하여 원하는 좌표계로 변환 후 다운로드하는 기능을 수행한다.

### 1.9.2. 단일 좌표 변환
- 사용자가 입력한 좌표계의 좌표를 원하는 좌표계로 변환하는 작업을 수행한다.

# 2. 앱빌더
## 2.1. 구성
앱(새앱, 검색), 상세설정, 미리보기, 저장 등의 메뉴로 구성되어 있다.

## 2.2. 표준/편집 테마
지도 빌더를 통해 제작된 지도를 표준, 편집 테마를 활용하여 앱의 레이아웃 및 스타일을 설정하고 위젯들을 선택하여 새로운 앱을 생성할 수 있다.

### 2.2.1. 앱 만들기 화면
- 테마 목록을 조회하고 테마를 선택하여 앱 편집 화면으로 이동한다.

### 2.2.2. 메인 설정
- 메인 패널에서 사용할 사용자 지도를 선택한다.

### 2.2.3. 레이아웃 설정
- 타이틀 및 제어도구 위치를 설정한다.

### 2.2.4. 테마 설정
- 헤더 및 푸터의 색상 및 타이틀 등 테마를 설정한다.

### 2.2.5. 위젯 설정
- 웹앱의 위젯을 추가하고 저장한다.

#### 헤더 영역 위젯
- 앱 기본정보: 웹앱에 추가된 지도에 대한 상세정보를 확인할 수 있는 위젯
- 레이어 영역 위젯: 레이어 검색, 레이어 스타일, 속성, 속성필터 등의 위젯

#### 상단 영역 위젯
- 북마크: 현재 지도의 위치를 저장할 수 있는 위젯
- 오버뷰: 오버뷰를 표출하는 위젯
- 주소 검색: 주소를 검색할 수 있는 위젯
- 행정경계 표시: 현재 지도의 행정경계 위치를 표시하고 다른 행정경계로 이동할 수 있는 위젯
- 저장: 지도를 PNG 또는 PDF로 저장할 수 있는 위젯
- 인쇄: 지도를 프린트 할 수 있는 위젯
- 피쳐속성폼: 위젯을 클릭한 후 지도상에 피쳐를 클릭하면 피쳐정보를 수정할 수 있는 위젯

#### 툴바 영역 위젯
- 홈: 지도 홈으로 이동하는 위젯
- 초기화: 지도에 그려진 임시 그래픽을 초기화하는 위젯
- 전체화면: 지도를 전체화면으로 확인할 수 있는 위젯
- 이동: 이전/다음으로 이동할 수 있는 위젯
- 분할지도: 2분할/3분할/4분할로 지도를 분할할 수 있는 위젯
- 그리기: 점/폴리곤/사각형/선/텍스트/곡선/원/버퍼를 지도상에 그릴 수 있는 위젯
- 측정: 지도상에서 면적/거리/반경/지점을 측정할 수 있는 위젯
- 회전/나침반: 지도상에서 Alt + Shift + 마우스 왼쪽 클릭 시 지도를 회전할 수 있는 위젯
- 배경지도: 배경지도를 변경할 수 있는 위젯

#### 하단 영역 위젯
- 확대/축소: 지도를 확대/축소할 수 있는 위젯

## 4.3. 제품 지원 사항
### 구분 설명

#### 적용 표준 및 권고안
- OGC공간정보 서비스 표준 준수: (WMS) 이미지 형태의 지도 제공 서비스 제공 시 해당 표준 준수, (WFS) 지리정보(Feature)에 대한 CRUD 서비스 제공 시 해당 표준 준수
- GeOn-Buider 등 웹 UI을 가진 프로그램은 W3C의 웹표준을 준수하여 개발하여 크롬, 엣지 등 웹표준을 준수하는 웹브라우저에서 구동함

#### 운영 지원 여부
- 운영 지원은 별도 운영 지원 계약을 통하여 지원
- 유지보수는 유상 유지보수 계약(통산 1년 단위)를 통하여 제공하며 유지보수 제공 및 유지 보수 내용
- 제공되는 유지보수 내용은 아래 내용을 포함함: 프로그램 오류에 대한 SW 오류 수정, 프로그램의 예측되는 오류를 선점 처리를 위한 예방 유지보수, HW 및 SW 환경 변화에 따른 SW 적응

# 6. 시스템 백업 및 복원
서비스 내의 안정적 제공을 위해 DB는 1일 1회 자동 백업을 합니다. 백업된 DB는 시스템 상의 중대 결함 발생 시 복구하는 데 사용됩니다. 백업 대상은 백업 당시 DB에 포함된 모든 내용이 백업 됩니다. DB 백업 원칙은 아래와 같습니다:
- 백업 시스템에 의해 주기적 백업 업무의 자동화
- 데이터량 변화에 따른 백업 주기의 조정 및 관리

# 7. 로그 관리
시스템의 운영 중에 발생하는 로그를 확인하는 방법을 설명합니다.

## 로그 확인 절차
1. WAS 터미널로 SSH 접속 연결한다.
2. 아래 명령어를 입력하여 로그를 확인한다.
3. 아래와 같이 로그를 확인할 수 있다.

# 8. 용어 및 약어
## 용어 설명

## API (application programming interface)
- API는 응용 프로그램에서 사용할 수 있도록, 운영 체제나 프로그래밍 언어가 제공하는 기능을 제어할 수 있게 만든 인터페이스를 뜻한다.

## Bessel 타원체
- Bessel 타원체는 1841년에 만들어졌으며 동경좌표계(Tokyo Datum)를 기준으로 한다. 장반경 6377397.155m, 단반경 6356078.963m, 편평율 299.1528128이다.

## CSV(comma-separated values)
- 몇 가지 필드를 쉼표(,)로 구분한 텍스트 데이터 및 텍스트 파일이다.

## Docker
- Docker는 애플리케이션을 신속하게 구축, 테스트 및 배포할 수 있는 소프트웨어 플랫폼입니다. Docker는 소프트웨어를 컨테이너라는 표준화된 유닛으로 패키징하며, 이 컨테이너에는 라이브러리, 시스템 도구, 코드, 런타임 등 소프트웨어를 실행하는 데 필요한 모든 것이 포함되어 있습니다. Docker를 사용하면 환경에 구애받지 않고 애플리케이션을 신속하게 배포 및 확장할 수 있으며 코드가 문제없이 실행될 것임을 확신할 수 있습니다.

## dxf
- PC용 캐드 시스템에서 파일교환을 위한 기본도면 파일 형식

## GeoJSON
- 위치정보를 갖는 점을 기반으로 체계적으로 지형을 표현하기 위해 설계된 개방형 공개 표준 형식

## GeoServer
- Geoserver(지오서버)는 지리공간 데이터를 공유하고 편집할 수 있는 Java로 개발된 오픈 소스 GIS 소프트웨어 서버이다.
- 상호운용성을 전제로 개발되었기 때문에, 개방형 표준을 사용하여 다양한 공간 데이터 소스를 서비스할 수 있게 한다.

## GIS(Geographic Information System)
- 넓게는 지리공간적으로 참조 가능한 모든 형태의 정보를 효과적으로 수집, 저장, 갱신, 조정, 분석, 표현할 수 있도록 설계된 컴퓨터의 하드웨어와 소프트웨어 및 지리적 자료, 인적자원의 통합체를 의미. 좁게는 전 국토의 지리공간정보를 디지털화하여 수치 지도(digital map)로 작성하고 다양한 정보통신기술을 통해 재해, 환경, 시설물, 국토공간 관리와 행정서비스에 활용하고자 하는 첨단정보시스템을 의미

## GRS80 타원체
- Geodetic Reference System 1980 타원체의 약자로 타원체의 형상, 지구 중심을 원점으로 정한 타원체. 민간 분야의 국제 협력으로 구축되었으므로 고정밀도로 정밀한 WGS84라 불리기도 함(개정을 통하여 WGS84 타원체와의 차이를 단반경 약 0.1mm 정도로 축소하였으므로 실용적으로 동일하게 취급 가능). 장반경 6378137.000m, 단반경 6356752.314m, 편평율 298.257222101임.

## Java
- 객체 지향 프로그래밍 언어로서 보안성이 뛰어나며 컴파일한 코드는 다른 운영 체제에서 사용할 수 있도록 클래스(class)로 제공된다.
- 객체 지향 언어인 C+ 언어의 객체 지향적인 장점을 살리면서 분산 환경을 지원하며 더욱 효율적이다.

## JavaScript API
- 서버 컴포넌트에서 REST API로 구현된 기능을 웹브라우저 클라이언트 Javascript 환경에서 쉽게 사용할 수 있도록 SDK 형태로 개발한 API

## Javascript (자바스크립트)
- 자바스크립트는 객체 기반의 스크립트 프로그래밍 언어이다. 이 언어는 웹 브라우저 내에서 주로 사용하며, 다른 응용 프로그램의 내장 객체에도 접근할 수 있는 기능을 가지고 있다. 또한 Node.js와 같은 런타임 환경과 같이 서버 프로그래밍에도 사용되고 있다.

## JSON(JavaScript Object Notation)
- JSON은 경량의 DATA교환 형식.
- 이 형식은 사람이 읽고 쓰기에 용이하며, 기계가 분석하고 생성함에도 용이. 속성:값 쌍 또는 “키:값 쌍”으로 이루어진 데이터 오브젝트를 전달하기 위해 인간이 읽을 수 있는 텍스트를 사용하는 개방형 표준 포맷이다.

## NAS
- 컴퓨터 네트워크에 연결된 파일 수준의 컴퓨터 기억장치

## OGC(개방형 공간정보 컨소시엄)
- 개방형 공간 정보 컨소시엄(Open Geospatial Consortium, OGC)은 1994년에 기원한 국제 표준화 기구이다. OGC에서 전 세계 500곳 이상의 상업, 정부, 비영리, 연구 단체들이 지리 공간적 콘텐츠와 서비스, 센서, 웹, 사물 인터넷, GIS 데이터 처리, 데이터 공유를 위한 개방형 표준의 개발 및 구현을 장려하는 콘센서스 프로세스에서 서로 협업한다.

## PNU
- 필지를 고유하게 식별할 수 있는 값으로 시도 코드 2자리, 시군구 코드 3자리, 읍면동 코드 3자리, 리 2자리, 필지구분 코드 1자리, 본번 코드 4자리, 부번 코드 4자리 총 19자리로 구성되어있다.
- Point of Interest
- 관심 지점 (POI로 약칭)은 누군가가 흥미롭거나 유용하다고 생각할 수 있는 POI 지도상의 특정 장소 또는 위치 지점입니다. 그것은 레스토랑, 호텔 또는 관광 명소일 수도 있고 ATM, 주유소, 학교 또는 공원과 같은 평범한 장소일 수도 있습니다. 관심 지점은 이벤트와 같이 일시적일 수도 있고 건물이나 도시와 같이 영구적일 수도 있습니다.

## PostgreSQL
- 확장 가능성 및 표준 준수를 강조하는 객체-관계형 데이터베이스 관리 시스템

## Proxy (프록시)
- 다른 서버 상의 자원을 찾는 클라이언트로부터 요청을 받아 중계하는 서버

## REST API
- REST(Representational state transfer) 아키텍처의 제약 조건을 준수하는 API(Application Programming Interface, 애플리케이션 프로그래밍 인터페이스). 자원의 식별, 메시지를 통한 리소스의 조작, 자기 서술적 메시지, 애플리케이션의 상태에 대한 엔진으로서 하이퍼미디어의 특성을 지님.

## SHP
- 위상관계를 가지지 않는 공간 데이터 형식을 말하며, 파일구조가 단순해서 구조화된 데이터에서는 거의 표준으로 사용되는 GIS 파일형식을 말한다. 배포되는 자원의 형식의 일종.
- ESRI사 제품의 파일포멧으로 도형 및 속장자료의 통합변환이 가능하다.

## TIFF
- 태그 붙은 화상 파일 형식이라는 뜻으로, 미국의 앨더스사(현재는 어도비 시스템스사에 흡수 합병)와 마이크로소프트사가 공동 개발한 래스터 화상 파일 형식

## TM 좌표계
- 우리나라는 국토를 네 구역으로 나누어 투영하며 투영된 지도에 N38도/E125도, N38도/E127도, N38도/E129도, N38도/E131도를 서부, 중부, 동부, 동해 원점으로 하여 각각 평면 직각 좌표계를 만들었다. 이 좌표계들을 서쪽에서부터 서부 좌표계, 중부 좌표계, 동부 좌표계, 동해 좌표계라고 하며 이들을 통합하여 지칭하는 것이 TM 좌표계이다.

## TOC(Table of contents)
- GIS에서 레이어의 목차 또는 지도 레이어의 관리를 의미.

## TXT
- 문서 파일 형식 중에서 가장 호환성이 높은 파일 형식

## URL
- 웹 문서의 각종 서비스를 제공하는 서버들에 있는 파일의 위치를 표시하는 표준

## UTM (universal transverse mercator)
- UTM 좌표계(Universal Transverse Mercator Coordinate System)는 전 지구상 점들의 위치를 통일된 체계로 나타내기 위한 격자 좌표 체계의 하나로 1947년에 개발되었다. UTM 좌표계에서는 지구를 경도 6° 간격의 세로 띠로 나누어 횡축 메르카토르 도법으로 그린 뒤, 위도 8° 간격으로 총 60×20 개의 격자로 나누어 각 세로 구역마다 설정된 원점에 대한 종·횡 좌표로 위치를 나타낸다. 지리 좌표계가 극지방으로 갈수록 직사각형이 크게 감소하는 반면 UTM 좌표계는 직사각형 모양을 유지하므로 거리, 면적, 방향 등을 나타내는 데 매우 편리하다는 장점이 있다.