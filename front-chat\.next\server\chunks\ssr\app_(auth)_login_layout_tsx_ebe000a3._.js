module.exports = {

"[project]/app/(auth)/login/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>LoginLayout),
    "metadata": (()=>metadata)
});
const metadata = {
    title: "로그인",
    description: "말로 만드는 지도 demo 로그인",
    keywords: [
        "로그인",
        "GeOn",
        "지온파스",
        "인증"
    ],
    openGraph: {
        title: "로그인",
        description: "말로 만드는 지도 demo 로그인.",
        type: "website"
    },
    twitter: {
        card: "summary_large_image",
        title: "로그인",
        description: "말로 만드는 지도 demo 로그인."
    },
    robots: {
        index: false,
        follow: false
    }
};
function LoginLayout({ children }) {
    return children;
}
}}),

};

//# sourceMappingURL=app_%28auth%29_login_layout_tsx_ebe000a3._.js.map