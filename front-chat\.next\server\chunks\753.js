"use strict";exports.id=753,exports.ids=[753],exports.modules={473:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},1771:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},2170:(e,t,n)=>{n.d(t,{a:()=>o});var r=n(91024),i=n(42664);function o(e,t,n){return function(e,t,n){let o=(0,i.a)(e,n?.in);if(isNaN(t))return(0,r.w)(n?.in||e,NaN);if(!t)return o;let l=o.getDate(),a=(0,r.w)(n?.in||e,o.getTime());return(a.setMonth(o.getMonth()+t+1,0),l>=a.getDate())?a:(o.setFullYear(a.getFullYear(),a.getMonth(),l),o)}(e,-t,n)}},5786:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},5829:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]])},7332:(e,t,n)=>{function r(e,t){return"function"==typeof e?e(t):e}function i(e,t){return n=>{t.setState(t=>({...t,[e]:r(n,t[e])}))}}function o(e){return e instanceof Function}n.d(t,{HT:()=>$,ZR:()=>H,h5:()=>q,hM:()=>U,kW:()=>W});function l(e,t,n){let r,i=[];return o=>{let l,a;n.key&&n.debug&&(l=Date.now());let s=e(o);if(!(s.length!==i.length||s.some((e,t)=>i[t]!==e)))return r;if(i=s,n.key&&n.debug&&(a=Date.now()),r=t(...s),null==n||null==n.onChange||n.onChange(r),n.key&&n.debug&&null!=n&&n.debug()){let e=Math.round((Date.now()-l)*100)/100,t=Math.round((Date.now()-a)*100)/100,r=t/16,i=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${i(t,5)} /${i(e,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*r,120))}deg 100% 31%);`,null==n?void 0:n.key)}return r}}function a(e,t,n,r){return{debug:()=>{var n;return null!=(n=null==e?void 0:e.debugAll)?n:e[t]},key:!1,onChange:r}}let s="debugHeaders";function u(e,t,n){var r;let i={id:null!=(r=n.id)?r:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{let e=[],t=n=>{n.subHeaders&&n.subHeaders.length&&n.subHeaders.map(t),e.push(n)};return t(i),e},getContext:()=>({table:e,header:i,column:t})};return e._features.forEach(t=>{null==t.createHeader||t.createHeader(i,e)}),i}function d(e,t,n,r){var i,o;let l=0,a=function(e,t){void 0===t&&(t=1),l=Math.max(l,t),e.filter(e=>e.getIsVisible()).forEach(e=>{var n;null!=(n=e.columns)&&n.length&&a(e.columns,t+1)},0)};a(e);let s=[],d=(e,t)=>{let i={depth:t,id:[r,`${t}`].filter(Boolean).join("_"),headers:[]},o=[];e.forEach(e=>{let l,a=[...o].reverse()[0],s=e.column.depth===i.depth,d=!1;if(s&&e.column.parent?l=e.column.parent:(l=e.column,d=!0),a&&(null==a?void 0:a.column)===l)a.subHeaders.push(e);else{let i=u(n,l,{id:[r,t,l.id,null==e?void 0:e.id].filter(Boolean).join("_"),isPlaceholder:d,placeholderId:d?`${o.filter(e=>e.column===l).length}`:void 0,depth:t,index:o.length});i.subHeaders.push(e),o.push(i)}i.headers.push(e),e.headerGroup=i}),s.push(i),t>0&&d(o,t-1)};d(t.map((e,t)=>u(n,e,{depth:l,index:t})),l-1),s.reverse();let c=e=>e.filter(e=>e.column.getIsVisible()).map(e=>{let t=0,n=0,r=[0];return e.subHeaders&&e.subHeaders.length?(r=[],c(e.subHeaders).forEach(e=>{let{colSpan:n,rowSpan:i}=e;t+=n,r.push(i)})):t=1,n+=Math.min(...r),e.colSpan=t,e.rowSpan=n,{colSpan:t,rowSpan:n}});return c(null!=(i=null==(o=s[0])?void 0:o.headers)?i:[]),s}let c=(e,t,n,r,i,o,s)=>{let u={id:t,index:r,original:n,depth:i,parentId:s,_valuesCache:{},_uniqueValuesCache:{},getValue:t=>{if(u._valuesCache.hasOwnProperty(t))return u._valuesCache[t];let n=e.getColumn(t);if(null!=n&&n.accessorFn)return u._valuesCache[t]=n.accessorFn(u.original,r),u._valuesCache[t]},getUniqueValues:t=>{if(u._uniqueValuesCache.hasOwnProperty(t))return u._uniqueValuesCache[t];let n=e.getColumn(t);if(null!=n&&n.accessorFn)return n.columnDef.getUniqueValues?u._uniqueValuesCache[t]=n.columnDef.getUniqueValues(u.original,r):u._uniqueValuesCache[t]=[u.getValue(t)],u._uniqueValuesCache[t]},renderValue:t=>{var n;return null!=(n=u.getValue(t))?n:e.options.renderFallbackValue},subRows:null!=o?o:[],getLeafRows:()=>(function(e,t){let n=[],r=e=>{e.forEach(e=>{n.push(e);let i=t(e);null!=i&&i.length&&r(i)})};return r(e),n})(u.subRows,e=>e.subRows),getParentRow:()=>u.parentId?e.getRow(u.parentId,!0):void 0,getParentRows:()=>{let e=[],t=u;for(;;){let n=t.getParentRow();if(!n)break;e.push(n),t=n}return e.reverse()},getAllCells:l(()=>[e.getAllLeafColumns()],t=>t.map(t=>(function(e,t,n,r){let i={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(r),renderValue:()=>{var t;return null!=(t=i.getValue())?t:e.options.renderFallbackValue},getContext:l(()=>[e,n,t,i],(e,t,n,r)=>({table:e,column:t,row:n,cell:r,getValue:r.getValue,renderValue:r.renderValue}),a(e.options,"debugCells","cell.getContext"))};return e._features.forEach(r=>{null==r.createCell||r.createCell(i,n,t,e)},{}),i})(e,u,t,t.id)),a(e.options,"debugRows","getAllCells")),_getAllCellsByColumnId:l(()=>[u.getAllCells()],e=>e.reduce((e,t)=>(e[t.column.id]=t,e),{}),a(e.options,"debugRows","getAllCellsByColumnId"))};for(let t=0;t<e._features.length;t++){let n=e._features[t];null==n||null==n.createRow||n.createRow(u,e)}return u},p=(e,t,n)=>{var r,i;let o=null==n||null==(r=n.toString())?void 0:r.toLowerCase();return!!(null==(i=e.getValue(t))||null==(i=i.toString())||null==(i=i.toLowerCase())?void 0:i.includes(o))};p.autoRemove=e=>S(e);let h=(e,t,n)=>{var r;return!!(null==(r=e.getValue(t))||null==(r=r.toString())?void 0:r.includes(n))};h.autoRemove=e=>S(e);let f=(e,t,n)=>{var r;return(null==(r=e.getValue(t))||null==(r=r.toString())?void 0:r.toLowerCase())===(null==n?void 0:n.toLowerCase())};f.autoRemove=e=>S(e);let g=(e,t,n)=>{var r;return null==(r=e.getValue(t))?void 0:r.includes(n)};g.autoRemove=e=>S(e);let m=(e,t,n)=>!n.some(n=>{var r;return!(null!=(r=e.getValue(t))&&r.includes(n))});m.autoRemove=e=>S(e)||!(null!=e&&e.length);let v=(e,t,n)=>n.some(n=>{var r;return null==(r=e.getValue(t))?void 0:r.includes(n)});v.autoRemove=e=>S(e)||!(null!=e&&e.length);let y=(e,t,n)=>e.getValue(t)===n;y.autoRemove=e=>S(e);let b=(e,t,n)=>e.getValue(t)==n;b.autoRemove=e=>S(e);let w=(e,t,n)=>{let[r,i]=n,o=e.getValue(t);return o>=r&&o<=i};w.resolveFilterValue=e=>{let[t,n]=e,r="number"!=typeof t?parseFloat(t):t,i="number"!=typeof n?parseFloat(n):n,o=null===t||Number.isNaN(r)?-1/0:r,l=null===n||Number.isNaN(i)?1/0:i;if(o>l){let e=o;o=l,l=e}return[o,l]},w.autoRemove=e=>S(e)||S(e[0])&&S(e[1]);let x={includesString:p,includesStringSensitive:h,equalsString:f,arrIncludes:g,arrIncludesAll:m,arrIncludesSome:v,equals:y,weakEquals:b,inNumberRange:w};function S(e){return null==e||""===e}function C(e,t,n){return!!e&&!!e.autoRemove&&e.autoRemove(t,n)||void 0===t||"string"==typeof t&&!t}let R={sum:(e,t,n)=>n.reduce((t,n)=>{let r=n.getValue(e);return t+("number"==typeof r?r:0)},0),min:(e,t,n)=>{let r;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(r>n||void 0===r&&n>=n)&&(r=n)}),r},max:(e,t,n)=>{let r;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(r<n||void 0===r&&n>=n)&&(r=n)}),r},extent:(e,t,n)=>{let r,i;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(void 0===r?n>=n&&(r=i=n):(r>n&&(r=n),i<n&&(i=n)))}),[r,i]},mean:(e,t)=>{let n=0,r=0;if(t.forEach(t=>{let i=t.getValue(e);null!=i&&(i*=1)>=i&&(++n,r+=i)}),n)return r/n},median:(e,t)=>{if(!t.length)return;let n=t.map(t=>t.getValue(e));if(!function(e){return Array.isArray(e)&&e.every(e=>"number"==typeof e)}(n))return;if(1===n.length)return n[0];let r=Math.floor(n.length/2),i=n.sort((e,t)=>e-t);return n.length%2!=0?i[r]:(i[r-1]+i[r])/2},unique:(e,t)=>Array.from(new Set(t.map(t=>t.getValue(e))).values()),uniqueCount:(e,t)=>new Set(t.map(t=>t.getValue(e))).size,count:(e,t)=>t.length},P=()=>({left:[],right:[]}),E={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},A=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),D=null;function I(e){return"touchstart"===e.type}function M(e,t){return t?"center"===t?e.getCenterVisibleLeafColumns():"left"===t?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}let T=()=>({pageIndex:0,pageSize:10}),k=()=>({top:[],bottom:[]}),L=(e,t,n,r,i)=>{var o;let l=i.getRow(t,!0);n?(l.getCanMultiSelect()||Object.keys(e).forEach(t=>delete e[t]),l.getCanSelect()&&(e[t]=!0)):delete e[t],r&&null!=(o=l.subRows)&&o.length&&l.getCanSelectSubRows()&&l.subRows.forEach(t=>L(e,t.id,n,r,i))};function F(e,t){let n=e.getState().rowSelection,r=[],i={},o=function(e,t){return e.map(e=>{var t;let l=O(e,n);if(l&&(r.push(e),i[e.id]=e),null!=(t=e.subRows)&&t.length&&(e={...e,subRows:o(e.subRows)}),l)return e}).filter(Boolean)};return{rows:o(t.rows),flatRows:r,rowsById:i}}function O(e,t){var n;return null!=(n=t[e.id])&&n}function N(e,t,n){var r;if(!(null!=(r=e.subRows)&&r.length))return!1;let i=!0,o=!1;return e.subRows.forEach(e=>{if((!o||i)&&(e.getCanSelect()&&(O(e,t)?o=!0:i=!1),e.subRows&&e.subRows.length)){let n=N(e,t);"all"===n?o=!0:("some"===n&&(o=!0),i=!1)}}),i?"all":!!o&&"some"}let V=/([0-9]+)/gm;function j(e,t){return e===t?0:e>t?1:-1}function B(e){return"number"==typeof e?isNaN(e)||e===1/0||e===-1/0?"":String(e):"string"==typeof e?e:""}function G(e,t){let n=e.split(V).filter(Boolean),r=t.split(V).filter(Boolean);for(;n.length&&r.length;){let e=n.shift(),t=r.shift(),i=parseInt(e,10),o=parseInt(t,10),l=[i,o].sort();if(isNaN(l[0])){if(e>t)return 1;if(t>e)return -1;continue}if(isNaN(l[1]))return isNaN(i)?-1:1;if(i>o)return 1;if(o>i)return -1}return n.length-r.length}let _={alphanumeric:(e,t,n)=>G(B(e.getValue(n)).toLowerCase(),B(t.getValue(n)).toLowerCase()),alphanumericCaseSensitive:(e,t,n)=>G(B(e.getValue(n)),B(t.getValue(n))),text:(e,t,n)=>j(B(e.getValue(n)).toLowerCase(),B(t.getValue(n)).toLowerCase()),textCaseSensitive:(e,t,n)=>j(B(e.getValue(n)),B(t.getValue(n))),datetime:(e,t,n)=>{let r=e.getValue(n),i=t.getValue(n);return r>i?1:r<i?-1:0},basic:(e,t,n)=>j(e.getValue(n),t.getValue(n))},z=[{createTable:e=>{e.getHeaderGroups=l(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,i)=>{var o,l;let a=null!=(o=null==r?void 0:r.map(e=>n.find(t=>t.id===e)).filter(Boolean))?o:[],s=null!=(l=null==i?void 0:i.map(e=>n.find(t=>t.id===e)).filter(Boolean))?l:[];return d(t,[...a,...n.filter(e=>!(null!=r&&r.includes(e.id))&&!(null!=i&&i.includes(e.id))),...s],e)},a(e.options,s,"getHeaderGroups")),e.getCenterHeaderGroups=l(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,i)=>d(t,n=n.filter(e=>!(null!=r&&r.includes(e.id))&&!(null!=i&&i.includes(e.id))),e,"center"),a(e.options,s,"getCenterHeaderGroups")),e.getLeftHeaderGroups=l(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,r)=>{var i;return d(t,null!=(i=null==r?void 0:r.map(e=>n.find(t=>t.id===e)).filter(Boolean))?i:[],e,"left")},a(e.options,s,"getLeftHeaderGroups")),e.getRightHeaderGroups=l(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,r)=>{var i;return d(t,null!=(i=null==r?void 0:r.map(e=>n.find(t=>t.id===e)).filter(Boolean))?i:[],e,"right")},a(e.options,s,"getRightHeaderGroups")),e.getFooterGroups=l(()=>[e.getHeaderGroups()],e=>[...e].reverse(),a(e.options,s,"getFooterGroups")),e.getLeftFooterGroups=l(()=>[e.getLeftHeaderGroups()],e=>[...e].reverse(),a(e.options,s,"getLeftFooterGroups")),e.getCenterFooterGroups=l(()=>[e.getCenterHeaderGroups()],e=>[...e].reverse(),a(e.options,s,"getCenterFooterGroups")),e.getRightFooterGroups=l(()=>[e.getRightHeaderGroups()],e=>[...e].reverse(),a(e.options,s,"getRightFooterGroups")),e.getFlatHeaders=l(()=>[e.getHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,s,"getFlatHeaders")),e.getLeftFlatHeaders=l(()=>[e.getLeftHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,s,"getLeftFlatHeaders")),e.getCenterFlatHeaders=l(()=>[e.getCenterHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,s,"getCenterFlatHeaders")),e.getRightFlatHeaders=l(()=>[e.getRightHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,s,"getRightFlatHeaders")),e.getCenterLeafHeaders=l(()=>[e.getCenterFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),a(e.options,s,"getCenterLeafHeaders")),e.getLeftLeafHeaders=l(()=>[e.getLeftFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),a(e.options,s,"getLeftLeafHeaders")),e.getRightLeafHeaders=l(()=>[e.getRightFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),a(e.options,s,"getRightLeafHeaders")),e.getLeafHeaders=l(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(e,t,n)=>{var r,i,o,l,a,s;return[...null!=(r=null==(i=e[0])?void 0:i.headers)?r:[],...null!=(o=null==(l=t[0])?void 0:l.headers)?o:[],...null!=(a=null==(s=n[0])?void 0:s.headers)?a:[]].map(e=>e.getLeafHeaders()).flat()},a(e.options,s,"getLeafHeaders"))}},{getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:i("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=n=>{e.getCanHide()&&t.setColumnVisibility(t=>({...t,[e.id]:null!=n?n:!e.getIsVisible()}))},e.getIsVisible=()=>{var n,r;let i=e.columns;return null==(n=i.length?i.some(e=>e.getIsVisible()):null==(r=t.getState().columnVisibility)?void 0:r[e.id])||n},e.getCanHide=()=>{var n,r;return(null==(n=e.columnDef.enableHiding)||n)&&(null==(r=t.options.enableHiding)||r)},e.getToggleVisibilityHandler=()=>t=>{null==e.toggleVisibility||e.toggleVisibility(t.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=l(()=>[e.getAllCells(),t.getState().columnVisibility],e=>e.filter(e=>e.column.getIsVisible()),a(t.options,"debugRows","_getAllVisibleCells")),e.getVisibleCells=l(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(e,t,n)=>[...e,...t,...n],a(t.options,"debugRows","getVisibleCells"))},createTable:e=>{let t=(t,n)=>l(()=>[n(),n().filter(e=>e.getIsVisible()).map(e=>e.id).join("_")],e=>e.filter(e=>null==e.getIsVisible?void 0:e.getIsVisible()),a(e.options,"debugColumns",t));e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>null==e.options.onColumnVisibilityChange?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var n;e.setColumnVisibility(t?{}:null!=(n=e.initialState.columnVisibility)?n:{})},e.toggleAllColumnsVisible=t=>{var n;t=null!=(n=t)?n:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((e,n)=>({...e,[n.id]:t||!(null!=n.getCanHide&&n.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(e=>!(null!=e.getIsVisible&&e.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(e=>null==e.getIsVisible?void 0:e.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var n;e.toggleAllColumnsVisible(null==(n=t.target)?void 0:n.checked)}}},{getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:i("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=l(e=>[M(t,e)],t=>t.findIndex(t=>t.id===e.id),a(t.options,"debugColumns","getIndex")),e.getIsFirstColumn=n=>{var r;return(null==(r=M(t,n)[0])?void 0:r.id)===e.id},e.getIsLastColumn=n=>{var r;let i=M(t,n);return(null==(r=i[i.length-1])?void 0:r.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>null==e.options.onColumnOrderChange?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var n;e.setColumnOrder(t?[]:null!=(n=e.initialState.columnOrder)?n:[])},e._getOrderColumnsFn=l(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(e,t,n)=>r=>{let i=[];if(null!=e&&e.length){let t=[...e],n=[...r];for(;n.length&&t.length;){let e=t.shift(),r=n.findIndex(t=>t.id===e);r>-1&&i.push(n.splice(r,1)[0])}i=[...i,...n]}else i=r;return function(e,t,n){if(!(null!=t&&t.length)||!n)return e;let r=e.filter(e=>!t.includes(e.id));return"remove"===n?r:[...t.map(t=>e.find(e=>e.id===t)).filter(Boolean),...r]}(i,t,n)},a(e.options,"debugTable","_getOrderColumnsFn"))}},{getInitialState:e=>({columnPinning:P(),...e}),getDefaultOptions:e=>({onColumnPinningChange:i("columnPinning",e)}),createColumn:(e,t)=>{e.pin=n=>{let r=e.getLeafColumns().map(e=>e.id).filter(Boolean);t.setColumnPinning(e=>{var t,i,o,l,a,s;return"right"===n?{left:(null!=(o=null==e?void 0:e.left)?o:[]).filter(e=>!(null!=r&&r.includes(e))),right:[...(null!=(l=null==e?void 0:e.right)?l:[]).filter(e=>!(null!=r&&r.includes(e))),...r]}:"left"===n?{left:[...(null!=(a=null==e?void 0:e.left)?a:[]).filter(e=>!(null!=r&&r.includes(e))),...r],right:(null!=(s=null==e?void 0:e.right)?s:[]).filter(e=>!(null!=r&&r.includes(e)))}:{left:(null!=(t=null==e?void 0:e.left)?t:[]).filter(e=>!(null!=r&&r.includes(e))),right:(null!=(i=null==e?void 0:e.right)?i:[]).filter(e=>!(null!=r&&r.includes(e)))}})},e.getCanPin=()=>e.getLeafColumns().some(e=>{var n,r,i;return(null==(n=e.columnDef.enablePinning)||n)&&(null==(r=null!=(i=t.options.enableColumnPinning)?i:t.options.enablePinning)||r)}),e.getIsPinned=()=>{let n=e.getLeafColumns().map(e=>e.id),{left:r,right:i}=t.getState().columnPinning,o=n.some(e=>null==r?void 0:r.includes(e)),l=n.some(e=>null==i?void 0:i.includes(e));return o?"left":!!l&&"right"},e.getPinnedIndex=()=>{var n,r;let i=e.getIsPinned();return i?null!=(n=null==(r=t.getState().columnPinning)||null==(r=r[i])?void 0:r.indexOf(e.id))?n:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=l(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(e,t,n)=>{let r=[...null!=t?t:[],...null!=n?n:[]];return e.filter(e=>!r.includes(e.column.id))},a(t.options,"debugRows","getCenterVisibleCells")),e.getLeftVisibleCells=l(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"left"})),a(t.options,"debugRows","getLeftVisibleCells")),e.getRightVisibleCells=l(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"right"})),a(t.options,"debugRows","getRightVisibleCells"))},createTable:e=>{e.setColumnPinning=t=>null==e.options.onColumnPinningChange?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var n,r;return e.setColumnPinning(t?P():null!=(n=null==(r=e.initialState)?void 0:r.columnPinning)?n:P())},e.getIsSomeColumnsPinned=t=>{var n,r,i;let o=e.getState().columnPinning;return t?!!(null==(n=o[t])?void 0:n.length):!!((null==(r=o.left)?void 0:r.length)||(null==(i=o.right)?void 0:i.length))},e.getLeftLeafColumns=l(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),a(e.options,"debugColumns","getLeftLeafColumns")),e.getRightLeafColumns=l(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),a(e.options,"debugColumns","getRightLeafColumns")),e.getCenterLeafColumns=l(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(e,t,n)=>{let r=[...null!=t?t:[],...null!=n?n:[]];return e.filter(e=>!r.includes(e.id))},a(e.options,"debugColumns","getCenterLeafColumns"))}},{createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},{getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:i("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{let n=t.getCoreRowModel().flatRows[0],r=null==n?void 0:n.getValue(e.id);return"string"==typeof r?x.includesString:"number"==typeof r?x.inNumberRange:"boolean"==typeof r||null!==r&&"object"==typeof r?x.equals:Array.isArray(r)?x.arrIncludes:x.weakEquals},e.getFilterFn=()=>{var n,r;return o(e.columnDef.filterFn)?e.columnDef.filterFn:"auto"===e.columnDef.filterFn?e.getAutoFilterFn():null!=(n=null==(r=t.options.filterFns)?void 0:r[e.columnDef.filterFn])?n:x[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,r,i;return(null==(n=e.columnDef.enableColumnFilter)||n)&&(null==(r=t.options.enableColumnFilters)||r)&&(null==(i=t.options.enableFilters)||i)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return null==(n=t.getState().columnFilters)||null==(n=n.find(t=>t.id===e.id))?void 0:n.value},e.getFilterIndex=()=>{var n,r;return null!=(n=null==(r=t.getState().columnFilters)?void 0:r.findIndex(t=>t.id===e.id))?n:-1},e.setFilterValue=n=>{t.setColumnFilters(t=>{var i,o;let l=e.getFilterFn(),a=null==t?void 0:t.find(t=>t.id===e.id),s=r(n,a?a.value:void 0);if(C(l,s,e))return null!=(i=null==t?void 0:t.filter(t=>t.id!==e.id))?i:[];let u={id:e.id,value:s};return a?null!=(o=null==t?void 0:t.map(t=>t.id===e.id?u:t))?o:[]:null!=t&&t.length?[...t,u]:[u]})}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{let n=e.getAllLeafColumns();null==e.options.onColumnFiltersChange||e.options.onColumnFiltersChange(e=>{var i;return null==(i=r(t,e))?void 0:i.filter(e=>{let t=n.find(t=>t.id===e.id);return!(t&&C(t.getFilterFn(),e.value,t))&&!0})})},e.resetColumnFilters=t=>{var n,r;e.setColumnFilters(t?[]:null!=(n=null==(r=e.initialState)?void 0:r.columnFilters)?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel)?e.getPreFilteredRowModel():e._getFilteredRowModel()}},{createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},{getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:i("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n;let r=null==(n=e.getCoreRowModel().flatRows[0])||null==(n=n._getAllCellsByColumnId()[t.id])?void 0:n.getValue();return"string"==typeof r||"number"==typeof r}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var n,r,i,o;return(null==(n=e.columnDef.enableGlobalFilter)||n)&&(null==(r=t.options.enableGlobalFilter)||r)&&(null==(i=t.options.enableFilters)||i)&&(null==(o=null==t.options.getColumnCanGlobalFilter?void 0:t.options.getColumnCanGlobalFilter(e))||o)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>x.includesString,e.getGlobalFilterFn=()=>{var t,n;let{globalFilterFn:r}=e.options;return o(r)?r:"auto"===r?e.getGlobalAutoFilterFn():null!=(t=null==(n=e.options.filterFns)?void 0:n[r])?t:x[r]},e.setGlobalFilter=t=>{null==e.options.onGlobalFilterChange||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},{getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:i("sorting",e),isMultiSortEvent:e=>e.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{let n=t.getFilteredRowModel().flatRows.slice(10),r=!1;for(let t of n){let n=null==t?void 0:t.getValue(e.id);if("[object Date]"===Object.prototype.toString.call(n))return _.datetime;if("string"==typeof n&&(r=!0,n.split(V).length>1))return _.alphanumeric}return r?_.text:_.basic},e.getAutoSortDir=()=>{let n=t.getFilteredRowModel().flatRows[0];return"string"==typeof(null==n?void 0:n.getValue(e.id))?"asc":"desc"},e.getSortingFn=()=>{var n,r;if(!e)throw Error();return o(e.columnDef.sortingFn)?e.columnDef.sortingFn:"auto"===e.columnDef.sortingFn?e.getAutoSortingFn():null!=(n=null==(r=t.options.sortingFns)?void 0:r[e.columnDef.sortingFn])?n:_[e.columnDef.sortingFn]},e.toggleSorting=(n,r)=>{let i=e.getNextSortingOrder(),o=null!=n;t.setSorting(l=>{let a,s=null==l?void 0:l.find(t=>t.id===e.id),u=null==l?void 0:l.findIndex(t=>t.id===e.id),d=[],c=o?n:"desc"===i;if("toggle"!=(a=null!=l&&l.length&&e.getCanMultiSort()&&r?s?"toggle":"add":null!=l&&l.length&&u!==l.length-1?"replace":s?"toggle":"replace")||o||i||(a="remove"),"add"===a){var p;(d=[...l,{id:e.id,desc:c}]).splice(0,d.length-(null!=(p=t.options.maxMultiSortColCount)?p:Number.MAX_SAFE_INTEGER))}else d="toggle"===a?l.map(t=>t.id===e.id?{...t,desc:c}:t):"remove"===a?l.filter(t=>t.id!==e.id):[{id:e.id,desc:c}];return d})},e.getFirstSortDir=()=>{var n,r;return(null!=(n=null!=(r=e.columnDef.sortDescFirst)?r:t.options.sortDescFirst)?n:"desc"===e.getAutoSortDir())?"desc":"asc"},e.getNextSortingOrder=n=>{var r,i;let o=e.getFirstSortDir(),l=e.getIsSorted();return l?(l===o||null!=(r=t.options.enableSortingRemoval)&&!r||!!n&&null!=(i=t.options.enableMultiRemove)&&!i)&&("desc"===l?"asc":"desc"):o},e.getCanSort=()=>{var n,r;return(null==(n=e.columnDef.enableSorting)||n)&&(null==(r=t.options.enableSorting)||r)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,r;return null!=(n=null!=(r=e.columnDef.enableMultiSort)?r:t.options.enableMultiSort)?n:!!e.accessorFn},e.getIsSorted=()=>{var n;let r=null==(n=t.getState().sorting)?void 0:n.find(t=>t.id===e.id);return!!r&&(r.desc?"desc":"asc")},e.getSortIndex=()=>{var n,r;return null!=(n=null==(r=t.getState().sorting)?void 0:r.findIndex(t=>t.id===e.id))?n:-1},e.clearSorting=()=>{t.setSorting(t=>null!=t&&t.length?t.filter(t=>t.id!==e.id):[])},e.getToggleSortingHandler=()=>{let n=e.getCanSort();return r=>{n&&(null==r.persist||r.persist(),null==e.toggleSorting||e.toggleSorting(void 0,!!e.getCanMultiSort()&&(null==t.options.isMultiSortEvent?void 0:t.options.isMultiSortEvent(r))))}}},createTable:e=>{e.setSorting=t=>null==e.options.onSortingChange?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var n,r;e.setSorting(t?[]:null!=(n=null==(r=e.initialState)?void 0:r.sorting)?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel)?e.getPreSortedRowModel():e._getSortedRowModel()}},{getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return null!=(t=null==(n=e.getValue())||null==n.toString?void 0:n.toString())?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:i("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(t=>null!=t&&t.includes(e.id)?t.filter(t=>t!==e.id):[...null!=t?t:[],e.id])},e.getCanGroup=()=>{var n,r;return(null==(n=e.columnDef.enableGrouping)||n)&&(null==(r=t.options.enableGrouping)||r)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{let t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{let n=t.getCoreRowModel().flatRows[0],r=null==n?void 0:n.getValue(e.id);return"number"==typeof r?R.sum:"[object Date]"===Object.prototype.toString.call(r)?R.extent:void 0},e.getAggregationFn=()=>{var n,r;if(!e)throw Error();return o(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:"auto"===e.columnDef.aggregationFn?e.getAutoAggregationFn():null!=(n=null==(r=t.options.aggregationFns)?void 0:r[e.columnDef.aggregationFn])?n:R[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>null==e.options.onGroupingChange?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var n,r;e.setGrouping(t?[]:null!=(n=null==(r=e.initialState)?void 0:r.grouping)?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel)?e.getPreGroupedRowModel():e._getGroupedRowModel()},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];let r=t.getColumn(n);return null!=r&&r.columnDef.getGroupingValue?(e._groupingValuesCache[n]=r.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,t,n,r)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var t;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!(null!=(t=n.subRows)&&t.length)}}},{getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:i("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;e._autoResetExpanded=()=>{var r,i;if(!t)return void e._queue(()=>{t=!0});if(null!=(r=null!=(i=e.options.autoResetAll)?i:e.options.autoResetExpanded)?r:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=t=>null==e.options.onExpandedChange?void 0:e.options.onExpandedChange(t),e.toggleAllRowsExpanded=t=>{(null!=t?t:!e.getIsAllRowsExpanded())?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=t=>{var n,r;e.setExpanded(t?{}:null!=(n=null==(r=e.initialState)?void 0:r.expanded)?n:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(e=>e.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>t=>{null==t.persist||t.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{let t=e.getState().expanded;return!0===t||Object.values(t).some(Boolean)},e.getIsAllRowsExpanded=()=>{let t=e.getState().expanded;return"boolean"==typeof t?!0===t:!(!Object.keys(t).length||e.getRowModel().flatRows.some(e=>!e.getIsExpanded()))},e.getExpandedDepth=()=>{let t=0;return(!0===e.getState().expanded?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(e=>{let n=e.split(".");t=Math.max(t,n.length)}),t},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel)?e.getPreExpandedRowModel():e._getExpandedRowModel()},createRow:(e,t)=>{e.toggleExpanded=n=>{t.setExpanded(r=>{var i;let o=!0===r||!!(null!=r&&r[e.id]),l={};if(!0===r?Object.keys(t.getRowModel().rowsById).forEach(e=>{l[e]=!0}):l=r,n=null!=(i=n)?i:!o,!o&&n)return{...l,[e.id]:!0};if(o&&!n){let{[e.id]:t,...n}=l;return n}return r})},e.getIsExpanded=()=>{var n;let r=t.getState().expanded;return!!(null!=(n=null==t.options.getIsRowExpanded?void 0:t.options.getIsRowExpanded(e))?n:!0===r||(null==r?void 0:r[e.id]))},e.getCanExpand=()=>{var n,r,i;return null!=(n=null==t.options.getRowCanExpand?void 0:t.options.getRowCanExpand(e))?n:(null==(r=t.options.enableExpanding)||r)&&!!(null!=(i=e.subRows)&&i.length)},e.getIsAllParentsExpanded=()=>{let n=!0,r=e;for(;n&&r.parentId;)n=(r=t.getRow(r.parentId,!0)).getIsExpanded();return n},e.getToggleExpandedHandler=()=>{let t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},{getInitialState:e=>({...e,pagination:{...T(),...null==e?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:i("pagination",e)}),createTable:e=>{let t=!1,n=!1;e._autoResetPageIndex=()=>{var r,i;if(!t)return void e._queue(()=>{t=!0});if(null!=(r=null!=(i=e.options.autoResetAll)?i:e.options.autoResetPageIndex)?r:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=t=>null==e.options.onPaginationChange?void 0:e.options.onPaginationChange(e=>r(t,e)),e.resetPagination=t=>{var n;e.setPagination(t?T():null!=(n=e.initialState.pagination)?n:T())},e.setPageIndex=t=>{e.setPagination(n=>{let i=r(t,n.pageIndex);return i=Math.max(0,Math.min(i,void 0===e.options.pageCount||-1===e.options.pageCount?Number.MAX_SAFE_INTEGER:e.options.pageCount-1)),{...n,pageIndex:i}})},e.resetPageIndex=t=>{var n,r;e.setPageIndex(t?0:null!=(n=null==(r=e.initialState)||null==(r=r.pagination)?void 0:r.pageIndex)?n:0)},e.resetPageSize=t=>{var n,r;e.setPageSize(t?10:null!=(n=null==(r=e.initialState)||null==(r=r.pagination)?void 0:r.pageSize)?n:10)},e.setPageSize=t=>{e.setPagination(e=>{let n=Math.max(1,r(t,e.pageSize)),i=Math.floor(e.pageSize*e.pageIndex/n);return{...e,pageIndex:i,pageSize:n}})},e.setPageCount=t=>e.setPagination(n=>{var i;let o=r(t,null!=(i=e.options.pageCount)?i:-1);return"number"==typeof o&&(o=Math.max(-1,o)),{...n,pageCount:o}}),e.getPageOptions=l(()=>[e.getPageCount()],e=>{let t=[];return e&&e>0&&(t=[...Array(e)].fill(null).map((e,t)=>t)),t},a(e.options,"debugTable","getPageOptions")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{let{pageIndex:t}=e.getState().pagination,n=e.getPageCount();return -1===n||0!==n&&t<n-1},e.previousPage=()=>e.setPageIndex(e=>e-1),e.nextPage=()=>e.setPageIndex(e=>e+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel)?e.getPrePaginationRowModel():e._getPaginationRowModel(),e.getPageCount=()=>{var t;return null!=(t=e.options.pageCount)?t:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var t;return null!=(t=e.options.rowCount)?t:e.getPrePaginationRowModel().rows.length}}},{getInitialState:e=>({rowPinning:k(),...e}),getDefaultOptions:e=>({onRowPinningChange:i("rowPinning",e)}),createRow:(e,t)=>{e.pin=(n,r,i)=>{let o=r?e.getLeafRows().map(e=>{let{id:t}=e;return t}):[],l=new Set([...i?e.getParentRows().map(e=>{let{id:t}=e;return t}):[],e.id,...o]);t.setRowPinning(e=>{var t,r,i,o,a,s;return"bottom"===n?{top:(null!=(i=null==e?void 0:e.top)?i:[]).filter(e=>!(null!=l&&l.has(e))),bottom:[...(null!=(o=null==e?void 0:e.bottom)?o:[]).filter(e=>!(null!=l&&l.has(e))),...Array.from(l)]}:"top"===n?{top:[...(null!=(a=null==e?void 0:e.top)?a:[]).filter(e=>!(null!=l&&l.has(e))),...Array.from(l)],bottom:(null!=(s=null==e?void 0:e.bottom)?s:[]).filter(e=>!(null!=l&&l.has(e)))}:{top:(null!=(t=null==e?void 0:e.top)?t:[]).filter(e=>!(null!=l&&l.has(e))),bottom:(null!=(r=null==e?void 0:e.bottom)?r:[]).filter(e=>!(null!=l&&l.has(e)))}})},e.getCanPin=()=>{var n;let{enableRowPinning:r,enablePinning:i}=t.options;return"function"==typeof r?r(e):null==(n=null!=r?r:i)||n},e.getIsPinned=()=>{let n=[e.id],{top:r,bottom:i}=t.getState().rowPinning,o=n.some(e=>null==r?void 0:r.includes(e)),l=n.some(e=>null==i?void 0:i.includes(e));return o?"top":!!l&&"bottom"},e.getPinnedIndex=()=>{var n,r;let i=e.getIsPinned();if(!i)return -1;let o=null==(n="top"===i?t.getTopRows():t.getBottomRows())?void 0:n.map(e=>{let{id:t}=e;return t});return null!=(r=null==o?void 0:o.indexOf(e.id))?r:-1}},createTable:e=>{e.setRowPinning=t=>null==e.options.onRowPinningChange?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var n,r;return e.setRowPinning(t?k():null!=(n=null==(r=e.initialState)?void 0:r.rowPinning)?n:k())},e.getIsSomeRowsPinned=t=>{var n,r,i;let o=e.getState().rowPinning;return t?!!(null==(n=o[t])?void 0:n.length):!!((null==(r=o.top)?void 0:r.length)||(null==(i=o.bottom)?void 0:i.length))},e._getPinnedRows=(t,n,r)=>{var i;return(null==(i=e.options.keepPinnedRows)||i?(null!=n?n:[]).map(t=>{let n=e.getRow(t,!0);return n.getIsAllParentsExpanded()?n:null}):(null!=n?n:[]).map(e=>t.find(t=>t.id===e))).filter(Boolean).map(e=>({...e,position:r}))},e.getTopRows=l(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(t,n)=>e._getPinnedRows(t,n,"top"),a(e.options,"debugRows","getTopRows")),e.getBottomRows=l(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(t,n)=>e._getPinnedRows(t,n,"bottom"),a(e.options,"debugRows","getBottomRows")),e.getCenterRows=l(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(e,t,n)=>{let r=new Set([...null!=t?t:[],...null!=n?n:[]]);return e.filter(e=>!r.has(e.id))},a(e.options,"debugRows","getCenterRows"))}},{getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:i("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>null==e.options.onRowSelectionChange?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var n;return e.setRowSelection(t?{}:null!=(n=e.initialState.rowSelection)?n:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(n=>{t=void 0!==t?t:!e.getIsAllRowsSelected();let r={...n},i=e.getPreGroupedRowModel().flatRows;return t?i.forEach(e=>{e.getCanSelect()&&(r[e.id]=!0)}):i.forEach(e=>{delete r[e.id]}),r})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(n=>{let r=void 0!==t?t:!e.getIsAllPageRowsSelected(),i={...n};return e.getRowModel().rows.forEach(t=>{L(i,t.id,r,!0,e)}),i}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=l(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?F(e,n):{rows:[],flatRows:[],rowsById:{}},a(e.options,"debugTable","getSelectedRowModel")),e.getFilteredSelectedRowModel=l(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?F(e,n):{rows:[],flatRows:[],rowsById:{}},a(e.options,"debugTable","getFilteredSelectedRowModel")),e.getGroupedSelectedRowModel=l(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?F(e,n):{rows:[],flatRows:[],rowsById:{}},a(e.options,"debugTable","getGroupedSelectedRowModel")),e.getIsAllRowsSelected=()=>{let t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState(),r=!!(t.length&&Object.keys(n).length);return r&&t.some(e=>e.getCanSelect()&&!n[e.id])&&(r=!1),r},e.getIsAllPageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows.filter(e=>e.getCanSelect()),{rowSelection:n}=e.getState(),r=!!t.length;return r&&t.some(e=>!n[e.id])&&(r=!1),r},e.getIsSomeRowsSelected=()=>{var t;let n=Object.keys(null!=(t=e.getState().rowSelection)?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows;return!e.getIsAllPageRowsSelected()&&t.filter(e=>e.getCanSelect()).some(e=>e.getIsSelected()||e.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(n,r)=>{let i=e.getIsSelected();t.setRowSelection(o=>{var l;if(n=void 0!==n?n:!i,e.getCanSelect()&&i===n)return o;let a={...o};return L(a,e.id,n,null==(l=null==r?void 0:r.selectChildren)||l,t),a})},e.getIsSelected=()=>{let{rowSelection:n}=t.getState();return O(e,n)},e.getIsSomeSelected=()=>{let{rowSelection:n}=t.getState();return"some"===N(e,n)},e.getIsAllSubRowsSelected=()=>{let{rowSelection:n}=t.getState();return"all"===N(e,n)},e.getCanSelect=()=>{var n;return"function"==typeof t.options.enableRowSelection?t.options.enableRowSelection(e):null==(n=t.options.enableRowSelection)||n},e.getCanSelectSubRows=()=>{var n;return"function"==typeof t.options.enableSubRowSelection?t.options.enableSubRowSelection(e):null==(n=t.options.enableSubRowSelection)||n},e.getCanMultiSelect=()=>{var n;return"function"==typeof t.options.enableMultiRowSelection?t.options.enableMultiRowSelection(e):null==(n=t.options.enableMultiRowSelection)||n},e.getToggleSelectedHandler=()=>{let t=e.getCanSelect();return n=>{var r;t&&e.toggleSelected(null==(r=n.target)?void 0:r.checked)}}}},{getDefaultColumnDef:()=>E,getInitialState:e=>({columnSizing:{},columnSizingInfo:A(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:i("columnSizing",e),onColumnSizingInfoChange:i("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var n,r,i;let o=t.getState().columnSizing[e.id];return Math.min(Math.max(null!=(n=e.columnDef.minSize)?n:E.minSize,null!=(r=null!=o?o:e.columnDef.size)?r:E.size),null!=(i=e.columnDef.maxSize)?i:E.maxSize)},e.getStart=l(e=>[e,M(t,e),t.getState().columnSizing],(t,n)=>n.slice(0,e.getIndex(t)).reduce((e,t)=>e+t.getSize(),0),a(t.options,"debugColumns","getStart")),e.getAfter=l(e=>[e,M(t,e),t.getState().columnSizing],(t,n)=>n.slice(e.getIndex(t)+1).reduce((e,t)=>e+t.getSize(),0),a(t.options,"debugColumns","getAfter")),e.resetSize=()=>{t.setColumnSizing(t=>{let{[e.id]:n,...r}=t;return r})},e.getCanResize=()=>{var n,r;return(null==(n=e.columnDef.enableResizing)||n)&&(null==(r=t.options.enableColumnResizing)||r)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let t=0,n=e=>{if(e.subHeaders.length)e.subHeaders.forEach(n);else{var r;t+=null!=(r=e.column.getSize())?r:0}};return n(e),t},e.getStart=()=>{if(e.index>0){let t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=n=>{let r=t.getColumn(e.column.id),i=null==r?void 0:r.getCanResize();return o=>{if(!r||!i||(null==o.persist||o.persist(),I(o)&&o.touches&&o.touches.length>1))return;let l=e.getSize(),a=e?e.getLeafHeaders().map(e=>[e.column.id,e.column.getSize()]):[[r.id,r.getSize()]],s=I(o)?Math.round(o.touches[0].clientX):o.clientX,u={},d=(e,n)=>{"number"==typeof n&&(t.setColumnSizingInfo(e=>{var r,i;let o="rtl"===t.options.columnResizeDirection?-1:1,l=(n-(null!=(r=null==e?void 0:e.startOffset)?r:0))*o,a=Math.max(l/(null!=(i=null==e?void 0:e.startSize)?i:0),-.999999);return e.columnSizingStart.forEach(e=>{let[t,n]=e;u[t]=Math.round(100*Math.max(n+n*a,0))/100}),{...e,deltaOffset:l,deltaPercentage:a}}),("onChange"===t.options.columnResizeMode||"end"===e)&&t.setColumnSizing(e=>({...e,...u})))},c=e=>d("move",e),p=e=>{d("end",e),t.setColumnSizingInfo(e=>({...e,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},h=n||("undefined"!=typeof document?document:null),f={moveHandler:e=>c(e.clientX),upHandler:e=>{null==h||h.removeEventListener("mousemove",f.moveHandler),null==h||h.removeEventListener("mouseup",f.upHandler),p(e.clientX)}},g={moveHandler:e=>(e.cancelable&&(e.preventDefault(),e.stopPropagation()),c(e.touches[0].clientX),!1),upHandler:e=>{var t;null==h||h.removeEventListener("touchmove",g.moveHandler),null==h||h.removeEventListener("touchend",g.upHandler),e.cancelable&&(e.preventDefault(),e.stopPropagation()),p(null==(t=e.touches[0])?void 0:t.clientX)}},m=!!function(){if("boolean"==typeof D)return D;let e=!1;try{let t=()=>{};window.addEventListener("test",t,{get passive(){return e=!0,!1}}),window.removeEventListener("test",t)}catch(t){e=!1}return D=e}()&&{passive:!1};I(o)?(null==h||h.addEventListener("touchmove",g.moveHandler,m),null==h||h.addEventListener("touchend",g.upHandler,m)):(null==h||h.addEventListener("mousemove",f.moveHandler,m),null==h||h.addEventListener("mouseup",f.upHandler,m)),t.setColumnSizingInfo(e=>({...e,startOffset:s,startSize:l,deltaOffset:0,deltaPercentage:0,columnSizingStart:a,isResizingColumn:r.id}))}}},createTable:e=>{e.setColumnSizing=t=>null==e.options.onColumnSizingChange?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>null==e.options.onColumnSizingInfoChange?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var n;e.setColumnSizing(t?{}:null!=(n=e.initialState.columnSizing)?n:{})},e.resetHeaderSizeInfo=t=>{var n;e.setColumnSizingInfo(t?A():null!=(n=e.initialState.columnSizingInfo)?n:A())},e.getTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getLeftTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getLeftHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getCenterTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getCenterHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getRightTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getRightHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0}}}];function H(e){var t,n;let i=[...z,...null!=(t=e._features)?t:[]],o={_features:i},s=o._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultOptions?void 0:t.getDefaultOptions(o)),{}),u=e=>o.options.mergeOptions?o.options.mergeOptions(s,e):{...s,...e},d={...null!=(n=e.initialState)?n:{}};o._features.forEach(e=>{var t;d=null!=(t=null==e.getInitialState?void 0:e.getInitialState(d))?t:d});let c=[],p=!1,h={_features:i,options:{...s,...e},initialState:d,_queue:e=>{c.push(e),p||(p=!0,Promise.resolve().then(()=>{for(;c.length;)c.shift()();p=!1}).catch(e=>setTimeout(()=>{throw e})))},reset:()=>{o.setState(o.initialState)},setOptions:e=>{let t=r(e,o.options);o.options=u(t)},getState:()=>o.options.state,setState:e=>{null==o.options.onStateChange||o.options.onStateChange(e)},_getRowId:(e,t,n)=>{var r;return null!=(r=null==o.options.getRowId?void 0:o.options.getRowId(e,t,n))?r:`${n?[n.id,t].join("."):t}`},getCoreRowModel:()=>(o._getCoreRowModel||(o._getCoreRowModel=o.options.getCoreRowModel(o)),o._getCoreRowModel()),getRowModel:()=>o.getPaginationRowModel(),getRow:(e,t)=>{let n=(t?o.getPrePaginationRowModel():o.getRowModel()).rowsById[e];if(!n&&!(n=o.getCoreRowModel().rowsById[e]))throw Error();return n},_getDefaultColumnDef:l(()=>[o.options.defaultColumn],e=>{var t;return e=null!=(t=e)?t:{},{header:e=>{let t=e.header.column.columnDef;return t.accessorKey?t.accessorKey:t.accessorFn?t.id:null},cell:e=>{var t,n;return null!=(t=null==(n=e.renderValue())||null==n.toString?void 0:n.toString())?t:null},...o._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultColumnDef?void 0:t.getDefaultColumnDef()),{}),...e}},a(e,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>o.options.columns,getAllColumns:l(()=>[o._getColumnDefs()],e=>{let t=function(e,n,r){return void 0===r&&(r=0),e.map(e=>{let i=function(e,t,n,r){var i,o;let s,u={...e._getDefaultColumnDef(),...t},d=u.accessorKey,c=null!=(i=null!=(o=u.id)?o:d?"function"==typeof String.prototype.replaceAll?d.replaceAll(".","_"):d.replace(/\./g,"_"):void 0)?i:"string"==typeof u.header?u.header:void 0;if(u.accessorFn?s=u.accessorFn:d&&(s=d.includes(".")?e=>{let t=e;for(let e of d.split(".")){var n;t=null==(n=t)?void 0:n[e]}return t}:e=>e[u.accessorKey]),!c)throw Error();let p={id:`${String(c)}`,accessorFn:s,parent:r,depth:n,columnDef:u,columns:[],getFlatColumns:l(()=>[!0],()=>{var e;return[p,...null==(e=p.columns)?void 0:e.flatMap(e=>e.getFlatColumns())]},a(e.options,"debugColumns","column.getFlatColumns")),getLeafColumns:l(()=>[e._getOrderColumnsFn()],e=>{var t;return null!=(t=p.columns)&&t.length?e(p.columns.flatMap(e=>e.getLeafColumns())):[p]},a(e.options,"debugColumns","column.getLeafColumns"))};for(let t of e._features)null==t.createColumn||t.createColumn(p,e);return p}(o,e,r,n);return i.columns=e.columns?t(e.columns,i,r+1):[],i})};return t(e)},a(e,"debugColumns","getAllColumns")),getAllFlatColumns:l(()=>[o.getAllColumns()],e=>e.flatMap(e=>e.getFlatColumns()),a(e,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:l(()=>[o.getAllFlatColumns()],e=>e.reduce((e,t)=>(e[t.id]=t,e),{}),a(e,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:l(()=>[o.getAllColumns(),o._getOrderColumnsFn()],(e,t)=>t(e.flatMap(e=>e.getLeafColumns())),a(e,"debugColumns","getAllLeafColumns")),getColumn:e=>o._getAllFlatColumnsById()[e]};Object.assign(o,h);for(let e=0;e<o._features.length;e++){let t=o._features[e];null==t||null==t.createTable||t.createTable(o)}return o}function $(){return e=>l(()=>[e.options.data],t=>{let n={rows:[],flatRows:[],rowsById:{}},r=function(t,i,o){void 0===i&&(i=0);let l=[];for(let s=0;s<t.length;s++){let u=c(e,e._getRowId(t[s],s,o),t[s],s,i,void 0,null==o?void 0:o.id);if(n.flatRows.push(u),n.rowsById[u.id]=u,l.push(u),e.options.getSubRows){var a;u.originalSubRows=e.options.getSubRows(t[s],s),null!=(a=u.originalSubRows)&&a.length&&(u.subRows=r(u.originalSubRows,i+1,u))}}return l};return n.rows=r(t),n},a(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function U(){return e=>l(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(t,n,r)=>{var i,o,l;let a,s;if(!t.rows.length||!(null!=n&&n.length)&&!r){for(let e=0;e<t.flatRows.length;e++)t.flatRows[e].columnFilters={},t.flatRows[e].columnFiltersMeta={};return t}let u=[],d=[];(null!=n?n:[]).forEach(t=>{var n;let r=e.getColumn(t.id);if(!r)return;let i=r.getFilterFn();i&&u.push({id:t.id,filterFn:i,resolvedValue:null!=(n=null==i.resolveFilterValue?void 0:i.resolveFilterValue(t.value))?n:t.value})});let p=(null!=n?n:[]).map(e=>e.id),h=e.getGlobalFilterFn(),f=e.getAllLeafColumns().filter(e=>e.getCanGlobalFilter());r&&h&&f.length&&(p.push("__global__"),f.forEach(e=>{var t;d.push({id:e.id,filterFn:h,resolvedValue:null!=(t=null==h.resolveFilterValue?void 0:h.resolveFilterValue(r))?t:r})}));for(let e=0;e<t.flatRows.length;e++){let n=t.flatRows[e];if(n.columnFilters={},u.length)for(let e=0;e<u.length;e++){let t=(a=u[e]).id;n.columnFilters[t]=a.filterFn(n,t,a.resolvedValue,e=>{n.columnFiltersMeta[t]=e})}if(d.length){for(let e=0;e<d.length;e++){let t=(s=d[e]).id;if(s.filterFn(n,t,s.resolvedValue,e=>{n.columnFiltersMeta[t]=e})){n.columnFilters.__global__=!0;break}}!0!==n.columnFilters.__global__&&(n.columnFilters.__global__=!1)}}return i=t.rows,o=e=>{for(let t=0;t<p.length;t++)if(!1===e.columnFilters[p[t]])return!1;return!0},(l=e).options.filterFromLeafRows?function(e,t,n){var r;let i=[],o={},l=null!=(r=n.options.maxLeafRowFilterDepth)?r:100,a=function(e,r){void 0===r&&(r=0);let s=[];for(let d=0;d<e.length;d++){var u;let p=e[d],h=c(n,p.id,p.original,p.index,p.depth,void 0,p.parentId);if(h.columnFilters=p.columnFilters,null!=(u=p.subRows)&&u.length&&r<l){if(h.subRows=a(p.subRows,r+1),t(p=h)&&!h.subRows.length||t(p)||h.subRows.length){s.push(p),o[p.id]=p,i.push(p);continue}}else t(p=h)&&(s.push(p),o[p.id]=p,i.push(p))}return s};return{rows:a(e),flatRows:i,rowsById:o}}(i,o,l):function(e,t,n){var r;let i=[],o={},l=null!=(r=n.options.maxLeafRowFilterDepth)?r:100,a=function(e,r){void 0===r&&(r=0);let s=[];for(let d=0;d<e.length;d++){let p=e[d];if(t(p)){var u;if(null!=(u=p.subRows)&&u.length&&r<l){let e=c(n,p.id,p.original,p.index,p.depth,void 0,p.parentId);e.subRows=a(p.subRows,r+1),p=e}s.push(p),i.push(p),o[p.id]=p}}return s};return{rows:a(e),flatRows:i,rowsById:o}}(i,o,l)},a(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex()))}function W(e){return e=>l(()=>[e.getState().pagination,e.getPrePaginationRowModel(),e.options.paginateExpandedRows?void 0:e.getState().expanded],(t,n)=>{let r;if(!n.rows.length)return n;let{pageSize:i,pageIndex:o}=t,{rows:l,flatRows:a,rowsById:s}=n,u=i*o;l=l.slice(u,u+i),(r=e.options.paginateExpandedRows?{rows:l,flatRows:a,rowsById:s}:function(e){let t=[],n=e=>{var r;t.push(e),null!=(r=e.subRows)&&r.length&&e.getIsExpanded()&&e.subRows.forEach(n)};return e.rows.forEach(n),{rows:t,flatRows:e.flatRows,rowsById:e.rowsById}}({rows:l,flatRows:a,rowsById:s})).flatRows=[];let d=e=>{r.flatRows.push(e),e.subRows.length&&e.subRows.forEach(d)};return r.rows.forEach(d),r},a(e.options,"debugTable","getPaginationRowModel"))}function q(){return e=>l(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,n)=>{if(!n.rows.length||!(null!=t&&t.length))return n;let r=e.getState().sorting,i=[],o=r.filter(t=>{var n;return null==(n=e.getColumn(t.id))?void 0:n.getCanSort()}),l={};o.forEach(t=>{let n=e.getColumn(t.id);n&&(l[t.id]={sortUndefined:n.columnDef.sortUndefined,invertSorting:n.columnDef.invertSorting,sortingFn:n.getSortingFn()})});let a=e=>{let t=e.map(e=>({...e}));return t.sort((e,t)=>{for(let r=0;r<o.length;r+=1){var n;let i=o[r],a=l[i.id],s=a.sortUndefined,u=null!=(n=null==i?void 0:i.desc)&&n,d=0;if(s){let n=e.getValue(i.id),r=t.getValue(i.id),o=void 0===n,l=void 0===r;if(o||l){if("first"===s)return o?-1:1;if("last"===s)return o?1:-1;d=o&&l?0:o?s:-s}}if(0===d&&(d=a.sortingFn(e,t,i.id)),0!==d)return u&&(d*=-1),a.invertSorting&&(d*=-1),d}return e.index-t.index}),t.forEach(e=>{var t;i.push(e),null!=(t=e.subRows)&&t.length&&(e.subRows=a(e.subRows))}),t};return{rows:a(n.rows),flatRows:i,rowsById:n.rowsById}},a(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}},7380:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},8006:(e,t,n)=>{n.d(t,{l:()=>r});let r=e=>e},10342:(e,t,n)=>{n.d(t,{JY:()=>i_,sx:()=>i4,gL:()=>oA});var r=n(63185),i=n.n(r),o=n(67108),l=n.n(o);function a(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var s="function"==typeof Symbol&&Symbol.observable||"@@observable",u=()=>Math.random().toString(36).substring(7).split("").join("."),d={INIT:`@@redux/INIT${u()}`,REPLACE:`@@redux/REPLACE${u()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${u()}`};function c(e,t){return function(...n){return t(e.apply(this,n))}}function p(e,t){if("function"==typeof e)return c(e,t);if("object"!=typeof e||null===e)throw Error(a(16));let n={};for(let r in e){let i=e[r];"function"==typeof i&&(n[r]=c(i,t))}return n}function h(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...n)=>e(t(...n)))}n(89755);var f=Symbol.for(r.version.startsWith("19")?"react.transitional.element":"react.element"),g=Symbol.for("react.portal"),m=Symbol.for("react.fragment"),v=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),b=Symbol.for("react.consumer"),w=Symbol.for("react.context"),x=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),C=Symbol.for("react.suspense_list"),R=Symbol.for("react.memo"),P=Symbol.for("react.lazy");function E(e){return function(t){let n=e(t);function r(){return n}return r.dependsOnOwnProps=!1,r}}function A(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:1!==e.length}function D(e,t){return function(t,{displayName:n}){let r=function(e,t){return r.dependsOnOwnProps?r.mapToProps(e,t):r.mapToProps(e,void 0)};return r.dependsOnOwnProps=!0,r.mapToProps=function(t,n){r.mapToProps=e,r.dependsOnOwnProps=A(e);let i=r(t,n);return"function"==typeof i&&(r.mapToProps=i,r.dependsOnOwnProps=A(i),i=r(t,n)),i},r}}function I(e,t){return(n,r)=>{throw Error(`Invalid value of type ${typeof e} for ${t} argument when connecting component ${r.wrappedComponentName}.`)}}function M(e,t,n){return{...n,...e,...t}}var T={notify(){},get:()=>[]};function k(e,t){let n,r=T,i=0,o=!1;function l(){u.onStateChange&&u.onStateChange()}function a(){if(i++,!n){let i,o;n=t?t.addNestedSub(l):e.subscribe(l),i=null,o=null,r={clear(){i=null,o=null},notify(){let e=i;for(;e;)e.callback(),e=e.next},get(){let e=[],t=i;for(;t;)e.push(t),t=t.next;return e},subscribe(e){let t=!0,n=o={callback:e,next:null,prev:o};return n.prev?n.prev.next=n:i=n,function(){t&&null!==i&&(t=!1,n.next?n.next.prev=n.prev:o=n.prev,n.prev?n.prev.next=n.next:i=n.next)}}}}}function s(){i--,n&&0===i&&(n(),n=void 0,r.clear(),r=T)}let u={addNestedSub:function(e){a();let t=r.subscribe(e),n=!1;return()=>{n||(n=!0,t(),s())}},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:l,isSubscribed:function(){return o},trySubscribe:function(){o||(o=!0,a())},tryUnsubscribe:function(){o&&(o=!1,s())},getListeners:()=>r};return u}var L="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,F="undefined"!=typeof navigator&&"ReactNative"===navigator.product,O=L||F?r.useLayoutEffect:r.useEffect;function N(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}function V(e,t){if(N(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;let n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(let r=0;r<n.length;r++)if(!Object.prototype.hasOwnProperty.call(t,n[r])||!N(e[n[r]],t[n[r]]))return!1;return!0}var j={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},B={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},G={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},_={[x]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[R]:G};function z(e){return function(e){if("object"==typeof e&&null!==e){let{$$typeof:t}=e;switch(t){case f:switch(e=e.type){case m:case y:case v:case S:case C:return e;default:switch(e=e&&e.$$typeof){case w:case x:case P:case R:case b:return e;default:return t}}case g:return t}}}(e)===R?G:_[e.$$typeof]||j}var H=Object.defineProperty,$=Object.getOwnPropertyNames,U=Object.getOwnPropertySymbols,W=Object.getOwnPropertyDescriptor,q=Object.getPrototypeOf,Y=Object.prototype;function X(e,t){if("string"!=typeof t){if(Y){let n=q(t);n&&n!==Y&&X(e,n)}let n=$(t);U&&(n=n.concat(U(t)));let r=z(e),i=z(t);for(let o=0;o<n.length;++o){let l=n[o];if(!B[l]&&!(i&&i[l])&&!(r&&r[l])){let n=W(t,l);try{H(e,l,n)}catch(e){}}}}return e}var K=Symbol.for("react-redux-context"),Z="undefined"!=typeof globalThis?globalThis:{},Q=function(){if(!r.createContext)return{};let e=Z[K]??=new Map,t=e.get(r.createContext);return t||(t=r.createContext(null),e.set(r.createContext,t)),t}(),J=[null,null];function ee(e,t,n,r,i,o){e.current=r,n.current=!1,i.current&&(i.current=null,o())}function et(e,t){return e===t}var en=function(e,t,n,{pure:i,areStatesEqual:o=et,areOwnPropsEqual:l=V,areStatePropsEqual:a=V,areMergedPropsEqual:s=V,forwardRef:u=!1,context:d=Q}={}){let c=e?"function"==typeof e?D(e,"mapStateToProps"):I(e,"mapStateToProps"):E(()=>({})),p=t&&"object"==typeof t?E(e=>(function(e,t){let n={};for(let r in e){let i=e[r];"function"==typeof i&&(n[r]=(...e)=>t(i(...e)))}return n})(t,e)):t?"function"==typeof t?D(t,"mapDispatchToProps"):I(t,"mapDispatchToProps"):E(e=>({dispatch:e})),h=n?"function"==typeof n?function(e,{displayName:t,areMergedPropsEqual:r}){let i,o=!1;return function(e,t,l){let a=n(e,t,l);return o?r(a,i)||(i=a):(o=!0,i=a),i}}:I(n,"mergeProps"):()=>M,f=!!e;return e=>{let t=e.displayName||e.name||"Component",n=`Connect(${t})`,i={shouldHandleStateChanges:f,displayName:n,wrappedComponentName:t,WrappedComponent:e,initMapStateToProps:c,initMapDispatchToProps:p,initMergeProps:h,areStatesEqual:o,areStatePropsEqual:a,areOwnPropsEqual:l,areMergedPropsEqual:s};function g(t){var n;let o,[l,a,s]=r.useMemo(()=>{let{reactReduxForwardedRef:e,...n}=t;return[t.context,e,n]},[t]),u=r.useMemo(()=>(l?.Consumer,d),[l,d]),c=r.useContext(u),p=!!t.store&&!!t.store.getState&&!!t.store.dispatch,h=!!c&&!!c.store,g=p?t.store:c.store,m=h?c.getServerState:g.getState,v=r.useMemo(()=>(function(e,{initMapStateToProps:t,initMapDispatchToProps:n,initMergeProps:r,...i}){let o=t(e,i),l=n(e,i);return function(e,t,n,r,{areStatesEqual:i,areOwnPropsEqual:o,areStatePropsEqual:l}){let a,s,u,d,c,p=!1;return function(h,f){return p?function(p,h){let f=!o(h,s),g=!i(p,a,h,s);if(a=p,s=h,f&&g)return u=e(a,s),t.dependsOnOwnProps&&(d=t(r,s)),c=n(u,d,s);if(f)return e.dependsOnOwnProps&&(u=e(a,s)),t.dependsOnOwnProps&&(d=t(r,s)),c=n(u,d,s);if(g){let t=e(a,s),r=!l(t,u);return u=t,r&&(c=n(u,d,s)),c}return c}(h,f):(u=e(a=h,s=f),d=t(r,s),c=n(u,d,s),p=!0,c)}}(o,l,r(e,i),e,i)})(g.dispatch,i),[g]),[y,b]=r.useMemo(()=>{if(!f)return J;let e=k(g,p?void 0:c.subscription),t=e.notifyNestedSubs.bind(e);return[e,t]},[g,p,c]),w=r.useMemo(()=>p?c:{...c,subscription:y},[p,c,y]),x=r.useRef(void 0),S=r.useRef(s),C=r.useRef(void 0),R=r.useRef(!1),P=r.useRef(!1),E=r.useRef(void 0);O(()=>(P.current=!0,()=>{P.current=!1}),[]);let A=r.useMemo(()=>()=>C.current&&s===S.current?C.current:v(g.getState(),s),[g,s]),D=r.useMemo(()=>e=>{if(!y)return()=>{};if(!f)return()=>{};let t=!1,n=null,r=()=>{let r,i;if(t||!P.current)return;let o=g.getState();try{r=v(o,S.current)}catch(e){i=e,n=e}i||(n=null),r===x.current?R.current||b():(x.current=r,C.current=r,R.current=!0,e())};return y.onStateChange=r,y.trySubscribe(),r(),()=>{if(t=!0,y.tryUnsubscribe(),y.onStateChange=null,n)throw n}},[y]);n=[S,x,R,s,C,b],O(()=>ee(...n),void 0);try{o=r.useSyncExternalStore(D,A,m?()=>v(m(),s):A)}catch(e){throw E.current&&(e.message+=`
The error may be correlated with this previous error:
${E.current.stack}

`),e}O(()=>{E.current=void 0,C.current=void 0,x.current=o});let I=r.useMemo(()=>r.createElement(e,{...o,ref:a}),[a,e,o]);return r.useMemo(()=>f?r.createElement(u.Provider,{value:w},I):I,[u,I,w])}let m=r.memo(g);if(m.WrappedComponent=e,m.displayName=g.displayName=n,u){let t=r.forwardRef(function(e,t){return r.createElement(m,{...e,reactReduxForwardedRef:t})});return t.displayName=n,t.WrappedComponent=e,X(t,e)}return X(m,e)}},er=function(e){let{children:t,context:n,serverState:i,store:o}=e,l=r.useMemo(()=>{let e=k(o);return{store:o,subscription:e,getServerState:i?()=>i:void 0}},[o,i]),a=r.useMemo(()=>o.getState(),[o]);return O(()=>{let{subscription:e}=l;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),a!==o.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[l,a]),r.createElement((n||Q).Provider,{value:l},t)},ei=function(e){var t=e.top,n=e.right,r=e.bottom,i=e.left;return{top:t,right:n,bottom:r,left:i,width:n-i,height:r-t,x:i,y:t,center:{x:(n+i)/2,y:(r+t)/2}}},eo=function(e,t){return{top:e.top-t.top,left:e.left-t.left,bottom:e.bottom+t.bottom,right:e.right+t.right}},el=function(e,t){return{top:e.top+t.top,left:e.left+t.left,bottom:e.bottom-t.bottom,right:e.right-t.right}},ea={top:0,right:0,bottom:0,left:0},es=function(e){var t=e.borderBox,n=e.margin,r=void 0===n?ea:n,i=e.border,o=void 0===i?ea:i,l=e.padding,a=void 0===l?ea:l,s=ei(eo(t,r)),u=ei(el(t,o)),d=ei(el(u,a));return{marginBox:s,borderBox:ei(t),paddingBox:u,contentBox:d,margin:r,border:o,padding:a}},eu=function(e){var t=e.slice(0,-2);if("px"!==e.slice(-2))return 0;var n=Number(t);return isNaN(n)&&function(e,t){if(!e)throw Error("Invariant failed")}(!1),n},ed=function(e,t){var n=e.borderBox,r=e.border,i=e.margin,o=e.padding;return es({borderBox:{top:n.top+t.y,left:n.left+t.x,bottom:n.bottom+t.y,right:n.right+t.x},border:r,margin:i,padding:o})},ec=function(e,t){return void 0===t&&(t={x:window.pageXOffset,y:window.pageYOffset}),ed(e,t)},ep=function(e,t){return es({borderBox:e,margin:{top:eu(t.marginTop),right:eu(t.marginRight),bottom:eu(t.marginBottom),left:eu(t.marginLeft)},padding:{top:eu(t.paddingTop),right:eu(t.paddingRight),bottom:eu(t.paddingBottom),left:eu(t.paddingLeft)},border:{top:eu(t.borderTopWidth),right:eu(t.borderRightWidth),bottom:eu(t.borderBottomWidth),left:eu(t.borderLeftWidth)}})},eh=function(e){return ep(e.getBoundingClientRect(),window.getComputedStyle(e))};let ef=function(e){var t=[],n=null,r=function(){for(var r=arguments.length,i=Array(r),o=0;o<r;o++)i[o]=arguments[o];t=i,n||(n=requestAnimationFrame(function(){n=null,e.apply(void 0,t)}))};return r.cancel=function(){n&&(cancelAnimationFrame(n),n=null)},r};function eg(){return(eg=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}let em=/[ \t]{2,}/g,ev=/^[ \t]*/gm,ey=e=>e.replace(em," ").replace(ev,"").trim(),eb=e=>ey(`
  %c@hello-pangea/dnd

  %c${ey(e)}

  %c👷‍ This is a development only message. It will be removed in production builds.
`);function ew(e,t){}ew.bind(null,"warn");let ex=ew.bind(null,"error");function eS(){}function eC(e,t,n){let r=t.map(t=>{var r;let i=(r=t.options,{...n,...r});return e.addEventListener(t.eventName,t.fn,i),function(){e.removeEventListener(t.eventName,t.fn,i)}});return function(){r.forEach(e=>{e()})}}class eR extends Error{}function eP(e,t){throw new eR("Invariant failed")}eR.prototype.toString=function(){return this.message};class eE extends i().Component{constructor(...e){super(...e),this.callbacks=null,this.unbind=eS,this.onWindowError=e=>{let t=this.getCallbacks();t.isDragging()&&t.tryAbort(),e.error instanceof eR&&e.preventDefault()},this.getCallbacks=()=>{if(!this.callbacks)throw Error("Unable to find AppCallbacks in <ErrorBoundary/>");return this.callbacks},this.setCallbacks=e=>{this.callbacks=e}}componentDidMount(){this.unbind=eC(window,[{eventName:"error",fn:this.onWindowError}])}componentDidCatch(e){if(e instanceof eR)return void this.setState({});throw e}componentWillUnmount(){this.unbind()}render(){return this.props.children(this.setCallbacks)}}let eA=e=>e+1,eD=(e,t)=>{let n=e.droppableId===t.droppableId,r=eA(e.index),i=eA(t.index);return n?`
      You have moved the item from position ${r}
      to position ${i}
    `:`
    You have moved the item from position ${r}
    in list ${e.droppableId}
    to list ${t.droppableId}
    in position ${i}
  `},eI=(e,t,n)=>t.droppableId===n.droppableId?`
      The item ${e}
      has been combined with ${n.draggableId}`:`
      The item ${e}
      in list ${t.droppableId}
      has been combined with ${n.draggableId}
      in list ${n.droppableId}
    `,eM=e=>`
  The item has returned to its starting position
  of ${eA(e.index)}
`,eT={dragHandleUsageInstructions:`
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
`,onDragStart:e=>`
  You have lifted an item in position ${eA(e.source.index)}
`,onDragUpdate:e=>{let t=e.destination;if(t)return eD(e.source,t);let n=e.combine;return n?eI(e.draggableId,e.source,n):"You are over an area that cannot be dropped on"},onDragEnd:e=>{if("CANCEL"===e.reason)return`
      Movement cancelled.
      ${eM(e.source)}
    `;let t=e.destination,n=e.combine;return t?`
      You have dropped the item.
      ${eD(e.source,t)}
    `:n?`
      You have dropped the item.
      ${eI(e.draggableId,e.source,n)}
    `:`
    The item has been dropped while not over a drop area.
    ${eM(e.source)}
  `}};function ek(e,t){if(e.length!==t.length)return!1;for(let i=0;i<e.length;i++){var n,r;if(!((n=e[i])===(r=t[i])||Number.isNaN(n)&&Number.isNaN(r))&&1)return!1}return!0}function eL(e,t){let n=(0,r.useState)(()=>({inputs:t,result:e()}))[0],i=(0,r.useRef)(!0),o=(0,r.useRef)(n),l=i.current||t&&o.current.inputs&&ek(t,o.current.inputs)?o.current:{inputs:t,result:e()};return(0,r.useEffect)(()=>{i.current=!1,o.current=l},[l]),l.result}function eF(e,t){return eL(()=>e,t)}let eO={x:0,y:0},eN=(e,t)=>({x:e.x+t.x,y:e.y+t.y}),eV=(e,t)=>({x:e.x-t.x,y:e.y-t.y}),ej=(e,t)=>e.x===t.x&&e.y===t.y,eB=e=>({x:0!==e.x?-e.x:0,y:0!==e.y?-e.y:0}),eG=(e,t,n=0)=>"x"===e?{x:t,y:n}:{x:n,y:t},e_=(e,t)=>Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2),ez=(e,t)=>Math.min(...t.map(t=>e_(e,t))),eH=e=>t=>({x:e(t.x),y:e(t.y)});var e$=(e,t)=>{let n=ei({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return n.width<=0||n.height<=0?null:n};let eU=(e,t)=>({top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}),eW=e=>[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}],eq=(e,t)=>t?eU(e,t.scroll.diff.displacement):e,eY=(e,t,n)=>n&&n.increasedBy?{...e,[t.end]:e[t.end]+n.increasedBy[t.line]}:e,eX=(e,t)=>t&&t.shouldClipSubject?e$(t.pageMarginBox,e):ei(e);var eK=({page:e,withPlaceholder:t,axis:n,frame:r})=>{let i=eX(eY(eq(e.marginBox,r),n,t),r);return{page:e,withPlaceholder:t,active:i}},eZ=(e,t)=>{e.frame||eP();let n=e.frame,r=eV(t,n.scroll.initial),i=eB(r),o={...n,scroll:{initial:n.scroll.initial,current:t,diff:{value:r,displacement:i},max:n.scroll.max}},l=eK({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:o});return{...e,frame:o,subject:l}};function eQ(e,t=ek){let n=null;function r(...i){if(n&&n.lastThis===this&&t(i,n.lastArgs))return n.lastResult;let o=e.apply(this,i);return n={lastResult:o,lastArgs:i,lastThis:this},o}return r.clear=function(){n=null},r}let eJ=eQ(e=>e.reduce((e,t)=>(e[t.descriptor.id]=t,e),{})),e0=eQ(e=>e.reduce((e,t)=>(e[t.descriptor.id]=t,e),{})),e1=eQ(e=>Object.values(e)),e2=eQ(e=>Object.values(e));var e5=eQ((e,t)=>e2(t).filter(t=>e===t.descriptor.droppableId).sort((e,t)=>e.descriptor.index-t.descriptor.index));function e4(e){return e.at&&"REORDER"===e.at.type?e.at.destination:null}function e3(e){return e.at&&"COMBINE"===e.at.type?e.at.combine:null}var e7=eQ((e,t)=>t.filter(t=>t.descriptor.id!==e.descriptor.id)),e8=({isMovingForward:e,draggable:t,destination:n,insideDestination:r,previousImpact:i})=>{if(!n.isCombineEnabled||!e4(i))return null;function o(e){let t={type:"COMBINE",combine:{draggableId:e,droppableId:n.descriptor.id}};return{...i,at:t}}let l=i.displaced.all,a=l.length?l[0]:null;if(e)return a?o(a):null;let s=e7(t,r);if(!a)return s.length?o(s[s.length-1].descriptor.id):null;let u=s.findIndex(e=>e.descriptor.id===a);-1===u&&eP();let d=u-1;return d<0?null:o(s[d].descriptor.id)},e9=(e,t)=>e.descriptor.droppableId===t.descriptor.id;let e6={point:eO,value:0},te={invisible:{},visible:{},all:[]},tt={displaced:te,displacedBy:e6,at:null};var tn=(e,t)=>n=>e<=n&&n<=t,tr=e=>{let t=tn(e.top,e.bottom),n=tn(e.left,e.right);return r=>{if(t(r.top)&&t(r.bottom)&&n(r.left)&&n(r.right))return!0;let i=t(r.top)||t(r.bottom),o=n(r.left)||n(r.right);if(i&&o)return!0;let l=r.top<e.top&&r.bottom>e.bottom,a=r.left<e.left&&r.right>e.right;return!!l&&!!a||l&&o||a&&i}},ti=e=>{let t=tn(e.top,e.bottom),n=tn(e.left,e.right);return e=>t(e.top)&&t(e.bottom)&&n(e.left)&&n(e.right)};let to={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},tl={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"};var ta=e=>t=>{let n=tn(t.top,t.bottom),r=tn(t.left,t.right);return t=>e===to?n(t.top)&&n(t.bottom):r(t.left)&&r(t.right)};let ts=(e,t)=>eU(e,t.frame?t.frame.scroll.diff.displacement:eO),tu=(e,t,n)=>!!t.subject.active&&n(t.subject.active)(e),td=(e,t,n)=>n(t)(e),tc=({target:e,destination:t,viewport:n,withDroppableDisplacement:r,isVisibleThroughFrameFn:i})=>{let o=r?ts(e,t):e;return tu(o,t,i)&&td(o,n,i)},tp=e=>tc({...e,isVisibleThroughFrameFn:tr}),th=e=>tc({...e,isVisibleThroughFrameFn:ti}),tf=e=>tc({...e,isVisibleThroughFrameFn:ta(e.destination.axis)}),tg=(e,t,n)=>{if("boolean"==typeof n)return n;if(!t)return!0;let{invisible:r,visible:i}=t;if(r[e])return!1;let o=i[e];return!o||o.shouldAnimate};function tm({afterDragging:e,destination:t,displacedBy:n,viewport:r,forceShouldAnimate:i,last:o}){return e.reduce(function(e,l){var a,s;let u=(a=l,s=n,ei(eo(a.page.marginBox,{top:s.point.y,right:0,bottom:0,left:s.point.x}))),d=l.descriptor.id;if(e.all.push(d),!tp({target:u,destination:t,viewport:r,withDroppableDisplacement:!0}))return e.invisible[l.descriptor.id]=!0,e;let c=tg(d,o,i);return e.visible[d]={draggableId:d,shouldAnimate:c},e},{all:[],visible:{},invisible:{}})}function tv({insideDestination:e,inHomeList:t,displacedBy:n,destination:r}){let i=function(e,t){if(!e.length)return 0;let n=e[e.length-1].descriptor.index;return t.inHomeList?n:n+1}(e,{inHomeList:t});return{displaced:te,displacedBy:n,at:{type:"REORDER",destination:{droppableId:r.descriptor.id,index:i}}}}function ty({draggable:e,insideDestination:t,destination:n,viewport:r,displacedBy:i,last:o,index:l,forceShouldAnimate:a}){let s=e9(e,n);if(null==l)return tv({insideDestination:t,inHomeList:s,displacedBy:i,destination:n});let u=t.find(e=>e.descriptor.index===l);if(!u)return tv({insideDestination:t,inHomeList:s,displacedBy:i,destination:n});let d=e7(e,t),c=t.indexOf(u);return{displaced:tm({afterDragging:d.slice(c),destination:n,displacedBy:i,last:o,viewport:r.frame,forceShouldAnimate:a}),displacedBy:i,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:l}}}}function tb(e,t){return!!t.effected[e]}var tw=({isMovingForward:e,destination:t,draggables:n,combine:r,afterCritical:i})=>{if(!t.isCombineEnabled)return null;let o=r.draggableId,l=n[o].descriptor.index;return tb(o,i)?e?l:l-1:e?l+1:l},tx=({isMovingForward:e,isInHomeList:t,insideDestination:n,location:r})=>{if(!n.length)return null;let i=r.index,o=e?i+1:i-1,l=n[0].descriptor.index,a=n[n.length-1].descriptor.index;return o<l||o>(t?a:a+1)?null:o},tS=({isMovingForward:e,isInHomeList:t,draggable:n,draggables:r,destination:i,insideDestination:o,previousImpact:l,viewport:a,afterCritical:s})=>{let u=l.at;if(u||eP(),"REORDER"===u.type){let r=tx({isMovingForward:e,isInHomeList:t,location:u.destination,insideDestination:o});return null==r?null:ty({draggable:n,insideDestination:o,destination:i,viewport:a,last:l.displaced,displacedBy:l.displacedBy,index:r})}let d=tw({isMovingForward:e,destination:i,displaced:l.displaced,draggables:r,combine:u.combine,afterCritical:s});return null==d?null:ty({draggable:n,insideDestination:o,destination:i,viewport:a,last:l.displaced,displacedBy:l.displacedBy,index:d})},tC=({displaced:e,afterCritical:t,combineWith:n,displacedBy:r})=>{let i=!!(e.visible[n]||e.invisible[n]);return tb(n,t)?i?eO:eB(r.point):i?r.point:eO},tR=({afterCritical:e,impact:t,draggables:n})=>{let r=e3(t);r||eP();let i=r.draggableId;return eN(n[i].page.borderBox.center,tC({displaced:t.displaced,afterCritical:e,combineWith:i,displacedBy:t.displacedBy}))};let tP=(e,t)=>t.margin[e.start]+t.borderBox[e.size]/2,tE=(e,t)=>t.margin[e.end]+t.borderBox[e.size]/2,tA=(e,t,n)=>t[e.crossAxisStart]+n.margin[e.crossAxisStart]+n.borderBox[e.crossAxisSize]/2,tD=({axis:e,moveRelativeTo:t,isMoving:n})=>eG(e.line,t.marginBox[e.end]+tP(e,n),tA(e,t.marginBox,n)),tI=({axis:e,moveRelativeTo:t,isMoving:n})=>eG(e.line,t.marginBox[e.start]-tE(e,n),tA(e,t.marginBox,n)),tM=({axis:e,moveInto:t,isMoving:n})=>eG(e.line,t.contentBox[e.start]+tP(e,n),tA(e,t.contentBox,n));var tT=({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:i})=>{let o=e5(r.descriptor.id,n),l=t.page,a=r.axis;if(!o.length)return tM({axis:a,moveInto:r.page,isMoving:l});let{displaced:s,displacedBy:u}=e,d=s.all[0];if(d){let e=n[d];return tb(d,i)?tI({axis:a,moveRelativeTo:e.page,isMoving:l}):tI({axis:a,moveRelativeTo:ed(e.page,u.point),isMoving:l})}let c=o[o.length-1];return c.descriptor.id===t.descriptor.id?l.borderBox.center:tb(c.descriptor.id,i)?tD({axis:a,moveRelativeTo:ed(c.page,eB(i.displacedBy.point)),isMoving:l}):tD({axis:a,moveRelativeTo:c.page,isMoving:l})},tk=(e,t)=>{let n=e.frame;return n?eN(t,n.scroll.diff.displacement):t};let tL=({impact:e,draggable:t,droppable:n,draggables:r,afterCritical:i})=>{let o=t.page.borderBox.center,l=e.at;return n&&l?"REORDER"===l.type?tT({impact:e,draggable:t,draggables:r,droppable:n,afterCritical:i}):tR({impact:e,draggables:r,afterCritical:i}):o};var tF=e=>{let t=tL(e),n=e.droppable;return n?tk(n,t):t},tO=(e,t)=>{let n=eV(t,e.scroll.initial),r=eB(n);return{frame:ei({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:n,displacement:r}}}};function tN(e,t){return e.map(e=>t[e])}var tV=({impact:e,viewport:t,destination:n,draggables:r,maxScrollChange:i})=>{let o=tO(t,eN(t.scroll.current,i)),l=n.frame?eZ(n,eN(n.frame.scroll.current,i)):n,a=e.displaced,s=tm({afterDragging:tN(a.all,r),destination:n,displacedBy:e.displacedBy,viewport:o.frame,last:a,forceShouldAnimate:!1}),u=tm({afterDragging:tN(a.all,r),destination:l,displacedBy:e.displacedBy,viewport:t.frame,last:a,forceShouldAnimate:!1}),d={},c={},p=[a,s,u];return a.all.forEach(e=>{let t=function(e,t){for(let n=0;n<t.length;n++){let r=t[n].visible[e];if(r)return r}return null}(e,p);if(t){c[e]=t;return}d[e]=!0}),{...e,displaced:{all:a.all,invisible:d,visible:c}}},tj=(e,t)=>eN(e.scroll.diff.displacement,t),tB=({pageBorderBoxCenter:e,draggable:t,viewport:n})=>{let r=eV(tj(n,e),t.page.borderBox.center);return eN(t.client.borderBox.center,r)},tG=({draggable:e,destination:t,newPageBorderBoxCenter:n,viewport:r,withDroppableDisplacement:i,onlyOnMainAxis:o=!1})=>{let l=eV(n,e.page.borderBox.center),a={target:eU(e.page.borderBox,l),destination:t,withDroppableDisplacement:i,viewport:r};return o?tf(a):th(a)},t_=({isMovingForward:e,draggable:t,destination:n,draggables:r,previousImpact:i,viewport:o,previousPageBorderBoxCenter:l,previousClientSelection:a,afterCritical:s})=>{if(!n.isEnabled)return null;let u=e5(n.descriptor.id,r),d=e9(t,n),c=e8({isMovingForward:e,draggable:t,destination:n,insideDestination:u,previousImpact:i})||tS({isMovingForward:e,isInHomeList:d,draggable:t,draggables:r,destination:n,insideDestination:u,previousImpact:i,viewport:o,afterCritical:s});if(!c)return null;let p=tF({impact:c,draggable:t,droppable:n,draggables:r,afterCritical:s});if(tG({draggable:t,destination:n,newPageBorderBoxCenter:p,viewport:o.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0}))return{clientSelection:tB({pageBorderBoxCenter:p,draggable:t,viewport:o}),impact:c,scrollJumpRequest:null};let h=eV(p,l);return{clientSelection:a,impact:tV({impact:c,viewport:o,destination:n,draggables:r,maxScrollChange:h}),scrollJumpRequest:h}};let tz=e=>{let t=e.subject.active;return t||eP(),t};var tH=({isMovingForward:e,pageBorderBoxCenter:t,source:n,droppables:r,viewport:i})=>{let o=n.subject.active;if(!o)return null;let l=n.axis,a=tn(o[l.start],o[l.end]),s=e1(r).filter(e=>e!==n).filter(e=>e.isEnabled).filter(e=>!!e.subject.active).filter(e=>tr(i.frame)(tz(e))).filter(t=>{let n=tz(t);return e?o[l.crossAxisEnd]<n[l.crossAxisEnd]:n[l.crossAxisStart]<o[l.crossAxisStart]}).filter(e=>{let t=tz(e),n=tn(t[l.start],t[l.end]);return a(t[l.start])||a(t[l.end])||n(o[l.start])||n(o[l.end])}).sort((t,n)=>{let r=tz(t)[l.crossAxisStart],i=tz(n)[l.crossAxisStart];return e?r-i:i-r}).filter((e,t,n)=>tz(e)[l.crossAxisStart]===tz(n[0])[l.crossAxisStart]);if(!s.length)return null;if(1===s.length)return s[0];let u=s.filter(e=>tn(tz(e)[l.start],tz(e)[l.end])(t[l.line]));return 1===u.length?u[0]:u.length>1?u.sort((e,t)=>tz(e)[l.start]-tz(t)[l.start])[0]:s.sort((e,n)=>{let r=ez(t,eW(tz(e))),i=ez(t,eW(tz(n)));return r!==i?r-i:tz(e)[l.start]-tz(n)[l.start]})[0]};let t$=(e,t)=>{let n=e.page.borderBox.center;return tb(e.descriptor.id,t)?eV(n,t.displacedBy.point):n},tU=(e,t)=>{let n=e.page.borderBox;return tb(e.descriptor.id,t)?eU(n,eB(t.displacedBy.point)):n};var tW=({pageBorderBoxCenter:e,viewport:t,destination:n,insideDestination:r,afterCritical:i})=>r.filter(e=>th({target:tU(e,i),destination:n,viewport:t.frame,withDroppableDisplacement:!0})).sort((t,r)=>{let o=e_(e,tk(n,t$(t,i))),l=e_(e,tk(n,t$(r,i)));return o<l?-1:l<o?1:t.descriptor.index-r.descriptor.index})[0]||null,tq=eQ(function(e,t){let n=t[e.line];return{value:n,point:eG(e.line,n)}});let tY=(e,t,n)=>{let r=e.axis;if("virtual"===e.descriptor.mode)return eG(r.line,t[r.line]);let i=e.subject.page.contentBox[r.size],o=e5(e.descriptor.id,n).reduce((e,t)=>e+t.client.marginBox[r.size],0)+t[r.line]-i;return o<=0?null:eG(r.line,o)},tX=(e,t)=>({...e,scroll:{...e.scroll,max:t}}),tK=(e,t,n)=>{let r=e.frame;e9(t,e)&&eP(),e.subject.withPlaceholder&&eP();let i=tq(e.axis,t.displaceBy).point,o=tY(e,i,n),l={placeholderSize:i,increasedBy:o,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};if(!r){let t=eK({page:e.subject.page,withPlaceholder:l,axis:e.axis,frame:e.frame});return{...e,subject:t}}let a=o?eN(r.scroll.max,o):r.scroll.max,s=tX(r,a),u=eK({page:e.subject.page,withPlaceholder:l,axis:e.axis,frame:s});return{...e,subject:u,frame:s}},tZ=e=>{let t=e.subject.withPlaceholder;t||eP();let n=e.frame;if(!n){let t=eK({page:e.subject.page,axis:e.axis,frame:null,withPlaceholder:null});return{...e,subject:t}}let r=t.oldFrameMaxScroll;r||eP();let i=tX(n,r),o=eK({page:e.subject.page,axis:e.axis,frame:i,withPlaceholder:null});return{...e,subject:o,frame:i}};var tQ=({previousPageBorderBoxCenter:e,moveRelativeTo:t,insideDestination:n,draggable:r,draggables:i,destination:o,viewport:l,afterCritical:a})=>{if(!t){if(n.length)return null;let e={displaced:te,displacedBy:e6,at:{type:"REORDER",destination:{droppableId:o.descriptor.id,index:0}}},t=tF({impact:e,draggable:r,droppable:o,draggables:i,afterCritical:a}),s=e9(r,o)?o:tK(o,r,i);return tG({draggable:r,destination:s,newPageBorderBoxCenter:t,viewport:l.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?e:null}let s=e[o.axis.line]<=t.page.borderBox.center[o.axis.line],u=(()=>{let e=t.descriptor.index;return t.descriptor.id===r.descriptor.id||s?e:e+1})(),d=tq(o.axis,r.displaceBy);return ty({draggable:r,insideDestination:n,destination:o,viewport:l,displacedBy:d,last:te,index:u})},tJ=({isMovingForward:e,previousPageBorderBoxCenter:t,draggable:n,isOver:r,draggables:i,droppables:o,viewport:l,afterCritical:a})=>{let s=tH({isMovingForward:e,pageBorderBoxCenter:t,source:r,droppables:o,viewport:l});if(!s)return null;let u=e5(s.descriptor.id,i),d=tW({pageBorderBoxCenter:t,viewport:l,destination:s,insideDestination:u,afterCritical:a}),c=tQ({previousPageBorderBoxCenter:t,destination:s,draggable:n,draggables:i,moveRelativeTo:d,insideDestination:u,viewport:l,afterCritical:a});return c?{clientSelection:tB({pageBorderBoxCenter:tF({impact:c,draggable:n,droppable:s,draggables:i,afterCritical:a}),draggable:n,viewport:l}),impact:c,scrollJumpRequest:null}:null},t0=e=>{let t=e.at;return t?"REORDER"===t.type?t.destination.droppableId:t.combine.droppableId:null};let t1=(e,t)=>{let n=t0(e);return n?t[n]:null};var t2=({state:e,type:t})=>{let n=t1(e.impact,e.dimensions.droppables),r=!!n,i=e.dimensions.droppables[e.critical.droppable.id],o=n||i,l=o.axis.direction,a="vertical"===l&&("MOVE_UP"===t||"MOVE_DOWN"===t)||"horizontal"===l&&("MOVE_LEFT"===t||"MOVE_RIGHT"===t);if(a&&!r)return null;let s="MOVE_DOWN"===t||"MOVE_RIGHT"===t,u=e.dimensions.draggables[e.critical.draggable.id],d=e.current.page.borderBoxCenter,{draggables:c,droppables:p}=e.dimensions;return a?t_({isMovingForward:s,previousPageBorderBoxCenter:d,draggable:u,destination:o,draggables:c,viewport:e.viewport,previousClientSelection:e.current.client.selection,previousImpact:e.impact,afterCritical:e.afterCritical}):tJ({isMovingForward:s,previousPageBorderBoxCenter:d,draggable:u,isOver:o,draggables:c,droppables:p,viewport:e.viewport,afterCritical:e.afterCritical})};function t5(e){return"DRAGGING"===e.phase||"COLLECTING"===e.phase}function t4(e){let t=tn(e.top,e.bottom),n=tn(e.left,e.right);return function(e){return t(e.y)&&n(e.x)}}let t3=(e,t)=>ei(eU(e,t));var t7=(e,t)=>{let n=e.frame;return n?t3(t,n.scroll.diff.value):t};function t8({displaced:e,id:t}){return!!(e.visible[t]||e.invisible[t])}var t9=({pageBorderBoxWithDroppableScroll:e,draggable:t,destination:n,insideDestination:r,last:i,viewport:o,afterCritical:l})=>{let a=n.axis,s=tq(n.axis,t.displaceBy),u=s.value,d=e[a.start],c=e[a.end],p=e7(t,r).find(e=>{let t=e.descriptor.id,n=e.page.borderBox.center[a.line],r=tb(t,l),o=t8({displaced:i,id:t});return r?o?c<=n:d<n-u:o?c<=n+u:d<n})||null,h=function({draggable:e,closest:t,inHomeList:n}){return t?n&&t.descriptor.index>e.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}({draggable:t,closest:p,inHomeList:e9(t,n)});return ty({draggable:t,insideDestination:r,destination:n,viewport:o,last:i,displacedBy:s,index:h})},t6=({draggable:e,pageBorderBoxWithDroppableScroll:t,previousImpact:n,destination:r,insideDestination:i,afterCritical:o})=>{if(!r.isCombineEnabled)return null;let l=r.axis,a=tq(r.axis,e.displaceBy),s=a.value,u=t[l.start],d=t[l.end],c=e7(e,i).find(e=>{let t=e.descriptor.id,r=e.page.borderBox,i=r[l.size]/4,a=tb(t,o),c=t8({displaced:n.displaced,id:t});return a?c?d>r[l.start]+i&&d<r[l.end]-i:u>r[l.start]-s+i&&u<r[l.end]-s-i:c?d>r[l.start]+s+i&&d<r[l.end]+s-i:u>r[l.start]+i&&u<r[l.end]-i});return c?{displacedBy:a,displaced:n.displaced,at:{type:"COMBINE",combine:{draggableId:c.descriptor.id,droppableId:r.descriptor.id}}}:null},ne=({pageOffset:e,draggable:t,draggables:n,droppables:r,previousImpact:i,viewport:o,afterCritical:l})=>{let a=t3(t.page.borderBox,e),s=function({pageBorderBox:e,draggable:t,droppables:n}){let r=e1(n).filter(t=>{if(!t.isEnabled)return!1;let n=t.subject.active;if(!n||!(e.left<n.right)||!(e.right>n.left)||!(e.top<n.bottom)||!(e.bottom>n.top))return!1;if(t4(n)(e.center))return!0;let r=t.axis,i=n.center[r.crossAxisLine],o=e[r.crossAxisStart],l=e[r.crossAxisEnd],a=tn(n[r.crossAxisStart],n[r.crossAxisEnd]),s=a(o),u=a(l);return!s&&!u||(s?o<i:l>i)});return r.length?1===r.length?r[0].descriptor.id:function({pageBorderBox:e,draggable:t,candidates:n}){let r=t.page.borderBox.center,i=n.map(t=>{let n=t.axis,i=eG(t.axis.line,e.center[n.line],t.page.borderBox.center[n.crossAxisLine]);return{id:t.descriptor.id,distance:e_(r,i)}}).sort((e,t)=>t.distance-e.distance);return i[0]?i[0].id:null}({pageBorderBox:e,draggable:t,candidates:r}):null}({pageBorderBox:a,draggable:t,droppables:r});if(!s)return tt;let u=r[s],d=e5(u.descriptor.id,n),c=t7(u,a);return t6({pageBorderBoxWithDroppableScroll:c,draggable:t,previousImpact:i,destination:u,insideDestination:d,afterCritical:l})||t9({pageBorderBoxWithDroppableScroll:c,draggable:t,destination:u,insideDestination:d,last:i.displaced,viewport:o,afterCritical:l})},nt=(e,t)=>({...e,[t.descriptor.id]:t});let nn=({previousImpact:e,impact:t,droppables:n})=>{let r=t0(e),i=t0(t);if(!r||r===i)return n;let o=n[r];return o.subject.withPlaceholder?nt(n,tZ(o)):n};var nr=({draggable:e,draggables:t,droppables:n,previousImpact:r,impact:i})=>{let o=nn({previousImpact:r,impact:i,droppables:n}),l=t0(i);if(!l)return o;let a=n[l];return e9(e,a)||a.subject.withPlaceholder?o:nt(o,tK(a,e,t))},ni=({state:e,clientSelection:t,dimensions:n,viewport:r,impact:i,scrollJumpRequest:o})=>{let l=r||e.viewport,a=n||e.dimensions,s=t||e.current.client.selection,u=eV(s,e.initial.client.selection),d={offset:u,selection:s,borderBoxCenter:eN(e.initial.client.borderBoxCenter,u)},c={selection:eN(d.selection,l.scroll.current),borderBoxCenter:eN(d.borderBoxCenter,l.scroll.current),offset:eN(d.offset,l.scroll.diff.value)},p={client:d,page:c};if("COLLECTING"===e.phase)return{...e,dimensions:a,viewport:l,current:p};let h=a.draggables[e.critical.draggable.id],f=i||ne({pageOffset:c.offset,draggable:h,draggables:a.draggables,droppables:a.droppables,previousImpact:e.impact,viewport:l,afterCritical:e.afterCritical}),g=nr({draggable:h,impact:f,previousImpact:e.impact,draggables:a.draggables,droppables:a.droppables});return{...e,current:p,dimensions:{draggables:a.draggables,droppables:g},impact:f,viewport:l,scrollJumpRequest:o||null,forceShouldAnimate:!o&&null}},no=({impact:e,viewport:t,draggables:n,destination:r,forceShouldAnimate:i})=>{var o;let l=e.displaced,a=tm({afterDragging:(o=l.all,o.map(e=>n[e])),destination:r,displacedBy:e.displacedBy,viewport:t.frame,forceShouldAnimate:i,last:l});return{...e,displaced:a}},nl=({impact:e,draggable:t,droppable:n,draggables:r,viewport:i,afterCritical:o})=>tB({pageBorderBoxCenter:tF({impact:e,draggable:t,draggables:r,droppable:n,afterCritical:o}),draggable:t,viewport:i}),na=({state:e,dimensions:t,viewport:n})=>{"SNAP"!==e.movementMode&&eP();let r=e.impact,i=n||e.viewport,o=t||e.dimensions,{draggables:l,droppables:a}=o,s=l[e.critical.draggable.id],u=t0(r);u||eP();let d=a[u],c=no({impact:r,viewport:i,destination:d,draggables:l}),p=nl({impact:c,draggable:s,droppable:d,draggables:l,viewport:i,afterCritical:e.afterCritical});return ni({impact:c,clientSelection:p,state:e,dimensions:o,viewport:i})},ns=e=>({index:e.index,droppableId:e.droppableId}),nu=({draggable:e,home:t,draggables:n,viewport:r})=>{let i=tq(t.axis,e.displaceBy),o=e5(t.descriptor.id,n),l=o.indexOf(e);-1===l&&eP();let a=o.slice(l+1),s=a.reduce((e,t)=>(e[t.descriptor.id]=!0,e),{}),u={inVirtualList:"virtual"===t.descriptor.mode,displacedBy:i,effected:s};return{impact:{displaced:tm({afterDragging:a,destination:t,displacedBy:i,last:null,viewport:r.frame,forceShouldAnimate:!1}),displacedBy:i,at:{type:"REORDER",destination:ns(e.descriptor)}},afterCritical:u}},nd=(e,t)=>({draggables:e.draggables,droppables:nt(e.droppables,t)});let nc=e=>{},np=e=>{};var nh=({draggable:e,offset:t,initialWindowScroll:n})=>{let r=ed(e.client,t),i=ec(r,n);return{...e,placeholder:{...e.placeholder,client:r},client:r,page:i}},nf=e=>{let t=e.frame;return t||eP(),t},ng=({additions:e,updatedDroppables:t,viewport:n})=>{let r=n.scroll.diff.value;return e.map(e=>{let i=eN(r,nf(t[e.descriptor.droppableId]).scroll.diff.value);return nh({draggable:e,offset:i,initialWindowScroll:n.scroll.initial})})},nm=({state:e,published:t})=>{nc();let n=t.modified.map(t=>eZ(e.dimensions.droppables[t.droppableId],t.scroll)),r={...e.dimensions.droppables,...eJ(n)},i=e0(ng({additions:t.additions,updatedDroppables:r,viewport:e.viewport})),o={...e.dimensions.draggables,...i};t.removals.forEach(e=>{delete o[e]});let l={droppables:r,draggables:o},a=t0(e.impact),s=a?l.droppables[a]:null,{impact:u,afterCritical:d}=nu({draggable:l.draggables[e.critical.draggable.id],home:l.droppables[e.critical.droppable.id],draggables:o,viewport:e.viewport}),c=s&&s.isCombineEnabled?e.impact:u,p=ne({pageOffset:e.current.page.offset,draggable:l.draggables[e.critical.draggable.id],draggables:l.draggables,droppables:l.droppables,previousImpact:c,viewport:e.viewport,afterCritical:d});np();let h={...e,phase:"DRAGGING",impact:p,onLiftImpact:u,dimensions:l,afterCritical:d,forceShouldAnimate:!1};return"COLLECTING"===e.phase?h:{...h,phase:"DROP_PENDING",reason:e.reason,isWaiting:!1}};let nv=e=>"SNAP"===e.movementMode,ny=(e,t,n)=>{let r=nd(e.dimensions,t);return!nv(e)||n?ni({state:e,dimensions:r}):na({state:e,dimensions:r})};function nb(e){return e.isDragging&&"SNAP"===e.movementMode?{...e,scrollJumpRequest:null}:e}let nw={phase:"IDLE",completed:null,shouldFlush:!1};var nx=(e=nw,t)=>{if("FLUSH"===t.type)return{...nw,shouldFlush:!0};if("INITIAL_PUBLISH"===t.type){"IDLE"!==e.phase&&eP();let{critical:n,clientSelection:r,viewport:i,dimensions:o,movementMode:l}=t.payload,a=o.draggables[n.draggable.id],s=o.droppables[n.droppable.id],u={selection:r,borderBoxCenter:a.client.borderBox.center,offset:eO},d={client:u,page:{selection:eN(u.selection,i.scroll.initial),borderBoxCenter:eN(u.selection,i.scroll.initial),offset:eN(u.selection,i.scroll.diff.value)}},c=e1(o.droppables).every(e=>!e.isFixedOnPage),{impact:p,afterCritical:h}=nu({draggable:a,home:s,draggables:o.draggables,viewport:i});return{phase:"DRAGGING",isDragging:!0,critical:n,movementMode:l,dimensions:o,initial:d,current:d,isWindowScrollAllowed:c,impact:p,afterCritical:h,onLiftImpact:p,viewport:i,scrollJumpRequest:null,forceShouldAnimate:null}}if("COLLECTION_STARTING"===t.type)return"COLLECTING"===e.phase||"DROP_PENDING"===e.phase?e:("DRAGGING"!==e.phase&&eP(),{...e,phase:"COLLECTING"});if("PUBLISH_WHILE_DRAGGING"===t.type)return"COLLECTING"!==e.phase&&"DROP_PENDING"!==e.phase&&eP(),nm({state:e,published:t.payload});if("MOVE"===t.type){if("DROP_PENDING"===e.phase)return e;t5(e)||eP();let{client:n}=t.payload;return ej(n,e.current.client.selection)?e:ni({state:e,clientSelection:n,impact:nv(e)?e.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"COLLECTING"===e.phase)return nb(e);t5(e)||eP();let{id:n,newScroll:r}=t.payload,i=e.dimensions.droppables[n];return i?ny(e,eZ(i,r),!1):e}if("UPDATE_DROPPABLE_IS_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;t5(e)||eP();let{id:n,isEnabled:r}=t.payload,i=e.dimensions.droppables[n];return i||eP(),i.isEnabled===r&&eP(),ny(e,{...i,isEnabled:r},!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;t5(e)||eP();let{id:n,isCombineEnabled:r}=t.payload,i=e.dimensions.droppables[n];return i||eP(),i.isCombineEnabled===r&&eP(),ny(e,{...i,isCombineEnabled:r},!0)}if("MOVE_BY_WINDOW_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"DROP_ANIMATING"===e.phase)return e;t5(e)||eP(),e.isWindowScrollAllowed||eP();let n=t.payload.newScroll;if(ej(e.viewport.scroll.current,n))return nb(e);let r=tO(e.viewport,n);return nv(e)?na({state:e,viewport:r}):ni({state:e,viewport:r})}if("UPDATE_VIEWPORT_MAX_SCROLL"===t.type){if(!t5(e))return e;let n=t.payload.maxScroll;if(ej(n,e.viewport.scroll.max))return e;let r={...e.viewport,scroll:{...e.viewport.scroll,max:n}};return{...e,viewport:r}}if("MOVE_UP"===t.type||"MOVE_DOWN"===t.type||"MOVE_LEFT"===t.type||"MOVE_RIGHT"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&eP();let n=t2({state:e,type:t.type});return n?ni({state:e,impact:n.impact,clientSelection:n.clientSelection,scrollJumpRequest:n.scrollJumpRequest}):e}if("DROP_PENDING"===t.type){let n=t.payload.reason;return"COLLECTING"!==e.phase&&eP(),{...e,phase:"DROP_PENDING",isWaiting:!0,reason:n}}if("DROP_ANIMATE"===t.type){let{completed:n,dropDuration:r,newHomeClientOffset:i}=t.payload;return"DRAGGING"!==e.phase&&"DROP_PENDING"!==e.phase&&eP(),{phase:"DROP_ANIMATING",completed:n,dropDuration:r,newHomeClientOffset:i,dimensions:e.dimensions}}if("DROP_COMPLETE"===t.type){let{completed:e}=t.payload;return{phase:"IDLE",completed:e,shouldFlush:!1}}return e};function nS(e,t){return e instanceof Object&&"type"in e&&e.type===t}let nC=e=>({type:"BEFORE_INITIAL_CAPTURE",payload:e}),nR=e=>({type:"LIFT",payload:e}),nP=e=>({type:"INITIAL_PUBLISH",payload:e}),nE=e=>({type:"PUBLISH_WHILE_DRAGGING",payload:e}),nA=()=>({type:"COLLECTION_STARTING",payload:null}),nD=e=>({type:"UPDATE_DROPPABLE_SCROLL",payload:e}),nI=e=>({type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}),nM=e=>({type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}),nT=e=>({type:"MOVE",payload:e}),nk=e=>({type:"MOVE_BY_WINDOW_SCROLL",payload:e}),nL=()=>({type:"MOVE_UP",payload:null}),nF=()=>({type:"MOVE_DOWN",payload:null}),nO=()=>({type:"MOVE_RIGHT",payload:null}),nN=()=>({type:"MOVE_LEFT",payload:null}),nV=()=>({type:"FLUSH",payload:null}),nj=e=>({type:"DROP_ANIMATE",payload:e}),nB=e=>({type:"DROP_COMPLETE",payload:e}),nG=e=>({type:"DROP",payload:e}),n_=e=>({type:"DROP_PENDING",payload:e}),nz=()=>({type:"DROP_ANIMATION_FINISHED",payload:null});var nH=e=>({getState:t,dispatch:n})=>r=>i=>{if(!nS(i,"LIFT"))return void r(i);let{id:o,clientSelection:l,movementMode:a}=i.payload,s=t();"DROP_ANIMATING"===s.phase&&n(nB({completed:s.completed})),"IDLE"!==t().phase&&eP(),n(nV()),n(nC({draggableId:o,movementMode:a}));let{critical:u,dimensions:d,viewport:c}=e.startPublishing({draggableId:o,scrollOptions:{shouldPublishImmediately:"SNAP"===a}});n(nP({critical:u,dimensions:d,clientSelection:l,movementMode:a,viewport:c}))},n$=e=>()=>t=>n=>{nS(n,"INITIAL_PUBLISH")&&e.dragging(),nS(n,"DROP_ANIMATE")&&e.dropping(n.payload.completed.result.reason),(nS(n,"FLUSH")||nS(n,"DROP_COMPLETE"))&&e.resting(),t(n)};let nU={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},nW={opacity:{drop:0,combining:.7},scale:{drop:.75}},nq={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},nY=`${nq.outOfTheWay}s ${nU.outOfTheWay}`,nX={fluid:`opacity ${nY}`,snap:`transform ${nY}, opacity ${nY}`,drop:e=>{let t=`${e}s ${nU.drop}`;return`transform ${t}, opacity ${t}`},outOfTheWay:`transform ${nY}`,placeholder:`height ${nY}, width ${nY}, margin ${nY}`},nK=e=>ej(e,eO)?void 0:`translate(${e.x}px, ${e.y}px)`,nZ={moveTo:nK,drop:(e,t)=>{let n=nK(e);if(n)return t?`${n} scale(${nW.scale.drop})`:n}},{minDropTime:nQ,maxDropTime:nJ}=nq,n0=nJ-nQ;var n1=({current:e,destination:t,reason:n})=>{let r=e_(e,t);if(r<=0)return nQ;if(r>=1500)return nJ;let i=nQ+r/1500*n0;return Number(("CANCEL"===n?.6*i:i).toFixed(2))},n2=({impact:e,draggable:t,dimensions:n,viewport:r,afterCritical:i})=>{let{draggables:o,droppables:l}=n,a=t0(e),s=a?l[a]:null,u=l[t.descriptor.droppableId];return eV(nl({impact:e,draggable:t,draggables:o,afterCritical:i,droppable:s||u,viewport:r}),t.client.borderBox.center)},n5=({draggables:e,reason:t,lastImpact:n,home:r,viewport:i,onLiftImpact:o})=>n.at&&"DROP"===t?"REORDER"===n.at.type?{impact:n,didDropInsideDroppable:!0}:{impact:{...n,displaced:te},didDropInsideDroppable:!0}:{impact:no({draggables:e,impact:o,destination:r,viewport:i,forceShouldAnimate:!0}),didDropInsideDroppable:!1};let n4=({getState:e,dispatch:t})=>n=>r=>{if(!nS(r,"DROP"))return void n(r);let i=e(),o=r.payload.reason;if("COLLECTING"===i.phase)return void t(n_({reason:o}));if("IDLE"===i.phase)return;"DROP_PENDING"===i.phase&&i.isWaiting&&eP(),"DRAGGING"!==i.phase&&"DROP_PENDING"!==i.phase&&eP();let l=i.critical,a=i.dimensions,s=a.draggables[i.critical.draggable.id],{impact:u,didDropInsideDroppable:d}=n5({reason:o,lastImpact:i.impact,afterCritical:i.afterCritical,onLiftImpact:i.onLiftImpact,home:i.dimensions.droppables[i.critical.droppable.id],viewport:i.viewport,draggables:i.dimensions.draggables}),c=d?e4(u):null,p=d?e3(u):null,h={index:l.draggable.index,droppableId:l.droppable.id},f={draggableId:s.descriptor.id,type:s.descriptor.type,source:h,reason:o,mode:i.movementMode,destination:c,combine:p},g=n2({impact:u,draggable:s,dimensions:a,viewport:i.viewport,afterCritical:i.afterCritical}),m={critical:i.critical,afterCritical:i.afterCritical,result:f,impact:u};if(!(!ej(i.current.client.offset,g)||f.combine))return void t(nB({completed:m}));let v=n1({current:i.current.client.offset,destination:g,reason:o});t(nj({newHomeClientOffset:g,dropDuration:v,completed:m}))};var n3=()=>({x:window.pageXOffset,y:window.pageYOffset});let n7=e=>nS(e,"DROP_COMPLETE")||nS(e,"DROP_ANIMATE")||nS(e,"FLUSH"),n8=e=>{let t=function({onWindowScroll:e}){let t=ef(function(){e(n3())}),n={eventName:"scroll",options:{passive:!0,capture:!1},fn:e=>{(e.target===window||e.target===window.document)&&t()}},r=eS;function i(){return r!==eS}return{start:function(){i()&&eP(),r=eC(window,[n])},stop:function(){i()||eP(),t.cancel(),r(),r=eS},isActive:i}}({onWindowScroll:t=>{e.dispatch(nk({newScroll:t}))}});return e=>n=>{!t.isActive()&&nS(n,"INITIAL_PUBLISH")&&t.start(),t.isActive()&&n7(n)&&t.stop(),e(n)}};var n9=e=>{let t=!1,n=!1,r=setTimeout(()=>{n=!0}),i=i=>{!t&&(n||(t=!0,e(i),clearTimeout(r)))};return i.wasCalled=()=>t,i},n6=()=>{let e=[],t=t=>{let n=e.findIndex(e=>e.timerId===t);-1===n&&eP();let[r]=e.splice(n,1);r.callback()};return{add:n=>{let r=setTimeout(()=>t(r));e.push({timerId:r,callback:n})},flush:()=>{if(!e.length)return;let t=[...e];e.length=0,t.forEach(e=>{clearTimeout(e.timerId),e.callback()})}}};let re=(e,t)=>null==e&&null==t||null!=e&&null!=t&&e.droppableId===t.droppableId&&e.index===t.index,rt=(e,t)=>null==e&&null==t||null!=e&&null!=t&&e.draggableId===t.draggableId&&e.droppableId===t.droppableId,rn=(e,t)=>{if(e===t)return!0;let n=e.draggable.id===t.draggable.id&&e.draggable.droppableId===t.draggable.droppableId&&e.draggable.type===t.draggable.type&&e.draggable.index===t.draggable.index,r=e.droppable.id===t.droppable.id&&e.droppable.type===t.droppable.type;return n&&r},rr=(e,t)=>{nc(),t(),np()},ri=(e,t)=>({draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t});function ro(e,t,n,r){if(!e)return void n(r(t));let i=n9(n);e(t,{announce:i}),i.wasCalled()||n(r(t))}var rl=(e,t)=>{let n=n6(),r=null,i=n=>{r||eP(),r=null,rr("onDragEnd",()=>ro(e().onDragEnd,n,t,eT.onDragEnd))};return{beforeCapture:(t,n)=>{r&&eP(),rr("onBeforeCapture",()=>{let r=e().onBeforeCapture;r&&r({draggableId:t,mode:n})})},beforeStart:(t,n)=>{r&&eP(),rr("onBeforeDragStart",()=>{let r=e().onBeforeDragStart;r&&r(ri(t,n))})},start:(i,o)=>{r&&eP();let l=ri(i,o);r={mode:o,lastCritical:i,lastLocation:l.source,lastCombine:null},n.add(()=>{rr("onDragStart",()=>ro(e().onDragStart,l,t,eT.onDragStart))})},update:(i,o)=>{let l=e4(o),a=e3(o);r||eP();let s=!rn(i,r.lastCritical);s&&(r.lastCritical=i);let u=!re(r.lastLocation,l);u&&(r.lastLocation=l);let d=!rt(r.lastCombine,a);if(d&&(r.lastCombine=a),!s&&!u&&!d)return;let c={...ri(i,r.mode),combine:a,destination:l};n.add(()=>{rr("onDragUpdate",()=>ro(e().onDragUpdate,c,t,eT.onDragUpdate))})},flush:()=>{r||eP(),n.flush()},drop:i,abort:()=>{r&&i({...ri(r.lastCritical,r.mode),combine:null,destination:null,reason:"CANCEL"})}}},ra=(e,t)=>{let n=rl(e,t);return e=>t=>r=>{if(nS(r,"BEFORE_INITIAL_CAPTURE"))return void n.beforeCapture(r.payload.draggableId,r.payload.movementMode);if(nS(r,"INITIAL_PUBLISH")){let e=r.payload.critical;n.beforeStart(e,r.payload.movementMode),t(r),n.start(e,r.payload.movementMode);return}if(nS(r,"DROP_COMPLETE")){let e=r.payload.completed.result;n.flush(),t(r),n.drop(e);return}if(t(r),nS(r,"FLUSH"))return void n.abort();let i=e.getState();"DRAGGING"===i.phase&&n.update(i.critical,i.impact)}};let rs=e=>t=>n=>{if(!nS(n,"DROP_ANIMATION_FINISHED"))return void t(n);let r=e.getState();"DROP_ANIMATING"!==r.phase&&eP(),e.dispatch(nB({completed:r.completed}))},ru=e=>{let t=null,n=null;return r=>i=>{if((nS(i,"FLUSH")||nS(i,"DROP_COMPLETE")||nS(i,"DROP_ANIMATION_FINISHED"))&&(n&&(cancelAnimationFrame(n),n=null),t&&(t(),t=null)),r(i),!nS(i,"DROP_ANIMATE"))return;let o={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===e.getState().phase&&e.dispatch(nz())}};n=requestAnimationFrame(()=>{n=null,t=eC(window,[o])})}};var rd=e=>()=>t=>n=>{(nS(n,"DROP_COMPLETE")||nS(n,"FLUSH")||nS(n,"DROP_ANIMATE"))&&e.stopPublishing(),t(n)},rc=e=>{let t=!1;return()=>n=>r=>{if(nS(r,"INITIAL_PUBLISH")){t=!0,e.tryRecordFocus(r.payload.critical.draggable.id),n(r),e.tryRestoreFocusRecorded();return}if(n(r),t){if(nS(r,"FLUSH")){t=!1,e.tryRestoreFocusRecorded();return}if(nS(r,"DROP_COMPLETE")){t=!1;let n=r.payload.completed.result;n.combine&&e.tryShiftRecord(n.draggableId,n.combine.draggableId),e.tryRestoreFocusRecorded()}}}};let rp=e=>nS(e,"DROP_COMPLETE")||nS(e,"DROP_ANIMATE")||nS(e,"FLUSH");var rh=e=>t=>n=>r=>{if(rp(r)){e.stop(),n(r);return}if(nS(r,"INITIAL_PUBLISH")){n(r);let i=t.getState();"DRAGGING"!==i.phase&&eP(),e.start(i);return}n(r),e.scroll(t.getState())};let rf=e=>t=>n=>{if(t(n),!nS(n,"PUBLISH_WHILE_DRAGGING"))return;let r=e.getState();"DROP_PENDING"===r.phase&&(r.isWaiting||e.dispatch(nG({reason:r.reason})))};var rg=({dimensionMarshal:e,focusMarshal:t,styleMarshal:n,getResponders:r,announce:i,autoScroller:o})=>(function e(t,n,r){if("function"!=typeof t)throw Error(a(2));if("function"==typeof n&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error(a(0));if("function"==typeof n&&void 0===r&&(r=n,n=void 0),void 0!==r){if("function"!=typeof r)throw Error(a(1));return r(e)(t,n)}let i=t,o=n,l=new Map,u=l,c=0,p=!1;function h(){u===l&&(u=new Map,l.forEach((e,t)=>{u.set(t,e)}))}function f(){if(p)throw Error(a(3));return o}function g(e){if("function"!=typeof e)throw Error(a(4));if(p)throw Error(a(5));let t=!0;h();let n=c++;return u.set(n,e),function(){if(t){if(p)throw Error(a(6));t=!1,h(),u.delete(n),l=null}}}function m(e){if(!function(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}(e))throw Error(a(7));if(void 0===e.type)throw Error(a(8));if("string"!=typeof e.type)throw Error(a(17));if(p)throw Error(a(9));try{p=!0,o=i(o,e)}finally{p=!1}return(l=u).forEach(e=>{e()}),e}return m({type:d.INIT}),{dispatch:m,subscribe:g,getState:f,replaceReducer:function(e){if("function"!=typeof e)throw Error(a(10));i=e,m({type:d.REPLACE})},[s]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(a(11));function t(){e.next&&e.next(f())}return t(),{unsubscribe:g(t)}},[s](){return this}}}}})(nx,h(function(...e){return t=>(n,r)=>{let i=t(n,r),o=()=>{throw Error(a(15))},l={getState:i.getState,dispatch:(e,...t)=>o(e,...t)};return o=h(...e.map(e=>e(l)))(i.dispatch),{...i,dispatch:o}}}(n$(n),rd(e),nH(e),n4,rs,ru,rf,rh(o),n8,rc(t),ra(r,i))));let rm=()=>({additions:{},removals:{},modified:{}});var rv=({scrollHeight:e,scrollWidth:t,height:n,width:r})=>{let i=eV({x:t,y:e},{x:r,y:n});return{x:Math.max(0,i.x),y:Math.max(0,i.y)}},ry=()=>{let e=document.documentElement;return e||eP(),e},rb=()=>{let e=ry();return rv({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})},rw=()=>{let e=n3(),t=rb(),n=e.y,r=e.x,i=ry(),o=i.clientWidth;return{frame:ei({top:n,left:r,right:r+o,bottom:n+i.clientHeight}),scroll:{initial:e,current:e,max:t,diff:{value:eO,displacement:eO}}}},rx=({critical:e,scrollOptions:t,registry:n})=>{nc();let r=rw(),i=r.scroll.current,o=e.droppable,l=n.droppable.getAllByType(o.type).map(e=>e.callbacks.getDimensionAndWatchScroll(i,t)),a={draggables:e0(n.draggable.getAllByType(e.draggable.type).map(e=>e.getDimension(i))),droppables:eJ(l)};return np(),{dimensions:a,critical:e,viewport:r}};function rS(e,t,n){return n.descriptor.id!==t.id&&n.descriptor.type===t.type&&"virtual"===e.droppable.getById(n.descriptor.droppableId).descriptor.mode}var rC=(e,t)=>{let n=null,r=function({registry:e,callbacks:t}){let n=rm(),r=null,i=()=>{r||(t.collectionStarting(),r=requestAnimationFrame(()=>{r=null,nc();let{additions:i,removals:o,modified:l}=n,a=Object.keys(i).map(t=>e.draggable.getById(t).getDimension(eO)).sort((e,t)=>e.descriptor.index-t.descriptor.index),s=Object.keys(l).map(t=>{let n=e.droppable.getById(t).callbacks.getScrollWhileDragging();return{droppableId:t,scroll:n}}),u={additions:a,removals:Object.keys(o),modified:s};n=rm(),np(),t.publish(u)}))};return{add:e=>{let t=e.descriptor.id;n.additions[t]=e,n.modified[e.descriptor.droppableId]=!0,n.removals[t]&&delete n.removals[t],i()},remove:e=>{let t=e.descriptor;n.removals[t.id]=!0,n.modified[t.droppableId]=!0,n.additions[t.id]&&delete n.additions[t.id],i()},stop:()=>{r&&(cancelAnimationFrame(r),r=null,n=rm())}}}({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),i=t=>{n||eP();let i=n.critical.draggable;"ADDITION"===t.type&&rS(e,i,t.value)&&r.add(t.value),"REMOVAL"===t.type&&rS(e,i,t.value)&&r.remove(t.value)};return{updateDroppableIsEnabled:(r,i)=>{e.droppable.exists(r)||eP(),n&&t.updateDroppableIsEnabled({id:r,isEnabled:i})},updateDroppableIsCombineEnabled:(r,i)=>{n&&(e.droppable.exists(r)||eP(),t.updateDroppableIsCombineEnabled({id:r,isCombineEnabled:i}))},scrollDroppable:(t,r)=>{n&&e.droppable.getById(t).callbacks.scroll(r)},updateDroppableScroll:(r,i)=>{n&&(e.droppable.exists(r)||eP(),t.updateDroppableScroll({id:r,newScroll:i}))},startPublishing:t=>{n&&eP();let r=e.draggable.getById(t.draggableId),o=e.droppable.getById(r.descriptor.droppableId),l={draggable:r.descriptor,droppable:o.descriptor};return n={critical:l,unsubscribe:e.subscribe(i)},rx({critical:l,registry:e,scrollOptions:t.scrollOptions})},stopPublishing:()=>{if(!n)return;r.stop();let t=n.critical.droppable;e.droppable.getAllByType(t.type).forEach(e=>e.callbacks.dragStopped()),n.unsubscribe(),n=null}}},rR=(e,t)=>"IDLE"===e.phase||"DROP_ANIMATING"===e.phase&&e.completed.result.draggableId!==t&&"DROP"===e.completed.result.reason,rP=e=>{window.scrollBy(e.x,e.y)};let rE=eQ(e=>e1(e).filter(e=>!!e.isEnabled&&!!e.frame)),rA=(e,t)=>rE(t).find(t=>(t.frame||eP(),t4(t.frame.pageMarginBox)(e)))||null;var rD=({center:e,destination:t,droppables:n})=>{if(t){let e=n[t];return e.frame?e:null}return rA(e,n)};let rI={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:e=>e**2,durationDampening:{stopDampeningAt:1200,accelerateAt:360},disabled:!1};var rM=(e,t,n=()=>rI)=>{let r=n(),i=e[t.size]*r.startFromPercentage;return{startScrollingFrom:i,maxScrollValueAt:e[t.size]*r.maxScrollAtPercentage}},rT=({startOfRange:e,endOfRange:t,current:n})=>{let r=t-e;return 0===r?0:(n-e)/r},rk=(e,t,n=()=>rI)=>{let r=n();if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return r.maxPixelScroll;if(e===t.startScrollingFrom)return 1;let i=rT({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e});return Math.ceil(r.maxPixelScroll*r.ease(1-i))},rL=(e,t,n)=>{let r=n(),i=r.durationDampening.accelerateAt,o=r.durationDampening.stopDampeningAt,l=Date.now()-t;if(l>=o)return e;if(l<i)return 1;let a=rT({startOfRange:i,endOfRange:o,current:l});return Math.ceil(e*r.ease(a))},rF=({distanceToEdge:e,thresholds:t,dragStartTime:n,shouldUseTimeDampening:r,getAutoScrollerOptions:i})=>{let o=rk(e,t,i);return 0===o?0:r?Math.max(rL(o,n,i),1):o},rO=({container:e,distanceToEdges:t,dragStartTime:n,axis:r,shouldUseTimeDampening:i,getAutoScrollerOptions:o})=>{let l=rM(e,r,o);return t[r.end]<t[r.start]?rF({distanceToEdge:t[r.end],thresholds:l,dragStartTime:n,shouldUseTimeDampening:i,getAutoScrollerOptions:o}):-1*rF({distanceToEdge:t[r.start],thresholds:l,dragStartTime:n,shouldUseTimeDampening:i,getAutoScrollerOptions:o})},rN=({container:e,subject:t,proposedScroll:n})=>{let r=t.height>e.height,i=t.width>e.width;return i||r?i&&r?null:{x:i?0:n.x,y:r?0:n.y}:n};let rV=eH(e=>0===e?0:e);var rj=({dragStartTime:e,container:t,subject:n,center:r,shouldUseTimeDampening:i,getAutoScrollerOptions:o})=>{let l={top:r.y-t.top,right:t.right-r.x,bottom:t.bottom-r.y,left:r.x-t.left},a=rO({container:t,distanceToEdges:l,dragStartTime:e,axis:to,shouldUseTimeDampening:i,getAutoScrollerOptions:o}),s=rV({x:rO({container:t,distanceToEdges:l,dragStartTime:e,axis:tl,shouldUseTimeDampening:i,getAutoScrollerOptions:o}),y:a});if(ej(s,eO))return null;let u=rN({container:t,subject:n,proposedScroll:s});return u?ej(u,eO)?null:u:null};let rB=eH(e=>0===e?0:e>0?1:-1),rG=(()=>{let e=(e,t)=>e<0?e:e>t?e-t:0;return({current:t,max:n,change:r})=>{let i=eN(t,r),o={x:e(i.x,n.x),y:e(i.y,n.y)};return ej(o,eO)?null:o}})(),r_=({max:e,current:t,change:n})=>{let r={x:Math.max(t.x,e.x),y:Math.max(t.y,e.y)},i=rB(n),o=rG({max:r,current:t,change:i});return!o||0!==i.x&&0===o.x||0!==i.y&&0===o.y},rz=(e,t)=>r_({current:e.scroll.current,max:e.scroll.max,change:t}),rH=(e,t)=>{if(!rz(e,t))return null;let n=e.scroll.max;return rG({current:e.scroll.current,max:n,change:t})},r$=(e,t)=>{let n=e.frame;return!!n&&r_({current:n.scroll.current,max:n.scroll.max,change:t})},rU=(e,t)=>{let n=e.frame;return n&&r$(e,t)?rG({current:n.scroll.current,max:n.scroll.max,change:t}):null};var rW=({viewport:e,subject:t,center:n,dragStartTime:r,shouldUseTimeDampening:i,getAutoScrollerOptions:o})=>{let l=rj({dragStartTime:r,container:e.frame,subject:t,center:n,shouldUseTimeDampening:i,getAutoScrollerOptions:o});return l&&rz(e,l)?l:null},rq=({droppable:e,subject:t,center:n,dragStartTime:r,shouldUseTimeDampening:i,getAutoScrollerOptions:o})=>{let l=e.frame;if(!l)return null;let a=rj({dragStartTime:r,container:l.pageMarginBox,subject:t,center:n,shouldUseTimeDampening:i,getAutoScrollerOptions:o});return a&&r$(e,a)?a:null},rY=({state:e,dragStartTime:t,shouldUseTimeDampening:n,scrollWindow:r,scrollDroppable:i,getAutoScrollerOptions:o})=>{let l=e.current.page.borderBoxCenter,a=e.dimensions.draggables[e.critical.draggable.id].page.marginBox;if(e.isWindowScrollAllowed){let i=rW({dragStartTime:t,viewport:e.viewport,subject:a,center:l,shouldUseTimeDampening:n,getAutoScrollerOptions:o});if(i)return void r(i)}let s=rD({center:l,destination:t0(e.impact),droppables:e.dimensions.droppables});if(!s)return;let u=rq({dragStartTime:t,droppable:s,subject:a,center:l,shouldUseTimeDampening:n,getAutoScrollerOptions:o});u&&i(s.descriptor.id,u)},rX=({scrollWindow:e,scrollDroppable:t,getAutoScrollerOptions:n=()=>rI})=>{let r=ef(e),i=ef(t),o=null,l=e=>{o||eP();let{shouldUseTimeDampening:t,dragStartTime:l}=o;rY({state:e,scrollWindow:r,scrollDroppable:i,dragStartTime:l,shouldUseTimeDampening:t,getAutoScrollerOptions:n})};return{start:e=>{nc(),o&&eP();let t=Date.now(),r=!1,i=()=>{r=!0};rY({state:e,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:i,scrollDroppable:i,getAutoScrollerOptions:n}),o={dragStartTime:t,shouldUseTimeDampening:r},np(),r&&l(e)},stop:()=>{o&&(r.cancel(),i.cancel(),o=null)},scroll:l}},rK=({move:e,scrollDroppable:t,scrollWindow:n})=>{let r=(t,n)=>{e({client:eN(t.current.client.selection,n)})},i=(e,n)=>{if(!r$(e,n))return n;let r=rU(e,n);if(!r)return t(e.descriptor.id,n),null;let i=eV(n,r);return t(e.descriptor.id,i),eV(n,i)},o=(e,t,r)=>{if(!e||!rz(t,r))return r;let i=rH(t,r);if(!i)return n(r),null;let o=eV(r,i);return n(o),eV(r,o)};return e=>{let t=e.scrollJumpRequest;if(!t)return;let n=t0(e.impact);n||eP();let l=i(e.dimensions.droppables[n],t);if(!l)return;let a=e.viewport,s=o(e.isWindowScrollAllowed,a,l);s&&r(e,s)}},rZ=({scrollDroppable:e,scrollWindow:t,move:n,getAutoScrollerOptions:r})=>{let i=rX({scrollWindow:t,scrollDroppable:e,getAutoScrollerOptions:r}),o=rK({move:n,scrollWindow:t,scrollDroppable:e});return{scroll:e=>{if(!r().disabled&&"DRAGGING"===e.phase){if("FLUID"===e.movementMode)return void i.scroll(e);e.scrollJumpRequest&&o(e)}},start:i.start,stop:i.stop}};let rQ="data-rfd",rJ=(()=>{let e=`${rQ}-drag-handle`;return{base:e,draggableId:`${e}-draggable-id`,contextId:`${e}-context-id`}})(),r0=(()=>{let e=`${rQ}-draggable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),r1=(()=>{let e=`${rQ}-droppable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),r2={contextId:`${rQ}-scroll-container-context-id`},r5=e=>t=>`[${t}="${e}"]`,r4=(e,t)=>e.map(e=>{let n=e.styles[t];return n?`${e.selector} { ${n} }`:""}).join(" ");var r3=e=>{let t=r5(e),n=(()=>{let e=`
      cursor: -webkit-grab;
      cursor: grab;
    `;return{selector:t(rJ.contextId),styles:{always:`
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
        `,resting:e,dragging:"pointer-events: none;",dropAnimating:e}}})(),r=(()=>{let e=`
      transition: ${nX.outOfTheWay};
    `;return{selector:t(r0.contextId),styles:{dragging:e,dropAnimating:e,userCancel:e}}})(),i=[r,n,{selector:t(r1.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:`
        cursor: grabbing;
        cursor: -webkit-grabbing;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        overflow-anchor: none;
      `}}];return{always:r4(i,"always"),resting:r4(i,"resting"),dragging:r4(i,"dragging"),dropAnimating:r4(i,"dropAnimating"),userCancel:r4(i,"userCancel")}};let r7="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?r.useLayoutEffect:r.useEffect,r8=()=>{let e=document.querySelector("head");return e||eP(),e},r9=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t};function r6(e,t){return Array.from(e.querySelectorAll(t))}var ie=e=>e&&e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView:window;function it(e){return e instanceof ie(e).HTMLElement}function ir(e,t){let n=r6(document,`[${rJ.contextId}="${e}"]`);if(!n.length)return null;let r=n.find(e=>e.getAttribute(rJ.draggableId)===t);return r&&it(r)?r:null}function ii(){let e={draggables:{},droppables:{}},t=[];function n(e){t.length&&t.forEach(t=>t(e))}function r(t){return e.draggables[t]||null}function i(t){return e.droppables[t]||null}return{draggable:{register:t=>{e.draggables[t.descriptor.id]=t,n({type:"ADDITION",value:t})},update:(t,n)=>{let r=e.draggables[n.descriptor.id];r&&r.uniqueId===t.uniqueId&&(delete e.draggables[n.descriptor.id],e.draggables[t.descriptor.id]=t)},unregister:t=>{let i=t.descriptor.id,o=r(i);o&&t.uniqueId===o.uniqueId&&(delete e.draggables[i],e.droppables[t.descriptor.droppableId]&&n({type:"REMOVAL",value:t}))},getById:function(e){let t=r(e);return t||eP(),t},findById:r,exists:e=>!!r(e),getAllByType:t=>Object.values(e.draggables).filter(e=>e.descriptor.type===t)},droppable:{register:t=>{e.droppables[t.descriptor.id]=t},unregister:t=>{let n=i(t.descriptor.id);n&&t.uniqueId===n.uniqueId&&delete e.droppables[t.descriptor.id]},getById:function(e){let t=i(e);return t||eP(),t},findById:i,exists:e=>!!i(e),getAllByType:t=>Object.values(e.droppables).filter(e=>e.descriptor.type===t)},subscribe:function(e){return t.push(e),function(){let n=t.indexOf(e);-1!==n&&t.splice(n,1)}},clean:function(){e.draggables={},e.droppables={},t.length=0}}}var io=i().createContext(null),il=()=>{let e=document.body;return e||eP(),e};let ia={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},is=e=>`rfd-announcement-${e}`,iu={separator:"::"};function id(e,t=iu){let n=i().useId();return eL(()=>`${e}${t.separator}${n}`,[t.separator,e,n])}var ic=i().createContext(null),ip={react:"^18.0.0 || ^19.0.0"};let ih=/(\d+)\.(\d+)\.(\d+)/,ig=e=>{let t=ih.exec(e);null==t&&eP();let n=Number(t[1]);return{major:n,minor:Number(t[2]),patch:Number(t[3]),raw:e}},im=(e,t)=>t.major>e.major||!(t.major<e.major)&&(t.minor>e.minor||!(t.minor<e.minor)&&t.patch>=e.patch);var iv=(e,t)=>{if(im(ig(e),ig(t)))return},iy=e=>{let t=e.doctype;t&&(t.name.toLowerCase(),t.publicId)};function ib(e){}function iw(e,t){}function ix(e){let t=(0,r.useRef)(e);return(0,r.useEffect)(()=>{t.current=e}),t}function iS(e){return"IDLE"!==e.phase&&"DROP_ANIMATING"!==e.phase&&e.isDragging}let iC={13:!0,9:!0};var iR=e=>{iC[e.keyCode]&&e.preventDefault()};let iP=(()=>{let e="visibilitychange";return"undefined"==typeof document?e:[e,`ms${e}`,`webkit${e}`,`moz${e}`,`o${e}`].find(e=>`on${e}`in document)||e})(),iE={type:"IDLE"};function iA(){}let iD={34:!0,33:!0,36:!0,35:!0},iI={type:"IDLE"},iM=["input","button","textarea","select","option","optgroup","video","audio"];var iT=e=>ei(e.getBoundingClientRect()).center;let ik=(()=>{let e="matches";return"undefined"==typeof document?e:[e,"msMatchesSelector","webkitMatchesSelector"].find(e=>e in Element.prototype)||e})();function iL(e){e.preventDefault()}function iF({expected:e,phase:t,isLockActive:n,shouldWarn:r}){return!!n()&&e===t}function iO({lockAPI:e,store:t,registry:n,draggableId:r}){if(e.isClaimed())return!1;let i=n.draggable.findById(r);return!!i&&!!i.options.isEnabled&&!!rR(t.getState(),r)}let iN=[function(e){let t=(0,r.useRef)(iE),n=(0,r.useRef)(eS),i=eL(()=>({eventName:"mousedown",fn:function(t){if(t.defaultPrevented||0!==t.button||t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)return;let r=e.findClosestDraggableId(t);if(!r)return;let i=e.tryGetLock(r,a,{sourceEvent:t});if(!i)return;t.preventDefault();let o={x:t.clientX,y:t.clientY};n.current(),d(i,o)}}),[e]),o=eL(()=>({eventName:"webkitmouseforcewillbegin",fn:t=>{if(t.defaultPrevented)return;let n=e.findClosestDraggableId(t);if(!n)return;let r=e.findOptionsForDraggable(n);r&&!r.shouldRespectForcePress&&e.canGetLock(n)&&t.preventDefault()}}),[e]),l=eF(function(){n.current=eC(window,[o,i],{passive:!1,capture:!0})},[o,i]),a=eF(()=>{"IDLE"!==t.current.type&&(t.current=iE,n.current(),l())},[l]),s=eF(()=>{let e=t.current;a(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[a]),u=eF(function(){n.current=eC(window,function({cancel:e,completed:t,getPhase:n,setPhase:r}){return[{eventName:"mousemove",fn:e=>{var t;let{button:i,clientX:o,clientY:l}=e;if(0!==i)return;let a={x:o,y:l},s=n();if("DRAGGING"===s.type){e.preventDefault(),s.actions.move(a);return}"PENDING"!==s.type&&eP(),t=s.point,(Math.abs(a.x-t.x)>=5||Math.abs(a.y-t.y)>=5)&&(e.preventDefault(),r({type:"DRAGGING",actions:s.actions.fluidLift(a)}))}},{eventName:"mouseup",fn:r=>{let i=n();if("DRAGGING"!==i.type)return void e();r.preventDefault(),i.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"mousedown",fn:t=>{"DRAGGING"===n().type&&t.preventDefault(),e()}},{eventName:"keydown",fn:t=>{if("PENDING"===n().type)return void e();if(27===t.keyCode){t.preventDefault(),e();return}iR(t)}},{eventName:"resize",fn:e},{eventName:"scroll",options:{passive:!0,capture:!1},fn:()=>{"PENDING"===n().type&&e()}},{eventName:"webkitmouseforcedown",fn:t=>{let r=n();if("IDLE"===r.type&&eP(),r.actions.shouldRespectForcePress())return void e();t.preventDefault()}},{eventName:iP,fn:e}]}({cancel:s,completed:a,getPhase:()=>t.current,setPhase:e=>{t.current=e}}),{capture:!0,passive:!1})},[s,a]),d=eF(function(e,n){"IDLE"!==t.current.type&&eP(),t.current={type:"PENDING",point:n,actions:e},u()},[u]);r7(function(){return l(),function(){n.current()}},[l])},function(e){let t=(0,r.useRef)(iA),n=eL(()=>({eventName:"keydown",fn:function(n){if(n.defaultPrevented||32!==n.keyCode)return;let r=e.findClosestDraggableId(n);if(!r)return;let o=e.tryGetLock(r,s,{sourceEvent:n});if(!o)return;n.preventDefault();let l=!0,a=o.snapLift();function s(){l||eP(),l=!1,t.current(),i()}t.current(),t.current=eC(window,function(e,t){function n(){t(),e.cancel()}return[{eventName:"keydown",fn:r=>{if(27===r.keyCode){r.preventDefault(),n();return}if(32===r.keyCode){r.preventDefault(),t(),e.drop();return}if(40===r.keyCode){r.preventDefault(),e.moveDown();return}if(38===r.keyCode){r.preventDefault(),e.moveUp();return}if(39===r.keyCode){r.preventDefault(),e.moveRight();return}if(37===r.keyCode){r.preventDefault(),e.moveLeft();return}if(iD[r.keyCode])return void r.preventDefault();iR(r)}},{eventName:"mousedown",fn:n},{eventName:"mouseup",fn:n},{eventName:"click",fn:n},{eventName:"touchstart",fn:n},{eventName:"resize",fn:n},{eventName:"wheel",fn:n,options:{passive:!0}},{eventName:iP,fn:n}]}(a,s),{capture:!0,passive:!1})}}),[e]),i=eF(function(){t.current=eC(window,[n],{passive:!1,capture:!0})},[n]);r7(function(){return i(),function(){t.current()}},[i])},function(e){let t=(0,r.useRef)(iI),n=(0,r.useRef)(eS),i=eF(function(){return t.current},[]),o=eF(function(e){t.current=e},[]),l=eL(()=>({eventName:"touchstart",fn:function(t){if(t.defaultPrevented)return;let r=e.findClosestDraggableId(t);if(!r)return;let i=e.tryGetLock(r,s,{sourceEvent:t});if(!i)return;let{clientX:o,clientY:l}=t.touches[0];n.current(),p(i,{x:o,y:l})}}),[e]),a=eF(function(){n.current=eC(window,[l],{capture:!0,passive:!1})},[l]),s=eF(()=>{let e=t.current;"IDLE"!==e.type&&("PENDING"===e.type&&clearTimeout(e.longPressTimerId),o(iI),n.current(),a())},[a,o]),u=eF(()=>{let e=t.current;s(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[s]),d=eF(function(){let e={capture:!0,passive:!1},t={cancel:u,completed:s,getPhase:i},r=eC(window,function({cancel:e,completed:t,getPhase:n}){return[{eventName:"touchmove",options:{capture:!1},fn:t=>{let r=n();if("DRAGGING"!==r.type)return void e();r.hasMoved=!0;let{clientX:i,clientY:o}=t.touches[0];t.preventDefault(),r.actions.move({x:i,y:o})}},{eventName:"touchend",fn:r=>{let i=n();if("DRAGGING"!==i.type)return void e();r.preventDefault(),i.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"touchcancel",fn:t=>{if("DRAGGING"!==n().type)return void e();t.preventDefault(),e()}},{eventName:"touchforcechange",fn:t=>{let r=n();"IDLE"===r.type&&eP();let i=t.touches[0];if(!i||!(i.force>=.15))return;let o=r.actions.shouldRespectForcePress();if("PENDING"===r.type){o&&e();return}if(o)return r.hasMoved?void t.preventDefault():void e();t.preventDefault()}},{eventName:iP,fn:e}]}(t),e),o=eC(window,function({cancel:e,getPhase:t}){return[{eventName:"orientationchange",fn:e},{eventName:"resize",fn:e},{eventName:"contextmenu",fn:e=>{e.preventDefault()}},{eventName:"keydown",fn:n=>{if("DRAGGING"!==t().type)return void e();27===n.keyCode&&n.preventDefault(),e()}},{eventName:iP,fn:e}]}(t),e);n.current=function(){r(),o()}},[u,i,s]),c=eF(function(){let e=i();"PENDING"!==e.type&&eP(),o({type:"DRAGGING",actions:e.actions.fluidLift(e.point),hasMoved:!1})},[i,o]),p=eF(function(e,t){"IDLE"!==i().type&&eP(),o({type:"PENDING",point:t,actions:e,longPressTimerId:setTimeout(c,120)}),d()},[d,i,o,c]);r7(function(){return a(),function(){n.current();let e=i();"PENDING"===e.type&&(clearTimeout(e.longPressTimerId),o(iI))}},[i,a,o]),r7(function(){return eC(window,[{eventName:"touchmove",fn:()=>{},options:{capture:!1,passive:!1}}])},[])}],iV=e=>({onBeforeCapture:t=>{(0,o.flushSync)(()=>{e.onBeforeCapture&&e.onBeforeCapture(t)})},onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}),ij=e=>({...rI,...e.autoScrollerOptions,durationDampening:{...rI.durationDampening,...e.autoScrollerOptions}});function iB(e){return e.current||eP(),e.current}function iG(e){let{contextId:t,setCallbacks:n,sensors:o,nonce:l,dragHandleUsageInstructions:a}=e,s=(0,r.useRef)(null);iw(()=>{iv(ip.react,i().version),iy(document)},[]);let u=ix(e),d=eF(()=>iV(u.current),[u]),c=eF(()=>ij(u.current),[u]),h=function(e){let t=eL(()=>is(e),[e]),n=(0,r.useRef)(null);return(0,r.useEffect)(function(){let e=document.createElement("div");return n.current=e,e.id=t,e.setAttribute("aria-live","assertive"),e.setAttribute("aria-atomic","true"),eg(e.style,ia),il().appendChild(e),function(){setTimeout(function(){let t=il();t.contains(e)&&t.removeChild(e),e===n.current&&(n.current=null)})}},[t]),eF(e=>{let t=n.current;if(t){t.textContent=e;return}},[])}(t),f=function({contextId:e,text:t}){let n=id("hidden-text",{separator:"-"}),i=eL(()=>(function({contextId:e,uniqueId:t}){return`rfd-hidden-text-${e}-${t}`})({contextId:e,uniqueId:n}),[n,e]);return(0,r.useEffect)(function(){let e=document.createElement("div");return e.id=i,e.textContent=t,e.style.display="none",il().appendChild(e),function(){let t=il();t.contains(e)&&t.removeChild(e)}},[i,t]),i}({contextId:t,text:a}),g=function(e,t){let n=eL(()=>r3(e),[e]),i=(0,r.useRef)(null),o=(0,r.useRef)(null),l=eF(eQ(e=>{let t=o.current;t||eP(),t.textContent=e}),[]),a=eF(e=>{let t=i.current;t||eP(),t.textContent=e},[]);r7(()=>{(i.current||o.current)&&eP();let r=r9(t),s=r9(t);return i.current=r,o.current=s,r.setAttribute(`${rQ}-always`,e),s.setAttribute(`${rQ}-dynamic`,e),r8().appendChild(r),r8().appendChild(s),a(n.always),l(n.resting),()=>{let e=e=>{let t=e.current;t||eP(),r8().removeChild(t),e.current=null};e(i),e(o)}},[t,a,l,n.always,n.resting,e]);let s=eF(()=>l(n.dragging),[l,n.dragging]),u=eF(e=>{if("DROP"===e)return void l(n.dropAnimating);l(n.userCancel)},[l,n.dropAnimating,n.userCancel]),d=eF(()=>{o.current&&l(n.resting)},[l,n.resting]);return eL(()=>({dragging:s,dropping:u,resting:d}),[s,u,d])}(t,l),m=eF(e=>{iB(s).dispatch(e)},[]),v=eL(()=>p({publishWhileDragging:nE,updateDroppableScroll:nD,updateDroppableIsEnabled:nI,updateDroppableIsCombineEnabled:nM,collectionStarting:nA},m),[m]),y=function(){let e=eL(ii,[]);return(0,r.useEffect)(()=>function(){e.clean()},[e]),e}(),b=eL(()=>rC(y,v),[y,v]),w=eL(()=>rZ({scrollWindow:rP,scrollDroppable:b.scrollDroppable,getAutoScrollerOptions:c,...p({move:nT},m)}),[b.scrollDroppable,m,c]),x=function(e){let t=(0,r.useRef)({}),n=(0,r.useRef)(null),i=(0,r.useRef)(null),o=(0,r.useRef)(!1),l=eF(function(e,n){let r={id:e,focus:n};return t.current[e]=r,function(){let n=t.current;n[e]!==r&&delete n[e]}},[]),a=eF(function(t){let n=ir(e,t);n&&n!==document.activeElement&&n.focus()},[e]),s=eF(function(e,t){n.current===e&&(n.current=t)},[]),u=eF(function(){!i.current&&o.current&&(i.current=requestAnimationFrame(()=>{i.current=null;let e=n.current;e&&a(e)}))},[a]),d=eF(function(e){n.current=null;let t=document.activeElement;t&&t.getAttribute(rJ.draggableId)===e&&(n.current=e)},[]);return r7(()=>(o.current=!0,function(){o.current=!1;let e=i.current;e&&cancelAnimationFrame(e)}),[]),eL(()=>({register:l,tryRecordFocus:d,tryRestoreFocusRecorded:u,tryShiftRecord:s}),[l,d,u,s])}(t),S=eL(()=>rg({announce:h,autoScroller:w,dimensionMarshal:b,focusMarshal:x,getResponders:d,styleMarshal:g}),[h,w,b,x,d,g]);s.current=S;let C=eF(()=>{let e=iB(s);"IDLE"!==e.getState().phase&&e.dispatch(nV())},[]),R=eF(()=>{let e=iB(s).getState();return"DROP_ANIMATING"===e.phase||"IDLE"!==e.phase&&e.isDragging},[]);n(eL(()=>({isDragging:R,tryAbort:C}),[R,C]));let P=eF(e=>rR(iB(s).getState(),e),[]),E=eF(()=>t5(iB(s).getState()),[]),A=eL(()=>({marshal:b,focus:x,contextId:t,canLift:P,isMovementAllowed:E,dragHandleUsageInstructionsId:f,registry:y}),[t,b,f,x,P,E,y]);return!function({contextId:e,store:t,registry:n,customSensors:i,enableDefaultSensors:o}){let l=[...o?iN:[],...i||[]],a=(0,r.useState)(()=>(function(){let e=null;function t(){e||eP(),e=null}return{isClaimed:function(){return!!e},isActive:function(t){return t===e},claim:function(t){e&&eP();let n={abandon:t};return e=n,n},release:t,tryAbandon:function(){e&&(e.abandon(),t())}}})())[0],s=eF(function(e,t){iS(e)&&!iS(t)&&a.tryAbandon()},[a]);r7(function(){let e=t.getState();return t.subscribe(()=>{let n=t.getState();s(e,n),e=n})},[a,t,s]),r7(()=>a.tryAbandon,[a.tryAbandon]);let u=eF(e=>iO({lockAPI:a,registry:n,store:t,draggableId:e}),[a,n,t]),d=eF((r,i,o)=>(function({lockAPI:e,contextId:t,store:n,registry:r,draggableId:i,forceSensorStop:o,sourceEvent:l}){if(!iO({lockAPI:e,store:n,registry:r,draggableId:i}))return null;let a=r.draggable.getById(i),s=function(e,t){let n=r6(document,`[${r0.contextId}="${e}"]`).find(e=>e.getAttribute(r0.id)===t);return n&&it(n)?n:null}(t,a.descriptor.id);if(!s||l&&!a.options.canDragInteractiveElements&&function(e,t){let n=t.target;return!!it(n)&&function e(t,n){if(null==n)return!1;if(iM.includes(n.tagName.toLowerCase()))return!0;let r=n.getAttribute("contenteditable");return"true"===r||""===r||n!==t&&e(t,n.parentElement)}(e,n)}(s,l))return null;let u=e.claim(o||eS),d="PRE_DRAG";function c(){return a.options.shouldRespectForcePress}function p(){return e.isActive(u)}let h=(function(e,t){iF({expected:e,phase:d,isLockActive:p,shouldWarn:!0})&&n.dispatch(t())}).bind(null,"DRAGGING");function f(t){function r(){e.release(),d="COMPLETED"}function i(e,o={shouldBlockNextClick:!1}){t.cleanup(),o.shouldBlockNextClick&&setTimeout(eC(window,[{eventName:"click",fn:iL,options:{once:!0,passive:!1,capture:!0}}])),r(),n.dispatch(nG({reason:e}))}return"PRE_DRAG"!==d&&(r(),eP()),n.dispatch(nR(t.liftActionArgs)),d="DRAGGING",{isActive:()=>iF({expected:"DRAGGING",phase:d,isLockActive:p,shouldWarn:!1}),shouldRespectForcePress:c,drop:e=>i("DROP",e),cancel:e=>i("CANCEL",e),...t.actions}}return{isActive:()=>iF({expected:"PRE_DRAG",phase:d,isLockActive:p,shouldWarn:!1}),shouldRespectForcePress:c,fluidLift:function(e){let t=ef(e=>{h(()=>nT({client:e}))});return{...f({liftActionArgs:{id:i,clientSelection:e,movementMode:"FLUID"},cleanup:()=>t.cancel(),actions:{move:t}}),move:t}},snapLift:function(){return f({liftActionArgs:{id:i,clientSelection:iT(s),movementMode:"SNAP"},cleanup:eS,actions:{moveUp:()=>h(nL),moveRight:()=>h(nO),moveDown:()=>h(nF),moveLeft:()=>h(nN)}})},abort:function(){iF({expected:"PRE_DRAG",phase:d,isLockActive:p,shouldWarn:!0})&&e.release()}}})({lockAPI:a,registry:n,contextId:e,store:t,draggableId:r,forceSensorStop:i||null,sourceEvent:o&&o.sourceEvent?o.sourceEvent:null}),[e,a,n,t]),c=eF(t=>(function(e,t){let n=function(e,t){let n=t.target;if(!(n instanceof ie(n).Element))return null;let r=`[${rJ.contextId}="${e}"]`,i=n.closest?n.closest(r):function e(t,n){return null==t?null:t[ik](n)?t:e(t.parentElement,n)}(n,r);return i&&it(i)?i:null}(e,t);return n?n.getAttribute(rJ.draggableId):null})(e,t),[e]),p=eF(e=>{let t=n.draggable.findById(e);return t?t.options:null},[n.draggable]),h=eF(function(){a.isClaimed()&&(a.tryAbandon(),"IDLE"!==t.getState().phase&&t.dispatch(nV()))},[a,t]),f=eF(()=>a.isClaimed(),[a]),g=eL(()=>({canGetLock:u,tryGetLock:d,findClosestDraggableId:c,findOptionsForDraggable:p,tryReleaseLock:h,isLockClaimed:f}),[u,d,c,p,h,f]);for(let e=0;e<l.length;e++)l[e](g)}({contextId:t,store:S,registry:y,customSensors:o||null,enableDefaultSensors:!1!==e.enableDefaultSensors}),(0,r.useEffect)(()=>C,[C]),i().createElement(ic.Provider,{value:A},i().createElement(er,{context:io,store:S},e.children))}function i_(e){let t=i().useId(),n=e.dragHandleUsageInstructions||eT.dragHandleUsageInstructions;return i().createElement(eE,null,r=>i().createElement(iG,{nonce:e.nonce,contextId:t,setCallbacks:r,dragHandleUsageInstructions:n,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd,autoScrollerOptions:e.autoScrollerOptions},e.children))}let iz={dragging:5e3,dropAnimating:4500},iH=(e,t)=>t?nX.drop(t.duration):e?nX.snap:nX.fluid,i$=(e,t)=>{if(e)return t?nW.opacity.drop:nW.opacity.combining},iU=e=>null!=e.forceShouldAnimate?e.forceShouldAnimate:"SNAP"===e.mode;var iW=i().createContext(null);function iq(e){e&&it(e)||eP()}function iY(e){let t=(0,r.useContext)(e);return t||eP(),t}function iX(e){e.preventDefault()}var iK=(e,t)=>e===t,iZ=e=>{let{combine:t,destination:n}=e;return n?n.droppableId:t?t.droppableId:null};let iQ=e=>e.combine?e.combine.draggableId:null,iJ=e=>e.at&&"COMBINE"===e.at.type?e.at.combine.draggableId:null;function i0(e=null){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}let i1={mapped:{type:"SECONDARY",offset:eO,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:i0(null)}},i2=en(()=>{let e=function(){let e=eQ((e,t)=>({x:e,y:t})),t=eQ((e,t,n=null,r=null,i=null)=>({isDragging:!0,isClone:t,isDropAnimating:!!i,dropAnimation:i,mode:e,draggingOver:n,combineWith:r,combineTargetFor:null})),n=eQ((e,n,r,i,o=null,l=null,a=null)=>({mapped:{type:"DRAGGING",dropping:null,draggingOver:o,combineWith:l,mode:n,offset:e,dimension:r,forceShouldAnimate:a,snapshot:t(n,i,o,l,null)}}));return(r,i)=>{if(iS(r)){if(r.critical.draggable.id!==i.draggableId)return null;let t=r.current.client.offset,o=r.dimensions.draggables[i.draggableId],l=t0(r.impact),a=iJ(r.impact),s=r.forceShouldAnimate;return n(e(t.x,t.y),r.movementMode,o,i.isClone,l,a,s)}if("DROP_ANIMATING"===r.phase){let e=r.completed;if(e.result.draggableId!==i.draggableId)return null;let n=i.isClone,o=r.dimensions.draggables[i.draggableId],l=e.result,a=l.mode,s=iZ(l),u=iQ(l),d={duration:r.dropDuration,curve:nU.drop,moveTo:r.newHomeClientOffset,opacity:u?nW.opacity.drop:null,scale:u?nW.scale.drop:null};return{mapped:{type:"DRAGGING",offset:r.newHomeClientOffset,dimension:o,dropping:d,draggingOver:s,combineWith:u,mode:a,forceShouldAnimate:null,snapshot:t(a,n,s,u,d)}}}return null}}(),t=function(){let e=eQ((e,t)=>({x:e,y:t})),t=eQ(i0),n=eQ((e,n=null,r)=>({mapped:{type:"SECONDARY",offset:e,combineTargetFor:n,shouldAnimateDisplacement:r,snapshot:t(n)}})),r=e=>e?n(eO,e,!0):null,i=(t,i,o,l)=>{let a=o.displaced.visible[t],s=!!(l.inVirtualList&&l.effected[t]),u=e3(o),d=u&&u.draggableId===t?i:null;if(!a){if(!s)return r(d);if(o.displaced.invisible[t])return null;let i=eB(l.displacedBy.point);return n(e(i.x,i.y),d,!0)}if(s)return r(d);let c=o.displacedBy.point;return n(e(c.x,c.y),d,a.shouldAnimate)};return(e,t)=>{if(iS(e))return e.critical.draggable.id===t.draggableId?null:i(t.draggableId,e.critical.draggable.id,e.impact,e.afterCritical);if("DROP_ANIMATING"===e.phase){let n=e.completed;return n.result.draggableId===t.draggableId?null:i(t.draggableId,n.result.draggableId,n.impact,n.afterCritical)}return null}}();return(n,r)=>e(n,r)||t(n,r)||i1},{dropAnimationFinished:nz},null,{context:io,areStatePropsEqual:iK})(e=>{let t=(0,r.useRef)(null),n=eF((e=null)=>{t.current=e},[]),l=eF(()=>t.current,[]),{contextId:a,dragHandleUsageInstructionsId:s,registry:u}=iY(ic),{type:d,droppableId:c}=iY(iW),p=eL(()=>({id:e.draggableId,index:e.index,type:d,droppableId:c}),[e.draggableId,e.index,d,c]),{children:h,draggableId:f,isEnabled:g,shouldRespectForcePress:m,canDragInteractiveElements:v,isClone:y,mapped:b,dropAnimationFinished:w}=e;!function(e,t,n){iw(()=>{let r=e.draggableId;r||eP(!1),"string"!=typeof r&&eP(!1),Number.isInteger(e.index)||eP(!1),"DRAGGING"!==e.mapped.type&&(iq(n()),e.isEnabled&&(ir(t,r)||eP(!1)))})}(e,a,l),y||function(e){let t=id("draggable"),{descriptor:n,registry:i,getDraggableRef:o,canDragInteractiveElements:l,shouldRespectForcePress:a,isEnabled:s}=e,u=eL(()=>({canDragInteractiveElements:l,shouldRespectForcePress:a,isEnabled:s}),[l,s,a]),d=eF(e=>{let t=o();return t||eP(),function(e,t,n=eO){let r=window.getComputedStyle(t),i=ep(t.getBoundingClientRect(),r),o=ec(i,n),l={client:i,tagName:t.tagName.toLowerCase(),display:r.display};return{descriptor:e,placeholder:l,displaceBy:{x:i.marginBox.width,y:i.marginBox.height},client:i,page:o}}(n,t,e)},[n,o]),c=eL(()=>({uniqueId:t,descriptor:n,options:u,getDimension:d}),[n,d,u,t]),p=(0,r.useRef)(c),h=(0,r.useRef)(!0);r7(()=>(i.draggable.register(p.current),()=>i.draggable.unregister(p.current)),[i.draggable]),r7(()=>{if(h.current){h.current=!1;return}let e=p.current;p.current=c,i.draggable.update(c,e)},[c,i.draggable])}(eL(()=>({descriptor:p,registry:u,getDraggableRef:l,canDragInteractiveElements:v,shouldRespectForcePress:m,isEnabled:g}),[p,u,l,v,m,g]));let x=eL(()=>g?{tabIndex:0,role:"button","aria-describedby":s,"data-rfd-drag-handle-draggable-id":f,"data-rfd-drag-handle-context-id":a,draggable:!1,onDragStart:iX}:null,[a,s,f,g]),S=eF(e=>{"DRAGGING"===b.type&&b.dropping&&"transform"===e.propertyName&&(0,o.flushSync)(w)},[w,b]),C=eL(()=>{let e=function(e){return"DRAGGING"===e.type?function(e){let t=e.dimension.client,{offset:n,combineWith:r,dropping:i}=e,o=!!r,l=iU(e),a=!!i,s=a?nZ.drop(n,o):nZ.moveTo(n);return{position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:iH(l,i),transform:s,opacity:i$(o,a),zIndex:a?iz.dropAnimating:iz.dragging,pointerEvents:"none"}}(e):{transform:nZ.moveTo(e.offset),transition:e.shouldAnimateDisplacement?void 0:"none"}}(b);return{innerRef:n,draggableProps:{"data-rfd-draggable-context-id":a,"data-rfd-draggable-id":f,style:e,onTransitionEnd:"DRAGGING"===b.type&&b.dropping?S:void 0},dragHandleProps:x}},[a,x,f,b,S,n]),R=eL(()=>({draggableId:p.id,type:p.type,source:{index:p.index,droppableId:p.droppableId}}),[p.droppableId,p.id,p.index,p.type]);return i().createElement(i().Fragment,null,h(C,b.snapshot,R))});function i5(e){return iY(iW).isUsingCloneFor!==e.draggableId||e.isClone?i().createElement(i2,e):null}function i4(e){let t="boolean"!=typeof e.isDragDisabled||!e.isDragDisabled,n=!!e.disableInteractiveElementBlocking,r=!!e.shouldRespectForcePress;return i().createElement(i5,eg({},e,{isClone:!1,isEnabled:t,canDragInteractiveElements:n,shouldRespectForcePress:r}))}let i3=e=>t=>e===t,i7=i3("scroll"),i8=i3("auto");i3("visible");let i9=(e,t)=>t(e.overflowX)||t(e.overflowY),i6=e=>{let t=window.getComputedStyle(e),n={overflowX:t.overflowX,overflowY:t.overflowY};return i9(n,i7)||i9(n,i8)},oe=()=>!1,ot=e=>null==e?null:e===document.body?oe()?e:null:e===document.documentElement?null:i6(e)?e:ot(e.parentElement);var on=e=>({x:e.scrollLeft,y:e.scrollTop});let or=e=>!!e&&("fixed"===window.getComputedStyle(e).position||or(e.parentElement));var oi=e=>({closestScrollable:ot(e),isFixedOnPage:or(e)}),oo=({descriptor:e,isEnabled:t,isCombineEnabled:n,isFixedOnPage:r,direction:i,client:o,page:l,closest:a})=>{let s=(()=>{if(!a)return null;let{scrollSize:e,client:t}=a,n=rv({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,height:t.paddingBox.height,width:t.paddingBox.width});return{pageMarginBox:a.page.marginBox,frameClient:t,scrollSize:e,shouldClipSubject:a.shouldClipSubject,scroll:{initial:a.scroll,current:a.scroll,max:n,diff:{value:eO,displacement:eO}}}})(),u="vertical"===i?to:tl,d=eK({page:l,withPlaceholder:null,axis:u,frame:s});return{descriptor:e,isCombineEnabled:n,isFixedOnPage:r,axis:u,isEnabled:t,client:o,page:l,frame:s,subject:d}};let ol=(e,t)=>{let n=eh(e);if(!t||e!==t)return n;let r=n.paddingBox.top-t.scrollTop,i=n.paddingBox.left-t.scrollLeft,o=r+t.scrollHeight;return es({borderBox:eo({top:r,right:i+t.scrollWidth,bottom:o,left:i},n.border),margin:n.margin,border:n.border,padding:n.padding})};var oa=({ref:e,descriptor:t,env:n,windowScroll:r,direction:i,isDropDisabled:o,isCombineEnabled:l,shouldClipSubject:a})=>{let s=n.closestScrollable,u=ol(e,s),d=ec(u,r),c=(()=>{if(!s)return null;let e=eh(s),t={scrollHeight:s.scrollHeight,scrollWidth:s.scrollWidth};return{client:e,page:ec(e,r),scroll:on(s),scrollSize:t,shouldClipSubject:a}})();return oo({descriptor:t,isEnabled:!o,isCombineEnabled:l,isFixedOnPage:n.isFixedOnPage,direction:i,client:u,page:d,closest:c})};let os={passive:!1},ou={passive:!0};var od=e=>e.shouldPublishImmediately?os:ou;let oc=e=>e&&e.env.closestScrollable||null;function op(){}let oh={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}},of=({isAnimatingOpenOnMount:e,placeholder:t,animate:n})=>e||"close"===n?oh:{height:t.client.borderBox.height,width:t.client.borderBox.width,margin:t.client.margin},og=({isAnimatingOpenOnMount:e,placeholder:t,animate:n})=>{let r=of({isAnimatingOpenOnMount:e,placeholder:t,animate:n});return{display:t.display,boxSizing:"border-box",width:r.width,height:r.height,marginTop:r.margin.top,marginRight:r.margin.right,marginBottom:r.margin.bottom,marginLeft:r.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==n?nX.placeholder:null}};var om=i().memo(e=>{let t=(0,r.useRef)(null),n=eF(()=>{t.current&&(clearTimeout(t.current),t.current=null)},[]),{animate:o,onTransitionEnd:l,onClose:a,contextId:s}=e,[u,d]=(0,r.useState)("open"===e.animate);(0,r.useEffect)(()=>u?"open"!==o?(n(),d(!1),op):t.current?op:(t.current=setTimeout(()=>{t.current=null,d(!1)}),n):op,[o,u,n]);let c=eF(e=>{"height"===e.propertyName&&(l(),"close"===o&&a())},[o,a,l]),p=og({isAnimatingOpenOnMount:u,animate:e.animate,placeholder:e.placeholder});return i().createElement(e.placeholder.tagName,{style:p,"data-rfd-placeholder-context-id":s,onTransitionEnd:c,ref:e.innerRef})});function ov(e){return"boolean"==typeof e}function oy(e,t){t.forEach(t=>t(e))}let ob=[function({props:e}){e.droppableId||eP(),"string"!=typeof e.droppableId&&eP()},function({props:e}){ov(e.isDropDisabled)||eP(),ov(e.isCombineEnabled)||eP(),ov(e.ignoreContainerClipping)||eP()},function({getDroppableRef:e}){iq(e())}],ow=[function({props:e,getPlaceholderRef:t}){if(!e.placeholder||t())return}],ox=[function({props:e}){e.renderClone||eP()},function({getPlaceholderRef:e}){e()&&eP()}];class oS extends i().PureComponent{constructor(...e){super(...e),this.state={isVisible:!!this.props.on,data:this.props.on,animate:this.props.shouldAnimate&&this.props.on?"open":"none"},this.onClose=()=>{"close"===this.state.animate&&this.setState({isVisible:!1})}}static getDerivedStateFromProps(e,t){return e.shouldAnimate?e.on?{isVisible:!0,data:e.on,animate:"open"}:t.isVisible?{isVisible:!0,data:t.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:!!e.on,data:e.on,animate:"none"}}render(){if(!this.state.isVisible)return null;let e={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(e)}}let oC={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||eP(),document.body}},oR=e=>{let t,n={...e};for(t in oC)void 0===e[t]&&(n={...n,[t]:oC[t]});return n},oP=(e,t)=>e===t.droppable.type,oE=(e,t)=>t.draggables[e.draggable.id],oA=en(()=>{let e={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t={...e,shouldAnimatePlaceholder:!1},n=eQ(e=>({draggableId:e.id,type:e.type,source:{index:e.index,droppableId:e.droppableId}})),r=eQ((r,i,o,l,a,s)=>{let u=a.descriptor.id;if(a.descriptor.droppableId===r){let e=s?{render:s,dragging:n(a.descriptor)}:null;return{placeholder:a.placeholder,shouldAnimatePlaceholder:!1,snapshot:{isDraggingOver:o,draggingOverWith:o?u:null,draggingFromThisWith:u,isUsingPlaceholder:!0},useClone:e}}return i?l?{placeholder:a.placeholder,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:o,draggingOverWith:u,draggingFromThisWith:null,isUsingPlaceholder:!0},useClone:null}:e:t});return(n,i)=>{let o=oR(i),l=o.droppableId,a=o.type,s=!o.isDropDisabled,u=o.renderClone;if(iS(n)){let e=n.critical;if(!oP(a,e))return t;let i=oE(e,n.dimensions),o=t0(n.impact)===l;return r(l,s,o,o,i,u)}if("DROP_ANIMATING"===n.phase){let e=n.completed;if(!oP(a,e.critical))return t;let i=oE(e.critical,n.dimensions);return r(l,s,iZ(e.result)===l,t0(e.impact)===l,i,u)}if("IDLE"===n.phase&&n.completed&&!n.shouldFlush){let r=n.completed;if(!oP(a,r.critical))return t;let i=t0(r.impact)===l,o=!!(r.impact.at&&"COMBINE"===r.impact.at.type),s=r.critical.droppable.id===l;if(i)return o?e:t;if(s)return e}return t}},{updateViewportMaxScroll:e=>({type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e})},(e,t,n)=>({...oR(n),...e,...t}),{context:io,areStatePropsEqual:iK})(e=>{let t=(0,r.useContext)(ic);t||eP();let{contextId:n,isMovementAllowed:o}=t,a=(0,r.useRef)(null),s=(0,r.useRef)(null),{children:u,droppableId:d,type:c,mode:p,direction:h,ignoreContainerClipping:f,isDropDisabled:g,isCombineEnabled:m,snapshot:v,useClone:y,updateViewportMaxScroll:b,getContainerForClone:w}=e,x=eF(()=>a.current,[]),S=eF((e=null)=>{a.current=e},[]),C=eF(()=>s.current,[]),R=eF((e=null)=>{s.current=e},[]);!function(e){iw(()=>{oy(e,ob),"standard"===e.props.mode&&oy(e,ow),"virtual"===e.props.mode&&oy(e,ox)})}({props:e,getDroppableRef:x,getPlaceholderRef:C});let P=eF(()=>{o()&&b({maxScroll:rb()})},[o,b]);!function(e){let t=(0,r.useRef)(null),n=iY(ic),i=id("droppable"),{registry:o,marshal:l}=n,a=ix(e),s=eL(()=>({id:e.droppableId,type:e.type,mode:e.mode}),[e.droppableId,e.mode,e.type]),u=(0,r.useRef)(s),d=eL(()=>eQ((e,n)=>{t.current||eP(),l.updateDroppableScroll(s.id,{x:e,y:n})}),[s.id,l]),c=eF(()=>{let e=t.current;return e&&e.env.closestScrollable?on(e.env.closestScrollable):eO},[]),p=eF(()=>{let e=c();d(e.x,e.y)},[c,d]),h=eL(()=>ef(p),[p]),f=eF(()=>{let e=t.current,n=oc(e);if(e&&n||eP(),e.scrollOptions.shouldPublishImmediately)return void p();h()},[h,p]),g=eF((e,r)=>{t.current&&eP();let i=a.current,o=i.getDroppableRef();o||eP();let l=oi(o),u={ref:o,descriptor:s,env:l,scrollOptions:r};t.current=u;let d=oa({ref:o,descriptor:s,env:l,windowScroll:e,direction:i.direction,isDropDisabled:i.isDropDisabled,isCombineEnabled:i.isCombineEnabled,shouldClipSubject:!i.ignoreContainerClipping}),c=l.closestScrollable;return c&&(c.setAttribute(r2.contextId,n.contextId),c.addEventListener("scroll",f,od(u.scrollOptions))),d},[n.contextId,s,f,a]),m=eF(()=>{let e=t.current,n=oc(e);return e&&n||eP(),on(n)},[]),v=eF(()=>{let e=t.current;e||eP();let n=oc(e);t.current=null,n&&(h.cancel(),n.removeAttribute(r2.contextId),n.removeEventListener("scroll",f,od(e.scrollOptions)))},[f,h]),y=eF(e=>{let n=t.current;n||eP();let r=oc(n);r||eP(),r.scrollTop+=e.y,r.scrollLeft+=e.x},[]),b=eL(()=>({getDimensionAndWatchScroll:g,getScrollWhileDragging:m,dragStopped:v,scroll:y}),[v,g,m,y]),w=eL(()=>({uniqueId:i,descriptor:s,callbacks:b}),[b,s,i]);r7(()=>(u.current=w.descriptor,o.droppable.register(w),()=>{t.current&&v(),o.droppable.unregister(w)}),[b,s,v,w,l,o.droppable]),r7(()=>{t.current&&l.updateDroppableIsEnabled(u.current.id,!e.isDropDisabled)},[e.isDropDisabled,l]),r7(()=>{t.current&&l.updateDroppableIsCombineEnabled(u.current.id,e.isCombineEnabled)},[e.isCombineEnabled,l])}({droppableId:d,type:c,mode:p,direction:h,isDropDisabled:g,isCombineEnabled:m,ignoreContainerClipping:f,getDroppableRef:x});let E=eL(()=>i().createElement(oS,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},({onClose:e,data:t,animate:r})=>i().createElement(om,{placeholder:t,onClose:e,innerRef:R,animate:r,contextId:n,onTransitionEnd:P})),[n,P,e.placeholder,e.shouldAnimatePlaceholder,R]),A=eL(()=>({innerRef:S,placeholder:E,droppableProps:{"data-rfd-droppable-id":d,"data-rfd-droppable-context-id":n}}),[n,d,E,S]),D=y?y.dragging.draggableId:null,I=eL(()=>({droppableId:d,type:c,isUsingCloneFor:D}),[d,D,c]);return i().createElement(iW.Provider,{value:I},u(A,v),function(){if(!y)return null;let{dragging:e,render:t}=y,n=i().createElement(i5,{draggableId:e.draggableId,index:e.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},(n,r)=>t(n,r,e));return l().createPortal(n,w())}())})},12901:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("BrainCircuit",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M9 13a4.5 4.5 0 0 0 3-4",key:"10igwf"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M12 13h4",key:"1ku699"}],["path",{d:"M12 18h6a2 2 0 0 1 2 2v1",key:"105ag5"}],["path",{d:"M12 8h8",key:"1lhi5i"}],["path",{d:"M16 8V5a2 2 0 0 1 2-2",key:"u6izg6"}],["circle",{cx:"16",cy:"13",r:".5",key:"ry7gng"}],["circle",{cx:"18",cy:"3",r:".5",key:"1aiba7"}],["circle",{cx:"20",cy:"21",r:".5",key:"yhc1fs"}],["circle",{cx:"20",cy:"8",r:".5",key:"1e43v0"}]])},13044:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]])},14294:(e,t,n)=>{n.d(t,{LM:()=>Y,OK:()=>X,VM:()=>C,bL:()=>q,lr:()=>F});var r=n(63185),i=n(34245),o=n(80799),l=n(63699),a=n(85729),s=n(57647),u=n(272),d=n(70505),c=n(20830),p=n(19141),h=n(84464),f="ScrollArea",[g,m]=(0,l.A)(f),[v,y]=g(f),b=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,type:o="hover",dir:l,scrollHideDelay:s=600,...d}=e,[c,p]=r.useState(null),[f,g]=r.useState(null),[m,y]=r.useState(null),[b,w]=r.useState(null),[x,S]=r.useState(null),[C,R]=r.useState(0),[P,E]=r.useState(0),[A,D]=r.useState(!1),[I,M]=r.useState(!1),T=(0,a.s)(t,e=>p(e)),k=(0,u.jH)(l);return(0,h.jsx)(v,{scope:n,type:o,dir:k,scrollHideDelay:s,scrollArea:c,viewport:f,onViewportChange:g,content:m,onContentChange:y,scrollbarX:b,onScrollbarXChange:w,scrollbarXEnabled:A,onScrollbarXEnabledChange:D,scrollbarY:x,onScrollbarYChange:S,scrollbarYEnabled:I,onScrollbarYEnabledChange:M,onCornerWidthChange:R,onCornerHeightChange:E,children:(0,h.jsx)(i.sG.div,{dir:k,...d,ref:T,style:{position:"relative","--radix-scroll-area-corner-width":C+"px","--radix-scroll-area-corner-height":P+"px",...e.style}})})});b.displayName=f;var w="ScrollAreaViewport",x=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,children:o,nonce:l,...s}=e,u=y(w,n),d=r.useRef(null),c=(0,a.s)(t,d,u.onViewportChange);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,h.jsx)(i.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:c,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,h.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});x.displayName=w;var S="ScrollAreaScrollbar",C=r.forwardRef((e,t)=>{let{forceMount:n,...i}=e,o=y(S,e.__scopeScrollArea),{onScrollbarXEnabledChange:l,onScrollbarYEnabledChange:a}=o,s="horizontal"===e.orientation;return r.useEffect(()=>(s?l(!0):a(!0),()=>{s?l(!1):a(!1)}),[s,l,a]),"hover"===o.type?(0,h.jsx)(R,{...i,ref:t,forceMount:n}):"scroll"===o.type?(0,h.jsx)(P,{...i,ref:t,forceMount:n}):"auto"===o.type?(0,h.jsx)(E,{...i,ref:t,forceMount:n}):"always"===o.type?(0,h.jsx)(A,{...i,ref:t}):null});C.displayName=S;var R=r.forwardRef((e,t)=>{let{forceMount:n,...i}=e,l=y(S,e.__scopeScrollArea),[a,s]=r.useState(!1);return r.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let n=()=>{window.clearTimeout(t),s(!0)},r=()=>{t=window.setTimeout(()=>s(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",n),e.addEventListener("pointerleave",r),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",n),e.removeEventListener("pointerleave",r)}}},[l.scrollArea,l.scrollHideDelay]),(0,h.jsx)(o.C,{present:n||a,children:(0,h.jsx)(E,{"data-state":a?"visible":"hidden",...i,ref:t})})}),P=r.forwardRef((e,t)=>{var n,i;let{forceMount:l,...a}=e,s=y(S,e.__scopeScrollArea),u="horizontal"===e.orientation,d=U(()=>f("SCROLL_END"),100),[c,f]=(n="hidden",i={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},r.useReducer((e,t)=>i[e][t]??e,n));return r.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>f("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,s.scrollHideDelay,f]),r.useEffect(()=>{let e=s.viewport,t=u?"scrollLeft":"scrollTop";if(e){let n=e[t],r=()=>{let r=e[t];n!==r&&(f("SCROLL"),d()),n=r};return e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[s.viewport,u,f,d]),(0,h.jsx)(o.C,{present:l||"hidden"!==c,children:(0,h.jsx)(A,{"data-state":"hidden"===c?"hidden":"visible",...a,ref:t,onPointerEnter:(0,p.m)(e.onPointerEnter,()=>f("POINTER_ENTER")),onPointerLeave:(0,p.m)(e.onPointerLeave,()=>f("POINTER_LEAVE"))})})}),E=r.forwardRef((e,t)=>{let n=y(S,e.__scopeScrollArea),{forceMount:i,...l}=e,[a,s]=r.useState(!1),u="horizontal"===e.orientation,d=U(()=>{if(n.viewport){let e=n.viewport.offsetWidth<n.viewport.scrollWidth,t=n.viewport.offsetHeight<n.viewport.scrollHeight;s(u?e:t)}},10);return W(n.viewport,d),W(n.content,d),(0,h.jsx)(o.C,{present:i||a,children:(0,h.jsx)(A,{"data-state":a?"visible":"hidden",...l,ref:t})})}),A=r.forwardRef((e,t)=>{let{orientation:n="vertical",...i}=e,o=y(S,e.__scopeScrollArea),l=r.useRef(null),a=r.useRef(0),[s,u]=r.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=G(s.viewport,s.content),c={...i,sizes:s,onSizesChange:u,hasThumb:!!(d>0&&d<1),onThumbChange:e=>l.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function p(e,t){return function(e,t,n,r="ltr"){let i=_(n),o=t||i/2,l=n.scrollbar.paddingStart+o,a=n.scrollbar.size-n.scrollbar.paddingEnd-(i-o),s=n.content-n.viewport;return H([l,a],"ltr"===r?[0,s]:[-1*s,0])(e)}(e,a.current,s,t)}return"horizontal"===n?(0,h.jsx)(D,{...c,ref:t,onThumbPositionChange:()=>{if(o.viewport&&l.current){let e=z(o.viewport.scrollLeft,s,o.dir);l.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{o.viewport&&(o.viewport.scrollLeft=e)},onDragScroll:e=>{o.viewport&&(o.viewport.scrollLeft=p(e,o.dir))}}):"vertical"===n?(0,h.jsx)(I,{...c,ref:t,onThumbPositionChange:()=>{if(o.viewport&&l.current){let e=z(o.viewport.scrollTop,s);l.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{o.viewport&&(o.viewport.scrollTop=e)},onDragScroll:e=>{o.viewport&&(o.viewport.scrollTop=p(e))}}):null}),D=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:i,...o}=e,l=y(S,e.__scopeScrollArea),[s,u]=r.useState(),d=r.useRef(null),c=(0,a.s)(t,d,l.onScrollbarXChange);return r.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,h.jsx)(k,{"data-orientation":"horizontal",...o,ref:c,sizes:n,style:{bottom:0,left:"rtl"===l.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===l.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":_(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,n)=>{if(l.viewport){let r=l.viewport.scrollLeft+t.deltaX;e.onWheelScroll(r),function(e,t){return e>0&&e<t}(r,n)&&t.preventDefault()}},onResize:()=>{d.current&&l.viewport&&s&&i({content:l.viewport.scrollWidth,viewport:l.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:B(s.paddingLeft),paddingEnd:B(s.paddingRight)}})}})}),I=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:i,...o}=e,l=y(S,e.__scopeScrollArea),[s,u]=r.useState(),d=r.useRef(null),c=(0,a.s)(t,d,l.onScrollbarYChange);return r.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,h.jsx)(k,{"data-orientation":"vertical",...o,ref:c,sizes:n,style:{top:0,right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":_(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,n)=>{if(l.viewport){let r=l.viewport.scrollTop+t.deltaY;e.onWheelScroll(r),function(e,t){return e>0&&e<t}(r,n)&&t.preventDefault()}},onResize:()=>{d.current&&l.viewport&&s&&i({content:l.viewport.scrollHeight,viewport:l.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:B(s.paddingTop),paddingEnd:B(s.paddingBottom)}})}})}),[M,T]=g(S),k=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,sizes:o,hasThumb:l,onThumbChange:u,onThumbPointerUp:d,onThumbPointerDown:c,onThumbPositionChange:f,onDragScroll:g,onWheelScroll:m,onResize:v,...b}=e,w=y(S,n),[x,C]=r.useState(null),R=(0,a.s)(t,e=>C(e)),P=r.useRef(null),E=r.useRef(""),A=w.viewport,D=o.content-o.viewport,I=(0,s.c)(m),T=(0,s.c)(f),k=U(v,10);function L(e){P.current&&g({x:e.clientX-P.current.left,y:e.clientY-P.current.top})}return r.useEffect(()=>{let e=e=>{let t=e.target;x?.contains(t)&&I(e,D)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[A,x,D,I]),r.useEffect(T,[o,T]),W(x,k),W(w.content,k),(0,h.jsx)(M,{scope:n,scrollbar:x,hasThumb:l,onThumbChange:(0,s.c)(u),onThumbPointerUp:(0,s.c)(d),onThumbPositionChange:T,onThumbPointerDown:(0,s.c)(c),children:(0,h.jsx)(i.sG.div,{...b,ref:R,style:{position:"absolute",...b.style},onPointerDown:(0,p.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),P.current=x.getBoundingClientRect(),E.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",w.viewport&&(w.viewport.style.scrollBehavior="auto"),L(e))}),onPointerMove:(0,p.m)(e.onPointerMove,L),onPointerUp:(0,p.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=E.current,w.viewport&&(w.viewport.style.scrollBehavior=""),P.current=null})})})}),L="ScrollAreaThumb",F=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,i=T(L,e.__scopeScrollArea);return(0,h.jsx)(o.C,{present:n||i.hasThumb,children:(0,h.jsx)(O,{ref:t,...r})})}),O=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,style:o,...l}=e,s=y(L,n),u=T(L,n),{onThumbPositionChange:d}=u,c=(0,a.s)(t,e=>u.onThumbChange(e)),f=r.useRef(void 0),g=U(()=>{f.current&&(f.current(),f.current=void 0)},100);return r.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{g(),f.current||(f.current=$(e,d),d())};return d(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,g,d]),(0,h.jsx)(i.sG.div,{"data-state":u.hasThumb?"visible":"hidden",...l,ref:c,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:(0,p.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),n=e.clientX-t.left,r=e.clientY-t.top;u.onThumbPointerDown({x:n,y:r})}),onPointerUp:(0,p.m)(e.onPointerUp,u.onThumbPointerUp)})});F.displayName=L;var N="ScrollAreaCorner",V=r.forwardRef((e,t)=>{let n=y(N,e.__scopeScrollArea),r=!!(n.scrollbarX&&n.scrollbarY);return"scroll"!==n.type&&r?(0,h.jsx)(j,{...e,ref:t}):null});V.displayName=N;var j=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,...o}=e,l=y(N,n),[a,s]=r.useState(0),[u,d]=r.useState(0),c=!!(a&&u);return W(l.scrollbarX,()=>{let e=l.scrollbarX?.offsetHeight||0;l.onCornerHeightChange(e),d(e)}),W(l.scrollbarY,()=>{let e=l.scrollbarY?.offsetWidth||0;l.onCornerWidthChange(e),s(e)}),c?(0,h.jsx)(i.sG.div,{...o,ref:t,style:{width:a,height:u,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function B(e){return e?parseInt(e,10):0}function G(e,t){let n=e/t;return isNaN(n)?0:n}function _(e){let t=G(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-n)*t,18)}function z(e,t,n="ltr"){let r=_(t),i=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,o=t.scrollbar.size-i,l=t.content-t.viewport,a=(0,c.q)(e,"ltr"===n?[0,l]:[-1*l,0]);return H([0,l],[0,o-r])(a)}function H(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}var $=(e,t=()=>{})=>{let n={left:e.scrollLeft,top:e.scrollTop},r=0;return!function i(){let o={left:e.scrollLeft,top:e.scrollTop},l=n.left!==o.left,a=n.top!==o.top;(l||a)&&t(),n=o,r=window.requestAnimationFrame(i)}(),()=>window.cancelAnimationFrame(r)};function U(e,t){let n=(0,s.c)(e),i=r.useRef(0);return r.useEffect(()=>()=>window.clearTimeout(i.current),[]),r.useCallback(()=>{window.clearTimeout(i.current),i.current=window.setTimeout(n,t)},[n,t])}function W(e,t){let n=(0,s.c)(t);(0,d.N)(()=>{let t=0;if(e){let r=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)});return r.observe(e),()=>{window.cancelAnimationFrame(t),r.unobserve(e)}}},[e,n])}var q=b,Y=x,X=V},17952:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Mountain",[["path",{d:"m8 3 4 8 5-5 5 15H2L8 3z",key:"otkl63"}]])},20131:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},20830:(e,t,n)=>{n.d(t,{q:()=>r});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},23250:(e,t,n)=>{n.d(t,{Kv:()=>o,N4:()=>l});var r=n(63185),i=n(7332);function o(e,t){var n,i,o;return e?"function"==typeof(i=n=e)&&(()=>{let e=Object.getPrototypeOf(i);return e.prototype&&e.prototype.isReactComponent})()||"function"==typeof n||"object"==typeof(o=n)&&"symbol"==typeof o.$$typeof&&["react.memo","react.forward_ref"].includes(o.$$typeof.description)?r.createElement(e,t):e:null}function l(e){let t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=r.useState(()=>({current:(0,i.ZR)(t)})),[o,l]=r.useState(()=>n.current.initialState);return n.current.setOptions(t=>({...t,...e,state:{...o,...e.state},onStateChange:t=>{l(t),null==e.onStateChange||e.onStateChange(t)}})),n.current}},25161:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]])},29792:(e,t,n)=>{n.d(t,{UC:()=>T,bL:()=>I,l9:()=>M});var r,i=n(63185),o=n(19141),l=n(63699),a=n(41505),s=n(85729),u=n(42924),d=(n(20901),n(80799)),c=n(34245),p=n(17095),h=n(84464),f="HoverCard",[g,m]=(0,l.A)(f,[u.Bk]),v=(0,u.Bk)(),[y,b]=g(f),w=e=>{let{__scopeHoverCard:t,children:n,open:r,defaultOpen:o,onOpenChange:l,openDelay:s=700,closeDelay:d=300}=e,c=v(t),p=i.useRef(0),f=i.useRef(0),g=i.useRef(!1),m=i.useRef(!1),[b=!1,w]=(0,a.i)({prop:r,defaultProp:o,onChange:l}),x=i.useCallback(()=>{clearTimeout(f.current),p.current=window.setTimeout(()=>w(!0),s)},[s,w]),S=i.useCallback(()=>{clearTimeout(p.current),g.current||m.current||(f.current=window.setTimeout(()=>w(!1),d))},[d,w]),C=i.useCallback(()=>w(!1),[w]);return i.useEffect(()=>()=>{clearTimeout(p.current),clearTimeout(f.current)},[]),(0,h.jsx)(y,{scope:t,open:b,onOpenChange:w,onOpen:x,onClose:S,onDismiss:C,hasSelectionRef:g,isPointerDownOnContentRef:m,children:(0,h.jsx)(u.bL,{...c,children:n})})};w.displayName=f;var x="HoverCardTrigger",S=i.forwardRef((e,t)=>{let{__scopeHoverCard:n,...r}=e,i=b(x,n),l=v(n);return(0,h.jsx)(u.Mz,{asChild:!0,...l,children:(0,h.jsx)(c.sG.a,{"data-state":i.open?"open":"closed",...r,ref:t,onPointerEnter:(0,o.m)(e.onPointerEnter,D(i.onOpen)),onPointerLeave:(0,o.m)(e.onPointerLeave,D(i.onClose)),onFocus:(0,o.m)(e.onFocus,i.onOpen),onBlur:(0,o.m)(e.onBlur,i.onClose),onTouchStart:(0,o.m)(e.onTouchStart,e=>e.preventDefault())})})});S.displayName=x;var[C,R]=g("HoverCardPortal",{forceMount:void 0}),P="HoverCardContent",E=i.forwardRef((e,t)=>{let n=R(P,e.__scopeHoverCard),{forceMount:r=n.forceMount,...i}=e,l=b(P,e.__scopeHoverCard);return(0,h.jsx)(d.C,{present:r||l.open,children:(0,h.jsx)(A,{"data-state":l.open?"open":"closed",...i,onPointerEnter:(0,o.m)(e.onPointerEnter,D(l.onOpen)),onPointerLeave:(0,o.m)(e.onPointerLeave,D(l.onClose)),ref:t})})});E.displayName=P;var A=i.forwardRef((e,t)=>{let{__scopeHoverCard:n,onEscapeKeyDown:l,onPointerDownOutside:a,onFocusOutside:d,onInteractOutside:c,...f}=e,g=b(P,n),m=v(n),y=i.useRef(null),w=(0,s.s)(t,y),[x,S]=i.useState(!1);return i.useEffect(()=>{if(x){let e=document.body;return r=e.style.userSelect||e.style.webkitUserSelect,e.style.userSelect="none",e.style.webkitUserSelect="none",()=>{e.style.userSelect=r,e.style.webkitUserSelect=r}}},[x]),i.useEffect(()=>{if(y.current){let e=()=>{S(!1),g.isPointerDownOnContentRef.current=!1,setTimeout(()=>{document.getSelection()?.toString()!==""&&(g.hasSelectionRef.current=!0)})};return document.addEventListener("pointerup",e),()=>{document.removeEventListener("pointerup",e),g.hasSelectionRef.current=!1,g.isPointerDownOnContentRef.current=!1}}},[g.isPointerDownOnContentRef,g.hasSelectionRef]),i.useEffect(()=>{y.current&&(function(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP});for(;n.nextNode();)t.push(n.currentNode);return t})(y.current).forEach(e=>e.setAttribute("tabindex","-1"))}),(0,h.jsx)(p.qW,{asChild:!0,disableOutsidePointerEvents:!1,onInteractOutside:c,onEscapeKeyDown:l,onPointerDownOutside:a,onFocusOutside:(0,o.m)(d,e=>{e.preventDefault()}),onDismiss:g.onDismiss,children:(0,h.jsx)(u.UC,{...m,...f,onPointerDown:(0,o.m)(f.onPointerDown,e=>{e.currentTarget.contains(e.target)&&S(!0),g.hasSelectionRef.current=!1,g.isPointerDownOnContentRef.current=!0}),ref:w,style:{...f.style,userSelect:x?"text":void 0,WebkitUserSelect:x?"text":void 0,"--radix-hover-card-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-hover-card-content-available-width":"var(--radix-popper-available-width)","--radix-hover-card-content-available-height":"var(--radix-popper-available-height)","--radix-hover-card-trigger-width":"var(--radix-popper-anchor-width)","--radix-hover-card-trigger-height":"var(--radix-popper-anchor-height)"}})})});function D(e){return t=>"touch"===t.pointerType?void 0:e()}i.forwardRef((e,t)=>{let{__scopeHoverCard:n,...r}=e,i=v(n);return(0,h.jsx)(u.i3,{...i,...r,ref:t})}).displayName="HoverCardArrow";var I=w,M=S,T=E},29987:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Compass",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polygon",{points:"16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76",key:"m9r19z"}]])},30427:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},31099:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Eraser",[["path",{d:"m7 21-4.3-4.3c-1-1-1-2.5 0-3.4l9.6-9.6c1-1 2.5-1 3.4 0l5.6 5.6c1 1 1 2.5 0 3.4L13 21",key:"182aya"}],["path",{d:"M22 21H7",key:"t4ddhn"}],["path",{d:"m5 11 9 9",key:"1mo9qw"}]])},31990:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},32288:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},33265:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},33353:(e,t,n)=>{n.d(t,{b:()=>u});var r=n(63185),i=n(34245),o=n(84464),l="horizontal",a=["horizontal","vertical"],s=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:s=l,...u}=e,d=(n=s,a.includes(n))?s:l;return(0,o.jsx)(i.sG.div,{"data-orientation":d,...r?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...u,ref:t})});s.displayName="Separator";var u=s},34190:(e,t,n)=>{n.d(t,{f:()=>r});function r(e,t){return t?1e3/t*e:0}},36298:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},36468:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},37720:(e,t,n)=>{n.d(t,{B$:()=>D,KU:()=>v,W6:()=>I,Wd:()=>T,XS:()=>S,zD:()=>k});var r=n(63185),i=n(84464),o=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,c=(e,t,n)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,p=(e,t)=>{for(var n in t||(t={}))u.call(t,n)&&c(e,n,t[n]);if(s)for(var n of s(t))d.call(t,n)&&c(e,n,t[n]);return e},h=(e,t)=>l(e,a(t)),f=(e,t)=>{var n={};for(var r in e)u.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&s)for(var r of s(e))0>t.indexOf(r)&&d.call(e,r)&&(n[r]=e[r]);return n},g=(e,t,n)=>new Promise((r,i)=>{var o=e=>{try{a(n.next(e))}catch(e){i(e)}},l=e=>{try{a(n.throw(e))}catch(e){i(e)}},a=e=>e.done?r(e.value):Promise.resolve(e.value).then(o,l);a((n=n.apply(e,t)).next())}),m=(0,r.createContext)(null);function v({children:e,value:t}){return(0,i.jsx)(m.Provider,{value:t,children:e})}function y(){let e=(0,r.useContext)(m);if(!e)throw Error("useMapContext must be used within a MapProvider");return e}var b={layers:[],selectedLayerId:void 0,expandedGroups:new Set};function w(e,t){switch(t.type){case"ADD_LAYER":return h(p({},e),{layers:[...e.layers,t.payload]});case"REMOVE_LAYER":return h(p({},e),{layers:e.layers.filter(e=>e.id!==t.payload)});case"UPDATE_LAYER":return h(p({},e),{layers:e.layers.map(e=>e.id===t.payload.id?p(p({},e),t.payload.updates):e)});case"SET_LAYERS":return h(p({},e),{layers:t.payload});case"TOGGLE_LAYER_VISIBILITY":return h(p({},e),{layers:e.layers.map(e=>e.id===t.payload?h(p({},e),{visible:!e.visible}):e)});case"SET_LAYER_FILTER":return h(p({},e),{layers:e.layers.map(e=>e.id===t.payload.id?h(p({},e),{filter:t.payload.filter}):e)});case"SET_SELECTED_LAYER":return h(p({},e),{selectedLayerId:t.payload});case"TOGGLE_GROUP":let n=new Set(e.expandedGroups);return n.has(t.payload)?n.delete(t.payload):n.add(t.payload),h(p({},e),{expandedGroups:n});default:return e}}var x=(0,r.createContext)(null);function S({children:e}){let[t,n]=(0,r.useReducer)(w,b);return(0,i.jsx)(x.Provider,{value:{state:t,dispatch:n},children:e})}function C(){let{map:e}=y(),{state:t,dispatch:n}=function(){let e=(0,r.use)(x);if(!e)throw Error("useLayerContext must be used within a LayerProvider");return e}(),i=(0,r.useCallback)(r=>g(null,null,function*(){var i,o,l,a;if(!e||!window.odf)return null;let s="geoserver"===r.type?r.layer:r.id,u=t.layers.find(e=>"geoserver"===r.type?e.params.layer===r.layer:e.id===s);if(u)return console.log("Context에서 중복 레이어 발견, 추가 중단:",s),u.id;let d=e.getODFLayers().find(e=>{var t,n;let i=e.getInitialOption();return"geoserver"===r.type?(null==(t=null==i?void 0:i.params)?void 0:t.layer)===r.layer:(null==(n=null==i?void 0:i.params)?void 0:n.id)===s});if(d)return console.log("ODF에서 중복 레이어 발견, 추가 중단:",s),d.getODFId();console.log("새 레이어 생성 시작:",s,r.type);let{type:c,params:g}=function(e){var t;let{type:n,renderOptions:r,className:i,webGLRender:o,attributions:l,children:a,visible:s,zIndex:u}=e,d=f(e,["type","renderOptions","className","webGLRender","attributions","children","visible","zIndex"]);switch(n){case"geoserver":return{type:n,params:p({server:"string"==typeof d.server?d.server:d.server.url,layer:d.layer,service:d.service,method:d.method||"get",bbox:d.bbox||!1,crtfckey:d.crtfckey||"",projection:d.projection||"EPSG:5186",limit:d.limit,tiled:d.tiled,geometryType:d.geometryType,serviceTy:d.serviceTy},"string"!=typeof d.server&&{version:d.server.version,proxyURL:d.server.proxyURL,proxyParam:d.server.proxyParam})};case"geotiff":return{type:n,params:{sources:d.sources,normalize:null==(t=d.normalize)||t,wrapX:d.wrapX,opaque:d.opaque,transition:d.transition,renderOptions:r}};case"geojson":return{type:n,params:{data:d.data,dataProjectionCode:d.dataProjectionCode,featureProjectionCode:d.featureProjectionCode,service:d.service,renderOptions:r}};case"kml":return{type:n,params:{data:d.data,dataProjectionCode:d.dataProjectionCode,featureProjectionCode:d.featureProjectionCode,renderOptions:r}};case"csv":return{type:n,params:{data:d.data,dataProjectionCode:d.dataProjectionCode,featureProjectionCode:d.featureProjectionCode,geometryColumnName:d.geometryColumnName,delimiter:d.delimiter,renderOptions:r}};case"api":return{type:n,params:h(p({server:"string"==typeof d.server?d.server:d.server.url,service:d.service,bbox:d.bbox,tiled:d.tiled,tileGrid:d.tileGrid,originalOption:d.originalOption,parameterFilter:d.parameterFilter},"string"!=typeof d.server&&{proxyURL:d.server.proxyURL,proxyParam:d.server.proxyParam}),{renderOptions:r})};case"svg":return{type:n,params:{svgContainer:d.svgContainer,extent:d.extent,renderOptions:r}};default:throw Error(`Unsupported layer type: ${n}`)}}(r),m=window.odf.LayerFactory.produce(c,p({},g));if(null==(i=r.renderOptions)?void 0:i.style)if("geoserver"===c&&"wms"===g.service){let e=window.odf.StyleFactory.produceSLD(r.renderOptions.style);m.setSLD(e)}else{let e=window.odf.StyleFactory.produce(r.renderOptions.style);m.setStyle(e)}m.setMap(e);let v=null==(o=r.visible)||o,y=null!=(l=r.opacity)?l:1,b=null!=(a=r.zIndex)?a:t.layers.length;m.setVisible(v),m.setOpacity(y),m.setZIndex(b),!1!==r.autoFit&&m.fit(r.fitDuration||0);let w={id:m.getODFId(),name:"geoserver"===r.type?r.name:s||m.getODFId(),type:c,visible:v,opacity:y,zIndex:b,odfLayer:m,params:g,info:"geoserver"===r.type?r.info:{lyrId:"",lyrNm:""}};if(n({type:"ADD_LAYER",payload:w}),w.id)return w.id}),[e]),o=(0,r.useCallback)(t=>{e.removeLayer(t),n({type:"REMOVE_LAYER",payload:t})},[e,n,t]),l=(0,r.useCallback)((e,r)=>{let i=t.layers.find(t=>t.id===e);if(i)try{let t="string"==typeof r?JSON.parse(r):r;if("geoserver"===i.type&&"wms"===i.params.service){let e=window.odf.StyleFactory.produceSLD(t);i.odfLayer.setSLD(e)}else{let e=window.odf.StyleFactory.produce(t);i.odfLayer.setStyle(e)}n({type:"UPDATE_LAYER",payload:{id:e,updates:{style:t}}})}catch(e){}},[t.layers,n]),a=(0,r.useCallback)((e,r)=>{let i=t.layers.find(t=>t.id===e);i&&(i.odfLayer.setVisible(r),n({type:"UPDATE_LAYER",payload:{id:e,updates:{visible:r}}}))},[t.layers,n]),s=(0,r.useCallback)((t,r)=>{e&&(e.switchLayer(t,r),n({type:"UPDATE_LAYER",payload:{id:t,updates:{visible:r}}}))},[e,n]),u=(0,r.useCallback)((e,n=0)=>{let r=t.layers.find(t=>t.id===e);(null==r?void 0:r.odfLayer)&&r.odfLayer.fit(n)},[t.layers]),d=(0,r.useCallback)((e,r)=>{let i=t.layers.find(t=>t.id===e);i&&(i.odfLayer.setZIndex(r),n({type:"UPDATE_LAYER",payload:{id:e,updates:{zIndex:r}}}))},[t.layers,n]),c=(0,r.useCallback)((r,i)=>{if(!e||!window.odf)return console.log("map 또는 odf가 없어서 스타일 적용 불가"),!1;let o=t.layers.find(e=>e.id===r);if(!o)return console.error(`\uB808\uC774\uC5B4\uB97C \uCC3E\uC744 \uC218 \uC5C6\uC74C (ID: ${r})`),!1;try{let e="string"==typeof i?JSON.parse(i):i;if("geoserver"===o.type&&"wms"===o.params.service){let t=window.odf.StyleFactory.produceSLD(e);o.odfLayer.setSLD(t)}else o.odfLayer.setStyle(e);return n({type:"UPDATE_LAYER",payload:{id:r,updates:{style:e}}}),!0}catch(e){return console.error(`\uC2A4\uD0C0\uC77C \uC5C5\uB370\uC774\uD2B8 \uC911 \uC624\uB958 (ID: ${r}):`,e),!1}},[e,t.layers,n]),m=(0,r.useCallback)((e,r)=>g(null,null,function*(){try{let i=t.layers.find(t=>t.id===e);if(!i)throw Error("레이어를 찾을 수 없습니다.");r&&""!==r.trim()?(console.log(`\uD544\uD130 \uC801\uC6A9: ${e} -> ${r}`),i.odfLayer.defineQuery({condition:r})):(console.log(`\uD544\uD130 \uCD08\uAE30\uD654: ${e}`),i.odfLayer.defineQuery({condition:null})),n({type:"SET_LAYER_FILTER",payload:{id:e,filter:r}})}catch(e){throw console.error("레이어 필터 처리 중 오류:",e),Error("레이어 필터 적용 중 오류가 발생하였습니다.")}}),[t.layers,n]);return{layers:t.layers,addLayer:i,removeLayer:o,updateStyle:l,setVisible:a,switchLayer:s,setZIndex:d,fitLayer:u,selectedLayerId:t.selectedLayerId,selectLayer:e=>n({type:"SET_SELECTED_LAYER",payload:e}),expandedGroups:t.expandedGroups,toggleGroup:e=>n({type:"TOGGLE_GROUP",payload:e}),setOpacity:(e,r)=>{let i=t.layers.find(t=>t.id===e);(null==i?void 0:i.odfLayer)&&(i.odfLayer.setOpacity(r),n({type:"UPDATE_LAYER",payload:{id:e,updates:{opacity:r}}}))},updateLayerStyle:c,setLayerFilter:m}}var R=e=>{var t,n,r,i,o,l,a;return{center:new odf.Coordinate(null!=(n=null==(t=e.center)?void 0:t[0])?n:199312.9996,null!=(i=null==(r=e.center)?void 0:r[1])?i:551784.6924),zoom:null!=(o=e.zoom)?o:11,projection:null!=(l=e.projection)?l:"EPSG:5186",baroEMapURL:"https://geon-gateway.geon.kr/map/api/map/baroemap",baroEMapAirURL:"https://geon-gateway.geon.kr/map/api/map/ngisair",basemap:null!=(a=e.basemap)?a:{baroEMap:["eMapBasic","eMapAIR","eMapColor","eMapWhite"]},optimization:!0}},P=(e,t,n)=>e.getProjection().project(t,n),E=(e,t,n)=>{if(t)if("wms"===n){let n=odf.StyleFactory.produceSLD(t);e.setSLD(n)}else{let n=odf.StyleFactory.produce(t);e.setStyle(n)}},A=()=>new Promise((e,t)=>{let n=0,r=setInterval(()=>{n++,odf?(clearInterval(r),e()):n>=30&&(clearInterval(r),t(Error("ODF가 프로젝트에 추가되지 않았습니다. ODF를 프로젝트에 추가해주세요.")))},100)});function D(e){var t;let[n,i]=(0,r.useState)(null),[o,l]=(0,r.useState)(!1),[a,s]=(0,r.useState)([]),[u,d]=(0,r.useState)(null!=(t=e.projection)?t:"EPSG:5186"),c=(0,r.useCallback)(()=>g(null,null,function*(){if(e.containerRef.current&&!o)try{yield A();let t=e.containerRef.current,n=R(e),r=new odf.Map(t,n),o=new odf.BasemapControl;o.setMap(r),r.basemapControl=o,i(r),l(!0)}catch(e){throw console.error("Failed to initialize map:",e),e}}),[o,e]);return(0,r.useEffect)(()=>{e.autoInit&&c()},[c,e.autoInit]),(0,r.useEffect)(()=>{var t;n&&(e.projection!==u&&d(null!=(t=e.projection)?t:"EPSG:5186"),e.center&&n.setCenter(new odf.Coordinate(e.center[0],e.center[1])),void 0!==e.zoom&&n.setZoom(e.zoom))},[n,e.center,e.zoom,e.projection,u]),{map:n,view:{setCenter:(e,t)=>{if(!n)return;let r=t?P(n,e,t):e;n.setCenter(new odf.Coordinate(r[0],r[1]))},setZoom:e=>{n&&n.setZoom(e)},setBasemap:e=>{(null==n?void 0:n.basemapControl)&&n.basemapControl.switchBaseLayer(e)},getCenter:e=>{if(!n)return[0,0];let t=n.getCenter(),r=[t.x,t.y];return e?P(n,r,u):r},getZoom:()=>{var e;return null!=(e=null==n?void 0:n.getZoom())?e:0},moveToCurrentLocation:e=>{n&&(null==navigator?void 0:navigator.geolocation)&&navigator.geolocation.getCurrentPosition(t=>{let{latitude:r,longitude:i}=t.coords,o=P(n,[i,r],"4326");console.log(o),n.setCenter(o),void 0!==e&&n.setZoom(e)},e=>e)}},layer:{add:(e,t,r)=>g(null,null,function*(){if(!n)return null;try{let i=odf.LayerFactory.produce(t.type,t.params);E(i,r,t.params.service),i.setMap(n),n.setZIndex(i.getODFId(),a.length);let o={id:e.lyrId,name:e.lyrNm,type:t.type,visible:!0,zIndex:a.length,odfLayer:i,params:t.params,info:e,style:r?p({},r):void 0};return s(e=>[...e,o]),o}catch(e){return console.error("Layer add error:",e),null}}),remove:e=>{if(!n)return;let t=a.find(t=>t.id===e);(null==t?void 0:t.odfLayer)&&(n.removeLayer(t.odfLayer),s(t=>t.filter(t=>t.id!==e)))},toggle:e=>{if(!n)return;let t=a.find(t=>t.id===e);t&&(n.switchLayer(t.odfLayer.getODFId(),!t.visible),s(t=>t.map(t=>t.id===e?h(p({},t),{visible:!t.visible}):t)))},getAll:()=>a,updateStyle:(e,t)=>g(null,null,function*(){if(!n)return;let r=a.find(t=>t.id===e);if(!r)return;let i="string"==typeof t?JSON.parse(t):t;E(r.odfLayer,i,r.params.service),s(t=>t.map(t=>t.id===e?h(p({},t),{style:i}):t))}),updateFilter:(e,t)=>g(null,null,function*(){if(!n)return;let r=a.find(t=>t.id===e);if(r)try{r.odfLayer.defineQuery({condition:t}),s(n=>n.map(n=>n.id===e?h(p({},n),{filter:t}):n))}catch(e){throw console.error("Layer filter update error:",e),Error("레이어 필터 적용 중 오류가 발생하였습니다.")}})},isLoading:!o}}var I=r.memo(({className:e,containerRef:t,id:n})=>(0,i.jsx)("div",{id:`map-${n}`,ref:t,className:e}));I.displayName="MapContainer";var M=r.createContext(null);function T(e){let{addLayer:t,removeLayer:n,setVisible:o,updateStyle:l,setLayerFilter:a,setOpacity:s,setZIndex:u}=C(),[d,c]=(0,r.useState)(null),p=(0,r.useRef)(null),h=(0,r.useRef)(!1),m=(0,r.useRef)(null),{visible:v,renderOptions:y,zIndex:b,opacity:w,children:x}=e,S=f(e,["visible","renderOptions","zIndex","opacity","children"]),R=(0,r.useMemo)(()=>S,[S.type,"geoserver"===S.type?S.layer:S.id,"geoserver"===S.type?S.server:void 0,"geoserver"===S.type?S.service:void 0]),P=(0,r.useCallback)(()=>{let e=p.current;if(console.log("레이어 정리 시작 레이어 ID",e),e)try{n(e)}catch(e){console.error("레이어 정리 중 오류:",e)}finally{c(null),p.current=null,h.current=!1}},[n]),E=(0,r.useCallback)(()=>g(null,null,function*(){if(!h.current&&!p.current){h.current=!0;try{let n=yield t(e);n&&(c(n),p.current=n,console.log("레이어 초기화 완료, ID:",n))}catch(e){console.error("레이어 초기화 중 오류:",e)}finally{h.current=!1}}}),[t,e]);(0,r.useEffect)(()=>(console.log("Layer 컴포넌트 마운트 또는 핵심 속성 변경"),p.current&&P(),E(),()=>{console.log("Layer 컴포넌트 언마운트"),P()}),[R]),(0,r.useEffect)(()=>{p.current=d},[d]),(0,r.useEffect)(()=>{var e;if(d&&void 0!==v){let t=null==(e=m.current)?void 0:e.visible;m.current&&t!==v&&(console.log(`\uB808\uC774\uC5B4 \uAC00\uC2DC\uC131 \uBCC0\uACBD: ${d} -> ${v}`),o(d,v))}},[d,v,o]),(0,r.useEffect)(()=>{var e,t;d&&(null==y?void 0:y.style)&&JSON.stringify(null==(t=null==(e=m.current)?void 0:e.renderOptions)?void 0:t.style)!==JSON.stringify(y.style)&&(console.log(`\uB808\uC774\uC5B4 \uC2A4\uD0C0\uC77C \uBCC0\uACBD: ${d}`),l(d,y.style))},[d,null==y?void 0:y.style,l]),(0,r.useEffect)(()=>{var e;if(d&&void 0!==b){let t=null==(e=m.current)?void 0:e.zIndex;m.current&&t!==b&&(console.log(`\uB808\uC774\uC5B4 zIndex \uBCC0\uACBD: ${d} -> ${b}`),u(d,b))}},[d,b,u]),(0,r.useEffect)(()=>{var e;if(d&&void 0!==w){let t=null==(e=m.current)?void 0:e.opacity;m.current&&t!==w&&(console.log(`\uB808\uC774\uC5B4 \uD22C\uBA85\uB3C4 \uBCC0\uACBD: ${d} -> ${w}`),s(d,w))}},[d,w,s]),(0,r.useEffect)(()=>{if(d&&"geoserver"===e.type){let t=e.filter;t!==(m.current&&"filter"in m.current?m.current.filter:void 0)&&(""===t||null==t?(console.log(`\uB808\uC774\uC5B4 \uD544\uD130 \uCD08\uAE30\uD654: ${d}`),a(d,"").catch(e=>{console.error("필터 초기화 실패:",e)})):(console.log(`\uB808\uC774\uC5B4 \uD544\uD130 \uBCC0\uACBD: ${d} -> ${t}`),a(d,t).catch(e=>{console.error("필터 적용 실패:",e)})))}},[d,e.type,e.filter,a]),(0,r.useEffect)(()=>{m.current=e});let A=r.useMemo(()=>d?{layerId:d}:null,[d]);return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(M.Provider,{value:A,children:d&&x})})}function k(e){var{children:t}=e,n=f(e,["children"]);let{map:o}=y(),l=(0,r.useRef)(null),a=(0,r.useRef)(null);return(0,r.useEffect)(()=>{var e,t,r,i,s;if(!o||!window.odf||!l.current)return;let u=new window.odf.Marker({position:n.position,positioning:null!=(e=n.positioning)?e:"bottom-center",offset:null!=(t=n.offset)?t:[0,0],stopEvent:!0,style:{element:l.current},autoPan:null!=(r=n.autoPan)&&r,autoPanAnimation:null!=(i=n.autoPanAnimation)?i:250,autoPanMargin:null!=(s=n.autoPanMargin)?s:20});return u.setMap(o),a.current=u,()=>{a.current&&a.current.removeMap()}},[o,n.position,n.positioning,n.offset,n.autoPan,n.autoPanAnimation,n.autoPanMargin]),(0,i.jsx)("div",{ref:l,className:"popup-content",children:t})}(0,r.memo)(function({children:e,style:t}){let n=(0,r.useContext)(M),{updateLayerStyle:i}=C(),o="string"==typeof e?e:t&&"object"==typeof t&&0===Object.keys(t).length?null:t;return(0,r.useEffect)(()=>{if((null==n?void 0:n.layerId)&&o)try{i(n.layerId,o)&&console.log("스타일 적용 성공:",n.layerId)}catch(e){console.error("스타일 적용 중 오류:",e)}},[t]),null},(e,t)=>e.children===t.children&&(e.style&&t.style?JSON.stringify(e.style)===JSON.stringify(t.style):e.style===t.style))},39822:(e,t,n)=>{n.d(t,{E:()=>a});var r=n(53859),i=n(83886),o=n(99134),l=n(82982);function a(e,...t){let n=e.length;return function(e,t){let n=(0,i.d)(t()),l=()=>n.set(t());return l(),(0,r.E)(()=>{let t=()=>o.Gt.preRender(l,!1,!0),n=e.map(e=>e.on("change",t));return()=>{n.forEach(e=>e()),(0,o.WG)(l)}}),n}(t.filter(l.S),function(){let r="";for(let i=0;i<n;i++){r+=e[i];let n=t[i];n&&(r+=(0,l.S)(n)?n.get():n)}return r})}},40106:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Forward",[["polyline",{points:"15 17 20 12 15 7",key:"1w3sku"}],["path",{d:"M4 18v-2a4 4 0 0 1 4-4h12",key:"jmiej9"}]])},41521:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("CircleCheckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},42409:(e,t,n)=>{n.d(t,{Q:()=>r});let r={value:null,addProjectionMetrics:null}},42447:(e,t,n)=>{function r(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function i(e){let t=[{},{}];return e?.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function o(e,t,n,r){if("function"==typeof t){let[o,l]=i(r);t=t(void 0!==n?n:e.custom,o,l)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[o,l]=i(r);t=t(void 0!==n?n:e.custom,o,l)}return t}function l(e,t,n){let r=e.getProps();return o(r,t,void 0!==n?n:r.custom,e)}n.d(t,{P:()=>om});let a=e=>Array.isArray(e);var s,u,d=n(70815),c=n(82982),p=n(98838);function h(e,t){let n=e.getValue("willChange");if((0,c.S)(n)&&n.add)return n.add(t);if(!n&&p.W.WillChange){let n=new p.W.WillChange("auto");e.addValue("willChange",n),n.add(t)}}let f=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),g="data-"+f("framerAppearId"),m=e=>null!==e,v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],y=new Set(v),b={type:"spring",stiffness:500,damping:25,restSpeed:10},w=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),x={type:"keyframes",duration:.8},S={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},C=(e,{keyframes:t})=>t.length>2?x:y.has(e)?e.startsWith("scale")?w(t[1]):b:S;function R(e,t){return e?.[t]??e?.default??e}let P=e=>1e3*e,E=e=>e/1e3;var A=n(99134),D=n(89108);let I={layout:0,mainThread:0,waapi:0},M=e=>t=>"string"==typeof t&&t.startsWith(e),T=M("--"),k=M("var(--"),L=e=>!!k(e)&&F.test(e.split("/*")[0].trim()),F=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,O=(e,t,n)=>n>t?t:n<e?e:n,N={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},V={...N,transform:e=>O(0,1,e)},j={...N,default:1},B=e=>Math.round(1e5*e)/1e5,G=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,_=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,z=(e,t)=>n=>!!("string"==typeof n&&_.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),H=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,o,l,a]=r.match(G);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(l),alpha:void 0!==a?parseFloat(a):1}},$=e=>O(0,255,e),U={...N,transform:e=>Math.round($(e))},W={test:z("rgb","red"),parse:H("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+U.transform(e)+", "+U.transform(t)+", "+U.transform(n)+", "+B(V.transform(r))+")"},q={test:z("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:W.transform},Y=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),X=Y("deg"),K=Y("%"),Z=Y("px"),Q=Y("vh"),J=Y("vw"),ee={...K,parse:e=>K.parse(e)/100,transform:e=>K.transform(100*e)},et={test:z("hsl","hue"),parse:H("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+K.transform(B(t))+", "+K.transform(B(n))+", "+B(V.transform(r))+")"},en={test:e=>W.test(e)||q.test(e)||et.test(e),parse:e=>W.test(e)?W.parse(e):et.test(e)?et.parse(e):q.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?W.transform(e):et.transform(e)},er=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,ei="number",eo="color",el=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ea(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],o=0,l=t.replace(el,e=>(en.test(e)?(r.color.push(o),i.push(eo),n.push(en.parse(e))):e.startsWith("var(")?(r.var.push(o),i.push("var"),n.push(e)):(r.number.push(o),i.push(ei),n.push(parseFloat(e))),++o,"${}")).split("${}");return{values:n,split:l,indexes:r,types:i}}function es(e){return ea(e).values}function eu(e){let{split:t,types:n}=ea(e),r=t.length;return e=>{let i="";for(let o=0;o<r;o++)if(i+=t[o],void 0!==e[o]){let t=n[o];t===ei?i+=B(e[o]):t===eo?i+=en.transform(e[o]):i+=e[o]}return i}}let ed=e=>"number"==typeof e?0:e,ec={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(G)?.length||0)+(e.match(er)?.length||0)>0},parse:es,createTransformer:eu,getAnimatableNone:function(e){let t=es(e);return eu(e)(t.map(ed))}};function ep(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function eh(e,t){return n=>n>0?t:e}let ef=(e,t,n)=>e+(t-e)*n,eg=()=>{},em=()=>{},ev=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},ey=[q,W,et],eb=e=>ey.find(t=>t.test(e));function ew(e){let t=eb(e);if(eg(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let n=t.parse(e);return t===et&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,o=0,l=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,a=2*n-r;i=ep(a,r,e+1/3),o=ep(a,r,e),l=ep(a,r,e-1/3)}else i=o=l=n;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*l),alpha:r}}(n)),n}let ex=(e,t)=>{let n=ew(e),r=ew(t);if(!n||!r)return eh(e,t);let i={...n};return e=>(i.red=ev(n.red,r.red,e),i.green=ev(n.green,r.green,e),i.blue=ev(n.blue,r.blue,e),i.alpha=ef(n.alpha,r.alpha,e),W.transform(i))},eS=new Set(["none","hidden"]),eC=(e,t)=>n=>t(e(n)),eR=(...e)=>e.reduce(eC);function eP(e,t){return n=>ef(e,t,n)}function eE(e){return"number"==typeof e?eP:"string"==typeof e?L(e)?eh:en.test(e)?ex:eI:Array.isArray(e)?eA:"object"==typeof e?en.test(e)?ex:eD:eh}function eA(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>eE(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function eD(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=eE(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let eI=(e,t)=>{let n=ec.createTransformer(t),r=ea(e),i=ea(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?eS.has(e)&&!i.values.length||eS.has(t)&&!r.values.length?function(e,t){return eS.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):eR(eA(function(e,t){let n=[],r={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let o=t.types[i],l=e.indexes[o][r[o]],a=e.values[l]??0;n[i]=a,r[o]++}return n}(r,i),i.values),n):(eg(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eh(e,t))};function eM(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?ef(e,t,n):eE(e)(e,t)}let eT=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>A.Gt.update(t,e),stop:()=>(0,A.WG)(t),now:()=>A.uv.isProcessing?A.uv.timestamp:D.k.now()}},ek=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=e(t/(i-1))+", ";return`linear(${r.substring(0,r.length-2)})`};function eL(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}var eF=n(34190);function eO(e,t,n){let r=Math.max(t-5,0);return(0,eF.f)(n-e(r),t-r)}let eN={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eV(e,t){return e*Math.sqrt(1-t*t)}let ej=["duration","bounce"],eB=["stiffness","damping","mass"];function eG(e,t){return t.some(t=>void 0!==e[t])}function e_(e=eN.visualDuration,t=eN.bounce){let n,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:o}=r,l=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],s={done:!1,value:l},{stiffness:u,damping:d,mass:c,duration:p,velocity:h,isResolvedFromDuration:f}=function(e){let t={velocity:eN.velocity,stiffness:eN.stiffness,damping:eN.damping,mass:eN.mass,isResolvedFromDuration:!1,...e};if(!eG(e,eB)&&eG(e,ej))if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*O(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:eN.mass,stiffness:r,damping:i}}else{let n=function({duration:e=eN.duration,bounce:t=eN.bounce,velocity:n=eN.velocity,mass:r=eN.mass}){let i,o;eg(e<=P(eN.maxDuration),"Spring duration must be 10 seconds or less");let l=1-t;l=O(eN.minDamping,eN.maxDamping,l),e=O(eN.minDuration,eN.maxDuration,E(e)),l<1?(i=t=>{let r=t*l,i=r*e;return .001-(r-n)/eV(t,l)*Math.exp(-i)},o=t=>{let r=t*l*e,o=Math.pow(l,2)*Math.pow(t,2)*e,a=Math.exp(-r),s=eV(Math.pow(t,2),l);return(r*n+n-o)*a*(-i(t)+.001>0?-1:1)/s}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),o=t=>e*e*(n-t)*Math.exp(-t*e));let a=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,o,5/e);if(e=P(e),isNaN(a))return{stiffness:eN.stiffness,damping:eN.damping,duration:e};{let t=Math.pow(a,2)*r;return{stiffness:t,damping:2*l*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:eN.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-E(r.velocity||0)}),g=h||0,m=d/(2*Math.sqrt(u*c)),v=a-l,y=E(Math.sqrt(u/c)),b=5>Math.abs(v);if(i||(i=b?eN.restSpeed.granular:eN.restSpeed.default),o||(o=b?eN.restDelta.granular:eN.restDelta.default),m<1){let e=eV(y,m);n=t=>a-Math.exp(-m*y*t)*((g+m*y*v)/e*Math.sin(e*t)+v*Math.cos(e*t))}else if(1===m)n=e=>a-Math.exp(-y*e)*(v+(g+y*v)*e);else{let e=y*Math.sqrt(m*m-1);n=t=>{let n=Math.exp(-m*y*t),r=Math.min(e*t,300);return a-n*((g+m*y*v)*Math.sinh(r)+e*v*Math.cosh(r))/e}}let w={calculatedDuration:f&&p||null,next:e=>{let t=n(e);if(f)s.done=e>=p;else{let r=0===e?g:0;m<1&&(r=0===e?P(g):eO(n,e,t));let l=Math.abs(a-t)<=o;s.done=Math.abs(r)<=i&&l}return s.value=s.done?a:t,s},toString:()=>{let e=Math.min(eL(w),2e4),t=ek(t=>w.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return w}function ez({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:l,min:a,max:s,restDelta:u=.5,restSpeed:d}){let c,p,h=e[0],f={done:!1,value:h},g=e=>void 0!==a&&e<a||void 0!==s&&e>s,m=e=>void 0===a?s:void 0===s||Math.abs(a-e)<Math.abs(s-e)?a:s,v=n*t,y=h+v,b=void 0===l?y:l(y);b!==y&&(v=b-h);let w=e=>-v*Math.exp(-e/r),x=e=>b+w(e),S=e=>{let t=w(e),n=x(e);f.done=Math.abs(t)<=u,f.value=f.done?b:n},C=e=>{g(f.value)&&(c=e,p=e_({keyframes:[f.value,m(f.value)],velocity:eO(x,e,f.value),damping:i,stiffness:o,restDelta:u,restSpeed:d}))};return C(0),{calculatedDuration:null,next:e=>{let t=!1;return(p||void 0!==c||(t=!0,S(e),C(e)),void 0!==c&&e>=c)?p.next(e-c):(t||S(e),f)}}}e_.applyToOptions=e=>{let t=function(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min(eL(r),2e4);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:E(i)}}(e,100,e_);return e.ease=t.ease,e.duration=P(t.duration),e.type="keyframes",e};var eH=n(8006);let e$=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r},eU=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function eW(e,t,n,r){if(e===t&&n===r)return eH.l;let i=t=>(function(e,t,n,r,i){let o,l,a=0;do(o=eU(l=t+(n-t)/2,r,i)-e)>0?n=l:t=l;while(Math.abs(o)>1e-7&&++a<12);return l})(t,0,1,e,n);return e=>0===e||1===e?e:eU(i(e),t,r)}let eq=eW(.42,0,1,1),eY=eW(0,0,.58,1),eX=eW(.42,0,.58,1),eK=e=>Array.isArray(e)&&"number"!=typeof e[0],eZ=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,eQ=e=>t=>1-e(1-t),eJ=eW(.33,1.53,.69,.99),e0=eQ(eJ),e1=eZ(e0),e2=e=>(e*=2)<1?.5*e0(e):.5*(2-Math.pow(2,-10*(e-1))),e5=e=>1-Math.sin(Math.acos(e)),e4=eQ(e5),e3=eZ(e5),e7=e=>Array.isArray(e)&&"number"==typeof e[0],e8={linear:eH.l,easeIn:eq,easeInOut:eX,easeOut:eY,circIn:e5,circInOut:e3,circOut:e4,backIn:e0,backInOut:e1,backOut:eJ,anticipate:e2},e9=e=>"string"==typeof e,e6=e=>{if(e7(e)){em(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,n,r,i]=e;return eW(t,n,r,i)}return e9(e)?(em(void 0!==e8[e],`Invalid easing type '${e}'`),e8[e]):e};function te({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){var i;let o=eK(r)?r.map(e6):e6(r),l={done:!1,value:t[0]},a=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let o=e.length;if(em(o===t.length,"Both input and output ranges must be the same length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let l=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,n){let r=[],i=n||p.W.mix||eM,o=e.length-1;for(let n=0;n<o;n++){let o=i(e[n],e[n+1]);t&&(o=eR(Array.isArray(t)?t[n]||eH.l:t,o)),r.push(o)}return r}(t,r,i),s=a.length,u=n=>{if(l&&n<e[0])return t[0];let r=0;if(s>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=e$(e[r],e[r+1],n);return a[r](i)};return n?t=>u(O(e[0],e[o-1],t)):u}((i=n&&n.length===t.length?n:function(e){let t=[0];return!function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=e$(0,t,r);e.push(ef(n,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(o)?o:t.map(()=>o||eX).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(l.value=a(t),l.done=t>=e,l)}}let tt=e=>null!==e;function tn(e,{repeat:t,repeatType:n="loop"},r,i=1){let o=e.filter(tt),l=i<0||t&&"loop"!==n&&t%2==1?0:o.length-1;return l&&void 0!==r?r:o[l]}let tr={decay:ez,inertia:ez,tween:te,keyframes:te,spring:e_};function ti(e){"string"==typeof e.type&&(e.type=tr[e.type])}class to{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let tl=e=>e/100;class ta extends to{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==D.k.now()&&this.tick(D.k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},I.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;ti(e);let{type:t=te,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:o=0}=e,{keyframes:l}=e,a=t||te;a!==te&&"number"!=typeof l[0]&&(this.mixKeyframes=eR(tl,eM(l[0],l[1])),l=[0,100]);let s=a({...e,keyframes:l});"mirror"===i&&(this.mirroredGenerator=a({...e,keyframes:[...l].reverse(),velocity:-o})),null===s.calculatedDuration&&(s.calculatedDuration=eL(s));let{calculatedDuration:u}=s;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=s}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:o,resolvedDuration:l,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);let{delay:s=0,keyframes:u,repeat:d,repeatType:c,repeatDelay:p,type:h,onUpdate:f,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let m=this.currentTime-s*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?m<0:m>r;this.currentTime=Math.max(m,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let y=this.currentTime,b=n;if(d){let e=Math.min(this.currentTime,r)/l,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,d+1))%2&&("reverse"===c?(n=1-n,p&&(n-=p/l)):"mirror"===c&&(b=o)),y=O(0,1,n)*l}let w=v?{done:!1,value:u[0]}:b.next(y);i&&(w.value=i(w.value));let{done:x}=w;v||null===a||(x=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return S&&h!==ez&&(w.value=tn(u,this.options,g,this.speed)),f&&f(w.value),S&&this.finish(),w}then(e,t){return this.finished.then(e,t)}get duration(){return E(this.calculatedDuration)}get time(){return E(this.currentTime)}set time(e){e=P(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(D.k.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=E(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eT,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=t??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(D.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,I.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let ts=e=>180*e/Math.PI,tu=e=>tc(ts(Math.atan2(e[1],e[0]))),td={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tu,rotateZ:tu,skewX:e=>ts(Math.atan(e[1])),skewY:e=>ts(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},tc=e=>((e%=360)<0&&(e+=360),e),tp=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),th=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tf={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tp,scaleY:th,scale:e=>(tp(e)+th(e))/2,rotateX:e=>tc(ts(Math.atan2(e[6],e[5]))),rotateY:e=>tc(ts(Math.atan2(-e[2],e[0]))),rotateZ:tu,rotate:tu,skewX:e=>ts(Math.atan(e[4])),skewY:e=>ts(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tg(e){return+!!e.includes("scale")}function tm(e,t){let n,r;if(!e||"none"===e)return tg(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)n=tf,r=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=td,r=t}if(!r)return tg(t);let o=n[t],l=r[1].split(",").map(ty);return"function"==typeof o?o(l):l[o]}let tv=(e,t)=>{let{transform:n="none"}=getComputedStyle(e);return tm(n,t)};function ty(e){return parseFloat(e.trim())}let tb=e=>e===N||e===Z,tw=new Set(["x","y","z"]),tx=v.filter(e=>!tw.has(e)),tS={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tm(t,"x"),y:(e,{transform:t})=>tm(t,"y")};tS.translateX=tS.x,tS.translateY=tS.y;let tC=new Set,tR=!1,tP=!1,tE=!1;function tA(){if(tP){let e=Array.from(tC).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return tx.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{e.getValue(t)?.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tP=!1,tR=!1,tC.forEach(e=>e.complete(tE)),tC.clear()}function tD(){tC.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tP=!0)})}class tI{constructor(e,t,n,r,i,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(tC.add(this),tR||(tR=!0,A.Gt.read(tD),A.Gt.resolveKeyframes(tA))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){let i=r?.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,o);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=o),r&&void 0===i&&r.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tC.delete(this)}cancel(){"scheduled"===this.state&&(tC.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tM=e=>e.startsWith("--");function tT(e){let t;return()=>(void 0===t&&(t=e()),t)}let tk=tT(()=>void 0!==window.ScrollTimeline);var tL=n(42409);let tF={},tO=function(e,t){let n=tT(e);return()=>tF[t]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tN=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,tV={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tN([0,.65,.55,1]),circOut:tN([.55,0,1,.45]),backIn:tN([.31,.01,.66,-.59]),backOut:tN([.33,1.53,.69,.99])};function tj(e){return"function"==typeof e&&"applyToOptions"in e}class tB extends to{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:o=!1,finalKeyframe:l,onComplete:a}=e;this.isPseudoElement=!!i,this.allowFlatten=o,this.options=e,em("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let s=function({type:e,...t}){return tj(e)&&tO()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:l="loop",ease:a="easeOut",times:s}={},u){let d={[t]:n};s&&(d.offset=s);let c=function e(t,n){if(t)return"function"==typeof t?tO()?ek(t,n):"ease-out":e7(t)?tN(t):Array.isArray(t)?t.map(t=>e(t,n)||tV.easeOut):tV[t]}(a,i);Array.isArray(c)&&(d.easing=c),tL.Q.value&&I.waapi++;let p={delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===l?"alternate":"normal"};u&&(p.pseudoElement=u);let h=e.animate(d,p);return tL.Q.value&&h.finished.finally(()=>{I.waapi--}),h}(t,n,r,s,i),!1===s.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=tn(r,this.options,l,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){tM(t)?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return E(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return E(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=P(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tk())?(this.animation.timeline=e,eH.l):t(this)}}let tG={anticipate:e2,backInOut:e1,circInOut:e3};class t_ extends tB{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tG&&(e.ease=tG[e.ease])}(e),ti(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:n,onComplete:r,element:i,...o}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let l=new ta({...o,autoplay:!1}),a=P(this.finishedTime??this.time);t.setWithVelocity(l.sample(a-10).value,l.sample(a).value,10),l.stop()}}let tz=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(ec.test(e)||"0"===e)&&!e.startsWith("url("));function tH(e){return"object"==typeof e&&null!==e}function t$(e){return tH(e)&&"offsetHeight"in e}let tU=new Set(["opacity","clipPath","filter","transform"]),tW=tT(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tq extends to{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o="loop",keyframes:l,name:a,motionValue:s,element:u,...d}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=D.k.now();let c={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:o,name:a,motionValue:s,element:u,...d},p=u?.KeyframeResolver||tI;this.keyframeResolver=new p(l,(e,t,n)=>this.onKeyframesResolved(e,t,c,!n),a,s,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;let{name:i,type:o,velocity:l,delay:a,isHandoff:s,onUpdate:u}=n;this.resolvedAt=D.k.now(),!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],l=tz(i,t),a=tz(o,t);return eg(l===a,`You are trying to animate ${t} from "${i}" to "${o}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${o} via the \`style\` property.`),!!l&&!!a&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||tj(n))&&r)}(e,i,o,l)&&((p.W.instantAnimations||!a)&&u?.(tn(e,n,t)),e[0]=e[e.length-1],n.duration=0,n.repeat=0);let d={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...n,keyframes:e},c=!s&&function(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:o,type:l}=e;if(!t$(t?.owner?.current))return!1;let{onUpdate:a,transformTemplate:s}=t.owner.getProps();return tW()&&n&&tU.has(n)&&("transform"!==n||!s)&&!a&&!r&&"mirror"!==i&&0!==o&&"inertia"!==l}(d)?new t_({...d,element:d.motionValue.owner.current}):new ta(d);c.finished.then(()=>this.notifyFinished()).catch(eH.l),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tE=!0,tD(),tA(),tE=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let tY=(e,t,n,r={},i,o)=>l=>{let a=R(r,e)||{},s=a.delay||r.delay||0,{elapsed:u=0}=r;u-=P(s);let d={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-u,onUpdate:e=>{t.set(e),a.onUpdate&&a.onUpdate(e)},onComplete:()=>{l(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:l,repeatDelay:a,from:s,elapsed:u,...d}){return!!Object.keys(d).length}(a)&&Object.assign(d,C(e,d)),d.duration&&(d.duration=P(d.duration)),d.repeatDelay&&(d.repeatDelay=P(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let c=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(c=!0)),(p.W.instantAnimations||p.W.skipAnimations)&&(c=!0,d.duration=0,d.delay=0),d.allowFlatten=!a.type&&!a.ease,c&&!o&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(m),o=t&&"loop"!==n&&t%2==1?0:i.length-1;return i[o]}(d.keyframes,a);if(void 0!==e)return void A.Gt.update(()=>{d.onUpdate(e),d.onComplete()})}return a.isSync?new ta(d):new tq(d)},tX=new Set(["width","height","top","left","right","bottom",...v]);function tK(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...u}=t;r&&(o=r);let c=[],p=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let r=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||p&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(p,t))continue;let l={delay:n,...R(o||{},t)},a=r.get();if(void 0!==a&&!r.isAnimating&&!Array.isArray(i)&&i===a&&!l.velocity)continue;let s=!1;if(window.MotionHandoffAnimation){let n=e.props[g];if(n){let e=window.MotionHandoffAnimation(n,t,A.Gt);null!==e&&(l.startTime=e,s=!0)}}h(e,t),r.start(tY(t,r,i,e.shouldReduceMotion&&tX.has(t)?{type:!1}:l,e,s));let d=r.animation;d&&c.push(d)}return s&&Promise.all(c).then(()=>{A.Gt.update(()=>{s&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=l(e,t)||{};for(let t in i={...i,...n}){var o;let n=a(o=i[t])?o[o.length-1]||0:o;e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,(0,d.OQ)(n))}}(e,s)})}),c}function tZ(e,t,n={}){let r=l(e,t,"exit"===n.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let o=r?()=>Promise.all(tK(e,r,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:o=0,staggerChildren:l,staggerDirection:a}=i;return function(e,t,n=0,r=0,i=1,o){let l=[],a=(e.variantChildren.size-1)*r,s=1===i?(e=0)=>e*r:(e=0)=>a-e*r;return Array.from(e.variantChildren).sort(tQ).forEach((e,r)=>{e.notify("AnimationStart",t),l.push(tZ(e,t,{...o,delay:n+s(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(l)}(e,t,o+r,l,a,n)}:()=>Promise.resolve(),{when:s}=i;if(!s)return Promise.all([o(),a(n.delay)]);{let[e,t]="beforeChildren"===s?[o,a]:[a,o];return e().then(()=>t())}}function tQ(e,t){return e.sortNodePosition(t)}function tJ(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function t0(e){return"string"==typeof e||Array.isArray(e)}let t1=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],t2=["initial",...t1],t5=t2.length,t4=[...t1].reverse(),t3=t1.length;function t7(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function t8(){return{animate:t7(!0),whileInView:t7(),whileHover:t7(),whileTap:t7(),whileDrag:t7(),whileFocus:t7(),exit:t7()}}class t9{constructor(e){this.isMounted=!1,this.node=e}update(){}}class t6 extends t9{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>tZ(e,t,n)));else if("string"==typeof t)r=tZ(e,t,n);else{let i="function"==typeof t?l(e,t,n.custom):t;r=Promise.all(tK(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=t8(),i=!0,o=t=>(n,r)=>{let i=l(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...r}=i;n={...n,...r,...t}}return n};function s(s){let{props:u}=e,d=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<t5;e++){let r=t2[e],i=t.props[r];(t0(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},c=[],p=new Set,h={},f=1/0;for(let t=0;t<t3;t++){var g,m;let l=t4[t],v=n[l],y=void 0!==u[l]?u[l]:d[l],b=t0(y),w=l===s?v.isActive:null;!1===w&&(f=t);let x=y===d[l]&&y!==u[l]&&b;if(x&&i&&e.manuallyAnimateOnMount&&(x=!1),v.protectedKeys={...h},!v.isActive&&null===w||!y&&!v.prevProp||r(y)||"boolean"==typeof y)continue;let S=(g=v.prevProp,"string"==typeof(m=y)?m!==g:!!Array.isArray(m)&&!tJ(m,g)),C=S||l===s&&v.isActive&&!x&&b||t>f&&b,R=!1,P=Array.isArray(y)?y:[y],E=P.reduce(o(l),{});!1===w&&(E={});let{prevResolvedValues:A={}}=v,D={...A,...E},I=t=>{C=!0,p.has(t)&&(R=!0,p.delete(t)),v.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in D){let t=E[e],n=A[e];if(h.hasOwnProperty(e))continue;let r=!1;(a(t)&&a(n)?tJ(t,n):t===n)?void 0!==t&&p.has(e)?I(e):v.protectedKeys[e]=!0:null!=t?I(e):p.add(e)}v.prevProp=y,v.prevResolvedValues=E,v.isActive&&(h={...h,...E}),i&&e.blockInitialAnimation&&(C=!1);let M=!(x&&S)||R;C&&M&&c.push(...P.map(e=>({animation:e,options:{type:l}})))}if(p.size){let t={};if("boolean"!=typeof u.initial){let n=l(e,Array.isArray(u.initial)?u.initial[0]:u.initial);n&&n.transition&&(t.transition=n.transition)}p.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=r??null}),c.push({animation:t})}let v=!!c.length;return i&&(!1===u.initial||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(v=!1),i=!1,v?t(c):Promise.resolve()}return{animateChanges:s,setActive:function(t,r){if(n[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),n[t].isActive=r;let i=s(t);for(let e in n)n[e].protectedKeys={};return i},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=t8(),i=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();r(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ne=0;class nt extends t9{constructor(){super(...arguments),this.id=ne++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}function nn(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}let nr=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function ni(e){return{point:{x:e.pageX,y:e.pageY}}}let no=e=>t=>nr(t)&&e(t,ni(t));function nl(e,t,n,r){return nn(e,t,no(n),r)}function na({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function ns(e){return e.max-e.min}function nu(e,t,n,r=.5){e.origin=r,e.originPoint=ef(t.min,t.max,e.origin),e.scale=ns(n)/ns(t),e.translate=ef(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function nd(e,t,n,r){nu(e.x,t.x,n.x,r?r.originX:void 0),nu(e.y,t.y,n.y,r?r.originY:void 0)}function nc(e,t,n){e.min=n.min+t.min,e.max=e.min+ns(t)}function np(e,t,n){e.min=t.min-n.min,e.max=e.min+ns(t)}function nh(e,t,n){np(e.x,t.x,n.x),np(e.y,t.y,n.y)}let nf=()=>({translate:0,scale:1,origin:0,originPoint:0}),ng=()=>({x:nf(),y:nf()}),nm=()=>({min:0,max:0}),nv=()=>({x:nm(),y:nm()});function ny(e){return[e("x"),e("y")]}function nb(e){return void 0===e||1===e}function nw({scale:e,scaleX:t,scaleY:n}){return!nb(e)||!nb(t)||!nb(n)}function nx(e){return nw(e)||nS(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function nS(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function nC(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function nR(e,t=0,n=1,r,i){e.min=nC(e.min,t,n,r,i),e.max=nC(e.max,t,n,r,i)}function nP(e,{x:t,y:n}){nR(e.x,t.translate,t.scale,t.originPoint),nR(e.y,n.translate,n.scale,n.originPoint)}function nE(e,t){e.min=e.min+t,e.max=e.max+t}function nA(e,t,n,r,i=.5){let o=ef(e.min,e.max,i);nR(e,t,n,o,r)}function nD(e,t){nA(e.x,t.x,t.scaleX,t.scale,t.originX),nA(e.y,t.y,t.scaleY,t.scale,t.originY)}function nI(e,t){return na(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let nM=({current:e})=>e?e.ownerDocument.defaultView:null;function nT(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let nk=(e,t)=>Math.abs(e-t);class nL{constructor(e,t,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=nN(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(nk(e.x,t.x)**2+nk(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=A.uv;this.history.push({...r,timestamp:i});let{onStart:o,onMove:l}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=nF(t,this.transformPagePoint),A.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=nN("pointercancel"===e.type?this.lastMoveEventInfo:nF(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),r&&r(e,o)},!nr(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;let o=nF(ni(e),this.transformPagePoint),{point:l}=o,{timestamp:a}=A.uv;this.history=[{...l,timestamp:a}];let{onSessionStart:s}=t;s&&s(e,nN(o,this.history)),this.removeListeners=eR(nl(this.contextWindow,"pointermove",this.handlePointerMove),nl(this.contextWindow,"pointerup",this.handlePointerUp),nl(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),(0,A.WG)(this.updatePoint)}}function nF(e,t){return t?{point:t(e.point)}:e}function nO(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nN({point:e},t){return{point:e,delta:nO(e,nV(t)),offset:nO(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=nV(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>P(.1)));)n--;if(!r)return{x:0,y:0};let o=E(i.timestamp-r.timestamp);if(0===o)return{x:0,y:0};let l={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return l.x===1/0&&(l.x=0),l.y===1/0&&(l.y=0),l}(t,.1)}}function nV(e){return e[e.length-1]}function nj(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function nB(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function nG(e,t,n){return{min:n_(e,t),max:n_(e,n)}}function n_(e,t){return"number"==typeof e?e:e[t]||0}let nz={x:!1,y:!1},nH=new WeakMap;class n${constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nv(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new nL(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ni(e).point)},onStart:(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(nz[e])return null;else return nz[e]=!0,()=>{nz[e]=!1};return nz.x||nz.y?null:(nz.x=nz.y=!0,()=>{nz.x=nz.y=!1})}(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ny(e=>{let t=this.getAxisMotionValue(e).get()||0;if(K.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=ns(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&A.Gt.postRender(()=>i(e,t)),h(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:l}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(l),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,l),this.updateAxis("y",t.point,l),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>ny(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:nM(this.visualElement)})}stop(e,t){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:i}=this.getProps();i&&A.Gt.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!nU(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?ef(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?ef(n,e,r.max):Math.min(e,n)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&nT(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:nj(e.x,n,i),y:nj(e.y,t,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:nG(e,"left","right"),y:nG(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&ny(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!nT(t))return!1;let r=t.current;em(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,n){let r=nI(e,n),{scroll:i}=t;return i&&(nE(r.x,i.offset.x),nE(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),l=(e=i.layout.layoutBox,{x:nB(e.x,o.x),y:nB(e.y,o.y)});if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(l));this.hasMutatedConstraints=!!e,e&&(l=na(e))}return l}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{};return Promise.all(ny(l=>{if(!nU(l,t,this.currentDirection))return;let s=a&&a[l]||{};o&&(s={min:0,max:0});let u={type:"inertia",velocity:n?e[l]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...s};return this.startAxisValueAnimation(l,u)})).then(l)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return h(this.visualElement,e),n.start(tY(e,n,0,t,this.visualElement,!1))}stopAnimation(){ny(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){ny(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){ny(t=>{let{drag:n}=this.getProps();if(!nU(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:o}=r.layout.layoutBox[t];i.set(e[t]-ef(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!nT(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};ny(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=ns(e),i=ns(t);return i>r?n=e$(t.min,t.max-r,e.min):r>i&&(n=e$(e.min,e.max-i,t.min)),O(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),ny(t=>{if(!nU(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];n.set(ef(i,o,r[t]))})}addListeners(){if(!this.visualElement.current)return;nH.set(this.visualElement,this);let e=nl(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();nT(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),A.Gt.read(t);let i=nn(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(ny(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:l=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:l}}}function nU(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class nW extends t9{constructor(e){super(e),this.removeGroupControls=eH.l,this.removeListeners=eH.l,this.controls=new n$(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||eH.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let nq=e=>(t,n)=>{e&&A.Gt.postRender(()=>e(t,n))};class nY extends t9{constructor(){super(...arguments),this.removePointerDownListener=eH.l}onPointerDown(e){this.session=new nL(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nM(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:nq(e),onStart:nq(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&A.Gt.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=nl(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var nX=n(84464),nK=n(63185);let nZ=(0,nK.createContext)(null),nQ=(0,nK.createContext)({}),nJ=(0,nK.createContext)({}),n0={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function n1(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let n2={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!Z.test(e))return e;else e=parseFloat(e);let n=n1(e,t.target.x),r=n1(e,t.target.y);return`${n}% ${r}%`}},n5={},{schedule:n4}=(0,n(69465).I)(queueMicrotask,!1);class n3 extends nK.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;for(let e in n8)n5[e]=n8[e],T(e)&&(n5[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),n0.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:o}=n;return o&&(o.isPresent=i,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||A.Gt.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),n4.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function n7(e){let[t,n]=function(e=!0){let t=(0,nK.useContext)(nZ);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:r,register:i}=t,o=(0,nK.useId)();(0,nK.useEffect)(()=>{if(e)return i(o)},[e]);let l=(0,nK.useCallback)(()=>e&&r&&r(o),[o,r,e]);return!n&&r?[!1,l]:[!0]}(),r=(0,nK.useContext)(nQ);return(0,nX.jsx)(n3,{...e,layoutGroup:r,switchLayoutGroup:(0,nK.useContext)(nJ),isPresent:t,safeToRemove:n})}let n8={borderRadius:{...n2,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:n2,borderTopRightRadius:n2,borderBottomLeftRadius:n2,borderBottomRightRadius:n2,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=ec.parse(e);if(r.length>5)return e;let i=ec.createTransformer(e),o=+("number"!=typeof r[0]),l=n.x.scale*t.x,a=n.y.scale*t.y;r[0+o]/=l,r[1+o]/=a;let s=ef(l,a,.5);return"number"==typeof r[2+o]&&(r[2+o]/=s),"number"==typeof r[3+o]&&(r[3+o]/=s),i(r)}}},n9=(e,t)=>e.depth-t.depth;var n6=n(65717);class re{constructor(){this.children=[],this.isDirty=!1}add(e){(0,n6.Kq)(this.children,e),this.isDirty=!0}remove(e){(0,n6.Ai)(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(n9),this.isDirty=!1,this.children.forEach(e)}}function rt(e){return(0,c.S)(e)?e.get():e}let rn=["TopLeft","TopRight","BottomLeft","BottomRight"],rr=rn.length,ri=e=>"string"==typeof e?parseFloat(e):e,ro=e=>"number"==typeof e||Z.test(e);function rl(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let ra=ru(0,.5,e4),rs=ru(.5,.95,eH.l);function ru(e,t,n){return r=>r<e?0:r>t?1:n(e$(e,t,r))}function rd(e,t){e.min=t.min,e.max=t.max}function rc(e,t){rd(e.x,t.x),rd(e.y,t.y)}function rp(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rh(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function rf(e,t,[n,r,i],o,l){!function(e,t=0,n=1,r=.5,i,o=e,l=e){if(K.test(t)&&(t=parseFloat(t),t=ef(l.min,l.max,t/100)-l.min),"number"!=typeof t)return;let a=ef(o.min,o.max,r);e===o&&(a-=t),e.min=rh(e.min,t,n,a,i),e.max=rh(e.max,t,n,a,i)}(e,t[n],t[r],t[i],t.scale,o,l)}let rg=["x","scaleX","originX"],rm=["y","scaleY","originY"];function rv(e,t,n,r){rf(e.x,t,rg,n?n.x:void 0,r?r.x:void 0),rf(e.y,t,rm,n?n.y:void 0,r?r.y:void 0)}function ry(e){return 0===e.translate&&1===e.scale}function rb(e){return ry(e.x)&&ry(e.y)}function rw(e,t){return e.min===t.min&&e.max===t.max}function rx(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rS(e,t){return rx(e.x,t.x)&&rx(e.y,t.y)}function rC(e){return ns(e.x)/ns(e.y)}function rR(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rP{constructor(){this.members=[]}add(e){(0,n6.Kq)(this.members,e),e.scheduleRender()}remove(e){if((0,n6.Ai)(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}var rE=n(84311);function rA(e){return tH(e)&&"ownerSVGElement"in e}let rD={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rI=["","X","Y","Z"],rM={visibility:"hidden"},rT=0;function rk(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function rL({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=t?.()){this.id=rT++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,tL.Q.value&&(rD.nodes=rD.calculatedTargetDeltas=rD.calculatedProjections=0),this.nodes.forEach(rN),this.nodes.forEach(rH),this.nodes.forEach(r$),this.nodes.forEach(rV),tL.Q.addProjectionMetrics&&tL.Q.addProjectionMetrics(rD)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new re)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new rE.v),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=rA(t)&&!(rA(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),e){let n,r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=D.k.now(),r=({timestamp:i})=>{let o=i-n;o>=250&&((0,A.WG)(r),e(o-t))};return A.Gt.setup(r,!0),()=>(0,A.WG)(r)}(r,250),n0.hasAnimatedSinceResize&&(n0.hasAnimatedSinceResize=!1,this.nodes.forEach(rz))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||rK,{onLayoutAnimationStart:l,onLayoutAnimationComplete:a}=i.getProps(),s=!this.targetLayout||!rS(this.targetLayout,r),u=!t&&n;if(this.options.layoutRoot||this.resumeFrom||u||t&&(s||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...R(o,"layout"),onPlay:l,onComplete:a};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,u)}else t||rz(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,A.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rU),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[g];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",A.Gt,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rB);return}this.isUpdating||this.nodes.forEach(rG),this.isUpdating=!1,this.nodes.forEach(r_),this.nodes.forEach(rF),this.nodes.forEach(rO),this.clearAllSnapshots();let e=D.k.now();A.uv.delta=O(0,1e3/60,e-A.uv.timestamp),A.uv.timestamp=e,A.uv.isProcessing=!0,A.PP.update.process(A.uv),A.PP.preRender.process(A.uv),A.PP.render.process(A.uv),A.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,n4.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rj),this.sharedNodes.forEach(rW)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,A.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){A.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||ns(this.snapshot.measuredBox.x)||ns(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nv(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rb(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,o=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||nx(this.latestValues)||o)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),rJ((t=r).x),rJ(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return nv();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(r1))){let{scroll:e}=this.root;e&&(nE(t.x,e.offset.x),nE(t.y,e.offset.y))}return t}removeElementScroll(e){let t=nv();if(rc(t,e),this.scroll?.wasRoot)return t;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:o}=r;r!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&rc(t,e),nE(t.x,i.offset.x),nE(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let n=nv();rc(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&nD(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),nx(r.latestValues)&&nD(n,r.latestValues)}return nx(this.latestValues)&&nD(n,this.latestValues),n}removeTransform(e){let t=nv();rc(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!nx(n.latestValues))continue;nw(n.latestValues)&&n.updateSnapshot();let r=nv();rc(r,n.measurePageBox()),rv(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return nx(this.latestValues)&&rv(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==A.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==t;if(!(e||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:i}=this.options;if(this.layout&&(r||i)){if(this.resolvedRelativeTargetAt=A.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nv(),this.relativeTargetOrigin=nv(),nh(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rc(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=nv(),this.targetWithTransforms=nv()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,l,a;this.forceRelativeParentToResolveTarget(),o=this.target,l=this.relativeTarget,a=this.relativeParent.target,nc(o.x,l.x,a.x),nc(o.y,l.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rc(this.target,this.layout.layoutBox),nP(this.target,this.targetDelta)):rc(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nv(),this.relativeTargetOrigin=nv(),nh(this.relativeTargetOrigin,this.target,e.target),rc(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}tL.Q.value&&rD.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||nw(this.parent.latestValues)||nS(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===A.uv.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;rc(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,l=this.treeScale.y;!function(e,t,n,r=!1){let i,o,l=n.length;if(l){t.x=t.y=1;for(let a=0;a<l;a++){o=(i=n[a]).projectionDelta;let{visualElement:l}=i.options;(!l||!l.props.style||"contents"!==l.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&nD(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,nP(e,o)),r&&nx(i.latestValues)&&nD(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=nv());let{target:a}=e;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rp(this.prevProjectionDelta.x,this.projectionDelta.x),rp(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nd(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===l&&rR(this.projectionDelta.x,this.prevProjectionDelta.x)&&rR(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),tL.Q.value&&rD.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ng(),this.projectionDelta=ng(),this.projectionDeltaWithTransform=ng()}setAnimationOrigin(e,t=!1){let n,r=this.snapshot,i=r?r.latestValues:{},o={...this.latestValues},l=ng();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=nv(),s=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),d=!u||u.members.length<=1,c=!!(s&&!d&&!0===this.options.crossfade&&!this.path.some(rX));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(rq(l.x,e.x,r),rq(l.y,e.y,r),this.setTargetDelta(l),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,p,h,f,g,m;nh(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),h=this.relativeTarget,f=this.relativeTargetOrigin,g=a,m=r,rY(h.x,f.x,g.x,m),rY(h.y,f.y,g.y,m),n&&(u=this.relativeTarget,p=n,rw(u.x,p.x)&&rw(u.y,p.y))&&(this.isProjectionDirty=!1),n||(n=nv()),rc(n,this.relativeTarget)}s&&(this.animationValues=o,function(e,t,n,r,i,o){i?(e.opacity=ef(0,n.opacity??1,ra(r)),e.opacityExit=ef(t.opacity??1,0,rs(r))):o&&(e.opacity=ef(t.opacity??1,n.opacity??1,r));for(let i=0;i<rr;i++){let o=`border${rn[i]}Radius`,l=rl(t,o),a=rl(n,o);(void 0!==l||void 0!==a)&&(l||(l=0),a||(a=0),0===l||0===a||ro(l)===ro(a)?(e[o]=Math.max(ef(ri(l),ri(a),r),0),(K.test(a)||K.test(l))&&(e[o]+="%")):e[o]=a)}(t.rotate||n.rotate)&&(e.rotate=ef(t.rotate||0,n.rotate||0,r))}(o,i,this.latestValues,r,c,d)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,A.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=A.Gt.update(()=>{n0.hasAnimatedSinceResize=!0,I.layout++,this.motionValue||(this.motionValue=(0,d.OQ)(0)),this.currentAnimation=function(e,t,n){let r=(0,c.S)(e)?e:(0,d.OQ)(e);return r.start(tY("",r,t,n)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{I.layout--},onComplete:()=>{I.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&r0(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||nv();let t=ns(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=ns(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}rc(t,n),nD(t,i),nd(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rP),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&rk("z",e,r,this.animationValues);for(let t=0;t<rI.length;t++)rk(`rotate${rI[t]}`,e,r,this.animationValues),rk(`skew${rI[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return rM;let t={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=rt(e?.pointerEvents)||"",t.transform=n?n(this.latestValues,""):"none",t;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=rt(e?.pointerEvents)||""),this.hasProjected&&!nx(this.latestValues)&&(t.transform=n?n({},""):"none",this.hasProjected=!1),t}let i=r.animationValues||r.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,n){let r="",i=e.x.translate/t.x,o=e.y.translate/t.y,l=n?.z||0;if((i||o||l)&&(r=`translate3d(${i}px, ${o}px, ${l}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:l,skewY:a}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),o&&(r+=`rotateY(${o}deg) `),l&&(r+=`skewX(${l}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=e.x.scale*t.x,s=e.y.scale*t.y;return(1!==a||1!==s)&&(r+=`scale(${a}, ${s})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i),n&&(t.transform=n(i,t.transform));let{x:o,y:l}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*l.origin}% 0`,r.animationValues?t.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:t.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,n5){if(void 0===i[e])continue;let{correct:n,applyTo:o,isCSSVariable:l}=n5[e],a="none"===t.transform?i[e]:n(i[e],r);if(o){let e=o.length;for(let n=0;n<e;n++)t[o[n]]=a}else l?this.options.visualElement.renderState.vars[e]=a:t[e]=a}return this.options.layoutId&&(t.pointerEvents=r===this?rt(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(rB),this.root.sharedNodes.clear()}}}function rF(e){e.updateLayout()}function rO(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:r}=e.layout,{animationType:i}=e.options,o=t.source!==e.layout.source;"size"===i?ny(e=>{let r=o?t.measuredBox[e]:t.layoutBox[e],i=ns(r);r.min=n[e].min,r.max=r.min+i}):r0(i,t.layoutBox,n)&&ny(r=>{let i=o?t.measuredBox[r]:t.layoutBox[r],l=ns(n[r]);i.max=i.min+l,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+l)});let l=ng();nd(l,n,t.layoutBox);let a=ng();o?nd(a,e.applyTransform(r,!0),t.measuredBox):nd(a,n,t.layoutBox);let s=!rb(l),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:o}=r;if(i&&o){let l=nv();nh(l,t.layoutBox,i.layoutBox);let a=nv();nh(a,n,o.layoutBox),rS(l,a)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=l,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:a,layoutDelta:l,hasLayoutChanged:s,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rN(e){tL.Q.value&&rD.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rV(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rj(e){e.clearSnapshot()}function rB(e){e.clearMeasurements()}function rG(e){e.isLayoutDirty=!1}function r_(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rz(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rH(e){e.resolveTargetDelta()}function r$(e){e.calcProjection()}function rU(e){e.resetSkewAndRotation()}function rW(e){e.removeLeadSnapshot()}function rq(e,t,n){e.translate=ef(t.translate,0,n),e.scale=ef(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function rY(e,t,n,r){e.min=ef(t.min,n.min,r),e.max=ef(t.max,n.max,r)}function rX(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let rK={duration:.45,ease:[.4,0,.1,1]},rZ=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),rQ=rZ("applewebkit/")&&!rZ("chrome/")?Math.round:eH.l;function rJ(e){e.min=rQ(e.min),e.max=rQ(e.max)}function r0(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rC(t)-rC(n)))}function r1(e){return e!==e.root&&e.scroll?.wasRoot}let r2=rL({attachResizeListener:(e,t)=>nn(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),r5={current:void 0},r4=rL({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!r5.current){let e=new r2({});e.mount(window),e.setOptions({layoutScroll:!0}),r5.current=e}return r5.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function r3(e,t){let n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,n=(void 0)??t.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function r7(e){return!("touch"===e.pointerType||nz.x||nz.y)}function r8(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&A.Gt.postRender(()=>i(t,ni(t)))}class r9 extends t9{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=r3(e,n),l=e=>{if(!r7(e))return;let{target:n}=e,r=t(n,e);if("function"!=typeof r||!n)return;let o=e=>{r7(e)&&(r(e),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,i)};return r.forEach(e=>{e.addEventListener("pointerenter",l,i)}),o}(e,(e,t)=>(r8(this.node,t,"Start"),e=>r8(this.node,e,"End"))))}unmount(){}}class r6 extends t9{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eR(nn(this.node.current,"focus",()=>this.onFocus()),nn(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let ie=(e,t)=>!!t&&(e===t||ie(e,t.parentElement)),it=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ir=new WeakSet;function ii(e){return t=>{"Enter"===t.key&&e(t)}}function io(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let il=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=ii(()=>{if(ir.has(n))return;io(n,"down");let e=ii(()=>{io(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>io(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function ia(e){return nr(e)&&!(nz.x||nz.y)}function is(e,t,n){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&A.Gt.postRender(()=>i(t,ni(t)))}class iu extends t9{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=r3(e,n),l=e=>{let r=e.currentTarget;if(!ia(e))return;ir.add(r);let o=t(r,e),l=(e,t)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",s),ir.has(r)&&ir.delete(r),ia(e)&&"function"==typeof o&&o(e,{success:t})},a=e=>{l(e,r===window||r===document||n.useGlobalTarget||ie(r,e.target))},s=e=>{l(e,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",s,i)};return r.forEach(e=>{((n.useGlobalTarget?window:e).addEventListener("pointerdown",l,i),t$(e))&&(e.addEventListener("focus",e=>il(e,i)),it.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),o}(e,(e,t)=>(is(this.node,t,"Start"),(e,{success:t})=>is(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let id=new WeakMap,ic=new WeakMap,ip=e=>{let t=id.get(e.target);t&&t(e)},ih=e=>{e.forEach(ip)},ig={some:0,all:1};class im extends t9{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:ig[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;ic.has(n)||ic.set(n,{});let r=ic.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(ih,{root:e,...t})),r[i]}(t);return id.set(e,n),r.observe(e),()=>{id.delete(e),r.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=t?n:r;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let iv=(0,nK.createContext)({strict:!1});var iy=n(61477);let ib=(0,nK.createContext)({});function iw(e){return r(e.animate)||t2.some(t=>t0(e[t]))}function ix(e){return!!(iw(e)||e.variants)}function iS(e){return Array.isArray(e)?e.join(" "):e}var iC=n(57925);let iR={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iP={};for(let e in iR)iP[e]={isEnabled:t=>iR[e].some(e=>!!t[e])};let iE=Symbol.for("motionComponentSymbol");var iA=n(53859);function iD(e,{layout:t,layoutId:n}){return y.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!n5[e]||"opacity"===e)}let iI=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iM={...N,transform:Math.round},iT={borderWidth:Z,borderTopWidth:Z,borderRightWidth:Z,borderBottomWidth:Z,borderLeftWidth:Z,borderRadius:Z,radius:Z,borderTopLeftRadius:Z,borderTopRightRadius:Z,borderBottomRightRadius:Z,borderBottomLeftRadius:Z,width:Z,maxWidth:Z,height:Z,maxHeight:Z,top:Z,right:Z,bottom:Z,left:Z,padding:Z,paddingTop:Z,paddingRight:Z,paddingBottom:Z,paddingLeft:Z,margin:Z,marginTop:Z,marginRight:Z,marginBottom:Z,marginLeft:Z,backgroundPositionX:Z,backgroundPositionY:Z,rotate:X,rotateX:X,rotateY:X,rotateZ:X,scale:j,scaleX:j,scaleY:j,scaleZ:j,skew:X,skewX:X,skewY:X,distance:Z,translateX:Z,translateY:Z,translateZ:Z,x:Z,y:Z,z:Z,perspective:Z,transformPerspective:Z,opacity:V,originX:ee,originY:ee,originZ:Z,zIndex:iM,fillOpacity:V,strokeOpacity:V,numOctaves:iM},ik={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iL=v.length;function iF(e,t,n){let{style:r,vars:i,transformOrigin:o}=e,l=!1,a=!1;for(let e in t){let n=t[e];if(y.has(e)){l=!0;continue}if(T(e)){i[e]=n;continue}{let t=iI(n,iT[e]);e.startsWith("origin")?(a=!0,o[e]=t):r[e]=t}}if(!t.transform&&(l||n?r.transform=function(e,t,n){let r="",i=!0;for(let o=0;o<iL;o++){let l=v[o],a=e[l];if(void 0===a)continue;let s=!0;if(!(s="number"==typeof a?a===+!!l.startsWith("scale"):0===parseFloat(a))||n){let e=iI(a,iT[l]);if(!s){i=!1;let t=ik[l]||l;r+=`${t}(${e}) `}n&&(t[l]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),a){let{originX:e="50%",originY:t="50%",originZ:n=0}=o;r.transformOrigin=`${e} ${t} ${n}`}}let iO=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iN(e,t,n){for(let r in t)(0,c.S)(t[r])||iD(r,n)||(e[r]=t[r])}let iV={offset:"stroke-dashoffset",array:"stroke-dasharray"},ij={offset:"strokeDashoffset",array:"strokeDasharray"};function iB(e,{attrX:t,attrY:n,attrScale:r,pathLength:i,pathSpacing:o=1,pathOffset:l=0,...a},s,u,d){if(iF(e,a,u),s){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:c,style:p}=e;c.transform&&(p.transform=c.transform,delete c.transform),(p.transform||c.transformOrigin)&&(p.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),p.transform&&(p.transformBox=d?.transformBox??"fill-box",delete c.transformBox),void 0!==t&&(c.x=t),void 0!==n&&(c.y=n),void 0!==r&&(c.scale=r),void 0!==i&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let o=i?iV:ij;e[o.offset]=Z.transform(-r);let l=Z.transform(t),a=Z.transform(n);e[o.array]=`${l} ${a}`}(c,i,o,l,!1)}let iG=()=>({...iO(),attrs:{}}),i_=e=>"string"==typeof e&&"svg"===e.toLowerCase(),iz=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function iH(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||iz.has(e)}let i$=e=>!iH(e);try{!function(e){e&&(i$=t=>t.startsWith("on")?!iH(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let iU=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function iW(e){if("string"!=typeof e||e.includes("-"));else if(iU.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var iq=n(89156);let iY=e=>(t,n)=>{let i=(0,nK.useContext)(ib),l=(0,nK.useContext)(nZ),a=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},n,i,l){return{latestValues:function(e,t,n,i){let l={},a=i(e,{});for(let e in a)l[e]=rt(a[e]);let{initial:s,animate:u}=e,d=iw(e),c=ix(e);t&&c&&!d&&!1!==e.inherit&&(void 0===s&&(s=t.initial),void 0===u&&(u=t.animate));let p=!!n&&!1===n.initial,h=(p=p||!1===s)?u:s;if(h&&"boolean"!=typeof h&&!r(h)){let t=Array.isArray(h)?h:[h];for(let n=0;n<t.length;n++){let r=o(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=p?t.length-1:0;t=t[e]}null!==t&&(l[e]=t)}for(let t in e)l[t]=e[t]}}}return l}(n,i,l,e),renderState:t()}})(e,t,i,l);return n?a():(0,iq.M)(a)};function iX(e,t,n){let{style:r}=e,i={};for(let o in r)((0,c.S)(r[o])||t.style&&(0,c.S)(t.style[o])||iD(o,e)||n?.getValue(o)?.liveStyle!==void 0)&&(i[o]=r[o]);return i}let iK={useVisualState:iY({scrapeMotionValuesFromProps:iX,createRenderState:iO})};function iZ(e,t,n){let r=iX(e,t,n);for(let n in e)((0,c.S)(e[n])||(0,c.S)(t[n]))&&(r[-1!==v.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}let iQ={useVisualState:iY({scrapeMotionValuesFromProps:iZ,createRenderState:iG})},iJ={current:null},i0={current:!1},i1=new WeakMap,i2=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),i5=e=>/^0[^.\s]+$/u.test(e),i4=e=>t=>t.test(e),i3=[N,Z,K,X,J,Q,{test:e=>"auto"===e,parse:e=>e}],i7=e=>i3.find(i4(e)),i8=[...i3,en,ec],i9=e=>i8.find(i4(e)),i6=new Set(["brightness","contrast","saturate","opacity"]);function oe(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(G)||[];if(!r)return e;let i=n.replace(r,""),o=+!!i6.has(t);return r!==n&&(o*=100),t+"("+o+i+")"}let ot=/\b([a-z-]*)\(.*?\)/gu,on={...ec,getAnimatableNone:e=>{let t=e.match(ot);return t?t.map(oe).join(" "):e}},or={...iT,color:en,backgroundColor:en,outlineColor:en,fill:en,stroke:en,borderColor:en,borderTopColor:en,borderRightColor:en,borderBottomColor:en,borderLeftColor:en,filter:on,WebkitFilter:on},oi=e=>or[e];function oo(e,t){let n=oi(e);return n!==on&&(n=ec),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let ol=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class oa{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:o},l={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tI,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=D.k.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,A.Gt.render(this.render,!1,!0))};let{latestValues:a,renderState:s}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=s,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=l,this.blockInitialAnimation=!!i,this.isControllingVariants=iw(t),this.isVariantNode=ix(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...d}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in d){let t=d[e];void 0!==a[e]&&(0,c.S)(t)&&t.set(a[e],!1)}}mount(e){this.current=e,i1.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),i0.current||function(){if(i0.current=!0,iC.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>iJ.current=e.matches;e.addListener(t),t()}else iJ.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||iJ.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),(0,A.WG)(this.notifyUpdate),(0,A.WG)(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=y.has(e);r&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&A.Gt.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),o(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iP){let t=iP[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nv()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<ol.length;t++){let n=ol[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],o=n[r];if((0,c.S)(i))e.addValue(r,i);else if((0,c.S)(o))e.addValue(r,(0,d.OQ)(i,{owner:e}));else if(o!==i)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,(0,d.OQ)(void 0!==t?t:i,{owner:e}))}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=(0,d.OQ)(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){let n=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=n&&("string"==typeof n&&(i2(n)||i5(n))?n=parseFloat(n):!i9(n)&&ec.test(t)&&(n=oo(e,t)),this.setBaseTarget(e,(0,c.S)(n)?n.get():n)),(0,c.S)(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=o(this.props,n,this.presenceContext?.custom);r&&(t=r[e])}if(n&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||(0,c.S)(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new rE.v),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}let os=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ou=new Set(["auto","none","0"]);class od extends tI{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&L(r=r.trim())){let i=function e(t,n,r=1){em(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,o]=function(e){let t=os.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${n??r}`,i]}(t);if(!i)return;let l=window.getComputedStyle(n).getPropertyValue(i);if(l){let e=l.trim();return i2(e)?parseFloat(e):e}return L(o)?e(o,n,r+1):o}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!tX.has(n)||2!==e.length)return;let[r,i]=e,o=i7(r),l=i7(i);if(o!==l)if(tb(o)&&tb(l))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else tS[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||i5(r)))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!ou.has(t)&&ea(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=oo(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tS[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,o=n[i];n[i]=tS[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}class oc extends oa{constructor(){super(...arguments),this.KeyframeResolver=od}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;(0,c.S)(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function op(e,{style:t,vars:n},r,i){for(let o in Object.assign(e.style,t,i&&i.getProjectionStyles(r)),n)e.style.setProperty(o,n[o])}class oh extends oc{constructor(){super(...arguments),this.type="html",this.renderInstance=op}readValueFromInstance(e,t){if(y.has(t))return this.projection?.isProjecting?tg(t):tv(e,t);{let n=window.getComputedStyle(e),r=(T(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return nI(e,t)}build(e,t,n){iF(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return iX(e,t,n)}}let of=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class og extends oc{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=nv}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(y.has(t)){let e=oi(t);return e&&e.default||0}return t=of.has(t)?t:f(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return iZ(e,t,n)}build(e,t,n){iB(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){for(let n in op(e,t,void 0,r),t.attrs)e.setAttribute(of.has(n)?n:f(n),t.attrs[n])}mount(e){this.isSVGTag=i_(e.tagName),super.mount(e)}}let om=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(n,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((s={animation:{Feature:t6},exit:{Feature:nt},inView:{Feature:im},tap:{Feature:iu},focus:{Feature:r6},hover:{Feature:r9},pan:{Feature:nY},drag:{Feature:nW,ProjectionNode:r4,MeasureLayout:n7},layout:{ProjectionNode:r4,MeasureLayout:n7}},u=(e,t)=>iW(e)?new og(t):new oh(t,{allowProjection:e!==nK.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){function o(e,o){var l,a,s;let u,d={...(0,nK.useContext)(iy.Q),...e,layoutId:function({layoutId:e}){let t=(0,nK.useContext)(nQ).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:c}=d,p=function(e){let{initial:t,animate:n}=function(e,t){if(iw(e)){let{initial:t,animate:n}=e;return{initial:!1===t||t0(t)?t:void 0,animate:t0(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,nK.useContext)(ib));return(0,nK.useMemo)(()=>({initial:t,animate:n}),[iS(t),iS(n)])}(e),h=r(e,c);if(!c&&iC.B){a=0,s=0,(0,nK.useContext)(iv).strict;let e=function(e){let{drag:t,layout:n}=iP;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:t?.isEnabled(e)||n?.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(d);u=e.MeasureLayout,p.visualElement=function(e,t,n,r,i){let{visualElement:o}=(0,nK.useContext)(ib),l=(0,nK.useContext)(iv),a=(0,nK.useContext)(nZ),s=(0,nK.useContext)(iy.Q).reducedMotion,u=(0,nK.useRef)(null);r=r||l.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:o,props:n,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:s}));let d=u.current,c=(0,nK.useContext)(nJ);d&&!d.projection&&i&&("html"===d.type||"svg"===d.type)&&function(e,t,n,r){let{layoutId:i,layout:o,drag:l,dragConstraints:a,layoutScroll:s,layoutRoot:u,layoutCrossfade:d}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!l||a&&nT(a),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:r,crossfade:d,layoutScroll:s,layoutRoot:u})}(u.current,n,i,c);let p=(0,nK.useRef)(!1);(0,nK.useInsertionEffect)(()=>{d&&p.current&&d.update(n,a)});let h=n[g],f=(0,nK.useRef)(!!h&&!window.MotionHandoffIsComplete?.(h)&&window.MotionHasOptimisedAnimation?.(h));return(0,iA.E)(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),n4.render(d.render),f.current&&d.animationState&&d.animationState.animateChanges())}),(0,nK.useEffect)(()=>{d&&(!f.current&&d.animationState&&d.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(h)}),f.current=!1))}),d}(i,h,d,t,e.ProjectionNode)}return(0,nX.jsxs)(ib.Provider,{value:p,children:[u&&p.visualElement?(0,nX.jsx)(u,{visualElement:p.visualElement,...d}):null,n(i,e,(l=p.visualElement,(0,nK.useCallback)(e=>{e&&h.onMount&&h.onMount(e),l&&(e?l.mount(e):l.unmount()),o&&("function"==typeof o?o(e):nT(o)&&(o.current=e))},[l])),h,c,p.visualElement)]})}e&&function(e){for(let t in e)iP[t]={...iP[t],...e[t]}}(e),o.displayName=`motion.${"string"==typeof i?i:`create(${i.displayName??i.name??""})`}`;let l=(0,nK.forwardRef)(o);return l[iE]=i,l}({...iW(e)?iQ:iK,preloadedFeatures:s,useRender:function(e=!1){return(t,n,r,{latestValues:i},o)=>{let l=(iW(t)?function(e,t,n,r){let i=(0,nK.useMemo)(()=>{let n=iG();return iB(n,t,i_(r),e.transformTemplate,e.style),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};iN(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return iN(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,nK.useMemo)(()=>{let n=iO();return iF(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(n,i,o,t),a=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(i$(i)||!0===n&&iH(i)||!t&&!iH(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),s=t!==nK.Fragment?{...a,...l,ref:r}:{},{children:u}=n,d=(0,nK.useMemo)(()=>(0,c.S)(u)?u.get():u,[u]);return(0,nK.createElement)(t,{...s,children:d})}}(t),createVisualElement:u,Component:e})}))},42538:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},42664:(e,t,n)=>{n.d(t,{a:()=>i});var r=n(91024);function i(e,t){return(0,r.w)(t||e,e)}},43162:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},43597:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},44810:(e,t,n)=>{n.d(t,{C1:()=>P,bL:()=>R});var r=n(63185),i=n(85729),o=n(63699),l=n(19141),a=n(41505),s=n(82474),u=n(81447),d=n(80799),c=n(34245),p=n(84464),h="Checkbox",[f,g]=(0,o.A)(h),[m,v]=f(h),y=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:o,checked:s,defaultChecked:u,required:d,disabled:h,value:f="on",onCheckedChange:g,form:v,...y}=e,[b,w]=r.useState(null),R=(0,i.s)(t,e=>w(e)),P=r.useRef(!1),E=!b||v||!!b.closest("form"),[A=!1,D]=(0,a.i)({prop:s,defaultProp:u,onChange:g}),I=r.useRef(A);return r.useEffect(()=>{let e=b?.form;if(e){let t=()=>D(I.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[b,D]),(0,p.jsxs)(m,{scope:n,state:A,disabled:h,children:[(0,p.jsx)(c.sG.button,{type:"button",role:"checkbox","aria-checked":S(A)?"mixed":A,"aria-required":d,"data-state":C(A),"data-disabled":h?"":void 0,disabled:h,value:f,...y,ref:R,onKeyDown:(0,l.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(e.onClick,e=>{D(e=>!!S(e)||!e),E&&(P.current=e.isPropagationStopped(),P.current||e.stopPropagation())})}),E&&(0,p.jsx)(x,{control:b,bubbles:!P.current,name:o,value:f,checked:A,required:d,disabled:h,form:v,style:{transform:"translateX(-100%)"},defaultChecked:!S(u)&&u})]})});y.displayName=h;var b="CheckboxIndicator",w=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...i}=e,o=v(b,n);return(0,p.jsx)(d.C,{present:r||S(o.state)||!0===o.state,children:(0,p.jsx)(c.sG.span,{"data-state":C(o.state),"data-disabled":o.disabled?"":void 0,...i,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=b;var x=e=>{let{control:t,checked:n,bubbles:i=!0,defaultChecked:o,...l}=e,a=r.useRef(null),d=(0,s.Z)(n),c=(0,u.X)(t);r.useEffect(()=>{let e=a.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==n&&t){let r=new Event("click",{bubbles:i});e.indeterminate=S(n),t.call(e,!S(n)&&n),e.dispatchEvent(r)}},[d,n,i]);let h=r.useRef(!S(n)&&n);return(0,p.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:o??h.current,...l,tabIndex:-1,ref:a,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function S(e){return"indeterminate"===e}function C(e){return S(e)?"indeterminate":e?"checked":"unchecked"}var R=y,P=w},46240:(e,t,n)=>{n.d(t,{H4:()=>x,_V:()=>w,bL:()=>b});var r=n(63185),i=n(63699),o=n(57647),l=n(70505),a=n(34245),s=n(84464),u="Avatar",[d,c]=(0,i.A)(u),[p,h]=d(u),f=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...i}=e,[o,l]=r.useState("idle");return(0,s.jsx)(p,{scope:n,imageLoadingStatus:o,onImageLoadingStatusChange:l,children:(0,s.jsx)(a.sG.span,{...i,ref:t})})});f.displayName=u;var g="AvatarImage",m=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:i,onLoadingStatusChange:u=()=>{},...d}=e,c=h(g,n),p=function(e,t){let[n,i]=r.useState("idle");return(0,l.N)(()=>{if(!e)return void i("error");let n=!0,r=new window.Image,o=e=>()=>{n&&i(e)};return i("loading"),r.onload=o("loaded"),r.onerror=o("error"),r.src=e,t&&(r.referrerPolicy=t),()=>{n=!1}},[e,t]),n}(i,d.referrerPolicy),f=(0,o.c)(e=>{u(e),c.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==p&&f(p)},[p,f]),"loaded"===p?(0,s.jsx)(a.sG.img,{...d,ref:t,src:i}):null});m.displayName=g;var v="AvatarFallback",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:i,...o}=e,l=h(v,n),[u,d]=r.useState(void 0===i);return r.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>d(!0),i);return()=>window.clearTimeout(e)}},[i]),u&&"loaded"!==l.imageLoadingStatus?(0,s.jsx)(a.sG.span,{...o,ref:t}):null});y.displayName=v;var b=f,w=m,x=y},47495:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Earth",[["path",{d:"M21.54 15H17a2 2 0 0 0-2 2v4.54",key:"1djwo0"}],["path",{d:"M7 3.34V5a3 3 0 0 0 3 3v0a2 2 0 0 1 2 2v0c0 1.1.9 2 2 2v0a2 2 0 0 0 2-2v0c0-1.1.9-2 2-2h3.17",key:"1fi5u6"}],["path",{d:"M11 21.95V18a2 2 0 0 0-2-2v0a2 2 0 0 1-2-2v-1a2 2 0 0 0-2-2H2.05",key:"xsiumc"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},51058:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},52178:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},52990:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},53859:(e,t,n)=>{n.d(t,{E:()=>i});var r=n(63185);let i=n(57925).B?r.useLayoutEffect:r.useEffect},57003:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},57925:(e,t,n)=>{n.d(t,{B:()=>r});let r="undefined"!=typeof window},58154:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Route",[["circle",{cx:"6",cy:"19",r:"3",key:"1kj8tv"}],["path",{d:"M9 19h8.5a3.5 3.5 0 0 0 0-7h-11a3.5 3.5 0 0 1 0-7H15",key:"1d8sl"}],["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}]])},59872:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("GalleryVerticalEnd",[["path",{d:"M7 2h10",key:"nczekb"}],["path",{d:"M5 6h14",key:"u2x4p"}],["rect",{width:"18",height:"12",x:"3",y:"10",rx:"2",key:"l0tzu3"}]])},59945:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Map",[["path",{d:"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z",key:"169xi5"}],["path",{d:"M15 5.764v15",key:"1pn4in"}],["path",{d:"M9 3.236v15",key:"1uimfh"}]])},60722:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(91024);function i(e){return(0,r.w)(e,Date.now())}},61091:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},61477:(e,t,n)=>{n.d(t,{Q:()=>r});let r=(0,n(63185).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},62423:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Triangle",[["path",{d:"M13.73 4a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"14u9p9"}]])},63906:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]])},64261:(e,t,n)=>{n.d(t,{UC:()=>$,ZL:()=>H,bL:()=>_,l9:()=>z});var r=n(63185),i=n(19141),o=n(85729),l=n(63699),a=n(17095),s=n(7369),u=n(71561),d=n(19133),c=n(42924),p=n(20901),h=n(80799),f=n(34245),g=n(3166),m=n(41505),v=n(53917),y=n(7219),b=n(84464),w="Popover",[x,S]=(0,l.A)(w,[c.Bk]),C=(0,c.Bk)(),[R,P]=x(w),E=e=>{let{__scopePopover:t,children:n,open:i,defaultOpen:o,onOpenChange:l,modal:a=!1}=e,s=C(t),u=r.useRef(null),[p,h]=r.useState(!1),[f=!1,g]=(0,m.i)({prop:i,defaultProp:o,onChange:l});return(0,b.jsx)(c.bL,{...s,children:(0,b.jsx)(R,{scope:t,contentId:(0,d.B)(),triggerRef:u,open:f,onOpenChange:g,onOpenToggle:r.useCallback(()=>g(e=>!e),[g]),hasCustomAnchor:p,onCustomAnchorAdd:r.useCallback(()=>h(!0),[]),onCustomAnchorRemove:r.useCallback(()=>h(!1),[]),modal:a,children:n})})};E.displayName=w;var A="PopoverAnchor";r.forwardRef((e,t)=>{let{__scopePopover:n,...i}=e,o=P(A,n),l=C(n),{onCustomAnchorAdd:a,onCustomAnchorRemove:s}=o;return r.useEffect(()=>(a(),()=>s()),[a,s]),(0,b.jsx)(c.Mz,{...l,...i,ref:t})}).displayName=A;var D="PopoverTrigger",I=r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,l=P(D,n),a=C(n),s=(0,o.s)(t,l.triggerRef),u=(0,b.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":G(l.open),...r,ref:s,onClick:(0,i.m)(e.onClick,l.onOpenToggle)});return l.hasCustomAnchor?u:(0,b.jsx)(c.Mz,{asChild:!0,...a,children:u})});I.displayName=D;var M="PopoverPortal",[T,k]=x(M,{forceMount:void 0}),L=e=>{let{__scopePopover:t,forceMount:n,children:r,container:i}=e,o=P(M,t);return(0,b.jsx)(T,{scope:t,forceMount:n,children:(0,b.jsx)(h.C,{present:n||o.open,children:(0,b.jsx)(p.Z,{asChild:!0,container:i,children:r})})})};L.displayName=M;var F="PopoverContent",O=r.forwardRef((e,t)=>{let n=k(F,e.__scopePopover),{forceMount:r=n.forceMount,...i}=e,o=P(F,e.__scopePopover);return(0,b.jsx)(h.C,{present:r||o.open,children:o.modal?(0,b.jsx)(N,{...i,ref:t}):(0,b.jsx)(V,{...i,ref:t})})});O.displayName=F;var N=r.forwardRef((e,t)=>{let n=P(F,e.__scopePopover),l=r.useRef(null),a=(0,o.s)(t,l),s=r.useRef(!1);return r.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,b.jsx)(y.A,{as:g.DX,allowPinchZoom:!0,children:(0,b.jsx)(j,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),s.current||n.triggerRef.current?.focus()}),onPointerDownOutside:(0,i.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;s.current=2===t.button||n},{checkForDefaultPrevented:!1}),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),V=r.forwardRef((e,t)=>{let n=P(F,e.__scopePopover),i=r.useRef(!1),o=r.useRef(!1);return(0,b.jsx)(j,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(i.current||n.triggerRef.current?.focus(),t.preventDefault()),i.current=!1,o.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(i.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),j=r.forwardRef((e,t)=>{let{__scopePopover:n,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:o,disableOutsidePointerEvents:l,onEscapeKeyDown:d,onPointerDownOutside:p,onFocusOutside:h,onInteractOutside:f,...g}=e,m=P(F,n),v=C(n);return(0,s.Oh)(),(0,b.jsx)(u.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,b.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:l,onInteractOutside:f,onEscapeKeyDown:d,onPointerDownOutside:p,onFocusOutside:h,onDismiss:()=>m.onOpenChange(!1),children:(0,b.jsx)(c.UC,{"data-state":G(m.open),role:"dialog",id:m.contentId,...v,...g,ref:t,style:{...g.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),B="PopoverClose";function G(e){return e?"open":"closed"}r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=P(B,n);return(0,b.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,i.m)(e.onClick,()=>o.onOpenChange(!1))})}).displayName=B,r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,i=C(n);return(0,b.jsx)(c.i3,{...i,...r,ref:t})}).displayName="PopoverArrow";var _=E,z=I,H=L,$=O},64451:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Bookmark",[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z",key:"1fy3hk"}]])},64714:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},65717:(e,t,n)=>{function r(e,t){-1===e.indexOf(t)&&e.push(t)}function i(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}n.d(t,{Ai:()=>i,Kq:()=>r})},67532:(e,t,n)=>{n.d(t,{r:()=>l});var r=n(91024),i=n(42664);function o(e,t){let n=(0,i.a)(e,t?.in);return n.setHours(0,0,0,0),n}function l(e,t,n){let[i,l]=function(e,...t){let n=r.w.bind(null,e||t.find(e=>"object"==typeof e));return t.map(n)}(n?.in,e,t);return+o(i)==+o(l)}},68428:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},69465:(e,t,n)=>{n.d(t,{I:()=>l});let r=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var i=n(42409),o=n(98838);function l(e,t){let n=!1,l=!0,a={delta:0,timestamp:0,isProcessing:!1},s=()=>n=!0,u=r.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,o=!1,l=!1,a=new WeakSet,s={delta:0,timestamp:0,isProcessing:!1},u=0;function d(t){a.has(t)&&(c.schedule(t),e()),u++,t(s)}let c={schedule:(e,t=!1,i=!1)=>{let l=i&&o?n:r;return t&&a.add(e),l.has(e)||l.add(e),e},cancel:e=>{r.delete(e),a.delete(e)},process:e=>{if(s=e,o){l=!0;return}o=!0,[n,r]=[r,n],n.forEach(d),t&&i.Q.value&&i.Q.value.frameloop[t].push(u),u=0,n.clear(),o=!1,l&&(l=!1,c.process(e))}};return c}(s,t?n:void 0),e),{}),{setup:d,read:c,resolveKeyframes:p,preUpdate:h,update:f,preRender:g,render:m,postRender:v}=u,y=()=>{let r=o.W.useManualTiming?a.timestamp:performance.now();n=!1,o.W.useManualTiming||(a.delta=l?1e3/60:Math.max(Math.min(r-a.timestamp,40),1)),a.timestamp=r,a.isProcessing=!0,d.process(a),c.process(a),p.process(a),h.process(a),f.process(a),g.process(a),m.process(a),v.process(a),a.isProcessing=!1,n&&t&&(l=!1,e(y))},b=()=>{n=!0,l=!0,a.isProcessing||e(y)};return{schedule:r.reduce((e,t)=>{let r=u[t];return e[t]=(e,t=!1,i=!1)=>(n||b(),r.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<r.length;t++)u[r[t]].cancel(e)},state:a,steps:u}}},70312:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},70815:(e,t,n)=>{n.d(t,{OQ:()=>d});var r=n(89108),i=n(99134),o=n(84311),l=n(34190);let a=e=>!isNaN(parseFloat(e)),s={current:void 0};class u{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=r.k.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=r.k.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=a(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new o.v);let n=this.events[e].add(t);return"change"===e?()=>{n(),i.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return s.current&&s.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let e=r.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let t=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,l.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),t)}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function d(e,t){return new u(e,t)}},71116:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},72949:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Layers3",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m6.08 9.5-3.5 1.6a1 1 0 0 0 0 1.81l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9a1 1 0 0 0 0-1.83l-3.5-1.59",key:"1e5n1m"}],["path",{d:"m6.08 14.5-3.5 1.6a1 1 0 0 0 0 1.81l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9a1 1 0 0 0 0-1.83l-3.5-1.59",key:"1iwflc"}]])},73823:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},74884:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},75187:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},75321:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},76444:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},76799:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},80378:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},80656:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(63185),i=n(34245),o=n(84464),l=r.forwardRef((e,t)=>(0,o.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},81441:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},81760:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Pen",[["path",{d:"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z",key:"5qss01"}]])},82440:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},82474:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(63185);function i(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},82982:(e,t,n)=>{n.d(t,{S:()=>r});let r=e=>!!(e&&e.getVelocity)},83886:(e,t,n)=>{n.d(t,{d:()=>a});var r=n(63185),i=n(61477),o=n(89156),l=n(70815);function a(e){let t=(0,o.M)(()=>(0,l.OQ)(e)),{isStatic:n}=(0,r.useContext)(i.Q);if(n){let[,n]=(0,r.useState)(e);(0,r.useEffect)(()=>t.on("change",n),[])}return t}},84311:(e,t,n)=>{n.d(t,{v:()=>i});var r=n(65717);class i{constructor(){this.subscriptions=[]}add(e){return(0,r.Kq)(this.subscriptions,e),()=>(0,r.Ai)(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},84899:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},85477:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]])},86839:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},87119:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Paintbrush",[["path",{d:"M18.37 2.63 14 7l-1.59-1.59a2 2 0 0 0-2.82 0L8 7l9 9 1.59-1.59a2 2 0 0 0 0-2.82L17 10l4.37-4.37a2.12 2.12 0 1 0-3-3Z",key:"m6k5sh"}],["path",{d:"M9 8c-2 3-4 3.5-7 4l8 10c2-1 6-5 6-7",key:"arzq70"}],["path",{d:"M14.5 17.5 4.5 15",key:"s7fvrz"}]])},89108:(e,t,n)=>{let r;n.d(t,{k:()=>a});var i=n(99134),o=n(98838);function l(){r=void 0}let a={now:()=>(void 0===r&&a.set(i.uv.isProcessing||o.W.useManualTiming?i.uv.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(l)}}},89156:(e,t,n)=>{n.d(t,{M:()=>i});var r=n(63185);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},89755:(e,t,n)=>{n(95371)},89779:(e,t,n)=>{n.d(t,{P:()=>a});var r=n(91024),i=n(60722),o=n(67532),l=n(92929);function a(e,t){var n;return(0,o.r)((0,r.w)(t?.in||e,e),(n=(0,i.A)(t?.in||e),(0,l.f)(n,-1,void 0)))}},90871:(e,t,n)=>{n.d(t,{k:()=>i});var r=n(92929);function i(e,t,n){var i;return i=-t,(0,r.f)(e,7*i,n)}},91024:(e,t,n)=>{n.d(t,{w:()=>i});let r=Symbol.for("constructDateFrom");function i(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&r in e?e[r](t):e instanceof Date?new e.constructor(t):new Date(t)}},91097:(e,t,n)=>{n.d(t,{B8:()=>I,UC:()=>T,bL:()=>D,l9:()=>M});var r=n(63185),i=n(19141),o=n(63699),l=n(7910),a=n(80799),s=n(34245),u=n(272),d=n(41505),c=n(19133),p=n(84464),h="Tabs",[f,g]=(0,o.A)(h,[l.RG]),m=(0,l.RG)(),[v,y]=f(h),b=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:i,defaultValue:o,orientation:l="horizontal",dir:a,activationMode:h="automatic",...f}=e,g=(0,u.jH)(a),[m,y]=(0,d.i)({prop:r,onChange:i,defaultProp:o});return(0,p.jsx)(v,{scope:n,baseId:(0,c.B)(),value:m,onValueChange:y,orientation:l,dir:g,activationMode:h,children:(0,p.jsx)(s.sG.div,{dir:g,"data-orientation":l,...f,ref:t})})});b.displayName=h;var w="TabsList",x=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...i}=e,o=y(w,n),a=m(n);return(0,p.jsx)(l.bL,{asChild:!0,...a,orientation:o.orientation,dir:o.dir,loop:r,children:(0,p.jsx)(s.sG.div,{role:"tablist","aria-orientation":o.orientation,...i,ref:t})})});x.displayName=w;var S="TabsTrigger",C=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:o=!1,...a}=e,u=y(S,n),d=m(n),c=E(u.baseId,r),h=A(u.baseId,r),f=r===u.value;return(0,p.jsx)(l.q7,{asChild:!0,...d,focusable:!o,active:f,children:(0,p.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":h,"data-state":f?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:c,...a,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(r)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(r)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==u.activationMode;f||o||!e||u.onValueChange(r)})})})});C.displayName=S;var R="TabsContent",P=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:i,forceMount:o,children:l,...u}=e,d=y(R,n),c=E(d.baseId,i),h=A(d.baseId,i),f=i===d.value,g=r.useRef(f);return r.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(a.C,{present:o||f,children:({present:n})=>(0,p.jsx)(s.sG.div,{"data-state":f?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":c,hidden:!n,id:h,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:g.current?"0s":void 0},children:n&&l})})});function E(e,t){return`${e}-trigger-${t}`}function A(e,t){return`${e}-content-${t}`}P.displayName=R;var D=b,I=x,M=C,T=P},91473:(e,t,n)=>{n.d(t,{CC:()=>H,Q6:()=>$,bL:()=>z,zi:()=>U});var r=n(63185),i=n(20830),o=n(19141),l=n(85729),a=n(63699),s=n(41505),u=n(272),d=n(82474),c=n(81447),p=n(34245),h=n(76850),f=n(84464),g=["PageUp","PageDown"],m=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],v={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},y="Slider",[b,w,x]=(0,h.N)(y),[S,C]=(0,a.A)(y,[x]),[R,P]=S(y),E=r.forwardRef((e,t)=>{let{name:n,min:l=0,max:a=100,step:u=1,orientation:d="horizontal",disabled:c=!1,minStepsBetweenThumbs:p=0,defaultValue:h=[l],value:v,onValueChange:y=()=>{},onValueCommit:w=()=>{},inverted:x=!1,form:S,...C}=e,P=r.useRef(new Set),E=r.useRef(0),A="horizontal"===d,[D=[],T]=(0,s.i)({prop:v,defaultProp:h,onChange:e=>{let t=[...P.current];t[E.current]?.focus(),y(e)}}),k=r.useRef(D);function L(e,t,{commit:n}={commit:!1}){let r=(String(u).split(".")[1]||"").length,o=function(e,t){let n=Math.pow(10,t);return Math.round(e*n)/n}(Math.round((e-l)/u)*u+l,r),s=(0,i.q)(o,[l,a]);T((e=[])=>{let r=function(e=[],t,n){let r=[...e];return r[n]=t,r.sort((e,t)=>e-t)}(e,s,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,n)=>e[n+1]-t))>=t;return!0}(r,p*u))return e;{E.current=r.indexOf(s);let t=String(r)!==String(e);return t&&n&&w(r),t?r:e}})}return(0,f.jsx)(R,{scope:e.__scopeSlider,name:n,disabled:c,min:l,max:a,valueIndexToChangeRef:E,thumbs:P.current,values:D,orientation:d,form:S,children:(0,f.jsx)(b.Provider,{scope:e.__scopeSlider,children:(0,f.jsx)(b.Slot,{scope:e.__scopeSlider,children:(0,f.jsx)(A?I:M,{"aria-disabled":c,"data-disabled":c?"":void 0,...C,ref:t,onPointerDown:(0,o.m)(C.onPointerDown,()=>{c||(k.current=D)}),min:l,max:a,inverted:x,onSlideStart:c?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let n=e.map(e=>Math.abs(e-t)),r=Math.min(...n);return n.indexOf(r)}(D,e);L(e,t)},onSlideMove:c?void 0:function(e){L(e,E.current)},onSlideEnd:c?void 0:function(){let e=k.current[E.current];D[E.current]!==e&&w(D)},onHomeKeyDown:()=>!c&&L(l,0,{commit:!0}),onEndKeyDown:()=>!c&&L(a,D.length-1,{commit:!0}),onStepKeyDown:({event:e,direction:t})=>{if(!c){let n=g.includes(e.key)||e.shiftKey&&m.includes(e.key),r=E.current;L(D[r]+u*(n?10:1)*t,r,{commit:!0})}}})})})})});E.displayName=y;var[A,D]=S(y,{startEdge:"left",endEdge:"right",size:"width",direction:1}),I=r.forwardRef((e,t)=>{let{min:n,max:i,dir:o,inverted:a,onSlideStart:s,onSlideMove:d,onSlideEnd:c,onStepKeyDown:p,...h}=e,[g,m]=r.useState(null),y=(0,l.s)(t,e=>m(e)),b=r.useRef(void 0),w=(0,u.jH)(o),x="ltr"===w,S=x&&!a||!x&&a;function C(e){let t=b.current||g.getBoundingClientRect(),r=_([0,t.width],S?[n,i]:[i,n]);return b.current=t,r(e-t.left)}return(0,f.jsx)(A,{scope:e.__scopeSlider,startEdge:S?"left":"right",endEdge:S?"right":"left",direction:S?1:-1,size:"width",children:(0,f.jsx)(T,{dir:w,"data-orientation":"horizontal",...h,ref:y,style:{...h.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=C(e.clientX);s?.(t)},onSlideMove:e=>{let t=C(e.clientX);d?.(t)},onSlideEnd:()=>{b.current=void 0,c?.()},onStepKeyDown:e=>{let t=v[S?"from-left":"from-right"].includes(e.key);p?.({event:e,direction:t?-1:1})}})})}),M=r.forwardRef((e,t)=>{let{min:n,max:i,inverted:o,onSlideStart:a,onSlideMove:s,onSlideEnd:u,onStepKeyDown:d,...c}=e,p=r.useRef(null),h=(0,l.s)(t,p),g=r.useRef(void 0),m=!o;function y(e){let t=g.current||p.current.getBoundingClientRect(),r=_([0,t.height],m?[i,n]:[n,i]);return g.current=t,r(e-t.top)}return(0,f.jsx)(A,{scope:e.__scopeSlider,startEdge:m?"bottom":"top",endEdge:m?"top":"bottom",size:"height",direction:m?1:-1,children:(0,f.jsx)(T,{"data-orientation":"vertical",...c,ref:h,style:{...c.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=y(e.clientY);a?.(t)},onSlideMove:e=>{let t=y(e.clientY);s?.(t)},onSlideEnd:()=>{g.current=void 0,u?.()},onStepKeyDown:e=>{let t=v[m?"from-bottom":"from-top"].includes(e.key);d?.({event:e,direction:t?-1:1})}})})}),T=r.forwardRef((e,t)=>{let{__scopeSlider:n,onSlideStart:r,onSlideMove:i,onSlideEnd:l,onHomeKeyDown:a,onEndKeyDown:s,onStepKeyDown:u,...d}=e,c=P(y,n);return(0,f.jsx)(p.sG.span,{...d,ref:t,onKeyDown:(0,o.m)(e.onKeyDown,e=>{"Home"===e.key?(a(e),e.preventDefault()):"End"===e.key?(s(e),e.preventDefault()):g.concat(m).includes(e.key)&&(u(e),e.preventDefault())}),onPointerDown:(0,o.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),c.thumbs.has(t)?t.focus():r(e)}),onPointerMove:(0,o.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&i(e)}),onPointerUp:(0,o.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),l(e))})})}),k="SliderTrack",L=r.forwardRef((e,t)=>{let{__scopeSlider:n,...r}=e,i=P(k,n);return(0,f.jsx)(p.sG.span,{"data-disabled":i.disabled?"":void 0,"data-orientation":i.orientation,...r,ref:t})});L.displayName=k;var F="SliderRange",O=r.forwardRef((e,t)=>{let{__scopeSlider:n,...i}=e,o=P(F,n),a=D(F,n),s=r.useRef(null),u=(0,l.s)(t,s),d=o.values.length,c=o.values.map(e=>G(e,o.min,o.max)),h=d>1?Math.min(...c):0,g=100-Math.max(...c);return(0,f.jsx)(p.sG.span,{"data-orientation":o.orientation,"data-disabled":o.disabled?"":void 0,...i,ref:u,style:{...e.style,[a.startEdge]:h+"%",[a.endEdge]:g+"%"}})});O.displayName=F;var N="SliderThumb",V=r.forwardRef((e,t)=>{let n=w(e.__scopeSlider),[i,o]=r.useState(null),a=(0,l.s)(t,e=>o(e)),s=r.useMemo(()=>i?n().findIndex(e=>e.ref.current===i):-1,[n,i]);return(0,f.jsx)(j,{...e,ref:a,index:s})}),j=r.forwardRef((e,t)=>{let{__scopeSlider:n,index:i,name:a,...s}=e,u=P(N,n),d=D(N,n),[h,g]=r.useState(null),m=(0,l.s)(t,e=>g(e)),v=!h||u.form||!!h.closest("form"),y=(0,c.X)(h),w=u.values[i],x=void 0===w?0:G(w,u.min,u.max),S=function(e,t){return t>2?`Value ${e+1} of ${t}`:2===t?["Minimum","Maximum"][e]:void 0}(i,u.values.length),C=y?.[d.size],R=C?function(e,t,n){let r=e/2,i=_([0,50],[0,r]);return(r-i(t)*n)*n}(C,x,d.direction):0;return r.useEffect(()=>{if(h)return u.thumbs.add(h),()=>{u.thumbs.delete(h)}},[h,u.thumbs]),(0,f.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[d.startEdge]:`calc(${x}% + ${R}px)`},children:[(0,f.jsx)(b.ItemSlot,{scope:e.__scopeSlider,children:(0,f.jsx)(p.sG.span,{role:"slider","aria-label":e["aria-label"]||S,"aria-valuemin":u.min,"aria-valuenow":w,"aria-valuemax":u.max,"aria-orientation":u.orientation,"data-orientation":u.orientation,"data-disabled":u.disabled?"":void 0,tabIndex:u.disabled?void 0:0,...s,ref:m,style:void 0===w?{display:"none"}:e.style,onFocus:(0,o.m)(e.onFocus,()=>{u.valueIndexToChangeRef.current=i})})}),v&&(0,f.jsx)(B,{name:a??(u.name?u.name+(u.values.length>1?"[]":""):void 0),form:u.form,value:w},i)]})});V.displayName=N;var B=e=>{let{value:t,...n}=e,i=r.useRef(null),o=(0,d.Z)(t);return r.useEffect(()=>{let e=i.current,n=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(o!==t&&n){let r=new Event("input",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[o,t]),(0,f.jsx)("input",{style:{display:"none"},...n,ref:i,defaultValue:t})};function G(e,t,n){return(0,i.q)(100/(n-t)*(e-t),[0,100])}function _(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}var z=E,H=L,$=O,U=V},92147:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Type",[["polyline",{points:"4 7 4 4 20 4 20 7",key:"1nosan"}],["line",{x1:"9",x2:"15",y1:"20",y2:"20",key:"swin9y"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]])},92251:(e,t,n)=>{n.d(t,{In:()=>ek,JU:()=>eV,LM:()=>eO,PP:()=>e_,UC:()=>eF,VF:()=>eG,WT:()=>eT,YJ:()=>eN,ZL:()=>eL,bL:()=>eI,l9:()=>eM,p4:()=>eB,q7:()=>ej,wn:()=>ez,wv:()=>eH});var r=n(63185),i=n(67108),o=n(20830),l=n(19141),a=n(76850),s=n(85729),u=n(63699),d=n(272),c=n(17095),p=n(7369),h=n(71561),f=n(19133),g=n(42924),m=n(20901),v=n(34245),y=n(3166),b=n(57647),w=n(41505),x=n(70505),S=n(82474),C=n(83552),R=n(53917),P=n(7219),E=n(84464),A=[" ","Enter","ArrowUp","ArrowDown"],D=[" ","Enter"],I="Select",[M,T,k]=(0,a.N)(I),[L,F]=(0,u.A)(I,[k,g.Bk]),O=(0,g.Bk)(),[N,V]=L(I),[j,B]=L(I),G=e=>{let{__scopeSelect:t,children:n,open:i,defaultOpen:o,onOpenChange:l,value:a,defaultValue:s,onValueChange:u,dir:c,name:p,autoComplete:h,disabled:m,required:v,form:y}=e,b=O(t),[x,S]=r.useState(null),[C,R]=r.useState(null),[P,A]=r.useState(!1),D=(0,d.jH)(c),[I=!1,T]=(0,w.i)({prop:i,defaultProp:o,onChange:l}),[k,L]=(0,w.i)({prop:a,defaultProp:s,onChange:u}),F=r.useRef(null),V=!x||y||!!x.closest("form"),[B,G]=r.useState(new Set),_=Array.from(B).map(e=>e.props.value).join(";");return(0,E.jsx)(g.bL,{...b,children:(0,E.jsxs)(N,{required:v,scope:t,trigger:x,onTriggerChange:S,valueNode:C,onValueNodeChange:R,valueNodeHasChildren:P,onValueNodeHasChildrenChange:A,contentId:(0,f.B)(),value:k,onValueChange:L,open:I,onOpenChange:T,dir:D,triggerPointerDownPosRef:F,disabled:m,children:[(0,E.jsx)(M.Provider,{scope:t,children:(0,E.jsx)(j,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{G(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{G(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),V?(0,E.jsxs)(eE,{"aria-hidden":!0,required:v,tabIndex:-1,name:p,autoComplete:h,value:k,onChange:e=>L(e.target.value),disabled:m,form:y,children:[void 0===k?(0,E.jsx)("option",{value:""}):null,Array.from(B)]},_):null]})})};G.displayName=I;var _="SelectTrigger",z=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:i=!1,...o}=e,a=O(n),u=V(_,n),d=u.disabled||i,c=(0,s.s)(t,u.onTriggerChange),p=T(n),h=r.useRef("touch"),[f,m,y]=eA(e=>{let t=p().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=eD(t,e,n);void 0!==r&&u.onValueChange(r.value)}),b=e=>{d||(u.onOpenChange(!0),y()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,E.jsx)(g.Mz,{asChild:!0,...a,children:(0,E.jsx)(v.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":eP(u.value)?"":void 0,...o,ref:c,onClick:(0,l.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==h.current&&b(e)}),onPointerDown:(0,l.m)(o.onPointerDown,e=>{h.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(b(e),e.preventDefault())}),onKeyDown:(0,l.m)(o.onKeyDown,e=>{let t=""!==f.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&A.includes(e.key)&&(b(),e.preventDefault())})})})});z.displayName=_;var H="SelectValue",$=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:i,children:o,placeholder:l="",...a}=e,u=V(H,n),{onValueNodeHasChildrenChange:d}=u,c=void 0!==o,p=(0,s.s)(t,u.onValueNodeChange);return(0,x.N)(()=>{d(c)},[d,c]),(0,E.jsx)(v.sG.span,{...a,ref:p,style:{pointerEvents:"none"},children:eP(u.value)?(0,E.jsx)(E.Fragment,{children:l}):o})});$.displayName=H;var U=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...i}=e;return(0,E.jsx)(v.sG.span,{"aria-hidden":!0,...i,ref:t,children:r||"▼"})});U.displayName="SelectIcon";var W=e=>(0,E.jsx)(m.Z,{asChild:!0,...e});W.displayName="SelectPortal";var q="SelectContent",Y=r.forwardRef((e,t)=>{let n=V(q,e.__scopeSelect),[o,l]=r.useState();return((0,x.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,E.jsx)(Z,{...e,ref:t}):o?i.createPortal((0,E.jsx)(X,{scope:e.__scopeSelect,children:(0,E.jsx)(M.Slot,{scope:e.__scopeSelect,children:(0,E.jsx)("div",{children:e.children})})}),o):null});Y.displayName=q;var[X,K]=L(q),Z=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:i="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:a,onPointerDownOutside:u,side:d,sideOffset:f,align:g,alignOffset:m,arrowPadding:v,collisionBoundary:b,collisionPadding:w,sticky:x,hideWhenDetached:S,avoidCollisions:C,...A}=e,D=V(q,n),[I,M]=r.useState(null),[k,L]=r.useState(null),F=(0,s.s)(t,e=>M(e)),[O,N]=r.useState(null),[j,B]=r.useState(null),G=T(n),[_,z]=r.useState(!1),H=r.useRef(!1);r.useEffect(()=>{if(I)return(0,R.Eq)(I)},[I]),(0,p.Oh)();let $=r.useCallback(e=>{let[t,...n]=G().map(e=>e.ref.current),[r]=n.slice(-1),i=document.activeElement;for(let n of e)if(n===i||(n?.scrollIntoView({block:"nearest"}),n===t&&k&&(k.scrollTop=0),n===r&&k&&(k.scrollTop=k.scrollHeight),n?.focus(),document.activeElement!==i))return},[G,k]),U=r.useCallback(()=>$([O,I]),[$,O,I]);r.useEffect(()=>{_&&U()},[_,U]);let{onOpenChange:W,triggerPointerDownPosRef:Y}=D;r.useEffect(()=>{if(I){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(Y.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(Y.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():I.contains(n.target)||W(!1),document.removeEventListener("pointermove",t),Y.current=null};return null!==Y.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[I,W,Y]),r.useEffect(()=>{let e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[W]);let[K,Z]=eA(e=>{let t=G().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eD(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),ee=r.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==D.value&&D.value===t||r)&&(N(e),r&&(H.current=!0))},[D.value]),et=r.useCallback(()=>I?.focus(),[I]),en=r.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==D.value&&D.value===t||r)&&B(e)},[D.value]),er="popper"===i?J:Q,ei=er===J?{side:d,sideOffset:f,align:g,alignOffset:m,arrowPadding:v,collisionBoundary:b,collisionPadding:w,sticky:x,hideWhenDetached:S,avoidCollisions:C}:{};return(0,E.jsx)(X,{scope:n,content:I,viewport:k,onViewportChange:L,itemRefCallback:ee,selectedItem:O,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:U,selectedItemText:j,position:i,isPositioned:_,searchRef:K,children:(0,E.jsx)(P.A,{as:y.DX,allowPinchZoom:!0,children:(0,E.jsx)(h.n,{asChild:!0,trapped:D.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(o,e=>{D.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,E.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>D.onOpenChange(!1),children:(0,E.jsx)(er,{role:"listbox",id:D.contentId,"data-state":D.open?"open":"closed",dir:D.dir,onContextMenu:e=>e.preventDefault(),...A,...ei,onPlaced:()=>z(!0),ref:F,style:{display:"flex",flexDirection:"column",outline:"none",...A.style},onKeyDown:(0,l.m)(A.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=G().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>$(t)),e.preventDefault()}})})})})})})});Z.displayName="SelectContentImpl";var Q=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:i,...l}=e,a=V(q,n),u=K(q,n),[d,c]=r.useState(null),[p,h]=r.useState(null),f=(0,s.s)(t,e=>h(e)),g=T(n),m=r.useRef(!1),y=r.useRef(!0),{viewport:b,selectedItem:w,selectedItemText:S,focusSelectedItem:C}=u,R=r.useCallback(()=>{if(a.trigger&&a.valueNode&&d&&p&&b&&w&&S){let e=a.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=S.getBoundingClientRect();if("rtl"!==a.dir){let i=r.left-t.left,l=n.left-i,a=e.left-l,s=e.width+a,u=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.q)(l,[10,Math.max(10,c-u)]);d.style.minWidth=s+"px",d.style.left=p+"px"}else{let i=t.right-r.right,l=window.innerWidth-n.right-i,a=window.innerWidth-e.right-l,s=e.width+a,u=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.q)(l,[10,Math.max(10,c-u)]);d.style.minWidth=s+"px",d.style.right=p+"px"}let l=g(),s=window.innerHeight-20,u=b.scrollHeight,c=window.getComputedStyle(p),h=parseInt(c.borderTopWidth,10),f=parseInt(c.paddingTop,10),v=parseInt(c.borderBottomWidth,10),y=h+f+u+parseInt(c.paddingBottom,10)+v,x=Math.min(5*w.offsetHeight,y),C=window.getComputedStyle(b),R=parseInt(C.paddingTop,10),P=parseInt(C.paddingBottom,10),E=e.top+e.height/2-10,A=w.offsetHeight/2,D=h+f+(w.offsetTop+A);if(D<=E){let e=l.length>0&&w===l[l.length-1].ref.current;d.style.bottom="0px";let t=Math.max(s-E,A+(e?P:0)+(p.clientHeight-b.offsetTop-b.offsetHeight)+v);d.style.height=D+t+"px"}else{let e=l.length>0&&w===l[0].ref.current;d.style.top="0px";let t=Math.max(E,h+b.offsetTop+(e?R:0)+A);d.style.height=t+(y-D)+"px",b.scrollTop=D-E+b.offsetTop}d.style.margin="10px 0",d.style.minHeight=x+"px",d.style.maxHeight=s+"px",i?.(),requestAnimationFrame(()=>m.current=!0)}},[g,a.trigger,a.valueNode,d,p,b,w,S,a.dir,i]);(0,x.N)(()=>R(),[R]);let[P,A]=r.useState();(0,x.N)(()=>{p&&A(window.getComputedStyle(p).zIndex)},[p]);let D=r.useCallback(e=>{e&&!0===y.current&&(R(),C?.(),y.current=!1)},[R,C]);return(0,E.jsx)(ee,{scope:n,contentWrapper:d,shouldExpandOnScrollRef:m,onScrollButtonChange:D,children:(0,E.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:P},children:(0,E.jsx)(v.sG.div,{...l,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});Q.displayName="SelectItemAlignedPosition";var J=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:i=10,...o}=e,l=O(n);return(0,E.jsx)(g.UC,{...l,...o,ref:t,align:r,collisionPadding:i,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});J.displayName="SelectPopperPosition";var[ee,et]=L(q,{}),en="SelectViewport",er=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:i,...o}=e,a=K(en,n),u=et(en,n),d=(0,s.s)(t,a.onViewportChange),c=r.useRef(0);return(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,E.jsx)(M.Slot,{scope:n,children:(0,E.jsx)(v.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,l.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if(r?.current&&n){let e=Math.abs(c.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,i=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(i<r){let o=i+e,l=Math.min(r,o),a=o-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});er.displayName=en;var ei="SelectGroup",[eo,el]=L(ei),ea=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,i=(0,f.B)();return(0,E.jsx)(eo,{scope:n,id:i,children:(0,E.jsx)(v.sG.div,{role:"group","aria-labelledby":i,...r,ref:t})})});ea.displayName=ei;var es="SelectLabel",eu=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,i=el(es,n);return(0,E.jsx)(v.sG.div,{id:i.id,...r,ref:t})});eu.displayName=es;var ed="SelectItem",[ec,ep]=L(ed),eh=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:i,disabled:o=!1,textValue:a,...u}=e,d=V(ed,n),c=K(ed,n),p=d.value===i,[h,g]=r.useState(a??""),[m,y]=r.useState(!1),b=(0,s.s)(t,e=>c.itemRefCallback?.(e,i,o)),w=(0,f.B)(),x=r.useRef("touch"),S=()=>{o||(d.onValueChange(i),d.onOpenChange(!1))};if(""===i)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,E.jsx)(ec,{scope:n,value:i,disabled:o,textId:w,isSelected:p,onItemTextChange:r.useCallback(e=>{g(t=>t||(e?.textContent??"").trim())},[]),children:(0,E.jsx)(M.ItemSlot,{scope:n,value:i,disabled:o,textValue:h,children:(0,E.jsx)(v.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...u,ref:b,onFocus:(0,l.m)(u.onFocus,()=>y(!0)),onBlur:(0,l.m)(u.onBlur,()=>y(!1)),onClick:(0,l.m)(u.onClick,()=>{"mouse"!==x.current&&S()}),onPointerUp:(0,l.m)(u.onPointerUp,()=>{"mouse"===x.current&&S()}),onPointerDown:(0,l.m)(u.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,l.m)(u.onPointerMove,e=>{x.current=e.pointerType,o?c.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(u.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,l.m)(u.onKeyDown,e=>{(c.searchRef?.current===""||" "!==e.key)&&(D.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});eh.displayName=ed;var ef="SelectItemText",eg=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:o,style:l,...a}=e,u=V(ef,n),d=K(ef,n),c=ep(ef,n),p=B(ef,n),[h,f]=r.useState(null),g=(0,s.s)(t,e=>f(e),c.onItemTextChange,e=>d.itemTextRefCallback?.(e,c.value,c.disabled)),m=h?.textContent,y=r.useMemo(()=>(0,E.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:b,onNativeOptionRemove:w}=p;return(0,x.N)(()=>(b(y),()=>w(y)),[b,w,y]),(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(v.sG.span,{id:c.textId,...a,ref:g}),c.isSelected&&u.valueNode&&!u.valueNodeHasChildren?i.createPortal(a.children,u.valueNode):null]})});eg.displayName=ef;var em="SelectItemIndicator",ev=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ep(em,n).isSelected?(0,E.jsx)(v.sG.span,{"aria-hidden":!0,...r,ref:t}):null});ev.displayName=em;var ey="SelectScrollUpButton",eb=r.forwardRef((e,t)=>{let n=K(ey,e.__scopeSelect),i=et(ey,e.__scopeSelect),[o,l]=r.useState(!1),a=(0,s.s)(t,i.onScrollButtonChange);return(0,x.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,E.jsx)(eS,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eb.displayName=ey;var ew="SelectScrollDownButton",ex=r.forwardRef((e,t)=>{let n=K(ew,e.__scopeSelect),i=et(ew,e.__scopeSelect),[o,l]=r.useState(!1),a=(0,s.s)(t,i.onScrollButtonChange);return(0,x.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,E.jsx)(eS,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ex.displayName=ew;var eS=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:i,...o}=e,a=K("SelectScrollButton",n),s=r.useRef(null),u=T(n),d=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>d(),[d]),(0,x.N)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,E.jsx)(v.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,l.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(i,50))}),onPointerMove:(0,l.m)(o.onPointerMove,()=>{a.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(i,50))}),onPointerLeave:(0,l.m)(o.onPointerLeave,()=>{d()})})}),eC=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,E.jsx)(v.sG.div,{"aria-hidden":!0,...r,ref:t})});eC.displayName="SelectSeparator";var eR="SelectArrow";function eP(e){return""===e||void 0===e}r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,i=O(n),o=V(eR,n),l=K(eR,n);return o.open&&"popper"===l.position?(0,E.jsx)(g.i3,{...i,...r,ref:t}):null}).displayName=eR;var eE=r.forwardRef((e,t)=>{let{value:n,...i}=e,o=r.useRef(null),l=(0,s.s)(t,o),a=(0,S.Z)(n);return r.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==n&&t){let r=new Event("change",{bubbles:!0});t.call(e,n),e.dispatchEvent(r)}},[a,n]),(0,E.jsx)(C.s,{asChild:!0,children:(0,E.jsx)("select",{...i,ref:l,defaultValue:n})})});function eA(e){let t=(0,b.c)(e),n=r.useRef(""),i=r.useRef(0),o=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(i.current),""!==t&&(i.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(i.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(i.current),[]),[n,o,l]}function eD(e,t,n){var r,i;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,i=Math.max(l,0),r.map((e,t)=>r[(i+t)%r.length]));1===o.length&&(a=a.filter(e=>e!==n));let s=a.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==n?s:void 0}eE.displayName="BubbleSelect";var eI=G,eM=z,eT=$,ek=U,eL=W,eF=Y,eO=er,eN=ea,eV=eu,ej=eh,eB=eg,eG=ev,e_=eb,ez=ex,eH=eC},92802:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Pencil",[["path",{d:"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z",key:"5qss01"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},92929:(e,t,n)=>{n.d(t,{f:()=>o});var r=n(91024),i=n(42664);function o(e,t,n){let o=(0,i.a)(e,n?.in);return isNaN(t)?(0,r.w)(n?.in||e,NaN):(t&&o.setDate(o.getDate()+t),o)}},94149:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},95371:(e,t,n)=>{var r=n(63185);"function"==typeof Object.is&&Object.is,r.useSyncExternalStore,r.useRef,r.useEffect,r.useMemo,r.useDebugValue},95796:(e,t,n)=>{n.d(t,{c:()=>l});var r=n(91024),i=n(60722),o=n(67532);function l(e,t){return(0,o.r)((0,r.w)(t?.in||e,e),(0,i.A)(t?.in||e))}},95932:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},97825:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]])},98379:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71410).A)("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]])},98708:(e,t,n)=>{let r;n.d(t,{TW:()=>eS,YZ:()=>eb,Zk:()=>C});var i=n(63185);let{createElement:o,createContext:l,createRef:a,forwardRef:s,useCallback:u,useContext:d,useEffect:c,useImperativeHandle:p,useLayoutEffect:h,useMemo:f,useRef:g,useState:m}=i,v=i[`useId${Math.random()}`.slice(0,5)],y=l(null);y.displayName="PanelGroupContext";let b="function"==typeof v?v:()=>null,w=0;function x(e=null){let t=b(),n=g(e||t||null);return null===n.current&&(n.current=""+w++),null!=e?e:n.current}function S({children:e,className:t="",collapsedSize:n,collapsible:r,defaultSize:i,forwardedRef:l,id:a,maxSize:s,minSize:u,onCollapse:c,onExpand:h,onResize:f,order:m,style:v,tagName:b="div",...w}){let S=d(y);if(null===S)throw Error("Panel components must be rendered within a PanelGroup container");let{collapsePanel:C,expandPanel:R,getPanelSize:P,getPanelStyle:E,groupId:A,isPanelCollapsed:D,reevaluatePanelConstraints:I,registerPanel:M,resizePanel:T,unregisterPanel:k}=S,L=x(a),F=g({callbacks:{onCollapse:c,onExpand:h,onResize:f},constraints:{collapsedSize:n,collapsible:r,defaultSize:i,maxSize:s,minSize:u},id:L,idIsFromProps:void 0!==a,order:m});g({didLogMissingDefaultSizeWarning:!1}),p(l,()=>({collapse:()=>{C(F.current)},expand:e=>{R(F.current,e)},getId:()=>L,getSize:()=>P(F.current),isCollapsed:()=>D(F.current),isExpanded:()=>!D(F.current),resize:e=>{T(F.current,e)}}),[C,R,P,D,L,T]);let O=E(F.current,i);return o(b,{...w,children:e,className:t,id:a,style:{...O,...v},"data-panel":"","data-panel-collapsible":r||void 0,"data-panel-group-id":A,"data-panel-id":L,"data-panel-size":parseFloat(""+O.flexGrow).toFixed(1)})}let C=s((e,t)=>o(S,{...e,forwardedRef:t}));S.displayName="Panel",C.displayName="forwardRef(Panel)";let R=null,P=null;function E(e,t){0;let n=function(e,t){if(t){let e=(t&N)!=0,n=(t&V)!=0,r=(t&j)!=0,i=(t&B)!=0;if(e)if(r)return"se-resize";else if(i)return"ne-resize";else return"e-resize";if(n)if(r)return"sw-resize";else if(i)return"nw-resize";else return"w-resize";if(r)return"s-resize";else if(i)return"n-resize"}switch(e){case"horizontal":return"ew-resize";case"intersection":return"move";case"vertical":return"ns-resize"}}(e,t);if(R!==n){if(R=n,null===P){P=document.createElement("style");r&&P.setAttribute("nonce",r),document.head.appendChild(P)}P.innerHTML=`*{cursor: ${n}!important;}`}}function A(e){return"keydown"===e.type}function D(e){return e.type.startsWith("pointer")}function I(e){return e.type.startsWith("mouse")}function M(e){if(D(e)){if(e.isPrimary)return{x:e.clientX,y:e.clientY}}else if(I(e))return{x:e.clientX,y:e.clientY};return{x:1/0,y:1/0}}let T=/\b(?:position|zIndex|opacity|transform|webkitTransform|mixBlendMode|filter|webkitFilter|isolation)\b/;function k(e){let t=e.length;for(;t--;){let n=e[t];if(J(n,"Missing node"),function(e){let t=getComputedStyle(e);return!!("fixed"===t.position||"auto"!==t.zIndex&&("static"!==t.position||function(e){var t;let n=getComputedStyle(null!=(t=O(e))?t:e).display;return"flex"===n||"inline-flex"===n}(e))||1>+t.opacity||"transform"in t&&"none"!==t.transform||"webkitTransform"in t&&"none"!==t.webkitTransform||"mixBlendMode"in t&&"normal"!==t.mixBlendMode||"filter"in t&&"none"!==t.filter||"webkitFilter"in t&&"none"!==t.webkitFilter||"isolation"in t&&"isolate"===t.isolation||T.test(t.willChange))||"touch"===t.webkitOverflowScrolling}(n))return n}return null}function L(e){return e&&Number(getComputedStyle(e).zIndex)||0}function F(e){let t=[];for(;e;)t.push(e),e=O(e);return t}function O(e){let{parentNode:t}=e;return t&&t instanceof ShadowRoot?t.host:t}let N=1,V=2,j=4,B=8,G="coarse"===function(){if("function"==typeof matchMedia)return matchMedia("(pointer:coarse)").matches?"coarse":"fine"}(),_=[],z=!1,H=new Map,$=new Map,U=new Set;function W(e){let{target:t}=e,{x:n,y:r}=M(e);z=!0,X({target:t,x:n,y:r}),Z(),_.length>0&&(Q("down",e),e.preventDefault(),e.stopPropagation())}function q(e){let{x:t,y:n}=M(e);if(z&&0===e.buttons&&(z=!1,Q("up",e)),!z){let{target:r}=e;X({target:r,x:t,y:n})}Q("move",e),K(),_.length>0&&e.preventDefault()}function Y(e){let{target:t}=e,{x:n,y:r}=M(e);$.clear(),z=!1,_.length>0&&e.preventDefault(),Q("up",e),X({target:t,x:n,y:r}),K(),Z()}function X({target:e,x:t,y:n}){_.splice(0);let r=null;(e instanceof HTMLElement||e instanceof SVGElement)&&(r=e),U.forEach(e=>{let{element:i,hitAreaMargins:o}=e,l=i.getBoundingClientRect(),{bottom:a,left:s,right:u,top:d}=l,c=G?o.coarse:o.fine;if(t>=s-c&&t<=u+c&&n>=d-c&&n<=a+c){if(null!==r&&document.contains(r)&&i!==r&&!i.contains(r)&&!r.contains(i)&&function(e,t){let n;if(e===t)throw Error("Cannot compare node with itself");let r={a:F(e),b:F(t)};for(;r.a.at(-1)===r.b.at(-1);)e=r.a.pop(),t=r.b.pop(),n=e;J(n,"Stacking order can only be calculated for elements with a common ancestor");let i={a:L(k(r.a)),b:L(k(r.b))};if(i.a===i.b){let e=n.childNodes,t={a:r.a.at(-1),b:r.b.at(-1)},i=e.length;for(;i--;){let n=e[i];if(n===t.a)return 1;if(n===t.b)return -1}}return Math.sign(i.a-i.b)}(r,i)>0){let e=r,t=!1;for(;e&&!e.contains(i);){var p,h;if(p=e.getBoundingClientRect(),h=l,p.x<h.x+h.width&&p.x+p.width>h.x&&p.y<h.y+h.height&&p.y+p.height>h.y){t=!0;break}e=e.parentElement}if(t)return}_.push(e)}})}function K(){let e=!1,t=!1;_.forEach(n=>{let{direction:r}=n;"horizontal"===r?e=!0:t=!0});let n=0;$.forEach(e=>{n|=e}),e&&t?E("intersection",n):e?E("horizontal",n):t?E("vertical",n):null!==P&&(document.head.removeChild(P),R=null,P=null)}function Z(){H.forEach((e,t)=>{let{body:n}=t;n.removeEventListener("contextmenu",Y),n.removeEventListener("pointerdown",W),n.removeEventListener("pointerleave",q),n.removeEventListener("pointermove",q)}),window.removeEventListener("pointerup",Y),window.removeEventListener("pointercancel",Y),U.size>0&&(z?(_.length>0&&H.forEach((e,t)=>{let{body:n}=t;e>0&&(n.addEventListener("contextmenu",Y),n.addEventListener("pointerleave",q),n.addEventListener("pointermove",q))}),window.addEventListener("pointerup",Y),window.addEventListener("pointercancel",Y)):H.forEach((e,t)=>{let{body:n}=t;e>0&&(n.addEventListener("pointerdown",W,{capture:!0}),n.addEventListener("pointermove",q))}))}function Q(e,t){U.forEach(n=>{let{setResizeHandlerState:r}=n;r(e,_.includes(n),t)})}function J(e,t){if(!e)throw console.error(t),Error(t)}function ee(e,t,n=10){return e.toFixed(n)===t.toFixed(n)?0:e>t?1:-1}function et(e,t,n=10){return 0===ee(e,t,n)}function en(e,t,n){return 0===ee(e,t,n)}function er({panelConstraints:e,panelIndex:t,size:n}){let r=e[t];J(null!=r,`Panel constraints not found for index ${t}`);let{collapsedSize:i=0,collapsible:o,maxSize:l=100,minSize:a=0}=r;return 0>ee(n,a)&&(n=o&&0>ee(n,(i+a)/2)?i:a),n=parseFloat((n=Math.min(l,n)).toFixed(10))}function ei({delta:e,initialLayout:t,panelConstraints:n,pivotIndices:r,prevLayout:i,trigger:o}){if(en(e,0))return t;let l=[...t],[a,s]=r;J(null!=a,"Invalid first pivot index"),J(null!=s,"Invalid second pivot index");let u=0;if("keyboard"===o){{let r=e<0?s:a,i=n[r];J(i,`Panel constraints not found for index ${r}`);let{collapsedSize:o=0,collapsible:l,minSize:u=0}=i;if(l){let n=t[r];if(J(null!=n,`Previous layout not found for panel index ${r}`),en(n,o)){let t=u-n;ee(t,Math.abs(e))>0&&(e=e<0?0-t:t)}}}{let r=e<0?a:s,i=n[r];J(i,`No panel constraints found for index ${r}`);let{collapsedSize:o=0,collapsible:l,minSize:u=0}=i;if(l){let n=t[r];if(J(null!=n,`Previous layout not found for panel index ${r}`),en(n,u)){let t=n-o;ee(t,Math.abs(e))>0&&(e=e<0?0-t:t)}}}}{let r=e<0?1:-1,i=e<0?s:a,o=0;for(;;){let e=t[i];if(J(null!=e,`Previous layout not found for panel index ${i}`),o+=er({panelConstraints:n,panelIndex:i,size:100})-e,(i+=r)<0||i>=n.length)break}let l=Math.min(Math.abs(e),Math.abs(o));e=e<0?0-l:l}{let r=e<0?a:s;for(;r>=0&&r<n.length;){let i=Math.abs(e)-Math.abs(u),o=t[r];J(null!=o,`Previous layout not found for panel index ${r}`);let a=er({panelConstraints:n,panelIndex:r,size:o-i});if(!en(o,a)&&(u+=o-a,l[r]=a,u.toPrecision(3).localeCompare(Math.abs(e).toPrecision(3),void 0,{numeric:!0})>=0))break;e<0?r--:r++}}if(function(e,t,n){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(!en(e[n],t[n],void 0))return!1;return!0}(i,l))return i;{let r=e<0?s:a,i=t[r];J(null!=i,`Previous layout not found for panel index ${r}`);let o=i+u,d=er({panelConstraints:n,panelIndex:r,size:o});if(l[r]=d,!en(d,o)){let t=o-d,r=e<0?s:a;for(;r>=0&&r<n.length;){let i=l[r];J(null!=i,`Previous layout not found for panel index ${r}`);let o=er({panelConstraints:n,panelIndex:r,size:i+t});if(en(i,o)||(t-=o-i,l[r]=o),en(t,0))break;e>0?r--:r++}}}return en(l.reduce((e,t)=>t+e,0),100)?l:i}function eo(e,t=document){return Array.from(t.querySelectorAll(`[data-panel-resize-handle-id][data-panel-group-id="${e}"]`))}function el(e,t,n=document){let r=eo(e,n).findIndex(e=>e.getAttribute("data-panel-resize-handle-id")===t);return null!=r?r:null}function ea(e,t,n){let r=el(e,t,n);return null!=r?[r,r+1]:[-1,-1]}function es(e,t=document){var n;if(t instanceof HTMLElement&&(null==t||null==(n=t.dataset)?void 0:n.panelGroupId)==e)return t;let r=t.querySelector(`[data-panel-group][data-panel-group-id="${e}"]`);return r||null}function eu(e,t=document){let n=t.querySelector(`[data-panel-resize-handle-id="${e}"]`);return n||null}function ed(e,t){let{x:n,y:r}=M(t);return"horizontal"===e?n:r}function ec(e,t,n){t.forEach((t,r)=>{let i=e[r];J(i,`Panel data not found for index ${r}`);let{callbacks:o,constraints:l,id:a}=i,{collapsedSize:s=0,collapsible:u}=l,d=n[a];if(null==d||t!==d){n[a]=t;let{onCollapse:e,onExpand:r,onResize:i}=o;i&&i(t,d),u&&(e||r)&&(r&&(null==d||et(d,s))&&!et(t,s)&&r(),e&&(null==d||!et(d,s))&&et(t,s)&&e())}})}function ep(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!=t[n])return!1;return!0}function eh(e){try{if("undefined"!=typeof localStorage)e.getItem=e=>localStorage.getItem(e),e.setItem=(e,t)=>{localStorage.setItem(e,t)};else throw Error("localStorage not supported in this environment")}catch(t){console.error(t),e.getItem=()=>null,e.setItem=()=>{}}}function ef(e){return`react-resizable-panels:${e}`}function eg(e,t,n,r,i){var o;let l=ef(e),a=t.map(e=>{let{constraints:t,id:n,idIsFromProps:r,order:i}=e;return r?n:i?`${i}:${JSON.stringify(t)}`:JSON.stringify(t)}).sort((e,t)=>e.localeCompare(t)).join(","),s=null!=(o=function(e,t){try{let n=ef(e),r=t.getItem(n);if(r){let e=JSON.parse(r);if("object"==typeof e&&null!=e)return e}}catch(e){}return null}(e,i))?o:{};s[a]={expandToSizes:Object.fromEntries(n.entries()),layout:r};try{i.setItem(l,JSON.stringify(s))}catch(e){console.error(e)}}let em={getItem:e=>(eh(em),em.getItem(e)),setItem:(e,t)=>{eh(em),em.setItem(e,t)}},ev={};function ey({autoSaveId:e=null,children:t,className:n="",direction:r,forwardedRef:i,id:l=null,onLayout:a=null,keyboardResizeBy:s=null,storage:d=em,style:h,tagName:v="div",...b}){let w=x(l),S=g(null),[C,R]=m(null),[P,E]=m([]),M=function(){let[e,t]=m(0);return u(()=>t(e=>e+1),[])}(),T=g({}),k=g(new Map),L=g(0),F=g({autoSaveId:e,direction:r,dragState:C,id:w,keyboardResizeBy:s,onLayout:a,storage:d}),O=g({layout:P,panelDataArray:[],panelDataArrayChanged:!1});g({didLogIdAndOrderWarning:!1,didLogPanelConstraintsWarning:!1,prevPanelIds:[]}),p(i,()=>({getId:()=>F.current.id,getLayout:()=>{let{layout:e}=O.current;return e},setLayout:e=>{let{onLayout:t}=F.current,{layout:n,panelDataArray:r}=O.current,i=function({layout:e,panelConstraints:t}){let n=[...e],r=n.reduce((e,t)=>e+t,0);if(n.length!==t.length)throw Error(`Invalid ${t.length} panel layout: ${n.map(e=>`${e}%`).join(", ")}`);if(!en(r,100)&&n.length>0)for(let e=0;e<t.length;e++){let t=n[e];J(null!=t,`No layout data found for index ${e}`);let i=100/r*t;n[e]=i}let i=0;for(let e=0;e<t.length;e++){let r=n[e];J(null!=r,`No layout data found for index ${e}`);let o=er({panelConstraints:t,panelIndex:e,size:r});r!=o&&(i+=r-o,n[e]=o)}if(!en(i,0))for(let e=0;e<t.length;e++){let r=n[e];J(null!=r,`No layout data found for index ${e}`);let o=er({panelConstraints:t,panelIndex:e,size:r+i});if(r!==o&&(i-=o-r,n[e]=o,en(i,0)))break}return n}({layout:e,panelConstraints:r.map(e=>e.constraints)});!function(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(n,i)&&(E(i),O.current.layout=i,t&&t(i),ec(r,i,T.current))}}),[]),function({committedValuesRef:e,eagerValuesRef:t,groupId:n,layout:r,panelDataArray:i,panelGroupElement:o,setLayout:l}){g({didWarnAboutMissingResizeHandle:!1}),c(()=>{if(!o)return;let e=t.current;J(e,"Eager values not found");let{panelDataArray:i}=e;J(null!=es(n,o),`No group found for id "${n}"`);let a=eo(n,o);J(a,`No resize handles found for group id "${n}"`);let s=a.map(e=>{let t=e.getAttribute("data-panel-resize-handle-id");J(t,"Resize handle element has no handle id attribute");let[a,s]=function(e,t,n,r=document){var i,o,l,a;let s=eu(t,r),u=eo(e,r),d=s?u.indexOf(s):-1;return[null!=(i=null==(o=n[d])?void 0:o.id)?i:null,null!=(l=null==(a=n[d+1])?void 0:a.id)?l:null]}(n,t,i,o);if(null==a||null==s)return()=>{};let u=e=>{if(!e.defaultPrevented&&"Enter"===e.key){e.preventDefault();let s=i.findIndex(e=>e.id===a);if(s>=0){let e=i[s];J(e,`No panel data found for index ${s}`);let a=r[s],{collapsedSize:u=0,collapsible:d,minSize:c=0}=e.constraints;if(null!=a&&d){let e=ei({delta:en(a,u)?c-u:u-a,initialLayout:r,panelConstraints:i.map(e=>e.constraints),pivotIndices:ea(n,t,o),prevLayout:r,trigger:"keyboard"});r!==e&&l(e)}}}};return e.addEventListener("keydown",u),()=>{e.removeEventListener("keydown",u)}});return()=>{s.forEach(e=>e())}},[o,e,t,n,r,i,l])}({committedValuesRef:F,eagerValuesRef:O,groupId:w,layout:P,panelDataArray:O.current.panelDataArray,setLayout:E,panelGroupElement:S.current}),c(()=>{let{panelDataArray:t}=O.current;if(e){if(0===P.length||P.length!==t.length)return;let n=ev[e];null==n&&(n=function(e,t=10){let n=null;return(...r)=>{null!==n&&clearTimeout(n),n=setTimeout(()=>{e(...r)},t)}}(eg,100),ev[e]=n),n(e,[...t],new Map(k.current),P,d)}},[e,P,d]),c(()=>{});let G=u(e=>{let{onLayout:t}=F.current,{layout:n,panelDataArray:r}=O.current;if(e.constraints.collapsible){let i=r.map(e=>e.constraints),{collapsedSize:o=0,panelSize:l,pivotIndices:a}=ex(r,e,n);if(J(null!=l,`Panel size not found for panel "${e.id}"`),!et(l,o)){k.current.set(e.id,l);let s=ei({delta:ew(r,e)===r.length-1?l-o:o-l,initialLayout:n,panelConstraints:i,pivotIndices:a,prevLayout:n,trigger:"imperative-api"});ep(n,s)||(E(s),O.current.layout=s,t&&t(s),ec(r,s,T.current))}}},[]),_=u((e,t)=>{let{onLayout:n}=F.current,{layout:r,panelDataArray:i}=O.current;if(e.constraints.collapsible){let o=i.map(e=>e.constraints),{collapsedSize:l=0,panelSize:a=0,minSize:s=0,pivotIndices:u}=ex(i,e,r),d=null!=t?t:s;if(et(a,l)){let t=k.current.get(e.id),l=null!=t&&t>=d?t:d,s=ei({delta:ew(i,e)===i.length-1?a-l:l-a,initialLayout:r,panelConstraints:o,pivotIndices:u,prevLayout:r,trigger:"imperative-api"});ep(r,s)||(E(s),O.current.layout=s,n&&n(s),ec(i,s,T.current))}}},[]),z=u(e=>{let{layout:t,panelDataArray:n}=O.current,{panelSize:r}=ex(n,e,t);return J(null!=r,`Panel size not found for panel "${e.id}"`),r},[]),H=u((e,t)=>{let{panelDataArray:n}=O.current,r=ew(n,e);return function({defaultSize:e,dragState:t,layout:n,panelData:r,panelIndex:i,precision:o=3}){let l,a=n[i];return{flexBasis:0,flexGrow:null==a?void 0!=e?e.toPrecision(o):"1":1===r.length?"1":a.toPrecision(o),flexShrink:1,overflow:"hidden",pointerEvents:null!==t?"none":void 0}}({defaultSize:t,dragState:C,layout:P,panelData:n,panelIndex:r})},[C,P]),U=u(e=>{let{layout:t,panelDataArray:n}=O.current,{collapsedSize:r=0,collapsible:i,panelSize:o}=ex(n,e,t);return J(null!=o,`Panel size not found for panel "${e.id}"`),!0===i&&et(o,r)},[]),W=u(e=>{let{layout:t,panelDataArray:n}=O.current,{collapsedSize:r=0,collapsible:i,panelSize:o}=ex(n,e,t);return J(null!=o,`Panel size not found for panel "${e.id}"`),!i||ee(o,r)>0},[]),q=u(e=>{let{panelDataArray:t}=O.current;t.push(e),t.sort((e,t)=>{let n=e.order,r=t.order;return null==n&&null==r?0:null==n?-1:null==r?1:n-r}),O.current.panelDataArrayChanged=!0,M()},[M]),Y=u(e=>{let t=!1,n=S.current;return n&&"rtl"===window.getComputedStyle(n,null).getPropertyValue("direction")&&(t=!0),function(n){var r,i;n.preventDefault();let o=S.current;if(!o)return()=>null;let{direction:l,dragState:a,id:s,keyboardResizeBy:u,onLayout:d}=F.current,{layout:c,panelDataArray:p}=O.current,{initialLayout:h}=null!=a?a:{},f=ea(s,e,o),g=function(e,t,n,r,i,o){if(A(e)){let t="horizontal"===n,r=0;r=e.shiftKey?100:null!=i?i:10;let o=0;switch(e.key){case"ArrowDown":o=t?0:r;break;case"ArrowLeft":o=t?-r:0;break;case"ArrowRight":o=t?r:0;break;case"ArrowUp":o=t?0:-r;break;case"End":o=100;break;case"Home":o=-100}return o}return null==r?0:function(e,t,n,r,i){let o="horizontal"===n,l=eu(t,i);J(l,`No resize handle element found for id "${t}"`);let a=l.getAttribute("data-panel-group-id");J(a,"Resize handle element has no group id attribute");let{initialCursorPosition:s}=r,u=ed(n,e),d=es(a,i);J(d,`No group element found for id "${a}"`);let c=d.getBoundingClientRect();return(u-s)/(o?c.width:c.height)*100}(e,t,n,r,o)}(n,e,l,a,u,o),m="horizontal"===l;m&&t&&(g=-g);let v=ei({delta:g,initialLayout:null!=h?h:c,panelConstraints:p.map(e=>e.constraints),pivotIndices:f,prevLayout:c,trigger:A(n)?"keyboard":"mouse-or-touch"}),y=!ep(c,v);(D(n)||I(n))&&L.current!=g&&((L.current=g,y||0===g)?$.set(e,0):m?(r=g<0?N:V,$.set(e,r)):(i=g<0?j:B,$.set(e,i))),y&&(E(v),O.current.layout=v,d&&d(v),ec(p,v,T.current))}},[]),X=u((e,t)=>{let{onLayout:n}=F.current,{layout:r,panelDataArray:i}=O.current,o=i.map(e=>e.constraints),{panelSize:l,pivotIndices:a}=ex(i,e,r);J(null!=l,`Panel size not found for panel "${e.id}"`);let s=ei({delta:ew(i,e)===i.length-1?l-t:t-l,initialLayout:r,panelConstraints:o,pivotIndices:a,prevLayout:r,trigger:"imperative-api"});ep(r,s)||(E(s),O.current.layout=s,n&&n(s),ec(i,s,T.current))},[]),K=u((e,t)=>{let{layout:n,panelDataArray:r}=O.current,{collapsedSize:i=0,collapsible:o}=t,{collapsedSize:l=0,collapsible:a,maxSize:s=100,minSize:u=0}=e.constraints,{panelSize:d}=ex(r,e,n);null!=d&&(o&&a&&et(d,i)?et(i,l)||X(e,l):d<u?X(e,u):d>s&&X(e,s))},[X]),Z=u((e,t)=>{let{direction:n}=F.current,{layout:r}=O.current;if(!S.current)return;let i=eu(e,S.current);J(i,`Drag handle element not found for id "${e}"`);let o=ed(n,t);R({dragHandleId:e,dragHandleRect:i.getBoundingClientRect(),initialCursorPosition:o,initialLayout:r})},[]),Q=u(()=>{R(null)},[]),el=u(e=>{let{panelDataArray:t}=O.current,n=ew(t,e);n>=0&&(t.splice(n,1),delete T.current[e.id],O.current.panelDataArrayChanged=!0,M())},[M]),eh=f(()=>({collapsePanel:G,direction:r,dragState:C,expandPanel:_,getPanelSize:z,getPanelStyle:H,groupId:w,isPanelCollapsed:U,isPanelExpanded:W,reevaluatePanelConstraints:K,registerPanel:q,registerResizeHandle:Y,resizePanel:X,startDragging:Z,stopDragging:Q,unregisterPanel:el,panelGroupElement:S.current}),[G,C,r,_,z,H,w,U,W,K,q,Y,X,Z,Q,el]);return o(y.Provider,{value:eh},o(v,{...b,children:t,className:n,id:l,ref:S,style:{display:"flex",flexDirection:"horizontal"===r?"row":"column",height:"100%",overflow:"hidden",width:"100%",...h},"data-panel-group":"","data-panel-group-direction":r,"data-panel-group-id":w}))}let eb=s((e,t)=>o(ey,{...e,forwardedRef:t}));function ew(e,t){return e.findIndex(e=>e===t||e.id===t.id)}function ex(e,t,n){let r=ew(e,t),i=r===e.length-1,o=n[r];return{...t.constraints,panelSize:o,pivotIndices:i?[r-1,r]:[r,r+1]}}function eS({children:e=null,className:t="",disabled:n=!1,hitAreaMargins:r,id:i,onBlur:l,onDragging:a,onFocus:s,style:u={},tabIndex:p=0,tagName:h="div",...f}){var v,b;let w=g(null),S=g({onDragging:a});c(()=>{S.current.onDragging=a});let C=d(y);if(null===C)throw Error("PanelResizeHandle components must be rendered within a PanelGroup container");let{direction:R,groupId:P,registerResizeHandle:E,startDragging:A,stopDragging:D,panelGroupElement:I}=C,M=x(i),[T,k]=m("inactive"),[L,F]=m(!1),[O,N]=m(null),V=g({state:T});c(()=>{if(n)N(null);else{let e=E(M);N(()=>e)}},[n,M,E]);let j=null!=(v=null==r?void 0:r.coarse)?v:15,B=null!=(b=null==r?void 0:r.fine)?b:5;return c(()=>{if(n||null==O)return;let e=w.current;return J(e,"Element ref not attached"),function(e,t,n,r,i){var o;let{ownerDocument:l}=t,a={direction:n,element:t,hitAreaMargins:r,setResizeHandlerState:i},s=null!=(o=H.get(l))?o:0;return H.set(l,s+1),U.add(a),Z(),function(){var t;$.delete(e),U.delete(a);let n=null!=(t=H.get(l))?t:1;if(H.set(l,n-1),Z(),1===n&&H.delete(l),_.includes(a)){let e=_.indexOf(a);e>=0&&_.splice(e,1),K(),i("up",!0,null)}}}(M,e,R,{coarse:j,fine:B},(e,t,n)=>{if(t)switch(e){case"down":{k("drag"),J(n,'Expected event to be defined for "down" action'),A(M,n);let{onDragging:e}=S.current;e&&e(!0);break}case"move":{let{state:e}=V.current;"drag"!==e&&k("hover"),J(n,'Expected event to be defined for "move" action'),O(n);break}case"up":{k("hover"),D();let{onDragging:e}=S.current;e&&e(!1)}}else k("inactive")})},[j,R,n,B,E,M,O,A,D]),!function({disabled:e,handleId:t,resizeHandler:n,panelGroupElement:r}){c(()=>{if(e||null==n||null==r)return;let i=eu(t,r);if(null==i)return;let o=e=>{if(!e.defaultPrevented)switch(e.key){case"ArrowDown":case"ArrowLeft":case"ArrowRight":case"ArrowUp":case"End":case"Home":e.preventDefault(),n(e);break;case"F6":{e.preventDefault();let n=i.getAttribute("data-panel-group-id");J(n,`No group element found for id "${n}"`);let o=eo(n,r),l=el(n,t,r);J(null!==l,`No resize element found for id "${t}"`);let a=e.shiftKey?l>0?l-1:o.length-1:l+1<o.length?l+1:0;o[a].focus()}}};return i.addEventListener("keydown",o),()=>{i.removeEventListener("keydown",o)}},[r,e,t,n])}({disabled:n,handleId:M,resizeHandler:O,panelGroupElement:I}),o(h,{...f,children:e,className:t,id:i,onBlur:()=>{F(!1),null==l||l()},onFocus:()=>{F(!0),null==s||s()},ref:w,role:"separator",style:{touchAction:"none",userSelect:"none",...u},tabIndex:p,"data-panel-group-direction":R,"data-panel-group-id":P,"data-resize-handle":"","data-resize-handle-active":"drag"===T?"pointer":L?"keyboard":void 0,"data-resize-handle-state":T,"data-panel-resize-handle-enabled":!n,"data-panel-resize-handle-id":M})}ey.displayName="PanelGroup",eb.displayName="forwardRef(PanelGroup)",eS.displayName="PanelResizeHandle"},98838:(e,t,n)=>{n.d(t,{W:()=>r});let r={}},99134:(e,t,n)=>{n.d(t,{Gt:()=>o,PP:()=>s,WG:()=>l,uv:()=>a});var r=n(69465),i=n(8006);let{schedule:o,cancel:l,state:a,steps:s}=(0,r.I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:i.l,!0)}};