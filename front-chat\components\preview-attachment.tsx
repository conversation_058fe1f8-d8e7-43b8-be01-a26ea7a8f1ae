import { Attachment } from "ai";

import { Loader2 } from "lucide-react";
import React from "react";

export const PreviewAttachment = ({
	attachment,
	isUploading = false,
	removeFile
}: {
	attachment: Attachment,
	isUploading?: boolean,
	removeFile?: (url: string) => void
}) => {
	const { name, url, contentType } = attachment;

	return (
		(<div className="flex flex-col gap-2 max-w-16">
			<div className="h-16 w-16 bg-muted rounded-md relative flex flex-col items-center justify-center">
				{contentType ? (
					contentType.startsWith("image") ? (
						// NOTE: it is recommended to use next/image for images
						// eslint-disable-next-line @next/next/no-img-element
						(<img
							key={url}
							src={url}
							alt={name ?? "An image attachment"}
							className="rounded-md size-full object-cover"
						/>)
					) : (
						<div className=""></div>
					)
				) : (
					<div className=""></div>
				)}

				{isUploading && (
					<div className="animate-spin absolute text-zinc-500">
						<Loader2 className="h-4 w-4 animate-spin text-gray-500" />
					</div>
				)}
				{removeFile &&
					<button
						type="button"
						onClick={() => removeFile(url)}
						className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity"
					>
						×
					</button>
				}
			</div>
			<div className="text-xs  max-w-16 truncate">{name}</div>
		</div>)
	);
};
