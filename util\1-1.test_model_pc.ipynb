{"cells": [{"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import requests\n", "import asyncio\n", "from pydantic import BaseModel\n", "from fastapi import APIRouter,Query\n", "from langchain.callbacks import AsyncIteratorCallbackHandler\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_openai import ChatOpenAI\n", "from fastapi.responses import StreamingResponse\n", "from langchain.schema import HumanMessage, SystemMessage, AIMessage\n", "from langchain_community.vectorstores import FAISS\n", "from typing import List, AsyncIterable\n", "from langchain_ollama import ChatOllama\n", "import os\n", "import csv\n", "import json\n", "from fastapi import APIRouter\n", "from langchain_community.embeddings.openai import OpenAIEmbeddings\n", "# from langchain_openai import OpenAIEmbeddings\n", "from langchain_community.document_loaders import PDFPlumberLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_core.prompts import PromptTemplate\n", "from fastapi import UploadFile, File, Form\n", "import shutil\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain_community.vectorstores import FAISS\n", "\n", "from dotenv import load_dotenv\n", "load_dotenv('./dot.env')\n", "\n", "router = APIRouter()\n", "class ChatHistory(BaseModel):\n", "    history: List\n", "\n", "#system_prompt\n", "#input_variable은 임의 설정\n", "name = '맵픽'\n", "language = 'korean'\n", "greeting = 'hihi'\n", "answer_length = 20000\n", "\n", "prompt_template = \"\"\"You're Mappick, you are helpful chatbot. You communicate exclusively in Korean language. \n", "\n", "            You'll provide answers that are 20000, straight to the point.\n", "            If I have a question related to the below context, I'll ask.\n", "\n", "            Here's the context you need to remember :\n", "            \n", "            Context:\n", "            -----------\n", "            {context}\n", "            -----------\n", "\n", "            If a question doesn't pertain to the context, you'll respond with 'I do not have any trained info regarding your question' in Korean language.\n", "            If my question doesn't belong to above context just refuse and say 'I do not have any trained info regarding your question' in Korean language.\n", "            Now, go ahead and answer my question: \n", "            Question: {question}\n", "\n", "            Answer in Korean:\"\"\"\n", "\n", "persist_directory = '../trained_db/Lk5521ILdWe9t18yi0zcJKU0teE3/chbt_ahiNhfU_all_embeddings'\n", "\n", "def stream_generate_from_open_source(system_prompt,query,model,docs):\n", "    found_data = ''\n", "    for i, doc in enumerate(docs):\n", "        found_data += str(f\"{i + 1}. {doc.page_content} \\n\")\n", "    system_prompt = system_prompt.replace(\"{context}\",found_data).replace(\"{question}\",query)\n", "    \n", "    url = 'http://localhost:11434/api/generate'\n", "    headers = {\n", "        'accept': 'application/json',\n", "        'Content-Type': 'application/json'\n", "    }\n", "    data = {\n", "        'prompt': system_prompt,\n", "        'model': model,\n", "        'stream' : <PERSON><PERSON><PERSON>,\n", "        'options':{\"temperature\": 0.0},\n", "        \"keep_alive\" : '1m'\n", "    }\n", "\n", "    response = requests.post(url, headers=headers, data=json.dumps(data))\n", "    \n", "    return response.json()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 사용가능한 모델 정보 출력"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NAME                                                       \tID          \tSIZE  \tMODIFIED     \n", "gemma2:27b-instruct-q8_0                                   \tdab5dca674db\t28 GB \t25 hours ago\t\n", "spow12_Qwen2-7B-ko-q8_0:latest                             \ta4a0fdcf640b\t8.1 GB\t32 hours ago\t\n", "deepseek-coder-v2:16b-lite-instruct-q8_0                   \t44250301ba51\t16 GB \t41 hours ago\t\n", "codestral:22b-v0.1-q8_0                                    \td805f7d07b03\t23 GB \t4 days ago  \t\n", "mistral-nemo:12b-instruct-2407-q8_0                        \t550a4a7f593a\t13 GB \t4 days ago  \t\n", "llama3.1:8b-instruct-q8_0                                  \t9b90f0f552e7\t8.5 GB\t4 days ago  \t\n", "nomic-embed-text:latest                                    \t0a109f422b47\t274 MB\t6 days ago  \t\n", "spow12_Qwen2-7B-ko-Instruct-orpo-ver_2.0_wo_chat-F16:latest\t3713f91a3557\t15 GB \t8 days ago  \t\n", "joongi007_Ko-Qwen2-7B-Instruct-GGUF:latest                 \t7f2cc98a7ef4\t15 GB \t8 days ago  \t\n", "QuantFactory_ko-gemma-2-9b-it-GGUF:latest                  \t8f110e7f7e3c\t9.8 GB\t8 days ago  \t\n"]}], "source": ["# ollama server에 등록되어 있는 모델 정보\n", "!ollama list"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NAME                                                       \tID          \tSIZE  \tMODIFIED     \n", "gemma2:27b-instruct-q8_0                                   \tdab5dca674db\t28 GB \t25 hours ago\t\n", "spow12_Qwen2-7B-ko-q8_0:latest                             \ta4a0fdcf640b\t8.1 GB\t32 hours ago\t\n", "deepseek-coder-v2:16b-lite-instruct-q8_0                   \t44250301ba51\t16 GB \t41 hours ago\t\n", "codestral:22b-v0.1-q8_0                                    \td805f7d07b03\t23 GB \t4 days ago  \t\n", "mistral-nemo:12b-instruct-2407-q8_0                        \t550a4a7f593a\t13 GB \t4 days ago  \t\n", "llama3.1:8b-instruct-q8_0                                  \t9b90f0f552e7\t8.5 GB\t4 days ago  \t\n", "nomic-embed-text:latest                                    \t0a109f422b47\t274 MB\t6 days ago  \t\n", "spow12_Qwen2-7B-ko-Instruct-orpo-ver_2.0_wo_chat-F16:latest\t3713f91a3557\t15 GB \t8 days ago  \t\n", "joongi007_Ko-Qwen2-7B-Instruct-GGUF:latest                 \t7f2cc98a7ef4\t15 GB \t8 days ago  \t\n", "QuantFactory_ko-gemma-2-9b-it-GGUF:latest                  \t8f110e7f7e3c\t9.8 GB\t8 days ago  \t\n"]}], "source": ["!ollama list"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["li= [\n", "'gemma2:27b-instruct-q8_0', \n", "]"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["embeddings = OpenAIEmbeddings()\n", "\n", "# gpt 기준 v2\n", "file_path = './train_data/org_v2_지도마법사하_240806.txt'\n", "\n", "loader = TextLoader(file_path,encoding='utf-8')\n", "documents = loader.load()\n", "# text_splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=20)\n", "# split_docs = text_splitter.split_documents(documents)\n", "\n", "Vectordb = FAISS.from_documents(documents, embeddings)\n", "\n", "retriever = Vectordb.as_retriever(search_type=\"mmr\")\n", "\n", "query = \"줌 생성과 축척을 반영한 배경지도 코드 보여줘\"\n", "# query = \"줌 생성과 축척을 반영한 배경지도 코드 보여줘\"\n", "prompt = PromptTemplate(template=prompt_template)\n", "for i in li:\n", "    if 'gpt' in i:\n", "        llm = ChatOpenAI(model=i)\n", "    else:\n", "        llm = ChatOllama(model=i)\n", "    chain = (\n", "        {\"question\": RunnablePassthrough(),\n", "        \"context\": retriever\n", "        }\n", "        | prompt\n", "        | llm\n", "    )\n", "    answer = chain.invoke(query)\n", "    print(answer)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "mppckbt", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}