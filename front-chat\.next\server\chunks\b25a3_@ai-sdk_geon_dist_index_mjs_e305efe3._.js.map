{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bgeon%400.1.5_react%4019.0.0_zod%403.24.1/node_modules/%40ai-sdk/geon/src/geon-chat-language-model.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bgeon%400.1.5_react%4019.0.0_zod%403.24.1/node_modules/%40ai-sdk/geon/src/convert-to-geon-chat-messages.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bgeon%400.1.5_react%4019.0.0_zod%403.24.1/node_modules/%40ai-sdk/geon/src/map-geon-finish-reason.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bgeon%400.1.5_react%4019.0.0_zod%403.24.1/node_modules/%40ai-sdk/geon/src/geon-error.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bgeon%400.1.5_react%4019.0.0_zod%403.24.1/node_modules/%40ai-sdk/geon/src/util.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bgeon%400.1.5_react%4019.0.0_zod%403.24.1/node_modules/%40ai-sdk/geon/src/get-response-metadata.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bgeon%400.1.5_react%4019.0.0_zod%403.24.1/node_modules/%40ai-sdk/geon/src/geon-prepare-tools.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bgeon%400.1.5_react%4019.0.0_zod%403.24.1/node_modules/%40ai-sdk/geon/src/geon-provider.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bgeon%400.1.5_react%4019.0.0_zod%403.24.1/node_modules/%40ai-sdk/geon/src/geon-transcription-model.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bgeon%400.1.5_react%4019.0.0_zod%403.24.1/node_modules/%40ai-sdk/geon/src/responses/geon-responses-language-model.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bgeon%400.1.5_react%4019.0.0_zod%403.24.1/node_modules/%40ai-sdk/geon/src/responses/convert-to-geon-responses-messages.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bgeon%400.1.5_react%4019.0.0_zod%403.24.1/node_modules/%40ai-sdk/geon/src/responses/map-geon-responses-finish-reason.ts", "file:///C:/chatbot/front-chat/node_modules/.pnpm/%40ai-sdk%2Bgeon%400.1.5_react%4019.0.0_zod%403.24.1/node_modules/%40ai-sdk/geon/src/responses/geon-responses-prepare-tools.ts"], "sourcesContent": ["import {\n  InvalidResponseDataError,\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1FunctionToolCall,\n  LanguageModelV1ProviderMetadata,\n  LanguageModelV1StreamPart,\n} from \"@ai-sdk/provider\";\nimport {\n  ParseResult,\n  combineHeaders,\n  postJsonToApi,\n  isParsableJson,\n  createJsonResponseHandler,\n} from \"@ai-sdk/provider-utils\";\nimport { z } from \"zod\";\nimport { convertToGeonChatMessages } from \"./convert-to-geon-chat-messages\";\nimport { mapGeonFinishReason } from \"./map-geon-finish-reason\";\nimport { GeonChatModelId, GeonChatSettings } from \"./geon-chat-settings\";\nimport { geonErrorDataSchema, geonFailedResponseHandler } from \"./geon-error\";\nimport {\n  createEventSourceResponseHandler,\n  createJsonStreamResponseHandler,\n} from \"./util\";\nimport { getResponseMetadata } from \"./get-response-metadata\";\nimport { generateId } from \"@ai-sdk/provider-utils\";\nimport { prepareTools } from \"./geon-prepare-tools\";\n\nexport interface Model {\n  id: string;\n  label: string;\n  apiIdentifier: string;\n  description: string;\n}\n\nexport const openWebUIModels: Array<Model> = [\n  {\n    id: \"dev\",\n    label: \"dev\",\n    apiIdentifier: \"dev\",\n    description: \"ODF 지도 문서를 지식베이스로 가지고 있는 모델\",\n  },\n  {\n    id: \"geon-coder\",\n    label: \"geon-coder\",\n    apiIdentifier: \"qwen-coder\",\n    description: \"ODF 지도 문서 + 프롬프트 적용 모델\",\n  },\n] as const;\n\ntype GeonChatConfig = {\n  provider: string;\n  baseURL: string;\n  headers: () => Record<string, string | undefined>;\n  generateId: () => string;\n  fetch?: typeof fetch;\n};\n\nexport class GeonChatLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = \"v1\";\n  readonly defaultObjectGenerationMode = \"json\";\n\n  readonly modelId: GeonChatModelId;\n  readonly settings: GeonChatSettings;\n\n  private readonly config: GeonChatConfig;\n\n  constructor(\n    modelId: GeonChatModelId,\n    settings: GeonChatSettings,\n    config: GeonChatConfig\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  private getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    providerMetadata,\n  }: Parameters<LanguageModelV1[\"doGenerate\"]>[0]) {\n    const type = mode.type;\n\n    const warnings: LanguageModelV1CallWarning[] = [];\n\n    if (topK != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"topK\",\n      });\n    }\n\n    if (\n      responseFormat != null &&\n      responseFormat.type === \"json\" &&\n      responseFormat.schema != null\n    ) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"responseFormat\",\n        details: \"JSON response format schema is not supported\",\n      });\n    }\n\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n\n      // model specific settings:\n      //   user: this.settings.user,\n      //   parallel_tool_calls: this.settings.parallelToolCalls,\n\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      stop: stopSequences,\n      seed,\n\n      // openai specific settings:\n      max_completion_tokens:\n        providerMetadata?.geon?.maxCompletionTokens ?? undefined,\n      store: providerMetadata?.geon?.store ?? undefined,\n      metadata: providerMetadata?.geon?.metadata ?? undefined,\n\n      // response format:\n      response_format:\n        responseFormat?.type === \"json\" ? { type: \"json_object\" } : undefined,\n\n      // messages:\n      messages: convertToGeonChatMessages(prompt),\n    };\n\n    // reasoning models have fixed params, remove them if they are set:\n    // if (isReasoningModel(this.modelId)) {\n    //   baseArgs.temperature = undefined;\n    //   baseArgs.top_p = undefined;\n    //   baseArgs.frequency_penalty = undefined;\n    //   baseArgs.presence_penalty = undefined;\n    // }\n\n    switch (type) {\n      case \"regular\": {\n        const { tools, tool_choice, toolWarnings } = prepareTools(mode, false);\n\n        return {\n          args: { ...baseArgs, tools, tool_choice },\n          warnings: [...warnings, ...toolWarnings],\n        };\n      }\n\n      case \"object-json\": {\n        return {\n          args: {\n            ...baseArgs,\n            response_format: { type: \"json_object\" },\n          },\n          warnings,\n        };\n      }\n\n      case \"object-tool\": {\n        return {\n          args: {\n            ...baseArgs,\n            tool_choice: \"any\",\n            tools: [{ type: \"function\", function: mode.tool }],\n          },\n          warnings,\n        };\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1[\"doGenerate\"]>[0]\n  ): Promise<Awaited<ReturnType<LanguageModelV1[\"doGenerate\"]>>> {\n    const { args, warnings } = this.getArgs(options);\n    const body = {\n      ...args,\n      stream: false,\n    };\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      // url: `${this.config.baseURL}/chat/completions`,\n      url: `${this.config.baseURL}/chat/completions`,\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: geonFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        geonChatResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n    const { messages: rawPrompt, ...rawSettings } = body;\n\n    // OpenAI 형식 응답인지 확인\n    if (\n      response.choices &&\n      Array.isArray(response.choices) &&\n      response.choices.length > 0\n    ) {\n      const choice = response.choices[0];\n\n      // provider metadata:\n      const completionTokenDetails = response.usage?.completion_tokens_details;\n      const promptTokenDetails = response.usage?.prompt_tokens_details;\n      const providerMetadata: LanguageModelV1ProviderMetadata = { openai: {} };\n      if (\n        completionTokenDetails?.reasoning_tokens != null &&\n        providerMetadata.openai\n      ) {\n        providerMetadata.openai.reasoningTokens =\n          completionTokenDetails?.reasoning_tokens;\n      }\n      if (\n        completionTokenDetails?.accepted_prediction_tokens != null &&\n        providerMetadata.openai\n      ) {\n        providerMetadata.openai.acceptedPredictionTokens =\n          completionTokenDetails?.accepted_prediction_tokens;\n      }\n      if (\n        completionTokenDetails?.rejected_prediction_tokens != null &&\n        providerMetadata.openai\n      ) {\n        providerMetadata.openai.rejectedPredictionTokens =\n          completionTokenDetails?.rejected_prediction_tokens;\n      }\n      if (\n        promptTokenDetails?.cached_tokens != null &&\n        providerMetadata.openai\n      ) {\n        providerMetadata.openai.cachedPromptTokens =\n          promptTokenDetails?.cached_tokens;\n      }\n\n      return {\n        text: choice?.message?.content ?? \"\",\n        toolCalls: choice?.message?.function_call\n          ? [\n              {\n                toolCallType: \"function\",\n                toolCallId: generateId(),\n                toolName:\n                  choice?.message?.function_call?.name ?? \"unknown_tool\",\n                args: choice?.message?.function_call?.arguments ?? \"\",\n              },\n            ]\n          : choice?.message?.tool_calls?.map((toolCall) => ({\n              toolCallType: \"function\",\n              toolCallId: toolCall.id ?? generateId(),\n              toolName: toolCall.function.name ?? \"unknown_tool\",\n              args: toolCall.function.arguments!,\n            })),\n        finishReason: mapGeonFinishReason(choice?.finish_reason ?? \"\"),\n        usage: {\n          promptTokens: response.usage?.prompt_tokens ?? NaN,\n          completionTokens: response.usage?.completion_tokens ?? NaN,\n        },\n        rawCall: { rawPrompt, rawSettings },\n        rawResponse: { headers: responseHeaders },\n        request: { body: JSON.stringify(body) },\n        response: getResponseMetadata(response),\n        providerMetadata,\n      };\n    }\n\n    // Geon API 형식 응답 처리 (기존 로직)\n    const toolCalls: LanguageModelV1FunctionToolCall[] | undefined =\n      response.message?.tool_calls?.map((toolCall) => ({\n        args: JSON.stringify(toolCall.function.arguments),\n        toolCallId: toolCall.id ?? generateId(),\n        toolCallType: \"function\",\n        toolName: toolCall.function.name ?? \"unknown_tool\",\n      }));\n\n    const content = response.message?.content ?? \"\";\n    const thinkMatch = content.match(/<think>(.*?)<\\/think>/);\n\n    return {\n      finishReason: mapGeonFinishReason(response.done_reason),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      request: { body: JSON.stringify(body) },\n      text: content.replace(/<think>.*?<\\/think>/, \"\").trim(),\n      ...(thinkMatch && {\n        reasoning: thinkMatch[1]?.trim(),\n      }),\n      reasoning: response.choices?.[0]?.message.reasoning_content ?? undefined,\n      toolCalls,\n      usage: {\n        completionTokens: response.eval_count || 0,\n        promptTokens: response.prompt_eval_count || 0,\n      },\n      warnings,\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1[\"doStream\"]>[0]\n  ): Promise<Awaited<ReturnType<LanguageModelV1[\"doStream\"]>>> {\n    const { args, warnings } = this.getArgs(options);\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: `${this.config.baseURL}/chat/completions`,\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: {\n        ...args,\n        ...(typeof args.metadata === \"object\" && args.metadata !== null\n          ? args.metadata\n          : {}),\n        stream: true,\n      },\n      failedResponseHandler: geonFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        geonChatChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n\n    const toolCalls: Array<{\n      id: string;\n      type: \"function\";\n      function: {\n        name: string;\n        arguments: string;\n      };\n      hasFinished: boolean;\n    }> = [];\n\n    let finishReason: LanguageModelV1FinishReason = \"unknown\";\n    let usage: {\n      promptTokens: number | undefined;\n      completionTokens: number | undefined;\n    } = {\n      promptTokens: undefined,\n      completionTokens: undefined,\n    };\n    // let logprobs: LanguageModelV1LogProbs;\n    let isFirstChunk = true;\n\n    // const { useLegacyFunctionCalling } = this.settings;\n\n    let providerMetadata: LanguageModelV1ProviderMetadata | undefined;\n    let isThinking = false;\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof geonChatChunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            // handle failed chunk parsing / validation:\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n            \n            // handle error chunks:\n            if (\"error\" in value) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: value.error });\n              return;\n            }\n\n            // OpenAI 형식 처리: choices 배열이 있는지 확인\n            if (\n              value.choices &&\n              Array.isArray(value.choices) &&\n              value.choices.length > 0\n            ) {\n              // OpenAI 형식 처리 로직\n              const choice = value.choices[0];\n\n              if (choice?.finish_reason != null) {\n                finishReason = mapGeonFinishReason(choice.finish_reason);\n              }\n\n              if (choice?.delta == null) {\n                return;\n              }\n\n              const delta = choice.delta;\n\n              if (delta.reasoning_content != null) {\n                controller.enqueue({\n                  type: 'reasoning',\n                  textDelta: delta.reasoning_content,\n                });\n              }\n\n              if (delta.content != null) {\n                controller.enqueue({\n                  type: \"text-delta\",\n                  textDelta: delta.content,\n                });\n              }\n\n              // const mappedLogprobs = mapOpenAIChatLogProbsOutput(\n              //   choice?.logprobs,\n              // );\n              // if (mappedLogprobs?.length) {\n              //   if (logprobs === undefined) logprobs = [];\n              //   logprobs.push(...mappedLogprobs);\n              // }\n\n              const mappedToolCalls: typeof delta.tool_calls =\n                delta.function_call != null\n                  ? [\n                      {\n                        type: \"function\",\n                        id: generateId(),\n                        function: delta.function_call,\n                        index: 0,\n                      },\n                    ]\n                  : delta.tool_calls;\n\n              if (mappedToolCalls != null) {\n                for (const toolCallDelta of mappedToolCalls) {\n                  const index = toolCallDelta.index || choice.index;\n\n                  // Tool call start. OpenAI returns all information except the arguments in the first chunk.\n                  if (toolCalls[index] == null) {\n                    if (toolCallDelta.type !== \"function\") {\n                      throw new InvalidResponseDataError({\n                        data: toolCallDelta,\n                        message: `Expected 'function' type.`,\n                      });\n                    }\n\n                    if (toolCallDelta.id == null) {\n                      throw new InvalidResponseDataError({\n                        data: toolCallDelta,\n                        message: `Expected 'id' to be a string.`,\n                      });\n                    }\n\n                    if (toolCallDelta.function?.name == null) {\n                      throw new InvalidResponseDataError({\n                        data: toolCallDelta,\n                        message: `Expected 'function.name' to be a string.`,\n                      });\n                    }\n\n                    toolCalls[index] = {\n                      id: toolCallDelta.id,\n                      type: \"function\",\n                      function: {\n                        name: toolCallDelta.function.name,\n                        arguments: toolCallDelta.function.arguments ?? \"\",\n                      },\n                      hasFinished: false,\n                    };\n\n                    const toolCall = toolCalls[index];\n\n                    if (\n                      toolCall.function?.name != null &&\n                      toolCall.function?.arguments != null\n                    ) {\n                      // send delta if the argument text has already started:\n                      if (toolCall.function.arguments.length > 0) {\n                        controller.enqueue({\n                          type: \"tool-call-delta\",\n                          toolCallType: \"function\",\n                          toolCallId: toolCall.id,\n                          toolName: toolCall.function.name,\n                          argsTextDelta: toolCall.function.arguments,\n                        });\n                      }\n\n                      // check if tool call is complete\n                      // (some providers send the full tool call in one chunk):\n                      if (isParsableJson(toolCall.function.arguments)) {\n                        controller.enqueue({\n                          type: \"tool-call\",\n                          toolCallType: \"function\",\n                          toolCallId: toolCall.id ?? generateId(),\n                          toolName: toolCall.function.name,\n                          args: toolCall.function.arguments,\n                        });\n                        toolCall.hasFinished = true;\n                      }\n                    }\n\n                    continue;\n                  }\n\n                  // existing tool call, merge if not finished\n                  const toolCall = toolCalls[index];\n\n                  if (toolCallDelta.function?.arguments != null) {\n                    toolCall.function!.arguments +=\n                      toolCallDelta.function?.arguments ?? \"\";\n                  }\n\n                  // send delta\n                  controller.enqueue({\n                    type: \"tool-call-delta\",\n                    toolCallType: \"function\",\n                    toolCallId: toolCall.id,\n                    toolName: toolCall.function.name,\n                    argsTextDelta: toolCallDelta.function.arguments ?? \"\",\n                  });\n\n                  // check if tool call is complete\n                  if (\n                    toolCall.function?.name != null &&\n                    toolCall.function?.arguments != null &&\n                    isParsableJson(toolCall.function.arguments)\n                  ) {\n                    controller.enqueue({\n                      type: \"tool-call\",\n                      toolCallType: \"function\",\n                      toolCallId: toolCall.id ?? generateId(),\n                      toolName: toolCall.function.name,\n                      args: toolCall.function.arguments,\n                    });\n                    toolCall.hasFinished = true;\n                  }\n                }\n              }\n\n              return; // OpenAI 처리 완료 후 종료\n            }\n\n            // Geon API 형식 처리 (기존 로직)\n            if (isFirstChunk) {\n              isFirstChunk = false;\n              controller.enqueue({\n                type: \"response-metadata\",\n                ...getResponseMetadata(value),\n              });\n            }\n\n            if (value.sources != null) {\n              controller.enqueue({\n                type: \"tool-call\",\n                toolCallType: \"function\",\n                toolCallId: generateId(),\n                toolName: \"knowledgeBase\",\n                args: JSON.stringify(value.sources),\n              });\n            }\n\n            // finish_reason openai, done_reason ollama\n\n            if (value.done_reason != null) {\n              finishReason = mapGeonFinishReason(value.done_reason);\n              if (finishReason != \"stop\") {\n                controller.enqueue({\n                  type: \"finish\",\n                  finishReason,\n                  usage: {\n                    promptTokens: usage.promptTokens ?? NaN,\n                    completionTokens: usage.completionTokens ?? NaN,\n                  },\n                  ...(providerMetadata != null ? { providerMetadata } : {}),\n                });\n              }\n            }\n\n            // message: openwebui or ollama, content: legacy\n            if (value.message != null) {\n              const content = value.message.content ?? \"\";\n              // <think> 태그가 있으면 reasoning으로 전달\n              if (isThinking || content.includes(\"<think>\")) {\n                isThinking = true;\n                if (content.includes(\"</think>\")) {\n                  isThinking = false;\n                }\n                // <think> </think> 태그 제외하고 내용만 전달\n                const cleanContent = content\n                  .replace(\"<think>\", \"\")\n                  .replace(\"</think>\", \"\");\n                controller.enqueue({\n                  type: \"reasoning\",\n                  textDelta: cleanContent,\n                });\n              } else {\n                controller.enqueue({\n                  type: \"text-delta\",\n                  textDelta: content,\n                });\n              }\n\n              if (value.message.tool_calls != null) {\n                for (const toolCall of value.message.tool_calls) {\n                  if (\n                    toolCall.function?.name != null &&\n                    toolCall.function?.arguments != null\n                  ) {\n                    const args =\n                      typeof toolCall.function.arguments === \"object\"\n                        ? JSON.stringify(toolCall.function.arguments)\n                        : isParsableJson(toolCall.function.arguments)\n                          ? toolCall.function.arguments\n                          : null;\n\n                    controller.enqueue({\n                      type: \"tool-call\",\n                      toolCallType: \"function\",\n                      toolCallId: toolCall.id ?? generateId(),\n                      toolName: toolCall.function.name,\n                      args: args,\n                    });\n                  }\n                }\n              }\n            }\n\n            if (value.content != null) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: value.content,\n              });\n            }\n\n            if (value.tool_calls != null) {\n              for (const toolCallDelta of value.tool_calls) {\n                const index = toolCallDelta.index;\n                if (index == null) {\n                  throw new InvalidResponseDataError({\n                    data: toolCallDelta,\n                    message: `Expected 'index' to be a number.`,\n                  });\n                }\n                // Tool call start. OpenAI returns all information except the arguments in the first chunk.\n                if (toolCalls[index] == null) {\n                  if (toolCallDelta.type !== \"function\") {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function' type.`,\n                    });\n                  }\n\n                  if (toolCallDelta.id == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'id' to be a string.`,\n                    });\n                  }\n\n                  if (toolCallDelta.function?.name == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function.name' to be a string.`,\n                    });\n                  }\n\n                  toolCalls[index] = {\n                    id: toolCallDelta.id,\n                    type: \"function\",\n                    hasFinished: false,\n                    function: {\n                      name: toolCallDelta.function.name,\n                      arguments: toolCallDelta.function.arguments ?? \"\",\n                    },\n                  };\n\n                  const toolCall = toolCalls[index];\n\n                  if (\n                    toolCall.function?.name != null &&\n                    toolCall.function?.arguments != null\n                  ) {\n                    // send delta if the argument text has already started:\n                    if (toolCall.function.arguments.length > 0) {\n                      controller.enqueue({\n                        type: \"tool-call-delta\",\n                        toolCallType: \"function\",\n                        toolCallId: toolCall.id,\n                        toolName: toolCall.function.name,\n                        argsTextDelta: toolCall.function.arguments,\n                      });\n                    }\n\n                    // check if tool call is complete\n                    // (some providers send the full tool call in one chunk):\n                    if (isParsableJson(toolCall.function.arguments)) {\n                      controller.enqueue({\n                        type: \"tool-call\",\n                        toolCallType: \"function\",\n                        toolCallId: toolCall.id ?? generateId(),\n                        toolName: toolCall.function.name,\n                        args: toolCall.function.arguments,\n                      });\n                    }\n                  }\n\n                  continue;\n                }\n\n                // existing tool call, merge\n                const toolCall = toolCalls[index];\n\n                if (toolCallDelta.function?.arguments != null) {\n                  toolCall.function!.arguments +=\n                    toolCallDelta.function?.arguments ?? \"\";\n                }\n\n                // send delta\n                controller.enqueue({\n                  type: \"tool-call-delta\",\n                  toolCallType: \"function\",\n                  toolCallId: toolCall.id,\n                  toolName: toolCall.function.name,\n                  argsTextDelta: toolCallDelta.function.arguments ?? \"\",\n                });\n\n                // check if tool call is complete\n                if (\n                  toolCall.function?.name != null &&\n                  toolCall.function?.arguments != null &&\n                  isParsableJson(toolCall.function.arguments)\n                ) {\n                  controller.enqueue({\n                    type: \"tool-call\",\n                    toolCallType: \"function\",\n                    toolCallId: toolCall.id ?? generateId(),\n                    toolName: toolCall.function.name,\n                    args: toolCall.function.arguments,\n                  });\n                }\n              }\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              // logprobs,\n              usage: {\n                promptTokens: usage.promptTokens ?? NaN,\n                completionTokens: usage.completionTokens ?? NaN,\n              },\n              ...(providerMetadata != null ? { providerMetadata } : {}),\n            });\n          },\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n    };\n  }\n}\n\nconst openaiTokenUsageSchema = z\n  .object({\n    prompt_tokens: z.number().nullish(),\n    completion_tokens: z.number().nullish(),\n    prompt_tokens_details: z\n      .object({\n        cached_tokens: z.number().nullish(),\n      })\n      .nullish(),\n    completion_tokens_details: z\n      .object({\n        reasoning_tokens: z.number().nullish(),\n        accepted_prediction_tokens: z.number().nullish(),\n        rejected_prediction_tokens: z.number().nullish(),\n      })\n      .nullish(),\n  })\n  .nullish();\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst geonChatResponseSchema = z.object({\n  id: z.string().nullish(),\n  created: z.number().nullish(),\n  content: z.string().nullish(),\n  tool_calls: z\n    .array(\n      z.object({\n        index: z.number().nullish(),\n        id: z.string().nullish(),\n        type: z.string().nullish(),\n        function: z.object({\n          name: z.string().nullish(),\n          arguments: z.string().nullish(),\n        }),\n      })\n    )\n    .nullish(),\n  message: z\n    .object({\n      role: z.enum([\"assistant\"]).nullish(),\n      content: z.string().nullish(),\n      function_call: z\n        .object({\n          name: z.string().optional(),\n          arguments: z.string().optional(),\n        })\n        .nullish(),\n      tool_calls: z\n        .array(\n          z.object({\n            index: z.number().nullish(),\n            id: z.string().nullish(),\n            type: z.literal(\"function\").optional(),\n            function: z.object({\n              name: z.string().nullish(),\n              arguments: z.any().nullish(),\n            }),\n          })\n        )\n        .nullish(),\n    })\n    .nullish(),\n  // general metadata\n  model: z.string(),\n  done: z.literal(true).nullish(),\n  done_reason: z.string().optional().nullable(),\n  eval_count: z.number().nullish(),\n  eval_duration: z.number().nullish(),\n  prompt_eval_count: z.number().optional(),\n  prompt_eval_duration: z.number().optional(),\n  load_duration: z.number().optional(),\n  finish_reason: z.string().nullable().optional(),\n  total_duration: z.number().nullish(),\n  choices: z\n    .array(\n      z.object({\n        message: z.object({\n          role: z.literal(\"assistant\").nullish(),\n          content: z.string().nullish(),\n          reasoning_content: z.string().nullish(),\n          function_call: z\n            .object({\n              arguments: z.string(),\n              name: z.string(),\n            })\n            .nullish(),\n          tool_calls: z\n            .array(\n              z.object({\n                id: z.string().nullish(),\n                type: z.literal(\"function\"),\n                function: z.object({\n                  name: z.string(),\n                  arguments: z.string(),\n                }),\n              })\n            )\n            .nullish(),\n        }),\n        index: z.number(),\n        logprobs: z\n          .object({\n            content: z\n              .array(\n                z.object({\n                  token: z.string(),\n                  logprob: z.number(),\n                  top_logprobs: z.array(\n                    z.object({\n                      token: z.string(),\n                      logprob: z.number(),\n                    })\n                  ),\n                })\n              )\n              .nullable(),\n          })\n          .nullish(),\n        finish_reason: z.string().nullish(),\n      })\n    )\n    .nullish(),\n  usage: openaiTokenUsageSchema,\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst geonChatChunkSchema = z.union([\n  z.object({\n    // general metadata\n    id: z.string().nullish(),\n    created: z.number().nullish(),\n    model: z.string().nullish(),\n\n    sources: z\n      .array(\n        z.object({\n          source: z.object({\n            id: z.string(),\n            user_id: z.string(),\n            name: z.string(),\n            description: z.string(),\n            data: z.any().optional(),\n            meta: z.any().nullable(),\n            access_control: z.any().nullable(),\n            created_at: z.number(),\n            updated_at: z.number(),\n            user: z.object({\n              id: z.string(),\n              name: z.string(),\n              email: z.string(),\n              role: z.string(),\n              profile_image_url: z.string(),\n            }),\n            files: z.array(\n              z.object({\n                id: z.string(),\n                meta: z.object({\n                  name: z.string(),\n                  content_type: z.string(),\n                  size: z.number(),\n                  collection_name: z.string(),\n                }),\n                created_at: z.number(),\n                updated_at: z.number(),\n              })\n            ),\n            type: z.string(),\n          }),\n          document: z.array(z.string()),\n          metadata: z.array(\n            z.object({\n              created_by: z.string(),\n              embedding_config: z.string(),\n              file_id: z.string(),\n              hash: z.string(),\n              name: z.string(),\n              source: z.string(),\n              start_index: z.number(),\n              score: z.number(),\n            })\n          ),\n          distances: z.array(z.number()),\n        })\n      )\n      .optional(),\n\n    // open-web-ui, ollama specific message\n    message: z\n      .object({\n        role: z.enum([\"assistant\"]).nullish(),\n        content: z.string().nullish(),\n        function_call: z\n          .object({\n            name: z.string().optional(),\n            arguments: z.string().optional(),\n          })\n          .nullish(),\n        tool_calls: z\n          .array(\n            z.object({\n              index: z.number().nullish(),\n              id: z.string().nullish(),\n              type: z.literal(\"function\").optional(),\n              function: z.object({\n                name: z.string().nullish(),\n                arguments: z.any().nullish(),\n              }),\n            })\n          )\n          .nullish(),\n      })\n      .nullish(),\n\n    // legacy message, to be deprecated...\n    content: z.string().nullish(),\n    tool_calls: z\n      .array(\n        z.object({\n          index: z.number().nullish(),\n          id: z.string().nullish(),\n          type: z.string().nullish(),\n          function: z.object({\n            name: z.string().nullish(),\n            arguments: z.string().nullish(),\n          }),\n        })\n      )\n      .nullish(),\n\n    // openai compatible response\n    choices: z\n      .array(\n        z.object({\n          delta: z\n            .object({\n              role: z.enum([\"assistant\"]).nullish(),\n              content: z.string().nullish(),\n              reasoning_content: z.string().nullish(),\n              function_call: z\n                .object({\n                  name: z.string().optional(),\n                  arguments: z.string().optional(),\n                })\n                .nullish(),\n              tool_calls: z\n                .array(\n                  z.object({\n                    index: z.number().nullish(),\n                    id: z.string().nullish(),\n                    type: z.literal(\"function\").optional(),\n                    function: z.object({\n                      name: z.string().nullish(),\n                      arguments: z.string().nullish(),\n                    }),\n                  })\n                )\n                .nullish(),\n            })\n            .nullish(),\n          logprobs: z\n            .object({\n              content: z\n                .array(\n                  z.object({\n                    token: z.string(),\n                    logprob: z.number(),\n                    top_logprobs: z.array(\n                      z.object({\n                        token: z.string(),\n                        logprob: z.number(),\n                      })\n                    ),\n                  })\n                )\n                .nullable(),\n            })\n            .nullish(),\n          finish_reason: z.string().nullable().optional(),\n          index: z.number(),\n        })\n      )\n      .nullish(),\n    usage: openaiTokenUsageSchema,\n    // finish reason\n    finish_reason: z.string().nullable().optional(),\n    done_reason: z.string().nullable().optional(),\n    done: z.boolean().nullable().optional(),\n    detail: z.any().nullish(),\n  }),\n  geonErrorDataSchema,\n]);\n", "import {\n  LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from \"@ai-sdk/provider\";\nimport { GeonChatPrompt } from \"./geon-chat-prompt\";\nimport { convertUint8ArrayToBase64 } from \"@ai-sdk/provider-utils\";\n\nexport function convertToGeonChatMessages(\n  prompt: LanguageModelV1Prompt\n): GeonChatPrompt {\n  const messages: GeonChatPrompt = [];\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        messages.push({ role: \"system\", content });\n        break;\n      }\n\n      case \"user\": {\n        if (content.length > 0 && content[0]?.type === \"text\") {\n          messages.push({ role: \"user\", content: content[0].text });\n          break;\n        }\n\n        messages.push({\n          role: 'user',\n          content: content.map((part, index) => {\n            switch (part.type) {\n              case 'text': {\n                return { type: 'text', text: part.text };\n              }\n              case 'image': {\n                return {\n                  type: 'image_url',\n                  image_url: {\n                    url:\n                      part.image instanceof URL\n                        ? part.image.toString()\n                        : `data:${\n                            part.mimeType ?? 'image/jpeg'\n                          };base64,${convertUint8ArrayToBase64(part.image)}`,\n\n                    // OpenAI specific extension: image detail\n                    detail: part.providerMetadata?.openai?.imageDetail,\n                  },\n                };\n              }\n              case 'file': {\n                if (part.data instanceof URL) {\n                  throw new UnsupportedFunctionalityError({\n                    functionality:\n                      \"'File content parts with URL data' functionality not supported.\",\n                  });\n                }\n\n                switch (part.mimeType) {\n                  case 'audio/wav': {\n                    return {\n                      type: 'input_audio',\n                      input_audio: { data: part.data, format: 'wav' },\n                    };\n                  }\n                  case 'audio/mp3':\n                  case 'audio/mpeg': {\n                    return {\n                      type: 'input_audio',\n                      input_audio: { data: part.data, format: 'mp3' },\n                    };\n                  }\n                  case 'application/pdf': {\n                    return {\n                      type: 'file',\n                      file: {\n                        filename: part.filename ?? `part-${index}.pdf`,\n                        file_data: `data:application/pdf;base64,${part.data}`,\n                      },\n                    };\n                  }\n                  default: {\n                    throw new UnsupportedFunctionalityError({\n                      functionality: `File content part type ${part.mimeType} in user messages`,\n                    });\n                  }\n                }\n              }\n            }\n          }),\n        });\n\n        break;\n      }\n\n      case \"assistant\": {\n        let text = \"\";\n        const toolCalls: Array<{\n          id: string;\n          type: \"function\";\n          function: { name: string; arguments: any };\n        }> = [];\n\n        for (const part of content) {\n          switch (part.type) {\n            case \"text\": {\n              text += part.text;\n              break;\n            }\n            case \"tool-call\": {\n              toolCalls.push({\n                id: part.toolCallId,\n                type: \"function\",\n                function: {\n                  name: part.toolName,\n                  arguments: JSON.stringify(part.args),\n                },\n              });\n              break;\n            }\n          }\n        }\n\n        messages.push({\n          role: \"assistant\",\n          content: text,\n          tool_calls: toolCalls.length > 0 ? toolCalls : undefined\n        });\n\n        break;\n      }\n      case \"tool\": {\n        for (const toolResponse of content) {\n          messages.push({\n            role: \"tool\",\n            name: toolResponse.toolName,\n            content:\n              typeof toolResponse.result === \"object\"\n                ? JSON.stringify(toolResponse.result)\n                : `${toolResponse.result}`,\n          });\n        }\n        break;\n      }\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return messages;\n}\n", "import {LanguageModelV1FinishReason} from \"@ai-sdk/provider\";\n\nexport function mapGeonFinishReason(\n\tfinishReason: string | null | undefined,\n): LanguageModelV1FinishReason {\n\tswitch (finishReason) {\n\t\tcase 'stop':\n\t\t\treturn 'stop';\n\t\tcase 'length':\n\t\t\treturn 'length';\n\t\tcase 'content_filter':\n\t\t\treturn 'content-filter';\n\t\tcase 'function_call':\n\t\tcase 'tool_calls':\n\t\t\treturn 'tool-calls';\n\t\tdefault:\n\t\t\treturn 'unknown';\n\t}\n}\n", "import {createJsonErrorResponseHandler} from \"@ai-sdk/provider-utils\";\nimport { z } from \"zod\";\n\nexport const geonErrorDataSchema = z.object({\n  error: z.object({\n    message: z.string(),\n\n    // The additional information below is handled loosely to support\n    // OpenAI-compatible providers that have slightly different error\n    // responses:\n    type: z.string().nullish(),\n    param: z.any().nullish(),\n    code: z.union([z.string(), z.number()]).nullish(),\n  }),\n});\nexport type GeonErrorData = z.infer<typeof geonErrorDataSchema>;\n\nexport const geonFailedResponseHandler = createJsonErrorResponseHandler({\n\terrorSchema: geonErrorDataSchema,\n\terrorToMessage: data => data.error.message,\n});\n", "import { EmptyResponseBodyError } from \"@ai-sdk/provider\";\nimport {\n  createEventSourceParserStream,\n  EventSourceChunk,\n  extractResponseHeaders,\n  ParseResult,\n  ResponseHandler,\n  safeParseJSON,\n} from \"@ai-sdk/provider-utils\";\nimport { ZodSchema } from \"zod\";\nimport {\n  EventSourceParserStream,\n  ParsedEvent,\n} from \"eventsource-parser/stream\";\n\nexport const createJsonStreamResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    let buffer = \"\";\n\n    return {\n      responseHeaders,\n      value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n        new TransformStream<string, ParseResult<T>>({\n          transform(chunkText, controller) {\n            buffer += chunkText;\n            // 개행문자로 분리하여 완성된 JSON 처리\n            if (buffer.includes('\\n')) {\n              const lines = buffer.split('\\n');\n              // 마지막 라인은 불완전할 수 있으므로 버퍼에 남김\n              buffer = lines.pop() || '';\n              // 완성된 라인들 처리\n              for (const line of lines) {\n                if (!line.trim()) continue; // 빈 라인 무시\n                controller.enqueue(\n                  safeParseJSON({\n                    text: line,\n                    schema: chunkSchema,\n                  })\n                );\n              }\n            }\n          },\n          // 스트림 종료시 남은 버퍼 처리\n          // flush(controller) {\n          //   if (buffer.trim()) {\n          //     controller.enqueue(\n          //       safeParseJSON({\n          //         text: buffer,\n          //         schema: chunkSchema,\n          //       })\n          //     );\n          //   }\n          // }\n        })\n      ),\n    };\n  };\n\nexport const createEventSourceResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    return {\n      responseHeaders,\n      value: response.body\n        .pipeThrough(new TextDecoderStream())\n        .pipeThrough(createEventSourceParserStream())\n        .pipeThrough(\n          new TransformStream<EventSourceChunk, ParseResult<T>>({\n            transform({ event, data }, controller) {\n              // ignore the 'DONE' event that e.g. OpenAI sends:\n              if (data === '[DONE]') {\n                return;\n              }\n              try {\n                const jsonData = JSON.parse(data);\n                if (jsonData.content) {\n                  // 한글 디코딩\n                  jsonData.content = decodeURIComponent(jsonData.content);\n                }\n                controller.enqueue(\n                  safeParseJSON({\n                    text: JSON.stringify(jsonData),\n                    schema: chunkSchema,\n                  })\n                );\n              } catch (error) {\n                console.error(\"Error parsing or decoding data:\", error);\n              }\n            },\n          })\n        ),\n    };\n  };\n", "export function getResponseMetadata({\n\t                                    id,\n\t                                    // model,\n\t                                    created,\n                                    }: {\n\tid?: string | undefined | null;\n\tcreated?: number | undefined | null;\n\t// model?: string | undefined | null;\n}) {\n\treturn {\n\t\tid: id ?? undefined,\n\t\t// modelId: model ?? undefined,\n\t\ttimestamp: created != null ? new Date(created * 1000) : undefined,\n\t};\n}\n", "import {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  UnsupportedFunctionalityError,\n} from \"@ai-sdk/provider\";\n\nexport function prepareTools(\n  mode: Parameters<LanguageModelV1[\"doGenerate\"]>[0][\"mode\"] & {\n    type: \"regular\";\n  },\n  structuredOutputs: boolean\n): {\n  tools:\n    | Array<{\n        type: \"function\";\n        function: {\n          name: string;\n          description: string | undefined;\n          parameters: unknown;\n        };\n      }>\n    | undefined;\n  tool_choice:\n    | { type: \"function\"; function: { name: string } }\n    | \"auto\"\n    | \"none\"\n    | \"any\"\n    | undefined;\n  toolWarnings: Array<LanguageModelV1CallWarning>;\n} {\n  // when the tools array is empty, change it to undefined to prevent errors:\n  const tools = mode.tools?.length ? mode.tools : undefined;\n  const toolWarnings: Array<LanguageModelV1CallWarning> = [];\n\n  if (tools == null) {\n    return { tools: undefined, tool_choice: undefined, toolWarnings };\n  }\n\n  const geonTools: Array<{\n    type: \"function\";\n    function: {\n      name: string;\n      description: string | undefined;\n      parameters: unknown;\n      strict?: boolean;\n    };\n  }> = [];\n\n  for (const tool of tools) {\n    if (tool.type === \"provider-defined\") {\n      toolWarnings.push({ type: \"unsupported-tool\", tool });\n    } else {\n      geonTools.push({\n        type: \"function\",\n        function: {\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters,\n          strict: structuredOutputs ? true : undefined,\n        },\n      });\n    }\n  }\n\n  const toolChoice = mode.toolChoice;\n\n  if (toolChoice == null) {\n    return { tools: geonTools, tool_choice: undefined, toolWarnings };\n  }\n\n  const type = toolChoice.type;\n\n  switch (type) {\n    case \"auto\":\n    case \"none\":\n      return { tools: geonTools, tool_choice: type, toolWarnings };\n    case \"required\":\n      return { tools: geonTools, tool_choice: \"any\", toolWarnings };\n\n    // geon does not support tool mode directly,\n    // so we filter the tools and force the tool choice through 'any'\n    case \"tool\":\n      return {\n        tools: geonTools.filter(\n          (tool) => tool.function.name === toolChoice.toolName\n        ),\n        tool_choice: \"any\",\n        toolWarnings,\n      };\n    default: {\n      const _exhaustiveCheck: never = type;\n      throw new UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`,\n      });\n    }\n  }\n}\n", "import { GeonChatModelId, GeonChatSettings } from \"./geon-chat-settings\";\nimport { GeonChatLanguageModel } from \"./geon-chat-language-model\";\nimport {\n  generateId,\n  loadApiKey,\n  withoutTrailingSlash,\n} from \"@ai-sdk/provider-utils\";\nimport { TranscriptionModelV1 } from \"@ai-sdk/provider\";\nimport { GeonTranscriptionModel } from \"./geon-transcription-model\";\nimport { GeonTranscriptionModelId } from \"./geon-transcription-settings\";\nimport { GeonResponsesModelId } from \"./responses/geon-responses-settings\";\nimport { GeonResponsesLanguageModel } from \"./responses/geon-responses-language-model\";\n\n// 스레드 실행 인터페이스 추가\nexport interface GeonRunsResponse {\n  threadId: string;\n  messageId: string;\n  stream: ReadableStream;\n}\n\nexport interface GeonRunsOptions {\n  assistantId: string;\n  query: string;\n  threadId?: string | null;\n  user?: string;\n  difyType?: string;\n  stream?: boolean;\n  generateId?: () => string; // generateId 속성 추가\n}\n\n// 스레드 인터페이스 추가\nexport interface GeonThreads {\n  runs: {\n    create: (options: GeonRunsOptions) => Promise<Response>;\n    stream: (options: GeonRunsOptions) => Promise<GeonRunsResponse>;\n  };\n}\n\nexport interface GeonProvider {\n  (\n    modelId: GeonChatModelId,\n    settings?: GeonChatSettings\n  ): GeonChatLanguageModel;\n\n  /**\nCreates a model for text generation.\n\t */\n  languageModel(\n    modelId: GeonChatModelId,\n    settings?: GeonChatSettings\n  ): GeonChatLanguageModel;\n\n  /**\nCreates a model for text generation.\n\t */\n  chat(\n    modelId: GeonChatModelId,\n    settings?: GeonChatSettings\n  ): GeonChatLanguageModel;\n\n  /**\n   * Threads API for conversation management\n   */\n  beta: {\n    threads: GeonThreads;\n  };\n\n  /**\nCreates a model for transcription.\n   */\n  transcription(modelId: GeonTranscriptionModelId): TranscriptionModelV1;\n\n  /**\nCreates a model for responses.\n   */\n  responses(modelId: GeonResponsesModelId): GeonResponsesLanguageModel;\n}\n\ntype GeonChatConfig = {\n  provider: string;\n  baseURL: string;\n  headers: () => Record<string, string | undefined>;\n  generateId: () => string;\n  fetch?: typeof fetch;\n};\n\nexport interface GeonProviderSettings {\n  /**\nUse a different URL prefix for API calls, e.g. to use proxy servers.\nThe default prefix is `https://api.mistral.ai/v1`.\n\t */\n  baseURL?: string;\n\n  /**\nAPI key that is being send using the `Authorization` header.\nIt defaults to the `MISTRAL_API_KEY` environment variable.\n\t */\n  apiKey?: string;\n\n  /**\nCustom headers to include in the requests.\n\t */\n  headers?: Record<string, string>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n\t */\n  fetch?: typeof fetch;\n\n  generateId?: () => string;\n}\n\n// SSE 스트림 파싱 함수\nasync function parseSSEStream(\n  response: Response,\n  messageId: string\n): Promise<ReadableStream> {\n  const body = response.body;\n  if (!body) {\n    throw new Error(\"응답 본문이 없습니다\");\n  }\n\n  return new ReadableStream({\n    async start(controller) {\n      // 초기 어시스턴트 메시지 ID 사용\n      let hasCreatedMessage = false;\n\n      // 텍스트 디코더 및 버퍼 설정\n      const textDecoder = new TextDecoder();\n      let buffer = \"\";\n\n      const reader = body.getReader();\n\n      try {\n        while (true) {\n          const { done, value } = await reader.read();\n          if (done) break;\n\n          buffer += textDecoder.decode(value, { stream: true });\n\n          // SSE 메시지는 이중 개행(\\n\\n)으로 구분됨\n          const messages = buffer.split(\"\\n\\n\");\n          buffer = messages.pop() || \"\"; // 마지막 불완전한 메시지는 버퍼에 유지\n\n          for (const message of messages) {\n            if (!message.trim()) continue;\n\n            const eventMatch = message.match(/^event:\\s*(.+)$/m);\n            const dataMatch = message.match(/^data:\\s*(.+)$/m);\n\n            if (!eventMatch || !dataMatch) continue;\n\n            const eventType = eventMatch[1]?.trim() || \"\";\n            const dataText = dataMatch[1]?.trim() || \"\";\n\n            try {\n              const data = JSON.parse(dataText);\n\n              // Dify 이벤트 타입에 따라 처리\n              if (eventType === \"thread.message.created\") {\n                if (!hasCreatedMessage) {\n                  controller.enqueue({\n                    event: \"thread.message.created\",\n                    data: {\n                      id: data.id || messageId,\n                      role: \"assistant\",\n                      content: [{ type: \"text\", text: { value: \"\" } }],\n                    },\n                  });\n                  hasCreatedMessage = true;\n                }\n              } else if (eventType === \"thread.message.delta\") {\n                const content = data.delta?.content?.[0];\n                if (content?.type === \"text\" && content.text?.value != null) {\n                  controller.enqueue({\n                    event: \"thread.message.delta\",\n                    data: {\n                      delta: {\n                        content: [\n                          {\n                            type: \"text\",\n                            text: { value: content.text.value },\n                          },\n                        ],\n                      },\n                    },\n                  });\n                }\n              } else if (eventType === \"thread.run.completed\") {\n                controller.enqueue({\n                  event: \"thread.run.completed\",\n                  data,\n                });\n              } else if (eventType === \"thread.run.requires_action\") {\n                controller.enqueue({\n                  event: \"thread.run.requires_action\",\n                  data,\n                });\n              }\n            } catch (e) {\n              console.error(\"JSON 파싱 오류:\", e, dataText);\n            }\n          }\n        }\n        controller.close();\n      } catch (error) {\n        console.error(\"스트림 처리 오류:\", error);\n        controller.error(error);\n      }\n    },\n  });\n}\n\n/**\nCreate a Geon AI provider instance.\n */\nexport function createGeon(options: GeonProviderSettings = {}): GeonProvider {\n  const baseURL =\n    withoutTrailingSlash(options.baseURL) ?? \"http://**************:8081/v1\";\n  // withoutTrailingSlash(options.baseURL) ?? 'http://localhost:8080/v1'\n\n  const providerName = \"geon\";\n\n  // todo: 기존 fastapi 서버에는 인증 토큰이 없음, open-web-ui 통합 시 해당 기능 점검 필요\n  const getHeaders = () => ({\n    Authorization: `Bearer ${loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: \"GEON_AI_API_KEY\",\n      description: \"Geon\",\n    })}`,\n    ...options.headers,\n  });\n\n  const createChatModel = (\n    modelId: GeonChatModelId,\n    settings: GeonChatSettings = {}\n  ) =>\n    new GeonChatLanguageModel(modelId, settings, {\n      provider: \"geon.chat\",\n      baseURL,\n      headers: getHeaders,\n      generateId: options.generateId ?? generateId,\n      fetch: options.fetch,\n    });\n\n  const createResponsesModel = (modelId: GeonResponsesModelId) => {\n    return new GeonResponsesLanguageModel(modelId, {\n      provider: `${providerName}.responses`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n  };\n\n  // 스레드 API 구현\n  const threadsApi: GeonThreads = {\n    runs: {\n      create: async (options: GeonRunsOptions) => {\n        return fetch(`${baseURL}/threads/runs`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Accept: \"application/json\",\n            ...getHeaders(),\n          },\n          body: JSON.stringify({\n            query: options.query,\n            assistantId: options.assistantId,\n            threadId: options.threadId,\n            user: options.user || \"<EMAIL>\",\n            difyType: options.difyType || \"chat\",\n            stream: options.stream !== undefined ? options.stream : false,\n          }),\n        });\n      },\n      stream: async (options: GeonRunsOptions) => {\n        const newMessageId = (options.generateId ?? generateId)();\n\n        const response = await fetch(`${baseURL}/threads/runs`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Accept: \"text/event-stream\",\n            ...getHeaders(),\n          },\n          body: JSON.stringify({\n            query: options.query,\n            assistantId: options.assistantId,\n            threadId: options.threadId,\n            user: options.user || \"<EMAIL>\",\n            difyType: options.difyType || \"chat\",\n            stream: true,\n          }),\n        });\n\n        if (!response.ok) {\n          throw new Error(\n            `Dify API 호출 실패: ${response.status} ${response.statusText}`\n          );\n        }\n\n        const stream = await parseSSEStream(response, newMessageId);\n\n        return {\n          threadId: options.threadId || \"\",\n          messageId: newMessageId,\n          stream,\n        };\n      },\n    },\n  };\n\n  const createTranscriptionModel = (modelId: GeonTranscriptionModelId) =>\n    new GeonTranscriptionModel(modelId, {\n      provider: `${providerName}.transcription`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const provider = function (\n    modelId: GeonChatModelId,\n    settings?: GeonChatSettings\n  ) {\n    if (new.target) {\n      throw new Error(\n        \"The Geon model function cannot be called with the new keyword.\"\n      );\n    }\n\n    return createChatModel(modelId, settings);\n  };\n\n  provider.languageModel = createChatModel;\n  provider.chat = createChatModel;\n  provider.beta = {\n    threads: threadsApi,\n  };\n\n  provider.transcription = createTranscriptionModel;\n  provider.transcriptionModel = createTranscriptionModel;\n  \n  provider.responses = createResponsesModel;\n\n  return provider as GeonProvider;\n}\n\n/**\nDefault Mistral provider instance.\n */\nexport const geon = createGeon();\n", "import {\n    TranscriptionModelV1,\n    TranscriptionModelV1CallOptions,\n    TranscriptionModelV1CallWarning,\n  } from '@ai-sdk/provider';\n  import {\n    combineHeaders,\n    convertBase64ToUint8Array,\n    createJsonResponseHandler,\n    parseProviderOptions,\n    postFormDataToApi,\n  } from '@ai-sdk/provider-utils';\n  import { z } from 'zod';\n  import { GeonConfig } from './geon-config';\n  import { geonFailedResponseHandler } from './geon-error';\n  import {\n    GeonTranscriptionModelId,\n    GeonTranscriptionModelOptions,\n  } from './geon-transcription-settings';\n  \n  // https://platform.openai.com/docs/api-reference/audio/createTranscription\n  const geonProviderOptionsSchema = z.object({\n    include: z.array(z.string()).nullish(),\n    language: z.string().nullish(),\n    prompt: z.string().nullish(),\n    temperature: z.number().min(0).max(1).nullish().default(0),\n    timestampGranularities: z\n      .array(z.enum(['word', 'segment']))\n      .nullish()\n      .default(['segment']),\n  });\n  \n  export type GeonTranscriptionCallOptions = Omit<\n    TranscriptionModelV1CallOptions,\n    'providerOptions'\n  > & {\n    providerOptions?: {\n      geon?: z.infer<typeof geonProviderOptionsSchema>;\n    };\n  };\n  \n  interface GeonTranscriptionModelConfig extends GeonConfig {\n    _internal?: {\n      currentDate?: () => Date;\n    };\n  }\n  \n  // https://platform.openai.com/docs/guides/speech-to-text#supported-languages\n  const languageMap = {\n    afrikaans: 'af',\n    arabic: 'ar',\n    armenian: 'hy',\n    azerbaijani: 'az',\n    belarusian: 'be',\n    bosnian: 'bs',\n    bulgarian: 'bg',\n    catalan: 'ca',\n    chinese: 'zh',\n    croatian: 'hr',\n    czech: 'cs',\n    danish: 'da',\n    dutch: 'nl',\n    english: 'en',\n    estonian: 'et',\n    finnish: 'fi',\n    french: 'fr',\n    galician: 'gl',\n    german: 'de',\n    greek: 'el',\n    hebrew: 'he',\n    hindi: 'hi',\n    hungarian: 'hu',\n    icelandic: 'is',\n    indonesian: 'id',\n    italian: 'it',\n    japanese: 'ja',\n    kannada: 'kn',\n    kazakh: 'kk',\n    korean: 'ko',\n    latvian: 'lv',\n    lithuanian: 'lt',\n    macedonian: 'mk',\n    malay: 'ms',\n    marathi: 'mr',\n    maori: 'mi',\n    nepali: 'ne',\n    norwegian: 'no',\n    persian: 'fa',\n    polish: 'pl',\n    portuguese: 'pt',\n    romanian: 'ro',\n    russian: 'ru',\n    serbian: 'sr',\n    slovak: 'sk',\n    slovenian: 'sl',\n    spanish: 'es',\n    swahili: 'sw',\n    swedish: 'sv',\n    tagalog: 'tl',\n    tamil: 'ta',\n    thai: 'th',\n    turkish: 'tr',\n    ukrainian: 'uk',\n    urdu: 'ur',\n    vietnamese: 'vi',\n    welsh: 'cy',\n  };\n  \n  export class GeonTranscriptionModel implements TranscriptionModelV1 {\n    readonly specificationVersion = 'v1';\n  \n    get provider(): string {\n      return this.config.provider;\n    }\n  \n    constructor(\n      readonly modelId: GeonTranscriptionModelId,\n      private readonly config: GeonTranscriptionModelConfig,\n    ) {}\n  \n    private getArgs({\n      audio,\n      mediaType,\n      providerOptions,\n    }: GeonTranscriptionCallOptions) {\n      const warnings: TranscriptionModelV1CallWarning[] = [];\n  \n      // Parse provider options\n      const geonOptions = parseProviderOptions({\n        provider: 'geon',\n        providerOptions,\n        schema: geonProviderOptionsSchema,\n      });\n  \n      // Create form data with base fields\n      const formData = new FormData();\n      const blob =\n        audio instanceof Uint8Array\n          ? new Blob([audio])\n          : new Blob([convertBase64ToUint8Array(audio)]);\n  \n      formData.append('model', this.modelId);\n      formData.append('file', new File([blob], 'audio', { type: mediaType }));\n  \n      // Add provider-specific options\n      if (geonOptions) {\n        const transcriptionModelOptions: GeonTranscriptionModelOptions = {\n          include: geonOptions.include ?? undefined,\n          language: geonOptions.language ?? undefined,\n          prompt: geonOptions.prompt ?? undefined,\n          temperature: geonOptions.temperature ?? undefined,\n          timestamp_granularities:\n            geonOptions.timestampGranularities ?? undefined,\n        };\n  \n        for (const key in transcriptionModelOptions) {\n          const value =\n            transcriptionModelOptions[\n              key as keyof GeonTranscriptionModelOptions\n            ];\n          if (value !== undefined) {\n            formData.append(key, String(value));\n          }\n        }\n      }\n  \n      return {\n        formData,\n        warnings,\n      };\n    }\n  \n    async doGenerate(\n      options: GeonTranscriptionCallOptions,\n    ): Promise<Awaited<ReturnType<TranscriptionModelV1['doGenerate']>>> {\n      const currentDate = this.config._internal?.currentDate?.() ?? new Date();\n      const { formData, warnings } = this.getArgs(options);\n  \n      const {\n        value: response,\n        responseHeaders,\n        rawValue: rawResponse,\n      } = await postFormDataToApi({\n        url: this.config.url({\n          path: '/audio/transcriptions',\n          modelId: this.modelId,\n        }),\n        headers: combineHeaders(this.config.headers(), options.headers),\n        formData,\n        failedResponseHandler: geonFailedResponseHandler,\n        successfulResponseHandler: createJsonResponseHandler(\n          geonTranscriptionResponseSchema,\n        ),\n        abortSignal: options.abortSignal,\n        fetch: this.config.fetch,\n      });\n  \n      const language =\n        response.language != null && response.language in languageMap\n          ? languageMap[response.language as keyof typeof languageMap]\n          : undefined;\n  \n      return {\n        text: response.text,\n        segments:\n          response.words?.map(word => ({\n            text: word.word,\n            startSecond: word.start,\n            endSecond: word.end,\n          })) ?? [],\n        language,\n        durationInSeconds: response.duration ?? undefined,\n        warnings,\n        response: {\n          timestamp: currentDate,\n          modelId: this.modelId,\n          headers: responseHeaders,\n          body: rawResponse,\n        },\n      };\n    }\n  }\n  \n  const geonTranscriptionResponseSchema = z.object({\n    text: z.string(),\n    language: z.string().nullish(),\n    duration: z.number().nullish(),\n    words: z\n      .array(\n        z.object({\n          word: z.string(),\n          start: z.number(),\n          end: z.number(),\n        }),\n      )\n      .nullish(),\n  });", "import {\n    LanguageModelV1,\n    LanguageModelV1CallWarning,\n    LanguageModelV1FinishReason,\n    LanguageModelV1StreamPart,\n  } from '@ai-sdk/provider';\n  import {\n    combineHeaders,\n    createEventSourceResponseHandler,\n    createJsonResponseHandler,\n    generateId,\n    parseProviderOptions,\n    ParseResult,\n    postJsonToApi,\n  } from '@ai-sdk/provider-utils';\n  import { z } from 'zod';\n  import { GeonConfig } from '../geon-config';\n  import { geonFailedResponseHandler } from '../geon-error';\n  import { convertToGeonResponsesMessages } from './convert-to-geon-responses-messages';\n  import { mapGeonResponseFinishReason } from './map-geon-responses-finish-reason';\n  import { prepareResponsesTools } from './geon-responses-prepare-tools';\n  import { GeonResponsesModelId } from './geon-responses-settings';\n  \n  export class GeonResponsesLanguageModel implements LanguageModelV1 {\n    readonly specificationVersion = 'v1';\n    readonly defaultObjectGenerationMode = 'json';\n    readonly supportsStructuredOutputs = true;\n  \n    readonly modelId: GeonResponsesModelId;\n  \n    private readonly config: GeonConfig;\n  \n    constructor(modelId: GeonResponsesModelId, config: GeonConfig) {\n      this.modelId = modelId;\n      this.config = config;\n    }\n  \n    get provider(): string {\n      return this.config.provider;\n    }\n  \n    private getArgs({\n      mode,\n      maxTokens,\n      temperature,\n      stopSequences,\n      topP,\n      topK,\n      presencePenalty,\n      frequencyPenalty,\n      seed,\n      prompt,\n      providerMetadata,\n      responseFormat,\n    }: Parameters<LanguageModelV1['doGenerate']>[0]) {\n      const warnings: LanguageModelV1CallWarning[] = [];\n      const modelConfig = getResponsesModelConfig(this.modelId);\n      const type = mode.type;\n  \n      if (topK != null) {\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'topK',\n        });\n      }\n  \n      if (seed != null) {\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'seed',\n        });\n      }\n  \n      if (presencePenalty != null) {\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'presencePenalty',\n        });\n      }\n  \n      if (frequencyPenalty != null) {\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'frequencyPenalty',\n        });\n      }\n  \n      if (stopSequences != null) {\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'stopSequences',\n        });\n      }\n  \n      const { messages, warnings: messageWarnings } =\n        convertToGeonResponsesMessages({\n          prompt,\n          systemMessageMode: modelConfig.systemMessageMode,\n        });\n  \n      warnings.push(...messageWarnings);\n  \n      const geonOptions = parseProviderOptions({\n        provider: 'geon',\n        providerOptions: providerMetadata,\n        schema: geonResponsesProviderOptionsSchema,\n      });\n  \n      const isStrict = geonOptions?.strictSchemas ?? true;\n  \n      const baseArgs = {\n        model: this.modelId,\n        input: messages,\n        temperature,\n        top_p: topP,\n        max_output_tokens: maxTokens,\n  \n        ...(responseFormat?.type === 'json' && {\n          text: {\n            format:\n              responseFormat.schema != null\n                ? {\n                    type: 'json_schema',\n                    strict: isStrict,\n                    name: responseFormat.name ?? 'response',\n                    description: responseFormat.description,\n                    schema: responseFormat.schema,\n                  }\n                : { type: 'json_object' },\n          },\n        }),\n  \n        // provider options:\n        metadata: geonOptions?.metadata,\n        parallel_tool_calls: geonOptions?.parallelToolCalls,\n        previous_response_id: geonOptions?.previousResponseId,\n        store: geonOptions?.store,\n        user: geonOptions?.user,\n        instructions: geonOptions?.instructions,\n  \n        // model-specific settings:\n        ...(modelConfig.isReasoningModel &&\n          geonOptions?.reasoningEffort != null && {\n            reasoning: { effort: geonOptions?.reasoningEffort },\n          }),\n        ...(modelConfig.requiredAutoTruncation && {\n          truncation: 'auto',\n        }),\n      };\n  \n      if (modelConfig.isReasoningModel) {\n        // remove unsupported settings for reasoning models\n        // see https://platform.openai.com/docs/guides/reasoning#limitations\n        if (baseArgs.temperature != null) {\n          baseArgs.temperature = undefined;\n          warnings.push({\n            type: 'unsupported-setting',\n            setting: 'temperature',\n            details: 'temperature is not supported for reasoning models',\n          });\n        }\n  \n        if (baseArgs.top_p != null) {\n          baseArgs.top_p = undefined;\n          warnings.push({\n            type: 'unsupported-setting',\n            setting: 'topP',\n            details: 'topP is not supported for reasoning models',\n          });\n        }\n      }\n  \n      switch (type) {\n        case 'regular': {\n          const { tools, tool_choice, toolWarnings } = prepareResponsesTools({\n            mode,\n            strict: isStrict, // TODO support provider options on tools\n          });\n  \n          return {\n            args: {\n              ...baseArgs,\n              tools,\n              tool_choice,\n            },\n            warnings: [...warnings, ...toolWarnings],\n          };\n        }\n  \n        case 'object-json': {\n          return {\n            args: {\n              ...baseArgs,\n              text: {\n                format:\n                  mode.schema != null\n                    ? {\n                        type: 'json_schema',\n                        strict: isStrict,\n                        name: mode.name ?? 'response',\n                        description: mode.description,\n                        schema: mode.schema,\n                      }\n                    : { type: 'json_object' },\n              },\n            },\n            warnings,\n          };\n        }\n  \n        case 'object-tool': {\n          return {\n            args: {\n              ...baseArgs,\n              tool_choice: { type: 'function', name: mode.tool.name },\n              tools: [\n                {\n                  type: 'function',\n                  name: mode.tool.name,\n                  description: mode.tool.description,\n                  parameters: mode.tool.parameters,\n                  strict: isStrict,\n                },\n              ],\n            },\n            warnings,\n          };\n        }\n  \n        default: {\n          const _exhaustiveCheck: never = type;\n          throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n        }\n      }\n    }\n  \n    async doGenerate(\n      options: Parameters<LanguageModelV1['doGenerate']>[0],\n    ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n      const { args: body, warnings } = this.getArgs(options);\n  \n      const {\n        responseHeaders,\n        value: response,\n        rawValue: rawResponse,\n      } = await postJsonToApi({\n        url: this.config.url({\n          path: '/responses',\n          modelId: this.modelId,\n        }),\n        headers: combineHeaders(this.config.headers(), options.headers),\n        body,\n        failedResponseHandler: geonFailedResponseHandler,\n        successfulResponseHandler: createJsonResponseHandler(\n          z.object({\n            id: z.string(),\n            created_at: z.number(),\n            model: z.string(),\n            output: z.array(\n              z.discriminatedUnion('type', [\n                z.object({\n                  type: z.literal('message'),\n                  role: z.literal('assistant'),\n                  content: z.array(\n                    z.object({\n                      type: z.literal('output_text'),\n                      text: z.string(),\n                      annotations: z.array(\n                        z.object({\n                          type: z.literal('url_citation'),\n                          start_index: z.number(),\n                          end_index: z.number(),\n                          url: z.string(),\n                          title: z.string(),\n                        }),\n                      ),\n                    }),\n                  ),\n                }),\n                z.object({\n                  type: z.literal('function_call'),\n                  call_id: z.string(),\n                  name: z.string(),\n                  arguments: z.string(),\n                }),\n                z.object({\n                  type: z.literal('web_search_call'),\n                }),\n                z.object({\n                  type: z.literal('computer_call'),\n                }),\n                z.object({\n                  type: z.literal('reasoning'),\n                }),\n              ]),\n            ),\n            incomplete_details: z.object({ reason: z.string() }).nullable(),\n            usage: usageSchema,\n          }),\n        ),\n        abortSignal: options.abortSignal,\n        fetch: this.config.fetch,\n      });\n  \n      const outputTextElements = response.output\n        .filter(output => output.type === 'message')\n        .flatMap(output => output.content)\n        .filter(content => content.type === 'output_text');\n  \n      const toolCalls = response.output\n        .filter(output => output.type === 'function_call')\n        .map(output => ({\n          toolCallType: 'function' as const,\n          toolCallId: output.call_id,\n          toolName: output.name,\n          args: output.arguments,\n        }));\n  \n      return {\n        text: outputTextElements.map(content => content.text).join('\\n'),\n        sources: outputTextElements.flatMap(content =>\n          content.annotations.map(annotation => ({\n            sourceType: 'url',\n            id: this.config.generateId?.() ?? generateId(),\n            url: annotation.url,\n            title: annotation.title,\n          })),\n        ),\n        finishReason: mapGeonResponseFinishReason({\n          finishReason: response.incomplete_details?.reason,\n          hasToolCalls: toolCalls.length > 0,\n        }),\n        toolCalls: toolCalls.length > 0 ? toolCalls : undefined,\n        usage: {\n          promptTokens: response.usage.input_tokens,\n          completionTokens: response.usage.output_tokens,\n        },\n        rawCall: {\n          rawPrompt: undefined,\n          rawSettings: {},\n        },\n        rawResponse: {\n          headers: responseHeaders,\n          body: rawResponse,\n        },\n        request: {\n          body: JSON.stringify(body),\n        },\n        response: {\n          id: response.id,\n          timestamp: new Date(response.created_at * 1000),\n          modelId: response.model,\n        },\n        providerMetadata: {\n          geon: {\n            responseId: response.id,\n            cachedPromptTokens:\n              response.usage.input_tokens_details?.cached_tokens ?? null,\n            reasoningTokens:\n              response.usage.output_tokens_details?.reasoning_tokens ?? null,\n          },\n        },\n        warnings,\n      };\n    }\n  \n    async doStream(\n      options: Parameters<LanguageModelV1['doStream']>[0],\n    ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n      const { args: body, warnings } = this.getArgs(options);\n  \n      console.log(body)\n\n      const { responseHeaders, value: response } = await postJsonToApi({\n        url: this.config.url({\n          path: '/responses',\n          modelId: this.modelId,\n        }),\n        headers: combineHeaders(this.config.headers(), options.headers),\n        body: {\n          ...body,\n          stream: true,\n        },\n        failedResponseHandler: geonFailedResponseHandler,\n        successfulResponseHandler: createEventSourceResponseHandler(\n          geonResponsesChunkSchema,\n        ),\n        abortSignal: options.abortSignal,\n        fetch: this.config.fetch,\n      });\n  \n      const self = this;\n  \n      let finishReason: LanguageModelV1FinishReason = 'unknown';\n      let promptTokens = NaN;\n      let completionTokens = NaN;\n      let cachedPromptTokens: number | null = null;\n      let reasoningTokens: number | null = null;\n      let responseId: string | null = null;\n      const ongoingToolCalls: Record<\n        number,\n        { toolName: string; toolCallId: string } | undefined\n      > = {};\n      let hasToolCalls = false;\n  \n      return {\n        stream: response.pipeThrough(\n          new TransformStream<\n            ParseResult<z.infer<typeof geonResponsesChunkSchema>>,\n            LanguageModelV1StreamPart\n          >({\n            transform(chunk, controller) {\n              // handle failed chunk parsing / validation:\n              if (!chunk.success) {\n                finishReason = 'error';\n                controller.enqueue({ type: 'error', error: chunk.error });\n                return;\n              }\n  \n              const value = chunk.value;\n              if (isResponseOutputItemAddedChunk(value)) {\n                if (value.item.type === 'function_call') {\n                  ongoingToolCalls[value.output_index] = {\n                    toolName: value.item.name,\n                    toolCallId: value.item.call_id,\n                  };\n  \n                  controller.enqueue({\n                    type: 'tool-call-delta',\n                    toolCallType: 'function',\n                    toolCallId: value.item.call_id,\n                    toolName: value.item.name,\n                    argsTextDelta: value.item.arguments,\n                  });\n                }\n              } else if (isResponseFunctionCallArgumentsDeltaChunk(value)) {\n                const toolCall = ongoingToolCalls[value.output_index];\n  \n                if (toolCall != null) {\n                  controller.enqueue({\n                    type: 'tool-call-delta',\n                    toolCallType: 'function',\n                    toolCallId: toolCall.toolCallId,\n                    toolName: toolCall.toolName,\n                    argsTextDelta: value.delta,\n                  });\n                }\n              } else if (isResponseCreatedChunk(value)) {\n                responseId = value.response.id;\n                controller.enqueue({\n                  type: 'response-metadata',\n                  id: value.response.id,\n                  timestamp: new Date(value.response.created_at * 1000),\n                  modelId: value.response.model,\n                });\n              } else if (isTextDeltaChunk(value)) {\n                controller.enqueue({\n                  type: 'text-delta',\n                  textDelta: value.delta,\n                });\n              } else if (\n                isResponseOutputItemDoneChunk(value) &&\n                value.item.type === 'function_call'\n              ) {\n                ongoingToolCalls[value.output_index] = undefined;\n                hasToolCalls = true;\n                controller.enqueue({\n                  type: 'tool-call',\n                  toolCallType: 'function',\n                  toolCallId: value.item.call_id,\n                  toolName: value.item.name,\n                  args: value.item.arguments,\n                });\n              } else if (isResponseFinishedChunk(value)) {\n                finishReason = mapGeonResponseFinishReason({\n                  finishReason: value.response.incomplete_details?.reason,\n                  hasToolCalls,\n                });\n                promptTokens = value.response.usage.input_tokens;\n                completionTokens = value.response.usage.output_tokens;\n                cachedPromptTokens =\n                  value.response.usage.input_tokens_details?.cached_tokens ??\n                  cachedPromptTokens;\n                reasoningTokens =\n                  value.response.usage.output_tokens_details?.reasoning_tokens ??\n                  reasoningTokens;\n              } else if (isResponseAnnotationAddedChunk(value)) {\n                controller.enqueue({\n                  type: 'source',\n                  source: {\n                    sourceType: 'url',\n                    id: self.config.generateId?.() ?? generateId(),\n                    url: value.annotation.url,\n                    title: value.annotation.title,\n                  },\n                });\n              }\n            },\n  \n            flush(controller) {\n              controller.enqueue({\n                type: 'finish',\n                finishReason,\n                usage: { promptTokens, completionTokens },\n                providerMetadata: {\n                  geon: {\n                    responseId,\n                    cachedPromptTokens,\n                    reasoningTokens,\n                  },\n                },\n              });\n            },\n          }),\n        ),\n        rawCall: {\n          rawPrompt: undefined,\n          rawSettings: {},\n        },\n        rawResponse: { headers: responseHeaders },\n        request: { body: JSON.stringify(body) },\n        warnings,\n      };\n    }\n  }\n  \n  const usageSchema = z.object({\n    input_tokens: z.number(),\n    input_tokens_details: z\n      .object({ cached_tokens: z.number().nullish() })\n      .nullish(),\n    output_tokens: z.number(),\n    output_tokens_details: z\n      .object({ reasoning_tokens: z.number().nullish() })\n      .nullish(),\n  });\n  \n  const textDeltaChunkSchema = z.object({\n    type: z.literal('response.output_text.delta'),\n    delta: z.string(),\n  });\n  \n  const responseFinishedChunkSchema = z.object({\n    type: z.enum(['response.completed', 'response.incomplete']),\n    response: z.object({\n      incomplete_details: z.object({ reason: z.string() }).nullish(),\n      usage: usageSchema,\n    }),\n  });\n  \n  const responseCreatedChunkSchema = z.object({\n    type: z.literal('response.created'),\n    response: z.object({\n      id: z.string(),\n      created_at: z.number(),\n      model: z.string(),\n    }),\n  });\n  \n  const responseOutputItemDoneSchema = z.object({\n    type: z.literal('response.output_item.done'),\n    output_index: z.number(),\n    item: z.discriminatedUnion('type', [\n      z.object({\n        type: z.literal('message'),\n      }),\n      z.object({\n        type: z.literal('function_call'),\n        id: z.string(),\n        call_id: z.string(),\n        name: z.string(),\n        arguments: z.string(),\n        status: z.literal('completed'),\n      }),\n    ]),\n  });\n  \n  const responseFunctionCallArgumentsDeltaSchema = z.object({\n    type: z.literal('response.function_call_arguments.delta'),\n    item_id: z.string(),\n    output_index: z.number(),\n    delta: z.string(),\n  });\n  \n  const responseOutputItemAddedSchema = z.object({\n    type: z.literal('response.output_item.added'),\n    output_index: z.number(),\n    item: z.discriminatedUnion('type', [\n      z.object({\n        type: z.literal('message'),\n      }),\n      z.object({\n        type: z.literal('function_call'),\n        id: z.string(),\n        call_id: z.string(),\n        name: z.string(),\n        arguments: z.string(),\n      }),\n    ]),\n  });\n  \n  const responseAnnotationAddedSchema = z.object({\n    type: z.literal('response.output_text.annotation.added'),\n    annotation: z.object({\n      type: z.literal('url_citation'),\n      url: z.string(),\n      title: z.string(),\n    }),\n  });\n  \n  const geonResponsesChunkSchema = z.union([\n    textDeltaChunkSchema,\n    responseFinishedChunkSchema,\n    responseCreatedChunkSchema,\n    responseOutputItemDoneSchema,\n    responseFunctionCallArgumentsDeltaSchema,\n    responseOutputItemAddedSchema,\n    responseAnnotationAddedSchema,\n    z.object({ type: z.string() }).passthrough(), // fallback for unknown chunks\n  ]);\n  \n  function isTextDeltaChunk(\n    chunk: z.infer<typeof geonResponsesChunkSchema>,\n  ): chunk is z.infer<typeof textDeltaChunkSchema> {\n    return chunk.type === 'response.output_text.delta';\n  }\n  \n  function isResponseOutputItemDoneChunk(\n    chunk: z.infer<typeof geonResponsesChunkSchema>,\n  ): chunk is z.infer<typeof responseOutputItemDoneSchema> {\n    return chunk.type === 'response.output_item.done';\n  }\n  \n  function isResponseFinishedChunk(\n    chunk: z.infer<typeof geonResponsesChunkSchema>,\n  ): chunk is z.infer<typeof responseFinishedChunkSchema> {\n    return (\n      chunk.type === 'response.completed' || chunk.type === 'response.incomplete'\n    );\n  }\n  \n  function isResponseCreatedChunk(\n    chunk: z.infer<typeof geonResponsesChunkSchema>,\n  ): chunk is z.infer<typeof responseCreatedChunkSchema> {\n    return chunk.type === 'response.created';\n  }\n  \n  function isResponseFunctionCallArgumentsDeltaChunk(\n    chunk: z.infer<typeof geonResponsesChunkSchema>,\n  ): chunk is z.infer<typeof responseFunctionCallArgumentsDeltaSchema> {\n    return chunk.type === 'response.function_call_arguments.delta';\n  }\n  \n  function isResponseOutputItemAddedChunk(\n    chunk: z.infer<typeof geonResponsesChunkSchema>,\n  ): chunk is z.infer<typeof responseOutputItemAddedSchema> {\n    return chunk.type === 'response.output_item.added';\n  }\n  \n  function isResponseAnnotationAddedChunk(\n    chunk: z.infer<typeof geonResponsesChunkSchema>,\n  ): chunk is z.infer<typeof responseAnnotationAddedSchema> {\n    return chunk.type === 'response.output_text.annotation.added';\n  }\n  \n  type ResponsesModelConfig = {\n    isReasoningModel: boolean;\n    systemMessageMode: 'remove' | 'system' | 'developer';\n    requiredAutoTruncation: boolean;\n  };\n  \n  function getResponsesModelConfig(modelId: string): ResponsesModelConfig {\n    // o series reasoning models:\n    if (modelId.startsWith('o')) {\n      if (modelId.startsWith('o1-mini') || modelId.startsWith('o1-preview')) {\n        return {\n          isReasoningModel: true,\n          systemMessageMode: 'remove',\n          requiredAutoTruncation: false,\n        };\n      }\n  \n      return {\n        isReasoningModel: true,\n        systemMessageMode: 'developer',\n        requiredAutoTruncation: false,\n      };\n    }\n  \n    // gpt models:\n    return {\n      isReasoningModel: false,\n      systemMessageMode: 'remove',\n      requiredAutoTruncation: false,\n    };\n  }\n  \n  const geonResponsesProviderOptionsSchema = z.object({\n    metadata: z.any().nullish(),\n    parallelToolCalls: z.boolean().nullish(),\n    previousResponseId: z.string().nullish(),\n    store: z.boolean().nullish(),\n    user: z.string().nullish(),\n    reasoningEffort: z.string().nullish(),\n    strictSchemas: z.boolean().nullish(),\n    instructions: z.string().nullish(),\n  });\n  \n  export type geonResponsesProviderOptions = z.infer<\n    typeof geonResponsesProviderOptionsSchema\n  >;", "import {\n    LanguageModelV1CallWarning,\n    LanguageModelV1Prompt,\n    UnsupportedFunctionalityError,\n  } from '@ai-sdk/provider';\n  import { convertUint8ArrayToBase64 } from '@ai-sdk/provider-utils';\n  import { GeonResponsesPrompt } from './geon-responses-api-types';\n  \n  export function convertToGeonResponsesMessages({\n    prompt,\n    systemMessageMode,\n  }: {\n    prompt: LanguageModelV1Prompt;\n    systemMessageMode: 'system' | 'developer' | 'remove';\n  }): {\n    messages: GeonResponsesPrompt;\n    warnings: Array<LanguageModelV1CallWarning>;\n  } {\n    const messages: GeonResponsesPrompt = [];\n    const warnings: Array<LanguageModelV1CallWarning> = [];\n  \n    for (const { role, content } of prompt) {\n      switch (role) {\n        case 'system': {\n          switch (systemMessageMode) {\n            case 'system': {\n              messages.push({ role: 'system', content });\n              break;\n            }\n            case 'developer': {\n              messages.push({ role: 'developer', content });\n              break;\n            }\n            case 'remove': {\n              warnings.push({\n                type: 'other',\n                message: 'system messages are removed for this model',\n              });\n              break;\n            }\n            default: {\n              const _exhaustiveCheck: never = systemMessageMode;\n              throw new Error(\n                `Unsupported system message mode: ${_exhaustiveCheck}`,\n              );\n            }\n          }\n          break;\n        }\n  \n        case 'user': {\n          messages.push({\n            role: 'user',\n            content: content.map((part, index) => {\n              switch (part.type) {\n                case 'text': {\n                  return { type: 'input_text', text: part.text };\n                }\n                case 'image': {\n                  return {\n                    type: 'input_image',\n                    image_url:\n                      part.image instanceof URL\n                        ? part.image.toString()\n                        : `data:${\n                            part.mimeType ?? 'image/jpeg'\n                          };base64,${convertUint8ArrayToBase64(part.image)}`,\n  \n                    // OpenAI specific extension: image detail\n                    detail: part.providerMetadata?.openai?.imageDetail,\n                  };\n                }\n                case 'file': {\n                  if (part.data instanceof URL) {\n                    // The AI SDK automatically downloads files for user file parts with URLs\n                    throw new UnsupportedFunctionalityError({\n                      functionality: 'File URLs in user messages',\n                    });\n                  }\n  \n                  switch (part.mimeType) {\n                    case 'application/pdf': {\n                      return {\n                        type: 'input_file',\n                        filename: part.filename ?? `part-${index}.pdf`,\n                        file_data: `data:application/pdf;base64,${part.data}`,\n                      };\n                    }\n                    default: {\n                      throw new UnsupportedFunctionalityError({\n                        functionality:\n                          'Only PDF files are supported in user messages',\n                      });\n                    }\n                  }\n                }\n              }\n            }),\n          });\n  \n          break;\n        }\n  \n        case 'assistant': {\n          for (const part of content) {\n            switch (part.type) {\n              case 'text': {\n                messages.push({\n                  role: 'assistant',\n                  content: [{ type: 'output_text', text: part.text }],\n                });\n                break;\n              }\n              case 'tool-call': {\n                messages.push({\n                  type: 'function_call',\n                  call_id: part.toolCallId,\n                  name: part.toolName,\n                  arguments: JSON.stringify(part.args),\n                });\n                break;\n              }\n            }\n          }\n  \n          break;\n        }\n  \n        case 'tool': {\n          for (const part of content) {\n            messages.push({\n              type: 'function_call_output',\n              call_id: part.toolCallId,\n              output: JSON.stringify(part.result),\n            });\n          }\n  \n          break;\n        }\n  \n        default: {\n          const _exhaustiveCheck: never = role;\n          throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n        }\n      }\n    }\n  \n    return { messages, warnings };\n  }", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\n\nexport function mapGeonResponseFinishReason({\n  finishReason,\n  hasToolCalls,\n}: {\n  finishReason: string | null | undefined;\n  hasToolCalls: boolean;\n}): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case undefined:\n    case null:\n      return hasToolCalls ? 'tool-calls' : 'stop';\n    case 'max_output_tokens':\n      return 'length';\n    case 'content_filter':\n      return 'content-filter';\n    default:\n      return hasToolCalls ? 'tool-calls' : 'unknown';\n  }\n}", "import {\n    LanguageModelV1,\n    LanguageModelV1CallWarning,\n    UnsupportedFunctionalityError,\n  } from '@ai-sdk/provider';\n  import { GeonResponsesTool } from './geon-responses-api-types';\n  \n  export function prepareResponsesTools({\n    mode,\n    strict,\n  }: {\n    mode: Parameters<LanguageModelV1['doGenerate']>[0]['mode'] & {\n      type: 'regular';\n    };\n    strict: boolean;\n  }): {\n    tools?: Array<GeonResponsesTool>;\n    tool_choice?:\n      | 'auto'\n      | 'none'\n      | 'required'\n      | { type: 'web_search_preview' }\n      | { type: 'function'; name: string };\n    toolWarnings: LanguageModelV1CallWarning[];\n  } {\n    // when the tools array is empty, change it to undefined to prevent errors:\n    const tools = mode.tools?.length ? mode.tools : undefined;\n  \n    const toolWarnings: LanguageModelV1CallWarning[] = [];\n  \n    if (tools == null) {\n      return { tools: undefined, tool_choice: undefined, toolWarnings };\n    }\n  \n    const toolChoice = mode.toolChoice;\n  \n    const geonTools: Array<GeonResponsesTool> = [];\n  \n    for (const tool of tools) {\n      switch (tool.type) {\n        case 'function':\n          geonTools.push({\n            type: 'function',\n            name: tool.name,\n            description: tool.description,\n            parameters: tool.parameters,\n            strict: strict ? true : undefined,\n          });\n          break;\n        case 'provider-defined':\n          switch (tool.id) {\n            case 'openai.web_search_preview':\n              geonTools.push({\n                type: 'web_search_preview',\n                search_context_size: tool.args.searchContextSize as\n                  | 'low'\n                  | 'medium'\n                  | 'high',\n                user_location: tool.args.userLocation as {\n                  type: 'approximate';\n                  city: string;\n                  region: string;\n                },\n              });\n              break;\n            default:\n              toolWarnings.push({ type: 'unsupported-tool', tool });\n              break;\n          }\n          break;\n        default:\n          toolWarnings.push({ type: 'unsupported-tool', tool });\n          break;\n      }\n    }\n  \n    if (toolChoice == null) {\n      return { tools: geonTools, tool_choice: undefined, toolWarnings };\n    }\n  \n    const type = toolChoice.type;\n  \n    switch (type) {\n      case 'auto':\n      case 'none':\n      case 'required':\n        return { tools: geonTools, tool_choice: type, toolWarnings };\n      case 'tool': {\n        if (toolChoice.toolName === 'web_search_preview') {\n          return {\n            tools: geonTools,\n            tool_choice: {\n              type: 'web_search_preview',\n            },\n            toolWarnings,\n          };\n        }\n        return {\n          tools: geonTools,\n          tool_choice: {\n            type: 'function',\n            name: toolChoice.toolName,\n          },\n          toolWarnings,\n        };\n      }\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new UnsupportedFunctionalityError({\n          functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`,\n        });\n      }\n    }\n  }"], "names": ["z", "_a", "UnsupportedFunctionalityError", "_a", "_b", "toolCall", "args", "z", "generateId", "combineHeaders", "createJsonResponseHandler", "z", "z", "combineHeaders", "createJsonResponseHandler", "combineHeaders", "createEventSourceResponseHandler", "createJsonResponseHandler", "generateId", "parseProviderOptions", "postJsonToApi", "z", "UnsupportedFunctionalityError", "convertUint8ArrayToBase64", "UnsupportedFunctionalityError", "parseProviderOptions", "postJsonToApi", "combineHeaders", "createJsonResponseHandler", "z", "_a", "_b", "_c", "generateId", "createEventSourceResponseHandler", "_a", "generateId", "options"], "mappings": ";;;;;AAAA;AASA;AAOA,SAAS,KAAAA,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTX,SAAS,0BACd,MAAA,EACgB;IATlB,IAAA;IAUE,MAAM,WAA2B,CAAC,CAAA;IAElC,KAAA,MAAW,EAAE,IAAA,EAAM,OAAA,CAAQ,CAAA,IAAK,OAAQ;QACtC,OAAQ,MAAM;YACZ,KAAK;gBAAU;oBACb,SAAS,IAAA,CAAK;wBAAE,MAAM;wBAAU;oBAAQ,CAAC;oBACzC;gBACF;YAEA,KAAK;gBAAQ;oBACX,IAAI,QAAQ,MAAA,GAAS,KAAA,CAAA,CAAK,KAAA,OAAA,CAAQ,CAAC,CAAA,KAAT,OAAA,KAAA,IAAA,GAAY,IAAA,MAAS,QAAQ;wBACrD,SAAS,IAAA,CAAK;4BAAE,MAAM;4BAAQ,SAAS,OAAA,CAAQ,CAAC,CAAA,CAAE,IAAA;wBAAK,CAAC;wBACxD;oBACF;oBAEA,SAAS,IAAA,CAAK;wBACZ,MAAM;wBACN,SAAS,QAAQ,GAAA,CAAI,CAAC,MAAM,UAAU;4BA3BhD,IAAAC,KAAA,IAAA,IAAA;4BA4BY,OAAQ,KAAK,IAAA,EAAM;gCACjB,KAAK;oCAAQ;wCACX,OAAO;4CAAE,MAAM;4CAAQ,MAAM,KAAK,IAAA;wCAAK;oCACzC;gCACA,KAAK;oCAAS;wCACZ,OAAO;4CACL,MAAM;4CACN,WAAW;gDACT,KACE,KAAK,KAAA,YAAiB,MAClB,KAAK,KAAA,CAAM,QAAA,CAAS,IACpB,CAAA,KAAA,EAAA,CACEA,MAAA,KAAK,QAAA,KAAL,OAAAA,MAAiB,YACnB,CAAA,QAAA,4QAAW,4BAAA,EAA0B,KAAK,KAAK,CAAC,EAAA;gDAAA,0CAAA;gDAGtD,QAAA,CAAQ,KAAA,CAAA,KAAA,KAAK,gBAAA,KAAL,OAAA,KAAA,IAAA,GAAuB,MAAA,KAAvB,OAAA,KAAA,IAAA,GAA+B,WAAA;4CACzC;wCACF;oCACF;gCACA,KAAK;oCAAQ;wCACX,IAAI,KAAK,IAAA,YAAgB,KAAK;4CAC5B,MAAM,oOAAI,gCAAA,CAA8B;gDACtC,eACE;4CACJ,CAAC;wCACH;wCAEA,OAAQ,KAAK,QAAA,EAAU;4CACrB,KAAK;gDAAa;oDAChB,OAAO;wDACL,MAAM;wDACN,aAAa;4DAAE,MAAM,KAAK,IAAA;4DAAM,QAAQ;wDAAM;oDAChD;gDACF;4CACA,KAAK;4CACL,KAAK;gDAAc;oDACjB,OAAO;wDACL,MAAM;wDACN,aAAa;4DAAE,MAAM,KAAK,IAAA;4DAAM,QAAQ;wDAAM;oDAChD;gDACF;4CACA,KAAK;gDAAmB;oDACtB,OAAO;wDACL,MAAM;wDACN,MAAM;4DACJ,UAAA,CAAU,KAAA,KAAK,QAAA,KAAL,OAAA,KAAiB,CAAA,KAAA,EAAQ,KAAK,CAAA,IAAA,CAAA;4DACxC,WAAW,CAAA,4BAAA,EAA+B,KAAK,IAAI,EAAA;wDACrD;oDACF;gDACF;4CACA;gDAAS;oDACP,MAAM,oOAAI,gCAAA,CAA8B;wDACtC,eAAe,CAAA,uBAAA,EAA0B,KAAK,QAAQ,CAAA,iBAAA,CAAA;oDACxD,CAAC;gDACH;wCACF;oCACF;4BACF;wBACF,CAAC;oBACH,CAAC;oBAED;gBACF;YAEA,KAAK;gBAAa;oBAChB,IAAI,OAAO;oBACX,MAAM,YAID,CAAC,CAAA;oBAEN,KAAA,MAAW,QAAQ,QAAS;wBAC1B,OAAQ,KAAK,IAAA,EAAM;4BACjB,KAAK;gCAAQ;oCACX,QAAQ,KAAK,IAAA;oCACb;gCACF;4BACA,KAAK;gCAAa;oCAChB,UAAU,IAAA,CAAK;wCACb,IAAI,KAAK,UAAA;wCACT,MAAM;wCACN,UAAU;4CACR,MAAM,KAAK,QAAA;4CACX,WAAW,KAAK,SAAA,CAAU,KAAK,IAAI;wCACrC;oCACF,CAAC;oCACD;gCACF;wBACF;oBACF;oBAEA,SAAS,IAAA,CAAK;wBACZ,MAAM;wBACN,SAAS;wBACT,YAAY,UAAU,MAAA,GAAS,IAAI,YAAY,KAAA;oBACjD,CAAC;oBAED;gBACF;YACA,KAAK;gBAAQ;oBACX,KAAA,MAAW,gBAAgB,QAAS;wBAClC,SAAS,IAAA,CAAK;4BACZ,MAAM;4BACN,MAAM,aAAa,QAAA;4BACnB,SACE,OAAO,aAAa,MAAA,KAAW,WAC3B,KAAK,SAAA,CAAU,aAAa,MAAM,IAClC,GAAG,aAAa,MAAM,EAAA;wBAC9B,CAAC;oBACH;oBACA;gBACF;YACA;gBAAS;oBACP,MAAM,mBAA0B;oBAChC,MAAM,IAAI,MAAM,CAAA,kBAAA,EAAqB,gBAAgB,EAAE;gBACzD;QACF;IACF;IAEA,OAAO;AACT;;ACpJO,SAAS,oBACf,YAAA,EAC8B;IAC9B,OAAQ,cAAc;QACrB,KAAK;YACJ,OAAO;QACR,KAAK;YACJ,OAAO;QACR,KAAK;YACJ,OAAO;QACR,KAAK;QACL,KAAK;YACJ,OAAO;QACR;YACC,OAAO;IACT;AACD;;;ACfO,IAAM,8MAAsB,IAAA,CAAE,MAAA,CAAO;IAC1C,+LAAO,IAAA,CAAE,MAAA,CAAO;QACd,iMAAS,IAAA,CAAE,MAAA,CAAO;QAAA,iEAAA;QAAA,iEAAA;QAAA,aAAA;QAKlB,8LAAM,IAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QACzB,+LAAO,IAAA,CAAE,GAAA,CAAI,EAAE,OAAA,CAAQ;QACvB,8LAAM,IAAA,CAAE,KAAA,CAAM;oMAAC,IAAA,CAAE,MAAA,CAAO;oMAAG,IAAA,CAAE,MAAA,CAAO,CAAC;SAAC,EAAE,OAAA,CAAQ;IAClD,CAAC;AACH,CAAC;AAGM,IAAM,gCAA4B,uSAAA,EAA+B;IACvE,aAAa;IACb,gBAAgB,CAAA,OAAQ,KAAK,KAAA,CAAM,OAAA;AACpC,CAAC;;;AC+CM,IAAM,mCACX,CACE,cAEF,CAAO,KAAyC,QAAA,MAAA;YAAzC;SAAA,EAAyC,UAAzC,EAAE,QAAA,CAAS,CAAA,EAA8B;YAC9C,MAAM,4RAAkB,yBAAA,EAAuB,QAAQ;YAEvD,IAAI,SAAS,IAAA,IAAQ,MAAM;gBACzB,MAAM,IAAI,yPAAA,CAAuB,CAAC,CAAC;YACrC;YAEA,OAAO;gBACL;gBACA,OAAO,SAAS,IAAA,CACb,WAAA,CAAY,IAAI,kBAAkB,CAAC,EACnC,WAAA,2QAAY,gCAAA,CAA8B,CAAC,GAC3C,WAAA,CACC,IAAI,gBAAkD;oBACpD,WAAU,EAAE,KAAA,EAAO,IAAA,CAAK,CAAA,EAAG,UAAA,EAAY;wBAErC,IAAI,SAAS,UAAU;4BACrB;wBACF;wBACA,IAAI;4BACF,MAAM,WAAW,KAAK,KAAA,CAAM,IAAI;4BAChC,IAAI,SAAS,OAAA,EAAS;gCAEpB,SAAS,OAAA,GAAU,mBAAmB,SAAS,OAAO;4BACxD;4BACA,WAAW,OAAA,2QACT,gBAAA,EAAc;gCACZ,MAAM,KAAK,SAAA,CAAU,QAAQ;gCAC7B,QAAQ;4BACV,CAAC;wBAEL,EAAA,OAAS,OAAO;4BACd,QAAQ,KAAA,CAAM,mCAAmC,KAAK;wBACxD;oBACF;gBACF,CAAC;YAEP;QACF;;AC7GK,SAAS,oBAAoB,EACC,EAAA,EAAA,SAAA;AAEA,OAAA,EACD,EAIjC;IACF,OAAO;QACN,IAAI,MAAA,OAAA,KAAM,KAAA;QAAA,+BAAA;QAEV,WAAW,WAAW,OAAO,IAAI,KAAK,UAAU,GAAI,IAAI,KAAA;IACzD;AACD;;;ACRO,SAAS,aACd,IAAA,EAGA,iBAAA,EAmBA;IA7BF,IAAA;IA+BE,MAAM,QAAA,CAAA,CAAQ,KAAA,KAAK,KAAA,KAAL,OAAA,KAAA,IAAA,GAAY,MAAA,IAAS,KAAK,KAAA,GAAQ,KAAA;IAChD,MAAM,eAAkD,CAAC,CAAA;IAEzD,IAAI,SAAS,MAAM;QACjB,OAAO;YAAE,OAAO,KAAA;YAAW,aAAa,KAAA;YAAW;QAAa;IAClE;IAEA,MAAM,YAQD,CAAC,CAAA;IAEN,KAAA,MAAW,QAAQ,MAAO;QACxB,IAAI,KAAK,IAAA,KAAS,oBAAoB;YACpC,aAAa,IAAA,CAAK;gBAAE,MAAM;gBAAoB;YAAK,CAAC;QACtD,OAAO;YACL,UAAU,IAAA,CAAK;gBACb,MAAM;gBACN,UAAU;oBACR,MAAM,KAAK,IAAA;oBACX,aAAa,KAAK,WAAA;oBAClB,YAAY,KAAK,UAAA;oBACjB,QAAQ,oBAAoB,OAAO,KAAA;gBACrC;YACF,CAAC;QACH;IACF;IAEA,MAAM,aAAa,KAAK,UAAA;IAExB,IAAI,cAAc,MAAM;QACtB,OAAO;YAAE,OAAO;YAAW,aAAa,KAAA;YAAW;QAAa;IAClE;IAEA,MAAM,OAAO,WAAW,IAAA;IAExB,OAAQ,MAAM;QACZ,KAAK;QACL,KAAK;YACH,OAAO;gBAAE,OAAO;gBAAW,aAAa;gBAAM;YAAa;QAC7D,KAAK;YACH,OAAO;gBAAE,OAAO;gBAAW,aAAa;gBAAO;YAAa;QAAA,4CAAA;QAAA,iEAAA;QAI9D,KAAK;YACH,OAAO;gBACL,OAAO,UAAU,MAAA,CACf,CAAC,OAAS,KAAK,QAAA,CAAS,IAAA,KAAS,WAAW,QAAA;gBAE9C,aAAa;gBACb;YACF;QACF;YAAS;gBACP,MAAM,mBAA0B;gBAChC,MAAM,oOAAIC,gCAAAA,CAA8B;oBACtC,eAAe,CAAA,8BAAA,EAAiC,gBAAgB,EAAA;gBAClE,CAAC;YACH;IACF;AACF;;ANrCO,IAAM,wBAAN,MAAuD;IAS5D,YACE,OAAA,EACA,QAAA,EACA,MAAA,CACA;QAZF,IAAA,CAAS,oBAAA,GAAuB;QAChC,IAAA,CAAS,2BAAA,GAA8B;QAYrC,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,MAAA,GAAS;IAChB;IAEA,IAAI,WAAmB;QACrB,OAAO,IAAA,CAAK,MAAA,CAAO,QAAA;IACrB;IAEQ,QAAQ,EACd,IAAA,EACA,MAAA,EACA,SAAA,EACA,WAAA,EACA,IAAA,EACA,IAAA,EACA,gBAAA,EACA,eAAA,EACA,aAAA,EACA,cAAA,EACA,IAAA,EACA,gBAAA,EACF,EAAiD;QA/FnD,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;QAgGI,MAAM,OAAO,KAAK,IAAA;QAElB,MAAM,WAAyC,CAAC,CAAA;QAEhD,IAAI,QAAQ,MAAM;YAChB,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,IACE,kBAAkB,QAClB,eAAe,IAAA,KAAS,UACxB,eAAe,MAAA,IAAU,MACzB;YACA,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;gBACT,SAAS;YACX,CAAC;QACH;QAEA,MAAM,WAAW;YAAA,YAAA;YAEf,OAAO,IAAA,CAAK,OAAA;YAAA,2BAAA;YAAA,8BAAA;YAAA,0DAAA;YAAA,yBAAA;YAOZ,YAAY;YACZ;YACA,OAAO;YACP,mBAAmB;YACnB,kBAAkB;YAClB,MAAM;YACN;YAAA,4BAAA;YAGA,uBAAA,CACE,KAAA,CAAA,KAAA,oBAAA,OAAA,KAAA,IAAA,iBAAkB,IAAA,KAAlB,OAAA,KAAA,IAAA,GAAwB,mBAAA,KAAxB,OAAA,KAA+C,KAAA;YACjD,OAAA,CAAO,KAAA,CAAA,KAAA,oBAAA,OAAA,KAAA,IAAA,iBAAkB,IAAA,KAAlB,OAAA,KAAA,IAAA,GAAwB,KAAA,KAAxB,OAAA,KAAiC,KAAA;YACxC,UAAA,CAAU,KAAA,CAAA,KAAA,oBAAA,OAAA,KAAA,IAAA,iBAAkB,IAAA,KAAlB,OAAA,KAAA,IAAA,GAAwB,QAAA,KAAxB,OAAA,KAAoC,KAAA;YAAA,mBAAA;YAG9C,iBAAA,CACE,kBAAA,OAAA,KAAA,IAAA,eAAgB,IAAA,MAAS,SAAS;gBAAE,MAAM;YAAc,IAAI,KAAA;YAAA,YAAA;YAG9D,UAAU,0BAA0B,MAAM;QAC5C;QAUA,OAAQ,MAAM;YACZ,KAAK;gBAAW;oBACd,MAAM,EAAE,KAAA,EAAO,WAAA,EAAa,YAAA,CAAa,CAAA,GAAI,aAAa,MAAM,KAAK;oBAErE,OAAO;wBACL,MAAM,cAAA,eAAA,CAAA,GAAK,WAAL;4BAAe;4BAAO;wBAAY;wBACxC,UAAU,CAAC;+BAAG,UAAU;+BAAG,YAAY;yBAAA;oBACzC;gBACF;YAEA,KAAK;gBAAe;oBAClB,OAAO;wBACL,MAAM,cAAA,eAAA,CAAA,GACD,WADC;4BAEJ,iBAAiB;gCAAE,MAAM;4BAAc;wBACzC;wBACA;oBACF;gBACF;YAEA,KAAK;gBAAe;oBAClB,OAAO;wBACL,MAAM,cAAA,eAAA,CAAA,GACD,WADC;4BAEJ,aAAa;4BACb,OAAO;gCAAC;oCAAE,MAAM;oCAAY,UAAU,KAAK,IAAA;gCAAK,CAAC;6BAAA;wBACnD;wBACA;oBACF;gBACF;YAEA;gBAAS;oBACP,MAAM,mBAA0B;oBAChC,MAAM,IAAI,MAAM,CAAA,kBAAA,EAAqB,gBAAgB,EAAE;gBACzD;QACF;IACF;IAEM,WACJ,OAAA,EAC6D;QAAA,OAAA,QAAA,IAAA,EAAA,MAAA;YAtMjE,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;YAuMI,MAAM,EAAE,IAAA,EAAM,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,OAAO;YAC/C,MAAM,OAAO,cAAA,eAAA,CAAA,GACR,OADQ;gBAEX,QAAQ;YACV;YAEA,MAAM,EAAE,eAAA,EAAiB,OAAO,QAAA,CAAS,CAAA,GAAI,gRAAM,gBAAA,EAAc;gBAAA,kDAAA;gBAE/D,KAAK,GAAG,IAAA,CAAK,MAAA,CAAO,OAAO,CAAA,iBAAA,CAAA;gBAC3B,mRAAS,iBAAA,EAAe,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,QAAQ,OAAO;gBAC9D;gBACA,uBAAuB;gBACvB,qSAA2B,4BAAA,EACzB;gBAEF,aAAa,QAAQ,WAAA;gBACrB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;YACrB,CAAC;YACD,MAAgD,KAAA,MAAxC,EAAA,UAAU,SAAA,CAzNtB,CAAA,GAyNoD,IAAhB,cAAA,UAAgB,IAAhB;gBAAxB;aAAA;YAGR,IACE,SAAS,OAAA,IACT,MAAM,OAAA,CAAQ,SAAS,OAAO,KAC9B,SAAS,OAAA,CAAQ,MAAA,GAAS,GAC1B;gBACA,MAAM,SAAS,SAAS,OAAA,CAAQ,CAAC,CAAA;gBAGjC,MAAM,yBAAA,CAAyB,KAAA,SAAS,KAAA,KAAT,OAAA,KAAA,IAAA,GAAgB,yBAAA;gBAC/C,MAAM,qBAAA,CAAqB,KAAA,SAAS,KAAA,KAAT,OAAA,KAAA,IAAA,GAAgB,qBAAA;gBAC3C,MAAM,mBAAoD;oBAAE,QAAQ,CAAC;gBAAE;gBACvE,IAAA,CACE,0BAAA,OAAA,KAAA,IAAA,uBAAwB,gBAAA,KAAoB,QAC5C,iBAAiB,MAAA,EACjB;oBACA,iBAAiB,MAAA,CAAO,eAAA,GACtB,0BAAA,OAAA,KAAA,IAAA,uBAAwB,gBAAA;gBAC5B;gBACA,IAAA,CACE,0BAAA,OAAA,KAAA,IAAA,uBAAwB,0BAAA,KAA8B,QACtD,iBAAiB,MAAA,EACjB;oBACA,iBAAiB,MAAA,CAAO,wBAAA,GACtB,0BAAA,OAAA,KAAA,IAAA,uBAAwB,0BAAA;gBAC5B;gBACA,IAAA,CACE,0BAAA,OAAA,KAAA,IAAA,uBAAwB,0BAAA,KAA8B,QACtD,iBAAiB,MAAA,EACjB;oBACA,iBAAiB,MAAA,CAAO,wBAAA,GACtB,0BAAA,OAAA,KAAA,IAAA,uBAAwB,0BAAA;gBAC5B;gBACA,IAAA,CACE,sBAAA,OAAA,KAAA,IAAA,mBAAoB,aAAA,KAAiB,QACrC,iBAAiB,MAAA,EACjB;oBACA,iBAAiB,MAAA,CAAO,kBAAA,GACtB,sBAAA,OAAA,KAAA,IAAA,mBAAoB,aAAA;gBACxB;gBAEA,OAAO;oBACL,MAAA,CAAM,KAAA,CAAA,KAAA,UAAA,OAAA,KAAA,IAAA,OAAQ,OAAA,KAAR,OAAA,KAAA,IAAA,GAAiB,OAAA,KAAjB,OAAA,KAA4B;oBAClC,WAAA,CAAA,CAAW,KAAA,UAAA,OAAA,KAAA,IAAA,OAAQ,OAAA,KAAR,OAAA,KAAA,IAAA,GAAiB,aAAA,IACxB;wBACE;4BACE,cAAc;4BACd,sRAAY,aAAA,CAAW;4BACvB,UAAA,CACE,KAAA,CAAA,KAAA,CAAA,KAAA,UAAA,OAAA,KAAA,IAAA,OAAQ,OAAA,KAAR,OAAA,KAAA,IAAA,GAAiB,aAAA,KAAjB,OAAA,KAAA,IAAA,GAAgC,IAAA,KAAhC,OAAA,KAAwC;4BAC1C,MAAA,CAAM,KAAA,CAAA,KAAA,CAAA,KAAA,UAAA,OAAA,KAAA,IAAA,OAAQ,OAAA,KAAR,OAAA,KAAA,IAAA,GAAiB,aAAA,KAAjB,OAAA,KAAA,IAAA,GAAgC,SAAA,KAAhC,OAAA,KAA6C;wBACrD;qBACF,GAAA,CACA,KAAA,CAAA,KAAA,UAAA,OAAA,KAAA,IAAA,OAAQ,OAAA,KAAR,OAAA,KAAA,IAAA,GAAiB,UAAA,KAAjB,OAAA,KAAA,IAAA,GAA6B,GAAA,CAAI,CAAC,aAAU;wBAhRxD,IAAAC,KAAAC;wBAgR4D,OAAA;4BAC9C,cAAc;4BACd,YAAA,CAAYD,MAAA,SAAS,EAAA,KAAT,OAAAA,MAAe,uRAAA,CAAW;4BACtC,UAAA,CAAUC,MAAA,SAAS,QAAA,CAAS,IAAA,KAAlB,OAAAA,MAA0B;4BACpC,MAAM,SAAS,QAAA,CAAS,SAAA;wBAC1B;oBAAA;oBACJ,cAAc,oBAAA,CAAoB,KAAA,UAAA,OAAA,KAAA,IAAA,OAAQ,aAAA,KAAR,OAAA,KAAyB,EAAE;oBAC7D,OAAO;wBACL,cAAA,CAAc,KAAA,CAAA,KAAA,SAAS,KAAA,KAAT,OAAA,KAAA,IAAA,GAAgB,aAAA,KAAhB,OAAA,KAAiC;wBAC/C,kBAAA,CAAkB,KAAA,CAAA,KAAA,SAAS,KAAA,KAAT,OAAA,KAAA,IAAA,GAAgB,iBAAA,KAAhB,OAAA,KAAqC;oBACzD;oBACA,SAAS;wBAAE;wBAAW;oBAAY;oBAClC,aAAa;wBAAE,SAAS;oBAAgB;oBACxC,SAAS;wBAAE,MAAM,KAAK,SAAA,CAAU,IAAI;oBAAE;oBACtC,UAAU,oBAAoB,QAAQ;oBACtC;gBACF;YACF;YAGA,MAAM,YAAA,CACJ,KAAA,CAAA,KAAA,SAAS,OAAA,KAAT,OAAA,KAAA,IAAA,GAAkB,UAAA,KAAlB,OAAA,KAAA,IAAA,GAA8B,GAAA,CAAI,CAAC,aAAU;gBArSnD,IAAAD,KAAAC;gBAqSuD,OAAA;oBAC/C,MAAM,KAAK,SAAA,CAAU,SAAS,QAAA,CAAS,SAAS;oBAChD,YAAA,CAAYD,MAAA,SAAS,EAAA,KAAT,OAAAA,gRAAe,aAAA,CAAW;oBACtC,cAAc;oBACd,UAAA,CAAUC,MAAA,SAAS,QAAA,CAAS,IAAA,KAAlB,OAAAA,MAA0B;gBACtC;YAAA;YAEF,MAAM,UAAA,CAAU,KAAA,CAAA,KAAA,SAAS,OAAA,KAAT,OAAA,KAAA,IAAA,GAAkB,OAAA,KAAlB,OAAA,KAA6B;YAC7C,MAAM,aAAa,QAAQ,KAAA,CAAM,uBAAuB;YAExD,OAAO,cAAA,eAAA;gBACL,cAAc,oBAAoB,SAAS,WAAW;gBACtD,SAAS;oBAAE;oBAAW;gBAAY;gBAClC,aAAa;oBAAE,SAAS;gBAAgB;gBACxC,SAAS;oBAAE,MAAM,KAAK,SAAA,CAAU,IAAI;gBAAE;gBACtC,MAAM,QAAQ,OAAA,CAAQ,uBAAuB,EAAE,EAAE,IAAA,CAAK;YAAA,GAClD,cAAc;gBAChB,WAAA,CAAW,KAAA,UAAA,CAAW,CAAC,CAAA,KAAZ,OAAA,KAAA,IAAA,GAAe,IAAA;YAC5B,IARK;gBASL,WAAA,CAAW,KAAA,CAAA,KAAA,CAAA,KAAA,SAAS,OAAA,KAAT,OAAA,KAAA,IAAA,EAAA,CAAmB,EAAA,KAAnB,OAAA,KAAA,IAAA,GAAuB,OAAA,CAAQ,iBAAA,KAA/B,OAAA,KAAoD,KAAA;gBAC/D;gBACA,OAAO;oBACL,kBAAkB,SAAS,UAAA,IAAc;oBACzC,cAAc,SAAS,iBAAA,IAAqB;gBAC9C;gBACA;YACF;QACF;IAAA;IAEM,SACJ,OAAA,EAC2D;QAAA,OAAA,QAAA,IAAA,EAAA,MAAA;YAC3D,MAAM,EAAE,IAAA,EAAM,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,OAAO;YAE/C,MAAM,EAAE,eAAA,EAAiB,OAAO,QAAA,CAAS,CAAA,GAAI,gRAAM,gBAAA,EAAc;gBAC/D,KAAK,GAAG,IAAA,CAAK,MAAA,CAAO,OAAO,CAAA,iBAAA,CAAA;gBAC3B,mRAAS,iBAAA,EAAe,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,QAAQ,OAAO;gBAC9D,MAAM,cAAA,eAAA,eAAA,CAAA,GACD,OACC,OAAO,KAAK,QAAA,KAAa,YAAY,KAAK,QAAA,KAAa,OACvD,KAAK,QAAA,GACL,CAAC,IAJD;oBAKJ,QAAQ;gBACV;gBACA,uBAAuB;gBACvB,2BAA2B,iCACzB;gBAEF,aAAa,QAAQ,WAAA;gBACrB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;YACrB,CAAC;YAED,MAAgD,KAAA,MAAxC,EAAA,UAAU,SAAA,CAzVtB,CAAA,GAyVoD,IAAhB,cAAA,UAAgB,IAAhB;gBAAxB;aAAA;YAER,MAAM,YAQD,CAAC,CAAA;YAEN,IAAI,eAA4C;YAChD,IAAI,QAGA;gBACF,cAAc,KAAA;gBACd,kBAAkB,KAAA;YACpB;YAEA,IAAI,eAAe;YAInB,IAAI;YACJ,IAAI,aAAa;YACjB,OAAO;gBACL,QAAQ,SAAS,WAAA,CACf,IAAI,gBAGF;oBACA,WAAU,KAAA,EAAO,UAAA,EAAY;wBA1XvC,IAAAD,KAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;wBA4XY,IAAI,CAAC,MAAM,OAAA,EAAS;4BAClB,eAAe;4BACf,WAAW,OAAA,CAAQ;gCAAE,MAAM;gCAAS,OAAO,MAAM,KAAA;4BAAM,CAAC;4BACxD;wBACF;wBAEA,MAAM,QAAQ,MAAM,KAAA;wBAGpB,IAAI,WAAW,OAAO;4BACpB,eAAe;4BACf,WAAW,OAAA,CAAQ;gCAAE,MAAM;gCAAS,OAAO,MAAM,KAAA;4BAAM,CAAC;4BACxD;wBACF;wBAGA,IACE,MAAM,OAAA,IACN,MAAM,OAAA,CAAQ,MAAM,OAAO,KAC3B,MAAM,OAAA,CAAQ,MAAA,GAAS,GACvB;4BAEA,MAAM,SAAS,MAAM,OAAA,CAAQ,CAAC,CAAA;4BAE9B,IAAA,CAAI,UAAA,OAAA,KAAA,IAAA,OAAQ,aAAA,KAAiB,MAAM;gCACjC,eAAe,oBAAoB,OAAO,aAAa;4BACzD;4BAEA,IAAA,CAAI,UAAA,OAAA,KAAA,IAAA,OAAQ,KAAA,KAAS,MAAM;gCACzB;4BACF;4BAEA,MAAM,QAAQ,OAAO,KAAA;4BAErB,IAAI,MAAM,iBAAA,IAAqB,MAAM;gCACnC,WAAW,OAAA,CAAQ;oCACjB,MAAM;oCACN,WAAW,MAAM,iBAAA;gCACnB,CAAC;4BACH;4BAEA,IAAI,MAAM,OAAA,IAAW,MAAM;gCACzB,WAAW,OAAA,CAAQ;oCACjB,MAAM;oCACN,WAAW,MAAM,OAAA;gCACnB,CAAC;4BACH;4BAUA,MAAM,kBACJ,MAAM,aAAA,IAAiB,OACnB;gCACE;oCACE,MAAM;oCACN,8QAAI,aAAA,CAAW;oCACf,UAAU,MAAM,aAAA;oCAChB,OAAO;gCACT;6BACF,GACA,MAAM,UAAA;4BAEZ,IAAI,mBAAmB,MAAM;gCAC3B,KAAA,MAAW,iBAAiB,gBAAiB;oCAC3C,MAAM,QAAQ,cAAc,KAAA,IAAS,OAAO,KAAA;oCAG5C,IAAI,SAAA,CAAU,KAAK,CAAA,IAAK,MAAM;wCAC5B,IAAI,cAAc,IAAA,KAAS,YAAY;4CACrC,MAAM,oOAAI,2BAAA,CAAyB;gDACjC,MAAM;gDACN,SAAS,CAAA,yBAAA,CAAA;4CACX,CAAC;wCACH;wCAEA,IAAI,cAAc,EAAA,IAAM,MAAM;4CAC5B,MAAM,oOAAI,2BAAA,CAAyB;gDACjC,MAAM;gDACN,SAAS,CAAA,6BAAA,CAAA;4CACX,CAAC;wCACH;wCAEA,IAAA,CAAA,CAAIA,MAAA,cAAc,QAAA,KAAd,OAAA,KAAA,IAAAA,IAAwB,IAAA,KAAQ,MAAM;4CACxC,MAAM,IAAI,2PAAA,CAAyB;gDACjC,MAAM;gDACN,SAAS,CAAA,wCAAA,CAAA;4CACX,CAAC;wCACH;wCAEA,SAAA,CAAU,KAAK,CAAA,GAAI;4CACjB,IAAI,cAAc,EAAA;4CAClB,MAAM;4CACN,UAAU;gDACR,MAAM,cAAc,QAAA,CAAS,IAAA;gDAC7B,WAAA,CAAW,KAAA,cAAc,QAAA,CAAS,SAAA,KAAvB,OAAA,KAAoC;4CACjD;4CACA,aAAa;wCACf;wCAEA,MAAME,YAAW,SAAA,CAAU,KAAK,CAAA;wCAEhC,IAAA,CAAA,CACE,KAAAA,UAAS,QAAA,KAAT,OAAA,KAAA,IAAA,GAAmB,IAAA,KAAQ,QAAA,CAAA,CAC3B,KAAAA,UAAS,QAAA,KAAT,OAAA,KAAA,IAAA,GAAmB,SAAA,KAAa,MAChC;4CAEA,IAAIA,UAAS,QAAA,CAAS,SAAA,CAAU,MAAA,GAAS,GAAG;gDAC1C,WAAW,OAAA,CAAQ;oDACjB,MAAM;oDACN,cAAc;oDACd,YAAYA,UAAS,EAAA;oDACrB,UAAUA,UAAS,QAAA,CAAS,IAAA;oDAC5B,eAAeA,UAAS,QAAA,CAAS,SAAA;gDACnC,CAAC;4CACH;4CAIA,8QAAI,iBAAA,EAAeA,UAAS,QAAA,CAAS,SAAS,GAAG;gDAC/C,WAAW,OAAA,CAAQ;oDACjB,MAAM;oDACN,cAAc;oDACd,YAAA,CAAY,KAAAA,UAAS,EAAA,KAAT,OAAA,KAAe,uRAAA,CAAW;oDACtC,UAAUA,UAAS,QAAA,CAAS,IAAA;oDAC5B,MAAMA,UAAS,QAAA,CAAS,SAAA;gDAC1B,CAAC;gDACDA,UAAS,WAAA,GAAc;4CACzB;wCACF;wCAEA;oCACF;oCAGA,MAAM,WAAW,SAAA,CAAU,KAAK,CAAA;oCAEhC,IAAA,CAAA,CAAI,KAAA,cAAc,QAAA,KAAd,OAAA,KAAA,IAAA,GAAwB,SAAA,KAAa,MAAM;wCAC7C,SAAS,QAAA,CAAU,SAAA,IAAA,CACjB,KAAA,CAAA,KAAA,cAAc,QAAA,KAAd,OAAA,KAAA,IAAA,GAAwB,SAAA,KAAxB,OAAA,KAAqC;oCACzC;oCAGA,WAAW,OAAA,CAAQ;wCACjB,MAAM;wCACN,cAAc;wCACd,YAAY,SAAS,EAAA;wCACrB,UAAU,SAAS,QAAA,CAAS,IAAA;wCAC5B,eAAA,CAAe,KAAA,cAAc,QAAA,CAAS,SAAA,KAAvB,OAAA,KAAoC;oCACrD,CAAC;oCAGD,IAAA,CAAA,CACE,KAAA,SAAS,QAAA,KAAT,OAAA,KAAA,IAAA,GAAmB,IAAA,KAAQ,QAAA,CAAA,CAC3B,KAAA,SAAS,QAAA,KAAT,OAAA,KAAA,IAAA,GAAmB,SAAA,KAAa,kRAChC,iBAAA,EAAe,SAAS,QAAA,CAAS,SAAS,GAC1C;wCACA,WAAW,OAAA,CAAQ;4CACjB,MAAM;4CACN,cAAc;4CACd,YAAA,CAAY,KAAA,SAAS,EAAA,KAAT,OAAA,KAAe,uRAAA,CAAW;4CACtC,UAAU,SAAS,QAAA,CAAS,IAAA;4CAC5B,MAAM,SAAS,QAAA,CAAS,SAAA;wCAC1B,CAAC;wCACD,SAAS,WAAA,GAAc;oCACzB;gCACF;4BACF;4BAEA;wBACF;wBAGA,IAAI,cAAc;4BAChB,eAAe;4BACf,WAAW,OAAA,CAAQ,eAAA;gCACjB,MAAM;4BAAA,GACH,oBAAoB,KAAK,EAC7B;wBACH;wBAEA,IAAI,MAAM,OAAA,IAAW,MAAM;4BACzB,WAAW,OAAA,CAAQ;gCACjB,MAAM;gCACN,cAAc;gCACd,sRAAY,aAAA,CAAW;gCACvB,UAAU;gCACV,MAAM,KAAK,SAAA,CAAU,MAAM,OAAO;4BACpC,CAAC;wBACH;wBAIA,IAAI,MAAM,WAAA,IAAe,MAAM;4BAC7B,eAAe,oBAAoB,MAAM,WAAW;4BACpD,IAAI,gBAAgB,QAAQ;gCAC1B,WAAW,OAAA,CAAQ,eAAA;oCACjB,MAAM;oCACN;oCACA,OAAO;wCACL,cAAA,CAAc,KAAA,MAAM,YAAA,KAAN,OAAA,KAAsB;wCACpC,kBAAA,CAAkB,KAAA,MAAM,gBAAA,KAAN,OAAA,KAA0B;oCAC9C;gCAAA,GACI,oBAAoB,OAAO;oCAAE;gCAAiB,IAAI,CAAC,EACxD;4BACH;wBACF;wBAGA,IAAI,MAAM,OAAA,IAAW,MAAM;4BACzB,MAAM,UAAA,CAAU,KAAA,MAAM,OAAA,CAAQ,OAAA,KAAd,OAAA,KAAyB;4BAEzC,IAAI,cAAc,QAAQ,QAAA,CAAS,SAAS,GAAG;gCAC7C,aAAa;gCACb,IAAI,QAAQ,QAAA,CAAS,UAAU,GAAG;oCAChC,aAAa;gCACf;gCAEA,MAAM,eAAe,QAClB,OAAA,CAAQ,WAAW,EAAE,EACrB,OAAA,CAAQ,YAAY,EAAE;gCACzB,WAAW,OAAA,CAAQ;oCACjB,MAAM;oCACN,WAAW;gCACb,CAAC;4BACH,OAAO;gCACL,WAAW,OAAA,CAAQ;oCACjB,MAAM;oCACN,WAAW;gCACb,CAAC;4BACH;4BAEA,IAAI,MAAM,OAAA,CAAQ,UAAA,IAAc,MAAM;gCACpC,KAAA,MAAW,YAAY,MAAM,OAAA,CAAQ,UAAA,CAAY;oCAC/C,IAAA,CAAA,CACE,KAAA,SAAS,QAAA,KAAT,OAAA,KAAA,IAAA,GAAmB,IAAA,KAAQ,QAAA,CAAA,CAC3B,KAAA,SAAS,QAAA,KAAT,OAAA,KAAA,IAAA,GAAmB,SAAA,KAAa,MAChC;wCACA,MAAMC,QACJ,OAAO,SAAS,QAAA,CAAS,SAAA,KAAc,WACnC,KAAK,SAAA,CAAU,SAAS,QAAA,CAAS,SAAS,IAC1C,2RAAA,EAAe,SAAS,QAAA,CAAS,SAAS,IACxC,SAAS,QAAA,CAAS,SAAA,GAClB;wCAER,WAAW,OAAA,CAAQ;4CACjB,MAAM;4CACN,cAAc;4CACd,YAAA,CAAY,KAAA,SAAS,EAAA,KAAT,OAAA,+QAAe,aAAA,CAAW;4CACtC,UAAU,SAAS,QAAA,CAAS,IAAA;4CAC5B,MAAMA;wCACR,CAAC;oCACH;gCACF;4BACF;wBACF;wBAEA,IAAI,MAAM,OAAA,IAAW,MAAM;4BACzB,WAAW,OAAA,CAAQ;gCACjB,MAAM;gCACN,WAAW,MAAM,OAAA;4BACnB,CAAC;wBACH;wBAEA,IAAI,MAAM,UAAA,IAAc,MAAM;4BAC5B,KAAA,MAAW,iBAAiB,MAAM,UAAA,CAAY;gCAC5C,MAAM,QAAQ,cAAc,KAAA;gCAC5B,IAAI,SAAS,MAAM;oCACjB,MAAM,oOAAI,2BAAA,CAAyB;wCACjC,MAAM;wCACN,SAAS,CAAA,gCAAA,CAAA;oCACX,CAAC;gCACH;gCAEA,IAAI,SAAA,CAAU,KAAK,CAAA,IAAK,MAAM;oCAC5B,IAAI,cAAc,IAAA,KAAS,YAAY;wCACrC,MAAM,oOAAI,2BAAA,CAAyB;4CACjC,MAAM;4CACN,SAAS,CAAA,yBAAA,CAAA;wCACX,CAAC;oCACH;oCAEA,IAAI,cAAc,EAAA,IAAM,MAAM;wCAC5B,MAAM,oOAAI,2BAAA,CAAyB;4CACjC,MAAM;4CACN,SAAS,CAAA,6BAAA,CAAA;wCACX,CAAC;oCACH;oCAEA,IAAA,CAAA,CAAI,KAAA,cAAc,QAAA,KAAd,OAAA,KAAA,IAAA,GAAwB,IAAA,KAAQ,MAAM;wCACxC,MAAM,oOAAI,2BAAA,CAAyB;4CACjC,MAAM;4CACN,SAAS,CAAA,wCAAA,CAAA;wCACX,CAAC;oCACH;oCAEA,SAAA,CAAU,KAAK,CAAA,GAAI;wCACjB,IAAI,cAAc,EAAA;wCAClB,MAAM;wCACN,aAAa;wCACb,UAAU;4CACR,MAAM,cAAc,QAAA,CAAS,IAAA;4CAC7B,WAAA,CAAW,KAAA,cAAc,QAAA,CAAS,SAAA,KAAvB,OAAA,KAAoC;wCACjD;oCACF;oCAEA,MAAMD,YAAW,SAAA,CAAU,KAAK,CAAA;oCAEhC,IAAA,CAAA,CACE,KAAAA,UAAS,QAAA,KAAT,OAAA,KAAA,IAAA,GAAmB,IAAA,KAAQ,QAAA,CAAA,CAC3B,KAAAA,UAAS,QAAA,KAAT,OAAA,KAAA,IAAA,GAAmB,SAAA,KAAa,MAChC;wCAEA,IAAIA,UAAS,QAAA,CAAS,SAAA,CAAU,MAAA,GAAS,GAAG;4CAC1C,WAAW,OAAA,CAAQ;gDACjB,MAAM;gDACN,cAAc;gDACd,YAAYA,UAAS,EAAA;gDACrB,UAAUA,UAAS,QAAA,CAAS,IAAA;gDAC5B,eAAeA,UAAS,QAAA,CAAS,SAAA;4CACnC,CAAC;wCACH;wCAIA,8QAAI,iBAAA,EAAeA,UAAS,QAAA,CAAS,SAAS,GAAG;4CAC/C,WAAW,OAAA,CAAQ;gDACjB,MAAM;gDACN,cAAc;gDACd,YAAA,CAAY,KAAAA,UAAS,EAAA,KAAT,OAAA,+QAAe,aAAA,CAAW;gDACtC,UAAUA,UAAS,QAAA,CAAS,IAAA;gDAC5B,MAAMA,UAAS,QAAA,CAAS,SAAA;4CAC1B,CAAC;wCACH;oCACF;oCAEA;gCACF;gCAGA,MAAM,WAAW,SAAA,CAAU,KAAK,CAAA;gCAEhC,IAAA,CAAA,CAAI,KAAA,cAAc,QAAA,KAAd,OAAA,KAAA,IAAA,GAAwB,SAAA,KAAa,MAAM;oCAC7C,SAAS,QAAA,CAAU,SAAA,IAAA,CACjB,KAAA,CAAA,KAAA,cAAc,QAAA,KAAd,OAAA,KAAA,IAAA,GAAwB,SAAA,KAAxB,OAAA,KAAqC;gCACzC;gCAGA,WAAW,OAAA,CAAQ;oCACjB,MAAM;oCACN,cAAc;oCACd,YAAY,SAAS,EAAA;oCACrB,UAAU,SAAS,QAAA,CAAS,IAAA;oCAC5B,eAAA,CAAe,KAAA,cAAc,QAAA,CAAS,SAAA,KAAvB,OAAA,KAAoC;gCACrD,CAAC;gCAGD,IAAA,CAAA,CACE,KAAA,SAAS,QAAA,KAAT,OAAA,KAAA,IAAA,GAAmB,IAAA,KAAQ,QAAA,CAAA,CAC3B,KAAA,SAAS,QAAA,KAAT,OAAA,KAAA,IAAA,GAAmB,SAAA,KAAa,kRAChC,iBAAA,EAAe,SAAS,QAAA,CAAS,SAAS,GAC1C;oCACA,WAAW,OAAA,CAAQ;wCACjB,MAAM;wCACN,cAAc;wCACd,YAAA,CAAY,KAAA,SAAS,EAAA,KAAT,OAAA,+QAAe,aAAA,CAAW;wCACtC,UAAU,SAAS,QAAA,CAAS,IAAA;wCAC5B,MAAM,SAAS,QAAA,CAAS,SAAA;oCAC1B,CAAC;gCACH;4BACF;wBACF;oBACF;oBAEA,OAAM,UAAA,EAAY;wBAvvB5B,IAAAF,KAAA;wBAwvBY,WAAW,OAAA,CAAQ,eAAA;4BACjB,MAAM;4BACN;4BAAA,YAAA;4BAEA,OAAO;gCACL,cAAA,CAAcA,MAAA,MAAM,YAAA,KAAN,OAAAA,MAAsB;gCACpC,kBAAA,CAAkB,KAAA,MAAM,gBAAA,KAAN,OAAA,KAA0B;4BAC9C;wBAAA,GACI,oBAAoB,OAAO;4BAAE;wBAAiB,IAAI,CAAC,EACxD;oBACH;gBACF,CAAC;gBAEH,SAAS;oBAAE;oBAAW;gBAAY;gBAClC,aAAa;oBAAE,SAAS;gBAAgB;gBACxC;YACF;QACF;IAAA;AACF;AAEA,IAAM,iNAAyBI,IAAAA,CAC5B,MAAA,CAAO;IACN,uMAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAClC,2MAAmBA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACtC,+MAAuBA,IAAAA,CACpB,MAAA,CAAO;QACN,uMAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACpC,CAAC,EACA,OAAA,CAAQ;IACX,mNAA2BA,IAAAA,CACxB,MAAA,CAAO;QACN,kBAAkBA,4LAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QACrC,oNAA4BA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QAC/C,oNAA4BA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACjD,CAAC,EACA,OAAA,CAAQ;AACb,CAAC,EACA,OAAA,CAAQ;AAIX,IAAM,iNAAyBA,IAAAA,CAAE,MAAA,CAAO;IACtC,4LAAIA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACvB,iMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC5B,SAASA,4LAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC5B,oMAAYA,IAAAA,CACT,KAAA,CACCA,4LAAAA,CAAE,MAAA,CAAO;QACP,+LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QAC1B,4LAAIA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QACvB,8LAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QACzB,kMAAUA,IAAAA,CAAE,MAAA,CAAO;YACjB,8LAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YACzB,WAAWA,4LAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QAChC,CAAC;IACH,CAAC,GAEF,OAAA,CAAQ;IACX,iMAASA,IAAAA,CACN,MAAA,CAAO;QACN,8LAAMA,IAAAA,CAAE,IAAA,CAAK;YAAC,WAAW;SAAC,EAAE,OAAA,CAAQ;QACpC,iMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QAC5B,uMAAeA,IAAAA,CACZ,MAAA,CAAO;YACN,8LAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;YAC1B,mMAAWA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;QACjC,CAAC,EACA,OAAA,CAAQ;QACX,oMAAYA,IAAAA,CACT,KAAA,yLACCA,IAAAA,CAAE,MAAA,CAAO;YACP,+LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YAC1B,4LAAIA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YACvB,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,UAAU,EAAE,QAAA,CAAS;YACrC,kMAAUA,IAAAA,CAAE,MAAA,CAAO;gBACjB,8LAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;gBACzB,mMAAWA,IAAAA,CAAE,GAAA,CAAI,EAAE,OAAA,CAAQ;YAC7B,CAAC;QACH,CAAC,GAEF,OAAA,CAAQ;IACb,CAAC,EACA,OAAA,CAAQ;IAAA,mBAAA;IAEX,OAAOA,4LAAAA,CAAE,MAAA,CAAO;IAChB,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,IAAI,EAAE,OAAA,CAAQ;IAC9B,qMAAaA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS;IAC5C,oMAAYA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC/B,uMAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAClC,mBAAmBA,4LAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACvC,8MAAsBA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC1C,uMAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACnC,uMAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS;IAC9C,wMAAgBA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACnC,SAASA,4LAAAA,CACN,KAAA,yLACCA,IAAAA,CAAE,MAAA,CAAO;QACP,iMAASA,IAAAA,CAAE,MAAA,CAAO;YAChB,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,WAAW,EAAE,OAAA,CAAQ;YACrC,iMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YAC5B,2MAAmBA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YACtC,eAAeA,4LAAAA,CACZ,MAAA,CAAO;gBACN,mMAAWA,IAAAA,CAAE,MAAA,CAAO;gBACpB,8LAAMA,IAAAA,CAAE,MAAA,CAAO;YACjB,CAAC,EACA,OAAA,CAAQ;YACX,oMAAYA,IAAAA,CACT,KAAA,yLACCA,IAAAA,CAAE,MAAA,CAAO;gBACP,4LAAIA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;gBACvB,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,UAAU;gBAC1B,kMAAUA,IAAAA,CAAE,MAAA,CAAO;oBACjB,8LAAMA,IAAAA,CAAE,MAAA,CAAO;oBACf,mMAAWA,IAAAA,CAAE,MAAA,CAAO;gBACtB,CAAC;YACH,CAAC,GAEF,OAAA,CAAQ;QACb,CAAC;QACD,OAAOA,4LAAAA,CAAE,MAAA,CAAO;QAChB,kMAAUA,IAAAA,CACP,MAAA,CAAO;YACN,iMAASA,IAAAA,CACN,KAAA,yLACCA,IAAAA,CAAE,MAAA,CAAO;gBACP,+LAAOA,IAAAA,CAAE,MAAA,CAAO;gBAChB,iMAASA,IAAAA,CAAE,MAAA,CAAO;gBAClB,sMAAcA,IAAAA,CAAE,KAAA,yLACdA,IAAAA,CAAE,MAAA,CAAO;oBACP,+LAAOA,IAAAA,CAAE,MAAA,CAAO;oBAChB,iMAASA,IAAAA,CAAE,MAAA,CAAO;gBACpB,CAAC;YAEL,CAAC,GAEF,QAAA,CAAS;QACd,CAAC,EACA,OAAA,CAAQ;QACX,uMAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACpC,CAAC,GAEF,OAAA,CAAQ;IACX,OAAO;AACT,CAAC;AAID,IAAM,8MAAsBA,IAAAA,CAAE,KAAA,CAAM;4LAClCA,IAAAA,CAAE,MAAA,CAAO;QAAA,mBAAA;QAEP,4LAAIA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QACvB,iMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QAC5B,+LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QAE1B,iMAASA,IAAAA,CACN,KAAA,yLACCA,IAAAA,CAAE,MAAA,CAAO;YACP,gMAAQA,IAAAA,CAAE,MAAA,CAAO;gBACf,4LAAIA,IAAAA,CAAE,MAAA,CAAO;gBACb,iMAASA,IAAAA,CAAE,MAAA,CAAO;gBAClB,8LAAMA,IAAAA,CAAE,MAAA,CAAO;gBACf,qMAAaA,IAAAA,CAAE,MAAA,CAAO;gBACtB,8LAAMA,IAAAA,CAAE,GAAA,CAAI,EAAE,QAAA,CAAS;gBACvB,8LAAMA,IAAAA,CAAE,GAAA,CAAI,EAAE,QAAA,CAAS;gBACvB,wMAAgBA,IAAAA,CAAE,GAAA,CAAI,EAAE,QAAA,CAAS;gBACjC,oMAAYA,IAAAA,CAAE,MAAA,CAAO;gBACrB,oMAAYA,IAAAA,CAAE,MAAA,CAAO;gBACrB,8LAAMA,IAAAA,CAAE,MAAA,CAAO;oBACb,4LAAIA,IAAAA,CAAE,MAAA,CAAO;oBACb,6LAAMA,KAAAA,CAAE,MAAA,CAAO;oBACf,+LAAOA,IAAAA,CAAE,MAAA,CAAO;oBAChB,8LAAMA,IAAAA,CAAE,MAAA,CAAO;oBACf,2MAAmBA,IAAAA,CAAE,MAAA,CAAO;gBAC9B,CAAC;gBACD,+LAAOA,IAAAA,CAAE,KAAA,yLACPA,IAAAA,CAAE,MAAA,CAAO;oBACP,4LAAIA,IAAAA,CAAE,MAAA,CAAO;oBACb,8LAAMA,IAAAA,CAAE,MAAA,CAAO;wBACb,8LAAMA,IAAAA,CAAE,MAAA,CAAO;wBACf,sMAAcA,IAAAA,CAAE,MAAA,CAAO;wBACvB,8LAAMA,IAAAA,CAAE,MAAA,CAAO;wBACf,yMAAiBA,IAAAA,CAAE,MAAA,CAAO;oBAC5B,CAAC;oBACD,oMAAYA,IAAAA,CAAE,MAAA,CAAO;oBACrB,YAAYA,4LAAAA,CAAE,MAAA,CAAO;gBACvB,CAAC;gBAEH,8LAAMA,IAAAA,CAAE,MAAA,CAAO;YACjB,CAAC;YACD,kMAAUA,IAAAA,CAAE,KAAA,yLAAMA,IAAAA,CAAE,MAAA,CAAO,CAAC;YAC5B,kMAAUA,IAAAA,CAAE,KAAA,yLACVA,IAAAA,CAAE,MAAA,CAAO;gBACP,YAAYA,4LAAAA,CAAE,MAAA,CAAO;gBACrB,0MAAkBA,IAAAA,CAAE,MAAA,CAAO;gBAC3B,iMAASA,IAAAA,CAAE,MAAA,CAAO;gBAClB,8LAAMA,IAAAA,CAAE,MAAA,CAAO;gBACf,8LAAMA,IAAAA,CAAE,MAAA,CAAO;gBACf,gMAAQA,IAAAA,CAAE,MAAA,CAAO;gBACjB,oMAAaA,KAAAA,CAAE,MAAA,CAAO;gBACtB,+LAAOA,IAAAA,CAAE,MAAA,CAAO;YAClB,CAAC;YAEH,mMAAWA,IAAAA,CAAE,KAAA,yLAAMA,IAAAA,CAAE,MAAA,CAAO,CAAC;QAC/B,CAAC,GAEF,QAAA,CAAS;QAAA,uCAAA;QAGZ,SAASA,4LAAAA,CACN,MAAA,CAAO;YACN,8LAAMA,IAAAA,CAAE,IAAA,CAAK;gBAAC,WAAW;aAAC,EAAE,OAAA,CAAQ;YACpC,iMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YAC5B,uMAAeA,IAAAA,CACZ,MAAA,CAAO;gBACN,8LAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;gBAC1B,mMAAWA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;YACjC,CAAC,EACA,OAAA,CAAQ;YACX,oMAAYA,IAAAA,CACT,KAAA,yLACCA,IAAAA,CAAE,MAAA,CAAO;gBACP,+LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;gBAC1B,4LAAIA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;gBACvB,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,UAAU,EAAE,QAAA,CAAS;gBACrC,kMAAUA,IAAAA,CAAE,MAAA,CAAO;oBACjB,8LAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;oBACzB,kMAAWA,KAAAA,CAAE,GAAA,CAAI,EAAE,OAAA,CAAQ;gBAC7B,CAAC;YACH,CAAC,GAEF,OAAA,CAAQ;QACb,CAAC,EACA,OAAA,CAAQ;QAAA,sCAAA;QAGX,iMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QAC5B,YAAYA,4LAAAA,CACT,KAAA,yLACCA,IAAAA,CAAE,MAAA,CAAO;YACP,+LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YAC1B,4LAAIA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YACvB,8LAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YACzB,kMAAUA,IAAAA,CAAE,MAAA,CAAO;gBACjB,8LAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;gBACzB,kMAAWA,KAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YAChC,CAAC;QACH,CAAC,GAEF,OAAA,CAAQ;QAAA,6BAAA;QAGX,iMAASA,IAAAA,CACN,KAAA,yLACCA,IAAAA,CAAE,MAAA,CAAO;YACP,+LAAOA,IAAAA,CACJ,MAAA,CAAO;gBACN,8LAAMA,IAAAA,CAAE,IAAA,CAAK;oBAAC,WAAW;iBAAC,EAAE,OAAA,CAAQ;gBACpC,iMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;gBAC5B,2MAAmBA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;gBACtC,uMAAeA,IAAAA,CACZ,MAAA,CAAO;oBACN,8LAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;oBAC1B,mMAAWA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;gBACjC,CAAC,EACA,OAAA,CAAQ;gBACX,YAAYA,4LAAAA,CACT,KAAA,yLACCA,IAAAA,CAAE,MAAA,CAAO;oBACP,+LAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;oBAC1B,4LAAIA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;oBACvB,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,UAAU,EAAE,QAAA,CAAS;oBACrC,kMAAUA,IAAAA,CAAE,MAAA,CAAO;wBACjB,8LAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;wBACzB,mMAAWA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;oBAChC,CAAC;gBACH,CAAC,GAEF,OAAA,CAAQ;YACb,CAAC,EACA,OAAA,CAAQ;YACX,kMAAUA,IAAAA,CACP,MAAA,CAAO;gBACN,SAASA,4LAAAA,CACN,KAAA,yLACCA,IAAAA,CAAE,MAAA,CAAO;oBACP,+LAAOA,IAAAA,CAAE,MAAA,CAAO;oBAChB,iMAASA,IAAAA,CAAE,MAAA,CAAO;oBAClB,sMAAcA,IAAAA,CAAE,KAAA,yLACdA,IAAAA,CAAE,MAAA,CAAO;wBACP,+LAAOA,IAAAA,CAAE,MAAA,CAAO;wBAChB,iMAASA,IAAAA,CAAE,MAAA,CAAO;oBACpB,CAAC;gBAEL,CAAC,GAEF,QAAA,CAAS;YACd,CAAC,EACA,OAAA,CAAQ;YACX,uMAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS;YAC9C,+LAAOA,IAAAA,CAAE,MAAA,CAAO;QAClB,CAAC,GAEF,OAAA,CAAQ;QACX,OAAO;QAAA,gBAAA;QAEP,uMAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS;QAC9C,qMAAaA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS;QAC5C,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS;QACtC,gMAAQA,IAAAA,CAAE,GAAA,CAAI,EAAE,OAAA,CAAQ;IAC1B,CAAC;IACD;CACD;;;;AQ3hCC,IAAM,oNAA4BK,IAAAA,CAAE,MAAA,CAAO;IACzC,iMAASA,IAAAA,CAAE,KAAA,CAAMA,4LAAAA,CAAE,MAAA,CAAO,CAAC,EAAE,OAAA,CAAQ;IACrC,kMAAUA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC7B,gMAAQA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC3B,aAAaA,4LAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC,EAAE,GAAA,CAAI,CAAC,EAAE,OAAA,CAAQ,EAAE,OAAA,CAAQ,CAAC;IACzD,gNAAwBA,IAAAA,CACrB,KAAA,yLAAMA,IAAAA,CAAE,IAAA,CAAK;QAAC;QAAQ,SAAS;KAAC,CAAC,EACjC,OAAA,CAAQ,EACR,OAAA,CAAQ;QAAC,SAAS;KAAC;AACxB,CAAC;AAkBD,IAAM,cAAc;IAClB,WAAW;IACX,QAAQ;IACR,UAAU;IACV,aAAa;IACb,YAAY;IACZ,SAAS;IACT,WAAW;IACX,SAAS;IACT,SAAS;IACT,UAAU;IACV,OAAO;IACP,QAAQ;IACR,OAAO;IACP,SAAS;IACT,UAAU;IACV,SAAS;IACT,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,OAAO;IACP,WAAW;IACX,WAAW;IACX,YAAY;IACZ,SAAS;IACT,UAAU;IACV,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,YAAY;IACZ,YAAY;IACZ,OAAO;IACP,SAAS;IACT,OAAO;IACP,QAAQ;IACR,WAAW;IACX,SAAS;IACT,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,SAAS;IACT,SAAS;IACT,QAAQ;IACR,WAAW;IACX,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,OAAO;IACP,MAAM;IACN,SAAS;IACT,WAAW;IACX,MAAM;IACN,YAAY;IACZ,OAAO;AACT;AAEO,IAAM,yBAAN,MAA6D;IAOlE,YACW,OAAA,EACQ,MAAA,CACjB;QAFS,IAAA,CAAA,OAAA,GAAA;QACQ,IAAA,CAAA,MAAA,GAAA;QARnB,IAAA,CAAS,oBAAA,GAAuB;IAS7B;IAPH,IAAI,WAAmB;QACrB,OAAO,IAAA,CAAK,MAAA,CAAO,QAAA;IACrB;IAOQ,QAAQ,EACd,KAAA,EACA,SAAA,EACA,eAAA,EACF,EAAiC;QA5HrC,IAAA,IAAA,IAAA,IAAA,IAAA;QA6HM,MAAM,WAA8C,CAAC,CAAA;QAGrD,MAAM,kBAAc,6RAAA,EAAqB;YACvC,UAAU;YACV;YACA,QAAQ;QACV,CAAC;QAGD,MAAM,WAAW,IAAI,SAAS;QAC9B,MAAM,OACJ,iBAAiB,aACb,IAAI,KAAK;YAAC,KAAK;SAAC,IAChB,IAAI,KAAK;sRAAC,4BAAA,EAA0B,KAAK,CAAC;SAAC;QAEjD,SAAS,MAAA,CAAO,SAAS,IAAA,CAAK,OAAO;QACrC,SAAS,MAAA,CAAO,QAAQ,IAAI,KAAK;YAAC,IAAI;SAAA,EAAG,SAAS;YAAE,MAAM;QAAU,CAAC,CAAC;QAGtE,IAAI,aAAa;YACf,MAAM,4BAA2D;gBAC/D,SAAA,CAAS,KAAA,YAAY,OAAA,KAAZ,OAAA,KAAuB,KAAA;gBAChC,UAAA,CAAU,KAAA,YAAY,QAAA,KAAZ,OAAA,KAAwB,KAAA;gBAClC,QAAA,CAAQ,KAAA,YAAY,MAAA,KAAZ,OAAA,KAAsB,KAAA;gBAC9B,aAAA,CAAa,KAAA,YAAY,WAAA,KAAZ,OAAA,KAA2B,KAAA;gBACxC,yBAAA,CACE,KAAA,YAAY,sBAAA,KAAZ,OAAA,KAAsC,KAAA;YAC1C;YAEA,IAAA,MAAW,OAAO,0BAA2B;gBAC3C,MAAM,QACJ,yBAAA,CACE,GACF,CAAA;gBACF,IAAI,UAAU,KAAA,GAAW;oBACvB,SAAS,MAAA,CAAO,KAAK,OAAO,KAAK,CAAC;gBACpC;YACF;QACF;QAEA,OAAO;YACL;YACA;QACF;IACF;IAEM,WACJ,OAAA,EACkE;QAAA,OAAA,QAAA,IAAA,EAAA,MAAA;YA9KxE,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;YA+KM,MAAM,cAAA,CAAc,KAAA,CAAA,KAAA,CAAA,KAAA,IAAA,CAAK,MAAA,CAAO,SAAA,KAAZ,OAAA,KAAA,IAAA,GAAuB,WAAA,KAAvB,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,GAAA,KAAA,OAAA,KAA0C,aAAA,GAAA,IAAI,KAAK;YACvE,MAAM,EAAE,QAAA,EAAU,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,OAAO;YAEnD,MAAM,EACJ,OAAO,QAAA,EACP,eAAA,EACA,UAAU,WAAA,EACZ,GAAI,gRAAM,oBAAA,EAAkB;gBAC1B,KAAK,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI;oBACnB,MAAM;oBACN,SAAS,IAAA,CAAK,OAAA;gBAChB,CAAC;gBACD,mRAASC,iBAAAA,EAAe,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,QAAQ,OAAO;gBAC9D;gBACA,uBAAuB;gBACvB,qSAA2BC,4BAAAA,EACzB;gBAEF,aAAa,QAAQ,WAAA;gBACrB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;YACrB,CAAC;YAED,MAAM,WACJ,SAAS,QAAA,IAAY,QAAQ,SAAS,QAAA,IAAY,cAC9C,WAAA,CAAY,SAAS,QAAoC,CAAA,GACzD,KAAA;YAEN,OAAO;gBACL,MAAM,SAAS,IAAA;gBACf,UAAA,CACE,KAAA,CAAA,KAAA,SAAS,KAAA,KAAT,OAAA,KAAA,IAAA,GAAgB,GAAA,CAAI,CAAA,OAAA,CAAS;wBAC3B,MAAM,KAAK,IAAA;wBACX,aAAa,KAAK,KAAA;wBAClB,WAAW,KAAK,GAAA;oBAClB,CAAA,EAAA,KAJA,OAAA,KAIO,CAAC,CAAA;gBACV;gBACA,mBAAA,CAAmB,KAAA,SAAS,QAAA,KAAT,OAAA,KAAqB,KAAA;gBACxC;gBACA,UAAU;oBACR,WAAW;oBACX,SAAS,IAAA,CAAK,OAAA;oBACd,SAAS;oBACT,MAAM;gBACR;YACF;QACF;IAAA;AACF;AAEA,IAAM,0NAAkCF,IAAAA,CAAE,MAAA,CAAO;IAC/C,8LAAMA,IAAAA,CAAE,MAAA,CAAO;IACf,kMAAUA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC7B,kMAAUA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC7B,+LAAOA,IAAAA,CACJ,KAAA,yLACCA,IAAAA,CAAE,MAAA,CAAO;QACP,8LAAMA,IAAAA,CAAE,MAAA,CAAO;QACf,+LAAOA,IAAAA,CAAE,MAAA,CAAO;QAChB,6LAAKA,IAAAA,CAAE,MAAA,CAAO;IAChB,CAAC,GAEF,OAAA,CAAQ;AACb,CAAC;;;;;AEpOM,SAAS,+BAA+B,EAC7C,MAAA,EACA,iBAAA,EACF,EAME;IACA,MAAM,WAAgC,CAAC,CAAA;IACvC,MAAM,WAA8C,CAAC,CAAA;IAErD,KAAA,MAAW,EAAE,IAAA,EAAM,OAAA,CAAQ,CAAA,IAAK,OAAQ;QACtC,OAAQ,MAAM;YACZ,KAAK;gBAAU;oBACb,OAAQ,mBAAmB;wBACzB,KAAK;4BAAU;gCACb,SAAS,IAAA,CAAK;oCAAE,MAAM;oCAAU;gCAAQ,CAAC;gCACzC;4BACF;wBACA,KAAK;4BAAa;gCAChB,SAAS,IAAA,CAAK;oCAAE,MAAM;oCAAa;gCAAQ,CAAC;gCAC5C;4BACF;wBACA,KAAK;4BAAU;gCACb,SAAS,IAAA,CAAK;oCACZ,MAAM;oCACN,SAAS;gCACX,CAAC;gCACD;4BACF;wBACA;4BAAS;gCACP,MAAM,mBAA0B;gCAChC,MAAM,IAAI,MACR,CAAA,iCAAA,EAAoC,gBAAgB,EAAA;4BAExD;oBACF;oBACA;gBACF;YAEA,KAAK;gBAAQ;oBACX,SAAS,IAAA,CAAK;wBACZ,MAAM;wBACN,SAAS,QAAQ,GAAA,CAAI,CAAC,MAAM,UAAU;4BArDlD,IAAA,IAAA,IAAA,IAAA;4BAsDc,OAAQ,KAAK,IAAA,EAAM;gCACjB,KAAK;oCAAQ;wCACX,OAAO;4CAAE,MAAM;4CAAc,MAAM,KAAK,IAAA;wCAAK;oCAC/C;gCACA,KAAK;oCAAS;wCACZ,OAAO;4CACL,MAAM;4CACN,WACE,KAAK,KAAA,YAAiB,MAClB,KAAK,KAAA,CAAM,QAAA,CAAS,IACpB,CAAA,KAAA,EAAA,CACE,KAAA,KAAK,QAAA,KAAL,OAAA,KAAiB,YACnB,CAAA,QAAA,4QAAWW,4BAAAA,EAA0B,KAAK,KAAK,CAAC,EAAA;4CAAA,0CAAA;4CAGtD,QAAA,CAAQ,KAAA,CAAA,KAAA,KAAK,gBAAA,KAAL,OAAA,KAAA,IAAA,GAAuB,MAAA,KAAvB,OAAA,KAAA,IAAA,GAA+B,WAAA;wCACzC;oCACF;gCACA,KAAK;oCAAQ;wCACX,IAAI,KAAK,IAAA,YAAgB,KAAK;4CAE5B,MAAM,oOAAID,gCAAAA,CAA8B;gDACtC,eAAe;4CACjB,CAAC;wCACH;wCAEA,OAAQ,KAAK,QAAA,EAAU;4CACrB,KAAK;gDAAmB;oDACtB,OAAO;wDACL,MAAM;wDACN,UAAA,CAAU,KAAA,KAAK,QAAA,KAAL,OAAA,KAAiB,CAAA,KAAA,EAAQ,KAAK,CAAA,IAAA,CAAA;wDACxC,WAAW,CAAA,4BAAA,EAA+B,KAAK,IAAI,EAAA;oDACrD;gDACF;4CACA;gDAAS;oDACP,MAAM,IAAIA,gQAAAA,CAA8B;wDACtC,eACE;oDACJ,CAAC;gDACH;wCACF;oCACF;4BACF;wBACF,CAAC;oBACH,CAAC;oBAED;gBACF;YAEA,KAAK;gBAAa;oBAChB,KAAA,MAAW,QAAQ,QAAS;wBAC1B,OAAQ,KAAK,IAAA,EAAM;4BACjB,KAAK;gCAAQ;oCACX,SAAS,IAAA,CAAK;wCACZ,MAAM;wCACN,SAAS;4CAAC;gDAAE,MAAM;gDAAe,MAAM,KAAK,IAAA;4CAAK,CAAC;yCAAA;oCACpD,CAAC;oCACD;gCACF;4BACA,KAAK;gCAAa;oCAChB,SAAS,IAAA,CAAK;wCACZ,MAAM;wCACN,SAAS,KAAK,UAAA;wCACd,MAAM,KAAK,QAAA;wCACX,WAAW,KAAK,SAAA,CAAU,KAAK,IAAI;oCACrC,CAAC;oCACD;gCACF;wBACF;oBACF;oBAEA;gBACF;YAEA,KAAK;gBAAQ;oBACX,KAAA,MAAW,QAAQ,QAAS;wBAC1B,SAAS,IAAA,CAAK;4BACZ,MAAM;4BACN,SAAS,KAAK,UAAA;4BACd,QAAQ,KAAK,SAAA,CAAU,KAAK,MAAM;wBACpC,CAAC;oBACH;oBAEA;gBACF;YAEA;gBAAS;oBACP,MAAM,mBAA0B;oBAChC,MAAM,IAAI,MAAM,CAAA,kBAAA,EAAqB,gBAAgB,EAAE;gBACzD;QACF;IACF;IAEA,OAAO;QAAE;QAAU;IAAS;AAC9B;;AClJK,SAAS,4BAA4B,EAC1C,YAAA,EACA,YAAA,EACF,EAGgC;IAC9B,OAAQ,cAAc;QACpB,KAAK,KAAA;QACL,KAAK;YACH,OAAO,eAAe,eAAe;QACvC,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,eAAe,eAAe;IACzC;AACF;;ACbS,SAAS,sBAAsB,EACpC,IAAA,EACA,MAAA,EACF,EAcE;IAxBJ,IAAA;IA0BI,MAAM,QAAA,CAAA,CAAQ,KAAA,KAAK,KAAA,KAAL,OAAA,KAAA,IAAA,GAAY,MAAA,IAAS,KAAK,KAAA,GAAQ,KAAA;IAEhD,MAAM,eAA6C,CAAC,CAAA;IAEpD,IAAI,SAAS,MAAM;QACjB,OAAO;YAAE,OAAO,KAAA;YAAW,aAAa,KAAA;YAAW;QAAa;IAClE;IAEA,MAAM,aAAa,KAAK,UAAA;IAExB,MAAM,YAAsC,CAAC,CAAA;IAE7C,KAAA,MAAW,QAAQ,MAAO;QACxB,OAAQ,KAAK,IAAA,EAAM;YACjB,KAAK;gBACH,UAAU,IAAA,CAAK;oBACb,MAAM;oBACN,MAAM,KAAK,IAAA;oBACX,aAAa,KAAK,WAAA;oBAClB,YAAY,KAAK,UAAA;oBACjB,QAAQ,SAAS,OAAO,KAAA;gBAC1B,CAAC;gBACD;YACF,KAAK;gBACH,OAAQ,KAAK,EAAA,EAAI;oBACf,KAAK;wBACH,UAAU,IAAA,CAAK;4BACb,MAAM;4BACN,qBAAqB,KAAK,IAAA,CAAK,iBAAA;4BAI/B,eAAe,KAAK,IAAA,CAAK,YAAA;wBAK3B,CAAC;wBACD;oBACF;wBACE,aAAa,IAAA,CAAK;4BAAE,MAAM;4BAAoB;wBAAK,CAAC;wBACpD;gBACJ;gBACA;YACF;gBACE,aAAa,IAAA,CAAK;oBAAE,MAAM;oBAAoB;gBAAK,CAAC;gBACpD;QACJ;IACF;IAEA,IAAI,cAAc,MAAM;QACtB,OAAO;YAAE,OAAO;YAAW,aAAa,KAAA;YAAW;QAAa;IAClE;IAEA,MAAM,OAAO,WAAW,IAAA;IAExB,OAAQ,MAAM;QACZ,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;gBAAE,OAAO;gBAAW,aAAa;gBAAM;YAAa;QAC7D,KAAK;YAAQ;gBACX,IAAI,WAAW,QAAA,KAAa,sBAAsB;oBAChD,OAAO;wBACL,OAAO;wBACP,aAAa;4BACX,MAAM;wBACR;wBACA;oBACF;gBACF;gBACA,OAAO;oBACL,OAAO;oBACP,aAAa;wBACX,MAAM;wBACN,MAAM,WAAW,QAAA;oBACnB;oBACA;gBACF;YACF;QACA;YAAS;gBACP,MAAM,mBAA0B;gBAChC,MAAM,oOAAIE,gCAAAA,CAA8B;oBACtC,eAAe,CAAA,8BAAA,EAAiC,gBAAgB,EAAA;gBAClE,CAAC;YACH;IACF;AACF;;AH1FO,IAAM,6BAAN,MAA4D;IASjE,YAAY,OAAA,EAA+B,MAAA,CAAoB;QAR/D,IAAA,CAAS,oBAAA,GAAuB;QAChC,IAAA,CAAS,2BAAA,GAA8B;QACvC,IAAA,CAAS,yBAAA,GAA4B;QAOnC,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,MAAA,GAAS;IAChB;IAEA,IAAI,WAAmB;QACrB,OAAO,IAAA,CAAK,MAAA,CAAO,QAAA;IACrB;IAEQ,QAAQ,EACd,IAAA,EACA,SAAA,EACA,WAAA,EACA,aAAA,EACA,IAAA,EACA,IAAA,EACA,eAAA,EACA,gBAAA,EACA,IAAA,EACA,MAAA,EACA,gBAAA,EACA,cAAA,EACF,EAAiD;QAtDrD,IAAA,IAAA,IAAA;QAuDM,MAAM,WAAyC,CAAC,CAAA;QAChD,MAAM,cAAc,wBAAwB,IAAA,CAAK,OAAO;QACxD,MAAM,OAAO,KAAK,IAAA;QAElB,IAAI,QAAQ,MAAM;YAChB,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,IAAI,QAAQ,MAAM;YAChB,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,IAAI,mBAAmB,MAAM;YAC3B,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,IAAI,oBAAoB,MAAM;YAC5B,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,IAAI,iBAAiB,MAAM;YACzB,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,MAAM,EAAE,QAAA,EAAU,UAAU,eAAA,CAAgB,CAAA,GAC1C,+BAA+B;YAC7B;YACA,mBAAmB,YAAY,iBAAA;QACjC,CAAC;QAEH,SAAS,IAAA,CAAK,GAAG,eAAe;QAEhC,MAAM,wRAAcC,uBAAAA,EAAqB;YACvC,UAAU;YACV,iBAAiB;YACjB,QAAQ;QACV,CAAC;QAED,MAAM,WAAA,CAAW,KAAA,eAAA,OAAA,KAAA,IAAA,YAAa,aAAA,KAAb,OAAA,KAA8B;QAE/C,MAAM,WAAW,eAAA,eAAA,cAAA,eAAA;YACf,OAAO,IAAA,CAAK,OAAA;YACZ,OAAO;YACP;YACA,OAAO;YACP,mBAAmB;QAAA,GAAA,CAEf,kBAAA,OAAA,KAAA,IAAA,eAAgB,IAAA,MAAS,UAAU;YACrC,MAAM;gBACJ,QACE,eAAe,MAAA,IAAU,OACrB;oBACE,MAAM;oBACN,QAAQ;oBACR,MAAA,CAAM,KAAA,eAAe,IAAA,KAAf,OAAA,KAAuB;oBAC7B,aAAa,eAAe,WAAA;oBAC5B,QAAQ,eAAe,MAAA;gBACzB,IACA;oBAAE,MAAM;gBAAc;YAC9B;QACF,IApBe;YAAA,oBAAA;YAuBf,UAAU,eAAA,OAAA,KAAA,IAAA,YAAa,QAAA;YACvB,qBAAqB,eAAA,OAAA,KAAA,IAAA,YAAa,iBAAA;YAClC,sBAAsB,eAAA,OAAA,KAAA,IAAA,YAAa,kBAAA;YACnC,OAAO,eAAA,OAAA,KAAA,IAAA,YAAa,KAAA;YACpB,MAAM,eAAA,OAAA,KAAA,IAAA,YAAa,IAAA;YACnB,cAAc,eAAA,OAAA,KAAA,IAAA,YAAa,YAAA;QAAA,IAGvB,YAAY,gBAAA,IAAA,CACd,eAAA,OAAA,KAAA,IAAA,YAAa,eAAA,KAAmB,QAAQ;YACtC,WAAW;gBAAE,QAAQ,eAAA,OAAA,KAAA,IAAA,YAAa,eAAA;YAAgB;QACpD,IACE,YAAY,sBAAA,IAA0B;YACxC,YAAY;QACd;QAGF,IAAI,YAAY,gBAAA,EAAkB;YAGhC,IAAI,SAAS,WAAA,IAAe,MAAM;gBAChC,SAAS,WAAA,GAAc,KAAA;gBACvB,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;oBACT,SAAS;gBACX,CAAC;YACH;YAEA,IAAI,SAAS,KAAA,IAAS,MAAM;gBAC1B,SAAS,KAAA,GAAQ,KAAA;gBACjB,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;oBACT,SAAS;gBACX,CAAC;YACH;QACF;QAEA,OAAQ,MAAM;YACZ,KAAK;gBAAW;oBACd,MAAM,EAAE,KAAA,EAAO,WAAA,EAAa,YAAA,CAAa,CAAA,GAAI,sBAAsB;wBACjE;wBACA,QAAQ;oBACV,CAAC;oBAED,OAAO;wBACL,MAAM,cAAA,eAAA,CAAA,GACD,WADC;4BAEJ;4BACA;wBACF;wBACA,UAAU,CAAC;+BAAG,UAAU;+BAAG,YAAY;yBAAA;oBACzC;gBACF;YAEA,KAAK;gBAAe;oBAClB,OAAO;wBACL,MAAM,cAAA,eAAA,CAAA,GACD,WADC;4BAEJ,MAAM;gCACJ,QACE,KAAK,MAAA,IAAU,OACX;oCACE,MAAM;oCACN,QAAQ;oCACR,MAAA,CAAM,KAAA,KAAK,IAAA,KAAL,OAAA,KAAa;oCACnB,aAAa,KAAK,WAAA;oCAClB,QAAQ,KAAK,MAAA;gCACf,IACA;oCAAE,MAAM;gCAAc;4BAC9B;wBACF;wBACA;oBACF;gBACF;YAEA,KAAK;gBAAe;oBAClB,OAAO;wBACL,MAAM,cAAA,eAAA,CAAA,GACD,WADC;4BAEJ,aAAa;gCAAE,MAAM;gCAAY,MAAM,KAAK,IAAA,CAAK,IAAA;4BAAK;4BACtD,OAAO;gCACL;oCACE,MAAM;oCACN,MAAM,KAAK,IAAA,CAAK,IAAA;oCAChB,aAAa,KAAK,IAAA,CAAK,WAAA;oCACvB,YAAY,KAAK,IAAA,CAAK,UAAA;oCACtB,QAAQ;gCACV;6BACF;wBACF;wBACA;oBACF;gBACF;YAEA;gBAAS;oBACP,MAAM,mBAA0B;oBAChC,MAAM,IAAI,MAAM,CAAA,kBAAA,EAAqB,gBAAgB,EAAE;gBACzD;QACF;IACF;IAEM,WACJ,OAAA,EAC6D;QAAA,OAAA,QAAA,IAAA,EAAA,MAAA;YA9OnE,IAAA,IAAA,IAAA,IAAA,IAAA;YA+OM,MAAM,EAAE,MAAM,IAAA,EAAM,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,OAAO;YAErD,MAAM,EACJ,eAAA,EACA,OAAO,QAAA,EACP,UAAU,WAAA,EACZ,GAAI,gRAAMC,gBAAAA,EAAc;gBACtB,KAAK,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI;oBACnB,MAAM;oBACN,SAAS,IAAA,CAAK,OAAA;gBAChB,CAAC;gBACD,SAASC,2RAAAA,EAAe,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,QAAQ,OAAO;gBAC9D;gBACA,uBAAuB;gBACvB,2BAA2BC,sSAAAA,0LACzBC,IAAAA,CAAE,MAAA,CAAO;oBACP,4LAAIA,IAAAA,CAAE,MAAA,CAAO;oBACb,oMAAYA,IAAAA,CAAE,MAAA,CAAO;oBACrB,+LAAOA,IAAAA,CAAE,MAAA,CAAO;oBAChB,gMAAQA,IAAAA,CAAE,KAAA,yLACRA,IAAAA,CAAE,kBAAA,CAAmB,QAAQ;gNAC3BA,IAAAA,CAAE,MAAA,CAAO;4BACP,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,SAAS;4BACzB,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,WAAW;4BAC3B,iMAASA,IAAAA,CAAE,KAAA,yLACTA,IAAAA,CAAE,MAAA,CAAO;gCACP,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,aAAa;gCAC7B,8LAAMA,IAAAA,CAAE,MAAA,CAAO;gCACf,aAAaA,4LAAAA,CAAE,KAAA,yLACbA,IAAAA,CAAE,MAAA,CAAO;oCACP,MAAMA,4LAAAA,CAAE,OAAA,CAAQ,cAAc;oCAC9B,qMAAaA,IAAAA,CAAE,MAAA,CAAO;oCACtB,mMAAWA,IAAAA,CAAE,MAAA,CAAO;oCACpB,6LAAKA,IAAAA,CAAE,MAAA,CAAO;oCACd,+LAAOA,IAAAA,CAAE,MAAA,CAAO;gCAClB,CAAC;4BAEL,CAAC;wBAEL,CAAC;gNACDA,IAAAA,CAAE,MAAA,CAAO;4BACP,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,eAAe;4BAC/B,iMAASA,IAAAA,CAAE,MAAA,CAAO;4BAClB,MAAMA,4LAAAA,CAAE,MAAA,CAAO;4BACf,mMAAWA,IAAAA,CAAE,MAAA,CAAO;wBACtB,CAAC;gNACDA,IAAAA,CAAE,MAAA,CAAO;4BACP,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,iBAAiB;wBACnC,CAAC;gNACDA,IAAAA,CAAE,MAAA,CAAO;4BACP,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,eAAe;wBACjC,CAAC;gNACDA,IAAAA,CAAE,MAAA,CAAO;4BACP,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,WAAW;wBAC7B,CAAC;qBACF;oBAEH,oBAAoBA,4LAAAA,CAAE,MAAA,CAAO;wBAAE,gMAAQA,IAAAA,CAAE,MAAA,CAAO;oBAAE,CAAC,EAAE,QAAA,CAAS;oBAC9D,OAAO;gBACT,CAAC;gBAEH,aAAa,QAAQ,WAAA;gBACrB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;YACrB,CAAC;YAED,MAAM,qBAAqB,SAAS,MAAA,CACjC,MAAA,CAAO,CAAA,SAAU,OAAO,IAAA,KAAS,SAAS,EAC1C,OAAA,CAAQ,CAAA,SAAU,OAAO,OAAO,EAChC,MAAA,CAAO,CAAA,UAAW,QAAQ,IAAA,KAAS,aAAa;YAEnD,MAAM,YAAY,SAAS,MAAA,CACxB,MAAA,CAAO,CAAA,SAAU,OAAO,IAAA,KAAS,eAAe,EAChD,GAAA,CAAI,CAAA,SAAA,CAAW;oBACd,cAAc;oBACd,YAAY,OAAO,OAAA;oBACnB,UAAU,OAAO,IAAA;oBACjB,MAAM,OAAO,SAAA;gBACf,CAAA,CAAE;YAEJ,OAAO;gBACL,MAAM,mBAAmB,GAAA,CAAI,CAAA,UAAW,QAAQ,IAAI,EAAE,IAAA,CAAK,IAAI;gBAC/D,SAAS,mBAAmB,OAAA,CAAQ,CAAA,UAClC,QAAQ,WAAA,CAAY,GAAA,CAAI,CAAA,eAAW;wBAjU7C,IAAAC,KAAAC,KAAAC;wBAiUiD,OAAA;4BACrC,YAAY;4BACZ,IAAA,CAAIA,MAAAA,CAAAD,MAAAA,CAAAD,MAAA,IAAA,CAAK,MAAA,EAAO,UAAA,KAAZ,OAAA,KAAA,IAAAC,IAAA,IAAA,CAAAD,IAAAA,KAAA,OAAAE,gRAA8BC,aAAAA,CAAW;4BAC7C,KAAK,WAAW,GAAA;4BAChB,OAAO,WAAW,KAAA;wBACpB;oBAAA,CAAE;gBAEJ,cAAc,4BAA4B;oBACxC,cAAA,CAAc,KAAA,SAAS,kBAAA,KAAT,OAAA,KAAA,IAAA,GAA6B,MAAA;oBAC3C,cAAc,UAAU,MAAA,GAAS;gBACnC,CAAC;gBACD,WAAW,UAAU,MAAA,GAAS,IAAI,YAAY,KAAA;gBAC9C,OAAO;oBACL,cAAc,SAAS,KAAA,CAAM,YAAA;oBAC7B,kBAAkB,SAAS,KAAA,CAAM,aAAA;gBACnC;gBACA,SAAS;oBACP,WAAW,KAAA;oBACX,aAAa,CAAC;gBAChB;gBACA,aAAa;oBACX,SAAS;oBACT,MAAM;gBACR;gBACA,SAAS;oBACP,MAAM,KAAK,SAAA,CAAU,IAAI;gBAC3B;gBACA,UAAU;oBACR,IAAI,SAAS,EAAA;oBACb,WAAW,IAAI,KAAK,SAAS,UAAA,GAAa,GAAI;oBAC9C,SAAS,SAAS,KAAA;gBACpB;gBACA,kBAAkB;oBAChB,MAAM;wBACJ,YAAY,SAAS,EAAA;wBACrB,oBAAA,CACE,KAAA,CAAA,KAAA,SAAS,KAAA,CAAM,oBAAA,KAAf,OAAA,KAAA,IAAA,GAAqC,aAAA,KAArC,OAAA,KAAsD;wBACxD,iBAAA,CACE,KAAA,CAAA,KAAA,SAAS,KAAA,CAAM,qBAAA,KAAf,OAAA,KAAA,IAAA,GAAsC,gBAAA,KAAtC,OAAA,KAA0D;oBAC9D;gBACF;gBACA;YACF;QACF;IAAA;IAEM,SACJ,OAAA,EAC2D;QAAA,OAAA,QAAA,IAAA,EAAA,MAAA;YAC3D,MAAM,EAAE,MAAM,IAAA,EAAM,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,OAAO;YAErD,QAAQ,GAAA,CAAI,IAAI;YAEhB,MAAM,EAAE,eAAA,EAAiB,OAAO,QAAA,CAAS,CAAA,GAAI,gRAAMP,gBAAAA,EAAc;gBAC/D,KAAK,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI;oBACnB,MAAM;oBACN,SAAS,IAAA,CAAK,OAAA;gBAChB,CAAC;gBACD,UAASC,0RAAAA,EAAe,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,QAAQ,OAAO;gBAC9D,MAAM,cAAA,eAAA,CAAA,GACD,OADC;oBAEJ,QAAQ;gBACV;gBACA,uBAAuB;gBACvB,qSAA2BO,mCAAAA,EACzB;gBAEF,aAAa,QAAQ,WAAA;gBACrB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;YACrB,CAAC;YAED,MAAM,OAAO,IAAA;YAEb,IAAI,eAA4C;YAChD,IAAI,eAAe;YACnB,IAAI,mBAAmB;YACvB,IAAI,qBAAoC;YACxC,IAAI,kBAAiC;YACrC,IAAI,aAA4B;YAChC,MAAM,mBAGF,CAAC;YACL,IAAI,eAAe;YAEnB,OAAO;gBACL,QAAQ,SAAS,WAAA,CACf,IAAI,gBAGF;oBACA,WAAU,KAAA,EAAO,UAAA,EAAY;wBA3ZzC,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;wBA6Zc,IAAI,CAAC,MAAM,OAAA,EAAS;4BAClB,eAAe;4BACf,WAAW,OAAA,CAAQ;gCAAE,MAAM;gCAAS,OAAO,MAAM,KAAA;4BAAM,CAAC;4BACxD;wBACF;wBAEA,MAAM,QAAQ,MAAM,KAAA;wBACpB,IAAI,+BAA+B,KAAK,GAAG;4BACzC,IAAI,MAAM,IAAA,CAAK,IAAA,KAAS,iBAAiB;gCACvC,gBAAA,CAAiB,MAAM,YAAY,CAAA,GAAI;oCACrC,UAAU,MAAM,IAAA,CAAK,IAAA;oCACrB,YAAY,MAAM,IAAA,CAAK,OAAA;gCACzB;gCAEA,WAAW,OAAA,CAAQ;oCACjB,MAAM;oCACN,cAAc;oCACd,YAAY,MAAM,IAAA,CAAK,OAAA;oCACvB,UAAU,MAAM,IAAA,CAAK,IAAA;oCACrB,eAAe,MAAM,IAAA,CAAK,SAAA;gCAC5B,CAAC;4BACH;wBACF,OAAA,IAAW,0CAA0C,KAAK,GAAG;4BAC3D,MAAM,WAAW,gBAAA,CAAiB,MAAM,YAAY,CAAA;4BAEpD,IAAI,YAAY,MAAM;gCACpB,WAAW,OAAA,CAAQ;oCACjB,MAAM;oCACN,cAAc;oCACd,YAAY,SAAS,UAAA;oCACrB,UAAU,SAAS,QAAA;oCACnB,eAAe,MAAM,KAAA;gCACvB,CAAC;4BACH;wBACF,OAAA,IAAW,uBAAuB,KAAK,GAAG;4BACxC,aAAa,MAAM,QAAA,CAAS,EAAA;4BAC5B,WAAW,OAAA,CAAQ;gCACjB,MAAM;gCACN,IAAI,MAAM,QAAA,CAAS,EAAA;gCACnB,WAAW,IAAI,KAAK,MAAM,QAAA,CAAS,UAAA,GAAa,GAAI;gCACpD,SAAS,MAAM,QAAA,CAAS,KAAA;4BAC1B,CAAC;wBACH,OAAA,IAAW,iBAAiB,KAAK,GAAG;4BAClC,WAAW,OAAA,CAAQ;gCACjB,MAAM;gCACN,WAAW,MAAM,KAAA;4BACnB,CAAC;wBACH,OAAA,IACE,8BAA8B,KAAK,KACnC,MAAM,IAAA,CAAK,IAAA,KAAS,iBACpB;4BACA,gBAAA,CAAiB,MAAM,YAAY,CAAA,GAAI,KAAA;4BACvC,eAAe;4BACf,WAAW,OAAA,CAAQ;gCACjB,MAAM;gCACN,cAAc;gCACd,YAAY,MAAM,IAAA,CAAK,OAAA;gCACvB,UAAU,MAAM,IAAA,CAAK,IAAA;gCACrB,MAAM,MAAM,IAAA,CAAK,SAAA;4BACnB,CAAC;wBACH,OAAA,IAAW,wBAAwB,KAAK,GAAG;4BACzC,eAAe,4BAA4B;gCACzC,cAAA,CAAc,KAAA,MAAM,QAAA,CAAS,kBAAA,KAAf,OAAA,KAAA,IAAA,GAAmC,MAAA;gCACjD;4BACF,CAAC;4BACD,eAAe,MAAM,QAAA,CAAS,KAAA,CAAM,YAAA;4BACpC,mBAAmB,MAAM,QAAA,CAAS,KAAA,CAAM,aAAA;4BACxC,qBAAA,CACE,KAAA,CAAA,KAAA,MAAM,QAAA,CAAS,KAAA,CAAM,oBAAA,KAArB,OAAA,KAAA,IAAA,GAA2C,aAAA,KAA3C,OAAA,KACA;4BACF,kBAAA,CACE,KAAA,CAAA,KAAA,MAAM,QAAA,CAAS,KAAA,CAAM,qBAAA,KAArB,OAAA,KAAA,IAAA,GAA4C,gBAAA,KAA5C,OAAA,KACA;wBACJ,OAAA,IAAW,+BAA+B,KAAK,GAAG;4BAChD,WAAW,OAAA,CAAQ;gCACjB,MAAM;gCACN,QAAQ;oCACN,YAAY;oCACZ,IAAA,CAAI,KAAA,CAAA,KAAA,CAAA,KAAA,KAAK,MAAA,EAAO,UAAA,KAAZ,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,GAAA,KAAA,OAAA,+QAA8BD,aAAAA,CAAW;oCAC7C,KAAK,MAAM,UAAA,CAAW,GAAA;oCACtB,OAAO,MAAM,UAAA,CAAW,KAAA;gCAC1B;4BACF,CAAC;wBACH;oBACF;oBAEA,OAAM,UAAA,EAAY;wBAChB,WAAW,OAAA,CAAQ;4BACjB,MAAM;4BACN;4BACA,OAAO;gCAAE;gCAAc;4BAAiB;4BACxC,kBAAkB;gCAChB,MAAM;oCACJ;oCACA;oCACA;gCACF;4BACF;wBACF,CAAC;oBACH;gBACF,CAAC;gBAEH,SAAS;oBACP,WAAW,KAAA;oBACX,aAAa,CAAC;gBAChB;gBACA,aAAa;oBAAE,SAAS;gBAAgB;gBACxC,SAAS;oBAAE,MAAM,KAAK,SAAA,CAAU,IAAI;gBAAE;gBACtC;YACF;QACF;IAAA;AACF;AAEA,IAAM,sMAAcJ,IAAAA,CAAE,MAAA,CAAO;IAC3B,cAAcA,4LAAAA,CAAE,MAAA,CAAO;IACvB,8MAAsBA,IAAAA,CACnB,MAAA,CAAO;QAAE,eAAeA,4LAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAAE,CAAC,EAC9C,OAAA,CAAQ;IACX,uMAAeA,IAAAA,CAAE,MAAA,CAAO;IACxB,uBAAuBA,4LAAAA,CACpB,MAAA,CAAO;QAAE,0MAAkBA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAAE,CAAC,EACjD,OAAA,CAAQ;AACb,CAAC;AAED,IAAM,+MAAuBA,IAAAA,CAAE,MAAA,CAAO;IACpC,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,4BAA4B;IAC5C,+LAAOA,IAAAA,CAAE,MAAA,CAAO;AAClB,CAAC;AAED,IAAM,sNAA8BA,IAAAA,CAAE,MAAA,CAAO;IAC3C,8LAAMA,IAAAA,CAAE,IAAA,CAAK;QAAC;QAAsB,qBAAqB;KAAC;IAC1D,kMAAUA,IAAAA,CAAE,MAAA,CAAO;QACjB,4MAAoBA,IAAAA,CAAE,MAAA,CAAO;YAAE,gMAAQA,IAAAA,CAAE,MAAA,CAAO;QAAE,CAAC,EAAE,OAAA,CAAQ;QAC7D,OAAO;IACT,CAAC;AACH,CAAC;AAED,IAAM,qNAA6BA,IAAAA,CAAE,MAAA,CAAO;IAC1C,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,kBAAkB;IAClC,kMAAUA,IAAAA,CAAE,MAAA,CAAO;QACjB,IAAIA,4LAAAA,CAAE,MAAA,CAAO;QACb,oMAAYA,IAAAA,CAAE,MAAA,CAAO;QACrB,+LAAOA,IAAAA,CAAE,MAAA,CAAO;IAClB,CAAC;AACH,CAAC;AAED,IAAM,uNAA+BA,IAAAA,CAAE,MAAA,CAAO;IAC5C,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,2BAA2B;IAC3C,sMAAcA,IAAAA,CAAE,MAAA,CAAO;IACvB,8LAAMA,IAAAA,CAAE,kBAAA,CAAmB,QAAQ;QACjCA,4LAAAA,CAAE,MAAA,CAAO;YACP,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,SAAS;QAC3B,CAAC;gMACDA,IAAAA,CAAE,MAAA,CAAO;YACP,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,eAAe;YAC/B,4LAAIA,IAAAA,CAAE,MAAA,CAAO;YACb,SAASA,4LAAAA,CAAE,MAAA,CAAO;YAClB,8LAAMA,IAAAA,CAAE,MAAA,CAAO;YACf,mMAAWA,IAAAA,CAAE,MAAA,CAAO;YACpB,gMAAQA,IAAAA,CAAE,OAAA,CAAQ,WAAW;QAC/B,CAAC;KACF;AACH,CAAC;AAED,IAAM,mOAA2CA,IAAAA,CAAE,MAAA,CAAO;IACxD,MAAMA,4LAAAA,CAAE,OAAA,CAAQ,wCAAwC;IACxD,iMAASA,IAAAA,CAAE,MAAA,CAAO;IAClB,sMAAcA,IAAAA,CAAE,MAAA,CAAO;IACvB,+LAAOA,IAAAA,CAAE,MAAA,CAAO;AAClB,CAAC;AAED,IAAM,gCAAgCA,4LAAAA,CAAE,MAAA,CAAO;IAC7C,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,4BAA4B;IAC5C,sMAAcA,IAAAA,CAAE,MAAA,CAAO;IACvB,8LAAMA,IAAAA,CAAE,kBAAA,CAAmB,QAAQ;gMACjCA,IAAAA,CAAE,MAAA,CAAO;YACP,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,SAAS;QAC3B,CAAC;gMACDA,IAAAA,CAAE,MAAA,CAAO;YACP,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,eAAe;YAC/B,4LAAIA,IAAAA,CAAE,MAAA,CAAO;YACb,iMAASA,IAAAA,CAAE,MAAA,CAAO;YAClB,8LAAMA,IAAAA,CAAE,MAAA,CAAO;YACf,WAAWA,4LAAAA,CAAE,MAAA,CAAO;QACtB,CAAC;KACF;AACH,CAAC;AAED,IAAM,wNAAgCA,IAAAA,CAAE,MAAA,CAAO;IAC7C,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,uCAAuC;IACvD,oMAAYA,IAAAA,CAAE,MAAA,CAAO;QACnB,8LAAMA,IAAAA,CAAE,OAAA,CAAQ,cAAc;QAC9B,6LAAKA,IAAAA,CAAE,MAAA,CAAO;QACd,+LAAOA,IAAAA,CAAE,MAAA,CAAO;IAClB,CAAC;AACH,CAAC;AAED,IAAM,mNAA2BA,IAAAA,CAAE,KAAA,CAAM;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;4LACAA,IAAAA,CAAE,MAAA,CAAO;QAAE,8LAAMA,IAAAA,CAAE,MAAA,CAAO;IAAE,CAAC,EAAE,WAAA,CAAY;CAC5C;AAED,SAAS,iBACP,KAAA,EAC+C;IAC/C,OAAO,MAAM,IAAA,KAAS;AACxB;AAEA,SAAS,8BACP,KAAA,EACuD;IACvD,OAAO,MAAM,IAAA,KAAS;AACxB;AAEA,SAAS,wBACP,KAAA,EACsD;IACtD,OACE,MAAM,IAAA,KAAS,wBAAwB,MAAM,IAAA,KAAS;AAE1D;AAEA,SAAS,uBACP,KAAA,EACqD;IACrD,OAAO,MAAM,IAAA,KAAS;AACxB;AAEA,SAAS,0CACP,KAAA,EACmE;IACnE,OAAO,MAAM,IAAA,KAAS;AACxB;AAEA,SAAS,+BACP,KAAA,EACwD;IACxD,OAAO,MAAM,IAAA,KAAS;AACxB;AAEA,SAAS,+BACP,KAAA,EACwD;IACxD,OAAO,MAAM,IAAA,KAAS;AACxB;AAQA,SAAS,wBAAwB,OAAA,EAAuC;IAEtE,IAAI,QAAQ,UAAA,CAAW,GAAG,GAAG;QAC3B,IAAI,QAAQ,UAAA,CAAW,SAAS,KAAK,QAAQ,UAAA,CAAW,YAAY,GAAG;YACrE,OAAO;gBACL,kBAAkB;gBAClB,mBAAmB;gBACnB,wBAAwB;YAC1B;QACF;QAEA,OAAO;YACL,kBAAkB;YAClB,mBAAmB;YACnB,wBAAwB;QAC1B;IACF;IAGA,OAAO;QACL,kBAAkB;QAClB,mBAAmB;QACnB,wBAAwB;IAC1B;AACF;AAEA,IAAM,6NAAqCA,IAAAA,CAAE,MAAA,CAAO;IAClD,kMAAUA,IAAAA,CAAE,GAAA,CAAI,EAAE,OAAA,CAAQ;IAC1B,0MAAmBA,KAAAA,CAAE,OAAA,CAAQ,EAAE,OAAA,CAAQ;IACvC,4MAAoBA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACvC,+LAAOA,IAAAA,CAAE,OAAA,CAAQ,EAAE,OAAA,CAAQ;IAC3B,MAAMA,4LAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACzB,yMAAiBA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACpC,eAAeA,4LAAAA,CAAE,OAAA,CAAQ,EAAE,OAAA,CAAQ;IACnC,sMAAcA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;AACnC,CAAC;;AFhlBH,SAAe,eACb,QAAA,EACA,SAAA,EACyB;IAAA,OAAA,QAAA,IAAA,EAAA,MAAA;QACzB,MAAM,OAAO,SAAS,IAAA;QACtB,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM,0DAAa;QAC/B;QAEA,OAAO,IAAI,eAAe;YAClB,OAAM,UAAA,EAAY;gBAAA,OAAA,QAAA,IAAA,EAAA,MAAA;oBA5H5B,IAAAM,KAAA,IAAA,IAAA,IAAA;oBA8HM,IAAI,oBAAoB;oBAGxB,MAAM,cAAc,IAAI,YAAY;oBACpC,IAAI,SAAS;oBAEb,MAAM,SAAS,KAAK,SAAA,CAAU;oBAE9B,IAAI;wBACF,MAAO,KAAM;4BACX,MAAM,EAAE,IAAA,EAAM,KAAA,CAAM,CAAA,GAAI,MAAM,OAAO,IAAA,CAAK;4BAC1C,IAAI,KAAM,CAAA;4BAEV,UAAU,YAAY,MAAA,CAAO,OAAO;gCAAE,QAAQ;4BAAK,CAAC;4BAGpD,MAAM,WAAW,OAAO,KAAA,CAAM,MAAM;4BACpC,SAAS,SAAS,GAAA,CAAI,KAAK;4BAE3B,KAAA,MAAW,WAAW,SAAU;gCAC9B,IAAI,CAAC,QAAQ,IAAA,CAAK,EAAG,CAAA;gCAErB,MAAM,aAAa,QAAQ,KAAA,CAAM,kBAAkB;gCACnD,MAAM,YAAY,QAAQ,KAAA,CAAM,iBAAiB;gCAEjD,IAAI,CAAC,cAAc,CAAC,UAAW,CAAA;gCAE/B,MAAM,YAAA,CAAA,CAAYA,MAAA,UAAA,CAAW,CAAC,CAAA,KAAZ,OAAA,KAAA,IAAAA,IAAe,IAAA,EAAA,KAAU;gCAC3C,MAAM,WAAA,CAAA,CAAW,KAAA,SAAA,CAAU,CAAC,CAAA,KAAX,OAAA,KAAA,IAAA,GAAc,IAAA,EAAA,KAAU;gCAEzC,IAAI;oCACF,MAAM,OAAO,KAAK,KAAA,CAAM,QAAQ;oCAGhC,IAAI,cAAc,0BAA0B;wCAC1C,IAAI,CAAC,mBAAmB;4CACtB,WAAW,OAAA,CAAQ;gDACjB,OAAO;gDACP,MAAM;oDACJ,IAAI,KAAK,EAAA,IAAM;oDACf,MAAM;oDACN,SAAS;wDAAC;4DAAE,MAAM;4DAAQ,MAAM;gEAAE,OAAO;4DAAG;wDAAE,CAAC;qDAAA;gDACjD;4CACF,CAAC;4CACD,oBAAoB;wCACtB;oCACF,OAAA,IAAW,cAAc,wBAAwB;wCAC/C,MAAM,UAAA,CAAU,KAAA,CAAA,KAAA,KAAK,KAAA,KAAL,OAAA,KAAA,IAAA,GAAY,OAAA,KAAZ,OAAA,KAAA,IAAA,EAAA,CAAsB,EAAA;wCACtC,IAAA,CAAI,WAAA,OAAA,KAAA,IAAA,QAAS,IAAA,MAAS,UAAA,CAAA,CAAU,KAAA,QAAQ,IAAA,KAAR,OAAA,KAAA,IAAA,GAAc,KAAA,KAAS,MAAM;4CAC3D,WAAW,OAAA,CAAQ;gDACjB,OAAO;gDACP,MAAM;oDACJ,OAAO;wDACL,SAAS;4DACP;gEACE,MAAM;gEACN,MAAM;oEAAE,OAAO,QAAQ,IAAA,CAAK,KAAA;gEAAM;4DACpC;yDACF;oDACF;gDACF;4CACF,CAAC;wCACH;oCACF,OAAA,IAAW,cAAc,wBAAwB;wCAC/C,WAAW,OAAA,CAAQ;4CACjB,OAAO;4CACP;wCACF,CAAC;oCACH,OAAA,IAAW,cAAc,8BAA8B;wCACrD,WAAW,OAAA,CAAQ;4CACjB,OAAO;4CACP;wCACF,CAAC;oCACH;gCACF,EAAA,OAAS,GAAG;oCACV,QAAQ,KAAA,CAAM,mCAAe,GAAG,QAAQ;gCAC1C;4BACF;wBACF;wBACA,WAAW,KAAA,CAAM;oBACnB,EAAA,OAAS,OAAO;wBACd,QAAQ,KAAA,CAAM,iDAAc,KAAK;wBACjC,WAAW,KAAA,CAAM,KAAK;oBACxB;gBACF;YAAA;QACF,CAAC;IACH;AAAA;AAKO,SAAS,WAAW,UAAgC,CAAC,CAAA,EAAiB;IAzN7E,IAAA;IA0NE,MAAM,UAAA,CACJ,KAAA,CAAA,GAAA,qQAAA,CAAA,uBAAA,EAAqB,QAAQ,OAAO,CAAA,KAApC,OAAA,KAAyC;IAG3C,MAAM,eAAe;IAGrB,MAAM,aAAa,IAAO,eAAA;YACxB,eAAe,CAAA,OAAA,4QAAU,aAAA,EAAW;gBAClC,QAAQ,QAAQ,MAAA;gBAChB,yBAAyB;gBACzB,aAAa;YACf,CAAC,CAAC,EAAA;QAAA,GACC,QAAQ,OAAA;IAGb,MAAM,kBAAkB,CACtB,SACA,WAA6B,CAAC,CAAA,KAC9B;QA7OJ,IAAAA;QA8OI,OAAA,IAAI,sBAAsB,SAAS,UAAU;YAC3C,UAAU;YACV;YACA,SAAS;YACT,YAAA,CAAYA,MAAA,QAAQ,UAAA,KAAR,OAAAA,4QAAsBC,aAAAA;YAClC,OAAO,QAAQ,KAAA;QACjB,CAAC;IAAA;IAEH,MAAM,uBAAuB,CAAC,YAAkC;QAC9D,OAAO,IAAI,2BAA2B,SAAS;YAC7C,UAAU,GAAG,YAAY,CAAA,UAAA,CAAA;YACzB,KAAK,CAAC,EAAE,IAAA,CAAK,CAAA,GAAM,GAAG,OAAO,GAAG,IAAI,EAAA;YACpC,SAAS;YACT,OAAO,QAAQ,KAAA;QACjB,CAAC;IACH;IAGA,MAAM,aAA0B;QAC9B,MAAM;YACJ,QAAQ,CAAOC,WAA6B,QAAA,MAAA,MAAA;oBAC1C,OAAO,MAAM,GAAG,OAAO,CAAA,aAAA,CAAA,EAAiB;wBACtC,QAAQ;wBACR,SAAS,eAAA;4BACP,gBAAgB;4BAChB,QAAQ;wBAAA,GACL,WAAW;wBAEhB,MAAM,KAAK,SAAA,CAAU;4BACnB,OAAOA,SAAQ,KAAA;4BACf,aAAaA,SAAQ,WAAA;4BACrB,UAAUA,SAAQ,QAAA;4BAClB,MAAMA,SAAQ,IAAA,IAAQ;4BACtB,UAAUA,SAAQ,QAAA,IAAY;4BAC9B,QAAQA,SAAQ,MAAA,KAAW,KAAA,IAAYA,SAAQ,MAAA,GAAS;wBAC1D,CAAC;oBACH,CAAC;gBACH;YACA,QAAQ,CAAOA,WAA6B,QAAA,MAAA,MAAA;oBApRlD,IAAAF;oBAqRQ,MAAM,eAAA,CAAA,CAAgBA,MAAAE,SAAQ,UAAA,KAAR,OAAAF,4QAAsBC,aAAAA,EAAY;oBAExD,MAAM,WAAW,MAAM,MAAM,GAAG,OAAO,CAAA,aAAA,CAAA,EAAiB;wBACtD,QAAQ;wBACR,SAAS,eAAA;4BACP,gBAAgB;4BAChB,QAAQ;wBAAA,GACL,WAAW;wBAEhB,MAAM,KAAK,SAAA,CAAU;4BACnB,OAAOC,SAAQ,KAAA;4BACf,aAAaA,SAAQ,WAAA;4BACrB,UAAUA,SAAQ,QAAA;4BAClB,MAAMA,SAAQ,IAAA,IAAQ;4BACtB,UAAUA,SAAQ,QAAA,IAAY;4BAC9B,QAAQ;wBACV,CAAC;oBACH,CAAC;oBAED,IAAI,CAAC,SAAS,EAAA,EAAI;wBAChB,MAAM,IAAI,MACR,CAAA,oCAAA,EAAmB,SAAS,MAAM,CAAA,CAAA,EAAI,SAAS,UAAU,EAAA;oBAE7D;oBAEA,MAAM,SAAS,MAAM,eAAe,UAAU,YAAY;oBAE1D,OAAO;wBACL,UAAUA,SAAQ,QAAA,IAAY;wBAC9B,WAAW;wBACX;oBACF;gBACF;QACF;IACF;IAEA,MAAM,2BAA2B,CAAC,UAChC,IAAI,uBAAuB,SAAS;YAClC,UAAU,GAAG,YAAY,CAAA,cAAA,CAAA;YACzB,KAAK,CAAC,EAAE,IAAA,CAAK,CAAA,GAAM,GAAG,OAAO,GAAG,IAAI,EAAA;YACpC,SAAS;YACT,OAAO,QAAQ,KAAA;QACjB,CAAC;IAEH,MAAM,WAAW,SACf,OAAA,EACA,QAAA,EACA;QACA,IAAI,YAAY;YACd,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO,gBAAgB,SAAS,QAAQ;IAC1C;IAEA,SAAS,aAAA,GAAgB;IACzB,SAAS,IAAA,GAAO;IAChB,SAAS,IAAA,GAAO;QACd,SAAS;IACX;IAEA,SAAS,aAAA,GAAgB;IACzB,SAAS,kBAAA,GAAqB;IAE9B,SAAS,SAAA,GAAY;IAErB,OAAO;AACT;AAKO,IAAM,OAAO,WAAW", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], "debugId": null}}]}