"use client";

import * as React from "react";
import { type User as NextAuthUser } from "next-auth";
import {
  ChevronsUpDown,
  GalleryVerticalEnd,
  Bell,
  Bookmark,
  Search,
  Check,
  Code2,
  Bot,
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import {
  EarthIcon,
  MapIcon,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { TooltipProvider } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog";
import Profile from "@/components/profile";
import Link from "next/link";
import { SidebarHistory } from "./sidebar-history";
import { Collapsible } from "@radix-ui/react-collapsible";
import { startTransition, useMemo, useState } from "react";
import { models } from "@/lib/ai/models";
import { saveModelId } from "@/app/(map)/actions";

const VERSIONS = ["1.0.0", "1.1.0", "2.0.0"] as const;
type Version = (typeof VERSIONS)[number];

const APPLICATION_ITEMS = [
  {
    label: "말로 만드는 지도",
    url: "/geon-2d-map",
    icon: EarthIcon,
    badge: "Beta",
    disabled: false,
  },
  {
    label: "업무지원(챗봇)",
    url: "/preview",
    icon: Bot,
    badge: "Beta",
    disabled: false,
  },
];

const CHAT_ITEMS = [
  // {icon: History, label: "최근 대화", disabled: true},
  { icon: Bookmark, label: "북마크", disabled: true },
  { icon: Bell, label: "알림", count: 3, disabled: true },
];

// Custom hooks
const useVersionControl = (initialVersion: Version) => {
  const [version, setVersion] = React.useState<Version>(initialVersion);
  const isLatest = version === "2.0.0";

  return { version, setVersion, isLatest };
};

// Sub-components
const ModelSelector = ({
  selectedModelId,
  className,
}: {
  selectedModelId: string;
  className?: string;
}) => {
  const [open, setOpen] = useState(false);
  const [optimisticModelId, setOptimisticModelId] =
    React.useOptimistic(selectedModelId);

  const selectModel = useMemo(
    () => models.find((model) => model.id === optimisticModelId),
    [optimisticModelId]
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <SidebarMenuButton
          size="lg"
          className="group data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
        >
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-500 text-white shadow-lg transition-all group-hover:shadow-blue-500/25">
            <GalleryVerticalEnd className="size-4" />
          </div>
          <div className="flex flex-col gap-0.5 leading-none">
            <span className="font-semibold">말로 만드는 지도</span>
            <span className="text-xs text-muted-foreground">
              {selectedModelId}
            </span>
          </div>
          <ChevronsUpDown className="ml-auto h-4 w-4 transition-transform duration-200" />
        </SidebarMenuButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-[--radix-dropdown-menu-trigger-width]"
        align="start"
      >
        {models.map((model) => (
          <DropdownMenuItem
            key={model.id}
            onSelect={() => {
              setOpen(false);

              startTransition(() => {
                setOptimisticModelId(model.id);
                saveModelId(model.id);
              });
            }}
            className="gap-4 group/item"
            data-active={model.id === optimisticModelId}
            // disabled={model.label.includes("Llama")}
          >
            <div className="flex flex-col gap-1 items-start">
              {model.label}
              {model.description && (
                <div className="text-xs text-muted-foreground">
                  {model.description}
                </div>
              )}
            </div>
            <Check className="size-4 ml-auto opacity-0 group-data-[active=true]/item:opacity-100" />
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const SearchDialog = ({
  open,
  onOpenChange,
  searchQuery,
  setSearchQuery,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
}) => (
  <Dialog open={open} onOpenChange={onOpenChange}>
    <DialogContent className="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>빠른 검색</DialogTitle>
      </DialogHeader>
      <div className="relative">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="검색어를 입력하세요..."
          className="pl-8"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          autoFocus
        />
      </div>
    </DialogContent>
  </Dialog>
);

// Main component
export function AppSidebar({
  user,
  selectedModelId,
}: {
  user: NextAuthUser | undefined;
  selectedModelId: string;
}) {
  const { open, setOpen } = useSidebar();
  const { version, setVersion } = useVersionControl("1.0.0");

  return (
    <TooltipProvider>
      <Sidebar
        collapsible="icon"
        className="border-r border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
      >
        <SidebarHeader className="border-b border-border/40">
          <SidebarMenu>
            <SidebarMenuItem className="flex justify-between items-center">
              <ModelSelector selectedModelId={selectedModelId} />
              {open && <SidebarTrigger />}
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>

        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>Application</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {APPLICATION_ITEMS.map((item) => (
                  <SidebarMenuItem key={item.label}>
                    <SidebarMenuButton
                      asChild
                      tooltip={item.label}
                      disabled={item.disabled}
                      className={cn(
                        "relative transition-colors hover:bg-accent/50",
                        item.disabled && "opacity-50 cursor-not-allowed"
                      )}
                    >
                      <Link href={item.url}>
                        <item.icon className="mr-2 h-4 w-4" />
                        <span>{item.label}</span>
                        {item.badge && (
                          <Badge
                            variant="default"
                            className={cn(
                              "ml-auto",
                              item.badge === "New"
                                ? "bg-green-500"
                                : "bg-blue-500"
                            )}
                          >
                            {item.badge}
                          </Badge>
                        )}
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>

          {/* <SidebarGroup>
            <SidebarGroupLabel>Map</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMap user={user} />
            </SidebarGroupContent>
          </SidebarGroup> */}

          <SidebarGroup>
            <SidebarGroupLabel>Chat</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarHistory user={user} />
            </SidebarGroupContent>
          </SidebarGroup>
          
        </SidebarContent>

        <SidebarFooter className="border-t border-border/40">
          <SidebarMenu>
            {!open && (
              <SidebarMenuItem>
                <SidebarTrigger />
              </SidebarMenuItem>
            )}
            <SidebarMenuItem>
              <Profile user={user} />
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
      </Sidebar>
    </TooltipProvider>
  );
}
