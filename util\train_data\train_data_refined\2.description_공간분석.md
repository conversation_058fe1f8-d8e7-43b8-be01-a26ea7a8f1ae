# 공간분석 설명
## 포인트집계분석
포인트 집계 분석은 지정된 지역 내에서 포인트 데이터를 기반으로 집계를 수행하는 분석입니다. 이 분석은 포인트 데이터의 속성을 특정 지역으로 그룹화하여 합계, 평균, 빈도 등을 계산할 수 있습니다. 이를 통해 포인트 데이터의 패턴이나 특성을 파악할 수 있습니다. 분석 결과는 지도 상에 시각화되어 포인트의 집계 결과를 쉽게 확인할 수 있습니다.


## 공간조인분석
공간 조인 분석은 공간 데이터를 기반으로 두 개 이상의 데이터셋을 결합하는 분석 방법입니다. 이 분석은 공간 데이터에 기반하여 다른 데이터셋과 결합하여 추가 정보를 얻을 수 있습니다. 예를 들어, 도로 데이터와 인구 데이터를 공간 조인 분석을 통해 결합하여 도로 주변 인구 수를 분석할 수 있습니다. 이러한 분석은 지리적 위치에 따라 데이터를 통합하고 관찰할 수 있도록 도와줍니다.


## 영역 내 집계 분석
비교 대상 레이어의 폴리곤 경계 내에 있는 분석 대상 레이어를 레이어 유형(점/선/면)에 따라 집계(총건수/총길이/총면적) 분석한다.


## 공간분포패턴
레이어의 공간 분포 패턴을 분석하여 요약 유형별로 중심을 추출한다.


## 주변집계분석
직선 거리 또는 이동 모드를 사용하여 입력 레이어에서 피처로부터 지정된 거리 내에 있는 피처를 찾는다. 그런 다음 인근 피처에 대한 통계를 계산한다.


## 공간조건검색분석
공간조건검색분석은 비교 레이어를 기준으로 공간 검색 유형의 조건을 만족하는 분석 레이어의 객체를 검색하는 것입니다. 다음은 화면 구성과 조작 방법입니다.


## 공간조건추출분석
비교 레이어를 기준으로 공간검색 유형의 조건을 만족하는 분석 레이어의 객체를 검색 후 교차하는 영역으로 자르고 비교 레이어의 속성을 추가한다.


## 중심찾기분석
분석 대상 레이어 객체에서 결과 위치 설정에 따라 중심점을 찾는다.


## 유사한 위치찾기분석
유사한 위치 찾기 분석은 어떤 공간적 특성이 유사한 지점들을 찾는 분석 방법입니다. 이를 위해 일반적으로 거리나 유사도 지표를 사용하여 지점 사이의 유사성을 측정합니다. 예를 들어, 주어진 한 지점에 대해 유사한 다른 지점들을 찾는 것이 일반적인 사용 사례입니다. 이를 통해 비슷한 특성이 가까이 있는 다른 지점을 찾거나, 유사한 환경의 다른 위치를 찾을 수 있습니다. 이러한 분석은 지리 정보 시스템(GIS) 분야 등에서 활발하게 사용되고 있습니다.


## 밀도분석
위치에 있는 포인트를 기반으로 군집 포인트의 개수가 많은 곳을 쉽게 식별할 수 있도록 시각화하는 분석을 수행한다.


## 핫스팟분석
데이터의 공간 패턴에 통계적으로 유의미한 군집이 있는지를 격자 그리드로 시각화하는 분석을 수행한다.


## 포인트군집분석
포인트군집분석은 공간 데이터에 있는 포인트들을 일정한 기준에 따라 군집화하는 분석 방법입니다. 이를 통해 포인트들의 공간적 패턴이나 분포를 파악할 수 있습니다. 기준으로는 거리나 밀도 등을 사용할 수 있으며, 주요 목적은 비슷한 특성을 가진 포인트들을 함께 그룹화하여 서로 비교하거나 클러스터링된 그룹 간 유사성을 평가하는 것입니다.


## 버퍼분석
분석 대상 레이어 주위에 입력한 거리까지의 영역을 생성하는 작업을 수행한다.


## 최근접위치찾기분석
최근접 위치 찾기 분석은 어떤 한 지점을 기준으로 가장 가까운 위치를 찾는 분석 방법입니다. 이를 통해 특정 지점과 가장 가까운 주변 위치를 식별할 수 있습니다. 이 분석을 사용하여 예를 들면, 가장 가까운 상점이나 호텔, 공원 등을 찾을 수 있습니다. 이 방법은 지리적인 거리를 기준으로 가장 가까운 위치를 찾는데 사용됩니다.


## 경계디졸브
경계 또는 중첩되는 영역을 병합하여 단일 영역으로 생성하는 작업을 수행한다.


## 데이터추출
지정된 관심 영역에 대한 데이터를 선택하여 다운로드하거나 레이어에 추가된다.


## 공간분할생성
분석 대상 레이어를 입력한 분할타입으로 영역을 분할하여 생성하는 작업을 수행한다.


## 레이어 병합
동일한 유형의 레이어를 하나의 새로운 레이어로 병합하여 생성하는 작업을 수행한다.


## 레이어중쳡(지우기)
동일한 유형의 두 레이어의 중첩되는 영역을 제외한 새 레이어로 속성을 포함하여 병합하는 작업을 수행한다.


## 레이어중쳡(교차)
동일한 유형의 두 레이어의 교차되는 영역을 새 레이어로 속성을 포함하여 병합하는 작업을 수행한다.


## 레이어중쳡(유니온)
동일한 유형의 두 레이어를 새 레이어로 속성을 포함하여 교차되는 영역은 자르기 후 병합하는 작업을 수행한다.


## 클러스터링
포인트 레이어의 위치 근접도를 분석하여 그룹으로 묶어 포인트수를 시각화하는 작업을 수행한다.


## 면적계산
대상 레이어 내 피처들의 실제 면적과 실제 둘레 길이를 측정하는 작업을 수행한다.


## 길이계산
대상 레이어 내 피처들의 실제 길이를 측정하는 작업을 수행한다.


## 파일좌표변환
Shape(ZIP), GeoJSON 파일 형태의 파일을 업로드하여 원하는 좌표계로 변환 후 다운로드하는 기능을 수행한다.


## 단일좌표변환
사용자가 입력한 좌표계의 좌표를 원하는 좌표계로 변환하는 작업을 수행한다.