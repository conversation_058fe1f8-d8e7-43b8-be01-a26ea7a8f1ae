"use client";

import { useState, useEffect, useCallback } from 'react';

export interface ServerHealthStatus {
  isHealthy: boolean;
  isLoading: boolean;
  error: string | null;
  lastChecked: Date | null;
  responseTime: number | null;
}

const HEALTH_CHECK_URL = '/api/health';
const CHECK_INTERVAL = 30000; // 30초마다 체크
const TIMEOUT_DURATION = 5000; // 5초 타임아웃

export function useServerHealth(enabled: boolean = true) {
  const [status, setStatus] = useState<ServerHealthStatus>({
    isHealthy: false,
    isLoading: true,
    error: null,
    lastChecked: null,
    responseTime: null,
  });

  const checkHealth = useCallback(async () => {
    if (!enabled) return;

    setStatus(prev => ({ ...prev, isLoading: true, error: null }));
    
    const startTime = Date.now();
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_DURATION);

      const response = await fetch(HEALTH_CHECK_URL, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      clearTimeout(timeoutId);
      const clientResponseTime = Date.now() - startTime;

      if (response.ok) {
        const data = await response.json();
        setStatus({
          isHealthy: data.status === 'healthy',
          isLoading: false,
          error: null,
          lastChecked: new Date(),
          responseTime: data.responseTime || clientResponseTime,
        });
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      let errorMessage = 'Unknown error';
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = 'Request timeout';
        } else if (error.message.includes('fetch')) {
          errorMessage = 'Network error';
        } else {
          errorMessage = error.message;
        }
      }

      setStatus({
        isHealthy: false,
        isLoading: false,
        error: errorMessage,
        lastChecked: new Date(),
        responseTime,
      });
    }
  }, [enabled]);

  // 초기 체크 및 주기적 체크
  useEffect(() => {
    if (!enabled) return;

    // 즉시 체크
    checkHealth();

    // 주기적 체크
    const interval = setInterval(checkHealth, CHECK_INTERVAL);

    return () => {
      clearInterval(interval);
    };
  }, [checkHealth, enabled]);

  // 수동 새로고침
  const refresh = useCallback(() => {
    checkHealth();
  }, [checkHealth]);

  return {
    ...status,
    refresh,
  };
}
