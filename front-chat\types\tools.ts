export interface AddressResponse {
    code: number;
    message: string;
    result: {
      common: {
        totalCount: string;
        currentPage: number;
        countPerPage: number;
        searchAddressGbn: string;
        gbn: string;
        clOk: string;
        trOk: string;
        clCd: string;
        trCd: string;
        clMessage: string;
        trMessage: string;
      };
      jusoList: Array<{
        roadAddr: string;
        jibunAddr: string;
        buildName: string;
        buildLo: string;
        buildLa: string;
        parcelLo: string;
        parcelLa: string;
        poiName: string;
        buildGeom?: string;
        geom?: string;
      }>;
    };
  }

// 레이어 목록 아이템 응답 타입
 export interface LayerListItem {
  lyrId: string; // 레이어ID
  cntntsId: string; // 콘텐츠일련번호
  jobClCode: string; // 업무구분코드
  jobClCodeNm: string; // 업무구분코드명
  lyrNm: string; // 레이어명
  lyrClCode: string; // 레이어분류코드
  lyrClCodeNm: string; // 레이어분류코드명
  lyrClSeCode: string; // 레이어분류구분코드
  lyrClSeCodeNm: string; // 레이어분류구분코드명
  lyrPosesnSeCode: string; // 레이어소유구분코드
  lyrPosesnSeCodeNm: string; // 레이어소유구분코드명
  lyrTySeCode: string; // 레이어유형구분코드
  lyrTySeCodeNm: string; // 레이어유형구분코드명
  svcTySeCode: string; // 서비스유형구분코드
  svcTySeCodeNm: string; // 서비스유형구분코드명
  cntmSeCode: string; // 좌표계구분코드
  cntmSeCodeNm: string; // 좌표계구분코드명
  usePblonsipSeCode: string; // 사용공유구분코드
  usePblonsipSeCodeNm: string; // 사용공유구분코드명
  useSttusSeCode: string; // 사용상태구분코드
  useSttusSeCodeNm: string; // 사용상태구분코드명
  holdDataId: string; // 보유데이터ID
  lyrDc: string; // 레이어설명
  mapUrl: string; // 지도url
  mapUrlParamtr: string; // 지도url파라미터
  xyOrdrNrmltAt: string; // XY순서정상여부
  registerId: string; // 등록자ID
  registerNm: string; // 등록자명
  registDt: string; // 등록일자
  updusrId: string; // 수정자ID
  updusrNm: string; // 수정자명
  updtDt: string; // 수정일자
  userNm: string; // 사용자명
  ownerNm: string; // 소유자 명
  ownerId: string; // 소유자ID
}

// 레이어 목록 응답 타입
export interface LayerListResponse {
  code: number;
  message: string;
  result: {
    pageInfo: {
      pageSize: number;
      pageIndex: number;
      totalCount: number;
    };
    list: LayerListItem[];
  };
}

type LayerResult = {
  id: string;
  name: string;
  type: string;
  visible: boolean;
  zIndex: number;
  server?: string;
  layer?: string;
  service?: string;
  bbox?: boolean;
  method?: string;
  crtfckey?: string;
  projection?: string;
  geometryType?: string;
  info: {
    lyrId: string;
    lyrNm: string;
    description?: string;
    metadata: {
      cntntsId: string;
      jobClCode?: string;
      lyrClCode: string;
      lyrTySeCode: string;
      namespace?: string;
    };
  };
  namespace?: string;
  style?: any;
  matrixSet?: string;
  opacity?: number;
  filter?: string;
  dynmFlterCndCn?: string;
};

export interface DirectionsResponse {
  trans_id: string;
  routes: Array<{
    result_code: number;
    result_msg: string;
    summary: {
      distance: number;
      duration: number;
      fare: {
        taxi: number;
        toll: number;
      };
    };
    sections?: Array<{
      distance: number;
      duration: number;
      roads: Array<{
        name: string;
        distance: number;
        duration: number;
        traffic_speed: number;
        traffic_state: number;
        vertexes: number[];
      }>;
    }>;
  }>;
}

interface LayerFilterResponse {
  lyr_id: string;
  filter: string
  description: string;
}