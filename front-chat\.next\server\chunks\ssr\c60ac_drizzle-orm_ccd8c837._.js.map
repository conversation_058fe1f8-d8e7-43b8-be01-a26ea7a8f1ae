{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/entity.ts"], "sourcesContent": ["export const entityKind = Symbol.for('drizzle:entityKind');\nexport const hasOwnEntityKind = Symbol.for('drizzle:hasOwnEntityKind');\n\nexport interface DrizzleEntity {\n\t[entityKind]: string;\n}\n\nexport type DrizzleEntityClass<T> =\n\t& ((abstract new(...args: any[]) => T) | (new(...args: any[]) => T))\n\t& DrizzleEntity;\n\nexport function is<T extends DrizzleEntityClass<any>>(value: any, type: T): value is InstanceType<T> {\n\tif (!value || typeof value !== 'object') {\n\t\treturn false;\n\t}\n\n\tif (value instanceof type) { // eslint-disable-line no-instanceof/no-instanceof\n\t\treturn true;\n\t}\n\n\tif (!Object.prototype.hasOwnProperty.call(type, entityKind)) {\n\t\tthrow new Error(\n\t\t\t`Class \"${\n\t\t\t\ttype.name ?? '<unknown>'\n\t\t\t}\" doesn't look like a Drizzle entity. If this is incorrect and the class is provided by Dr<PERSON><PERSON>, please report this as a bug.`,\n\t\t);\n\t}\n\n\tlet cls = value.constructor;\n\tif (cls) {\n\t\t// Traverse the prototype chain to find the entityKind\n\t\twhile (cls) {\n\t\t\tif (entityKind in cls && cls[entityKind] === type[entityKind]) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tcls = Object.getPrototypeOf(cls);\n\t\t}\n\t}\n\n\treturn false;\n}\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,aAAa,OAAO,GAAA,CAAI,oBAAoB;AAClD,MAAM,mBAAmB,OAAO,GAAA,CAAI,0BAA0B;AAU9D,SAAS,GAAsC,KAAA,EAAY,IAAA,EAAmC;IACpG,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU;QACxC,OAAO;IACR;IAEA,IAAI,iBAAiB,MAAM;QAC1B,OAAO;IACR;IAEA,IAAI,CAAC,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,MAAM,UAAU,GAAG;QAC5D,MAAM,IAAI,MACT,CAAA,OAAA,EACC,KAAK,IAAA,IAAQ,WACd,CAAA,6HAAA,CAAA;IAEF;IAEA,IAAI,MAAM,MAAM,WAAA;IAChB,IAAI,KAAK;QAER,MAAO,IAAK;YACX,IAAI,cAAc,OAAO,GAAA,CAAI,UAAU,CAAA,KAAM,IAAA,CAAK,UAAU,CAAA,EAAG;gBAC9D,OAAO;YACR;YAEA,MAAM,OAAO,cAAA,CAAe,GAAG;QAChC;IACD;IAEA,OAAO;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/table.ts"], "sourcesContent": ["import type { Column, GetColumnData } from './column.ts';\nimport { entityKind } from './entity.ts';\nimport type { OptionalKeyOnly, RequiredKeyOnly } from './operations.ts';\nimport type { ExtraConfigColumn } from './pg-core/index.ts';\nimport type { SQLWrapper } from './sql/sql.ts';\nimport type { Simplify, Update } from './utils.ts';\n\nexport interface TableConfig<TColumn extends Column = Column<any>> {\n\tname: string;\n\tschema: string | undefined;\n\tcolumns: Record<string, TColumn>;\n\tdialect: string;\n}\n\nexport type UpdateTableConfig<T extends TableConfig, TUpdate extends Partial<TableConfig>> = Required<\n\tUpdate<T, TUpdate>\n>;\n\n/** @internal */\nexport const TableName = Symbol.for('drizzle:Name');\n\n/** @internal */\nexport const Schema = Symbol.for('drizzle:Schema');\n\n/** @internal */\nexport const Columns = Symbol.for('drizzle:Columns');\n\n/** @internal */\nexport const ExtraConfigColumns = Symbol.for('drizzle:ExtraConfigColumns');\n\n/** @internal */\nexport const OriginalName = Symbol.for('drizzle:OriginalName');\n\n/** @internal */\nexport const BaseName = Symbol.for('drizzle:BaseName');\n\n/** @internal */\nexport const IsAlias = Symbol.for('drizzle:IsAlias');\n\n/** @internal */\nexport const ExtraConfigBuilder = Symbol.for('drizzle:ExtraConfigBuilder');\n\nconst IsDrizzleTable = Symbol.for('drizzle:IsDrizzleTable');\n\nexport interface Table<\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tT extends TableConfig = TableConfig,\n> extends SQLWrapper {\n\t// SQLWrapper runtime implementation is defined in 'sql/sql.ts'\n}\n\nexport class Table<T extends TableConfig = TableConfig> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Table';\n\n\tdeclare readonly _: {\n\t\treadonly brand: 'Table';\n\t\treadonly config: T;\n\t\treadonly name: T['name'];\n\t\treadonly schema: T['schema'];\n\t\treadonly columns: T['columns'];\n\t\treadonly inferSelect: InferSelectModel<Table<T>>;\n\t\treadonly inferInsert: InferInsertModel<Table<T>>;\n\t};\n\n\tdeclare readonly $inferSelect: InferSelectModel<Table<T>>;\n\tdeclare readonly $inferInsert: InferInsertModel<Table<T>>;\n\n\t/** @internal */\n\tstatic readonly Symbol = {\n\t\tName: TableName as typeof TableName,\n\t\tSchema: Schema as typeof Schema,\n\t\tOriginalName: OriginalName as typeof OriginalName,\n\t\tColumns: Columns as typeof Columns,\n\t\tExtraConfigColumns: ExtraConfigColumns as typeof ExtraConfigColumns,\n\t\tBaseName: BaseName as typeof BaseName,\n\t\tIsAlias: IsAlias as typeof IsAlias,\n\t\tExtraConfigBuilder: ExtraConfigBuilder as typeof ExtraConfigBuilder,\n\t};\n\n\t/**\n\t * @internal\n\t * Can be changed if the table is aliased.\n\t */\n\t[TableName]: string;\n\n\t/**\n\t * @internal\n\t * Used to store the original name of the table, before any aliasing.\n\t */\n\t[OriginalName]: string;\n\n\t/** @internal */\n\t[Schema]: string | undefined;\n\n\t/** @internal */\n\t[Columns]!: T['columns'];\n\n\t/** @internal */\n\t[ExtraConfigColumns]!: Record<string, ExtraConfigColumn>;\n\n\t/**\n\t *  @internal\n\t * Used to store the table name before the transformation via the `tableCreator` functions.\n\t */\n\t[BaseName]: string;\n\n\t/** @internal */\n\t[IsAlias] = false;\n\n\t/** @internal */\n\t[ExtraConfigBuilder]: ((self: any) => Record<string, unknown>) | undefined = undefined;\n\n\tconstructor(name: string, schema: string | undefined, baseName: string) {\n\t\tthis[TableName] = this[OriginalName] = name;\n\t\tthis[Schema] = schema;\n\t\tthis[BaseName] = baseName;\n\t}\n}\n\nexport function isTable(table: unknown): table is Table {\n\treturn typeof table === 'object' && table !== null && IsDrizzleTable in table;\n}\n\n/**\n * Any table with a specified boundary.\n *\n * @example\n\t```ts\n\t// Any table with a specific name\n\ttype AnyUsersTable = AnyTable<{ name: 'users' }>;\n\t```\n *\n * To describe any table with any config, simply use `Table` without any type arguments, like this:\n *\n\t```ts\n\tfunction needsTable(table: Table) {\n\t\t...\n\t}\n\t```\n */\nexport type AnyTable<TPartial extends Partial<TableConfig>> = Table<UpdateTableConfig<TableConfig, TPartial>>;\n\nexport function getTableName<T extends Table>(table: T): T['_']['name'] {\n\treturn table[TableName];\n}\n\nexport function getTableUniqueName<T extends Table>(table: T): `${T['_']['schema']}.${T['_']['name']}` {\n\treturn `${table[Schema] ?? 'public'}.${table[TableName]}`;\n}\n\nexport type MapColumnName<TName extends string, TColumn extends Column, TDBColumNames extends boolean> =\n\tTDBColumNames extends true ? TColumn['_']['name']\n\t\t: TName;\n\nexport type InferModelFromColumns<\n\tTColumns extends Record<string, Column>,\n\tTInferMode extends 'select' | 'insert' = 'select',\n\tTConfig extends { dbColumnNames: boolean } = { dbColumnNames: false },\n> = Simplify<\n\tTInferMode extends 'insert' ?\n\t\t\t& {\n\t\t\t\t[\n\t\t\t\t\tKey in keyof TColumns & string as RequiredKeyOnly<\n\t\t\t\t\t\tMapColumnName<Key, TColumns[Key], TConfig['dbColumnNames']>,\n\t\t\t\t\t\tTColumns[Key]\n\t\t\t\t\t>\n\t\t\t\t]: GetColumnData<TColumns[Key], 'query'>;\n\t\t\t}\n\t\t\t& {\n\t\t\t\t[\n\t\t\t\t\tKey in keyof TColumns & string as OptionalKeyOnly<\n\t\t\t\t\t\tMapColumnName<Key, TColumns[Key], TConfig['dbColumnNames']>,\n\t\t\t\t\t\tTColumns[Key]\n\t\t\t\t\t>\n\t\t\t\t]?: GetColumnData<TColumns[Key], 'query'>;\n\t\t\t}\n\t\t: {\n\t\t\t[\n\t\t\t\tKey in keyof TColumns & string as MapColumnName<\n\t\t\t\t\tKey,\n\t\t\t\t\tTColumns[Key],\n\t\t\t\t\tTConfig['dbColumnNames']\n\t\t\t\t>\n\t\t\t]: GetColumnData<TColumns[Key], 'query'>;\n\t\t}\n>;\n\n/** @deprecated Use one of the alternatives: {@link InferSelectModel} / {@link InferInsertModel}, or `table.$inferSelect` / `table.$inferInsert`\n */\nexport type InferModel<\n\tTTable extends Table,\n\tTInferMode extends 'select' | 'insert' = 'select',\n\tTConfig extends { dbColumnNames: boolean } = { dbColumnNames: false },\n> = InferModelFromColumns<TTable['_']['columns'], TInferMode, TConfig>;\n\nexport type InferSelectModel<\n\tTTable extends Table,\n\tTConfig extends { dbColumnNames: boolean } = { dbColumnNames: false },\n> = InferModelFromColumns<TTable['_']['columns'], 'select', TConfig>;\n\nexport type InferInsertModel<\n\tTTable extends Table,\n\tTConfig extends { dbColumnNames: boolean } = { dbColumnNames: false },\n> = InferModelFromColumns<TTable['_']['columns'], 'insert', TConfig>;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AACA,SAAS,kBAAkB;;AAkBpB,MAAM,YAAY,OAAO,GAAA,CAAI,cAAc;AAG3C,MAAM,SAAS,OAAO,GAAA,CAAI,gBAAgB;AAG1C,MAAM,UAAU,OAAO,GAAA,CAAI,iBAAiB;AAG5C,MAAM,qBAAqB,OAAO,GAAA,CAAI,4BAA4B;AAGlE,MAAM,eAAe,OAAO,GAAA,CAAI,sBAAsB;AAGtD,MAAM,WAAW,OAAO,GAAA,CAAI,kBAAkB;AAG9C,MAAM,UAAU,OAAO,GAAA,CAAI,iBAAiB;AAG5C,MAAM,qBAAqB,OAAO,GAAA,CAAI,4BAA4B;AAEzE,MAAM,iBAAiB,OAAO,GAAA,CAAI,wBAAwB;AASnD,MAAM,MAAiE;IAC7E,OAAA,mPAAiB,aAAU,CAAA,GAAY,QAAA;IAAA,cAAA,GAgBvC,OAAgB,SAAS;QACxB,MAAM;QACN;QACA;QACA;QACA;QACA;QACA;QACA;IACD,EAAA;IAAA;;;GAAA,GAMA,CAAC,SAAS,CAAA,CAAA;IAAA;;;GAAA,GAMV,CAAC,YAAY,CAAA,CAAA;IAAA,cAAA,GAGb,CAAC,MAAM,CAAA,CAAA;IAAA,cAAA,GAGP,CAAC,OAAO,CAAA,CAAA;IAAA,cAAA,GAGR,CAAC,kBAAkB,CAAA,CAAA;IAAA;;;GAAA,GAMnB,CAAC,QAAQ,CAAA,CAAA;IAAA,cAAA,GAGT,CAAC,OAAO,CAAA,GAAI,MAAA;IAAA,cAAA,GAGZ,CAAC,kBAAkB,CAAA,GAA0D,KAAA,EAAA;IAE7E,YAAY,IAAA,EAAc,MAAA,EAA4B,QAAA,CAAkB;QACvE,IAAA,CAAK,SAAS,CAAA,GAAI,IAAA,CAAK,YAAY,CAAA,GAAI;QACvC,IAAA,CAAK,MAAM,CAAA,GAAI;QACf,IAAA,CAAK,QAAQ,CAAA,GAAI;IAClB;AACD;AAEO,SAAS,QAAQ,KAAA,EAAgC;IACvD,OAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,kBAAkB;AACzE;AAqBO,SAAS,aAA8B,KAAA,EAA0B;IACvE,OAAO,KAAA,CAAM,SAAS,CAAA;AACvB;AAEO,SAAS,mBAAoC,KAAA,EAAmD;IACtG,OAAO,GAAG,KAAA,CAAM,MAAM,CAAA,IAAK,QAAQ,CAAA,CAAA,EAAI,KAAA,CAAM,SAAS,CAAC,EAAA;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/column.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, ColumnDataType } from './column-builder.ts';\nimport { entityKind } from './entity.ts';\nimport type { DriverValueMapper, SQL, SQLWrapper } from './sql/sql.ts';\nimport type { Table } from './table.ts';\nimport type { Update } from './utils.ts';\n\nexport interface ColumnBaseConfig<\n\tTDataType extends ColumnDataType,\n\tTColumnType extends string,\n> extends ColumnBuilderBaseConfig<TDataType, TColumnType> {\n\ttableName: string;\n\tnotNull: boolean;\n\thasDefault: boolean;\n}\n\nexport type ColumnTypeConfig<T extends ColumnBaseConfig<ColumnDataType, string>, TTypeConfig extends object> = T & {\n\tbrand: 'Column';\n\ttableName: T['tableName'];\n\tname: T['name'];\n\tdataType: T['dataType'];\n\tcolumnType: T['columnType'];\n\tdata: T['data'];\n\tdriverParam: T['driverParam'];\n\tnotNull: T['notNull'];\n\thasDefault: T['hasDefault'];\n\tenumValues: T['enumValues'];\n\tbaseColumn: T extends { baseColumn: infer U } ? U : unknown;\n} & TTypeConfig;\n\nexport type ColumnRuntimeConfig<TData, TRuntimeConfig extends object> = ColumnBuilderRuntimeConfig<\n\tTData,\n\tTRuntimeConfig\n>;\n\nexport interface Column<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTRuntimeConfig extends object = object,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTTypeConfig extends object = object,\n> extends DriverValueMapper<T['data'], T['driverParam']>, SQLWrapper {\n\t// SQLWrapper runtime implementation is defined in 'sql/sql.ts'\n}\n/*\n\t`Column` only accepts a full `ColumnConfig` as its generic.\n\tTo infer parts of the config, use `AnyColumn` that accepts a partial config.\n\tSee `GetColumnData` for example usage of inferring.\n*/\nexport abstract class Column<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n> implements DriverValueMapper<T['data'], T['driverParam']>, SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Column';\n\n\tdeclare readonly _: ColumnTypeConfig<T, TTypeConfig>;\n\n\treadonly name: string;\n\treadonly primary: boolean;\n\treadonly notNull: boolean;\n\treadonly default: T['data'] | SQL | undefined;\n\treadonly defaultFn: (() => T['data'] | SQL) | undefined;\n\treadonly onUpdateFn: (() => T['data'] | SQL) | undefined;\n\treadonly hasDefault: boolean;\n\treadonly isUnique: boolean;\n\treadonly uniqueName: string | undefined;\n\treadonly uniqueType: string | undefined;\n\treadonly dataType: T['dataType'];\n\treadonly columnType: T['columnType'];\n\treadonly enumValues: T['enumValues'] = undefined;\n\n\tprotected config: ColumnRuntimeConfig<T['data'], TRuntimeConfig>;\n\n\tconstructor(\n\t\treadonly table: Table,\n\t\tconfig: ColumnRuntimeConfig<T['data'], TRuntimeConfig>,\n\t) {\n\t\tthis.config = config;\n\t\tthis.name = config.name;\n\t\tthis.notNull = config.notNull;\n\t\tthis.default = config.default;\n\t\tthis.defaultFn = config.defaultFn;\n\t\tthis.onUpdateFn = config.onUpdateFn;\n\t\tthis.hasDefault = config.hasDefault;\n\t\tthis.primary = config.primaryKey;\n\t\tthis.isUnique = config.isUnique;\n\t\tthis.uniqueName = config.uniqueName;\n\t\tthis.uniqueType = config.uniqueType;\n\t\tthis.dataType = config.dataType as T['dataType'];\n\t\tthis.columnType = config.columnType;\n\t}\n\n\tabstract getSQLType(): string;\n\n\tmapFromDriverValue(value: unknown): unknown {\n\t\treturn value;\n\t}\n\n\tmapToDriverValue(value: unknown): unknown {\n\t\treturn value;\n\t}\n}\n\nexport type UpdateColConfig<\n\tT extends ColumnBaseConfig<ColumnDataType, string>,\n\tTUpdate extends Partial<ColumnBaseConfig<ColumnDataType, string>>,\n> = Update<T, TUpdate>;\n\nexport type AnyColumn<TPartial extends Partial<ColumnBaseConfig<ColumnDataType, string>> = {}> = Column<\n\tRequired<Update<ColumnBaseConfig<ColumnDataType, string>, TPartial>>\n>;\n\nexport type GetColumnData<TColumn extends Column, TInferMode extends 'query' | 'raw' = 'query'> =\n\t// dprint-ignore\n\tTInferMode extends 'raw' // Raw mode\n\t\t? TColumn['_']['data'] // Just return the underlying type\n\t\t: TColumn['_']['notNull'] extends true // Query mode\n\t\t? TColumn['_']['data'] // Query mode, not null\n\t\t: TColumn['_']['data'] | null; // Query mode, nullable\n\nexport type InferColumnsDataTypes<TColumns extends Record<string, Column>> = {\n\t[Key in keyof TColumns]: GetColumnData<TColumns[Key], 'query'>;\n};\n"], "names": [], "mappings": ";;;AACA,SAAS,kBAAkB;;AA+CpB,MAAe,OAIkD;IAqBvE,YACU,KAAA,EACT,MAAA,CACC;QAFQ,IAAA,CAAA,KAAA,GAAA;QAGT,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,OAAO,IAAA;QACnB,IAAA,CAAK,OAAA,GAAU,OAAO,OAAA;QACtB,IAAA,CAAK,OAAA,GAAU,OAAO,OAAA;QACtB,IAAA,CAAK,SAAA,GAAY,OAAO,SAAA;QACxB,IAAA,CAAK,UAAA,GAAa,OAAO,UAAA;QACzB,IAAA,CAAK,UAAA,GAAa,OAAO,UAAA;QACzB,IAAA,CAAK,OAAA,GAAU,OAAO,UAAA;QACtB,IAAA,CAAK,QAAA,GAAW,OAAO,QAAA;QACvB,IAAA,CAAK,UAAA,GAAa,OAAO,UAAA;QACzB,IAAA,CAAK,UAAA,GAAa,OAAO,UAAA;QACzB,IAAA,CAAK,QAAA,GAAW,OAAO,QAAA;QACvB,IAAA,CAAK,UAAA,GAAa,OAAO,UAAA;IAC1B;IArCA,OAAA,mPAAiB,aAAU,CAAA,GAAY,SAAA;IAI9B,KAAA;IACA,QAAA;IACA,QAAA;IACA,QAAA;IACA,UAAA;IACA,WAAA;IACA,WAAA;IACA,SAAA;IACA,WAAA;IACA,WAAA;IACA,SAAA;IACA,WAAA;IACA,aAA8B,KAAA,EAAA;IAE7B,OAAA;IAuBV,mBAAmB,KAAA,EAAyB;QAC3C,OAAO;IACR;IAEA,iBAAiB,KAAA,EAAyB;QACzC,OAAO;IACR;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/table.ts"], "sourcesContent": ["import type { BuildColumns, BuildExtraConfigColumns } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport { Table, type TableConfig as TableConfigBase, type UpdateTableConfig } from '~/table.ts';\nimport type { CheckBuilder } from './checks.ts';\nimport type { PgColumn, PgColumnBuilder, PgColumnBuilderBase } from './columns/common.ts';\nimport type { ForeignKey, ForeignKeyBuilder } from './foreign-keys.ts';\nimport type { AnyIndexBuilder } from './indexes.ts';\nimport type { PrimaryKeyBuilder } from './primary-keys.ts';\nimport type { UniqueConstraintBuilder } from './unique-constraint.ts';\n\nexport type PgTableExtraConfig = Record<\n\tstring,\n\t| AnyIndexBuilder\n\t| CheckBuilder\n\t| ForeignKeyBuilder\n\t| PrimaryKeyBuilder\n\t| UniqueConstraintBuilder\n>;\n\nexport type TableConfig = TableConfigBase<PgColumn>;\n\n/** @internal */\nexport const InlineForeignKeys = Symbol.for('drizzle:PgInlineForeignKeys');\n\nexport class PgTable<T extends TableConfig = TableConfig> extends Table<T> {\n\tstatic readonly [entityKind]: string = 'PgTable';\n\n\t/** @internal */\n\tstatic override readonly Symbol = Object.assign({}, Table.Symbol, {\n\t\tInlineForeignKeys: InlineForeignKeys as typeof InlineForeignKeys,\n\t});\n\n\t/**@internal */\n\t[InlineForeignKeys]: ForeignKey[] = [];\n\n\t/** @internal */\n\toverride [Table.Symbol.ExtraConfigBuilder]: ((self: Record<string, PgColumn>) => PgTableExtraConfig) | undefined =\n\t\tundefined;\n}\n\nexport type AnyPgTable<TPartial extends Partial<TableConfig> = {}> = PgTable<UpdateTableConfig<TableConfig, TPartial>>;\n\nexport type PgTableWithColumns<T extends TableConfig> =\n\t& PgTable<T>\n\t& {\n\t\t[Key in keyof T['columns']]: T['columns'][Key];\n\t};\n\n/** @internal */\nexport function pgTableWithSchema<\n\tTTableName extends string,\n\tTSchemaName extends string | undefined,\n\tTColumnsMap extends Record<string, PgColumnBuilderBase>,\n>(\n\tname: TTableName,\n\tcolumns: TColumnsMap,\n\textraConfig: ((self: BuildExtraConfigColumns<TTableName, TColumnsMap, 'pg'>) => PgTableExtraConfig) | undefined,\n\tschema: TSchemaName,\n\tbaseName = name,\n): PgTableWithColumns<{\n\tname: TTableName;\n\tschema: TSchemaName;\n\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\tdialect: 'pg';\n}> {\n\tconst rawTable = new PgTable<{\n\t\tname: TTableName;\n\t\tschema: TSchemaName;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\t\tdialect: 'pg';\n\t}>(name, schema, baseName);\n\n\tconst builtColumns = Object.fromEntries(\n\t\tObject.entries(columns).map(([name, colBuilderBase]) => {\n\t\t\tconst colBuilder = colBuilderBase as PgColumnBuilder;\n\t\t\tconst column = colBuilder.build(rawTable);\n\t\t\trawTable[InlineForeignKeys].push(...colBuilder.buildForeignKeys(column, rawTable));\n\t\t\treturn [name, column];\n\t\t}),\n\t) as unknown as BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\n\tconst builtColumnsForExtraConfig = Object.fromEntries(\n\t\tObject.entries(columns).map(([name, colBuilderBase]) => {\n\t\t\tconst colBuilder = colBuilderBase as PgColumnBuilder;\n\t\t\tconst column = colBuilder.buildExtraConfigColumn(rawTable);\n\t\t\treturn [name, column];\n\t\t}),\n\t) as unknown as BuildExtraConfigColumns<TTableName, TColumnsMap, 'pg'>;\n\n\tconst table = Object.assign(rawTable, builtColumns);\n\n\ttable[Table.Symbol.Columns] = builtColumns;\n\ttable[Table.Symbol.ExtraConfigColumns] = builtColumnsForExtraConfig;\n\n\tif (extraConfig) {\n\t\ttable[PgTable.Symbol.ExtraConfigBuilder] = extraConfig as any;\n\t}\n\n\treturn table;\n}\n\nexport interface PgTableFn<TSchema extends string | undefined = undefined> {\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, PgColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: TColumnsMap,\n\t\textraConfig?: (self: BuildExtraConfigColumns<TTableName, TColumnsMap, 'pg'>) => PgTableExtraConfig,\n\t): PgTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\t\tdialect: 'pg';\n\t}>;\n}\n\nexport const pgTable: PgTableFn = (name, columns, extraConfig) => {\n\treturn pgTableWithSchema(name, columns, extraConfig, undefined);\n};\n\nexport function pgTableCreator(customizeTableName: (name: string) => string): PgTableFn {\n\treturn (name, columns, extraConfig) => {\n\t\treturn pgTableWithSchema(customizeTableName(name) as typeof name, columns, extraConfig, undefined, name);\n\t};\n}\n"], "names": ["name"], "mappings": ";;;;;;;AACA,SAAS,kBAAkB;AAC3B,SAAS,aAA0E;;;AAoB5E,MAAM,oBAAoB,OAAO,GAAA,CAAI,6BAA6B;AAElE,MAAM,iQAAqD,QAAA,CAAS;IAC1E,OAAA,mPAAiB,aAAU,CAAA,GAAY,UAAA;IAAA,cAAA,GAGvC,OAAyB,SAAS,OAAO,MAAA,CAAO,CAAC,oPAAG,QAAA,CAAM,MAAA,EAAQ;QACjE;IACD,CAAC,EAAA;IAAA,aAAA,GAGD,CAAC,iBAAiB,CAAA,GAAkB,CAAC,CAAA,CAAA;IAAA,cAAA,GAGrC,kPAAU,QAAA,CAAM,MAAA,CAAO,kBAAkB,CAAA,GACxC,KAAA,EAAA;AACF;AAWO,SAAS,kBAKf,IAAA,EACA,OAAA,EACA,WAAA,EACA,MAAA,EACA,WAAW,IAAA,EAMT;IACF,MAAM,WAAW,IAAI,QAKlB,MAAM,QAAQ,QAAQ;IAEzB,MAAM,eAAe,OAAO,WAAA,CAC3B,OAAO,OAAA,CAAQ,OAAO,EAAE,GAAA,CAAI,CAAC,CAACA,OAAM,cAAc,CAAA,KAAM;QACvD,MAAM,aAAa;QACnB,MAAM,SAAS,WAAW,KAAA,CAAM,QAAQ;QACxC,QAAA,CAAS,iBAAiB,CAAA,CAAE,IAAA,CAAK,GAAG,WAAW,gBAAA,CAAiB,QAAQ,QAAQ,CAAC;QACjF,OAAO;YAACA;YAAM,MAAM;SAAA;IACrB,CAAC;IAGF,MAAM,6BAA6B,OAAO,WAAA,CACzC,OAAO,OAAA,CAAQ,OAAO,EAAE,GAAA,CAAI,CAAC,CAACA,OAAM,cAAc,CAAA,KAAM;QACvD,MAAM,aAAa;QACnB,MAAM,SAAS,WAAW,sBAAA,CAAuB,QAAQ;QACzD,OAAO;YAACA;YAAM,MAAM;SAAA;IACrB,CAAC;IAGF,MAAM,QAAQ,OAAO,MAAA,CAAO,UAAU,YAAY;IAElD,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA,GAAI;IAC9B,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,kBAAkB,CAAA,GAAI;IAEzC,IAAI,aAAa;QAChB,KAAA,CAAM,QAAQ,MAAA,CAAO,kBAAkB,CAAA,GAAI;IAC5C;IAEA,OAAO;AACR;AAkBO,MAAM,UAAqB,CAAC,MAAM,SAAS,gBAAgB;IACjE,OAAO,kBAAkB,MAAM,SAAS,aAAa,KAAA,CAAS;AAC/D;AAEO,SAAS,eAAe,kBAAA,EAAyD;IACvF,OAAO,CAAC,MAAM,SAAS,gBAAgB;QACtC,OAAO,kBAAkB,mBAAmB,IAAI,GAAkB,SAAS,aAAa,KAAA,GAAW,IAAI;IACxG;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/primary-keys.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { AnyPgColumn, PgColumn } from './columns/index.ts';\nimport { PgTable } from './table.ts';\n\nexport function primaryKey<\n\tTTableName extends string,\n\tTColumn extends AnyPgColumn<{ tableName: TTableName }>,\n\tTColumns extends AnyPgColumn<{ tableName: TTableName }>[],\n>(config: { name?: string; columns: [TColumn, ...TColumns] }): PrimaryKeyBuilder;\n/**\n * @deprecated: Please use primaryKey({ columns: [] }) instead of this function\n * @param columns\n */\nexport function primaryKey<\n\tTTableName extends string,\n\tTColumns extends AnyPgColumn<{ tableName: TTableName }>[],\n>(...columns: TColumns): PrimaryKeyBuilder;\nexport function primaryKey(...config: any) {\n\tif (config[0].columns) {\n\t\treturn new PrimaryKeyBuilder(config[0].columns, config[0].name);\n\t}\n\treturn new PrimaryKeyBuilder(config);\n}\n\nexport class PrimaryKeyBuilder {\n\tstatic readonly [entityKind]: string = 'PgPrimaryKeyBuilder';\n\n\t/** @internal */\n\tcolumns: PgColumn[];\n\n\t/** @internal */\n\tname?: string;\n\n\tconstructor(\n\t\tcolumns: PgColumn[],\n\t\tname?: string,\n\t) {\n\t\tthis.columns = columns;\n\t\tthis.name = name;\n\t}\n\n\t/** @internal */\n\tbuild(table: PgTable): PrimaryKey {\n\t\treturn new PrimaryKey(table, this.columns, this.name);\n\t}\n}\n\nexport class PrimaryKey {\n\tstatic readonly [entityKind]: string = 'PgPrimaryKey';\n\n\treadonly columns: AnyPgColumn<{}>[];\n\treadonly name?: string;\n\n\tconstructor(readonly table: PgTable, columns: AnyPgColumn<{}>[], name?: string) {\n\t\tthis.columns = columns;\n\t\tthis.name = name;\n\t}\n\n\tgetName(): string {\n\t\treturn this.name ?? `${this.table[PgTable.Symbol.Name]}_${this.columns.map((column) => column.name).join('_')}_pk`;\n\t}\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,kBAAkB;AAE3B,SAAS,eAAe;;;AAejB,SAAS,WAAA,GAAc,MAAA,EAAa;IAC1C,IAAI,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,EAAS;QACtB,OAAO,IAAI,kBAAkB,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,EAAS,MAAA,CAAO,CAAC,CAAA,CAAE,IAAI;IAC/D;IACA,OAAO,IAAI,kBAAkB,MAAM;AACpC;AAEO,MAAM,kBAAkB;IAC9B,OAAA,mPAAiB,aAAU,CAAA,GAAY,sBAAA;IAAA,cAAA,GAGvC,QAAA;IAAA,cAAA,GAGA,KAAA;IAEA,YACC,OAAA,EACA,IAAA,CACC;QACD,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,IAAA,GAAO;IACb;IAAA,cAAA,GAGA,MAAM,KAAA,EAA4B;QACjC,OAAO,IAAI,WAAW,OAAO,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,IAAI;IACrD;AACD;AAEO,MAAM,WAAW;IAMvB,YAAqB,KAAA,EAAgB,OAAA,EAA4B,IAAA,CAAe;QAA3D,IAAA,CAAA,KAAA,GAAA;QACpB,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,IAAA,GAAO;IACb;IARA,OAAA,mPAAiB,aAAU,CAAA,GAAY,eAAA;IAE9B,QAAA;IACA,KAAA;IAOT,UAAkB;QACjB,OAAO,IAAA,CAAK,IAAA,IAAQ,GAAG,IAAA,CAAK,KAAA,gQAAM,UAAA,CAAQ,MAAA,CAAO,IAAI,CAAC,CAAA,CAAA,EAAI,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,CAAC,SAAW,OAAO,IAAI,EAAE,IAAA,CAAK,GAAG,CAAC,CAAA,GAAA,CAAA;IAC9G;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/column-builder.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { Column } from './column.ts';\nimport type { MySqlColumn } from './mysql-core/index.ts';\nimport type { ExtraConfigColumn, PgColumn } from './pg-core/index.ts';\nimport type { SQL } from './sql/sql.ts';\nimport type { SQLiteColumn } from './sqlite-core/index.ts';\nimport type { Simplify } from './utils.ts';\n\nexport type ColumnDataType =\n\t| 'string'\n\t| 'number'\n\t| 'boolean'\n\t| 'array'\n\t| 'json'\n\t| 'date'\n\t| 'bigint'\n\t| 'custom'\n\t| 'buffer';\n\nexport type Dialect = 'pg' | 'mysql' | 'sqlite' | 'common';\n\nexport interface ColumnBuilderBaseConfig<TDataType extends ColumnDataType, TColumnType extends string> {\n\tname: string;\n\tdataType: TDataType;\n\tcolumnType: TColumnType;\n\tdata: unknown;\n\tdriverParam: unknown;\n\tenumValues: string[] | undefined;\n}\n\nexport type MakeColumnConfig<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTableName extends string,\n> = {\n\tname: T['name'];\n\ttableName: TTableName;\n\tdataType: T['dataType'];\n\tcolumnType: T['columnType'];\n\tdata: T extends { $type: infer U } ? U : T['data'];\n\tdriverParam: T['driverParam'];\n\tnotNull: T extends { notNull: true } ? true : false;\n\thasDefault: T extends { hasDefault: true } ? true : false;\n\tenumValues: T['enumValues'];\n\tbaseColumn: T extends { baseBuilder: infer U extends ColumnBuilderBase } ? BuildColumn<TTableName, U, 'common'>\n\t\t: never;\n} & {};\n\nexport type ColumnBuilderTypeConfig<\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> = Simplify<\n\t& {\n\t\tbrand: 'ColumnBuilder';\n\t\tname: T['name'];\n\t\tdataType: T['dataType'];\n\t\tcolumnType: T['columnType'];\n\t\tdata: T['data'];\n\t\tdriverParam: T['driverParam'];\n\t\tnotNull: T extends { notNull: infer U } ? U : boolean;\n\t\thasDefault: T extends { hasDefault: infer U } ? U : boolean;\n\t\tenumValues: T['enumValues'];\n\t}\n\t& TTypeConfig\n>;\n\nexport type ColumnBuilderRuntimeConfig<TData, TRuntimeConfig extends object = object> = {\n\tname: string;\n\tnotNull: boolean;\n\tdefault: TData | SQL | undefined;\n\tdefaultFn: (() => TData | SQL) | undefined;\n\tonUpdateFn: (() => TData | SQL) | undefined;\n\thasDefault: boolean;\n\tprimaryKey: boolean;\n\tisUnique: boolean;\n\tuniqueName: string | undefined;\n\tuniqueType: string | undefined;\n\tdataType: string;\n\tcolumnType: string;\n} & TRuntimeConfig;\n\nexport interface ColumnBuilderExtraConfig {\n\tprimaryKeyHasDefault?: boolean;\n}\n\nexport type NotNull<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\tnotNull: true;\n\t};\n};\n\nexport type HasDefault<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\thasDefault: true;\n\t};\n};\n\nexport type $Type<T extends ColumnBuilderBase, TType> = T & {\n\t_: {\n\t\t$type: TType;\n\t};\n};\n\nexport interface ColumnBuilderBase<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> {\n\t_: ColumnBuilderTypeConfig<T, TTypeConfig>;\n}\n\n// To understand how to use `ColumnBuilder` and `AnyColumnBuilder`, see `Column` and `AnyColumn` documentation.\nexport abstract class ColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = ColumnBuilderExtraConfig,\n> implements ColumnBuilderBase<T, TTypeConfig> {\n\tstatic readonly [entityKind]: string = 'ColumnBuilder';\n\n\tdeclare _: ColumnBuilderTypeConfig<T, TTypeConfig>;\n\n\tprotected config: ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>;\n\n\tconstructor(name: T['name'], dataType: T['dataType'], columnType: T['columnType']) {\n\t\tthis.config = {\n\t\t\tname,\n\t\t\tnotNull: false,\n\t\t\tdefault: undefined,\n\t\t\thasDefault: false,\n\t\t\tprimaryKey: false,\n\t\t\tisUnique: false,\n\t\t\tuniqueName: undefined,\n\t\t\tuniqueType: undefined,\n\t\t\tdataType,\n\t\t\tcolumnType,\n\t\t} as ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>;\n\t}\n\n\t/**\n\t * Changes the data type of the column. Commonly used with `json` columns. Also, useful for branded types.\n\t *\n\t * @example\n\t * ```ts\n\t * const users = pgTable('users', {\n\t * \tid: integer('id').$type<UserId>().primaryKey(),\n\t * \tdetails: json('details').$type<UserDetails>().notNull(),\n\t * });\n\t * ```\n\t */\n\t$type<TType>(): $Type<this, TType> {\n\t\treturn this as $Type<this, TType>;\n\t}\n\n\t/**\n\t * Adds a `not null` clause to the column definition.\n\t *\n\t * Affects the `select` model of the table - columns *without* `not null` will be nullable on select.\n\t */\n\tnotNull(): NotNull<this> {\n\t\tthis.config.notNull = true;\n\t\treturn this as NotNull<this>;\n\t}\n\n\t/**\n\t * Adds a `default <value>` clause to the column definition.\n\t *\n\t * Affects the `insert` model of the table - columns *with* `default` are optional on insert.\n\t *\n\t * If you need to set a dynamic default value, use {@link $defaultFn} instead.\n\t */\n\tdefault(value: (this['_'] extends { $type: infer U } ? U : this['_']['data']) | SQL): HasDefault<this> {\n\t\tthis.config.default = value;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as HasDefault<this>;\n\t}\n\n\t/**\n\t * Adds a dynamic default value to the column.\n\t * The function will be called when the row is inserted, and the returned value will be used as the column value.\n\t *\n\t * **Note:** This value does not affect the `drizzle-kit` behavior, it is only used at runtime in `drizzle-orm`.\n\t */\n\t$defaultFn(\n\t\tfn: () => (this['_'] extends { $type: infer U } ? U : this['_']['data']) | SQL,\n\t): HasDefault<this> {\n\t\tthis.config.defaultFn = fn;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as HasDefault<this>;\n\t}\n\n\t/**\n\t * Alias for {@link $defaultFn}.\n\t */\n\t$default = this.$defaultFn;\n\n\t/**\n\t * Adds a dynamic update value to the column.\n\t * The function will be called when the row is updated, and the returned value will be used as the column value if none is provided.\n\t * If no `default` (or `$defaultFn`) value is provided, the function will be called when the row is inserted as well, and the returned value will be used as the column value.\n\t *\n\t * **Note:** This value does not affect the `drizzle-kit` behavior, it is only used at runtime in `drizzle-orm`.\n\t */\n\t$onUpdateFn(\n\t\tfn: () => (this['_'] extends { $type: infer U } ? U : this['_']['data']) | SQL,\n\t): HasDefault<this> {\n\t\tthis.config.onUpdateFn = fn;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as HasDefault<this>;\n\t}\n\n\t/**\n\t * Alias for {@link $onUpdateFn}.\n\t */\n\t$onUpdate = this.$onUpdateFn;\n\n\t/**\n\t * Adds a `primary key` clause to the column definition. This implicitly makes the column `not null`.\n\t *\n\t * In SQLite, `integer primary key` implicitly makes the column auto-incrementing.\n\t */\n\tprimaryKey(): TExtraConfig['primaryKeyHasDefault'] extends true ? HasDefault<NotNull<this>> : NotNull<this> {\n\t\tthis.config.primaryKey = true;\n\t\tthis.config.notNull = true;\n\t\treturn this as TExtraConfig['primaryKeyHasDefault'] extends true ? HasDefault<NotNull<this>> : NotNull<this>;\n\t}\n}\n\nexport type BuildColumn<\n\tTTableName extends string,\n\tTBuilder extends ColumnBuilderBase,\n\tTDialect extends Dialect,\n> = TDialect extends 'pg' ? PgColumn<MakeColumnConfig<TBuilder['_'], TTableName>>\n\t: TDialect extends 'mysql' ? MySqlColumn<MakeColumnConfig<TBuilder['_'], TTableName>>\n\t: TDialect extends 'sqlite' ? SQLiteColumn<MakeColumnConfig<TBuilder['_'], TTableName>>\n\t: TDialect extends 'common' ? Column<MakeColumnConfig<TBuilder['_'], TTableName>>\n\t: never;\n\nexport type BuildIndexColumn<\n\tTDialect extends Dialect,\n> = TDialect extends 'pg' ? ExtraConfigColumn : never;\n\n// TODO\n// try to make sql as well + indexRaw\n\n// optional after everything will be working as expected\n// also try to leave only needed methods for extraConfig\n// make an error if I pass .asc() to fk and so on\n\nexport type BuildColumns<\n\tTTableName extends string,\n\tTConfigMap extends Record<string, ColumnBuilderBase>,\n\tTDialect extends Dialect,\n> =\n\t& {\n\t\t[Key in keyof TConfigMap]: BuildColumn<TTableName, TConfigMap[Key], TDialect>;\n\t}\n\t& {};\n\nexport type BuildExtraConfigColumns<\n\t_TTableName extends string,\n\tTConfigMap extends Record<string, ColumnBuilderBase>,\n\tTDialect extends Dialect,\n> =\n\t& {\n\t\t[Key in keyof TConfigMap]: BuildIndexColumn<TDialect>;\n\t}\n\t& {};\n\nexport type ChangeColumnTableName<TColumn extends Column, TAlias extends string, TDialect extends Dialect> =\n\tTDialect extends 'pg' ? PgColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: TDialect extends 'mysql' ? MySqlColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: TDialect extends 'sqlite' ? SQLiteColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: never;\n"], "names": [], "mappings": ";;;AAAA,SAAS,kBAAkB;;AA+GpB,MAAe,cAKyB;IAC9C,OAAA,mPAAiB,aAAU,CAAA,GAAY,gBAAA;IAI7B,OAAA;IAEV,YAAY,IAAA,EAAiB,QAAA,EAAyB,UAAA,CAA6B;QAClF,IAAA,CAAK,MAAA,GAAS;YACb;YACA,SAAS;YACT,SAAS,KAAA;YACT,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,YAAY,KAAA;YACZ,YAAY,KAAA;YACZ;YACA;QACD;IACD;IAAA;;;;;;;;;;GAAA,GAaA,QAAmC;QAClC,OAAO,IAAA;IACR;IAAA;;;;GAAA,GAOA,UAAyB;QACxB,IAAA,CAAK,MAAA,CAAO,OAAA,GAAU;QACtB,OAAO,IAAA;IACR;IAAA;;;;;;GAAA,GASA,QAAQ,KAAA,EAA+F;QACtG,IAAA,CAAK,MAAA,CAAO,OAAA,GAAU;QACtB,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;QACzB,OAAO,IAAA;IACR;IAAA;;;;;GAAA,GAQA,WACC,EAAA,EACmB;QACnB,IAAA,CAAK,MAAA,CAAO,SAAA,GAAY;QACxB,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;QACzB,OAAO,IAAA;IACR;IAAA;;GAAA,GAKA,WAAW,IAAA,CAAK,UAAA,CAAA;IAAA;;;;;;GAAA,GAShB,YACC,EAAA,EACmB;QACnB,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;QACzB,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;QACzB,OAAO,IAAA;IACR;IAAA;;GAAA,GAKA,YAAY,IAAA,CAAK,WAAA,CAAA;IAAA;;;;GAAA,GAOjB,aAA4G;QAC3G,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;QACzB,IAAA,CAAK,MAAA,CAAO,OAAA,GAAU;QACtB,OAAO,IAAA;IACR;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/foreign-keys.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { AnyPgColumn, PgColumn } from './columns/index.ts';\nimport { PgTable } from './table.ts';\n\nexport type UpdateDeleteAction = 'cascade' | 'restrict' | 'no action' | 'set null' | 'set default';\n\nexport type Reference = () => {\n\treadonly name?: string;\n\treadonly columns: PgColumn[];\n\treadonly foreignTable: PgTable;\n\treadonly foreignColumns: PgColumn[];\n};\n\nexport class ForeignKeyBuilder {\n\tstatic readonly [entityKind]: string = 'PgForeignKeyBuilder';\n\n\t/** @internal */\n\treference: Reference;\n\n\t/** @internal */\n\t_onUpdate: UpdateDeleteAction | undefined = 'no action';\n\n\t/** @internal */\n\t_onDelete: UpdateDeleteAction | undefined = 'no action';\n\n\tconstructor(\n\t\tconfig: () => {\n\t\t\tname?: string;\n\t\t\tcolumns: PgColumn[];\n\t\t\tforeignColumns: PgColumn[];\n\t\t},\n\t\tactions?: {\n\t\t\tonUpdate?: UpdateDeleteAction;\n\t\t\tonDelete?: UpdateDeleteAction;\n\t\t} | undefined,\n\t) {\n\t\tthis.reference = () => {\n\t\t\tconst { name, columns, foreignColumns } = config();\n\t\t\treturn { name, columns, foreignTable: foreignColumns[0]!.table as PgTable, foreignColumns };\n\t\t};\n\t\tif (actions) {\n\t\t\tthis._onUpdate = actions.onUpdate;\n\t\t\tthis._onDelete = actions.onDelete;\n\t\t}\n\t}\n\n\tonUpdate(action: UpdateDeleteAction): this {\n\t\tthis._onUpdate = action === undefined ? 'no action' : action;\n\t\treturn this;\n\t}\n\n\tonDelete(action: UpdateDeleteAction): this {\n\t\tthis._onDelete = action === undefined ? 'no action' : action;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: PgTable): ForeignKey {\n\t\treturn new ForeignKey(table, this);\n\t}\n}\n\nexport type AnyForeignKeyBuilder = ForeignKeyBuilder;\n\nexport class ForeignKey {\n\tstatic readonly [entityKind]: string = 'PgForeignKey';\n\n\treadonly reference: Reference;\n\treadonly onUpdate: UpdateDeleteAction | undefined;\n\treadonly onDelete: UpdateDeleteAction | undefined;\n\n\tconstructor(readonly table: PgTable, builder: ForeignKeyBuilder) {\n\t\tthis.reference = builder.reference;\n\t\tthis.onUpdate = builder._onUpdate;\n\t\tthis.onDelete = builder._onDelete;\n\t}\n\n\tgetName(): string {\n\t\tconst { name, columns, foreignColumns } = this.reference();\n\t\tconst columnNames = columns.map((column) => column.name);\n\t\tconst foreignColumnNames = foreignColumns.map((column) => column.name);\n\t\tconst chunks = [\n\t\t\tthis.table[PgTable.Symbol.Name],\n\t\t\t...columnNames,\n\t\t\tforeignColumns[0]!.table[PgTable.Symbol.Name],\n\t\t\t...foreignColumnNames,\n\t\t];\n\t\treturn name ?? `${chunks.join('_')}_fk`;\n\t}\n}\n\ntype ColumnsWithTable<\n\tTTableName extends string,\n\tTColumns extends PgColumn[],\n> = { [Key in keyof TColumns]: AnyPgColumn<{ tableName: TTableName }> };\n\nexport function foreignKey<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends [AnyPgColumn<{ tableName: TTableName }>, ...AnyPgColumn<{ tableName: TTableName }>[]],\n>(\n\tconfig: {\n\t\tname?: string;\n\t\tcolumns: TColumns;\n\t\tforeignColumns: ColumnsWithTable<TForeignTableName, TColumns>;\n\t},\n): ForeignKeyBuilder {\n\tfunction mappedConfig() {\n\t\tconst { name, columns, foreignColumns } = config;\n\t\treturn {\n\t\t\tname,\n\t\t\tcolumns,\n\t\t\tforeignColumns,\n\t\t};\n\t}\n\n\treturn new ForeignKeyBuilder(mappedConfig);\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,kBAAkB;AAE3B,SAAS,eAAe;;;AAWjB,MAAM,kBAAkB;IAC9B,OAAA,mPAAiB,aAAU,CAAA,GAAY,sBAAA;IAAA,cAAA,GAGvC,UAAA;IAAA,cAAA,GAGA,YAA4C,YAAA;IAAA,cAAA,GAG5C,YAA4C,YAAA;IAE5C,YACC,MAAA,EAKA,OAAA,CAIC;QACD,IAAA,CAAK,SAAA,GAAY,MAAM;YACtB,MAAM,EAAE,IAAA,EAAM,OAAA,EAAS,cAAA,CAAe,CAAA,GAAI,OAAO;YACjD,OAAO;gBAAE;gBAAM;gBAAS,cAAc,cAAA,CAAe,CAAC,CAAA,CAAG,KAAA;gBAAkB;YAAe;QAC3F;QACA,IAAI,SAAS;YACZ,IAAA,CAAK,SAAA,GAAY,QAAQ,QAAA;YACzB,IAAA,CAAK,SAAA,GAAY,QAAQ,QAAA;QAC1B;IACD;IAEA,SAAS,MAAA,EAAkC;QAC1C,IAAA,CAAK,SAAA,GAAY,WAAW,KAAA,IAAY,cAAc;QACtD,OAAO,IAAA;IACR;IAEA,SAAS,MAAA,EAAkC;QAC1C,IAAA,CAAK,SAAA,GAAY,WAAW,KAAA,IAAY,cAAc;QACtD,OAAO,IAAA;IACR;IAAA,cAAA,GAGA,MAAM,KAAA,EAA4B;QACjC,OAAO,IAAI,WAAW,OAAO,IAAI;IAClC;AACD;AAIO,MAAM,WAAW;IAOvB,YAAqB,KAAA,EAAgB,OAAA,CAA4B;QAA5C,IAAA,CAAA,KAAA,GAAA;QACpB,IAAA,CAAK,SAAA,GAAY,QAAQ,SAAA;QACzB,IAAA,CAAK,QAAA,GAAW,QAAQ,SAAA;QACxB,IAAA,CAAK,QAAA,GAAW,QAAQ,SAAA;IACzB;IAVA,OAAA,mPAAiB,aAAU,CAAA,GAAY,eAAA;IAE9B,UAAA;IACA,SAAA;IACA,SAAA;IAQT,UAAkB;QACjB,MAAM,EAAE,IAAA,EAAM,OAAA,EAAS,cAAA,CAAe,CAAA,GAAI,IAAA,CAAK,SAAA,CAAU;QACzD,MAAM,cAAc,QAAQ,GAAA,CAAI,CAAC,SAAW,OAAO,IAAI;QACvD,MAAM,qBAAqB,eAAe,GAAA,CAAI,CAAC,SAAW,OAAO,IAAI;QACrE,MAAM,SAAS;YACd,IAAA,CAAK,KAAA,gQAAM,UAAA,CAAQ,MAAA,CAAO,IAAI,CAAA;eAC3B;YACH,cAAA,CAAe,CAAC,CAAA,CAAG,KAAA,gQAAM,UAAA,CAAQ,MAAA,CAAO,IAAI,CAAA;eACzC;SACJ;QACA,OAAO,QAAQ,GAAG,OAAO,IAAA,CAAK,GAAG,CAAC,CAAA,GAAA,CAAA;IACnC;AACD;AAOO,SAAS,WAKf,MAAA,EAKoB;IACpB,SAAS,eAAe;QACvB,MAAM,EAAE,IAAA,EAAM,OAAA,EAAS,cAAA,CAAe,CAAA,GAAI;QAC1C,OAAO;YACN;YACA;YACA;QACD;IACD;IAEA,OAAO,IAAI,kBAAkB,YAAY;AAC1C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/tracing-utils.ts"], "sourcesContent": ["export function iife<T extends unknown[], U>(fn: (...args: T) => U, ...args: T): U {\n\treturn fn(...args);\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,KAA6B,EAAA,EAAA,GAA0B,IAAA,EAAY;IAClF,OAAO,GAAG,GAAG,IAAI;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/unique-constraint.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { PgColumn } from './columns/index.ts';\nimport { PgTable } from './table.ts';\n\nexport function unique(name?: string): UniqueOnConstraintBuilder {\n\treturn new UniqueOnConstraintBuilder(name);\n}\n\nexport function uniqueKeyName(table: PgTable, columns: string[]) {\n\treturn `${table[PgTable.Symbol.Name]}_${columns.join('_')}_unique`;\n}\n\nexport class UniqueConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'PgUniqueConstraintBuilder';\n\n\t/** @internal */\n\tcolumns: PgColumn[];\n\t/** @internal */\n\tnullsNotDistinctConfig = false;\n\n\tconstructor(\n\t\tcolumns: PgColumn[],\n\t\tprivate name?: string,\n\t) {\n\t\tthis.columns = columns;\n\t}\n\n\tnullsNotDistinct() {\n\t\tthis.nullsNotDistinctConfig = true;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: PgTable): UniqueConstraint {\n\t\treturn new UniqueConstraint(table, this.columns, this.nullsNotDistinctConfig, this.name);\n\t}\n}\n\nexport class UniqueOnConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'PgUniqueOnConstraintBuilder';\n\n\t/** @internal */\n\tname?: string;\n\n\tconstructor(\n\t\tname?: string,\n\t) {\n\t\tthis.name = name;\n\t}\n\n\ton(...columns: [PgColumn, ...PgColumn[]]) {\n\t\treturn new UniqueConstraintBuilder(columns, this.name);\n\t}\n}\n\nexport class UniqueConstraint {\n\tstatic readonly [entityKind]: string = 'PgUniqueConstraint';\n\n\treadonly columns: PgColumn[];\n\treadonly name?: string;\n\treadonly nullsNotDistinct: boolean = false;\n\n\tconstructor(readonly table: PgTable, columns: PgColumn[], nullsNotDistinct: boolean, name?: string) {\n\t\tthis.columns = columns;\n\t\tthis.name = name ?? uniqueKeyName(this.table, this.columns.map((column) => column.name));\n\t\tthis.nullsNotDistinct = nullsNotDistinct;\n\t}\n\n\tgetName() {\n\t\treturn this.name;\n\t}\n}\n"], "names": [], "mappings": ";;;;;;;AAAA,SAAS,kBAAkB;AAE3B,SAAS,eAAe;;;AAEjB,SAAS,OAAO,IAAA,EAA0C;IAChE,OAAO,IAAI,0BAA0B,IAAI;AAC1C;AAEO,SAAS,cAAc,KAAA,EAAgB,OAAA,EAAmB;IAChE,OAAO,GAAG,KAAA,gQAAM,UAAA,CAAQ,MAAA,CAAO,IAAI,CAAC,CAAA,CAAA,EAAI,QAAQ,IAAA,CAAK,GAAG,CAAC,CAAA,OAAA,CAAA;AAC1D;AAEO,MAAM,wBAAwB;IAQpC,YACC,OAAA,EACQ,IAAA,CACP;QADO,IAAA,CAAA,IAAA,GAAA;QAER,IAAA,CAAK,OAAA,GAAU;IAChB;IAZA,OAAA,mPAAiB,aAAU,CAAA,GAAY,4BAAA;IAAA,cAAA,GAGvC,QAAA;IAAA,cAAA,GAEA,yBAAyB,MAAA;IASzB,mBAAmB;QAClB,IAAA,CAAK,sBAAA,GAAyB;QAC9B,OAAO,IAAA;IACR;IAAA,cAAA,GAGA,MAAM,KAAA,EAAkC;QACvC,OAAO,IAAI,iBAAiB,OAAO,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,sBAAA,EAAwB,IAAA,CAAK,IAAI;IACxF;AACD;AAEO,MAAM,0BAA0B;IACtC,OAAA,mPAAiB,aAAU,CAAA,GAAY,8BAAA;IAAA,cAAA,GAGvC,KAAA;IAEA,YACC,IAAA,CACC;QACD,IAAA,CAAK,IAAA,GAAO;IACb;IAEA,GAAA,GAAM,OAAA,EAAoC;QACzC,OAAO,IAAI,wBAAwB,SAAS,IAAA,CAAK,IAAI;IACtD;AACD;AAEO,MAAM,iBAAiB;IAO7B,YAAqB,KAAA,EAAgB,OAAA,EAAqB,gBAAA,EAA2B,IAAA,CAAe;QAA/E,IAAA,CAAA,KAAA,GAAA;QACpB,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,IAAA,GAAO,QAAQ,cAAc,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,CAAC,SAAW,OAAO,IAAI,CAAC;QACvF,IAAA,CAAK,gBAAA,GAAmB;IACzB;IAVA,OAAA,mPAAiB,aAAU,CAAA,GAAY,qBAAA;IAE9B,QAAA;IACA,KAAA;IACA,mBAA4B,MAAA;IAQrC,UAAU;QACT,OAAO,IAAA,CAAK,IAAA;IACb;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/utils/array.ts"], "sourcesContent": ["function parsePgArrayValue(arrayString: string, startFrom: number, inQuotes: boolean): [string, number] {\n\tfor (let i = startFrom; i < arrayString.length; i++) {\n\t\tconst char = arrayString[i];\n\n\t\tif (char === '\\\\') {\n\t\t\ti++;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === '\"') {\n\t\t\treturn [arrayString.slice(startFrom, i).replace(/\\\\/g, ''), i + 1];\n\t\t}\n\n\t\tif (inQuotes) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === ',' || char === '}') {\n\t\t\treturn [arrayString.slice(startFrom, i).replace(/\\\\/g, ''), i];\n\t\t}\n\t}\n\n\treturn [arrayString.slice(startFrom).replace(/\\\\/g, ''), arrayString.length];\n}\n\nexport function parsePgNestedArray(arrayString: string, startFrom = 0): [any[], number] {\n\tconst result: any[] = [];\n\tlet i = startFrom;\n\tlet lastCharIsComma = false;\n\n\twhile (i < arrayString.length) {\n\t\tconst char = arrayString[i];\n\n\t\tif (char === ',') {\n\t\t\tif (lastCharIsComma || i === startFrom) {\n\t\t\t\tresult.push('');\n\t\t\t}\n\t\t\tlastCharIsComma = true;\n\t\t\ti++;\n\t\t\tcontinue;\n\t\t}\n\n\t\tlastCharIsComma = false;\n\n\t\tif (char === '\\\\') {\n\t\t\ti += 2;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === '\"') {\n\t\t\tconst [value, startFrom] = parsePgArrayValue(arrayString, i + 1, true);\n\t\t\tresult.push(value);\n\t\t\ti = startFrom;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === '}') {\n\t\t\treturn [result, i + 1];\n\t\t}\n\n\t\tif (char === '{') {\n\t\t\tconst [value, startFrom] = parsePgNestedArray(arrayString, i + 1);\n\t\t\tresult.push(value);\n\t\t\ti = startFrom;\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst [value, newStartFrom] = parsePgArrayValue(arrayString, i, false);\n\t\tresult.push(value);\n\t\ti = newStartFrom;\n\t}\n\n\treturn [result, i];\n}\n\nexport function parsePgArray(arrayString: string): any[] {\n\tconst [result] = parsePgNestedArray(arrayString, 1);\n\treturn result;\n}\n\nexport function makePgArray(array: any[]): string {\n\treturn `{${\n\t\tarray.map((item) => {\n\t\t\tif (Array.isArray(item)) {\n\t\t\t\treturn makePgArray(item);\n\t\t\t}\n\n\t\t\tif (typeof item === 'string') {\n\t\t\t\treturn `\"${item.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"')}\"`;\n\t\t\t}\n\n\t\t\treturn `${item}`;\n\t\t}).join(',')\n\t}}`;\n}\n"], "names": ["value", "startFrom"], "mappings": ";;;;;AAAA,SAAS,kBAAkB,WAAA,EAAqB,SAAA,EAAmB,QAAA,EAAqC;IACvG,IAAA,IAAS,IAAI,WAAW,IAAI,YAAY,MAAA,EAAQ,IAAK;QACpD,MAAM,OAAO,WAAA,CAAY,CAAC,CAAA;QAE1B,IAAI,SAAS,MAAM;YAClB;YACA;QACD;QAEA,IAAI,SAAS,KAAK;YACjB,OAAO;gBAAC,YAAY,KAAA,CAAM,WAAW,CAAC,EAAE,OAAA,CAAQ,OAAO,EAAE;gBAAG,IAAI,CAAC;aAAA;QAClE;QAEA,IAAI,UAAU;YACb;QACD;QAEA,IAAI,SAAS,OAAO,SAAS,KAAK;YACjC,OAAO;gBAAC,YAAY,KAAA,CAAM,WAAW,CAAC,EAAE,OAAA,CAAQ,OAAO,EAAE;gBAAG,CAAC;aAAA;QAC9D;IACD;IAEA,OAAO;QAAC,YAAY,KAAA,CAAM,SAAS,EAAE,OAAA,CAAQ,OAAO,EAAE;QAAG,YAAY,MAAM;KAAA;AAC5E;AAEO,SAAS,mBAAmB,WAAA,EAAqB,YAAY,CAAA,EAAoB;IACvF,MAAM,SAAgB,CAAC,CAAA;IACvB,IAAI,IAAI;IACR,IAAI,kBAAkB;IAEtB,MAAO,IAAI,YAAY,MAAA,CAAQ;QAC9B,MAAM,OAAO,WAAA,CAAY,CAAC,CAAA;QAE1B,IAAI,SAAS,KAAK;YACjB,IAAI,mBAAmB,MAAM,WAAW;gBACvC,OAAO,IAAA,CAAK,EAAE;YACf;YACA,kBAAkB;YAClB;YACA;QACD;QAEA,kBAAkB;QAElB,IAAI,SAAS,MAAM;YAClB,KAAK;YACL;QACD;QAEA,IAAI,SAAS,KAAK;YACjB,MAAM,CAACA,QAAOC,UAAS,CAAA,GAAI,kBAAkB,aAAa,IAAI,GAAG,IAAI;YACrE,OAAO,IAAA,CAAKD,MAAK;YACjB,IAAIC;YACJ;QACD;QAEA,IAAI,SAAS,KAAK;YACjB,OAAO;gBAAC;gBAAQ,IAAI,CAAC;aAAA;QACtB;QAEA,IAAI,SAAS,KAAK;YACjB,MAAM,CAACD,QAAOC,UAAS,CAAA,GAAI,mBAAmB,aAAa,IAAI,CAAC;YAChE,OAAO,IAAA,CAAKD,MAAK;YACjB,IAAIC;YACJ;QACD;QAEA,MAAM,CAAC,OAAO,YAAY,CAAA,GAAI,kBAAkB,aAAa,GAAG,KAAK;QACrE,OAAO,IAAA,CAAK,KAAK;QACjB,IAAI;IACL;IAEA,OAAO;QAAC;QAAQ,CAAC;KAAA;AAClB;AAEO,SAAS,aAAa,WAAA,EAA4B;IACxD,MAAM,CAAC,MAAM,CAAA,GAAI,mBAAmB,aAAa,CAAC;IAClD,OAAO;AACR;AAEO,SAAS,YAAY,KAAA,EAAsB;IACjD,OAAO,CAAA,CAAA,EACN,MAAM,GAAA,CAAI,CAAC,SAAS;QACnB,IAAI,MAAM,OAAA,CAAQ,IAAI,GAAG;YACxB,OAAO,YAAY,IAAI;QACxB;QAEA,IAAI,OAAO,SAAS,UAAU;YAC7B,OAAO,CAAA,CAAA,EAAI,KAAK,OAAA,CAAQ,OAAO,MAAM,EAAE,OAAA,CAAQ,MAAM,KAAK,CAAC,CAAA,CAAA,CAAA;QAC5D;QAEA,OAAO,GAAG,IAAI,EAAA;IACf,CAAC,EAAE,IAAA,CAAK,GAAG,CACZ,CAAA,CAAA,CAAA;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/columns/common.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBase,\n\tColumnBuilderBaseConfig,\n\tColumnBuilderExtraConfig,\n\tColumnBuilderRuntimeConfig,\n\tColumnDataType,\n\tMakeColumnConfig,\n} from '~/column-builder.ts';\nimport { ColumnBuilder } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { Column } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { Update } from '~/utils.ts';\n\nimport type { ForeignKey, UpdateDeleteAction } from '~/pg-core/foreign-keys.ts';\nimport { ForeignKeyBuilder } from '~/pg-core/foreign-keys.ts';\nimport type { AnyPgTable, PgTable } from '~/pg-core/table.ts';\nimport { iife } from '~/tracing-utils.ts';\nimport type { PgIndexOpClass } from '../indexes.ts';\nimport { uniqueKeyName } from '../unique-constraint.ts';\nimport { makePgArray, parsePgArray } from '../utils/array.ts';\n\nexport interface ReferenceConfig {\n\tref: () => PgColumn;\n\tactions: {\n\t\tonUpdate?: UpdateDeleteAction;\n\t\tonDelete?: UpdateDeleteAction;\n\t};\n}\n\nexport interface PgColumnBuilderBase<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> extends ColumnBuilderBase<T, TTypeConfig & { dialect: 'pg' }> {}\n\nexport abstract class PgColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = ColumnBuilderExtraConfig,\n> extends ColumnBuilder<T, TRuntimeConfig, TTypeConfig & { dialect: 'pg' }, TExtraConfig>\n\timplements PgColumnBuilderBase<T, TTypeConfig>\n{\n\tprivate foreignKeyConfigs: ReferenceConfig[] = [];\n\n\tstatic readonly [entityKind]: string = 'PgColumnBuilder';\n\n\tarray(size?: number): PgArrayBuilder<\n\t\t& {\n\t\t\tname: T['name'];\n\t\t\tdataType: 'array';\n\t\t\tcolumnType: 'PgArray';\n\t\t\tdata: T['data'][];\n\t\t\tdriverParam: T['driverParam'][] | string;\n\t\t\tenumValues: T['enumValues'];\n\t\t}\n\t\t& (T extends { notNull: true } ? { notNull: true } : {})\n\t\t& (T extends { hasDefault: true } ? { hasDefault: true } : {}),\n\t\tT\n\t> {\n\t\treturn new PgArrayBuilder(this.config.name, this as PgColumnBuilder<any, any>, size);\n\t}\n\n\treferences(\n\t\tref: ReferenceConfig['ref'],\n\t\tactions: ReferenceConfig['actions'] = {},\n\t): this {\n\t\tthis.foreignKeyConfigs.push({ ref, actions });\n\t\treturn this;\n\t}\n\n\tunique(\n\t\tname?: string,\n\t\tconfig?: { nulls: 'distinct' | 'not distinct' },\n\t): this {\n\t\tthis.config.isUnique = true;\n\t\tthis.config.uniqueName = name;\n\t\tthis.config.uniqueType = config?.nulls;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuildForeignKeys(column: PgColumn, table: PgTable): ForeignKey[] {\n\t\treturn this.foreignKeyConfigs.map(({ ref, actions }) => {\n\t\t\treturn iife(\n\t\t\t\t(ref, actions) => {\n\t\t\t\t\tconst builder = new ForeignKeyBuilder(() => {\n\t\t\t\t\t\tconst foreignColumn = ref();\n\t\t\t\t\t\treturn { columns: [column], foreignColumns: [foreignColumn] };\n\t\t\t\t\t});\n\t\t\t\t\tif (actions.onUpdate) {\n\t\t\t\t\t\tbuilder.onUpdate(actions.onUpdate);\n\t\t\t\t\t}\n\t\t\t\t\tif (actions.onDelete) {\n\t\t\t\t\t\tbuilder.onDelete(actions.onDelete);\n\t\t\t\t\t}\n\t\t\t\t\treturn builder.build(table);\n\t\t\t\t},\n\t\t\t\tref,\n\t\t\t\tactions,\n\t\t\t);\n\t\t});\n\t}\n\n\t/** @internal */\n\tabstract build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgColumn<MakeColumnConfig<T, TTableName>>;\n\n\t/** @internal */\n\tbuildExtraConfigColumn<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): ExtraConfigColumn {\n\t\treturn new ExtraConfigColumn(table, this.config);\n\t}\n}\n\n// To understand how to use `PgColumn` and `PgColumn`, see `Column` and `AnyColumn` documentation.\nexport abstract class PgColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = {},\n\tTTypeConfig extends object = {},\n> extends Column<T, TRuntimeConfig, TTypeConfig & { dialect: 'pg' }> {\n\tstatic readonly [entityKind]: string = 'PgColumn';\n\n\tconstructor(\n\t\toverride readonly table: PgTable,\n\t\tconfig: ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>,\n\t) {\n\t\tif (!config.uniqueName) {\n\t\t\tconfig.uniqueName = uniqueKeyName(table, [config.name]);\n\t\t}\n\t\tsuper(table, config);\n\t}\n}\n\nexport type IndexedExtraConfigType = { order?: 'asc' | 'desc'; nulls?: 'first' | 'last'; opClass?: string };\n\nexport class ExtraConfigColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n> extends PgColumn<T, IndexedExtraConfigType> {\n\tstatic readonly [entityKind]: string = 'ExtraConfigColumn';\n\n\toverride getSQLType(): string {\n\t\treturn this.getSQLType();\n\t}\n\n\tindexConfig: IndexedExtraConfigType = {\n\t\torder: this.config.order ?? 'asc',\n\t\tnulls: this.config.nulls ?? 'last',\n\t\topClass: this.config.opClass,\n\t};\n\tdefaultConfig: IndexedExtraConfigType = {\n\t\torder: 'asc',\n\t\tnulls: 'last',\n\t\topClass: undefined,\n\t};\n\n\tasc(): Omit<this, 'asc' | 'desc'> {\n\t\tthis.indexConfig.order = 'asc';\n\t\treturn this;\n\t}\n\n\tdesc(): Omit<this, 'asc' | 'desc'> {\n\t\tthis.indexConfig.order = 'desc';\n\t\treturn this;\n\t}\n\n\tnullsFirst(): Omit<this, 'nullsFirst' | 'nullsLast'> {\n\t\tthis.indexConfig.nulls = 'first';\n\t\treturn this;\n\t}\n\n\tnullsLast(): Omit<this, 'nullsFirst' | 'nullsLast'> {\n\t\tthis.indexConfig.nulls = 'last';\n\t\treturn this;\n\t}\n\n\t/**\n\t * ### PostgreSQL documentation quote\n\t *\n\t * > An operator class with optional parameters can be specified for each column of an index.\n\t * The operator class identifies the operators to be used by the index for that column.\n\t * For example, a B-tree index on four-byte integers would use the int4_ops class;\n\t * this operator class includes comparison functions for four-byte integers.\n\t * In practice the default operator class for the column's data type is usually sufficient.\n\t * The main point of having operator classes is that for some data types, there could be more than one meaningful ordering.\n\t * For example, we might want to sort a complex-number data type either by absolute value or by real part.\n\t * We could do this by defining two operator classes for the data type and then selecting the proper class when creating an index.\n\t * More information about operator classes check:\n\t *\n\t * ### Useful links\n\t * https://www.postgresql.org/docs/current/sql-createindex.html\n\t *\n\t * https://www.postgresql.org/docs/current/indexes-opclass.html\n\t *\n\t * https://www.postgresql.org/docs/current/xindex.html\n\t *\n\t * ### Additional types\n\t * If you have the `pg_vector` extension installed in your database, you can use the\n\t * `vector_l2_ops`, `vector_ip_ops`, `vector_cosine_ops`, `vector_l1_ops`, `bit_hamming_ops`, `bit_jaccard_ops`, `halfvec_l2_ops`, `sparsevec_l2_ops` options, which are predefined types.\n\t *\n\t * **You can always specify any string you want in the operator class, in case Drizzle doesn't have it natively in its types**\n\t *\n\t * @param opClass\n\t * @returns\n\t */\n\top(opClass: PgIndexOpClass): Omit<this, 'op'> {\n\t\tthis.indexConfig.opClass = opClass;\n\t\treturn this;\n\t}\n}\n\nexport class IndexedColumn {\n\tstatic readonly [entityKind]: string = 'IndexedColumn';\n\tconstructor(\n\t\tname: string | undefined,\n\t\ttype: string,\n\t\tindexConfig: IndexedExtraConfigType,\n\t) {\n\t\tthis.name = name;\n\t\tthis.type = type;\n\t\tthis.indexConfig = indexConfig;\n\t}\n\n\tname: string | undefined;\n\ttype: string;\n\tindexConfig: IndexedExtraConfigType;\n}\n\nexport type AnyPgColumn<TPartial extends Partial<ColumnBaseConfig<ColumnDataType, string>> = {}> = PgColumn<\n\tRequired<Update<ColumnBaseConfig<ColumnDataType, string>, TPartial>>\n>;\n\nexport class PgArrayBuilder<\n\tT extends ColumnBuilderBaseConfig<'array', 'PgArray'>,\n\tTBase extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n> extends PgColumnBuilder<\n\tT,\n\t{\n\t\tbaseBuilder: PgColumnBuilder<TBase>;\n\t\tsize: number | undefined;\n\t},\n\t{\n\t\tbaseBuilder: PgColumnBuilder<TBase>;\n\t}\n> {\n\tstatic override readonly [entityKind] = 'PgArrayBuilder';\n\n\tconstructor(\n\t\tname: string,\n\t\tbaseBuilder: PgArrayBuilder<T, TBase>['config']['baseBuilder'],\n\t\tsize: number | undefined,\n\t) {\n\t\tsuper(name, 'array', 'PgArray');\n\t\tthis.config.baseBuilder = baseBuilder;\n\t\tthis.config.size = size;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgArray<MakeColumnConfig<T, TTableName>, TBase> {\n\t\tconst baseColumn = this.config.baseBuilder.build(table);\n\t\treturn new PgArray<MakeColumnConfig<T, TTableName>, TBase>(\n\t\t\ttable as AnyPgTable<{ name: MakeColumnConfig<T, TTableName>['tableName'] }>,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t\tbaseColumn,\n\t\t);\n\t}\n}\n\nexport class PgArray<\n\tT extends ColumnBaseConfig<'array', 'PgArray'>,\n\tTBase extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n> extends PgColumn<T> {\n\treadonly size: number | undefined;\n\n\tstatic readonly [entityKind]: string = 'PgArray';\n\n\tconstructor(\n\t\ttable: AnyPgTable<{ name: T['tableName'] }>,\n\t\tconfig: PgArrayBuilder<T, TBase>['config'],\n\t\treadonly baseColumn: PgColumn,\n\t\treadonly range?: [number | undefined, number | undefined],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.size = config.size;\n\t}\n\n\tgetSQLType(): string {\n\t\treturn `${this.baseColumn.getSQLType()}[${typeof this.size === 'number' ? this.size : ''}]`;\n\t}\n\n\toverride mapFromDriverValue(value: unknown[] | string): T['data'] {\n\t\tif (typeof value === 'string') {\n\t\t\t// Thank you node-postgres for not parsing enum arrays\n\t\t\tvalue = parsePgArray(value);\n\t\t}\n\t\treturn value.map((v) => this.baseColumn.mapFromDriverValue(v));\n\t}\n\n\toverride mapToDriverValue(value: unknown[], isNestedArray = false): unknown[] | string {\n\t\tconst a = value.map((v) =>\n\t\t\tv === null\n\t\t\t\t? null\n\t\t\t\t: is(this.baseColumn, PgArray)\n\t\t\t\t? this.baseColumn.mapToDriverValue(v as unknown[], true)\n\t\t\t\t: this.baseColumn.mapToDriverValue(v)\n\t\t);\n\t\tif (isNestedArray) return a;\n\t\treturn makePgArray(a);\n\t}\n}\n"], "names": ["ref", "actions"], "mappings": ";;;;;;;;AAQA,SAAS,qBAAqB;AAE9B,SAAS,cAAc;AACvB,SAAS,YAAY,UAAU;AAI/B,SAAS,yBAAyB;AAElC,SAAS,YAAY;AAErB,SAAS,qBAAqB;AAC9B,SAAS,aAAa,oBAAoB;;;;;;;;AAenC,MAAe,qRAKZ,gBAAA,CAEV;IACS,oBAAuC,CAAC,CAAA,CAAA;IAEhD,OAAA,mPAAiB,aAAU,CAAA,GAAY,kBAAA;IAEvC,MAAM,IAAA,EAYJ;QACD,OAAO,IAAI,eAAe,IAAA,CAAK,MAAA,CAAO,IAAA,EAAM,IAAA,EAAmC,IAAI;IACpF;IAEA,WACC,GAAA,EACA,UAAsC,CAAC,CAAA,EAChC;QACP,IAAA,CAAK,iBAAA,CAAkB,IAAA,CAAK;YAAE;YAAK;QAAQ,CAAC;QAC5C,OAAO,IAAA;IACR;IAEA,OACC,IAAA,EACA,MAAA,EACO;QACP,IAAA,CAAK,MAAA,CAAO,QAAA,GAAW;QACvB,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;QACzB,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa,QAAQ;QACjC,OAAO,IAAA;IACR;IAAA,cAAA,GAGA,iBAAiB,MAAA,EAAkB,KAAA,EAA8B;QAChE,OAAO,IAAA,CAAK,iBAAA,CAAkB,GAAA,CAAI,CAAC,EAAE,GAAA,EAAK,OAAA,CAAQ,CAAA,KAAM;YACvD,uQAAO,OAAA,EACN,CAACA,MAAKC,aAAY;gBACjB,MAAM,UAAU,6QAAI,oBAAA,CAAkB,MAAM;oBAC3C,MAAM,gBAAgBD,KAAI;oBAC1B,OAAO;wBAAE,SAAS;4BAAC,MAAM;yBAAA;wBAAG,gBAAgB;4BAAC,aAAa;yBAAA;oBAAE;gBAC7D,CAAC;gBACD,IAAIC,SAAQ,QAAA,EAAU;oBACrB,QAAQ,QAAA,CAASA,SAAQ,QAAQ;gBAClC;gBACA,IAAIA,SAAQ,QAAA,EAAU;oBACrB,QAAQ,QAAA,CAASA,SAAQ,QAAQ;gBAClC;gBACA,OAAO,QAAQ,KAAA,CAAM,KAAK;YAC3B,GACA,KACA;QAEF,CAAC;IACF;IAAA,cAAA,GAQA,uBACC,KAAA,EACoB;QACpB,OAAO,IAAI,kBAAkB,OAAO,IAAA,CAAK,MAAM;IAChD;AACD;AAGO,MAAe,kQAIZ,UAAA,CAA2D;IAGpE,YACmB,KAAA,EAClB,MAAA,CACC;QACD,IAAI,CAAC,OAAO,UAAA,EAAY;YACvB,OAAO,UAAA,qRAAa,gBAAA,EAAc,OAAO;gBAAC,OAAO,IAAI;aAAC;QACvD;QACA,KAAA,CAAM,OAAO,MAAM;QAND,IAAA,CAAA,KAAA,GAAA;IAOnB;IAVA,OAAA,mPAAiB,aAAU,CAAA,GAAY,WAAA;AAWxC;AAIO,MAAM,0BAEH,SAAoC;IAC7C,OAAA,mPAAiB,aAAU,CAAA,GAAY,oBAAA;IAE9B,aAAqB;QAC7B,OAAO,IAAA,CAAK,UAAA,CAAW;IACxB;IAEA,cAAsC;QACrC,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA,IAAS;QAC5B,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA,IAAS;QAC5B,SAAS,IAAA,CAAK,MAAA,CAAO,OAAA;IACtB,EAAA;IACA,gBAAwC;QACvC,OAAO;QACP,OAAO;QACP,SAAS,KAAA;IACV,EAAA;IAEA,MAAkC;QACjC,IAAA,CAAK,WAAA,CAAY,KAAA,GAAQ;QACzB,OAAO,IAAA;IACR;IAEA,OAAmC;QAClC,IAAA,CAAK,WAAA,CAAY,KAAA,GAAQ;QACzB,OAAO,IAAA;IACR;IAEA,aAAqD;QACpD,IAAA,CAAK,WAAA,CAAY,KAAA,GAAQ;QACzB,OAAO,IAAA;IACR;IAEA,YAAoD;QACnD,IAAA,CAAK,WAAA,CAAY,KAAA,GAAQ;QACzB,OAAO,IAAA;IACR;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA+BA,GAAG,OAAA,EAA2C;QAC7C,IAAA,CAAK,WAAA,CAAY,OAAA,GAAU;QAC3B,OAAO,IAAA;IACR;AACD;AAEO,MAAM,cAAc;IAC1B,OAAA,mPAAiB,aAAU,CAAA,GAAY,gBAAA;IACvC,YACC,IAAA,EACA,IAAA,EACA,WAAA,CACC;QACD,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,WAAA,GAAc;IACpB;IAEA,KAAA;IACA,KAAA;IACA,YAAA;AACD;AAMO,MAAM,uBAGH,gBASR;IACD,OAAA,mPAA0B,aAAU,CAAA,GAAI,iBAAA;IAExC,YACC,IAAA,EACA,WAAA,EACA,IAAA,CACC;QACD,KAAA,CAAM,MAAM,SAAS,SAAS;QAC9B,IAAA,CAAK,MAAA,CAAO,WAAA,GAAc;QAC1B,IAAA,CAAK,MAAA,CAAO,IAAA,GAAO;IACpB;IAAA,cAAA,GAGS,MACR,KAAA,EACkD;QAClD,MAAM,aAAa,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY,KAAA,CAAM,KAAK;QACtD,OAAO,IAAI,QACV,OACA,IAAA,CAAK,MAAA,EACL;IAEF;AACD;AAEO,MAAM,gBAGH,SAAY;IAKrB,YACC,KAAA,EACA,MAAA,EACS,UAAA,EACA,KAAA,CACR;QACD,KAAA,CAAM,OAAO,MAAM;QAHV,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,KAAA,GAAA;QAGT,IAAA,CAAK,IAAA,GAAO,OAAO,IAAA;IACpB;IAZS,KAAA;IAET,OAAA,CAAiB,+PAAU,CAAA,GAAY,UAAA;IAYvC,aAAqB;QACpB,OAAO,GAAG,IAAA,CAAK,UAAA,CAAW,UAAA,CAAW,CAAC,CAAA,CAAA,EAAI,OAAO,IAAA,CAAK,IAAA,KAAS,WAAW,IAAA,CAAK,IAAA,GAAO,EAAE,CAAA,CAAA,CAAA;IACzF;IAES,mBAAmB,KAAA,EAAsC;QACjE,IAAI,OAAO,UAAU,UAAU;YAE9B,oRAAQ,eAAA,EAAa,KAAK;QAC3B;QACA,OAAO,MAAM,GAAA,CAAI,CAAC,IAAM,IAAA,CAAK,UAAA,CAAW,kBAAA,CAAmB,CAAC,CAAC;IAC9D;IAES,iBAAiB,KAAA,EAAkB,gBAAgB,KAAA,EAA2B;QACtF,MAAM,IAAI,MAAM,GAAA,CAAI,CAAC,IACpB,MAAM,OACH,6PACA,KAAA,EAAG,IAAA,CAAK,UAAA,EAAY,OAAO,IAC3B,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,GAAgB,IAAI,IACrD,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,CAAC;QAEtC,IAAI,eAAe,OAAO;QAC1B,mRAAO,cAAA,EAAY,CAAC;IACrB;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 848, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/columns/enum.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport type { Writable } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgEnumColumnBuilderInitial<TName extends string, TValues extends [string, ...string[]]> =\n\tPgEnumColumnBuilder<{\n\t\tname: TName;\n\t\tdataType: 'string';\n\t\tcolumnType: 'PgEnumColumn';\n\t\tdata: TValues[number];\n\t\tenumValues: TValues;\n\t\tdriverParam: string;\n\t}>;\n\nconst isPgEnumSym = Symbol.for('drizzle:isPgEnum');\nexport interface PgEnum<TValues extends [string, ...string[]]> {\n\t<TName extends string>(name: TName): PgEnumColumnBuilderInitial<TName, TValues>;\n\n\treadonly enumName: string;\n\treadonly enumValues: TValues;\n\treadonly schema: string | undefined;\n\t/** @internal */\n\t[isPgEnumSym]: true;\n}\n\nexport function isPgEnum(obj: unknown): obj is PgEnum<[string, ...string[]]> {\n\treturn !!obj && typeof obj === 'function' && isPgEnumSym in obj && obj[isPgEnumSym] === true;\n}\n\nexport class PgEnumColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'PgEnumColumn'> & { enumValues: [string, ...string[]] },\n> extends PgColumnBuilder<T, { enum: PgEnum<T['enumValues']> }> {\n\tstatic readonly [entityKind]: string = 'PgEnumColumnBuilder';\n\n\tconstructor(name: string, enumInstance: PgEnum<T['enumValues']>) {\n\t\tsuper(name, 'string', 'PgEnumColumn');\n\t\tthis.config.enum = enumInstance;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgEnumColumn<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgEnumColumn<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgEnumColumn<T extends ColumnBaseConfig<'string', 'PgEnumColumn'> & { enumValues: [string, ...string[]] }>\n\textends PgColumn<T, { enum: PgEnum<T['enumValues']> }>\n{\n\tstatic readonly [entityKind]: string = 'PgEnumColumn';\n\n\treadonly enum = this.config.enum;\n\toverride readonly enumValues = this.config.enum.enumValues;\n\n\tconstructor(\n\t\ttable: AnyPgTable<{ name: T['tableName'] }>,\n\t\tconfig: PgEnumColumnBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.enum = config.enum;\n\t}\n\n\tgetSQLType(): string {\n\t\treturn this.enum.enumName;\n\t}\n}\n\n// Gratitude to zod for the enum function types\nexport function pgEnum<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tenumName: string,\n\tvalues: T | Writable<T>,\n): PgEnum<Writable<T>> {\n\treturn pgEnumWithSchema(enumName, values, undefined);\n}\n\n/** @internal */\nexport function pgEnumWithSchema<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tenumName: string,\n\tvalues: T | Writable<T>,\n\tschema?: string,\n): PgEnum<Writable<T>> {\n\tconst enumInstance: PgEnum<Writable<T>> = Object.assign(\n\t\t<TName extends string>(name: TName): PgEnumColumnBuilderInitial<TName, Writable<T>> =>\n\t\t\tnew PgEnumColumnBuilder(name, enumInstance),\n\t\t{\n\t\t\tenumName,\n\t\t\tenumValues: values,\n\t\t\tschema,\n\t\t\t[isPgEnumSym]: true,\n\t\t} as const,\n\t);\n\n\treturn enumInstance;\n}\n"], "names": [], "mappings": ";;;;;;;AAEA,SAAS,kBAAkB;AAG3B,SAAS,UAAU,uBAAuB;;;AAY1C,MAAM,cAAc,OAAO,GAAA,CAAI,kBAAkB;AAW1C,SAAS,SAAS,GAAA,EAAoD;IAC5E,OAAO,CAAC,CAAC,OAAO,OAAO,QAAQ,cAAc,eAAe,OAAO,GAAA,CAAI,WAAW,CAAA,KAAM;AACzF;AAEO,MAAM,uSAEH,kBAAA,CAAsD;IAC/D,OAAA,mPAAiB,aAAU,CAAA,GAAY,sBAAA;IAEvC,YAAY,IAAA,EAAc,YAAA,CAAuC;QAChE,KAAA,CAAM,MAAM,UAAU,cAAc;QACpC,IAAA,CAAK,MAAA,CAAO,IAAA,GAAO;IACpB;IAAA,cAAA,GAGS,MACR,KAAA,EACgD;QAChD,OAAO,IAAI,aACV,OACA,IAAA,CAAK,MAAA;IAEP;AACD;AAEO,MAAM,gSACJ,WAAA,CACT;IACC,OAAA,mPAAiB,aAAU,CAAA,GAAY,eAAA;IAE9B,OAAO,IAAA,CAAK,MAAA,CAAO,IAAA,CAAA;IACV,aAAa,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,UAAA,CAAA;IAEhD,YACC,KAAA,EACA,MAAA,CACC;QACD,KAAA,CAAM,OAAO,MAAM;QACnB,IAAA,CAAK,IAAA,GAAO,OAAO,IAAA;IACpB;IAEA,aAAqB;QACpB,OAAO,IAAA,CAAK,IAAA,CAAK,QAAA;IAClB;AACD;AAGO,SAAS,OACf,QAAA,EACA,MAAA,EACsB;IACtB,OAAO,iBAAiB,UAAU,QAAQ,KAAA,CAAS;AACpD;AAGO,SAAS,iBACf,QAAA,EACA,MAAA,EACA,MAAA,EACsB;IACtB,MAAM,eAAoC,OAAO,MAAA,CAChD,CAAuB,OACtB,IAAI,oBAAoB,MAAM,YAAY,GAC3C;QACC;QACA,YAAY;QACZ;QACA,CAAC,WAAW,CAAA,EAAG;IAChB;IAGD,OAAO;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 905, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/subquery.ts"], "sourcesContent": ["import { entityKind } from './entity.ts';\nimport type { SQL, SQLWrapper } from './sql/sql.ts';\n\nexport interface Subquery<\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTAlias extends string = string,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTSelectedFields extends Record<string, unknown> = Record<string, unknown>,\n> extends SQLWrapper {\n\t// SQLWrapper runtime implementation is defined in 'sql/sql.ts'\n}\nexport class Subquery<\n\tTAlias extends string = string,\n\tTSelectedFields extends Record<string, unknown> = Record<string, unknown>,\n> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Subquery';\n\n\tdeclare _: {\n\t\tbrand: 'Subquery';\n\t\tsql: SQL;\n\t\tselectedFields: TSelectedFields;\n\t\talias: TAlias;\n\t\tisWith: boolean;\n\t};\n\n\tconstructor(sql: SQL, selection: Record<string, unknown>, alias: string, isWith = false) {\n\t\tthis._ = {\n\t\t\tbrand: 'Subquery',\n\t\t\tsql,\n\t\t\tselectedFields: selection as TSelectedFields,\n\t\t\talias: alias as <PERSON><PERSON><PERSON><PERSON>,\n\t\t\tisWith,\n\t\t};\n\t}\n\n\t// getSQL(): SQL<unknown> {\n\t// \treturn new SQL([this]);\n\t// }\n}\n\nexport class WithSubquery<\n\tTAlias extends string = string,\n\tTSelection extends Record<string, unknown> = Record<string, unknown>,\n> extends Subquery<TAlias, TSelection> {\n\tstatic readonly [entityKind]: string = 'WithSubquery';\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,kBAAkB;;AAWpB,MAAM,SAGW;IACvB,OAAA,mPAAiB,aAAU,CAAA,GAAY,WAAA;IAUvC,YAAY,GAAA,EAAU,SAAA,EAAoC,KAAA,EAAe,SAAS,KAAA,CAAO;QACxF,IAAA,CAAK,CAAA,GAAI;YACR,OAAO;YACP;YACA,gBAAgB;YAChB;YACA;QACD;IACD;AAKD;AAEO,MAAM,qBAGH,SAA6B;IACtC,OAAA,mPAAiB,aAAU,CAAA,GAAY,eAAA;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 934, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/drizzle-orm/version.js"], "sourcesContent": ["// package.json\nvar version = \"0.31.4\";\n\n// src/version.ts\nvar compatibilityVersion = 7;\nexport {\n  compatibilityVersion,\n  version as npmVersion\n};\n"], "names": [], "mappings": "AAAA,eAAe;;;;;AACf,IAAI,UAAU;AAEd,iBAAiB;AACjB,IAAI,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/tracing.ts"], "sourcesContent": ["import type { Span, Tracer } from '@opentelemetry/api';\nimport { iife } from '~/tracing-utils.ts';\nimport { npmVersion } from '~/version.ts';\n\nlet otel: typeof import('@opentelemetry/api') | undefined;\nlet rawTracer: Tracer | undefined;\n// try {\n// \totel = await import('@opentelemetry/api');\n// } catch (err: any) {\n// \tif (err.code !== 'MODULE_NOT_FOUND' && err.code !== 'ERR_MODULE_NOT_FOUND') {\n// \t\tthrow err;\n// \t}\n// }\n\ntype SpanName =\n\t| 'drizzle.operation'\n\t| 'drizzle.prepareQuery'\n\t| 'drizzle.buildSQL'\n\t| 'drizzle.execute'\n\t| 'drizzle.driver.execute'\n\t| 'drizzle.mapResponse';\n\n/** @internal */\nexport const tracer = {\n\tstartActiveSpan<F extends (span?: Span) => unknown>(name: SpanName, fn: F): ReturnType<F> {\n\t\tif (!otel) {\n\t\t\treturn fn() as ReturnType<F>;\n\t\t}\n\n\t\tif (!rawTracer) {\n\t\t\trawTracer = otel.trace.getTracer('drizzle-orm', npmVersion);\n\t\t}\n\n\t\treturn iife(\n\t\t\t(otel, rawTracer) =>\n\t\t\t\trawTracer.startActiveSpan(\n\t\t\t\t\tname,\n\t\t\t\t\t((span: Span) => {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\treturn fn(span);\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tspan.setStatus({\n\t\t\t\t\t\t\t\tcode: otel.SpanStatusCode.ERROR,\n\t\t\t\t\t\t\t\tmessage: e instanceof Error ? e.message : 'Unknown error', // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthrow e;\n\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\tspan.end();\n\t\t\t\t\t\t}\n\t\t\t\t\t}) as F,\n\t\t\t\t),\n\t\t\totel,\n\t\t\trawTracer,\n\t\t);\n\t},\n};\n"], "names": ["otel", "rawTracer"], "mappings": ";;;AACA,SAAS,YAAY;AACrB,SAAS,kBAAkB;;;AAE3B,IAAI;AACJ,IAAI;AAkBG,MAAM,SAAS;IACrB,iBAAoD,IAAA,EAAgB,EAAA,EAAsB;QACzF,IAAI,CAAC,MAAM;YACV,OAAO,GAAG;QACX;QAEA,IAAI,CAAC,WAAW;YACf,YAAY,KAAK,KAAA,CAAM,SAAA,CAAU,kQAAe,aAAU;QAC3D;QAEA,uQAAO,OAAA,EACN,CAACA,OAAMC,aACNA,WAAU,eAAA,CACT,MACC,CAAC,SAAe;gBAChB,IAAI;oBACH,OAAO,GAAG,IAAI;gBACf,EAAA,OAAS,GAAG;oBACX,KAAK,SAAA,CAAU;wBACd,MAAMD,MAAK,cAAA,CAAe,KAAA;wBAC1B,SAAS,aAAa,QAAQ,EAAE,OAAA,GAAU;oBAC3C,CAAC;oBACD,MAAM;gBACP,SAAE;oBACD,KAAK,GAAA,CAAI;gBACV;YACD,IAEF,MACA;IAEF;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 989, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/view-common.ts"], "sourcesContent": ["export const ViewBaseConfig = Symbol.for('drizzle:ViewBaseConfig');\n"], "names": [], "mappings": ";;;AAAO,MAAM,iBAAiB,OAAO,GAAA,CAAI,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1001, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/sql/sql.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport type { SelectedFields } from '~/operations.ts';\nimport { isPgEnum } from '~/pg-core/columns/enum.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { tracer } from '~/tracing.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type { AnyColumn } from '../column.ts';\nimport { Column } from '../column.ts';\nimport { Table } from '../table.ts';\n\n/**\n * This class is used to indicate a primitive param value that is used in `sql` tag.\n * It is only used on type level and is never instantiated at runtime.\n * If you see a value of this type in the code, its runtime value is actually the primitive param value.\n */\nexport class FakePrimitiveParam {\n\tstatic readonly [entityKind]: string = 'FakePrimitiveParam';\n}\n\nexport type Chunk =\n\t| string\n\t| Table\n\t| View\n\t| AnyColumn\n\t| Name\n\t| Param\n\t| Placeholder\n\t| SQL;\n\nexport interface BuildQueryConfig {\n\tescapeName(name: string): string;\n\tescapeParam(num: number, value: unknown): string;\n\tescapeString(str: string): string;\n\tprepareTyping?: (encoder: DriverValueEncoder<unknown, unknown>) => QueryTypingsValue;\n\tparamStartIndex?: { value: number };\n\tinlineParams?: boolean;\n\tinvokeSource?: 'indexes' | undefined;\n}\n\nexport type QueryTypingsValue = 'json' | 'decimal' | 'time' | 'timestamp' | 'uuid' | 'date' | 'none';\n\nexport interface Query {\n\tsql: string;\n\tparams: unknown[];\n}\n\nexport interface QueryWithTypings extends Query {\n\ttypings?: QueryTypingsValue[];\n}\n\n/**\n * Any value that implements the `getSQL` method. The implementations include:\n * - `Table`\n * - `Column`\n * - `View`\n * - `Subquery`\n * - `SQL`\n * - `SQL.Aliased`\n * - `Placeholder`\n * - `Param`\n */\nexport interface SQLWrapper {\n\tgetSQL(): SQL;\n\tshouldOmitSQLParens?(): boolean;\n}\n\nexport function isSQLWrapper(value: unknown): value is SQLWrapper {\n\treturn value !== null && value !== undefined && typeof (value as any).getSQL === 'function';\n}\n\nfunction mergeQueries(queries: QueryWithTypings[]): QueryWithTypings {\n\tconst result: QueryWithTypings = { sql: '', params: [] };\n\tfor (const query of queries) {\n\t\tresult.sql += query.sql;\n\t\tresult.params.push(...query.params);\n\t\tif (query.typings?.length) {\n\t\t\tif (!result.typings) {\n\t\t\t\tresult.typings = [];\n\t\t\t}\n\t\t\tresult.typings.push(...query.typings);\n\t\t}\n\t}\n\treturn result;\n}\n\nexport class StringChunk implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'StringChunk';\n\n\treadonly value: string[];\n\n\tconstructor(value: string | string[]) {\n\t\tthis.value = Array.isArray(value) ? value : [value];\n\t}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\nexport class SQL<T = unknown> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'SQL';\n\n\tdeclare _: {\n\t\tbrand: 'SQL';\n\t\ttype: T;\n\t};\n\n\t/** @internal */\n\tdecoder: DriverValueDecoder<T, any> = noopDecoder;\n\tprivate shouldInlineParams = false;\n\n\tconstructor(readonly queryChunks: SQLChunk[]) {}\n\n\tappend(query: SQL): this {\n\t\tthis.queryChunks.push(...query.queryChunks);\n\t\treturn this;\n\t}\n\n\ttoQuery(config: BuildQueryConfig): QueryWithTypings {\n\t\treturn tracer.startActiveSpan('drizzle.buildSQL', (span) => {\n\t\t\tconst query = this.buildQueryFromSourceParams(this.queryChunks, config);\n\t\t\tspan?.setAttributes({\n\t\t\t\t'drizzle.query.text': query.sql,\n\t\t\t\t'drizzle.query.params': JSON.stringify(query.params),\n\t\t\t});\n\t\t\treturn query;\n\t\t});\n\t}\n\n\tbuildQueryFromSourceParams(chunks: SQLChunk[], _config: BuildQueryConfig): Query {\n\t\tconst config = Object.assign({}, _config, {\n\t\t\tinlineParams: _config.inlineParams || this.shouldInlineParams,\n\t\t\tparamStartIndex: _config.paramStartIndex || { value: 0 },\n\t\t});\n\n\t\tconst {\n\t\t\tescapeName,\n\t\t\tescapeParam,\n\t\t\tprepareTyping,\n\t\t\tinlineParams,\n\t\t\tparamStartIndex,\n\t\t} = config;\n\n\t\treturn mergeQueries(chunks.map((chunk): QueryWithTypings => {\n\t\t\tif (is(chunk, StringChunk)) {\n\t\t\t\treturn { sql: chunk.value.join(''), params: [] };\n\t\t\t}\n\n\t\t\tif (is(chunk, Name)) {\n\t\t\t\treturn { sql: escapeName(chunk.value), params: [] };\n\t\t\t}\n\n\t\t\tif (chunk === undefined) {\n\t\t\t\treturn { sql: '', params: [] };\n\t\t\t}\n\n\t\t\tif (Array.isArray(chunk)) {\n\t\t\t\tconst result: SQLChunk[] = [new StringChunk('(')];\n\t\t\t\tfor (const [i, p] of chunk.entries()) {\n\t\t\t\t\tresult.push(p);\n\t\t\t\t\tif (i < chunk.length - 1) {\n\t\t\t\t\t\tresult.push(new StringChunk(', '));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tresult.push(new StringChunk(')'));\n\t\t\t\treturn this.buildQueryFromSourceParams(result, config);\n\t\t\t}\n\n\t\t\tif (is(chunk, SQL)) {\n\t\t\t\treturn this.buildQueryFromSourceParams(chunk.queryChunks, {\n\t\t\t\t\t...config,\n\t\t\t\t\tinlineParams: inlineParams || chunk.shouldInlineParams,\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tif (is(chunk, Table)) {\n\t\t\t\tconst schemaName = chunk[Table.Symbol.Schema];\n\t\t\t\tconst tableName = chunk[Table.Symbol.Name];\n\t\t\t\treturn {\n\t\t\t\t\tsql: schemaName === undefined\n\t\t\t\t\t\t? escapeName(tableName)\n\t\t\t\t\t\t: escapeName(schemaName) + '.' + escapeName(tableName),\n\t\t\t\t\tparams: [],\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (is(chunk, Column)) {\n\t\t\t\tif (_config.invokeSource === 'indexes') {\n\t\t\t\t\treturn { sql: escapeName(chunk.name), params: [] };\n\t\t\t\t}\n\t\t\t\treturn { sql: escapeName(chunk.table[Table.Symbol.Name]) + '.' + escapeName(chunk.name), params: [] };\n\t\t\t}\n\n\t\t\tif (is(chunk, View)) {\n\t\t\t\tconst schemaName = chunk[ViewBaseConfig].schema;\n\t\t\t\tconst viewName = chunk[ViewBaseConfig].name;\n\t\t\t\treturn {\n\t\t\t\t\tsql: schemaName === undefined\n\t\t\t\t\t\t? escapeName(viewName)\n\t\t\t\t\t\t: escapeName(schemaName) + '.' + escapeName(viewName),\n\t\t\t\t\tparams: [],\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (is(chunk, Param)) {\n\t\t\t\tconst mappedValue = (chunk.value === null) ? null : chunk.encoder.mapToDriverValue(chunk.value);\n\n\t\t\t\tif (is(mappedValue, SQL)) {\n\t\t\t\t\treturn this.buildQueryFromSourceParams([mappedValue], config);\n\t\t\t\t}\n\n\t\t\t\tif (inlineParams) {\n\t\t\t\t\treturn { sql: this.mapInlineParam(mappedValue, config), params: [] };\n\t\t\t\t}\n\n\t\t\t\tlet typings: QueryTypingsValue[] | undefined;\n\t\t\t\tif (prepareTyping) {\n\t\t\t\t\ttypings = [prepareTyping(chunk.encoder)];\n\t\t\t\t}\n\n\t\t\t\treturn { sql: escapeParam(paramStartIndex.value++, mappedValue), params: [mappedValue], typings };\n\t\t\t}\n\n\t\t\tif (is(chunk, Placeholder)) {\n\t\t\t\treturn { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: ['none'] };\n\t\t\t}\n\n\t\t\tif (is(chunk, SQL.Aliased) && chunk.fieldAlias !== undefined) {\n\t\t\t\treturn { sql: escapeName(chunk.fieldAlias), params: [] };\n\t\t\t}\n\n\t\t\tif (is(chunk, Subquery)) {\n\t\t\t\tif (chunk._.isWith) {\n\t\t\t\t\treturn { sql: escapeName(chunk._.alias), params: [] };\n\t\t\t\t}\n\t\t\t\treturn this.buildQueryFromSourceParams([\n\t\t\t\t\tnew StringChunk('('),\n\t\t\t\t\tchunk._.sql,\n\t\t\t\t\tnew StringChunk(') '),\n\t\t\t\t\tnew Name(chunk._.alias),\n\t\t\t\t], config);\n\t\t\t}\n\n\t\t\tif (isPgEnum(chunk)) {\n\t\t\t\tif (chunk.schema) {\n\t\t\t\t\treturn { sql: escapeName(chunk.schema) + '.' + escapeName(chunk.enumName), params: [] };\n\t\t\t\t}\n\t\t\t\treturn { sql: escapeName(chunk.enumName), params: [] };\n\t\t\t}\n\n\t\t\tif (isSQLWrapper(chunk)) {\n\t\t\t\tif (chunk.shouldOmitSQLParens?.()) {\n\t\t\t\t\treturn this.buildQueryFromSourceParams([chunk.getSQL()], config);\n\t\t\t\t}\n\t\t\t\treturn this.buildQueryFromSourceParams([\n\t\t\t\t\tnew StringChunk('('),\n\t\t\t\t\tchunk.getSQL(),\n\t\t\t\t\tnew StringChunk(')'),\n\t\t\t\t], config);\n\t\t\t}\n\n\t\t\tif (inlineParams) {\n\t\t\t\treturn { sql: this.mapInlineParam(chunk, config), params: [] };\n\t\t\t}\n\n\t\t\treturn { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk] };\n\t\t}));\n\t}\n\n\tprivate mapInlineParam(\n\t\tchunk: unknown,\n\t\t{ escapeString }: BuildQueryConfig,\n\t): string {\n\t\tif (chunk === null) {\n\t\t\treturn 'null';\n\t\t}\n\t\tif (typeof chunk === 'number' || typeof chunk === 'boolean') {\n\t\t\treturn chunk.toString();\n\t\t}\n\t\tif (typeof chunk === 'string') {\n\t\t\treturn escapeString(chunk);\n\t\t}\n\t\tif (typeof chunk === 'object') {\n\t\t\tconst mappedValueAsString = chunk.toString();\n\t\t\tif (mappedValueAsString === '[object Object]') {\n\t\t\t\treturn escapeString(JSON.stringify(chunk));\n\t\t\t}\n\t\t\treturn escapeString(mappedValueAsString);\n\t\t}\n\t\tthrow new Error('Unexpected param value: ' + chunk);\n\t}\n\n\tgetSQL(): SQL {\n\t\treturn this;\n\t}\n\n\tas(alias: string): SQL.Aliased<T>;\n\t/**\n\t * @deprecated\n\t * Use ``sql<DataType>`query`.as(alias)`` instead.\n\t */\n\tas<TData>(): SQL<TData>;\n\t/**\n\t * @deprecated\n\t * Use ``sql<DataType>`query`.as(alias)`` instead.\n\t */\n\tas<TData>(alias: string): SQL.Aliased<TData>;\n\tas(alias?: string): SQL<T> | SQL.Aliased<T> {\n\t\t// TODO: remove with deprecated overloads\n\t\tif (alias === undefined) {\n\t\t\treturn this;\n\t\t}\n\n\t\treturn new SQL.Aliased(this, alias);\n\t}\n\n\tmapWith<\n\t\tTDecoder extends\n\t\t\t| DriverValueDecoder<any, any>\n\t\t\t| DriverValueDecoder<any, any>['mapFromDriverValue'],\n\t>(decoder: TDecoder): SQL<GetDecoderResult<TDecoder>> {\n\t\tthis.decoder = typeof decoder === 'function' ? { mapFromDriverValue: decoder } : decoder;\n\t\treturn this as SQL<GetDecoderResult<TDecoder>>;\n\t}\n\n\tinlineParams(): this {\n\t\tthis.shouldInlineParams = true;\n\t\treturn this;\n\t}\n\n\t/**\n\t * This method is used to conditionally include a part of the query.\n\t *\n\t * @param condition - Condition to check\n\t * @returns itself if the condition is `true`, otherwise `undefined`\n\t */\n\tif(condition: any | undefined): this | undefined {\n\t\treturn condition ? this : undefined;\n\t}\n}\n\nexport type GetDecoderResult<T> = T extends Column ? T['_']['data'] : T extends\n\t| DriverValueDecoder<infer TData, any>\n\t| DriverValueDecoder<infer TData, any>['mapFromDriverValue'] ? TData\n: never;\n\n/**\n * Any DB name (table, column, index etc.)\n */\nexport class Name implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Name';\n\n\tprotected brand!: 'Name';\n\n\tconstructor(readonly value: string) {}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\n/**\n * Any DB name (table, column, index etc.)\n * @deprecated Use `sql.identifier` instead.\n */\nexport function name(value: string): Name {\n\treturn new Name(value);\n}\n\nexport interface DriverValueDecoder<TData, TDriverParam> {\n\tmapFromDriverValue(value: TDriverParam): TData;\n}\n\nexport interface DriverValueEncoder<TData, TDriverParam> {\n\tmapToDriverValue(value: TData): TDriverParam | SQL;\n}\n\nexport function isDriverValueEncoder(value: unknown): value is DriverValueEncoder<any, any> {\n\treturn typeof value === 'object' && value !== null && 'mapToDriverValue' in value\n\t\t&& typeof (value as any).mapToDriverValue === 'function';\n}\n\nexport const noopDecoder: DriverValueDecoder<any, any> = {\n\tmapFromDriverValue: (value) => value,\n};\n\nexport const noopEncoder: DriverValueEncoder<any, any> = {\n\tmapToDriverValue: (value) => value,\n};\n\nexport interface DriverValueMapper<TData, TDriverParam>\n\textends DriverValueDecoder<TData, TDriverParam>, DriverValueEncoder<TData, TDriverParam>\n{}\n\nexport const noopMapper: DriverValueMapper<any, any> = {\n\t...noopDecoder,\n\t...noopEncoder,\n};\n\n/** Parameter value that is optionally bound to an encoder (for example, a column). */\nexport class Param<TDataType = unknown, TDriverParamType = TDataType> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Param';\n\n\tprotected brand!: 'BoundParamValue';\n\n\t/**\n\t * @param value - Parameter value\n\t * @param encoder - Encoder to convert the value to a driver parameter\n\t */\n\tconstructor(\n\t\treadonly value: TDataType,\n\t\treadonly encoder: DriverValueEncoder<TDataType, TDriverParamType> = noopEncoder,\n\t) {}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\n/** @deprecated Use `sql.param` instead. */\nexport function param<TData, TDriver>(\n\tvalue: TData,\n\tencoder?: DriverValueEncoder<TData, TDriver>,\n): Param<TData, TDriver> {\n\treturn new Param(value, encoder);\n}\n\n/**\n * Anything that can be passed to the `` sql`...` `` tagged function.\n */\nexport type SQLChunk =\n\t| StringChunk\n\t| SQLChunk[]\n\t| SQLWrapper\n\t| SQL\n\t| Table\n\t| View\n\t| Subquery\n\t| AnyColumn\n\t| Param\n\t| Name\n\t| undefined\n\t| FakePrimitiveParam\n\t| Placeholder;\n\nexport function sql<T>(strings: TemplateStringsArray, ...params: any[]): SQL<T>;\n/*\n\tThe type of `params` is specified as `SQLChunk[]`, but that's slightly incorrect -\n\tin runtime, users won't pass `FakePrimitiveParam` instances as `params` - they will pass primitive values\n\twhich will be wrapped in `Param`. That's why the overload specifies `params` as `any[]` and not as `SQLSourceParam[]`.\n\tThis type is used to make our lives easier and the type checker happy.\n*/\nexport function sql(strings: TemplateStringsArray, ...params: SQLChunk[]): SQL {\n\tconst queryChunks: SQLChunk[] = [];\n\tif (params.length > 0 || (strings.length > 0 && strings[0] !== '')) {\n\t\tqueryChunks.push(new StringChunk(strings[0]!));\n\t}\n\tfor (const [paramIndex, param] of params.entries()) {\n\t\tqueryChunks.push(param, new StringChunk(strings[paramIndex + 1]!));\n\t}\n\n\treturn new SQL(queryChunks);\n}\n\nexport namespace sql {\n\texport function empty(): SQL {\n\t\treturn new SQL([]);\n\t}\n\n\t/** @deprecated - use `sql.join()` */\n\texport function fromList(list: SQLChunk[]): SQL {\n\t\treturn new SQL(list);\n\t}\n\n\t/**\n\t * Convenience function to create an SQL query from a raw string.\n\t * @param str The raw SQL query string.\n\t */\n\texport function raw(str: string): SQL {\n\t\treturn new SQL([new StringChunk(str)]);\n\t}\n\n\t/**\n\t * Join a list of SQL chunks with a separator.\n\t * @example\n\t * ```ts\n\t * const query = sql.join([sql`a`, sql`b`, sql`c`]);\n\t * // sql`abc`\n\t * ```\n\t * @example\n\t * ```ts\n\t * const query = sql.join([sql`a`, sql`b`, sql`c`], sql`, `);\n\t * // sql`a, b, c`\n\t * ```\n\t */\n\texport function join(chunks: SQLChunk[], separator?: SQLChunk): SQL {\n\t\tconst result: SQLChunk[] = [];\n\t\tfor (const [i, chunk] of chunks.entries()) {\n\t\t\tif (i > 0 && separator !== undefined) {\n\t\t\t\tresult.push(separator);\n\t\t\t}\n\t\t\tresult.push(chunk);\n\t\t}\n\t\treturn new SQL(result);\n\t}\n\n\t/**\n\t * Create a SQL chunk that represents a DB identifier (table, column, index etc.).\n\t * When used in a query, the identifier will be escaped based on the DB engine.\n\t * For example, in PostgreSQL, identifiers are escaped with double quotes.\n\t *\n\t * **WARNING: This function does not offer any protection against SQL injections, so you must validate any user input beforehand.**\n\t *\n\t * @example ```ts\n\t * const query = sql`SELECT * FROM ${sql.identifier('my-table')}`;\n\t * // 'SELECT * FROM \"my-table\"'\n\t * ```\n\t */\n\texport function identifier(value: string): Name {\n\t\treturn new Name(value);\n\t}\n\n\texport function placeholder<TName extends string>(name: TName): Placeholder<TName> {\n\t\treturn new Placeholder(name);\n\t}\n\n\texport function param<TData, TDriver>(\n\t\tvalue: TData,\n\t\tencoder?: DriverValueEncoder<TData, TDriver>,\n\t): Param<TData, TDriver> {\n\t\treturn new Param(value, encoder);\n\t}\n}\n\nexport namespace SQL {\n\texport class Aliased<T = unknown> implements SQLWrapper {\n\t\tstatic readonly [entityKind]: string = 'SQL.Aliased';\n\n\t\tdeclare _: {\n\t\t\tbrand: 'SQL.Aliased';\n\t\t\ttype: T;\n\t\t};\n\n\t\t/** @internal */\n\t\tisSelectionField = false;\n\n\t\tconstructor(\n\t\t\treadonly sql: SQL,\n\t\t\treadonly fieldAlias: string,\n\t\t) {}\n\n\t\tgetSQL(): SQL {\n\t\t\treturn this.sql;\n\t\t}\n\n\t\t/** @internal */\n\t\tclone() {\n\t\t\treturn new Aliased(this.sql, this.fieldAlias);\n\t\t}\n\t}\n}\n\nexport class Placeholder<TName extends string = string, TValue = any> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Placeholder';\n\n\tdeclare protected: TValue;\n\n\tconstructor(readonly name: TName) {}\n\n\tgetSQL(): SQL {\n\t\treturn new SQL([this]);\n\t}\n}\n\n/** @deprecated Use `sql.placeholder` instead. */\nexport function placeholder<TName extends string>(name: TName): Placeholder<TName> {\n\treturn new Placeholder(name);\n}\n\nexport function fillPlaceholders(params: unknown[], values: Record<string, unknown>): unknown[] {\n\treturn params.map((p) => {\n\t\tif (is(p, Placeholder)) {\n\t\t\tif (!(p.name in values)) {\n\t\t\t\tthrow new Error(`No value for placeholder \"${p.name}\" was provided`);\n\t\t\t}\n\t\t\treturn values[p.name];\n\t\t}\n\n\t\treturn p;\n\t});\n}\n\nexport type ColumnsSelection = Record<string, unknown>;\n\nexport abstract class View<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelection extends ColumnsSelection = ColumnsSelection,\n> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'View';\n\n\tdeclare _: {\n\t\tbrand: 'View';\n\t\tviewBrand: string;\n\t\tname: TName;\n\t\texisting: TExisting;\n\t\tselectedFields: TSelection;\n\t};\n\n\t/** @internal */\n\t[ViewBaseConfig]: {\n\t\tname: TName;\n\t\toriginalName: TName;\n\t\tschema: string | undefined;\n\t\tselectedFields: SelectedFields<AnyColumn, Table>;\n\t\tisExisting: TExisting;\n\t\tquery: TExisting extends true ? undefined : SQL;\n\t\tisAlias: boolean;\n\t};\n\n\tconstructor(\n\t\t{ name, schema, selectedFields, query }: {\n\t\t\tname: TName;\n\t\t\tschema: string | undefined;\n\t\t\tselectedFields: SelectedFields<AnyColumn, Table>;\n\t\t\tquery: SQL | undefined;\n\t\t},\n\t) {\n\t\tthis[ViewBaseConfig] = {\n\t\t\tname,\n\t\t\toriginalName: name,\n\t\t\tschema,\n\t\t\tselectedFields,\n\t\t\tquery: query as (TExisting extends true ? undefined : SQL),\n\t\t\tisExisting: !query as TExisting,\n\t\t\tisAlias: false,\n\t\t};\n\t}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\n// Defined separately from the Column class to resolve circular dependency\nColumn.prototype.getSQL = function() {\n\treturn new SQL([this]);\n};\n\n// Defined separately from the Table class to resolve circular dependency\nTable.prototype.getSQL = function() {\n\treturn new SQL([this]);\n};\n\n// Defined separately from the Column class to resolve circular dependency\nSubquery.prototype.getSQL = function() {\n\treturn new SQL([this]);\n};\n"], "names": ["param", "sql", "placeholder", "name", "SQL"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,SAAS,YAAY,UAAU;AAE/B,SAAS,gBAAgB;AACzB,SAAS,gBAAgB;AACzB,SAAS,cAAc;AACvB,SAAS,sBAAsB;AAE/B,SAAS,cAAc;AACvB,SAAS,aAAa;;;;;;;;AAOf,MAAM,mBAAmB;IAC/B,OAAA,mPAAiB,aAAU,CAAA,GAAY,qBAAA;AACxC;AAiDO,SAAS,aAAa,KAAA,EAAqC;IACjE,OAAO,UAAU,QAAQ,UAAU,KAAA,KAAa,OAAQ,MAAc,MAAA,KAAW;AAClF;AAEA,SAAS,aAAa,OAAA,EAA+C;IACpE,MAAM,SAA2B;QAAE,KAAK;QAAI,QAAQ,CAAC,CAAA;IAAE;IACvD,KAAA,MAAW,SAAS,QAAS;QAC5B,OAAO,GAAA,IAAO,MAAM,GAAA;QACpB,OAAO,MAAA,CAAO,IAAA,CAAK,GAAG,MAAM,MAAM;QAClC,IAAI,MAAM,OAAA,EAAS,QAAQ;YAC1B,IAAI,CAAC,OAAO,OAAA,EAAS;gBACpB,OAAO,OAAA,GAAU,CAAC,CAAA;YACnB;YACA,OAAO,OAAA,CAAQ,IAAA,CAAK,GAAG,MAAM,OAAO;QACrC;IACD;IACA,OAAO;AACR;AAEO,MAAM,YAAkC;IAC9C,OAAA,CAAiB,+PAAU,CAAA,GAAY,cAAA;IAE9B,MAAA;IAET,YAAY,KAAA,CAA0B;QACrC,IAAA,CAAK,KAAA,GAAQ,MAAM,OAAA,CAAQ,KAAK,IAAI,QAAQ;YAAC,KAAK;SAAA;IACnD;IAEA,SAAuB;QACtB,OAAO,IAAI,IAAI;YAAC,IAAI;SAAC;IACtB;AACD;AAEO,MAAM,IAAuC;IAYnD,YAAqB,WAAA,CAAyB;QAAzB,IAAA,CAAA,WAAA,GAAA;IAA0B;IAX/C,OAAA,mPAAiB,aAAU,CAAA,GAAY,MAAA;IAAA,cAAA,GAQvC,UAAsC,YAAA;IAC9B,qBAAqB,MAAA;IAI7B,OAAO,KAAA,EAAkB;QACxB,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,GAAG,MAAM,WAAW;QAC1C,OAAO,IAAA;IACR;IAEA,QAAQ,MAAA,EAA4C;QACnD,0PAAO,SAAA,CAAO,eAAA,CAAgB,oBAAoB,CAAC,SAAS;YAC3D,MAAM,QAAQ,IAAA,CAAK,0BAAA,CAA2B,IAAA,CAAK,WAAA,EAAa,MAAM;YACtE,MAAM,cAAc;gBACnB,sBAAsB,MAAM,GAAA;gBAC5B,wBAAwB,KAAK,SAAA,CAAU,MAAM,MAAM;YACpD,CAAC;YACD,OAAO;QACR,CAAC;IACF;IAEA,2BAA2B,MAAA,EAAoB,OAAA,EAAkC;QAChF,MAAM,SAAS,OAAO,MAAA,CAAO,CAAC,GAAG,SAAS;YACzC,cAAc,QAAQ,YAAA,IAAgB,IAAA,CAAK,kBAAA;YAC3C,iBAAiB,QAAQ,eAAA,IAAmB;gBAAE,OAAO;YAAE;QACxD,CAAC;QAED,MAAM,EACL,UAAA,EACA,WAAA,EACA,aAAA,EACA,YAAA,EACA,eAAA,EACD,GAAI;QAEJ,OAAO,aAAa,OAAO,GAAA,CAAI,CAAC,UAA4B;YAC3D,yPAAI,MAAA,EAAG,OAAO,WAAW,GAAG;gBAC3B,OAAO;oBAAE,KAAK,MAAM,KAAA,CAAM,IAAA,CAAK,EAAE;oBAAG,QAAQ,CAAC,CAAA;gBAAE;YAChD;YAEA,IAAI,2PAAA,EAAG,OAAO,IAAI,GAAG;gBACpB,OAAO;oBAAE,KAAK,WAAW,MAAM,KAAK;oBAAG,QAAQ,CAAC,CAAA;gBAAE;YACnD;YAEA,IAAI,UAAU,KAAA,GAAW;gBACxB,OAAO;oBAAE,KAAK;oBAAI,QAAQ,CAAC,CAAA;gBAAE;YAC9B;YAEA,IAAI,MAAM,OAAA,CAAQ,KAAK,GAAG;gBACzB,MAAM,SAAqB;oBAAC,IAAI,YAAY,GAAG,CAAC;iBAAA;gBAChD,KAAA,MAAW,CAAC,GAAG,CAAC,CAAA,IAAK,MAAM,OAAA,CAAQ,EAAG;oBACrC,OAAO,IAAA,CAAK,CAAC;oBACb,IAAI,IAAI,MAAM,MAAA,GAAS,GAAG;wBACzB,OAAO,IAAA,CAAK,IAAI,YAAY,IAAI,CAAC;oBAClC;gBACD;gBACA,OAAO,IAAA,CAAK,IAAI,YAAY,GAAG,CAAC;gBAChC,OAAO,IAAA,CAAK,0BAAA,CAA2B,QAAQ,MAAM;YACtD;YAEA,0PAAI,KAAA,EAAG,OAAO,GAAG,GAAG;gBACnB,OAAO,IAAA,CAAK,0BAAA,CAA2B,MAAM,WAAA,EAAa;oBACzD,GAAG,MAAA;oBACH,cAAc,gBAAgB,MAAM,kBAAA;gBACrC,CAAC;YACF;YAEA,0PAAI,KAAA,EAAG,wPAAO,QAAK,GAAG;gBACrB,MAAM,aAAa,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,MAAM,CAAA;gBAC5C,MAAM,YAAY,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,IAAI,CAAA;gBACzC,OAAO;oBACN,KAAK,eAAe,KAAA,IACjB,WAAW,SAAS,IACpB,WAAW,UAAU,IAAI,MAAM,WAAW,SAAS;oBACtD,QAAQ,CAAC,CAAA;gBACV;YACD;YAEA,0PAAI,KAAA,EAAG,yPAAO,SAAM,GAAG;gBACtB,IAAI,QAAQ,YAAA,KAAiB,WAAW;oBACvC,OAAO;wBAAE,KAAK,WAAW,MAAM,IAAI;wBAAG,QAAQ,CAAC,CAAA;oBAAE;gBAClD;gBACA,OAAO;oBAAE,KAAK,WAAW,MAAM,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,IAAI,CAAC,IAAI,MAAM,WAAW,MAAM,IAAI;oBAAG,QAAQ,CAAC,CAAA;gBAAE;YACrG;YAEA,IAAI,2PAAA,EAAG,OAAO,IAAI,GAAG;gBACpB,MAAM,aAAa,KAAA,2PAAM,iBAAc,CAAA,CAAE,MAAA;gBACzC,MAAM,WAAW,KAAA,2PAAM,iBAAc,CAAA,CAAE,IAAA;gBACvC,OAAO;oBACN,KAAK,eAAe,KAAA,IACjB,WAAW,QAAQ,IACnB,WAAW,UAAU,IAAI,MAAM,WAAW,QAAQ;oBACrD,QAAQ,CAAC,CAAA;gBACV;YACD;YAEA,0PAAI,KAAA,EAAG,OAAO,KAAK,GAAG;gBACrB,MAAM,cAAe,MAAM,KAAA,KAAU,OAAQ,OAAO,MAAM,OAAA,CAAQ,gBAAA,CAAiB,MAAM,KAAK;gBAE9F,IAAI,2PAAA,EAAG,aAAa,GAAG,GAAG;oBACzB,OAAO,IAAA,CAAK,0BAAA,CAA2B;wBAAC,WAAW;qBAAA,EAAG,MAAM;gBAC7D;gBAEA,IAAI,cAAc;oBACjB,OAAO;wBAAE,KAAK,IAAA,CAAK,cAAA,CAAe,aAAa,MAAM;wBAAG,QAAQ,CAAC,CAAA;oBAAE;gBACpE;gBAEA,IAAI;gBACJ,IAAI,eAAe;oBAClB,UAAU;wBAAC,cAAc,MAAM,OAAO,CAAC;qBAAA;gBACxC;gBAEA,OAAO;oBAAE,KAAK,YAAY,gBAAgB,KAAA,IAAS,WAAW;oBAAG,QAAQ;wBAAC,WAAW;qBAAA;oBAAG;gBAAQ;YACjG;YAEA,0PAAI,KAAA,EAAG,OAAO,WAAW,GAAG;gBAC3B,OAAO;oBAAE,KAAK,YAAY,gBAAgB,KAAA,IAAS,KAAK;oBAAG,QAAQ;wBAAC,KAAK;qBAAA;oBAAG,SAAS;wBAAC,MAAM;qBAAA;gBAAE;YAC/F;YAEA,0PAAI,KAAA,EAAG,OAAO,IAAI,OAAO,KAAK,MAAM,UAAA,KAAe,KAAA,GAAW;gBAC7D,OAAO;oBAAE,KAAK,WAAW,MAAM,UAAU;oBAAG,QAAQ,CAAC,CAAA;gBAAE;YACxD;YAEA,0PAAI,KAAA,EAAG,OAAO,+PAAQ,GAAG;gBACxB,IAAI,MAAM,CAAA,CAAE,MAAA,EAAQ;oBACnB,OAAO;wBAAE,KAAK,WAAW,MAAM,CAAA,CAAE,KAAK;wBAAG,QAAQ,CAAC,CAAA;oBAAE;gBACrD;gBACA,OAAO,IAAA,CAAK,0BAAA,CAA2B;oBACtC,IAAI,YAAY,GAAG;oBACnB,MAAM,CAAA,CAAE,GAAA;oBACR,IAAI,YAAY,IAAI;oBACpB,IAAI,KAAK,MAAM,CAAA,CAAE,KAAK;iBACvB,EAAG,MAAM;YACV;YAEA,iRAAI,WAAA,EAAS,KAAK,GAAG;gBACpB,IAAI,MAAM,MAAA,EAAQ;oBACjB,OAAO;wBAAE,KAAK,WAAW,MAAM,MAAM,IAAI,MAAM,WAAW,MAAM,QAAQ;wBAAG,QAAQ,CAAC,CAAA;oBAAE;gBACvF;gBACA,OAAO;oBAAE,KAAK,WAAW,MAAM,QAAQ;oBAAG,QAAQ,CAAC,CAAA;gBAAE;YACtD;YAEA,IAAI,aAAa,KAAK,GAAG;gBACxB,IAAI,MAAM,mBAAA,GAAsB,GAAG;oBAClC,OAAO,IAAA,CAAK,0BAAA,CAA2B;wBAAC,MAAM,MAAA,CAAO,CAAC;qBAAA,EAAG,MAAM;gBAChE;gBACA,OAAO,IAAA,CAAK,0BAAA,CAA2B;oBACtC,IAAI,YAAY,GAAG;oBACnB,MAAM,MAAA,CAAO;oBACb,IAAI,YAAY,GAAG;iBACpB,EAAG,MAAM;YACV;YAEA,IAAI,cAAc;gBACjB,OAAO;oBAAE,KAAK,IAAA,CAAK,cAAA,CAAe,OAAO,MAAM;oBAAG,QAAQ,CAAC,CAAA;gBAAE;YAC9D;YAEA,OAAO;gBAAE,KAAK,YAAY,gBAAgB,KAAA,IAAS,KAAK;gBAAG,QAAQ;oBAAC,KAAK;iBAAA;YAAE;QAC5E,CAAC,CAAC;IACH;IAEQ,eACP,KAAA,EACA,EAAE,YAAA,CAAa,CAAA,EACN;QACT,IAAI,UAAU,MAAM;YACnB,OAAO;QACR;QACA,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,WAAW;YAC5D,OAAO,MAAM,QAAA,CAAS;QACvB;QACA,IAAI,OAAO,UAAU,UAAU;YAC9B,OAAO,aAAa,KAAK;QAC1B;QACA,IAAI,OAAO,UAAU,UAAU;YAC9B,MAAM,sBAAsB,MAAM,QAAA,CAAS;YAC3C,IAAI,wBAAwB,mBAAmB;gBAC9C,OAAO,aAAa,KAAK,SAAA,CAAU,KAAK,CAAC;YAC1C;YACA,OAAO,aAAa,mBAAmB;QACxC;QACA,MAAM,IAAI,MAAM,6BAA6B,KAAK;IACnD;IAEA,SAAc;QACb,OAAO,IAAA;IACR;IAaA,GAAG,KAAA,EAAyC;QAE3C,IAAI,UAAU,KAAA,GAAW;YACxB,OAAO,IAAA;QACR;QAEA,OAAO,IAAI,IAAI,OAAA,CAAQ,IAAA,EAAM,KAAK;IACnC;IAEA,QAIE,OAAA,EAAoD;QACrD,IAAA,CAAK,OAAA,GAAU,OAAO,YAAY,aAAa;YAAE,oBAAoB;QAAQ,IAAI;QACjF,OAAO,IAAA;IACR;IAEA,eAAqB;QACpB,IAAA,CAAK,kBAAA,GAAqB;QAC1B,OAAO,IAAA;IACR;IAAA;;;;;GAAA,GAQA,GAAG,SAAA,EAA8C;QAChD,OAAO,YAAY,IAAA,GAAO,KAAA;IAC3B;AACD;AAUO,MAAM,KAA2B;IAKvC,YAAqB,KAAA,CAAe;QAAf,IAAA,CAAA,KAAA,GAAA;IAAgB;IAJrC,OAAA,mPAAiB,aAAU,CAAA,GAAY,OAAA;IAE7B,MAAA;IAIV,SAAuB;QACtB,OAAO,IAAI,IAAI;YAAC,IAAI;SAAC;IACtB;AACD;AAMO,SAAS,KAAK,KAAA,EAAqB;IACzC,OAAO,IAAI,KAAK,KAAK;AACtB;AAUO,SAAS,qBAAqB,KAAA,EAAuD;IAC3F,OAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,sBAAsB,SACxE,OAAQ,MAAc,gBAAA,KAAqB;AAChD;AAEO,MAAM,cAA4C;IACxD,oBAAoB,CAAC,QAAU;AAChC;AAEO,MAAM,cAA4C;IACxD,kBAAkB,CAAC,QAAU;AAC9B;AAMO,MAAM,aAA0C;IACtD,GAAG,WAAA;IACH,GAAG,WAAA;AACJ;AAGO,MAAM,MAA+E;IAAA;;;GAAA,GAS3F,YACU,KAAA,EACA,UAA2D,WAAA,CACnE;QAFQ,IAAA,CAAA,KAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;IACP;IAXH,OAAA,mPAAiB,aAAU,CAAA,GAAY,QAAA;IAE7B,MAAA;IAWV,SAAuB;QACtB,OAAO,IAAI,IAAI;YAAC,IAAI;SAAC;IACtB;AACD;AAGO,SAAS,MACf,KAAA,EACA,OAAA,EACwB;IACxB,OAAO,IAAI,MAAM,OAAO,OAAO;AAChC;AA2BO,SAAS,IAAI,OAAA,EAAA,GAAkC,MAAA,EAAyB;IAC9E,MAAM,cAA0B,CAAC,CAAA;IACjC,IAAI,OAAO,MAAA,GAAS,KAAM,QAAQ,MAAA,GAAS,KAAK,OAAA,CAAQ,CAAC,CAAA,KAAM,IAAK;QACnE,YAAY,IAAA,CAAK,IAAI,YAAY,OAAA,CAAQ,CAAC,CAAE,CAAC;IAC9C;IACA,KAAA,MAAW,CAAC,YAAYA,MAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,EAAG;QACnD,YAAY,IAAA,CAAKA,QAAO,IAAI,YAAY,OAAA,CAAQ,aAAa,CAAC,CAAE,CAAC;IAClE;IAEA,OAAO,IAAI,IAAI,WAAW;AAC3B;AAAA,CAEO,CAAUC,SAAV;IACC,SAAS,QAAa;QAC5B,OAAO,IAAI,IAAI,CAAC,CAAC;IAClB;IAFOA,KAAS,KAAA,GAAA;IAKT,SAAS,SAAS,IAAA,EAAuB;QAC/C,OAAO,IAAI,IAAI,IAAI;IACpB;IAFOA,KAAS,QAAA,GAAA;IAQT,SAAS,IAAI,GAAA,EAAkB;QACrC,OAAO,IAAI,IAAI;YAAC,IAAI,YAAY,GAAG,CAAC;SAAC;IACtC;IAFOA,KAAS,GAAA,GAAA;IAiBT,SAAS,KAAK,MAAA,EAAoB,SAAA,EAA2B;QACnE,MAAM,SAAqB,CAAC,CAAA;QAC5B,KAAA,MAAW,CAAC,GAAG,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,EAAG;YAC1C,IAAI,IAAI,KAAK,cAAc,KAAA,GAAW;gBACrC,OAAO,IAAA,CAAK,SAAS;YACtB;YACA,OAAO,IAAA,CAAK,KAAK;QAClB;QACA,OAAO,IAAI,IAAI,MAAM;IACtB;IATOA,KAAS,IAAA,GAAA;IAuBT,SAAS,WAAW,KAAA,EAAqB;QAC/C,OAAO,IAAI,KAAK,KAAK;IACtB;IAFOA,KAAS,UAAA,GAAA;IAIT,SAASC,aAAkCC,KAAAA,EAAiC;QAClF,OAAO,IAAI,YAAYA,KAAI;IAC5B;IAFOF,KAAS,WAAA,GAAAC;IAIT,SAASF,OACf,KAAA,EACA,OAAA,EACwB;QACxB,OAAO,IAAI,MAAM,OAAO,OAAO;IAChC;IALOC,KAAS,KAAA,GAAAD;AAAA,CAAA,EA9DA,OAAA,CAAA,MAAA,CAAA,CAAA;AAAA,CAsEV,CAAUI,SAAV;IACC,MAAM,QAA2C;QAWvD,YACUH,IAAAA,EACA,UAAA,CACR;YAFQ,IAAA,CAAA,GAAA,GAAAA;YACA,IAAA,CAAA,UAAA,GAAA;QACP;QAbH,OAAA,kPAAiB,cAAU,CAAA,GAAY,cAAA;QAAA,cAAA,GAQvC,mBAAmB,MAAA;QAOnB,SAAc;YACb,OAAO,IAAA,CAAK,GAAA;QACb;QAAA,cAAA,GAGA,QAAQ;YACP,OAAO,IAAI,QAAQ,IAAA,CAAK,GAAA,EAAK,IAAA,CAAK,UAAU;QAC7C;IACD;IAxBOG,KAAM,OAAA,GAAA;AAAA,CAAA,EADG,OAAA,CAAA,MAAA,CAAA,CAAA;AA4BV,MAAM,YAA+E;IAK3F,YAAqBD,KAAAA,CAAa;QAAb,IAAA,CAAA,IAAA,GAAAA;IAAc;IAJnC,OAAA,CAAiB,+PAAU,CAAA,GAAY,cAAA;IAMvC,SAAc;QACb,OAAO,IAAI,IAAI;YAAC,IAAI;SAAC;IACtB;AACD;AAGO,SAAS,YAAkCA,KAAAA,EAAiC;IAClF,OAAO,IAAI,YAAYA,KAAI;AAC5B;AAEO,SAAS,iBAAiB,MAAA,EAAmB,MAAA,EAA4C;IAC/F,OAAO,OAAO,GAAA,CAAI,CAAC,MAAM;QACxB,KAAI,0PAAA,EAAG,GAAG,WAAW,GAAG;YACvB,IAAI,CAAA,CAAE,EAAE,IAAA,IAAQ,MAAA,GAAS;gBACxB,MAAM,IAAI,MAAM,CAAA,0BAAA,EAA6B,EAAE,IAAI,CAAA,cAAA,CAAgB;YACpE;YACA,OAAO,MAAA,CAAO,EAAE,IAAI,CAAA;QACrB;QAEA,OAAO;IACR,CAAC;AACF;AAIO,MAAe,KAIE;IACvB,OAAA,mPAAiB,aAAU,CAAA,GAAY,OAAA;IAAA,cAAA,GAWvC,2PAAC,iBAAc,CAAA,CAAA;IAUf,YACC,EAAE,MAAAA,KAAAA,EAAM,MAAA,EAAQ,cAAA,EAAgB,KAAA,CAAM,CAAA,CAMrC;QACD,IAAA,2PAAK,iBAAc,CAAA,GAAI;YACtB,MAAAA;YACA,cAAcA;YACd;YACA;YACA;YACA,YAAY,CAAC;YACb,SAAS;QACV;IACD;IAEA,SAAuB;QACtB,OAAO,IAAI,IAAI;YAAC,IAAI;SAAC;IACtB;AACD;kPAGA,SAAA,CAAO,SAAA,CAAU,MAAA,GAAS,WAAW;IACpC,OAAO,IAAI,IAAI;QAAC,IAAI;KAAC;AACtB;AAGA,yPAAA,CAAM,SAAA,CAAU,MAAA,GAAS,WAAW;IACnC,OAAO,IAAI,IAAI;QAAC,IAAI;KAAC;AACtB;oPAGA,WAAA,CAAS,SAAA,CAAU,MAAA,GAAS,WAAW;IACtC,OAAO,IAAI,IAAI;QAAC,IAAI;KAAC;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1490, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/sql/expressions/conditions.ts"], "sourcesContent": ["import { type AnyColumn, Column, type GetColumnData } from '~/column.ts';\nimport { is } from '~/entity.ts';\nimport { Table } from '~/table.ts';\nimport {\n\tisDriverValueEncoder,\n\tisSQLWrapper,\n\tParam,\n\tPlaceholder,\n\tSQL,\n\tsql,\n\ttype S<PERSON><PERSON>hunk,\n\ttype SQLWrapper,\n\tStringChunk,\n\tView,\n} from '../sql.ts';\n\nexport function bindIfParam(value: unknown, column: SQLWrapper): SQLChunk {\n\tif (\n\t\tisDriverValueEncoder(column)\n\t\t&& !isSQLWrapper(value)\n\t\t&& !is(value, Param)\n\t\t&& !is(value, Placeholder)\n\t\t&& !is(value, Column)\n\t\t&& !is(value, Table)\n\t\t&& !is(value, View)\n\t) {\n\t\treturn new Param(value, column);\n\t}\n\treturn value as SQLChunk;\n}\n\nexport interface BinaryOperator {\n\t<TColumn extends Column>(\n\t\tleft: TColumn,\n\t\tright: GetColumnData<TColumn, 'raw'> | SQLWrapper,\n\t): SQL;\n\t<T>(left: SQL.Aliased<T>, right: T | SQLWrapper): SQL;\n\t<T extends SQLWrapper>(\n\t\tleft: Exclude<T, SQL.Aliased | Column>,\n\t\tright: unknown,\n\t): SQL;\n}\n\n/**\n * Test that two values are equal.\n *\n * Remember that the SQL standard dictates that\n * two NULL values are not equal, so if you want to test\n * whether a value is null, you may want to use\n * `isNull` instead.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made by Ford\n * db.select().from(cars)\n *   .where(eq(cars.make, 'Ford'))\n * ```\n *\n * @see isNull for a way to test equality to NULL.\n */\nexport const eq: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} = ${bindIfParam(right, left)}`;\n};\n\n/**\n * Test that two values are not equal.\n *\n * Remember that the SQL standard dictates that\n * two NULL values are not equal, so if you want to test\n * whether a value is not null, you may want to use\n * `isNotNull` instead.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars not made by Ford\n * db.select().from(cars)\n *   .where(ne(cars.make, 'Ford'))\n * ```\n *\n * @see isNotNull for a way to test whether a value is not null.\n */\nexport const ne: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} <> ${bindIfParam(right, left)}`;\n};\n\n/**\n * Combine a list of conditions with the `and` operator. Conditions\n * that are equal `undefined` are automatically ignored.\n *\n * ## Examples\n *\n * ```ts\n * db.select().from(cars)\n *   .where(\n *     and(\n *       eq(cars.make, 'Volvo'),\n *       eq(cars.year, 1950),\n *     )\n *   )\n * ```\n */\nexport function and(...conditions: (SQLWrapper | undefined)[]): SQL | undefined;\nexport function and(\n\t...unfilteredConditions: (SQLWrapper | undefined)[]\n): SQL | undefined {\n\tconst conditions = unfilteredConditions.filter(\n\t\t(c): c is Exclude<typeof c, undefined> => c !== undefined,\n\t);\n\n\tif (conditions.length === 0) {\n\t\treturn undefined;\n\t}\n\n\tif (conditions.length === 1) {\n\t\treturn new SQL(conditions);\n\t}\n\n\treturn new SQL([\n\t\tnew StringChunk('('),\n\t\tsql.join(conditions, new StringChunk(' and ')),\n\t\tnew StringChunk(')'),\n\t]);\n}\n\n/**\n * Combine a list of conditions with the `or` operator. Conditions\n * that are equal `undefined` are automatically ignored.\n *\n * ## Examples\n *\n * ```ts\n * db.select().from(cars)\n *   .where(\n *     or(\n *       eq(cars.make, 'GM'),\n *       eq(cars.make, 'Ford'),\n *     )\n *   )\n * ```\n */\nexport function or(...conditions: (SQLWrapper | undefined)[]): SQL | undefined;\nexport function or(\n\t...unfilteredConditions: (SQLWrapper | undefined)[]\n): SQL | undefined {\n\tconst conditions = unfilteredConditions.filter(\n\t\t(c): c is Exclude<typeof c, undefined> => c !== undefined,\n\t);\n\n\tif (conditions.length === 0) {\n\t\treturn undefined;\n\t}\n\n\tif (conditions.length === 1) {\n\t\treturn new SQL(conditions);\n\t}\n\n\treturn new SQL([\n\t\tnew StringChunk('('),\n\t\tsql.join(conditions, new StringChunk(' or ')),\n\t\tnew StringChunk(')'),\n\t]);\n}\n\n/**\n * Negate the meaning of an expression using the `not` keyword.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars _not_ made by GM or Ford.\n * db.select().from(cars)\n *   .where(not(inArray(cars.make, ['GM', 'Ford'])))\n * ```\n */\nexport function not(condition: SQLWrapper): SQL {\n\treturn sql`not ${condition}`;\n}\n\n/**\n * Test that the first expression passed is greater than\n * the second expression.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made after 2000.\n * db.select().from(cars)\n *   .where(gt(cars.year, 2000))\n * ```\n *\n * @see gte for greater-than-or-equal\n */\nexport const gt: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} > ${bindIfParam(right, left)}`;\n};\n\n/**\n * Test that the first expression passed is greater than\n * or equal to the second expression. Use `gt` to\n * test whether an expression is strictly greater\n * than another.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made on or after 2000.\n * db.select().from(cars)\n *   .where(gte(cars.year, 2000))\n * ```\n *\n * @see gt for a strictly greater-than condition\n */\nexport const gte: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} >= ${bindIfParam(right, left)}`;\n};\n\n/**\n * Test that the first expression passed is less than\n * the second expression.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made before 2000.\n * db.select().from(cars)\n *   .where(lt(cars.year, 2000))\n * ```\n *\n * @see lte for greater-than-or-equal\n */\nexport const lt: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} < ${bindIfParam(right, left)}`;\n};\n\n/**\n * Test that the first expression passed is less than\n * or equal to the second expression.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made before 2000.\n * db.select().from(cars)\n *   .where(lte(cars.year, 2000))\n * ```\n *\n * @see lt for a strictly less-than condition\n */\nexport const lte: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} <= ${bindIfParam(right, left)}`;\n};\n\n/**\n * Test whether the first parameter, a column or expression,\n * has a value from a list passed as the second argument.\n *\n * ## Throws\n *\n * The argument passed in the second array can't be empty:\n * if an empty is provided, this method will throw.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made by Ford or GM.\n * db.select().from(cars)\n *   .where(inArray(cars.make, ['Ford', 'GM']))\n * ```\n *\n * @see notInArray for the inverse of this test\n */\nexport function inArray<T>(\n\tcolumn: SQL.Aliased<T>,\n\tvalues: (T | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function inArray<TColumn extends Column>(\n\tcolumn: TColumn,\n\tvalues: (GetColumnData<TColumn, 'raw'> | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function inArray<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function inArray(\n\tcolumn: SQLWrapper,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL {\n\tif (Array.isArray(values)) {\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('inArray requires at least one value');\n\t\t}\n\t\treturn sql`${column} in ${values.map((v) => bindIfParam(v, column))}`;\n\t}\n\n\treturn sql`${column} in ${bindIfParam(values, column)}`;\n}\n\n/**\n * Test whether the first parameter, a column or expression,\n * has a value that is not present in a list passed as the\n * second argument.\n *\n * ## Throws\n *\n * The argument passed in the second array can't be empty:\n * if an empty is provided, this method will throw.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made by any company except Ford or GM.\n * db.select().from(cars)\n *   .where(notInArray(cars.make, ['Ford', 'GM']))\n * ```\n *\n * @see inArray for the inverse of this test\n */\nexport function notInArray<T>(\n\tcolumn: SQL.Aliased<T>,\n\tvalues: (T | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function notInArray<TColumn extends Column>(\n\tcolumn: TColumn,\n\tvalues: (GetColumnData<TColumn, 'raw'> | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function notInArray<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function notInArray(\n\tcolumn: SQLWrapper,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL {\n\tif (Array.isArray(values)) {\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('notInArray requires at least one value');\n\t\t}\n\t\treturn sql`${column} not in ${values.map((v) => bindIfParam(v, column))}`;\n\t}\n\n\treturn sql`${column} not in ${bindIfParam(values, column)}`;\n}\n\n/**\n * Test whether an expression is NULL. By the SQL standard,\n * NULL is neither equal nor not equal to itself, so\n * it's recommended to use `isNull` and `notIsNull` for\n * comparisons to NULL.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars that have no discontinuedAt date.\n * db.select().from(cars)\n *   .where(isNull(cars.discontinuedAt))\n * ```\n *\n * @see isNotNull for the inverse of this test\n */\nexport function isNull(value: SQLWrapper): SQL {\n\treturn sql`${value} is null`;\n}\n\n/**\n * Test whether an expression is not NULL. By the SQL standard,\n * NULL is neither equal nor not equal to itself, so\n * it's recommended to use `isNull` and `notIsNull` for\n * comparisons to NULL.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars that have been discontinued.\n * db.select().from(cars)\n *   .where(isNotNull(cars.discontinuedAt))\n * ```\n *\n * @see isNull for the inverse of this test\n */\nexport function isNotNull(value: SQLWrapper): SQL {\n\treturn sql`${value} is not null`;\n}\n\n/**\n * Test whether a subquery evaluates to have any rows.\n *\n * ## Examples\n *\n * ```ts\n * // Users whose `homeCity` column has a match in a cities\n * // table.\n * db\n *   .select()\n *   .from(users)\n *   .where(\n *     exists(db.select()\n *       .from(cities)\n *       .where(eq(users.homeCity, cities.id))),\n *   );\n * ```\n *\n * @see notExists for the inverse of this test\n */\nexport function exists(subquery: SQLWrapper): SQL {\n\treturn sql`exists ${subquery}`;\n}\n\n/**\n * Test whether a subquery doesn't include any result\n * rows.\n *\n * ## Examples\n *\n * ```ts\n * // Users whose `homeCity` column doesn't match\n * // a row in the cities table.\n * db\n *   .select()\n *   .from(users)\n *   .where(\n *     notExists(db.select()\n *       .from(cities)\n *       .where(eq(users.homeCity, cities.id))),\n *   );\n * ```\n *\n * @see exists for the inverse of this test\n */\nexport function notExists(subquery: SQLWrapper): SQL {\n\treturn sql`not exists ${subquery}`;\n}\n\n/**\n * Test whether an expression is between two values. This\n * is an easier way to express range tests, which would be\n * expressed mathematically as `x <= a <= y` but in SQL\n * would have to be like `a >= x AND a <= y`.\n *\n * Between is inclusive of the endpoints: if `column`\n * is equal to `min` or `max`, it will be TRUE.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made between 1990 and 2000\n * db.select().from(cars)\n *   .where(between(cars.year, 1990, 2000))\n * ```\n *\n * @see notBetween for the inverse of this test\n */\nexport function between<T>(\n\tcolumn: SQL.Aliased,\n\tmin: T | SQLWrapper,\n\tmax: T | SQLWrapper,\n): SQL;\nexport function between<TColumn extends AnyColumn>(\n\tcolumn: TColumn,\n\tmin: GetColumnData<TColumn, 'raw'> | SQLWrapper,\n\tmax: GetColumnData<TColumn, 'raw'> | SQLWrapper,\n): SQL;\nexport function between<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tmin: unknown,\n\tmax: unknown,\n): SQL;\nexport function between(column: SQLWrapper, min: unknown, max: unknown): SQL {\n\treturn sql`${column} between ${bindIfParam(min, column)} and ${\n\t\tbindIfParam(\n\t\t\tmax,\n\t\t\tcolumn,\n\t\t)\n\t}`;\n}\n\n/**\n * Test whether an expression is not between two values.\n *\n * This, like `between`, includes its endpoints, so if\n * the `column` is equal to `min` or `max`, in this case\n * it will evaluate to FALSE.\n *\n * ## Examples\n *\n * ```ts\n * // Exclude cars made in the 1970s\n * db.select().from(cars)\n *   .where(notBetween(cars.year, 1970, 1979))\n * ```\n *\n * @see between for the inverse of this test\n */\nexport function notBetween<T>(\n\tcolumn: SQL.Aliased,\n\tmin: T | SQLWrapper,\n\tmax: T | SQLWrapper,\n): SQL;\nexport function notBetween<TColumn extends AnyColumn>(\n\tcolumn: TColumn,\n\tmin: GetColumnData<TColumn, 'raw'> | SQLWrapper,\n\tmax: GetColumnData<TColumn, 'raw'> | SQLWrapper,\n): SQL;\nexport function notBetween<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tmin: unknown,\n\tmax: unknown,\n): SQL;\nexport function notBetween(\n\tcolumn: SQLWrapper,\n\tmin: unknown,\n\tmax: unknown,\n): SQL {\n\treturn sql`${column} not between ${\n\t\tbindIfParam(\n\t\t\tmin,\n\t\t\tcolumn,\n\t\t)\n\t} and ${bindIfParam(max, column)}`;\n}\n\n/**\n * Compare a column to a pattern, which can include `%` and `_`\n * characters to match multiple variations. Including `%`\n * in the pattern matches zero or more characters, and including\n * `_` will match a single character.\n *\n * ## Examples\n *\n * ```ts\n * // Select all cars with 'Turbo' in their names.\n * db.select().from(cars)\n *   .where(like(cars.name, '%Turbo%'))\n * ```\n *\n * @see ilike for a case-insensitive version of this condition\n */\nexport function like(column: Column, value: string | SQLWrapper): SQL {\n\treturn sql`${column} like ${value}`;\n}\n\n/**\n * The inverse of like - this tests that a given column\n * does not match a pattern, which can include `%` and `_`\n * characters to match multiple variations. Including `%`\n * in the pattern matches zero or more characters, and including\n * `_` will match a single character.\n *\n * ## Examples\n *\n * ```ts\n * // Select all cars that don't have \"ROver\" in their name.\n * db.select().from(cars)\n *   .where(notLike(cars.name, '%Rover%'))\n * ```\n *\n * @see like for the inverse condition\n * @see notIlike for a case-insensitive version of this condition\n */\nexport function notLike(column: Column, value: string | SQLWrapper): SQL {\n\treturn sql`${column} not like ${value}`;\n}\n\n/**\n * Case-insensitively compare a column to a pattern,\n * which can include `%` and `_`\n * characters to match multiple variations. Including `%`\n * in the pattern matches zero or more characters, and including\n * `_` will match a single character.\n *\n * Unlike like, this performs a case-insensitive comparison.\n *\n * ## Examples\n *\n * ```ts\n * // Select all cars with 'Turbo' in their names.\n * db.select().from(cars)\n *   .where(ilike(cars.name, '%Turbo%'))\n * ```\n *\n * @see like for a case-sensitive version of this condition\n */\nexport function ilike(column: Column, value: string | SQLWrapper): SQL {\n\treturn sql`${column} ilike ${value}`;\n}\n\n/**\n * The inverse of ilike - this case-insensitively tests that a given column\n * does not match a pattern, which can include `%` and `_`\n * characters to match multiple variations. Including `%`\n * in the pattern matches zero or more characters, and including\n * `_` will match a single character.\n *\n * ## Examples\n *\n * ```ts\n * // Select all cars that don't have \"Rover\" in their name.\n * db.select().from(cars)\n *   .where(notLike(cars.name, '%Rover%'))\n * ```\n *\n * @see ilike for the inverse condition\n * @see notLike for a case-sensitive version of this condition\n */\nexport function notIlike(column: Column, value: string | SQLWrapper): SQL {\n\treturn sql`${column} not ilike ${value}`;\n}\n\n/**\n * Test that a column or expression contains all elements of\n * the list passed as the second argument.\n *\n * ## Throws\n *\n * The argument passed in the second array can't be empty:\n * if an empty is provided, this method will throw.\n *\n * ## Examples\n *\n * ```ts\n * // Select posts where its tags contain \"Typescript\" and \"ORM\".\n * db.select().from(posts)\n *   .where(arrayContains(posts.tags, ['Typescript', 'ORM']))\n * ```\n *\n * @see arrayContained to find if an array contains all elements of a column or expression\n * @see arrayOverlaps to find if a column or expression contains any elements of an array\n */\nexport function arrayContains<T>(\n\tcolumn: SQL.Aliased<T>,\n\tvalues: (T | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayContains<TColumn extends Column>(\n\tcolumn: TColumn,\n\tvalues: (GetColumnData<TColumn, 'raw'> | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayContains<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function arrayContains(\n\tcolumn: SQLWrapper,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL {\n\tif (Array.isArray(values)) {\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('arrayContains requires at least one value');\n\t\t}\n\t\tconst array = sql`${bindIfParam(values, column)}`;\n\t\treturn sql`${column} @> ${array}`;\n\t}\n\n\treturn sql`${column} @> ${bindIfParam(values, column)}`;\n}\n\n/**\n * Test that the list passed as the second argument contains\n * all elements of a column or expression.\n *\n * ## Throws\n *\n * The argument passed in the second array can't be empty:\n * if an empty is provided, this method will throw.\n *\n * ## Examples\n *\n * ```ts\n * // Select posts where its tags contain \"Typescript\", \"ORM\" or both,\n * // but filtering posts that have additional tags.\n * db.select().from(posts)\n *   .where(arrayContained(posts.tags, ['Typescript', 'ORM']))\n * ```\n *\n * @see arrayContains to find if a column or expression contains all elements of an array\n * @see arrayOverlaps to find if a column or expression contains any elements of an array\n */\nexport function arrayContained<T>(\n\tcolumn: SQL.Aliased<T>,\n\tvalues: (T | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayContained<TColumn extends Column>(\n\tcolumn: TColumn,\n\tvalues: (GetColumnData<TColumn, 'raw'> | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayContained<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function arrayContained(\n\tcolumn: SQLWrapper,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL {\n\tif (Array.isArray(values)) {\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('arrayContained requires at least one value');\n\t\t}\n\t\tconst array = sql`${bindIfParam(values, column)}`;\n\t\treturn sql`${column} <@ ${array}`;\n\t}\n\n\treturn sql`${column} <@ ${bindIfParam(values, column)}`;\n}\n\n/**\n * Test that a column or expression contains any elements of\n * the list passed as the second argument.\n *\n * ## Throws\n *\n * The argument passed in the second array can't be empty:\n * if an empty is provided, this method will throw.\n *\n * ## Examples\n *\n * ```ts\n * // Select posts where its tags contain \"Typescript\", \"ORM\" or both.\n * db.select().from(posts)\n *   .where(arrayOverlaps(posts.tags, ['Typescript', 'ORM']))\n * ```\n *\n * @see arrayContains to find if a column or expression contains all elements of an array\n * @see arrayContained to find if an array contains all elements of a column or expression\n */\nexport function arrayOverlaps<T>(\n\tcolumn: SQL.Aliased<T>,\n\tvalues: (T | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayOverlaps<TColumn extends Column>(\n\tcolumn: TColumn,\n\tvalues: (GetColumnData<TColumn, 'raw'> | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayOverlaps<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function arrayOverlaps(\n\tcolumn: SQLWrapper,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL {\n\tif (Array.isArray(values)) {\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('arrayOverlaps requires at least one value');\n\t\t}\n\t\tconst array = sql`${bindIfParam(values, column)}`;\n\t\treturn sql`${column} && ${array}`;\n\t}\n\n\treturn sql`${column} && ${bindIfParam(values, column)}`;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAyB,cAAkC;AAC3D,SAAS,UAAU;AACnB,SAAS,aAAa;AACtB;;;;;AAaO,SAAS,YAAY,KAAA,EAAgB,MAAA,EAA8B;IACzE,8PACC,uBAAA,EAAqB,MAAM,KACxB,2PAAC,eAAA,EAAa,KAAK,KACnB,KAAC,uPAAA,EAAG,6PAAO,QAAK,KAChB,uPAAC,KAAA,EAAG,4PAAO,eAAW,KACtB,uPAAC,KAAA,EAAG,yPAAO,SAAM,KACjB,uPAAC,KAAA,EAAG,wPAAO,QAAK,KAChB,uPAAC,KAAA,EAAG,OAAO,6PAAI,GACjB;QACD,OAAO,0PAAI,QAAA,CAAM,OAAO,MAAM;IAC/B;IACA,OAAO;AACR;AAgCO,MAAM,KAAqB,CAAC,MAAkB,UAAwB;IAC5E,6PAAO,MAAA,CAAA,EAAM,IAAI,CAAA,GAAA,EAAM,YAAY,OAAO,IAAI,CAAC,CAAA,CAAA;AAChD;AAoBO,MAAM,KAAqB,CAAC,MAAkB,UAAwB;IAC5E,6PAAO,MAAA,CAAA,EAAM,IAAI,CAAA,IAAA,EAAO,YAAY,OAAO,IAAI,CAAC,CAAA,CAAA;AACjD;AAmBO,SAAS,IAAA,GACZ,oBAAA,EACe;IAClB,MAAM,aAAa,qBAAqB,MAAA,CACvC,CAAC,IAAyC,MAAM,KAAA;IAGjD,IAAI,WAAW,MAAA,KAAW,GAAG;QAC5B,OAAO,KAAA;IACR;IAEA,IAAI,WAAW,MAAA,KAAW,GAAG;QAC5B,OAAO,IAAI,4PAAA,CAAI,UAAU;IAC1B;IAEA,OAAO,0PAAI,MAAA,CAAI;QACd,0PAAI,cAAA,CAAY,GAAG;8PACnB,MAAA,CAAI,IAAA,CAAK,YAAY,0PAAI,cAAA,CAAY,OAAO,CAAC;QAC7C,0PAAI,cAAA,CAAY,GAAG;KACnB;AACF;AAmBO,SAAS,GAAA,GACZ,oBAAA,EACe;IAClB,MAAM,aAAa,qBAAqB,MAAA,CACvC,CAAC,IAAyC,MAAM,KAAA;IAGjD,IAAI,WAAW,MAAA,KAAW,GAAG;QAC5B,OAAO,KAAA;IACR;IAEA,IAAI,WAAW,MAAA,KAAW,GAAG;QAC5B,OAAO,0PAAI,MAAA,CAAI,UAAU;IAC1B;IAEA,OAAO,0PAAI,MAAA,CAAI;QACd,0PAAI,cAAA,CAAY,GAAG;8PACnB,MAAA,CAAI,IAAA,CAAK,YAAY,IAAI,oQAAA,CAAY,MAAM,CAAC;QAC5C,0PAAI,cAAA,CAAY,GAAG;KACnB;AACF;AAaO,SAAS,IAAI,SAAA,EAA4B;IAC/C,6PAAO,MAAA,CAAA,IAAA,EAAU,SAAS,CAAA,CAAA;AAC3B;AAgBO,MAAM,KAAqB,CAAC,MAAkB,UAAwB;IAC5E,6PAAO,MAAA,CAAA,EAAM,IAAI,CAAA,GAAA,EAAM,YAAY,OAAO,IAAI,CAAC,CAAA,CAAA;AAChD;AAkBO,MAAM,MAAsB,CAAC,MAAkB,UAAwB;IAC7E,6PAAO,MAAA,CAAA,EAAM,IAAI,CAAA,IAAA,EAAO,YAAY,OAAO,IAAI,CAAC,CAAA,CAAA;AACjD;AAgBO,MAAM,KAAqB,CAAC,MAAkB,UAAwB;IAC5E,6PAAO,MAAA,CAAA,EAAM,IAAI,CAAA,GAAA,EAAM,YAAY,OAAO,IAAI,CAAC,CAAA,CAAA;AAChD;AAgBO,MAAM,MAAsB,CAAC,MAAkB,UAAwB;IAC7E,OAAO,4PAAA,CAAA,EAAM,IAAI,CAAA,IAAA,EAAO,YAAY,OAAO,IAAI,CAAC,CAAA,CAAA;AACjD;AAiCO,SAAS,QACf,MAAA,EACA,MAAA,EACM;IACN,IAAI,MAAM,OAAA,CAAQ,MAAM,GAAG;QAC1B,IAAI,OAAO,MAAA,KAAW,GAAG;YACxB,MAAM,IAAI,MAAM,qCAAqC;QACtD;QACA,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,IAAA,EAAO,OAAO,GAAA,CAAI,CAAC,IAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAA,CAAA;IACpE;IAEA,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,IAAA,EAAO,YAAY,QAAQ,MAAM,CAAC,CAAA,CAAA;AACtD;AAkCO,SAAS,WACf,MAAA,EACA,MAAA,EACM;IACN,IAAI,MAAM,OAAA,CAAQ,MAAM,GAAG;QAC1B,IAAI,OAAO,MAAA,KAAW,GAAG;YACxB,MAAM,IAAI,MAAM,wCAAwC;QACzD;QACA,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,QAAA,EAAW,OAAO,GAAA,CAAI,CAAC,IAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAA,CAAA;IACxE;IAEA,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,QAAA,EAAW,YAAY,QAAQ,MAAM,CAAC,CAAA,CAAA;AAC1D;AAkBO,SAAS,OAAO,KAAA,EAAwB;IAC9C,6PAAO,MAAA,CAAA,EAAM,KAAK,CAAA,QAAA,CAAA;AACnB;AAkBO,SAAS,UAAU,KAAA,EAAwB;IACjD,6PAAO,MAAA,CAAA,EAAM,KAAK,CAAA,YAAA,CAAA;AACnB;AAsBO,SAAS,OAAO,QAAA,EAA2B;IACjD,4PAAO,OAAA,CAAA,OAAA,EAAa,QAAQ,CAAA,CAAA;AAC7B;AAuBO,SAAS,UAAU,QAAA,EAA2B;IACpD,6PAAO,MAAA,CAAA,WAAA,EAAiB,QAAQ,CAAA,CAAA;AACjC;AAoCO,SAAS,QAAQ,MAAA,EAAoB,GAAA,EAAc,GAAA,EAAmB;IAC5E,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,SAAA,EAAY,YAAY,KAAK,MAAM,CAAC,CAAA,KAAA,EACtD,YACC,KACA,QAEF,CAAA;AACD;AAkCO,SAAS,WACf,MAAA,EACA,GAAA,EACA,GAAA,EACM;IACN,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,aAAA,EAClB,YACC,KACA,QAEF,KAAA,EAAQ,YAAY,KAAK,MAAM,CAAC,CAAA,CAAA;AACjC;AAkBO,SAAS,KAAK,MAAA,EAAgB,KAAA,EAAiC;IACrE,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,MAAA,EAAS,KAAK,CAAA,CAAA;AAClC;AAoBO,SAAS,QAAQ,MAAA,EAAgB,KAAA,EAAiC;IACxE,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,UAAA,EAAa,KAAK,CAAA,CAAA;AACtC;AAqBO,SAAS,MAAM,MAAA,EAAgB,KAAA,EAAiC;IACtE,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,OAAA,EAAU,KAAK,CAAA,CAAA;AACnC;AAoBO,SAAS,SAAS,MAAA,EAAgB,KAAA,EAAiC;IACzE,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,WAAA,EAAc,KAAK,CAAA,CAAA;AACvC;AAkCO,SAAS,cACf,MAAA,EACA,MAAA,EACM;IACN,IAAI,MAAM,OAAA,CAAQ,MAAM,GAAG;QAC1B,IAAI,OAAO,MAAA,KAAW,GAAG;YACxB,MAAM,IAAI,MAAM,2CAA2C;QAC5D;QACA,MAAM,8PAAQ,MAAA,CAAA,EAAM,YAAY,QAAQ,MAAM,CAAC,CAAA,CAAA;QAC/C,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,IAAA,EAAO,KAAK,CAAA,CAAA;IAChC;IAEA,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,IAAA,EAAO,YAAY,QAAQ,MAAM,CAAC,CAAA,CAAA;AACtD;AAmCO,SAAS,eACf,MAAA,EACA,MAAA,EACM;IACN,IAAI,MAAM,OAAA,CAAQ,MAAM,GAAG;QAC1B,IAAI,OAAO,MAAA,KAAW,GAAG;YACxB,MAAM,IAAI,MAAM,4CAA4C;QAC7D;QACA,MAAM,6PAAQ,OAAA,CAAA,EAAM,YAAY,QAAQ,MAAM,CAAC,CAAA,CAAA;QAC/C,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,IAAA,EAAO,KAAK,CAAA,CAAA;IAChC;IAEA,OAAO,4PAAA,CAAA,EAAM,MAAM,CAAA,IAAA,EAAO,YAAY,QAAQ,MAAM,CAAC,CAAA,CAAA;AACtD;AAkCO,SAAS,cACf,MAAA,EACA,MAAA,EACM;IACN,IAAI,MAAM,OAAA,CAAQ,MAAM,GAAG;QAC1B,IAAI,OAAO,MAAA,KAAW,GAAG;YACxB,MAAM,IAAI,MAAM,2CAA2C;QAC5D;QACA,MAAM,QAAQ,4PAAA,CAAA,EAAM,YAAY,QAAQ,MAAM,CAAC,CAAA,CAAA;QAC/C,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,IAAA,EAAO,KAAK,CAAA,CAAA;IAChC;IAEA,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,IAAA,EAAO,YAAY,QAAQ,MAAM,CAAC,CAAA,CAAA;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/sql/expressions/select.ts"], "sourcesContent": ["import type { AnyColumn } from '../../column.ts';\nimport type { SQL, SQLWrapper } from '../sql.ts';\nimport { sql } from '../sql.ts';\n\n/**\n * Used in sorting, this specifies that the given\n * column or expression should be sorted in ascending\n * order. By the SQL standard, ascending order is the\n * default, so it is not usually necessary to specify\n * ascending sort order.\n *\n * ## Examples\n *\n * ```ts\n * // Return cars, starting with the oldest models\n * // and going in ascending order to the newest.\n * db.select().from(cars)\n *   .orderBy(asc(cars.year));\n * ```\n *\n * @see desc to sort in descending order\n */\nexport function asc(column: AnyColumn | SQLWrapper): SQL {\n\treturn sql`${column} asc`;\n}\n\n/**\n * Used in sorting, this specifies that the given\n * column or expression should be sorted in descending\n * order.\n *\n * ## Examples\n *\n * ```ts\n * // Select users, with the most recently created\n * // records coming first.\n * db.select().from(users)\n *   .orderBy(desc(users.createdAt));\n * ```\n *\n * @see asc to sort in ascending order\n */\nexport function desc(column: AnyColumn | SQLWrapper): SQL {\n\treturn sql`${column} desc`;\n}\n"], "names": [], "mappings": ";;;;AAEA,SAAS,WAAW;;AAoBb,SAAS,IAAI,MAAA,EAAqC;IACxD,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,IAAA,CAAA;AACpB;AAkBO,SAAS,KAAK,MAAA,EAAqC;IACzD,6PAAO,MAAA,CAAA,EAAM,MAAM,CAAA,KAAA,CAAA;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/relations.ts"], "sourcesContent": ["import { type AnyTable, getTableUniqueName, type InferModelFromColumns, Table } from '~/table.ts';\nimport { type AnyColumn, Column } from './column.ts';\nimport { entityKind, is } from './entity.ts';\nimport { PrimaryKeyBuilder } from './pg-core/primary-keys.ts';\nimport {\n\tand,\n\tasc,\n\tbetween,\n\tdesc,\n\teq,\n\texists,\n\tgt,\n\tgte,\n\tilike,\n\tinArray,\n\tisNotNull,\n\tisNull,\n\tlike,\n\tlt,\n\tlte,\n\tne,\n\tnot,\n\tnotBetween,\n\tnotExists,\n\tnotIlike,\n\tnotInArray,\n\tnotLike,\n\tor,\n} from './sql/expressions/index.ts';\nimport { type Placeholder, SQL, sql } from './sql/sql.ts';\nimport type { Assume, ColumnsWithTable, Equal, Simplify, ValueOrArray } from './utils.ts';\n\nexport abstract class Relation<TTableName extends string = string> {\n\tstatic readonly [entityKind]: string = 'Relation';\n\n\tdeclare readonly $brand: 'Relation';\n\treadonly referencedTableName: TTableName;\n\tfieldName!: string;\n\n\tconstructor(\n\t\treadonly sourceTable: Table,\n\t\treadonly referencedTable: AnyTable<{ name: TTableName }>,\n\t\treadonly relationName: string | undefined,\n\t) {\n\t\tthis.referencedTableName = referencedTable[Table.Symbol.Name] as TTableName;\n\t}\n\n\tabstract withFieldName(fieldName: string): Relation<TTableName>;\n}\n\nexport class Relations<\n\tTTableName extends string = string,\n\tTConfig extends Record<string, Relation> = Record<string, Relation>,\n> {\n\tstatic readonly [entityKind]: string = 'Relations';\n\n\tdeclare readonly $brand: 'Relations';\n\n\tconstructor(\n\t\treadonly table: AnyTable<{ name: TTableName }>,\n\t\treadonly config: (helpers: TableRelationsHelpers<TTableName>) => TConfig,\n\t) {}\n}\n\nexport class One<\n\tTTableName extends string = string,\n\tTIsNullable extends boolean = boolean,\n> extends Relation<TTableName> {\n\tstatic readonly [entityKind]: string = 'One';\n\n\tdeclare protected $relationBrand: 'One';\n\n\tconstructor(\n\t\tsourceTable: Table,\n\t\treferencedTable: AnyTable<{ name: TTableName }>,\n\t\treadonly config:\n\t\t\t| RelationConfig<\n\t\t\t\tTTableName,\n\t\t\t\tstring,\n\t\t\t\tAnyColumn<{ tableName: TTableName }>[]\n\t\t\t>\n\t\t\t| undefined,\n\t\treadonly isNullable: TIsNullable,\n\t) {\n\t\tsuper(sourceTable, referencedTable, config?.relationName);\n\t}\n\n\twithFieldName(fieldName: string): One<TTableName> {\n\t\tconst relation = new One(\n\t\t\tthis.sourceTable,\n\t\t\tthis.referencedTable,\n\t\t\tthis.config,\n\t\t\tthis.isNullable,\n\t\t);\n\t\trelation.fieldName = fieldName;\n\t\treturn relation;\n\t}\n}\n\nexport class Many<TTableName extends string> extends Relation<TTableName> {\n\tstatic readonly [entityKind]: string = 'Many';\n\n\tdeclare protected $relationBrand: 'Many';\n\n\tconstructor(\n\t\tsourceTable: Table,\n\t\treferencedTable: AnyTable<{ name: TTableName }>,\n\t\treadonly config: { relationName: string } | undefined,\n\t) {\n\t\tsuper(sourceTable, referencedTable, config?.relationName);\n\t}\n\n\twithFieldName(fieldName: string): Many<TTableName> {\n\t\tconst relation = new Many(\n\t\t\tthis.sourceTable,\n\t\t\tthis.referencedTable,\n\t\t\tthis.config,\n\t\t);\n\t\trelation.fieldName = fieldName;\n\t\treturn relation;\n\t}\n}\n\nexport type TableRelationsKeysOnly<\n\tTSchema extends Record<string, unknown>,\n\tTTableName extends string,\n\tK extends keyof TSchema,\n> = TSchema[K] extends Relations<TTableName> ? K : never;\n\nexport type ExtractTableRelationsFromSchema<\n\tTSchema extends Record<string, unknown>,\n\tTTableName extends string,\n> = ExtractObjectValues<\n\t{\n\t\t[\n\t\t\tK in keyof TSchema as TableRelationsKeysOnly<\n\t\t\t\tTSchema,\n\t\t\t\tTTableName,\n\t\t\t\tK\n\t\t\t>\n\t\t]: TSchema[K] extends Relations<TTableName, infer TConfig> ? TConfig : never;\n\t}\n>;\n\nexport type ExtractObjectValues<T> = T[keyof T];\n\nexport type ExtractRelationsFromTableExtraConfigSchema<\n\tTConfig extends unknown[],\n> = ExtractObjectValues<\n\t{\n\t\t[\n\t\t\tK in keyof TConfig as TConfig[K] extends Relations<any> ? K\n\t\t\t\t: never\n\t\t]: TConfig[K] extends Relations<infer TRelationConfig> ? TRelationConfig\n\t\t\t: never;\n\t}\n>;\n\nexport function getOperators() {\n\treturn {\n\t\tand,\n\t\tbetween,\n\t\teq,\n\t\texists,\n\t\tgt,\n\t\tgte,\n\t\tilike,\n\t\tinArray,\n\t\tisNull,\n\t\tisNotNull,\n\t\tlike,\n\t\tlt,\n\t\tlte,\n\t\tne,\n\t\tnot,\n\t\tnotBetween,\n\t\tnotExists,\n\t\tnotLike,\n\t\tnotIlike,\n\t\tnotInArray,\n\t\tor,\n\t\tsql,\n\t};\n}\n\nexport type Operators = ReturnType<typeof getOperators>;\n\nexport function getOrderByOperators() {\n\treturn {\n\t\tsql,\n\t\tasc,\n\t\tdesc,\n\t};\n}\n\nexport type OrderByOperators = ReturnType<typeof getOrderByOperators>;\n\nexport type FindTableByDBName<\n\tTSchema extends TablesRelationalConfig,\n\tTTableName extends string,\n> = ExtractObjectValues<\n\t{\n\t\t[\n\t\t\tK in keyof TSchema as TSchema[K]['dbName'] extends TTableName ? K\n\t\t\t\t: never\n\t\t]: TSchema[K];\n\t}\n>;\n\nexport type DBQueryConfig<\n\tTRelationType extends 'one' | 'many' = 'one' | 'many',\n\tTIsRoot extends boolean = boolean,\n\tTSchema extends TablesRelationalConfig = TablesRelationalConfig,\n\tTTableConfig extends TableRelationalConfig = TableRelationalConfig,\n> =\n\t& {\n\t\tcolumns?: {\n\t\t\t[K in keyof TTableConfig['columns']]?: boolean;\n\t\t};\n\t\twith?: {\n\t\t\t[K in keyof TTableConfig['relations']]?:\n\t\t\t\t| true\n\t\t\t\t| DBQueryConfig<\n\t\t\t\t\tTTableConfig['relations'][K] extends One ? 'one' : 'many',\n\t\t\t\t\tfalse,\n\t\t\t\t\tTSchema,\n\t\t\t\t\tFindTableByDBName<\n\t\t\t\t\t\tTSchema,\n\t\t\t\t\t\tTTableConfig['relations'][K]['referencedTableName']\n\t\t\t\t\t>\n\t\t\t\t>;\n\t\t};\n\t\textras?:\n\t\t\t| Record<string, SQL.Aliased>\n\t\t\t| ((\n\t\t\t\tfields: Simplify<\n\t\t\t\t\t[TTableConfig['columns']] extends [never] ? {}\n\t\t\t\t\t\t: TTableConfig['columns']\n\t\t\t\t>,\n\t\t\t\toperators: { sql: Operators['sql'] },\n\t\t\t) => Record<string, SQL.Aliased>);\n\t}\n\t& (TRelationType extends 'many' ?\n\t\t\t& {\n\t\t\t\twhere?:\n\t\t\t\t\t| SQL\n\t\t\t\t\t| undefined\n\t\t\t\t\t| ((\n\t\t\t\t\t\tfields: Simplify<\n\t\t\t\t\t\t\t[TTableConfig['columns']] extends [never] ? {}\n\t\t\t\t\t\t\t\t: TTableConfig['columns']\n\t\t\t\t\t\t>,\n\t\t\t\t\t\toperators: Operators,\n\t\t\t\t\t) => SQL | undefined);\n\t\t\t\torderBy?:\n\t\t\t\t\t| ValueOrArray<AnyColumn | SQL>\n\t\t\t\t\t| ((\n\t\t\t\t\t\tfields: Simplify<\n\t\t\t\t\t\t\t[TTableConfig['columns']] extends [never] ? {}\n\t\t\t\t\t\t\t\t: TTableConfig['columns']\n\t\t\t\t\t\t>,\n\t\t\t\t\t\toperators: OrderByOperators,\n\t\t\t\t\t) => ValueOrArray<AnyColumn | SQL>);\n\t\t\t\tlimit?: number | Placeholder;\n\t\t\t}\n\t\t\t& (TIsRoot extends true ? {\n\t\t\t\t\toffset?: number | Placeholder;\n\t\t\t\t}\n\t\t\t\t: {})\n\t\t: {});\n\nexport interface TableRelationalConfig {\n\ttsName: string;\n\tdbName: string;\n\tcolumns: Record<string, Column>;\n\trelations: Record<string, Relation>;\n\tprimaryKey: AnyColumn[];\n\tschema?: string;\n}\n\nexport type TablesRelationalConfig = Record<string, TableRelationalConfig>;\n\nexport interface RelationalSchemaConfig<\n\tTSchema extends TablesRelationalConfig,\n> {\n\tfullSchema: Record<string, unknown>;\n\tschema: TSchema;\n\ttableNamesMap: Record<string, string>;\n}\n\nexport type ExtractTablesWithRelations<\n\tTSchema extends Record<string, unknown>,\n> = {\n\t[\n\t\tK in keyof TSchema as TSchema[K] extends Table ? K\n\t\t\t: never\n\t]: TSchema[K] extends Table ? {\n\t\t\ttsName: K & string;\n\t\t\tdbName: TSchema[K]['_']['name'];\n\t\t\tcolumns: TSchema[K]['_']['columns'];\n\t\t\trelations: ExtractTableRelationsFromSchema<\n\t\t\t\tTSchema,\n\t\t\t\tTSchema[K]['_']['name']\n\t\t\t>;\n\t\t\tprimaryKey: AnyColumn[];\n\t\t}\n\t\t: never;\n};\n\nexport type ReturnTypeOrValue<T> = T extends (...args: any[]) => infer R ? R\n\t: T;\n\nexport type BuildRelationResult<\n\tTSchema extends TablesRelationalConfig,\n\tTInclude,\n\tTRelations extends Record<string, Relation>,\n> = {\n\t[\n\t\tK in\n\t\t\t& NonUndefinedKeysOnly<TInclude>\n\t\t\t& keyof TRelations\n\t]: TRelations[K] extends infer TRel extends Relation ? BuildQueryResult<\n\t\t\tTSchema,\n\t\t\tFindTableByDBName<TSchema, TRel['referencedTableName']>,\n\t\t\tAssume<TInclude[K], true | Record<string, unknown>>\n\t\t> extends infer TResult ? TRel extends One ?\n\t\t\t\t\t| TResult\n\t\t\t\t\t| (Equal<TRel['isNullable'], false> extends true ? null : never)\n\t\t\t: TResult[]\n\t\t: never\n\t\t: never;\n};\n\nexport type NonUndefinedKeysOnly<T> =\n\t& ExtractObjectValues<\n\t\t{\n\t\t\t[K in keyof T as T[K] extends undefined ? never : K]: K;\n\t\t}\n\t>\n\t& keyof T;\n\nexport type BuildQueryResult<\n\tTSchema extends TablesRelationalConfig,\n\tTTableConfig extends TableRelationalConfig,\n\tTFullSelection extends true | Record<string, unknown>,\n> = Equal<TFullSelection, true> extends true ? InferModelFromColumns<TTableConfig['columns']>\n\t: TFullSelection extends Record<string, unknown> ? Simplify<\n\t\t\t& (TFullSelection['columns'] extends Record<string, unknown> ? InferModelFromColumns<\n\t\t\t\t\t{\n\t\t\t\t\t\t[\n\t\t\t\t\t\t\tK in Equal<\n\t\t\t\t\t\t\t\tExclude<\n\t\t\t\t\t\t\t\t\tTFullSelection['columns'][\n\t\t\t\t\t\t\t\t\t\t& keyof TFullSelection['columns']\n\t\t\t\t\t\t\t\t\t\t& keyof TTableConfig['columns']\n\t\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t\t\tundefined\n\t\t\t\t\t\t\t\t>,\n\t\t\t\t\t\t\t\tfalse\n\t\t\t\t\t\t\t> extends true ? Exclude<\n\t\t\t\t\t\t\t\t\tkeyof TTableConfig['columns'],\n\t\t\t\t\t\t\t\t\tNonUndefinedKeysOnly<TFullSelection['columns']>\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t:\n\t\t\t\t\t\t\t\t\t& {\n\t\t\t\t\t\t\t\t\t\t[K in keyof TFullSelection['columns']]: Equal<\n\t\t\t\t\t\t\t\t\t\t\tTFullSelection['columns'][K],\n\t\t\t\t\t\t\t\t\t\t\ttrue\n\t\t\t\t\t\t\t\t\t\t> extends true ? K\n\t\t\t\t\t\t\t\t\t\t\t: never;\n\t\t\t\t\t\t\t\t\t}[keyof TFullSelection['columns']]\n\t\t\t\t\t\t\t\t\t& keyof TTableConfig['columns']\n\t\t\t\t\t\t]: TTableConfig['columns'][K];\n\t\t\t\t\t}\n\t\t\t\t>\n\t\t\t\t: InferModelFromColumns<TTableConfig['columns']>)\n\t\t\t& (TFullSelection['extras'] extends\n\t\t\t\t| Record<string, unknown>\n\t\t\t\t| ((...args: any[]) => Record<string, unknown>) ? {\n\t\t\t\t\t[\n\t\t\t\t\t\tK in NonUndefinedKeysOnly<\n\t\t\t\t\t\t\tReturnTypeOrValue<TFullSelection['extras']>\n\t\t\t\t\t\t>\n\t\t\t\t\t]: Assume<\n\t\t\t\t\t\tReturnTypeOrValue<TFullSelection['extras']>[K],\n\t\t\t\t\t\tSQL.Aliased\n\t\t\t\t\t>['_']['type'];\n\t\t\t\t}\n\t\t\t\t: {})\n\t\t\t& (TFullSelection['with'] extends Record<string, unknown> ? BuildRelationResult<\n\t\t\t\t\tTSchema,\n\t\t\t\t\tTFullSelection['with'],\n\t\t\t\t\tTTableConfig['relations']\n\t\t\t\t>\n\t\t\t\t: {})\n\t\t>\n\t: never;\n\nexport interface RelationConfig<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends AnyColumn<{ tableName: TTableName }>[],\n> {\n\trelationName?: string;\n\tfields: TColumns;\n\treferences: ColumnsWithTable<TTableName, TForeignTableName, TColumns>;\n}\n\nexport function extractTablesRelationalConfig<\n\tTTables extends TablesRelationalConfig,\n>(\n\tschema: Record<string, unknown>,\n\tconfigHelpers: (table: Table) => any,\n): { tables: TTables; tableNamesMap: Record<string, string> } {\n\tif (\n\t\tObject.keys(schema).length === 1\n\t\t&& 'default' in schema\n\t\t&& !is(schema['default'], Table)\n\t) {\n\t\tschema = schema['default'] as Record<string, unknown>;\n\t}\n\n\t// table DB name -> schema table key\n\tconst tableNamesMap: Record<string, string> = {};\n\t// Table relations found before their tables - need to buffer them until we know the schema table key\n\tconst relationsBuffer: Record<\n\t\tstring,\n\t\t{ relations: Record<string, Relation>; primaryKey?: AnyColumn[] }\n\t> = {};\n\tconst tablesConfig: TablesRelationalConfig = {};\n\tfor (const [key, value] of Object.entries(schema)) {\n\t\tif (is(value, Table)) {\n\t\t\tconst dbName = getTableUniqueName(value);\n\t\t\tconst bufferedRelations = relationsBuffer[dbName];\n\t\t\ttableNamesMap[dbName] = key;\n\t\t\ttablesConfig[key] = {\n\t\t\t\ttsName: key,\n\t\t\t\tdbName: value[Table.Symbol.Name],\n\t\t\t\tschema: value[Table.Symbol.Schema],\n\t\t\t\tcolumns: value[Table.Symbol.Columns],\n\t\t\t\trelations: bufferedRelations?.relations ?? {},\n\t\t\t\tprimaryKey: bufferedRelations?.primaryKey ?? [],\n\t\t\t};\n\n\t\t\t// Fill in primary keys\n\t\t\tfor (\n\t\t\t\tconst column of Object.values(\n\t\t\t\t\t(value as Table)[Table.Symbol.Columns],\n\t\t\t\t)\n\t\t\t) {\n\t\t\t\tif (column.primary) {\n\t\t\t\t\ttablesConfig[key]!.primaryKey.push(column);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst extraConfig = value[Table.Symbol.ExtraConfigBuilder]?.((value as Table)[Table.Symbol.ExtraConfigColumns]);\n\t\t\tif (extraConfig) {\n\t\t\t\tfor (const configEntry of Object.values(extraConfig)) {\n\t\t\t\t\tif (is(configEntry, PrimaryKeyBuilder)) {\n\t\t\t\t\t\ttablesConfig[key]!.primaryKey.push(...configEntry.columns);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (is(value, Relations)) {\n\t\t\tconst dbName = getTableUniqueName(value.table);\n\t\t\tconst tableName = tableNamesMap[dbName];\n\t\t\tconst relations: Record<string, Relation> = value.config(\n\t\t\t\tconfigHelpers(value.table),\n\t\t\t);\n\t\t\tlet primaryKey: AnyColumn[] | undefined;\n\n\t\t\tfor (const [relationName, relation] of Object.entries(relations)) {\n\t\t\t\tif (tableName) {\n\t\t\t\t\tconst tableConfig = tablesConfig[tableName]!;\n\t\t\t\t\ttableConfig.relations[relationName] = relation;\n\t\t\t\t\tif (primaryKey) {\n\t\t\t\t\t\ttableConfig.primaryKey.push(...primaryKey);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tif (!(dbName in relationsBuffer)) {\n\t\t\t\t\t\trelationsBuffer[dbName] = {\n\t\t\t\t\t\t\trelations: {},\n\t\t\t\t\t\t\tprimaryKey,\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\trelationsBuffer[dbName]!.relations[relationName] = relation;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn { tables: tablesConfig as TTables, tableNamesMap };\n}\n\nexport function relations<\n\tTTableName extends string,\n\tTRelations extends Record<string, Relation<any>>,\n>(\n\ttable: AnyTable<{ name: TTableName }>,\n\trelations: (helpers: TableRelationsHelpers<TTableName>) => TRelations,\n): Relations<TTableName, TRelations> {\n\treturn new Relations<TTableName, TRelations>(\n\t\ttable,\n\t\t(helpers: TableRelationsHelpers<TTableName>) =>\n\t\t\tObject.fromEntries(\n\t\t\t\tObject.entries(relations(helpers)).map(([key, value]) => [\n\t\t\t\t\tkey,\n\t\t\t\t\tvalue.withFieldName(key),\n\t\t\t\t]),\n\t\t\t) as TRelations,\n\t);\n}\n\nexport function createOne<TTableName extends string>(sourceTable: Table) {\n\treturn function one<\n\t\tTForeignTable extends Table,\n\t\tTColumns extends [\n\t\t\tAnyColumn<{ tableName: TTableName }>,\n\t\t\t...AnyColumn<{ tableName: TTableName }>[],\n\t\t],\n\t>(\n\t\ttable: TForeignTable,\n\t\tconfig?: RelationConfig<TTableName, TForeignTable['_']['name'], TColumns>,\n\t): One<\n\t\tTForeignTable['_']['name'],\n\t\tEqual<TColumns[number]['_']['notNull'], true>\n\t> {\n\t\treturn new One(\n\t\t\tsourceTable,\n\t\t\ttable,\n\t\t\tconfig,\n\t\t\t(config?.fields.reduce<boolean>((res, f) => res && f.notNull, true)\n\t\t\t\t?? false) as Equal<TColumns[number]['_']['notNull'], true>,\n\t\t);\n\t};\n}\n\nexport function createMany(sourceTable: Table) {\n\treturn function many<TForeignTable extends Table>(\n\t\treferencedTable: TForeignTable,\n\t\tconfig?: { relationName: string },\n\t): Many<TForeignTable['_']['name']> {\n\t\treturn new Many(sourceTable, referencedTable, config);\n\t};\n}\n\nexport interface NormalizedRelation {\n\tfields: AnyColumn[];\n\treferences: AnyColumn[];\n}\n\nexport function normalizeRelation(\n\tschema: TablesRelationalConfig,\n\ttableNamesMap: Record<string, string>,\n\trelation: Relation,\n): NormalizedRelation {\n\tif (is(relation, One) && relation.config) {\n\t\treturn {\n\t\t\tfields: relation.config.fields,\n\t\t\treferences: relation.config.references,\n\t\t};\n\t}\n\n\tconst referencedTableTsName = tableNamesMap[getTableUniqueName(relation.referencedTable)];\n\tif (!referencedTableTsName) {\n\t\tthrow new Error(\n\t\t\t`Table \"${relation.referencedTable[Table.Symbol.Name]}\" not found in schema`,\n\t\t);\n\t}\n\n\tconst referencedTableConfig = schema[referencedTableTsName];\n\tif (!referencedTableConfig) {\n\t\tthrow new Error(`Table \"${referencedTableTsName}\" not found in schema`);\n\t}\n\n\tconst sourceTable = relation.sourceTable;\n\tconst sourceTableTsName = tableNamesMap[getTableUniqueName(sourceTable)];\n\tif (!sourceTableTsName) {\n\t\tthrow new Error(\n\t\t\t`Table \"${sourceTable[Table.Symbol.Name]}\" not found in schema`,\n\t\t);\n\t}\n\n\tconst reverseRelations: Relation[] = [];\n\tfor (\n\t\tconst referencedTableRelation of Object.values(\n\t\t\treferencedTableConfig.relations,\n\t\t)\n\t) {\n\t\tif (\n\t\t\t(relation.relationName\n\t\t\t\t&& relation !== referencedTableRelation\n\t\t\t\t&& referencedTableRelation.relationName === relation.relationName)\n\t\t\t|| (!relation.relationName\n\t\t\t\t&& referencedTableRelation.referencedTable === relation.sourceTable)\n\t\t) {\n\t\t\treverseRelations.push(referencedTableRelation);\n\t\t}\n\t}\n\n\tif (reverseRelations.length > 1) {\n\t\tthrow relation.relationName\n\t\t\t? new Error(\n\t\t\t\t`There are multiple relations with name \"${relation.relationName}\" in table \"${referencedTableTsName}\"`,\n\t\t\t)\n\t\t\t: new Error(\n\t\t\t\t`There are multiple relations between \"${referencedTableTsName}\" and \"${\n\t\t\t\t\trelation.sourceTable[Table.Symbol.Name]\n\t\t\t\t}\". Please specify relation name`,\n\t\t\t);\n\t}\n\n\tif (\n\t\treverseRelations[0]\n\t\t&& is(reverseRelations[0], One)\n\t\t&& reverseRelations[0].config\n\t) {\n\t\treturn {\n\t\t\tfields: reverseRelations[0].config.references,\n\t\t\treferences: reverseRelations[0].config.fields,\n\t\t};\n\t}\n\n\tthrow new Error(\n\t\t`There is not enough information to infer relation \"${sourceTableTsName}.${relation.fieldName}\"`,\n\t);\n}\n\nexport function createTableRelationsHelpers<TTableName extends string>(\n\tsourceTable: AnyTable<{ name: TTableName }>,\n) {\n\treturn {\n\t\tone: createOne<TTableName>(sourceTable),\n\t\tmany: createMany(sourceTable),\n\t};\n}\n\nexport type TableRelationsHelpers<TTableName extends string> = ReturnType<\n\ttypeof createTableRelationsHelpers<TTableName>\n>;\n\nexport interface BuildRelationalQueryResult<\n\tTTable extends Table = Table,\n\tTColumn extends Column = Column,\n> {\n\ttableTsKey: string;\n\tselection: {\n\t\tdbKey: string;\n\t\ttsKey: string;\n\t\tfield: TColumn | SQL | SQL.Aliased;\n\t\trelationTableTsKey: string | undefined;\n\t\tisJson: boolean;\n\t\tisExtra?: boolean;\n\t\tselection: BuildRelationalQueryResult<TTable>['selection'];\n\t}[];\n\tsql: TTable | SQL;\n}\n\nexport function mapRelationalRow(\n\ttablesConfig: TablesRelationalConfig,\n\ttableConfig: TableRelationalConfig,\n\trow: unknown[],\n\tbuildQueryResultSelection: BuildRelationalQueryResult['selection'],\n\tmapColumnValue: (value: unknown) => unknown = (value) => value,\n): Record<string, unknown> {\n\tconst result: Record<string, unknown> = {};\n\n\tfor (\n\t\tconst [\n\t\t\tselectionItemIndex,\n\t\t\tselectionItem,\n\t\t] of buildQueryResultSelection.entries()\n\t) {\n\t\tif (selectionItem.isJson) {\n\t\t\tconst relation = tableConfig.relations[selectionItem.tsKey]!;\n\t\t\tconst rawSubRows = row[selectionItemIndex] as\n\t\t\t\t| unknown[]\n\t\t\t\t| null\n\t\t\t\t| [null]\n\t\t\t\t| string;\n\t\t\tconst subRows = typeof rawSubRows === 'string'\n\t\t\t\t? (JSON.parse(rawSubRows) as unknown[])\n\t\t\t\t: rawSubRows;\n\t\t\tresult[selectionItem.tsKey] = is(relation, One)\n\t\t\t\t? subRows\n\t\t\t\t\t&& mapRelationalRow(\n\t\t\t\t\t\ttablesConfig,\n\t\t\t\t\t\ttablesConfig[selectionItem.relationTableTsKey!]!,\n\t\t\t\t\t\tsubRows,\n\t\t\t\t\t\tselectionItem.selection,\n\t\t\t\t\t\tmapColumnValue,\n\t\t\t\t\t)\n\t\t\t\t: (subRows as unknown[][]).map((subRow) =>\n\t\t\t\t\tmapRelationalRow(\n\t\t\t\t\t\ttablesConfig,\n\t\t\t\t\t\ttablesConfig[selectionItem.relationTableTsKey!]!,\n\t\t\t\t\t\tsubRow,\n\t\t\t\t\t\tselectionItem.selection,\n\t\t\t\t\t\tmapColumnValue,\n\t\t\t\t\t)\n\t\t\t\t);\n\t\t} else {\n\t\t\tconst value = mapColumnValue(row[selectionItemIndex]);\n\t\t\tconst field = selectionItem.field!;\n\t\t\tlet decoder;\n\t\t\tif (is(field, Column)) {\n\t\t\t\tdecoder = field;\n\t\t\t} else if (is(field, SQL)) {\n\t\t\t\tdecoder = field.decoder;\n\t\t\t} else {\n\t\t\t\tdecoder = field.sql.decoder;\n\t\t\t}\n\t\t\tresult[selectionItem.tsKey] = value === null ? null : decoder.mapFromDriverValue(value);\n\t\t}\n\t}\n\n\treturn result;\n}\n"], "names": ["relations"], "mappings": ";;;;;;;;;;;;;;;AAAA,SAAwB,oBAAgD,aAAa;AACrF,SAAyB,cAAc;AACvC,SAAS,YAAY,UAAU;AAC/B,SAAS,yBAAyB;AAClC;;AAyBA,SAA2B,KAAK,WAAW;;;;;;;AAGpC,MAAe,SAA6C;IAOlE,YACU,WAAA,EACA,eAAA,EACA,YAAA,CACR;QAHQ,IAAA,CAAA,WAAA,GAAA;QACA,IAAA,CAAA,eAAA,GAAA;QACA,IAAA,CAAA,YAAA,GAAA;QAET,IAAA,CAAK,mBAAA,GAAsB,eAAA,kPAAgB,QAAA,CAAM,MAAA,CAAO,IAAI,CAAA;IAC7D;IAZA,OAAA,mPAAiB,aAAU,CAAA,GAAY,WAAA;IAG9B,oBAAA;IACT,UAAA;AAWD;AAEO,MAAM,UAGX;IAKD,YACU,KAAA,EACA,MAAA,CACR;QAFQ,IAAA,CAAA,KAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;IACP;IAPH,OAAA,mPAAiB,aAAU,CAAA,GAAY,YAAA;AAQxC;AAEO,MAAM,YAGH,SAAqB;IAK9B,YACC,WAAA,EACA,eAAA,EACS,MAAA,EAOA,UAAA,CACR;QACD,KAAA,CAAM,aAAa,iBAAiB,QAAQ,YAAY;QAT/C,IAAA,CAAA,MAAA,GAAA;QAOA,IAAA,CAAA,UAAA,GAAA;IAGV;IAjBA,OAAA,mPAAiB,aAAU,CAAA,GAAY,MAAA;IAmBvC,cAAc,SAAA,EAAoC;QACjD,MAAM,WAAW,IAAI,IACpB,IAAA,CAAK,WAAA,EACL,IAAA,CAAK,eAAA,EACL,IAAA,CAAK,MAAA,EACL,IAAA,CAAK,UAAA;QAEN,SAAS,SAAA,GAAY;QACrB,OAAO;IACR;AACD;AAEO,MAAM,aAAwC,SAAqB;IAKzE,YACC,WAAA,EACA,eAAA,EACS,MAAA,CACR;QACD,KAAA,CAAM,aAAa,iBAAiB,QAAQ,YAAY;QAF/C,IAAA,CAAA,MAAA,GAAA;IAGV;IAVA,OAAA,CAAiB,+PAAU,CAAA,GAAY,OAAA;IAYvC,cAAc,SAAA,EAAqC;QAClD,MAAM,WAAW,IAAI,KACpB,IAAA,CAAK,WAAA,EACL,IAAA,CAAK,eAAA,EACL,IAAA,CAAK,MAAA;QAEN,SAAS,SAAA,GAAY;QACrB,OAAO;IACR;AACD;AAqCO,SAAS,eAAe;IAC9B,OAAO;yRACN,MAAA;4RACA,WAAA;QACA,qRAAA;4RACA,SAAA;wRACA,KAAA;yRACA,MAAA;2RACA,QAAA;6RACA,UAAA;4RACA,SAAA;+RACA,YAAA;0RACA,OAAA;wRACA,KAAA;yRACA,MAAA;wRACA,KAAA;yRACA,MAAA;gSACA,aAAA;+RACA,YAAA;4RACA,WAAA;8RACA,WAAA;gSACA,aAAA;wRACA,KAAA;kQACA,OAAA;IACD;AACD;AAIO,SAAS,sBAAsB;IACrC,OAAO;mQACN,MAAA;qRACA,MAAA;QACA,qRAAA;IACD;AACD;AAuNO,SAAS,8BAGf,MAAA,EACA,aAAA,EAC6D;IAC7D,IACC,OAAO,IAAA,CAAK,MAAM,EAAE,MAAA,KAAW,KAC5B,aAAa,UACb,CAAC,2PAAA,EAAG,MAAA,CAAO,SAAS,CAAA,mPAAG,QAAK,GAC9B;QACD,SAAS,MAAA,CAAO,SAAS,CAAA;IAC1B;IAGA,MAAM,gBAAwC,CAAC;IAE/C,MAAM,kBAGF,CAAC;IACL,MAAM,eAAuC,CAAC;IAC9C,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,MAAM,EAAG;QAClD,IAAI,2PAAA,EAAG,wPAAO,QAAK,GAAG;YACrB,MAAM,8PAAS,qBAAA,EAAmB,KAAK;YACvC,MAAM,oBAAoB,eAAA,CAAgB,MAAM,CAAA;YAChD,aAAA,CAAc,MAAM,CAAA,GAAI;YACxB,YAAA,CAAa,GAAG,CAAA,GAAI;gBACnB,QAAQ;gBACR,QAAQ,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,IAAI,CAAA;gBAC/B,QAAQ,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,MAAM,CAAA;gBACjC,SAAS,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA;gBACnC,WAAW,mBAAmB,aAAa,CAAC;gBAC5C,YAAY,mBAAmB,cAAc,CAAC,CAAA;YAC/C;YAGA,KAAA,MACO,UAAU,OAAO,MAAA,CACrB,KAAA,CAAgB,yPAAA,CAAM,MAAA,CAAO,OAAO,CAAA,EAErC;gBACD,IAAI,OAAO,OAAA,EAAS;oBACnB,YAAA,CAAa,GAAG,CAAA,CAAG,UAAA,CAAW,IAAA,CAAK,MAAM;gBAC1C;YACD;YAEA,MAAM,cAAc,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,kBAAkB,CAAA,GAAK,KAAA,kPAAgB,QAAA,CAAM,MAAA,CAAO,kBAAkB,CAAC;YAC9G,IAAI,aAAa;gBAChB,KAAA,MAAW,eAAe,OAAO,MAAA,CAAO,WAAW,EAAG;oBACrD,IAAI,2PAAA,EAAG,sRAAa,oBAAiB,GAAG;wBACvC,YAAA,CAAa,GAAG,CAAA,CAAG,UAAA,CAAW,IAAA,CAAK,GAAG,YAAY,OAAO;oBAC1D;gBACD;YACD;QACD,OAAA,0PAAW,KAAA,EAAG,OAAO,SAAS,GAAG;YAChC,MAAM,6PAAS,sBAAA,EAAmB,MAAM,KAAK;YAC7C,MAAM,YAAY,aAAA,CAAc,MAAM,CAAA;YACtC,MAAMA,aAAsC,MAAM,MAAA,CACjD,cAAc,MAAM,KAAK;YAE1B,IAAI;YAEJ,KAAA,MAAW,CAAC,cAAc,QAAQ,CAAA,IAAK,OAAO,OAAA,CAAQA,UAAS,EAAG;gBACjE,IAAI,WAAW;oBACd,MAAM,cAAc,YAAA,CAAa,SAAS,CAAA;oBAC1C,YAAY,SAAA,CAAU,YAAY,CAAA,GAAI;oBACtC,IAAI,YAAY;wBACf,YAAY,UAAA,CAAW,IAAA,CAAK,GAAG,UAAU;oBAC1C;gBACD,OAAO;oBACN,IAAI,CAAA,CAAE,UAAU,eAAA,GAAkB;wBACjC,eAAA,CAAgB,MAAM,CAAA,GAAI;4BACzB,WAAW,CAAC;4BACZ;wBACD;oBACD;oBACA,eAAA,CAAgB,MAAM,CAAA,CAAG,SAAA,CAAU,YAAY,CAAA,GAAI;gBACpD;YACD;QACD;IACD;IAEA,OAAO;QAAE,QAAQ;QAAyB;IAAc;AACzD;AAEO,SAAS,UAIf,KAAA,EACAA,UAAAA,EACoC;IACpC,OAAO,IAAI,UACV,OACA,CAAC,UACA,OAAO,WAAA,CACN,OAAO,OAAA,CAAQA,WAAU,OAAO,CAAC,EAAE,GAAA,CAAI,CAAC,CAAC,KAAK,KAAK,CAAA,GAAM;gBACxD;gBACA,MAAM,aAAA,CAAc,GAAG;aACvB;AAGL;AAEO,SAAS,UAAqC,WAAA,EAAoB;IACxE,OAAO,SAAS,IAOf,KAAA,EACA,MAAA,EAIC;QACD,OAAO,IAAI,IACV,aACA,OACA,QACC,QAAQ,OAAO,OAAgB,CAAC,KAAK,IAAM,OAAO,EAAE,OAAA,EAAS,IAAI,KAC9D;IAEN;AACD;AAEO,SAAS,WAAW,WAAA,EAAoB;IAC9C,OAAO,SAAS,KACf,eAAA,EACA,MAAA,EACmC;QACnC,OAAO,IAAI,KAAK,aAAa,iBAAiB,MAAM;IACrD;AACD;AAOO,SAAS,kBACf,MAAA,EACA,aAAA,EACA,QAAA,EACqB;IACrB,KAAI,0PAAA,EAAG,UAAU,GAAG,KAAK,SAAS,MAAA,EAAQ;QACzC,OAAO;YACN,QAAQ,SAAS,MAAA,CAAO,MAAA;YACxB,YAAY,SAAS,MAAA,CAAO,UAAA;QAC7B;IACD;IAEA,MAAM,wBAAwB,aAAA,sPAAc,qBAAA,EAAmB,SAAS,eAAe,CAAC,CAAA;IACxF,IAAI,CAAC,uBAAuB;QAC3B,MAAM,IAAI,MACT,CAAA,OAAA,EAAU,SAAS,eAAA,kPAAgB,QAAA,CAAM,MAAA,CAAO,IAAI,CAAC,CAAA,qBAAA,CAAA;IAEvD;IAEA,MAAM,wBAAwB,MAAA,CAAO,qBAAqB,CAAA;IAC1D,IAAI,CAAC,uBAAuB;QAC3B,MAAM,IAAI,MAAM,CAAA,OAAA,EAAU,qBAAqB,CAAA,qBAAA,CAAuB;IACvE;IAEA,MAAM,cAAc,SAAS,WAAA;IAC7B,MAAM,oBAAoB,aAAA,CAAc,0QAAA,EAAmB,WAAW,CAAC,CAAA;IACvE,IAAI,CAAC,mBAAmB;QACvB,MAAM,IAAI,MACT,CAAA,OAAA,EAAU,WAAA,kPAAY,QAAA,CAAM,MAAA,CAAO,IAAI,CAAC,CAAA,qBAAA,CAAA;IAE1C;IAEA,MAAM,mBAA+B,CAAC,CAAA;IACtC,KAAA,MACO,2BAA2B,OAAO,MAAA,CACvC,sBAAsB,SAAA,EAEtB;QACD,IACE,SAAS,YAAA,IACN,aAAa,2BACb,wBAAwB,YAAA,KAAiB,SAAS,YAAA,IAClD,CAAC,SAAS,YAAA,IACV,wBAAwB,eAAA,KAAoB,SAAS,WAAA,EACxD;YACD,iBAAiB,IAAA,CAAK,uBAAuB;QAC9C;IACD;IAEA,IAAI,iBAAiB,MAAA,GAAS,GAAG;QAChC,MAAM,SAAS,YAAA,GACZ,IAAI,MACL,CAAA,wCAAA,EAA2C,SAAS,YAAY,CAAA,YAAA,EAAe,qBAAqB,CAAA,CAAA,CAAA,IAEnG,IAAI,MACL,CAAA,sCAAA,EAAyC,qBAAqB,CAAA,OAAA,EAC7D,SAAS,WAAA,iPAAY,SAAA,CAAM,MAAA,CAAO,IAAI,CACvC,CAAA,+BAAA,CAAA;IAEH;IAEA,IACC,gBAAA,CAAiB,CAAC,CAAA,0PACf,KAAA,EAAG,gBAAA,CAAiB,CAAC,CAAA,EAAG,GAAG,KAC3B,gBAAA,CAAiB,CAAC,CAAA,CAAE,MAAA,EACtB;QACD,OAAO;YACN,QAAQ,gBAAA,CAAiB,CAAC,CAAA,CAAE,MAAA,CAAO,UAAA;YACnC,YAAY,gBAAA,CAAiB,CAAC,CAAA,CAAE,MAAA,CAAO,MAAA;QACxC;IACD;IAEA,MAAM,IAAI,MACT,CAAA,mDAAA,EAAsD,iBAAiB,CAAA,CAAA,EAAI,SAAS,SAAS,CAAA,CAAA,CAAA;AAE/F;AAEO,SAAS,4BACf,WAAA,EACC;IACD,OAAO;QACN,KAAK,UAAsB,WAAW;QACtC,MAAM,WAAW,WAAW;IAC7B;AACD;AAuBO,SAAS,iBACf,YAAA,EACA,WAAA,EACA,GAAA,EACA,yBAAA,EACA,iBAA8C,CAAC,QAAU,KAAA,EAC/B;IAC1B,MAAM,SAAkC,CAAC;IAEzC,KAAA,MACO,CACL,oBACA,cACD,IAAK,0BAA0B,OAAA,CAAQ,EACtC;QACD,IAAI,cAAc,MAAA,EAAQ;YACzB,MAAM,WAAW,YAAY,SAAA,CAAU,cAAc,KAAK,CAAA;YAC1D,MAAM,aAAa,GAAA,CAAI,kBAAkB,CAAA;YAKzC,MAAM,UAAU,OAAO,eAAe,WAClC,KAAK,KAAA,CAAM,UAAU,IACtB;YACH,MAAA,CAAO,cAAc,KAAK,CAAA,yPAAI,KAAA,EAAG,UAAU,GAAG,IAC3C,WACE,iBACF,cACA,YAAA,CAAa,cAAc,kBAAmB,CAAA,EAC9C,SACA,cAAc,SAAA,EACd,kBAEC,QAAwB,GAAA,CAAI,CAAC,SAC/B,iBACC,cACA,YAAA,CAAa,cAAc,kBAAmB,CAAA,EAC9C,QACA,cAAc,SAAA,EACd;QAGJ,OAAO;YACN,MAAM,QAAQ,eAAe,GAAA,CAAI,kBAAkB,CAAC;YACpD,MAAM,QAAQ,cAAc,KAAA;YAC5B,IAAI;YACJ,0PAAI,KAAA,EAAG,wPAAO,UAAM,GAAG;gBACtB,UAAU;YACX,OAAA,0PAAW,KAAA,EAAG,6PAAO,MAAG,GAAG;gBAC1B,UAAU,MAAM,OAAA;YACjB,OAAO;gBACN,UAAU,MAAM,GAAA,CAAI,OAAA;YACrB;YACA,MAAA,CAAO,cAAc,KAAK,CAAA,GAAI,UAAU,OAAO,OAAO,QAAQ,kBAAA,CAAmB,KAAK;QACvF;IACD;IAEA,OAAO;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1943, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/columns/boolean.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgBooleanBuilderInitial<TName extends string> = PgBooleanBuilder<{\n\tname: TName;\n\tdataType: 'boolean';\n\tcolumnType: 'PgBoolean';\n\tdata: boolean;\n\tdriverParam: boolean;\n\tenumValues: undefined;\n}>;\n\nexport class PgBooleanBuilder<T extends ColumnBuilderBaseConfig<'boolean', 'PgBoolean'>> extends PgColumnBuilder<T> {\n\tstatic readonly [entityKind]: string = 'PgBooleanBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'boolean', 'PgBoolean');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgBoolean<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgBoolean<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgBoolean<T extends ColumnBaseConfig<'boolean', 'PgBoolean'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgBoolean';\n\n\tgetSQLType(): string {\n\t\treturn 'boolean';\n\t}\n}\n\nexport function boolean<TName extends string>(name: TName): PgBooleanBuilderInitial<TName> {\n\treturn new PgBooleanBuilder(name);\n}\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,kBAAkB;AAE3B,SAAS,UAAU,uBAAuB;;;AAWnC,MAAM,oSAAoF,kBAAA,CAAmB;IACnH,OAAA,mPAAiB,aAAU,CAAA,GAAY,mBAAA;IAEvC,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,WAAW,WAAW;IACnC;IAAA,cAAA,GAGS,MACR,KAAA,EAC6C;QAC7C,OAAO,IAAI,UAA2C,OAAO,IAAA,CAAK,MAA8C;IACjH;AACD;AAEO,MAAM,6RAAsE,WAAA,CAAY;IAC9F,OAAA,mPAAiB,aAAU,CAAA,GAAY,YAAA;IAEvC,aAAqB;QACpB,OAAO;IACR;AACD;AAEO,SAAS,QAA8B,IAAA,EAA6C;IAC1F,OAAO,IAAI,iBAAiB,IAAI;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1978, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/indexes.ts"], "sourcesContent": ["import { SQL } from '~/sql/sql.ts';\n\nimport { entityKind, is } from '~/entity.ts';\nimport type { ExtraConfigColumn, PgColumn } from './columns/index.ts';\nimport { IndexedColumn } from './columns/index.ts';\nimport type { PgTable } from './table.ts';\n\ninterface IndexConfig {\n\tname?: string;\n\n\tcolumns: Partial<IndexedColumn | SQL>[];\n\n\t/**\n\t * If true, the index will be created as `create unique index` instead of `create index`.\n\t */\n\tunique: boolean;\n\n\t/**\n\t * If true, the index will be created as `create index concurrently` instead of `create index`.\n\t */\n\tconcurrently?: boolean;\n\n\t/**\n\t * If true, the index will be created as `create index ... on only <table>` instead of `create index ... on <table>`.\n\t */\n\tonly: boolean;\n\n\t/**\n\t * Condition for partial index.\n\t */\n\twhere?: SQL;\n\n\t/**\n\t * The optional WITH clause specifies storage parameters for the index\n\t */\n\twith?: Record<string, any>;\n\n\t/**\n\t * The optional WITH clause method for the index\n\t */\n\tmethod?: 'btree' | string;\n}\n\nexport type IndexColumn = PgColumn;\n\nexport type PgIndexMethod = 'btree' | 'hash' | 'gist' | 'spgist' | 'gin' | 'brin' | 'hnsw' | 'ivfflat' | (string & {});\n\nexport type PgIndexOpClass =\n\t| 'abstime_ops'\n\t| 'access_method'\n\t| 'anyarray_eq'\n\t| 'anyarray_ge'\n\t| 'anyarray_gt'\n\t| 'anyarray_le'\n\t| 'anyarray_lt'\n\t| 'anyarray_ne'\n\t| 'bigint_ops'\n\t| 'bit_ops'\n\t| 'bool_ops'\n\t| 'box_ops'\n\t| 'bpchar_ops'\n\t| 'char_ops'\n\t| 'cidr_ops'\n\t| 'cstring_ops'\n\t| 'date_ops'\n\t| 'float_ops'\n\t| 'int2_ops'\n\t| 'int4_ops'\n\t| 'int8_ops'\n\t| 'interval_ops'\n\t| 'jsonb_ops'\n\t| 'macaddr_ops'\n\t| 'name_ops'\n\t| 'numeric_ops'\n\t| 'oid_ops'\n\t| 'oidint4_ops'\n\t| 'oidint8_ops'\n\t| 'oidname_ops'\n\t| 'oidvector_ops'\n\t| 'point_ops'\n\t| 'polygon_ops'\n\t| 'range_ops'\n\t| 'record_eq'\n\t| 'record_ge'\n\t| 'record_gt'\n\t| 'record_le'\n\t| 'record_lt'\n\t| 'record_ne'\n\t| 'text_ops'\n\t| 'time_ops'\n\t| 'timestamp_ops'\n\t| 'timestamptz_ops'\n\t| 'timetz_ops'\n\t| 'uuid_ops'\n\t| 'varbit_ops'\n\t| 'varchar_ops'\n\t// pg_vector types\n\t| 'xml_ops'\n\t| 'vector_l2_ops'\n\t| 'vector_ip_ops'\n\t| 'vector_cosine_ops'\n\t| 'vector_l1_ops'\n\t| 'bit_hamming_ops'\n\t| 'bit_jaccard_ops'\n\t| 'halfvec_l2_ops'\n\t| 'sparsevec_l2_op'\n\t| (string & {});\n\nexport class IndexBuilderOn {\n\tstatic readonly [entityKind]: string = 'PgIndexBuilderOn';\n\n\tconstructor(private unique: boolean, private name?: string) {}\n\n\ton(...columns: [Partial<ExtraConfigColumn> | SQL, ...Partial<ExtraConfigColumn>[] | SQL[]]): IndexBuilder {\n\t\treturn new IndexBuilder(\n\t\t\tcolumns.map((it) => {\n\t\t\t\tif (is(it, SQL)) {\n\t\t\t\t\treturn it;\n\t\t\t\t}\n\t\t\t\tit = it as ExtraConfigColumn;\n\t\t\t\tconst clonedIndexedColumn = new IndexedColumn(it.name, it.columnType!, it.indexConfig!);\n\t\t\t\tit.indexConfig = JSON.parse(JSON.stringify(it.defaultConfig));\n\t\t\t\treturn clonedIndexedColumn;\n\t\t\t}),\n\t\t\tthis.unique,\n\t\t\tfalse,\n\t\t\tthis.name,\n\t\t);\n\t}\n\n\tonOnly(...columns: [Partial<ExtraConfigColumn | SQL>, ...Partial<ExtraConfigColumn>[] | SQL[]]): IndexBuilder {\n\t\treturn new IndexBuilder(\n\t\t\tcolumns.map((it) => {\n\t\t\t\tif (is(it, SQL)) {\n\t\t\t\t\treturn it;\n\t\t\t\t}\n\t\t\t\tit = it as ExtraConfigColumn;\n\t\t\t\tconst clonedIndexedColumn = new IndexedColumn(it.name, it.columnType!, it.indexConfig!);\n\t\t\t\tit.indexConfig = it.defaultConfig;\n\t\t\t\treturn clonedIndexedColumn;\n\t\t\t}),\n\t\t\tthis.unique,\n\t\t\ttrue,\n\t\t\tthis.name,\n\t\t);\n\t}\n\n\t/**\n\t * Specify what index method to use. Choices are `btree`, `hash`, `gist`, `spgist`, `gin`, `brin`, or user-installed access methods like `bloom`. The default method is `btree.\n\t *\n\t * If you have the `pg_vector` extension installed in your database, you can use the `hnsw` and `ivfflat` options, which are predefined types.\n\t *\n\t * **You can always specify any string you want in the method, in case Drizzle doesn't have it natively in its types**\n\t *\n\t * @param method The name of the index method to be used\n\t * @param columns\n\t * @returns\n\t */\n\tusing(\n\t\tmethod: PgIndexMethod,\n\t\t...columns: [Partial<ExtraConfigColumn | SQL>, ...Partial<ExtraConfigColumn>[] | SQL[]]\n\t): IndexBuilder {\n\t\treturn new IndexBuilder(\n\t\t\tcolumns.map((it) => {\n\t\t\t\tif (is(it, SQL)) {\n\t\t\t\t\treturn it;\n\t\t\t\t}\n\t\t\t\tit = it as ExtraConfigColumn;\n\t\t\t\tconst clonedIndexedColumn = new IndexedColumn(it.name, it.columnType!, it.indexConfig!);\n\t\t\t\tit.indexConfig = JSON.parse(JSON.stringify(it.defaultConfig));\n\t\t\t\treturn clonedIndexedColumn;\n\t\t\t}),\n\t\t\tthis.unique,\n\t\t\ttrue,\n\t\t\tthis.name,\n\t\t\tmethod,\n\t\t);\n\t}\n}\n\nexport interface AnyIndexBuilder {\n\tbuild(table: PgTable): Index;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface IndexBuilder extends AnyIndexBuilder {}\n\nexport class IndexBuilder implements AnyIndexBuilder {\n\tstatic readonly [entityKind]: string = 'PgIndexBuilder';\n\n\t/** @internal */\n\tconfig: IndexConfig;\n\n\tconstructor(\n\t\tcolumns: Partial<IndexedColumn | SQL>[],\n\t\tunique: boolean,\n\t\tonly: boolean,\n\t\tname?: string,\n\t\tmethod: string = 'btree',\n\t) {\n\t\tthis.config = {\n\t\t\tname,\n\t\t\tcolumns,\n\t\t\tunique,\n\t\t\tonly,\n\t\t\tmethod,\n\t\t};\n\t}\n\n\tconcurrently(): this {\n\t\tthis.config.concurrently = true;\n\t\treturn this;\n\t}\n\n\twith(obj: Record<string, any>): this {\n\t\tthis.config.with = obj;\n\t\treturn this;\n\t}\n\n\twhere(condition: SQL): this {\n\t\tthis.config.where = condition;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: PgTable): Index {\n\t\treturn new Index(this.config, table);\n\t}\n}\n\nexport class Index {\n\tstatic readonly [entityKind]: string = 'PgIndex';\n\n\treadonly config: IndexConfig & { table: PgTable };\n\n\tconstructor(config: IndexConfig, table: PgTable) {\n\t\tthis.config = { ...config, table };\n\t}\n}\n\nexport type GetColumnsTableName<TColumns> = TColumns extends PgColumn ? TColumns['_']['name']\n\t: TColumns extends PgColumn[] ? TColumns[number]['_']['name']\n\t: never;\n\nexport function index(name?: string): IndexBuilderOn {\n\treturn new IndexBuilderOn(false, name);\n}\n\nexport function uniqueIndex(name?: string): IndexBuilderOn {\n\treturn new IndexBuilderOn(true, name);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA,SAAS,WAAW;AAEpB,SAAS,YAAY,UAAU;AAE/B,SAAS,qBAAqB;;;;AAwGvB,MAAM,eAAe;IAG3B,YAAoB,MAAA,EAAyB,IAAA,CAAe;QAAxC,IAAA,CAAA,MAAA,GAAA;QAAyB,IAAA,CAAA,IAAA,GAAA;IAAgB;IAF7D,OAAA,CAAiB,+PAAU,CAAA,GAAY,mBAAA;IAIvC,GAAA,GAAM,OAAA,EAAoG;QACzG,OAAO,IAAI,aACV,QAAQ,GAAA,CAAI,CAAC,OAAO;YACnB,0PAAI,KAAA,EAAG,0PAAI,MAAG,GAAG;gBAChB,OAAO;YACR;YACA,KAAK;YACL,MAAM,sBAAsB,+QAAI,gBAAA,CAAc,GAAG,IAAA,EAAM,GAAG,UAAA,EAAa,GAAG,WAAY;YACtF,GAAG,WAAA,GAAc,KAAK,KAAA,CAAM,KAAK,SAAA,CAAU,GAAG,aAAa,CAAC;YAC5D,OAAO;QACR,CAAC,GACD,IAAA,CAAK,MAAA,EACL,OACA,IAAA,CAAK,IAAA;IAEP;IAEA,OAAA,GAAU,OAAA,EAAoG;QAC7G,OAAO,IAAI,aACV,QAAQ,GAAA,CAAI,CAAC,OAAO;YACnB,0PAAI,KAAA,EAAG,IAAI,4PAAG,GAAG;gBAChB,OAAO;YACR;YACA,KAAK;YACL,MAAM,sBAAsB,+QAAI,gBAAA,CAAc,GAAG,IAAA,EAAM,GAAG,UAAA,EAAa,GAAG,WAAY;YACtF,GAAG,WAAA,GAAc,GAAG,aAAA;YACpB,OAAO;QACR,CAAC,GACD,IAAA,CAAK,MAAA,EACL,MACA,IAAA,CAAK,IAAA;IAEP;IAAA;;;;;;;;;;GAAA,GAaA,MACC,MAAA,EAAA,GACG,OAAA,EACY;QACf,OAAO,IAAI,aACV,QAAQ,GAAA,CAAI,CAAC,OAAO;YACnB,0PAAI,KAAA,EAAG,0PAAI,MAAG,GAAG;gBAChB,OAAO;YACR;YACA,KAAK;YACL,MAAM,sBAAsB,+QAAI,gBAAA,CAAc,GAAG,IAAA,EAAM,GAAG,UAAA,EAAa,GAAG,WAAY;YACtF,GAAG,WAAA,GAAc,KAAK,KAAA,CAAM,KAAK,SAAA,CAAU,GAAG,aAAa,CAAC;YAC5D,OAAO;QACR,CAAC,GACD,IAAA,CAAK,MAAA,EACL,MACA,IAAA,CAAK,IAAA,EACL;IAEF;AACD;AASO,MAAM,aAAwC;IACpD,OAAA,mPAAiB,aAAU,CAAA,GAAY,iBAAA;IAAA,cAAA,GAGvC,OAAA;IAEA,YACC,OAAA,EACA,MAAA,EACA,IAAA,EACA,IAAA,EACA,SAAiB,OAAA,CAChB;QACD,IAAA,CAAK,MAAA,GAAS;YACb;YACA;YACA;YACA;YACA;QACD;IACD;IAEA,eAAqB;QACpB,IAAA,CAAK,MAAA,CAAO,YAAA,GAAe;QAC3B,OAAO,IAAA;IACR;IAEA,KAAK,GAAA,EAAgC;QACpC,IAAA,CAAK,MAAA,CAAO,IAAA,GAAO;QACnB,OAAO,IAAA;IACR;IAEA,MAAM,SAAA,EAAsB;QAC3B,IAAA,CAAK,MAAA,CAAO,KAAA,GAAQ;QACpB,OAAO,IAAA;IACR;IAAA,cAAA,GAGA,MAAM,KAAA,EAAuB;QAC5B,OAAO,IAAI,MAAM,IAAA,CAAK,MAAA,EAAQ,KAAK;IACpC;AACD;AAEO,MAAM,MAAM;IAClB,OAAA,mPAAiB,aAAU,CAAA,GAAY,UAAA;IAE9B,OAAA;IAET,YAAY,MAAA,EAAqB,KAAA,CAAgB;QAChD,IAAA,CAAK,MAAA,GAAS;YAAE,GAAG,MAAA;YAAQ;QAAM;IAClC;AACD;AAMO,SAAS,MAAM,IAAA,EAA+B;IACpD,OAAO,IAAI,eAAe,OAAO,IAAI;AACtC;AAEO,SAAS,YAAY,IAAA,EAA+B;IAC1D,OAAO,IAAI,eAAe,MAAM,IAAI;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2093, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/columns/integer.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '../table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\ntype PgIntegerBuilderInitial<TName extends string> = PgIntegerBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'PgInteger';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class PgIntegerBuilder<T extends ColumnBuilderBaseConfig<'number', 'PgInteger'>> extends PgColumnBuilder<T> {\n\tstatic readonly [entityKind]: string = 'PgIntegerBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'PgInteger');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgInteger<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgInteger<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgInteger<T extends ColumnBaseConfig<'number', 'PgInteger'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgInteger';\n\n\tgetSQLType(): string {\n\t\treturn 'integer';\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number.parseInt(value);\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function integer<TName extends string>(name: TName): PgIntegerBuilderInitial<TName> {\n\treturn new PgIntegerBuilder(name);\n}\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,kBAAkB;AAE3B,SAAS,UAAU,uBAAuB;;;AAWnC,MAAM,oSAAmF,kBAAA,CAAmB;IAClH,OAAA,mPAAiB,aAAU,CAAA,GAAY,mBAAA;IAEvC,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,UAAU,WAAW;IAClC;IAAA,cAAA,GAGS,MACR,KAAA,EAC6C;QAC7C,OAAO,IAAI,UAA2C,OAAO,IAAA,CAAK,MAA8C;IACjH;AACD;AAEO,MAAM,6RAAqE,WAAA,CAAY;IAC7F,OAAA,mPAAiB,aAAU,CAAA,GAAY,YAAA;IAEvC,aAAqB;QACpB,OAAO;IACR;IAES,mBAAmB,KAAA,EAAgC;QAC3D,IAAI,OAAO,UAAU,UAAU;YAC9B,OAAO,OAAO,QAAA,CAAS,KAAK;QAC7B;QACA,OAAO;IACR;AACD;AAEO,SAAS,QAA8B,IAAA,EAA6C;IAC1F,OAAO,IAAI,iBAAiB,IAAI;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/columns/json.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgJsonBuilderInitial<TName extends string> = PgJsonBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'PgJson';\n\tdata: unknown;\n\tdriverParam: unknown;\n\tenumValues: undefined;\n}>;\n\nexport class PgJsonBuilder<T extends ColumnBuilderBaseConfig<'json', 'PgJson'>> extends PgColumnBuilder<\n\tT\n> {\n\tstatic readonly [entityKind]: string = 'PgJsonBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'PgJson');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgJson<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgJson<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgJson<T extends ColumnBaseConfig<'json', 'PgJson'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgJson';\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgJsonBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'json';\n\t}\n\n\toverride mapToDriverValue(value: T['data']): string {\n\t\treturn JSON.stringify(value);\n\t}\n\n\toverride mapFromDriverValue(value: T['data'] | string): T['data'] {\n\t\tif (typeof value === 'string') {\n\t\t\ttry {\n\t\t\t\treturn JSON.parse(value);\n\t\t\t} catch {\n\t\t\t\treturn value as T['data'];\n\t\t\t}\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function json<TName extends string>(name: TName): PgJsonBuilderInitial<TName> {\n\treturn new PgJsonBuilder(name);\n}\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,kBAAkB;AAE3B,SAAS,UAAU,uBAAuB;;;AAWnC,MAAM,iSAA2E,kBAAA,CAEtF;IACD,OAAA,mPAAiB,aAAU,CAAA,GAAY,gBAAA;IAEvC,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,QAAQ,QAAQ;IAC7B;IAAA,cAAA,GAGS,MACR,KAAA,EAC0C;QAC1C,OAAO,IAAI,OAAwC,OAAO,IAAA,CAAK,MAA8C;IAC9G;AACD;AAEO,MAAM,0RAA6D,WAAA,CAAY;IACrF,OAAA,mPAAiB,aAAU,CAAA,GAAY,SAAA;IAEvC,YAAY,KAAA,EAA6C,MAAA,CAAoC;QAC5F,KAAA,CAAM,OAAO,MAAM;IACpB;IAEA,aAAqB;QACpB,OAAO;IACR;IAES,iBAAiB,KAAA,EAA0B;QACnD,OAAO,KAAK,SAAA,CAAU,KAAK;IAC5B;IAES,mBAAmB,KAAA,EAAsC;QACjE,IAAI,OAAO,UAAU,UAAU;YAC9B,IAAI;gBACH,OAAO,KAAK,KAAA,CAAM,KAAK;YACxB,EAAA,OAAQ;gBACP,OAAO;YACR;QACD;QACA,OAAO;IACR;AACD;AAEO,SAAS,KAA2B,IAAA,EAA0C;IACpF,OAAO,IAAI,cAAc,IAAI;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/columns/text.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport type { Writable } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\ntype PgTextBuilderInitial<TName extends string, TEnum extends [string, ...string[]]> = PgTextBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgText';\n\tdata: TEnum[number];\n\tenumValues: TEnum;\n\tdriverParam: string;\n}>;\n\nexport class PgTextBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'PgText'>,\n> extends PgColumnBuilder<T, { enumValues: T['enumValues'] }> {\n\tstatic readonly [entityKind]: string = 'PgTextBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t\tconfig: PgTextConfig<T['enumValues']>,\n\t) {\n\t\tsuper(name, 'string', 'PgText');\n\t\tthis.config.enumValues = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgText<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgText<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgText<T extends ColumnBaseConfig<'string', 'PgText'>>\n\textends PgColumn<T, { enumValues: T['enumValues'] }>\n{\n\tstatic readonly [entityKind]: string = 'PgText';\n\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn 'text';\n\t}\n}\n\nexport interface PgTextConfig<TEnum extends readonly string[] | string[] | undefined> {\n\tenum?: TEnum;\n}\n\nexport function text<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig: PgTextConfig<T | Writable<T>> = {},\n): PgTextBuilderInitial<TName, Writable<T>> {\n\treturn new PgTextBuilder(name, config);\n}\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,kBAAkB;AAG3B,SAAS,UAAU,uBAAuB;;;AAWnC,MAAM,iSAEH,kBAAA,CAAoD;IAC7D,OAAA,mPAAiB,aAAU,CAAA,GAAY,gBAAA;IAEvC,YACC,IAAA,EACA,MAAA,CACC;QACD,KAAA,CAAM,MAAM,UAAU,QAAQ;QAC9B,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa,OAAO,IAAA;IACjC;IAAA,cAAA,GAGS,MACR,KAAA,EAC0C;QAC1C,OAAO,IAAI,OAAwC,OAAO,IAAA,CAAK,MAA8C;IAC9G;AACD;AAEO,MAAM,0RACJ,WAAA,CACT;IACC,OAAA,mPAAiB,aAAU,CAAA,GAAY,SAAA;IAErB,aAAa,IAAA,CAAK,MAAA,CAAO,UAAA,CAAA;IAE3C,aAAqB;QACpB,OAAO;IACR;AACD;AAMO,SAAS,KACf,IAAA,EACA,SAAwC,CAAC,CAAA,EACE;IAC3C,OAAO,IAAI,cAAc,MAAM,MAAM;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/columns/date.common.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnDataType } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport { sql } from '~/sql/sql.ts';\nimport { PgColumnBuilder } from './common.ts';\n\nexport abstract class PgDateColumnBaseBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n> extends PgColumnBuilder<T, TRuntimeConfig> {\n\tstatic readonly [entityKind]: string = 'PgDateColumnBaseBuilder';\n\n\tdefaultNow() {\n\t\treturn this.default(sql`now()`);\n\t}\n}\n"], "names": [], "mappings": ";;;AACA,SAAS,kBAAkB;AAC3B,SAAS,WAAW;AACpB,SAAS,uBAAuB;;;;AAEzB,MAAe,2SAGZ,kBAAA,CAAmC;IAC5C,OAAA,mPAAiB,aAAU,CAAA,GAAY,0BAAA;IAEvC,aAAa;QACZ,OAAO,IAAA,CAAK,OAAA,uPAAQ,MAAA,CAAA,KAAA,CAAU;IAC/B;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/columns/timestamp.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport type { Equal } from '~/utils.ts';\nimport { PgColumn } from './common.ts';\nimport { PgDateColumnBaseBuilder } from './date.common.ts';\n\nexport type PgTimestampBuilderInitial<TName extends string> = PgTimestampBuilder<{\n\tname: TName;\n\tdataType: 'date';\n\tcolumnType: 'PgTimestamp';\n\tdata: Date;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgTimestampBuilder<T extends ColumnBuilderBaseConfig<'date', 'PgTimestamp'>>\n\textends PgDateColumnBaseBuilder<\n\t\tT,\n\t\t{ withTimezone: boolean; precision: number | undefined }\n\t>\n{\n\tstatic readonly [entityKind]: string = 'PgTimestampBuilder';\n\n\tconstructor(\n\t\tname: string,\n\t\twithTimezone: boolean,\n\t\tprecision: number | undefined,\n\t) {\n\t\tsuper(name, 'date', 'PgTimestamp');\n\t\tthis.config.withTimezone = withTimezone;\n\t\tthis.config.precision = precision;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgTimestamp<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgTimestamp<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgTimestamp<T extends ColumnBaseConfig<'date', 'PgTimestamp'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgTimestamp';\n\n\treadonly withTimezone: boolean;\n\treadonly precision: number | undefined;\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgTimestampBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t\tthis.withTimezone = config.withTimezone;\n\t\tthis.precision = config.precision;\n\t}\n\n\tgetSQLType(): string {\n\t\tconst precision = this.precision === undefined ? '' : ` (${this.precision})`;\n\t\treturn `timestamp${precision}${this.withTimezone ? ' with time zone' : ''}`;\n\t}\n\n\toverride mapFromDriverValue = (value: string): Date | null => {\n\t\treturn new Date(this.withTimezone ? value : value + '+0000');\n\t};\n\n\toverride mapToDriverValue = (value: Date): string => {\n\t\treturn value.toISOString();\n\t};\n}\n\nexport type PgTimestampStringBuilderInitial<TName extends string> = PgTimestampStringBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgTimestampString';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgTimestampStringBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgTimestampString'>>\n\textends PgDateColumnBaseBuilder<\n\t\tT,\n\t\t{ withTimezone: boolean; precision: number | undefined }\n\t>\n{\n\tstatic readonly [entityKind]: string = 'PgTimestampStringBuilder';\n\n\tconstructor(\n\t\tname: string,\n\t\twithTimezone: boolean,\n\t\tprecision: number | undefined,\n\t) {\n\t\tsuper(name, 'string', 'PgTimestampString');\n\t\tthis.config.withTimezone = withTimezone;\n\t\tthis.config.precision = precision;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgTimestampString<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgTimestampString<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgTimestampString<T extends ColumnBaseConfig<'string', 'PgTimestampString'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgTimestampString';\n\n\treadonly withTimezone: boolean;\n\treadonly precision: number | undefined;\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgTimestampStringBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t\tthis.withTimezone = config.withTimezone;\n\t\tthis.precision = config.precision;\n\t}\n\n\tgetSQLType(): string {\n\t\tconst precision = this.precision === undefined ? '' : `(${this.precision})`;\n\t\treturn `timestamp${precision}${this.withTimezone ? ' with time zone' : ''}`;\n\t}\n}\n\nexport type Precision = 0 | 1 | 2 | 3 | 4 | 5 | 6;\n\nexport interface PgTimestampConfig<TMode extends 'date' | 'string' = 'date' | 'string'> {\n\tmode?: TMode;\n\tprecision?: Precision;\n\twithTimezone?: boolean;\n}\n\nexport function timestamp<TName extends string, TMode extends PgTimestampConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: PgTimestampConfig<TMode>,\n): Equal<TMode, 'string'> extends true ? PgTimestampStringBuilderInitial<TName> : PgTimestampBuilderInitial<TName>;\nexport function timestamp(\n\tname: string,\n\tconfig: PgTimestampConfig = {},\n) {\n\tif (config.mode === 'string') {\n\t\treturn new PgTimestampStringBuilder(name, config.withTimezone ?? false, config.precision);\n\t}\n\treturn new PgTimestampBuilder(name, config.withTimezone ?? false, config.precision);\n}\n"], "names": [], "mappings": ";;;;;;;AAEA,SAAS,kBAAkB;AAG3B,SAAS,gBAAgB;AACzB,SAAS,+BAA+B;;;;AAWjC,MAAM,8SACJ,0BAAA,CAIT;IACC,OAAA,mPAAiB,aAAU,CAAA,GAAY,qBAAA;IAEvC,YACC,IAAA,EACA,YAAA,EACA,SAAA,CACC;QACD,KAAA,CAAM,MAAM,QAAQ,aAAa;QACjC,IAAA,CAAK,MAAA,CAAO,YAAA,GAAe;QAC3B,IAAA,CAAK,MAAA,CAAO,SAAA,GAAY;IACzB;IAAA,cAAA,GAGS,MACR,KAAA,EAC+C;QAC/C,OAAO,IAAI,YAA6C,OAAO,IAAA,CAAK,MAA8C;IACnH;AACD;AAEO,MAAM,+RAAuE,WAAA,CAAY;IAC/F,OAAA,mPAAiB,aAAU,CAAA,GAAY,cAAA;IAE9B,aAAA;IACA,UAAA;IAET,YAAY,KAAA,EAA6C,MAAA,CAAyC;QACjG,KAAA,CAAM,OAAO,MAAM;QACnB,IAAA,CAAK,YAAA,GAAe,OAAO,YAAA;QAC3B,IAAA,CAAK,SAAA,GAAY,OAAO,SAAA;IACzB;IAEA,aAAqB;QACpB,MAAM,YAAY,IAAA,CAAK,SAAA,KAAc,KAAA,IAAY,KAAK,CAAA,EAAA,EAAK,IAAA,CAAK,SAAS,CAAA,CAAA,CAAA;QACzE,OAAO,CAAA,SAAA,EAAY,SAAS,GAAG,IAAA,CAAK,YAAA,GAAe,oBAAoB,EAAE,EAAA;IAC1E;IAES,qBAAqB,CAAC,UAA+B;QAC7D,OAAO,IAAI,KAAK,IAAA,CAAK,YAAA,GAAe,QAAQ,QAAQ,OAAO;IAC5D,EAAA;IAES,mBAAmB,CAAC,UAAwB;QACpD,OAAO,MAAM,WAAA,CAAY;IAC1B,EAAA;AACD;AAWO,MAAM,oTACJ,0BAAA,CAIT;IACC,OAAA,mPAAiB,aAAU,CAAA,GAAY,2BAAA;IAEvC,YACC,IAAA,EACA,YAAA,EACA,SAAA,CACC;QACD,KAAA,CAAM,MAAM,UAAU,mBAAmB;QACzC,IAAA,CAAK,MAAA,CAAO,YAAA,GAAe;QAC3B,IAAA,CAAK,MAAA,CAAO,SAAA,GAAY;IACzB;IAAA,cAAA,GAGS,MACR,KAAA,EACqD;QACrD,OAAO,IAAI,kBACV,OACA,IAAA,CAAK,MAAA;IAEP;AACD;AAEO,MAAM,qSAAqF,WAAA,CAAY;IAC7G,OAAA,mPAAiB,aAAU,CAAA,GAAY,oBAAA;IAE9B,aAAA;IACA,UAAA;IAET,YAAY,KAAA,EAA6C,MAAA,CAA+C;QACvG,KAAA,CAAM,OAAO,MAAM;QACnB,IAAA,CAAK,YAAA,GAAe,OAAO,YAAA;QAC3B,IAAA,CAAK,SAAA,GAAY,OAAO,SAAA;IACzB;IAEA,aAAqB;QACpB,MAAM,YAAY,IAAA,CAAK,SAAA,KAAc,KAAA,IAAY,KAAK,CAAA,CAAA,EAAI,IAAA,CAAK,SAAS,CAAA,CAAA,CAAA;QACxE,OAAO,CAAA,SAAA,EAAY,SAAS,GAAG,IAAA,CAAK,YAAA,GAAe,oBAAoB,EAAE,EAAA;IAC1E;AACD;AAcO,SAAS,UACf,IAAA,EACA,SAA4B,CAAC,CAAA,EAC5B;IACD,IAAI,OAAO,IAAA,KAAS,UAAU;QAC7B,OAAO,IAAI,yBAAyB,MAAM,OAAO,YAAA,IAAgB,OAAO,OAAO,SAAS;IACzF;IACA,OAAO,IAAI,mBAAmB,MAAM,OAAO,YAAA,IAAgB,OAAO,OAAO,SAAS;AACnF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2328, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/columns/uuid.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { sql } from '~/sql/sql.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgUUIDBuilderInitial<TName extends string> = PgUUIDBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgUUID';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgUUIDBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgUUID'>> extends PgColumnBuilder<T> {\n\tstatic readonly [entityKind]: string = 'PgUUIDBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'PgUUID');\n\t}\n\n\t/**\n\t * Adds `default gen_random_uuid()` to the column definition.\n\t */\n\tdefaultRandom(): ReturnType<this['default']> {\n\t\treturn this.default(sql`gen_random_uuid()`) as ReturnType<this['default']>;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgUUID<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgUUID<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgUUID<T extends ColumnBaseConfig<'string', 'PgUUID'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgUUID';\n\n\tgetSQLType(): string {\n\t\treturn 'uuid';\n\t}\n}\n\nexport function uuid<TName extends string>(name: TName): PgUUIDBuilderInitial<TName> {\n\treturn new PgUUIDBuilder(name);\n}\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,kBAAkB;AAE3B,SAAS,WAAW;AACpB,SAAS,UAAU,uBAAuB;;;;AAWnC,MAAM,iSAA6E,kBAAA,CAAmB;IAC5G,OAAA,mPAAiB,aAAU,CAAA,GAAY,gBAAA;IAEvC,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,UAAU,QAAQ;IAC/B;IAAA;;GAAA,GAKA,gBAA6C;QAC5C,OAAO,IAAA,CAAK,OAAA,uPAAQ,MAAA,CAAA,iBAAA,CAAsB;IAC3C;IAAA,cAAA,GAGS,MACR,KAAA,EAC0C;QAC1C,OAAO,IAAI,OAAwC,OAAO,IAAA,CAAK,MAA8C;IAC9G;AACD;AAEO,MAAM,0RAA+D,WAAA,CAAY;IACvF,OAAA,mPAAiB,aAAU,CAAA,GAAY,SAAA;IAEvC,aAAqB;QACpB,OAAO;IACR;AACD;AAEO,SAAS,KAA2B,IAAA,EAA0C;IACpF,OAAO,IAAI,cAAc,IAAI;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2370, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/columns/varchar.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport type { Writable } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgVarcharBuilderInitial<TName extends string, TEnum extends [string, ...string[]]> = PgVarcharBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgVarchar';\n\tdata: TEnum[number];\n\tdriverParam: string;\n\tenumValues: TEnum;\n}>;\n\nexport class PgVarcharBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgVarchar'>> extends PgColumnBuilder<\n\tT,\n\t{ length: number | undefined; enumValues: T['enumValues'] }\n> {\n\tstatic readonly [entityKind]: string = 'PgVarcharBuilder';\n\n\tconstructor(name: string, config: PgVarcharConfig<T['enumValues']>) {\n\t\tsuper(name, 'string', 'PgVarchar');\n\t\tthis.config.length = config.length;\n\t\tthis.config.enumValues = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgVarchar<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgVarchar<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgVarchar<T extends ColumnBaseConfig<'string', 'PgVarchar'>>\n\textends PgColumn<T, { length: number | undefined; enumValues: T['enumValues'] }>\n{\n\tstatic readonly [entityKind]: string = 'PgVarchar';\n\n\treadonly length = this.config.length;\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `varchar` : `varchar(${this.length})`;\n\t}\n}\n\nexport interface PgVarcharConfig<TEnum extends readonly string[] | string[] | undefined> {\n\tlength?: number;\n\tenum?: TEnum;\n}\n\nexport function varchar<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig: PgVarcharConfig<T | Writable<T>> = {},\n): PgVarcharBuilderInitial<TName, Writable<T>> {\n\treturn new PgVarcharBuilder(name, config);\n}\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,kBAAkB;AAG3B,SAAS,UAAU,uBAAuB;;;AAWnC,MAAM,oSAAmF,kBAAA,CAG9F;IACD,OAAA,mPAAiB,aAAU,CAAA,GAAY,mBAAA;IAEvC,YAAY,IAAA,EAAc,MAAA,CAA0C;QACnE,KAAA,CAAM,MAAM,UAAU,WAAW;QACjC,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS,OAAO,MAAA;QAC5B,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa,OAAO,IAAA;IACjC;IAAA,cAAA,GAGS,MACR,KAAA,EAC6C;QAC7C,OAAO,IAAI,UAA2C,OAAO,IAAA,CAAK,MAA8C;IACjH;AACD;AAEO,MAAM,6RACJ,WAAA,CACT;IACC,OAAA,mPAAiB,aAAU,CAAA,GAAY,YAAA;IAE9B,SAAS,IAAA,CAAK,MAAA,CAAO,MAAA,CAAA;IACZ,aAAa,IAAA,CAAK,MAAA,CAAO,UAAA,CAAA;IAE3C,aAAqB;QACpB,OAAO,IAAA,CAAK,MAAA,KAAW,KAAA,IAAY,CAAA,OAAA,CAAA,GAAY,CAAA,QAAA,EAAW,IAAA,CAAK,MAAM,CAAA,CAAA,CAAA;IACtE;AACD;AAOO,SAAS,QACf,IAAA,EACA,SAA2C,CAAC,CAAA,EACE;IAC9C,OAAO,IAAI,iBAAiB,MAAM,MAAM;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2409, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/logger.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\n\nexport interface Logger {\n\tlogQuery(query: string, params: unknown[]): void;\n}\n\nexport interface LogWriter {\n\twrite(message: string): void;\n}\n\nexport class ConsoleLogWriter implements LogWriter {\n\tstatic readonly [entityKind]: string = 'ConsoleLogWriter';\n\n\twrite(message: string) {\n\t\tconsole.log(message);\n\t}\n}\n\nexport class DefaultLogger implements Logger {\n\tstatic readonly [entityKind]: string = 'DefaultLogger';\n\n\treadonly writer: LogWriter;\n\n\tconstructor(config?: { writer: LogWriter }) {\n\t\tthis.writer = config?.writer ?? new ConsoleLogWriter();\n\t}\n\n\tlogQuery(query: string, params: unknown[]): void {\n\t\tconst stringifiedParams = params.map((p) => {\n\t\t\ttry {\n\t\t\t\treturn JSON.stringify(p);\n\t\t\t} catch {\n\t\t\t\treturn String(p);\n\t\t\t}\n\t\t});\n\t\tconst paramsStr = stringifiedParams.length ? ` -- params: [${stringifiedParams.join(', ')}]` : '';\n\t\tthis.writer.write(`Query: ${query}${paramsStr}`);\n\t}\n}\n\nexport class NoopLogger implements Logger {\n\tstatic readonly [entityKind]: string = 'NoopLogger';\n\n\tlogQuery(): void {\n\t\t// noop\n\t}\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,kBAAkB;;AAUpB,MAAM,iBAAsC;IAClD,OAAA,mPAAiB,aAAU,CAAA,GAAY,mBAAA;IAEvC,MAAM,OAAA,EAAiB;QACtB,QAAQ,GAAA,CAAI,OAAO;IACpB;AACD;AAEO,MAAM,cAAgC;IAC5C,OAAA,mPAAiB,aAAU,CAAA,GAAY,gBAAA;IAE9B,OAAA;IAET,YAAY,MAAA,CAAgC;QAC3C,IAAA,CAAK,MAAA,GAAS,QAAQ,UAAU,IAAI,iBAAiB;IACtD;IAEA,SAAS,KAAA,EAAe,MAAA,EAAyB;QAChD,MAAM,oBAAoB,OAAO,GAAA,CAAI,CAAC,MAAM;YAC3C,IAAI;gBACH,OAAO,KAAK,SAAA,CAAU,CAAC;YACxB,EAAA,OAAQ;gBACP,OAAO,OAAO,CAAC;YAChB;QACD,CAAC;QACD,MAAM,YAAY,kBAAkB,MAAA,GAAS,CAAA,aAAA,EAAgB,kBAAkB,IAAA,CAAK,IAAI,CAAC,CAAA,CAAA,CAAA,GAAM;QAC/F,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,CAAA,OAAA,EAAU,KAAK,GAAG,SAAS,EAAE;IAChD;AACD;AAEO,MAAM,WAA6B;IACzC,OAAA,mPAAiB,aAAU,CAAA,GAAY,aAAA;IAEvC,WAAiB,CAEjB;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/query-promise.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\n\nexport abstract class QueryPromise<T> implements Promise<T> {\n\tstatic readonly [entityKind]: string = 'QueryPromise';\n\n\t[Symbol.toStringTag] = 'QueryPromise';\n\n\tcatch<TResult = never>(\n\t\tonRejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null | undefined,\n\t): Promise<T | TResult> {\n\t\treturn this.then(undefined, onRejected);\n\t}\n\n\tfinally(onFinally?: (() => void) | null | undefined): Promise<T> {\n\t\treturn this.then(\n\t\t\t(value) => {\n\t\t\t\tonFinally?.();\n\t\t\t\treturn value;\n\t\t\t},\n\t\t\t(reason) => {\n\t\t\t\tonFinally?.();\n\t\t\t\tthrow reason;\n\t\t\t},\n\t\t);\n\t}\n\n\tthen<TResult1 = T, TResult2 = never>(\n\t\tonFulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,\n\t\tonRejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null,\n\t): Promise<TResult1 | TResult2> {\n\t\treturn this.execute().then(onFulfilled, onRejected);\n\t}\n\n\tabstract execute(): Promise<T>;\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,kBAAkB;;AAEpB,MAAe,aAAsC;IAC3D,OAAA,mPAAiB,aAAU,CAAA,GAAY,eAAA;IAEvC,CAAC,OAAO,WAAW,CAAA,GAAI,eAAA;IAEvB,MACC,UAAA,EACuB;QACvB,OAAO,IAAA,CAAK,IAAA,CAAK,KAAA,GAAW,UAAU;IACvC;IAEA,QAAQ,SAAA,EAAyD;QAChE,OAAO,IAAA,CAAK,IAAA,CACX,CAAC,UAAU;YACV,YAAY;YACZ,OAAO;QACR,GACA,CAAC,WAAW;YACX,YAAY;YACZ,MAAM;QACP;IAEF;IAEA,KACC,WAAA,EACA,UAAA,EAC+B;QAC/B,OAAO,IAAA,CAAK,OAAA,CAAQ,EAAE,IAAA,CAAK,aAAa,UAAU;IACnD;AAGD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/utils.ts"], "sourcesContent": ["import type { AnyColumn } from './column.ts';\nimport { Column } from './column.ts';\nimport { is } from './entity.ts';\nimport type { Logger } from './logger.ts';\nimport type { SelectedFieldsOrdered } from './operations.ts';\nimport type { TableLike } from './query-builders/select.types.ts';\nimport { Param, SQL, View } from './sql/sql.ts';\nimport type { DriverValueDecoder } from './sql/sql.ts';\nimport { Subquery } from './subquery.ts';\nimport { getTableName, Table } from './table.ts';\nimport { ViewBaseConfig } from './view-common.ts';\n\n/** @internal */\nexport function mapResultRow<TResult>(\n\tcolumns: SelectedFieldsOrdered<AnyColumn>,\n\trow: unknown[],\n\tjoinsNotNullableMap: Record<string, boolean> | undefined,\n): TResult {\n\t// Key -> nested object key, value -> table name if all fields in the nested object are from the same table, false otherwise\n\tconst nullifyMap: Record<string, string | false> = {};\n\n\tconst result = columns.reduce<Record<string, any>>(\n\t\t(result, { path, field }, columnIndex) => {\n\t\t\tlet decoder: DriverValueDecoder<unknown, unknown>;\n\t\t\tif (is(field, Column)) {\n\t\t\t\tdecoder = field;\n\t\t\t} else if (is(field, SQL)) {\n\t\t\t\tdecoder = field.decoder;\n\t\t\t} else {\n\t\t\t\tdecoder = field.sql.decoder;\n\t\t\t}\n\t\t\tlet node = result;\n\t\t\tfor (const [pathChunkIndex, pathChunk] of path.entries()) {\n\t\t\t\tif (pathChunkIndex < path.length - 1) {\n\t\t\t\t\tif (!(pathChunk in node)) {\n\t\t\t\t\t\tnode[pathChunk] = {};\n\t\t\t\t\t}\n\t\t\t\t\tnode = node[pathChunk];\n\t\t\t\t} else {\n\t\t\t\t\tconst rawValue = row[columnIndex]!;\n\t\t\t\t\tconst value = node[pathChunk] = rawValue === null ? null : decoder.mapFromDriverValue(rawValue);\n\n\t\t\t\t\tif (joinsNotNullableMap && is(field, Column) && path.length === 2) {\n\t\t\t\t\t\tconst objectName = path[0]!;\n\t\t\t\t\t\tif (!(objectName in nullifyMap)) {\n\t\t\t\t\t\t\tnullifyMap[objectName] = value === null ? getTableName(field.table) : false;\n\t\t\t\t\t\t} else if (\n\t\t\t\t\t\t\ttypeof nullifyMap[objectName] === 'string' && nullifyMap[objectName] !== getTableName(field.table)\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tnullifyMap[objectName] = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn result;\n\t\t},\n\t\t{},\n\t);\n\n\t// Nullify all nested objects from nullifyMap that are nullable\n\tif (joinsNotNullableMap && Object.keys(nullifyMap).length > 0) {\n\t\tfor (const [objectName, tableName] of Object.entries(nullifyMap)) {\n\t\t\tif (typeof tableName === 'string' && !joinsNotNullableMap[tableName]) {\n\t\t\t\tresult[objectName] = null;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn result as TResult;\n}\n\n/** @internal */\nexport function orderSelectedFields<TColumn extends AnyColumn>(\n\tfields: Record<string, unknown>,\n\tpathPrefix?: string[],\n): SelectedFieldsOrdered<TColumn> {\n\treturn Object.entries(fields).reduce<SelectedFieldsOrdered<AnyColumn>>((result, [name, field]) => {\n\t\tif (typeof name !== 'string') {\n\t\t\treturn result;\n\t\t}\n\n\t\tconst newPath = pathPrefix ? [...pathPrefix, name] : [name];\n\t\tif (is(field, Column) || is(field, SQL) || is(field, SQL.Aliased)) {\n\t\t\tresult.push({ path: newPath, field });\n\t\t} else if (is(field, Table)) {\n\t\t\tresult.push(...orderSelectedFields(field[Table.Symbol.Columns], newPath));\n\t\t} else {\n\t\t\tresult.push(...orderSelectedFields(field as Record<string, unknown>, newPath));\n\t\t}\n\t\treturn result;\n\t}, []) as SelectedFieldsOrdered<TColumn>;\n}\n\nexport function haveSameKeys(left: Record<string, unknown>, right: Record<string, unknown>) {\n\tconst leftKeys = Object.keys(left);\n\tconst rightKeys = Object.keys(right);\n\n\tif (leftKeys.length !== rightKeys.length) {\n\t\treturn false;\n\t}\n\n\tfor (const [index, key] of leftKeys.entries()) {\n\t\tif (key !== rightKeys[index]) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\treturn true;\n}\n\n/** @internal */\nexport function mapUpdateSet(table: Table, values: Record<string, unknown>): UpdateSet {\n\tconst entries: [string, UpdateSet[string]][] = Object.entries(values)\n\t\t.filter(([, value]) => value !== undefined)\n\t\t.map(([key, value]) => {\n\t\t\t// eslint-disable-next-line unicorn/prefer-ternary\n\t\t\tif (is(value, SQL)) {\n\t\t\t\treturn [key, value];\n\t\t\t} else {\n\t\t\t\treturn [key, new Param(value, table[Table.Symbol.Columns][key])];\n\t\t\t}\n\t\t});\n\n\tif (entries.length === 0) {\n\t\tthrow new Error('No values to set');\n\t}\n\n\treturn Object.fromEntries(entries);\n}\n\nexport type UpdateSet = Record<string, SQL | Param | null | undefined>;\n\nexport type OneOrMany<T> = T | T[];\n\nexport type Update<T, TUpdate> =\n\t& {\n\t\t[K in Exclude<keyof T, keyof TUpdate>]: T[K];\n\t}\n\t& TUpdate;\n\nexport type Simplify<T> =\n\t& {\n\t\t// @ts-ignore - \"Type parameter 'K' has a circular constraint\", not sure why\n\t\t[K in keyof T]: T[K];\n\t}\n\t& {};\n\nexport type SimplifyMappedType<T> = [T] extends [unknown] ? T : never;\n\nexport type ShallowRecord<K extends keyof any, T> = SimplifyMappedType<{ [P in K]: T }>;\n\nexport type Assume<T, U> = T extends U ? T : U;\n\nexport type Equal<X, Y> = (<T>() => T extends X ? 1 : 2) extends (<T>() => T extends Y ? 1 : 2) ? true : false;\n\nexport interface DrizzleTypeError<T extends string> {\n\t$drizzleTypeError: T;\n}\n\nexport type ValueOrArray<T> = T | T[];\n\n/** @internal */\nexport function applyMixins(baseClass: any, extendedClasses: any[]) {\n\tfor (const extendedClass of extendedClasses) {\n\t\tfor (const name of Object.getOwnPropertyNames(extendedClass.prototype)) {\n\t\t\tif (name === 'constructor') continue;\n\n\t\t\tObject.defineProperty(\n\t\t\t\tbaseClass.prototype,\n\t\t\t\tname,\n\t\t\t\tObject.getOwnPropertyDescriptor(extendedClass.prototype, name) || Object.create(null),\n\t\t\t);\n\t\t}\n\t}\n}\n\nexport type Or<T1, T2> = T1 extends true ? true : T2 extends true ? true : false;\n\nexport type IfThenElse<If, Then, Else> = If extends true ? Then : Else;\n\nexport type PromiseOf<T> = T extends Promise<infer U> ? U : T;\n\nexport type Writable<T> = {\n\t-readonly [P in keyof T]: T[P];\n};\n\nexport function getTableColumns<T extends Table>(table: T): T['_']['columns'] {\n\treturn table[Table.Symbol.Columns];\n}\n\n/** @internal */\nexport function getTableLikeName(table: TableLike): string | undefined {\n\treturn is(table, Subquery)\n\t\t? table._.alias\n\t\t: is(table, View)\n\t\t? table[ViewBaseConfig].name\n\t\t: is(table, SQL)\n\t\t? undefined\n\t\t: table[Table.Symbol.IsAlias]\n\t\t? table[Table.Symbol.Name]\n\t\t: table[Table.Symbol.BaseName];\n}\n\nexport type ColumnsWithTable<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends AnyColumn<{ tableName: TTableName }>[],\n> = { [Key in keyof TColumns]: AnyColumn<{ tableName: TForeignTableName }> };\n\nexport interface DrizzleConfig<TSchema extends Record<string, unknown> = Record<string, never>> {\n\tlogger?: boolean | Logger;\n\tschema?: TSchema;\n}\nexport type ValidateShape<T, ValidShape, TResult = T> = T extends ValidShape\n\t? Exclude<keyof T, keyof ValidShape> extends never ? TResult\n\t: DrizzleTypeError<\n\t\t`Invalid key(s): ${Exclude<(keyof T) & (string | number | bigint | boolean | null | undefined), keyof ValidShape>}`\n\t>\n\t: never;\n\nexport type KnownKeysOnly<T, U> = {\n\t[K in keyof T]: K extends keyof U ? T[K] : never;\n};\n\nexport type IsAny<T> = 0 extends (1 & T) ? true : false;\n"], "names": ["result"], "mappings": ";;;;;;;;;AACA,SAAS,cAAc;AACvB,SAAS,UAAU;AAInB,SAAS,OAAO,KAAK,YAAY;AAEjC,SAAS,gBAAgB;AACzB,SAAS,cAAc,aAAa;AACpC,SAAS,sBAAsB;;;;;;;AAGxB,SAAS,aACf,OAAA,EACA,GAAA,EACA,mBAAA,EACU;IAEV,MAAM,aAA6C,CAAC;IAEpD,MAAM,SAAS,QAAQ,MAAA,CACtB,CAACA,SAAQ,EAAE,IAAA,EAAM,KAAA,CAAM,CAAA,EAAG,gBAAgB;QACzC,IAAI;QACJ,0PAAI,KAAA,EAAG,yPAAO,SAAM,GAAG;YACtB,UAAU;QACX,OAAA,0PAAW,KAAA,EAAG,6PAAO,MAAG,GAAG;YAC1B,UAAU,MAAM,OAAA;QACjB,OAAO;YACN,UAAU,MAAM,GAAA,CAAI,OAAA;QACrB;QACA,IAAI,OAAOA;QACX,KAAA,MAAW,CAAC,gBAAgB,SAAS,CAAA,IAAK,KAAK,OAAA,CAAQ,EAAG;YACzD,IAAI,iBAAiB,KAAK,MAAA,GAAS,GAAG;gBACrC,IAAI,CAAA,CAAE,aAAa,IAAA,GAAO;oBACzB,IAAA,CAAK,SAAS,CAAA,GAAI,CAAC;gBACpB;gBACA,OAAO,IAAA,CAAK,SAAS,CAAA;YACtB,OAAO;gBACN,MAAM,WAAW,GAAA,CAAI,WAAW,CAAA;gBAChC,MAAM,QAAQ,IAAA,CAAK,SAAS,CAAA,GAAI,aAAa,OAAO,OAAO,QAAQ,kBAAA,CAAmB,QAAQ;gBAE9F,IAAI,6QAAuB,KAAA,EAAG,OAAO,2PAAM,KAAK,KAAK,MAAA,KAAW,GAAG;oBAClE,MAAM,aAAa,IAAA,CAAK,CAAC,CAAA;oBACzB,IAAI,CAAA,CAAE,cAAc,UAAA,GAAa;wBAChC,UAAA,CAAW,UAAU,CAAA,GAAI,UAAU,WAAO,gQAAA,EAAa,MAAM,KAAK,IAAI;oBACvE,OAAA,IACC,OAAO,UAAA,CAAW,UAAU,CAAA,KAAM,YAAY,UAAA,CAAW,UAAU,CAAA,SAAM,gQAAA,EAAa,MAAM,KAAK,GAChG;wBACD,UAAA,CAAW,UAAU,CAAA,GAAI;oBAC1B;gBACD;YACD;QACD;QACA,OAAOA;IACR,GACA,CAAC;IAIF,IAAI,uBAAuB,OAAO,IAAA,CAAK,UAAU,EAAE,MAAA,GAAS,GAAG;QAC9D,KAAA,MAAW,CAAC,YAAY,SAAS,CAAA,IAAK,OAAO,OAAA,CAAQ,UAAU,EAAG;YACjE,IAAI,OAAO,cAAc,YAAY,CAAC,mBAAA,CAAoB,SAAS,CAAA,EAAG;gBACrE,MAAA,CAAO,UAAU,CAAA,GAAI;YACtB;QACD;IACD;IAEA,OAAO;AACR;AAGO,SAAS,oBACf,MAAA,EACA,UAAA,EACiC;IACjC,OAAO,OAAO,OAAA,CAAQ,MAAM,EAAE,MAAA,CAAyC,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAA,KAAM;QACjG,IAAI,OAAO,SAAS,UAAU;YAC7B,OAAO;QACR;QAEA,MAAM,UAAU,aAAa,CAAC;eAAG;YAAY,IAAI;SAAA,GAAI;YAAC,IAAI;SAAA;QAC1D,0PAAI,KAAA,EAAG,yPAAO,SAAM,KAAK,2PAAA,EAAG,6PAAO,MAAG,2PAAK,KAAA,EAAG,6PAAO,MAAA,CAAI,OAAO,GAAG;YAClE,OAAO,IAAA,CAAK;gBAAE,MAAM;gBAAS;YAAM,CAAC;QACrC,OAAA,KAAW,0PAAA,EAAG,wPAAO,QAAK,GAAG;YAC5B,OAAO,IAAA,CAAK,GAAG,oBAAoB,KAAA,CAAM,yPAAA,CAAM,MAAA,CAAO,OAAO,CAAA,EAAG,OAAO,CAAC;QACzE,OAAO;YACN,OAAO,IAAA,CAAK,GAAG,oBAAoB,OAAkC,OAAO,CAAC;QAC9E;QACA,OAAO;IACR,GAAG,CAAC,CAAC;AACN;AAEO,SAAS,aAAa,IAAA,EAA+B,KAAA,EAAgC;IAC3F,MAAM,WAAW,OAAO,IAAA,CAAK,IAAI;IACjC,MAAM,YAAY,OAAO,IAAA,CAAK,KAAK;IAEnC,IAAI,SAAS,MAAA,KAAW,UAAU,MAAA,EAAQ;QACzC,OAAO;IACR;IAEA,KAAA,MAAW,CAAC,OAAO,GAAG,CAAA,IAAK,SAAS,OAAA,CAAQ,EAAG;QAC9C,IAAI,QAAQ,SAAA,CAAU,KAAK,CAAA,EAAG;YAC7B,OAAO;QACR;IACD;IAEA,OAAO;AACR;AAGO,SAAS,aAAa,KAAA,EAAc,MAAA,EAA4C;IACtF,MAAM,UAAyC,OAAO,OAAA,CAAQ,MAAM,EAClE,MAAA,CAAO,CAAC,CAAC,EAAE,KAAK,CAAA,GAAM,UAAU,KAAA,CAAS,EACzC,GAAA,CAAI,CAAC,CAAC,KAAK,KAAK,CAAA,KAAM;QAEtB,0PAAI,KAAA,EAAG,OAAO,4PAAG,GAAG;YACnB,OAAO;gBAAC;gBAAK,KAAK;aAAA;QACnB,OAAO;YACN,OAAO;gBAAC;gBAAK,0PAAI,QAAA,CAAM,OAAO,KAAA,iPAAM,SAAA,CAAM,MAAA,CAAO,OAAO,CAAA,CAAE,GAAG,CAAC,CAAC;aAAA;QAChE;IACD,CAAC;IAEF,IAAI,QAAQ,MAAA,KAAW,GAAG;QACzB,MAAM,IAAI,MAAM,kBAAkB;IACnC;IAEA,OAAO,OAAO,WAAA,CAAY,OAAO;AAClC;AAkCO,SAAS,YAAY,SAAA,EAAgB,eAAA,EAAwB;IACnE,KAAA,MAAW,iBAAiB,gBAAiB;QAC5C,KAAA,MAAW,QAAQ,OAAO,mBAAA,CAAoB,cAAc,SAAS,EAAG;YACvE,IAAI,SAAS,eAAe;YAE5B,OAAO,cAAA,CACN,UAAU,SAAA,EACV,MACA,OAAO,wBAAA,CAAyB,cAAc,SAAA,EAAW,IAAI,KAAK,aAAA,GAAA,OAAO,MAAA,CAAO,IAAI;QAEtF;IACD;AACD;AAYO,SAAS,gBAAiC,KAAA,EAA6B;IAC7E,OAAO,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA;AAClC;AAGO,SAAS,iBAAiB,KAAA,EAAsC;IACtE,6PAAO,KAAA,EAAG,2PAAO,WAAQ,IACtB,MAAM,CAAA,CAAE,KAAA,yPACR,KAAA,EAAG,4PAAO,QAAI,IACd,KAAA,2PAAM,iBAAc,CAAA,CAAE,IAAA,yPACtB,KAAA,EAAG,6PAAO,MAAG,IACb,KAAA,IACA,KAAA,iPAAM,SAAA,CAAM,MAAA,CAAO,OAAO,CAAA,GAC1B,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,IAAI,CAAA,GACvB,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,QAAQ,CAAA;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2625, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/query-builders/delete.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgTable } from '~/pg-core/table.ts';\nimport type { SelectResultFields } from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport { tracer } from '~/tracing.ts';\nimport { orderSelectedFields } from '~/utils.ts';\nimport type { PgColumn } from '../columns/common.ts';\nimport type { SelectedFieldsFlat, SelectedFieldsOrdered } from './select.types.ts';\n\nexport type PgDeleteWithout<\n\tT extends AnyPgDeleteBase,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T\n\t: Omit<\n\t\tPgDeleteBase<\n\t\t\tT['_']['table'],\n\t\t\tT['_']['queryResult'],\n\t\t\tT['_']['returning'],\n\t\t\tTDynamic,\n\t\t\tT['_']['excludedMethods'] | K\n\t\t>,\n\t\tT['_']['excludedMethods'] | K\n\t>;\n\nexport type PgDelete<\n\tTTable extends PgTable = PgTable,\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n> = PgDeleteBase<TTable, TQueryResult, TReturning, true, never>;\n\nexport interface PgDeleteConfig {\n\twhere?: SQL | undefined;\n\ttable: PgTable;\n\treturning?: SelectedFieldsOrdered;\n\twithList?: Subquery[];\n}\n\nexport type PgDeleteReturningAll<\n\tT extends AnyPgDeleteBase,\n\tTDynamic extends boolean,\n> = PgDeleteWithout<\n\tPgDeleteBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['table']['$inferSelect'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgDeleteReturning<\n\tT extends AnyPgDeleteBase,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFieldsFlat,\n> = PgDeleteWithout<\n\tPgDeleteBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tSelectResultFields<TSelectedFields>,\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgDeletePrepare<T extends AnyPgDeleteBase> = PgPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? PgQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type PgDeleteDynamic<T extends AnyPgDeleteBase> = PgDelete<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['returning']\n>;\n\nexport type AnyPgDeleteBase = PgDeleteBase<any, any, any, any, any>;\n\nexport interface PgDeleteBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tQueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\tdialect: 'pg';\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class PgDeleteBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\t\tSQLWrapper\n{\n\tstatic readonly [entityKind]: string = 'PgDelete';\n\n\tprivate config: PgDeleteConfig;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, withList };\n\t}\n\n\t/**\n\t * Adds a `where` clause to the query.\n\t *\n\t * Calling this method will delete only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t *\n\t * @param where the `where` clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be deleted.\n\t *\n\t * ```ts\n\t * // Delete all cars with green color\n\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t * // or\n\t * await db.delete(cars).where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Delete all BMW cars with a green color\n\t * await db.delete(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Delete all cars with the green or blue color\n\t * await db.delete(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(where: SQL | undefined): PgDeleteWithout<this, TDynamic, 'where'> {\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the deleted rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete#delete-with-return}\n\t *\n\t * @example\n\t * ```ts\n\t * // Delete all cars with the green color and return all fields\n\t * const deletedCars: Car[] = await db.delete(cars)\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning();\n\t *\n\t * // Delete all cars with the green color and return only their id and brand fields\n\t * const deletedCarsIdsAndBrands: { id: number, brand: string }[] = await db.delete(cars)\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning({ id: cars.id, brand: cars.brand });\n\t * ```\n\t */\n\treturning(): PgDeleteReturningAll<this, TDynamic>;\n\treturning<TSelectedFields extends SelectedFieldsFlat>(\n\t\tfields: TSelectedFields,\n\t): PgDeleteReturning<this, TDynamic, TSelectedFields>;\n\treturning(\n\t\tfields: SelectedFieldsFlat = this.config.table[Table.Symbol.Columns],\n\t): PgDeleteReturning<this, TDynamic, any> {\n\t\tthis.config.returning = orderSelectedFields<PgColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildDeleteQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgDeletePrepare<this> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\treturn this.session.prepareQuery<\n\t\t\t\tPreparedQueryConfig & {\n\t\t\t\t\texecute: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t\t\t\t}\n\t\t\t>(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true);\n\t\t});\n\t}\n\n\tprepare(name: string): PgDeletePrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues);\n\t\t});\n\t};\n\n\t$dynamic(): PgDeleteDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,kBAAkB;AAW3B,SAAS,oBAAoB;AAI7B,SAAS,aAAa;AACtB,SAAS,cAAc;AACvB,SAAS,2BAA2B;;;;;;AAqG7B,MAAM,iRAOH,eAAA,CAIV;IAKC,YACC,KAAA,EACQ,OAAA,EACA,OAAA,EACR,QAAA,CACC;QACD,KAAA,CAAM;QAJE,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QAIR,IAAA,CAAK,MAAA,GAAS;YAAE;YAAO;QAAS;IACjC;IAZA,OAAA,mPAAiB,aAAU,CAAA,GAAY,WAAA;IAE/B,OAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GAyCR,MAAM,KAAA,EAAkE;QACvE,IAAA,CAAK,MAAA,CAAO,KAAA,GAAQ;QACpB,OAAO,IAAA;IACR;IA0BA,UACC,SAA6B,IAAA,CAAK,MAAA,CAAO,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA,EAC1B;QACzC,IAAA,CAAK,MAAA,CAAO,SAAA,GAAY,2QAAA,EAA8B,MAAM;QAC5D,OAAO,IAAA;IACR;IAAA,cAAA,GAGA,SAAc;QACb,OAAO,IAAA,CAAK,OAAA,CAAQ,gBAAA,CAAiB,IAAA,CAAK,MAAM;IACjD;IAEA,QAAe;QACd,MAAM,EAAE,SAAS,QAAA,EAAU,GAAG,KAAK,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,IAAA,CAAK,MAAA,CAAO,CAAC;QAC5E,OAAO;IACR;IAAA,cAAA,GAGA,SAAS,IAAA,EAAsC;QAC9C,yPAAO,UAAA,CAAO,eAAA,CAAgB,wBAAwB,MAAM;YAC3D,OAAO,IAAA,CAAK,OAAA,CAAQ,YAAA,CAIlB,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,IAAA,CAAK,MAAA,CAAO,CAAC,GAAG,IAAA,CAAK,MAAA,CAAO,SAAA,EAAW,MAAM,IAAI;QAC5E,CAAC;IACF;IAEA,QAAQ,IAAA,EAAqC;QAC5C,OAAO,IAAA,CAAK,QAAA,CAAS,IAAI;IAC1B;IAES,UAAkD,CAAC,sBAAsB;QACjF,0PAAO,SAAA,CAAO,eAAA,CAAgB,qBAAqB,MAAM;YACxD,OAAO,IAAA,CAAK,QAAA,CAAS,EAAE,OAAA,CAAQ,iBAAiB;QACjD,CAAC;IACF,EAAA;IAEA,WAAkC;QACjC,OAAO,IAAA;IACR;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2718, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/query-builders/insert.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type { IndexColumn } from '~/pg-core/indexes.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgTable } from '~/pg-core/table.ts';\nimport type { SelectResultFields } from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Placeholder, Query, SQLWrapper } from '~/sql/sql.ts';\nimport { Param, SQL, sql } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport { tracer } from '~/tracing.ts';\nimport { mapUpdateSet, orderSelectedFields } from '~/utils.ts';\nimport type { PgColumn } from '../columns/common.ts';\nimport type { SelectedFieldsFlat, SelectedFieldsOrdered } from './select.types.ts';\nimport type { PgUpdateSetSource } from './update.ts';\n\nexport interface PgInsertConfig<TTable extends PgTable = PgTable> {\n\ttable: TTable;\n\tvalues: Record<string, Param | SQL>[];\n\twithList?: Subquery[];\n\tonConflict?: SQL;\n\treturning?: SelectedFieldsOrdered;\n}\n\nexport type PgInsertValue<TTable extends PgTable> =\n\t& {\n\t\t[Key in keyof TTable['$inferInsert']]: TTable['$inferInsert'][Key] | SQL | Placeholder;\n\t}\n\t& {};\n\nexport class PgInsertBuilder<TTable extends PgTable, TQueryResult extends PgQueryResultHKT> {\n\tstatic readonly [entityKind]: string = 'PgInsertBuilder';\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\tprivate withList?: Subquery[],\n\t) {}\n\n\tvalues(value: PgInsertValue<TTable>): PgInsertBase<TTable, TQueryResult>;\n\tvalues(values: PgInsertValue<TTable>[]): PgInsertBase<TTable, TQueryResult>;\n\tvalues(values: PgInsertValue<TTable> | PgInsertValue<TTable>[]): PgInsertBase<TTable, TQueryResult> {\n\t\tvalues = Array.isArray(values) ? values : [values];\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('values() must be called with at least one value');\n\t\t}\n\t\tconst mappedValues = values.map((entry) => {\n\t\t\tconst result: Record<string, Param | SQL> = {};\n\t\t\tconst cols = this.table[Table.Symbol.Columns];\n\t\t\tfor (const colKey of Object.keys(entry)) {\n\t\t\t\tconst colValue = entry[colKey as keyof typeof entry];\n\t\t\t\tresult[colKey] = is(colValue, SQL) ? colValue : new Param(colValue, cols[colKey]);\n\t\t\t}\n\t\t\treturn result;\n\t\t});\n\n\t\treturn new PgInsertBase(this.table, mappedValues, this.session, this.dialect, this.withList);\n\t}\n}\n\nexport type PgInsertWithout<T extends AnyPgInsert, TDynamic extends boolean, K extends keyof T & string> =\n\tTDynamic extends true ? T\n\t\t: Omit<\n\t\t\tPgInsertBase<\n\t\t\t\tT['_']['table'],\n\t\t\t\tT['_']['queryResult'],\n\t\t\t\tT['_']['returning'],\n\t\t\t\tTDynamic,\n\t\t\t\tT['_']['excludedMethods'] | K\n\t\t\t>,\n\t\t\tT['_']['excludedMethods'] | K\n\t\t>;\n\nexport type PgInsertReturning<\n\tT extends AnyPgInsert,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFieldsFlat,\n> = PgInsertBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tSelectResultFields<TSelectedFields>,\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\nexport type PgInsertReturningAll<T extends AnyPgInsert, TDynamic extends boolean> = PgInsertBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['table']['$inferSelect'],\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\nexport interface PgInsertOnConflictDoUpdateConfig<T extends AnyPgInsert> {\n\ttarget: IndexColumn | IndexColumn[];\n\t/** @deprecated use either `targetWhere` or `setWhere` */\n\twhere?: SQL;\n\t// TODO: add tests for targetWhere and setWhere\n\ttargetWhere?: SQL;\n\tsetWhere?: SQL;\n\tset: PgUpdateSetSource<T['_']['table']>;\n}\n\nexport type PgInsertPrepare<T extends AnyPgInsert> = PgPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? PgQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type PgInsertDynamic<T extends AnyPgInsert> = PgInsert<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['returning']\n>;\n\nexport type AnyPgInsert = PgInsertBase<any, any, any, any, any>;\n\nexport type PgInsert<\n\tTTable extends PgTable = PgTable,\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n> = PgInsertBase<TTable, TQueryResult, TReturning, true, never>;\n\nexport interface PgInsertBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tQueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class PgInsertBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\t\tSQLWrapper\n{\n\tstatic readonly [entityKind]: string = 'PgInsert';\n\n\tprivate config: PgInsertConfig<TTable>;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tvalues: PgInsertConfig['values'],\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, values, withList };\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the inserted rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#insert-returning}\n\t *\n\t * @example\n\t * ```ts\n\t * // Insert one row and return all fields\n\t * const insertedCar: Car[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning();\n\t *\n\t * // Insert one row and return only the id\n\t * const insertedCarId: { id: number }[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning({ id: cars.id });\n\t * ```\n\t */\n\treturning(): PgInsertWithout<PgInsertReturningAll<this, TDynamic>, TDynamic, 'returning'>;\n\treturning<TSelectedFields extends SelectedFieldsFlat>(\n\t\tfields: TSelectedFields,\n\t): PgInsertWithout<PgInsertReturning<this, TDynamic, TSelectedFields>, TDynamic, 'returning'>;\n\treturning(\n\t\tfields: SelectedFieldsFlat = this.config.table[Table.Symbol.Columns],\n\t): PgInsertWithout<AnyPgInsert, TDynamic, 'returning'> {\n\t\tthis.config.returning = orderSelectedFields<PgColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `on conflict do nothing` clause to the query.\n\t *\n\t * Calling this method simply avoids inserting a row as its alternative action.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#on-conflict-do-nothing}\n\t *\n\t * @param config The `target` and `where` clauses.\n\t *\n\t * @example\n\t * ```ts\n\t * // Insert one row and cancel the insert if there's a conflict\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoNothing();\n\t *\n\t * // Explicitly specify conflict target\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoNothing({ target: cars.id });\n\t * ```\n\t */\n\tonConflictDoNothing(\n\t\tconfig: { target?: IndexColumn | IndexColumn[]; where?: SQL } = {},\n\t): PgInsertWithout<this, TDynamic, 'onConflictDoNothing' | 'onConflictDoUpdate'> {\n\t\tif (config.target === undefined) {\n\t\t\tthis.config.onConflict = sql`do nothing`;\n\t\t} else {\n\t\t\tlet targetColumn = '';\n\t\t\ttargetColumn = Array.isArray(config.target)\n\t\t\t\t? config.target.map((it) => this.dialect.escapeName(it.name)).join(',')\n\t\t\t\t: this.dialect.escapeName(config.target.name);\n\n\t\t\tconst whereSql = config.where ? sql` where ${config.where}` : undefined;\n\t\t\tthis.config.onConflict = sql`(${sql.raw(targetColumn)})${whereSql} do nothing`;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `on conflict do update` clause to the query.\n\t *\n\t * Calling this method will update the existing row that conflicts with the row proposed for insertion as its alternative action.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#upserts-and-conflicts}\n\t *\n\t * @param config The `target`, `set` and `where` clauses.\n\t *\n\t * @example\n\t * ```ts\n\t * // Update the row if there's a conflict\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoUpdate({\n\t *     target: cars.id,\n\t *     set: { brand: 'Porsche' }\n\t *   });\n\t *\n\t * // Upsert with 'where' clause\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoUpdate({\n\t *     target: cars.id,\n\t *     set: { brand: 'newBMW' },\n\t *     targetWhere: sql`${cars.createdAt} > '2023-01-01'::date`,\n\t *   });\n\t * ```\n\t */\n\tonConflictDoUpdate(\n\t\tconfig: PgInsertOnConflictDoUpdateConfig<this>,\n\t): PgInsertWithout<this, TDynamic, 'onConflictDoNothing' | 'onConflictDoUpdate'> {\n\t\tif (config.where && (config.targetWhere || config.setWhere)) {\n\t\t\tthrow new Error(\n\t\t\t\t'You cannot use both \"where\" and \"targetWhere\"/\"setWhere\" at the same time - \"where\" is deprecated, use \"targetWhere\" or \"setWhere\" instead.',\n\t\t\t);\n\t\t}\n\t\tconst whereSql = config.where ? sql` where ${config.where}` : undefined;\n\t\tconst targetWhereSql = config.targetWhere ? sql` where ${config.targetWhere}` : undefined;\n\t\tconst setWhereSql = config.setWhere ? sql` where ${config.setWhere}` : undefined;\n\t\tconst setSql = this.dialect.buildUpdateSet(this.config.table, mapUpdateSet(this.config.table, config.set));\n\t\tlet targetColumn = '';\n\t\ttargetColumn = Array.isArray(config.target)\n\t\t\t? config.target.map((it) => this.dialect.escapeName(it.name)).join(',')\n\t\t\t: this.dialect.escapeName(config.target.name);\n\t\tthis.config.onConflict = sql`(${\n\t\t\tsql.raw(targetColumn)\n\t\t})${targetWhereSql} do update set ${setSql}${whereSql}${setWhereSql}`;\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildInsertQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgInsertPrepare<this> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\treturn this.session.prepareQuery<\n\t\t\t\tPreparedQueryConfig & {\n\t\t\t\t\texecute: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t\t\t\t}\n\t\t\t>(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true);\n\t\t});\n\t}\n\n\tprepare(name: string): PgInsertPrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues);\n\t\t});\n\t};\n\n\t$dynamic(): PgInsertDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,YAAY,UAAU;AAY/B,SAAS,oBAAoB;AAG7B,SAAS,OAAO,KAAK,WAAW;AAEhC,SAAS,aAAa;AACtB,SAAS,cAAc;AACvB,SAAS,cAAc,2BAA2B;;;;;;;AAmB3C,MAAM,gBAA+E;IAG3F,YACS,KAAA,EACA,OAAA,EACA,OAAA,EACA,QAAA,CACP;QAJO,IAAA,CAAA,KAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;IACN;IAPH,OAAA,mPAAiB,aAAU,CAAA,GAAY,kBAAA;IAWvC,OAAO,MAAA,EAA6F;QACnG,SAAS,MAAM,OAAA,CAAQ,MAAM,IAAI,SAAS;YAAC,MAAM;SAAA;QACjD,IAAI,OAAO,MAAA,KAAW,GAAG;YACxB,MAAM,IAAI,MAAM,iDAAiD;QAClE;QACA,MAAM,eAAe,OAAO,GAAA,CAAI,CAAC,UAAU;YAC1C,MAAM,SAAsC,CAAC;YAC7C,MAAM,OAAO,IAAA,CAAK,KAAA,CAAM,yPAAA,CAAM,MAAA,CAAO,OAAO,CAAA;YAC5C,KAAA,MAAW,UAAU,OAAO,IAAA,CAAK,KAAK,EAAG;gBACxC,MAAM,WAAW,KAAA,CAAM,MAA4B,CAAA;gBACnD,MAAA,CAAO,MAAM,CAAA,GAAI,2PAAA,EAAG,gQAAU,MAAG,IAAI,WAAW,0PAAI,QAAA,CAAM,UAAU,IAAA,CAAK,MAAM,CAAC;YACjF;YACA,OAAO;QACR,CAAC;QAED,OAAO,IAAI,aAAa,IAAA,CAAK,KAAA,EAAO,cAAc,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,QAAQ;IAC5F;AACD;AAwFO,MAAM,qBAQH,2QAAA,CAIV;IAKC,YACC,KAAA,EACA,MAAA,EACQ,OAAA,EACA,OAAA,EACR,QAAA,CACC;QACD,KAAA,CAAM;QAJE,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QAIR,IAAA,CAAK,MAAA,GAAS;YAAE;YAAO;YAAQ;QAAS;IACzC;IAbA,OAAA,kPAAiB,cAAU,CAAA,GAAY,WAAA;IAE/B,OAAA;IAqCR,UACC,SAA6B,IAAA,CAAK,MAAA,CAAO,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA,EACb;QACtD,IAAA,CAAK,MAAA,CAAO,SAAA,wPAAY,sBAAA,EAA8B,MAAM;QAC5D,OAAO,IAAA;IACR;IAAA;;;;;;;;;;;;;;;;;;;;;GAAA,GAwBA,oBACC,SAAgE,CAAC,CAAA,EACe;QAChF,IAAI,OAAO,MAAA,KAAW,KAAA,GAAW;YAChC,IAAA,CAAK,MAAA,CAAO,UAAA,yPAAa,MAAA,CAAA,UAAA,CAAA;QAC1B,OAAO;YACN,IAAI,eAAe;YACnB,eAAe,MAAM,OAAA,CAAQ,OAAO,MAAM,IACvC,OAAO,MAAA,CAAO,GAAA,CAAI,CAAC,KAAO,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,GAAG,IAAI,CAAC,EAAE,IAAA,CAAK,GAAG,IACpE,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,OAAO,MAAA,CAAO,IAAI;YAE7C,MAAM,WAAW,OAAO,KAAA,yPAAQ,MAAA,CAAA,OAAA,EAAa,OAAO,KAAK,CAAA,CAAA,GAAK,KAAA;YAC9D,IAAA,CAAK,MAAA,CAAO,UAAA,yPAAa,MAAA,CAAA,CAAA,wPAAO,MAAA,CAAI,GAAA,CAAI,YAAY,CAAC,CAAA,CAAA,EAAI,QAAQ,CAAA,WAAA,CAAA;QAClE;QACA,OAAO,IAAA;IACR;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA+BA,mBACC,MAAA,EACgF;QAChF,IAAI,OAAO,KAAA,IAAA,CAAU,OAAO,WAAA,IAAe,OAAO,QAAA,GAAW;YAC5D,MAAM,IAAI,MACT;QAEF;QACA,MAAM,WAAW,OAAO,KAAA,yPAAQ,MAAA,CAAA,OAAA,EAAa,OAAO,KAAK,CAAA,CAAA,GAAK,KAAA;QAC9D,MAAM,iBAAiB,OAAO,WAAA,yPAAc,MAAA,CAAA,OAAA,EAAa,OAAO,WAAW,CAAA,CAAA,GAAK,KAAA;QAChF,MAAM,cAAc,OAAO,QAAA,GAAW,4PAAA,CAAA,OAAA,EAAa,OAAO,QAAQ,CAAA,CAAA,GAAK,KAAA;QACvE,MAAM,SAAS,IAAA,CAAK,OAAA,CAAQ,cAAA,CAAe,IAAA,CAAK,MAAA,CAAO,KAAA,uPAAO,eAAA,EAAa,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO,OAAO,GAAG,CAAC;QACzG,IAAI,eAAe;QACnB,eAAe,MAAM,OAAA,CAAQ,OAAO,MAAM,IACvC,OAAO,MAAA,CAAO,GAAA,CAAI,CAAC,KAAO,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,GAAG,IAAI,CAAC,EAAE,IAAA,CAAK,GAAG,IACpE,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,OAAO,MAAA,CAAO,IAAI;QAC7C,IAAA,CAAK,MAAA,CAAO,UAAA,yPAAa,MAAA,CAAA,CAAA,wPACxB,MAAA,CAAI,GAAA,CAAI,YAAY,CACrB,CAAA,CAAA,EAAI,cAAc,CAAA,eAAA,EAAkB,MAAM,CAAA,EAAG,QAAQ,CAAA,EAAG,WAAW,CAAA,CAAA;QACnE,OAAO,IAAA;IACR;IAAA,cAAA,GAGA,SAAc;QACb,OAAO,IAAA,CAAK,OAAA,CAAQ,gBAAA,CAAiB,IAAA,CAAK,MAAM;IACjD;IAEA,QAAe;QACd,MAAM,EAAE,SAAS,QAAA,EAAU,GAAG,KAAK,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,IAAA,CAAK,MAAA,CAAO,CAAC;QAC5E,OAAO;IACR;IAAA,cAAA,GAGA,SAAS,IAAA,EAAsC;QAC9C,0PAAO,SAAA,CAAO,eAAA,CAAgB,wBAAwB,MAAM;YAC3D,OAAO,IAAA,CAAK,OAAA,CAAQ,YAAA,CAIlB,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,IAAA,CAAK,MAAA,CAAO,CAAC,GAAG,IAAA,CAAK,MAAA,CAAO,SAAA,EAAW,MAAM,IAAI;QAC5E,CAAC;IACF;IAEA,QAAQ,IAAA,EAAqC;QAC5C,OAAO,IAAA,CAAK,QAAA,CAAS,IAAI;IAC1B;IAES,UAAkD,CAAC,sBAAsB;QACjF,0PAAO,SAAA,CAAO,eAAA,CAAgB,qBAAqB,MAAM;YACxD,OAAO,IAAA,CAAK,QAAA,CAAS,EAAE,OAAA,CAAQ,iBAAiB;QACjD,CAAC;IACF,EAAA;IAEA,WAAkC;QACjC,OAAO,IAAA;IACR;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2883, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/view-base.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { type ColumnsSelection, View } from '~/sql/sql.ts';\n\nexport abstract class PgViewBase<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> extends View<TName, TExisting, TSelectedFields> {\n\tstatic readonly [entityKind]: string = 'PgViewBase';\n\n\tdeclare readonly _: View<TName, TExisting, TSelectedFields>['_'] & {\n\t\treadonly viewBrand: 'PgViewBase';\n\t};\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,kBAAkB;AAC3B,SAAgC,YAAY;;;AAErC,MAAe,yQAIZ,OAAA,CAAwC;IACjD,OAAA,mPAAiB,aAAU,CAAA,GAAY,aAAA;AAKxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2901, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/query-builders/query-builder.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { SQL, SQLWrapper } from '~/sql/index.ts';\n\nexport abstract class TypedQueryBuilder<TSelection, TResult = unknown> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'TypedQueryBuilder';\n\n\tdeclare _: {\n\t\tselectedFields: TSelection;\n\t\tresult: TResult;\n\t};\n\n\t/** @internal */\n\tgetSelectedFields(): TSelection {\n\t\treturn this._.selectedFields;\n\t}\n\n\tabstract getSQL(): SQL;\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,kBAAkB;;AAGpB,MAAe,kBAAuE;IAC5F,OAAA,mPAAiB,aAAU,CAAA,GAAY,oBAAA;IAAA,cAAA,GAQvC,oBAAgC;QAC/B,OAAO,IAAA,CAAK,CAAA,CAAE,cAAA;IACf;AAGD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2920, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/alias.ts"], "sourcesContent": ["import type { AnyColumn } from './column.ts';\nimport { Column } from './column.ts';\nimport { entityKind, is } from './entity.ts';\nimport type { Relation } from './relations.ts';\nimport type { View } from './sql/sql.ts';\nimport { SQL, sql } from './sql/sql.ts';\nimport { Table } from './table.ts';\nimport { ViewBaseConfig } from './view-common.ts';\n\nexport class ColumnAliasProxyHandler<TColumn extends Column> implements ProxyHandler<TColumn> {\n\tstatic readonly [entityKind]: string = 'ColumnAliasProxyHandler';\n\n\tconstructor(private table: Table | View) {}\n\n\tget(columnObj: TColumn, prop: string | symbol): any {\n\t\tif (prop === 'table') {\n\t\t\treturn this.table;\n\t\t}\n\n\t\treturn columnObj[prop as keyof TColumn];\n\t}\n}\n\nexport class TableAliasProxyHandler<T extends Table | View> implements ProxyHandler<T> {\n\tstatic readonly [entityKind]: string = 'TableAliasProxyHandler';\n\n\tconstructor(private alias: string, private replaceOriginalName: boolean) {}\n\n\tget(target: T, prop: string | symbol): any {\n\t\tif (prop === Table.Symbol.IsAlias) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif (prop === Table.Symbol.Name) {\n\t\t\treturn this.alias;\n\t\t}\n\n\t\tif (this.replaceOriginalName && prop === Table.Symbol.OriginalName) {\n\t\t\treturn this.alias;\n\t\t}\n\n\t\tif (prop === ViewBaseConfig) {\n\t\t\treturn {\n\t\t\t\t...target[ViewBaseConfig as keyof typeof target],\n\t\t\t\tname: this.alias,\n\t\t\t\tisAlias: true,\n\t\t\t};\n\t\t}\n\n\t\tif (prop === Table.Symbol.Columns) {\n\t\t\tconst columns = (target as Table)[Table.Symbol.Columns];\n\t\t\tif (!columns) {\n\t\t\t\treturn columns;\n\t\t\t}\n\n\t\t\tconst proxiedColumns: { [key: string]: any } = {};\n\n\t\t\tObject.keys(columns).map((key) => {\n\t\t\t\tproxiedColumns[key] = new Proxy(\n\t\t\t\t\tcolumns[key]!,\n\t\t\t\t\tnew ColumnAliasProxyHandler(new Proxy(target, this)),\n\t\t\t\t);\n\t\t\t});\n\n\t\t\treturn proxiedColumns;\n\t\t}\n\n\t\tconst value = target[prop as keyof typeof target];\n\t\tif (is(value, Column)) {\n\t\t\treturn new Proxy(value as AnyColumn, new ColumnAliasProxyHandler(new Proxy(target, this)));\n\t\t}\n\n\t\treturn value;\n\t}\n}\n\nexport class RelationTableAliasProxyHandler<T extends Relation> implements ProxyHandler<T> {\n\tstatic readonly [entityKind]: string = 'RelationTableAliasProxyHandler';\n\n\tconstructor(private alias: string) {}\n\n\tget(target: T, prop: string | symbol): any {\n\t\tif (prop === 'sourceTable') {\n\t\t\treturn aliasedTable(target.sourceTable, this.alias);\n\t\t}\n\n\t\treturn target[prop as keyof typeof target];\n\t}\n}\n\nexport function aliasedTable<T extends Table>(table: T, tableAlias: string): T {\n\treturn new Proxy(table, new TableAliasProxyHandler(tableAlias, false));\n}\n\nexport function aliasedRelation<T extends Relation>(relation: T, tableAlias: string): T {\n\treturn new Proxy(relation, new RelationTableAliasProxyHandler(tableAlias));\n}\n\nexport function aliasedTableColumn<T extends AnyColumn>(column: T, tableAlias: string): T {\n\treturn new Proxy(\n\t\tcolumn,\n\t\tnew ColumnAliasProxyHandler(new Proxy(column.table, new TableAliasProxyHandler(tableAlias, false))),\n\t);\n}\n\nexport function mapColumnsInAliasedSQLToAlias(query: SQL.Aliased, alias: string): SQL.Aliased {\n\treturn new SQL.Aliased(mapColumnsInSQLToAlias(query.sql, alias), query.fieldAlias);\n}\n\nexport function mapColumnsInSQLToAlias(query: SQL, alias: string): SQL {\n\treturn sql.join(query.queryChunks.map((c) => {\n\t\tif (is(c, Column)) {\n\t\t\treturn aliasedTableColumn(c, alias);\n\t\t}\n\t\tif (is(c, SQL)) {\n\t\t\treturn mapColumnsInSQLToAlias(c, alias);\n\t\t}\n\t\tif (is(c, SQL.Aliased)) {\n\t\t\treturn mapColumnsInAliasedSQLToAlias(c, alias);\n\t\t}\n\t\treturn c;\n\t}));\n}\n"], "names": [], "mappings": ";;;;;;;;;;AACA,SAAS,cAAc;AACvB,SAAS,YAAY,UAAU;AAG/B,SAAS,KAAK,WAAW;AACzB,SAAS,aAAa;AACtB,SAAS,sBAAsB;;;;;;AAExB,MAAM,wBAAiF;IAG7F,YAAoB,KAAA,CAAqB;QAArB,IAAA,CAAA,KAAA,GAAA;IAAsB;IAF1C,OAAA,mPAAiB,aAAU,CAAA,GAAY,0BAAA;IAIvC,IAAI,SAAA,EAAoB,IAAA,EAA4B;QACnD,IAAI,SAAS,SAAS;YACrB,OAAO,IAAA,CAAK,KAAA;QACb;QAEA,OAAO,SAAA,CAAU,IAAqB,CAAA;IACvC;AACD;AAEO,MAAM,uBAA0E;IAGtF,YAAoB,KAAA,EAAuB,mBAAA,CAA8B;QAArD,IAAA,CAAA,KAAA,GAAA;QAAuB,IAAA,CAAA,mBAAA,GAAA;IAA+B;IAF1E,OAAA,CAAiB,+PAAU,CAAA,GAAY,yBAAA;IAIvC,IAAI,MAAA,EAAW,IAAA,EAA4B;QAC1C,IAAI,SAAS,yPAAA,CAAM,MAAA,CAAO,OAAA,EAAS;YAClC,OAAO;QACR;QAEA,IAAI,0PAAS,QAAA,CAAM,MAAA,CAAO,IAAA,EAAM;YAC/B,OAAO,IAAA,CAAK,KAAA;QACb;QAEA,IAAI,IAAA,CAAK,mBAAA,IAAuB,0PAAS,QAAA,CAAM,MAAA,CAAO,YAAA,EAAc;YACnE,OAAO,IAAA,CAAK,KAAA;QACb;QAEA,IAAI,mQAAS,iBAAA,EAAgB;YAC5B,OAAO;gBACN,GAAG,MAAA,CAAO,2QAAqC,CAAA;gBAC/C,MAAM,IAAA,CAAK,KAAA;gBACX,SAAS;YACV;QACD;QAEA,IAAI,0PAAS,QAAA,CAAM,MAAA,CAAO,OAAA,EAAS;YAClC,MAAM,UAAW,MAAA,kPAAiB,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA;YACtD,IAAI,CAAC,SAAS;gBACb,OAAO;YACR;YAEA,MAAM,iBAAyC,CAAC;YAEhD,OAAO,IAAA,CAAK,OAAO,EAAE,GAAA,CAAI,CAAC,QAAQ;gBACjC,cAAA,CAAe,GAAG,CAAA,GAAI,IAAI,MACzB,OAAA,CAAQ,GAAG,CAAA,EACX,IAAI,wBAAwB,IAAI,MAAM,QAAQ,IAAI,CAAC;YAErD,CAAC;YAED,OAAO;QACR;QAEA,MAAM,QAAQ,MAAA,CAAO,IAA2B,CAAA;QAChD,IAAI,2PAAA,EAAG,yPAAO,SAAM,GAAG;YACtB,OAAO,IAAI,MAAM,OAAoB,IAAI,wBAAwB,IAAI,MAAM,QAAQ,IAAI,CAAC,CAAC;QAC1F;QAEA,OAAO;IACR;AACD;AAEO,MAAM,+BAA8E;IAG1F,YAAoB,KAAA,CAAe;QAAf,IAAA,CAAA,KAAA,GAAA;IAAgB;IAFpC,OAAA,mPAAiB,aAAU,CAAA,GAAY,iCAAA;IAIvC,IAAI,MAAA,EAAW,IAAA,EAA4B;QAC1C,IAAI,SAAS,eAAe;YAC3B,OAAO,aAAa,OAAO,WAAA,EAAa,IAAA,CAAK,KAAK;QACnD;QAEA,OAAO,MAAA,CAAO,IAA2B,CAAA;IAC1C;AACD;AAEO,SAAS,aAA8B,KAAA,EAAU,UAAA,EAAuB;IAC9E,OAAO,IAAI,MAAM,OAAO,IAAI,uBAAuB,YAAY,KAAK,CAAC;AACtE;AAEO,SAAS,gBAAoC,QAAA,EAAa,UAAA,EAAuB;IACvF,OAAO,IAAI,MAAM,UAAU,IAAI,+BAA+B,UAAU,CAAC;AAC1E;AAEO,SAAS,mBAAwC,MAAA,EAAW,UAAA,EAAuB;IACzF,OAAO,IAAI,MACV,QACA,IAAI,wBAAwB,IAAI,MAAM,OAAO,KAAA,EAAO,IAAI,uBAAuB,YAAY,KAAK,CAAC,CAAC;AAEpG;AAEO,SAAS,8BAA8B,KAAA,EAAoB,KAAA,EAA4B;IAC7F,OAAO,0PAAI,MAAA,CAAI,OAAA,CAAQ,uBAAuB,MAAM,GAAA,EAAK,KAAK,GAAG,MAAM,UAAU;AAClF;AAEO,SAAS,uBAAuB,KAAA,EAAY,KAAA,EAAoB;IACtE,6PAAO,MAAA,CAAI,IAAA,CAAK,MAAM,WAAA,CAAY,GAAA,CAAI,CAAC,MAAM;QAC5C,0PAAI,KAAA,EAAG,qPAAG,SAAM,GAAG;YAClB,OAAO,mBAAmB,GAAG,KAAK;QACnC;QACA,IAAI,2PAAA,EAAG,yPAAG,MAAG,GAAG;YACf,OAAO,uBAAuB,GAAG,KAAK;QACvC;QACA,0PAAI,KAAA,EAAG,yPAAG,MAAA,CAAI,OAAO,GAAG;YACvB,OAAO,8BAA8B,GAAG,KAAK;QAC9C;QACA,OAAO;IACR,CAAC,CAAC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3039, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/selection-proxy.ts"], "sourcesContent": ["import { ColumnAliasProxyHandler, TableAliasProxyHandler } from './alias.ts';\nimport { Column } from './column.ts';\nimport { entityKind, is } from './entity.ts';\nimport { SQL, View } from './sql/sql.ts';\nimport { Subquery } from './subquery.ts';\nimport { ViewBaseConfig } from './view-common.ts';\n\nexport class SelectionProxyHandler<T extends Subquery | Record<string, unknown> | View>\n\timplements ProxyHandler<Subquery | Record<string, unknown> | View>\n{\n\tstatic readonly [entityKind]: string = 'SelectionProxyHandler';\n\n\tprivate config: {\n\t\t/**\n\t\t * Table alias for the columns\n\t\t */\n\t\talias?: string;\n\t\t/**\n\t\t * What to do when a field is an instance of `SQL.Aliased` and it's not a selection field (from a subquery)\n\t\t *\n\t\t * `sql` - return the underlying SQL expression\n\t\t *\n\t\t * `alias` - return the field alias\n\t\t */\n\t\tsqlAliasedBehavior: 'sql' | 'alias';\n\t\t/**\n\t\t * What to do when a field is an instance of `SQL` and it doesn't have an alias declared\n\t\t *\n\t\t * `sql` - return the underlying SQL expression\n\t\t *\n\t\t * `error` - return a DrizzleTypeError on type level and throw an error on runtime\n\t\t */\n\t\tsqlBehavior: 'sql' | 'error';\n\n\t\t/**\n\t\t * Whether to replace the original name of the column with the alias\n\t\t * Should be set to `true` for views creation\n\t\t * @default false\n\t\t */\n\t\treplaceOriginalName?: boolean;\n\t};\n\n\tconstructor(config: SelectionProxyHandler<T>['config']) {\n\t\tthis.config = { ...config };\n\t}\n\n\tget(subquery: T, prop: string | symbol): any {\n\t\tif (prop === '_') {\n\t\t\treturn {\n\t\t\t\t...subquery['_' as keyof typeof subquery],\n\t\t\t\tselectedFields: new Proxy(\n\t\t\t\t\t(subquery as Subquery)._.selectedFields,\n\t\t\t\t\tthis as ProxyHandler<Record<string, unknown>>,\n\t\t\t\t),\n\t\t\t};\n\t\t}\n\n\t\tif (prop === ViewBaseConfig) {\n\t\t\treturn {\n\t\t\t\t...subquery[ViewBaseConfig as keyof typeof subquery],\n\t\t\t\tselectedFields: new Proxy(\n\t\t\t\t\t(subquery as View)[ViewBaseConfig].selectedFields,\n\t\t\t\t\tthis as ProxyHandler<Record<string, unknown>>,\n\t\t\t\t),\n\t\t\t};\n\t\t}\n\n\t\tif (typeof prop === 'symbol') {\n\t\t\treturn subquery[prop as keyof typeof subquery];\n\t\t}\n\n\t\tconst columns = is(subquery, Subquery)\n\t\t\t? subquery._.selectedFields\n\t\t\t: is(subquery, View)\n\t\t\t? subquery[ViewBaseConfig].selectedFields\n\t\t\t: subquery;\n\t\tconst value: unknown = columns[prop as keyof typeof columns];\n\n\t\tif (is(value, SQL.Aliased)) {\n\t\t\t// Never return the underlying SQL expression for a field previously selected in a subquery\n\t\t\tif (this.config.sqlAliasedBehavior === 'sql' && !value.isSelectionField) {\n\t\t\t\treturn value.sql;\n\t\t\t}\n\n\t\t\tconst newValue = value.clone();\n\t\t\tnewValue.isSelectionField = true;\n\t\t\treturn newValue;\n\t\t}\n\n\t\tif (is(value, SQL)) {\n\t\t\tif (this.config.sqlBehavior === 'sql') {\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tthrow new Error(\n\t\t\t\t`You tried to reference \"${prop}\" field from a subquery, which is a raw SQL field, but it doesn't have an alias declared. Please add an alias to the field using \".as('alias')\" method.`,\n\t\t\t);\n\t\t}\n\n\t\tif (is(value, Column)) {\n\t\t\tif (this.config.alias) {\n\t\t\t\treturn new Proxy(\n\t\t\t\t\tvalue,\n\t\t\t\t\tnew ColumnAliasProxyHandler(\n\t\t\t\t\t\tnew Proxy(\n\t\t\t\t\t\t\tvalue.table,\n\t\t\t\t\t\t\tnew TableAliasProxyHandler(this.config.alias, this.config.replaceOriginalName ?? false),\n\t\t\t\t\t\t),\n\t\t\t\t\t),\n\t\t\t\t);\n\t\t\t}\n\t\t\treturn value;\n\t\t}\n\n\t\tif (typeof value !== 'object' || value === null) {\n\t\t\treturn value;\n\t\t}\n\n\t\treturn new Proxy(value, new SelectionProxyHandler(this.config));\n\t}\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,yBAAyB,8BAA8B;AAChE,SAAS,cAAc;AACvB,SAAS,YAAY,UAAU;AAC/B,SAAS,KAAK,YAAY;AAC1B,SAAS,gBAAgB;AACzB,SAAS,sBAAsB;;;;;;;AAExB,MAAM,sBAEb;IACC,OAAA,mPAAiB,aAAU,CAAA,GAAY,wBAAA;IAE/B,OAAA;IA8BR,YAAY,MAAA,CAA4C;QACvD,IAAA,CAAK,MAAA,GAAS;YAAE,GAAG,MAAA;QAAO;IAC3B;IAEA,IAAI,QAAA,EAAa,IAAA,EAA4B;QAC5C,IAAI,SAAS,KAAK;YACjB,OAAO;gBACN,GAAG,QAAA,CAAS,GAA4B,CAAA;gBACxC,gBAAgB,IAAI,MAClB,SAAsB,CAAA,CAAE,cAAA,EACzB,IAAA;YAEF;QACD;QAEA,IAAI,mQAAS,iBAAA,EAAgB;YAC5B,OAAO;gBACN,GAAG,QAAA,0PAAS,kBAAuC,CAAA;gBACnD,gBAAgB,IAAI,MAClB,QAAA,2PAAkB,iBAAc,CAAA,CAAE,cAAA,EACnC,IAAA;YAEF;QACD;QAEA,IAAI,OAAO,SAAS,UAAU;YAC7B,OAAO,QAAA,CAAS,IAA6B,CAAA;QAC9C;QAEA,MAAM,cAAU,uPAAA,EAAG,8PAAU,WAAQ,IAClC,SAAS,CAAA,CAAE,cAAA,yPACX,KAAA,EAAG,+PAAU,QAAI,IACjB,QAAA,2PAAS,iBAAc,CAAA,CAAE,cAAA,GACzB;QACH,MAAM,QAAiB,OAAA,CAAQ,IAA4B,CAAA;QAE3D,0PAAI,KAAA,EAAG,6PAAO,MAAA,CAAI,OAAO,GAAG;YAE3B,IAAI,IAAA,CAAK,MAAA,CAAO,kBAAA,KAAuB,SAAS,CAAC,MAAM,gBAAA,EAAkB;gBACxE,OAAO,MAAM,GAAA;YACd;YAEA,MAAM,WAAW,MAAM,KAAA,CAAM;YAC7B,SAAS,gBAAA,GAAmB;YAC5B,OAAO;QACR;QAEA,0PAAI,KAAA,EAAG,6PAAO,MAAG,GAAG;YACnB,IAAI,IAAA,CAAK,MAAA,CAAO,WAAA,KAAgB,OAAO;gBACtC,OAAO;YACR;YAEA,MAAM,IAAI,MACT,CAAA,wBAAA,EAA2B,IAAI,CAAA,uJAAA,CAAA;QAEjC;QAEA,IAAI,2PAAA,EAAG,yPAAO,SAAM,GAAG;YACtB,IAAI,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO;gBACtB,OAAO,IAAI,MACV,OACA,qPAAI,0BAAA,CACH,IAAI,MACH,MAAM,KAAA,EACN,qPAAI,yBAAA,CAAuB,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO,IAAA,CAAK,MAAA,CAAO,mBAAA,IAAuB,KAAK;YAI1F;YACA,OAAO;QACR;QAEA,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;YAChD,OAAO;QACR;QAEA,OAAO,IAAI,MAAM,OAAO,IAAI,sBAAsB,IAAA,CAAK,MAAM,CAAC;IAC/D;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/query-builders/select.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport type { PgColumn } from '~/pg-core/columns/index.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type { PgSession, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport type { SubqueryWithSelection } from '~/pg-core/subquery.ts';\nimport type { PgTable } from '~/pg-core/table.ts';\nimport { PgViewBase } from '~/pg-core/view-base.ts';\nimport { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type {\n\tBuildSubquerySelection,\n\tGetSelectTableName,\n\tGetSelectTableSelection,\n\tJoinNullability,\n\tJoinType,\n\tSelectMode,\n\tSelectResult,\n\tSetOperator,\n} from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport { SQL, View } from '~/sql/sql.ts';\nimport type { ColumnsSelection, Placeholder, Query, SQLWrapper } from '~/sql/sql.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport { tracer } from '~/tracing.ts';\nimport { applyMixins, getTableColumns, getTableLikeName, haveSameKeys, type ValueOrArray } from '~/utils.ts';\nimport { orderSelectedFields } from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type {\n\tAnyPgSelect,\n\tCreatePgSelectFromBuilderMode,\n\tGetPgSetOperators,\n\tLockConfig,\n\tLockStrength,\n\tPgCreateSetOperatorFn,\n\tPgJoinFn,\n\tPgSelectConfig,\n\tPgSelectDynamic,\n\tPgSelectHKT,\n\tPgSelectHKTBase,\n\tPgSelectPrepare,\n\tPgSelectWithout,\n\tPgSetOperatorExcludedMethods,\n\tPgSetOperatorWithResult,\n\tSelectedFields,\n\tSetOperatorRightSelect,\n} from './select.types.ts';\n\nexport class PgSelectBuilder<\n\tTSelection extends SelectedFields | undefined,\n\tTBuilderMode extends 'db' | 'qb' = 'db',\n> {\n\tstatic readonly [entityKind]: string = 'PgSelectBuilder';\n\n\tprivate fields: TSelection;\n\tprivate session: PgSession | undefined;\n\tprivate dialect: PgDialect;\n\tprivate withList: Subquery[] = [];\n\tprivate distinct: boolean | {\n\t\ton: (PgColumn | SQLWrapper)[];\n\t} | undefined;\n\n\tconstructor(\n\t\tconfig: {\n\t\t\tfields: TSelection;\n\t\t\tsession: PgSession | undefined;\n\t\t\tdialect: PgDialect;\n\t\t\twithList?: Subquery[];\n\t\t\tdistinct?: boolean | {\n\t\t\t\ton: (PgColumn | SQLWrapper)[];\n\t\t\t};\n\t\t},\n\t) {\n\t\tthis.fields = config.fields;\n\t\tthis.session = config.session;\n\t\tthis.dialect = config.dialect;\n\t\tif (config.withList) {\n\t\t\tthis.withList = config.withList;\n\t\t}\n\t\tthis.distinct = config.distinct;\n\t}\n\n\t/**\n\t * Specify the table, subquery, or other target that you're\n\t * building a select query against.\n\t *\n\t * {@link https://www.postgresql.org/docs/current/sql-select.html#SQL-FROM | Postgres from documentation}\n\t */\n\tfrom<TFrom extends PgTable | Subquery | PgViewBase | SQL>(\n\t\tsource: TFrom,\n\t): CreatePgSelectFromBuilderMode<\n\t\tTBuilderMode,\n\t\tGetSelectTableName<TFrom>,\n\t\tTSelection extends undefined ? GetSelectTableSelection<TFrom> : TSelection,\n\t\tTSelection extends undefined ? 'single' : 'partial'\n\t> {\n\t\tconst isPartialSelect = !!this.fields;\n\n\t\tlet fields: SelectedFields;\n\t\tif (this.fields) {\n\t\t\tfields = this.fields;\n\t\t} else if (is(source, Subquery)) {\n\t\t\t// This is required to use the proxy handler to get the correct field values from the subquery\n\t\t\tfields = Object.fromEntries(\n\t\t\t\tObject.keys(source._.selectedFields).map((\n\t\t\t\t\tkey,\n\t\t\t\t) => [key, source[key as unknown as keyof typeof source] as unknown as SelectedFields[string]]),\n\t\t\t);\n\t\t} else if (is(source, PgViewBase)) {\n\t\t\tfields = source[ViewBaseConfig].selectedFields as SelectedFields;\n\t\t} else if (is(source, SQL)) {\n\t\t\tfields = {};\n\t\t} else {\n\t\t\tfields = getTableColumns<PgTable>(source);\n\t\t}\n\n\t\treturn new PgSelectBase({\n\t\t\ttable: source,\n\t\t\tfields,\n\t\t\tisPartialSelect,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t\twithList: this.withList,\n\t\t\tdistinct: this.distinct,\n\t\t}) as any;\n\t}\n}\n\nexport abstract class PgSelectQueryBuilderBase<\n\tTHKT extends PgSelectHKTBase,\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> extends TypedQueryBuilder<TSelectedFields, TResult> {\n\tstatic readonly [entityKind]: string = 'PgSelectQueryBuilder';\n\n\toverride readonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly hkt: THKT;\n\t\treadonly tableName: TTableName;\n\t\treadonly selection: TSelection;\n\t\treadonly selectMode: TSelectMode;\n\t\treadonly nullabilityMap: TNullabilityMap;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TResult;\n\t\treadonly selectedFields: TSelectedFields;\n\t};\n\n\tprotected config: PgSelectConfig;\n\tprotected joinsNotNullableMap: Record<string, boolean>;\n\tprivate tableName: string | undefined;\n\tprivate isPartialSelect: boolean;\n\tprotected session: PgSession | undefined;\n\tprotected dialect: PgDialect;\n\n\tconstructor(\n\t\t{ table, fields, isPartialSelect, session, dialect, withList, distinct }: {\n\t\t\ttable: PgSelectConfig['table'];\n\t\t\tfields: PgSelectConfig['fields'];\n\t\t\tisPartialSelect: boolean;\n\t\t\tsession: PgSession | undefined;\n\t\t\tdialect: PgDialect;\n\t\t\twithList: Subquery[];\n\t\t\tdistinct: boolean | {\n\t\t\t\ton: (PgColumn | SQLWrapper)[];\n\t\t\t} | undefined;\n\t\t},\n\t) {\n\t\tsuper();\n\t\tthis.config = {\n\t\t\twithList,\n\t\t\ttable,\n\t\t\tfields: { ...fields },\n\t\t\tdistinct,\n\t\t\tsetOperators: [],\n\t\t};\n\t\tthis.isPartialSelect = isPartialSelect;\n\t\tthis.session = session;\n\t\tthis.dialect = dialect;\n\t\tthis._ = {\n\t\t\tselectedFields: fields as TSelectedFields,\n\t\t} as this['_'];\n\t\tthis.tableName = getTableLikeName(table);\n\t\tthis.joinsNotNullableMap = typeof this.tableName === 'string' ? { [this.tableName]: true } : {};\n\t}\n\n\tprivate createJoin<TJoinType extends JoinType>(\n\t\tjoinType: TJoinType,\n\t): PgJoinFn<this, TDynamic, TJoinType> {\n\t\treturn (\n\t\t\ttable: PgTable | Subquery | PgViewBase | SQL,\n\t\t\ton: ((aliases: TSelection) => SQL | undefined) | SQL | undefined,\n\t\t) => {\n\t\t\tconst baseTableName = this.tableName;\n\t\t\tconst tableName = getTableLikeName(table);\n\n\t\t\tif (typeof tableName === 'string' && this.config.joins?.some((join) => join.alias === tableName)) {\n\t\t\t\tthrow new Error(`Alias \"${tableName}\" is already used in this query`);\n\t\t\t}\n\n\t\t\tif (!this.isPartialSelect) {\n\t\t\t\t// If this is the first join and this is not a partial select and we're not selecting from raw SQL, \"move\" the fields from the main table to the nested object\n\t\t\t\tif (Object.keys(this.joinsNotNullableMap).length === 1 && typeof baseTableName === 'string') {\n\t\t\t\t\tthis.config.fields = {\n\t\t\t\t\t\t[baseTableName]: this.config.fields,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tif (typeof tableName === 'string' && !is(table, SQL)) {\n\t\t\t\t\tconst selection = is(table, Subquery)\n\t\t\t\t\t\t? table._.selectedFields\n\t\t\t\t\t\t: is(table, View)\n\t\t\t\t\t\t? table[ViewBaseConfig].selectedFields\n\t\t\t\t\t\t: table[Table.Symbol.Columns];\n\t\t\t\t\tthis.config.fields[tableName] = selection;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (typeof on === 'function') {\n\t\t\t\ton = on(\n\t\t\t\t\tnew Proxy(\n\t\t\t\t\t\tthis.config.fields,\n\t\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t\t) as TSelection,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tif (!this.config.joins) {\n\t\t\t\tthis.config.joins = [];\n\t\t\t}\n\n\t\t\tthis.config.joins.push({ on, table, joinType, alias: tableName });\n\n\t\t\tif (typeof tableName === 'string') {\n\t\t\t\tswitch (joinType) {\n\t\t\t\t\tcase 'left': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'right': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'inner': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'full': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn this as any;\n\t\t};\n\t}\n\n\t/**\n\t * Executes a `left join` operation by adding another table to the current query.\n\t *\n\t * Calling this method associates each row of the table with the corresponding row from the joined table, if a match is found. If no matching row exists, it sets all columns of the joined table to null.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#left-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User; pets: Pet | null }[] = await db.select()\n\t *   .from(users)\n\t *   .leftJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number; petId: number | null }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .leftJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\tleftJoin = this.createJoin('left');\n\n\t/**\n\t * Executes a `right join` operation by adding another table to the current query.\n\t *\n\t * Calling this method associates each row of the joined table with the corresponding row from the main table, if a match is found. If no matching row exists, it sets all columns of the main table to null.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#right-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User | null; pets: Pet }[] = await db.select()\n\t *   .from(users)\n\t *   .rightJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number | null; petId: number }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .rightJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\trightJoin = this.createJoin('right');\n\n\t/**\n\t * Executes an `inner join` operation, creating a new table by combining rows from two tables that have matching values.\n\t *\n\t * Calling this method retrieves rows that have corresponding entries in both joined tables. Rows without matching entries in either table are excluded, resulting in a table that includes only matching pairs.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#inner-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User; pets: Pet }[] = await db.select()\n\t *   .from(users)\n\t *   .innerJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number; petId: number }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .innerJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\tinnerJoin = this.createJoin('inner');\n\n\t/**\n\t * Executes a `full join` operation by combining rows from two tables into a new table.\n\t *\n\t * Calling this method retrieves all rows from both main and joined tables, merging rows with matching values and filling in `null` for non-matching columns.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#full-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User | null; pets: Pet | null }[] = await db.select()\n\t *   .from(users)\n\t *   .fullJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number | null; petId: number | null }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .fullJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\tfullJoin = this.createJoin('full');\n\n\tprivate createSetOperator(\n\t\ttype: SetOperator,\n\t\tisAll: boolean,\n\t): <TValue extends PgSetOperatorWithResult<TResult>>(\n\t\trightSelection:\n\t\t\t| ((setOperators: GetPgSetOperators) => SetOperatorRightSelect<TValue, TResult>)\n\t\t\t| SetOperatorRightSelect<TValue, TResult>,\n\t) => PgSelectWithout<\n\t\tthis,\n\t\tTDynamic,\n\t\tPgSetOperatorExcludedMethods,\n\t\ttrue\n\t> {\n\t\treturn (rightSelection) => {\n\t\t\tconst rightSelect = (typeof rightSelection === 'function'\n\t\t\t\t? rightSelection(getPgSetOperators())\n\t\t\t\t: rightSelection) as TypedQueryBuilder<\n\t\t\t\t\tany,\n\t\t\t\t\tTResult\n\t\t\t\t>;\n\n\t\t\tif (!haveSameKeys(this.getSelectedFields(), rightSelect.getSelectedFields())) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t'Set operator error (union / intersect / except): selected fields are not the same or are in a different order',\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthis.config.setOperators.push({ type, isAll, rightSelect });\n\t\t\treturn this as any;\n\t\t};\n\t}\n\n\t/**\n\t * Adds `union` set operator to the query.\n\t *\n\t * Calling this method will combine the result sets of the `select` statements and remove any duplicate rows that appear across them.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#union}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all unique names from customers and users tables\n\t * await db.select({ name: users.name })\n\t *   .from(users)\n\t *   .union(\n\t *     db.select({ name: customers.name }).from(customers)\n\t *   );\n\t * // or\n\t * import { union } from 'drizzle-orm/pg-core'\n\t *\n\t * await union(\n\t *   db.select({ name: users.name }).from(users),\n\t *   db.select({ name: customers.name }).from(customers)\n\t * );\n\t * ```\n\t */\n\tunion = this.createSetOperator('union', false);\n\n\t/**\n\t * Adds `union all` set operator to the query.\n\t *\n\t * Calling this method will combine the result-set of the `select` statements and keep all duplicate rows that appear across them.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#union-all}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all transaction ids from both online and in-store sales\n\t * await db.select({ transaction: onlineSales.transactionId })\n\t *   .from(onlineSales)\n\t *   .unionAll(\n\t *     db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n\t *   );\n\t * // or\n\t * import { unionAll } from 'drizzle-orm/pg-core'\n\t *\n\t * await unionAll(\n\t *   db.select({ transaction: onlineSales.transactionId }).from(onlineSales),\n\t *   db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n\t * );\n\t * ```\n\t */\n\tunionAll = this.createSetOperator('union', true);\n\n\t/**\n\t * Adds `intersect` set operator to the query.\n\t *\n\t * Calling this method will retain only the rows that are present in both result sets and eliminate duplicates.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select course names that are offered in both departments A and B\n\t * await db.select({ courseName: depA.courseName })\n\t *   .from(depA)\n\t *   .intersect(\n\t *     db.select({ courseName: depB.courseName }).from(depB)\n\t *   );\n\t * // or\n\t * import { intersect } from 'drizzle-orm/pg-core'\n\t *\n\t * await intersect(\n\t *   db.select({ courseName: depA.courseName }).from(depA),\n\t *   db.select({ courseName: depB.courseName }).from(depB)\n\t * );\n\t * ```\n\t */\n\tintersect = this.createSetOperator('intersect', false);\n\n\t/**\n\t * Adds `intersect all` set operator to the query.\n\t *\n\t * Calling this method will retain only the rows that are present in both result sets including all duplicates.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect-all}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all products and quantities that are ordered by both regular and VIP customers\n\t * await db.select({\n\t *   productId: regularCustomerOrders.productId,\n\t *   quantityOrdered: regularCustomerOrders.quantityOrdered\n\t * })\n\t * .from(regularCustomerOrders)\n\t * .intersectAll(\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * // or\n\t * import { intersectAll } from 'drizzle-orm/pg-core'\n\t *\n\t * await intersectAll(\n\t *   db.select({\n\t *     productId: regularCustomerOrders.productId,\n\t *     quantityOrdered: regularCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(regularCustomerOrders),\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * ```\n\t */\n\tintersectAll = this.createSetOperator('intersect', true);\n\n\t/**\n\t * Adds `except` set operator to the query.\n\t *\n\t * Calling this method will retrieve all unique rows from the left query, except for the rows that are present in the result set of the right query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#except}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all courses offered in department A but not in department B\n\t * await db.select({ courseName: depA.courseName })\n\t *   .from(depA)\n\t *   .except(\n\t *     db.select({ courseName: depB.courseName }).from(depB)\n\t *   );\n\t * // or\n\t * import { except } from 'drizzle-orm/pg-core'\n\t *\n\t * await except(\n\t *   db.select({ courseName: depA.courseName }).from(depA),\n\t *   db.select({ courseName: depB.courseName }).from(depB)\n\t * );\n\t * ```\n\t */\n\texcept = this.createSetOperator('except', false);\n\n\t/**\n\t * Adds `except all` set operator to the query.\n\t *\n\t * Calling this method will retrieve all rows from the left query, except for the rows that are present in the result set of the right query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#except-all}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all products that are ordered by regular customers but not by VIP customers\n\t * await db.select({\n\t *   productId: regularCustomerOrders.productId,\n\t *   quantityOrdered: regularCustomerOrders.quantityOrdered,\n\t * })\n\t * .from(regularCustomerOrders)\n\t * .exceptAll(\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered,\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * // or\n\t * import { exceptAll } from 'drizzle-orm/pg-core'\n\t *\n\t * await exceptAll(\n\t *   db.select({\n\t *     productId: regularCustomerOrders.productId,\n\t *     quantityOrdered: regularCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(regularCustomerOrders),\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * ```\n\t */\n\texceptAll = this.createSetOperator('except', true);\n\n\t/** @internal */\n\taddSetOperators(setOperators: PgSelectConfig['setOperators']): PgSelectWithout<\n\t\tthis,\n\t\tTDynamic,\n\t\tPgSetOperatorExcludedMethods,\n\t\ttrue\n\t> {\n\t\tthis.config.setOperators.push(...setOperators);\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `where` clause to the query.\n\t *\n\t * Calling this method will select only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#filtering}\n\t *\n\t * @param where the `where` clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be selected.\n\t *\n\t * ```ts\n\t * // Select all cars with green color\n\t * await db.select().from(cars).where(eq(cars.color, 'green'));\n\t * // or\n\t * await db.select().from(cars).where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Select all BMW cars with a green color\n\t * await db.select().from(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Select all cars with the green or blue color\n\t * await db.select().from(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(\n\t\twhere: ((aliases: this['_']['selection']) => SQL | undefined) | SQL | undefined,\n\t): PgSelectWithout<this, TDynamic, 'where'> {\n\t\tif (typeof where === 'function') {\n\t\t\twhere = where(\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\t\t}\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `having` clause to the query.\n\t *\n\t * Calling this method will select only those rows that fulfill a specified condition. It is typically used with aggregate functions to filter the aggregated data based on a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#aggregations}\n\t *\n\t * @param having the `having` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all brands with more than one car\n\t * await db.select({\n\t * \tbrand: cars.brand,\n\t * \tcount: sql<number>`cast(count(${cars.id}) as int)`,\n\t * })\n\t *   .from(cars)\n\t *   .groupBy(cars.brand)\n\t *   .having(({ count }) => gt(count, 1));\n\t * ```\n\t */\n\thaving(\n\t\thaving: ((aliases: this['_']['selection']) => SQL | undefined) | SQL | undefined,\n\t): PgSelectWithout<this, TDynamic, 'having'> {\n\t\tif (typeof having === 'function') {\n\t\t\thaving = having(\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\t\t}\n\t\tthis.config.having = having;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `group by` clause to the query.\n\t *\n\t * Calling this method will group rows that have the same values into summary rows, often used for aggregation purposes.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#aggregations}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Group and count people by their last names\n\t * await db.select({\n\t *    lastName: people.lastName,\n\t *    count: sql<number>`cast(count(*) as int)`\n\t * })\n\t *   .from(people)\n\t *   .groupBy(people.lastName);\n\t * ```\n\t */\n\tgroupBy(\n\t\tbuilder: (aliases: this['_']['selection']) => ValueOrArray<PgColumn | SQL | SQL.Aliased>,\n\t): PgSelectWithout<this, TDynamic, 'groupBy'>;\n\tgroupBy(...columns: (PgColumn | SQL | SQL.Aliased)[]): PgSelectWithout<this, TDynamic, 'groupBy'>;\n\tgroupBy(\n\t\t...columns:\n\t\t\t| [(aliases: this['_']['selection']) => ValueOrArray<PgColumn | SQL | SQL.Aliased>]\n\t\t\t| (PgColumn | SQL | SQL.Aliased)[]\n\t): PgSelectWithout<this, TDynamic, 'groupBy'> {\n\t\tif (typeof columns[0] === 'function') {\n\t\t\tconst groupBy = columns[0](\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'alias', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\t\t\tthis.config.groupBy = Array.isArray(groupBy) ? groupBy : [groupBy];\n\t\t} else {\n\t\t\tthis.config.groupBy = columns as (PgColumn | SQL | SQL.Aliased)[];\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `order by` clause to the query.\n\t *\n\t * Calling this method will sort the result-set in ascending or descending order. By default, the sort order is ascending.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#order-by}\n\t *\n\t * @example\n\t *\n\t * ```\n\t * // Select cars ordered by year\n\t * await db.select().from(cars).orderBy(cars.year);\n\t * ```\n\t *\n\t * You can specify whether results are in ascending or descending order with the `asc()` and `desc()` operators.\n\t *\n\t * ```ts\n\t * // Select cars ordered by year in descending order\n\t * await db.select().from(cars).orderBy(desc(cars.year));\n\t *\n\t * // Select cars ordered by year and price\n\t * await db.select().from(cars).orderBy(asc(cars.year), desc(cars.price));\n\t * ```\n\t */\n\torderBy(\n\t\tbuilder: (aliases: this['_']['selection']) => ValueOrArray<PgColumn | SQL | SQL.Aliased>,\n\t): PgSelectWithout<this, TDynamic, 'orderBy'>;\n\torderBy(...columns: (PgColumn | SQL | SQL.Aliased)[]): PgSelectWithout<this, TDynamic, 'orderBy'>;\n\torderBy(\n\t\t...columns:\n\t\t\t| [(aliases: this['_']['selection']) => ValueOrArray<PgColumn | SQL | SQL.Aliased>]\n\t\t\t| (PgColumn | SQL | SQL.Aliased)[]\n\t): PgSelectWithout<this, TDynamic, 'orderBy'> {\n\t\tif (typeof columns[0] === 'function') {\n\t\t\tconst orderBy = columns[0](\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'alias', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\n\t\t\tconst orderByArray = Array.isArray(orderBy) ? orderBy : [orderBy];\n\n\t\t\tif (this.config.setOperators.length > 0) {\n\t\t\t\tthis.config.setOperators.at(-1)!.orderBy = orderByArray;\n\t\t\t} else {\n\t\t\t\tthis.config.orderBy = orderByArray;\n\t\t\t}\n\t\t} else {\n\t\t\tconst orderByArray = columns as (PgColumn | SQL | SQL.Aliased)[];\n\n\t\t\tif (this.config.setOperators.length > 0) {\n\t\t\t\tthis.config.setOperators.at(-1)!.orderBy = orderByArray;\n\t\t\t} else {\n\t\t\t\tthis.config.orderBy = orderByArray;\n\t\t\t}\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `limit` clause to the query.\n\t *\n\t * Calling this method will set the maximum number of rows that will be returned by this query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#limit--offset}\n\t *\n\t * @param limit the `limit` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Get the first 10 people from this query.\n\t * await db.select().from(people).limit(10);\n\t * ```\n\t */\n\tlimit(limit: number | Placeholder): PgSelectWithout<this, TDynamic, 'limit'> {\n\t\tif (this.config.setOperators.length > 0) {\n\t\t\tthis.config.setOperators.at(-1)!.limit = limit;\n\t\t} else {\n\t\t\tthis.config.limit = limit;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `offset` clause to the query.\n\t *\n\t * Calling this method will skip a number of rows when returning results from this query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#limit--offset}\n\t *\n\t * @param offset the `offset` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Get the 10th-20th people from this query.\n\t * await db.select().from(people).offset(10).limit(10);\n\t * ```\n\t */\n\toffset(offset: number | Placeholder): PgSelectWithout<this, TDynamic, 'offset'> {\n\t\tif (this.config.setOperators.length > 0) {\n\t\t\tthis.config.setOperators.at(-1)!.offset = offset;\n\t\t} else {\n\t\t\tthis.config.offset = offset;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `for` clause to the query.\n\t *\n\t * Calling this method will specify a lock strength for this query that controls how strictly it acquires exclusive access to the rows being queried.\n\t *\n\t * See docs: {@link https://www.postgresql.org/docs/current/sql-select.html#SQL-FOR-UPDATE-SHARE}\n\t *\n\t * @param strength the lock strength.\n\t * @param config the lock configuration.\n\t */\n\tfor(strength: LockStrength, config: LockConfig = {}): PgSelectWithout<this, TDynamic, 'for'> {\n\t\tthis.config.lockingClause = { strength, config };\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildSelectQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\tas<TAlias extends string>(\n\t\talias: TAlias,\n\t): SubqueryWithSelection<this['_']['selectedFields'], TAlias> {\n\t\treturn new Proxy(\n\t\t\tnew Subquery(this.getSQL(), this.config.fields, alias),\n\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t) as SubqueryWithSelection<this['_']['selectedFields'], TAlias>;\n\t}\n\n\t/** @internal */\n\toverride getSelectedFields(): this['_']['selectedFields'] {\n\t\treturn new Proxy(\n\t\t\tthis.config.fields,\n\t\t\tnew SelectionProxyHandler({ alias: this.tableName, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t) as this['_']['selectedFields'];\n\t}\n\n\t$dynamic(): PgSelectDynamic<this> {\n\t\treturn this;\n\t}\n}\n\nexport interface PgSelectBase<\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> extends\n\tPgSelectQueryBuilderBase<\n\t\tPgSelectHKT,\n\t\tTTableName,\n\t\tTSelection,\n\t\tTSelectMode,\n\t\tTNullabilityMap,\n\t\tTDynamic,\n\t\tTExcludedMethods,\n\t\tTResult,\n\t\tTSelectedFields\n\t>,\n\tQueryPromise<TResult>,\n\tSQLWrapper\n{}\n\nexport class PgSelectBase<\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> extends PgSelectQueryBuilderBase<\n\tPgSelectHKT,\n\tTTableName,\n\tTSelection,\n\tTSelectMode,\n\tTNullabilityMap,\n\tTDynamic,\n\tTExcludedMethods,\n\tTResult,\n\tTSelectedFields\n> implements RunnableQuery<TResult, 'pg'>, SQLWrapper {\n\tstatic readonly [entityKind]: string = 'PgSelect';\n\n\t/** @internal */\n\t_prepare(name?: string): PgSelectPrepare<this> {\n\t\tconst { session, config, dialect, joinsNotNullableMap } = this;\n\t\tif (!session) {\n\t\t\tthrow new Error('Cannot execute a query on a query builder. Please use a database instance instead.');\n\t\t}\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\tconst fieldsList = orderSelectedFields<PgColumn>(config.fields);\n\t\t\tconst query = session.prepareQuery<\n\t\t\t\tPreparedQueryConfig & { execute: TResult }\n\t\t\t>(dialect.sqlToQuery(this.getSQL()), fieldsList, name, true);\n\t\t\tquery.joinsNotNullableMap = joinsNotNullableMap;\n\t\t\treturn query;\n\t\t});\n\t}\n\n\t/**\n\t * Create a prepared statement for this query. This allows\n\t * the database to remember this query for the given session\n\t * and call it by name, rather than specifying the full query.\n\t *\n\t * {@link https://www.postgresql.org/docs/current/sql-prepare.html | Postgres prepare documentation}\n\t */\n\tprepare(name: string): PgSelectPrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\texecute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues);\n\t\t});\n\t};\n}\n\napplyMixins(PgSelectBase, [QueryPromise]);\n\nfunction createSetOperator(type: SetOperator, isAll: boolean): PgCreateSetOperatorFn {\n\treturn (leftSelect, rightSelect, ...restSelects) => {\n\t\tconst setOperators = [rightSelect, ...restSelects].map((select) => ({\n\t\t\ttype,\n\t\t\tisAll,\n\t\t\trightSelect: select as AnyPgSelect,\n\t\t}));\n\n\t\tfor (const setOperator of setOperators) {\n\t\t\tif (!haveSameKeys((leftSelect as any).getSelectedFields(), setOperator.rightSelect.getSelectedFields())) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t'Set operator error (union / intersect / except): selected fields are not the same or are in a different order',\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\treturn (leftSelect as AnyPgSelect).addSetOperators(setOperators) as any;\n\t};\n}\n\nconst getPgSetOperators = () => ({\n\tunion,\n\tunionAll,\n\tintersect,\n\tintersectAll,\n\texcept,\n\texceptAll,\n});\n\n/**\n * Adds `union` set operator to the query.\n *\n * Calling this method will combine the result sets of the `select` statements and remove any duplicate rows that appear across them.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#union}\n *\n * @example\n *\n * ```ts\n * // Select all unique names from customers and users tables\n * import { union } from 'drizzle-orm/pg-core'\n *\n * await union(\n *   db.select({ name: users.name }).from(users),\n *   db.select({ name: customers.name }).from(customers)\n * );\n * // or\n * await db.select({ name: users.name })\n *   .from(users)\n *   .union(\n *     db.select({ name: customers.name }).from(customers)\n *   );\n * ```\n */\nexport const union = createSetOperator('union', false);\n\n/**\n * Adds `union all` set operator to the query.\n *\n * Calling this method will combine the result-set of the `select` statements and keep all duplicate rows that appear across them.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#union-all}\n *\n * @example\n *\n * ```ts\n * // Select all transaction ids from both online and in-store sales\n * import { unionAll } from 'drizzle-orm/pg-core'\n *\n * await unionAll(\n *   db.select({ transaction: onlineSales.transactionId }).from(onlineSales),\n *   db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n * );\n * // or\n * await db.select({ transaction: onlineSales.transactionId })\n *   .from(onlineSales)\n *   .unionAll(\n *     db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n *   );\n * ```\n */\nexport const unionAll = createSetOperator('union', true);\n\n/**\n * Adds `intersect` set operator to the query.\n *\n * Calling this method will retain only the rows that are present in both result sets and eliminate duplicates.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect}\n *\n * @example\n *\n * ```ts\n * // Select course names that are offered in both departments A and B\n * import { intersect } from 'drizzle-orm/pg-core'\n *\n * await intersect(\n *   db.select({ courseName: depA.courseName }).from(depA),\n *   db.select({ courseName: depB.courseName }).from(depB)\n * );\n * // or\n * await db.select({ courseName: depA.courseName })\n *   .from(depA)\n *   .intersect(\n *     db.select({ courseName: depB.courseName }).from(depB)\n *   );\n * ```\n */\nexport const intersect = createSetOperator('intersect', false);\n\n/**\n * Adds `intersect all` set operator to the query.\n *\n * Calling this method will retain only the rows that are present in both result sets including all duplicates.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect-all}\n *\n * @example\n *\n * ```ts\n * // Select all products and quantities that are ordered by both regular and VIP customers\n * import { intersectAll } from 'drizzle-orm/pg-core'\n *\n * await intersectAll(\n *   db.select({\n *     productId: regularCustomerOrders.productId,\n *     quantityOrdered: regularCustomerOrders.quantityOrdered\n *   })\n *   .from(regularCustomerOrders),\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered\n *   })\n *   .from(vipCustomerOrders)\n * );\n * // or\n * await db.select({\n *   productId: regularCustomerOrders.productId,\n *   quantityOrdered: regularCustomerOrders.quantityOrdered\n * })\n * .from(regularCustomerOrders)\n * .intersectAll(\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered\n *   })\n *   .from(vipCustomerOrders)\n * );\n * ```\n */\nexport const intersectAll = createSetOperator('intersect', true);\n\n/**\n * Adds `except` set operator to the query.\n *\n * Calling this method will retrieve all unique rows from the left query, except for the rows that are present in the result set of the right query.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#except}\n *\n * @example\n *\n * ```ts\n * // Select all courses offered in department A but not in department B\n * import { except } from 'drizzle-orm/pg-core'\n *\n * await except(\n *   db.select({ courseName: depA.courseName }).from(depA),\n *   db.select({ courseName: depB.courseName }).from(depB)\n * );\n * // or\n * await db.select({ courseName: depA.courseName })\n *   .from(depA)\n *   .except(\n *     db.select({ courseName: depB.courseName }).from(depB)\n *   );\n * ```\n */\nexport const except = createSetOperator('except', false);\n\n/**\n * Adds `except all` set operator to the query.\n *\n * Calling this method will retrieve all rows from the left query, except for the rows that are present in the result set of the right query.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#except-all}\n *\n * @example\n *\n * ```ts\n * // Select all products that are ordered by regular customers but not by VIP customers\n * import { exceptAll } from 'drizzle-orm/pg-core'\n *\n * await exceptAll(\n *   db.select({\n *     productId: regularCustomerOrders.productId,\n *     quantityOrdered: regularCustomerOrders.quantityOrdered\n *   })\n *   .from(regularCustomerOrders),\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered\n *   })\n *   .from(vipCustomerOrders)\n * );\n * // or\n * await db.select({\n *   productId: regularCustomerOrders.productId,\n *   quantityOrdered: regularCustomerOrders.quantityOrdered,\n * })\n * .from(regularCustomerOrders)\n * .exceptAll(\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered,\n *   })\n *   .from(vipCustomerOrders)\n * );\n * ```\n */\nexport const exceptAll = createSetOperator('except', true);\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA,SAAS,YAAY,UAAU;AAM/B,SAAS,kBAAkB;AAC3B,SAAS,yBAAyB;AAWlC,SAAS,oBAAoB;AAE7B,SAAS,6BAA6B;AACtC,SAAS,KAAK,YAAY;AAE1B,SAAS,gBAAgB;AACzB,SAAS,aAAa;AACtB,SAAS,cAAc;AACvB,SAAS,aAAa,iBAAiB,kBAAkB,oBAAuC;AAEhG,SAAS,sBAAsB;;;;;;;;;;;;;AAqBxB,MAAM,gBAGX;IACD,OAAA,mPAAiB,aAAU,CAAA,GAAY,kBAAA;IAE/B,OAAA;IACA,QAAA;IACA,QAAA;IACA,WAAuB,CAAC,CAAA,CAAA;IACxB,SAAA;IAIR,YACC,MAAA,CASC;QACD,IAAA,CAAK,MAAA,GAAS,OAAO,MAAA;QACrB,IAAA,CAAK,OAAA,GAAU,OAAO,OAAA;QACtB,IAAA,CAAK,OAAA,GAAU,OAAO,OAAA;QACtB,IAAI,OAAO,QAAA,EAAU;YACpB,IAAA,CAAK,QAAA,GAAW,OAAO,QAAA;QACxB;QACA,IAAA,CAAK,QAAA,GAAW,OAAO,QAAA;IACxB;IAAA;;;;;GAAA,GAQA,KACC,MAAA,EAMC;QACD,MAAM,kBAAkB,CAAC,CAAC,IAAA,CAAK,MAAA;QAE/B,IAAI;QACJ,IAAI,IAAA,CAAK,MAAA,EAAQ;YAChB,SAAS,IAAA,CAAK,MAAA;QACf,OAAA,0PAAW,KAAA,EAAG,4PAAQ,WAAQ,GAAG;YAEhC,SAAS,OAAO,WAAA,CACf,OAAO,IAAA,CAAK,OAAO,CAAA,CAAE,cAAc,EAAE,GAAA,CAAI,CACxC,MACI;oBAAC;oBAAK,MAAA,CAAO,GAAqC,CAAsC;iBAAC;QAEhG,OAAA,0PAAW,KAAA,EAAG,8QAAQ,aAAU,GAAG;YAClC,SAAS,MAAA,2PAAO,iBAAc,CAAA,CAAE,cAAA;QACjC,OAAA,IAAW,2PAAA,EAAG,8PAAQ,MAAG,GAAG;YAC3B,SAAS,CAAC;QACX,OAAO;YACN,8PAAS,kBAAA,EAAyB,MAAM;QACzC;QAEA,OAAO,IAAI,aAAa;YACvB,OAAO;YACP;YACA;YACA,SAAS,IAAA,CAAK,OAAA;YACd,SAAS,IAAA,CAAK,OAAA;YACd,UAAU,IAAA,CAAK,QAAA;YACf,UAAU,IAAA,CAAK,QAAA;QAChB,CAAC;IACF;AACD;AAEO,MAAe,kTAWZ,oBAAA,CAA4C;IACrD,OAAA,mPAAiB,aAAU,CAAA,GAAY,uBAAA;IAErB,EAAA;IAaR,OAAA;IACA,oBAAA;IACF,UAAA;IACA,gBAAA;IACE,QAAA;IACA,QAAA;IAEV,YACC,EAAE,KAAA,EAAO,MAAA,EAAQ,eAAA,EAAiB,OAAA,EAAS,OAAA,EAAS,QAAA,EAAU,QAAA,CAAS,CAAA,CAWtE;QACD,KAAA,CAAM;QACN,IAAA,CAAK,MAAA,GAAS;YACb;YACA;YACA,QAAQ;gBAAE,GAAG,MAAA;YAAO;YACpB;YACA,cAAc,CAAC,CAAA;QAChB;QACA,IAAA,CAAK,eAAA,GAAkB;QACvB,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,CAAA,GAAI;YACR,gBAAgB;QACjB;QACA,IAAA,CAAK,SAAA,wPAAY,mBAAA,EAAiB,KAAK;QACvC,IAAA,CAAK,mBAAA,GAAsB,OAAO,IAAA,CAAK,SAAA,KAAc,WAAW;YAAE,CAAC,IAAA,CAAK,SAAS,CAAA,EAAG;QAAK,IAAI,CAAC;IAC/F;IAEQ,WACP,QAAA,EACsC;QACtC,OAAO,CACN,OACA,OACI;YACJ,MAAM,gBAAgB,IAAA,CAAK,SAAA;YAC3B,MAAM,iQAAY,mBAAA,EAAiB,KAAK;YAExC,IAAI,OAAO,cAAc,YAAY,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO,KAAK,CAAC,OAAS,KAAK,KAAA,KAAU,SAAS,GAAG;gBACjG,MAAM,IAAI,MAAM,CAAA,OAAA,EAAU,SAAS,CAAA,+BAAA,CAAiC;YACrE;YAEA,IAAI,CAAC,IAAA,CAAK,eAAA,EAAiB;gBAE1B,IAAI,OAAO,IAAA,CAAK,IAAA,CAAK,mBAAmB,EAAE,MAAA,KAAW,KAAK,OAAO,kBAAkB,UAAU;oBAC5F,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS;wBACpB,CAAC,aAAa,CAAA,EAAG,IAAA,CAAK,MAAA,CAAO,MAAA;oBAC9B;gBACD;gBACA,IAAI,OAAO,cAAc,YAAY,uPAAC,KAAA,EAAG,6PAAO,MAAG,GAAG;oBACrD,MAAM,kQAAY,KAAA,EAAG,2PAAO,WAAQ,IACjC,MAAM,CAAA,CAAE,cAAA,yPACR,KAAA,EAAG,6PAAO,OAAI,IACd,KAAA,2PAAM,iBAAc,CAAA,CAAE,cAAA,GACtB,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA;oBAC7B,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO,SAAS,CAAA,GAAI;gBACjC;YACD;YAEA,IAAI,OAAO,OAAO,YAAY;gBAC7B,KAAK,GACJ,IAAI,MACH,IAAA,CAAK,MAAA,CAAO,MAAA,EACZ,kQAAI,wBAAA,CAAsB;oBAAE,oBAAoB;oBAAO,aAAa;gBAAM,CAAC;YAG9E;YAEA,IAAI,CAAC,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO;gBACvB,IAAA,CAAK,MAAA,CAAO,KAAA,GAAQ,CAAC,CAAA;YACtB;YAEA,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA,CAAK;gBAAE;gBAAI;gBAAO;gBAAU,OAAO;YAAU,CAAC;YAEhE,IAAI,OAAO,cAAc,UAAU;gBAClC,OAAQ,UAAU;oBACjB,KAAK;wBAAQ;4BACZ,IAAA,CAAK,mBAAA,CAAoB,SAAS,CAAA,GAAI;4BACtC;wBACD;oBACA,KAAK;wBAAS;4BACb,IAAA,CAAK,mBAAA,GAAsB,OAAO,WAAA,CACjC,OAAO,OAAA,CAAQ,IAAA,CAAK,mBAAmB,EAAE,GAAA,CAAI,CAAC,CAAC,GAAG,CAAA,GAAM;oCAAC;oCAAK,KAAK;iCAAC;4BAErE,IAAA,CAAK,mBAAA,CAAoB,SAAS,CAAA,GAAI;4BACtC;wBACD;oBACA,KAAK;wBAAS;4BACb,IAAA,CAAK,mBAAA,CAAoB,SAAS,CAAA,GAAI;4BACtC;wBACD;oBACA,KAAK;wBAAQ;4BACZ,IAAA,CAAK,mBAAA,GAAsB,OAAO,WAAA,CACjC,OAAO,OAAA,CAAQ,IAAA,CAAK,mBAAmB,EAAE,GAAA,CAAI,CAAC,CAAC,GAAG,CAAA,GAAM;oCAAC;oCAAK,KAAK;iCAAC;4BAErE,IAAA,CAAK,mBAAA,CAAoB,SAAS,CAAA,GAAI;4BACtC;wBACD;gBACD;YACD;YAEA,OAAO,IAAA;QACR;IACD;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA6BA,WAAW,IAAA,CAAK,UAAA,CAAW,MAAM,EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA6BjC,YAAY,IAAA,CAAK,UAAA,CAAW,OAAO,EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA6BnC,YAAY,IAAA,CAAK,UAAA,CAAW,OAAO,EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA6BnC,WAAW,IAAA,CAAK,UAAA,CAAW,MAAM,EAAA;IAEzB,kBACP,IAAA,EACA,KAAA,EAUC;QACD,OAAO,CAAC,mBAAmB;YAC1B,MAAM,cAAe,OAAO,mBAAmB,aAC5C,eAAe,kBAAkB,CAAC,IAClC;YAKH,IAAI,sPAAC,eAAA,EAAa,IAAA,CAAK,iBAAA,CAAkB,GAAG,YAAY,iBAAA,CAAkB,CAAC,GAAG;gBAC7E,MAAM,IAAI,MACT;YAEF;YAEA,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa,IAAA,CAAK;gBAAE;gBAAM;gBAAO;YAAY,CAAC;YAC1D,OAAO,IAAA;QACR;IACD;IAAA;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA2BA,QAAQ,IAAA,CAAK,iBAAA,CAAkB,SAAS,KAAK,EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA2B7C,WAAW,IAAA,CAAK,iBAAA,CAAkB,SAAS,IAAI,EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA2B/C,YAAY,IAAA,CAAK,iBAAA,CAAkB,aAAa,KAAK,EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA0CrD,eAAe,IAAA,CAAK,iBAAA,CAAkB,aAAa,IAAI,EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA2BvD,SAAS,IAAA,CAAK,iBAAA,CAAkB,UAAU,KAAK,EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA0C/C,YAAY,IAAA,CAAK,iBAAA,CAAkB,UAAU,IAAI,EAAA;IAAA,cAAA,GAGjD,gBAAgB,YAAA,EAKd;QACD,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa,IAAA,CAAK,GAAG,YAAY;QAC7C,OAAO,IAAA;IACR;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA+BA,MACC,KAAA,EAC2C;QAC3C,IAAI,OAAO,UAAU,YAAY;YAChC,QAAQ,MACP,IAAI,MACH,IAAA,CAAK,MAAA,CAAO,MAAA,EACZ,kQAAI,wBAAA,CAAsB;gBAAE,oBAAoB;gBAAO,aAAa;YAAM,CAAC;QAG9E;QACA,IAAA,CAAK,MAAA,CAAO,KAAA,GAAQ;QACpB,OAAO,IAAA;IACR;IAAA;;;;;;;;;;;;;;;;;;;;;GAAA,GAwBA,OACC,MAAA,EAC4C;QAC5C,IAAI,OAAO,WAAW,YAAY;YACjC,SAAS,OACR,IAAI,MACH,IAAA,CAAK,MAAA,CAAO,MAAA,EACZ,kQAAI,wBAAA,CAAsB;gBAAE,oBAAoB;gBAAO,aAAa;YAAM,CAAC;QAG9E;QACA,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS;QACrB,OAAO,IAAA;IACR;IAyBA,QAAA,GACI,OAAA,EAG0C;QAC7C,IAAI,OAAO,OAAA,CAAQ,CAAC,CAAA,KAAM,YAAY;YACrC,MAAM,UAAU,OAAA,CAAQ,CAAC,CAAA,CACxB,IAAI,MACH,IAAA,CAAK,MAAA,CAAO,MAAA,EACZ,IAAI,sRAAA,CAAsB;gBAAE,oBAAoB;gBAAS,aAAa;YAAM,CAAC;YAG/E,IAAA,CAAK,MAAA,CAAO,OAAA,GAAU,MAAM,OAAA,CAAQ,OAAO,IAAI,UAAU;gBAAC,OAAO;aAAA;QAClE,OAAO;YACN,IAAA,CAAK,MAAA,CAAO,OAAA,GAAU;QACvB;QACA,OAAO,IAAA;IACR;IA8BA,QAAA,GACI,OAAA,EAG0C;QAC7C,IAAI,OAAO,OAAA,CAAQ,CAAC,CAAA,KAAM,YAAY;YACrC,MAAM,UAAU,OAAA,CAAQ,CAAC,CAAA,CACxB,IAAI,MACH,IAAA,CAAK,MAAA,CAAO,MAAA,EACZ,kQAAI,wBAAA,CAAsB;gBAAE,oBAAoB;gBAAS,aAAa;YAAM,CAAC;YAI/E,MAAM,eAAe,MAAM,OAAA,CAAQ,OAAO,IAAI,UAAU;gBAAC,OAAO;aAAA;YAEhE,IAAI,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa,MAAA,GAAS,GAAG;gBACxC,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa,EAAA,CAAG,CAAA,CAAE,EAAG,OAAA,GAAU;YAC5C,OAAO;gBACN,IAAA,CAAK,MAAA,CAAO,OAAA,GAAU;YACvB;QACD,OAAO;YACN,MAAM,eAAe;YAErB,IAAI,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa,MAAA,GAAS,GAAG;gBACxC,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa,EAAA,CAAG,CAAA,CAAE,EAAG,OAAA,GAAU;YAC5C,OAAO;gBACN,IAAA,CAAK,MAAA,CAAO,OAAA,GAAU;YACvB;QACD;QACA,OAAO,IAAA;IACR;IAAA;;;;;;;;;;;;;;;GAAA,GAkBA,MAAM,KAAA,EAAuE;QAC5E,IAAI,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa,MAAA,GAAS,GAAG;YACxC,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa,EAAA,CAAG,CAAA,CAAE,EAAG,KAAA,GAAQ;QAC1C,OAAO;YACN,IAAA,CAAK,MAAA,CAAO,KAAA,GAAQ;QACrB;QACA,OAAO,IAAA;IACR;IAAA;;;;;;;;;;;;;;;GAAA,GAkBA,OAAO,MAAA,EAAyE;QAC/E,IAAI,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa,MAAA,GAAS,GAAG;YACxC,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa,EAAA,CAAG,CAAA,CAAE,EAAG,MAAA,GAAS;QAC3C,OAAO;YACN,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS;QACtB;QACA,OAAO,IAAA;IACR;IAAA;;;;;;;;;GAAA,GAYA,IAAI,QAAA,EAAwB,SAAqB,CAAC,CAAA,EAA2C;QAC5F,IAAA,CAAK,MAAA,CAAO,aAAA,GAAgB;YAAE;YAAU;QAAO;QAC/C,OAAO,IAAA;IACR;IAAA,cAAA,GAGA,SAAc;QACb,OAAO,IAAA,CAAK,OAAA,CAAQ,gBAAA,CAAiB,IAAA,CAAK,MAAM;IACjD;IAEA,QAAe;QACd,MAAM,EAAE,SAAS,QAAA,EAAU,GAAG,KAAK,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,IAAA,CAAK,MAAA,CAAO,CAAC;QAC5E,OAAO;IACR;IAEA,GACC,KAAA,EAC6D;QAC7D,OAAO,IAAI,MACV,wPAAI,WAAA,CAAS,IAAA,CAAK,MAAA,CAAO,GAAG,IAAA,CAAK,MAAA,CAAO,MAAA,EAAQ,KAAK,GACrD,kQAAI,wBAAA,CAAsB;YAAE;YAAO,oBAAoB;YAAS,aAAa;QAAQ,CAAC;IAExF;IAAA,cAAA,GAGS,oBAAiD;QACzD,OAAO,IAAI,MACV,IAAA,CAAK,MAAA,CAAO,MAAA,EACZ,kQAAI,wBAAA,CAAsB;YAAE,OAAO,IAAA,CAAK,SAAA;YAAW,oBAAoB;YAAS,aAAa;QAAQ,CAAC;IAExG;IAEA,WAAkC;QACjC,OAAO,IAAA;IACR;AACD;AA4BO,MAAM,qBAUH,yBAU4C;IACrD,OAAA,mPAAiB,aAAU,CAAA,GAAY,WAAA;IAAA,cAAA,GAGvC,SAAS,IAAA,EAAsC;QAC9C,MAAM,EAAE,OAAA,EAAS,MAAA,EAAQ,OAAA,EAAS,mBAAA,CAAoB,CAAA,GAAI,IAAA;QAC1D,IAAI,CAAC,SAAS;YACb,MAAM,IAAI,MAAM,oFAAoF;QACrG;QACA,0PAAO,SAAA,CAAO,eAAA,CAAgB,wBAAwB,MAAM;YAC3D,MAAM,kQAAa,sBAAA,EAA8B,OAAO,MAAM;YAC9D,MAAM,QAAQ,QAAQ,YAAA,CAEpB,QAAQ,UAAA,CAAW,IAAA,CAAK,MAAA,CAAO,CAAC,GAAG,YAAY,MAAM,IAAI;YAC3D,MAAM,mBAAA,GAAsB;YAC5B,OAAO;QACR,CAAC;IACF;IAAA;;;;;;GAAA,GASA,QAAQ,IAAA,EAAqC;QAC5C,OAAO,IAAA,CAAK,QAAA,CAAS,IAAI;IAC1B;IAEA,UAAkD,CAAC,sBAAsB;QACxE,0PAAO,SAAA,CAAO,eAAA,CAAgB,qBAAqB,MAAM;YACxD,OAAO,IAAA,CAAK,QAAA,CAAS,EAAE,OAAA,CAAQ,iBAAiB;QACjD,CAAC;IACF,EAAA;AACD;qPAEA,cAAA,EAAY,cAAc;gQAAC,eAAY;CAAC;AAExC,SAAS,kBAAkB,IAAA,EAAmB,KAAA,EAAuC;IACpF,OAAO,CAAC,YAAY,aAAA,GAAgB,gBAAgB;QACnD,MAAM,eAAe;YAAC,aAAa;eAAG,WAAW;SAAA,CAAE,GAAA,CAAI,CAAC,SAAA,CAAY;gBACnE;gBACA;gBACA,aAAa;YACd,CAAA,CAAE;QAEF,KAAA,MAAW,eAAe,aAAc;YACvC,IAAI,sPAAC,eAAA,EAAc,WAAmB,iBAAA,CAAkB,GAAG,YAAY,WAAA,CAAY,iBAAA,CAAkB,CAAC,GAAG;gBACxG,MAAM,IAAI,MACT;YAEF;QACD;QAEA,OAAQ,WAA2B,eAAA,CAAgB,YAAY;IAChE;AACD;AAEA,MAAM,oBAAoB,IAAA,CAAO;QAChC;QACA;QACA;QACA;QACA;QACA;IACD,CAAA;AA2BO,MAAM,QAAQ,kBAAkB,SAAS,KAAK;AA2B9C,MAAM,WAAW,kBAAkB,SAAS,IAAI;AA2BhD,MAAM,YAAY,kBAAkB,aAAa,KAAK;AA0CtD,MAAM,eAAe,kBAAkB,aAAa,IAAI;AA2BxD,MAAM,SAAS,kBAAkB,UAAU,KAAK;AA0ChD,MAAM,YAAY,kBAAkB,UAAU,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/query-builders/update.ts"], "sourcesContent": ["import type { GetColumnData } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgTable } from '~/pg-core/table.ts';\nimport type { SelectResultFields } from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport { mapUpdateSet, orderSelectedFields, type UpdateSet } from '~/utils.ts';\nimport type { PgColumn } from '../columns/common.ts';\nimport type { SelectedFields, SelectedFieldsOrdered } from './select.types.ts';\n\nexport interface PgUpdateConfig {\n\twhere?: SQL | undefined;\n\tset: UpdateSet;\n\ttable: PgTable;\n\treturning?: SelectedFieldsOrdered;\n\twithList?: Subquery[];\n}\n\nexport type PgUpdateSetSource<TTable extends PgTable> =\n\t& {\n\t\t[Key in keyof TTable['_']['columns']]?:\n\t\t\t| GetColumnData<TTable['_']['columns'][Key]>\n\t\t\t| SQL;\n\t}\n\t& {};\n\nexport class PgUpdateBuilder<TTable extends PgTable, TQueryResult extends PgQueryResultHKT> {\n\tstatic readonly [entityKind]: string = 'PgUpdateBuilder';\n\n\tdeclare readonly _: {\n\t\treadonly table: TTable;\n\t};\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\tprivate withList?: Subquery[],\n\t) {}\n\n\tset(values: PgUpdateSetSource<TTable>): PgUpdateBase<TTable, TQueryResult> {\n\t\treturn new PgUpdateBase<TTable, TQueryResult>(\n\t\t\tthis.table,\n\t\t\tmapUpdateSet(this.table, values),\n\t\t\tthis.session,\n\t\t\tthis.dialect,\n\t\t\tthis.withList,\n\t\t);\n\t}\n}\n\nexport type PgUpdateWithout<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T : Omit<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['returning'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods'] | K\n\t>,\n\tT['_']['excludedMethods'] | K\n>;\n\nexport type PgUpdateReturningAll<T extends AnyPgUpdate, TDynamic extends boolean> = PgUpdateWithout<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['table']['$inferSelect'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgUpdateReturning<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFields,\n> = PgUpdateWithout<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tSelectResultFields<TSelectedFields>,\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgUpdatePrepare<T extends AnyPgUpdate> = PgPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? PgQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type PgUpdateDynamic<T extends AnyPgUpdate> = PgUpdate<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['returning']\n>;\n\nexport type PgUpdate<\n\tTTable extends PgTable = PgTable,\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n> = PgUpdateBase<TTable, TQueryResult, TReturning, true, never>;\n\ntype AnyPgUpdate = PgUpdateBase<any, any, any, any, any>;\n\nexport interface PgUpdateBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tQueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class PgUpdateBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\t\tSQLWrapper\n{\n\tstatic readonly [entityKind]: string = 'PgUpdate';\n\n\tprivate config: PgUpdateConfig;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tset: UpdateSet,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { set, table, withList };\n\t}\n\n\t/**\n\t * Adds a 'where' clause to the query.\n\t *\n\t * Calling this method will update only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t *\n\t * @param where the 'where' clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be updated.\n\t *\n\t * ```ts\n\t * // Update all cars with green color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'));\n\t * // or\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Update all BMW cars with a green color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Update all cars with the green or blue color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(where: SQL | undefined): PgUpdateWithout<this, TDynamic, 'where'> {\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the updated rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update#update-with-returning}\n\t *\n\t * @example\n\t * ```ts\n\t * // Update all cars with the green color and return all fields\n\t * const updatedCars: Car[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning();\n\t *\n\t * // Update all cars with the green color and return only their id and brand fields\n\t * const updatedCarsIdsAndBrands: { id: number, brand: string }[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning({ id: cars.id, brand: cars.brand });\n\t * ```\n\t */\n\treturning(): PgUpdateReturningAll<this, TDynamic>;\n\treturning<TSelectedFields extends SelectedFields>(\n\t\tfields: TSelectedFields,\n\t): PgUpdateReturning<this, TDynamic, TSelectedFields>;\n\treturning(\n\t\tfields: SelectedFields = this.config.table[Table.Symbol.Columns],\n\t): PgUpdateWithout<AnyPgUpdate, TDynamic, 'returning'> {\n\t\tthis.config.returning = orderSelectedFields<PgColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildUpdateQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgUpdatePrepare<this> {\n\t\treturn this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true);\n\t}\n\n\tprepare(name: string): PgUpdatePrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn this._prepare().execute(placeholderValues);\n\t};\n\n\t$dynamic(): PgUpdateDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,kBAAkB;AAW3B,SAAS,oBAAoB;AAI7B,SAAS,aAAa;AACtB,SAAS,cAAc,2BAA2C;;;;;AAoB3D,MAAM,gBAA+E;IAO3F,YACS,KAAA,EACA,OAAA,EACA,OAAA,EACA,QAAA,CACP;QAJO,IAAA,CAAA,KAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;IACN;IAXH,OAAA,mPAAiB,aAAU,CAAA,GAAY,kBAAA;IAavC,IAAI,MAAA,EAAuE;QAC1E,OAAO,IAAI,aACV,IAAA,CAAK,KAAA,uPACL,eAAA,EAAa,IAAA,CAAK,KAAA,EAAO,MAAM,GAC/B,IAAA,CAAK,OAAA,EACL,IAAA,CAAK,OAAA,EACL,IAAA,CAAK,QAAA;IAEP;AACD;AAwFO,MAAM,gRAQH,gBAAA,CAIV;IAKC,YACC,KAAA,EACA,GAAA,EACQ,OAAA,EACA,OAAA,EACR,QAAA,CACC;QACD,KAAA,CAAM;QAJE,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QAIR,IAAA,CAAK,MAAA,GAAS;YAAE;YAAK;YAAO;QAAS;IACtC;IAbA,OAAA,kPAAiB,cAAU,CAAA,GAAY,WAAA;IAE/B,OAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA8CR,MAAM,KAAA,EAAkE;QACvE,IAAA,CAAK,MAAA,CAAO,KAAA,GAAQ;QACpB,OAAO,IAAA;IACR;IA4BA,UACC,SAAyB,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,yPAAA,CAAM,MAAA,CAAO,OAAO,CAAA,EACT;QACtD,IAAA,CAAK,MAAA,CAAO,SAAA,wPAAY,sBAAA,EAA8B,MAAM;QAC5D,OAAO,IAAA;IACR;IAAA,cAAA,GAGA,SAAc;QACb,OAAO,IAAA,CAAK,OAAA,CAAQ,gBAAA,CAAiB,IAAA,CAAK,MAAM;IACjD;IAEA,QAAe;QACd,MAAM,EAAE,SAAS,QAAA,EAAU,GAAG,KAAK,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,IAAA,CAAK,MAAA,CAAO,CAAC;QAC5E,OAAO;IACR;IAAA,cAAA,GAGA,SAAS,IAAA,EAAsC;QAC9C,OAAO,IAAA,CAAK,OAAA,CAAQ,YAAA,CAAa,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,IAAA,CAAK,MAAA,CAAO,CAAC,GAAG,IAAA,CAAK,MAAA,CAAO,SAAA,EAAW,MAAM,IAAI;IAC3G;IAEA,QAAQ,IAAA,EAAqC;QAC5C,OAAO,IAAA,CAAK,QAAA,CAAS,IAAI;IAC1B;IAES,UAAkD,CAAC,sBAAsB;QACjF,OAAO,IAAA,CAAK,QAAA,CAAS,EAAE,OAAA,CAAQ,iBAAiB;IACjD,EAAA;IAEA,WAAkC;QACjC,OAAO,IAAA;IACR;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3973, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/errors.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\n\nexport class DrizzleError extends Error {\n\tstatic readonly [entityKind]: string = 'DrizzleError';\n\n\tconstructor({ message, cause }: { message?: string; cause?: unknown }) {\n\t\tsuper(message);\n\t\tthis.name = 'DrizzleError';\n\t\tthis.cause = cause;\n\t}\n}\n\nexport class TransactionRollbackError extends DrizzleError {\n\tstatic readonly [entityKind]: string = 'TransactionRollbackError';\n\n\tconstructor() {\n\t\tsuper({ message: 'Rollback' });\n\t}\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,kBAAkB;;AAEpB,MAAM,qBAAqB,MAAM;IACvC,OAAA,mPAAiB,aAAU,CAAA,GAAY,eAAA;IAEvC,YAAY,EAAE,OAAA,EAAS,KAAA,CAAM,CAAA,CAA0C;QACtE,KAAA,CAAM,OAAO;QACb,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,KAAA,GAAQ;IACd;AACD;AAEO,MAAM,iCAAiC,aAAa;IAC1D,OAAA,mPAAiB,aAAU,CAAA,GAAY,2BAAA;IAEvC,aAAc;QACb,KAAA,CAAM;YAAE,SAAS;QAAW,CAAC;IAC9B;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4003, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/columns/date.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn } from './common.ts';\nimport { PgDateColumnBaseBuilder } from './date.common.ts';\n\nexport type PgDateBuilderInitial<TName extends string> = PgDateBuilder<{\n\tname: TName;\n\tdataType: 'date';\n\tcolumnType: 'PgDate';\n\tdata: Date;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgDateBuilder<T extends ColumnBuilderBaseConfig<'date', 'PgDate'>> extends PgDateColumnBaseBuilder<T> {\n\tstatic readonly [entityKind]: string = 'PgDateBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'date', 'PgDate');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgDate<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgDate<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgDate<T extends ColumnBaseConfig<'date', 'PgDate'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgDate';\n\n\tgetSQLType(): string {\n\t\treturn 'date';\n\t}\n\n\toverride mapFromDriverValue(value: string): Date {\n\t\treturn new Date(value);\n\t}\n\n\toverride mapToDriverValue(value: Date): string {\n\t\treturn value.toISOString();\n\t}\n}\n\nexport type PgDateStringBuilderInitial<TName extends string> = PgDateStringBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgDateString';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgDateStringBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgDateString'>>\n\textends PgDateColumnBaseBuilder<T>\n{\n\tstatic readonly [entityKind]: string = 'PgDateStringBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'PgDateString');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgDateString<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgDateString<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgDateString<T extends ColumnBaseConfig<'string', 'PgDateString'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgDateString';\n\n\tgetSQLType(): string {\n\t\treturn 'date';\n\t}\n}\n\nexport function date<TName extends string>(\n\tname: TName,\n\tconfig?: { mode: 'string' },\n): PgDateStringBuilderInitial<TName>;\nexport function date<TName extends string>(TName: TName, config?: { mode: 'date' }): PgDateBuilderInitial<TName>;\nexport function date<TName extends string>(name: TName, config?: { mode: 'date' | 'string' }) {\n\tif (config?.mode === 'date') {\n\t\treturn new PgDateBuilder(name);\n\t}\n\treturn new PgDateStringBuilder(name);\n}\n"], "names": [], "mappings": ";;;;;;;AAEA,SAAS,kBAAkB;AAE3B,SAAS,gBAAgB;AACzB,SAAS,+BAA+B;;;;AAWjC,MAAM,ySAA2E,0BAAA,CAA2B;IAClH,OAAA,mPAAiB,aAAU,CAAA,GAAY,gBAAA;IAEvC,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,QAAQ,QAAQ;IAC7B;IAAA,cAAA,GAGS,MACR,KAAA,EAC0C;QAC1C,OAAO,IAAI,OAAwC,OAAO,IAAA,CAAK,MAA8C;IAC9G;AACD;AAEO,MAAM,0RAA6D,WAAA,CAAY;IACrF,OAAA,mPAAiB,aAAU,CAAA,GAAY,SAAA;IAEvC,aAAqB;QACpB,OAAO;IACR;IAES,mBAAmB,KAAA,EAAqB;QAChD,OAAO,IAAI,KAAK,KAAK;IACtB;IAES,iBAAiB,KAAA,EAAqB;QAC9C,OAAO,MAAM,WAAA,CAAY;IAC1B;AACD;AAWO,MAAM,+SACJ,0BAAA,CACT;IACC,OAAA,mPAAiB,aAAU,CAAA,GAAY,sBAAA;IAEvC,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,UAAU,cAAc;IACrC;IAAA,cAAA,GAGS,MACR,KAAA,EACgD;QAChD,OAAO,IAAI,aACV,OACA,IAAA,CAAK,MAAA;IAEP;AACD;AAEO,MAAM,gSAA2E,WAAA,CAAY;IACnG,OAAA,mPAAiB,aAAU,CAAA,GAAY,eAAA;IAEvC,aAAqB;QACpB,OAAO;IACR;AACD;AAOO,SAAS,KAA2B,IAAA,EAAa,MAAA,EAAsC;IAC7F,IAAI,QAAQ,SAAS,QAAQ;QAC5B,OAAO,IAAI,cAAc,IAAI;IAC9B;IACA,OAAO,IAAI,oBAAoB,IAAI;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4066, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/columns/jsonb.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgJsonbBuilderInitial<TName extends string> = PgJsonbBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'PgJsonb';\n\tdata: unknown;\n\tdriverParam: unknown;\n\tenumValues: undefined;\n}>;\n\nexport class PgJsonbBuilder<T extends ColumnBuilderBaseConfig<'json', 'PgJsonb'>> extends PgColumnBuilder<T> {\n\tstatic readonly [entityKind]: string = 'PgJsonbBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'PgJsonb');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgJsonb<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgJsonb<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgJsonb<T extends ColumnBaseConfig<'json', 'PgJsonb'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgJsonb';\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgJsonbBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'jsonb';\n\t}\n\n\toverride mapToDriverValue(value: T['data']): string {\n\t\treturn JSON.stringify(value);\n\t}\n\n\toverride mapFromDriverValue(value: T['data'] | string): T['data'] {\n\t\tif (typeof value === 'string') {\n\t\t\ttry {\n\t\t\t\treturn JSON.parse(value);\n\t\t\t} catch {\n\t\t\t\treturn value as T['data'];\n\t\t\t}\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function jsonb<TName extends string>(name: TName): PgJsonbBuilderInitial<TName> {\n\treturn new PgJsonbBuilder(name);\n}\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,kBAAkB;AAE3B,SAAS,UAAU,uBAAuB;;;AAWnC,MAAM,kSAA6E,kBAAA,CAAmB;IAC5G,OAAA,mPAAiB,aAAU,CAAA,GAAY,iBAAA;IAEvC,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,QAAQ,SAAS;IAC9B;IAAA,cAAA,GAGS,MACR,KAAA,EAC2C;QAC3C,OAAO,IAAI,QAAyC,OAAO,IAAA,CAAK,MAA8C;IAC/G;AACD;AAEO,MAAM,2RAA+D,WAAA,CAAY;IACvF,OAAA,mPAAiB,aAAU,CAAA,GAAY,UAAA;IAEvC,YAAY,KAAA,EAA6C,MAAA,CAAqC;QAC7F,KAAA,CAAM,OAAO,MAAM;IACpB;IAEA,aAAqB;QACpB,OAAO;IACR;IAES,iBAAiB,KAAA,EAA0B;QACnD,OAAO,KAAK,SAAA,CAAU,KAAK;IAC5B;IAES,mBAAmB,KAAA,EAAsC;QACjE,IAAI,OAAO,UAAU,UAAU;YAC9B,IAAI;gBACH,OAAO,KAAK,KAAA,CAAM,KAAK;YACxB,EAAA,OAAQ;gBACP,OAAO;YACR;QACD;QACA,OAAO;IACR;AACD;AAEO,SAAS,MAA4B,IAAA,EAA2C;IACtF,OAAO,IAAI,eAAe,IAAI;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/columns/numeric.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgNumericBuilderInitial<TName extends string> = PgNumericBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgNumeric';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgNumericBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgNumeric'>> extends PgColumnBuilder<\n\tT,\n\t{\n\t\tprecision: number | undefined;\n\t\tscale: number | undefined;\n\t}\n> {\n\tstatic readonly [entityKind]: string = 'PgNumericBuilder';\n\n\tconstructor(name: string, precision?: number, scale?: number) {\n\t\tsuper(name, 'string', 'PgNumeric');\n\t\tthis.config.precision = precision;\n\t\tthis.config.scale = scale;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgNumeric<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgNumeric<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgNumeric<T extends ColumnBaseConfig<'string', 'PgNumeric'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgNumeric';\n\n\treadonly precision: number | undefined;\n\treadonly scale: number | undefined;\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgNumericBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t\tthis.precision = config.precision;\n\t\tthis.scale = config.scale;\n\t}\n\n\tgetSQLType(): string {\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\treturn `numeric(${this.precision}, ${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\treturn 'numeric';\n\t\t} else {\n\t\t\treturn `numeric(${this.precision})`;\n\t\t}\n\t}\n}\n\nexport function numeric<TName extends string>(\n\tname: TName,\n\tconfig?:\n\t\t| { precision: number; scale?: number }\n\t\t| { precision?: number; scale: number }\n\t\t| { precision: number; scale: number },\n): PgNumericBuilderInitial<TName> {\n\treturn new PgNumericBuilder(name, config?.precision, config?.scale);\n}\n\nexport const decimal = numeric;\n"], "names": [], "mappings": ";;;;;;AAEA,SAAS,kBAAkB;AAE3B,SAAS,UAAU,uBAAuB;;;AAWnC,MAAM,oSAAmF,kBAAA,CAM9F;IACD,OAAA,mPAAiB,aAAU,CAAA,GAAY,mBAAA;IAEvC,YAAY,IAAA,EAAc,SAAA,EAAoB,KAAA,CAAgB;QAC7D,KAAA,CAAM,MAAM,UAAU,WAAW;QACjC,IAAA,CAAK,MAAA,CAAO,SAAA,GAAY;QACxB,IAAA,CAAK,MAAA,CAAO,KAAA,GAAQ;IACrB;IAAA,cAAA,GAGS,MACR,KAAA,EAC6C;QAC7C,OAAO,IAAI,UAA2C,OAAO,IAAA,CAAK,MAA8C;IACjH;AACD;AAEO,MAAM,6RAAqE,WAAA,CAAY;IAC7F,OAAA,mPAAiB,aAAU,CAAA,GAAY,YAAA;IAE9B,UAAA;IACA,MAAA;IAET,YAAY,KAAA,EAA6C,MAAA,CAAuC;QAC/F,KAAA,CAAM,OAAO,MAAM;QACnB,IAAA,CAAK,SAAA,GAAY,OAAO,SAAA;QACxB,IAAA,CAAK,KAAA,GAAQ,OAAO,KAAA;IACrB;IAEA,aAAqB;QACpB,IAAI,IAAA,CAAK,SAAA,KAAc,KAAA,KAAa,IAAA,CAAK,KAAA,KAAU,KAAA,GAAW;YAC7D,OAAO,CAAA,QAAA,EAAW,IAAA,CAAK,SAAS,CAAA,EAAA,EAAK,IAAA,CAAK,KAAK,CAAA,CAAA,CAAA;QAChD,OAAA,IAAW,IAAA,CAAK,SAAA,KAAc,KAAA,GAAW;YACxC,OAAO;QACR,OAAO;YACN,OAAO,CAAA,QAAA,EAAW,IAAA,CAAK,SAAS,CAAA,CAAA,CAAA;QACjC;IACD;AACD;AAEO,SAAS,QACf,IAAA,EACA,MAAA,EAIiC;IACjC,OAAO,IAAI,iBAAiB,MAAM,QAAQ,WAAW,QAAQ,KAAK;AACnE;AAEO,MAAM,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/columns/time.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn } from './common.ts';\nimport { PgDateColumnBaseBuilder } from './date.common.ts';\nimport type { Precision } from './timestamp.ts';\n\nexport type PgTimeBuilderInitial<TName extends string> = PgTimeBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgTime';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgTimeBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgTime'>> extends PgDateColumnBaseBuilder<\n\tT,\n\t{ withTimezone: boolean; precision: number | undefined }\n> {\n\tstatic readonly [entityKind]: string = 'PgTimeBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t\treadonly withTimezone: boolean,\n\t\treadonly precision: number | undefined,\n\t) {\n\t\tsuper(name, 'string', 'PgTime');\n\t\tthis.config.withTimezone = withTimezone;\n\t\tthis.config.precision = precision;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgTime<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgTime<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgTime<T extends ColumnBaseConfig<'string', 'PgTime'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgTime';\n\n\treadonly withTimezone: boolean;\n\treadonly precision: number | undefined;\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgTimeBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t\tthis.withTimezone = config.withTimezone;\n\t\tthis.precision = config.precision;\n\t}\n\n\tgetSQLType(): string {\n\t\tconst precision = this.precision === undefined ? '' : `(${this.precision})`;\n\t\treturn `time${precision}${this.withTimezone ? ' with time zone' : ''}`;\n\t}\n}\n\nexport interface TimeConfig {\n\tprecision?: Precision;\n\twithTimezone?: boolean;\n}\n\nexport function time<TName extends string>(name: TName, config: TimeConfig = {}): PgTimeBuilderInitial<TName> {\n\treturn new PgTimeBuilder(name, config.withTimezone ?? false, config.precision);\n}\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,kBAAkB;AAE3B,SAAS,gBAAgB;AACzB,SAAS,+BAA+B;;;;AAYjC,MAAM,ySAA6E,0BAAA,CAGxF;IAGD,YACC,IAAA,EACS,YAAA,EACA,SAAA,CACR;QACD,KAAA,CAAM,MAAM,UAAU,QAAQ;QAHrB,IAAA,CAAA,YAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QAGT,IAAA,CAAK,MAAA,CAAO,YAAA,GAAe;QAC3B,IAAA,CAAK,MAAA,CAAO,SAAA,GAAY;IACzB;IAVA,OAAA,mPAAiB,aAAU,CAAA,GAAY,gBAAA;IAAA,cAAA,GAa9B,MACR,KAAA,EAC0C;QAC1C,OAAO,IAAI,OAAwC,OAAO,IAAA,CAAK,MAA8C;IAC9G;AACD;AAEO,MAAM,0RAA+D,WAAA,CAAY;IACvF,OAAA,mPAAiB,aAAU,CAAA,GAAY,SAAA;IAE9B,aAAA;IACA,UAAA;IAET,YAAY,KAAA,EAA6C,MAAA,CAAoC;QAC5F,KAAA,CAAM,OAAO,MAAM;QACnB,IAAA,CAAK,YAAA,GAAe,OAAO,YAAA;QAC3B,IAAA,CAAK,SAAA,GAAY,OAAO,SAAA;IACzB;IAEA,aAAqB;QACpB,MAAM,YAAY,IAAA,CAAK,SAAA,KAAc,KAAA,IAAY,KAAK,CAAA,CAAA,EAAI,IAAA,CAAK,SAAS,CAAA,CAAA,CAAA;QACxE,OAAO,CAAA,IAAA,EAAO,SAAS,GAAG,IAAA,CAAK,YAAA,GAAe,oBAAoB,EAAE,EAAA;IACrE;AACD;AAOO,SAAS,KAA2B,IAAA,EAAa,SAAqB,CAAC,CAAA,EAAgC;IAC7G,OAAO,IAAI,cAAc,MAAM,OAAO,YAAA,IAAgB,OAAO,OAAO,SAAS;AAC9E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/dialect.ts"], "sourcesContent": ["import { aliasedTable, aliasedTableColumn, mapColumnsInAliasedSQLToAlias, mapColumnsInSQLToAlias } from '~/alias.ts';\nimport { Column } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport { DrizzleError } from '~/errors.ts';\nimport type { MigrationConfig, MigrationMeta } from '~/migrator.ts';\nimport {\n\tPgColumn,\n\tPgDate,\n\tPgDateString,\n\tPgJson,\n\tPgJsonb,\n\tPgNumeric,\n\tPgTime,\n\tPgTimestamp,\n\tPgTimestampString,\n\tPgUUID,\n} from '~/pg-core/columns/index.ts';\nimport type {\n\tPgDeleteConfig,\n\tPgInsertConfig,\n\tPgSelectJoinConfig,\n\tPgUpdateConfig,\n} from '~/pg-core/query-builders/index.ts';\nimport type { PgSelectConfig, SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport { PgTable } from '~/pg-core/table.ts';\nimport {\n\ttype BuildRelationalQueryResult,\n\ttype DBQueryConfig,\n\tgetOperators,\n\tgetOrderByOperators,\n\tMany,\n\tnormalizeRelation,\n\tOne,\n\ttype Relation,\n\ttype TableRelationalConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { and, eq, View } from '~/sql/index.ts';\nimport {\n\ttype DriverValueEncoder,\n\ttype Name,\n\tParam,\n\ttype QueryTypingsValue,\n\ttype QueryWithTypings,\n\tSQL,\n\tsql,\n\ttype SQLChunk,\n} from '~/sql/sql.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { getTableName, getTableUniqueName, Table } from '~/table.ts';\nimport { orderSelectedFields, type UpdateSet } from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type { PgSession } from './session.ts';\nimport { PgViewBase } from './view-base.ts';\nimport type { PgMaterializedView } from './view.ts';\n\nexport class PgDialect {\n\tstatic readonly [entityKind]: string = 'PgDialect';\n\n\tasync migrate(migrations: MigrationMeta[], session: PgSession, config: string | MigrationConfig): Promise<void> {\n\t\tconst migrationsTable = typeof config === 'string'\n\t\t\t? '__drizzle_migrations'\n\t\t\t: config.migrationsTable ?? '__drizzle_migrations';\n\t\tconst migrationsSchema = typeof config === 'string' ? 'drizzle' : config.migrationsSchema ?? 'drizzle';\n\t\tconst migrationTableCreate = sql`\n\t\t\tCREATE TABLE IF NOT EXISTS ${sql.identifier(migrationsSchema)}.${sql.identifier(migrationsTable)} (\n\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\thash text NOT NULL,\n\t\t\t\tcreated_at bigint\n\t\t\t)\n\t\t`;\n\t\tawait session.execute(sql`CREATE SCHEMA IF NOT EXISTS ${sql.identifier(migrationsSchema)}`);\n\t\tawait session.execute(migrationTableCreate);\n\n\t\tconst dbMigrations = await session.all<{ id: number; hash: string; created_at: string }>(\n\t\t\tsql`select id, hash, created_at from ${sql.identifier(migrationsSchema)}.${\n\t\t\t\tsql.identifier(migrationsTable)\n\t\t\t} order by created_at desc limit 1`,\n\t\t);\n\n\t\tconst lastDbMigration = dbMigrations[0];\n\t\tawait session.transaction(async (tx) => {\n\t\t\tfor await (const migration of migrations) {\n\t\t\t\tif (\n\t\t\t\t\t!lastDbMigration\n\t\t\t\t\t|| Number(lastDbMigration.created_at) < migration.folderMillis\n\t\t\t\t) {\n\t\t\t\t\tfor (const stmt of migration.sql) {\n\t\t\t\t\t\tawait tx.execute(sql.raw(stmt));\n\t\t\t\t\t}\n\t\t\t\t\tawait tx.execute(\n\t\t\t\t\t\tsql`insert into ${sql.identifier(migrationsSchema)}.${\n\t\t\t\t\t\t\tsql.identifier(migrationsTable)\n\t\t\t\t\t\t} (\"hash\", \"created_at\") values(${migration.hash}, ${migration.folderMillis})`,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n\n\tescapeName(name: string): string {\n\t\treturn `\"${name}\"`;\n\t}\n\n\tescapeParam(num: number): string {\n\t\treturn `$${num + 1}`;\n\t}\n\n\tescapeString(str: string): string {\n\t\treturn `'${str.replace(/'/g, \"''\")}'`;\n\t}\n\n\tprivate buildWithCTE(queries: Subquery[] | undefined): SQL | undefined {\n\t\tif (!queries?.length) return undefined;\n\n\t\tconst withSqlChunks = [sql`with `];\n\t\tfor (const [i, w] of queries.entries()) {\n\t\t\twithSqlChunks.push(sql`${sql.identifier(w._.alias)} as (${w._.sql})`);\n\t\t\tif (i < queries.length - 1) {\n\t\t\t\twithSqlChunks.push(sql`, `);\n\t\t\t}\n\t\t}\n\t\twithSqlChunks.push(sql` `);\n\t\treturn sql.join(withSqlChunks);\n\t}\n\n\tbuildDeleteQuery({ table, where, returning, withList }: PgDeleteConfig): SQL {\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}`\n\t\t\t: undefined;\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\treturn sql`${withSql}delete from ${table}${whereSql}${returningSql}`;\n\t}\n\n\tbuildUpdateSet(table: PgTable, set: UpdateSet): SQL {\n\t\tconst tableColumns = table[Table.Symbol.Columns];\n\n\t\tconst columnNames = Object.keys(tableColumns).filter((colName) =>\n\t\t\tset[colName] !== undefined || tableColumns[colName]?.onUpdateFn !== undefined\n\t\t);\n\n\t\tconst setSize = columnNames.length;\n\t\treturn sql.join(columnNames.flatMap((colName, i) => {\n\t\t\tconst col = tableColumns[colName]!;\n\n\t\t\tconst value = set[colName] ?? sql.param(col.onUpdateFn!(), col);\n\t\t\tconst res = sql`${sql.identifier(col.name)} = ${value}`;\n\n\t\t\tif (i < setSize - 1) {\n\t\t\t\treturn [res, sql.raw(', ')];\n\t\t\t}\n\t\t\treturn [res];\n\t\t}));\n\t}\n\n\tbuildUpdateQuery({ table, set, where, returning, withList }: PgUpdateConfig): SQL {\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst setSql = this.buildUpdateSet(table, set);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}`\n\t\t\t: undefined;\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\treturn sql`${withSql}update ${table} set ${setSql}${whereSql}${returningSql}`;\n\t}\n\n\t/**\n\t * Builds selection SQL with provided fields/expressions\n\t *\n\t * Examples:\n\t *\n\t * `select <selection> from`\n\t *\n\t * `insert ... returning <selection>`\n\t *\n\t * If `isSingleTable` is true, then columns won't be prefixed with table name\n\t */\n\tprivate buildSelection(\n\t\tfields: SelectedFieldsOrdered,\n\t\t{ isSingleTable = false }: { isSingleTable?: boolean } = {},\n\t): SQL {\n\t\tconst columnsLen = fields.length;\n\n\t\tconst chunks = fields\n\t\t\t.flatMap(({ field }, i) => {\n\t\t\t\tconst chunk: SQLChunk[] = [];\n\n\t\t\t\tif (is(field, SQL.Aliased) && field.isSelectionField) {\n\t\t\t\t\tchunk.push(sql.identifier(field.fieldAlias));\n\t\t\t\t} else if (is(field, SQL.Aliased) || is(field, SQL)) {\n\t\t\t\t\tconst query = is(field, SQL.Aliased) ? field.sql : field;\n\n\t\t\t\t\tif (isSingleTable) {\n\t\t\t\t\t\tchunk.push(\n\t\t\t\t\t\t\tnew SQL(\n\t\t\t\t\t\t\t\tquery.queryChunks.map((c) => {\n\t\t\t\t\t\t\t\t\tif (is(c, PgColumn)) {\n\t\t\t\t\t\t\t\t\t\treturn sql.identifier(c.name);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\treturn c;\n\t\t\t\t\t\t\t\t}),\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tchunk.push(query);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (is(field, SQL.Aliased)) {\n\t\t\t\t\t\tchunk.push(sql` as ${sql.identifier(field.fieldAlias)}`);\n\t\t\t\t\t}\n\t\t\t\t} else if (is(field, Column)) {\n\t\t\t\t\tif (isSingleTable) {\n\t\t\t\t\t\tchunk.push(sql.identifier(field.name));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tchunk.push(field);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (i < columnsLen - 1) {\n\t\t\t\t\tchunk.push(sql`, `);\n\t\t\t\t}\n\n\t\t\t\treturn chunk;\n\t\t\t});\n\n\t\treturn sql.join(chunks);\n\t}\n\n\tbuildSelectQuery(\n\t\t{\n\t\t\twithList,\n\t\t\tfields,\n\t\t\tfieldsFlat,\n\t\t\twhere,\n\t\t\thaving,\n\t\t\ttable,\n\t\t\tjoins,\n\t\t\torderBy,\n\t\t\tgroupBy,\n\t\t\tlimit,\n\t\t\toffset,\n\t\t\tlockingClause,\n\t\t\tdistinct,\n\t\t\tsetOperators,\n\t\t}: PgSelectConfig,\n\t): SQL {\n\t\tconst fieldsList = fieldsFlat ?? orderSelectedFields<PgColumn>(fields);\n\t\tfor (const f of fieldsList) {\n\t\t\tif (\n\t\t\t\tis(f.field, Column)\n\t\t\t\t&& getTableName(f.field.table)\n\t\t\t\t\t!== (is(table, Subquery)\n\t\t\t\t\t\t? table._.alias\n\t\t\t\t\t\t: is(table, PgViewBase)\n\t\t\t\t\t\t? table[ViewBaseConfig].name\n\t\t\t\t\t\t: is(table, SQL)\n\t\t\t\t\t\t? undefined\n\t\t\t\t\t\t: getTableName(table))\n\t\t\t\t&& !((table) =>\n\t\t\t\t\tjoins?.some(({ alias }) =>\n\t\t\t\t\t\talias === (table[Table.Symbol.IsAlias] ? getTableName(table) : table[Table.Symbol.BaseName])\n\t\t\t\t\t))(f.field.table)\n\t\t\t) {\n\t\t\t\tconst tableName = getTableName(f.field.table);\n\t\t\t\tthrow new Error(\n\t\t\t\t\t`Your \"${\n\t\t\t\t\t\tf.path.join('->')\n\t\t\t\t\t}\" field references a column \"${tableName}\".\"${f.field.name}\", but the table \"${tableName}\" is not part of the query! Did you forget to join it?`,\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tconst isSingleTable = !joins || joins.length === 0;\n\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tlet distinctSql: SQL | undefined;\n\t\tif (distinct) {\n\t\t\tdistinctSql = distinct === true ? sql` distinct` : sql` distinct on (${sql.join(distinct.on, sql`, `)})`;\n\t\t}\n\n\t\tconst selection = this.buildSelection(fieldsList, { isSingleTable });\n\n\t\tconst tableSql = (() => {\n\t\t\tif (is(table, Table) && table[Table.Symbol.OriginalName] !== table[Table.Symbol.Name]) {\n\t\t\t\tlet fullName = sql`${sql.identifier(table[Table.Symbol.OriginalName])}`;\n\t\t\t\tif (table[Table.Symbol.Schema]) {\n\t\t\t\t\tfullName = sql`${sql.identifier(table[Table.Symbol.Schema]!)}.${fullName}`;\n\t\t\t\t}\n\t\t\t\treturn sql`${fullName} ${sql.identifier(table[Table.Symbol.Name])}`;\n\t\t\t}\n\n\t\t\treturn table;\n\t\t})();\n\n\t\tconst joinsArray: SQL[] = [];\n\n\t\tif (joins) {\n\t\t\tfor (const [index, joinMeta] of joins.entries()) {\n\t\t\t\tif (index === 0) {\n\t\t\t\t\tjoinsArray.push(sql` `);\n\t\t\t\t}\n\t\t\t\tconst table = joinMeta.table;\n\t\t\t\tconst lateralSql = joinMeta.lateral ? sql` lateral` : undefined;\n\n\t\t\t\tif (is(table, PgTable)) {\n\t\t\t\t\tconst tableName = table[PgTable.Symbol.Name];\n\t\t\t\t\tconst tableSchema = table[PgTable.Symbol.Schema];\n\t\t\t\t\tconst origTableName = table[PgTable.Symbol.OriginalName];\n\t\t\t\t\tconst alias = tableName === origTableName ? undefined : joinMeta.alias;\n\t\t\t\t\tjoinsArray.push(\n\t\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${\n\t\t\t\t\t\t\ttableSchema ? sql`${sql.identifier(tableSchema)}.` : undefined\n\t\t\t\t\t\t}${sql.identifier(origTableName)}${alias && sql` ${sql.identifier(alias)}`} on ${joinMeta.on}`,\n\t\t\t\t\t);\n\t\t\t\t} else if (is(table, View)) {\n\t\t\t\t\tconst viewName = table[ViewBaseConfig].name;\n\t\t\t\t\tconst viewSchema = table[ViewBaseConfig].schema;\n\t\t\t\t\tconst origViewName = table[ViewBaseConfig].originalName;\n\t\t\t\t\tconst alias = viewName === origViewName ? undefined : joinMeta.alias;\n\t\t\t\t\tjoinsArray.push(\n\t\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${\n\t\t\t\t\t\t\tviewSchema ? sql`${sql.identifier(viewSchema)}.` : undefined\n\t\t\t\t\t\t}${sql.identifier(origViewName)}${alias && sql` ${sql.identifier(alias)}`} on ${joinMeta.on}`,\n\t\t\t\t\t);\n\t\t\t\t} else {\n\t\t\t\t\tjoinsArray.push(\n\t\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${table} on ${joinMeta.on}`,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\tif (index < joins.length - 1) {\n\t\t\t\t\tjoinsArray.push(sql` `);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tconst joinsSql = sql.join(joinsArray);\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\tconst havingSql = having ? sql` having ${having}` : undefined;\n\n\t\tlet orderBySql;\n\t\tif (orderBy && orderBy.length > 0) {\n\t\t\torderBySql = sql` order by ${sql.join(orderBy, sql`, `)}`;\n\t\t}\n\n\t\tlet groupBySql;\n\t\tif (groupBy && groupBy.length > 0) {\n\t\t\tgroupBySql = sql` group by ${sql.join(groupBy, sql`, `)}`;\n\t\t}\n\n\t\tconst limitSql = limit ? sql` limit ${limit}` : undefined;\n\n\t\tconst offsetSql = offset ? sql` offset ${offset}` : undefined;\n\n\t\tconst lockingClauseSql = sql.empty();\n\t\tif (lockingClause) {\n\t\t\tconst clauseSql = sql` for ${sql.raw(lockingClause.strength)}`;\n\t\t\tif (lockingClause.config.of) {\n\t\t\t\tclauseSql.append(\n\t\t\t\t\tsql` of ${\n\t\t\t\t\t\tsql.join(\n\t\t\t\t\t\t\tArray.isArray(lockingClause.config.of) ? lockingClause.config.of : [lockingClause.config.of],\n\t\t\t\t\t\t\tsql`, `,\n\t\t\t\t\t\t)\n\t\t\t\t\t}`,\n\t\t\t\t);\n\t\t\t}\n\t\t\tif (lockingClause.config.noWait) {\n\t\t\t\tclauseSql.append(sql` no wait`);\n\t\t\t} else if (lockingClause.config.skipLocked) {\n\t\t\t\tclauseSql.append(sql` skip locked`);\n\t\t\t}\n\t\t\tlockingClauseSql.append(clauseSql);\n\t\t}\n\t\tconst finalQuery =\n\t\t\tsql`${withSql}select${distinctSql} ${selection} from ${tableSql}${joinsSql}${whereSql}${groupBySql}${havingSql}${orderBySql}${limitSql}${offsetSql}${lockingClauseSql}`;\n\n\t\tif (setOperators.length > 0) {\n\t\t\treturn this.buildSetOperations(finalQuery, setOperators);\n\t\t}\n\n\t\treturn finalQuery;\n\t}\n\n\tbuildSetOperations(leftSelect: SQL, setOperators: PgSelectConfig['setOperators']): SQL {\n\t\tconst [setOperator, ...rest] = setOperators;\n\n\t\tif (!setOperator) {\n\t\t\tthrow new Error('Cannot pass undefined values to any set operator');\n\t\t}\n\n\t\tif (rest.length === 0) {\n\t\t\treturn this.buildSetOperationQuery({ leftSelect, setOperator });\n\t\t}\n\n\t\t// Some recursive magic here\n\t\treturn this.buildSetOperations(\n\t\t\tthis.buildSetOperationQuery({ leftSelect, setOperator }),\n\t\t\trest,\n\t\t);\n\t}\n\n\tbuildSetOperationQuery({\n\t\tleftSelect,\n\t\tsetOperator: { type, isAll, rightSelect, limit, orderBy, offset },\n\t}: { leftSelect: SQL; setOperator: PgSelectConfig['setOperators'][number] }): SQL {\n\t\tconst leftChunk = sql`(${leftSelect.getSQL()}) `;\n\t\tconst rightChunk = sql`(${rightSelect.getSQL()})`;\n\n\t\tlet orderBySql;\n\t\tif (orderBy && orderBy.length > 0) {\n\t\t\tconst orderByValues: (SQL<unknown> | Name)[] = [];\n\n\t\t\t// The next bit is necessary because the sql operator replaces ${table.column} with `table`.`column`\n\t\t\t// which is invalid Sql syntax, Table from one of the SELECTs cannot be used in global ORDER clause\n\t\t\tfor (const singleOrderBy of orderBy) {\n\t\t\t\tif (is(singleOrderBy, PgColumn)) {\n\t\t\t\t\torderByValues.push(sql.identifier(singleOrderBy.name));\n\t\t\t\t} else if (is(singleOrderBy, SQL)) {\n\t\t\t\t\tfor (let i = 0; i < singleOrderBy.queryChunks.length; i++) {\n\t\t\t\t\t\tconst chunk = singleOrderBy.queryChunks[i];\n\n\t\t\t\t\t\tif (is(chunk, PgColumn)) {\n\t\t\t\t\t\t\tsingleOrderBy.queryChunks[i] = sql.identifier(chunk.name);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\torderByValues.push(sql`${singleOrderBy}`);\n\t\t\t\t} else {\n\t\t\t\t\torderByValues.push(sql`${singleOrderBy}`);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\torderBySql = sql` order by ${sql.join(orderByValues, sql`, `)} `;\n\t\t}\n\n\t\tconst limitSql = limit ? sql` limit ${limit}` : undefined;\n\n\t\tconst operatorChunk = sql.raw(`${type} ${isAll ? 'all ' : ''}`);\n\n\t\tconst offsetSql = offset ? sql` offset ${offset}` : undefined;\n\n\t\treturn sql`${leftChunk}${operatorChunk}${rightChunk}${orderBySql}${limitSql}${offsetSql}`;\n\t}\n\n\tbuildInsertQuery({ table, values, onConflict, returning, withList }: PgInsertConfig): SQL {\n\t\tconst valuesSqlList: ((SQLChunk | SQL)[] | SQL)[] = [];\n\t\tconst columns: Record<string, PgColumn> = table[Table.Symbol.Columns];\n\n\t\tconst colEntries: [string, PgColumn][] = Object.entries(columns);\n\n\t\tconst insertOrder = colEntries.map(([, column]) => sql.identifier(column.name));\n\n\t\tfor (const [valueIndex, value] of values.entries()) {\n\t\t\tconst valueList: (SQLChunk | SQL)[] = [];\n\t\t\tfor (const [fieldName, col] of colEntries) {\n\t\t\t\tconst colValue = value[fieldName];\n\t\t\t\tif (colValue === undefined || (is(colValue, Param) && colValue.value === undefined)) {\n\t\t\t\t\t// eslint-disable-next-line unicorn/no-negated-condition\n\t\t\t\t\tif (col.defaultFn !== undefined) {\n\t\t\t\t\t\tconst defaultFnResult = col.defaultFn();\n\t\t\t\t\t\tconst defaultValue = is(defaultFnResult, SQL) ? defaultFnResult : sql.param(defaultFnResult, col);\n\t\t\t\t\t\tvalueList.push(defaultValue);\n\t\t\t\t\t\t// eslint-disable-next-line unicorn/no-negated-condition\n\t\t\t\t\t} else if (!col.default && col.onUpdateFn !== undefined) {\n\t\t\t\t\t\tconst onUpdateFnResult = col.onUpdateFn();\n\t\t\t\t\t\tconst newValue = is(onUpdateFnResult, SQL) ? onUpdateFnResult : sql.param(onUpdateFnResult, col);\n\t\t\t\t\t\tvalueList.push(newValue);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvalueList.push(sql`default`);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tvalueList.push(colValue);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tvaluesSqlList.push(valueList);\n\t\t\tif (valueIndex < values.length - 1) {\n\t\t\t\tvaluesSqlList.push(sql`, `);\n\t\t\t}\n\t\t}\n\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst valuesSql = sql.join(valuesSqlList);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}`\n\t\t\t: undefined;\n\n\t\tconst onConflictSql = onConflict ? sql` on conflict ${onConflict}` : undefined;\n\n\t\treturn sql`${withSql}insert into ${table} ${insertOrder} values ${valuesSql}${onConflictSql}${returningSql}`;\n\t}\n\n\tbuildRefreshMaterializedViewQuery(\n\t\t{ view, concurrently, withNoData }: { view: PgMaterializedView; concurrently?: boolean; withNoData?: boolean },\n\t): SQL {\n\t\tconst concurrentlySql = concurrently ? sql` concurrently` : undefined;\n\t\tconst withNoDataSql = withNoData ? sql` with no data` : undefined;\n\n\t\treturn sql`refresh materialized view${concurrentlySql} ${view}${withNoDataSql}`;\n\t}\n\n\tprepareTyping(encoder: DriverValueEncoder<unknown, unknown>): QueryTypingsValue {\n\t\tif (is(encoder, PgJsonb) || is(encoder, PgJson)) {\n\t\t\treturn 'json';\n\t\t} else if (is(encoder, PgNumeric)) {\n\t\t\treturn 'decimal';\n\t\t} else if (is(encoder, PgTime)) {\n\t\t\treturn 'time';\n\t\t} else if (is(encoder, PgTimestamp) || is(encoder, PgTimestampString)) {\n\t\t\treturn 'timestamp';\n\t\t} else if (is(encoder, PgDate) || is(encoder, PgDateString)) {\n\t\t\treturn 'date';\n\t\t} else if (is(encoder, PgUUID)) {\n\t\t\treturn 'uuid';\n\t\t} else {\n\t\t\treturn 'none';\n\t\t}\n\t}\n\n\tsqlToQuery(sql: SQL, invokeSource?: 'indexes' | undefined): QueryWithTypings {\n\t\treturn sql.toQuery({\n\t\t\tescapeName: this.escapeName,\n\t\t\tescapeParam: this.escapeParam,\n\t\t\tescapeString: this.escapeString,\n\t\t\tprepareTyping: this.prepareTyping,\n\t\t\tinvokeSource,\n\t\t});\n\t}\n\n\t// buildRelationalQueryWithPK({\n\t// \tfullSchema,\n\t// \tschema,\n\t// \ttableNamesMap,\n\t// \ttable,\n\t// \ttableConfig,\n\t// \tqueryConfig: config,\n\t// \ttableAlias,\n\t// \tisRoot = false,\n\t// \tjoinOn,\n\t// }: {\n\t// \tfullSchema: Record<string, unknown>;\n\t// \tschema: TablesRelationalConfig;\n\t// \ttableNamesMap: Record<string, string>;\n\t// \ttable: PgTable;\n\t// \ttableConfig: TableRelationalConfig;\n\t// \tqueryConfig: true | DBQueryConfig<'many', true>;\n\t// \ttableAlias: string;\n\t// \tisRoot?: boolean;\n\t// \tjoinOn?: SQL;\n\t// }): BuildRelationalQueryResult<PgTable, PgColumn> {\n\t// \t// For { \"<relation>\": true }, return a table with selection of all columns\n\t// \tif (config === true) {\n\t// \t\tconst selectionEntries = Object.entries(tableConfig.columns);\n\t// \t\tconst selection: BuildRelationalQueryResult<PgTable, PgColumn>['selection'] = selectionEntries.map((\n\t// \t\t\t[key, value],\n\t// \t\t) => ({\n\t// \t\t\tdbKey: value.name,\n\t// \t\t\ttsKey: key,\n\t// \t\t\tfield: value as PgColumn,\n\t// \t\t\trelationTableTsKey: undefined,\n\t// \t\t\tisJson: false,\n\t// \t\t\tselection: [],\n\t// \t\t}));\n\n\t// \t\treturn {\n\t// \t\t\ttableTsKey: tableConfig.tsName,\n\t// \t\t\tsql: table,\n\t// \t\t\tselection,\n\t// \t\t};\n\t// \t}\n\n\t// \t// let selection: BuildRelationalQueryResult<PgTable, PgColumn>['selection'] = [];\n\t// \t// let selectionForBuild = selection;\n\n\t// \tconst aliasedColumns = Object.fromEntries(\n\t// \t\tObject.entries(tableConfig.columns).map(([key, value]) => [key, aliasedTableColumn(value, tableAlias)]),\n\t// \t);\n\n\t// \tconst aliasedRelations = Object.fromEntries(\n\t// \t\tObject.entries(tableConfig.relations).map(([key, value]) => [key, aliasedRelation(value, tableAlias)]),\n\t// \t);\n\n\t// \tconst aliasedFields = Object.assign({}, aliasedColumns, aliasedRelations);\n\n\t// \tlet where, hasUserDefinedWhere;\n\t// \tif (config.where) {\n\t// \t\tconst whereSql = typeof config.where === 'function' ? config.where(aliasedFields, operators) : config.where;\n\t// \t\twhere = whereSql && mapColumnsInSQLToAlias(whereSql, tableAlias);\n\t// \t\thasUserDefinedWhere = !!where;\n\t// \t}\n\t// \twhere = and(joinOn, where);\n\n\t// \t// const fieldsSelection: { tsKey: string; value: PgColumn | SQL.Aliased; isExtra?: boolean }[] = [];\n\t// \tlet joins: Join[] = [];\n\t// \tlet selectedColumns: string[] = [];\n\n\t// \t// Figure out which columns to select\n\t// \tif (config.columns) {\n\t// \t\tlet isIncludeMode = false;\n\n\t// \t\tfor (const [field, value] of Object.entries(config.columns)) {\n\t// \t\t\tif (value === undefined) {\n\t// \t\t\t\tcontinue;\n\t// \t\t\t}\n\n\t// \t\t\tif (field in tableConfig.columns) {\n\t// \t\t\t\tif (!isIncludeMode && value === true) {\n\t// \t\t\t\t\tisIncludeMode = true;\n\t// \t\t\t\t}\n\t// \t\t\t\tselectedColumns.push(field);\n\t// \t\t\t}\n\t// \t\t}\n\n\t// \t\tif (selectedColumns.length > 0) {\n\t// \t\t\tselectedColumns = isIncludeMode\n\t// \t\t\t\t? selectedColumns.filter((c) => config.columns?.[c] === true)\n\t// \t\t\t\t: Object.keys(tableConfig.columns).filter((key) => !selectedColumns.includes(key));\n\t// \t\t}\n\t// \t} else {\n\t// \t\t// Select all columns if selection is not specified\n\t// \t\tselectedColumns = Object.keys(tableConfig.columns);\n\t// \t}\n\n\t// \t// for (const field of selectedColumns) {\n\t// \t// \tconst column = tableConfig.columns[field]! as PgColumn;\n\t// \t// \tfieldsSelection.push({ tsKey: field, value: column });\n\t// \t// }\n\n\t// \tlet initiallySelectedRelations: {\n\t// \t\ttsKey: string;\n\t// \t\tqueryConfig: true | DBQueryConfig<'many', false>;\n\t// \t\trelation: Relation;\n\t// \t}[] = [];\n\n\t// \t// let selectedRelations: BuildRelationalQueryResult<PgTable, PgColumn>['selection'] = [];\n\n\t// \t// Figure out which relations to select\n\t// \tif (config.with) {\n\t// \t\tinitiallySelectedRelations = Object.entries(config.with)\n\t// \t\t\t.filter((entry): entry is [typeof entry[0], NonNullable<typeof entry[1]>] => !!entry[1])\n\t// \t\t\t.map(([tsKey, queryConfig]) => ({ tsKey, queryConfig, relation: tableConfig.relations[tsKey]! }));\n\t// \t}\n\n\t// \tconst manyRelations = initiallySelectedRelations.filter((r) =>\n\t// \t\tis(r.relation, Many)\n\t// \t\t&& (schema[tableNamesMap[r.relation.referencedTable[Table.Symbol.Name]]!]?.primaryKey.length ?? 0) > 0\n\t// \t);\n\t// \t// If this is the last Many relation (or there are no Many relations), we are on the innermost subquery level\n\t// \tconst isInnermostQuery = manyRelations.length < 2;\n\n\t// \tconst selectedExtras: {\n\t// \t\ttsKey: string;\n\t// \t\tvalue: SQL.Aliased;\n\t// \t}[] = [];\n\n\t// \t// Figure out which extras to select\n\t// \tif (isInnermostQuery && config.extras) {\n\t// \t\tconst extras = typeof config.extras === 'function'\n\t// \t\t\t? config.extras(aliasedFields, { sql })\n\t// \t\t\t: config.extras;\n\t// \t\tfor (const [tsKey, value] of Object.entries(extras)) {\n\t// \t\t\tselectedExtras.push({\n\t// \t\t\t\ttsKey,\n\t// \t\t\t\tvalue: mapColumnsInAliasedSQLToAlias(value, tableAlias),\n\t// \t\t\t});\n\t// \t\t}\n\t// \t}\n\n\t// \t// Transform `fieldsSelection` into `selection`\n\t// \t// `fieldsSelection` shouldn't be used after this point\n\t// \t// for (const { tsKey, value, isExtra } of fieldsSelection) {\n\t// \t// \tselection.push({\n\t// \t// \t\tdbKey: is(value, SQL.Aliased) ? value.fieldAlias : tableConfig.columns[tsKey]!.name,\n\t// \t// \t\ttsKey,\n\t// \t// \t\tfield: is(value, Column) ? aliasedTableColumn(value, tableAlias) : value,\n\t// \t// \t\trelationTableTsKey: undefined,\n\t// \t// \t\tisJson: false,\n\t// \t// \t\tisExtra,\n\t// \t// \t\tselection: [],\n\t// \t// \t});\n\t// \t// }\n\n\t// \tlet orderByOrig = typeof config.orderBy === 'function'\n\t// \t\t? config.orderBy(aliasedFields, orderByOperators)\n\t// \t\t: config.orderBy ?? [];\n\t// \tif (!Array.isArray(orderByOrig)) {\n\t// \t\torderByOrig = [orderByOrig];\n\t// \t}\n\t// \tconst orderBy = orderByOrig.map((orderByValue) => {\n\t// \t\tif (is(orderByValue, Column)) {\n\t// \t\t\treturn aliasedTableColumn(orderByValue, tableAlias) as PgColumn;\n\t// \t\t}\n\t// \t\treturn mapColumnsInSQLToAlias(orderByValue, tableAlias);\n\t// \t});\n\n\t// \tconst limit = isInnermostQuery ? config.limit : undefined;\n\t// \tconst offset = isInnermostQuery ? config.offset : undefined;\n\n\t// \t// For non-root queries without additional config except columns, return a table with selection\n\t// \tif (\n\t// \t\t!isRoot\n\t// \t\t&& initiallySelectedRelations.length === 0\n\t// \t\t&& selectedExtras.length === 0\n\t// \t\t&& !where\n\t// \t\t&& orderBy.length === 0\n\t// \t\t&& limit === undefined\n\t// \t\t&& offset === undefined\n\t// \t) {\n\t// \t\treturn {\n\t// \t\t\ttableTsKey: tableConfig.tsName,\n\t// \t\t\tsql: table,\n\t// \t\t\tselection: selectedColumns.map((key) => ({\n\t// \t\t\t\tdbKey: tableConfig.columns[key]!.name,\n\t// \t\t\t\ttsKey: key,\n\t// \t\t\t\tfield: tableConfig.columns[key] as PgColumn,\n\t// \t\t\t\trelationTableTsKey: undefined,\n\t// \t\t\t\tisJson: false,\n\t// \t\t\t\tselection: [],\n\t// \t\t\t})),\n\t// \t\t};\n\t// \t}\n\n\t// \tconst selectedRelationsWithoutPK:\n\n\t// \t// Process all relations without primary keys, because they need to be joined differently and will all be on the same query level\n\t// \tfor (\n\t// \t\tconst {\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tqueryConfig: selectedRelationConfigValue,\n\t// \t\t\trelation,\n\t// \t\t} of initiallySelectedRelations\n\t// \t) {\n\t// \t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t// \t\tconst relationTableName = relation.referencedTable[Table.Symbol.Name];\n\t// \t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t// \t\tconst relationTable = schema[relationTableTsName]!;\n\n\t// \t\tif (relationTable.primaryKey.length > 0) {\n\t// \t\t\tcontinue;\n\t// \t\t}\n\n\t// \t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t// \t\tconst joinOn = and(\n\t// \t\t\t...normalizedRelation.fields.map((field, i) =>\n\t// \t\t\t\teq(\n\t// \t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t// \t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t// \t\t\t\t)\n\t// \t\t\t),\n\t// \t\t);\n\t// \t\tconst builtRelation = this.buildRelationalQueryWithoutPK({\n\t// \t\t\tfullSchema,\n\t// \t\t\tschema,\n\t// \t\t\ttableNamesMap,\n\t// \t\t\ttable: fullSchema[relationTableTsName] as PgTable,\n\t// \t\t\ttableConfig: schema[relationTableTsName]!,\n\t// \t\t\tqueryConfig: selectedRelationConfigValue,\n\t// \t\t\ttableAlias: relationTableAlias,\n\t// \t\t\tjoinOn,\n\t// \t\t\tnestedQueryRelation: relation,\n\t// \t\t});\n\t// \t\tconst field = sql`${sql.identifier(relationTableAlias)}.${sql.identifier('data')}`.as(selectedRelationTsKey);\n\t// \t\tjoins.push({\n\t// \t\t\ton: sql`true`,\n\t// \t\t\ttable: new Subquery(builtRelation.sql as SQL, {}, relationTableAlias),\n\t// \t\t\talias: relationTableAlias,\n\t// \t\t\tjoinType: 'left',\n\t// \t\t\tlateral: true,\n\t// \t\t});\n\t// \t\tselectedRelations.push({\n\t// \t\t\tdbKey: selectedRelationTsKey,\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tfield,\n\t// \t\t\trelationTableTsKey: relationTableTsName,\n\t// \t\t\tisJson: true,\n\t// \t\t\tselection: builtRelation.selection,\n\t// \t\t});\n\t// \t}\n\n\t// \tconst oneRelations = initiallySelectedRelations.filter((r): r is typeof r & { relation: One } =>\n\t// \t\tis(r.relation, One)\n\t// \t);\n\n\t// \t// Process all One relations with PKs, because they can all be joined on the same level\n\t// \tfor (\n\t// \t\tconst {\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tqueryConfig: selectedRelationConfigValue,\n\t// \t\t\trelation,\n\t// \t\t} of oneRelations\n\t// \t) {\n\t// \t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t// \t\tconst relationTableName = relation.referencedTable[Table.Symbol.Name];\n\t// \t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t// \t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t// \t\tconst relationTable = schema[relationTableTsName]!;\n\n\t// \t\tif (relationTable.primaryKey.length === 0) {\n\t// \t\t\tcontinue;\n\t// \t\t}\n\n\t// \t\tconst joinOn = and(\n\t// \t\t\t...normalizedRelation.fields.map((field, i) =>\n\t// \t\t\t\teq(\n\t// \t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t// \t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t// \t\t\t\t)\n\t// \t\t\t),\n\t// \t\t);\n\t// \t\tconst builtRelation = this.buildRelationalQueryWithPK({\n\t// \t\t\tfullSchema,\n\t// \t\t\tschema,\n\t// \t\t\ttableNamesMap,\n\t// \t\t\ttable: fullSchema[relationTableTsName] as PgTable,\n\t// \t\t\ttableConfig: schema[relationTableTsName]!,\n\t// \t\t\tqueryConfig: selectedRelationConfigValue,\n\t// \t\t\ttableAlias: relationTableAlias,\n\t// \t\t\tjoinOn,\n\t// \t\t});\n\t// \t\tconst field = sql`case when ${sql.identifier(relationTableAlias)} is null then null else json_build_array(${\n\t// \t\t\tsql.join(\n\t// \t\t\t\tbuiltRelation.selection.map(({ field }) =>\n\t// \t\t\t\t\tis(field, SQL.Aliased)\n\t// \t\t\t\t\t\t? sql`${sql.identifier(relationTableAlias)}.${sql.identifier(field.fieldAlias)}`\n\t// \t\t\t\t\t\t: is(field, Column)\n\t// \t\t\t\t\t\t? aliasedTableColumn(field, relationTableAlias)\n\t// \t\t\t\t\t\t: field\n\t// \t\t\t\t),\n\t// \t\t\t\tsql`, `,\n\t// \t\t\t)\n\t// \t\t}) end`.as(selectedRelationTsKey);\n\t// \t\tconst isLateralJoin = is(builtRelation.sql, SQL);\n\t// \t\tjoins.push({\n\t// \t\t\ton: isLateralJoin ? sql`true` : joinOn,\n\t// \t\t\ttable: is(builtRelation.sql, SQL)\n\t// \t\t\t\t? new Subquery(builtRelation.sql, {}, relationTableAlias)\n\t// \t\t\t\t: aliasedTable(builtRelation.sql, relationTableAlias),\n\t// \t\t\talias: relationTableAlias,\n\t// \t\t\tjoinType: 'left',\n\t// \t\t\tlateral: is(builtRelation.sql, SQL),\n\t// \t\t});\n\t// \t\tselectedRelations.push({\n\t// \t\t\tdbKey: selectedRelationTsKey,\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tfield,\n\t// \t\t\trelationTableTsKey: relationTableTsName,\n\t// \t\t\tisJson: true,\n\t// \t\t\tselection: builtRelation.selection,\n\t// \t\t});\n\t// \t}\n\n\t// \tlet distinct: PgSelectConfig['distinct'];\n\t// \tlet tableFrom: PgTable | Subquery = table;\n\n\t// \t// Process first Many relation - each one requires a nested subquery\n\t// \tconst manyRelation = manyRelations[0];\n\t// \tif (manyRelation) {\n\t// \t\tconst {\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tqueryConfig: selectedRelationQueryConfig,\n\t// \t\t\trelation,\n\t// \t\t} = manyRelation;\n\n\t// \t\tdistinct = {\n\t// \t\t\ton: tableConfig.primaryKey.map((c) => aliasedTableColumn(c as PgColumn, tableAlias)),\n\t// \t\t};\n\n\t// \t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t// \t\tconst relationTableName = relation.referencedTable[Table.Symbol.Name];\n\t// \t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t// \t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t// \t\tconst joinOn = and(\n\t// \t\t\t...normalizedRelation.fields.map((field, i) =>\n\t// \t\t\t\teq(\n\t// \t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t// \t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t// \t\t\t\t)\n\t// \t\t\t),\n\t// \t\t);\n\n\t// \t\tconst builtRelationJoin = this.buildRelationalQueryWithPK({\n\t// \t\t\tfullSchema,\n\t// \t\t\tschema,\n\t// \t\t\ttableNamesMap,\n\t// \t\t\ttable: fullSchema[relationTableTsName] as PgTable,\n\t// \t\t\ttableConfig: schema[relationTableTsName]!,\n\t// \t\t\tqueryConfig: selectedRelationQueryConfig,\n\t// \t\t\ttableAlias: relationTableAlias,\n\t// \t\t\tjoinOn,\n\t// \t\t});\n\n\t// \t\tconst builtRelationSelectionField = sql`case when ${\n\t// \t\t\tsql.identifier(relationTableAlias)\n\t// \t\t} is null then '[]' else json_agg(json_build_array(${\n\t// \t\t\tsql.join(\n\t// \t\t\t\tbuiltRelationJoin.selection.map(({ field }) =>\n\t// \t\t\t\t\tis(field, SQL.Aliased)\n\t// \t\t\t\t\t\t? sql`${sql.identifier(relationTableAlias)}.${sql.identifier(field.fieldAlias)}`\n\t// \t\t\t\t\t\t: is(field, Column)\n\t// \t\t\t\t\t\t? aliasedTableColumn(field, relationTableAlias)\n\t// \t\t\t\t\t\t: field\n\t// \t\t\t\t),\n\t// \t\t\t\tsql`, `,\n\t// \t\t\t)\n\t// \t\t})) over (partition by ${sql.join(distinct.on, sql`, `)}) end`.as(selectedRelationTsKey);\n\t// \t\tconst isLateralJoin = is(builtRelationJoin.sql, SQL);\n\t// \t\tjoins.push({\n\t// \t\t\ton: isLateralJoin ? sql`true` : joinOn,\n\t// \t\t\ttable: isLateralJoin\n\t// \t\t\t\t? new Subquery(builtRelationJoin.sql as SQL, {}, relationTableAlias)\n\t// \t\t\t\t: aliasedTable(builtRelationJoin.sql as PgTable, relationTableAlias),\n\t// \t\t\talias: relationTableAlias,\n\t// \t\t\tjoinType: 'left',\n\t// \t\t\tlateral: isLateralJoin,\n\t// \t\t});\n\n\t// \t\t// Build the \"from\" subquery with the remaining Many relations\n\t// \t\tconst builtTableFrom = this.buildRelationalQueryWithPK({\n\t// \t\t\tfullSchema,\n\t// \t\t\tschema,\n\t// \t\t\ttableNamesMap,\n\t// \t\t\ttable,\n\t// \t\t\ttableConfig,\n\t// \t\t\tqueryConfig: {\n\t// \t\t\t\t...config,\n\t// \t\t\t\twhere: undefined,\n\t// \t\t\t\torderBy: undefined,\n\t// \t\t\t\tlimit: undefined,\n\t// \t\t\t\toffset: undefined,\n\t// \t\t\t\twith: manyRelations.slice(1).reduce<NonNullable<typeof config['with']>>(\n\t// \t\t\t\t\t(result, { tsKey, queryConfig: configValue }) => {\n\t// \t\t\t\t\t\tresult[tsKey] = configValue;\n\t// \t\t\t\t\t\treturn result;\n\t// \t\t\t\t\t},\n\t// \t\t\t\t\t{},\n\t// \t\t\t\t),\n\t// \t\t\t},\n\t// \t\t\ttableAlias,\n\t// \t\t});\n\n\t// \t\tselectedRelations.push({\n\t// \t\t\tdbKey: selectedRelationTsKey,\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tfield: builtRelationSelectionField,\n\t// \t\t\trelationTableTsKey: relationTableTsName,\n\t// \t\t\tisJson: true,\n\t// \t\t\tselection: builtRelationJoin.selection,\n\t// \t\t});\n\n\t// \t\t// selection = builtTableFrom.selection.map((item) =>\n\t// \t\t// \tis(item.field, SQL.Aliased)\n\t// \t\t// \t\t? { ...item, field: sql`${sql.identifier(tableAlias)}.${sql.identifier(item.field.fieldAlias)}` }\n\t// \t\t// \t\t: item\n\t// \t\t// );\n\t// \t\t// selectionForBuild = [{\n\t// \t\t// \tdbKey: '*',\n\t// \t\t// \ttsKey: '*',\n\t// \t\t// \tfield: sql`${sql.identifier(tableAlias)}.*`,\n\t// \t\t// \tselection: [],\n\t// \t\t// \tisJson: false,\n\t// \t\t// \trelationTableTsKey: undefined,\n\t// \t\t// }];\n\t// \t\t// const newSelectionItem: (typeof selection)[number] = {\n\t// \t\t// \tdbKey: selectedRelationTsKey,\n\t// \t\t// \ttsKey: selectedRelationTsKey,\n\t// \t\t// \tfield,\n\t// \t\t// \trelationTableTsKey: relationTableTsName,\n\t// \t\t// \tisJson: true,\n\t// \t\t// \tselection: builtRelationJoin.selection,\n\t// \t\t// };\n\t// \t\t// selection.push(newSelectionItem);\n\t// \t\t// selectionForBuild.push(newSelectionItem);\n\n\t// \t\ttableFrom = is(builtTableFrom.sql, PgTable)\n\t// \t\t\t? builtTableFrom.sql\n\t// \t\t\t: new Subquery(builtTableFrom.sql, {}, tableAlias);\n\t// \t}\n\n\t// \tif (selectedColumns.length === 0 && selectedRelations.length === 0 && selectedExtras.length === 0) {\n\t// \t\tthrow new DrizzleError(`No fields selected for table \"${tableConfig.tsName}\" (\"${tableAlias}\")`);\n\t// \t}\n\n\t// \tlet selection: BuildRelationalQueryResult<PgTable, PgColumn>['selection'];\n\n\t// \tfunction prepareSelectedColumns() {\n\t// \t\treturn selectedColumns.map((key) => ({\n\t// \t\t\tdbKey: tableConfig.columns[key]!.name,\n\t// \t\t\ttsKey: key,\n\t// \t\t\tfield: tableConfig.columns[key] as PgColumn,\n\t// \t\t\trelationTableTsKey: undefined,\n\t// \t\t\tisJson: false,\n\t// \t\t\tselection: [],\n\t// \t\t}));\n\t// \t}\n\n\t// \tfunction prepareSelectedExtras() {\n\t// \t\treturn selectedExtras.map((item) => ({\n\t// \t\t\tdbKey: item.value.fieldAlias,\n\t// \t\t\ttsKey: item.tsKey,\n\t// \t\t\tfield: item.value,\n\t// \t\t\trelationTableTsKey: undefined,\n\t// \t\t\tisJson: false,\n\t// \t\t\tselection: [],\n\t// \t\t}));\n\t// \t}\n\n\t// \tif (isRoot) {\n\t// \t\tselection = [\n\t// \t\t\t...prepareSelectedColumns(),\n\t// \t\t\t...prepareSelectedExtras(),\n\t// \t\t];\n\t// \t}\n\n\t// \tif (hasUserDefinedWhere || orderBy.length > 0) {\n\t// \t\ttableFrom = new Subquery(\n\t// \t\t\tthis.buildSelectQuery({\n\t// \t\t\t\ttable: is(tableFrom, PgTable) ? aliasedTable(tableFrom, tableAlias) : tableFrom,\n\t// \t\t\t\tfields: {},\n\t// \t\t\t\tfieldsFlat: selectionForBuild.map(({ field }) => ({\n\t// \t\t\t\t\tpath: [],\n\t// \t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t// \t\t\t\t})),\n\t// \t\t\t\tjoins,\n\t// \t\t\t\tdistinct,\n\t// \t\t\t}),\n\t// \t\t\t{},\n\t// \t\t\ttableAlias,\n\t// \t\t);\n\t// \t\tselectionForBuild = selection.map((item) =>\n\t// \t\t\tis(item.field, SQL.Aliased)\n\t// \t\t\t\t? { ...item, field: sql`${sql.identifier(tableAlias)}.${sql.identifier(item.field.fieldAlias)}` }\n\t// \t\t\t\t: item\n\t// \t\t);\n\t// \t\tjoins = [];\n\t// \t\tdistinct = undefined;\n\t// \t}\n\n\t// \tconst result = this.buildSelectQuery({\n\t// \t\ttable: is(tableFrom, PgTable) ? aliasedTable(tableFrom, tableAlias) : tableFrom,\n\t// \t\tfields: {},\n\t// \t\tfieldsFlat: selectionForBuild.map(({ field }) => ({\n\t// \t\t\tpath: [],\n\t// \t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t// \t\t})),\n\t// \t\twhere,\n\t// \t\tlimit,\n\t// \t\toffset,\n\t// \t\tjoins,\n\t// \t\torderBy,\n\t// \t\tdistinct,\n\t// \t});\n\n\t// \treturn {\n\t// \t\ttableTsKey: tableConfig.tsName,\n\t// \t\tsql: result,\n\t// \t\tselection,\n\t// \t};\n\t// }\n\n\tbuildRelationalQueryWithoutPK({\n\t\tfullSchema,\n\t\tschema,\n\t\ttableNamesMap,\n\t\ttable,\n\t\ttableConfig,\n\t\tqueryConfig: config,\n\t\ttableAlias,\n\t\tnestedQueryRelation,\n\t\tjoinOn,\n\t}: {\n\t\tfullSchema: Record<string, unknown>;\n\t\tschema: TablesRelationalConfig;\n\t\ttableNamesMap: Record<string, string>;\n\t\ttable: PgTable;\n\t\ttableConfig: TableRelationalConfig;\n\t\tqueryConfig: true | DBQueryConfig<'many', true>;\n\t\ttableAlias: string;\n\t\tnestedQueryRelation?: Relation;\n\t\tjoinOn?: SQL;\n\t}): BuildRelationalQueryResult<PgTable, PgColumn> {\n\t\tlet selection: BuildRelationalQueryResult<PgTable, PgColumn>['selection'] = [];\n\t\tlet limit, offset, orderBy: NonNullable<PgSelectConfig['orderBy']> = [], where;\n\t\tconst joins: PgSelectJoinConfig[] = [];\n\n\t\tif (config === true) {\n\t\t\tconst selectionEntries = Object.entries(tableConfig.columns);\n\t\t\tselection = selectionEntries.map((\n\t\t\t\t[key, value],\n\t\t\t) => ({\n\t\t\t\tdbKey: value.name,\n\t\t\t\ttsKey: key,\n\t\t\t\tfield: aliasedTableColumn(value as PgColumn, tableAlias),\n\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\tisJson: false,\n\t\t\t\tselection: [],\n\t\t\t}));\n\t\t} else {\n\t\t\tconst aliasedColumns = Object.fromEntries(\n\t\t\t\tObject.entries(tableConfig.columns).map(([key, value]) => [key, aliasedTableColumn(value, tableAlias)]),\n\t\t\t);\n\n\t\t\tif (config.where) {\n\t\t\t\tconst whereSql = typeof config.where === 'function'\n\t\t\t\t\t? config.where(aliasedColumns, getOperators())\n\t\t\t\t\t: config.where;\n\t\t\t\twhere = whereSql && mapColumnsInSQLToAlias(whereSql, tableAlias);\n\t\t\t}\n\n\t\t\tconst fieldsSelection: { tsKey: string; value: PgColumn | SQL.Aliased }[] = [];\n\t\t\tlet selectedColumns: string[] = [];\n\n\t\t\t// Figure out which columns to select\n\t\t\tif (config.columns) {\n\t\t\t\tlet isIncludeMode = false;\n\n\t\t\t\tfor (const [field, value] of Object.entries(config.columns)) {\n\t\t\t\t\tif (value === undefined) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (field in tableConfig.columns) {\n\t\t\t\t\t\tif (!isIncludeMode && value === true) {\n\t\t\t\t\t\t\tisIncludeMode = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tselectedColumns.push(field);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (selectedColumns.length > 0) {\n\t\t\t\t\tselectedColumns = isIncludeMode\n\t\t\t\t\t\t? selectedColumns.filter((c) => config.columns?.[c] === true)\n\t\t\t\t\t\t: Object.keys(tableConfig.columns).filter((key) => !selectedColumns.includes(key));\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Select all columns if selection is not specified\n\t\t\t\tselectedColumns = Object.keys(tableConfig.columns);\n\t\t\t}\n\n\t\t\tfor (const field of selectedColumns) {\n\t\t\t\tconst column = tableConfig.columns[field]! as PgColumn;\n\t\t\t\tfieldsSelection.push({ tsKey: field, value: column });\n\t\t\t}\n\n\t\t\tlet selectedRelations: {\n\t\t\t\ttsKey: string;\n\t\t\t\tqueryConfig: true | DBQueryConfig<'many', false>;\n\t\t\t\trelation: Relation;\n\t\t\t}[] = [];\n\n\t\t\t// Figure out which relations to select\n\t\t\tif (config.with) {\n\t\t\t\tselectedRelations = Object.entries(config.with)\n\t\t\t\t\t.filter((entry): entry is [typeof entry[0], NonNullable<typeof entry[1]>] => !!entry[1])\n\t\t\t\t\t.map(([tsKey, queryConfig]) => ({ tsKey, queryConfig, relation: tableConfig.relations[tsKey]! }));\n\t\t\t}\n\n\t\t\tlet extras;\n\n\t\t\t// Figure out which extras to select\n\t\t\tif (config.extras) {\n\t\t\t\textras = typeof config.extras === 'function'\n\t\t\t\t\t? config.extras(aliasedColumns, { sql })\n\t\t\t\t\t: config.extras;\n\t\t\t\tfor (const [tsKey, value] of Object.entries(extras)) {\n\t\t\t\t\tfieldsSelection.push({\n\t\t\t\t\t\ttsKey,\n\t\t\t\t\t\tvalue: mapColumnsInAliasedSQLToAlias(value, tableAlias),\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Transform `fieldsSelection` into `selection`\n\t\t\t// `fieldsSelection` shouldn't be used after this point\n\t\t\tfor (const { tsKey, value } of fieldsSelection) {\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: is(value, SQL.Aliased) ? value.fieldAlias : tableConfig.columns[tsKey]!.name,\n\t\t\t\t\ttsKey,\n\t\t\t\t\tfield: is(value, Column) ? aliasedTableColumn(value, tableAlias) : value,\n\t\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\t\tisJson: false,\n\t\t\t\t\tselection: [],\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tlet orderByOrig = typeof config.orderBy === 'function'\n\t\t\t\t? config.orderBy(aliasedColumns, getOrderByOperators())\n\t\t\t\t: config.orderBy ?? [];\n\t\t\tif (!Array.isArray(orderByOrig)) {\n\t\t\t\torderByOrig = [orderByOrig];\n\t\t\t}\n\t\t\torderBy = orderByOrig.map((orderByValue) => {\n\t\t\t\tif (is(orderByValue, Column)) {\n\t\t\t\t\treturn aliasedTableColumn(orderByValue, tableAlias) as PgColumn;\n\t\t\t\t}\n\t\t\t\treturn mapColumnsInSQLToAlias(orderByValue, tableAlias);\n\t\t\t});\n\n\t\t\tlimit = config.limit;\n\t\t\toffset = config.offset;\n\n\t\t\t// Process all relations\n\t\t\tfor (\n\t\t\t\tconst {\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tqueryConfig: selectedRelationConfigValue,\n\t\t\t\t\trelation,\n\t\t\t\t} of selectedRelations\n\t\t\t) {\n\t\t\t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t\t\t\tconst relationTableName = getTableUniqueName(relation.referencedTable);\n\t\t\t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t\t\t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t\t\t\tconst joinOn = and(\n\t\t\t\t\t...normalizedRelation.fields.map((field, i) =>\n\t\t\t\t\t\teq(\n\t\t\t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t\t\t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t\t\t\t\t\t)\n\t\t\t\t\t),\n\t\t\t\t);\n\t\t\t\tconst builtRelation = this.buildRelationalQueryWithoutPK({\n\t\t\t\t\tfullSchema,\n\t\t\t\t\tschema,\n\t\t\t\t\ttableNamesMap,\n\t\t\t\t\ttable: fullSchema[relationTableTsName] as PgTable,\n\t\t\t\t\ttableConfig: schema[relationTableTsName]!,\n\t\t\t\t\tqueryConfig: is(relation, One)\n\t\t\t\t\t\t? (selectedRelationConfigValue === true\n\t\t\t\t\t\t\t? { limit: 1 }\n\t\t\t\t\t\t\t: { ...selectedRelationConfigValue, limit: 1 })\n\t\t\t\t\t\t: selectedRelationConfigValue,\n\t\t\t\t\ttableAlias: relationTableAlias,\n\t\t\t\t\tjoinOn,\n\t\t\t\t\tnestedQueryRelation: relation,\n\t\t\t\t});\n\t\t\t\tconst field = sql`${sql.identifier(relationTableAlias)}.${sql.identifier('data')}`.as(selectedRelationTsKey);\n\t\t\t\tjoins.push({\n\t\t\t\t\ton: sql`true`,\n\t\t\t\t\ttable: new Subquery(builtRelation.sql as SQL, {}, relationTableAlias),\n\t\t\t\t\talias: relationTableAlias,\n\t\t\t\t\tjoinType: 'left',\n\t\t\t\t\tlateral: true,\n\t\t\t\t});\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: selectedRelationTsKey,\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tfield,\n\t\t\t\t\trelationTableTsKey: relationTableTsName,\n\t\t\t\t\tisJson: true,\n\t\t\t\t\tselection: builtRelation.selection,\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (selection.length === 0) {\n\t\t\tthrow new DrizzleError({ message: `No fields selected for table \"${tableConfig.tsName}\" (\"${tableAlias}\")` });\n\t\t}\n\n\t\tlet result;\n\n\t\twhere = and(joinOn, where);\n\n\t\tif (nestedQueryRelation) {\n\t\t\tlet field = sql`json_build_array(${\n\t\t\t\tsql.join(\n\t\t\t\t\tselection.map(({ field, tsKey, isJson }) =>\n\t\t\t\t\t\tisJson\n\t\t\t\t\t\t\t? sql`${sql.identifier(`${tableAlias}_${tsKey}`)}.${sql.identifier('data')}`\n\t\t\t\t\t\t\t: is(field, SQL.Aliased)\n\t\t\t\t\t\t\t? field.sql\n\t\t\t\t\t\t\t: field\n\t\t\t\t\t),\n\t\t\t\t\tsql`, `,\n\t\t\t\t)\n\t\t\t})`;\n\t\t\tif (is(nestedQueryRelation, Many)) {\n\t\t\t\tfield = sql`coalesce(json_agg(${field}${\n\t\t\t\t\torderBy.length > 0 ? sql` order by ${sql.join(orderBy, sql`, `)}` : undefined\n\t\t\t\t}), '[]'::json)`;\n\t\t\t\t// orderBy = [];\n\t\t\t}\n\t\t\tconst nestedSelection = [{\n\t\t\t\tdbKey: 'data',\n\t\t\t\ttsKey: 'data',\n\t\t\t\tfield: field.as('data'),\n\t\t\t\tisJson: true,\n\t\t\t\trelationTableTsKey: tableConfig.tsName,\n\t\t\t\tselection,\n\t\t\t}];\n\n\t\t\tconst needsSubquery = limit !== undefined || offset !== undefined || orderBy.length > 0;\n\n\t\t\tif (needsSubquery) {\n\t\t\t\tresult = this.buildSelectQuery({\n\t\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\t\tfields: {},\n\t\t\t\t\tfieldsFlat: [{\n\t\t\t\t\t\tpath: [],\n\t\t\t\t\t\tfield: sql.raw('*'),\n\t\t\t\t\t}],\n\t\t\t\t\twhere,\n\t\t\t\t\tlimit,\n\t\t\t\t\toffset,\n\t\t\t\t\torderBy,\n\t\t\t\t\tsetOperators: [],\n\t\t\t\t});\n\n\t\t\t\twhere = undefined;\n\t\t\t\tlimit = undefined;\n\t\t\t\toffset = undefined;\n\t\t\t\torderBy = [];\n\t\t\t} else {\n\t\t\t\tresult = aliasedTable(table, tableAlias);\n\t\t\t}\n\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: is(result, PgTable) ? result : new Subquery(result, {}, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: nestedSelection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\tjoins,\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t} else {\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: selection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\tjoins,\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t}\n\n\t\treturn {\n\t\t\ttableTsKey: tableConfig.tsName,\n\t\t\tsql: result,\n\t\t\tselection,\n\t\t};\n\t}\n}\n"], "names": ["table", "sql", "joinOn", "field"], "mappings": ";;;AAAA,SAAS,cAAc,oBAAoB,+BAA+B,8BAA8B;AACxG,SAAS,cAAc;AACvB,SAAS,YAAY,UAAU;AAC/B,SAAS,oBAAoB;;;;;;;;AAE7B;AAmBA,SAAS,eAAe;AACxB;AAYA,SAAS,KAAK,IAAI,YAAY;;AAW9B,SAAS,gBAAgB;AACzB,SAAS,cAAc,oBAAoB,aAAa;AACxD,SAAS,2BAA2C;AACpD,SAAS,sBAAsB;AAE/B,SAAS,kBAAkB;;;;;;;;;;;;;;;AAGpB,MAAM,UAAU;IACtB,OAAA,mPAAiB,aAAU,CAAA,GAAY,YAAA;IAEvC,MAAM,QAAQ,UAAA,EAA6B,OAAA,EAAoB,MAAA,EAAiD;QAC/G,MAAM,kBAAkB,OAAO,WAAW,WACvC,yBACA,OAAO,eAAA,IAAmB;QAC7B,MAAM,mBAAmB,OAAO,WAAW,WAAW,YAAY,OAAO,gBAAA,IAAoB;QAC7F,MAAM,6QAAuB,MAAA,CAAA;8BAAA,wPACC,MAAA,CAAI,UAAA,CAAW,gBAAgB,CAAC,CAAA,CAAA,wPAAI,MAAA,CAAI,UAAA,CAAW,eAAe,CAAC,CAAA;;;;;EAAA,CAAA;QAMjG,MAAM,QAAQ,OAAA,uPAAQ,MAAA,CAAA,4BAAA,wPAAkC,MAAA,CAAI,UAAA,CAAW,gBAAgB,CAAC,CAAA,CAAE;QAC1F,MAAM,QAAQ,OAAA,CAAQ,oBAAoB;QAE1C,MAAM,eAAe,MAAM,QAAQ,GAAA,uPAClC,MAAA,CAAA,iCAAA,EAAuC,4PAAA,CAAI,UAAA,CAAW,gBAAgB,CAAC,CAAA,CAAA,wPACtE,MAAA,CAAI,UAAA,CAAW,eAAe,CAC/B,CAAA,iCAAA,CAAA;QAGD,MAAM,kBAAkB,YAAA,CAAa,CAAC,CAAA;QACtC,MAAM,QAAQ,WAAA,CAAY,OAAO,OAAO;YACvC,WAAA,MAAiB,aAAa,WAAY;gBACzC,IACC,CAAC,mBACE,OAAO,gBAAgB,UAAU,IAAI,UAAU,YAAA,EACjD;oBACD,KAAA,MAAW,QAAQ,UAAU,GAAA,CAAK;wBACjC,MAAM,GAAG,OAAA,uPAAQ,MAAA,CAAI,GAAA,CAAI,IAAI,CAAC;oBAC/B;oBACA,MAAM,GAAG,OAAA,uPACR,MAAA,CAAA,YAAA,wPAAkB,MAAA,CAAI,UAAA,CAAW,gBAAgB,CAAC,CAAA,CAAA,wPACjD,MAAA,CAAI,UAAA,CAAW,eAAe,CAC/B,CAAA,+BAAA,EAAkC,UAAU,IAAI,CAAA,EAAA,EAAK,UAAU,YAAY,CAAA,CAAA,CAAA;gBAE7E;YACD;QACD,CAAC;IACF;IAEA,WAAW,IAAA,EAAsB;QAChC,OAAO,CAAA,CAAA,EAAI,IAAI,CAAA,CAAA,CAAA;IAChB;IAEA,YAAY,GAAA,EAAqB;QAChC,OAAO,CAAA,CAAA,EAAI,MAAM,CAAC,EAAA;IACnB;IAEA,aAAa,GAAA,EAAqB;QACjC,OAAO,CAAA,CAAA,EAAI,IAAI,OAAA,CAAQ,MAAM,IAAI,CAAC,CAAA,CAAA,CAAA;IACnC;IAEQ,aAAa,OAAA,EAAkD;QACtE,IAAI,CAAC,SAAS,QAAQ,OAAO,KAAA;QAE7B,MAAM,gBAAgB;kQAAC,MAAA,CAAA,KAAA,CAAU;SAAA;QACjC,KAAA,MAAW,CAAC,GAAG,CAAC,CAAA,IAAK,QAAQ,OAAA,CAAQ,EAAG;YACvC,cAAc,IAAA,CAAK,4PAAA,CAAA,wPAAM,MAAA,CAAI,UAAA,CAAW,EAAE,CAAA,CAAE,KAAK,CAAC,CAAA,KAAA,EAAQ,EAAE,CAAA,CAAE,GAAG,CAAA,CAAA,CAAG;YACpE,IAAI,IAAI,QAAQ,MAAA,GAAS,GAAG;gBAC3B,cAAc,IAAA,uPAAK,MAAA,CAAA,EAAA,CAAO;YAC3B;QACD;QACA,cAAc,IAAA,uPAAK,MAAA,CAAA,CAAA,CAAM;QACzB,6PAAO,MAAA,CAAI,IAAA,CAAK,aAAa;IAC9B;IAEA,iBAAiB,EAAE,KAAA,EAAO,KAAA,EAAO,SAAA,EAAW,QAAA,CAAS,CAAA,EAAwB;QAC5E,MAAM,UAAU,IAAA,CAAK,YAAA,CAAa,QAAQ;QAE1C,MAAM,eAAe,kQAClB,MAAA,CAAA,WAAA,EAAiB,IAAA,CAAK,cAAA,CAAe,WAAW;YAAE,eAAe;QAAK,CAAC,CAAC,CAAA,CAAA,GACxE,KAAA;QAEH,MAAM,WAAW,8PAAQ,MAAA,CAAA,OAAA,EAAa,KAAK,CAAA,CAAA,GAAK,KAAA;QAEhD,6PAAO,MAAA,CAAA,EAAM,OAAO,CAAA,YAAA,EAAe,KAAK,CAAA,EAAG,QAAQ,CAAA,EAAG,YAAY,CAAA,CAAA;IACnE;IAEA,eAAe,KAAA,EAAgB,GAAA,EAAqB;QACnD,MAAM,eAAe,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA;QAE/C,MAAM,cAAc,OAAO,IAAA,CAAK,YAAY,EAAE,MAAA,CAAO,CAAC,UACrD,GAAA,CAAI,OAAO,CAAA,KAAM,KAAA,KAAa,YAAA,CAAa,OAAO,CAAA,EAAG,eAAe,KAAA;QAGrE,MAAM,UAAU,YAAY,MAAA;QAC5B,OAAO,4PAAA,CAAI,IAAA,CAAK,YAAY,OAAA,CAAQ,CAAC,SAAS,MAAM;YACnD,MAAM,MAAM,YAAA,CAAa,OAAO,CAAA;YAEhC,MAAM,QAAQ,GAAA,CAAI,OAAO,CAAA,0PAAK,MAAA,CAAI,KAAA,CAAM,IAAI,UAAA,CAAY,GAAG,GAAG;YAC9D,MAAM,4PAAM,MAAA,CAAA,EAAM,4PAAA,CAAI,UAAA,CAAW,IAAI,IAAI,CAAC,CAAA,GAAA,EAAM,KAAK,CAAA,CAAA;YAErD,IAAI,IAAI,UAAU,GAAG;gBACpB,OAAO;oBAAC;0QAAK,MAAA,CAAI,GAAA,CAAI,IAAI,CAAC;iBAAA;YAC3B;YACA,OAAO;gBAAC,GAAG;aAAA;QACZ,CAAC,CAAC;IACH;IAEA,iBAAiB,EAAE,KAAA,EAAO,GAAA,EAAK,KAAA,EAAO,SAAA,EAAW,QAAA,CAAS,CAAA,EAAwB;QACjF,MAAM,UAAU,IAAA,CAAK,YAAA,CAAa,QAAQ;QAE1C,MAAM,SAAS,IAAA,CAAK,cAAA,CAAe,OAAO,GAAG;QAE7C,MAAM,eAAe,kQAClB,MAAA,CAAA,WAAA,EAAiB,IAAA,CAAK,cAAA,CAAe,WAAW;YAAE,eAAe;QAAK,CAAC,CAAC,CAAA,CAAA,GACxE,KAAA;QAEH,MAAM,WAAW,8PAAQ,MAAA,CAAA,OAAA,EAAa,KAAK,CAAA,CAAA,GAAK,KAAA;QAEhD,6PAAO,MAAA,CAAA,EAAM,OAAO,CAAA,OAAA,EAAU,KAAK,CAAA,KAAA,EAAQ,MAAM,CAAA,EAAG,QAAQ,CAAA,EAAG,YAAY,CAAA,CAAA;IAC5E;IAAA;;;;;;;;;;GAAA,GAaQ,eACP,MAAA,EACA,EAAE,gBAAgB,KAAA,CAAM,CAAA,GAAiC,CAAC,CAAA,EACpD;QACN,MAAM,aAAa,OAAO,MAAA;QAE1B,MAAM,SAAS,OACb,OAAA,CAAQ,CAAC,EAAE,KAAA,CAAM,CAAA,EAAG,MAAM;YAC1B,MAAM,QAAoB,CAAC,CAAA;YAE3B,0PAAI,KAAA,EAAG,6PAAO,MAAA,CAAI,OAAO,KAAK,MAAM,gBAAA,EAAkB;gBACrD,MAAM,IAAA,uPAAK,MAAA,CAAI,UAAA,CAAW,MAAM,UAAU,CAAC;YAC5C,OAAA,0PAAW,KAAA,EAAG,6PAAO,MAAA,CAAI,OAAO,2PAAK,KAAA,EAAG,6PAAO,MAAG,GAAG;gBACpD,MAAM,8PAAQ,KAAA,EAAG,4PAAO,OAAA,CAAI,OAAO,IAAI,MAAM,GAAA,GAAM;gBAEnD,IAAI,eAAe;oBAClB,MAAM,IAAA,CACL,0PAAI,MAAA,CACH,MAAM,WAAA,CAAY,GAAA,CAAI,CAAC,MAAM;wBAC5B,0PAAI,KAAA,EAAG,8QAAG,WAAQ,GAAG;4BACpB,6PAAO,MAAA,CAAI,UAAA,CAAW,EAAE,IAAI;wBAC7B;wBACA,OAAO;oBACR,CAAC;gBAGJ,OAAO;oBACN,MAAM,IAAA,CAAK,KAAK;gBACjB;gBAEA,0PAAI,KAAA,EAAG,6PAAO,MAAA,CAAI,OAAO,GAAG;oBAC3B,MAAM,IAAA,uPAAK,MAAA,CAAA,IAAA,wPAAU,MAAA,CAAI,UAAA,CAAW,MAAM,UAAU,CAAC,CAAA,CAAE;gBACxD;YACD,OAAA,IAAW,2PAAA,EAAG,yPAAO,SAAM,GAAG;gBAC7B,IAAI,eAAe;oBAClB,MAAM,IAAA,CAAK,4PAAA,CAAI,UAAA,CAAW,MAAM,IAAI,CAAC;gBACtC,OAAO;oBACN,MAAM,IAAA,CAAK,KAAK;gBACjB;YACD;YAEA,IAAI,IAAI,aAAa,GAAG;gBACvB,MAAM,IAAA,CAAK,4PAAA,CAAA,EAAA,CAAO;YACnB;YAEA,OAAO;QACR,CAAC;QAEF,6PAAO,MAAA,CAAI,IAAA,CAAK,MAAM;IACvB;IAEA,iBACC,EACC,QAAA,EACA,MAAA,EACA,UAAA,EACA,KAAA,EACA,MAAA,EACA,KAAA,EACA,KAAA,EACA,OAAA,EACA,OAAA,EACA,KAAA,EACA,MAAA,EACA,aAAA,EACA,QAAA,EACA,YAAA,EACD,EACM;QACN,MAAM,aAAa,cAAc,2QAAA,EAA8B,MAAM;QACrE,KAAA,MAAW,KAAK,WAAY;YAC3B,0PACC,KAAA,EAAG,EAAE,KAAA,oPAAO,SAAM,MACf,mQAAA,EAAa,EAAE,KAAA,CAAM,KAAK,MAAA,uPACvB,KAAA,EAAG,2PAAO,WAAQ,IACpB,MAAM,CAAA,CAAE,KAAA,IACR,0PAAA,EAAG,6QAAO,aAAU,IACpB,KAAA,2PAAM,iBAAc,CAAA,CAAE,IAAA,yPACtB,KAAA,EAAG,6PAAO,MAAG,IACb,KAAA,yPACA,eAAA,EAAa,KAAK,CAAA,KACnB,CAAA,CAAE,CAACA,SACL,OAAO,KAAK,CAAC,EAAE,KAAA,CAAM,CAAA,GACpB,UAAA,CAAWA,MAAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA,wPAAI,eAAA,EAAaA,MAAK,IAAIA,MAAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,QAAQ,CAAA,EAC3F,EAAG,EAAE,KAAA,CAAM,KAAK,GAChB;gBACD,MAAM,iQAAY,eAAA,EAAa,EAAE,KAAA,CAAM,KAAK;gBAC5C,MAAM,IAAI,MACT,CAAA,MAAA,EACC,EAAE,IAAA,CAAK,IAAA,CAAK,IAAI,CACjB,CAAA,6BAAA,EAAgC,SAAS,CAAA,GAAA,EAAM,EAAE,KAAA,CAAM,IAAI,CAAA,kBAAA,EAAqB,SAAS,CAAA,sDAAA,CAAA;YAE3F;QACD;QAEA,MAAM,gBAAgB,CAAC,SAAS,MAAM,MAAA,KAAW;QAEjD,MAAM,UAAU,IAAA,CAAK,YAAA,CAAa,QAAQ;QAE1C,IAAI;QACJ,IAAI,UAAU;YACb,cAAc,aAAa,6PAAO,MAAA,CAAA,SAAA,CAAA,yPAAiB,MAAA,CAAA,cAAA,EAAoB,4PAAA,CAAI,IAAA,CAAK,SAAS,EAAA,wPAAI,MAAA,CAAA,EAAA,CAAO,CAAC,CAAA,CAAA,CAAA;QACtG;QAEA,MAAM,YAAY,IAAA,CAAK,cAAA,CAAe,YAAY;YAAE;QAAc,CAAC;QAEnE,MAAM,WAAA,CAAY,MAAM;YACvB,0PAAI,KAAA,EAAG,wPAAO,QAAK,KAAK,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,YAAY,CAAA,KAAM,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,IAAI,CAAA,EAAG;gBACtF,IAAI,WAAW,4PAAA,CAAA,wPAAM,MAAA,CAAI,UAAA,CAAW,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,YAAY,CAAC,CAAC,CAAA,CAAA;gBACrE,IAAI,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,MAAM,CAAA,EAAG;oBAC/B,WAAW,4PAAA,CAAA,wPAAM,MAAA,CAAI,UAAA,CAAW,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,MAAM,CAAE,CAAC,CAAA,CAAA,EAAI,QAAQ,CAAA,CAAA;gBACzE;gBACA,6PAAO,MAAA,CAAA,EAAM,QAAQ,CAAA,CAAA,wPAAI,MAAA,CAAI,UAAA,CAAW,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,IAAI,CAAC,CAAC,CAAA,CAAA;YAClE;YAEA,OAAO;QACR,CAAA,EAAG;QAEH,MAAM,aAAoB,CAAC,CAAA;QAE3B,IAAI,OAAO;YACV,KAAA,MAAW,CAAC,OAAO,QAAQ,CAAA,IAAK,MAAM,OAAA,CAAQ,EAAG;gBAChD,IAAI,UAAU,GAAG;oBAChB,WAAW,IAAA,uPAAK,MAAA,CAAA,CAAA,CAAM;gBACvB;gBACA,MAAMA,SAAQ,SAAS,KAAA;gBACvB,MAAM,aAAa,SAAS,OAAA,yPAAU,MAAA,CAAA,QAAA,CAAA,GAAgB,KAAA;gBAEtD,0PAAI,KAAA,EAAGA,uQAAO,UAAO,GAAG;oBACvB,MAAM,YAAYA,MAAAA,gQAAM,UAAA,CAAQ,MAAA,CAAO,IAAI,CAAA;oBAC3C,MAAM,cAAcA,MAAAA,gQAAM,UAAA,CAAQ,MAAA,CAAO,MAAM,CAAA;oBAC/C,MAAM,gBAAgBA,MAAAA,CAAM,yQAAA,CAAQ,MAAA,CAAO,YAAY,CAAA;oBACvD,MAAM,QAAQ,cAAc,gBAAgB,KAAA,IAAY,SAAS,KAAA;oBACjE,WAAW,IAAA,uPACV,MAAA,CAAA,wPAAM,MAAA,CAAI,GAAA,CAAI,SAAS,QAAQ,CAAC,CAAA,KAAA,EAAQ,UAAU,CAAA,CAAA,EACjD,oQAAc,MAAA,CAAA,wPAAM,MAAA,CAAI,UAAA,CAAW,WAAW,CAAC,CAAA,CAAA,CAAA,GAAM,KAAA,CACtD,CAAA,wPAAG,MAAA,CAAI,UAAA,CAAW,aAAa,CAAC,CAAA,EAAG,+PAAS,MAAA,CAAA,CAAA,wPAAO,MAAA,CAAI,UAAA,CAAW,KAAK,CAAC,CAAA,CAAE,CAAA,IAAA,EAAO,SAAS,EAAE,CAAA,CAAA;gBAE9F,OAAA,yPAAW,MAAA,EAAGA,8PAAO,OAAI,GAAG;oBAC3B,MAAM,WAAWA,MAAAA,2PAAM,iBAAc,CAAA,CAAE,IAAA;oBACvC,MAAM,aAAaA,MAAAA,2PAAM,iBAAc,CAAA,CAAE,MAAA;oBACzC,MAAM,eAAeA,MAAAA,2PAAM,iBAAc,CAAA,CAAE,YAAA;oBAC3C,MAAM,QAAQ,aAAa,eAAe,KAAA,IAAY,SAAS,KAAA;oBAC/D,WAAW,IAAA,uPACV,MAAA,CAAA,wPAAM,MAAA,CAAI,GAAA,CAAI,SAAS,QAAQ,CAAC,CAAA,KAAA,EAAQ,UAAU,CAAA,CAAA,EACjD,mQAAa,MAAA,CAAA,wPAAM,MAAA,CAAI,UAAA,CAAW,UAAU,CAAC,CAAA,CAAA,CAAA,GAAM,KAAA,CACpD,CAAA,EAAG,4PAAA,CAAI,UAAA,CAAW,YAAY,CAAC,CAAA,EAAG,+PAAS,MAAA,CAAA,CAAA,wPAAO,MAAA,CAAI,UAAA,CAAW,KAAK,CAAC,CAAA,CAAE,CAAA,IAAA,EAAO,SAAS,EAAE,CAAA,CAAA;gBAE7F,OAAO;oBACN,WAAW,IAAA,uPACV,MAAA,CAAA,wPAAM,MAAA,CAAI,GAAA,CAAI,SAAS,QAAQ,CAAC,CAAA,KAAA,EAAQ,UAAU,CAAA,CAAA,EAAIA,MAAK,CAAA,IAAA,EAAO,SAAS,EAAE,CAAA,CAAA;gBAE/E;gBACA,IAAI,QAAQ,MAAM,MAAA,GAAS,GAAG;oBAC7B,WAAW,IAAA,uPAAK,MAAA,CAAA,CAAA,CAAM;gBACvB;YACD;QACD;QAEA,MAAM,iQAAW,MAAA,CAAI,IAAA,CAAK,UAAU;QAEpC,MAAM,WAAW,8PAAQ,MAAA,CAAA,OAAA,EAAa,KAAK,CAAA,CAAA,GAAK,KAAA;QAEhD,MAAM,YAAY,+PAAS,MAAA,CAAA,QAAA,EAAc,MAAM,CAAA,CAAA,GAAK,KAAA;QAEpD,IAAI;QACJ,IAAI,WAAW,QAAQ,MAAA,GAAS,GAAG;YAClC,kQAAa,OAAA,CAAA,UAAA,wPAAgB,MAAA,CAAI,IAAA,CAAK,+PAAS,MAAA,CAAA,EAAA,CAAO,CAAC,CAAA,CAAA;QACxD;QAEA,IAAI;QACJ,IAAI,WAAW,QAAQ,MAAA,GAAS,GAAG;YAClC,mQAAa,MAAA,CAAA,UAAA,wPAAgB,MAAA,CAAI,IAAA,CAAK,+PAAS,MAAA,CAAA,EAAA,CAAO,CAAC,CAAA,CAAA;QACxD;QAEA,MAAM,WAAW,QAAQ,4PAAA,CAAA,OAAA,EAAa,KAAK,CAAA,CAAA,GAAK,KAAA;QAEhD,MAAM,YAAY,+PAAS,MAAA,CAAA,QAAA,EAAc,MAAM,CAAA,CAAA,GAAK,KAAA;QAEpD,MAAM,yQAAmB,MAAA,CAAI,KAAA,CAAM;QACnC,IAAI,eAAe;YAClB,MAAM,kQAAY,MAAA,CAAA,KAAA,EAAW,4PAAA,CAAI,GAAA,CAAI,cAAc,QAAQ,CAAC,CAAA,CAAA;YAC5D,IAAI,cAAc,MAAA,CAAO,EAAA,EAAI;gBAC5B,UAAU,MAAA,uPACT,MAAA,CAAA,IAAA,wPACC,MAAA,CAAI,IAAA,CACH,MAAM,OAAA,CAAQ,cAAc,MAAA,CAAO,EAAE,IAAI,cAAc,MAAA,CAAO,EAAA,GAAK;oBAAC,cAAc,MAAA,CAAO,EAAE;iBAAA,wPAC3F,MAAA,CAAA,EAAA,CAAA,EAEF,CAAA;YAEF;YACA,IAAI,cAAc,MAAA,CAAO,MAAA,EAAQ;gBAChC,UAAU,MAAA,uPAAO,MAAA,CAAA,QAAA,CAAa;YAC/B,OAAA,IAAW,cAAc,MAAA,CAAO,UAAA,EAAY;gBAC3C,UAAU,MAAA,uPAAO,MAAA,CAAA,YAAA,CAAiB;YACnC;YACA,iBAAiB,MAAA,CAAO,SAAS;QAClC;QACA,MAAM,mQACL,MAAA,CAAA,EAAM,OAAO,CAAA,MAAA,EAAS,WAAW,CAAA,CAAA,EAAI,SAAS,CAAA,MAAA,EAAS,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,UAAU,CAAA,EAAG,SAAS,CAAA,EAAG,UAAU,CAAA,EAAG,QAAQ,CAAA,EAAG,SAAS,CAAA,EAAG,gBAAgB,CAAA,CAAA;QAEtK,IAAI,aAAa,MAAA,GAAS,GAAG;YAC5B,OAAO,IAAA,CAAK,kBAAA,CAAmB,YAAY,YAAY;QACxD;QAEA,OAAO;IACR;IAEA,mBAAmB,UAAA,EAAiB,YAAA,EAAmD;QACtF,MAAM,CAAC,aAAa,GAAG,IAAI,CAAA,GAAI;QAE/B,IAAI,CAAC,aAAa;YACjB,MAAM,IAAI,MAAM,kDAAkD;QACnE;QAEA,IAAI,KAAK,MAAA,KAAW,GAAG;YACtB,OAAO,IAAA,CAAK,sBAAA,CAAuB;gBAAE;gBAAY;YAAY,CAAC;QAC/D;QAGA,OAAO,IAAA,CAAK,kBAAA,CACX,IAAA,CAAK,sBAAA,CAAuB;YAAE;YAAY;QAAY,CAAC,GACvD;IAEF;IAEA,uBAAuB,EACtB,UAAA,EACA,aAAa,EAAE,IAAA,EAAM,KAAA,EAAO,WAAA,EAAa,KAAA,EAAO,OAAA,EAAS,MAAA,CAAO,CAAA,EACjE,EAAkF;QACjF,MAAM,kQAAY,MAAA,CAAA,CAAA,EAAO,WAAW,MAAA,CAAO,CAAC,CAAA,EAAA,CAAA;QAC5C,MAAM,kQAAa,OAAA,CAAA,CAAA,EAAO,YAAY,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA;QAE9C,IAAI;QACJ,IAAI,WAAW,QAAQ,MAAA,GAAS,GAAG;YAClC,MAAM,gBAAyC,CAAC,CAAA;YAIhD,KAAA,MAAW,iBAAiB,QAAS;gBACpC,0PAAI,KAAA,EAAG,0RAAe,WAAQ,GAAG;oBAChC,cAAc,IAAA,uPAAK,MAAA,CAAI,UAAA,CAAW,cAAc,IAAI,CAAC;gBACtD,OAAA,yPAAW,MAAA,EAAG,qQAAe,MAAG,GAAG;oBAClC,IAAA,IAAS,IAAI,GAAG,IAAI,cAAc,WAAA,CAAY,MAAA,EAAQ,IAAK;wBAC1D,MAAM,QAAQ,cAAc,WAAA,CAAY,CAAC,CAAA;wBAEzC,QAAI,uPAAA,EAAG,kRAAO,WAAQ,GAAG;4BACxB,cAAc,WAAA,CAAY,CAAC,CAAA,yPAAI,MAAA,CAAI,UAAA,CAAW,MAAM,IAAI;wBACzD;oBACD;oBAEA,cAAc,IAAA,uPAAK,MAAA,CAAA,EAAM,aAAa,CAAA,CAAE;gBACzC,OAAO;oBACN,cAAc,IAAA,uPAAK,MAAA,CAAA,EAAM,aAAa,CAAA,CAAE;gBACzC;YACD;YAEA,mQAAa,MAAA,CAAA,UAAA,EAAgB,4PAAA,CAAI,IAAA,CAAK,qQAAe,MAAA,CAAA,EAAA,CAAO,CAAC,CAAA,CAAA,CAAA;QAC9D;QAEA,MAAM,WAAW,8PAAQ,MAAA,CAAA,OAAA,EAAa,KAAK,CAAA,CAAA,GAAK,KAAA;QAEhD,MAAM,sQAAgB,MAAA,CAAI,GAAA,CAAI,GAAG,IAAI,CAAA,CAAA,EAAI,QAAQ,SAAS,EAAE,EAAE;QAE9D,MAAM,YAAY,+PAAS,MAAA,CAAA,QAAA,EAAc,MAAM,CAAA,CAAA,GAAK,KAAA;QAEpD,6PAAO,MAAA,CAAA,EAAM,SAAS,CAAA,EAAG,aAAa,CAAA,EAAG,UAAU,CAAA,EAAG,UAAU,CAAA,EAAG,QAAQ,CAAA,EAAG,SAAS,CAAA,CAAA;IACxF;IAEA,iBAAiB,EAAE,KAAA,EAAO,MAAA,EAAQ,UAAA,EAAY,SAAA,EAAW,QAAA,CAAS,CAAA,EAAwB;QACzF,MAAM,gBAA8C,CAAC,CAAA;QACrD,MAAM,UAAoC,KAAA,kPAAM,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA;QAEpE,MAAM,aAAmC,OAAO,OAAA,CAAQ,OAAO;QAE/D,MAAM,cAAc,WAAW,GAAA,CAAI,CAAC,CAAC,EAAE,MAAM,CAAA,GAAM,4PAAA,CAAI,UAAA,CAAW,OAAO,IAAI,CAAC;QAE9E,KAAA,MAAW,CAAC,YAAY,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,EAAG;YACnD,MAAM,YAAgC,CAAC,CAAA;YACvC,KAAA,MAAW,CAAC,WAAW,GAAG,CAAA,IAAK,WAAY;gBAC1C,MAAM,WAAW,KAAA,CAAM,SAAS,CAAA;gBAChC,IAAI,aAAa,KAAA,0PAAc,MAAA,EAAG,gQAAU,QAAK,KAAK,SAAS,KAAA,KAAU,KAAA,GAAY;oBAEpF,IAAI,IAAI,SAAA,KAAc,KAAA,GAAW;wBAChC,MAAM,kBAAkB,IAAI,SAAA,CAAU;wBACtC,MAAM,qQAAe,KAAA,EAAG,uQAAiB,MAAG,IAAI,wQAAkB,MAAA,CAAI,KAAA,CAAM,iBAAiB,GAAG;wBAChG,UAAU,IAAA,CAAK,YAAY;oBAE5B,OAAA,IAAW,CAAC,IAAI,OAAA,IAAW,IAAI,UAAA,KAAe,KAAA,GAAW;wBACxD,MAAM,mBAAmB,IAAI,UAAA,CAAW;wBACxC,MAAM,eAAW,uPAAA,EAAG,wQAAkB,MAAG,IAAI,yQAAmB,MAAA,CAAI,KAAA,CAAM,kBAAkB,GAAG;wBAC/F,UAAU,IAAA,CAAK,QAAQ;oBACxB,OAAO;wBACN,UAAU,IAAA,uPAAK,MAAA,CAAA,OAAA,CAAY;oBAC5B;gBACD,OAAO;oBACN,UAAU,IAAA,CAAK,QAAQ;gBACxB;YACD;YAEA,cAAc,IAAA,CAAK,SAAS;YAC5B,IAAI,aAAa,OAAO,MAAA,GAAS,GAAG;gBACnC,cAAc,IAAA,uPAAK,MAAA,CAAA,EAAA,CAAO;YAC3B;QACD;QAEA,MAAM,UAAU,IAAA,CAAK,YAAA,CAAa,QAAQ;QAE1C,MAAM,kQAAY,MAAA,CAAI,IAAA,CAAK,aAAa;QAExC,MAAM,eAAe,kQAClB,MAAA,CAAA,WAAA,EAAiB,IAAA,CAAK,cAAA,CAAe,WAAW;YAAE,eAAe;QAAK,CAAC,CAAC,CAAA,CAAA,GACxE,KAAA;QAEH,MAAM,gBAAgB,kQAAa,OAAA,CAAA,aAAA,EAAmB,UAAU,CAAA,CAAA,GAAK,KAAA;QAErE,6PAAO,MAAA,CAAA,EAAM,OAAO,CAAA,YAAA,EAAe,KAAK,CAAA,CAAA,EAAI,WAAW,CAAA,QAAA,EAAW,SAAS,CAAA,EAAG,aAAa,CAAA,EAAG,YAAY,CAAA,CAAA;IAC3G;IAEA,kCACC,EAAE,IAAA,EAAM,YAAA,EAAc,UAAA,CAAW,CAAA,EAC3B;QACN,MAAM,kBAAkB,qQAAe,MAAA,CAAA,aAAA,CAAA,GAAqB,KAAA;QAC5D,MAAM,gBAAgB,mQAAa,MAAA,CAAA,aAAA,CAAA,GAAqB,KAAA;QAExD,6PAAO,MAAA,CAAA,yBAAA,EAA+B,eAAe,CAAA,CAAA,EAAI,IAAI,CAAA,EAAG,aAAa,CAAA,CAAA;IAC9E;IAEA,cAAc,OAAA,EAAkE;QAC/E,0PAAI,KAAA,EAAG,mRAAS,UAAO,KAAK,2PAAA,EAAG,kRAAS,SAAM,GAAG;YAChD,OAAO;QACR,OAAA,0PAAW,KAAA,EAAG,SAAS,wRAAS,GAAG;YAClC,OAAO;QACR,OAAA,0PAAW,KAAA,EAAG,kRAAS,SAAM,GAAG;YAC/B,OAAO;QACR,OAAA,0PAAW,KAAA,EAAG,SAAS,4RAAW,2PAAK,KAAA,EAAG,uRAAS,oBAAiB,GAAG;YACtE,OAAO;QACR,OAAA,0PAAW,KAAA,EAAG,iRAAS,UAAM,2PAAK,KAAA,EAAG,kRAAS,eAAY,GAAG;YAC5D,OAAO;QACR,OAAA,0PAAW,KAAA,EAAG,SAAS,kRAAM,GAAG;YAC/B,OAAO;QACR,OAAO;YACN,OAAO;QACR;IACD;IAEA,WAAWC,IAAAA,EAAU,YAAA,EAAwD;QAC5E,OAAOA,KAAI,OAAA,CAAQ;YAClB,YAAY,IAAA,CAAK,UAAA;YACjB,aAAa,IAAA,CAAK,WAAA;YAClB,cAAc,IAAA,CAAK,YAAA;YACnB,eAAe,IAAA,CAAK,aAAA;YACpB;QACD,CAAC;IACF;IAAA,+BAAA;IAAA,eAAA;IAAA,WAAA;IAAA,kBAAA;IAAA,UAAA;IAAA,gBAAA;IAAA,wBAAA;IAAA,eAAA;IAAA,mBAAA;IAAA,WAAA;IAAA,OAAA;IAAA,wCAAA;IAAA,mCAAA;IAAA,0CAAA;IAAA,mBAAA;IAAA,uCAAA;IAAA,oDAAA;IAAA,uBAAA;IAAA,qBAAA;IAAA,iBAAA;IAAA,sDAAA;IAAA,+EAAA;IAAA,0BAAA;IAAA,kEAAA;IAAA,yGAAA;IAAA,mBAAA;IAAA,YAAA;IAAA,wBAAA;IAAA,iBAAA;IAAA,+BAAA;IAAA,oCAAA;IAAA,oBAAA;IAAA,oBAAA;IAAA,SAAA;IAAA,aAAA;IAAA,qCAAA;IAAA,iBAAA;IAAA,gBAAA;IAAA,OAAA;IAAA,KAAA;IAAA,sFAAA;IAAA,yCAAA;IAAA,8CAAA;IAAA,6GAAA;IAAA,MAAA;IAAA,gDAAA;IAAA,4GAAA;IAAA,MAAA;IAAA,8EAAA;IAAA,mCAAA;IAAA,uBAAA;IAAA,iHAAA;IAAA,sEAAA;IAAA,mCAAA;IAAA,KAAA;IAAA,+BAAA;IAAA,yGAAA;IAAA,2BAAA;IAAA,uCAAA;IAAA,yCAAA;IAAA,yBAAA;IAAA,+BAAA;IAAA,mEAAA;IAAA,gCAAA;IAAA,gBAAA;IAAA,OAAA;IAAA,yCAAA;IAAA,8CAAA;IAAA,6BAAA;IAAA,QAAA;IAAA,mCAAA;IAAA,OAAA;IAAA,MAAA;IAAA,sCAAA;IAAA,qCAAA;IAAA,oEAAA;IAAA,0FAAA;IAAA,MAAA;IAAA,YAAA;IAAA,wDAAA;IAAA,wDAAA;IAAA,KAAA;IAAA,6CAAA;IAAA,+DAAA;IAAA,8DAAA;IAAA,QAAA;IAAA,qCAAA;IAAA,mBAAA;IAAA,sDAAA;IAAA,wBAAA;IAAA,aAAA;IAAA,8FAAA;IAAA,2CAAA;IAAA,sBAAA;IAAA,6DAAA;IAAA,8FAAA;IAAA,wGAAA;IAAA,KAAA;IAAA,kEAAA;IAAA,yBAAA;IAAA,2GAAA;IAAA,MAAA;IAAA,iHAAA;IAAA,sDAAA;IAAA,2BAAA;IAAA,mBAAA;IAAA,wBAAA;IAAA,aAAA;IAAA,wCAAA;IAAA,4CAAA;IAAA,uDAAA;IAAA,6CAAA;IAAA,sBAAA;IAAA,2DAAA;IAAA,2BAAA;IAAA,aAAA;IAAA,+DAAA;IAAA,SAAA;IAAA,MAAA;IAAA,KAAA;IAAA,mDAAA;IAAA,2DAAA;IAAA,iEAAA;IAAA,wBAAA;IAAA,6FAAA;IAAA,eAAA;IAAA,kFAAA;IAAA,uCAAA;IAAA,uBAAA;IAAA,iBAAA;IAAA,uBAAA;IAAA,WAAA;IAAA,QAAA;IAAA,0DAAA;IAAA,sDAAA;IAAA,4BAAA;IAAA,sCAAA;IAAA,iCAAA;IAAA,KAAA;IAAA,uDAAA;IAAA,oCAAA;IAAA,sEAAA;IAAA,MAAA;IAAA,6DAAA;IAAA,OAAA;IAAA,8DAAA;IAAA,gEAAA;IAAA,mGAAA;IAAA,QAAA;IAAA,YAAA;IAAA,+CAAA;IAAA,mCAAA;IAAA,cAAA;IAAA,4BAAA;IAAA,2BAAA;IAAA,4BAAA;IAAA,OAAA;IAAA,aAAA;IAAA,qCAAA;IAAA,iBAAA;IAAA,gDAAA;IAAA,6CAAA;IAAA,kBAAA;IAAA,mDAAA;IAAA,qCAAA;IAAA,qBAAA;IAAA,qBAAA;IAAA,UAAA;IAAA,OAAA;IAAA,KAAA;IAAA,qCAAA;IAAA,qIAAA;IAAA,SAAA;IAAA,YAAA;IAAA,mCAAA;IAAA,+CAAA;IAAA,eAAA;IAAA,oCAAA;IAAA,OAAA;IAAA,mFAAA;IAAA,2EAAA;IAAA,mEAAA;IAAA,wDAAA;IAAA,+CAAA;IAAA,eAAA;IAAA,MAAA;IAAA,yEAAA;IAAA,wBAAA;IAAA,oDAAA;IAAA,UAAA;IAAA,kFAAA;IAAA,8CAAA;IAAA,QAAA;IAAA,QAAA;IAAA,OAAA;IAAA,+DAAA;IAAA,iBAAA;IAAA,aAAA;IAAA,oBAAA;IAAA,wDAAA;IAAA,gDAAA;IAAA,+CAAA;IAAA,qCAAA;IAAA,aAAA;IAAA,oCAAA;IAAA,QAAA;IAAA,kHAAA;IAAA,iBAAA;IAAA,oBAAA;IAAA,4EAAA;IAAA,gCAAA;IAAA,uBAAA;IAAA,oBAAA;IAAA,QAAA;IAAA,6BAAA;IAAA,mCAAA;IAAA,mCAAA;IAAA,YAAA;IAAA,8CAAA;IAAA,mBAAA;IAAA,yCAAA;IAAA,QAAA;IAAA,KAAA;IAAA,oGAAA;IAAA,wBAAA;IAAA,MAAA;IAAA,2FAAA;IAAA,SAAA;IAAA,YAAA;IAAA,mCAAA;IAAA,+CAAA;IAAA,eAAA;IAAA,sBAAA;IAAA,OAAA;IAAA,mFAAA;IAAA,2EAAA;IAAA,mEAAA;IAAA,yEAAA;IAAA,wDAAA;IAAA,iDAAA;IAAA,eAAA;IAAA,MAAA;IAAA,wBAAA;IAAA,oDAAA;IAAA,UAAA;IAAA,kFAAA;IAAA,8CAAA;IAAA,QAAA;IAAA,QAAA;IAAA,OAAA;IAAA,4DAAA;IAAA,iBAAA;IAAA,aAAA;IAAA,oBAAA;IAAA,wDAAA;IAAA,gDAAA;IAAA,+CAAA;IAAA,qCAAA;IAAA,aAAA;IAAA,QAAA;IAAA,iHAAA;IAAA,eAAA;IAAA,iDAAA;IAAA,8BAAA;IAAA,yFAAA;IAAA,4BAAA;IAAA,wDAAA;IAAA,gBAAA;IAAA,SAAA;IAAA,eAAA;IAAA,OAAA;IAAA,uCAAA;IAAA,sDAAA;IAAA,iBAAA;IAAA,6CAAA;IAAA,uCAAA;IAAA,gEAAA;IAAA,6DAAA;IAAA,gCAAA;IAAA,uBAAA;IAAA,0CAAA;IAAA,QAAA;IAAA,6BAAA;IAAA,mCAAA;IAAA,mCAAA;IAAA,YAAA;IAAA,8CAAA;IAAA,mBAAA;IAAA,yCAAA;IAAA,QAAA;IAAA,KAAA;IAAA,6CAAA;IAAA,8CAAA;IAAA,wEAAA;IAAA,0CAAA;IAAA,uBAAA;IAAA,YAAA;IAAA,mCAAA;IAAA,+CAAA;IAAA,eAAA;IAAA,sBAAA;IAAA,iBAAA;IAAA,2FAAA;IAAA,OAAA;IAAA,mFAAA;IAAA,2EAAA;IAAA,mEAAA;IAAA,yEAAA;IAAA,wBAAA;IAAA,oDAAA;IAAA,UAAA;IAAA,kFAAA;IAAA,8CAAA;IAAA,QAAA;IAAA,QAAA;IAAA,OAAA;IAAA,gEAAA;IAAA,iBAAA;IAAA,aAAA;IAAA,oBAAA;IAAA,wDAAA;IAAA,gDAAA;IAAA,+CAAA;IAAA,qCAAA;IAAA,aAAA;IAAA,QAAA;IAAA,yDAAA;IAAA,wCAAA;IAAA,0DAAA;IAAA,eAAA;IAAA,qDAAA;IAAA,8BAAA;IAAA,yFAAA;IAAA,4BAAA;IAAA,wDAAA;IAAA,gBAAA;IAAA,SAAA;IAAA,eAAA;IAAA,OAAA;IAAA,8FAAA;IAAA,0DAAA;IAAA,iBAAA;IAAA,6CAAA;IAAA,0BAAA;IAAA,2EAAA;IAAA,4EAAA;IAAA,gCAAA;IAAA,uBAAA;IAAA,6BAAA;IAAA,QAAA;IAAA,mEAAA;IAAA,6DAAA;IAAA,iBAAA;IAAA,aAAA;IAAA,oBAAA;IAAA,YAAA;IAAA,kBAAA;IAAA,oBAAA;IAAA,iBAAA;IAAA,wBAAA;IAAA,0BAAA;IAAA,wBAAA;IAAA,yBAAA;IAAA,+EAAA;IAAA,0DAAA;IAAA,qCAAA;IAAA,uBAAA;IAAA,UAAA;IAAA,WAAA;IAAA,SAAA;IAAA,QAAA;IAAA,iBAAA;IAAA,QAAA;IAAA,6BAAA;IAAA,mCAAA;IAAA,mCAAA;IAAA,yCAAA;IAAA,8CAAA;IAAA,mBAAA;IAAA,6CAAA;IAAA,QAAA;IAAA,0DAAA;IAAA,oCAAA;IAAA,2GAAA;IAAA,gBAAA;IAAA,UAAA;IAAA,8BAAA;IAAA,oBAAA;IAAA,oBAAA;IAAA,qDAAA;IAAA,uBAAA;IAAA,uBAAA;IAAA,uCAAA;IAAA,WAAA;IAAA,8DAAA;IAAA,sCAAA;IAAA,sCAAA;IAAA,eAAA;IAAA,iDAAA;IAAA,sBAAA;IAAA,gDAAA;IAAA,UAAA;IAAA,yCAAA;IAAA,iDAAA;IAAA,gDAAA;IAAA,0BAAA;IAAA,yDAAA;IAAA,KAAA;IAAA,wGAAA;IAAA,sGAAA;IAAA,KAAA;IAAA,8EAAA;IAAA,uCAAA;IAAA,2CAAA;IAAA,4CAAA;IAAA,iBAAA;IAAA,kDAAA;IAAA,oCAAA;IAAA,oBAAA;IAAA,oBAAA;IAAA,SAAA;IAAA,KAAA;IAAA,sCAAA;IAAA,2CAAA;IAAA,mCAAA;IAAA,wBAAA;IAAA,wBAAA;IAAA,oCAAA;IAAA,oBAAA;IAAA,oBAAA;IAAA,SAAA;IAAA,KAAA;IAAA,iBAAA;IAAA,kBAAA;IAAA,kCAAA;IAAA,iCAAA;IAAA,OAAA;IAAA,KAAA;IAAA,oDAAA;IAAA,8BAAA;IAAA,6BAAA;IAAA,uFAAA;IAAA,kBAAA;IAAA,0DAAA;IAAA,iBAAA;IAAA,iFAAA;IAAA,WAAA;IAAA,aAAA;IAAA,gBAAA;IAAA,SAAA;IAAA,SAAA;IAAA,iBAAA;IAAA,OAAA;IAAA,gDAAA;IAAA,iCAAA;IAAA,wGAAA;IAAA,aAAA;IAAA,OAAA;IAAA,gBAAA;IAAA,0BAAA;IAAA,KAAA;IAAA,0CAAA;IAAA,qFAAA;IAAA,gBAAA;IAAA,wDAAA;IAAA,eAAA;IAAA,+EAAA;IAAA,SAAA;IAAA,WAAA;IAAA,WAAA;IAAA,YAAA;IAAA,WAAA;IAAA,aAAA;IAAA,cAAA;IAAA,OAAA;IAAA,YAAA;IAAA,oCAAA;IAAA,iBAAA;IAAA,eAAA;IAAA,MAAA;IAAA,IAAA;IAohBA,8BAA8B,EAC7B,UAAA,EACA,MAAA,EACA,aAAA,EACA,KAAA,EACA,WAAA,EACA,aAAa,MAAA,EACb,UAAA,EACA,mBAAA,EACA,MAAA,EACD,EAUkD;QACjD,IAAI,YAAwE,CAAC,CAAA;QAC7E,IAAI,OAAO,QAAQ,UAAkD,CAAC,CAAA,EAAG;QACzE,MAAM,QAA8B,CAAC,CAAA;QAErC,IAAI,WAAW,MAAM;YACpB,MAAM,mBAAmB,OAAO,OAAA,CAAQ,YAAY,OAAO;YAC3D,YAAY,iBAAiB,GAAA,CAAI,CAChC,CAAC,KAAK,KAAK,CAAA,GAAA,CACN;oBACL,OAAO,MAAM,IAAA;oBACb,OAAO;oBACP,4PAAO,qBAAA,EAAmB,OAAmB,UAAU;oBACvD,oBAAoB,KAAA;oBACpB,QAAQ;oBACR,WAAW,CAAC,CAAA;gBACb,CAAA,CAAE;QACH,OAAO;YACN,MAAM,iBAAiB,OAAO,WAAA,CAC7B,OAAO,OAAA,CAAQ,YAAY,OAAO,EAAE,GAAA,CAAI,CAAC,CAAC,KAAK,KAAK,CAAA,GAAM;oBAAC;yQAAK,qBAAA,EAAmB,OAAO,UAAU,CAAC;iBAAC;YAGvG,IAAI,OAAO,KAAA,EAAO;gBACjB,MAAM,WAAW,OAAO,OAAO,KAAA,KAAU,aACtC,OAAO,KAAA,CAAM,yQAAgB,eAAA,CAAa,CAAC,KAC3C,OAAO,KAAA;gBACV,QAAQ,gBAAY,0QAAA,EAAuB,UAAU,UAAU;YAChE;YAEA,MAAM,kBAAsE,CAAC,CAAA;YAC7E,IAAI,kBAA4B,CAAC,CAAA;YAGjC,IAAI,OAAO,OAAA,EAAS;gBACnB,IAAI,gBAAgB;gBAEpB,KAAA,MAAW,CAAC,OAAO,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,OAAO,OAAO,EAAG;oBAC5D,IAAI,UAAU,KAAA,GAAW;wBACxB;oBACD;oBAEA,IAAI,SAAS,YAAY,OAAA,EAAS;wBACjC,IAAI,CAAC,iBAAiB,UAAU,MAAM;4BACrC,gBAAgB;wBACjB;wBACA,gBAAgB,IAAA,CAAK,KAAK;oBAC3B;gBACD;gBAEA,IAAI,gBAAgB,MAAA,GAAS,GAAG;oBAC/B,kBAAkB,gBACf,gBAAgB,MAAA,CAAO,CAAC,IAAM,OAAO,OAAA,EAAA,CAAU,CAAC,CAAA,KAAM,IAAI,IAC1D,OAAO,IAAA,CAAK,YAAY,OAAO,EAAE,MAAA,CAAO,CAAC,MAAQ,CAAC,gBAAgB,QAAA,CAAS,GAAG,CAAC;gBACnF;YACD,OAAO;gBAEN,kBAAkB,OAAO,IAAA,CAAK,YAAY,OAAO;YAClD;YAEA,KAAA,MAAW,SAAS,gBAAiB;gBACpC,MAAM,SAAS,YAAY,OAAA,CAAQ,KAAK,CAAA;gBACxC,gBAAgB,IAAA,CAAK;oBAAE,OAAO;oBAAO,OAAO;gBAAO,CAAC;YACrD;YAEA,IAAI,oBAIE,CAAC,CAAA;YAGP,IAAI,OAAO,IAAA,EAAM;gBAChB,oBAAoB,OAAO,OAAA,CAAQ,OAAO,IAAI,EAC5C,MAAA,CAAO,CAAC,QAAoE,CAAC,CAAC,KAAA,CAAM,CAAC,CAAC,EACtF,GAAA,CAAI,CAAC,CAAC,OAAO,WAAW,CAAA,GAAA,CAAO;wBAAE;wBAAO;wBAAa,UAAU,YAAY,SAAA,CAAU,KAAK,CAAA;oBAAG,CAAA,CAAE;YAClG;YAEA,IAAI;YAGJ,IAAI,OAAO,MAAA,EAAQ;gBAClB,SAAS,OAAO,OAAO,MAAA,KAAW,aAC/B,OAAO,MAAA,CAAO,gBAAgB;+QAAE,MAAA;gBAAI,CAAC,IACrC,OAAO,MAAA;gBACV,KAAA,MAAW,CAAC,OAAO,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,MAAM,EAAG;oBACpD,gBAAgB,IAAA,CAAK;wBACpB;wBACA,2PAAO,iCAAA,EAA8B,OAAO,UAAU;oBACvD,CAAC;gBACF;YACD;YAIA,KAAA,MAAW,EAAE,KAAA,EAAO,KAAA,CAAM,CAAA,IAAK,gBAAiB;gBAC/C,UAAU,IAAA,CAAK;oBACd,WAAO,uPAAA,EAAG,6PAAO,MAAA,CAAI,OAAO,IAAI,MAAM,UAAA,GAAa,YAAY,OAAA,CAAQ,KAAK,CAAA,CAAG,IAAA;oBAC/E;oBACA,6PAAO,KAAA,EAAG,yPAAO,SAAM,yPAAI,qBAAA,EAAmB,OAAO,UAAU,IAAI;oBACnE,oBAAoB,KAAA;oBACpB,QAAQ;oBACR,WAAW,CAAC,CAAA;gBACb,CAAC;YACF;YAEA,IAAI,cAAc,OAAO,OAAO,OAAA,KAAY,aACzC,OAAO,OAAA,CAAQ,yQAAgB,sBAAA,CAAoB,CAAC,KACpD,OAAO,OAAA,IAAW,CAAC,CAAA;YACtB,IAAI,CAAC,MAAM,OAAA,CAAQ,WAAW,GAAG;gBAChC,cAAc;oBAAC,WAAW;iBAAA;YAC3B;YACA,UAAU,YAAY,GAAA,CAAI,CAAC,iBAAiB;gBAC3C,0PAAI,KAAA,EAAG,gQAAc,SAAM,GAAG;oBAC7B,4PAAO,qBAAA,EAAmB,cAAc,UAAU;gBACnD;gBACA,WAAO,0QAAA,EAAuB,cAAc,UAAU;YACvD,CAAC;YAED,QAAQ,OAAO,KAAA;YACf,SAAS,OAAO,MAAA;YAGhB,KAAA,MACO,EACL,OAAO,qBAAA,EACP,aAAa,2BAAA,EACb,QAAA,EACD,IAAK,kBACJ;gBACD,MAAM,8QAAqB,oBAAA,EAAkB,QAAQ,eAAe,QAAQ;gBAC5E,MAAM,yQAAoB,qBAAA,EAAmB,SAAS,eAAe;gBACrE,MAAM,sBAAsB,aAAA,CAAc,iBAAiB,CAAA;gBAC3D,MAAM,qBAAqB,GAAG,UAAU,CAAA,CAAA,EAAI,qBAAqB,EAAA;gBACjE,MAAMC,0RAAS,MAAA,KACX,mBAAmB,MAAA,CAAO,GAAA,CAAI,CAACC,QAAO,oRACxC,KAAA,uPACC,qBAAA,EAAmB,mBAAmB,UAAA,CAAW,CAAC,CAAA,EAAI,kBAAkB,wPACxE,qBAAA,EAAmBA,QAAO,UAAU;gBAIvC,MAAM,gBAAgB,IAAA,CAAK,6BAAA,CAA8B;oBACxD;oBACA;oBACA;oBACA,OAAO,UAAA,CAAW,mBAAmB,CAAA;oBACrC,aAAa,MAAA,CAAO,mBAAmB,CAAA;oBACvC,mQAAa,KAAA,EAAG,+PAAU,MAAG,IACzB,gCAAgC,OAChC;wBAAE,OAAO;oBAAE,IACX;wBAAE,GAAG,2BAAA;wBAA6B,OAAO;oBAAE,IAC5C;oBACH,YAAY;oBACZ,QAAAD;oBACA,qBAAqB;gBACtB,CAAC;gBACD,MAAM,8PAAQ,MAAA,CAAA,wPAAM,MAAA,CAAI,UAAA,CAAW,kBAAkB,CAAC,CAAA,CAAA,uPAAI,OAAA,CAAI,UAAA,CAAW,MAAM,CAAC,CAAA,CAAA,CAAG,EAAA,CAAG,qBAAqB;gBAC3G,MAAM,IAAA,CAAK;oBACV,0PAAI,MAAA,CAAA,IAAA,CAAA;oBACJ,OAAO,wPAAI,WAAA,CAAS,cAAc,GAAA,EAAY,CAAC,GAAG,kBAAkB;oBACpE,OAAO;oBACP,UAAU;oBACV,SAAS;gBACV,CAAC;gBACD,UAAU,IAAA,CAAK;oBACd,OAAO;oBACP,OAAO;oBACP;oBACA,oBAAoB;oBACpB,QAAQ;oBACR,WAAW,cAAc,SAAA;gBAC1B,CAAC;YACF;QACD;QAEA,IAAI,UAAU,MAAA,KAAW,GAAG;YAC3B,MAAM,sPAAI,eAAA,CAAa;gBAAE,SAAS,CAAA,8BAAA,EAAiC,YAAY,MAAM,CAAA,IAAA,EAAO,UAAU,CAAA,EAAA,CAAA;YAAK,CAAC;QAC7G;QAEA,IAAI;QAEJ,wRAAQ,MAAA,EAAI,QAAQ,KAAK;QAEzB,IAAI,qBAAqB;YACxB,IAAI,8PAAQ,MAAA,CAAA,iBAAA,wPACX,MAAA,CAAI,IAAA,CACH,UAAU,GAAA,CAAI,CAAC,EAAE,OAAAC,MAAAA,EAAO,KAAA,EAAO,MAAA,CAAO,CAAA,GACrC,+PACG,MAAA,CAAA,EAAM,4PAAA,CAAI,UAAA,CAAW,GAAG,UAAU,CAAA,CAAA,EAAI,KAAK,EAAE,CAAC,CAAA,CAAA,wPAAI,MAAA,CAAI,UAAA,CAAW,MAAM,CAAC,CAAA,CAAA,yPACxE,KAAA,EAAGA,QAAO,4PAAA,CAAI,OAAO,IACrBA,OAAM,GAAA,GACNA,+PAEJ,MAAA,CAAA,EAAA,CAAA,EAEF,CAAA,CAAA;YACA,0PAAI,KAAA,EAAG,yQAAqB,QAAI,GAAG;gBAClC,8PAAQ,MAAA,CAAA,kBAAA,EAAwB,KAAK,CAAA,EACpC,QAAQ,MAAA,GAAS,0PAAI,MAAA,CAAA,UAAA,wPAAgB,MAAA,CAAI,IAAA,CAAK,SAAS,4PAAA,CAAA,EAAA,CAAO,CAAC,CAAA,CAAA,GAAK,KAAA,CACrE,CAAA,cAAA,CAAA;YAED;YACA,MAAM,kBAAkB;gBAAC;oBACxB,OAAO;oBACP,OAAO;oBACP,OAAO,MAAM,EAAA,CAAG,MAAM;oBACtB,QAAQ;oBACR,oBAAoB,YAAY,MAAA;oBAChC;gBACD,CAAC;aAAA;YAED,MAAM,gBAAgB,UAAU,KAAA,KAAa,WAAW,KAAA,KAAa,QAAQ,MAAA,GAAS;YAEtF,IAAI,eAAe;gBAClB,SAAS,IAAA,CAAK,gBAAA,CAAiB;oBAC9B,4PAAO,eAAA,EAAa,OAAO,UAAU;oBACrC,QAAQ,CAAC;oBACT,YAAY;wBAAC;4BACZ,MAAM,CAAC,CAAA;4BACP,OAAO,4PAAA,CAAI,GAAA,CAAI,GAAG;wBACnB,CAAC;qBAAA;oBACD;oBACA;oBACA;oBACA;oBACA,cAAc,CAAC,CAAA;gBAChB,CAAC;gBAED,QAAQ,KAAA;gBACR,QAAQ,KAAA;gBACR,SAAS,KAAA;gBACT,UAAU,CAAC,CAAA;YACZ,OAAO;gBACN,8PAAS,eAAA,EAAa,OAAO,UAAU;YACxC;YAEA,SAAS,IAAA,CAAK,gBAAA,CAAiB;gBAC9B,WAAO,uPAAA,EAAG,uQAAQ,UAAO,IAAI,SAAS,wPAAI,WAAA,CAAS,QAAQ,CAAC,GAAG,UAAU;gBACzE,QAAQ,CAAC;gBACT,YAAY,gBAAgB,GAAA,CAAI,CAAC,EAAE,OAAAA,MAAAA,CAAM,CAAA,GAAA,CAAO;wBAC/C,MAAM,CAAC,CAAA;wBACP,6PAAO,KAAA,EAAGA,yPAAO,UAAM,yPAAI,qBAAA,EAAmBA,QAAO,UAAU,IAAIA;oBACpE,CAAA,CAAE;gBACF;gBACA;gBACA;gBACA;gBACA;gBACA,cAAc,CAAC,CAAA;YAChB,CAAC;QACF,OAAO;YACN,SAAS,IAAA,CAAK,gBAAA,CAAiB;gBAC9B,4PAAO,eAAA,EAAa,OAAO,UAAU;gBACrC,QAAQ,CAAC;gBACT,YAAY,UAAU,GAAA,CAAI,CAAC,EAAE,KAAA,CAAM,CAAA,GAAA,CAAO;wBACzC,MAAM,CAAC,CAAA;wBACP,6PAAO,KAAA,EAAG,OAAO,2PAAM,yPAAI,qBAAA,EAAmB,OAAO,UAAU,IAAI;oBACpE,CAAA,CAAE;gBACF;gBACA;gBACA;gBACA;gBACA;gBACA,cAAc,CAAC,CAAA;YAChB,CAAC;QACF;QAEA,OAAO;YACN,YAAY,YAAY,MAAA;YACxB,KAAK;YACL;QACD;IACD;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/query-builders/query-builder.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, SQLWrapper } from '~/sql/sql.ts';\nimport { WithSubquery } from '~/subquery.ts';\nimport type { PgColumn } from '../columns/index.ts';\nimport type { WithSubqueryWithSelection } from '../subquery.ts';\nimport { PgSelectBuilder } from './select.ts';\nimport type { SelectedFields } from './select.types.ts';\n\nexport class QueryBuilder {\n\tstatic readonly [entityKind]: string = 'PgQueryBuilder';\n\n\tprivate dialect: PgDialect | undefined;\n\n\t$with<TAlias extends string>(alias: TAlias) {\n\t\tconst queryBuilder = this;\n\n\t\treturn {\n\t\t\tas<TSelection extends ColumnsSelection>(\n\t\t\t\tqb: TypedQueryBuilder<TSelection> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelection>),\n\t\t\t): WithSubqueryWithSelection<TSelection, TAlias> {\n\t\t\t\tif (typeof qb === 'function') {\n\t\t\t\t\tqb = qb(queryBuilder);\n\t\t\t\t}\n\n\t\t\t\treturn new Proxy(\n\t\t\t\t\tnew WithSubquery(qb.getSQL(), qb.getSelectedFields() as SelectedFields, alias, true),\n\t\t\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t\t\t) as WithSubqueryWithSelection<TSelection, TAlias>;\n\t\t\t},\n\t\t};\n\t}\n\n\twith(...queries: WithSubquery[]) {\n\t\tconst self = this;\n\n\t\tfunction select(): PgSelectBuilder<undefined, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(\n\t\t\tfields?: TSelection,\n\t\t): PgSelectBuilder<TSelection | undefined, 'qb'> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\twithList: queries,\n\t\t\t});\n\t\t}\n\n\t\tfunction selectDistinct(): PgSelectBuilder<undefined, 'qb'>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection, 'qb'>;\n\t\tfunction selectDistinct(fields?: SelectedFields): PgSelectBuilder<SelectedFields | undefined, 'qb'> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\tdistinct: true,\n\t\t\t});\n\t\t}\n\n\t\tfunction selectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined, 'qb'>;\n\t\tfunction selectDistinctOn<TSelection extends SelectedFields>(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields: TSelection,\n\t\t): PgSelectBuilder<TSelection, 'qb'>;\n\t\tfunction selectDistinctOn(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields?: SelectedFields,\n\t\t): PgSelectBuilder<SelectedFields | undefined, 'qb'> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\tdistinct: { on },\n\t\t\t});\n\t\t}\n\n\t\treturn { select, selectDistinct, selectDistinctOn };\n\t}\n\n\tselect(): PgSelectBuilder<undefined, 'qb'>;\n\tselect<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection, 'qb'>;\n\tselect<TSelection extends SelectedFields>(fields?: TSelection): PgSelectBuilder<TSelection | undefined, 'qb'> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t});\n\t}\n\n\tselectDistinct(): PgSelectBuilder<undefined>;\n\tselectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\tselectDistinct(fields?: SelectedFields): PgSelectBuilder<SelectedFields | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t\tdistinct: true,\n\t\t});\n\t}\n\n\tselectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined>;\n\tselectDistinctOn<TSelection extends SelectedFields>(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields: TSelection,\n\t): PgSelectBuilder<TSelection>;\n\tselectDistinctOn(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields?: SelectedFields,\n\t): PgSelectBuilder<SelectedFields | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t\tdistinct: { on },\n\t\t});\n\t}\n\n\t// Lazy load dialect to avoid circular dependency\n\tprivate getDialect() {\n\t\tif (!this.dialect) {\n\t\t\tthis.dialect = new PgDialect();\n\t\t}\n\n\t\treturn this.dialect;\n\t}\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;AAE1B,SAAS,6BAA6B;AAEtC,SAAS,oBAAoB;AAG7B,SAAS,uBAAuB;;;;;;AAGzB,MAAM,aAAa;IACzB,OAAA,mPAAiB,aAAU,CAAA,GAAY,iBAAA;IAE/B,QAAA;IAER,MAA6B,KAAA,EAAe;QAC3C,MAAM,eAAe,IAAA;QAErB,OAAO;YACN,IACC,EAAA,EACgD;gBAChD,IAAI,OAAO,OAAO,YAAY;oBAC7B,KAAK,GAAG,YAAY;gBACrB;gBAEA,OAAO,IAAI,MACV,wPAAI,eAAA,CAAa,GAAG,MAAA,CAAO,GAAG,GAAG,iBAAA,CAAkB,GAAqB,OAAO,IAAI,GACnF,kQAAI,wBAAA,CAAsB;oBAAE;oBAAO,oBAAoB;oBAAS,aAAa;gBAAQ,CAAC;YAExF;QACD;IACD;IAEA,KAAA,GAAQ,OAAA,EAAyB;QAChC,MAAM,OAAO,IAAA;QAIb,SAAS,OACR,MAAA,EACgD;YAChD,OAAO,yRAAI,kBAAA,CAAgB;gBAC1B,QAAQ,UAAU,KAAA;gBAClB,SAAS,KAAA;gBACT,SAAS,KAAK,UAAA,CAAW;gBACzB,UAAU;YACX,CAAC;QACF;QAIA,SAAS,eAAe,MAAA,EAA4E;YACnG,OAAO,yRAAI,kBAAA,CAAgB;gBAC1B,QAAQ,UAAU,KAAA;gBAClB,SAAS,KAAA;gBACT,SAAS,KAAK,UAAA,CAAW;gBACzB,UAAU;YACX,CAAC;QACF;QAOA,SAAS,iBACR,EAAA,EACA,MAAA,EACoD;YACpD,OAAO,yRAAI,kBAAA,CAAgB;gBAC1B,QAAQ,UAAU,KAAA;gBAClB,SAAS,KAAA;gBACT,SAAS,KAAK,UAAA,CAAW;gBACzB,UAAU;oBAAE;gBAAG;YAChB,CAAC;QACF;QAEA,OAAO;YAAE;YAAQ;YAAgB;QAAiB;IACnD;IAIA,OAA0C,MAAA,EAAoE;QAC7G,OAAO,yRAAI,kBAAA,CAAgB;YAC1B,QAAQ,UAAU,KAAA;YAClB,SAAS,KAAA;YACT,SAAS,IAAA,CAAK,UAAA,CAAW;QAC1B,CAAC;IACF;IAIA,eAAe,MAAA,EAAsE;QACpF,OAAO,yRAAI,kBAAA,CAAgB;YAC1B,QAAQ,UAAU,KAAA;YAClB,SAAS,KAAA;YACT,SAAS,IAAA,CAAK,UAAA,CAAW;YACzB,UAAU;QACX,CAAC;IACF;IAOA,iBACC,EAAA,EACA,MAAA,EAC8C;QAC9C,OAAO,yRAAI,kBAAA,CAAgB;YAC1B,QAAQ,UAAU,KAAA;YAClB,SAAS,KAAA;YACT,SAAS,IAAA,CAAK,UAAA,CAAW;YACzB,UAAU;gBAAE;YAAG;QAChB,CAAC;IACF;IAAA,iDAAA;IAGQ,aAAa;QACpB,IAAI,CAAC,IAAA,CAAK,OAAA,EAAS;YAClB,IAAA,CAAK,OAAA,GAAU,qQAAI,YAAA,CAAU;QAC9B;QAEA,OAAO,IAAA,CAAK,OAAA;IACb;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5402, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/query-builders/query.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport {\n\ttype BuildQueryResult,\n\ttype BuildRelationalQueryResult,\n\ttype DBQueryConfig,\n\tmapRelationalRow,\n\ttype TableRelationalConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Query, QueryWithTypings, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\nimport type { KnownKeysOnly } from '~/utils.ts';\nimport type { PgDialect } from '../dialect.ts';\nimport type { PgPreparedQuery, PgSession, PreparedQueryConfig } from '../session.ts';\nimport type { PgTable } from '../table.ts';\n\nexport class RelationalQueryBuilder<TSchema extends TablesRelationalConfig, T<PERSON>ields extends TableRelationalConfig> {\n\tstatic readonly [entityKind]: string = 'PgRelationalQueryBuilder';\n\n\tconstructor(\n\t\tprivate fullSchema: Record<string, unknown>,\n\t\tprivate schema: TSchema,\n\t\tprivate tableNamesMap: Record<string, string>,\n\t\tprivate table: PgTable,\n\t\tprivate tableConfig: TableRelationalConfig,\n\t\tprivate dialect: PgDialect,\n\t\tprivate session: PgSession,\n\t) {}\n\n\tfindMany<TConfig extends DBQueryConfig<'many', true, TSchema, TFields>>(\n\t\tconfig?: KnownKeysOnly<TConfig, DBQueryConfig<'many', true, TSchema, TFields>>,\n\t): PgRelationalQuery<BuildQueryResult<TSchema, TFields, TConfig>[]> {\n\t\treturn new PgRelationalQuery(\n\t\t\tthis.fullSchema,\n\t\t\tthis.schema,\n\t\t\tthis.tableNamesMap,\n\t\t\tthis.table,\n\t\t\tthis.tableConfig,\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tconfig ? (config as DBQueryConfig<'many', true>) : {},\n\t\t\t'many',\n\t\t);\n\t}\n\n\tfindFirst<TSelection extends Omit<DBQueryConfig<'many', true, TSchema, TFields>, 'limit'>>(\n\t\tconfig?: KnownKeysOnly<TSelection, Omit<DBQueryConfig<'many', true, TSchema, TFields>, 'limit'>>,\n\t): PgRelationalQuery<BuildQueryResult<TSchema, TFields, TSelection> | undefined> {\n\t\treturn new PgRelationalQuery(\n\t\t\tthis.fullSchema,\n\t\t\tthis.schema,\n\t\t\tthis.tableNamesMap,\n\t\t\tthis.table,\n\t\t\tthis.tableConfig,\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tconfig ? { ...(config as DBQueryConfig<'many', true> | undefined), limit: 1 } : { limit: 1 },\n\t\t\t'first',\n\t\t);\n\t}\n}\n\nexport class PgRelationalQuery<TResult> extends QueryPromise<TResult>\n\timplements RunnableQuery<TResult, 'pg'>, SQLWrapper\n{\n\tstatic readonly [entityKind]: string = 'PgRelationalQuery';\n\n\tdeclare readonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly result: TResult;\n\t};\n\n\tconstructor(\n\t\tprivate fullSchema: Record<string, unknown>,\n\t\tprivate schema: TablesRelationalConfig,\n\t\tprivate tableNamesMap: Record<string, string>,\n\t\tprivate table: PgTable,\n\t\tprivate tableConfig: TableRelationalConfig,\n\t\tprivate dialect: PgDialect,\n\t\tprivate session: PgSession,\n\t\tprivate config: DBQueryConfig<'many', true> | true,\n\t\tprivate mode: 'many' | 'first',\n\t) {\n\t\tsuper();\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgPreparedQuery<PreparedQueryConfig & { execute: TResult }> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\tconst { query, builtQuery } = this._toSQL();\n\n\t\t\treturn this.session.prepareQuery<PreparedQueryConfig & { execute: TResult }>(\n\t\t\t\tbuiltQuery,\n\t\t\t\tundefined,\n\t\t\t\tname,\n\t\t\t\ttrue,\n\t\t\t\t(rawRows, mapColumnValue) => {\n\t\t\t\t\tconst rows = rawRows.map((row) =>\n\t\t\t\t\t\tmapRelationalRow(this.schema, this.tableConfig, row, query.selection, mapColumnValue)\n\t\t\t\t\t);\n\t\t\t\t\tif (this.mode === 'first') {\n\t\t\t\t\t\treturn rows[0] as TResult;\n\t\t\t\t\t}\n\t\t\t\t\treturn rows as TResult;\n\t\t\t\t},\n\t\t\t);\n\t\t});\n\t}\n\n\tprepare(name: string): PgPreparedQuery<PreparedQueryConfig & { execute: TResult }> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate _getQuery() {\n\t\treturn this.dialect.buildRelationalQueryWithoutPK({\n\t\t\tfullSchema: this.fullSchema,\n\t\t\tschema: this.schema,\n\t\t\ttableNamesMap: this.tableNamesMap,\n\t\t\ttable: this.table,\n\t\t\ttableConfig: this.tableConfig,\n\t\t\tqueryConfig: this.config,\n\t\t\ttableAlias: this.tableConfig.tsName,\n\t\t});\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this._getQuery().sql as SQL;\n\t}\n\n\tprivate _toSQL(): { query: BuildRelationalQueryResult; builtQuery: QueryWithTypings } {\n\t\tconst query = this._getQuery();\n\n\t\tconst builtQuery = this.dialect.sqlToQuery(query.sql as SQL);\n\n\t\treturn { query, builtQuery };\n\t}\n\n\ttoSQL(): Query {\n\t\treturn this._toSQL().builtQuery;\n\t}\n\n\toverride execute(): Promise<TResult> {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute();\n\t\t});\n\t}\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;AAC7B;AAUA,SAAS,cAAc;;;;;AAMhB,MAAM,uBAAsG;IAGlH,YACS,UAAA,EACA,MAAA,EACA,aAAA,EACA,KAAA,EACA,WAAA,EACA,OAAA,EACA,OAAA,CACP;QAPO,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,aAAA,GAAA;QACA,IAAA,CAAA,KAAA,GAAA;QACA,IAAA,CAAA,WAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;IACN;IAVH,OAAA,mPAAiB,aAAU,CAAA,GAAY,2BAAA;IAYvC,SACC,MAAA,EACmE;QACnE,OAAO,IAAI,kBACV,IAAA,CAAK,UAAA,EACL,IAAA,CAAK,MAAA,EACL,IAAA,CAAK,aAAA,EACL,IAAA,CAAK,KAAA,EACL,IAAA,CAAK,WAAA,EACL,IAAA,CAAK,OAAA,EACL,IAAA,CAAK,OAAA,EACL,SAAU,SAAyC,CAAC,GACpD;IAEF;IAEA,UACC,MAAA,EACgF;QAChF,OAAO,IAAI,kBACV,IAAA,CAAK,UAAA,EACL,IAAA,CAAK,MAAA,EACL,IAAA,CAAK,aAAA,EACL,IAAA,CAAK,KAAA,EACL,IAAA,CAAK,WAAA,EACL,IAAA,CAAK,OAAA,EACL,IAAA,CAAK,OAAA,EACL,SAAS;YAAE,GAAI,MAAA;YAAoD,OAAO;QAAE,IAAI;YAAE,OAAO;QAAE,GAC3F;IAEF;AACD;AAEO,MAAM,sRAAmC,eAAA,CAEhD;IAQC,YACS,UAAA,EACA,MAAA,EACA,aAAA,EACA,KAAA,EACA,WAAA,EACA,OAAA,EACA,OAAA,EACA,MAAA,EACA,IAAA,CACP;QACD,KAAA,CAAM;QAVE,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,aAAA,GAAA;QACA,IAAA,CAAA,KAAA,GAAA;QACA,IAAA,CAAA,WAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,IAAA,GAAA;IAGT;IAnBA,OAAA,mPAAiB,aAAU,CAAA,GAAY,oBAAA;IAAA,cAAA,GAsBvC,SAAS,IAAA,EAA4E;QACpF,0PAAO,SAAA,CAAO,eAAA,CAAgB,wBAAwB,MAAM;YAC3D,MAAM,EAAE,KAAA,EAAO,UAAA,CAAW,CAAA,GAAI,IAAA,CAAK,MAAA,CAAO;YAE1C,OAAO,IAAA,CAAK,OAAA,CAAQ,YAAA,CACnB,YACA,KAAA,GACA,MACA,MACA,CAAC,SAAS,mBAAmB;gBAC5B,MAAM,OAAO,QAAQ,GAAA,CAAI,CAAC,MACzB,4QAAA,EAAiB,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,WAAA,EAAa,KAAK,MAAM,SAAA,EAAW,cAAc;gBAErF,IAAI,IAAA,CAAK,IAAA,KAAS,SAAS;oBAC1B,OAAO,IAAA,CAAK,CAAC,CAAA;gBACd;gBACA,OAAO;YACR;QAEF,CAAC;IACF;IAEA,QAAQ,IAAA,EAA2E;QAClF,OAAO,IAAA,CAAK,QAAA,CAAS,IAAI;IAC1B;IAEQ,YAAY;QACnB,OAAO,IAAA,CAAK,OAAA,CAAQ,6BAAA,CAA8B;YACjD,YAAY,IAAA,CAAK,UAAA;YACjB,QAAQ,IAAA,CAAK,MAAA;YACb,eAAe,IAAA,CAAK,aAAA;YACpB,OAAO,IAAA,CAAK,KAAA;YACZ,aAAa,IAAA,CAAK,WAAA;YAClB,aAAa,IAAA,CAAK,MAAA;YAClB,YAAY,IAAA,CAAK,WAAA,CAAY,MAAA;QAC9B,CAAC;IACF;IAAA,cAAA,GAGA,SAAc;QACb,OAAO,IAAA,CAAK,SAAA,CAAU,EAAE,GAAA;IACzB;IAEQ,SAA8E;QACrF,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU;QAE7B,MAAM,aAAa,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,MAAM,GAAU;QAE3D,OAAO;YAAE;YAAO;QAAW;IAC5B;IAEA,QAAe;QACd,OAAO,IAAA,CAAK,MAAA,CAAO,EAAE,UAAA;IACtB;IAES,UAA4B;QACpC,0PAAO,SAAA,CAAO,eAAA,CAAgB,qBAAqB,MAAM;YACxD,OAAO,IAAA,CAAK,QAAA,CAAS,EAAE,OAAA,CAAQ;QAChC,CAAC;IACF;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5505, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/query-builders/raw.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport type { Query, SQL, SQLWrapper } from '~/sql/sql.ts';\n\nexport interface PgRaw<TResult> extends QueryPromise<TResult>, RunnableQuery<TResult, 'pg'>, SQLWrapper {}\n\nexport class PgRaw<TResult> extends QueryPromise<TResult>\n\timplements RunnableQuery<TResult, 'pg'>, SQLWrapper, PreparedQuery\n{\n\tstatic readonly [entityKind]: string = 'PgRaw';\n\n\tdeclare readonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly result: TResult;\n\t};\n\n\tconstructor(\n\t\tpublic execute: () => Promise<TResult>,\n\t\tprivate sql: SQL,\n\t\tprivate query: Query,\n\t\tprivate mapBatchResult: (result: unknown) => unknown,\n\t) {\n\t\tsuper();\n\t}\n\n\t/** @internal */\n\tgetSQL() {\n\t\treturn this.sql;\n\t}\n\n\tgetQuery() {\n\t\treturn this.query;\n\t}\n\n\tmapResult(result: unknown, isFromBatch?: boolean) {\n\t\treturn isFromBatch ? this.mapBatchResult(result) : result;\n\t}\n\n\t_prepare(): PreparedQuery {\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode() {\n\t\treturn false;\n\t}\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;;;AAOtB,MAAM,0QAAuB,eAAA,CAEpC;IAQC,YACQ,OAAA,EACC,GAAA,EACA,KAAA,EACA,cAAA,CACP;QACD,KAAA,CAAM;QALC,IAAA,CAAA,OAAA,GAAA;QACC,IAAA,CAAA,GAAA,GAAA;QACA,IAAA,CAAA,KAAA,GAAA;QACA,IAAA,CAAA,cAAA,GAAA;IAGT;IAdA,OAAA,mPAAiB,aAAU,CAAA,GAAY,QAAA;IAAA,cAAA,GAiBvC,SAAS;QACR,OAAO,IAAA,CAAK,GAAA;IACb;IAEA,WAAW;QACV,OAAO,IAAA,CAAK,KAAA;IACb;IAEA,UAAU,MAAA,EAAiB,WAAA,EAAuB;QACjD,OAAO,cAAc,IAAA,CAAK,cAAA,CAAe,MAAM,IAAI;IACpD;IAEA,WAA0B;QACzB,OAAO,IAAA;IACR;IAAA,cAAA,GAGA,wBAAwB;QACvB,OAAO;IACR;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5545, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/query-builders/refresh-materialized-view.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgMaterializedView } from '~/pg-core/view.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface PgRefreshMaterializedView<TQueryResult extends PgQueryResultHKT>\n\textends\n\t\tQueryPromise<PgQueryResultKind<TQueryResult, never>>,\n\t\tRunnableQuery<PgQueryResultKind<TQueryResult, never>, 'pg'>,\n\t\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly result: PgQueryResultKind<TQueryResult, never>;\n\t};\n}\n\nexport class PgRefreshMaterializedView<TQueryResult extends PgQueryResultHKT>\n\textends QueryPromise<PgQueryResultKind<TQueryResult, never>>\n\timplements RunnableQuery<PgQueryResultKind<TQueryResult, never>, 'pg'>, SQLWrapper\n{\n\tstatic readonly [entityKind]: string = 'PgRefreshMaterializedView';\n\n\tprivate config: {\n\t\tview: PgMaterializedView;\n\t\tconcurrently?: boolean;\n\t\twithNoData?: boolean;\n\t};\n\n\tconstructor(\n\t\tview: PgMaterializedView,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t) {\n\t\tsuper();\n\t\tthis.config = { view };\n\t}\n\n\tconcurrently(): this {\n\t\tif (this.config.withNoData !== undefined) {\n\t\t\tthrow new Error('Cannot use concurrently and withNoData together');\n\t\t}\n\t\tthis.config.concurrently = true;\n\t\treturn this;\n\t}\n\n\twithNoData(): this {\n\t\tif (this.config.concurrently !== undefined) {\n\t\t\tthrow new Error('Cannot use concurrently and withNoData together');\n\t\t}\n\t\tthis.config.withNoData = true;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildRefreshMaterializedViewQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgPreparedQuery<\n\t\tPreparedQueryConfig & {\n\t\t\texecute: PgQueryResultKind<TQueryResult, never>;\n\t\t}\n\t> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\treturn this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()), undefined, name, true);\n\t\t});\n\t}\n\n\tprepare(name: string): PgPreparedQuery<\n\t\tPreparedQueryConfig & {\n\t\t\texecute: PgQueryResultKind<TQueryResult, never>;\n\t\t}\n\t> {\n\t\treturn this._prepare(name);\n\t}\n\n\texecute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues);\n\t\t});\n\t};\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,kBAAkB;AAU3B,SAAS,oBAAoB;AAG7B,SAAS,cAAc;;;;AAehB,MAAM,8RACJ,eAAA,CAET;IASC,YACC,IAAA,EACQ,OAAA,EACA,OAAA,CACP;QACD,KAAA,CAAM;QAHE,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QAGR,IAAA,CAAK,MAAA,GAAS;YAAE;QAAK;IACtB;IAfA,OAAA,mPAAiB,aAAU,CAAA,GAAY,4BAAA;IAE/B,OAAA;IAeR,eAAqB;QACpB,IAAI,IAAA,CAAK,MAAA,CAAO,UAAA,KAAe,KAAA,GAAW;YACzC,MAAM,IAAI,MAAM,iDAAiD;QAClE;QACA,IAAA,CAAK,MAAA,CAAO,YAAA,GAAe;QAC3B,OAAO,IAAA;IACR;IAEA,aAAmB;QAClB,IAAI,IAAA,CAAK,MAAA,CAAO,YAAA,KAAiB,KAAA,GAAW;YAC3C,MAAM,IAAI,MAAM,iDAAiD;QAClE;QACA,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;QACzB,OAAO,IAAA;IACR;IAAA,cAAA,GAGA,SAAc;QACb,OAAO,IAAA,CAAK,OAAA,CAAQ,iCAAA,CAAkC,IAAA,CAAK,MAAM;IAClE;IAEA,QAAe;QACd,MAAM,EAAE,SAAS,QAAA,EAAU,GAAG,KAAK,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,IAAA,CAAK,MAAA,CAAO,CAAC;QAC5E,OAAO;IACR;IAAA,cAAA,GAGA,SAAS,IAAA,EAIP;QACD,0PAAO,SAAA,CAAO,eAAA,CAAgB,wBAAwB,MAAM;YAC3D,OAAO,IAAA,CAAK,OAAA,CAAQ,YAAA,CAAa,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,IAAA,CAAK,MAAA,CAAO,CAAC,GAAG,KAAA,GAAW,MAAM,IAAI;QAC/F,CAAC;IACF;IAEA,QAAQ,IAAA,EAIN;QACD,OAAO,IAAA,CAAK,QAAA,CAAS,IAAI;IAC1B;IAEA,UAAkD,CAAC,sBAAsB;QACxE,0PAAO,SAAA,CAAO,eAAA,CAAgB,qBAAqB,MAAM;YACxD,OAAO,IAAA,CAAK,QAAA,CAAS,EAAE,OAAA,CAAQ,iBAAiB;QACjD,CAAC;IACF,EAAA;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/db.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport {\n\tPgDeleteBase,\n\tPgInsertBuilder,\n\tPgSelectBuilder,\n\tPgUpdateBuilder,\n\tQueryBuilder,\n} from '~/pg-core/query-builders/index.ts';\nimport type {\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPgTransaction,\n\tPgTransactionConfig,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgTable } from '~/pg-core/table.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { ExtractTablesWithRelations, RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, SQLWrapper } from '~/sql/sql.ts';\nimport { WithSubquery } from '~/subquery.ts';\nimport type { DrizzleTypeError } from '~/utils.ts';\nimport type { PgColumn } from './columns/index.ts';\nimport { RelationalQueryBuilder } from './query-builders/query.ts';\nimport { PgRaw } from './query-builders/raw.ts';\nimport { PgRefreshMaterializedView } from './query-builders/refresh-materialized-view.ts';\nimport type { SelectedFields } from './query-builders/select.types.ts';\nimport type { WithSubqueryWithSelection } from './subquery.ts';\nimport type { PgMaterializedView } from './view.ts';\n\nexport class PgDatabase<\n\tTQueryResult extends PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = ExtractTablesWithRelations<TFullSchema>,\n> {\n\tstatic readonly [entityKind]: string = 'PgDatabase';\n\n\tdeclare readonly _: {\n\t\treadonly schema: TSchema | undefined;\n\t\treadonly fullSchema: TFullSchema;\n\t\treadonly tableNamesMap: Record<string, string>;\n\t\treadonly session: PgSession<TQueryResult, TFullSchema, TSchema>;\n\t};\n\n\tquery: TFullSchema extends Record<string, never>\n\t\t? DrizzleTypeError<'Seems like the schema generic is missing - did you forget to add it to your DB type?'>\n\t\t: {\n\t\t\t[K in keyof TSchema]: RelationalQueryBuilder<TSchema, TSchema[K]>;\n\t\t};\n\n\tconstructor(\n\t\t/** @internal */\n\t\treadonly dialect: PgDialect,\n\t\t/** @internal */\n\t\treadonly session: PgSession<any, any, any>,\n\t\tschema: RelationalSchemaConfig<TSchema> | undefined,\n\t) {\n\t\tthis._ = schema\n\t\t\t? {\n\t\t\t\tschema: schema.schema,\n\t\t\t\tfullSchema: schema.fullSchema as TFullSchema,\n\t\t\t\ttableNamesMap: schema.tableNamesMap,\n\t\t\t\tsession,\n\t\t\t}\n\t\t\t: {\n\t\t\t\tschema: undefined,\n\t\t\t\tfullSchema: {} as TFullSchema,\n\t\t\t\ttableNamesMap: {},\n\t\t\t\tsession,\n\t\t\t};\n\t\tthis.query = {} as typeof this['query'];\n\t\tif (this._.schema) {\n\t\t\tfor (const [tableName, columns] of Object.entries(this._.schema)) {\n\t\t\t\t(this.query as PgDatabase<TQueryResult, Record<string, any>>['query'])[tableName] = new RelationalQueryBuilder(\n\t\t\t\t\tschema!.fullSchema,\n\t\t\t\t\tthis._.schema,\n\t\t\t\t\tthis._.tableNamesMap,\n\t\t\t\t\tschema!.fullSchema[tableName] as PgTable,\n\t\t\t\t\tcolumns,\n\t\t\t\t\tdialect,\n\t\t\t\t\tsession,\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Creates a subquery that defines a temporary named result set as a CTE.\n\t *\n\t * It is useful for breaking down complex queries into simpler parts and for reusing the result set in subsequent parts of the query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n\t *\n\t * @param alias The alias for the subquery.\n\t *\n\t * Failure to provide an alias will result in a DrizzleTypeError, preventing the subquery from being referenced in other queries.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Create a subquery with alias 'sq' and use it in the select query\n\t * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n\t *\n\t * const result = await db.with(sq).select().from(sq);\n\t * ```\n\t *\n\t * To select arbitrary SQL values as fields in a CTE and reference them in other CTEs or in the main query, you need to add aliases to them:\n\t *\n\t * ```ts\n\t * // Select an arbitrary SQL value as a field in a CTE and reference it in the main query\n\t * const sq = db.$with('sq').as(db.select({\n\t *   name: sql<string>`upper(${users.name})`.as('name'),\n\t * })\n\t * .from(users));\n\t *\n\t * const result = await db.with(sq).select({ name: sq.name }).from(sq);\n\t * ```\n\t */\n\t$with<TAlias extends string>(alias: TAlias) {\n\t\treturn {\n\t\t\tas<TSelection extends ColumnsSelection>(\n\t\t\t\tqb: TypedQueryBuilder<TSelection> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelection>),\n\t\t\t): WithSubqueryWithSelection<TSelection, TAlias> {\n\t\t\t\tif (typeof qb === 'function') {\n\t\t\t\t\tqb = qb(new QueryBuilder());\n\t\t\t\t}\n\n\t\t\t\treturn new Proxy(\n\t\t\t\t\tnew WithSubquery(qb.getSQL(), qb.getSelectedFields() as SelectedFields, alias, true),\n\t\t\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t\t\t) as WithSubqueryWithSelection<TSelection, TAlias>;\n\t\t\t},\n\t\t};\n\t}\n\n\t/**\n\t * Incorporates a previously defined CTE (using `$with`) into the main query.\n\t *\n\t * This method allows the main query to reference a temporary named result set.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n\t *\n\t * @param queries The CTEs to incorporate into the main query.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Define a subquery 'sq' as a CTE using $with\n\t * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n\t *\n\t * // Incorporate the CTE 'sq' into the main query and select from it\n\t * const result = await db.with(sq).select().from(sq);\n\t * ```\n\t */\n\twith(...queries: WithSubquery[]) {\n\t\tconst self = this;\n\n\t\t/**\n\t\t * Creates a select query.\n\t\t *\n\t\t * Calling this method with no arguments will select all columns from the table. Pass a selection object to specify the columns you want to select.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select}\n\t\t *\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Select all columns and all rows from the 'cars' table\n\t\t * const allCars: Car[] = await db.select().from(cars);\n\t\t *\n\t\t * // Select specific columns and all rows from the 'cars' table\n\t\t * const carsIdsAndBrands: { id: number; brand: string }[] = await db.select({\n\t\t *   id: cars.id,\n\t\t *   brand: cars.brand\n\t\t * })\n\t\t *   .from(cars);\n\t\t * ```\n\t\t *\n\t\t * Like in SQL, you can use arbitrary expressions as selection fields, not just table columns:\n\t\t *\n\t\t * ```ts\n\t\t * // Select specific columns along with expression and all rows from the 'cars' table\n\t\t * const carsIdsAndLowerNames: { id: number; lowerBrand: string }[] = await db.select({\n\t\t *   id: cars.id,\n\t\t *   lowerBrand: sql<string>`lower(${cars.brand})`,\n\t\t * })\n\t\t *   .from(cars);\n\t\t * ```\n\t\t */\n\t\tfunction select(): PgSelectBuilder<undefined>;\n\t\tfunction select<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\t\tfunction select(fields?: SelectedFields): PgSelectBuilder<SelectedFields | undefined> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Adds `distinct` expression to the select query.\n\t\t *\n\t\t * Calling this method will return only unique values. When multiple columns are selected, it returns rows with unique combinations of values in these columns.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t\t *\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t * ```ts\n\t\t * // Select all unique rows from the 'cars' table\n\t\t * await db.selectDistinct()\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.id, cars.brand, cars.color);\n\t\t *\n\t\t * // Select all unique brands from the 'cars' table\n\t\t * await db.selectDistinct({ brand: cars.brand })\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.brand);\n\t\t * ```\n\t\t */\n\t\tfunction selectDistinct(): PgSelectBuilder<undefined>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\t\tfunction selectDistinct(fields?: SelectedFields): PgSelectBuilder<SelectedFields | undefined> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t\tdistinct: true,\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Adds `distinct on` expression to the select query.\n\t\t *\n\t\t * Calling this method will specify how the unique rows are determined.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t\t *\n\t\t * @param on The expression defining uniqueness.\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t * ```ts\n\t\t * // Select the first row for each unique brand from the 'cars' table\n\t\t * await db.selectDistinctOn([cars.brand])\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.brand);\n\t\t *\n\t\t * // Selects the first occurrence of each unique car brand along with its color from the 'cars' table\n\t\t * await db.selectDistinctOn([cars.brand], { brand: cars.brand, color: cars.color })\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.brand, cars.color);\n\t\t * ```\n\t\t */\n\t\tfunction selectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined>;\n\t\tfunction selectDistinctOn<TSelection extends SelectedFields>(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields: TSelection,\n\t\t): PgSelectBuilder<TSelection>;\n\t\tfunction selectDistinctOn(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields?: SelectedFields,\n\t\t): PgSelectBuilder<SelectedFields | undefined> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t\tdistinct: { on },\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Creates an update query.\n\t\t *\n\t\t * Calling this method without `.where()` clause will update all rows in a table. The `.where()` clause specifies which rows should be updated.\n\t\t *\n\t\t * Use `.set()` method to specify which values to update.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t\t *\n\t\t * @param table The table to update.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Update all rows in the 'cars' table\n\t\t * await db.update(cars).set({ color: 'red' });\n\t\t *\n\t\t * // Update rows with filters and conditions\n\t\t * await db.update(cars).set({ color: 'red' }).where(eq(cars.brand, 'BMW'));\n\t\t *\n\t\t * // Update with returning clause\n\t\t * const updatedCar: Car[] = await db.update(cars)\n\t\t *   .set({ color: 'red' })\n\t\t *   .where(eq(cars.id, 1))\n\t\t *   .returning();\n\t\t * ```\n\t\t */\n\t\tfunction update<TTable extends PgTable>(table: TTable): PgUpdateBuilder<TTable, TQueryResult> {\n\t\t\treturn new PgUpdateBuilder(table, self.session, self.dialect, queries);\n\t\t}\n\n\t\t/**\n\t\t * Creates an insert query.\n\t\t *\n\t\t * Calling this method will create new rows in a table. Use `.values()` method to specify which values to insert.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/insert}\n\t\t *\n\t\t * @param table The table to insert into.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Insert one row\n\t\t * await db.insert(cars).values({ brand: 'BMW' });\n\t\t *\n\t\t * // Insert multiple rows\n\t\t * await db.insert(cars).values([{ brand: 'BMW' }, { brand: 'Porsche' }]);\n\t\t *\n\t\t * // Insert with returning clause\n\t\t * const insertedCar: Car[] = await db.insert(cars)\n\t\t *   .values({ brand: 'BMW' })\n\t\t *   .returning();\n\t\t * ```\n\t\t */\n\t\tfunction insert<TTable extends PgTable>(table: TTable): PgInsertBuilder<TTable, TQueryResult> {\n\t\t\treturn new PgInsertBuilder(table, self.session, self.dialect, queries);\n\t\t}\n\n\t\t/**\n\t\t * Creates a delete query.\n\t\t *\n\t\t * Calling this method without `.where()` clause will delete all rows in a table. The `.where()` clause specifies which rows should be deleted.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t\t *\n\t\t * @param table The table to delete from.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Delete all rows in the 'cars' table\n\t\t * await db.delete(cars);\n\t\t *\n\t\t * // Delete rows with filters and conditions\n\t\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t\t *\n\t\t * // Delete with returning clause\n\t\t * const deletedCar: Car[] = await db.delete(cars)\n\t\t *   .where(eq(cars.id, 1))\n\t\t *   .returning();\n\t\t * ```\n\t\t */\n\t\tfunction delete_<TTable extends PgTable>(table: TTable): PgDeleteBase<TTable, TQueryResult> {\n\t\t\treturn new PgDeleteBase(table, self.session, self.dialect, queries);\n\t\t}\n\n\t\treturn { select, selectDistinct, selectDistinctOn, update, insert, delete: delete_ };\n\t}\n\n\t/**\n\t * Creates a select query.\n\t *\n\t * Calling this method with no arguments will select all columns from the table. Pass a selection object to specify the columns you want to select.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select}\n\t *\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all columns and all rows from the 'cars' table\n\t * const allCars: Car[] = await db.select().from(cars);\n\t *\n\t * // Select specific columns and all rows from the 'cars' table\n\t * const carsIdsAndBrands: { id: number; brand: string }[] = await db.select({\n\t *   id: cars.id,\n\t *   brand: cars.brand\n\t * })\n\t *   .from(cars);\n\t * ```\n\t *\n\t * Like in SQL, you can use arbitrary expressions as selection fields, not just table columns:\n\t *\n\t * ```ts\n\t * // Select specific columns along with expression and all rows from the 'cars' table\n\t * const carsIdsAndLowerNames: { id: number; lowerBrand: string }[] = await db.select({\n\t *   id: cars.id,\n\t *   lowerBrand: sql<string>`lower(${cars.brand})`,\n\t * })\n\t *   .from(cars);\n\t * ```\n\t */\n\tselect(): PgSelectBuilder<undefined>;\n\tselect<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\tselect(fields?: SelectedFields): PgSelectBuilder<SelectedFields | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t});\n\t}\n\n\t/**\n\t * Adds `distinct` expression to the select query.\n\t *\n\t * Calling this method will return only unique values. When multiple columns are selected, it returns rows with unique combinations of values in these columns.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t *\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t * ```ts\n\t * // Select all unique rows from the 'cars' table\n\t * await db.selectDistinct()\n\t *   .from(cars)\n\t *   .orderBy(cars.id, cars.brand, cars.color);\n\t *\n\t * // Select all unique brands from the 'cars' table\n\t * await db.selectDistinct({ brand: cars.brand })\n\t *   .from(cars)\n\t *   .orderBy(cars.brand);\n\t * ```\n\t */\n\tselectDistinct(): PgSelectBuilder<undefined>;\n\tselectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\tselectDistinct(fields?: SelectedFields): PgSelectBuilder<SelectedFields | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t\tdistinct: true,\n\t\t});\n\t}\n\n\t/**\n\t * Adds `distinct on` expression to the select query.\n\t *\n\t * Calling this method will specify how the unique rows are determined.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t *\n\t * @param on The expression defining uniqueness.\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t * ```ts\n\t * // Select the first row for each unique brand from the 'cars' table\n\t * await db.selectDistinctOn([cars.brand])\n\t *   .from(cars)\n\t *   .orderBy(cars.brand);\n\t *\n\t * // Selects the first occurrence of each unique car brand along with its color from the 'cars' table\n\t * await db.selectDistinctOn([cars.brand], { brand: cars.brand, color: cars.color })\n\t *   .from(cars)\n\t *   .orderBy(cars.brand, cars.color);\n\t * ```\n\t */\n\tselectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined>;\n\tselectDistinctOn<TSelection extends SelectedFields>(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields: TSelection,\n\t): PgSelectBuilder<TSelection>;\n\tselectDistinctOn(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields?: SelectedFields,\n\t): PgSelectBuilder<SelectedFields | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t\tdistinct: { on },\n\t\t});\n\t}\n\n\t/**\n\t * Creates an update query.\n\t *\n\t * Calling this method without `.where()` clause will update all rows in a table. The `.where()` clause specifies which rows should be updated.\n\t *\n\t * Use `.set()` method to specify which values to update.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t *\n\t * @param table The table to update.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Update all rows in the 'cars' table\n\t * await db.update(cars).set({ color: 'red' });\n\t *\n\t * // Update rows with filters and conditions\n\t * await db.update(cars).set({ color: 'red' }).where(eq(cars.brand, 'BMW'));\n\t *\n\t * // Update with returning clause\n\t * const updatedCar: Car[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.id, 1))\n\t *   .returning();\n\t * ```\n\t */\n\tupdate<TTable extends PgTable>(table: TTable): PgUpdateBuilder<TTable, TQueryResult> {\n\t\treturn new PgUpdateBuilder(table, this.session, this.dialect);\n\t}\n\n\t/**\n\t * Creates an insert query.\n\t *\n\t * Calling this method will create new rows in a table. Use `.values()` method to specify which values to insert.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert}\n\t *\n\t * @param table The table to insert into.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Insert one row\n\t * await db.insert(cars).values({ brand: 'BMW' });\n\t *\n\t * // Insert multiple rows\n\t * await db.insert(cars).values([{ brand: 'BMW' }, { brand: 'Porsche' }]);\n\t *\n\t * // Insert with returning clause\n\t * const insertedCar: Car[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning();\n\t * ```\n\t */\n\tinsert<TTable extends PgTable>(table: TTable): PgInsertBuilder<TTable, TQueryResult> {\n\t\treturn new PgInsertBuilder(table, this.session, this.dialect);\n\t}\n\n\t/**\n\t * Creates a delete query.\n\t *\n\t * Calling this method without `.where()` clause will delete all rows in a table. The `.where()` clause specifies which rows should be deleted.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t *\n\t * @param table The table to delete from.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Delete all rows in the 'cars' table\n\t * await db.delete(cars);\n\t *\n\t * // Delete rows with filters and conditions\n\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t *\n\t * // Delete with returning clause\n\t * const deletedCar: Car[] = await db.delete(cars)\n\t *   .where(eq(cars.id, 1))\n\t *   .returning();\n\t * ```\n\t */\n\tdelete<TTable extends PgTable>(table: TTable): PgDeleteBase<TTable, TQueryResult> {\n\t\treturn new PgDeleteBase(table, this.session, this.dialect);\n\t}\n\n\trefreshMaterializedView<TView extends PgMaterializedView>(view: TView): PgRefreshMaterializedView<TQueryResult> {\n\t\treturn new PgRefreshMaterializedView(view, this.session, this.dialect);\n\t}\n\n\texecute<TRow extends Record<string, unknown> = Record<string, unknown>>(\n\t\tquery: SQLWrapper,\n\t): PgRaw<PgQueryResultKind<TQueryResult, TRow>> {\n\t\tconst sql = query.getSQL();\n\t\tconst builtQuery = this.dialect.sqlToQuery(sql);\n\t\tconst prepared = this.session.prepareQuery<\n\t\t\tPreparedQueryConfig & { execute: PgQueryResultKind<TQueryResult, TRow> }\n\t\t>(\n\t\t\tbuiltQuery,\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tfalse,\n\t\t);\n\t\treturn new PgRaw(\n\t\t\t() => prepared.execute(),\n\t\t\tsql,\n\t\t\tbuiltQuery,\n\t\t\t(result) => prepared.mapResult(result, true),\n\t\t);\n\t}\n\n\ttransaction<T>(\n\t\ttransaction: (tx: PgTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig,\n\t): Promise<T> {\n\t\treturn this.session.transaction(transaction, config);\n\t}\n}\n\nexport type PgWithReplicas<Q> = Q & { $primary: Q };\n\nexport const withReplicas = <\n\tHKT extends PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n\tQ extends PgDatabase<HKT, TFullSchema, TSchema>,\n>(\n\tprimary: Q,\n\treplicas: [Q, ...Q[]],\n\tgetReplica: (replicas: Q[]) => Q = () => replicas[Math.floor(Math.random() * replicas.length)]!,\n): PgWithReplicas<Q> => {\n\tconst select: Q['select'] = (...args: []) => getReplica(replicas).select(...args);\n\tconst selectDistinct: Q['selectDistinct'] = (...args: []) => getReplica(replicas).selectDistinct(...args);\n\tconst selectDistinctOn: Q['selectDistinctOn'] = (...args: [any]) => getReplica(replicas).selectDistinctOn(...args);\n\tconst $with: Q['with'] = (...args: any) => getReplica(replicas).with(...args);\n\n\tconst update: Q['update'] = (...args: [any]) => primary.update(...args);\n\tconst insert: Q['insert'] = (...args: [any]) => primary.insert(...args);\n\tconst $delete: Q['delete'] = (...args: [any]) => primary.delete(...args);\n\tconst execute: Q['execute'] = (...args: [any]) => primary.execute(...args);\n\tconst transaction: Q['transaction'] = (...args: [any]) => primary.transaction(...args);\n\tconst refreshMaterializedView: Q['refreshMaterializedView'] = (...args: [any]) =>\n\t\tprimary.refreshMaterializedView(...args);\n\n\treturn {\n\t\t...primary,\n\t\tupdate,\n\t\tinsert,\n\t\tdelete: $delete,\n\t\texecute,\n\t\ttransaction,\n\t\trefreshMaterializedView,\n\t\t$primary: primary,\n\t\tselect,\n\t\tselectDistinct,\n\t\tselectDistinctOn,\n\t\twith: $with,\n\t\tget query() {\n\t\t\treturn getReplica(replicas).query;\n\t\t},\n\t};\n};\n"], "names": [], "mappings": ";;;;AAAA,SAAS,kBAAkB;;;;;AAE3B;AAkBA,SAAS,6BAA6B;AAEtC,SAAS,oBAAoB;AAG7B,SAAS,8BAA8B;AACvC,SAAS,aAAa;AACtB,SAAS,iCAAiC;;;;;;;;AAKnC,MAAM,WAIX;IAgBD,YAEU,OAAA,EAEA,OAAA,EACT,MAAA,CACC;QAJQ,IAAA,CAAA,OAAA,GAAA;QAEA,IAAA,CAAA,OAAA,GAAA;QAGT,IAAA,CAAK,CAAA,GAAI,SACN;YACD,QAAQ,OAAO,MAAA;YACf,YAAY,OAAO,UAAA;YACnB,eAAe,OAAO,aAAA;YACtB;QACD,IACE;YACD,QAAQ,KAAA;YACR,YAAY,CAAC;YACb,eAAe,CAAC;YAChB;QACD;QACD,IAAA,CAAK,KAAA,GAAQ,CAAC;QACd,IAAI,IAAA,CAAK,CAAA,CAAE,MAAA,EAAQ;YAClB,KAAA,MAAW,CAAC,WAAW,OAAO,CAAA,IAAK,OAAO,OAAA,CAAQ,IAAA,CAAK,CAAA,CAAE,MAAM,EAAG;gBAChE,IAAA,CAAK,KAAA,CAAiE,SAAS,CAAA,GAAI,wRAAI,yBAAA,CACvF,OAAQ,UAAA,EACR,IAAA,CAAK,CAAA,CAAE,MAAA,EACP,IAAA,CAAK,CAAA,CAAE,aAAA,EACP,OAAQ,UAAA,CAAW,SAAS,CAAA,EAC5B,SACA,SACA;YAEF;QACD;IACD;IAjDA,OAAA,mPAAiB,aAAU,CAAA,GAAY,aAAA;IASvC,MAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA0EA,MAA6B,KAAA,EAAe;QAC3C,OAAO;YACN,IACC,EAAA,EACgD;gBAChD,IAAI,OAAO,OAAO,YAAY;oBAC7B,KAAK,GAAG,mSAAI,eAAA,CAAa,CAAC;gBAC3B;gBAEA,OAAO,IAAI,MACV,wPAAI,eAAA,CAAa,GAAG,MAAA,CAAO,GAAG,GAAG,iBAAA,CAAkB,GAAqB,OAAO,IAAI,GACnF,kQAAI,wBAAA,CAAsB;oBAAE;oBAAO,oBAAoB;oBAAS,aAAa;gBAAQ,CAAC;YAExF;QACD;IACD;IAAA;;;;;;;;;;;;;;;;;;GAAA,GAqBA,KAAA,GAAQ,OAAA,EAAyB;QAChC,MAAM,OAAO,IAAA;QAwCb,SAAS,OAAO,MAAA,EAAsE;YACrF,OAAO,yRAAI,kBAAA,CAAgB;gBAC1B,QAAQ,UAAU,KAAA;gBAClB,SAAS,KAAK,OAAA;gBACd,SAAS,KAAK,OAAA;gBACd,UAAU;YACX,CAAC;QACF;QA4BA,SAAS,eAAe,MAAA,EAAsE;YAC7F,OAAO,yRAAI,kBAAA,CAAgB;gBAC1B,QAAQ,UAAU,KAAA;gBAClB,SAAS,KAAK,OAAA;gBACd,SAAS,KAAK,OAAA;gBACd,UAAU;gBACV,UAAU;YACX,CAAC;QACF;QAgCA,SAAS,iBACR,EAAA,EACA,MAAA,EAC8C;YAC9C,OAAO,yRAAI,kBAAA,CAAgB;gBAC1B,QAAQ,UAAU,KAAA;gBAClB,SAAS,KAAK,OAAA;gBACd,SAAS,KAAK,OAAA;gBACd,UAAU;gBACV,UAAU;oBAAE;gBAAG;YAChB,CAAC;QACF;QA6BA,SAAS,OAA+B,KAAA,EAAsD;YAC7F,OAAO,yRAAI,kBAAA,CAAgB,OAAO,KAAK,OAAA,EAAS,KAAK,OAAA,EAAS,OAAO;QACtE;QA0BA,SAAS,OAA+B,KAAA,EAAsD;YAC7F,OAAO,yRAAI,kBAAA,CAAgB,OAAO,KAAK,OAAA,EAAS,KAAK,OAAA,EAAS,OAAO;QACtE;QA0BA,SAAS,QAAgC,KAAA,EAAmD;YAC3F,OAAO,yRAAI,eAAA,CAAa,OAAO,KAAK,OAAA,EAAS,KAAK,OAAA,EAAS,OAAO;QACnE;QAEA,OAAO;YAAE;YAAQ;YAAgB;YAAkB;YAAQ;YAAQ,QAAQ;QAAQ;IACpF;IAwCA,OAAO,MAAA,EAAsE;QAC5E,OAAO,yRAAI,kBAAA,CAAgB;YAC1B,QAAQ,UAAU,KAAA;YAClB,SAAS,IAAA,CAAK,OAAA;YACd,SAAS,IAAA,CAAK,OAAA;QACf,CAAC;IACF;IA4BA,eAAe,MAAA,EAAsE;QACpF,OAAO,yRAAI,kBAAA,CAAgB;YAC1B,QAAQ,UAAU,KAAA;YAClB,SAAS,IAAA,CAAK,OAAA;YACd,SAAS,IAAA,CAAK,OAAA;YACd,UAAU;QACX,CAAC;IACF;IAgCA,iBACC,EAAA,EACA,MAAA,EAC8C;QAC9C,OAAO,yRAAI,kBAAA,CAAgB;YAC1B,QAAQ,UAAU,KAAA;YAClB,SAAS,IAAA,CAAK,OAAA;YACd,SAAS,IAAA,CAAK,OAAA;YACd,UAAU;gBAAE;YAAG;QAChB,CAAC;IACF;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA6BA,OAA+B,KAAA,EAAsD;QACpF,OAAO,yRAAI,kBAAA,CAAgB,OAAO,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,OAAO;IAC7D;IAAA;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA0BA,OAA+B,KAAA,EAAsD;QACpF,OAAO,yRAAI,kBAAA,CAAgB,OAAO,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,OAAO;IAC7D;IAAA;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA0BA,OAA+B,KAAA,EAAmD;QACjF,OAAO,yRAAI,eAAA,CAAa,OAAO,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,OAAO;IAC1D;IAEA,wBAA0D,IAAA,EAAsD;QAC/G,OAAO,kTAAI,4BAAA,CAA0B,MAAM,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,OAAO;IACtE;IAEA,QACC,KAAA,EAC+C;QAC/C,MAAM,MAAM,MAAM,MAAA,CAAO;QACzB,MAAM,aAAa,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,GAAG;QAC9C,MAAM,WAAW,IAAA,CAAK,OAAA,CAAQ,YAAA,CAG7B,YACA,KAAA,GACA,KAAA,GACA;QAED,OAAO,sRAAI,QAAA,CACV,IAAM,SAAS,OAAA,CAAQ,GACvB,KACA,YACA,CAAC,SAAW,SAAS,SAAA,CAAU,QAAQ,IAAI;IAE7C;IAEA,YACC,WAAA,EACA,MAAA,EACa;QACb,OAAO,IAAA,CAAK,OAAA,CAAQ,WAAA,CAAY,aAAa,MAAM;IACpD;AACD;AAIO,MAAM,eAAe,CAM3B,SACA,UACA,aAAmC,IAAM,QAAA,CAAS,KAAK,KAAA,CAAM,KAAK,MAAA,CAAO,IAAI,SAAS,MAAM,CAAC,CAAA,KACtE;IACvB,MAAM,SAAsB,CAAA,GAAI,OAAa,WAAW,QAAQ,EAAE,MAAA,CAAO,GAAG,IAAI;IAChF,MAAM,iBAAsC,CAAA,GAAI,OAAa,WAAW,QAAQ,EAAE,cAAA,CAAe,GAAG,IAAI;IACxG,MAAM,mBAA0C,CAAA,GAAI,OAAgB,WAAW,QAAQ,EAAE,gBAAA,CAAiB,GAAG,IAAI;IACjH,MAAM,QAAmB,CAAA,GAAI,OAAc,WAAW,QAAQ,EAAE,IAAA,CAAK,GAAG,IAAI;IAE5E,MAAM,SAAsB,CAAA,GAAI,OAAgB,QAAQ,MAAA,CAAO,GAAG,IAAI;IACtE,MAAM,SAAsB,CAAA,GAAI,OAAgB,QAAQ,MAAA,CAAO,GAAG,IAAI;IACtE,MAAM,UAAuB,CAAA,GAAI,OAAgB,QAAQ,MAAA,CAAO,GAAG,IAAI;IACvE,MAAM,UAAwB,CAAA,GAAI,OAAgB,QAAQ,OAAA,CAAQ,GAAG,IAAI;IACzE,MAAM,cAAgC,CAAA,GAAI,OAAgB,QAAQ,WAAA,CAAY,GAAG,IAAI;IACrF,MAAM,0BAAwD,CAAA,GAAI,OACjE,QAAQ,uBAAA,CAAwB,GAAG,IAAI;IAExC,OAAO;QACN,GAAG,OAAA;QACH;QACA;QACA,QAAQ;QACR;QACA;QACA;QACA,UAAU;QACV;QACA;QACA;QACA,MAAM;QACN,IAAI,SAAQ;YACX,OAAO,WAAW,QAAQ,EAAE,KAAA;QAC7B;IACD;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5921, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/pg-core/session.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { TransactionRollbackError } from '~/errors.ts';\nimport type { TablesRelationalConfig } from '~/relations.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport { type Query, type SQL, sql } from '~/sql/index.ts';\nimport { tracer } from '~/tracing.ts';\nimport { PgDatabase } from './db.ts';\nimport type { PgDialect } from './dialect.ts';\nimport type { SelectedFieldsOrdered } from './query-builders/select.types.ts';\n\nexport interface PreparedQueryConfig {\n\texecute: unknown;\n\tall: unknown;\n\tvalues: unknown;\n}\n\nexport abstract class PgPreparedQuery<T extends PreparedQueryConfig> implements PreparedQuery {\n\tconstructor(protected query: Query) {}\n\n\tgetQuery(): Query {\n\t\treturn this.query;\n\t}\n\n\tmapResult(response: unknown, _isFromBatch?: boolean): unknown {\n\t\treturn response;\n\t}\n\n\tstatic readonly [entityKind]: string = 'PgPreparedQuery';\n\n\t/** @internal */\n\tjoinsNotNullableMap?: Record<string, boolean>;\n\n\tabstract execute(placeholderValues?: Record<string, unknown>): Promise<T['execute']>;\n\n\t/** @internal */\n\tabstract all(placeholderValues?: Record<string, unknown>): Promise<T['all']>;\n\n\t/** @internal */\n\tabstract isResponseInArrayMode(): boolean;\n}\n\nexport interface PgTransactionConfig {\n\tisolationLevel?: 'read uncommitted' | 'read committed' | 'repeatable read' | 'serializable';\n\taccessMode?: 'read only' | 'read write';\n\tdeferrable?: boolean;\n}\n\nexport abstract class PgSession<\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> {\n\tstatic readonly [entityKind]: string = 'PgSession';\n\n\tconstructor(protected dialect: PgDialect) {}\n\n\tabstract prepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][], mapColumnValue?: (value: unknown) => unknown) => T['execute'],\n\t): PgPreparedQuery<T>;\n\n\texecute<T>(query: SQL): Promise<T> {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\tconst prepared = tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\t\treturn this.prepareQuery<PreparedQueryConfig & { execute: T }>(\n\t\t\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\t\t\tundefined,\n\t\t\t\t\tundefined,\n\t\t\t\t\tfalse,\n\t\t\t\t);\n\t\t\t});\n\n\t\t\treturn prepared.execute();\n\t\t});\n\t}\n\n\tall<T = unknown>(query: SQL): Promise<T[]> {\n\t\treturn this.prepareQuery<PreparedQueryConfig & { all: T[] }>(\n\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tfalse,\n\t\t).all();\n\t}\n\n\tabstract transaction<T>(\n\t\ttransaction: (tx: PgTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig,\n\t): Promise<T>;\n}\n\nexport abstract class PgTransaction<\n\tTQueryResult extends PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> extends PgDatabase<TQueryResult, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'PgTransaction';\n\n\tconstructor(\n\t\tdialect: PgDialect,\n\t\tsession: PgSession<any, any, any>,\n\t\tprotected schema: {\n\t\t\tfullSchema: Record<string, unknown>;\n\t\t\tschema: TSchema;\n\t\t\ttableNamesMap: Record<string, string>;\n\t\t} | undefined,\n\t\tprotected readonly nestedIndex = 0,\n\t) {\n\t\tsuper(dialect, session, schema);\n\t}\n\n\trollback(): never {\n\t\tthrow new TransactionRollbackError();\n\t}\n\n\t/** @internal */\n\tgetTransactionConfigSQL(config: PgTransactionConfig): SQL {\n\t\tconst chunks: string[] = [];\n\t\tif (config.isolationLevel) {\n\t\t\tchunks.push(`isolation level ${config.isolationLevel}`);\n\t\t}\n\t\tif (config.accessMode) {\n\t\t\tchunks.push(config.accessMode);\n\t\t}\n\t\tif (typeof config.deferrable === 'boolean') {\n\t\t\tchunks.push(config.deferrable ? 'deferrable' : 'not deferrable');\n\t\t}\n\t\treturn sql.raw(chunks.join(' '));\n\t}\n\n\tsetTransaction(config: PgTransactionConfig): Promise<void> {\n\t\treturn this.session.execute(sql`set transaction ${this.getTransactionConfigSQL(config)}`);\n\t}\n\n\tabstract override transaction<T>(\n\t\ttransaction: (tx: PgTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T>;\n}\n\nexport interface PgQueryResultHKT {\n\treadonly $brand: 'PgQueryResultHKT';\n\treadonly row: unknown;\n\treadonly type: unknown;\n}\n\nexport type PgQueryResultKind<TKind extends PgQueryResultHKT, TRow> = (TKind & {\n\treadonly row: TRow;\n})['type'];\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,kBAAkB;AAC3B,SAAS,gCAAgC;AAGzC,SAA+B,WAAW;AAC1C,SAAS,cAAc;AACvB,SAAS,kBAAkB;;;;;;AAUpB,MAAe,gBAAwE;IAC7F,YAAsB,KAAA,CAAc;QAAd,IAAA,CAAA,KAAA,GAAA;IAAe;IAErC,WAAkB;QACjB,OAAO,IAAA,CAAK,KAAA;IACb;IAEA,UAAU,QAAA,EAAmB,YAAA,EAAiC;QAC7D,OAAO;IACR;IAEA,OAAA,mPAAiB,aAAU,CAAA,GAAY,kBAAA;IAAA,cAAA,GAGvC,oBAAA;AASD;AAQO,MAAe,UAIpB;IAGD,YAAsB,OAAA,CAAoB;QAApB,IAAA,CAAA,OAAA,GAAA;IAAqB;IAF3C,OAAA,mPAAiB,aAAU,CAAA,GAAY,YAAA;IAYvC,QAAW,KAAA,EAAwB;QAClC,0PAAO,SAAA,CAAO,eAAA,CAAgB,qBAAqB,MAAM;YACxD,MAAM,8PAAW,SAAA,CAAO,eAAA,CAAgB,wBAAwB,MAAM;gBACrE,OAAO,IAAA,CAAK,YAAA,CACX,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,KAAK,GAC7B,KAAA,GACA,KAAA,GACA;YAEF,CAAC;YAED,OAAO,SAAS,OAAA,CAAQ;QACzB,CAAC;IACF;IAEA,IAAiB,KAAA,EAA0B;QAC1C,OAAO,IAAA,CAAK,YAAA,CACX,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,KAAK,GAC7B,KAAA,GACA,KAAA,GACA,OACC,GAAA,CAAI;IACP;AAMD;AAEO,MAAe,kRAIZ,aAAA,CAA+C;IAGxD,YACC,OAAA,EACA,OAAA,EACU,MAAA,EAKS,cAAc,CAAA,CAChC;QACD,KAAA,CAAM,SAAS,SAAS,MAAM;QAPpB,IAAA,CAAA,MAAA,GAAA;QAKS,IAAA,CAAA,WAAA,GAAA;IAGpB;IAbA,OAAA,mPAAiB,aAAU,CAAA,GAAY,gBAAA;IAevC,WAAkB;QACjB,MAAM,sPAAI,2BAAA,CAAyB;IACpC;IAAA,cAAA,GAGA,wBAAwB,MAAA,EAAkC;QACzD,MAAM,SAAmB,CAAC,CAAA;QAC1B,IAAI,OAAO,cAAA,EAAgB;YAC1B,OAAO,IAAA,CAAK,CAAA,gBAAA,EAAmB,OAAO,cAAc,EAAE;QACvD;QACA,IAAI,OAAO,UAAA,EAAY;YACtB,OAAO,IAAA,CAAK,OAAO,UAAU;QAC9B;QACA,IAAI,OAAO,OAAO,UAAA,KAAe,WAAW;YAC3C,OAAO,IAAA,CAAK,OAAO,UAAA,GAAa,eAAe,gBAAgB;QAChE;QACA,6PAAO,MAAA,CAAI,GAAA,CAAI,OAAO,IAAA,CAAK,GAAG,CAAC;IAChC;IAEA,eAAe,MAAA,EAA4C;QAC1D,OAAO,IAAA,CAAK,OAAA,CAAQ,OAAA,uPAAQ,MAAA,CAAA,gBAAA,EAAsB,IAAA,CAAK,uBAAA,CAAwB,MAAM,CAAC,CAAA,CAAE;IACzF;AAKD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6001, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/node-postgres/session.ts"], "sourcesContent": ["import type { Client, PoolClient, QueryArrayConfig, QueryConfig, QueryResult, QueryResultRow } from 'pg';\nimport pg from 'pg';\nimport { entityKind } from '~/entity.ts';\nimport { type Logger, NoopLogger } from '~/logger.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport { PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgQueryResultHKT, PgTransactionConfig, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport { PgPreparedQuery, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, sql } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\n\nconst { Pool } = pg;\n\nexport type NodePgClient = pg.Pool | PoolClient | Client;\n\nexport class NodePgPreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {\n\tstatic readonly [entityKind]: string = 'NodePgPreparedQuery';\n\n\tprivate rawQueryConfig: QueryConfig;\n\tprivate queryConfig: QueryArrayConfig;\n\n\tconstructor(\n\t\tprivate client: NodePgClient,\n\t\tqueryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper({ sql: queryString, params });\n\t\tthis.rawQueryConfig = {\n\t\t\tname,\n\t\t\ttext: queryString,\n\t\t};\n\t\tthis.queryConfig = {\n\t\t\tname,\n\t\t\ttext: queryString,\n\t\t\trowMode: 'array',\n\t\t};\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\treturn tracer.startActiveSpan('drizzle.execute', async () => {\n\t\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\t\tthis.logger.logQuery(this.rawQueryConfig.text, params);\n\n\t\t\tconst { fields, rawQueryConfig: rawQuery, client, queryConfig: query, joinsNotNullableMap, customResultMapper } =\n\t\t\t\tthis;\n\t\t\tif (!fields && !customResultMapper) {\n\t\t\t\treturn tracer.startActiveSpan('drizzle.driver.execute', async (span) => {\n\t\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t\t'drizzle.query.name': rawQuery.name,\n\t\t\t\t\t\t'drizzle.query.text': rawQuery.text,\n\t\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t\t});\n\t\t\t\t\treturn client.query(rawQuery, params);\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tconst result = await tracer.startActiveSpan('drizzle.driver.execute', (span) => {\n\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t'drizzle.query.name': query.name,\n\t\t\t\t\t'drizzle.query.text': query.text,\n\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t});\n\t\t\t\treturn client.query(query, params);\n\t\t\t});\n\n\t\t\treturn tracer.startActiveSpan('drizzle.mapResponse', () => {\n\t\t\t\treturn customResultMapper\n\t\t\t\t\t? customResultMapper(result.rows)\n\t\t\t\t\t: result.rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t\t\t});\n\t\t});\n\t}\n\n\tall(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['all']> {\n\t\treturn tracer.startActiveSpan('drizzle.execute', () => {\n\t\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\t\tthis.logger.logQuery(this.rawQueryConfig.text, params);\n\t\t\treturn tracer.startActiveSpan('drizzle.driver.execute', (span) => {\n\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t'drizzle.query.name': this.rawQueryConfig.name,\n\t\t\t\t\t'drizzle.query.text': this.rawQueryConfig.text,\n\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t});\n\t\t\t\treturn this.client.query(this.rawQueryConfig, params).then((result) => result.rows);\n\t\t\t});\n\t\t});\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface NodePgSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class NodePgSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<NodePgQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'NodePgSession';\n\n\tprivate logger: Logger;\n\n\tconstructor(\n\t\tprivate client: NodePgClient,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: NodePgSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t): PgPreparedQuery<T> {\n\t\treturn new NodePgPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\tname,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: NodePgTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig | undefined,\n\t): Promise<T> {\n\t\tconst session = this.client instanceof Pool // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t? new NodePgSession(await this.client.connect(), this.dialect, this.schema, this.options)\n\t\t\t: this;\n\t\tconst tx = new NodePgTransaction<TFullSchema, TSchema>(this.dialect, session, this.schema);\n\t\tawait tx.execute(sql`begin${config ? sql` ${tx.getTransactionConfigSQL(config)}` : undefined}`);\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql`commit`);\n\t\t\treturn result;\n\t\t} catch (error) {\n\t\t\tawait tx.execute(sql`rollback`);\n\t\t\tthrow error;\n\t\t} finally {\n\t\t\tif (this.client instanceof Pool) { // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t\t(session.client as PoolClient).release();\n\t\t\t}\n\t\t}\n\t}\n}\n\nexport class NodePgTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<NodePgQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'NodePgTransaction';\n\n\toverride async transaction<T>(transaction: (tx: NodePgTransaction<TFullSchema, TSchema>) => Promise<T>): Promise<T> {\n\t\tconst savepointName = `sp${this.nestedIndex + 1}`;\n\t\tconst tx = new NodePgTransaction<TFullSchema, TSchema>(\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tthis.schema,\n\t\t\tthis.nestedIndex + 1,\n\t\t);\n\t\tawait tx.execute(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait tx.execute(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport interface NodePgQueryResultHKT extends PgQueryResultHKT {\n\ttype: QueryResult<Assume<this['row'], QueryResultRow>>;\n}\n"], "names": [], "mappings": ";;;;;AACA,OAAO,QAAQ;AACf,SAAS,kBAAkB;AAC3B,SAAsB,kBAAkB;AAExC,SAAS,qBAAqB;AAK9B,SAAS,kBAA8B,WAAW;AAClD,SAAS,cAAc;AACvB,SAAsB,oBAAoB;;;;;;;;;AAE1C,MAAM,EAAE,IAAA,CAAK,CAAA,iGAAI,UAAA;AAIV,MAAM,6RAA2D,kBAAA,CAAmB;IAM1F,YACS,MAAA,EACR,WAAA,EACQ,MAAA,EACA,MAAA,EACA,MAAA,EACR,IAAA,EACQ,sBAAA,EACA,kBAAA,CACP;QACD,KAAA,CAAM;YAAE,KAAK;YAAa;QAAO,CAAC;QAT1B,IAAA,CAAA,MAAA,GAAA;QAEA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QAEA,IAAA,CAAA,sBAAA,GAAA;QACA,IAAA,CAAA,kBAAA,GAAA;QAGR,IAAA,CAAK,cAAA,GAAiB;YACrB;YACA,MAAM;QACP;QACA,IAAA,CAAK,WAAA,GAAc;YAClB;YACA,MAAM;YACN,SAAS;QACV;IACD;IAzBA,OAAA,mPAAiB,aAAU,CAAA,GAAY,sBAAA;IAE/B,eAAA;IACA,YAAA;IAwBR,MAAM,QAAQ,oBAAyD,CAAC,CAAA,EAA0B;QACjG,0PAAO,SAAA,CAAO,eAAA,CAAgB,mBAAmB,YAAY;YAC5D,MAAM,aAAS,yQAAA,EAAiB,IAAA,CAAK,MAAA,EAAQ,iBAAiB;YAE9D,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,IAAA,CAAK,cAAA,CAAe,IAAA,EAAM,MAAM;YAErD,MAAM,EAAE,MAAA,EAAQ,gBAAgB,QAAA,EAAU,MAAA,EAAQ,aAAa,KAAA,EAAO,mBAAA,EAAqB,kBAAA,CAAmB,CAAA,GAC7G,IAAA;YACD,IAAI,CAAC,UAAU,CAAC,oBAAoB;gBACnC,0PAAO,SAAA,CAAO,eAAA,CAAgB,0BAA0B,OAAO,SAAS;oBACvE,MAAM,cAAc;wBACnB,sBAAsB,SAAS,IAAA;wBAC/B,sBAAsB,SAAS,IAAA;wBAC/B,wBAAwB,KAAK,SAAA,CAAU,MAAM;oBAC9C,CAAC;oBACD,OAAO,OAAO,KAAA,CAAM,UAAU,MAAM;gBACrC,CAAC;YACF;YAEA,MAAM,SAAS,yPAAM,SAAA,CAAO,eAAA,CAAgB,0BAA0B,CAAC,SAAS;gBAC/E,MAAM,cAAc;oBACnB,sBAAsB,MAAM,IAAA;oBAC5B,sBAAsB,MAAM,IAAA;oBAC5B,wBAAwB,KAAK,SAAA,CAAU,MAAM;gBAC9C,CAAC;gBACD,OAAO,OAAO,KAAA,CAAM,OAAO,MAAM;YAClC,CAAC;YAED,0PAAO,SAAA,CAAO,eAAA,CAAgB,uBAAuB,MAAM;gBAC1D,OAAO,qBACJ,mBAAmB,OAAO,IAAI,IAC9B,OAAO,IAAA,CAAK,GAAA,CAAI,CAAC,2PAAQ,eAAA,EAA2B,QAAS,KAAK,mBAAmB,CAAC;YAC1F,CAAC;QACF,CAAC;IACF;IAEA,IAAI,oBAAyD,CAAC,CAAA,EAAsB;QACnF,0PAAO,SAAA,CAAO,eAAA,CAAgB,mBAAmB,MAAM;YACtD,MAAM,mQAAS,mBAAA,EAAiB,IAAA,CAAK,MAAA,EAAQ,iBAAiB;YAC9D,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,IAAA,CAAK,cAAA,CAAe,IAAA,EAAM,MAAM;YACrD,0PAAO,SAAA,CAAO,eAAA,CAAgB,0BAA0B,CAAC,SAAS;gBACjE,MAAM,cAAc;oBACnB,sBAAsB,IAAA,CAAK,cAAA,CAAe,IAAA;oBAC1C,sBAAsB,IAAA,CAAK,cAAA,CAAe,IAAA;oBAC1C,wBAAwB,KAAK,SAAA,CAAU,MAAM;gBAC9C,CAAC;gBACD,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA,CAAK,cAAA,EAAgB,MAAM,EAAE,IAAA,CAAK,CAAC,SAAW,OAAO,IAAI;YACnF,CAAC;QACF,CAAC;IACF;IAAA,cAAA,GAGA,wBAAiC;QAChC,OAAO,IAAA,CAAK,sBAAA;IACb;AACD;AAMO,MAAM,sRAGH,aAAA,CAAsD;IAK/D,YACS,MAAA,EACR,OAAA,EACQ,MAAA,EACA,UAAgC,CAAC,CAAA,CACxC;QACD,KAAA,CAAM,OAAO;QALL,IAAA,CAAA,MAAA,GAAA;QAEA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QAGR,IAAA,CAAK,MAAA,GAAS,QAAQ,MAAA,IAAU,sPAAI,aAAA,CAAW;IAChD;IAZA,OAAA,mPAAiB,aAAU,CAAA,GAAY,gBAAA;IAE/B,OAAA;IAYR,aACC,KAAA,EACA,MAAA,EACA,IAAA,EACA,qBAAA,EACA,kBAAA,EACqB;QACrB,OAAO,IAAI,oBACV,IAAA,CAAK,MAAA,EACL,MAAM,GAAA,EACN,MAAM,MAAA,EACN,IAAA,CAAK,MAAA,EACL,QACA,MACA,uBACA;IAEF;IAEA,MAAe,YACd,WAAA,EACA,MAAA,EACa;QACb,MAAM,UAAU,IAAA,CAAK,MAAA,YAAkB,OACpC,IAAI,cAAc,MAAM,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,GAAG,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,OAAO,IACtF,IAAA;QACH,MAAM,KAAK,IAAI,kBAAwC,IAAA,CAAK,OAAA,EAAS,SAAS,IAAA,CAAK,MAAM;QACzF,MAAM,GAAG,OAAA,uPAAQ,MAAA,CAAA,KAAA,EAAW,+PAAS,MAAA,CAAA,CAAA,EAAO,GAAG,uBAAA,CAAwB,MAAM,CAAC,CAAA,CAAA,GAAK,KAAA,CAAS,CAAA,CAAE;QAC9F,IAAI;YACH,MAAM,SAAS,MAAM,YAAY,EAAE;YACnC,MAAM,GAAG,OAAA,uPAAQ,MAAA,CAAA,MAAA,CAAW;YAC5B,OAAO;QACR,EAAA,OAAS,OAAO;YACf,MAAM,GAAG,OAAA,CAAQ,4PAAA,CAAA,QAAA,CAAa;YAC9B,MAAM;QACP,SAAE;YACD,IAAI,IAAA,CAAK,MAAA,YAAkB,MAAM;gBAC/B,QAAQ,MAAA,CAAsB,OAAA,CAAQ;YACxC;QACD;IACD;AACD;AAEO,MAAM,2RAGH,gBAAA,CAA0D;IACnE,OAAA,mPAAiB,aAAU,CAAA,GAAY,oBAAA;IAEvC,MAAe,YAAe,WAAA,EAAsF;QACnH,MAAM,gBAAgB,CAAA,EAAA,EAAK,IAAA,CAAK,WAAA,GAAc,CAAC,EAAA;QAC/C,MAAM,KAAK,IAAI,kBACd,IAAA,CAAK,OAAA,EACL,IAAA,CAAK,OAAA,EACL,IAAA,CAAK,MAAA,EACL,IAAA,CAAK,WAAA,GAAc;QAEpB,MAAM,GAAG,OAAA,uPAAQ,MAAA,CAAI,GAAA,CAAI,CAAA,UAAA,EAAa,aAAa,EAAE,CAAC;QACtD,IAAI;YACH,MAAM,SAAS,MAAM,YAAY,EAAE;YACnC,MAAM,GAAG,OAAA,uPAAQ,MAAA,CAAI,GAAA,CAAI,CAAA,kBAAA,EAAqB,aAAa,EAAE,CAAC;YAC9D,OAAO;QACR,EAAA,OAAS,KAAK;YACb,MAAM,GAAG,OAAA,uPAAQ,MAAA,CAAI,GAAA,CAAI,CAAA,sBAAA,EAAyB,aAAa,EAAE,CAAC;YAClE,MAAM;QACP;IACD;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/node_modules/.pnpm/drizzle-orm%400.31.4_%40neondat_2c4e83f03bbbbe484e5e453678a61102/node_modules/src/node-postgres/driver.ts"], "sourcesContent": ["import pg from 'pg';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { PgDatabase } from '~/pg-core/db.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport type { NodePgClient, NodePgQueryResultHKT } from './session.ts';\nimport { NodePgSession } from './session.ts';\n\nconst { types } = pg;\n\nexport interface PgDriverOptions {\n\tlogger?: Logger;\n}\n\nexport class NodePgDriver {\n\tstatic readonly [entityKind]: string = 'NodePgDriver';\n\n\tconstructor(\n\t\tprivate client: NodePgClient,\n\t\tprivate dialect: PgDialect,\n\t\tprivate options: PgDriverOptions = {},\n\t) {\n\t\tthis.initMappers();\n\t}\n\n\tcreateSession(\n\t\tschema: RelationalSchemaConfig<TablesRelationalConfig> | undefined,\n\t): NodePgSession<Record<string, unknown>, TablesRelationalConfig> {\n\t\treturn new NodePgSession(this.client, this.dialect, schema, { logger: this.options.logger });\n\t}\n\n\tinitMappers() {\n\t\ttypes.setTypeParser(types.builtins.TIMESTAMPTZ, (val) => val);\n\t\ttypes.setTypeParser(types.builtins.TIMESTAMP, (val) => val);\n\t\ttypes.setTypeParser(types.builtins.DATE, (val) => val);\n\t\ttypes.setTypeParser(types.builtins.INTERVAL, (val) => val);\n\t}\n}\n\nexport type NodePgDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> = PgDatabase<NodePgQueryResultHKT, TSchema>;\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: NodePgClient,\n\tconfig: DrizzleConfig<TSchema> = {},\n): NodePgDatabase<TSchema> {\n\tconst dialect = new PgDialect();\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst driver = new NodePgDriver(client, dialect, { logger });\n\tconst session = driver.createSession(schema);\n\treturn new PgDatabase(dialect, session, schema) as NodePgDatabase<TSchema>;\n}\n"], "names": [], "mappings": ";;;;AAAA,OAAO,QAAQ;AACf,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAC9B,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;AAC1B;AAQA,SAAS,qBAAqB;;;;;;;;AAE9B,MAAM,EAAE,KAAA,CAAM,CAAA,iGAAI,UAAA;AAMX,MAAM,aAAa;IAGzB,YACS,MAAA,EACA,OAAA,EACA,UAA2B,CAAC,CAAA,CACnC;QAHO,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QAER,IAAA,CAAK,WAAA,CAAY;IAClB;IARA,OAAA,mPAAiB,aAAU,CAAA,GAAY,eAAA;IAUvC,cACC,MAAA,EACiE;QACjE,OAAO,2QAAI,gBAAA,CAAc,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,OAAA,EAAS,QAAQ;YAAE,QAAQ,IAAA,CAAK,OAAA,CAAQ,MAAA;QAAO,CAAC;IAC5F;IAEA,cAAc;QACb,MAAM,aAAA,CAAc,MAAM,QAAA,CAAS,WAAA,EAAa,CAAC,MAAQ,GAAG;QAC5D,MAAM,aAAA,CAAc,MAAM,QAAA,CAAS,SAAA,EAAW,CAAC,MAAQ,GAAG;QAC1D,MAAM,aAAA,CAAc,MAAM,QAAA,CAAS,IAAA,EAAM,CAAC,MAAQ,GAAG;QACrD,MAAM,aAAA,CAAc,MAAM,QAAA,CAAS,QAAA,EAAU,CAAC,MAAQ,GAAG;IAC1D;AACD;AAMO,SAAS,QACf,MAAA,EACA,SAAiC,CAAC,CAAA,EACR;IAC1B,MAAM,UAAU,qQAAI,YAAA,CAAU;IAC9B,IAAI;IACJ,IAAI,OAAO,MAAA,KAAW,MAAM;QAC3B,SAAS,sPAAI,gBAAA,CAAc;IAC5B,OAAA,IAAW,OAAO,MAAA,KAAW,OAAO;QACnC,SAAS,OAAO,MAAA;IACjB;IAEA,IAAI;IACJ,IAAI,OAAO,MAAA,EAAQ;QAClB,MAAM,wQAAe,gCAAA,EACpB,OAAO,MAAA,uPACP,8BAAA;QAED,SAAS;YACR,YAAY,OAAO,MAAA;YACnB,QAAQ,aAAa,MAAA;YACrB,eAAe,aAAa,aAAA;QAC7B;IACD;IAEA,MAAM,SAAS,IAAI,aAAa,QAAQ,SAAS;QAAE;IAAO,CAAC;IAC3D,MAAM,UAAU,OAAO,aAAA,CAAc,MAAM;IAC3C,OAAO,gQAAI,aAAA,CAAW,SAAS,SAAS,MAAM;AAC/C", "ignoreList": [0], "debugId": null}}]}