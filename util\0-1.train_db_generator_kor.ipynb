{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 한글 단어 리트리버 튜닝"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 환경변수 설정\n", "from langchain_ollama.llms import OllamaLLM\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_community.embeddings import HuggingFaceBgeEmbeddings\n", "from dotenv import load_dotenv\n", "load_dotenv('./dot.env')\n", "import os\n", "import re"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 형태소 분석기\n", "from kiwipiepy import Kiwi\n", "\n", "# markdown 문서 load 시 header 정보가 HTML tag 형식으로 불러들여짐\n", "from langchain_text_splitters import HTMLHeaderTextSplitter\n", "from langchain_text_splitters import MarkdownHeaderTextSplitter\n", "\n", "\n", "# embeding\n", "from langchain.retrievers import EnsembleRetriever\n", "from langchain_community.retrievers import BM25Retriever\n", "from langchain_core.documents import Document\n", "from langchain.vectorstores import FAISS\n", "\n", "# markdown\n", "import markdown\n", "\n", "kiwi = Kiwi()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Markdown 문서 load"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def load_markdown_file(file_path):\n", "    # if file_path.endswith('.md'):\n", "    with open(file_path, 'r', encoding='utf-8') as file:\n", "        content = file.read()\n", "    return markdown.markdown(content)\n", "doc = ''\n", "markdown_content = load_markdown_file(f'./test_db/first_test.txt')\n", "doc = '\\n'.join([doc, markdown_content])\n", "with open('./test_db/first_test.txt') as f:\n", "    file = f.read()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# header\n", "headers_to_split_on = [\n", "    (\"#\", \"구분\"),          # 분할할 헤더 레벨과 해당 레벨의 이름을 지정합니다.\n", "    (\"##\", \"설명\"),\n", "    (\"###\", \"언어\"),\n", "    (\"####\", \"기능\")\n", "]\n", "\n", "# Markdown 문서를 헤더 레벨에 따라 분할합니다.\n", "markdown_splitter = MarkdownHeaderTextSplitter(\n", "    headers_to_split_on=headers_to_split_on\n", ")\n", "code_splits = markdown_splitter.split_text(file)\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/envs/mppckbt/lib/python3.9/site-packages/sentence_transformers/cross_encoder/CrossEncoder.py:11: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from tqdm.autonotebook import tqdm, trange\n"]}], "source": ["# embeddings = OpenAIEmbeddings()\n", "embeddings = HuggingFaceBgeEmbeddings()\n", "\n", "Vectordb = FAISS.from_documents(code_splits, embeddings)\n", "\n", "#bm25는 반환값을 list로 가지기에 아래 함수도 list 반환값 형식으로\n", "def kiwi_tokenize(text):\n", "    return [token.form for token in kiwi.tokenize(text)]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["docs = str(code_splits)\n", "\n", "# 파일 저장 경로\n", "file_path = './train_db.txt'\n", "\n", "# 문자열 데이터를 텍스트 파일로 저장\n", "with open(file_path, 'w', encoding='utf-8') as file:\n", "    file.write(docs)\n", "\n", "# 텍스트 파일에서 문자열 데이터를 읽기\n", "with open(file_path, 'r', encoding='utf-8') as file:\n", "    document_str = file.read()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import ast\n", "\n", "class Document:\n", "    def __init__(self, metadata: dict, page_content: str):\n", "        self.metadata = metadata\n", "        self.page_content = page_content\n", "\n", "    def __repr__(self):\n", "        return f\"Document(metadata={self.metadata}, page_content={self.page_content[:100]}...)\"\n", "\n", "def parse_documents(doc_string: str):\n", "    doc_string = doc_string.replace(\"Document(\", \"{\").replace(\")\", \"}\")\n", "    doc_string = doc_string.replace(\"metadata=\", \"'metadata': \").replace(\"page_content=\", \"'page_content': \")\n", "    doc_list = ast.literal_eval(doc_string)\n", "    documents = [Document(metadata=doc['metadata'], page_content=doc['page_content']) for doc in doc_list]\n", "    return documents"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["docs = parse_documents(document_str)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["metadata = []\n", "for i in docs:\n", "    metadata.append(i.metadata)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'구분': 'odf', '설명': '기본지도 생성 및 스와이퍼 추가', '언어': 'html', '기능': '지도생성 필수'},\n", " {'구분': 'odf', '설명': '기본지도 생성 및 스와이퍼 추가', '언어': 'javascript', '기능': '맵객체 생성'},\n", " {'구분': 'odf', '설명': '기본지도 생성 및 스와이퍼 추가', '언어': 'javascript', '기능': '배경지도 생성'},\n", " {'구분': 'odf', '설명': '기본지도 생성 및 배경지도 최적화', '언어': 'html', '기능': '지도생성 필수'},\n", " {'구분': 'odf', '설명': '기본지도 생성 및 배경지도 최적화', '언어': 'javascript', '기능': '맵객체 생성'}]"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["metadata[:5]"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'dict' object has no attribute 'page_content'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[32], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m bm25 \u001b[38;5;241m=\u001b[39m \u001b[43mBM25Retriever\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_documents\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/anaconda3/envs/mppckbt/lib/python3.9/site-packages/langchain_community/retrievers/bm25.py:90\u001b[0m, in \u001b[0;36mBM25Retriever.from_documents\u001b[0;34m(cls, documents, bm25_params, preprocess_func, **kwargs)\u001b[0m\n\u001b[1;32m     70\u001b[0m \u001b[38;5;129m@classmethod\u001b[39m\n\u001b[1;32m     71\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mfrom_documents\u001b[39m(\n\u001b[1;32m     72\u001b[0m     \u001b[38;5;28mcls\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     77\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs: Any,\n\u001b[1;32m     78\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m BM25Retriever:\n\u001b[1;32m     79\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m     80\u001b[0m \u001b[38;5;124;03m    Create a BM25Retriever from a list of Documents.\u001b[39;00m\n\u001b[1;32m     81\u001b[0m \u001b[38;5;124;03m    Args:\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     88\u001b[0m \u001b[38;5;124;03m        A BM25Retriever instance.\u001b[39;00m\n\u001b[1;32m     89\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m---> 90\u001b[0m     texts, metadatas \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mzip\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m(\u001b[49m\u001b[43md\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpage_content\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43md\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmetadata\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43md\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mdocuments\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     91\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39mfrom_texts(\n\u001b[1;32m     92\u001b[0m         texts\u001b[38;5;241m=\u001b[39mtexts,\n\u001b[1;32m     93\u001b[0m         bm25_params\u001b[38;5;241m=\u001b[39mbm25_params,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     96\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs,\n\u001b[1;32m     97\u001b[0m     )\n", "File \u001b[0;32m~/anaconda3/envs/mppckbt/lib/python3.9/site-packages/langchain_community/retrievers/bm25.py:90\u001b[0m, in \u001b[0;36m<genexpr>\u001b[0;34m(.0)\u001b[0m\n\u001b[1;32m     70\u001b[0m \u001b[38;5;129m@classmethod\u001b[39m\n\u001b[1;32m     71\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mfrom_documents\u001b[39m(\n\u001b[1;32m     72\u001b[0m     \u001b[38;5;28mcls\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     77\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs: Any,\n\u001b[1;32m     78\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m BM25Retriever:\n\u001b[1;32m     79\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m     80\u001b[0m \u001b[38;5;124;03m    Create a BM25Retriever from a list of Documents.\u001b[39;00m\n\u001b[1;32m     81\u001b[0m \u001b[38;5;124;03m    Args:\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     88\u001b[0m \u001b[38;5;124;03m        A BM25Retriever instance.\u001b[39;00m\n\u001b[1;32m     89\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m---> 90\u001b[0m     texts, metadatas \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mzip\u001b[39m(\u001b[38;5;241m*\u001b[39m((\u001b[43md\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpage_content\u001b[49m, d\u001b[38;5;241m.\u001b[39mmetadata) \u001b[38;5;28;01mfor\u001b[39;00m d \u001b[38;5;129;01min\u001b[39;00m documents))\n\u001b[1;32m     91\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39mfrom_texts(\n\u001b[1;32m     92\u001b[0m         texts\u001b[38;5;241m=\u001b[39mtexts,\n\u001b[1;32m     93\u001b[0m         bm25_params\u001b[38;5;241m=\u001b[39mbm25_params,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     96\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs,\n\u001b[1;32m     97\u001b[0m     )\n", "\u001b[0;31mAttributeError\u001b[0m: 'dict' object has no attribute 'page_content'"]}], "source": ["bm25 = BM25Retriever.from_documents(metadata)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["bm25 = BM25Retriever.from_documents(guide_splits)\n", "kiwi_bm25 = BM25Retriever.from_documents(guide_splits, preprocess_func=kiwi_tokenize)\n", "faiss = FAISS.from_documents(guide_splits, OpenAIEmbeddings()).as_retriever()\n", "bm25_faiss_73 = EnsembleRetriever(\n", "    retrievers=[bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.7, 0.3],  # 각 검색 모델의 결과에 적용할 가중치\n", "    search_type=\"mmr\",  # 검색 결과의 다양성을 증진시키는 MMR 방식을 사용\n", ")\n", "bm25_faiss_37 = EnsembleRetriever(\n", "    retrievers=[bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.7, 0.3],  # 각 검색 모델의 결과에 적용할 가중치\n", "    search_type=\"mmr\",  # 검색 결과의 다양성을 증진시키는 MMR 방식을 사용\n", ")\n", "kiwibm25_faiss_73 = EnsembleRetriever(\n", "    retrievers=[kiwi_bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.7, 0.3],  # 각 검색 모델의 결과에 적용할 가중치\n", "    search_type=\"mmr\",  # 검색 결과의 다양성을 증진시키는 MMR 방식을 사용\n", ")\n", "kiwibm25_faiss_37 = EnsembleRetriever(\n", "    retrievers=[kiwi_bm25, faiss],  # 사용할 검색 모델의 리스트\n", "    weights=[0.3, 0.7],  # 각 검색 모델의 결과에 적용할 가중치\n", "    search_type=\"mmr\",\n", "    # 검색 결과의 다양성을 증진시키는 MMR 방식을 사용\n", ")\n", "\n", "retrievers = {\n", "    \"bm25\": bm25,\n", "    \"kiwi_bm25\": kiwi_bm25,\n", "    \"faiss\": faiss,\n", "    \"bm25_faiss_73\": bm25_faiss_73,\n", "    \"bm25_faiss_37\": bm25_faiss_37,\n", "    \"kiwi_bm25_faiss_73\": kiwibm25_faiss_73,\n", "    \"kiwi_bm25_faiss_37\": kiwibm25_faiss_37,\n", "}"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def print_search_results(retrievers, query):\n", "    print(f\"Query: {query}\")\n", "    for name, retriever in retrievers.items():\n", "        print(f\"{name}    \\t: {retriever.invoke(query)}\")\n", "    print(\"===\" * 20)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "from langchain import hub\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "# prompt = hub.pull(\"rlm/rag-prompt\")\n", "\n", "# You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. keep the answer concise. When the 코드 in [question], be sure to search only documents with header 1 as 코드.\n", "template = ''' \n", "너는 GeOnPaaS 솔루션 개발자를 위한 코드 생성 assistant야.\n", "주어진 {context}를 이용해 {question}에 부합하는 코드를 만들어주되 아래의 조건을 반드시 지켜줘\n", "[[조건]]\n", "- metadata의 기능이 지도 설정 필수인 부분은 하나 이상 포함\n", "- page_content의 순서는 언어를 기준으로 html 다음 javascript가 와야해\n", "- page_content 사이사이에는 다른 내용이 들어가면 안돼\n", "\n", "\n", "Question: {question} \n", "\n", "Context: {context} \n", "\n", "Answer:\n", "'''\n", "prompt = PromptTemplate.from_template(template)\n", "\n", "llm = OllamaLLM(model = 'EEVE-Korean-10.8B:latest',\n", "                 max_tokens = 4000,\n", "                stream = True \n", "                )\n", "retriever = bm25\n", "\n", "chain = (\n", "    {\"question\": RunnablePassthrough(),\n", "     \"context\": (lambda x: retriever.get_relevant_documents(x)[:10]) \n", "     }\n", "    | prompt\n", "    | llm\n", ")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NAME                     \tID          \tSIZE  \tMODIFIED     \n", "qwen2:latest             \te0d4e1163c58\t4.4 GB\t3 days ago  \t\n", "gemma2:latest            \tff02c3702f32\t5.4 GB\t3 days ago  \t\n", "dolphin-llama3:8b        \t613f068e29f8\t4.7 GB\t5 weeks ago \t\n", "openhermes:latest        \t95477a2659b7\t4.1 GB\t5 weeks ago \t\n", "llama3:latest            \t365c0bd3c000\t4.7 GB\t5 weeks ago \t\n", "llama3-instruct-8b:latest\td8ccdc976321\t8.5 GB\t3 months ago\t\n", "EEVE-Korean-10.8B:latest \tc3ebb32b93a5\t7.7 GB\t3 months ago\t\n"]}, {"name": "stderr", "output_type": "stream", "text": ["huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n"]}], "source": ["!ollama list"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 배경지도 최적화', '언어': 'javascript', '기능': '맵객체 생성'}, page_content=\"<script>\\n/* 맵 타겟 */\\nvar mapContainer = document.getElementById('map'};  \\n/* 맵 중심점 */\\nvar coord = new odf.Coordinate(199312.9996,551784.6924};  \\n/* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.} */\\nvar mapOption = {\\ncenter : coord,\\nzoom : 11,\\nprojection : 'EPSG:5186',\\n//proxyURL: 'proxyUrl.jsp',\\n//proxyParam: 'url',\\nbaroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',\\nbaroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',  \\nbasemap : {\\nbaroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],\\n},\\n};\\nmapOption.basemap.OSM = true;\\n/*\\n* 배경지도 종류\\neMapBasic - 바로e맵 일반 지도\\neMapColor - 바로e맵 색각 지도\\neMapLowV - 바로e맵 큰글씨 지도\\neMapWhite - 바로e맵 백지도\\neMapEnglish - 바로e맵 영어 지도\\neMapChinese - 바로e맵 중어 지도\\neMapJapanese - 바로e맵 일어 지도\\neMapWhiteEdu - 바로e맵 교육용 백지도\\neMapAIR - 바로e맵  항공지도  \\n* 프록시 사용\\nproxyURL: 'proxy.jsp' 프록시 설정\\n*/  \\n/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.} */\\nvar map = new odf.Map(mapContainer, {\\n...mapOption,\\n//배경지도 최적화 on\\noptimization: true,\\n}};  \\n/* 베이스맵 컨트롤 생성 */\\nvar basemapControl = new odf.BasemapControl({}};\\nbasemapControl.setMap(map};\"),\n", " Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 분할지도 추가', '언어': 'javascript', '기능': '배경지도 생성'}, page_content='/* 배경지도 컨트롤 생성 */\\nvar basemapControl = new odf.BasemapControl(};\\nbasemapControl.setMap(map};  \\n/* 줌 컨트롤 생성 */\\nvar zoomControl = new odf.ZoomControl(};\\nzoomControl.setMap(map};'),\n", " Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 배경지도 최적화', '언어': 'html', '기능': '지도생성 필수'}, page_content='<html>\\n<head>\\n<meta charset=\"utf-8\">\\n<link href=\"https://developer.geon.kr/js/odf/odf.css\" rel=\"stylesheet\">\\n<script type=\"text/javascript\" src=\"https://developer.geon.kr/js/odf/odf.min.js\"></script>\\n</head>\\n<body>\\n<div id=\"map\" class=\"odf-view\" style=\"height:550px;\"></div>\\n<div>현재 좌표계 : <span id=\"projection\"></span></div>\\n<div>\\n※ 배경지도 변경시 좌표계가 변경되면서 콘솔로그에 변경된 좌표계 값이 찍힙니다.\\n※ 같은 그룹 끼리는 좌표계가 변경되지 않습니다.\\n</div>\\n</body>'),\n", " Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 스와이퍼 추가', '언어': 'javascript', '기능': '배경지도 생성'}, page_content=\"basemap : {\\nbaroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],\\n},\\n};\\n/*\\n* 배경지도 종류\\neMapBasic - 바로e맵 일반 지도\\neMapColor - 바로e맵 색각 지도\\neMapLowV - 바로e맵 큰글씨 지도\\neMapWhite - 바로e맵 백지도\\neMapEnglish - 바로e맵 영어 지도\\neMapChinese - 바로e맵 중어 지도\\neMapJapanese - 바로e맵 일어 지도\\neMapWhiteEdu - 바로e맵 교육용 백지도\\neMapAIR - 바로e맵  항공지도  \\n* 프록시 사용\\nproxyURL: 'proxy.jsp' 프록시 설정\\n*/  \\n/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.} */\\nvar map = new odf.Map(mapContainer, mapOption};  \\n/* 베이스맵 컨트롤 생성 */\\nvar basemapControl = new odf.BasemapControl(};\\nbasemapControl.setMap(map};\\n</script>\\n</html>\")]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["query = '배경지도 최적화 코드를 알려줘'\n", "retriever.get_relevant_documents(query)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["다음은 배경지도 최적화를 위한 코드입니다:\n", "\n", "```javascript\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "  <meta charset=\"utf-8\">\n", "  <link href=\"https://developer.geon.kr/js/odf/odf.css\" rel=\"stylesheet\">\n", "  <script type=\"text/javascript\" src=\"https://developer.geon.kr/js/odf/odf.min.js\"></script>\n", "</head>\n", "<body>\n", "  <div id=\"map\" class=\"odf-view\" style=\"height:550px;\"></div>\n", "  <div>Current Projection: <span id=\"projection\"></span></div>\n", "  <div>\n", "    ※ The projection changes when the background map is changed, and the new projection value is printed in the console.\n", "     ※ The projection does not change if they are in the same group.\n", "  </div>\n", "  <script>\n", "    // 맵 타겟\n", "    var mapContainer = document.getElementById('map');\n", "\n", "    // 맵 중심점\n", "    var coord = new odf.Coord<PERSON>(199312.9996, 551784.6924);\n", "\n", "    // 맵 객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.} )\n", "    var mapOption = {\n", "      center: coord,\n", "      zoom: 11,\n", "      projection: 'EPGS:5186',\n", "      //proxyURL: 'proxyUrl.jsp',\n", "      //proxyParam: 'url',\n", "      baroEMapURL: 'https://geon-gateway.geon.kr/map/api/map/baroeMap',\n", "      baroEMapAirURL: 'https://geon-gateway.geon.kr/map/api/map/ngisair',\n", "      baseMap: {\n", "        baroEMap: ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],\n", "      },\n", "    };\n", "    mapOption.baseMap.OSM = true;\n", "\n", "    // 배경지도 종류\n", "    // eMapBasic - 바로e맵 일반 지도\n", "    // eMapColor - 바로e맵 색각 지도\n", "    // eMapLowV - 바로e맵 큰글씨 지도\n", "    // eMapWhite - 바로e맵 백지도\n", "    // eMapEnglish - 바로e맵 영어 지도\n", "    // eMapChinese - 바로e맵 중어 지도\n", "    // eMapJapanese - 바로e맵 일어 지도\n", "    // eMapWhiteEdu - 바로e맵 교육용 백지도\n", "    // eMapAIR - 바로e맵 항공지도\n", "\n", "    // 프록시 사용\n", "    // proxyURL: 'proxy.jsp' 프록시 설정\n", "\n", "    // 맵 객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.} )\n", "    var map = new odf.Map(mapContainer, {\n", "      ...mapOption,\n", "      // 배경지도 최적화 on\n", "      optimization: true,\n", "    });\n", "\n", "    // 베이스맵 컨트롤 생성\n", "    var baseMapControl = new odf.BaseMapControl({});\n", "    baseMapControl.setMap(map);\n", "  </script>\n", "</body>\n", "</html>\n", "```\n", "\n", "이 코드는 배경지도 최적화를 위한 필요한 코드를 포함하고 있으며, 지정된 조건을 모두 충족합니다:\n", "\n", "1. metadata의 기능이 지도 설정 필수인 부분이 하나 이상 포함되어 있습니다 (baseMap 옵션).\n", "2. page_content의 순서는 언어를 기준으로 html 다음 javascript가 와야 합니다.\n", "3. page_content 사이사이에 다른 내용이 들어가면 안 됩니다.\n"]}], "source": ["query = '배경지도 최적화 코드를 알려줘'\n", "print(chain.invoke(query))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "langchain-kr-lwwSZlnu-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}