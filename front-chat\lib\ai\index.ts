import { wrapLanguageModel } from "ai";
import { customMiddleware } from "./custom-middleware";
import { models } from "./dev-models";
import { createGeon } from "@ai-sdk/geon";
import { openai } from "@ai-sdk/openai";
import { createDifyProvider } from "dify-ai-provider";

// 기본 Geon 인스턴스 (Qwen3 모델용)
export const geon = createGeon({
  baseURL: "http://121.163.19.104:8005/v1",
  apiKey: "123"
});

// Qwen2.5 14B 모델용 Geon 인스턴스
export const geonQwen25 = createGeon({
  baseURL: "http://121.163.19.104:8002/v1",
  apiKey: "123"
});

// A.X-4 Light AWQ 모델용 Geon 인스턴스
export const geonAX4 = createGeon({
  baseURL: "http://121.163.19.104:8007/v1",
  apiKey: "123"
});

// Dify provider 인스턴스 생성
export const difyProvider = createDifyProvider({
  baseURL: "https://ai-dify.geon.kr/v1",
});

export const customModel = (modelId: string) => {
  // 모델 ID에 따라 적절한 Geon 인스턴스 선택
  let geonInstance = geon; // 기본값

  if (modelId.includes('Qwen2.5-14B')) {
    geonInstance = geonQwen25;
  } else if (modelId.includes('A.X-4-Light-awq')) {
    geonInstance = geonAX4;
  }

  return wrapLanguageModel({
    model: geonInstance(modelId),
    middleware: customMiddleware,
  });
};

export const difyModel = (modelId: string) => {
  const model = models.find(m => m.id === modelId);
  if (!model) {
    throw new Error(`Model not found: ${modelId}`);
  }

  const difyModelInstance = difyProvider(model.apiIdentifier, {
    apiKey: model.apiKey,
    responseMode: "streaming",
  });

  return wrapLanguageModel({
    model: difyModelInstance,
    middleware: customMiddleware,
  });
};

export const openaiModel = (modelId: string) => {
  return wrapLanguageModel({
    model: openai(modelId),
    middleware: customMiddleware,
  });
};
