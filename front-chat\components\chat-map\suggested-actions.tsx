
import { Message, CreateMessage, ChatRequestOptions } from "ai";
import { motion } from "framer-motion";
import { memo } from "react";

const suggestedActions = [
  {
    title: "지도 제어",
    label: "웨이버스 이동",
    action: "웨이버스로 이동해주세요.",
  },
  {
    title: "필터링 요청",
    label: "노후화된 건물 확인",
    action: "서울의 노후화된 건물을 보여줘.",
  },
];

interface SuggestedActionsProps {
  append: (
    message: Message | CreateMessage,
    chatRequestOptions?: ChatRequestOptions
  ) => Promise<string | null | undefined>;
  setInput: (value: string) => void;
  chatId: string;
}

function PureSuggestedActions({ append, setInput, chatId }: SuggestedActionsProps) {
  return (
    <div className="grid sm:grid-cols-2 gap-4 w-full md:px-4 mx-auto md:max-w-[500px]">
      {suggestedActions.map((suggestedAction, index) => (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ delay: 0.05 * index }}
          key={index}
        >
          <div className={index > 1 ? "hidden sm:block" : "block bg-background rounded-2xl"}>
            <button
              type="button"
              onClick={async () => {
                setInput(suggestedAction.action);

                // append 적용 시 onToolCall 에서 지도이동이 되지 않음. 원인 찾을때까지 주석
                // window.history.replaceState({}, '', `/geon-2d-map/${chatId}`);

                // append({
                //   role: "user",
                //   content: suggestedAction.action,
                // });
              }}
              className="w-full text-left border border-zinc-200 dark:border-zinc-800 text-zinc-800 dark:text-zinc-300 rounded-2xl p-2 text-sm hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors flex flex-col"
            >
              <span className="font-medium">{suggestedAction.title}</span>
              <span className="text-zinc-500 dark:text-zinc-400">
                {suggestedAction.label}
              </span>
            </button>
          </div>
        </motion.div>
      ))}
    </div>
  );
}

// export const SuggestedActions = PureSuggestedActions;
export const SuggestedActions = memo(PureSuggestedActions, () => true);