"use client";

import React from "react";
import { Layers3, <PERSON>fo, Eye, EyeOff, Settings, Database, MapPin, Hash, Palette } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Hover<PERSON>ard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { componentStyles } from "@/lib/design-tokens";
import { UseMapReturn } from "@geon-map/odf";
import { CompactResultTrigger } from "./common/compact-result-trigger";
import { getToolDisplayInfo } from "../annotations";

interface LayerInfoResponse {
  id: string;
  name: string;
  type: string;
  visible: boolean;
  zIndex: number;
  server?: string;
  layer?: string;
  service?: string;
  bbox?: boolean;
  method?: string;
  crtfckey?: string;
  projection?: string;
  geometryType?: string;
  info: {
    lyrId: string;
    lyrNm: string;
    description?: string;
    metadata: {
      cntntsId: string;
      jobClCode?: string;
      lyrClCode: string;
      lyrTySeCode: string;
      namespace?: string;
    };
  };
  namespace?: string;
  style?: any;
  matrixSet?: string;
  opacity?: number;
  filter?: string;
  dynmFlterCndCn?: string;
}

interface LayerInfoResultProps {
  content: LayerInfoResponse | string;
  className?: string;
  mapState?: UseMapReturn;
}

const getServiceTypeColor = (service: string) => {
  const colors = {
    'wms': 'bg-blue-100/80 text-blue-700 border-blue-200/60',
    'wfs': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',
    'wmts': 'bg-purple-100/80 text-purple-700 border-purple-200/60',
    'xyz': 'bg-orange-100/80 text-orange-700 border-orange-200/60',
    'default': 'bg-neutral-100/80 text-neutral-700 border-neutral-200/60'
  };
  return colors[service as keyof typeof colors] || colors.default;
};

export function LayerInfoResult({ content, className, mapState }: LayerInfoResultProps) {
  let result: LayerInfoResponse;

  try {
    result = typeof content === 'string' ? JSON.parse(content) : content;
  } catch (e) {
    return null;
  }

  const handleToggleVisibility = () => {
    if (mapState?.layer?.toggle) {
      mapState.layer.toggle(result.id);
      toast.success(`레이어가 ${result.visible ? '숨겨졌습니다' : '표시됩니다'}`);
    } else {
      toast.info("지도가 로드되지 않았습니다");
    }
  };

  const handleLayerSettings = () => {
    toast.info("레이어 설정을 열었습니다");
  };

  // 에러 처리는 실제 데이터 구조에 맞게 수정
  if (!result || !result.id) {
    return (
      <div className={cn(componentStyles.card.error, "p-3", className)}>
        <div className="flex items-center gap-2">
          <div className={cn(componentStyles.iconContainer.sm, "bg-red-100/80 text-red-600 border border-red-200/60")}>
            <Info className="h-3 w-3" />
          </div>
          <div>
            <p className="font-medium text-red-900 text-sm">레이어 정보를 가져올 수 없습니다</p>
            <p className="text-xs text-red-700">
              레이어가 존재하지 않거나 접근할 수 없습니다
            </p>
          </div>
        </div>
      </div>
    );
  }

  const toolInfo = getToolDisplayInfo("getLayerInfo");

  return (
    <CompactResultTrigger
      icon={toolInfo.icon}
      title={toolInfo.label}
      state="result"
      className={className}
      titleExtra={
        <div className="flex items-center gap-1">
          {result.service && (
            <Badge variant="outline" className={cn("text-xs py-0 border", getServiceTypeColor(result.service))}>
              {result.service.toUpperCase()}
            </Badge>
          )}
          <Badge
            variant="outline"
            className={cn(
              "text-xs border py-0",
              result.visible
                ? "bg-emerald-100/80 text-emerald-700 border-emerald-200/60"
                : "bg-neutral-100/80 text-neutral-700 border-neutral-200/60"
            )}
          >
            {result.visible ? "표시" : "숨김"}
          </Badge>
        </div>
      }
    >
      {/* 레이어 기본 정보 */}
      <div className="space-y-2">
        <div className="text-xs text-neutral-600">
          ID: {result.info?.lyrId || result.id}
        </div>

        {/* 상세 정보 그리드 */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <Layers3 className="h-3 w-3 text-neutral-500" />
              <span className="font-medium text-neutral-700">타입</span>
            </div>
            <Badge variant="outline" className="text-xs h-5">
              {result.type || 'Unknown'}
            </Badge>
          </div>

          {result.service && (
            <div className="space-y-1">
              <div className="flex items-center gap-1">
                <Database className="h-3 w-3 text-neutral-500" />
                <span className="font-medium text-neutral-700">서비스</span>
              </div>
              <Badge variant="outline" className={cn("text-xs h-5", getServiceTypeColor(result.service))}>
                {result.service.toUpperCase()}
              </Badge>
            </div>
          )}

          {result.opacity !== undefined && (
            <div className="space-y-1">
              <div className="flex items-center gap-1">
                <Palette className="h-3 w-3 text-neutral-500" />
                <span className="font-medium text-neutral-700">투명도</span>
              </div>
              <p className="text-neutral-600">{Math.round(result.opacity * 100)}%</p>
            </div>
          )}

          {result.zIndex !== undefined && (
            <div className="space-y-1">
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3 text-neutral-500" />
                <span className="font-medium text-neutral-700">Z-Index</span>
              </div>
              <p className="text-neutral-600">{result.zIndex}</p>
            </div>
          )}

          {result.projection && (
            <div className="space-y-1 col-span-2">
              <div className="flex items-center gap-1">
                <Database className="h-3 w-3 text-neutral-500" />
                <span className="font-medium text-neutral-700">좌표계</span>
              </div>
              <p className="text-neutral-600 font-mono text-xs">{result.projection}</p>
            </div>
          )}
        </div>

        {/* 메타데이터 정보 */}
        {result.info?.metadata && (
          <div className="pt-2 border-t border-neutral-200">
            <h5 className="text-xs font-medium text-neutral-700 mb-2">메타데이터</h5>
            <div className="space-y-1 text-xs">
              {result.info.metadata.cntntsId && (
                <div className="flex justify-between">
                  <span className="text-neutral-500">콘텐츠 ID</span>
                  <span className="text-neutral-700 font-mono">{result.info.metadata.cntntsId}</span>
                </div>
              )}
              {result.info.metadata.lyrClCode && (
                <div className="flex justify-between">
                  <span className="text-neutral-500">레이어 분류</span>
                  <span className="text-neutral-700">{result.info.metadata.lyrClCode}</span>
                </div>
              )}
              {result.info.metadata.lyrTySeCode && (
                <div className="flex justify-between">
                  <span className="text-neutral-500">레이어 유형</span>
                  <span className="text-neutral-700">{result.info.metadata.lyrTySeCode}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 설명 */}
        {result.info?.description && (
          <div className="pt-2 border-t border-neutral-200">
            <h5 className="text-xs font-medium text-neutral-700 mb-1">설명</h5>
            <p className="text-xs text-neutral-600">{result.info.description}</p>
          </div>
        )}
      </div>
    </CompactResultTrigger>
  );
}
