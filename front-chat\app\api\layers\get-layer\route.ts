import { NextRequest, NextResponse } from "next/server";
import { getLayer } from "@geon-ai/tools";
import { getApiConfig } from "@/lib/api-config";

export async function POST(request: NextRequest) {
  try {
    const { lyrId } = await request.json();

    if (!lyrId) {
      return NextResponse.json(
        { error: "레이어 ID가 필요합니다" },
        { status: 400 }
      );
    }

    const config = getApiConfig();

    // 디버깅을 위한 로깅
    console.log("=== getLayer API 호출 시작 ===");
    console.log("lyrId:", lyrId);
    console.log("userId: admin (AI 대화와 동일)");
    console.log("insttCode: geonpaas (AI 대화와 동일)");
    console.log("userSeCode: 14");
    console.log("API Key:", config.headers.crtfckey ? "존재함" : "없음");
    console.log("Base URL:", config.baseUrl);

    // getLayer 도구 실행 (AI 대화와 동일한 파라미터 사용)
    const result = await getLayer.execute({
      userId: "admin",
      insttCode: "geonpaas",
      userSeCode: "14",
      lyrId: lyrId
    }, {
      abortSignal: new AbortController().signal,
      toolCallId: `api-layer-add-${lyrId}-${Date.now()}`,
      messages: []
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error("=== 레이어 조회 실패 ===");
    console.error("Error:", error);
    console.error("Stack:", error instanceof Error ? error.stack : "No stack");

    return NextResponse.json(
      { error: "레이어 조회에 실패했습니다" },
      { status: 500 }
    );
  }
}
