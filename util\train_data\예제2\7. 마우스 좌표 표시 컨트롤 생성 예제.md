# 지도 라이브러리 문서

## 기능 : 마우스 좌표 표시 컨트롤 생성

**설명**: 기본 지도를 생성한 후 마우스 좌표 표시 컨트롤(MousePositionControl)을 추가합니다.

**입력**:
- `map` (Map): 지도 객체
- `showCoordinateTargetId` (Map): 지도 객체

**출력**:
- 생성된 마우스 좌표 표시 컨트롤 객체(mousePositionControl).


**코드 예제**:
```javascript
//마우스 좌표 표시 컨트롤 생성
var mousePositionControl  = new odf.MousePositionControl({
    //좌표값을 특정 element에 표시
    element: document.getElementById(showCoordinateTargetId),
    //좌표값을 callback의 매개변수로 넘김
    //callback: function (position) {
    // console.log(position);
    //},
});
//생성한 마우스 좌표 표시 컨트롤을 지도 객체에 추가
mousePositionControl.setMap(map);
```
