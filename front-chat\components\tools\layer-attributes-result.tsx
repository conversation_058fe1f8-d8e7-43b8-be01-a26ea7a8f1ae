"use client";

import React, { useState } from "react";
import { Database, Hash, Type, Calendar, MapPin, Eye } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { UseMapReturn } from "@geon-map/odf";
import { CompactResultTrigger } from "./common/compact-result-trigger";
import { LayerAttributesDetailDialog } from "./layer-attributes-detail-dialog";

interface LayerColumn {
  name: string;
  description: string;
  type: string;
  editable: boolean;
  required: boolean;
  minValue: number | null;
  maxValue: number | null;
  groupCode: string | null;
  groupName: string | null;
}

interface LayerAttributesResponse {
  columns: LayerColumn[];
  data: Record<string, any>[];
  error?: string;
  status?: string;
  layerInfo?: {
    lyrId: string;
    cntntsId: string;
    namespace: string;
    userId?: string;
  };
}

interface LayerAttributesResultProps {
  content: LayerAttributesResponse | string;
  className?: string;
  mapState?: UseMapReturn;
  invocation?: {
    args?: {
      userId?: string;
      lyrId?: string;
      namespace?: string;
      cntntsId?: string;
    };
  };
}

// 속성 타입에 따른 색상 반환
const getAttributeTypeColor = (type: string) => {
  const colors = {
    'string': 'bg-blue-100/80 text-blue-700 border-blue-200/60',
    'text': 'bg-blue-100/80 text-blue-700 border-blue-200/60',
    'varchar': 'bg-blue-100/80 text-blue-700 border-blue-200/60',
    'integer': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',
    'int': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',
    'number': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',
    'double': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',
    'float': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',
    'date': 'bg-purple-100/80 text-purple-700 border-purple-200/60',
    'datetime': 'bg-purple-100/80 text-purple-700 border-purple-200/60',
    'timestamp': 'bg-purple-100/80 text-purple-700 border-purple-200/60',
    'geometry': 'bg-orange-100/80 text-orange-700 border-orange-200/60',
    'point': 'bg-orange-100/80 text-orange-700 border-orange-200/60',
    'polygon': 'bg-orange-100/80 text-orange-700 border-orange-200/60',
    'boolean': 'bg-amber-100/80 text-amber-700 border-amber-200/60',
  };

  const lowerType = type.toLowerCase();
  return colors[lowerType as keyof typeof colors] || 'bg-neutral-100/80 text-neutral-700 border-neutral-200/60';
};

// 속성 타입에 따른 아이콘 반환
const getAttributeIcon = (type: string) => {
  const lowerType = type.toLowerCase();
  if (lowerType.includes('string') || lowerType.includes('text') || lowerType.includes('varchar')) {
    return <Type className="h-3 w-3" />;
  } else if (lowerType.includes('number') || lowerType.includes('int') || lowerType.includes('double') || lowerType.includes('float')) {
    return <Hash className="h-3 w-3" />;
  } else if (lowerType.includes('date') || lowerType.includes('time')) {
    return <Calendar className="h-3 w-3" />;
  } else if (lowerType.includes('geometry') || lowerType.includes('point') || lowerType.includes('polygon')) {
    return <MapPin className="h-3 w-3" />;
  }
  return <Database className="h-3 w-3" />;
};

export function LayerAttributesResult({ content, className, invocation }: LayerAttributesResultProps) {
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);

  let result: LayerAttributesResponse;

  try {
    if (typeof content === 'string') {
      result = JSON.parse(content);
    } else {
      result = content;
    }
  } catch (error) {
    return (
      <CompactResultTrigger
        icon={<Database className="h-4 w-4" />}
        title="레이어 속성 조회"
        state="result"
        className={className}
      >
        <div className="text-red-600 text-sm">
          결과를 파싱하는 중 오류가 발생했습니다: {error instanceof Error ? error.message : '알 수 없는 오류'}
        </div>
      </CompactResultTrigger>
    );
  }

  if (result.error) {
    return (
      <CompactResultTrigger
        icon={<Database className="h-4 w-4" />}
        title="레이어 속성 조회"
        state="result"
        className={className}
      >
        <div className="text-red-600 text-sm">
          {result.error}
        </div>
      </CompactResultTrigger>
    );
  }

  // invocation.args에서 레이어 정보 추출
  const getLayerInfoFromArgs = () => {
    if (result.layerInfo) {
      return result.layerInfo;
    }

    if (invocation?.args) {
      const { userId = 'geonuser', lyrId, namespace, cntntsId } = invocation.args;
      if (lyrId && namespace && cntntsId) {
        return {
          lyrId,
          cntntsId,
          namespace,
          userId,
        };
      }
    }

    return null;
  };

  const layerInfo = getLayerInfoFromArgs();

  return (
    <CompactResultTrigger
      icon={<Database className="h-4 w-4" />}
      title="레이어 속성 조회"
      state="result"
      className={className}
    >
      <div className="space-y-2">
        {/* 주요 속성 미리보기 - 컴팩트 버전 */}
        <div className="space-y-1">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-neutral-700">주요 속성 ({result.columns.length}개)</span>
            <Button
              variant="outline"
              size="sm"
              className="h-6 text-xs px-2"
              onClick={() => setIsDetailDialogOpen(true)}
            >
              <Eye className="h-3 w-3 mr-1" />
              자세히 보기
            </Button>
          </div>

          <div className="grid gap-1">
            {result.columns.slice(0, 3).map((column, index) => (
              <div key={index} className="flex items-center gap-2 p-1.5 bg-neutral-50/60 rounded border border-neutral-200/40">
                <div className="flex items-center gap-1.5 flex-1">
                  {getAttributeIcon(column.type)}
                  <span className="text-xs font-medium text-neutral-900">{column.name}</span>
                  <Badge variant="outline" className={cn("text-xs px-1 py-0", getAttributeTypeColor(column.type))}>
                    {column.type}
                  </Badge>
                </div>
                {column.description && column.description !== column.name && (
                  <span className="text-xs text-neutral-600 truncate max-w-[100px]">{column.description}</span>
                )}
              </div>
            ))}

            {result.columns.length > 3 && (
              <div className="text-center py-0.5">
                <span className="text-xs text-neutral-500">... 및 {result.columns.length - 3}개 속성 더</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 상세 정보 Dialog */}
      <LayerAttributesDetailDialog
        isOpen={isDetailDialogOpen}
        onOpenChange={setIsDetailDialogOpen}
        columns={result.columns}
        data={result.data}
        layerInfo={layerInfo}
      />
    </CompactResultTrigger>
  );
}