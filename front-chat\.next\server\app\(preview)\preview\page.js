(()=>{var e={};e.id=541,e.ids=[541],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3084:(e,t,s)=>{Promise.resolve().then(s.bind(s,76285)),Promise.resolve().then(s.bind(s,38326))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8115:(e,t,s)=>{"use strict";s.d(t,{Chat:()=>ep});var a=s(84464),r=s(61765),i=s(63185),n=s(69519),l=s(86270),o=s(3824);let d=(0,o.createServerReference)("40b8bc00089ac88f5caa786935681a3e95192f64cd",o.callServer,void 0,o.findSourceMapURL,"saveDevModelId");var c=s(96684),u=s(64051);let m=[{id:"지자체 공간정보 플랫폼 챗봇",label:"지자체 공간정보 플랫폼 챗봇",apiIdentifier:"EIjFYMz0dmL2HxkQJuBifqvF",description:"지자체 공간정보 플랫폼 챗봇",apiKey:"app-Hd682MZtRJh95QtTUe5H9aCl"},{id:"지도개발 어시스턴트",label:"지도개발 어시스턴트",apiIdentifier:"EIjFYMz0dmL2HxkQJuBifqvF",description:"지도개발을 위한 문서를 학습한 어시스턴트",apiKey:"app-EIjFYMz0dmL2HxkQJuBifqvF"}];var p=s(72487),x=s(95709),h=s(34594);function f({selectedModelId:e,className:t}){let[s,r]=(0,i.useState)(!1),[n,l]=(0,i.useOptimistic)(e),o=(0,i.useMemo)(()=>m.find(e=>e.id===n),[n]);return(0,a.jsxs)(u.rI,{open:s,onOpenChange:r,children:[(0,a.jsx)(u.ty,{asChild:!0,className:(0,p.cn)("w-fit data-[state=open]:bg-accent data-[state=open]:text-accent-foreground",t),children:(0,a.jsxs)(c.$,{variant:"outline",className:"md:px-2 md:h-[34px]",children:[o?.label,(0,a.jsx)(x.A,{})]})}),(0,a.jsx)(u.SQ,{align:"start",className:"min-w-[300px]",children:m.map(e=>(0,a.jsxs)(u._2,{onSelect:()=>{r(!1),(0,i.startTransition)(()=>{l(e.id),d(e.id)})},className:"gap-4 group/item flex flex-row justify-between items-center","data-active":e.id===n,children:[(0,a.jsxs)("div",{className:"flex flex-col gap-1 items-start",children:[e.label,e.description&&(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]}),(0,a.jsx)("div",{className:"text-primary dark:text-primary-foreground opacity-0 group-data-[active=true]/item:opacity-100",children:(0,a.jsx)(h.A,{})})]},e.id))})]})}var g=s(84371),v=s(63547),y=s(30569);let j=(0,i.memo)(function({chatId:e,selectedModelId:t,isReadonly:s}){let r=(0,l.useRouter)(),{width:i}=(0,n.lW)();return(0,a.jsxs)("header",{className:"flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2",children:[(0,a.jsxs)(g.m_,{children:[(0,a.jsx)(g.k$,{asChild:!0,children:(0,a.jsxs)(c.$,{variant:"outline",className:"order-2 md:order-2 md:px-2 px-2 md:h-fit ml-auto md:ml-0",onClick:()=>{r.refresh()},children:[(0,a.jsx)(v.A,{}),(0,a.jsx)("span",{className:"md:sr-only",children:"새 대화"})]})}),(0,a.jsx)(g.ZI,{children:"새 대화"})]}),(0,a.jsx)(f,{selectedModelId:t,className:"order-1 md:order-1"}),(0,a.jsx)(y.U,{className:"order-3 ml-auto"})]})},(e,t)=>e.selectedModelId===t.selectedModelId);var b=s(55703);let w=({size:e=16})=>(0,a.jsx)("svg",{height:e,strokeLinejoin:"round",viewBox:"0 0 16 16",width:e,style:{color:"currentcolor"},children:(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.75 0.189331L12.2803 0.719661L15.2803 3.71966L15.8107 4.24999L15.2803 4.78032L5.15901 14.9016C4.45575 15.6049 3.50192 16 2.50736 16H0.75H0V15.25V13.4926C0 12.4981 0.395088 11.5442 1.09835 10.841L11.2197 0.719661L11.75 0.189331ZM11.75 2.31065L9.81066 4.24999L11.75 6.18933L13.6893 4.24999L11.75 2.31065ZM2.15901 11.9016L8.75 5.31065L10.6893 7.24999L4.09835 13.841C3.67639 14.2629 3.1041 14.5 2.50736 14.5H1.5V13.4926C1.5 12.8959 1.73705 12.3236 2.15901 11.9016ZM9 16H16V14.5H9V16Z",fill:"currentColor"})}),N=({size:e=16})=>(0,a.jsx)("svg",{height:e,strokeLinejoin:"round",viewBox:"0 0 16 16",width:e,style:{color:"currentcolor"},children:(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.70711 1.39644C8.31659 1.00592 7.68342 1.00592 7.2929 1.39644L2.21968 6.46966L1.68935 6.99999L2.75001 8.06065L3.28034 7.53032L7.25001 3.56065V14.25V15H8.75001V14.25V3.56065L12.7197 7.53032L13.25 8.06065L14.3107 6.99999L13.7803 6.46966L8.70711 1.39644Z",fill:"currentColor"})}),k=({size:e=16})=>(0,a.jsx)("svg",{height:e,viewBox:"0 0 16 16",width:e,style:{color:"currentcolor"},children:(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3 3H13V13H3V3Z",fill:"currentColor"})});var C=s(51043),I=s(40198),M=s(11466);let P={"지자체 공간정보 플랫폼 챗봇":[{title:"지원 데이터",label:"어떤 데이터를 지원하나요?",action:"어떤 데이터를 지원하나요?"},{title:"플랫폼 목적",label:"무엇을 위한 플랫폼인가요?",action:"무엇을 위한 플랫폼인가요?"},{title:"지도공간 기능",label:"지도공간에서는 어떤 기능을 볼 수 있나요?",action:"지도공간에서는 어떤 기능을 볼 수 있나요?"},{title:"업무공간 서비스",label:"업무공간에는 어떤 서비스가 있나요?",action:"업무공간에는 어떤 서비스가 있나요?"}],"지도개발 어시스턴트":[{title:"지도 생성하기",label:"ODF를 활용한 지도 만들기",action:"ODF를 사용해서 지도를 생성하는 방법을 알려주세요."},{title:"배경지도 컨트롤 추가",label:"지도에 배경지도 컨트롤을 추가하는 방법",action:"배경지도 컨트롤을 추가한 지도를 보여주세요."}]},L=(0,i.memo)(function({chatId:e,append:t,selectedModelId:s="지도개발 어시스턴트"}){let r=P[s]||P["지도개발 어시스턴트"],i="지자체 공간정보 플랫폼 챗봇"===s;return(0,a.jsx)("div",{className:"grid sm:grid-cols-2 gap-2 w-full",children:r.map((e,s)=>(0,a.jsx)(M.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{delay:.05*s},className:!i&&s>1?"hidden sm:block":"block",children:(0,a.jsxs)(c.$,{variant:"ghost",onClick:async()=>{t({role:"user",content:e.action})},className:"text-left border rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start",children:[(0,a.jsx)("span",{className:"font-medium",children:e.title}),(0,a.jsx)("span",{className:"text-muted-foreground",children:e.label})]})},`suggested-action-${e.title}-${s}`))})},()=>!0);var R=s(52105),_=s.n(R);let S=(0,i.memo)(function({chatId:e,input:t,setInput:s,isLoading:r,stop:l,attachments:o,setAttachments:d,messages:c,setMessages:u,append:m,handleSubmit:x,className:h,selectedModelId:f}){let g=(0,i.useRef)(null),{width:v}=(0,n.lW)(),y=()=>{g.current&&(g.current.style.height="auto",g.current.style.height=`${g.current.scrollHeight+2}px`)},[j,w]=(0,n.Mj)("input",""),N=(0,i.useRef)(null),[k,M]=(0,i.useState)([]),P=(0,i.useCallback)(()=>{x(void 0,{experimental_attachments:o}),d([]),w(""),v&&v>768&&g.current?.focus()},[o,x,d,w,v,e]),R=async e=>{let t=new FormData;t.append("file",e);try{let e=await fetch("/api/files/upload",{method:"POST",body:t});if(e.ok){let{url:t,pathname:s,contentType:a}=await e.json();return{url:t,name:s,contentType:a}}let{error:s}=await e.json();b.oR.error(s)}catch(e){b.oR.error("Failed to upload file, please try again!")}},_=(0,i.useCallback)(async e=>{let t=Array.from(e.target.files||[]);M(t.map(e=>e.name));try{let e=t.map(e=>R(e)),s=(await Promise.all(e)).filter(e=>void 0!==e);d(e=>[...e,...s])}catch(e){console.error("Error uploading files!",e)}finally{M([])}},[d]);return(0,a.jsxs)("div",{className:"relative w-full flex flex-col gap-4",children:[0===c.length&&0===o.length&&0===k.length&&(0,a.jsx)(L,{append:m,chatId:e,selectedModelId:f}),(0,a.jsx)("input",{type:"file",className:"fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none",ref:N,multiple:!0,onChange:_,tabIndex:-1}),(o.length>0||k.length>0)&&(0,a.jsxs)("div",{className:"flex flex-row gap-2 overflow-x-scroll items-end",children:[o.map(e=>(0,a.jsx)(C.q,{attachment:e},e.url)),k.map(e=>(0,a.jsx)(C.q,{attachment:{url:"",name:e,contentType:""},isUploading:!0},e))]}),(0,a.jsxs)("div",{className:"ai-textarea-border w-full rounded-[28px] p-1",children:[(0,a.jsx)(I.T,{ref:g,placeholder:"자유롭게 질문해보세요.",value:t,onChange:e=>{s(e.target.value),y()},className:(0,p.cn)("min-h-[24px] max-h-[calc(75dvh)] overflow-hidden resize-none rounded-[28px] p-4 pr-12 !text-base bg-background",h),rows:3,autoFocus:!0,onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),r?b.oR.error("응답이 완료되기 전까지 잠시만 기다려주세요."):P())}}),r?(0,a.jsx)(A,{stop:l,setMessages:u}):(0,a.jsx)(E,{input:t,submitForm:P,uploadQueue:k})]})]})},(e,t)=>e.input===t.input&&e.isLoading===t.isLoading&&!!_()(e.attachments,t.attachments)),A=(0,i.memo)(function({stop:e,setMessages:t}){return(0,a.jsx)(c.$,{className:"rounded-full p-1.5 h-fit absolute bottom-10 right-4 m-0.5 border dark:border-zinc-600",onClick:t=>{t.preventDefault(),e()},children:(0,a.jsx)(k,{size:14})})}),E=(0,i.memo)(function({submitForm:e,input:t,uploadQueue:s}){return(0,a.jsx)(c.$,{className:"rounded-full p-1.5 h-fit absolute bottom-10 right-4 m-0.5 border dark:border-zinc-600",onClick:t=>{t.preventDefault(),e()},disabled:0===t.length||s.length>0,children:(0,a.jsx)(N,{size:14})})},(e,t)=>e.uploadQueue.length===t.uploadQueue.length&&!e.input==!t.input);var z=s(67443),O=s(48134),$=s(38035),H=s(46655),q=s(50847),D=s(25883);let T=({sources:e})=>{let[t,s]=(0,i.useState)(!1),[r,n]=(0,i.useState)(null),l=t=>{let a=e.flatMap(e=>e.metadata.map((t,s)=>({content:e.document[s],relevance:100*e.distances[s],fileName:t.name})).filter(e=>e.fileName===t));n({name:t,contents:a}),s(!0)};return(0,a.jsxs)("div",{className:"w-full max-w-2xl",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("div",{className:"text-sm flex items-center gap-1",children:[(0,a.jsx)(q.A,{size:16}),(0,a.jsx)("span",{children:"참고 문서"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:(()=>{let t=new Map;return e.forEach(({metadata:e,distances:s})=>{e.forEach((e,a)=>{let r=e.name,i=100*s[a];if(t.has(r)){let e=t.get(r);t.set(r,{name:r,relevance:e.relevance+i,count:e.count+1})}else t.set(r,{name:r,relevance:i,count:1})})}),Array.from(t.entries()).map(([e,t])=>({name:t.name,avgRelevance:t.relevance/t.count}))})().map(e=>(0,a.jsxs)("button",{onClick:()=>l(e.name),className:"inline-flex items-center gap-2 px-3 py-1 text-sm rounded-full border hover:opacity-80 transition-opacity",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsxs)("span",{className:"text-xs opacity-70",children:[e.avgRelevance.toFixed(1),"%"]})]},e.name))})]}),(0,a.jsx)(H.Lt,{open:t,onOpenChange:s,children:(0,a.jsxs)(H.EO,{children:[(0,a.jsxs)(H.wd,{className:"relative",children:[(0,a.jsx)(H.r7,{className:"pr-8",children:r?.name}),(0,a.jsx)("button",{onClick:()=>s(!1),className:"absolute right-0 top-0 p-1 rounded-full hover:opacity-70 transition-opacity",children:(0,a.jsx)(D.A,{size:18})})]}),(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto",children:r?.contents.map((e,t)=>(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("div",{className:"h-1 flex-grow rounded-full overflow-hidden border",children:(0,a.jsx)("div",{className:"h-full transition-all",style:{width:`${e.relevance}%`,backgroundColor:`hsl(${1.2*e.relevance}, 70%, 50%)`}})}),(0,a.jsxs)("span",{className:"text-xs opacity-70",children:[e.relevance.toFixed(1),"%"]})]}),(0,a.jsx)("div",{className:"border-l-2 pl-4 py-2",children:(0,a.jsx)(z.o,{children:e.content})})]},t))}),(0,a.jsx)(H.Rx,{onClick:()=>s(!1),className:"mt-4",children:"닫기"})]})})]})},F={src:"/_next/static/media/main-logo.7edc5622.png"},B=(0,i.memo)(({chatId:e,message:t,vote:s,isLoading:r,setMessages:n,reload:l,isReadonly:o})=>{let[d,u]=(0,i.useState)("view");return(0,a.jsx)(M.P.div,{className:"w-full mx-auto max-w-3xl px-4 group/message",initial:{y:5,opacity:0},animate:{y:0,opacity:1},"data-role":t.role,children:(0,a.jsxs)("div",{className:(0,p.cn)("flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl",{"w-full":"edit"===d,"group-data-[role=user]/message:w-fit":"edit"!==d}),children:["assistant"===t.role&&(0,a.jsx)("div",{className:"size-8 flex items-center rounded-full justify-center shrink-0 bg-background",children:(0,a.jsx)("div",{className:"rounded-full",children:(0,a.jsx)("img",{src:F.src,alt:"Bot",className:"w-full h-full object-cover"})})}),(0,a.jsxs)("div",{className:"flex flex-col gap-2 w-full",children:[t.experimental_attachments&&(0,a.jsx)("div",{className:"flex flex-row justify-end gap-2",children:t.experimental_attachments.map(e=>(0,a.jsx)(C.q,{attachment:e},e.url))}),t.content&&"view"===d&&(0,a.jsxs)("div",{className:"flex flex-row gap-2 items-start",children:["user"===t.role&&!o&&(0,a.jsxs)(g.m_,{children:[(0,a.jsx)(g.k$,{asChild:!0,children:(0,a.jsx)(c.$,{variant:"ghost",className:"px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100",onClick:()=>{u("edit")},children:(0,a.jsx)(w,{})})}),(0,a.jsx)(g.ZI,{children:"Edit message"})]}),(0,a.jsx)("div",{className:(0,p.cn)("flex flex-col gap-4",{"bg-primary text-primary-foreground px-3 py-2 rounded-xl":"user"===t.role}),children:(0,a.jsx)(z.o,{children:t.content})})]}),t.content&&"edit"===d&&(0,a.jsxs)("div",{className:"flex flex-row gap-2 items-start",children:[(0,a.jsx)("div",{className:"size-8"}),(0,a.jsx)($.j,{message:t,setMode:u,setMessages:n,reload:l},t.id)]}),t.toolInvocations&&t.toolInvocations.length>0&&(0,a.jsx)("div",{className:"flex flex-col gap-4",children:t.toolInvocations.map(e=>{let{toolName:t,toolCallId:s,state:r,args:i}=e;return(0,a.jsxs)("div",{children:["  ","knowledgeBase"===t&&(0,a.jsx)(T,{sources:i})]},s)})}),!o&&(0,a.jsx)(O.P,{chatId:e,message:t,vote:s,isLoading:r},`action-${t.id}`)]})]})})},(e,t)=>e.isLoading===t.isLoading&&e.message.content===t.message.content&&!!_()(e.message.toolInvocations,t.message.toolInvocations)&&!!_()(e.vote,t.vote)),V=()=>(0,a.jsx)(M.P.div,{className:"w-full mx-auto max-w-3xl px-4 group/message",initial:{y:5,opacity:0},animate:{y:0,opacity:1,transition:{delay:1}},"data-role":"assistant",children:(0,a.jsxs)("div",{className:(0,p.cn)("flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl",{"group-data-[role=user]/message:bg-muted":!0}),children:[(0,a.jsx)("div",{className:"size-8 flex items-center rounded-full justify-center shrink-0 bg-background",children:(0,a.jsx)("div",{className:"rounded-full",children:(0,a.jsx)("img",{src:F.src,alt:"Bot",className:"w-full h-full object-cover"})})}),(0,a.jsx)("div",{className:"flex flex-col gap-2 w-full",children:(0,a.jsx)("div",{className:"flex flex-col gap-4 mt-1 text-muted-foreground animate-pulse",children:"지식 기반 검색 중입니다..."})})]})});var J=s(23292),Q=s(71410);let Z=(0,Q.A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]),G=(0,Q.A)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);var Y=s(30346),U=s(48034),K=s(44132);let W={"지자체 공간정보 플랫폼 챗봇":{icon:Z,title:"지자체 공간정보 플랫폼 챗봇",description:"플랫폼에 대해 궁금한 점을 여쭤보세요!",accordionTitle:"플랫폼 지원 범위",sections:[{title:"지원 데이터 종류"},{title:"플랫폼 목적 및 기능"},{title:"지도공간 서비스"},{title:"업무공간 서비스"}],footerText:"지자체 공간정보 플랫폼에 대한 자세한 정보를 제공해드립니다."},"지도개발 어시스턴트":{icon:G,title:"지도개발 어시스턴트",description:"지도 개발과 관련된 질문을 해보세요",accordionTitle:"개발 지원 범위",sections:[{title:"지도 생성 방법"},{title:"레이어 생성"},{title:"이벤트 처리"},{title:"컨트롤/UI"}],footerText:"코드 예제와 함께 자세한 설명을 제공해드립니다."}},X=({selectedModelId:e="지도개발 어시스턴트"})=>{let t=W[e]||W["지도개발 어시스턴트"],s=t.icon;return(0,a.jsx)(M.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.3},className:"w-full max-w-sm backdrop-blur-sm mx-auto bg-gradient-to-b from-background/10 to-background/80 rounded-xl",children:(0,a.jsx)(U.Zp,{className:"border-none shadow-none bg-transparent",children:(0,a.jsxs)(U.Wu,{className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(M.P.div,{className:"flex items-center justify-center gap-4",initial:{scale:.9},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:150},children:[(0,a.jsx)(s,{size:28,className:"text-primary"}),(0,a.jsx)("span",{className:"font-bold text-2xl",children:"+"}),(0,a.jsx)(Y.A,{size:28,className:"text-primary"})]}),(0,a.jsx)(M.P.div,{className:"text-center mt-4",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:(0,a.jsx)("h2",{className:"text-lg font-semibold bg-clip-text bg-gradient-to-r from-primary to-primary/80",children:t.title})})]}),(0,a.jsxs)(M.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-sm text-center",children:t.description}),(0,a.jsx)(K.nD,{type:"single",collapsible:!0,defaultValue:"examples",className:"flex justify-center bg-background/40 rounded-lg",children:(0,a.jsxs)(K.As,{value:"examples",className:"border-none",children:[(0,a.jsx)(K.$m,{className:"justify-center gap-2 py-3 hover:no-underline",children:(0,a.jsx)("span",{className:"text-sm font-medium",children:t.accordionTitle})}),(0,a.jsx)(K.ub,{className:"py-4",children:(0,a.jsx)(M.P.div,{className:"space-y-6",variants:{hidden:{opacity:0},show:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"show",children:t.sections.map((e,t)=>(0,a.jsx)(M.P.div,{variants:{hidden:{opacity:0,y:10},show:{opacity:1,y:0}},className:"space-y-2",children:(0,a.jsx)("h3",{className:"text-sm font-medium text-primary",children:e.title})},t))})})]})})]}),(0,a.jsx)(M.P.p,{className:"text-xs text-center text-muted-foreground",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:t.footerText})]})})},`overview-${e}`)},ee=(0,i.memo)(function({chatId:e,isLoading:t,votes:s,messages:r,setMessages:i,reload:n,isReadonly:l,selectedModelId:o}){let[d,c]=(0,J.R)();return(0,a.jsxs)("div",{ref:d,className:"flex flex-col min-w-0 gap-6 flex-1 overflow-y-auto px-4 pt-4 styled-scrollbar",children:[0===r.length&&(0,a.jsx)(X,{selectedModelId:o}),r.map((o,d)=>(0,a.jsx)(B,{chatId:e,message:o,isLoading:t&&r.length-1===d,vote:s?s.find(e=>e.messageId===o.id):void 0,setMessages:i,reload:n,isReadonly:l},o.id)),t&&r.length>0&&"user"===r[r.length-1].role&&(0,a.jsx)(V,{}),(0,a.jsx)("div",{ref:c,className:"shrink-0 min-w-[24px] min-h-[24px]"})]})},(e,t)=>e.isLoading===t.isLoading&&(!e.isLoading||!t.isLoading)&&e.messages.length===t.messages.length&&!!_()(e.votes,t.votes)&&e.selectedModelId===t.selectedModelId);var et=s(37692),es=s(5996),ea=s(73851),er=s(73094);let ei=(0,Q.A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var en=s(65363),el=s(73996),eo=s(66170);let ed=(0,i.memo)(({selected:e,label:t,onClick:s})=>(0,a.jsx)("button",{onClick:s,className:(0,p.cn)("px-4 py-2 text-sm font-medium transition-colors",e?"text-foreground border-b-2 border-primary":"text-muted-foreground hover:text-foreground"),children:t}));ed.displayName="TabButton";let ec=(0,i.memo)(({logs:e})=>(0,a.jsx)("div",{className:"font-mono text-sm p-4 space-y-2 h-full overflow-auto styled-scrollbar",children:e.map((e,t)=>(0,a.jsxs)("div",{className:(0,p.cn)("flex items-start gap-2",{"text-red-500":"error"===e.type,"text-yellow-500":"warn"===e.type}),children:[(0,a.jsx)("span",{className:"text-muted-foreground text-xs",children:new Date(e.timestamp).toLocaleTimeString()}),(0,a.jsx)("span",{children:e.content})]},t))}));ec.displayName="ConsoleOutput";let eu=e=>`
  <!DOCTYPE html>
  <html>
    <head>
      <script>
        const originalConsole = {
          log: console.log,
          error: console.error,
          warn: console.warn
        };
        
        function captureConsole(type, args) {
          window.parent.postMessage({
            type: 'console',
            logType: type,
            content: Array.from(args).map(arg => 
              typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' '),
            timestamp: Date.now()
          }, '*');
        }
        
        console.log = (...args) => {
          originalConsole.log(...args);
          captureConsole('log', args);
        };
        console.error = (...args) => {
          originalConsole.error(...args);
          captureConsole('error', args);
        };
        console.warn = (...args) => {
          originalConsole.warn(...args);
          captureConsole('warn', args);
        };
      </script>
    </head>
    <body>
      ${e}
    </body>
  </html>
`,em=(0,i.memo)(function({isReadonly:e=!1,isVisible:t=!1}){let{preview:s,setPreview:r}=(0,ea.g)(),[n,l]=(0,i.useState)("preview"),[o,d]=(0,i.useState)(!1),[c,u]=(0,i.useState)([]),[m,x]=(0,i.useState)({history:[],currentIndex:-1}),h=e=>{let t="back"===e?m.currentIndex-1:m.currentIndex+1;t>=0&&t<m.history.length&&(x(e=>({...e,currentIndex:t})),r(e=>({...e,content:m.history[t]})))},f=(0,p.cn)("relative border-l border-border/30 overflow-hidden backdrop-blur-sm transition-all duration-300",{"fixed inset-0 z-50":o});return(0,a.jsx)(es.N,{children:t&&(0,a.jsx)(M.P.div,{initial:{width:"100%",x:"100%",opacity:0},animate:{x:0,opacity:1},exit:{x:"100%",opacity:0,transition:{duration:.1,ease:"easeOut"}},transition:{duration:.1,ease:"easeOut"},className:f,children:(0,a.jsxs)("div",{className:"flex flex-col h-full bg-background/80",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between px-4 py-2 border-b border-border/30",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("button",{onClick:()=>{r(e=>({...e,isVisible:!1})),d(!1)},className:"p-2 hover:bg-muted rounded-md text-muted-foreground hover:text-foreground",children:(0,a.jsx)(D.A,{size:16})}),(0,a.jsxs)("div",{className:"ml-2 flex items-center gap-2",children:[(0,a.jsx)(ed,{selected:"preview"===n,label:"Preview",onClick:()=>l("preview")}),(0,a.jsx)(ed,{selected:"code"===n,label:"Code",onClick:()=>l("code")}),(0,a.jsx)(ed,{selected:"console"===n,label:"Console",onClick:()=>l("console")})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>h("back"),disabled:m.currentIndex<=0,className:"p-2 hover:bg-muted rounded-md text-muted-foreground hover:text-foreground disabled:opacity-50",children:(0,a.jsx)(ei,{size:16})}),(0,a.jsx)("button",{onClick:()=>h("forward"),disabled:m.currentIndex>=m.history.length-1,className:"p-2 hover:bg-muted rounded-md text-muted-foreground hover:text-foreground disabled:opacity-50",children:(0,a.jsx)(en.A,{size:16})}),(0,a.jsx)("button",{onClick:()=>d(e=>!e),className:"p-2 hover:bg-muted rounded-md text-muted-foreground hover:text-foreground",children:o?(0,a.jsx)(el.A,{size:16}):(0,a.jsx)(eo.A,{size:16})})]})]}),(0,a.jsx)("div",{className:"flex-1 overflow-auto styled-scrollbar",children:(0,a.jsxs)("div",{className:"h-full",children:["preview"===n&&"html"===s.kind&&(0,a.jsx)("iframe",{srcDoc:eu(s.content),className:"w-full h-full border-0 bg-white dark:bg-zinc-900",sandbox:"allow-scripts",title:"HTML Preview"}),"preview"===n&&"html"!==s.kind&&(0,a.jsx)(er.B,{content:s.content,language:s.kind,mode:"edit",status:"idle"}),"code"===n&&(0,a.jsx)(er.B,{content:s.content,language:s.kind,mode:"edit",status:"idle",lineNumbers:!0}),"console"===n&&(0,a.jsx)(ec,{logs:c})]})})]})})})},(e,t)=>e.isVisible===t.isVisible&&e.isReadonly===t.isReadonly);function ep({id:e,initialMessages:t,selectedModelId:s,selectedVisibilityType:l,isReadonly:o}){let[d,c]=(0,i.useState)(void 0),{messages:u,input:m,setInput:p,setMessages:x,handleSubmit:h,append:f,isLoading:g,stop:v,reload:y,data:b,metadata:w}=(0,r.Y_)({api:"/api/dev-chat",id:e,body:{id:e,modelId:s,conversationId:d},initialMessages:t}),{width:N=1920,height:k=1080}=(0,n.lW)(),{preview:C}=(0,ea.g)(),[I,M]=(0,i.useState)([]);return(0,a.jsxs)("div",{className:"flex flex-row min-w-0 h-dvh overflow-hidden bg-background",children:[(0,a.jsxs)("div",{className:"flex flex-col min-w-0 w-full overflow-hidden",children:[(0,a.jsx)(j,{chatId:e,selectedModelId:s,isReadonly:o}),(0,a.jsx)(ee,{chatId:e,isLoading:g,votes:[],messages:u,setMessages:x,reload:y,isReadonly:o,selectedModelId:s}),(0,a.jsx)("form",{className:"flex mx-auto px-4 bg-background gap-2 w-full md:max-w-3xl",children:!o&&(0,a.jsx)(S,{chatId:e,input:m,setInput:p,handleSubmit:h,isLoading:g,stop:v,attachments:I,setAttachments:M,messages:u,setMessages:x,append:f,selectedModelId:s})}),(0,a.jsx)(et.$,{className:"py-2"})]}),(0,a.jsx)(em,{isReadonly:o,isVisible:C?.isVisible})]})}em.displayName="PreviewPanel"},9252:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>d});var a=s(33626),r=s(45346),i=s(38326),n=s(71003),l=s(85457),o=s(76285);let d={title:"업무지원(챗봇)",description:"AI 기반 지능형 어시스턴트로 다양한 작업을 수행하세요. 자연어 처리와 고급 AI 기능을 통해 효율적인 업무 지원을 제공합니다.",keywords:["AI 어시스턴트","챗봇","자연어 처리","AI","지능형 어시스턴트","업무 자동화"],openGraph:{title:"업무지원(챗봇)",description:"AI 기반 지능형 어시스턴트로 다양한 작업을 수행하세요. 자연어 처리와 고급 AI 기능을 통해 효율적인 업무 지원을 제공합니다.",type:"website"},twitter:{card:"summary_large_image",title:"업무지원(챗봇)",description:"AI 기반 지능형 어시스턴트로 다양한 작업을 수행하세요. 자연어 처리와 고급 AI 기능을 통해 효율적인 업무 지원을 제공합니다."}};async function c(){let e=(0,l.lk)(),t=await (0,r.UL)(),s=t.get("dev-model-id")?.value,d=n.Jn.find(e=>e.id===s)?.id||n.oQ;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.Chat,{id:e,initialMessages:[],selectedModelId:d,selectedVisibilityType:"private",isReadonly:!1},e),(0,a.jsx)(o.DataStreamHandler,{id:e})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38326:(e,t,s)=>{"use strict";s.d(t,{Chat:()=>a});let a=(0,s(35306).registerClientReference)(function(){throw Error("Attempted to call Chat() from the server but Chat is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\preview\\chat.tsx","Chat")},42449:e=>{"use strict";e.exports=require("pg")},43764:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{isRequestAPICallableInsideAfter:function(){return o},throwForSearchParamsAccessInUseCache:function(){return l},throwWithStaticGenerationBailoutError:function(){return i},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return n}});let a=s(33746),r=s(3295);function i(e,t){throw Object.defineProperty(new a.StaticGenBailoutError(`Route ${e} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function n(e,t){throw Object.defineProperty(new a.StaticGenBailoutError(`Route ${e} with \`dynamic = "error"\` couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function l(e){let t=Object.defineProperty(Error(`Route ${e.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw e.invalidUsageError??=t,t}function o(){let e=r.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},45714:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{describeHasCheckingStringProperty:function(){return r},describeStringPropertyAccess:function(){return a},wellKnownProperties:function(){return i}});let s=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function a(e,t){return s.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function r(e,t){let s=JSON.stringify(t);return"`Reflect.has("+e+", "+s+")`, `"+s+" in "+e+"`, or similar"}let i=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},48578:(e,t,s)=>{"use strict";s.d(t,{DataStreamHandler:()=>o});var a=s(61765),r=s(63185),i=s(41837);let n={documentId:"init",content:"",kind:"text",title:"",status:"idle",isVisible:!1,boundingBox:{top:0,left:0,width:0,height:0}};var l=s(61526);function o({id:e}){let{data:t}=(0,a.Y_)({id:e,api:"/api/dev-chat"}),{setUserMessageIdFromServer:s}=(0,l.n)(),{setBlock:o}=function(){let{data:e,mutate:t}=(0,i.Ay)("block",null,{fallbackData:n}),s=(0,r.useMemo)(()=>e||n,[e]),a=(0,r.useCallback)(e=>{t(t=>{let s=t||n;return"function"==typeof e?e(s):e})},[t]);return(0,r.useMemo)(()=>({block:s,setBlock:a}),[s,a])}();return(0,r.useRef)(-1),null}},54284:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return s}});class s{static get(e,t,s){let a=Reflect.get(e,t,s);return"function"==typeof a?a.bind(e):a}static set(e,t,s,a){return Reflect.set(e,t,s,a)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},61765:(e,t,s)=>{"use strict";s.d(t,{Y_:()=>a});var a=s(46549).Y_},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71003:(e,t,s)=>{"use strict";s.d(t,{Jn:()=>a,oQ:()=>r});let a=[{id:"지자체 공간정보 플랫폼 챗봇",label:"지자체 공간정보 플랫폼 챗봇",apiIdentifier:"EIjFYMz0dmL2HxkQJuBifqvF",description:"지자체 공간정보 플랫폼 챗봇",apiKey:"app-Hd682MZtRJh95QtTUe5H9aCl"},{id:"지도개발 어시스턴트",label:"지도개발 어시스턴트",apiIdentifier:"EIjFYMz0dmL2HxkQJuBifqvF",description:"지도개발을 위한 문서를 학습한 어시스턴트",apiKey:"app-EIjFYMz0dmL2HxkQJuBifqvF"}],r="지자체 공간정보 플랫폼 챗봇"},73136:e=>{"use strict";e.exports=require("node:url")},74664:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=s(49994),r=s(18765),i=s(42117),n=s.n(i),l=s(91962),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["(preview)",{children:["preview",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9252)),"C:\\chatbot\\front-chat\\app\\(preview)\\preview\\page.tsx"]}]},{}]},{forbidden:[()=>Promise.resolve().then(s.t.bind(s,81494,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,61135,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(s.bind(s,96592)),"C:\\chatbot\\front-chat\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,76532)),"C:\\chatbot\\front-chat\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,81494,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,61135,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["C:\\chatbot\\front-chat\\app\\(preview)\\preview\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(preview)/preview/page",pathname:"/preview",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},76285:(e,t,s)=>{"use strict";s.d(t,{DataStreamHandler:()=>a});let a=(0,s(35306).registerClientReference)(function(){throw Error("Attempted to call DataStreamHandler() from the server but DataStreamHandler is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\components\\data-stream-handler.tsx","DataStreamHandler")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},89932:(e,t,s)=>{Promise.resolve().then(s.bind(s,48578)),Promise.resolve().then(s.bind(s,8115))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[332,741,180,228,309,572,391,152],()=>s(74664));module.exports=a})();