exports.id=391,exports.ids=[391],exports.modules={8170:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>o});let o=(0,r(35306).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\app\\providers.tsx","Providers")},17379:(e,t,r)=>{Promise.resolve().then(r.bind(r,8170)),Promise.resolve().then(r.t.bind(r,7676,23))},25466:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,20349,23)),Promise.resolve().then(r.t.bind(r,24013,23)),Promise.resolve().then(r.t.bind(r,42117,23)),Promise.resolve().then(r.t.bind(r,18052,23)),Promise.resolve().then(r.t.bind(r,43452,23)),Promise.resolve().then(r.t.bind(r,60992,23)),Promise.resolve().then(r.t.bind(r,20526,23)),Promise.resolve().then(r.t.bind(r,17336,23))},29477:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,82817,23))},31451:(e,t,r)=>{Promise.resolve().then(r.bind(r,59110)),Promise.resolve().then(r.t.bind(r,28482,23))},42489:()=>{},42629:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,10875,23))},59110:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>u});var o=r(84464),s=r(21072),i=r(72414),n=r(55703);let a=({...e})=>{let{theme:t="system"}=(0,i.D)();return(0,o.jsx)(n.l$,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})};var d=r(84371),l=r(63185);let c=(0,l.createContext)(void 0),m=({children:e})=>{let[t,r]=(0,l.useState)(!1);return(0,o.jsx)(c.Provider,{value:{isOpen:t,toggleChat:()=>r(e=>!e)},children:e})};var h=r(41837);function u({children:e,...t}){return(0,o.jsx)(s.CP,{children:(0,o.jsxs)(h.BE,{value:{onError:e=>{401!==e.status&&console.error("SWR Error:",e)},revalidateOnFocus:!1,revalidateOnReconnect:!0},children:[(0,o.jsx)(a,{position:"top-center"}),(0,o.jsx)(i.N,{...t,attribute:"class",children:(0,o.jsx)(d.Bc,{children:(0,o.jsx)(m,{children:e})})})]})})}},72487:(e,t,r)=>{"use strict";r.d(t,{GO:()=>n,cn:()=>i});var o=r(11160),s=r(98639);function i(...e){return(0,s.QP)((0,o.$)(e))}let n=async e=>{let t=await fetch(e);if(!t.ok){t.status;let e=Error("An error occurred while fetching the data.");throw e.info=await t.json(),e.status=t.status,e}return t.json()}},76532:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>n});var o=r(33626),s=r(82817),i=r.n(s);let n={title:"페이지를 찾을 수 없음",description:"요청하신 페이지를 찾을 수 없습니다. URL을 확인하거나 홈페이지로 돌아가세요.",robots:{index:!1,follow:!1}};function a(){return(0,o.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center bg-background",children:(0,o.jsxs)("div",{className:"mx-auto max-w-md text-center",children:[(0,o.jsxs)("div",{className:"mb-8",children:[(0,o.jsx)("h1",{className:"text-6xl font-bold text-muted-foreground",children:"404"}),(0,o.jsx)("h2",{className:"mt-4 text-2xl font-semibold",children:"페이지를 찾을 수 없습니다"}),(0,o.jsx)("p",{className:"mt-2 text-muted-foreground",children:"요청하신 페이지가 존재하지 않거나 이동되었을 수 있습니다."})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)(i(),{href:"/geon-2d-map",className:"inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring",children:"홈으로 돌아가기"}),(0,o.jsxs)("div",{className:"text-sm text-muted-foreground",children:[(0,o.jsx)("p",{children:"또는"}),(0,o.jsx)(i(),{href:"/login",className:"text-primary hover:underline",children:"로그인 페이지로 이동"})]})]})]})})}},84371:(e,t,r)=>{"use strict";r.d(t,{Bc:()=>a,EA:()=>m,ZI:()=>c,k$:()=>l,m_:()=>d});var o=r(84464),s=r(63185),i=r(91040),n=r(72487);let a=i.Kq,d=i.bL,l=i.l9,c=s.forwardRef(({className:e,sideOffset:t=4,...r},s)=>(0,o.jsx)(i.UC,{ref:s,sideOffset:t,className:(0,n.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r}));c.displayName=i.UC.displayName;let m=({content:e,children:t,align:r="center",...s})=>(0,o.jsx)(a,{delayDuration:0,children:(0,o.jsxs)(d,{...s,children:[(0,o.jsx)(l,{asChild:!0,children:t}),(0,o.jsx)(c,{align:r,children:e})]})})},93922:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,50791,23)),Promise.resolve().then(r.t.bind(r,52795,23)),Promise.resolve().then(r.t.bind(r,75487,23)),Promise.resolve().then(r.t.bind(r,3822,23)),Promise.resolve().then(r.t.bind(r,88154,23)),Promise.resolve().then(r.t.bind(r,46414,23)),Promise.resolve().then(r.t.bind(r,20508,23)),Promise.resolve().then(r.t.bind(r,53678,23))},96592:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d,viewport:()=>l});var o=r(33626);r(42489);var s=r(92701),i=r.n(s),n=r(8170),a=r(56729);let d={title:{default:"말로 만드는 지도",template:"%s"},description:"AI 기반 지도 서비스 - 말로 만드는 지도와 지능형 어시스턴트",keywords:["말로만드는지도","지도","AI","어시스턴트","GeOn","GIS","지리정보"],authors:[{name:"GeOn Team"}],creator:"GeOn Team",publisher:"GeOn",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL(process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:3000"),openGraph:{type:"website",locale:"ko_KR",siteName:"업무지원(챗봇)",title:"업무지원(챗봇)",description:"AI 기반 지도 서비스 - 말로 만드는 지도와 지능형 어시스턴트"},twitter:{card:"summary_large_image",title:"업무지원(챗봇)",description:"AI 기반 지도 서비스 - 말로 만드는 지도와 지능형 어시스턴트"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{}},l={width:"device-width",initialScale:1,maximumScale:1,userScalable:!1,themeColor:[{media:"(prefers-color-scheme: light)",color:"white"},{media:"(prefers-color-scheme: dark)",color:"black"}]};function c({children:e}){return(0,o.jsx)("html",{suppressHydrationWarning:!0,lang:"ko",children:(0,o.jsx)("body",{className:`${i().className} overflow-hidden`,children:(0,o.jsxs)(n.Providers,{children:[e,(0,o.jsx)(a.default,{src:"/js/odf/odf.min.js"})]})})})}}};