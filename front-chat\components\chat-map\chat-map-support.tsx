import {
	ExpandableChat,
	ExpandableChatHeader,
	ExpandableChatBody
} from '@/components/ui/chat/expandable-chat'
import {ChatMessageList} from '@/components/ui/chat/chat-message-list'
import { Message as PreviewMessage } from "@/components/message";
import {Message} from "ai";
import { useScrollToBottom } from '@/lib/hooks/use-scroll-to-bottom';

interface ChatMapSupportProps {
	messages: Array<Message>
}

export default function ChatMapSupport({messages}: ChatMapSupportProps) {

	const [messagesContainerRef, messagesEndRef] =
		useScrollToBottom<HTMLDivElement>();

	return (
		<ExpandableChat
			size="sm"
			position={"bottom-right"}
			className={'sticky'}
		>
			<ExpandableChatHeader className='flex-col text-center justify-center'>
				<h1 className='font-semibold'>Geon AI 에게 물어보세요 ✨</h1>
			</ExpandableChatHeader>
			<ExpandableChatBody>
				<ChatMessageList ref={messagesContainerRef} className={'gap-4'}>
					{/*
						해당 컴포넌트 재사용 시 MESSAGES 컴포넌트 호출 필요함. 
					 */}
					<div
						ref={messagesEndRef}
						className="shrink-0 min-w-[20px] min-h-[20px]"
					/>
				</ChatMessageList>

				{/*<ButtonScrollToBottom*/}
				{/*	isAtBottom={isAtBottom}*/}
				{/*	scrollToBottom={scrollToBottom}*/}
				{/*/>*/}
			</ExpandableChatBody>
		</ExpandableChat>
	)
}
