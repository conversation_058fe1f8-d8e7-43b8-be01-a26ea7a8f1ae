import { NextRequest, NextResponse } from "next/server";

interface LayerColumnInfo {
  lyrId: string;
  columnOrdr: number;
  columnNm: string;
  columnNcm: string;
  dataTy: string;
  indictAt: string;
  editPosblAt: string;
  esntlAt: string;
  mummLt?: number;
  mxmmLt?: number;
  cmmnGroupCode: string | null;
  cmmnGroupCodeNm: string | null;
  useAt: string;
  registerId: string;
  registDt: string;
  updusrId: string;
  updtDt: string;
}

interface LayerColumnResponse {
  code: number;
  message: string;
  result: LayerColumnInfo[];
}

interface LayerAttributesResponse {
  code: number;
  message: string;
  result: {
    pageInfo: {
      pageSize: number;
      pageIndex: number;
      totalCount: number;
    };
    features: Array<{
      properties: Record<string, any>;
    }>;
    pkColumnName: string;
  };
}

export async function POST(request: NextRequest) {
  try {
    const { userId, lyrId, namespace, cntntsId, pageSize = 50, pageIndex = 1 } = await request.json();

    const apiKey = process.env.GEON_API_KEY;
    const apiBaseUrl = process.env.GEON_API_BASE_URL;
    
    if (!apiKey || !apiBaseUrl) {
      return NextResponse.json(
        { error: "API 키 또는 기본 URL이 설정되지 않았습니다." },
        { status: 500 }
      );
    }

    // 1. 컬럼 정보 조회
    const columnResponse = await fetch(
      `${apiBaseUrl}/builder/layer/column/select?crtfckey=${apiKey}&lyrId=${lyrId}&userId=${userId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          crtfckey: apiKey,
        },
      }
    );

    if (!columnResponse.ok) {
      throw new Error(
        `컬럼 정보 조회 실패: ${columnResponse.status} ${columnResponse.statusText}`
      );
    }

    const columnData: LayerColumnResponse = await columnResponse.json();

    // 2. 속성 데이터 조회
    const dataResponse = await fetch(
      `${apiBaseUrl}/builder/layer/attributes/select?crtfckey=${apiKey}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          crtfckey: apiKey,
        },
        body: JSON.stringify({
          typeName: `${namespace}:${cntntsId}`,
          pageIndex: pageIndex,
          pageSize: pageSize,
        }),
      }
    );

    if (!dataResponse.ok) {
      throw new Error(
        `속성 데이터 조회 실패: ${dataResponse.status} ${dataResponse.statusText}`
      );
    }

    const attributeData: LayerAttributesResponse = await dataResponse.json();

    if (
      !columnData.code ||
      columnData.code !== 200 ||
      !attributeData.code ||
      attributeData.code !== 200
    ) {
      return NextResponse.json(
        { error: "레이어 정보를 찾을 수 없습니다." },
        { status: 404 }
      );
    }

    // 컬럼 정보와 속성 데이터를 함께 반환
    const result = {
      columns: columnData.result.map((col) => ({
        name: col.columnNm,
        description: col.columnNcm,
        type: col.dataTy,
        editable: col.editPosblAt === "Y",
        required: col.esntlAt === "Y",
        minValue: col.mummLt,
        maxValue: col.mxmmLt,
        groupCode: col.cmmnGroupCode,
        groupName: col.cmmnGroupCodeNm,
        order: col.columnOrdr,
        indicator: col.indictAt === "Y",
      })),
      data: attributeData.result.features.map(
        (feature) => feature.properties
      ),
      totalCount: attributeData.result.pageInfo.totalCount,
      pageInfo: attributeData.result.pageInfo,
      pkColumnName: attributeData.result.pkColumnName,
    };

    return NextResponse.json(result);
  } catch (error: any) {
    console.error("Error fetching layer detailed information:", error);
    return NextResponse.json(
      { error: `레이어 상세 정보 조회 중 오류가 발생했습니다: ${error.message}` },
      { status: 500 }
    );
  }
}
