#!/bin/bash

# 인자로 받은 경로를 변수에 저장
base_dir="$1"

# 경로가 비어있지 않고 디렉토리인 경우에만 작업 수행
if [ -d "$base_dir" ]; then
  # 모든 하위 디렉토리를 순회
  find "$base_dir" -type d | while read -r dir; do
    # 최하위 폴더명 추출
    dirname=$(basename "$dir")

    # Modelfile이 존재하는지 확인
    if [ -f "$dir/Modelfile" ]; then
      # 해당 디렉토리로 이동하여 명령어 실행
      (
        cd "$dir" || exit
        echo "ollama create $dirname -f Modelfile"
        ollama create "$dirname" -f Modelfile
      )
    else
      echo "Modelfile not found in $dir"
    fi
  done

  echo "모든 작업이 완료되었습니다!"
else
  echo "지정된 경로가 유효하지 않습니다: $base_dir"
  exit 1
fi