{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/utils/cookie.js"], "sourcesContent": ["var __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _SessionStore_instances, _SessionStore_chunks, _SessionStore_option, _SessionStore_logger, _SessionStore_chunk, _SessionStore_clean;\n// Uncomment to recalculate the estimated size\n// of an empty session cookie\n// import * as cookie from \"../vendored/cookie.js\"\n// const { serialize } = cookie\n// console.log(\n//   \"Cookie estimated to be \",\n//   serialize(`__Secure.authjs.session-token.0`, \"\", {\n//     expires: new Date(),\n//     httpOnly: true,\n//     maxAge: Number.MAX_SAFE_INTEGER,\n//     path: \"/\",\n//     sameSite: \"strict\",\n//     secure: true,\n//     domain: \"example.com\",\n//   }).length,\n//   \" bytes\"\n// )\nconst ALLOWED_COOKIE_SIZE = 4096;\n// Based on commented out section above\nconst ESTIMATED_EMPTY_COOKIE_SIZE = 160;\nconst CHUNK_SIZE = ALLOWED_COOKIE_SIZE - ESTIMATED_EMPTY_COOKIE_SIZE;\n/**\n * Use secure cookies if the site uses HTTPS\n * This being conditional allows cookies to work non-HTTPS development URLs\n * Honour secure cookie option, which sets 'secure' and also adds '__Secure-'\n * prefix, but enable them by default if the site URL is HTTPS; but not for\n * non-HTTPS URLs like http://localhost which are used in development).\n * For more on prefixes see https://googlechrome.github.io/samples/cookie-prefixes/\n *\n * @TODO Review cookie settings (names, options)\n */\nexport function defaultCookies(useSecureCookies) {\n    const cookiePrefix = useSecureCookies ? \"__Secure-\" : \"\";\n    return {\n        // default cookie options\n        sessionToken: {\n            name: `${cookiePrefix}authjs.session-token`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: useSecureCookies,\n            },\n        },\n        callbackUrl: {\n            name: `${cookiePrefix}authjs.callback-url`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: useSecureCookies,\n            },\n        },\n        csrfToken: {\n            // Default to __Host- for CSRF token for additional protection if using useSecureCookies\n            // NB: The `__Host-` prefix is stricter than the `__Secure-` prefix.\n            name: `${useSecureCookies ? \"__Host-\" : \"\"}authjs.csrf-token`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: useSecureCookies,\n            },\n        },\n        pkceCodeVerifier: {\n            name: `${cookiePrefix}authjs.pkce.code_verifier`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: useSecureCookies,\n                maxAge: 60 * 15, // 15 minutes in seconds\n            },\n        },\n        state: {\n            name: `${cookiePrefix}authjs.state`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: useSecureCookies,\n                maxAge: 60 * 15, // 15 minutes in seconds\n            },\n        },\n        nonce: {\n            name: `${cookiePrefix}authjs.nonce`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: useSecureCookies,\n            },\n        },\n        webauthnChallenge: {\n            name: `${cookiePrefix}authjs.challenge`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: useSecureCookies,\n                maxAge: 60 * 15, // 15 minutes in seconds\n            },\n        },\n    };\n}\nexport class SessionStore {\n    constructor(option, cookies, logger) {\n        _SessionStore_instances.add(this);\n        _SessionStore_chunks.set(this, {});\n        _SessionStore_option.set(this, void 0);\n        _SessionStore_logger.set(this, void 0);\n        __classPrivateFieldSet(this, _SessionStore_logger, logger, \"f\");\n        __classPrivateFieldSet(this, _SessionStore_option, option, \"f\");\n        if (!cookies)\n            return;\n        const { name: sessionCookiePrefix } = option;\n        for (const [name, value] of Object.entries(cookies)) {\n            if (!name.startsWith(sessionCookiePrefix) || !value)\n                continue;\n            __classPrivateFieldGet(this, _SessionStore_chunks, \"f\")[name] = value;\n        }\n    }\n    /**\n     * The JWT Session or database Session ID\n     * constructed from the cookie chunks.\n     */\n    get value() {\n        // Sort the chunks by their keys before joining\n        const sortedKeys = Object.keys(__classPrivateFieldGet(this, _SessionStore_chunks, \"f\")).sort((a, b) => {\n            const aSuffix = parseInt(a.split(\".\").pop() || \"0\");\n            const bSuffix = parseInt(b.split(\".\").pop() || \"0\");\n            return aSuffix - bSuffix;\n        });\n        // Use the sorted keys to join the chunks in the correct order\n        return sortedKeys.map((key) => __classPrivateFieldGet(this, _SessionStore_chunks, \"f\")[key]).join(\"\");\n    }\n    /**\n     * Given a cookie value, return new cookies, chunked, to fit the allowed cookie size.\n     * If the cookie has changed from chunked to unchunked or vice versa,\n     * it deletes the old cookies as well.\n     */\n    chunk(value, options) {\n        // Assume all cookies should be cleaned by default\n        const cookies = __classPrivateFieldGet(this, _SessionStore_instances, \"m\", _SessionStore_clean).call(this);\n        // Calculate new chunks\n        const chunked = __classPrivateFieldGet(this, _SessionStore_instances, \"m\", _SessionStore_chunk).call(this, {\n            name: __classPrivateFieldGet(this, _SessionStore_option, \"f\").name,\n            value,\n            options: { ...__classPrivateFieldGet(this, _SessionStore_option, \"f\").options, ...options },\n        });\n        // Update stored chunks / cookies\n        for (const chunk of chunked) {\n            cookies[chunk.name] = chunk;\n        }\n        return Object.values(cookies);\n    }\n    /** Returns a list of cookies that should be cleaned. */\n    clean() {\n        return Object.values(__classPrivateFieldGet(this, _SessionStore_instances, \"m\", _SessionStore_clean).call(this));\n    }\n}\n_SessionStore_chunks = new WeakMap(), _SessionStore_option = new WeakMap(), _SessionStore_logger = new WeakMap(), _SessionStore_instances = new WeakSet(), _SessionStore_chunk = function _SessionStore_chunk(cookie) {\n    const chunkCount = Math.ceil(cookie.value.length / CHUNK_SIZE);\n    if (chunkCount === 1) {\n        __classPrivateFieldGet(this, _SessionStore_chunks, \"f\")[cookie.name] = cookie.value;\n        return [cookie];\n    }\n    const cookies = [];\n    for (let i = 0; i < chunkCount; i++) {\n        const name = `${cookie.name}.${i}`;\n        const value = cookie.value.substr(i * CHUNK_SIZE, CHUNK_SIZE);\n        cookies.push({ ...cookie, name, value });\n        __classPrivateFieldGet(this, _SessionStore_chunks, \"f\")[name] = value;\n    }\n    __classPrivateFieldGet(this, _SessionStore_logger, \"f\").debug(\"CHUNKING_SESSION_COOKIE\", {\n        message: `Session cookie exceeds allowed ${ALLOWED_COOKIE_SIZE} bytes.`,\n        emptyCookieSize: ESTIMATED_EMPTY_COOKIE_SIZE,\n        valueSize: cookie.value.length,\n        chunks: cookies.map((c) => c.value.length + ESTIMATED_EMPTY_COOKIE_SIZE),\n    });\n    return cookies;\n}, _SessionStore_clean = function _SessionStore_clean() {\n    const cleanedChunks = {};\n    for (const name in __classPrivateFieldGet(this, _SessionStore_chunks, \"f\")) {\n        delete __classPrivateFieldGet(this, _SessionStore_chunks, \"f\")?.[name];\n        cleanedChunks[name] = {\n            name,\n            value: \"\",\n            options: { ...__classPrivateFieldGet(this, _SessionStore_option, \"f\").options, maxAge: 0 },\n        };\n    }\n    return cleanedChunks;\n};\n"], "names": [], "mappings": ";;;;AAAA,IAAI,yBAAyB,AAAC,IAAI,IAAI,IAAI,CAAC,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC3G,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACxG;AACA,IAAI,yBAAyB,AAAC,IAAI,IAAI,IAAI,CAAC,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACpG,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACxF;AACA,IAAI,yBAAyB,sBAAsB,sBAAsB,sBAAsB,qBAAqB;AACpH,8CAA8C;AAC9C,6BAA6B;AAC7B,kDAAkD;AAClD,+BAA+B;AAC/B,eAAe;AACf,+BAA+B;AAC/B,uDAAuD;AACvD,2BAA2B;AAC3B,sBAAsB;AACtB,uCAAuC;AACvC,iBAAiB;AACjB,0BAA0B;AAC1B,oBAAoB;AACpB,6BAA6B;AAC7B,eAAe;AACf,aAAa;AACb,IAAI;AACJ,MAAM,sBAAsB;AAC5B,uCAAuC;AACvC,MAAM,8BAA8B;AACpC,MAAM,aAAa,sBAAsB;AAWlC,SAAS,eAAe,gBAAgB;IAC3C,MAAM,eAAe,mBAAmB,cAAc;IACtD,OAAO;QACH,yBAAyB;QACzB,cAAc;YACV,MAAM,GAAG,aAAa,oBAAoB,CAAC;YAC3C,SAAS;gBACL,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;YACZ;QACJ;QACA,aAAa;YACT,MAAM,GAAG,aAAa,mBAAmB,CAAC;YAC1C,SAAS;gBACL,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;YACZ;QACJ;QACA,WAAW;YACP,wFAAwF;YACxF,oEAAoE;YACpE,MAAM,GAAG,mBAAmB,YAAY,GAAG,iBAAiB,CAAC;YAC7D,SAAS;gBACL,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;YACZ;QACJ;QACA,kBAAkB;YACd,MAAM,GAAG,aAAa,yBAAyB,CAAC;YAChD,SAAS;gBACL,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,QAAQ,KAAK;YACjB;QACJ;QACA,OAAO;YACH,MAAM,GAAG,aAAa,YAAY,CAAC;YACnC,SAAS;gBACL,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,QAAQ,KAAK;YACjB;QACJ;QACA,OAAO;YACH,MAAM,GAAG,aAAa,YAAY,CAAC;YACnC,SAAS;gBACL,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;YACZ;QACJ;QACA,mBAAmB;YACf,MAAM,GAAG,aAAa,gBAAgB,CAAC;YACvC,SAAS;gBACL,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,QAAQ,KAAK;YACjB;QACJ;IACJ;AACJ;AACO,MAAM;IACT,YAAY,MAAM,EAAE,OAAO,EAAE,MAAM,CAAE;QACjC,wBAAwB,GAAG,CAAC,IAAI;QAChC,qBAAqB,GAAG,CAAC,IAAI,EAAE,CAAC;QAChC,qBAAqB,GAAG,CAAC,IAAI,EAAE,KAAK;QACpC,qBAAqB,GAAG,CAAC,IAAI,EAAE,KAAK;QACpC,uBAAuB,IAAI,EAAE,sBAAsB,QAAQ;QAC3D,uBAAuB,IAAI,EAAE,sBAAsB,QAAQ;QAC3D,IAAI,CAAC,SACD;QACJ,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG;QACtC,KAAK,MAAM,CAAC,MAAM,MAAM,IAAI,OAAO,OAAO,CAAC,SAAU;YACjD,IAAI,CAAC,KAAK,UAAU,CAAC,wBAAwB,CAAC,OAC1C;YACJ,uBAAuB,IAAI,EAAE,sBAAsB,IAAI,CAAC,KAAK,GAAG;QACpE;IACJ;IACA;;;KAGC,GACD,IAAI,QAAQ;QACR,+CAA+C;QAC/C,MAAM,aAAa,OAAO,IAAI,CAAC,uBAAuB,IAAI,EAAE,sBAAsB,MAAM,IAAI,CAAC,CAAC,GAAG;YAC7F,MAAM,UAAU,SAAS,EAAE,KAAK,CAAC,KAAK,GAAG,MAAM;YAC/C,MAAM,UAAU,SAAS,EAAE,KAAK,CAAC,KAAK,GAAG,MAAM;YAC/C,OAAO,UAAU;QACrB;QACA,8DAA8D;QAC9D,OAAO,WAAW,GAAG,CAAC,CAAC,MAAQ,uBAAuB,IAAI,EAAE,sBAAsB,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;IACtG;IACA;;;;KAIC,GACD,MAAM,KAAK,EAAE,OAAO,EAAE;QAClB,kDAAkD;QAClD,MAAM,UAAU,uBAAuB,IAAI,EAAE,yBAAyB,KAAK,qBAAqB,IAAI,CAAC,IAAI;QACzG,uBAAuB;QACvB,MAAM,UAAU,uBAAuB,IAAI,EAAE,yBAAyB,KAAK,qBAAqB,IAAI,CAAC,IAAI,EAAE;YACvG,MAAM,uBAAuB,IAAI,EAAE,sBAAsB,KAAK,IAAI;YAClE;YACA,SAAS;gBAAE,GAAG,uBAAuB,IAAI,EAAE,sBAAsB,KAAK,OAAO;gBAAE,GAAG,OAAO;YAAC;QAC9F;QACA,iCAAiC;QACjC,KAAK,MAAM,SAAS,QAAS;YACzB,OAAO,CAAC,MAAM,IAAI,CAAC,GAAG;QAC1B;QACA,OAAO,OAAO,MAAM,CAAC;IACzB;IACA,sDAAsD,GACtD,QAAQ;QACJ,OAAO,OAAO,MAAM,CAAC,uBAAuB,IAAI,EAAE,yBAAyB,KAAK,qBAAqB,IAAI,CAAC,IAAI;IAClH;AACJ;AACA,uBAAuB,IAAI,WAAW,uBAAuB,IAAI,WAAW,uBAAuB,IAAI,WAAW,0BAA0B,IAAI,WAAW,sBAAsB,SAAS,oBAAoB,MAAM;IAChN,MAAM,aAAa,KAAK,IAAI,CAAC,OAAO,KAAK,CAAC,MAAM,GAAG;IACnD,IAAI,eAAe,GAAG;QAClB,uBAAuB,IAAI,EAAE,sBAAsB,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,OAAO,KAAK;QACnF,OAAO;YAAC;SAAO;IACnB;IACA,MAAM,UAAU,EAAE;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;QACjC,MAAM,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC,EAAE,GAAG;QAClC,MAAM,QAAQ,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,YAAY;QAClD,QAAQ,IAAI,CAAC;YAAE,GAAG,MAAM;YAAE;YAAM;QAAM;QACtC,uBAAuB,IAAI,EAAE,sBAAsB,IAAI,CAAC,KAAK,GAAG;IACpE;IACA,uBAAuB,IAAI,EAAE,sBAAsB,KAAK,KAAK,CAAC,2BAA2B;QACrF,SAAS,CAAC,+BAA+B,EAAE,oBAAoB,OAAO,CAAC;QACvE,iBAAiB;QACjB,WAAW,OAAO,KAAK,CAAC,MAAM;QAC9B,QAAQ,QAAQ,GAAG,CAAC,CAAC,IAAM,EAAE,KAAK,CAAC,MAAM,GAAG;IAChD;IACA,OAAO;AACX,GAAG,sBAAsB,SAAS;IAC9B,MAAM,gBAAgB,CAAC;IACvB,IAAK,MAAM,QAAQ,uBAAuB,IAAI,EAAE,sBAAsB,KAAM;QACxE,OAAO,uBAAuB,IAAI,EAAE,sBAAsB,MAAM,CAAC,KAAK;QACtE,aAAa,CAAC,KAAK,GAAG;YAClB;YACA,OAAO;YACP,SAAS;gBAAE,GAAG,uBAAuB,IAAI,EAAE,sBAAsB,KAAK,OAAO;gBAAE,QAAQ;YAAE;QAC7F;IACJ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/errors.js"], "sourcesContent": ["/**\n * Base error class for all Auth.js errors.\n * It's optimized to be printed in the server logs in a nicely formatted way\n * via the [`logger.error`](https://authjs.dev/reference/core#logger) option.\n * @noInheritDoc\n */\nexport class AuthError extends Error {\n    /** @internal */\n    constructor(message, errorOptions) {\n        if (message instanceof Error) {\n            super(undefined, {\n                cause: { err: message, ...message.cause, ...errorOptions },\n            });\n        }\n        else if (typeof message === \"string\") {\n            if (errorOptions instanceof Error) {\n                errorOptions = { err: errorOptions, ...errorOptions.cause };\n            }\n            super(message, errorOptions);\n        }\n        else {\n            super(undefined, message);\n        }\n        this.name = this.constructor.name;\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.type = this.constructor.type ?? \"AuthError\";\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.kind = this.constructor.kind ?? \"error\";\n        Error.captureStackTrace?.(this, this.constructor);\n        const url = `https://errors.authjs.dev#${this.type.toLowerCase()}`;\n        this.message += `${this.message ? \". \" : \"\"}Read more at ${url}`;\n    }\n}\n/**\n * Thrown when the user's sign-in attempt failed.\n * @noInheritDoc\n */\nexport class SignInError extends AuthError {\n}\n/** @internal */\nSignInError.kind = \"signIn\";\n/**\n * One of the database [`Adapter` methods](https://authjs.dev/reference/core/adapters#methods)\n * failed during execution.\n *\n * :::tip\n * If `debug: true` is set, you can check out `[auth][debug]` in the logs to learn more about the failed adapter method execution.\n * @example\n * ```sh\n * [auth][debug]: adapter_getUserByEmail\n * { \"args\": [undefined] }\n * ```\n * :::\n * @noInheritDoc\n */\nexport class AdapterError extends AuthError {\n}\n/** @internal */\nAdapterError.type = \"AdapterError\";\n/**\n * Thrown when the execution of the [`signIn` callback](https://authjs.dev/reference/core/types#signin) fails\n * or if it returns `false`.\n * @noInheritDoc\n */\nexport class AccessDenied extends AuthError {\n}\n/** @internal */\nAccessDenied.type = \"AccessDenied\";\n/**\n * This error occurs when the user cannot finish login.\n * Depending on the provider type, this could have happened for multiple reasons.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n *\n * For an [OAuth provider](https://authjs.dev/getting-started/authentication/oauth), possible causes are:\n * - The user denied access to the application\n * - There was an error parsing the OAuth Profile:\n *   Check out the provider's `profile` or `userinfo.request` method to make sure\n *   it correctly fetches the user's profile.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * For an [Email provider](https://authjs.dev/getting-started/authentication/email), possible causes are:\n * - The provided email/token combination was invalid/missing:\n *   Check if the provider's `sendVerificationRequest` method correctly sends the email.\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n *\n * For a [Credentials provider](https://authjs.dev/getting-started/authentication/credentials), possible causes are:\n * - The `authorize` method threw an uncaught error:\n *   Check the provider's `authorize` method.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * :::tip\n * Check out `[auth][cause]` in the error message for more details.\n * It will show the original stack trace.\n * :::\n * @noInheritDoc\n */\nexport class CallbackRouteError extends AuthError {\n}\n/** @internal */\nCallbackRouteError.type = \"CallbackRouteError\";\n/**\n * Thrown when Auth.js is misconfigured and accidentally tried to require authentication on a custom error page.\n * To prevent an infinite loop, Auth.js will instead render its default error page.\n *\n * To fix this, make sure that the `error` page does not require authentication.\n *\n * Learn more at [Guide: Error pages](https://authjs.dev/guides/pages/error)\n * @noInheritDoc\n */\nexport class ErrorPageLoop extends AuthError {\n}\n/** @internal */\nErrorPageLoop.type = \"ErrorPageLoop\";\n/**\n * One of the [`events` methods](https://authjs.dev/reference/core/types#eventcallbacks)\n * failed during execution.\n *\n * Make sure that the `events` methods are implemented correctly and uncaught errors are handled.\n *\n * Learn more at [`events`](https://authjs.dev/reference/core/types#eventcallbacks)\n * @noInheritDoc\n */\nexport class EventError extends AuthError {\n}\n/** @internal */\nEventError.type = \"EventError\";\n/**\n * Thrown when Auth.js is unable to verify a `callbackUrl` value.\n * The browser either disabled cookies or the `callbackUrl` is not a valid URL.\n *\n * Somebody might have tried to manipulate the callback URL that Auth.js uses to redirect the user back to the configured `callbackUrl`/page.\n * This could be a malicious hacker trying to redirect the user to a phishing site.\n * To prevent this, Auth.js checks if the callback URL is valid and throws this error if it is not.\n *\n * There is no action required, but it might be an indicator that somebody is trying to attack your application.\n * @noInheritDoc\n */\nexport class InvalidCallbackUrl extends AuthError {\n}\n/** @internal */\nInvalidCallbackUrl.type = \"InvalidCallbackUrl\";\n/**\n * Can be thrown from the `authorize` callback of the Credentials provider.\n * When an error occurs during the `authorize` callback, two things can happen:\n * 1. The user is redirected to the signin page, with `error=CredentialsSignin&code=credentials` in the URL. `code` is configurable.\n * 2. If you throw this error in a framework that handles form actions server-side, this error is thrown, instead of redirecting the user, so you'll need to handle.\n * @noInheritDoc\n */\nexport class CredentialsSignin extends SignInError {\n    constructor() {\n        super(...arguments);\n        /**\n         * The error code that is set in the `code` query parameter of the redirect URL.\n         *\n         *\n         * ⚠ NOTE: This property is going to be included in the URL, so make sure it does not hint at sensitive errors.\n         *\n         * The full error is always logged on the server, if you need to debug.\n         *\n         * Generally, we don't recommend hinting specifically if the user had either a wrong username or password specifically,\n         * try rather something like \"Invalid credentials\".\n         */\n        this.code = \"credentials\";\n    }\n}\n/** @internal */\nCredentialsSignin.type = \"CredentialsSignin\";\n/**\n * One of the configured OAuth or OIDC providers is missing the `authorization`, `token` or `userinfo`, or `issuer` configuration.\n * To perform OAuth or OIDC sign in, at least one of these endpoints is required.\n *\n * Learn more at [`OAuth2Config`](https://authjs.dev/reference/core/providers#oauth2configprofile) or [Guide: OAuth Provider](https://authjs.dev/guides/configuring-oauth-providers)\n * @noInheritDoc\n */\nexport class InvalidEndpoints extends AuthError {\n}\n/** @internal */\nInvalidEndpoints.type = \"InvalidEndpoints\";\n/**\n * Thrown when a PKCE, state or nonce OAuth check could not be performed.\n * This could happen if the OAuth provider is configured incorrectly or if the browser is blocking cookies.\n *\n * Learn more at [`checks`](https://authjs.dev/reference/core/providers#checks)\n * @noInheritDoc\n */\nexport class InvalidCheck extends AuthError {\n}\n/** @internal */\nInvalidCheck.type = \"InvalidCheck\";\n/**\n * Logged on the server when Auth.js could not decode or encode a JWT-based (`strategy: \"jwt\"`) session.\n *\n * Possible causes are either a misconfigured `secret` or a malformed JWT or `encode/decode` methods.\n *\n * :::note\n * When this error is logged, the session cookie is destroyed.\n * :::\n *\n * Learn more at [`secret`](https://authjs.dev/reference/core#secret), [`jwt.encode`](https://authjs.dev/reference/core/jwt#encode-1) or [`jwt.decode`](https://authjs.dev/reference/core/jwt#decode-2) for more information.\n * @noInheritDoc\n */\nexport class JWTSessionError extends AuthError {\n}\n/** @internal */\nJWTSessionError.type = \"JWTSessionError\";\n/**\n * Thrown if Auth.js is misconfigured. This could happen if you configured an Email provider but did not set up a database adapter,\n * or tried using a `strategy: \"database\"` session without a database adapter.\n * In both cases, make sure you either remove the configuration or add the missing adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database), [Email provider](https://authjs.dev/getting-started/authentication/email) or [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database-session)\n * @noInheritDoc\n */\nexport class MissingAdapter extends AuthError {\n}\n/** @internal */\nMissingAdapter.type = \"MissingAdapter\";\n/**\n * Thrown similarily to [`MissingAdapter`](https://authjs.dev/reference/core/errors#missingadapter), but only some required methods were missing.\n *\n * Make sure you either remove the configuration or add the missing methods to the adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database)\n * @noInheritDoc\n */\nexport class MissingAdapterMethods extends AuthError {\n}\n/** @internal */\nMissingAdapterMethods.type = \"MissingAdapterMethods\";\n/**\n * Thrown when a Credentials provider is missing the `authorize` configuration.\n * To perform credentials sign in, the `authorize` method is required.\n *\n * Learn more at [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n * @noInheritDoc\n */\nexport class MissingAuthorize extends AuthError {\n}\n/** @internal */\nMissingAuthorize.type = \"MissingAuthorize\";\n/**\n * Auth.js requires a secret or multiple secrets to be set, but none was not found. This is used to encrypt cookies, JWTs and other sensitive data.\n *\n * :::note\n * If you are using a framework like Next.js, we try to automatically infer the secret from the `AUTH_SECRET`, `AUTH_SECRET_1`, etc. environment variables.\n * Alternatively, you can also explicitly set the [`AuthConfig.secret`](https://authjs.dev/reference/core#secret) option.\n * :::\n *\n *\n * :::tip\n * To generate a random string, you can use the Auth.js CLI: `npx auth secret`\n * :::\n * @noInheritDoc\n */\nexport class MissingSecret extends AuthError {\n}\n/** @internal */\nMissingSecret.type = \"MissingSecret\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an OAuth account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link OAuth accounts to existing accounts if the user is not signed in.\n *\n * :::tip\n * If you trust the OAuth provider to have verified the user's email address,\n * you can enable automatic account linking by setting [`allowDangerousEmailAccountLinking: true`](https://authjs.dev/reference/core/providers#allowdangerousemailaccountlinking)\n * in the provider configuration.\n * :::\n * @noInheritDoc\n */\nexport class OAuthAccountNotLinked extends SignInError {\n}\n/** @internal */\nOAuthAccountNotLinked.type = \"OAuthAccountNotLinked\";\n/**\n * Thrown when an OAuth provider returns an error during the sign in process.\n * This could happen for example if the user denied access to the application or there was a configuration error.\n *\n * For a full list of possible reasons, check out the specification [Authorization Code Grant: Error Response](https://www.rfc-editor.org/rfc/rfc6749#section-*******)\n * @noInheritDoc\n */\nexport class OAuthCallbackError extends SignInError {\n}\n/** @internal */\nOAuthCallbackError.type = \"OAuthCallbackError\";\n/**\n * This error occurs during an OAuth sign in attempt when the provider's\n * response could not be parsed. This could for example happen if the provider's API\n * changed, or the [`OAuth2Config.profile`](https://authjs.dev/reference/core/providers#oauth2configprofile) method is not implemented correctly.\n * @noInheritDoc\n */\nexport class OAuthProfileParseError extends AuthError {\n}\n/** @internal */\nOAuthProfileParseError.type = \"OAuthProfileParseError\";\n/**\n * Logged on the server when Auth.js could not retrieve a session from the database (`strategy: \"database\"`).\n *\n * The database adapter might be misconfigured or the database is not reachable.\n *\n * Learn more at [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database)\n * @noInheritDoc\n */\nexport class SessionTokenError extends AuthError {\n}\n/** @internal */\nSessionTokenError.type = \"SessionTokenError\";\n/**\n * Happens when login by [OAuth](https://authjs.dev/getting-started/authentication/oauth) could not be started.\n *\n * Possible causes are:\n * - The Authorization Server is not compliant with the [OAuth 2.0](https://www.ietf.org/rfc/rfc6749.html) or the [OIDC](https://openid.net/specs/openid-connect-core-1_0.html) specification.\n *   Check the details in the error message.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n * @noInheritDoc\n */\nexport class OAuthSignInError extends SignInError {\n}\n/** @internal */\nOAuthSignInError.type = \"OAuthSignInError\";\n/**\n * Happens when the login by an [Email provider](https://authjs.dev/getting-started/authentication/email) could not be started.\n *\n * Possible causes are:\n * - The email sent from the client is invalid, could not be normalized by [`EmailConfig.normalizeIdentifier`](https://authjs.dev/reference/core/providers/email#normalizeidentifier)\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n * @noInheritDoc\n */\nexport class EmailSignInError extends SignInError {\n}\n/** @internal */\nEmailSignInError.type = \"EmailSignInError\";\n/**\n * Represents an error that occurs during the sign-out process. This error\n * is logged when there are issues in terminating a user's session, either\n * by failing to delete the session from the database (in database session\n * strategies) or encountering issues during other parts of the sign-out\n * process, such as emitting sign-out events or clearing session cookies.\n *\n * The session cookie(s) are emptied even if this error is logged.\n * @noInheritDoc\n */\nexport class SignOutError extends AuthError {\n}\n/** @internal */\nSignOutError.type = \"SignOutError\";\n/**\n * Auth.js was requested to handle an operation that it does not support.\n *\n * See [`AuthAction`](https://authjs.dev/reference/core/types#authaction) for the supported actions.\n * @noInheritDoc\n */\nexport class UnknownAction extends AuthError {\n}\n/** @internal */\nUnknownAction.type = \"UnknownAction\";\n/**\n * Thrown when a Credentials provider is present but the JWT strategy (`strategy: \"jwt\"`) is not enabled.\n *\n * Learn more at [`strategy`](https://authjs.dev/reference/core#strategy) or [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n * @noInheritDoc\n */\nexport class UnsupportedStrategy extends AuthError {\n}\n/** @internal */\nUnsupportedStrategy.type = \"UnsupportedStrategy\";\n/**\n * Thrown when an endpoint was incorrectly called without a provider, or with an unsupported provider.\n * @noInheritDoc\n */\nexport class InvalidProvider extends AuthError {\n}\n/** @internal */\nInvalidProvider.type = \"InvalidProvider\";\n/**\n * Thrown when the `trustHost` option was not set to `true`.\n *\n * Auth.js requires the `trustHost` option to be set to `true` since it's relying on the request headers' `host` value.\n *\n * :::note\n * Official Auth.js libraries might attempt to automatically set the `trustHost` option to `true` if the request is coming from a trusted host on a trusted platform.\n * :::\n *\n * Learn more at [`trustHost`](https://authjs.dev/reference/core#trusthost) or [Guide: Deployment](https://authjs.dev/getting-started/deployment)\n * @noInheritDoc\n */\nexport class UntrustedHost extends AuthError {\n}\n/** @internal */\nUntrustedHost.type = \"UntrustedHost\";\n/**\n * The user's email/token combination was invalid.\n * This could be because the email/token combination was not found in the database,\n * or because the token has expired. Ask the user to log in again.\n * @noInheritDoc\n */\nexport class Verification extends AuthError {\n}\n/** @internal */\nVerification.type = \"Verification\";\n/**\n * Error for missing CSRF tokens in client-side actions (`signIn`, `signOut`, `useSession#update`).\n * Thrown when actions lack the double submit cookie, essential for CSRF protection.\n *\n * CSRF ([Cross-Site Request Forgery](https://owasp.org/www-community/attacks/csrf))\n * is an attack leveraging authenticated user credentials for unauthorized actions.\n *\n * Double submit cookie pattern, a CSRF defense, requires matching values in a cookie\n * and request parameter. More on this at [MDN Web Docs](https://developer.mozilla.org/en-US/docs/Glossary/CSRF).\n * @noInheritDoc\n */\nexport class MissingCSRF extends SignInError {\n}\n/** @internal */\nMissingCSRF.type = \"MissingCSRF\";\nconst clientErrors = new Set([\n    \"CredentialsSignin\",\n    \"OAuthAccountNotLinked\",\n    \"OAuthCallbackError\",\n    \"AccessDenied\",\n    \"Verification\",\n    \"MissingCSRF\",\n    \"AccountNotLinked\",\n    \"WebAuthnVerificationError\",\n]);\n/**\n * Used to only allow sending a certain subset of errors to the client.\n * Errors are always logged on the server, but to prevent leaking sensitive information,\n * only a subset of errors are sent to the client as-is.\n * @internal\n */\nexport function isClientError(error) {\n    if (error instanceof AuthError)\n        return clientErrors.has(error.type);\n    return false;\n}\n/**\n * Thrown when multiple providers have `enableConditionalUI` set to `true`.\n * Only one provider can have this option enabled at a time.\n * @noInheritDoc\n */\nexport class DuplicateConditionalUI extends AuthError {\n}\n/** @internal */\nDuplicateConditionalUI.type = \"DuplicateConditionalUI\";\n/**\n * Thrown when a WebAuthn provider has `enableConditionalUI` set to `true` but no formField has `webauthn` in its autocomplete param.\n *\n * The `webauthn` autocomplete param is required for conditional UI to work.\n * @noInheritDoc\n */\nexport class MissingWebAuthnAutocomplete extends AuthError {\n}\n/** @internal */\nMissingWebAuthnAutocomplete.type = \"MissingWebAuthnAutocomplete\";\n/**\n * Thrown when a WebAuthn provider fails to verify a client response.\n * @noInheritDoc\n */\nexport class WebAuthnVerificationError extends AuthError {\n}\n/** @internal */\nWebAuthnVerificationError.type = \"WebAuthnVerificationError\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link accounts to existing accounts if the user is not signed in.\n * @noInheritDoc\n */\nexport class AccountNotLinked extends SignInError {\n}\n/** @internal */\nAccountNotLinked.type = \"AccountNotLinked\";\n/**\n * Thrown when an experimental feature is used but not enabled.\n * @noInheritDoc\n */\nexport class ExperimentalFeatureNotEnabled extends AuthError {\n}\n/** @internal */\nExperimentalFeatureNotEnabled.type = \"ExperimentalFeatureNotEnabled\";\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACM,MAAM,kBAAkB;IAC3B,cAAc,GACd,YAAY,OAAO,EAAE,YAAY,CAAE;QAC/B,IAAI,mBAAmB,OAAO;YAC1B,KAAK,CAAC,WAAW;gBACb,OAAO;oBAAE,KAAK;oBAAS,GAAG,QAAQ,KAAK;oBAAE,GAAG,YAAY;gBAAC;YAC7D;QACJ,OACK,IAAI,OAAO,YAAY,UAAU;YAClC,IAAI,wBAAwB,OAAO;gBAC/B,eAAe;oBAAE,KAAK;oBAAc,GAAG,aAAa,KAAK;gBAAC;YAC9D;YACA,KAAK,CAAC,SAAS;QACnB,OACK;YACD,KAAK,CAAC,WAAW;QACrB;QACA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI;QACjC,uEAAuE;QACvE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI;QACrC,uEAAuE;QACvE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI;QACrC,MAAM,iBAAiB,GAAG,IAAI,EAAE,IAAI,CAAC,WAAW;QAChD,MAAM,MAAM,CAAC,0BAA0B,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI;QAClE,IAAI,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,OAAO,GAAG,aAAa,EAAE,KAAK;IACpE;AACJ;AAKO,MAAM,oBAAoB;AACjC;AACA,cAAc,GACd,YAAY,IAAI,GAAG;AAeZ,MAAM,qBAAqB;AAClC;AACA,cAAc,GACd,aAAa,IAAI,GAAG;AAMb,MAAM,qBAAqB;AAClC;AACA,cAAc,GACd,aAAa,IAAI,GAAG;AAyCb,MAAM,2BAA2B;AACxC;AACA,cAAc,GACd,mBAAmB,IAAI,GAAG;AAUnB,MAAM,sBAAsB;AACnC;AACA,cAAc,GACd,cAAc,IAAI,GAAG;AAUd,MAAM,mBAAmB;AAChC;AACA,cAAc,GACd,WAAW,IAAI,GAAG;AAYX,MAAM,2BAA2B;AACxC;AACA,cAAc,GACd,mBAAmB,IAAI,GAAG;AAQnB,MAAM,0BAA0B;IACnC,aAAc;QACV,KAAK,IAAI;QACT;;;;;;;;;;SAUC,GACD,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AACA,cAAc,GACd,kBAAkB,IAAI,GAAG;AAQlB,MAAM,yBAAyB;AACtC;AACA,cAAc,GACd,iBAAiB,IAAI,GAAG;AAQjB,MAAM,qBAAqB;AAClC;AACA,cAAc,GACd,aAAa,IAAI,GAAG;AAab,MAAM,wBAAwB;AACrC;AACA,cAAc,GACd,gBAAgB,IAAI,GAAG;AAShB,MAAM,uBAAuB;AACpC;AACA,cAAc,GACd,eAAe,IAAI,GAAG;AASf,MAAM,8BAA8B;AAC3C;AACA,cAAc,GACd,sBAAsB,IAAI,GAAG;AAQtB,MAAM,yBAAyB;AACtC;AACA,cAAc,GACd,iBAAiB,IAAI,GAAG;AAejB,MAAM,sBAAsB;AACnC;AACA,cAAc,GACd,cAAc,IAAI,GAAG;AAcd,MAAM,8BAA8B;AAC3C;AACA,cAAc,GACd,sBAAsB,IAAI,GAAG;AAQtB,MAAM,2BAA2B;AACxC;AACA,cAAc,GACd,mBAAmB,IAAI,GAAG;AAOnB,MAAM,+BAA+B;AAC5C;AACA,cAAc,GACd,uBAAuB,IAAI,GAAG;AASvB,MAAM,0BAA0B;AACvC;AACA,cAAc,GACd,kBAAkB,IAAI,GAAG;AAiBlB,MAAM,yBAAyB;AACtC;AACA,cAAc,GACd,iBAAiB,IAAI,GAAG;AAYjB,MAAM,yBAAyB;AACtC;AACA,cAAc,GACd,iBAAiB,IAAI,GAAG;AAWjB,MAAM,qBAAqB;AAClC;AACA,cAAc,GACd,aAAa,IAAI,GAAG;AAOb,MAAM,sBAAsB;AACnC;AACA,cAAc,GACd,cAAc,IAAI,GAAG;AAOd,MAAM,4BAA4B;AACzC;AACA,cAAc,GACd,oBAAoB,IAAI,GAAG;AAKpB,MAAM,wBAAwB;AACrC;AACA,cAAc,GACd,gBAAgB,IAAI,GAAG;AAahB,MAAM,sBAAsB;AACnC;AACA,cAAc,GACd,cAAc,IAAI,GAAG;AAOd,MAAM,qBAAqB;AAClC;AACA,cAAc,GACd,aAAa,IAAI,GAAG;AAYb,MAAM,oBAAoB;AACjC;AACA,cAAc,GACd,YAAY,IAAI,GAAG;AACnB,MAAM,eAAe,IAAI,IAAI;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AAOM,SAAS,cAAc,KAAK;IAC/B,IAAI,iBAAiB,WACjB,OAAO,aAAa,GAAG,CAAC,MAAM,IAAI;IACtC,OAAO;AACX;AAMO,MAAM,+BAA+B;AAC5C;AACA,cAAc,GACd,uBAAuB,IAAI,GAAG;AAOvB,MAAM,oCAAoC;AACjD;AACA,cAAc,GACd,4BAA4B,IAAI,GAAG;AAK5B,MAAM,kCAAkC;AAC/C;AACA,cAAc,GACd,0BAA0B,IAAI,GAAG;AAQ1B,MAAM,yBAAyB;AACtC;AACA,cAAc,GACd,iBAAiB,IAAI,GAAG;AAKjB,MAAM,sCAAsC;AACnD;AACA,cAAc,GACd,8BAA8B,IAAI,GAAG", "ignoreList": [0]}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/utils/assert.js"], "sourcesContent": ["import { defaultCookies } from \"./cookie.js\";\nimport { AuthError, DuplicateConditionalUI, ExperimentalFeatureNotEnabled, InvalidCallbackUrl, InvalidEndpoints, MissingAdapter, MissingAdapterMethods, MissingAuthorize, MissingSecret, MissingWebAuthnAutocomplete, UnsupportedStrategy, UntrustedHost, } from \"../../errors.js\";\nlet warned = false;\nfunction isValidHttpUrl(url, baseUrl) {\n    try {\n        return /^https?:/.test(new URL(url, url.startsWith(\"/\") ? baseUrl : undefined).protocol);\n    }\n    catch {\n        return false;\n    }\n}\nfunction isSemverString(version) {\n    return /^v\\d+(?:\\.\\d+){0,2}$/.test(version);\n}\nlet hasCredentials = false;\nlet hasEmail = false;\nlet hasWebAuthn = false;\nconst emailMethods = [\n    \"createVerificationToken\",\n    \"useVerificationToken\",\n    \"getUserByEmail\",\n];\nconst sessionMethods = [\n    \"createUser\",\n    \"getUser\",\n    \"getUserByEmail\",\n    \"getUserByAccount\",\n    \"updateUser\",\n    \"linkAccount\",\n    \"createSession\",\n    \"getSessionAndUser\",\n    \"updateSession\",\n    \"deleteSession\",\n];\nconst webauthnMethods = [\n    \"createUser\",\n    \"getUser\",\n    \"linkAccount\",\n    \"getAccount\",\n    \"getAuthenticator\",\n    \"createAuthenticator\",\n    \"listAuthenticatorsByUserId\",\n    \"updateAuthenticatorCounter\",\n];\n/**\n * Verify that the user configured Auth.js correctly.\n * Good place to mention deprecations as well.\n *\n * This is invoked before the init method, so default values are not available yet.\n */\nexport function assertConfig(request, options) {\n    const { url } = request;\n    const warnings = [];\n    if (!warned && options.debug)\n        warnings.push(\"debug-enabled\");\n    if (!options.trustHost) {\n        return new UntrustedHost(`Host must be trusted. URL was: ${request.url}`);\n    }\n    if (!options.secret?.length) {\n        return new MissingSecret(\"Please define a `secret`\");\n    }\n    const callbackUrlParam = request.query?.callbackUrl;\n    if (callbackUrlParam && !isValidHttpUrl(callbackUrlParam, url.origin)) {\n        return new InvalidCallbackUrl(`Invalid callback URL. Received: ${callbackUrlParam}`);\n    }\n    const { callbackUrl: defaultCallbackUrl } = defaultCookies(options.useSecureCookies ?? url.protocol === \"https:\");\n    const callbackUrlCookie = request.cookies?.[options.cookies?.callbackUrl?.name ?? defaultCallbackUrl.name];\n    if (callbackUrlCookie && !isValidHttpUrl(callbackUrlCookie, url.origin)) {\n        return new InvalidCallbackUrl(`Invalid callback URL. Received: ${callbackUrlCookie}`);\n    }\n    // Keep track of webauthn providers that use conditional UI\n    let hasConditionalUIProvider = false;\n    for (const p of options.providers) {\n        const provider = typeof p === \"function\" ? p() : p;\n        if ((provider.type === \"oauth\" || provider.type === \"oidc\") &&\n            !(provider.issuer ?? provider.options?.issuer)) {\n            const { authorization: a, token: t, userinfo: u } = provider;\n            let key;\n            if (typeof a !== \"string\" && !a?.url)\n                key = \"authorization\";\n            else if (typeof t !== \"string\" && !t?.url)\n                key = \"token\";\n            else if (typeof u !== \"string\" && !u?.url)\n                key = \"userinfo\";\n            if (key) {\n                return new InvalidEndpoints(`Provider \"${provider.id}\" is missing both \\`issuer\\` and \\`${key}\\` endpoint config. At least one of them is required`);\n            }\n        }\n        if (provider.type === \"credentials\")\n            hasCredentials = true;\n        else if (provider.type === \"email\")\n            hasEmail = true;\n        else if (provider.type === \"webauthn\") {\n            hasWebAuthn = true;\n            // Validate simpleWebAuthnBrowserVersion\n            if (provider.simpleWebAuthnBrowserVersion &&\n                !isSemverString(provider.simpleWebAuthnBrowserVersion)) {\n                return new AuthError(`Invalid provider config for \"${provider.id}\": simpleWebAuthnBrowserVersion \"${provider.simpleWebAuthnBrowserVersion}\" must be a valid semver string.`);\n            }\n            if (provider.enableConditionalUI) {\n                // Make sure only one webauthn provider has \"enableConditionalUI\" set to true\n                if (hasConditionalUIProvider) {\n                    return new DuplicateConditionalUI(`Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time`);\n                }\n                hasConditionalUIProvider = true;\n                // Make sure at least one formField has \"webauthn\" in its autocomplete param\n                const hasWebauthnFormField = Object.values(provider.formFields).some((f) => f.autocomplete && f.autocomplete.toString().indexOf(\"webauthn\") > -1);\n                if (!hasWebauthnFormField) {\n                    return new MissingWebAuthnAutocomplete(`Provider \"${provider.id}\" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`);\n                }\n            }\n        }\n    }\n    if (hasCredentials) {\n        const dbStrategy = options.session?.strategy === \"database\";\n        const onlyCredentials = !options.providers.some((p) => (typeof p === \"function\" ? p() : p).type !== \"credentials\");\n        if (dbStrategy && onlyCredentials) {\n            return new UnsupportedStrategy(\"Signing in with credentials only supported if JWT strategy is enabled\");\n        }\n        const credentialsNoAuthorize = options.providers.some((p) => {\n            const provider = typeof p === \"function\" ? p() : p;\n            return provider.type === \"credentials\" && !provider.authorize;\n        });\n        if (credentialsNoAuthorize) {\n            return new MissingAuthorize(\"Must define an authorize() handler to use credentials authentication provider\");\n        }\n    }\n    const { adapter, session } = options;\n    const requiredMethods = [];\n    if (hasEmail ||\n        session?.strategy === \"database\" ||\n        (!session?.strategy && adapter)) {\n        if (hasEmail) {\n            if (!adapter)\n                return new MissingAdapter(\"Email login requires an adapter\");\n            requiredMethods.push(...emailMethods);\n        }\n        else {\n            if (!adapter)\n                return new MissingAdapter(\"Database session requires an adapter\");\n            requiredMethods.push(...sessionMethods);\n        }\n    }\n    if (hasWebAuthn) {\n        // Log experimental warning\n        if (options.experimental?.enableWebAuthn) {\n            warnings.push(\"experimental-webauthn\");\n        }\n        else {\n            return new ExperimentalFeatureNotEnabled(\"WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config\");\n        }\n        if (!adapter)\n            return new MissingAdapter(\"WebAuthn requires an adapter\");\n        requiredMethods.push(...webauthnMethods);\n    }\n    if (adapter) {\n        const missing = requiredMethods.filter((m) => !(m in adapter));\n        if (missing.length) {\n            return new MissingAdapterMethods(`Required adapter methods were missing: ${missing.join(\", \")}`);\n        }\n    }\n    if (!warned)\n        warned = true;\n    return warnings;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,IAAI,SAAS;AACb,SAAS,eAAe,GAAG,EAAE,OAAO;IAChC,IAAI;QACA,OAAO,WAAW,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,UAAU,CAAC,OAAO,UAAU,WAAW,QAAQ;IAC3F,EACA,OAAM;QACF,OAAO;IACX;AACJ;AACA,SAAS,eAAe,OAAO;IAC3B,OAAO,uBAAuB,IAAI,CAAC;AACvC;AACA,IAAI,iBAAiB;AACrB,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,MAAM,eAAe;IACjB;IACA;IACA;CACH;AACD,MAAM,iBAAiB;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,MAAM,kBAAkB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AAOM,SAAS,aAAa,OAAO,EAAE,OAAO;IACzC,MAAM,EAAE,GAAG,EAAE,GAAG;IAChB,MAAM,WAAW,EAAE;IACnB,IAAI,CAAC,UAAU,QAAQ,KAAK,EACxB,SAAS,IAAI,CAAC;IAClB,IAAI,CAAC,QAAQ,SAAS,EAAE;QACpB,OAAO,IAAI,4MAAA,CAAA,gBAAa,CAAC,CAAC,+BAA+B,EAAE,QAAQ,GAAG,EAAE;IAC5E;IACA,IAAI,CAAC,QAAQ,MAAM,EAAE,QAAQ;QACzB,OAAO,IAAI,4MAAA,CAAA,gBAAa,CAAC;IAC7B;IACA,MAAM,mBAAmB,QAAQ,KAAK,EAAE;IACxC,IAAI,oBAAoB,CAAC,eAAe,kBAAkB,IAAI,MAAM,GAAG;QACnE,OAAO,IAAI,4MAAA,CAAA,qBAAkB,CAAC,CAAC,gCAAgC,EAAE,kBAAkB;IACvF;IACA,MAAM,EAAE,aAAa,kBAAkB,EAAE,GAAG,CAAA,GAAA,4NAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,gBAAgB,IAAI,IAAI,QAAQ,KAAK;IACxG,MAAM,oBAAoB,QAAQ,OAAO,EAAE,CAAC,QAAQ,OAAO,EAAE,aAAa,QAAQ,mBAAmB,IAAI,CAAC;IAC1G,IAAI,qBAAqB,CAAC,eAAe,mBAAmB,IAAI,MAAM,GAAG;QACrE,OAAO,IAAI,4MAAA,CAAA,qBAAkB,CAAC,CAAC,gCAAgC,EAAE,mBAAmB;IACxF;IACA,2DAA2D;IAC3D,IAAI,2BAA2B;IAC/B,KAAK,MAAM,KAAK,QAAQ,SAAS,CAAE;QAC/B,MAAM,WAAW,OAAO,MAAM,aAAa,MAAM;QACjD,IAAI,CAAC,SAAS,IAAI,KAAK,WAAW,SAAS,IAAI,KAAK,MAAM,KACtD,CAAC,CAAC,SAAS,MAAM,IAAI,SAAS,OAAO,EAAE,MAAM,GAAG;YAChD,MAAM,EAAE,eAAe,CAAC,EAAE,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,GAAG;YACpD,IAAI;YACJ,IAAI,OAAO,MAAM,YAAY,CAAC,GAAG,KAC7B,MAAM;iBACL,IAAI,OAAO,MAAM,YAAY,CAAC,GAAG,KAClC,MAAM;iBACL,IAAI,OAAO,MAAM,YAAY,CAAC,GAAG,KAClC,MAAM;YACV,IAAI,KAAK;gBACL,OAAO,IAAI,4MAAA,CAAA,mBAAgB,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE,CAAC,mCAAmC,EAAE,IAAI,oDAAoD,CAAC;YACvJ;QACJ;QACA,IAAI,SAAS,IAAI,KAAK,eAClB,iBAAiB;aAChB,IAAI,SAAS,IAAI,KAAK,SACvB,WAAW;aACV,IAAI,SAAS,IAAI,KAAK,YAAY;YACnC,cAAc;YACd,wCAAwC;YACxC,IAAI,SAAS,4BAA4B,IACrC,CAAC,eAAe,SAAS,4BAA4B,GAAG;gBACxD,OAAO,IAAI,4MAAA,CAAA,YAAS,CAAC,CAAC,6BAA6B,EAAE,SAAS,EAAE,CAAC,iCAAiC,EAAE,SAAS,4BAA4B,CAAC,gCAAgC,CAAC;YAC/K;YACA,IAAI,SAAS,mBAAmB,EAAE;gBAC9B,6EAA6E;gBAC7E,IAAI,0BAA0B;oBAC1B,OAAO,IAAI,4MAAA,CAAA,yBAAsB,CAAC,CAAC,4HAA4H,CAAC;gBACpK;gBACA,2BAA2B;gBAC3B,4EAA4E;gBAC5E,MAAM,uBAAuB,OAAO,MAAM,CAAC,SAAS,UAAU,EAAE,IAAI,CAAC,CAAC,IAAM,EAAE,YAAY,IAAI,EAAE,YAAY,CAAC,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;gBAC/I,IAAI,CAAC,sBAAsB;oBACvB,OAAO,IAAI,4MAAA,CAAA,8BAA2B,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE,CAAC,+GAA+G,CAAC;gBACpL;YACJ;QACJ;IACJ;IACA,IAAI,gBAAgB;QAChB,MAAM,aAAa,QAAQ,OAAO,EAAE,aAAa;QACjD,MAAM,kBAAkB,CAAC,QAAQ,SAAS,CAAC,IAAI,CAAC,CAAC,IAAM,CAAC,OAAO,MAAM,aAAa,MAAM,CAAC,EAAE,IAAI,KAAK;QACpG,IAAI,cAAc,iBAAiB;YAC/B,OAAO,IAAI,4MAAA,CAAA,sBAAmB,CAAC;QACnC;QACA,MAAM,yBAAyB,QAAQ,SAAS,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,WAAW,OAAO,MAAM,aAAa,MAAM;YACjD,OAAO,SAAS,IAAI,KAAK,iBAAiB,CAAC,SAAS,SAAS;QACjE;QACA,IAAI,wBAAwB;YACxB,OAAO,IAAI,4MAAA,CAAA,mBAAgB,CAAC;QAChC;IACJ;IACA,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAC7B,MAAM,kBAAkB,EAAE;IAC1B,IAAI,YACA,SAAS,aAAa,cACrB,CAAC,SAAS,YAAY,SAAU;QACjC,IAAI,UAAU;YACV,IAAI,CAAC,SACD,OAAO,IAAI,4MAAA,CAAA,iBAAc,CAAC;YAC9B,gBAAgB,IAAI,IAAI;QAC5B,OACK;YACD,IAAI,CAAC,SACD,OAAO,IAAI,4MAAA,CAAA,iBAAc,CAAC;YAC9B,gBAAgB,IAAI,IAAI;QAC5B;IACJ;IACA,IAAI,aAAa;QACb,2BAA2B;QAC3B,IAAI,QAAQ,YAAY,EAAE,gBAAgB;YACtC,SAAS,IAAI,CAAC;QAClB,OACK;YACD,OAAO,IAAI,4MAAA,CAAA,gCAA6B,CAAC;QAC7C;QACA,IAAI,CAAC,SACD,OAAO,IAAI,4MAAA,CAAA,iBAAc,CAAC;QAC9B,gBAAgB,IAAI,IAAI;IAC5B;IACA,IAAI,SAAS;QACT,MAAM,UAAU,gBAAgB,MAAM,CAAC,CAAC,IAAM,CAAC,CAAC,KAAK,OAAO;QAC5D,IAAI,QAAQ,MAAM,EAAE;YAChB,OAAO,IAAI,4MAAA,CAAA,wBAAqB,CAAC,CAAC,uCAAuC,EAAE,QAAQ,IAAI,CAAC,OAAO;QACnG;IACJ;IACA,IAAI,CAAC,QACD,SAAS;IACb,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/vendored/cookie.js"], "sourcesContent": ["/**\n * @source https://github.com/jshttp/cookie\n * <AUTHOR>\n * @license MIT\n */\n/**\n * This is a workaround to support ESM-only environments, until `cookie` ships ESM builds.\n * @see https://github.com/jshttp/cookie/issues/211\n */\n/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n */\nconst cookieNameRegExp = /^[!#$%&'*+\\-.^_`|~0-9A-Za-z]+$/;\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n */\nconst cookieValueRegExp = /^(\"?)[\\u0021\\u0023-\\u002B\\u002D-\\u003A\\u003C-\\u005B\\u005D-\\u007E]*\\1$/;\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\nconst domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\nconst pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\nconst __toString = Object.prototype.toString;\nconst NullObject = /* @__PURE__ */ (() => {\n    const C = function () { };\n    C.prototype = Object.create(null);\n    return C;\n})();\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n */\nexport function parse(str, options) {\n    const obj = new NullObject();\n    const len = str.length;\n    // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n    if (len < 2)\n        return obj;\n    const dec = options?.decode || decode;\n    let index = 0;\n    do {\n        const eqIdx = str.indexOf(\"=\", index);\n        if (eqIdx === -1)\n            break; // No more cookie pairs.\n        const colonIdx = str.indexOf(\";\", index);\n        const endIdx = colonIdx === -1 ? len : colonIdx;\n        if (eqIdx > endIdx) {\n            // backtrack on prior semicolon\n            index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n            continue;\n        }\n        const keyStartIdx = startIndex(str, index, eqIdx);\n        const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n        const key = str.slice(keyStartIdx, keyEndIdx);\n        // only assign once\n        if (obj[key] === undefined) {\n            let valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n            let valEndIdx = endIndex(str, endIdx, valStartIdx);\n            const value = dec(str.slice(valStartIdx, valEndIdx));\n            obj[key] = value;\n        }\n        index = endIdx + 1;\n    } while (index < len);\n    return obj;\n}\nfunction startIndex(str, index, max) {\n    do {\n        const code = str.charCodeAt(index);\n        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n            return index;\n    } while (++index < max);\n    return max;\n}\nfunction endIndex(str, index, min) {\n    while (index > min) {\n        const code = str.charCodeAt(--index);\n        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n            return index + 1;\n    }\n    return min;\n}\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n */\nexport function serialize(name, val, options) {\n    const enc = options?.encode || encodeURIComponent;\n    if (!cookieNameRegExp.test(name)) {\n        throw new TypeError(`argument name is invalid: ${name}`);\n    }\n    const value = enc(val);\n    if (!cookieValueRegExp.test(value)) {\n        throw new TypeError(`argument val is invalid: ${val}`);\n    }\n    let str = name + \"=\" + value;\n    if (!options)\n        return str;\n    if (options.maxAge !== undefined) {\n        if (!Number.isInteger(options.maxAge)) {\n            throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);\n        }\n        str += \"; Max-Age=\" + options.maxAge;\n    }\n    if (options.domain) {\n        if (!domainValueRegExp.test(options.domain)) {\n            throw new TypeError(`option domain is invalid: ${options.domain}`);\n        }\n        str += \"; Domain=\" + options.domain;\n    }\n    if (options.path) {\n        if (!pathValueRegExp.test(options.path)) {\n            throw new TypeError(`option path is invalid: ${options.path}`);\n        }\n        str += \"; Path=\" + options.path;\n    }\n    if (options.expires) {\n        if (!isDate(options.expires) ||\n            !Number.isFinite(options.expires.valueOf())) {\n            throw new TypeError(`option expires is invalid: ${options.expires}`);\n        }\n        str += \"; Expires=\" + options.expires.toUTCString();\n    }\n    if (options.httpOnly) {\n        str += \"; HttpOnly\";\n    }\n    if (options.secure) {\n        str += \"; Secure\";\n    }\n    if (options.partitioned) {\n        str += \"; Partitioned\";\n    }\n    if (options.priority) {\n        const priority = typeof options.priority === \"string\"\n            ? options.priority.toLowerCase()\n            : undefined;\n        switch (priority) {\n            case \"low\":\n                str += \"; Priority=Low\";\n                break;\n            case \"medium\":\n                str += \"; Priority=Medium\";\n                break;\n            case \"high\":\n                str += \"; Priority=High\";\n                break;\n            default:\n                throw new TypeError(`option priority is invalid: ${options.priority}`);\n        }\n    }\n    if (options.sameSite) {\n        const sameSite = typeof options.sameSite === \"string\"\n            ? options.sameSite.toLowerCase()\n            : options.sameSite;\n        switch (sameSite) {\n            case true:\n            case \"strict\":\n                str += \"; SameSite=Strict\";\n                break;\n            case \"lax\":\n                str += \"; SameSite=Lax\";\n                break;\n            case \"none\":\n                str += \"; SameSite=None\";\n                break;\n            default:\n                throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);\n        }\n    }\n    return str;\n}\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n */\nfunction decode(str) {\n    if (str.indexOf(\"%\") === -1)\n        return str;\n    try {\n        return decodeURIComponent(str);\n    }\n    catch (e) {\n        return str;\n    }\n}\n/**\n * Determine if value is a Date.\n */\nfunction isDate(val) {\n    return __toString.call(val) === \"[object Date]\";\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GACD;;;CAGC,GACD;;;;;;;;;;CAUC;;;;AACD,MAAM,mBAAmB;AACzB;;;;;;;;CAQC,GACD,MAAM,oBAAoB;AAC1B;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,MAAM,oBAAoB;AAC1B;;;;;;CAMC,GACD,MAAM,kBAAkB;AACxB,MAAM,aAAa,OAAO,SAAS,CAAC,QAAQ;AAC5C,MAAM,aAAa,aAAa,GAAG,CAAC;IAChC,MAAM,IAAI,YAAc;IACxB,EAAE,SAAS,GAAG,OAAO,MAAM,CAAC;IAC5B,OAAO;AACX,CAAC;AAOM,SAAS,MAAM,GAAG,EAAE,OAAO;IAC9B,MAAM,MAAM,IAAI;IAChB,MAAM,MAAM,IAAI,MAAM;IACtB,iGAAiG;IACjG,IAAI,MAAM,GACN,OAAO;IACX,MAAM,MAAM,SAAS,UAAU;IAC/B,IAAI,QAAQ;IACZ,GAAG;QACC,MAAM,QAAQ,IAAI,OAAO,CAAC,KAAK;QAC/B,IAAI,UAAU,CAAC,GACX,OAAO,wBAAwB;QACnC,MAAM,WAAW,IAAI,OAAO,CAAC,KAAK;QAClC,MAAM,SAAS,aAAa,CAAC,IAAI,MAAM;QACvC,IAAI,QAAQ,QAAQ;YAChB,+BAA+B;YAC/B,QAAQ,IAAI,WAAW,CAAC,KAAK,QAAQ,KAAK;YAC1C;QACJ;QACA,MAAM,cAAc,WAAW,KAAK,OAAO;QAC3C,MAAM,YAAY,SAAS,KAAK,OAAO;QACvC,MAAM,MAAM,IAAI,KAAK,CAAC,aAAa;QACnC,mBAAmB;QACnB,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW;YACxB,IAAI,cAAc,WAAW,KAAK,QAAQ,GAAG;YAC7C,IAAI,YAAY,SAAS,KAAK,QAAQ;YACtC,MAAM,QAAQ,IAAI,IAAI,KAAK,CAAC,aAAa;YACzC,GAAG,CAAC,IAAI,GAAG;QACf;QACA,QAAQ,SAAS;IACrB,QAAS,QAAQ,IAAK;IACtB,OAAO;AACX;AACA,SAAS,WAAW,GAAG,EAAE,KAAK,EAAE,GAAG;IAC/B,GAAG;QACC,MAAM,OAAO,IAAI,UAAU,CAAC;QAC5B,IAAI,SAAS,KAAK,KAAK,OAAM,SAAS,KAAK,MAAM,KAC7C,OAAO;IACf,QAAS,EAAE,QAAQ,IAAK;IACxB,OAAO;AACX;AACA,SAAS,SAAS,GAAG,EAAE,KAAK,EAAE,GAAG;IAC7B,MAAO,QAAQ,IAAK;QAChB,MAAM,OAAO,IAAI,UAAU,CAAC,EAAE;QAC9B,IAAI,SAAS,KAAK,KAAK,OAAM,SAAS,KAAK,MAAM,KAC7C,OAAO,QAAQ;IACvB;IACA,OAAO;AACX;AAUO,SAAS,UAAU,IAAI,EAAE,GAAG,EAAE,OAAO;IACxC,MAAM,MAAM,SAAS,UAAU;IAC/B,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO;QAC9B,MAAM,IAAI,UAAU,CAAC,0BAA0B,EAAE,MAAM;IAC3D;IACA,MAAM,QAAQ,IAAI;IAClB,IAAI,CAAC,kBAAkB,IAAI,CAAC,QAAQ;QAChC,MAAM,IAAI,UAAU,CAAC,yBAAyB,EAAE,KAAK;IACzD;IACA,IAAI,MAAM,OAAO,MAAM;IACvB,IAAI,CAAC,SACD,OAAO;IACX,IAAI,QAAQ,MAAM,KAAK,WAAW;QAC9B,IAAI,CAAC,OAAO,SAAS,CAAC,QAAQ,MAAM,GAAG;YACnC,MAAM,IAAI,UAAU,CAAC,0BAA0B,EAAE,QAAQ,MAAM,EAAE;QACrE;QACA,OAAO,eAAe,QAAQ,MAAM;IACxC;IACA,IAAI,QAAQ,MAAM,EAAE;QAChB,IAAI,CAAC,kBAAkB,IAAI,CAAC,QAAQ,MAAM,GAAG;YACzC,MAAM,IAAI,UAAU,CAAC,0BAA0B,EAAE,QAAQ,MAAM,EAAE;QACrE;QACA,OAAO,cAAc,QAAQ,MAAM;IACvC;IACA,IAAI,QAAQ,IAAI,EAAE;QACd,IAAI,CAAC,gBAAgB,IAAI,CAAC,QAAQ,IAAI,GAAG;YACrC,MAAM,IAAI,UAAU,CAAC,wBAAwB,EAAE,QAAQ,IAAI,EAAE;QACjE;QACA,OAAO,YAAY,QAAQ,IAAI;IACnC;IACA,IAAI,QAAQ,OAAO,EAAE;QACjB,IAAI,CAAC,OAAO,QAAQ,OAAO,KACvB,CAAC,OAAO,QAAQ,CAAC,QAAQ,OAAO,CAAC,OAAO,KAAK;YAC7C,MAAM,IAAI,UAAU,CAAC,2BAA2B,EAAE,QAAQ,OAAO,EAAE;QACvE;QACA,OAAO,eAAe,QAAQ,OAAO,CAAC,WAAW;IACrD;IACA,IAAI,QAAQ,QAAQ,EAAE;QAClB,OAAO;IACX;IACA,IAAI,QAAQ,MAAM,EAAE;QAChB,OAAO;IACX;IACA,IAAI,QAAQ,WAAW,EAAE;QACrB,OAAO;IACX;IACA,IAAI,QAAQ,QAAQ,EAAE;QAClB,MAAM,WAAW,OAAO,QAAQ,QAAQ,KAAK,WACvC,QAAQ,QAAQ,CAAC,WAAW,KAC5B;QACN,OAAQ;YACJ,KAAK;gBACD,OAAO;gBACP;YACJ,KAAK;gBACD,OAAO;gBACP;YACJ,KAAK;gBACD,OAAO;gBACP;YACJ;gBACI,MAAM,IAAI,UAAU,CAAC,4BAA4B,EAAE,QAAQ,QAAQ,EAAE;QAC7E;IACJ;IACA,IAAI,QAAQ,QAAQ,EAAE;QAClB,MAAM,WAAW,OAAO,QAAQ,QAAQ,KAAK,WACvC,QAAQ,QAAQ,CAAC,WAAW,KAC5B,QAAQ,QAAQ;QACtB,OAAQ;YACJ,KAAK;YACL,KAAK;gBACD,OAAO;gBACP;YACJ,KAAK;gBACD,OAAO;gBACP;YACJ,KAAK;gBACD,OAAO;gBACP;YACJ;gBACI,MAAM,IAAI,UAAU,CAAC,4BAA4B,EAAE,QAAQ,QAAQ,EAAE;QAC7E;IACJ;IACA,OAAO;AACX;AACA;;CAEC,GACD,SAAS,OAAO,GAAG;IACf,IAAI,IAAI,OAAO,CAAC,SAAS,CAAC,GACtB,OAAO;IACX,IAAI;QACA,OAAO,mBAAmB;IAC9B,EACA,OAAO,GAAG;QACN,OAAO;IACX;AACJ;AACA;;CAEC,GACD,SAAS,OAAO,GAAG;IACf,OAAO,WAAW,IAAI,CAAC,SAAS;AACpC", "ignoreList": [0]}}, {"offset": {"line": 790, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/jwt.js"], "sourcesContent": ["/**\n *\n *\n * This module contains functions and types\n * to encode and decode {@link https://authjs.dev/concepts/session-strategies#jwt-session JWT}s\n * issued and used by Auth.js.\n *\n * The JWT issued by Auth.js is _encrypted by default_, using the _A256CBC-HS512_ algorithm ({@link https://www.rfc-editor.org/rfc/rfc7518.html#section-5.2.5 JWE}).\n * It uses the `AUTH_SECRET` environment variable or the passed `secret` property to derive a suitable encryption key.\n *\n * :::info Note\n * Auth.js JWTs are meant to be used by the same app that issued them.\n * If you need JWT authentication for your third-party API, you should rely on your Identity Provider instead.\n * :::\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install @auth/core\n * ```\n *\n * You can then import this submodule from `@auth/core/jwt`.\n *\n * ## Usage\n *\n * :::warning Warning\n * This module *will* be refactored/changed. We do not recommend relying on it right now.\n * :::\n *\n *\n * ## Resources\n *\n * - [What is a JWT session strategy](https://authjs.dev/concepts/session-strategies#jwt-session)\n * - [RFC7519 - JSON Web Token (JWT)](https://www.rfc-editor.org/rfc/rfc7519)\n *\n * @module jwt\n */\nimport { hkdf } from \"@panva/hkdf\";\nimport { EncryptJWT, base64url, calculateJwkThumbprint, jwtDecrypt } from \"jose\";\nimport { defaultCookies, SessionStore } from \"./lib/utils/cookie.js\";\nimport { MissingSecret } from \"./errors.js\";\nimport * as cookie from \"./lib/vendored/cookie.js\";\nconst { parse: parseCookie } = cookie;\nconst DEFAULT_MAX_AGE = 30 * 24 * 60 * 60; // 30 days\nconst now = () => (Date.now() / 1000) | 0;\nconst alg = \"dir\";\nconst enc = \"A256CBC-HS512\";\n/** Issues a JWT. By default, the JWT is encrypted using \"A256CBC-HS512\". */\nexport async function encode(params) {\n    const { token = {}, secret, maxAge = DEFAULT_MAX_AGE, salt } = params;\n    const secrets = Array.isArray(secret) ? secret : [secret];\n    const encryptionSecret = await getDerivedEncryptionKey(enc, secrets[0], salt);\n    const thumbprint = await calculateJwkThumbprint({ kty: \"oct\", k: base64url.encode(encryptionSecret) }, `sha${encryptionSecret.byteLength << 3}`);\n    // @ts-expect-error `jose` allows any object as payload.\n    return await new EncryptJWT(token)\n        .setProtectedHeader({ alg, enc, kid: thumbprint })\n        .setIssuedAt()\n        .setExpirationTime(now() + maxAge)\n        .setJti(crypto.randomUUID())\n        .encrypt(encryptionSecret);\n}\n/** Decodes an Auth.js issued JWT. */\nexport async function decode(params) {\n    const { token, secret, salt } = params;\n    const secrets = Array.isArray(secret) ? secret : [secret];\n    if (!token)\n        return null;\n    const { payload } = await jwtDecrypt(token, async ({ kid, enc }) => {\n        for (const secret of secrets) {\n            const encryptionSecret = await getDerivedEncryptionKey(enc, secret, salt);\n            if (kid === undefined)\n                return encryptionSecret;\n            const thumbprint = await calculateJwkThumbprint({ kty: \"oct\", k: base64url.encode(encryptionSecret) }, `sha${encryptionSecret.byteLength << 3}`);\n            if (kid === thumbprint)\n                return encryptionSecret;\n        }\n        throw new Error(\"no matching decryption secret\");\n    }, {\n        clockTolerance: 15,\n        keyManagementAlgorithms: [alg],\n        contentEncryptionAlgorithms: [enc, \"A256GCM\"],\n    });\n    return payload;\n}\nexport async function getToken(params) {\n    const { secureCookie, cookieName = defaultCookies(secureCookie ?? false).sessionToken.name, decode: _decode = decode, salt = cookieName, secret, logger = console, raw, req, } = params;\n    if (!req)\n        throw new Error(\"Must pass `req` to JWT getToken()\");\n    const headers = req.headers instanceof Headers ? req.headers : new Headers(req.headers);\n    const sessionStore = new SessionStore({ name: cookieName, options: { secure: secureCookie } }, parseCookie(headers.get(\"cookie\") ?? \"\"), logger);\n    let token = sessionStore.value;\n    const authorizationHeader = headers.get(\"authorization\");\n    if (!token && authorizationHeader?.split(\" \")[0] === \"Bearer\") {\n        const urlEncodedToken = authorizationHeader.split(\" \")[1];\n        token = decodeURIComponent(urlEncodedToken);\n    }\n    if (!token)\n        return null;\n    if (raw)\n        return token;\n    if (!secret)\n        throw new MissingSecret(\"Must pass `secret` if not set to JWT getToken()\");\n    try {\n        return await _decode({ token, secret, salt });\n    }\n    catch {\n        return null;\n    }\n}\nasync function getDerivedEncryptionKey(enc, keyMaterial, salt) {\n    let length;\n    switch (enc) {\n        case \"A256CBC-HS512\":\n            length = 64;\n            break;\n        case \"A256GCM\":\n            length = 32;\n            break;\n        default:\n            throw new Error(\"Unsupported JWT Content Encryption Algorithm\");\n    }\n    return await hkdf(\"sha256\", keyMaterial, salt, `Auth.js Generated Encryption Key (${salt})`, length);\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCC;;;;;AACD;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AACA,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG;AAC/B,MAAM,kBAAkB,KAAK,KAAK,KAAK,IAAI,UAAU;AACrD,MAAM,MAAM,IAAM,AAAC,KAAK,GAAG,KAAK,OAAQ;AACxC,MAAM,MAAM;AACZ,MAAM,MAAM;AAEL,eAAe,OAAO,MAAM;IAC/B,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,SAAS,eAAe,EAAE,IAAI,EAAE,GAAG;IAC/D,MAAM,UAAU,MAAM,OAAO,CAAC,UAAU,SAAS;QAAC;KAAO;IACzD,MAAM,mBAAmB,MAAM,wBAAwB,KAAK,OAAO,CAAC,EAAE,EAAE;IACxE,MAAM,aAAa,MAAM,CAAA,GAAA,qNAAA,CAAA,yBAAsB,AAAD,EAAE;QAAE,KAAK;QAAO,GAAG,0PAAA,CAAA,YAAS,CAAC,MAAM,CAAC;IAAkB,GAAG,CAAC,GAAG,EAAE,iBAAiB,UAAU,IAAI,GAAG;IAC/I,wDAAwD;IACxD,OAAO,MAAM,IAAI,kNAAA,CAAA,aAAU,CAAC,OACvB,kBAAkB,CAAC;QAAE;QAAK;QAAK,KAAK;IAAW,GAC/C,WAAW,GACX,iBAAiB,CAAC,QAAQ,QAC1B,MAAM,CAAC,OAAO,UAAU,IACxB,OAAO,CAAC;AACjB;AAEO,eAAe,OAAO,MAAM;IAC/B,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;IAChC,MAAM,UAAU,MAAM,OAAO,CAAC,UAAU,SAAS;QAAC;KAAO;IACzD,IAAI,CAAC,OACD,OAAO;IACX,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,kNAAA,CAAA,aAAU,AAAD,EAAE,OAAO,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE;QAC3D,KAAK,MAAM,UAAU,QAAS;YAC1B,MAAM,mBAAmB,MAAM,wBAAwB,KAAK,QAAQ;YACpE,IAAI,QAAQ,WACR,OAAO;YACX,MAAM,aAAa,MAAM,CAAA,GAAA,qNAAA,CAAA,yBAAsB,AAAD,EAAE;gBAAE,KAAK;gBAAO,GAAG,0PAAA,CAAA,YAAS,CAAC,MAAM,CAAC;YAAkB,GAAG,CAAC,GAAG,EAAE,iBAAiB,UAAU,IAAI,GAAG;YAC/I,IAAI,QAAQ,YACR,OAAO;QACf;QACA,MAAM,IAAI,MAAM;IACpB,GAAG;QACC,gBAAgB;QAChB,yBAAyB;YAAC;SAAI;QAC9B,6BAA6B;YAAC;YAAK;SAAU;IACjD;IACA,OAAO;AACX;AACO,eAAe,SAAS,MAAM;IACjC,MAAM,EAAE,YAAY,EAAE,aAAa,CAAA,GAAA,4NAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,OAAO,YAAY,CAAC,IAAI,EAAE,QAAQ,UAAU,MAAM,EAAE,OAAO,UAAU,EAAE,MAAM,EAAE,SAAS,OAAO,EAAE,GAAG,EAAE,GAAG,EAAG,GAAG;IACjL,IAAI,CAAC,KACD,MAAM,IAAI,MAAM;IACpB,MAAM,UAAU,IAAI,OAAO,YAAY,UAAU,IAAI,OAAO,GAAG,IAAI,QAAQ,IAAI,OAAO;IACtF,MAAM,eAAe,IAAI,4NAAA,CAAA,eAAY,CAAC;QAAE,MAAM;QAAY,SAAS;YAAE,QAAQ;QAAa;IAAE,GAAG,YAAY,QAAQ,GAAG,CAAC,aAAa,KAAK;IACzI,IAAI,QAAQ,aAAa,KAAK;IAC9B,MAAM,sBAAsB,QAAQ,GAAG,CAAC;IACxC,IAAI,CAAC,SAAS,qBAAqB,MAAM,IAAI,CAAC,EAAE,KAAK,UAAU;QAC3D,MAAM,kBAAkB,oBAAoB,KAAK,CAAC,IAAI,CAAC,EAAE;QACzD,QAAQ,mBAAmB;IAC/B;IACA,IAAI,CAAC,OACD,OAAO;IACX,IAAI,KACA,OAAO;IACX,IAAI,CAAC,QACD,MAAM,IAAI,4MAAA,CAAA,gBAAa,CAAC;IAC5B,IAAI;QACA,OAAO,MAAM,QAAQ;YAAE;YAAO;YAAQ;QAAK;IAC/C,EACA,OAAM;QACF,OAAO;IACX;AACJ;AACA,eAAe,wBAAwB,GAAG,EAAE,WAAW,EAAE,IAAI;IACzD,IAAI;IACJ,OAAQ;QACJ,KAAK;YACD,SAAS;YACT;QACJ,KAAK;YACD,SAAS;YACT;QACJ;YACI,MAAM,IAAI,MAAM;IACxB;IACA,OAAO,MAAM,CAAA,GAAA,2NAAA,CAAA,OAAI,AAAD,EAAE,UAAU,aAAa,MAAM,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC,EAAE;AACjG", "ignoreList": [0]}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/utils/callback-url.js"], "sourcesContent": ["/**\n * Get callback URL based on query param / cookie + validation,\n * and add it to `req.options.callbackUrl`.\n */\nexport async function createCallbackUrl({ options, paramValue, cookieValue, }) {\n    const { url, callbacks } = options;\n    let callbackUrl = url.origin;\n    if (paramValue) {\n        // If callbackUrl form field or query parameter is passed try to use it if allowed\n        callbackUrl = await callbacks.redirect({\n            url: paramValue,\n            baseUrl: url.origin,\n        });\n    }\n    else if (cookieValue) {\n        // If no callbackUrl specified, try using the value from the cookie if allowed\n        callbackUrl = await callbacks.redirect({\n            url: cookieValue,\n            baseUrl: url.origin,\n        });\n    }\n    return {\n        callbackUrl,\n        // Save callback URL in a cookie so that it can be used for subsequent requests in signin/signout/callback flow\n        callbackUrlCookie: callbackUrl !== cookieValue ? callbackUrl : undefined,\n    };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACM,eAAe,kBAAkB,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAG;IACzE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG;IAC3B,IAAI,cAAc,IAAI,MAAM;IAC5B,IAAI,YAAY;QACZ,kFAAkF;QAClF,cAAc,MAAM,UAAU,QAAQ,CAAC;YACnC,KAAK;YACL,SAAS,IAAI,MAAM;QACvB;IACJ,OACK,IAAI,aAAa;QAClB,8EAA8E;QAC9E,cAAc,MAAM,UAAU,QAAQ,CAAC;YACnC,KAAK;YACL,SAAS,IAAI,MAAM;QACvB;IACJ;IACA,OAAO;QACH;QACA,+GAA+G;QAC/G,mBAAmB,gBAAgB,cAAc,cAAc;IACnE;AACJ", "ignoreList": [0]}}, {"offset": {"line": 978, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/utils/logger.js"], "sourcesContent": ["import { AuthError } from \"../../errors.js\";\nconst red = \"\\x1b[31m\";\nconst yellow = \"\\x1b[33m\";\nconst grey = \"\\x1b[90m\";\nconst reset = \"\\x1b[0m\";\nconst defaultLogger = {\n    error(error) {\n        const name = error instanceof AuthError ? error.type : error.name;\n        console.error(`${red}[auth][error]${reset} ${name}: ${error.message}`);\n        if (error.cause &&\n            typeof error.cause === \"object\" &&\n            \"err\" in error.cause &&\n            error.cause.err instanceof Error) {\n            const { err, ...data } = error.cause;\n            console.error(`${red}[auth][cause]${reset}:`, err.stack);\n            if (data)\n                console.error(`${red}[auth][details]${reset}:`, JSON.stringify(data, null, 2));\n        }\n        else if (error.stack) {\n            console.error(error.stack.replace(/.*/, \"\").substring(1));\n        }\n    },\n    warn(code) {\n        const url = `https://warnings.authjs.dev`;\n        console.warn(`${yellow}[auth][warn][${code}]${reset}`, `Read more: ${url}`);\n    },\n    debug(message, metadata) {\n        console.log(`${grey}[auth][debug]:${reset} ${message}`, JSON.stringify(metadata, null, 2));\n    },\n};\n/**\n * Override the built-in logger with user's implementation.\n * Any `undefined` level will use the default logger.\n */\nexport function setLogger(config) {\n    const newLogger = {\n        ...defaultLogger,\n    };\n    // Turn off debug logging if `debug` isn't set to `true`\n    if (!config.debug)\n        newLogger.debug = () => { };\n    if (config.logger?.error)\n        newLogger.error = config.logger.error;\n    if (config.logger?.warn)\n        newLogger.warn = config.logger.warn;\n    if (config.logger?.debug)\n        newLogger.debug = config.logger.debug;\n    config.logger ?? (config.logger = newLogger);\n    return newLogger;\n}\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,MAAM;AACZ,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,gBAAgB;IAClB,OAAM,KAAK;QACP,MAAM,OAAO,iBAAiB,4MAAA,CAAA,YAAS,GAAG,MAAM,IAAI,GAAG,MAAM,IAAI;QACjE,QAAQ,KAAK,CAAC,GAAG,IAAI,aAAa,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,OAAO,EAAE;QACrE,IAAI,MAAM,KAAK,IACX,OAAO,MAAM,KAAK,KAAK,YACvB,SAAS,MAAM,KAAK,IACpB,MAAM,KAAK,CAAC,GAAG,YAAY,OAAO;YAClC,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,GAAG,MAAM,KAAK;YACpC,QAAQ,KAAK,CAAC,GAAG,IAAI,aAAa,EAAE,MAAM,CAAC,CAAC,EAAE,IAAI,KAAK;YACvD,IAAI,MACA,QAAQ,KAAK,CAAC,GAAG,IAAI,eAAe,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM;QACnF,OACK,IAAI,MAAM,KAAK,EAAE;YAClB,QAAQ,KAAK,CAAC,MAAM,KAAK,CAAC,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC;QAC1D;IACJ;IACA,MAAK,IAAI;QACL,MAAM,MAAM,CAAC,2BAA2B,CAAC;QACzC,QAAQ,IAAI,CAAC,GAAG,OAAO,aAAa,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,WAAW,EAAE,KAAK;IAC9E;IACA,OAAM,OAAO,EAAE,QAAQ;QACnB,QAAQ,GAAG,CAAC,GAAG,KAAK,cAAc,EAAE,MAAM,CAAC,EAAE,SAAS,EAAE,KAAK,SAAS,CAAC,UAAU,MAAM;IAC3F;AACJ;AAKO,SAAS,UAAU,MAAM;IAC5B,MAAM,YAAY;QACd,GAAG,aAAa;IACpB;IACA,wDAAwD;IACxD,IAAI,CAAC,OAAO,KAAK,EACb,UAAU,KAAK,GAAG,KAAQ;IAC9B,IAAI,OAAO,MAAM,EAAE,OACf,UAAU,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;IACzC,IAAI,OAAO,MAAM,EAAE,MACf,UAAU,IAAI,GAAG,OAAO,MAAM,CAAC,IAAI;IACvC,IAAI,OAAO,MAAM,EAAE,OACf,UAAU,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;IACzC,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,GAAG,SAAS;IAC3C,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 1025, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/utils/actions.js"], "sourcesContent": ["const actions = [\n    \"providers\",\n    \"session\",\n    \"csrf\",\n    \"signin\",\n    \"signout\",\n    \"callback\",\n    \"verify-request\",\n    \"error\",\n    \"webauthn-options\",\n];\nexport function isAuthAction(action) {\n    return actions.includes(action);\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,UAAU;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACM,SAAS,aAAa,MAAM;IAC/B,OAAO,QAAQ,QAAQ,CAAC;AAC5B", "ignoreList": [0]}}, {"offset": {"line": 1048, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/utils/web.js"], "sourcesContent": ["import * as cookie from \"../vendored/cookie.js\";\nimport { UnknownAction } from \"../../errors.js\";\nimport { setLogger } from \"./logger.js\";\nimport { isAuthAction } from \"./actions.js\";\nconst { parse: parseCookie, serialize: serializeCookie } = cookie;\nasync function getBody(req) {\n    if (!(\"body\" in req) || !req.body || req.method !== \"POST\")\n        return;\n    const contentType = req.headers.get(\"content-type\");\n    if (contentType?.includes(\"application/json\")) {\n        return await req.json();\n    }\n    else if (contentType?.includes(\"application/x-www-form-urlencoded\")) {\n        const params = new URLSearchParams(await req.text());\n        return Object.fromEntries(params);\n    }\n}\nexport async function toInternalRequest(req, config) {\n    try {\n        if (req.method !== \"GET\" && req.method !== \"POST\")\n            throw new UnknownAction(\"Only GET and POST requests are supported\");\n        // Defaults are usually set in the `init` function, but this is needed below\n        config.basePath ?? (config.basePath = \"/auth\");\n        const url = new URL(req.url);\n        const { action, providerId } = parseActionAndProviderId(url.pathname, config.basePath);\n        return {\n            url,\n            action,\n            providerId,\n            method: req.method,\n            headers: Object.fromEntries(req.headers),\n            body: req.body ? await getBody(req) : undefined,\n            cookies: parseCookie(req.headers.get(\"cookie\") ?? \"\") ?? {},\n            error: url.searchParams.get(\"error\") ?? undefined,\n            query: Object.fromEntries(url.searchParams),\n        };\n    }\n    catch (e) {\n        const logger = setLogger(config);\n        logger.error(e);\n        logger.debug(\"request\", req);\n    }\n}\nexport function toRequest(request) {\n    return new Request(request.url, {\n        headers: request.headers,\n        method: request.method,\n        body: request.method === \"POST\"\n            ? JSON.stringify(request.body ?? {})\n            : undefined,\n    });\n}\nexport function toResponse(res) {\n    const headers = new Headers(res.headers);\n    res.cookies?.forEach((cookie) => {\n        const { name, value, options } = cookie;\n        const cookieHeader = serializeCookie(name, value, options);\n        if (headers.has(\"Set-Cookie\"))\n            headers.append(\"Set-Cookie\", cookieHeader);\n        else\n            headers.set(\"Set-Cookie\", cookieHeader);\n    });\n    let body = res.body;\n    if (headers.get(\"content-type\") === \"application/json\")\n        body = JSON.stringify(res.body);\n    else if (headers.get(\"content-type\") === \"application/x-www-form-urlencoded\")\n        body = new URLSearchParams(res.body).toString();\n    const status = res.redirect ? 302 : (res.status ?? 200);\n    const response = new Response(body, { headers, status });\n    if (res.redirect)\n        response.headers.set(\"Location\", res.redirect);\n    return response;\n}\n/** Web compatible method to create a hash, using SHA256 */\nexport async function createHash(message) {\n    const data = new TextEncoder().encode(message);\n    const hash = await crypto.subtle.digest(\"SHA-256\", data);\n    return Array.from(new Uint8Array(hash))\n        .map((b) => b.toString(16).padStart(2, \"0\"))\n        .join(\"\")\n        .toString();\n}\n/** Web compatible method to create a random string of a given length */\nexport function randomString(size) {\n    const i2hex = (i) => (\"0\" + i.toString(16)).slice(-2);\n    const r = (a, i) => a + i2hex(i);\n    const bytes = crypto.getRandomValues(new Uint8Array(size));\n    return Array.from(bytes).reduce(r, \"\");\n}\n/** @internal Parse the action and provider id from a URL pathname. */\nexport function parseActionAndProviderId(pathname, base) {\n    const a = pathname.match(new RegExp(`^${base}(.+)`));\n    if (a === null)\n        throw new UnknownAction(`Cannot parse action at ${pathname}`);\n    const actionAndProviderId = a.at(-1);\n    const b = actionAndProviderId.replace(/^\\//, \"\").split(\"/\").filter(Boolean);\n    if (b.length !== 1 && b.length !== 2)\n        throw new UnknownAction(`Cannot parse action at ${pathname}`);\n    const [action, providerId] = b;\n    if (!isAuthAction(action))\n        throw new UnknownAction(`Cannot parse action at ${pathname}`);\n    if (providerId &&\n        ![\"signin\", \"callback\", \"webauthn-options\"].includes(action))\n        throw new UnknownAction(`Cannot parse action at ${pathname}`);\n    return { action, providerId };\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;;;;;AACA,MAAM,EAAE,OAAO,WAAW,EAAE,WAAW,eAAe,EAAE,GAAG;AAC3D,eAAe,QAAQ,GAAG;IACtB,IAAI,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,IAAI,IAAI,IAAI,MAAM,KAAK,QAChD;IACJ,MAAM,cAAc,IAAI,OAAO,CAAC,GAAG,CAAC;IACpC,IAAI,aAAa,SAAS,qBAAqB;QAC3C,OAAO,MAAM,IAAI,IAAI;IACzB,OACK,IAAI,aAAa,SAAS,sCAAsC;QACjE,MAAM,SAAS,IAAI,gBAAgB,MAAM,IAAI,IAAI;QACjD,OAAO,OAAO,WAAW,CAAC;IAC9B;AACJ;AACO,eAAe,kBAAkB,GAAG,EAAE,MAAM;IAC/C,IAAI;QACA,IAAI,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,QACvC,MAAM,IAAI,4MAAA,CAAA,gBAAa,CAAC;QAC5B,4EAA4E;QAC5E,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,GAAG,OAAO;QAC7C,MAAM,MAAM,IAAI,IAAI,IAAI,GAAG;QAC3B,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,yBAAyB,IAAI,QAAQ,EAAE,OAAO,QAAQ;QACrF,OAAO;YACH;YACA;YACA;YACA,QAAQ,IAAI,MAAM;YAClB,SAAS,OAAO,WAAW,CAAC,IAAI,OAAO;YACvC,MAAM,IAAI,IAAI,GAAG,MAAM,QAAQ,OAAO;YACtC,SAAS,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,OAAO,CAAC;YAC1D,OAAO,IAAI,YAAY,CAAC,GAAG,CAAC,YAAY;YACxC,OAAO,OAAO,WAAW,CAAC,IAAI,YAAY;QAC9C;IACJ,EACA,OAAO,GAAG;QACN,MAAM,SAAS,CAAA,GAAA,4NAAA,CAAA,YAAS,AAAD,EAAE;QACzB,OAAO,KAAK,CAAC;QACb,OAAO,KAAK,CAAC,WAAW;IAC5B;AACJ;AACO,SAAS,UAAU,OAAO;IAC7B,OAAO,IAAI,QAAQ,QAAQ,GAAG,EAAE;QAC5B,SAAS,QAAQ,OAAO;QACxB,QAAQ,QAAQ,MAAM;QACtB,MAAM,QAAQ,MAAM,KAAK,SACnB,KAAK,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,KAChC;IACV;AACJ;AACO,SAAS,WAAW,GAAG;IAC1B,MAAM,UAAU,IAAI,QAAQ,IAAI,OAAO;IACvC,IAAI,OAAO,EAAE,QAAQ,CAAC;QAClB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG;QACjC,MAAM,eAAe,gBAAgB,MAAM,OAAO;QAClD,IAAI,QAAQ,GAAG,CAAC,eACZ,QAAQ,MAAM,CAAC,cAAc;aAE7B,QAAQ,GAAG,CAAC,cAAc;IAClC;IACA,IAAI,OAAO,IAAI,IAAI;IACnB,IAAI,QAAQ,GAAG,CAAC,oBAAoB,oBAChC,OAAO,KAAK,SAAS,CAAC,IAAI,IAAI;SAC7B,IAAI,QAAQ,GAAG,CAAC,oBAAoB,qCACrC,OAAO,IAAI,gBAAgB,IAAI,IAAI,EAAE,QAAQ;IACjD,MAAM,SAAS,IAAI,QAAQ,GAAG,MAAO,IAAI,MAAM,IAAI;IACnD,MAAM,WAAW,IAAI,SAAS,MAAM;QAAE;QAAS;IAAO;IACtD,IAAI,IAAI,QAAQ,EACZ,SAAS,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,QAAQ;IACjD,OAAO;AACX;AAEO,eAAe,WAAW,OAAO;IACpC,MAAM,OAAO,IAAI,cAAc,MAAM,CAAC;IACtC,MAAM,OAAO,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,WAAW;IACnD,OAAO,MAAM,IAAI,CAAC,IAAI,WAAW,OAC5B,GAAG,CAAC,CAAC,IAAM,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MACtC,IAAI,CAAC,IACL,QAAQ;AACjB;AAEO,SAAS,aAAa,IAAI;IAC7B,MAAM,QAAQ,CAAC,IAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACnD,MAAM,IAAI,CAAC,GAAG,IAAM,IAAI,MAAM;IAC9B,MAAM,QAAQ,OAAO,eAAe,CAAC,IAAI,WAAW;IACpD,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG;AACvC;AAEO,SAAS,yBAAyB,QAAQ,EAAE,IAAI;IACnD,MAAM,IAAI,SAAS,KAAK,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC;IAClD,IAAI,MAAM,MACN,MAAM,IAAI,4MAAA,CAAA,gBAAa,CAAC,CAAC,uBAAuB,EAAE,UAAU;IAChE,MAAM,sBAAsB,EAAE,EAAE,CAAC,CAAC;IAClC,MAAM,IAAI,oBAAoB,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC;IACnE,IAAI,EAAE,MAAM,KAAK,KAAK,EAAE,MAAM,KAAK,GAC/B,MAAM,IAAI,4MAAA,CAAA,gBAAa,CAAC,CAAC,uBAAuB,EAAE,UAAU;IAChE,MAAM,CAAC,QAAQ,WAAW,GAAG;IAC7B,IAAI,CAAC,CAAA,GAAA,6NAAA,CAAA,eAAY,AAAD,EAAE,SACd,MAAM,IAAI,4MAAA,CAAA,gBAAa,CAAC,CAAC,uBAAuB,EAAE,UAAU;IAChE,IAAI,cACA,CAAC;QAAC;QAAU;QAAY;KAAmB,CAAC,QAAQ,CAAC,SACrD,MAAM,IAAI,4MAAA,CAAA,gBAAa,CAAC,CAAC,uBAAuB,EAAE,UAAU;IAChE,OAAO;QAAE;QAAQ;IAAW;AAChC", "ignoreList": [0]}}, {"offset": {"line": 1160, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/actions/callback/oauth/csrf-token.js"], "sourcesContent": ["import { createHash, randomString } from \"../../../utils/web.js\";\nimport { MissingCSRF } from \"../../../../errors.js\";\n/**\n * Ensure CSRF Token cookie is set for any subsequent requests.\n * Used as part of the strategy for mitigation for CSRF tokens.\n *\n * Creates a cookie like 'next-auth.csrf-token' with the value 'token|hash',\n * where 'token' is the CSRF token and 'hash' is a hash made of the token and\n * the secret, and the two values are joined by a pipe '|'. By storing the\n * value and the hash of the value (with the secret used as a salt) we can\n * verify the cookie was set by the server and not by a malicious attacker.\n *\n * For more details, see the following OWASP links:\n * https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html#double-submit-cookie\n * https://owasp.org/www-chapter-london/assets/slides/<PERSON>_<PERSON>-Double_Defeat_of_Double-Submit_Cookie.pdf\n */\nexport async function createCSRFToken({ options, cookieValue, isPost, bodyValue, }) {\n    if (cookieValue) {\n        const [csrfToken, csrfTokenHash] = cookieValue.split(\"|\");\n        const expectedCsrfTokenHash = await createHash(`${csrfToken}${options.secret}`);\n        if (csrfTokenHash === expectedCsrfTokenHash) {\n            // If hash matches then we trust the CSRF token value\n            // If this is a POST request and the CSRF Token in the POST request matches\n            // the cookie we have already verified is the one we have set, then the token is verified!\n            const csrfTokenVerified = isPost && csrfToken === bodyValue;\n            return { csrfTokenVerified, csrfToken };\n        }\n    }\n    // New CSRF token\n    const csrfToken = randomString(32);\n    const csrfTokenHash = await createHash(`${csrfToken}${options.secret}`);\n    const cookie = `${csrfToken}|${csrfTokenHash}`;\n    return { cookie, csrfToken };\n}\nexport function validateCSRF(action, verified) {\n    if (verified)\n        return;\n    throw new MissingCSRF(`CSRF token was missing during an action ${action}`);\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAeO,eAAe,gBAAgB,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAG;IAC9E,IAAI,aAAa;QACb,MAAM,CAAC,WAAW,cAAc,GAAG,YAAY,KAAK,CAAC;QACrD,MAAM,wBAAwB,MAAM,CAAA,GAAA,yNAAA,CAAA,aAAU,AAAD,EAAE,GAAG,YAAY,QAAQ,MAAM,EAAE;QAC9E,IAAI,kBAAkB,uBAAuB;YACzC,qDAAqD;YACrD,2EAA2E;YAC3E,0FAA0F;YAC1F,MAAM,oBAAoB,UAAU,cAAc;YAClD,OAAO;gBAAE;gBAAmB;YAAU;QAC1C;IACJ;IACA,iBAAiB;IACjB,MAAM,YAAY,CAAA,GAAA,yNAAA,CAAA,eAAY,AAAD,EAAE;IAC/B,MAAM,gBAAgB,MAAM,CAAA,GAAA,yNAAA,CAAA,aAAU,AAAD,EAAE,GAAG,YAAY,QAAQ,MAAM,EAAE;IACtE,MAAM,SAAS,GAAG,UAAU,CAAC,EAAE,eAAe;IAC9C,OAAO;QAAE;QAAQ;IAAU;AAC/B;AACO,SAAS,aAAa,MAAM,EAAE,QAAQ;IACzC,IAAI,UACA;IACJ,MAAM,IAAI,4MAAA,CAAA,cAAW,CAAC,CAAC,wCAAwC,EAAE,QAAQ;AAC7E", "ignoreList": [0]}}, {"offset": {"line": 1202, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/utils/merge.js"], "sourcesContent": ["function isObject(item) {\n    return item !== null && typeof item === \"object\";\n}\n/** Deep merge two or more objects */\nexport function merge(target, ...sources) {\n    if (!sources.length)\n        return target;\n    const source = sources.shift();\n    if (isObject(target) && isObject(source)) {\n        for (const key in source) {\n            if (isObject(source[key])) {\n                if (!isObject(target[key]))\n                    target[key] = Array.isArray(source[key])\n                        ? []\n                        : {};\n                merge(target[key], source[key]);\n            }\n            else if (source[key] !== undefined)\n                target[key] = source[key];\n        }\n    }\n    return merge(target, ...sources);\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,SAAS,IAAI;IAClB,OAAO,SAAS,QAAQ,OAAO,SAAS;AAC5C;AAEO,SAAS,MAAM,MAAM,EAAE,GAAG,OAAO;IACpC,IAAI,CAAC,QAAQ,MAAM,EACf,OAAO;IACX,MAAM,SAAS,QAAQ,KAAK;IAC5B,IAAI,SAAS,WAAW,SAAS,SAAS;QACtC,IAAK,MAAM,OAAO,OAAQ;YACtB,IAAI,SAAS,MAAM,CAAC,IAAI,GAAG;gBACvB,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI,GACrB,MAAM,CAAC,IAAI,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,IAAI,IACjC,EAAE,GACF,CAAC;gBACX,MAAM,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI;YAClC,OACK,IAAI,MAAM,CAAC,IAAI,KAAK,WACrB,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QACjC;IACJ;IACA,OAAO,MAAM,WAAW;AAC5B", "ignoreList": [0]}}, {"offset": {"line": 1227, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/symbols.js"], "sourcesContent": ["/**\n * :::danger\n * This option is intended for framework authors.\n * :::\n *\n * Auth.js comes with built-in CSRF protection, but\n * if you are implementing a framework that is already protected against CSRF attacks, you can skip this check by\n * passing this value to {@link AuthConfig.skipCSRFCheck}.\n */\nexport const skipCSRFCheck = Symbol(\"skip-csrf-check\");\n/**\n * :::danger\n * This option is intended for framework authors.\n * :::\n *\n * Auth.js returns a web standard {@link Response} by default, but\n * if you are implementing a framework you might want to get access to the raw internal response\n * by passing this value to {@link AuthConfig.raw}.\n */\nexport const raw = Symbol(\"return-type-raw\");\n/**\n * :::danger\n * This option allows you to override the default `fetch` function used by the provider\n * to make requests to the provider's OAuth endpoints directly.\n * Used incorrectly, it can have security implications.\n * :::\n *\n * It can be used to support corporate proxies, custom fetch libraries, cache discovery endpoints,\n * add mocks for testing, logging, set custom headers/params for non-spec compliant providers, etc.\n *\n * @example\n * ```ts\n * import { Auth, customFetch } from \"@auth/core\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * const dispatcher = new ProxyAgent(\"my.proxy.server\")\n * function proxy(...args: Parameters<typeof fetch>): ReturnType<typeof fetch> {\n *   return undici(args[0], { ...(args[1] ?? {}), dispatcher })\n * }\n *\n * const response = await Auth(request, {\n *   providers: [GitHub({ [customFetch]: proxy })]\n * })\n * ```\n *\n * @see https://undici.nodejs.org/#/docs/api/ProxyAgent?id=example-basic-proxy-request-with-local-agent-dispatcher\n * @see https://authjs.dev/guides/corporate-proxy\n */\nexport const customFetch = Symbol(\"custom-fetch\");\n/**\n * @internal\n *\n * Used to mark some providers for processing within the core library.\n *\n * **Do not use or you will be fired.**\n */\nexport const conformInternal = Symbol(\"conform-internal\");\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;AACM,MAAM,gBAAgB,OAAO;AAU7B,MAAM,MAAM,OAAO;AA6BnB,MAAM,cAAc,OAAO;AAQ3B,MAAM,kBAAkB,OAAO", "ignoreList": [0]}}, {"offset": {"line": 1251, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/utils/providers.js"], "sourcesContent": ["import { merge } from \"./merge.js\";\nimport { customFetch } from \"../symbols.js\";\n/**\n * Adds `signinUrl` and `callbackUrl` to each provider\n * and deep merge user-defined options.\n */\nexport default function parseProviders(params) {\n    const { providerId, config } = params;\n    const url = new URL(config.basePath ?? \"/auth\", params.url.origin);\n    const providers = config.providers.map((p) => {\n        const provider = typeof p === \"function\" ? p() : p;\n        const { options: userOptions, ...defaults } = provider;\n        const id = (userOptions?.id ?? defaults.id);\n        // TODO: Support if properties have different types, e.g. authorization: string or object\n        const merged = merge(defaults, userOptions, {\n            signinUrl: `${url}/signin/${id}`,\n            callbackUrl: `${url}/callback/${id}`,\n        });\n        if (provider.type === \"oauth\" || provider.type === \"oidc\") {\n            merged.redirectProxyUrl ?? (merged.redirectProxyUrl = userOptions?.redirectProxyUrl ?? config.redirectProxyUrl);\n            const normalized = normalizeOAuth(merged);\n            // We currently don't support redirect proxies for response_mode=form_post\n            if (normalized.authorization?.url.searchParams.get(\"response_mode\") ===\n                \"form_post\") {\n                delete normalized.redirectProxyUrl;\n            }\n            // @ts-expect-error Symbols don't get merged by the `merge` function\n            // so we need to do it manually.\n            normalized[customFetch] ?? (normalized[customFetch] = userOptions?.[customFetch]);\n            return normalized;\n        }\n        return merged;\n    });\n    const provider = providers.find(({ id }) => id === providerId);\n    if (providerId && !provider) {\n        const availableProviders = providers.map((p) => p.id).join(\", \");\n        throw new Error(`Provider with id \"${providerId}\" not found. Available providers: [${availableProviders}].`);\n    }\n    return { providers, provider };\n}\n// TODO: Also add discovery here, if some endpoints/config are missing.\n// We should return both a client and authorization server config.\nfunction normalizeOAuth(c) {\n    if (c.issuer)\n        c.wellKnown ?? (c.wellKnown = `${c.issuer}/.well-known/openid-configuration`);\n    const authorization = normalizeEndpoint(c.authorization, c.issuer);\n    if (authorization && !authorization.url?.searchParams.has(\"scope\")) {\n        authorization.url.searchParams.set(\"scope\", \"openid profile email\");\n    }\n    const token = normalizeEndpoint(c.token, c.issuer);\n    const userinfo = normalizeEndpoint(c.userinfo, c.issuer);\n    const checks = c.checks ?? [\"pkce\"];\n    if (c.redirectProxyUrl) {\n        if (!checks.includes(\"state\"))\n            checks.push(\"state\");\n        c.redirectProxyUrl = `${c.redirectProxyUrl}/callback/${c.id}`;\n    }\n    return {\n        ...c,\n        authorization,\n        token,\n        checks,\n        userinfo,\n        profile: c.profile ?? defaultProfile,\n        account: c.account ?? defaultAccount,\n    };\n}\n/**\n * Returns basic user profile from the userinfo response/`id_token` claims.\n * The returned `id` will become the `account.providerAccountId`. `user.id`\n * and `account.id` are auto-generated UUID's.\n *\n * The result if this function is used to create the `User` in the database.\n * @see https://authjs.dev/reference/core/adapters#user\n * @see https://openid.net/specs/openid-connect-core-1_0.html#IDToken\n * @see https://openid.net/specs/openid-connect-core-1_0.html#\n */\nconst defaultProfile = (profile) => {\n    return stripUndefined({\n        id: profile.sub ?? profile.id ?? crypto.randomUUID(),\n        name: profile.name ?? profile.nickname ?? profile.preferred_username,\n        email: profile.email,\n        image: profile.picture,\n    });\n};\n/**\n * Returns basic OAuth/OIDC values from the token response.\n * @see https://www.ietf.org/rfc/rfc6749.html#section-5.1\n * @see https://openid.net/specs/openid-connect-core-1_0.html#TokenResponse\n * @see https://authjs.dev/reference/core/adapters#account\n */\nconst defaultAccount = (account) => {\n    return stripUndefined({\n        access_token: account.access_token,\n        id_token: account.id_token,\n        refresh_token: account.refresh_token,\n        expires_at: account.expires_at,\n        scope: account.scope,\n        token_type: account.token_type,\n        session_state: account.session_state,\n    });\n};\nfunction stripUndefined(o) {\n    const result = {};\n    for (const [k, v] of Object.entries(o)) {\n        if (v !== undefined)\n            result[k] = v;\n    }\n    return result;\n}\nfunction normalizeEndpoint(e, issuer) {\n    if (!e && issuer)\n        return;\n    if (typeof e === \"string\") {\n        return { url: new URL(e) };\n    }\n    // If e.url is undefined, it's because the provider config\n    // assumes that we will use the issuer endpoint.\n    // The existence of either e.url or provider.issuer is checked in\n    // assert.ts. We fallback to \"https://authjs.dev\" to be able to pass around\n    // a valid URL even if the user only provided params.\n    // NOTE: This need to be checked when constructing the URL\n    // for the authorization, token and userinfo endpoints.\n    const url = new URL(e?.url ?? \"https://authjs.dev\");\n    if (e?.params != null) {\n        for (let [key, value] of Object.entries(e.params)) {\n            if (key === \"claims\") {\n                value = JSON.stringify(value);\n            }\n            url.searchParams.set(key, String(value));\n        }\n    }\n    return {\n        url,\n        request: e?.request,\n        conform: e?.conform,\n        ...(e?.clientPrivateKey ? { clientPrivateKey: e?.clientPrivateKey } : null),\n    };\n}\nexport function isOIDCProvider(provider) {\n    return provider.type === \"oidc\";\n}\nexport function isOAuth2Provider(provider) {\n    return provider.type === \"oauth\";\n}\n/** Either OAuth 2 or OIDC */\nexport function isOAuthProvider(provider) {\n    return provider.type === \"oauth\" || provider.type === \"oidc\";\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAKe,SAAS,eAAe,MAAM;IACzC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;IAC/B,MAAM,MAAM,IAAI,IAAI,OAAO,QAAQ,IAAI,SAAS,OAAO,GAAG,CAAC,MAAM;IACjE,MAAM,YAAY,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,WAAW,OAAO,MAAM,aAAa,MAAM;QACjD,MAAM,EAAE,SAAS,WAAW,EAAE,GAAG,UAAU,GAAG;QAC9C,MAAM,KAAM,aAAa,MAAM,SAAS,EAAE;QAC1C,yFAAyF;QACzF,MAAM,SAAS,CAAA,GAAA,2NAAA,CAAA,QAAK,AAAD,EAAE,UAAU,aAAa;YACxC,WAAW,GAAG,IAAI,QAAQ,EAAE,IAAI;YAChC,aAAa,GAAG,IAAI,UAAU,EAAE,IAAI;QACxC;QACA,IAAI,SAAS,IAAI,KAAK,WAAW,SAAS,IAAI,KAAK,QAAQ;YACvD,OAAO,gBAAgB,IAAI,CAAC,OAAO,gBAAgB,GAAG,aAAa,oBAAoB,OAAO,gBAAgB;YAC9G,MAAM,aAAa,eAAe;YAClC,0EAA0E;YAC1E,IAAI,WAAW,aAAa,EAAE,IAAI,aAAa,IAAI,qBAC/C,aAAa;gBACb,OAAO,WAAW,gBAAgB;YACtC;YACA,oEAAoE;YACpE,gCAAgC;YAChC,UAAU,CAAC,oNAAA,CAAA,cAAW,CAAC,IAAI,CAAC,UAAU,CAAC,oNAAA,CAAA,cAAW,CAAC,GAAG,aAAa,CAAC,oNAAA,CAAA,cAAW,CAAC;YAChF,OAAO;QACX;QACA,OAAO;IACX;IACA,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAK,OAAO;IACnD,IAAI,cAAc,CAAC,UAAU;QACzB,MAAM,qBAAqB,UAAU,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE,EAAE,IAAI,CAAC;QAC3D,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,WAAW,mCAAmC,EAAE,mBAAmB,EAAE,CAAC;IAC/G;IACA,OAAO;QAAE;QAAW;IAAS;AACjC;AACA,uEAAuE;AACvE,kEAAkE;AAClE,SAAS,eAAe,CAAC;IACrB,IAAI,EAAE,MAAM,EACR,EAAE,SAAS,IAAI,CAAC,EAAE,SAAS,GAAG,GAAG,EAAE,MAAM,CAAC,iCAAiC,CAAC;IAChF,MAAM,gBAAgB,kBAAkB,EAAE,aAAa,EAAE,EAAE,MAAM;IACjE,IAAI,iBAAiB,CAAC,cAAc,GAAG,EAAE,aAAa,IAAI,UAAU;QAChE,cAAc,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS;IAChD;IACA,MAAM,QAAQ,kBAAkB,EAAE,KAAK,EAAE,EAAE,MAAM;IACjD,MAAM,WAAW,kBAAkB,EAAE,QAAQ,EAAE,EAAE,MAAM;IACvD,MAAM,SAAS,EAAE,MAAM,IAAI;QAAC;KAAO;IACnC,IAAI,EAAE,gBAAgB,EAAE;QACpB,IAAI,CAAC,OAAO,QAAQ,CAAC,UACjB,OAAO,IAAI,CAAC;QAChB,EAAE,gBAAgB,GAAG,GAAG,EAAE,gBAAgB,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE;IACjE;IACA,OAAO;QACH,GAAG,CAAC;QACJ;QACA;QACA;QACA;QACA,SAAS,EAAE,OAAO,IAAI;QACtB,SAAS,EAAE,OAAO,IAAI;IAC1B;AACJ;AACA;;;;;;;;;CASC,GACD,MAAM,iBAAiB,CAAC;IACpB,OAAO,eAAe;QAClB,IAAI,QAAQ,GAAG,IAAI,QAAQ,EAAE,IAAI,OAAO,UAAU;QAClD,MAAM,QAAQ,IAAI,IAAI,QAAQ,QAAQ,IAAI,QAAQ,kBAAkB;QACpE,OAAO,QAAQ,KAAK;QACpB,OAAO,QAAQ,OAAO;IAC1B;AACJ;AACA;;;;;CAKC,GACD,MAAM,iBAAiB,CAAC;IACpB,OAAO,eAAe;QAClB,cAAc,QAAQ,YAAY;QAClC,UAAU,QAAQ,QAAQ;QAC1B,eAAe,QAAQ,aAAa;QACpC,YAAY,QAAQ,UAAU;QAC9B,OAAO,QAAQ,KAAK;QACpB,YAAY,QAAQ,UAAU;QAC9B,eAAe,QAAQ,aAAa;IACxC;AACJ;AACA,SAAS,eAAe,CAAC;IACrB,MAAM,SAAS,CAAC;IAChB,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,OAAO,OAAO,CAAC,GAAI;QACpC,IAAI,MAAM,WACN,MAAM,CAAC,EAAE,GAAG;IACpB;IACA,OAAO;AACX;AACA,SAAS,kBAAkB,CAAC,EAAE,MAAM;IAChC,IAAI,CAAC,KAAK,QACN;IACJ,IAAI,OAAO,MAAM,UAAU;QACvB,OAAO;YAAE,KAAK,IAAI,IAAI;QAAG;IAC7B;IACA,0DAA0D;IAC1D,gDAAgD;IAChD,iEAAiE;IACjE,2EAA2E;IAC3E,qDAAqD;IACrD,0DAA0D;IAC1D,uDAAuD;IACvD,MAAM,MAAM,IAAI,IAAI,GAAG,OAAO;IAC9B,IAAI,GAAG,UAAU,MAAM;QACnB,KAAK,IAAI,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,EAAE,MAAM,EAAG;YAC/C,IAAI,QAAQ,UAAU;gBAClB,QAAQ,KAAK,SAAS,CAAC;YAC3B;YACA,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK,OAAO;QACrC;IACJ;IACA,OAAO;QACH;QACA,SAAS,GAAG;QACZ,SAAS,GAAG;QACZ,GAAI,GAAG,mBAAmB;YAAE,kBAAkB,GAAG;QAAiB,IAAI,IAAI;IAC9E;AACJ;AACO,SAAS,eAAe,QAAQ;IACnC,OAAO,SAAS,IAAI,KAAK;AAC7B;AACO,SAAS,iBAAiB,QAAQ;IACrC,OAAO,SAAS,IAAI,KAAK;AAC7B;AAEO,SAAS,gBAAgB,QAAQ;IACpC,OAAO,SAAS,IAAI,KAAK,WAAW,SAAS,IAAI,KAAK;AAC1D", "ignoreList": [0]}}, {"offset": {"line": 1411, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/init.js"], "sourcesContent": ["import * as jwt from \"../jwt.js\";\nimport { createCallbackUrl } from \"./utils/callback-url.js\";\nimport * as cookie from \"./utils/cookie.js\";\nimport { createCSRFToken } from \"./actions/callback/oauth/csrf-token.js\";\nimport { AdapterError, EventError } from \"../errors.js\";\nimport parseProviders from \"./utils/providers.js\";\nimport { setLogger } from \"./utils/logger.js\";\nimport { merge } from \"./utils/merge.js\";\nexport const defaultCallbacks = {\n    signIn() {\n        return true;\n    },\n    redirect({ url, baseUrl }) {\n        if (url.startsWith(\"/\"))\n            return `${baseUrl}${url}`;\n        else if (new URL(url).origin === baseUrl)\n            return url;\n        return baseUrl;\n    },\n    session({ session }) {\n        return {\n            user: {\n                name: session.user?.name,\n                email: session.user?.email,\n                image: session.user?.image,\n            },\n            expires: session.expires?.toISOString?.() ?? session.expires,\n        };\n    },\n    jwt({ token }) {\n        return token;\n    },\n};\n/** Initialize all internal options and cookies. */\nexport async function init({ authOptions: config, providerId, action, url, cookies: reqCookies, callbackUrl: reqCallbackUrl, csrfToken: reqCsrfToken, csrfDisabled, isPost, }) {\n    const logger = setLogger(config);\n    const { providers, provider } = parseProviders({ url, providerId, config });\n    const maxAge = 30 * 24 * 60 * 60; // Sessions expire after 30 days of being idle by default\n    let isOnRedirectProxy = false;\n    if ((provider?.type === \"oauth\" || provider?.type === \"oidc\") &&\n        provider.redirectProxyUrl) {\n        try {\n            isOnRedirectProxy =\n                new URL(provider.redirectProxyUrl).origin === url.origin;\n        }\n        catch {\n            throw new TypeError(`redirectProxyUrl must be a valid URL. Received: ${provider.redirectProxyUrl}`);\n        }\n    }\n    // User provided options are overridden by other options,\n    // except for the options with special handling above\n    const options = {\n        debug: false,\n        pages: {},\n        theme: {\n            colorScheme: \"auto\",\n            logo: \"\",\n            brandColor: \"\",\n            buttonText: \"\",\n        },\n        // Custom options override defaults\n        ...config,\n        // These computed settings can have values in userOptions but we override them\n        // and are request-specific.\n        url,\n        action,\n        // @ts-expect-errors\n        provider,\n        cookies: merge(cookie.defaultCookies(config.useSecureCookies ?? url.protocol === \"https:\"), config.cookies),\n        providers,\n        // Session options\n        session: {\n            // If no adapter specified, force use of JSON Web Tokens (stateless)\n            strategy: config.adapter ? \"database\" : \"jwt\",\n            maxAge,\n            updateAge: 24 * 60 * 60,\n            generateSessionToken: () => crypto.randomUUID(),\n            ...config.session,\n        },\n        // JWT options\n        jwt: {\n            secret: config.secret, // Asserted in assert.ts\n            maxAge: config.session?.maxAge ?? maxAge, // default to same as `session.maxAge`\n            encode: jwt.encode,\n            decode: jwt.decode,\n            ...config.jwt,\n        },\n        // Event messages\n        events: eventsErrorHandler(config.events ?? {}, logger),\n        adapter: adapterErrorHandler(config.adapter, logger),\n        // Callback functions\n        callbacks: { ...defaultCallbacks, ...config.callbacks },\n        logger,\n        callbackUrl: url.origin,\n        isOnRedirectProxy,\n        experimental: {\n            ...config.experimental,\n        },\n    };\n    // Init cookies\n    const cookies = [];\n    if (csrfDisabled) {\n        options.csrfTokenVerified = true;\n    }\n    else {\n        const { csrfToken, cookie: csrfCookie, csrfTokenVerified, } = await createCSRFToken({\n            options,\n            cookieValue: reqCookies?.[options.cookies.csrfToken.name],\n            isPost,\n            bodyValue: reqCsrfToken,\n        });\n        options.csrfToken = csrfToken;\n        options.csrfTokenVerified = csrfTokenVerified;\n        if (csrfCookie) {\n            cookies.push({\n                name: options.cookies.csrfToken.name,\n                value: csrfCookie,\n                options: options.cookies.csrfToken.options,\n            });\n        }\n    }\n    const { callbackUrl, callbackUrlCookie } = await createCallbackUrl({\n        options,\n        cookieValue: reqCookies?.[options.cookies.callbackUrl.name],\n        paramValue: reqCallbackUrl,\n    });\n    options.callbackUrl = callbackUrl;\n    if (callbackUrlCookie) {\n        cookies.push({\n            name: options.cookies.callbackUrl.name,\n            value: callbackUrlCookie,\n            options: options.cookies.callbackUrl.options,\n        });\n    }\n    return { options, cookies };\n}\n/** Wraps an object of methods and adds error handling. */\nfunction eventsErrorHandler(methods, logger) {\n    return Object.keys(methods).reduce((acc, name) => {\n        acc[name] = async (...args) => {\n            try {\n                const method = methods[name];\n                return await method(...args);\n            }\n            catch (e) {\n                logger.error(new EventError(e));\n            }\n        };\n        return acc;\n    }, {});\n}\n/** Handles adapter induced errors. */\nfunction adapterErrorHandler(adapter, logger) {\n    if (!adapter)\n        return;\n    return Object.keys(adapter).reduce((acc, name) => {\n        acc[name] = async (...args) => {\n            try {\n                logger.debug(`adapter_${name}`, { args });\n                const method = adapter[name];\n                return await method(...args);\n            }\n            catch (e) {\n                const error = new AdapterError(e);\n                logger.error(error);\n                throw error;\n            }\n        };\n        return acc;\n    }, {});\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACO,MAAM,mBAAmB;IAC5B;QACI,OAAO;IACX;IACA,UAAS,EAAE,GAAG,EAAE,OAAO,EAAE;QACrB,IAAI,IAAI,UAAU,CAAC,MACf,OAAO,GAAG,UAAU,KAAK;aACxB,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAC7B,OAAO;QACX,OAAO;IACX;IACA,SAAQ,EAAE,OAAO,EAAE;QACf,OAAO;YACH,MAAM;gBACF,MAAM,QAAQ,IAAI,EAAE;gBACpB,OAAO,QAAQ,IAAI,EAAE;gBACrB,OAAO,QAAQ,IAAI,EAAE;YACzB;YACA,SAAS,QAAQ,OAAO,EAAE,mBAAmB,QAAQ,OAAO;QAChE;IACJ;IACA,KAAI,EAAE,KAAK,EAAE;QACT,OAAO;IACX;AACJ;AAEO,eAAe,KAAK,EAAE,aAAa,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,UAAU,EAAE,aAAa,cAAc,EAAE,WAAW,YAAY,EAAE,YAAY,EAAE,MAAM,EAAG;IACzK,MAAM,SAAS,CAAA,GAAA,4NAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+NAAA,CAAA,UAAc,AAAD,EAAE;QAAE;QAAK;QAAY;IAAO;IACzE,MAAM,SAAS,KAAK,KAAK,KAAK,IAAI,yDAAyD;IAC3F,IAAI,oBAAoB;IACxB,IAAI,CAAC,UAAU,SAAS,WAAW,UAAU,SAAS,MAAM,KACxD,SAAS,gBAAgB,EAAE;QAC3B,IAAI;YACA,oBACI,IAAI,IAAI,SAAS,gBAAgB,EAAE,MAAM,KAAK,IAAI,MAAM;QAChE,EACA,OAAM;YACF,MAAM,IAAI,UAAU,CAAC,gDAAgD,EAAE,SAAS,gBAAgB,EAAE;QACtG;IACJ;IACA,yDAAyD;IACzD,qDAAqD;IACrD,MAAM,UAAU;QACZ,OAAO;QACP,OAAO,CAAC;QACR,OAAO;YACH,aAAa;YACb,MAAM;YACN,YAAY;YACZ,YAAY;QAChB;QACA,mCAAmC;QACnC,GAAG,MAAM;QACT,8EAA8E;QAC9E,4BAA4B;QAC5B;QACA;QACA,oBAAoB;QACpB;QACA,SAAS,CAAA,GAAA,2NAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,4NAAA,CAAA,iBAAqB,AAAD,EAAE,OAAO,gBAAgB,IAAI,IAAI,QAAQ,KAAK,WAAW,OAAO,OAAO;QAC1G;QACA,kBAAkB;QAClB,SAAS;YACL,oEAAoE;YACpE,UAAU,OAAO,OAAO,GAAG,aAAa;YACxC;YACA,WAAW,KAAK,KAAK;YACrB,sBAAsB,IAAM,OAAO,UAAU;YAC7C,GAAG,OAAO,OAAO;QACrB;QACA,cAAc;QACd,KAAK;YACD,QAAQ,OAAO,MAAM;YACrB,QAAQ,OAAO,OAAO,EAAE,UAAU;YAClC,QAAQ,yMAAA,CAAA,SAAU;YAClB,QAAQ,yMAAA,CAAA,SAAU;YAClB,GAAG,OAAO,GAAG;QACjB;QACA,iBAAiB;QACjB,QAAQ,mBAAmB,OAAO,MAAM,IAAI,CAAC,GAAG;QAChD,SAAS,oBAAoB,OAAO,OAAO,EAAE;QAC7C,qBAAqB;QACrB,WAAW;YAAE,GAAG,gBAAgB;YAAE,GAAG,OAAO,SAAS;QAAC;QACtD;QACA,aAAa,IAAI,MAAM;QACvB;QACA,cAAc;YACV,GAAG,OAAO,YAAY;QAC1B;IACJ;IACA,eAAe;IACf,MAAM,UAAU,EAAE;IAClB,IAAI,cAAc;QACd,QAAQ,iBAAiB,GAAG;IAChC,OACK;QACD,MAAM,EAAE,SAAS,EAAE,QAAQ,UAAU,EAAE,iBAAiB,EAAG,GAAG,MAAM,CAAA,GAAA,0PAAA,CAAA,kBAAe,AAAD,EAAE;YAChF;YACA,aAAa,YAAY,CAAC,QAAQ,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC;YACzD;YACA,WAAW;QACf;QACA,QAAQ,SAAS,GAAG;QACpB,QAAQ,iBAAiB,GAAG;QAC5B,IAAI,YAAY;YACZ,QAAQ,IAAI,CAAC;gBACT,MAAM,QAAQ,OAAO,CAAC,SAAS,CAAC,IAAI;gBACpC,OAAO;gBACP,SAAS,QAAQ,OAAO,CAAC,SAAS,CAAC,OAAO;YAC9C;QACJ;IACJ;IACA,MAAM,EAAE,WAAW,EAAE,iBAAiB,EAAE,GAAG,MAAM,CAAA,GAAA,qOAAA,CAAA,oBAAiB,AAAD,EAAE;QAC/D;QACA,aAAa,YAAY,CAAC,QAAQ,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;QAC3D,YAAY;IAChB;IACA,QAAQ,WAAW,GAAG;IACtB,IAAI,mBAAmB;QACnB,QAAQ,IAAI,CAAC;YACT,MAAM,QAAQ,OAAO,CAAC,WAAW,CAAC,IAAI;YACtC,OAAO;YACP,SAAS,QAAQ,OAAO,CAAC,WAAW,CAAC,OAAO;QAChD;IACJ;IACA,OAAO;QAAE;QAAS;IAAQ;AAC9B;AACA,wDAAwD,GACxD,SAAS,mBAAmB,OAAO,EAAE,MAAM;IACvC,OAAO,OAAO,IAAI,CAAC,SAAS,MAAM,CAAC,CAAC,KAAK;QACrC,GAAG,CAAC,KAAK,GAAG,OAAO,GAAG;YAClB,IAAI;gBACA,MAAM,SAAS,OAAO,CAAC,KAAK;gBAC5B,OAAO,MAAM,UAAU;YAC3B,EACA,OAAO,GAAG;gBACN,OAAO,KAAK,CAAC,IAAI,4MAAA,CAAA,aAAU,CAAC;YAChC;QACJ;QACA,OAAO;IACX,GAAG,CAAC;AACR;AACA,oCAAoC,GACpC,SAAS,oBAAoB,OAAO,EAAE,MAAM;IACxC,IAAI,CAAC,SACD;IACJ,OAAO,OAAO,IAAI,CAAC,SAAS,MAAM,CAAC,CAAC,KAAK;QACrC,GAAG,CAAC,KAAK,GAAG,OAAO,GAAG;YAClB,IAAI;gBACA,OAAO,KAAK,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE;oBAAE;gBAAK;gBACvC,MAAM,SAAS,OAAO,CAAC,KAAK;gBAC5B,OAAO,MAAM,UAAU;YAC3B,EACA,OAAO,GAAG;gBACN,MAAM,QAAQ,IAAI,4MAAA,CAAA,eAAY,CAAC;gBAC/B,OAAO,KAAK,CAAC;gBACb,MAAM;YACV;QACJ;QACA,OAAO;IACX,GAAG,CAAC;AACR", "ignoreList": [0]}}, {"offset": {"line": 1600, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/pages/error.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"preact/jsx-runtime\";\n/** Renders an error page. */\nexport default function ErrorPage(props) {\n    const { url, error = \"default\", theme } = props;\n    const signinPageUrl = `${url}/signin`;\n    const errors = {\n        default: {\n            status: 200,\n            heading: \"Error\",\n            message: (_jsx(\"p\", { children: _jsx(\"a\", { className: \"site\", href: url?.origin, children: url?.host }) })),\n        },\n        Configuration: {\n            status: 500,\n            heading: \"Server error\",\n            message: (_jsxs(\"div\", { children: [_jsx(\"p\", { children: \"There is a problem with the server configuration.\" }), _jsx(\"p\", { children: \"Check the server logs for more information.\" })] })),\n        },\n        AccessDenied: {\n            status: 403,\n            heading: \"Access Denied\",\n            message: (_jsxs(\"div\", { children: [_jsx(\"p\", { children: \"You do not have permission to sign in.\" }), _jsx(\"p\", { children: _jsx(\"a\", { className: \"button\", href: signinPageUrl, children: \"Sign in\" }) })] })),\n        },\n        Verification: {\n            status: 403,\n            heading: \"Unable to sign in\",\n            message: (_jsxs(\"div\", { children: [_jsx(\"p\", { children: \"The sign in link is no longer valid.\" }), _jsx(\"p\", { children: \"It may have been used already or it may have expired.\" })] })),\n            signin: (_jsx(\"a\", { className: \"button\", href: signinPageUrl, children: \"Sign in\" })),\n        },\n    };\n    const { status, heading, message, signin } = errors[error] ?? errors.default;\n    return {\n        status,\n        html: (_jsxs(\"div\", { className: \"error\", children: [theme?.brandColor && (_jsx(\"style\", { dangerouslySetInnerHTML: {\n                        __html: `\n        :root {\n          --brand-color: ${theme?.brandColor}\n        }\n      `,\n                    } })), _jsxs(\"div\", { className: \"card\", children: [theme?.logo && _jsx(\"img\", { src: theme?.logo, alt: \"Logo\", className: \"logo\" }), _jsx(\"h1\", { children: heading }), _jsx(\"div\", { className: \"message\", children: message }), signin] })] })),\n    };\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEe,SAAS,UAAU,KAAK;IACnC,MAAM,EAAE,GAAG,EAAE,QAAQ,SAAS,EAAE,KAAK,EAAE,GAAG;IAC1C,MAAM,gBAAgB,GAAG,IAAI,OAAO,CAAC;IACrC,MAAM,SAAS;QACX,SAAS;YACL,QAAQ;YACR,SAAS;YACT,SAAU,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,KAAK;gBAAE,UAAU,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,KAAK;oBAAE,WAAW;oBAAQ,MAAM,KAAK;oBAAQ,UAAU,KAAK;gBAAK;YAAG;QAC7G;QACA,eAAe;YACX,QAAQ;YACR,SAAS;YACT,SAAU,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,OAAO;gBAAE,UAAU;oBAAC,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wBAAE,UAAU;oBAAoD;oBAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wBAAE,UAAU;oBAA8C;iBAAG;YAAC;QAC9L;QACA,cAAc;YACV,QAAQ;YACR,SAAS;YACT,SAAU,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,OAAO;gBAAE,UAAU;oBAAC,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wBAAE,UAAU;oBAAyC;oBAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wBAAE,UAAU,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,KAAK;4BAAE,WAAW;4BAAU,MAAM;4BAAe,UAAU;wBAAU;oBAAG;iBAAG;YAAC;QAClN;QACA,cAAc;YACV,QAAQ;YACR,SAAS;YACT,SAAU,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,OAAO;gBAAE,UAAU;oBAAC,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wBAAE,UAAU;oBAAuC;oBAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wBAAE,UAAU;oBAAwD;iBAAG;YAAC;YACvL,QAAS,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,KAAK;gBAAE,WAAW;gBAAU,MAAM;gBAAe,UAAU;YAAU;QACvF;IACJ;IACA,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,IAAI,OAAO,OAAO;IAC5E,OAAO;QACH;QACA,MAAO,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,OAAO;YAAE,WAAW;YAAS,UAAU;gBAAC,OAAO,cAAe,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;oBAAE,yBAAyB;wBACpG,QAAQ,CAAC;;yBAER,EAAE,OAAO,WAAW;;MAEvC,CAAC;oBACa;gBAAE;gBAAK,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,OAAO;oBAAE,WAAW;oBAAQ,UAAU;wBAAC,OAAO,QAAQ,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,OAAO;4BAAE,KAAK,OAAO;4BAAM,KAAK;4BAAQ,WAAW;wBAAO;wBAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,MAAM;4BAAE,UAAU;wBAAQ;wBAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,OAAO;4BAAE,WAAW;4BAAW,UAAU;wBAAQ;wBAAI;qBAAO;gBAAC;aAAG;QAAC;IAC/P;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1716, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/utils/webauthn-client.js"], "sourcesContent": ["//@ts-check\n// Declare a SimpleWebAuthnBrowser variable as part of \"window\"\n/** @typedef {\"authenticate\"} WebAuthnAuthenticate */\n/** @typedef {\"register\"} WebAuthnRegister */\n/** @typedef {WebAuthnRegister | WebAuthnAuthenticate} WebAuthnOptionsAction */\n/**\n * @template {WebAuthnOptionsAction} T\n * @typedef {T extends WebAuthnAuthenticate ?\n *  { options: import(\"@simplewebauthn/types\").PublicKeyCredentialRequestOptionsJSON; action: \"authenticate\" } :\n *  T extends WebAuthnRegister ?\n *  { options: import(\"@simplewebauthn/types\").PublicKeyCredentialCreationOptionsJSON; action: \"register\" } :\n * never\n * } WebAuthnOptionsReturn\n */\n/**\n * webauthnScript is the client-side script that handles the webauthn form\n *\n * @param {string} authURL is the URL of the auth API\n * @param {string} providerID is the ID of the webauthn provider\n */\nexport async function webauthnScript(authURL, providerID) {\n    /** @type {typeof import(\"@simplewebauthn/browser\")} */\n    // @ts-ignore\n    const WebAuthnBrowser = window.SimpleWebAuthnBrowser;\n    /**\n     * Fetch webauthn options from the server\n     *\n     * @template {WebAuthnOptionsAction} T\n     * @param {T | undefined} action action to fetch options for\n     * @returns {Promise<WebAuthnOptionsReturn<T> | undefined>}\n     */\n    async function fetchOptions(action) {\n        // Create the options URL with the action and query parameters\n        const url = new URL(`${authURL}/webauthn-options/${providerID}`);\n        if (action)\n            url.searchParams.append(\"action\", action);\n        const formFields = getFormFields();\n        formFields.forEach((field) => {\n            url.searchParams.append(field.name, field.value);\n        });\n        const res = await fetch(url);\n        if (!res.ok) {\n            console.error(\"Failed to fetch options\", res);\n            return;\n        }\n        return res.json();\n    }\n    /**\n     * Get the webauthn form from the page\n     *\n     * @returns {HTMLFormElement}\n     */\n    function getForm() {\n        const formID = `#${providerID}-form`;\n        /** @type {HTMLFormElement | null} */\n        const form = document.querySelector(formID);\n        if (!form)\n            throw new Error(`Form '${formID}' not found`);\n        return form;\n    }\n    /**\n     * Get formFields from the form\n     *\n     * @returns {HTMLInputElement[]}\n     */\n    function getFormFields() {\n        const form = getForm();\n        /** @type {HTMLInputElement[]} */\n        const formFields = Array.from(form.querySelectorAll(\"input[data-form-field]\"));\n        return formFields;\n    }\n    /**\n     * Passkey form submission handler.\n     * Takes the input from the form and a few other parameters and submits it to the server.\n     *\n     * @param {WebAuthnOptionsAction} action action to submit\n     * @param {unknown | undefined} data optional data to submit\n     * @returns {Promise<void>}\n     */\n    async function submitForm(action, data) {\n        const form = getForm();\n        // If a POST request, create hidden fields in the form\n        // and submit it so the browser redirects on login\n        if (action) {\n            const actionInput = document.createElement(\"input\");\n            actionInput.type = \"hidden\";\n            actionInput.name = \"action\";\n            actionInput.value = action;\n            form.appendChild(actionInput);\n        }\n        if (data) {\n            const dataInput = document.createElement(\"input\");\n            dataInput.type = \"hidden\";\n            dataInput.name = \"data\";\n            dataInput.value = JSON.stringify(data);\n            form.appendChild(dataInput);\n        }\n        return form.submit();\n    }\n    /**\n     * Executes the authentication flow by fetching options from the server,\n     * starting the authentication, and submitting the response to the server.\n     *\n     * @param {WebAuthnOptionsReturn<WebAuthnAuthenticate>['options']} options\n     * @param {boolean} autofill Whether or not to use the browser's autofill\n     * @returns {Promise<void>}\n     */\n    async function authenticationFlow(options, autofill) {\n        // Start authentication\n        const authResp = await WebAuthnBrowser.startAuthentication(options, autofill);\n        // Submit authentication response to server\n        return await submitForm(\"authenticate\", authResp);\n    }\n    /**\n     * @param {WebAuthnOptionsReturn<WebAuthnRegister>['options']} options\n     */\n    async function registrationFlow(options) {\n        // Check if all required formFields are set\n        const formFields = getFormFields();\n        formFields.forEach((field) => {\n            if (field.required && !field.value) {\n                throw new Error(`Missing required field: ${field.name}`);\n            }\n        });\n        // Start registration\n        const regResp = await WebAuthnBrowser.startRegistration(options);\n        // Submit registration response to server\n        return await submitForm(\"register\", regResp);\n    }\n    /**\n     * Attempts to authenticate the user when the page loads\n     * using the browser's autofill popup.\n     *\n     * @returns {Promise<void>}\n     */\n    async function autofillAuthentication() {\n        // if the browser can't handle autofill, don't try\n        if (!WebAuthnBrowser.browserSupportsWebAuthnAutofill())\n            return;\n        const res = await fetchOptions(\"authenticate\");\n        if (!res) {\n            console.error(\"Failed to fetch option for autofill authentication\");\n            return;\n        }\n        try {\n            await authenticationFlow(res.options, true);\n        }\n        catch (e) {\n            console.error(e);\n        }\n    }\n    /**\n     * Sets up the passkey form by overriding the form submission handler\n     * so that it attempts to authenticate the user when the form is submitted.\n     * If the user is not registered, it will attempt to register them instead.\n     */\n    async function setupForm() {\n        const form = getForm();\n        // If the browser can't do WebAuthn, hide the form\n        if (!WebAuthnBrowser.browserSupportsWebAuthn()) {\n            form.style.display = \"none\";\n            return;\n        }\n        if (form) {\n            form.addEventListener(\"submit\", async (e) => {\n                e.preventDefault();\n                // Fetch options from the server without assuming that\n                // the user is registered\n                const res = await fetchOptions(undefined);\n                if (!res) {\n                    console.error(\"Failed to fetch options for form submission\");\n                    return;\n                }\n                // Then execute the appropriate flow\n                if (res.action === \"authenticate\") {\n                    try {\n                        await authenticationFlow(res.options, false);\n                    }\n                    catch (e) {\n                        console.error(e);\n                    }\n                }\n                else if (res.action === \"register\") {\n                    try {\n                        await registrationFlow(res.options);\n                    }\n                    catch (e) {\n                        console.error(e);\n                    }\n                }\n            });\n        }\n    }\n    // On page load, setup the form and attempt to authenticate the user.\n    setupForm();\n    autofillAuthentication();\n}\n"], "names": [], "mappings": "AAAA,WAAW;AACX,+DAA+D;AAC/D,mDAAmD,GACnD,2CAA2C,GAC3C,6EAA6E,GAC7E;;;;;;;;CAQC,GACD;;;;;CAKC;;;AACM,eAAe,eAAe,OAAO,EAAE,UAAU;IACpD,qDAAqD,GACrD,aAAa;IACb,MAAM,kBAAkB,OAAO,qBAAqB;IACpD;;;;;;KAMC,GACD,eAAe,aAAa,MAAM;QAC9B,8DAA8D;QAC9D,MAAM,MAAM,IAAI,IAAI,GAAG,QAAQ,kBAAkB,EAAE,YAAY;QAC/D,IAAI,QACA,IAAI,YAAY,CAAC,MAAM,CAAC,UAAU;QACtC,MAAM,aAAa;QACnB,WAAW,OAAO,CAAC,CAAC;YAChB,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,MAAM,KAAK;QACnD;QACA,MAAM,MAAM,MAAM,MAAM;QACxB,IAAI,CAAC,IAAI,EAAE,EAAE;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC;QACJ;QACA,OAAO,IAAI,IAAI;IACnB;IACA;;;;KAIC,GACD,SAAS;QACL,MAAM,SAAS,CAAC,CAAC,EAAE,WAAW,KAAK,CAAC;QACpC,mCAAmC,GACnC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,IAAI,CAAC,MACD,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,OAAO,WAAW,CAAC;QAChD,OAAO;IACX;IACA;;;;KAIC,GACD,SAAS;QACL,MAAM,OAAO;QACb,+BAA+B,GAC/B,MAAM,aAAa,MAAM,IAAI,CAAC,KAAK,gBAAgB,CAAC;QACpD,OAAO;IACX;IACA;;;;;;;KAOC,GACD,eAAe,WAAW,MAAM,EAAE,IAAI;QAClC,MAAM,OAAO;QACb,sDAAsD;QACtD,kDAAkD;QAClD,IAAI,QAAQ;YACR,MAAM,cAAc,SAAS,aAAa,CAAC;YAC3C,YAAY,IAAI,GAAG;YACnB,YAAY,IAAI,GAAG;YACnB,YAAY,KAAK,GAAG;YACpB,KAAK,WAAW,CAAC;QACrB;QACA,IAAI,MAAM;YACN,MAAM,YAAY,SAAS,aAAa,CAAC;YACzC,UAAU,IAAI,GAAG;YACjB,UAAU,IAAI,GAAG;YACjB,UAAU,KAAK,GAAG,KAAK,SAAS,CAAC;YACjC,KAAK,WAAW,CAAC;QACrB;QACA,OAAO,KAAK,MAAM;IACtB;IACA;;;;;;;KAOC,GACD,eAAe,mBAAmB,OAAO,EAAE,QAAQ;QAC/C,uBAAuB;QACvB,MAAM,WAAW,MAAM,gBAAgB,mBAAmB,CAAC,SAAS;QACpE,2CAA2C;QAC3C,OAAO,MAAM,WAAW,gBAAgB;IAC5C;IACA;;KAEC,GACD,eAAe,iBAAiB,OAAO;QACnC,2CAA2C;QAC3C,MAAM,aAAa;QACnB,WAAW,OAAO,CAAC,CAAC;YAChB,IAAI,MAAM,QAAQ,IAAI,CAAC,MAAM,KAAK,EAAE;gBAChC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM,IAAI,EAAE;YAC3D;QACJ;QACA,qBAAqB;QACrB,MAAM,UAAU,MAAM,gBAAgB,iBAAiB,CAAC;QACxD,yCAAyC;QACzC,OAAO,MAAM,WAAW,YAAY;IACxC;IACA;;;;;KAKC,GACD,eAAe;QACX,kDAAkD;QAClD,IAAI,CAAC,gBAAgB,+BAA+B,IAChD;QACJ,MAAM,MAAM,MAAM,aAAa;QAC/B,IAAI,CAAC,KAAK;YACN,QAAQ,KAAK,CAAC;YACd;QACJ;QACA,IAAI;YACA,MAAM,mBAAmB,IAAI,OAAO,EAAE;QAC1C,EACA,OAAO,GAAG;YACN,QAAQ,KAAK,CAAC;QAClB;IACJ;IACA;;;;KAIC,GACD,eAAe;QACX,MAAM,OAAO;QACb,kDAAkD;QAClD,IAAI,CAAC,gBAAgB,uBAAuB,IAAI;YAC5C,KAAK,KAAK,CAAC,OAAO,GAAG;YACrB;QACJ;QACA,IAAI,MAAM;YACN,KAAK,gBAAgB,CAAC,UAAU,OAAO;gBACnC,EAAE,cAAc;gBAChB,sDAAsD;gBACtD,yBAAyB;gBACzB,MAAM,MAAM,MAAM,aAAa;gBAC/B,IAAI,CAAC,KAAK;oBACN,QAAQ,KAAK,CAAC;oBACd;gBACJ;gBACA,oCAAoC;gBACpC,IAAI,IAAI,MAAM,KAAK,gBAAgB;oBAC/B,IAAI;wBACA,MAAM,mBAAmB,IAAI,OAAO,EAAE;oBAC1C,EACA,OAAO,GAAG;wBACN,QAAQ,KAAK,CAAC;oBAClB;gBACJ,OACK,IAAI,IAAI,MAAM,KAAK,YAAY;oBAChC,IAAI;wBACA,MAAM,iBAAiB,IAAI,OAAO;oBACtC,EACA,OAAO,GAAG;wBACN,QAAQ,KAAK,CAAC;oBAClB;gBACJ;YACJ;QACJ;IACJ;IACA,qEAAqE;IACrE;IACA;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/pages/signin.js"], "sourcesContent": ["import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"preact/jsx-runtime\";\nimport { webauthnScript } from \"../utils/webauthn-client.js\";\nconst signinErrors = {\n    default: \"Unable to sign in.\",\n    Signin: \"Try signing in with a different account.\",\n    OAuthSignin: \"Try signing in with a different account.\",\n    OAuthCallbackError: \"Try signing in with a different account.\",\n    OAuthCreateAccount: \"Try signing in with a different account.\",\n    EmailCreateAccount: \"Try signing in with a different account.\",\n    Callback: \"Try signing in with a different account.\",\n    OAuthAccountNotLinked: \"To confirm your identity, sign in with the same account you used originally.\",\n    EmailSignin: \"The e-mail could not be sent.\",\n    CredentialsSignin: \"Sign in failed. Check the details you provided are correct.\",\n    SessionRequired: \"Please sign in to access this page.\",\n};\nfunction ConditionalUIScript(providerID) {\n    const startConditionalUIScript = `\nconst currentURL = window.location.href;\nconst authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));\n(${webauthnScript})(authURL, \"${providerID}\");\n`;\n    return (_jsx(_Fragment, { children: _jsx(\"script\", { dangerouslySetInnerHTML: { __html: startConditionalUIScript } }) }));\n}\nexport default function SigninPage(props) {\n    const { csrfToken, providers = [], callbackUrl, theme, email, error: errorType, } = props;\n    if (typeof document !== \"undefined\" && theme?.brandColor) {\n        document.documentElement.style.setProperty(\"--brand-color\", theme.brandColor);\n    }\n    if (typeof document !== \"undefined\" && theme?.buttonText) {\n        document.documentElement.style.setProperty(\"--button-text-color\", theme.buttonText);\n    }\n    const error = errorType && (signinErrors[errorType] ?? signinErrors.default);\n    const providerLogoPath = \"https://authjs.dev/img/providers\";\n    const conditionalUIProviderID = providers.find((provider) => provider.type === \"webauthn\" && provider.enableConditionalUI)?.id;\n    return (_jsxs(\"div\", { className: \"signin\", children: [theme?.brandColor && (_jsx(\"style\", { dangerouslySetInnerHTML: {\n                    __html: `:root {--brand-color: ${theme.brandColor}}`,\n                } })), theme?.buttonText && (_jsx(\"style\", { dangerouslySetInnerHTML: {\n                    __html: `\n        :root {\n          --button-text-color: ${theme.buttonText}\n        }\n      `,\n                } })), _jsxs(\"div\", { className: \"card\", children: [error && (_jsx(\"div\", { className: \"error\", children: _jsx(\"p\", { children: error }) })), theme?.logo && _jsx(\"img\", { src: theme.logo, alt: \"Logo\", className: \"logo\" }), providers.map((provider, i) => {\n                        let bg, brandColor, logo;\n                        if (provider.type === \"oauth\" || provider.type === \"oidc\") {\n                            ;\n                            ({\n                                bg = \"#fff\",\n                                brandColor,\n                                logo = `${providerLogoPath}/${provider.id}.svg`,\n                            } = provider.style ?? {});\n                        }\n                        const color = brandColor ?? bg ?? \"#fff\";\n                        return (_jsxs(\"div\", { className: \"provider\", children: [provider.type === \"oauth\" || provider.type === \"oidc\" ? (_jsxs(\"form\", { action: provider.signinUrl, method: \"POST\", children: [_jsx(\"input\", { type: \"hidden\", name: \"csrfToken\", value: csrfToken }), callbackUrl && (_jsx(\"input\", { type: \"hidden\", name: \"callbackUrl\", value: callbackUrl })), _jsxs(\"button\", { type: \"submit\", className: \"button\", style: {\n                                                \"--provider-brand-color\": color,\n                                            }, tabIndex: 0, children: [_jsxs(\"span\", { style: {\n                                                        filter: \"invert(1) grayscale(1) brightness(1.3) contrast(9000)\",\n                                                        \"mix-blend-mode\": \"luminosity\",\n                                                        opacity: 0.95,\n                                                    }, children: [\"Sign in with \", provider.name] }), logo && _jsx(\"img\", { loading: \"lazy\", height: 24, src: logo })] })] })) : null, (provider.type === \"email\" ||\n                                    provider.type === \"credentials\" ||\n                                    provider.type === \"webauthn\") &&\n                                    i > 0 &&\n                                    providers[i - 1].type !== \"email\" &&\n                                    providers[i - 1].type !== \"credentials\" &&\n                                    providers[i - 1].type !== \"webauthn\" && _jsx(\"hr\", {}), provider.type === \"email\" && (_jsxs(\"form\", { action: provider.signinUrl, method: \"POST\", children: [_jsx(\"input\", { type: \"hidden\", name: \"csrfToken\", value: csrfToken }), _jsx(\"label\", { className: \"section-header\", htmlFor: `input-email-for-${provider.id}-provider`, children: \"Email\" }), _jsx(\"input\", { id: `input-email-for-${provider.id}-provider`, autoFocus: true, type: \"email\", name: \"email\", value: email, placeholder: \"<EMAIL>\", required: true }), _jsxs(\"button\", { id: \"submitButton\", type: \"submit\", tabIndex: 0, children: [\"Sign in with \", provider.name] })] })), provider.type === \"credentials\" && (_jsxs(\"form\", { action: provider.callbackUrl, method: \"POST\", children: [_jsx(\"input\", { type: \"hidden\", name: \"csrfToken\", value: csrfToken }), Object.keys(provider.credentials).map((credential) => {\n                                            return (_jsxs(\"div\", { children: [_jsx(\"label\", { className: \"section-header\", htmlFor: `input-${credential}-for-${provider.id}-provider`, children: provider.credentials[credential].label ?? credential }), _jsx(\"input\", { name: credential, id: `input-${credential}-for-${provider.id}-provider`, type: provider.credentials[credential].type ?? \"text\", placeholder: provider.credentials[credential].placeholder ?? \"\", ...provider.credentials[credential] })] }, `input-group-${provider.id}`));\n                                        }), _jsxs(\"button\", { id: \"submitButton\", type: \"submit\", tabIndex: 0, children: [\"Sign in with \", provider.name] })] })), provider.type === \"webauthn\" && (_jsxs(\"form\", { action: provider.callbackUrl, method: \"POST\", id: `${provider.id}-form`, children: [_jsx(\"input\", { type: \"hidden\", name: \"csrfToken\", value: csrfToken }), Object.keys(provider.formFields).map((field) => {\n                                            return (_jsxs(\"div\", { children: [_jsx(\"label\", { className: \"section-header\", htmlFor: `input-${field}-for-${provider.id}-provider`, children: provider.formFields[field].label ?? field }), _jsx(\"input\", { name: field, \"data-form-field\": true, id: `input-${field}-for-${provider.id}-provider`, type: provider.formFields[field].type ?? \"text\", placeholder: provider.formFields[field].placeholder ?? \"\", ...provider.formFields[field] })] }, `input-group-${provider.id}`));\n                                        }), _jsxs(\"button\", { id: `submitButton-${provider.id}`, type: \"submit\", tabIndex: 0, children: [\"Sign in with \", provider.name] })] })), (provider.type === \"email\" ||\n                                    provider.type === \"credentials\" ||\n                                    provider.type === \"webauthn\") &&\n                                    i + 1 < providers.length && _jsx(\"hr\", {})] }, provider.id));\n                    })] }), conditionalUIProviderID && ConditionalUIScript(conditionalUIProviderID)] }));\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA;;;AACA,MAAM,eAAe;IACjB,SAAS;IACT,QAAQ;IACR,aAAa;IACb,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,UAAU;IACV,uBAAuB;IACvB,aAAa;IACb,mBAAmB;IACnB,iBAAiB;AACrB;AACA,SAAS,oBAAoB,UAAU;IACnC,MAAM,2BAA2B,CAAC;;;CAGrC,EAAE,wOAAA,CAAA,iBAAc,CAAC,YAAY,EAAE,WAAW;AAC3C,CAAC;IACG,OAAQ,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,+MAAA,CAAA,WAAS,EAAE;QAAE,UAAU,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,UAAU;YAAE,yBAAyB;gBAAE,QAAQ;YAAyB;QAAE;IAAG;AAC1H;AACe,SAAS,WAAW,KAAK;IACpC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,SAAS,EAAG,GAAG;IACpF,IAAI,OAAO,aAAa,eAAe,OAAO,YAAY;QACtD,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,iBAAiB,MAAM,UAAU;IAChF;IACA,IAAI,OAAO,aAAa,eAAe,OAAO,YAAY;QACtD,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,uBAAuB,MAAM,UAAU;IACtF;IACA,MAAM,QAAQ,aAAa,CAAC,YAAY,CAAC,UAAU,IAAI,aAAa,OAAO;IAC3E,MAAM,mBAAmB;IACzB,MAAM,0BAA0B,UAAU,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI,KAAK,cAAc,SAAS,mBAAmB,GAAG;IAC5H,OAAQ,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,OAAO;QAAE,WAAW;QAAU,UAAU;YAAC,OAAO,cAAe,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;gBAAE,yBAAyB;oBACtG,QAAQ,CAAC,sBAAsB,EAAE,MAAM,UAAU,CAAC,CAAC,CAAC;gBACxD;YAAE;YAAK,OAAO,cAAe,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;gBAAE,yBAAyB;oBAClE,QAAQ,CAAC;;+BAEE,EAAE,MAAM,UAAU,CAAC;;MAE5C,CAAC;gBACS;YAAE;YAAK,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,OAAO;gBAAE,WAAW;gBAAQ,UAAU;oBAAC,SAAU,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,OAAO;wBAAE,WAAW;wBAAS,UAAU,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,KAAK;4BAAE,UAAU;wBAAM;oBAAG;oBAAK,OAAO,QAAQ,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,OAAO;wBAAE,KAAK,MAAM,IAAI;wBAAE,KAAK;wBAAQ,WAAW;oBAAO;oBAAI,UAAU,GAAG,CAAC,CAAC,UAAU;wBAChP,IAAI,IAAI,YAAY;wBACpB,IAAI,SAAS,IAAI,KAAK,WAAW,SAAS,IAAI,KAAK,QAAQ;;4BAEvD,CAAC,EACG,KAAK,MAAM,EACX,UAAU,EACV,OAAO,GAAG,iBAAiB,CAAC,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC,EAClD,GAAG,SAAS,KAAK,IAAI,CAAC,CAAC;wBAC5B;wBACA,MAAM,QAAQ,cAAc,MAAM;wBAClC,OAAQ,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,OAAO;4BAAE,WAAW;4BAAY,UAAU;gCAAC,SAAS,IAAI,KAAK,WAAW,SAAS,IAAI,KAAK,SAAU,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,QAAQ;oCAAE,QAAQ,SAAS,SAAS;oCAAE,QAAQ;oCAAQ,UAAU;wCAAC,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;4CAAE,MAAM;4CAAU,MAAM;4CAAa,OAAO;wCAAU;wCAAI,eAAgB,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;4CAAE,MAAM;4CAAU,MAAM;4CAAe,OAAO;wCAAY;wCAAK,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,UAAU;4CAAE,MAAM;4CAAU,WAAW;4CAAU,OAAO;gDACpY,0BAA0B;4CAC9B;4CAAG,UAAU;4CAAG,UAAU;gDAAC,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,QAAQ;oDAAE,OAAO;wDACtC,QAAQ;wDACR,kBAAkB;wDAClB,SAAS;oDACb;oDAAG,UAAU;wDAAC;wDAAiB,SAAS,IAAI;qDAAC;gDAAC;gDAAI,QAAQ,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,OAAO;oDAAE,SAAS;oDAAQ,QAAQ;oDAAI,KAAK;gDAAK;6CAAG;wCAAC;qCAAG;gCAAC,KAAM;gCAAM,CAAC,SAAS,IAAI,KAAK,WACtK,SAAS,IAAI,KAAK,iBAClB,SAAS,IAAI,KAAK,UAAU,KAC5B,IAAI,KACJ,SAAS,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,WAC1B,SAAS,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,iBAC1B,SAAS,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,cAAc,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,MAAM,CAAC;gCAAI,SAAS,IAAI,KAAK,WAAY,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,QAAQ;oCAAE,QAAQ,SAAS,SAAS;oCAAE,QAAQ;oCAAQ,UAAU;wCAAC,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;4CAAE,MAAM;4CAAU,MAAM;4CAAa,OAAO;wCAAU;wCAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;4CAAE,WAAW;4CAAkB,SAAS,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC;4CAAE,UAAU;wCAAQ;wCAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;4CAAE,IAAI,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC;4CAAE,WAAW;4CAAM,MAAM;4CAAS,MAAM;4CAAS,OAAO;4CAAO,aAAa;4CAAqB,UAAU;wCAAK;wCAAI,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,UAAU;4CAAE,IAAI;4CAAgB,MAAM;4CAAU,UAAU;4CAAG,UAAU;gDAAC;gDAAiB,SAAS,IAAI;6CAAC;wCAAC;qCAAG;gCAAC;gCAAK,SAAS,IAAI,KAAK,iBAAkB,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,QAAQ;oCAAE,QAAQ,SAAS,WAAW;oCAAE,QAAQ;oCAAQ,UAAU;wCAAC,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;4CAAE,MAAM;4CAAU,MAAM;4CAAa,OAAO;wCAAU;wCAAI,OAAO,IAAI,CAAC,SAAS,WAAW,EAAE,GAAG,CAAC,CAAC;4CACx1B,OAAQ,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,OAAO;gDAAE,UAAU;oDAAC,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;wDAAE,WAAW;wDAAkB,SAAS,CAAC,MAAM,EAAE,WAAW,KAAK,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC;wDAAE,UAAU,SAAS,WAAW,CAAC,WAAW,CAAC,KAAK,IAAI;oDAAW;oDAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;wDAAE,MAAM;wDAAY,IAAI,CAAC,MAAM,EAAE,WAAW,KAAK,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC;wDAAE,MAAM,SAAS,WAAW,CAAC,WAAW,CAAC,IAAI,IAAI;wDAAQ,aAAa,SAAS,WAAW,CAAC,WAAW,CAAC,WAAW,IAAI;wDAAI,GAAG,SAAS,WAAW,CAAC,WAAW;oDAAC;iDAAG;4CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,EAAE,EAAE;wCAC1e;wCAAI,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,UAAU;4CAAE,IAAI;4CAAgB,MAAM;4CAAU,UAAU;4CAAG,UAAU;gDAAC;gDAAiB,SAAS,IAAI;6CAAC;wCAAC;qCAAG;gCAAC;gCAAK,SAAS,IAAI,KAAK,cAAe,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,QAAQ;oCAAE,QAAQ,SAAS,WAAW;oCAAE,QAAQ;oCAAQ,IAAI,GAAG,SAAS,EAAE,CAAC,KAAK,CAAC;oCAAE,UAAU;wCAAC,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;4CAAE,MAAM;4CAAU,MAAM;4CAAa,OAAO;wCAAU;wCAAI,OAAO,IAAI,CAAC,SAAS,UAAU,EAAE,GAAG,CAAC,CAAC;4CAC1W,OAAQ,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,OAAO;gDAAE,UAAU;oDAAC,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;wDAAE,WAAW;wDAAkB,SAAS,CAAC,MAAM,EAAE,MAAM,KAAK,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC;wDAAE,UAAU,SAAS,UAAU,CAAC,MAAM,CAAC,KAAK,IAAI;oDAAM;oDAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;wDAAE,MAAM;wDAAO,mBAAmB;wDAAM,IAAI,CAAC,MAAM,EAAE,MAAM,KAAK,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC;wDAAE,MAAM,SAAS,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI;wDAAQ,aAAa,SAAS,UAAU,CAAC,MAAM,CAAC,WAAW,IAAI;wDAAI,GAAG,SAAS,UAAU,CAAC,MAAM;oDAAC;iDAAG;4CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,EAAE,EAAE;wCACvd;wCAAI,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,UAAU;4CAAE,IAAI,CAAC,aAAa,EAAE,SAAS,EAAE,EAAE;4CAAE,MAAM;4CAAU,UAAU;4CAAG,UAAU;gDAAC;gDAAiB,SAAS,IAAI;6CAAC;wCAAC;qCAAG;gCAAC;gCAAK,CAAC,SAAS,IAAI,KAAK,WACjK,SAAS,IAAI,KAAK,iBAClB,SAAS,IAAI,KAAK,UAAU,KAC5B,IAAI,IAAI,UAAU,MAAM,IAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,MAAM,CAAC;6BAAG;wBAAC,GAAG,SAAS,EAAE;oBAC1E;iBAAG;YAAC;YAAI,2BAA2B,oBAAoB;SAAyB;IAAC;AACrG", "ignoreList": [0]}}, {"offset": {"line": 2158, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/pages/signout.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"preact/jsx-runtime\";\nexport default function SignoutPage(props) {\n    const { url, csrfToken, theme } = props;\n    return (_jsxs(\"div\", { className: \"signout\", children: [theme?.brandColor && (_jsx(\"style\", { dangerouslySetInnerHTML: {\n                    __html: `\n        :root {\n          --brand-color: ${theme.brandColor}\n        }\n      `,\n                } })), theme?.buttonText && (_jsx(\"style\", { dangerouslySetInnerHTML: {\n                    __html: `\n        :root {\n          --button-text-color: ${theme.buttonText}\n        }\n      `,\n                } })), _jsxs(\"div\", { className: \"card\", children: [theme?.logo && _jsx(\"img\", { src: theme.logo, alt: \"Logo\", className: \"logo\" }), _jsx(\"h1\", { children: \"Signout\" }), _jsx(\"p\", { children: \"Are you sure you want to sign out?\" }), _jsxs(\"form\", { action: url?.toString(), method: \"POST\", children: [_jsx(\"input\", { type: \"hidden\", name: \"csrfToken\", value: csrfToken }), _jsx(\"button\", { id: \"submitButton\", type: \"submit\", children: \"Sign out\" })] })] })] }));\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AACe,SAAS,YAAY,KAAK;IACrC,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;IAClC,OAAQ,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,OAAO;QAAE,WAAW;QAAW,UAAU;YAAC,OAAO,cAAe,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;gBAAE,yBAAyB;oBACvG,QAAQ,CAAC;;yBAEJ,EAAE,MAAM,UAAU,CAAC;;MAEtC,CAAC;gBACS;YAAE;YAAK,OAAO,cAAe,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;gBAAE,yBAAyB;oBAClE,QAAQ,CAAC;;+BAEE,EAAE,MAAM,UAAU,CAAC;;MAE5C,CAAC;gBACS;YAAE;YAAK,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,OAAO;gBAAE,WAAW;gBAAQ,UAAU;oBAAC,OAAO,QAAQ,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,OAAO;wBAAE,KAAK,MAAM,IAAI;wBAAE,KAAK;wBAAQ,WAAW;oBAAO;oBAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,MAAM;wBAAE,UAAU;oBAAU;oBAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wBAAE,UAAU;oBAAqC;oBAAI,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,QAAQ;wBAAE,QAAQ,KAAK;wBAAY,QAAQ;wBAAQ,UAAU;4BAAC,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;gCAAE,MAAM;gCAAU,MAAM;gCAAa,OAAO;4BAAU;4BAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,UAAU;gCAAE,IAAI;gCAAgB,MAAM;gCAAU,UAAU;4BAAW;yBAAG;oBAAC;iBAAG;YAAC;SAAG;IAAC;AAC3d", "ignoreList": [0]}}, {"offset": {"line": 2228, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/pages/styles.js"], "sourcesContent": ["// Generated by `pnpm css`\nexport default `:root {\n  --border-width: 1px;\n  --border-radius: 0.5rem;\n  --color-error: #c94b4b;\n  --color-info: #157efb;\n  --color-info-hover: #0f6ddb;\n  --color-info-text: #fff;\n}\n\n.__next-auth-theme-auto,\n.__next-auth-theme-light {\n  --color-background: #ececec;\n  --color-background-hover: rgba(236, 236, 236, 0.8);\n  --color-background-card: #fff;\n  --color-text: #000;\n  --color-primary: #444;\n  --color-control-border: #bbb;\n  --color-button-active-background: #f9f9f9;\n  --color-button-active-border: #aaa;\n  --color-separator: #ccc;\n  --provider-bg: #fff;\n  --provider-bg-hover: color-mix(\n    in srgb,\n    var(--provider-brand-color) 30%,\n    #fff\n  );\n}\n\n.__next-auth-theme-dark {\n  --color-background: #161b22;\n  --color-background-hover: rgba(22, 27, 34, 0.8);\n  --color-background-card: #0d1117;\n  --color-text: #fff;\n  --color-primary: #ccc;\n  --color-control-border: #555;\n  --color-button-active-background: #060606;\n  --color-button-active-border: #666;\n  --color-separator: #444;\n  --provider-bg: #161b22;\n  --provider-bg-hover: color-mix(\n    in srgb,\n    var(--provider-brand-color) 30%,\n    #000\n  );\n}\n\n.__next-auth-theme-dark img[src$=\"42-school.svg\"],\n  .__next-auth-theme-dark img[src$=\"apple.svg\"],\n  .__next-auth-theme-dark img[src$=\"boxyhq-saml.svg\"],\n  .__next-auth-theme-dark img[src$=\"eveonline.svg\"],\n  .__next-auth-theme-dark img[src$=\"github.svg\"],\n  .__next-auth-theme-dark img[src$=\"mailchimp.svg\"],\n  .__next-auth-theme-dark img[src$=\"medium.svg\"],\n  .__next-auth-theme-dark img[src$=\"okta.svg\"],\n  .__next-auth-theme-dark img[src$=\"patreon.svg\"],\n  .__next-auth-theme-dark img[src$=\"ping-id.svg\"],\n  .__next-auth-theme-dark img[src$=\"roblox.svg\"],\n  .__next-auth-theme-dark img[src$=\"threads.svg\"],\n  .__next-auth-theme-dark img[src$=\"wikimedia.svg\"] {\n    filter: invert(1);\n  }\n\n.__next-auth-theme-dark #submitButton {\n    background-color: var(--provider-bg, var(--color-info));\n  }\n\n@media (prefers-color-scheme: dark) {\n  .__next-auth-theme-auto {\n    --color-background: #161b22;\n    --color-background-hover: rgba(22, 27, 34, 0.8);\n    --color-background-card: #0d1117;\n    --color-text: #fff;\n    --color-primary: #ccc;\n    --color-control-border: #555;\n    --color-button-active-background: #060606;\n    --color-button-active-border: #666;\n    --color-separator: #444;\n    --provider-bg: #161b22;\n    --provider-bg-hover: color-mix(\n      in srgb,\n      var(--provider-brand-color) 30%,\n      #000\n    );\n  }\n    .__next-auth-theme-auto img[src$=\"42-school.svg\"],\n    .__next-auth-theme-auto img[src$=\"apple.svg\"],\n    .__next-auth-theme-auto img[src$=\"boxyhq-saml.svg\"],\n    .__next-auth-theme-auto img[src$=\"eveonline.svg\"],\n    .__next-auth-theme-auto img[src$=\"github.svg\"],\n    .__next-auth-theme-auto img[src$=\"mailchimp.svg\"],\n    .__next-auth-theme-auto img[src$=\"medium.svg\"],\n    .__next-auth-theme-auto img[src$=\"okta.svg\"],\n    .__next-auth-theme-auto img[src$=\"patreon.svg\"],\n    .__next-auth-theme-auto img[src$=\"ping-id.svg\"],\n    .__next-auth-theme-auto img[src$=\"roblox.svg\"],\n    .__next-auth-theme-auto img[src$=\"threads.svg\"],\n    .__next-auth-theme-auto img[src$=\"wikimedia.svg\"] {\n      filter: invert(1);\n    }\n    .__next-auth-theme-auto #submitButton {\n      background-color: var(--provider-bg, var(--color-info));\n    }\n}\n\nhtml {\n  box-sizing: border-box;\n}\n\n*,\n*:before,\n*:after {\n  box-sizing: inherit;\n  margin: 0;\n  padding: 0;\n}\n\nbody {\n  background-color: var(--color-background);\n  margin: 0;\n  padding: 0;\n  font-family:\n    ui-sans-serif,\n    system-ui,\n    -apple-system,\n    BlinkMacSystemFont,\n    \"Segoe UI\",\n    Roboto,\n    \"Helvetica Neue\",\n    Arial,\n    \"Noto Sans\",\n    sans-serif,\n    \"Apple Color Emoji\",\n    \"Segoe UI Emoji\",\n    \"Segoe UI Symbol\",\n    \"Noto Color Emoji\";\n}\n\nh1 {\n  margin-bottom: 1.5rem;\n  padding: 0 1rem;\n  font-weight: 400;\n  color: var(--color-text);\n}\n\np {\n  margin-bottom: 1.5rem;\n  padding: 0 1rem;\n  color: var(--color-text);\n}\n\nform {\n  margin: 0;\n  padding: 0;\n}\n\nlabel {\n  font-weight: 500;\n  text-align: left;\n  margin-bottom: 0.25rem;\n  display: block;\n  color: var(--color-text);\n}\n\ninput[type] {\n  box-sizing: border-box;\n  display: block;\n  width: 100%;\n  padding: 0.5rem 1rem;\n  border: var(--border-width) solid var(--color-control-border);\n  background: var(--color-background-card);\n  font-size: 1rem;\n  border-radius: var(--border-radius);\n  color: var(--color-text);\n}\n\np {\n  font-size: 1.1rem;\n  line-height: 2rem;\n}\n\na.button {\n  text-decoration: none;\n  line-height: 1rem;\n}\n\na.button:link,\n  a.button:visited {\n    background-color: var(--color-background);\n    color: var(--color-primary);\n  }\n\nbutton,\na.button {\n  padding: 0.75rem 1rem;\n  color: var(--provider-color, var(--color-primary));\n  background-color: var(--provider-bg, var(--color-background));\n  border: 1px solid #00000031;\n  font-size: 0.9rem;\n  height: 50px;\n  border-radius: var(--border-radius);\n  transition: background-color 250ms ease-in-out;\n  font-weight: 300;\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n:is(button,a.button):hover {\n    background-color: var(--provider-bg-hover, var(--color-background-hover));\n    cursor: pointer;\n  }\n\n:is(button,a.button):active {\n    cursor: pointer;\n  }\n\n:is(button,a.button) span {\n    color: var(--provider-bg);\n  }\n\n#submitButton {\n  color: var(--button-text-color, var(--color-info-text));\n  background-color: var(--brand-color, var(--color-info));\n  width: 100%;\n}\n\n#submitButton:hover {\n    background-color: var(\n      --button-hover-bg,\n      var(--color-info-hover)\n    ) !important;\n  }\n\na.site {\n  color: var(--color-primary);\n  text-decoration: none;\n  font-size: 1rem;\n  line-height: 2rem;\n}\n\na.site:hover {\n    text-decoration: underline;\n  }\n\n.page {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  display: grid;\n  place-items: center;\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n.page > div {\n    text-align: center;\n  }\n\n.error a.button {\n    padding-left: 2rem;\n    padding-right: 2rem;\n    margin-top: 0.5rem;\n  }\n\n.error .message {\n    margin-bottom: 1.5rem;\n  }\n\n.signin input[type=\"text\"] {\n    margin-left: auto;\n    margin-right: auto;\n    display: block;\n  }\n\n.signin hr {\n    display: block;\n    border: 0;\n    border-top: 1px solid var(--color-separator);\n    margin: 2rem auto 1rem auto;\n    overflow: visible;\n  }\n\n.signin hr::before {\n      content: \"or\";\n      background: var(--color-background-card);\n      color: #888;\n      padding: 0 0.4rem;\n      position: relative;\n      top: -0.7rem;\n    }\n\n.signin .error {\n    background: #f5f5f5;\n    font-weight: 500;\n    border-radius: 0.3rem;\n    background: var(--color-error);\n  }\n\n.signin .error p {\n      text-align: left;\n      padding: 0.5rem 1rem;\n      font-size: 0.9rem;\n      line-height: 1.2rem;\n      color: var(--color-info-text);\n    }\n\n.signin > div,\n  .signin form {\n    display: block;\n  }\n\n.signin > div input[type], .signin form input[type] {\n      margin-bottom: 0.5rem;\n    }\n\n.signin > div button, .signin form button {\n      width: 100%;\n    }\n\n.signin .provider + .provider {\n    margin-top: 1rem;\n  }\n\n.logo {\n  display: inline-block;\n  max-width: 150px;\n  margin: 1.25rem 0;\n  max-height: 70px;\n}\n\n.card {\n  background-color: var(--color-background-card);\n  border-radius: 1rem;\n  padding: 1.25rem 2rem;\n}\n\n.card .header {\n    color: var(--color-primary);\n  }\n\n.card input[type]::-moz-placeholder {\n    color: color-mix(\n      in srgb,\n      var(--color-text) 20%,\n      var(--color-button-active-background)\n    );\n  }\n\n.card input[type]::placeholder {\n    color: color-mix(\n      in srgb,\n      var(--color-text) 20%,\n      var(--color-button-active-background)\n    );\n  }\n\n.card input[type] {\n    background: color-mix(in srgb, var(--color-background-card) 95%, black);\n  }\n\n.section-header {\n  color: var(--color-text);\n}\n\n@media screen and (min-width: 450px) {\n  .card {\n    margin: 2rem 0;\n    width: 368px;\n  }\n}\n\n@media screen and (max-width: 450px) {\n  .card {\n    margin: 1rem 0;\n    width: 343px;\n  }\n}\n`;\n"], "names": [], "mappings": "AAAA,0BAA0B;;;;uCACX,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2XhB,CAAC", "ignoreList": [0]}}, {"offset": {"line": 2618, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/pages/verify-request.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"preact/jsx-runtime\";\nexport default function VerifyRequestPage(props) {\n    const { url, theme } = props;\n    return (_jsxs(\"div\", { className: \"verify-request\", children: [theme.brandColor && (_jsx(\"style\", { dangerouslySetInnerHTML: {\n                    __html: `\n        :root {\n          --brand-color: ${theme.brandColor}\n        }\n      `,\n                } })), _jsxs(\"div\", { className: \"card\", children: [theme.logo && _jsx(\"img\", { src: theme.logo, alt: \"Logo\", className: \"logo\" }), _jsx(\"h1\", { children: \"Check your email\" }), _jsx(\"p\", { children: \"A sign in link has been sent to your email address.\" }), _jsx(\"p\", { children: _jsx(\"a\", { className: \"site\", href: url.origin, children: url.host }) })] })] }));\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AACe,SAAS,kBAAkB,KAAK;IAC3C,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG;IACvB,OAAQ,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,OAAO;QAAE,WAAW;QAAkB,UAAU;YAAC,MAAM,UAAU,IAAK,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,SAAS;gBAAE,yBAAyB;oBAC7G,QAAQ,CAAC;;yBAEJ,EAAE,MAAM,UAAU,CAAC;;MAEtC,CAAC;gBACS;YAAE;YAAK,CAAA,GAAA,qPAAA,CAAA,OAAK,AAAD,EAAE,OAAO;gBAAE,WAAW;gBAAQ,UAAU;oBAAC,MAAM,IAAI,IAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,OAAO;wBAAE,KAAK,MAAM,IAAI;wBAAE,KAAK;wBAAQ,WAAW;oBAAO;oBAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,MAAM;wBAAE,UAAU;oBAAmB;oBAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wBAAE,UAAU;oBAAsD;oBAAI,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wBAAE,UAAU,CAAA,GAAA,qPAAA,CAAA,MAAI,AAAD,EAAE,KAAK;4BAAE,WAAW;4BAAQ,MAAM,IAAI,MAAM;4BAAE,UAAU,IAAI,IAAI;wBAAC;oBAAG;iBAAG;YAAC;SAAG;IAAC;AACvX", "ignoreList": [0]}}, {"offset": {"line": 2670, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/pages/index.js"], "sourcesContent": ["import { renderToString } from \"preact-render-to-string\";\nimport ErrorPage from \"./error.js\";\nimport SigninPage from \"./signin.js\";\nimport SignoutPage from \"./signout.js\";\nimport css from \"./styles.js\";\nimport VerifyRequestPage from \"./verify-request.js\";\nimport { UnknownAction } from \"../../errors.js\";\nfunction send({ html, title, status, cookies, theme, headTags, }) {\n    return {\n        cookies,\n        status,\n        headers: { \"Content-Type\": \"text/html\" },\n        body: `<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><style>${css}</style><title>${title}</title>${headTags ?? \"\"}</head><body class=\"__next-auth-theme-${theme?.colorScheme ?? \"auto\"}\"><div class=\"page\">${renderToString(html)}</div></body></html>`,\n    };\n}\n/**\n * Unless the user defines their [own pages](https://authjs.dev/reference/core#pages),\n * we render a set of default ones, using Preact SSR.\n */\nexport default function renderPage(params) {\n    const { url, theme, query, cookies, pages, providers } = params;\n    return {\n        csrf(skip, options, cookies) {\n            if (!skip) {\n                return {\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Cache-Control\": \"private, no-cache, no-store\",\n                        Expires: \"0\",\n                        Pragma: \"no-cache\",\n                    },\n                    body: { csrfToken: options.csrfToken },\n                    cookies,\n                };\n            }\n            options.logger.warn(\"csrf-disabled\");\n            cookies.push({\n                name: options.cookies.csrfToken.name,\n                value: \"\",\n                options: { ...options.cookies.csrfToken.options, maxAge: 0 },\n            });\n            return { status: 404, cookies };\n        },\n        providers(providers) {\n            return {\n                headers: { \"Content-Type\": \"application/json\" },\n                body: providers.reduce((acc, { id, name, type, signinUrl, callbackUrl }) => {\n                    acc[id] = { id, name, type, signinUrl, callbackUrl };\n                    return acc;\n                }, {}),\n            };\n        },\n        signin(providerId, error) {\n            if (providerId)\n                throw new UnknownAction(\"Unsupported action\");\n            if (pages?.signIn) {\n                let signinUrl = `${pages.signIn}${pages.signIn.includes(\"?\") ? \"&\" : \"?\"}${new URLSearchParams({ callbackUrl: params.callbackUrl ?? \"/\" })}`;\n                if (error)\n                    signinUrl = `${signinUrl}&${new URLSearchParams({ error })}`;\n                return { redirect: signinUrl, cookies };\n            }\n            // If we have a webauthn provider with conditional UI and\n            // a simpleWebAuthnBrowserScript is defined, we need to\n            // render the script in the page.\n            const webauthnProvider = providers?.find((p) => p.type === \"webauthn\" &&\n                p.enableConditionalUI &&\n                !!p.simpleWebAuthnBrowserVersion);\n            let simpleWebAuthnBrowserScript = \"\";\n            if (webauthnProvider) {\n                const { simpleWebAuthnBrowserVersion } = webauthnProvider;\n                simpleWebAuthnBrowserScript = `<script src=\"https://unpkg.com/@simplewebauthn/browser@${simpleWebAuthnBrowserVersion}/dist/bundle/index.umd.min.js\" crossorigin=\"anonymous\"></script>`;\n            }\n            return send({\n                cookies,\n                theme,\n                html: SigninPage({\n                    csrfToken: params.csrfToken,\n                    // We only want to render providers\n                    providers: params.providers?.filter((provider) => \n                    // Always render oauth and email type providers\n                    [\"email\", \"oauth\", \"oidc\"].includes(provider.type) ||\n                        // Only render credentials type provider if credentials are defined\n                        (provider.type === \"credentials\" && provider.credentials) ||\n                        // Only render webauthn type provider if formFields are defined\n                        (provider.type === \"webauthn\" && provider.formFields) ||\n                        // Don't render other provider types\n                        false),\n                    callbackUrl: params.callbackUrl,\n                    theme: params.theme,\n                    error,\n                    ...query,\n                }),\n                title: \"Sign In\",\n                headTags: simpleWebAuthnBrowserScript,\n            });\n        },\n        signout() {\n            if (pages?.signOut)\n                return { redirect: pages.signOut, cookies };\n            return send({\n                cookies,\n                theme,\n                html: SignoutPage({ csrfToken: params.csrfToken, url, theme }),\n                title: \"Sign Out\",\n            });\n        },\n        verifyRequest(props) {\n            if (pages?.verifyRequest)\n                return {\n                    redirect: `${pages.verifyRequest}${url?.search ?? \"\"}`,\n                    cookies,\n                };\n            return send({\n                cookies,\n                theme,\n                html: VerifyRequestPage({ url, theme, ...props }),\n                title: \"Verify Request\",\n            });\n        },\n        error(error) {\n            if (pages?.error) {\n                return {\n                    redirect: `${pages.error}${pages.error.includes(\"?\") ? \"&\" : \"?\"}error=${error}`,\n                    cookies,\n                };\n            }\n            return send({\n                cookies,\n                theme,\n                // @ts-expect-error fix error type\n                ...ErrorPage({ url, theme, error }),\n                title: \"Error\",\n            });\n        },\n    };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,SAAS,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAG;IAC5D,OAAO;QACH;QACA;QACA,SAAS;YAAE,gBAAgB;QAAY;QACvC,MAAM,CAAC,6LAA6L,EAAE,4NAAA,CAAA,UAAG,CAAC,eAAe,EAAE,MAAM,QAAQ,EAAE,YAAY,GAAG,sCAAsC,EAAE,OAAO,eAAe,OAAO,oBAAoB,EAAE,CAAA,GAAA,yRAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,oBAAoB,CAAC;IACnY;AACJ;AAKe,SAAS,WAAW,MAAM;IACrC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG;IACzD,OAAO;QACH,MAAK,IAAI,EAAE,OAAO,EAAE,OAAO;YACvB,IAAI,CAAC,MAAM;gBACP,OAAO;oBACH,SAAS;wBACL,gBAAgB;wBAChB,iBAAiB;wBACjB,SAAS;wBACT,QAAQ;oBACZ;oBACA,MAAM;wBAAE,WAAW,QAAQ,SAAS;oBAAC;oBACrC;gBACJ;YACJ;YACA,QAAQ,MAAM,CAAC,IAAI,CAAC;YACpB,QAAQ,IAAI,CAAC;gBACT,MAAM,QAAQ,OAAO,CAAC,SAAS,CAAC,IAAI;gBACpC,OAAO;gBACP,SAAS;oBAAE,GAAG,QAAQ,OAAO,CAAC,SAAS,CAAC,OAAO;oBAAE,QAAQ;gBAAE;YAC/D;YACA,OAAO;gBAAE,QAAQ;gBAAK;YAAQ;QAClC;QACA,WAAU,SAAS;YACf,OAAO;gBACH,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,UAAU,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE;oBACnE,GAAG,CAAC,GAAG,GAAG;wBAAE;wBAAI;wBAAM;wBAAM;wBAAW;oBAAY;oBACnD,OAAO;gBACX,GAAG,CAAC;YACR;QACJ;QACA,QAAO,UAAU,EAAE,KAAK;YACpB,IAAI,YACA,MAAM,IAAI,4MAAA,CAAA,gBAAa,CAAC;YAC5B,IAAI,OAAO,QAAQ;gBACf,IAAI,YAAY,GAAG,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,MAAM,MAAM,IAAI,gBAAgB;oBAAE,aAAa,OAAO,WAAW,IAAI;gBAAI,IAAI;gBAC5I,IAAI,OACA,YAAY,GAAG,UAAU,CAAC,EAAE,IAAI,gBAAgB;oBAAE;gBAAM,IAAI;gBAChE,OAAO;oBAAE,UAAU;oBAAW;gBAAQ;YAC1C;YACA,yDAAyD;YACzD,uDAAuD;YACvD,iCAAiC;YACjC,MAAM,mBAAmB,WAAW,KAAK,CAAC,IAAM,EAAE,IAAI,KAAK,cACvD,EAAE,mBAAmB,IACrB,CAAC,CAAC,EAAE,4BAA4B;YACpC,IAAI,8BAA8B;YAClC,IAAI,kBAAkB;gBAClB,MAAM,EAAE,4BAA4B,EAAE,GAAG;gBACzC,8BAA8B,CAAC,uDAAuD,EAAE,6BAA6B,gEAAgE,CAAC;YAC1L;YACA,OAAO,KAAK;gBACR;gBACA;gBACA,MAAM,CAAA,GAAA,4NAAA,CAAA,UAAU,AAAD,EAAE;oBACb,WAAW,OAAO,SAAS;oBAC3B,mCAAmC;oBACnC,WAAW,OAAO,SAAS,EAAE,OAAO,CAAC,WACrC,+CAA+C;wBAC/C;4BAAC;4BAAS;4BAAS;yBAAO,CAAC,QAAQ,CAAC,SAAS,IAAI,KAE5C,SAAS,IAAI,KAAK,iBAAiB,SAAS,WAAW,IAEvD,SAAS,IAAI,KAAK,cAAc,SAAS,UAAU,IACpD,oCAAoC;wBACpC;oBACJ,aAAa,OAAO,WAAW;oBAC/B,OAAO,OAAO,KAAK;oBACnB;oBACA,GAAG,KAAK;gBACZ;gBACA,OAAO;gBACP,UAAU;YACd;QACJ;QACA;YACI,IAAI,OAAO,SACP,OAAO;gBAAE,UAAU,MAAM,OAAO;gBAAE;YAAQ;YAC9C,OAAO,KAAK;gBACR;gBACA;gBACA,MAAM,CAAA,GAAA,6NAAA,CAAA,UAAW,AAAD,EAAE;oBAAE,WAAW,OAAO,SAAS;oBAAE;oBAAK;gBAAM;gBAC5D,OAAO;YACX;QACJ;QACA,eAAc,KAAK;YACf,IAAI,OAAO,eACP,OAAO;gBACH,UAAU,GAAG,MAAM,aAAa,GAAG,KAAK,UAAU,IAAI;gBACtD;YACJ;YACJ,OAAO,KAAK;gBACR;gBACA;gBACA,MAAM,CAAA,GAAA,uOAAA,CAAA,UAAiB,AAAD,EAAE;oBAAE;oBAAK;oBAAO,GAAG,KAAK;gBAAC;gBAC/C,OAAO;YACX;QACJ;QACA,OAAM,KAAK;YACP,IAAI,OAAO,OAAO;gBACd,OAAO;oBACH,UAAU,GAAG,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,OAAO,MAAM,IAAI,MAAM,EAAE,OAAO;oBAChF;gBACJ;YACJ;YACA,OAAO,KAAK;gBACR;gBACA;gBACA,kCAAkC;gBAClC,GAAG,CAAA,GAAA,2NAAA,CAAA,UAAS,AAAD,EAAE;oBAAE;oBAAK;oBAAO;gBAAM,EAAE;gBACnC,OAAO;YACX;QACJ;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2850, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/utils/date.js"], "sourcesContent": ["/**\n * Takes a number in seconds and returns the date in the future.\n * Optionally takes a second date parameter. In that case\n * the date in the future will be calculated from that date instead of now.\n */\nexport function fromDate(time, date = Date.now()) {\n    return new Date(date + time * 1000);\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACM,SAAS,SAAS,IAAI,EAAE,OAAO,KAAK,GAAG,EAAE;IAC5C,OAAO,IAAI,KAAK,OAAO,OAAO;AAClC", "ignoreList": [0]}}, {"offset": {"line": 2866, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/actions/callback/handle-login.js"], "sourcesContent": ["import { AccountNotLinked, OAuthAccountNotLinked } from \"../../../errors.js\";\nimport { fromDate } from \"../../utils/date.js\";\n/**\n * This function handles the complex flow of signing users in, and either creating,\n * linking (or not linking) accounts depending on if the user is currently logged\n * in, if they have account already and the authentication mechanism they are using.\n *\n * It prevents insecure behaviour, such as linking OAuth accounts unless a user is\n * signed in and authenticated with an existing valid account.\n *\n * All verification (e.g. OAuth flows or email address verification flows) are\n * done prior to this handler being called to avoid additional complexity in this\n * handler.\n */\nexport async function handleLoginOrRegister(sessionToken, _profile, _account, options) {\n    // Input validation\n    if (!_account?.providerAccountId || !_account.type)\n        throw new Error(\"Missing or invalid provider account\");\n    if (![\"email\", \"oauth\", \"oidc\", \"webauthn\"].includes(_account.type))\n        throw new Error(\"Provider not supported\");\n    const { adapter, jwt, events, session: { strategy: sessionStrategy, generateSessionToken }, } = options;\n    // If no adapter is configured then we don't have a database and cannot\n    // persist data; in this mode we just return a dummy session object.\n    if (!adapter) {\n        return { user: _profile, account: _account };\n    }\n    const profile = _profile;\n    let account = _account;\n    const { createUser, updateUser, getUser, getUserByAccount, getUserByEmail, linkAccount, createSession, getSessionAndUser, deleteSession, } = adapter;\n    let session = null;\n    let user = null;\n    let isNewUser = false;\n    const useJwtSession = sessionStrategy === \"jwt\";\n    if (sessionToken) {\n        if (useJwtSession) {\n            try {\n                const salt = options.cookies.sessionToken.name;\n                session = await jwt.decode({ ...jwt, token: sessionToken, salt });\n                if (session && \"sub\" in session && session.sub) {\n                    user = await getUser(session.sub);\n                }\n            }\n            catch {\n                // If session can't be verified, treat as no session\n            }\n        }\n        else {\n            const userAndSession = await getSessionAndUser(sessionToken);\n            if (userAndSession) {\n                session = userAndSession.session;\n                user = userAndSession.user;\n            }\n        }\n    }\n    if (account.type === \"email\") {\n        // If signing in with an email, check if an account with the same email address exists already\n        const userByEmail = await getUserByEmail(profile.email);\n        if (userByEmail) {\n            // If they are not already signed in as the same user, this flow will\n            // sign them out of the current session and sign them in as the new user\n            if (user?.id !== userByEmail.id && !useJwtSession && sessionToken) {\n                // Delete existing session if they are currently signed in as another user.\n                // This will switch user accounts for the session in cases where the user was\n                // already logged in with a different account.\n                await deleteSession(sessionToken);\n            }\n            // Update emailVerified property on the user object\n            user = await updateUser({\n                id: userByEmail.id,\n                emailVerified: new Date(),\n            });\n            await events.updateUser?.({ user });\n        }\n        else {\n            // Create user account if there isn't one for the email address already\n            user = await createUser({ ...profile, emailVerified: new Date() });\n            await events.createUser?.({ user });\n            isNewUser = true;\n        }\n        // Create new session\n        session = useJwtSession\n            ? {}\n            : await createSession({\n                sessionToken: generateSessionToken(),\n                userId: user.id,\n                expires: fromDate(options.session.maxAge),\n            });\n        return { session, user, isNewUser };\n    }\n    else if (account.type === \"webauthn\") {\n        // Check if the account exists\n        const userByAccount = await getUserByAccount({\n            providerAccountId: account.providerAccountId,\n            provider: account.provider,\n        });\n        if (userByAccount) {\n            if (user) {\n                // If the user is already signed in with this account, we don't need to do anything\n                if (userByAccount.id === user.id) {\n                    const currentAccount = { ...account, userId: user.id };\n                    return { session, user, isNewUser, account: currentAccount };\n                }\n                // If the user is currently signed in, but the new account they are signing in\n                // with is already associated with another user, then we cannot link them\n                // and need to return an error.\n                throw new AccountNotLinked(\"The account is already associated with another user\", { provider: account.provider });\n            }\n            // If there is no active session, but the account being signed in with is already\n            // associated with a valid user then create session to sign the user in.\n            session = useJwtSession\n                ? {}\n                : await createSession({\n                    sessionToken: generateSessionToken(),\n                    userId: userByAccount.id,\n                    expires: fromDate(options.session.maxAge),\n                });\n            const currentAccount = {\n                ...account,\n                userId: userByAccount.id,\n            };\n            return {\n                session,\n                user: userByAccount,\n                isNewUser,\n                account: currentAccount,\n            };\n        }\n        else {\n            // If the account doesn't exist, we'll create it\n            if (user) {\n                // If the user is already signed in and the account isn't already associated\n                // with another user account then we can go ahead and link the accounts safely.\n                await linkAccount({ ...account, userId: user.id });\n                await events.linkAccount?.({ user, account, profile });\n                // As they are already signed in, we don't need to do anything after linking them\n                const currentAccount = { ...account, userId: user.id };\n                return { session, user, isNewUser, account: currentAccount };\n            }\n            // If the user is not signed in and it looks like a new account then we\n            // check there also isn't an user account already associated with the same\n            // email address as the one in the request.\n            const userByEmail = profile.email\n                ? await getUserByEmail(profile.email)\n                : null;\n            if (userByEmail) {\n                // We don't trust user-provided email addresses, so we don't want to link accounts\n                // if the email address associated with the new account is already associated with\n                // an existing account.\n                throw new AccountNotLinked(\"Another account already exists with the same e-mail address\", { provider: account.provider });\n            }\n            else {\n                // If the current user is not logged in and the profile isn't linked to any user\n                // accounts (by email or provider account id)...\n                //\n                // If no account matching the same [provider].id or .email exists, we can\n                // create a new account for the user, link it to the OAuth account and\n                // create a new session for them so they are signed in with it.\n                user = await createUser({ ...profile });\n            }\n            await events.createUser?.({ user });\n            await linkAccount({ ...account, userId: user.id });\n            await events.linkAccount?.({ user, account, profile });\n            session = useJwtSession\n                ? {}\n                : await createSession({\n                    sessionToken: generateSessionToken(),\n                    userId: user.id,\n                    expires: fromDate(options.session.maxAge),\n                });\n            const currentAccount = { ...account, userId: user.id };\n            return { session, user, isNewUser: true, account: currentAccount };\n        }\n    }\n    // If signing in with OAuth account, check to see if the account exists already\n    const userByAccount = await getUserByAccount({\n        providerAccountId: account.providerAccountId,\n        provider: account.provider,\n    });\n    if (userByAccount) {\n        if (user) {\n            // If the user is already signed in with this account, we don't need to do anything\n            if (userByAccount.id === user.id) {\n                return { session, user, isNewUser };\n            }\n            // If the user is currently signed in, but the new account they are signing in\n            // with is already associated with another user, then we cannot link them\n            // and need to return an error.\n            throw new OAuthAccountNotLinked(\"The account is already associated with another user\", { provider: account.provider });\n        }\n        // If there is no active session, but the account being signed in with is already\n        // associated with a valid user then create session to sign the user in.\n        session = useJwtSession\n            ? {}\n            : await createSession({\n                sessionToken: generateSessionToken(),\n                userId: userByAccount.id,\n                expires: fromDate(options.session.maxAge),\n            });\n        return { session, user: userByAccount, isNewUser };\n    }\n    else {\n        const { provider: p } = options;\n        const { type, provider, providerAccountId, userId, ...tokenSet } = account;\n        const defaults = { providerAccountId, provider, type, userId };\n        account = Object.assign(p.account(tokenSet) ?? {}, defaults);\n        if (user) {\n            // If the user is already signed in and the OAuth account isn't already associated\n            // with another user account then we can go ahead and link the accounts safely.\n            await linkAccount({ ...account, userId: user.id });\n            await events.linkAccount?.({ user, account, profile });\n            // As they are already signed in, we don't need to do anything after linking them\n            return { session, user, isNewUser };\n        }\n        // If the user is not signed in and it looks like a new OAuth account then we\n        // check there also isn't an user account already associated with the same\n        // email address as the one in the OAuth profile.\n        //\n        // This step is often overlooked in OAuth implementations, but covers the following cases:\n        //\n        // 1. It makes it harder for someone to accidentally create two accounts.\n        //    e.g. by signin in with email, then again with an oauth account connected to the same email.\n        // 2. It makes it harder to hijack a user account using a 3rd party OAuth account.\n        //    e.g. by creating an oauth account then changing the email address associated with it.\n        //\n        // It's quite common for services to automatically link accounts in this case, but it's\n        // better practice to require the user to sign in *then* link accounts to be sure\n        // someone is not exploiting a problem with a third party OAuth service.\n        //\n        // OAuth providers should require email address verification to prevent this, but in\n        // practice that is not always the case; this helps protect against that.\n        const userByEmail = profile.email\n            ? await getUserByEmail(profile.email)\n            : null;\n        if (userByEmail) {\n            const provider = options.provider;\n            if (provider?.allowDangerousEmailAccountLinking) {\n                // If you trust the oauth provider to correctly verify email addresses, you can opt-in to\n                // account linking even when the user is not signed-in.\n                user = userByEmail;\n                isNewUser = false;\n            }\n            else {\n                // We end up here when we don't have an account with the same [provider].id *BUT*\n                // we do already have an account with the same email address as the one in the\n                // OAuth profile the user has just tried to sign in with.\n                //\n                // We don't want to have two accounts with the same email address, and we don't\n                // want to link them in case it's not safe to do so, so instead we prompt the user\n                // to sign in via email to verify their identity and then link the accounts.\n                throw new OAuthAccountNotLinked(\"Another account already exists with the same e-mail address\", { provider: account.provider });\n            }\n        }\n        else {\n            // If the current user is not logged in and the profile isn't linked to any user\n            // accounts (by email or provider account id)...\n            //\n            // If no account matching the same [provider].id or .email exists, we can\n            // create a new account for the user, link it to the OAuth account and\n            // create a new session for them so they are signed in with it.\n            user = await createUser({ ...profile, emailVerified: null });\n            isNewUser = true;\n        }\n        await events.createUser?.({ user });\n        await linkAccount({ ...account, userId: user.id });\n        await events.linkAccount?.({ user, account, profile });\n        session = useJwtSession\n            ? {}\n            : await createSession({\n                sessionToken: generateSessionToken(),\n                userId: user.id,\n                expires: fromDate(options.session.maxAge),\n            });\n        return { session, user, isNewUser };\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAaO,eAAe,sBAAsB,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO;IACjF,mBAAmB;IACnB,IAAI,CAAC,UAAU,qBAAqB,CAAC,SAAS,IAAI,EAC9C,MAAM,IAAI,MAAM;IACpB,IAAI,CAAC;QAAC;QAAS;QAAS;QAAQ;KAAW,CAAC,QAAQ,CAAC,SAAS,IAAI,GAC9D,MAAM,IAAI,MAAM;IACpB,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,eAAe,EAAE,oBAAoB,EAAE,EAAG,GAAG;IAChG,uEAAuE;IACvE,oEAAoE;IACpE,IAAI,CAAC,SAAS;QACV,OAAO;YAAE,MAAM;YAAU,SAAS;QAAS;IAC/C;IACA,MAAM,UAAU;IAChB,IAAI,UAAU;IACd,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,gBAAgB,EAAE,cAAc,EAAE,WAAW,EAAE,aAAa,EAAE,iBAAiB,EAAE,aAAa,EAAG,GAAG;IAC7I,IAAI,UAAU;IACd,IAAI,OAAO;IACX,IAAI,YAAY;IAChB,MAAM,gBAAgB,oBAAoB;IAC1C,IAAI,cAAc;QACd,IAAI,eAAe;YACf,IAAI;gBACA,MAAM,OAAO,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI;gBAC9C,UAAU,MAAM,IAAI,MAAM,CAAC;oBAAE,GAAG,GAAG;oBAAE,OAAO;oBAAc;gBAAK;gBAC/D,IAAI,WAAW,SAAS,WAAW,QAAQ,GAAG,EAAE;oBAC5C,OAAO,MAAM,QAAQ,QAAQ,GAAG;gBACpC;YACJ,EACA,OAAM;YACF,oDAAoD;YACxD;QACJ,OACK;YACD,MAAM,iBAAiB,MAAM,kBAAkB;YAC/C,IAAI,gBAAgB;gBAChB,UAAU,eAAe,OAAO;gBAChC,OAAO,eAAe,IAAI;YAC9B;QACJ;IACJ;IACA,IAAI,QAAQ,IAAI,KAAK,SAAS;QAC1B,8FAA8F;QAC9F,MAAM,cAAc,MAAM,eAAe,QAAQ,KAAK;QACtD,IAAI,aAAa;YACb,qEAAqE;YACrE,wEAAwE;YACxE,IAAI,MAAM,OAAO,YAAY,EAAE,IAAI,CAAC,iBAAiB,cAAc;gBAC/D,2EAA2E;gBAC3E,6EAA6E;gBAC7E,8CAA8C;gBAC9C,MAAM,cAAc;YACxB;YACA,mDAAmD;YACnD,OAAO,MAAM,WAAW;gBACpB,IAAI,YAAY,EAAE;gBAClB,eAAe,IAAI;YACvB;YACA,MAAM,OAAO,UAAU,GAAG;gBAAE;YAAK;QACrC,OACK;YACD,uEAAuE;YACvE,OAAO,MAAM,WAAW;gBAAE,GAAG,OAAO;gBAAE,eAAe,IAAI;YAAO;YAChE,MAAM,OAAO,UAAU,GAAG;gBAAE;YAAK;YACjC,YAAY;QAChB;QACA,qBAAqB;QACrB,UAAU,gBACJ,CAAC,IACD,MAAM,cAAc;YAClB,cAAc;YACd,QAAQ,KAAK,EAAE;YACf,SAAS,CAAA,GAAA,0NAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,OAAO,CAAC,MAAM;QAC5C;QACJ,OAAO;YAAE;YAAS;YAAM;QAAU;IACtC,OACK,IAAI,QAAQ,IAAI,KAAK,YAAY;QAClC,8BAA8B;QAC9B,MAAM,gBAAgB,MAAM,iBAAiB;YACzC,mBAAmB,QAAQ,iBAAiB;YAC5C,UAAU,QAAQ,QAAQ;QAC9B;QACA,IAAI,eAAe;YACf,IAAI,MAAM;gBACN,mFAAmF;gBACnF,IAAI,cAAc,EAAE,KAAK,KAAK,EAAE,EAAE;oBAC9B,MAAM,iBAAiB;wBAAE,GAAG,OAAO;wBAAE,QAAQ,KAAK,EAAE;oBAAC;oBACrD,OAAO;wBAAE;wBAAS;wBAAM;wBAAW,SAAS;oBAAe;gBAC/D;gBACA,8EAA8E;gBAC9E,yEAAyE;gBACzE,+BAA+B;gBAC/B,MAAM,IAAI,4MAAA,CAAA,mBAAgB,CAAC,uDAAuD;oBAAE,UAAU,QAAQ,QAAQ;gBAAC;YACnH;YACA,iFAAiF;YACjF,wEAAwE;YACxE,UAAU,gBACJ,CAAC,IACD,MAAM,cAAc;gBAClB,cAAc;gBACd,QAAQ,cAAc,EAAE;gBACxB,SAAS,CAAA,GAAA,0NAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,OAAO,CAAC,MAAM;YAC5C;YACJ,MAAM,iBAAiB;gBACnB,GAAG,OAAO;gBACV,QAAQ,cAAc,EAAE;YAC5B;YACA,OAAO;gBACH;gBACA,MAAM;gBACN;gBACA,SAAS;YACb;QACJ,OACK;YACD,gDAAgD;YAChD,IAAI,MAAM;gBACN,4EAA4E;gBAC5E,+EAA+E;gBAC/E,MAAM,YAAY;oBAAE,GAAG,OAAO;oBAAE,QAAQ,KAAK,EAAE;gBAAC;gBAChD,MAAM,OAAO,WAAW,GAAG;oBAAE;oBAAM;oBAAS;gBAAQ;gBACpD,iFAAiF;gBACjF,MAAM,iBAAiB;oBAAE,GAAG,OAAO;oBAAE,QAAQ,KAAK,EAAE;gBAAC;gBACrD,OAAO;oBAAE;oBAAS;oBAAM;oBAAW,SAAS;gBAAe;YAC/D;YACA,uEAAuE;YACvE,0EAA0E;YAC1E,2CAA2C;YAC3C,MAAM,cAAc,QAAQ,KAAK,GAC3B,MAAM,eAAe,QAAQ,KAAK,IAClC;YACN,IAAI,aAAa;gBACb,kFAAkF;gBAClF,kFAAkF;gBAClF,uBAAuB;gBACvB,MAAM,IAAI,4MAAA,CAAA,mBAAgB,CAAC,+DAA+D;oBAAE,UAAU,QAAQ,QAAQ;gBAAC;YAC3H,OACK;gBACD,gFAAgF;gBAChF,gDAAgD;gBAChD,EAAE;gBACF,yEAAyE;gBACzE,sEAAsE;gBACtE,+DAA+D;gBAC/D,OAAO,MAAM,WAAW;oBAAE,GAAG,OAAO;gBAAC;YACzC;YACA,MAAM,OAAO,UAAU,GAAG;gBAAE;YAAK;YACjC,MAAM,YAAY;gBAAE,GAAG,OAAO;gBAAE,QAAQ,KAAK,EAAE;YAAC;YAChD,MAAM,OAAO,WAAW,GAAG;gBAAE;gBAAM;gBAAS;YAAQ;YACpD,UAAU,gBACJ,CAAC,IACD,MAAM,cAAc;gBAClB,cAAc;gBACd,QAAQ,KAAK,EAAE;gBACf,SAAS,CAAA,GAAA,0NAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,OAAO,CAAC,MAAM;YAC5C;YACJ,MAAM,iBAAiB;gBAAE,GAAG,OAAO;gBAAE,QAAQ,KAAK,EAAE;YAAC;YACrD,OAAO;gBAAE;gBAAS;gBAAM,WAAW;gBAAM,SAAS;YAAe;QACrE;IACJ;IACA,+EAA+E;IAC/E,MAAM,gBAAgB,MAAM,iBAAiB;QACzC,mBAAmB,QAAQ,iBAAiB;QAC5C,UAAU,QAAQ,QAAQ;IAC9B;IACA,IAAI,eAAe;QACf,IAAI,MAAM;YACN,mFAAmF;YACnF,IAAI,cAAc,EAAE,KAAK,KAAK,EAAE,EAAE;gBAC9B,OAAO;oBAAE;oBAAS;oBAAM;gBAAU;YACtC;YACA,8EAA8E;YAC9E,yEAAyE;YACzE,+BAA+B;YAC/B,MAAM,IAAI,4MAAA,CAAA,wBAAqB,CAAC,uDAAuD;gBAAE,UAAU,QAAQ,QAAQ;YAAC;QACxH;QACA,iFAAiF;QACjF,wEAAwE;QACxE,UAAU,gBACJ,CAAC,IACD,MAAM,cAAc;YAClB,cAAc;YACd,QAAQ,cAAc,EAAE;YACxB,SAAS,CAAA,GAAA,0NAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,OAAO,CAAC,MAAM;QAC5C;QACJ,OAAO;YAAE;YAAS,MAAM;YAAe;QAAU;IACrD,OACK;QACD,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG;QACxB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,iBAAiB,EAAE,MAAM,EAAE,GAAG,UAAU,GAAG;QACnE,MAAM,WAAW;YAAE;YAAmB;YAAU;YAAM;QAAO;QAC7D,UAAU,OAAO,MAAM,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC,GAAG;QACnD,IAAI,MAAM;YACN,kFAAkF;YAClF,+EAA+E;YAC/E,MAAM,YAAY;gBAAE,GAAG,OAAO;gBAAE,QAAQ,KAAK,EAAE;YAAC;YAChD,MAAM,OAAO,WAAW,GAAG;gBAAE;gBAAM;gBAAS;YAAQ;YACpD,iFAAiF;YACjF,OAAO;gBAAE;gBAAS;gBAAM;YAAU;QACtC;QACA,6EAA6E;QAC7E,0EAA0E;QAC1E,iDAAiD;QACjD,EAAE;QACF,0FAA0F;QAC1F,EAAE;QACF,yEAAyE;QACzE,iGAAiG;QACjG,kFAAkF;QAClF,2FAA2F;QAC3F,EAAE;QACF,uFAAuF;QACvF,iFAAiF;QACjF,wEAAwE;QACxE,EAAE;QACF,oFAAoF;QACpF,yEAAyE;QACzE,MAAM,cAAc,QAAQ,KAAK,GAC3B,MAAM,eAAe,QAAQ,KAAK,IAClC;QACN,IAAI,aAAa;YACb,MAAM,WAAW,QAAQ,QAAQ;YACjC,IAAI,UAAU,mCAAmC;gBAC7C,yFAAyF;gBACzF,uDAAuD;gBACvD,OAAO;gBACP,YAAY;YAChB,OACK;gBACD,iFAAiF;gBACjF,8EAA8E;gBAC9E,yDAAyD;gBACzD,EAAE;gBACF,+EAA+E;gBAC/E,kFAAkF;gBAClF,4EAA4E;gBAC5E,MAAM,IAAI,4MAAA,CAAA,wBAAqB,CAAC,+DAA+D;oBAAE,UAAU,QAAQ,QAAQ;gBAAC;YAChI;QACJ,OACK;YACD,gFAAgF;YAChF,gDAAgD;YAChD,EAAE;YACF,yEAAyE;YACzE,sEAAsE;YACtE,+DAA+D;YAC/D,OAAO,MAAM,WAAW;gBAAE,GAAG,OAAO;gBAAE,eAAe;YAAK;YAC1D,YAAY;QAChB;QACA,MAAM,OAAO,UAAU,GAAG;YAAE;QAAK;QACjC,MAAM,YAAY;YAAE,GAAG,OAAO;YAAE,QAAQ,KAAK,EAAE;QAAC;QAChD,MAAM,OAAO,WAAW,GAAG;YAAE;YAAM;YAAS;QAAQ;QACpD,UAAU,gBACJ,CAAC,IACD,MAAM,cAAc;YAClB,cAAc;YACd,QAAQ,KAAK,EAAE;YACf,SAAS,CAAA,GAAA,0NAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,OAAO,CAAC,MAAM;QAC5C;QACJ,OAAO;YAAE;YAAS;YAAM;QAAU;IACtC;AACJ", "ignoreList": [0]}}, {"offset": {"line": 3228, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/actions/callback/oauth/checks.js"], "sourcesContent": ["import * as o from \"oauth<PERSON><PERSON><PERSON><PERSON>\";\nimport { InvalidCheck } from \"../../../../errors.js\";\n// NOTE: We use the default JWT methods here because they encrypt/decrypt the payload, not just sign it.\nimport { decode, encode } from \"../../../../jwt.js\";\nconst COOKIE_TTL = 60 * 15; // 15 minutes\n/** Returns a cookie with a JWT encrypted payload. */\nasync function sealCookie(name, payload, options) {\n    const { cookies, logger } = options;\n    const cookie = cookies[name];\n    const expires = new Date();\n    expires.setTime(expires.getTime() + COOKIE_TTL * 1000);\n    logger.debug(`CREATE_${name.toUpperCase()}`, {\n        name: cookie.name,\n        payload,\n        COOKIE_TTL,\n        expires,\n    });\n    const encoded = await encode({\n        ...options.jwt,\n        maxAge: COOKIE_TTL,\n        token: { value: payload },\n        salt: cookie.name,\n    });\n    const cookieOptions = { ...cookie.options, expires };\n    return { name: cookie.name, value: encoded, options: cookieOptions };\n}\nasync function parseCookie(name, value, options) {\n    try {\n        const { logger, cookies, jwt } = options;\n        logger.debug(`PARSE_${name.toUpperCase()}`, { cookie: value });\n        if (!value)\n            throw new InvalidCheck(`${name} cookie was missing`);\n        const parsed = await decode({\n            ...jwt,\n            token: value,\n            salt: cookies[name].name,\n        });\n        if (parsed?.value)\n            return parsed.value;\n        throw new Error(\"Invalid cookie\");\n    }\n    catch (error) {\n        throw new InvalidCheck(`${name} value could not be parsed`, {\n            cause: error,\n        });\n    }\n}\nfunction clearCookie(name, options, resCookies) {\n    const { logger, cookies } = options;\n    const cookie = cookies[name];\n    logger.debug(`CLEAR_${name.toUpperCase()}`, { cookie });\n    resCookies.push({\n        name: cookie.name,\n        value: \"\",\n        options: { ...cookies[name].options, maxAge: 0 },\n    });\n}\nfunction useCookie(check, name) {\n    return async function (cookies, resCookies, options) {\n        const { provider, logger } = options;\n        if (!provider?.checks?.includes(check))\n            return;\n        const cookieValue = cookies?.[options.cookies[name].name];\n        logger.debug(`USE_${name.toUpperCase()}`, { value: cookieValue });\n        const parsed = await parseCookie(name, cookieValue, options);\n        clearCookie(name, options, resCookies);\n        return parsed;\n    };\n}\n/**\n * @see https://www.rfc-editor.org/rfc/rfc7636\n * @see https://danielfett.de/2020/05/16/pkce-vs-nonce-equivalent-or-not/#pkce\n */\nexport const pkce = {\n    /** Creates a PKCE code challenge and verifier pair. The verifier in stored in the cookie. */\n    async create(options) {\n        const code_verifier = o.generateRandomCodeVerifier();\n        const value = await o.calculatePKCECodeChallenge(code_verifier);\n        const cookie = await sealCookie(\"pkceCodeVerifier\", code_verifier, options);\n        return { cookie, value };\n    },\n    /**\n     * Returns code_verifier if the provider is configured to use PKCE,\n     * and clears the container cookie afterwards.\n     * An error is thrown if the code_verifier is missing or invalid.\n     */\n    use: useCookie(\"pkce\", \"pkceCodeVerifier\"),\n};\nconst STATE_MAX_AGE = 60 * 15; // 15 minutes in seconds\nconst encodedStateSalt = \"encodedState\";\n/**\n * @see https://www.rfc-editor.org/rfc/rfc6749#section-10.12\n * @see https://www.rfc-editor.org/rfc/rfc6749#section-4.1.1\n */\nexport const state = {\n    /** Creates a state cookie with an optionally encoded body. */\n    async create(options, origin) {\n        const { provider } = options;\n        if (!provider.checks.includes(\"state\")) {\n            if (origin) {\n                throw new InvalidCheck(\"State data was provided but the provider is not configured to use state\");\n            }\n            return;\n        }\n        // IDEA: Allow the user to pass data to be stored in the state\n        const payload = {\n            origin,\n            random: o.generateRandomState(),\n        };\n        const value = await encode({\n            secret: options.jwt.secret,\n            token: payload,\n            salt: encodedStateSalt,\n            maxAge: STATE_MAX_AGE,\n        });\n        const cookie = await sealCookie(\"state\", value, options);\n        return { cookie, value };\n    },\n    /**\n     * Returns state if the provider is configured to use state,\n     * and clears the container cookie afterwards.\n     * An error is thrown if the state is missing or invalid.\n     */\n    use: useCookie(\"state\", \"state\"),\n    /** Decodes the state. If it could not be decoded, it throws an error. */\n    async decode(state, options) {\n        try {\n            options.logger.debug(\"DECODE_STATE\", { state });\n            const payload = await decode({\n                secret: options.jwt.secret,\n                token: state,\n                salt: encodedStateSalt,\n            });\n            if (payload)\n                return payload;\n            throw new Error(\"Invalid state\");\n        }\n        catch (error) {\n            throw new InvalidCheck(\"State could not be decoded\", { cause: error });\n        }\n    },\n};\nexport const nonce = {\n    async create(options) {\n        if (!options.provider.checks.includes(\"nonce\"))\n            return;\n        const value = o.generateRandomNonce();\n        const cookie = await sealCookie(\"nonce\", value, options);\n        return { cookie, value };\n    },\n    /**\n     * Returns nonce if the provider is configured to use nonce,\n     * and clears the container cookie afterwards.\n     * An error is thrown if the nonce is missing or invalid.\n     * @see https://openid.net/specs/openid-connect-core-1_0.html#NonceNotes\n     * @see https://danielfett.de/2020/05/16/pkce-vs-nonce-equivalent-or-not/#nonce\n     */\n    use: useCookie(\"nonce\", \"nonce\"),\n};\nconst WEBAUTHN_CHALLENGE_MAX_AGE = 60 * 15; // 15 minutes in seconds\nconst webauthnChallengeSalt = \"encodedWebauthnChallenge\";\nexport const webauthnChallenge = {\n    async create(options, challenge, registerData) {\n        return {\n            cookie: await sealCookie(\"webauthnChallenge\", await encode({\n                secret: options.jwt.secret,\n                token: { challenge, registerData },\n                salt: webauthnChallengeSalt,\n                maxAge: WEBAUTHN_CHALLENGE_MAX_AGE,\n            }), options),\n        };\n    },\n    /** Returns WebAuthn challenge if present. */\n    async use(options, cookies, resCookies) {\n        const cookieValue = cookies?.[options.cookies.webauthnChallenge.name];\n        const parsed = await parseCookie(\"webauthnChallenge\", cookieValue, options);\n        const payload = await decode({\n            secret: options.jwt.secret,\n            token: parsed,\n            salt: webauthnChallengeSalt,\n        });\n        // Clear the WebAuthn challenge cookie after use\n        clearCookie(\"webauthnChallenge\", options, resCookies);\n        if (!payload)\n            throw new InvalidCheck(\"WebAuthn challenge was missing\");\n        return payload;\n    },\n};\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA,wGAAwG;AACxG;;;;AACA,MAAM,aAAa,KAAK,IAAI,aAAa;AACzC,mDAAmD,GACnD,eAAe,WAAW,IAAI,EAAE,OAAO,EAAE,OAAO;IAC5C,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG;IAC5B,MAAM,SAAS,OAAO,CAAC,KAAK;IAC5B,MAAM,UAAU,IAAI;IACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK,aAAa;IACjD,OAAO,KAAK,CAAC,CAAC,OAAO,EAAE,KAAK,WAAW,IAAI,EAAE;QACzC,MAAM,OAAO,IAAI;QACjB;QACA;QACA;IACJ;IACA,MAAM,UAAU,MAAM,CAAA,GAAA,yMAAA,CAAA,SAAM,AAAD,EAAE;QACzB,GAAG,QAAQ,GAAG;QACd,QAAQ;QACR,OAAO;YAAE,OAAO;QAAQ;QACxB,MAAM,OAAO,IAAI;IACrB;IACA,MAAM,gBAAgB;QAAE,GAAG,OAAO,OAAO;QAAE;IAAQ;IACnD,OAAO;QAAE,MAAM,OAAO,IAAI;QAAE,OAAO;QAAS,SAAS;IAAc;AACvE;AACA,eAAe,YAAY,IAAI,EAAE,KAAK,EAAE,OAAO;IAC3C,IAAI;QACA,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG;QACjC,OAAO,KAAK,CAAC,CAAC,MAAM,EAAE,KAAK,WAAW,IAAI,EAAE;YAAE,QAAQ;QAAM;QAC5D,IAAI,CAAC,OACD,MAAM,IAAI,4MAAA,CAAA,eAAY,CAAC,GAAG,KAAK,mBAAmB,CAAC;QACvD,MAAM,SAAS,MAAM,CAAA,GAAA,yMAAA,CAAA,SAAM,AAAD,EAAE;YACxB,GAAG,GAAG;YACN,OAAO;YACP,MAAM,OAAO,CAAC,KAAK,CAAC,IAAI;QAC5B;QACA,IAAI,QAAQ,OACR,OAAO,OAAO,KAAK;QACvB,MAAM,IAAI,MAAM;IACpB,EACA,OAAO,OAAO;QACV,MAAM,IAAI,4MAAA,CAAA,eAAY,CAAC,GAAG,KAAK,0BAA0B,CAAC,EAAE;YACxD,OAAO;QACX;IACJ;AACJ;AACA,SAAS,YAAY,IAAI,EAAE,OAAO,EAAE,UAAU;IAC1C,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG;IAC5B,MAAM,SAAS,OAAO,CAAC,KAAK;IAC5B,OAAO,KAAK,CAAC,CAAC,MAAM,EAAE,KAAK,WAAW,IAAI,EAAE;QAAE;IAAO;IACrD,WAAW,IAAI,CAAC;QACZ,MAAM,OAAO,IAAI;QACjB,OAAO;QACP,SAAS;YAAE,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO;YAAE,QAAQ;QAAE;IACnD;AACJ;AACA,SAAS,UAAU,KAAK,EAAE,IAAI;IAC1B,OAAO,eAAgB,OAAO,EAAE,UAAU,EAAE,OAAO;QAC/C,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;QAC7B,IAAI,CAAC,UAAU,QAAQ,SAAS,QAC5B;QACJ,MAAM,cAAc,SAAS,CAAC,QAAQ,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;QACzD,OAAO,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,WAAW,IAAI,EAAE;YAAE,OAAO;QAAY;QAC/D,MAAM,SAAS,MAAM,YAAY,MAAM,aAAa;QACpD,YAAY,MAAM,SAAS;QAC3B,OAAO;IACX;AACJ;AAKO,MAAM,OAAO;IAChB,2FAA2F,GAC3F,MAAM,QAAO,OAAO;QAChB,MAAM,gBAAgB,CAAA,GAAA,+MAAA,CAAA,6BAA4B,AAAD;QACjD,MAAM,QAAQ,MAAM,CAAA,GAAA,+MAAA,CAAA,6BAA4B,AAAD,EAAE;QACjD,MAAM,SAAS,MAAM,WAAW,oBAAoB,eAAe;QACnE,OAAO;YAAE;YAAQ;QAAM;IAC3B;IACA;;;;KAIC,GACD,KAAK,UAAU,QAAQ;AAC3B;AACA,MAAM,gBAAgB,KAAK,IAAI,wBAAwB;AACvD,MAAM,mBAAmB;AAKlB,MAAM,QAAQ;IACjB,4DAA4D,GAC5D,MAAM,QAAO,OAAO,EAAE,MAAM;QACxB,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,IAAI,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,UAAU;YACpC,IAAI,QAAQ;gBACR,MAAM,IAAI,4MAAA,CAAA,eAAY,CAAC;YAC3B;YACA;QACJ;QACA,8DAA8D;QAC9D,MAAM,UAAU;YACZ;YACA,QAAQ,CAAA,GAAA,+MAAA,CAAA,sBAAqB,AAAD;QAChC;QACA,MAAM,QAAQ,MAAM,CAAA,GAAA,yMAAA,CAAA,SAAM,AAAD,EAAE;YACvB,QAAQ,QAAQ,GAAG,CAAC,MAAM;YAC1B,OAAO;YACP,MAAM;YACN,QAAQ;QACZ;QACA,MAAM,SAAS,MAAM,WAAW,SAAS,OAAO;QAChD,OAAO;YAAE;YAAQ;QAAM;IAC3B;IACA;;;;KAIC,GACD,KAAK,UAAU,SAAS;IACxB,uEAAuE,GACvE,MAAM,QAAO,KAAK,EAAE,OAAO;QACvB,IAAI;YACA,QAAQ,MAAM,CAAC,KAAK,CAAC,gBAAgB;gBAAE;YAAM;YAC7C,MAAM,UAAU,MAAM,CAAA,GAAA,yMAAA,CAAA,SAAM,AAAD,EAAE;gBACzB,QAAQ,QAAQ,GAAG,CAAC,MAAM;gBAC1B,OAAO;gBACP,MAAM;YACV;YACA,IAAI,SACA,OAAO;YACX,MAAM,IAAI,MAAM;QACpB,EACA,OAAO,OAAO;YACV,MAAM,IAAI,4MAAA,CAAA,eAAY,CAAC,8BAA8B;gBAAE,OAAO;YAAM;QACxE;IACJ;AACJ;AACO,MAAM,QAAQ;IACjB,MAAM,QAAO,OAAO;QAChB,IAAI,CAAC,QAAQ,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,UAClC;QACJ,MAAM,QAAQ,CAAA,GAAA,+MAAA,CAAA,sBAAqB,AAAD;QAClC,MAAM,SAAS,MAAM,WAAW,SAAS,OAAO;QAChD,OAAO;YAAE;YAAQ;QAAM;IAC3B;IACA;;;;;;KAMC,GACD,KAAK,UAAU,SAAS;AAC5B;AACA,MAAM,6BAA6B,KAAK,IAAI,wBAAwB;AACpE,MAAM,wBAAwB;AACvB,MAAM,oBAAoB;IAC7B,MAAM,QAAO,OAAO,EAAE,SAAS,EAAE,YAAY;QACzC,OAAO;YACH,QAAQ,MAAM,WAAW,qBAAqB,MAAM,CAAA,GAAA,yMAAA,CAAA,SAAM,AAAD,EAAE;gBACvD,QAAQ,QAAQ,GAAG,CAAC,MAAM;gBAC1B,OAAO;oBAAE;oBAAW;gBAAa;gBACjC,MAAM;gBACN,QAAQ;YACZ,IAAI;QACR;IACJ;IACA,2CAA2C,GAC3C,MAAM,KAAI,OAAO,EAAE,OAAO,EAAE,UAAU;QAClC,MAAM,cAAc,SAAS,CAAC,QAAQ,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC;QACrE,MAAM,SAAS,MAAM,YAAY,qBAAqB,aAAa;QACnE,MAAM,UAAU,MAAM,CAAA,GAAA,yMAAA,CAAA,SAAM,AAAD,EAAE;YACzB,QAAQ,QAAQ,GAAG,CAAC,MAAM;YAC1B,OAAO;YACP,MAAM;QACV;QACA,gDAAgD;QAChD,YAAY,qBAAqB,SAAS;QAC1C,IAAI,CAAC,SACD,MAAM,IAAI,4MAAA,CAAA,eAAY,CAAC;QAC3B,OAAO;IACX;AACJ", "ignoreList": [0]}}, {"offset": {"line": 3441, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/actions/callback/oauth/callback.js"], "sourcesContent": ["import * as checks from \"./checks.js\";\nimport * as o from \"oauth4we<PERSON><PERSON>\";\nimport { OAuthCallbackError, OAuthProfileParseError, } from \"../../../../errors.js\";\nimport { isOIDCProvider } from \"../../../utils/providers.js\";\nimport { conformInternal, customFetch } from \"../../../symbols.js\";\nimport { decodeJwt } from \"jose\";\nfunction formUrlEncode(token) {\n    return encodeURIComponent(token).replace(/%20/g, \"+\");\n}\n/**\n * Formats client_id and client_secret as an HTTP Basic Authentication header as per the OAuth 2.0\n * specified in RFC6749.\n */\nfunction clientSecretBasic(clientId, clientSecret) {\n    const username = formUrlEncode(clientId);\n    const password = formUrlEncode(clientSecret);\n    const credentials = btoa(`${username}:${password}`);\n    return `Basic ${credentials}`;\n}\n/**\n * Handles the following OAuth steps.\n * https://www.rfc-editor.org/rfc/rfc6749#section-4.1.1\n * https://www.rfc-editor.org/rfc/rfc6749#section-4.1.3\n * https://openid.net/specs/openid-connect-core-1_0.html#UserInfoRequest\n *\n * @note Although requesting userinfo is not required by the OAuth2.0 spec,\n * we fetch it anyway. This is because we always want a user profile.\n */\nexport async function handleOAuth(params, cookies, options) {\n    const { logger, provider } = options;\n    let as;\n    const { token, userinfo } = provider;\n    // Falls back to authjs.dev if the user only passed params\n    if ((!token?.url || token.url.host === \"authjs.dev\") &&\n        (!userinfo?.url || userinfo.url.host === \"authjs.dev\")) {\n        // We assume that issuer is always defined as this has been asserted earlier\n        const issuer = new URL(provider.issuer);\n        const discoveryResponse = await o.discoveryRequest(issuer, {\n            [o.allowInsecureRequests]: true,\n            [o.customFetch]: provider[customFetch],\n        });\n        as = await o.processDiscoveryResponse(issuer, discoveryResponse);\n        if (!as.token_endpoint)\n            throw new TypeError(\"TODO: Authorization server did not provide a token endpoint.\");\n        if (!as.userinfo_endpoint)\n            throw new TypeError(\"TODO: Authorization server did not provide a userinfo endpoint.\");\n    }\n    else {\n        as = {\n            issuer: provider.issuer ?? \"https://authjs.dev\", // TODO: review fallback issuer\n            token_endpoint: token?.url.toString(),\n            userinfo_endpoint: userinfo?.url.toString(),\n        };\n    }\n    const client = {\n        client_id: provider.clientId,\n        ...provider.client,\n    };\n    let clientAuth;\n    switch (client.token_endpoint_auth_method) {\n        // TODO: in the next breaking major version have undefined be `client_secret_post`\n        case undefined:\n        case \"client_secret_basic\":\n            // TODO: in the next breaking major version use o.ClientSecretBasic() here\n            clientAuth = (_as, _client, _body, headers) => {\n                headers.set(\"authorization\", clientSecretBasic(provider.clientId, provider.clientSecret));\n            };\n            break;\n        case \"client_secret_post\":\n            clientAuth = o.ClientSecretPost(provider.clientSecret);\n            break;\n        case \"client_secret_jwt\":\n            clientAuth = o.ClientSecretJwt(provider.clientSecret);\n            break;\n        case \"private_key_jwt\":\n            clientAuth = o.PrivateKeyJwt(provider.token.clientPrivateKey, {\n                // TODO: review in the next breaking change\n                [o.modifyAssertion](_header, payload) {\n                    payload.aud = [as.issuer, as.token_endpoint];\n                },\n            });\n            break;\n        case \"none\":\n            clientAuth = o.None();\n            break;\n        default:\n            throw new Error(\"unsupported client authentication method\");\n    }\n    const resCookies = [];\n    const state = await checks.state.use(cookies, resCookies, options);\n    let codeGrantParams;\n    try {\n        codeGrantParams = o.validateAuthResponse(as, client, new URLSearchParams(params), provider.checks.includes(\"state\") ? state : o.skipStateCheck);\n    }\n    catch (err) {\n        if (err instanceof o.AuthorizationResponseError) {\n            const cause = {\n                providerId: provider.id,\n                ...Object.fromEntries(err.cause.entries()),\n            };\n            logger.debug(\"OAuthCallbackError\", cause);\n            throw new OAuthCallbackError(\"OAuth Provider returned an error\", cause);\n        }\n        throw err;\n    }\n    const codeVerifier = await checks.pkce.use(cookies, resCookies, options);\n    let redirect_uri = provider.callbackUrl;\n    if (!options.isOnRedirectProxy && provider.redirectProxyUrl) {\n        redirect_uri = provider.redirectProxyUrl;\n    }\n    let codeGrantResponse = await o.authorizationCodeGrantRequest(as, client, clientAuth, codeGrantParams, redirect_uri, codeVerifier ?? \"decoy\", {\n        // TODO: move away from allowing insecure HTTP requests\n        [o.allowInsecureRequests]: true,\n        [o.customFetch]: (...args) => {\n            if (!provider.checks.includes(\"pkce\")) {\n                args[1].body.delete(\"code_verifier\");\n            }\n            return (provider[customFetch] ?? fetch)(...args);\n        },\n    });\n    if (provider.token?.conform) {\n        codeGrantResponse =\n            (await provider.token.conform(codeGrantResponse.clone())) ??\n                codeGrantResponse;\n    }\n    let profile = {};\n    const requireIdToken = isOIDCProvider(provider);\n    if (provider[conformInternal]) {\n        switch (provider.id) {\n            case \"microsoft-entra-id\":\n            case \"azure-ad\": {\n                /**\n                 * These providers return errors in the response body and\n                 * need the authorization server metadata to be re-processed\n                 * based on the `id_token`'s `tid` claim.\n                 * @see: https://learn.microsoft.com/en-us/entra/identity-platform/v2-oauth2-auth-code-flow#error-response-1\n                 */\n                const responseJson = await codeGrantResponse.clone().json();\n                if (responseJson.error) {\n                    const cause = {\n                        providerId: provider.id,\n                        ...responseJson,\n                    };\n                    throw new OAuthCallbackError(`OAuth Provider returned an error: ${responseJson.error}`, cause);\n                }\n                const { tid } = decodeJwt(responseJson.id_token);\n                if (typeof tid === \"string\") {\n                    const tenantRe = /microsoftonline\\.com\\/(\\w+)\\/v2\\.0/;\n                    const tenantId = as.issuer?.match(tenantRe)?.[1] ?? \"common\";\n                    const issuer = new URL(as.issuer.replace(tenantId, tid));\n                    const discoveryResponse = await o.discoveryRequest(issuer, {\n                        [o.customFetch]: provider[customFetch],\n                    });\n                    as = await o.processDiscoveryResponse(issuer, discoveryResponse);\n                }\n                break;\n            }\n            default:\n                break;\n        }\n    }\n    const processedCodeResponse = await o.processAuthorizationCodeResponse(as, client, codeGrantResponse, {\n        expectedNonce: await checks.nonce.use(cookies, resCookies, options),\n        requireIdToken,\n    });\n    const tokens = processedCodeResponse;\n    if (requireIdToken) {\n        const idTokenClaims = o.getValidatedIdTokenClaims(processedCodeResponse);\n        profile = idTokenClaims;\n        // Apple sends some of the user information in a `user` parameter as a stringified JSON.\n        // It also only does so the first time the user consents to share their information.\n        if (provider[conformInternal] && provider.id === \"apple\") {\n            try {\n                profile.user = JSON.parse(params?.user);\n            }\n            catch { }\n        }\n        if (provider.idToken === false) {\n            const userinfoResponse = await o.userInfoRequest(as, client, processedCodeResponse.access_token, {\n                [o.customFetch]: provider[customFetch],\n                // TODO: move away from allowing insecure HTTP requests\n                [o.allowInsecureRequests]: true,\n            });\n            profile = await o.processUserInfoResponse(as, client, idTokenClaims.sub, userinfoResponse);\n        }\n    }\n    else {\n        if (userinfo?.request) {\n            const _profile = await userinfo.request({ tokens, provider });\n            if (_profile instanceof Object)\n                profile = _profile;\n        }\n        else if (userinfo?.url) {\n            const userinfoResponse = await o.userInfoRequest(as, client, processedCodeResponse.access_token, {\n                [o.customFetch]: provider[customFetch],\n                // TODO: move away from allowing insecure HTTP requests\n                [o.allowInsecureRequests]: true,\n            });\n            profile = await userinfoResponse.json();\n        }\n        else {\n            throw new TypeError(\"No userinfo endpoint configured\");\n        }\n    }\n    if (tokens.expires_in) {\n        tokens.expires_at =\n            Math.floor(Date.now() / 1000) + Number(tokens.expires_in);\n    }\n    const profileResult = await getUserAndAccount(profile, provider, tokens, logger);\n    return { ...profileResult, profile, cookies: resCookies };\n}\n/**\n * Returns the user and account that is going to be created in the database.\n * @internal\n */\nexport async function getUserAndAccount(OAuthProfile, provider, tokens, logger) {\n    try {\n        const userFromProfile = await provider.profile(OAuthProfile, tokens);\n        const user = {\n            ...userFromProfile,\n            // The user's id is intentionally not set based on the profile id, as\n            // the user should remain independent of the provider and the profile id\n            // is saved on the Account already, as `providerAccountId`.\n            id: crypto.randomUUID(),\n            email: userFromProfile.email?.toLowerCase(),\n        };\n        return {\n            user,\n            account: {\n                ...tokens,\n                provider: provider.id,\n                type: provider.type,\n                providerAccountId: userFromProfile.id ?? crypto.randomUUID(),\n            },\n        };\n    }\n    catch (e) {\n        // If we didn't get a response either there was a problem with the provider\n        // response *or* the user cancelled the action with the provider.\n        //\n        // Unfortunately, we can't tell which - at least not in a way that works for\n        // all providers, so we return an empty object; the user should then be\n        // redirected back to the sign up page. We log the error to help developers\n        // who might be trying to debug this when configuring a new provider.\n        logger.debug(\"getProfile error details\", OAuthProfile);\n        logger.error(new OAuthProfileParseError(e, { provider: provider.id }));\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,SAAS,cAAc,KAAK;IACxB,OAAO,mBAAmB,OAAO,OAAO,CAAC,QAAQ;AACrD;AACA;;;CAGC,GACD,SAAS,kBAAkB,QAAQ,EAAE,YAAY;IAC7C,MAAM,WAAW,cAAc;IAC/B,MAAM,WAAW,cAAc;IAC/B,MAAM,cAAc,KAAK,GAAG,SAAS,CAAC,EAAE,UAAU;IAClD,OAAO,CAAC,MAAM,EAAE,aAAa;AACjC;AAUO,eAAe,YAAY,MAAM,EAAE,OAAO,EAAE,OAAO;IACtD,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG;IAC7B,IAAI;IACJ,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;IAC5B,0DAA0D;IAC1D,IAAI,CAAC,CAAC,OAAO,OAAO,MAAM,GAAG,CAAC,IAAI,KAAK,YAAY,KAC/C,CAAC,CAAC,UAAU,OAAO,SAAS,GAAG,CAAC,IAAI,KAAK,YAAY,GAAG;QACxD,4EAA4E;QAC5E,MAAM,SAAS,IAAI,IAAI,SAAS,MAAM;QACtC,MAAM,oBAAoB,MAAM,CAAA,GAAA,+MAAA,CAAA,mBAAkB,AAAD,EAAE,QAAQ;YACvD,CAAC,+MAAA,CAAA,wBAAuB,CAAC,EAAE;YAC3B,CAAC,+MAAA,CAAA,cAAa,CAAC,EAAE,QAAQ,CAAC,oNAAA,CAAA,cAAW,CAAC;QAC1C;QACA,KAAK,MAAM,CAAA,GAAA,+MAAA,CAAA,2BAA0B,AAAD,EAAE,QAAQ;QAC9C,IAAI,CAAC,GAAG,cAAc,EAClB,MAAM,IAAI,UAAU;QACxB,IAAI,CAAC,GAAG,iBAAiB,EACrB,MAAM,IAAI,UAAU;IAC5B,OACK;QACD,KAAK;YACD,QAAQ,SAAS,MAAM,IAAI;YAC3B,gBAAgB,OAAO,IAAI;YAC3B,mBAAmB,UAAU,IAAI;QACrC;IACJ;IACA,MAAM,SAAS;QACX,WAAW,SAAS,QAAQ;QAC5B,GAAG,SAAS,MAAM;IACtB;IACA,IAAI;IACJ,OAAQ,OAAO,0BAA0B;QACrC,kFAAkF;QAClF,KAAK;QACL,KAAK;YACD,0EAA0E;YAC1E,aAAa,CAAC,KAAK,SAAS,OAAO;gBAC/B,QAAQ,GAAG,CAAC,iBAAiB,kBAAkB,SAAS,QAAQ,EAAE,SAAS,YAAY;YAC3F;YACA;QACJ,KAAK;YACD,aAAa,CAAA,GAAA,+MAAA,CAAA,mBAAkB,AAAD,EAAE,SAAS,YAAY;YACrD;QACJ,KAAK;YACD,aAAa,CAAA,GAAA,+MAAA,CAAA,kBAAiB,AAAD,EAAE,SAAS,YAAY;YACpD;QACJ,KAAK;YACD,aAAa,CAAA,GAAA,+MAAA,CAAA,gBAAe,AAAD,EAAE,SAAS,KAAK,CAAC,gBAAgB,EAAE;gBAC1D,2CAA2C;gBAC3C,CAAC,+MAAA,CAAA,kBAAiB,CAAC,EAAC,OAAO,EAAE,OAAO;oBAChC,QAAQ,GAAG,GAAG;wBAAC,GAAG,MAAM;wBAAE,GAAG,cAAc;qBAAC;gBAChD;YACJ;YACA;QACJ,KAAK;YACD,aAAa,CAAA,GAAA,+MAAA,CAAA,OAAM,AAAD;YAClB;QACJ;YACI,MAAM,IAAI,MAAM;IACxB;IACA,MAAM,aAAa,EAAE;IACrB,MAAM,QAAQ,MAAM,mPAAA,CAAA,QAAY,CAAC,GAAG,CAAC,SAAS,YAAY;IAC1D,IAAI;IACJ,IAAI;QACA,kBAAkB,CAAA,GAAA,+MAAA,CAAA,uBAAsB,AAAD,EAAE,IAAI,QAAQ,IAAI,gBAAgB,SAAS,SAAS,MAAM,CAAC,QAAQ,CAAC,WAAW,QAAQ,+MAAA,CAAA,iBAAgB;IAClJ,EACA,OAAO,KAAK;QACR,IAAI,eAAe,+MAAA,CAAA,6BAA4B,EAAE;YAC7C,MAAM,QAAQ;gBACV,YAAY,SAAS,EAAE;gBACvB,GAAG,OAAO,WAAW,CAAC,IAAI,KAAK,CAAC,OAAO,GAAG;YAC9C;YACA,OAAO,KAAK,CAAC,sBAAsB;YACnC,MAAM,IAAI,4MAAA,CAAA,qBAAkB,CAAC,oCAAoC;QACrE;QACA,MAAM;IACV;IACA,MAAM,eAAe,MAAM,mPAAA,CAAA,OAAW,CAAC,GAAG,CAAC,SAAS,YAAY;IAChE,IAAI,eAAe,SAAS,WAAW;IACvC,IAAI,CAAC,QAAQ,iBAAiB,IAAI,SAAS,gBAAgB,EAAE;QACzD,eAAe,SAAS,gBAAgB;IAC5C;IACA,IAAI,oBAAoB,MAAM,CAAA,GAAA,+MAAA,CAAA,gCAA+B,AAAD,EAAE,IAAI,QAAQ,YAAY,iBAAiB,cAAc,gBAAgB,SAAS;QAC1I,uDAAuD;QACvD,CAAC,+MAAA,CAAA,wBAAuB,CAAC,EAAE;QAC3B,CAAC,+MAAA,CAAA,cAAa,CAAC,EAAE,CAAC,GAAG;YACjB,IAAI,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS;gBACnC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;YACxB;YACA,OAAO,CAAC,QAAQ,CAAC,oNAAA,CAAA,cAAW,CAAC,IAAI,KAAK,KAAK;QAC/C;IACJ;IACA,IAAI,SAAS,KAAK,EAAE,SAAS;QACzB,oBACI,AAAC,MAAM,SAAS,KAAK,CAAC,OAAO,CAAC,kBAAkB,KAAK,OACjD;IACZ;IACA,IAAI,UAAU,CAAC;IACf,MAAM,iBAAiB,CAAA,GAAA,+NAAA,CAAA,iBAAc,AAAD,EAAE;IACtC,IAAI,QAAQ,CAAC,oNAAA,CAAA,kBAAe,CAAC,EAAE;QAC3B,OAAQ,SAAS,EAAE;YACf,KAAK;YACL,KAAK;gBAAY;oBACb;;;;;iBAKC,GACD,MAAM,eAAe,MAAM,kBAAkB,KAAK,GAAG,IAAI;oBACzD,IAAI,aAAa,KAAK,EAAE;wBACpB,MAAM,QAAQ;4BACV,YAAY,SAAS,EAAE;4BACvB,GAAG,YAAY;wBACnB;wBACA,MAAM,IAAI,4MAAA,CAAA,qBAAkB,CAAC,CAAC,kCAAkC,EAAE,aAAa,KAAK,EAAE,EAAE;oBAC5F;oBACA,MAAM,EAAE,GAAG,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD,EAAE,aAAa,QAAQ;oBAC/C,IAAI,OAAO,QAAQ,UAAU;wBACzB,MAAM,WAAW;wBACjB,MAAM,WAAW,GAAG,MAAM,EAAE,MAAM,WAAW,CAAC,EAAE,IAAI;wBACpD,MAAM,SAAS,IAAI,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU;wBACnD,MAAM,oBAAoB,MAAM,CAAA,GAAA,+MAAA,CAAA,mBAAkB,AAAD,EAAE,QAAQ;4BACvD,CAAC,+MAAA,CAAA,cAAa,CAAC,EAAE,QAAQ,CAAC,oNAAA,CAAA,cAAW,CAAC;wBAC1C;wBACA,KAAK,MAAM,CAAA,GAAA,+MAAA,CAAA,2BAA0B,AAAD,EAAE,QAAQ;oBAClD;oBACA;gBACJ;YACA;gBACI;QACR;IACJ;IACA,MAAM,wBAAwB,MAAM,CAAA,GAAA,+MAAA,CAAA,mCAAkC,AAAD,EAAE,IAAI,QAAQ,mBAAmB;QAClG,eAAe,MAAM,mPAAA,CAAA,QAAY,CAAC,GAAG,CAAC,SAAS,YAAY;QAC3D;IACJ;IACA,MAAM,SAAS;IACf,IAAI,gBAAgB;QAChB,MAAM,gBAAgB,CAAA,GAAA,+MAAA,CAAA,4BAA2B,AAAD,EAAE;QAClD,UAAU;QACV,wFAAwF;QACxF,oFAAoF;QACpF,IAAI,QAAQ,CAAC,oNAAA,CAAA,kBAAe,CAAC,IAAI,SAAS,EAAE,KAAK,SAAS;YACtD,IAAI;gBACA,QAAQ,IAAI,GAAG,KAAK,KAAK,CAAC,QAAQ;YACtC,EACA,OAAM,CAAE;QACZ;QACA,IAAI,SAAS,OAAO,KAAK,OAAO;YAC5B,MAAM,mBAAmB,MAAM,CAAA,GAAA,+MAAA,CAAA,kBAAiB,AAAD,EAAE,IAAI,QAAQ,sBAAsB,YAAY,EAAE;gBAC7F,CAAC,+MAAA,CAAA,cAAa,CAAC,EAAE,QAAQ,CAAC,oNAAA,CAAA,cAAW,CAAC;gBACtC,uDAAuD;gBACvD,CAAC,+MAAA,CAAA,wBAAuB,CAAC,EAAE;YAC/B;YACA,UAAU,MAAM,CAAA,GAAA,+MAAA,CAAA,0BAAyB,AAAD,EAAE,IAAI,QAAQ,cAAc,GAAG,EAAE;QAC7E;IACJ,OACK;QACD,IAAI,UAAU,SAAS;YACnB,MAAM,WAAW,MAAM,SAAS,OAAO,CAAC;gBAAE;gBAAQ;YAAS;YAC3D,IAAI,oBAAoB,QACpB,UAAU;QAClB,OACK,IAAI,UAAU,KAAK;YACpB,MAAM,mBAAmB,MAAM,CAAA,GAAA,+MAAA,CAAA,kBAAiB,AAAD,EAAE,IAAI,QAAQ,sBAAsB,YAAY,EAAE;gBAC7F,CAAC,+MAAA,CAAA,cAAa,CAAC,EAAE,QAAQ,CAAC,oNAAA,CAAA,cAAW,CAAC;gBACtC,uDAAuD;gBACvD,CAAC,+MAAA,CAAA,wBAAuB,CAAC,EAAE;YAC/B;YACA,UAAU,MAAM,iBAAiB,IAAI;QACzC,OACK;YACD,MAAM,IAAI,UAAU;QACxB;IACJ;IACA,IAAI,OAAO,UAAU,EAAE;QACnB,OAAO,UAAU,GACb,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,QAAQ,OAAO,OAAO,UAAU;IAChE;IACA,MAAM,gBAAgB,MAAM,kBAAkB,SAAS,UAAU,QAAQ;IACzE,OAAO;QAAE,GAAG,aAAa;QAAE;QAAS,SAAS;IAAW;AAC5D;AAKO,eAAe,kBAAkB,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM;IAC1E,IAAI;QACA,MAAM,kBAAkB,MAAM,SAAS,OAAO,CAAC,cAAc;QAC7D,MAAM,OAAO;YACT,GAAG,eAAe;YAClB,qEAAqE;YACrE,wEAAwE;YACxE,2DAA2D;YAC3D,IAAI,OAAO,UAAU;YACrB,OAAO,gBAAgB,KAAK,EAAE;QAClC;QACA,OAAO;YACH;YACA,SAAS;gBACL,GAAG,MAAM;gBACT,UAAU,SAAS,EAAE;gBACrB,MAAM,SAAS,IAAI;gBACnB,mBAAmB,gBAAgB,EAAE,IAAI,OAAO,UAAU;YAC9D;QACJ;IACJ,EACA,OAAO,GAAG;QACN,2EAA2E;QAC3E,iEAAiE;QACjE,EAAE;QACF,4EAA4E;QAC5E,uEAAuE;QACvE,2EAA2E;QAC3E,qEAAqE;QACrE,OAAO,KAAK,CAAC,4BAA4B;QACzC,OAAO,KAAK,CAAC,IAAI,4MAAA,CAAA,yBAAsB,CAAC,GAAG;YAAE,UAAU,SAAS,EAAE;QAAC;IACvE;AACJ", "ignoreList": [0]}}, {"offset": {"line": 3689, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/utils/webauthn-utils.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MissingAdapter, WebAuthnVerificationError, } from \"../../errors.js\";\nimport { webauthnChallenge } from \"../actions/callback/oauth/checks.js\";\nimport { randomString } from \"./web.js\";\n/**\n * Infers the WebAuthn options based on the provided parameters.\n *\n * @param action - The WebAuthn action to perform (optional).\n * @param loggedInUser - The logged-in user (optional).\n * @param userInfoResponse - The response containing user information (optional).\n *\n * @returns The WebAuthn action to perform, or null if no inference could be made.\n */\nexport function inferWebAuthnOptions(action, loggedIn, userInfoResponse) {\n    const { user, exists = false } = userInfoResponse ?? {};\n    switch (action) {\n        case \"authenticate\": {\n            /**\n             * Always allow explicit authentication requests.\n             */\n            return \"authenticate\";\n        }\n        case \"register\": {\n            /**\n             * Registration is only allowed if:\n             * - The user is logged in, meaning the user wants to register a new authenticator.\n             * - The user is not logged in and provided user info that does NOT exist, meaning the user wants to register a new account.\n             */\n            if (user && loggedIn === exists)\n                return \"register\";\n            break;\n        }\n        case undefined: {\n            /**\n             * When no explicit action is provided, we try to infer it based on the user info provided. These are the possible cases:\n             * - Logged in users must always send an explit action, so we bail out in this case.\n             * - Otherwise, if no user info is provided, the desired action is authentication without pre-defined authenticators.\n             * - Otherwise, if the user info provided is of an existing user, the desired action is authentication with their pre-defined authenticators.\n             * - Finally, if the user info provided is of a non-existing user, the desired action is registration.\n             */\n            if (!loggedIn) {\n                if (user) {\n                    if (exists) {\n                        return \"authenticate\";\n                    }\n                    else {\n                        return \"register\";\n                    }\n                }\n                else {\n                    return \"authenticate\";\n                }\n            }\n            break;\n        }\n    }\n    // No decision could be made\n    return null;\n}\n/**\n * Retrieves the registration response for WebAuthn options request.\n *\n * @param options - The internal options for WebAuthn.\n * @param request - The request object.\n * @param user - The user information.\n * @param resCookies - Optional cookies to be included in the response.\n * @returns A promise that resolves to the WebAuthnOptionsResponse.\n */\nexport async function getRegistrationResponse(options, request, user, resCookies) {\n    // Get registration options\n    const regOptions = await getRegistrationOptions(options, request, user);\n    // Get signed cookie\n    const { cookie } = await webauthnChallenge.create(options, regOptions.challenge, user);\n    return {\n        status: 200,\n        cookies: [...(resCookies ?? []), cookie],\n        body: {\n            action: \"register\",\n            options: regOptions,\n        },\n        headers: {\n            \"Content-Type\": \"application/json\",\n        },\n    };\n}\n/**\n * Retrieves the authentication response for WebAuthn options request.\n *\n * @param options - The internal options for WebAuthn.\n * @param request - The request object.\n * @param user - Optional user information.\n * @param resCookies - Optional array of cookies to be included in the response.\n * @returns A promise that resolves to a WebAuthnOptionsResponse object.\n */\nexport async function getAuthenticationResponse(options, request, user, resCookies) {\n    // Get authentication options\n    const authOptions = await getAuthenticationOptions(options, request, user);\n    // Get signed cookie\n    const { cookie } = await webauthnChallenge.create(options, authOptions.challenge);\n    return {\n        status: 200,\n        cookies: [...(resCookies ?? []), cookie],\n        body: {\n            action: \"authenticate\",\n            options: authOptions,\n        },\n        headers: {\n            \"Content-Type\": \"application/json\",\n        },\n    };\n}\nexport async function verifyAuthenticate(options, request, resCookies) {\n    const { adapter, provider } = options;\n    // Get WebAuthn response from request body\n    const data = request.body && typeof request.body.data === \"string\"\n        ? JSON.parse(request.body.data)\n        : undefined;\n    if (!data ||\n        typeof data !== \"object\" ||\n        !(\"id\" in data) ||\n        typeof data.id !== \"string\") {\n        throw new AuthError(\"Invalid WebAuthn Authentication response\");\n    }\n    // Reset the ID so we smooth out implementation differences\n    const credentialID = toBase64(fromBase64(data.id));\n    // Get authenticator from database\n    const authenticator = await adapter.getAuthenticator(credentialID);\n    if (!authenticator) {\n        throw new AuthError(`WebAuthn authenticator not found in database: ${JSON.stringify({\n            credentialID,\n        })}`);\n    }\n    // Get challenge from request cookies\n    const { challenge: expectedChallenge } = await webauthnChallenge.use(options, request.cookies, resCookies);\n    // Verify the response\n    let verification;\n    try {\n        const relayingParty = provider.getRelayingParty(options, request);\n        verification = await provider.simpleWebAuthn.verifyAuthenticationResponse({\n            ...provider.verifyAuthenticationOptions,\n            expectedChallenge,\n            response: data,\n            authenticator: fromAdapterAuthenticator(authenticator),\n            expectedOrigin: relayingParty.origin,\n            expectedRPID: relayingParty.id,\n        });\n    }\n    catch (e) {\n        throw new WebAuthnVerificationError(e);\n    }\n    const { verified, authenticationInfo } = verification;\n    // Make sure the response was verified\n    if (!verified) {\n        throw new WebAuthnVerificationError(\"WebAuthn authentication response could not be verified\");\n    }\n    // Update authenticator counter\n    try {\n        const { newCounter } = authenticationInfo;\n        await adapter.updateAuthenticatorCounter(authenticator.credentialID, newCounter);\n    }\n    catch (e) {\n        throw new AdapterError(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({\n            credentialID,\n            oldCounter: authenticator.counter,\n            newCounter: authenticationInfo.newCounter,\n        })}`, e);\n    }\n    // Get the account and user\n    const account = await adapter.getAccount(authenticator.providerAccountId, provider.id);\n    if (!account) {\n        throw new AuthError(`WebAuthn account not found in database: ${JSON.stringify({\n            credentialID,\n            providerAccountId: authenticator.providerAccountId,\n        })}`);\n    }\n    const user = await adapter.getUser(account.userId);\n    if (!user) {\n        throw new AuthError(`WebAuthn user not found in database: ${JSON.stringify({\n            credentialID,\n            providerAccountId: authenticator.providerAccountId,\n            userID: account.userId,\n        })}`);\n    }\n    return {\n        account,\n        user,\n    };\n}\nexport async function verifyRegister(options, request, resCookies) {\n    const { provider } = options;\n    // Get WebAuthn response from request body\n    const data = request.body && typeof request.body.data === \"string\"\n        ? JSON.parse(request.body.data)\n        : undefined;\n    if (!data ||\n        typeof data !== \"object\" ||\n        !(\"id\" in data) ||\n        typeof data.id !== \"string\") {\n        throw new AuthError(\"Invalid WebAuthn Registration response\");\n    }\n    // Get challenge from request cookies\n    const { challenge: expectedChallenge, registerData: user } = await webauthnChallenge.use(options, request.cookies, resCookies);\n    if (!user) {\n        throw new AuthError(\"Missing user registration data in WebAuthn challenge cookie\");\n    }\n    // Verify the response\n    let verification;\n    try {\n        const relayingParty = provider.getRelayingParty(options, request);\n        verification = await provider.simpleWebAuthn.verifyRegistrationResponse({\n            ...provider.verifyRegistrationOptions,\n            expectedChallenge,\n            response: data,\n            expectedOrigin: relayingParty.origin,\n            expectedRPID: relayingParty.id,\n        });\n    }\n    catch (e) {\n        throw new WebAuthnVerificationError(e);\n    }\n    // Make sure the response was verified\n    if (!verification.verified || !verification.registrationInfo) {\n        throw new WebAuthnVerificationError(\"WebAuthn registration response could not be verified\");\n    }\n    // Build a new account\n    const account = {\n        providerAccountId: toBase64(verification.registrationInfo.credentialID),\n        provider: options.provider.id,\n        type: provider.type,\n    };\n    // Build a new authenticator\n    const authenticator = {\n        providerAccountId: account.providerAccountId,\n        counter: verification.registrationInfo.counter,\n        credentialID: toBase64(verification.registrationInfo.credentialID),\n        credentialPublicKey: toBase64(verification.registrationInfo.credentialPublicKey),\n        credentialBackedUp: verification.registrationInfo.credentialBackedUp,\n        credentialDeviceType: verification.registrationInfo.credentialDeviceType,\n        transports: transportsToString(data.response\n            .transports),\n    };\n    // Return created stuff\n    return {\n        user,\n        account,\n        authenticator,\n    };\n}\n/**\n * Generates WebAuthn authentication options.\n *\n * @param options - The internal options for WebAuthn.\n * @param request - The request object.\n * @param user - Optional user information.\n * @returns The authentication options.\n */\nasync function getAuthenticationOptions(options, request, user) {\n    const { provider, adapter } = options;\n    // Get the user's authenticators.\n    const authenticators = user && user[\"id\"]\n        ? await adapter.listAuthenticatorsByUserId(user.id)\n        : null;\n    const relayingParty = provider.getRelayingParty(options, request);\n    // Return the authentication options.\n    return await provider.simpleWebAuthn.generateAuthenticationOptions({\n        ...provider.authenticationOptions,\n        rpID: relayingParty.id,\n        allowCredentials: authenticators?.map((a) => ({\n            id: fromBase64(a.credentialID),\n            type: \"public-key\",\n            transports: stringToTransports(a.transports),\n        })),\n    });\n}\n/**\n * Generates WebAuthn registration options.\n *\n * @param options - The internal options for WebAuthn.\n * @param request - The request object.\n * @param user - The user information.\n * @returns The registration options.\n */\nasync function getRegistrationOptions(options, request, user) {\n    const { provider, adapter } = options;\n    // Get the user's authenticators.\n    const authenticators = user[\"id\"]\n        ? await adapter.listAuthenticatorsByUserId(user.id)\n        : null;\n    // Generate a random user ID for the credential.\n    // We can do this because we don't use this user ID to link the\n    // credential to the user. Instead, we store actual userID in the\n    // Authenticator object and fetch it via it's credential ID.\n    const userID = randomString(32);\n    const relayingParty = provider.getRelayingParty(options, request);\n    // Return the registration options.\n    return await provider.simpleWebAuthn.generateRegistrationOptions({\n        ...provider.registrationOptions,\n        userID,\n        userName: user.email,\n        userDisplayName: user.name ?? undefined,\n        rpID: relayingParty.id,\n        rpName: relayingParty.name,\n        excludeCredentials: authenticators?.map((a) => ({\n            id: fromBase64(a.credentialID),\n            type: \"public-key\",\n            transports: stringToTransports(a.transports),\n        })),\n    });\n}\nexport function assertInternalOptionsWebAuthn(options) {\n    const { provider, adapter } = options;\n    // Adapter is required for WebAuthn\n    if (!adapter)\n        throw new MissingAdapter(\"An adapter is required for the WebAuthn provider\");\n    // Provider must be WebAuthn\n    if (!provider || provider.type !== \"webauthn\") {\n        throw new InvalidProvider(\"Provider must be WebAuthn\");\n    }\n    // Narrow the options type for typed usage later\n    return { ...options, provider, adapter };\n}\nfunction fromAdapterAuthenticator(authenticator) {\n    return {\n        ...authenticator,\n        credentialDeviceType: authenticator.credentialDeviceType,\n        transports: stringToTransports(authenticator.transports),\n        credentialID: fromBase64(authenticator.credentialID),\n        credentialPublicKey: fromBase64(authenticator.credentialPublicKey),\n    };\n}\nexport function fromBase64(base64) {\n    return new Uint8Array(Buffer.from(base64, \"base64\"));\n}\nexport function toBase64(bytes) {\n    return Buffer.from(bytes).toString(\"base64\");\n}\nexport function transportsToString(transports) {\n    return transports?.join(\",\");\n}\nexport function stringToTransports(tstring) {\n    return tstring\n        ? tstring.split(\",\")\n        : undefined;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AA0U0B;AA1U1B;AACA;AACA;;;;AAUO,SAAS,qBAAqB,MAAM,EAAE,QAAQ,EAAE,gBAAgB;IACnE,MAAM,EAAE,IAAI,EAAE,SAAS,KAAK,EAAE,GAAG,oBAAoB,CAAC;IACtD,OAAQ;QACJ,KAAK;YAAgB;gBACjB;;aAEC,GACD,OAAO;YACX;QACA,KAAK;YAAY;gBACb;;;;aAIC,GACD,IAAI,QAAQ,aAAa,QACrB,OAAO;gBACX;YACJ;QACA,KAAK;YAAW;gBACZ;;;;;;aAMC,GACD,IAAI,CAAC,UAAU;oBACX,IAAI,MAAM;wBACN,IAAI,QAAQ;4BACR,OAAO;wBACX,OACK;4BACD,OAAO;wBACX;oBACJ,OACK;wBACD,OAAO;oBACX;gBACJ;gBACA;YACJ;IACJ;IACA,4BAA4B;IAC5B,OAAO;AACX;AAUO,eAAe,wBAAwB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU;IAC5E,2BAA2B;IAC3B,MAAM,aAAa,MAAM,uBAAuB,SAAS,SAAS;IAClE,oBAAoB;IACpB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,mPAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC,SAAS,WAAW,SAAS,EAAE;IACjF,OAAO;QACH,QAAQ;QACR,SAAS;eAAK,cAAc,EAAE;YAAG;SAAO;QACxC,MAAM;YACF,QAAQ;YACR,SAAS;QACb;QACA,SAAS;YACL,gBAAgB;QACpB;IACJ;AACJ;AAUO,eAAe,0BAA0B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU;IAC9E,6BAA6B;IAC7B,MAAM,cAAc,MAAM,yBAAyB,SAAS,SAAS;IACrE,oBAAoB;IACpB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,mPAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC,SAAS,YAAY,SAAS;IAChF,OAAO;QACH,QAAQ;QACR,SAAS;eAAK,cAAc,EAAE;YAAG;SAAO;QACxC,MAAM;YACF,QAAQ;YACR,SAAS;QACb;QACA,SAAS;YACL,gBAAgB;QACpB;IACJ;AACJ;AACO,eAAe,mBAAmB,OAAO,EAAE,OAAO,EAAE,UAAU;IACjE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG;IAC9B,0CAA0C;IAC1C,MAAM,OAAO,QAAQ,IAAI,IAAI,OAAO,QAAQ,IAAI,CAAC,IAAI,KAAK,WACpD,KAAK,KAAK,CAAC,QAAQ,IAAI,CAAC,IAAI,IAC5B;IACN,IAAI,CAAC,QACD,OAAO,SAAS,YAChB,CAAC,CAAC,QAAQ,IAAI,KACd,OAAO,KAAK,EAAE,KAAK,UAAU;QAC7B,MAAM,IAAI,4MAAA,CAAA,YAAS,CAAC;IACxB;IACA,2DAA2D;IAC3D,MAAM,eAAe,SAAS,WAAW,KAAK,EAAE;IAChD,kCAAkC;IAClC,MAAM,gBAAgB,MAAM,QAAQ,gBAAgB,CAAC;IACrD,IAAI,CAAC,eAAe;QAChB,MAAM,IAAI,4MAAA,CAAA,YAAS,CAAC,CAAC,8CAA8C,EAAE,KAAK,SAAS,CAAC;YAChF;QACJ,IAAI;IACR;IACA,qCAAqC;IACrC,MAAM,EAAE,WAAW,iBAAiB,EAAE,GAAG,MAAM,mPAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,SAAS,QAAQ,OAAO,EAAE;IAC/F,sBAAsB;IACtB,IAAI;IACJ,IAAI;QACA,MAAM,gBAAgB,SAAS,gBAAgB,CAAC,SAAS;QACzD,eAAe,MAAM,SAAS,cAAc,CAAC,4BAA4B,CAAC;YACtE,GAAG,SAAS,2BAA2B;YACvC;YACA,UAAU;YACV,eAAe,yBAAyB;YACxC,gBAAgB,cAAc,MAAM;YACpC,cAAc,cAAc,EAAE;QAClC;IACJ,EACA,OAAO,GAAG;QACN,MAAM,IAAI,4MAAA,CAAA,4BAAyB,CAAC;IACxC;IACA,MAAM,EAAE,QAAQ,EAAE,kBAAkB,EAAE,GAAG;IACzC,sCAAsC;IACtC,IAAI,CAAC,UAAU;QACX,MAAM,IAAI,4MAAA,CAAA,4BAAyB,CAAC;IACxC;IACA,+BAA+B;IAC/B,IAAI;QACA,MAAM,EAAE,UAAU,EAAE,GAAG;QACvB,MAAM,QAAQ,0BAA0B,CAAC,cAAc,YAAY,EAAE;IACzE,EACA,OAAO,GAAG;QACN,MAAM,IAAI,4MAAA,CAAA,eAAY,CAAC,CAAC,+FAA+F,EAAE,KAAK,SAAS,CAAC;YACpI;YACA,YAAY,cAAc,OAAO;YACjC,YAAY,mBAAmB,UAAU;QAC7C,IAAI,EAAE;IACV;IACA,2BAA2B;IAC3B,MAAM,UAAU,MAAM,QAAQ,UAAU,CAAC,cAAc,iBAAiB,EAAE,SAAS,EAAE;IACrF,IAAI,CAAC,SAAS;QACV,MAAM,IAAI,4MAAA,CAAA,YAAS,CAAC,CAAC,wCAAwC,EAAE,KAAK,SAAS,CAAC;YAC1E;YACA,mBAAmB,cAAc,iBAAiB;QACtD,IAAI;IACR;IACA,MAAM,OAAO,MAAM,QAAQ,OAAO,CAAC,QAAQ,MAAM;IACjD,IAAI,CAAC,MAAM;QACP,MAAM,IAAI,4MAAA,CAAA,YAAS,CAAC,CAAC,qCAAqC,EAAE,KAAK,SAAS,CAAC;YACvE;YACA,mBAAmB,cAAc,iBAAiB;YAClD,QAAQ,QAAQ,MAAM;QAC1B,IAAI;IACR;IACA,OAAO;QACH;QACA;IACJ;AACJ;AACO,eAAe,eAAe,OAAO,EAAE,OAAO,EAAE,UAAU;IAC7D,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,0CAA0C;IAC1C,MAAM,OAAO,QAAQ,IAAI,IAAI,OAAO,QAAQ,IAAI,CAAC,IAAI,KAAK,WACpD,KAAK,KAAK,CAAC,QAAQ,IAAI,CAAC,IAAI,IAC5B;IACN,IAAI,CAAC,QACD,OAAO,SAAS,YAChB,CAAC,CAAC,QAAQ,IAAI,KACd,OAAO,KAAK,EAAE,KAAK,UAAU;QAC7B,MAAM,IAAI,4MAAA,CAAA,YAAS,CAAC;IACxB;IACA,qCAAqC;IACrC,MAAM,EAAE,WAAW,iBAAiB,EAAE,cAAc,IAAI,EAAE,GAAG,MAAM,mPAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,SAAS,QAAQ,OAAO,EAAE;IACnH,IAAI,CAAC,MAAM;QACP,MAAM,IAAI,4MAAA,CAAA,YAAS,CAAC;IACxB;IACA,sBAAsB;IACtB,IAAI;IACJ,IAAI;QACA,MAAM,gBAAgB,SAAS,gBAAgB,CAAC,SAAS;QACzD,eAAe,MAAM,SAAS,cAAc,CAAC,0BAA0B,CAAC;YACpE,GAAG,SAAS,yBAAyB;YACrC;YACA,UAAU;YACV,gBAAgB,cAAc,MAAM;YACpC,cAAc,cAAc,EAAE;QAClC;IACJ,EACA,OAAO,GAAG;QACN,MAAM,IAAI,4MAAA,CAAA,4BAAyB,CAAC;IACxC;IACA,sCAAsC;IACtC,IAAI,CAAC,aAAa,QAAQ,IAAI,CAAC,aAAa,gBAAgB,EAAE;QAC1D,MAAM,IAAI,4MAAA,CAAA,4BAAyB,CAAC;IACxC;IACA,sBAAsB;IACtB,MAAM,UAAU;QACZ,mBAAmB,SAAS,aAAa,gBAAgB,CAAC,YAAY;QACtE,UAAU,QAAQ,QAAQ,CAAC,EAAE;QAC7B,MAAM,SAAS,IAAI;IACvB;IACA,4BAA4B;IAC5B,MAAM,gBAAgB;QAClB,mBAAmB,QAAQ,iBAAiB;QAC5C,SAAS,aAAa,gBAAgB,CAAC,OAAO;QAC9C,cAAc,SAAS,aAAa,gBAAgB,CAAC,YAAY;QACjE,qBAAqB,SAAS,aAAa,gBAAgB,CAAC,mBAAmB;QAC/E,oBAAoB,aAAa,gBAAgB,CAAC,kBAAkB;QACpE,sBAAsB,aAAa,gBAAgB,CAAC,oBAAoB;QACxE,YAAY,mBAAmB,KAAK,QAAQ,CACvC,UAAU;IACnB;IACA,uBAAuB;IACvB,OAAO;QACH;QACA;QACA;IACJ;AACJ;AACA;;;;;;;CAOC,GACD,eAAe,yBAAyB,OAAO,EAAE,OAAO,EAAE,IAAI;IAC1D,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;IAC9B,iCAAiC;IACjC,MAAM,iBAAiB,QAAQ,IAAI,CAAC,KAAK,GACnC,MAAM,QAAQ,0BAA0B,CAAC,KAAK,EAAE,IAChD;IACN,MAAM,gBAAgB,SAAS,gBAAgB,CAAC,SAAS;IACzD,qCAAqC;IACrC,OAAO,MAAM,SAAS,cAAc,CAAC,6BAA6B,CAAC;QAC/D,GAAG,SAAS,qBAAqB;QACjC,MAAM,cAAc,EAAE;QACtB,kBAAkB,gBAAgB,IAAI,CAAC,IAAM,CAAC;gBAC1C,IAAI,WAAW,EAAE,YAAY;gBAC7B,MAAM;gBACN,YAAY,mBAAmB,EAAE,UAAU;YAC/C,CAAC;IACL;AACJ;AACA;;;;;;;CAOC,GACD,eAAe,uBAAuB,OAAO,EAAE,OAAO,EAAE,IAAI;IACxD,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;IAC9B,iCAAiC;IACjC,MAAM,iBAAiB,IAAI,CAAC,KAAK,GAC3B,MAAM,QAAQ,0BAA0B,CAAC,KAAK,EAAE,IAChD;IACN,gDAAgD;IAChD,+DAA+D;IAC/D,iEAAiE;IACjE,4DAA4D;IAC5D,MAAM,SAAS,CAAA,GAAA,yNAAA,CAAA,eAAY,AAAD,EAAE;IAC5B,MAAM,gBAAgB,SAAS,gBAAgB,CAAC,SAAS;IACzD,mCAAmC;IACnC,OAAO,MAAM,SAAS,cAAc,CAAC,2BAA2B,CAAC;QAC7D,GAAG,SAAS,mBAAmB;QAC/B;QACA,UAAU,KAAK,KAAK;QACpB,iBAAiB,KAAK,IAAI,IAAI;QAC9B,MAAM,cAAc,EAAE;QACtB,QAAQ,cAAc,IAAI;QAC1B,oBAAoB,gBAAgB,IAAI,CAAC,IAAM,CAAC;gBAC5C,IAAI,WAAW,EAAE,YAAY;gBAC7B,MAAM;gBACN,YAAY,mBAAmB,EAAE,UAAU;YAC/C,CAAC;IACL;AACJ;AACO,SAAS,8BAA8B,OAAO;IACjD,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;IAC9B,mCAAmC;IACnC,IAAI,CAAC,SACD,MAAM,IAAI,4MAAA,CAAA,iBAAc,CAAC;IAC7B,4BAA4B;IAC5B,IAAI,CAAC,YAAY,SAAS,IAAI,KAAK,YAAY;QAC3C,MAAM,IAAI,4MAAA,CAAA,kBAAe,CAAC;IAC9B;IACA,gDAAgD;IAChD,OAAO;QAAE,GAAG,OAAO;QAAE;QAAU;IAAQ;AAC3C;AACA,SAAS,yBAAyB,aAAa;IAC3C,OAAO;QACH,GAAG,aAAa;QAChB,sBAAsB,cAAc,oBAAoB;QACxD,YAAY,mBAAmB,cAAc,UAAU;QACvD,cAAc,WAAW,cAAc,YAAY;QACnD,qBAAqB,WAAW,cAAc,mBAAmB;IACrE;AACJ;AACO,SAAS,WAAW,MAAM;IAC7B,OAAO,IAAI,WAAW,qHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;AAC9C;AACO,SAAS,SAAS,KAAK;IAC1B,OAAO,qHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,OAAO,QAAQ,CAAC;AACvC;AACO,SAAS,mBAAmB,UAAU;IACzC,OAAO,YAAY,KAAK;AAC5B;AACO,SAAS,mBAAmB,OAAO;IACtC,OAAO,UACD,QAAQ,KAAK,CAAC,OACd;AACV", "ignoreList": [0]}}, {"offset": {"line": 4011, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/actions/callback/index.js"], "sourcesContent": ["// TODO: Make this file smaller\nimport { <PERSON>th<PERSON><PERSON><PERSON>, AccessDenied, CallbackRouteError, CredentialsSignin, InvalidProvider, Verification, } from \"../../../errors.js\";\nimport { handleLoginOrRegister } from \"./handle-login.js\";\nimport { handleOAuth } from \"./oauth/callback.js\";\nimport { state } from \"./oauth/checks.js\";\nimport { createHash } from \"../../utils/web.js\";\nimport { assertInternalOptionsWebAuthn, verifyAuthenticate, verifyRegister, } from \"../../utils/webauthn-utils.js\";\n/** Handle callbacks from login services */\nexport async function callback(request, options, sessionStore, cookies) {\n    if (!options.provider)\n        throw new InvalidProvider(\"Callback route called without provider\");\n    const { query, body, method, headers } = request;\n    const { provider, adapter, url, callbackUrl, pages, jwt, events, callbacks, session: { strategy: sessionStrategy, maxAge: sessionMaxAge }, logger, } = options;\n    const useJwtSession = sessionStrategy === \"jwt\";\n    try {\n        if (provider.type === \"oauth\" || provider.type === \"oidc\") {\n            // Use body if the response mode is set to form_post. For all other cases, use query\n            const params = provider.authorization?.url.searchParams.get(\"response_mode\") ===\n                \"form_post\"\n                ? body\n                : query;\n            // If we have a state and we are on a redirect proxy, we try to parse it\n            // and see if it contains a valid origin to redirect to. If it does, we\n            // redirect the user to that origin with the original state.\n            if (options.isOnRedirectProxy && params?.state) {\n                // NOTE: We rely on the state being encrypted using a shared secret\n                // between the proxy and the original server.\n                const parsedState = await state.decode(params.state, options);\n                const shouldRedirect = parsedState?.origin &&\n                    new URL(parsedState.origin).origin !== options.url.origin;\n                if (shouldRedirect) {\n                    const proxyRedirect = `${parsedState.origin}?${new URLSearchParams(params)}`;\n                    logger.debug(\"Proxy redirecting to\", proxyRedirect);\n                    return { redirect: proxyRedirect, cookies };\n                }\n            }\n            const authorizationResult = await handleOAuth(params, request.cookies, options);\n            if (authorizationResult.cookies.length) {\n                cookies.push(...authorizationResult.cookies);\n            }\n            logger.debug(\"authorization result\", authorizationResult);\n            const { user: userFromProvider, account, profile: OAuthProfile, } = authorizationResult;\n            // If we don't have a profile object then either something went wrong\n            // or the user cancelled signing in. We don't know which, so we just\n            // direct the user to the signin page for now. We could do something\n            // else in future.\n            // TODO: Handle user cancelling signin\n            if (!userFromProvider || !account || !OAuthProfile) {\n                return { redirect: `${url}/signin`, cookies };\n            }\n            // Check if user is allowed to sign in\n            // Attempt to get Profile from OAuth provider details before invoking\n            // signIn callback - but if no user object is returned, that is fine\n            // (that just means it's a new user signing in for the first time).\n            let userByAccount;\n            if (adapter) {\n                const { getUserByAccount } = adapter;\n                userByAccount = await getUserByAccount({\n                    providerAccountId: account.providerAccountId,\n                    provider: provider.id,\n                });\n            }\n            const redirect = await handleAuthorized({\n                user: userByAccount ?? userFromProvider,\n                account,\n                profile: OAuthProfile,\n            }, options);\n            if (redirect)\n                return { redirect, cookies };\n            const { user, session, isNewUser } = await handleLoginOrRegister(sessionStore.value, userFromProvider, account, options);\n            if (useJwtSession) {\n                const defaultToken = {\n                    name: user.name,\n                    email: user.email,\n                    picture: user.image,\n                    sub: user.id?.toString(),\n                };\n                const token = await callbacks.jwt({\n                    token: defaultToken,\n                    user,\n                    account,\n                    profile: OAuthProfile,\n                    isNewUser,\n                    trigger: isNewUser ? \"signUp\" : \"signIn\",\n                });\n                // Clear cookies if token is null\n                if (token === null) {\n                    cookies.push(...sessionStore.clean());\n                }\n                else {\n                    const salt = options.cookies.sessionToken.name;\n                    // Encode token\n                    const newToken = await jwt.encode({ ...jwt, token, salt });\n                    // Set cookie expiry date\n                    const cookieExpires = new Date();\n                    cookieExpires.setTime(cookieExpires.getTime() + sessionMaxAge * 1000);\n                    const sessionCookies = sessionStore.chunk(newToken, {\n                        expires: cookieExpires,\n                    });\n                    cookies.push(...sessionCookies);\n                }\n            }\n            else {\n                // Save Session Token in cookie\n                cookies.push({\n                    name: options.cookies.sessionToken.name,\n                    value: session.sessionToken,\n                    options: {\n                        ...options.cookies.sessionToken.options,\n                        expires: session.expires,\n                    },\n                });\n            }\n            await events.signIn?.({\n                user,\n                account,\n                profile: OAuthProfile,\n                isNewUser,\n            });\n            // Handle first logins on new accounts\n            // e.g. option to send users to a new account landing page on initial login\n            // Note that the callback URL is preserved, so the journey can still be resumed\n            if (isNewUser && pages.newUser) {\n                return {\n                    redirect: `${pages.newUser}${pages.newUser.includes(\"?\") ? \"&\" : \"?\"}${new URLSearchParams({ callbackUrl })}`,\n                    cookies,\n                };\n            }\n            return { redirect: callbackUrl, cookies };\n        }\n        else if (provider.type === \"email\") {\n            const paramToken = query?.token;\n            const paramIdentifier = query?.email;\n            if (!paramToken) {\n                const e = new TypeError(\"Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.\", { cause: { hasToken: !!paramToken } });\n                e.name = \"Configuration\";\n                throw e;\n            }\n            const secret = provider.secret ?? options.secret;\n            // @ts-expect-error -- Verified in `assertConfig`.\n            const invite = await adapter.useVerificationToken({\n                // @ts-expect-error User-land adapters might decide to omit the identifier during lookup\n                identifier: paramIdentifier, // TODO: Drop this requirement for lookup in official adapters too\n                token: await createHash(`${paramToken}${secret}`),\n            });\n            const hasInvite = !!invite;\n            const expired = hasInvite && invite.expires.valueOf() < Date.now();\n            const invalidInvite = !hasInvite ||\n                expired ||\n                // The user might have configured the link to not contain the identifier\n                // so we only compare if it exists\n                (paramIdentifier && invite.identifier !== paramIdentifier);\n            if (invalidInvite)\n                throw new Verification({ hasInvite, expired });\n            const { identifier } = invite;\n            const user = (await adapter.getUserByEmail(identifier)) ?? {\n                id: crypto.randomUUID(),\n                email: identifier,\n                emailVerified: null,\n            };\n            const account = {\n                providerAccountId: user.email,\n                userId: user.id,\n                type: \"email\",\n                provider: provider.id,\n            };\n            const redirect = await handleAuthorized({ user, account }, options);\n            if (redirect)\n                return { redirect, cookies };\n            // Sign user in\n            const { user: loggedInUser, session, isNewUser, } = await handleLoginOrRegister(sessionStore.value, user, account, options);\n            if (useJwtSession) {\n                const defaultToken = {\n                    name: loggedInUser.name,\n                    email: loggedInUser.email,\n                    picture: loggedInUser.image,\n                    sub: loggedInUser.id?.toString(),\n                };\n                const token = await callbacks.jwt({\n                    token: defaultToken,\n                    user: loggedInUser,\n                    account,\n                    isNewUser,\n                    trigger: isNewUser ? \"signUp\" : \"signIn\",\n                });\n                // Clear cookies if token is null\n                if (token === null) {\n                    cookies.push(...sessionStore.clean());\n                }\n                else {\n                    const salt = options.cookies.sessionToken.name;\n                    // Encode token\n                    const newToken = await jwt.encode({ ...jwt, token, salt });\n                    // Set cookie expiry date\n                    const cookieExpires = new Date();\n                    cookieExpires.setTime(cookieExpires.getTime() + sessionMaxAge * 1000);\n                    const sessionCookies = sessionStore.chunk(newToken, {\n                        expires: cookieExpires,\n                    });\n                    cookies.push(...sessionCookies);\n                }\n            }\n            else {\n                // Save Session Token in cookie\n                cookies.push({\n                    name: options.cookies.sessionToken.name,\n                    value: session.sessionToken,\n                    options: {\n                        ...options.cookies.sessionToken.options,\n                        expires: session.expires,\n                    },\n                });\n            }\n            await events.signIn?.({ user: loggedInUser, account, isNewUser });\n            // Handle first logins on new accounts\n            // e.g. option to send users to a new account landing page on initial login\n            // Note that the callback URL is preserved, so the journey can still be resumed\n            if (isNewUser && pages.newUser) {\n                return {\n                    redirect: `${pages.newUser}${pages.newUser.includes(\"?\") ? \"&\" : \"?\"}${new URLSearchParams({ callbackUrl })}`,\n                    cookies,\n                };\n            }\n            // Callback URL is already verified at this point, so safe to use if specified\n            return { redirect: callbackUrl, cookies };\n        }\n        else if (provider.type === \"credentials\" && method === \"POST\") {\n            const credentials = body ?? {};\n            // TODO: Forward the original request as is, instead of reconstructing it\n            Object.entries(query ?? {}).forEach(([k, v]) => url.searchParams.set(k, v));\n            const userFromAuthorize = await provider.authorize(credentials, \n            // prettier-ignore\n            new Request(url, { headers, method, body: JSON.stringify(body) }));\n            const user = userFromAuthorize;\n            if (!user)\n                throw new CredentialsSignin();\n            else\n                user.id = user.id?.toString() ?? crypto.randomUUID();\n            const account = {\n                providerAccountId: user.id,\n                type: \"credentials\",\n                provider: provider.id,\n            };\n            const redirect = await handleAuthorized({ user, account, credentials }, options);\n            if (redirect)\n                return { redirect, cookies };\n            const defaultToken = {\n                name: user.name,\n                email: user.email,\n                picture: user.image,\n                sub: user.id,\n            };\n            const token = await callbacks.jwt({\n                token: defaultToken,\n                user,\n                account,\n                isNewUser: false,\n                trigger: \"signIn\",\n            });\n            // Clear cookies if token is null\n            if (token === null) {\n                cookies.push(...sessionStore.clean());\n            }\n            else {\n                const salt = options.cookies.sessionToken.name;\n                // Encode token\n                const newToken = await jwt.encode({ ...jwt, token, salt });\n                // Set cookie expiry date\n                const cookieExpires = new Date();\n                cookieExpires.setTime(cookieExpires.getTime() + sessionMaxAge * 1000);\n                const sessionCookies = sessionStore.chunk(newToken, {\n                    expires: cookieExpires,\n                });\n                cookies.push(...sessionCookies);\n            }\n            await events.signIn?.({ user, account });\n            return { redirect: callbackUrl, cookies };\n        }\n        else if (provider.type === \"webauthn\" && method === \"POST\") {\n            // Get callback action from request. It should be either \"authenticate\" or \"register\"\n            const action = request.body?.action;\n            if (typeof action !== \"string\" ||\n                (action !== \"authenticate\" && action !== \"register\")) {\n                throw new AuthError(\"Invalid action parameter\");\n            }\n            // Return an error if the adapter is missing or if the provider\n            // is not a webauthn provider.\n            const localOptions = assertInternalOptionsWebAuthn(options);\n            // Verify request to get user, account and authenticator\n            let user;\n            let account;\n            let authenticator;\n            switch (action) {\n                case \"authenticate\": {\n                    const verified = await verifyAuthenticate(localOptions, request, cookies);\n                    user = verified.user;\n                    account = verified.account;\n                    break;\n                }\n                case \"register\": {\n                    const verified = await verifyRegister(options, request, cookies);\n                    user = verified.user;\n                    account = verified.account;\n                    authenticator = verified.authenticator;\n                    break;\n                }\n            }\n            // Check if user is allowed to sign in\n            await handleAuthorized({ user, account }, options);\n            // Sign user in, creating them and their account if needed\n            const { user: loggedInUser, isNewUser, session, account: currentAccount, } = await handleLoginOrRegister(sessionStore.value, user, account, options);\n            if (!currentAccount) {\n                // This is mostly for type checking. It should never actually happen.\n                throw new AuthError(\"Error creating or finding account\");\n            }\n            // Create new authenticator if needed\n            if (authenticator && loggedInUser.id) {\n                await localOptions.adapter.createAuthenticator({\n                    ...authenticator,\n                    userId: loggedInUser.id,\n                });\n            }\n            // Do the session registering dance\n            if (useJwtSession) {\n                const defaultToken = {\n                    name: loggedInUser.name,\n                    email: loggedInUser.email,\n                    picture: loggedInUser.image,\n                    sub: loggedInUser.id?.toString(),\n                };\n                const token = await callbacks.jwt({\n                    token: defaultToken,\n                    user: loggedInUser,\n                    account: currentAccount,\n                    isNewUser,\n                    trigger: isNewUser ? \"signUp\" : \"signIn\",\n                });\n                // Clear cookies if token is null\n                if (token === null) {\n                    cookies.push(...sessionStore.clean());\n                }\n                else {\n                    const salt = options.cookies.sessionToken.name;\n                    // Encode token\n                    const newToken = await jwt.encode({ ...jwt, token, salt });\n                    // Set cookie expiry date\n                    const cookieExpires = new Date();\n                    cookieExpires.setTime(cookieExpires.getTime() + sessionMaxAge * 1000);\n                    const sessionCookies = sessionStore.chunk(newToken, {\n                        expires: cookieExpires,\n                    });\n                    cookies.push(...sessionCookies);\n                }\n            }\n            else {\n                // Save Session Token in cookie\n                cookies.push({\n                    name: options.cookies.sessionToken.name,\n                    value: session.sessionToken,\n                    options: {\n                        ...options.cookies.sessionToken.options,\n                        expires: session.expires,\n                    },\n                });\n            }\n            await events.signIn?.({\n                user: loggedInUser,\n                account: currentAccount,\n                isNewUser,\n            });\n            // Handle first logins on new accounts\n            // e.g. option to send users to a new account landing page on initial login\n            // Note that the callback URL is preserved, so the journey can still be resumed\n            if (isNewUser && pages.newUser) {\n                return {\n                    redirect: `${pages.newUser}${pages.newUser.includes(\"?\") ? \"&\" : \"?\"}${new URLSearchParams({ callbackUrl })}`,\n                    cookies,\n                };\n            }\n            // Callback URL is already verified at this point, so safe to use if specified\n            return { redirect: callbackUrl, cookies };\n        }\n        throw new InvalidProvider(`Callback for provider type (${provider.type}) is not supported`);\n    }\n    catch (e) {\n        if (e instanceof AuthError)\n            throw e;\n        const error = new CallbackRouteError(e, { provider: provider.id });\n        logger.debug(\"callback route error details\", { method, query, body });\n        throw error;\n    }\n}\nasync function handleAuthorized(params, config) {\n    let authorized;\n    const { signIn, redirect } = config.callbacks;\n    try {\n        authorized = await signIn(params);\n    }\n    catch (e) {\n        if (e instanceof AuthError)\n            throw e;\n        throw new AccessDenied(e);\n    }\n    if (!authorized)\n        throw new AccessDenied(\"AccessDenied\");\n    if (typeof authorized !== \"string\")\n        return;\n    return await redirect({ url: authorized, baseUrl: config.url.origin });\n}\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;AAC/B;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEO,eAAe,SAAS,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO;IAClE,IAAI,CAAC,QAAQ,QAAQ,EACjB,MAAM,IAAI,4MAAA,CAAA,kBAAe,CAAC;IAC9B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG;IACzC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,eAAe,EAAE,QAAQ,aAAa,EAAE,EAAE,MAAM,EAAG,GAAG;IACvJ,MAAM,gBAAgB,oBAAoB;IAC1C,IAAI;QACA,IAAI,SAAS,IAAI,KAAK,WAAW,SAAS,IAAI,KAAK,QAAQ;YACvD,oFAAoF;YACpF,MAAM,SAAS,SAAS,aAAa,EAAE,IAAI,aAAa,IAAI,qBACxD,cACE,OACA;YACN,wEAAwE;YACxE,uEAAuE;YACvE,4DAA4D;YAC5D,IAAI,QAAQ,iBAAiB,IAAI,QAAQ,OAAO;gBAC5C,mEAAmE;gBACnE,6CAA6C;gBAC7C,MAAM,cAAc,MAAM,mPAAA,CAAA,QAAK,CAAC,MAAM,CAAC,OAAO,KAAK,EAAE;gBACrD,MAAM,iBAAiB,aAAa,UAChC,IAAI,IAAI,YAAY,MAAM,EAAE,MAAM,KAAK,QAAQ,GAAG,CAAC,MAAM;gBAC7D,IAAI,gBAAgB;oBAChB,MAAM,gBAAgB,GAAG,YAAY,MAAM,CAAC,CAAC,EAAE,IAAI,gBAAgB,SAAS;oBAC5E,OAAO,KAAK,CAAC,wBAAwB;oBACrC,OAAO;wBAAE,UAAU;wBAAe;oBAAQ;gBAC9C;YACJ;YACA,MAAM,sBAAsB,MAAM,CAAA,GAAA,qPAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,QAAQ,OAAO,EAAE;YACvE,IAAI,oBAAoB,OAAO,CAAC,MAAM,EAAE;gBACpC,QAAQ,IAAI,IAAI,oBAAoB,OAAO;YAC/C;YACA,OAAO,KAAK,CAAC,wBAAwB;YACrC,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,EAAE,SAAS,YAAY,EAAG,GAAG;YACpE,qEAAqE;YACrE,oEAAoE;YACpE,oEAAoE;YACpE,kBAAkB;YAClB,sCAAsC;YACtC,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,cAAc;gBAChD,OAAO;oBAAE,UAAU,GAAG,IAAI,OAAO,CAAC;oBAAE;gBAAQ;YAChD;YACA,sCAAsC;YACtC,qEAAqE;YACrE,oEAAoE;YACpE,mEAAmE;YACnE,IAAI;YACJ,IAAI,SAAS;gBACT,MAAM,EAAE,gBAAgB,EAAE,GAAG;gBAC7B,gBAAgB,MAAM,iBAAiB;oBACnC,mBAAmB,QAAQ,iBAAiB;oBAC5C,UAAU,SAAS,EAAE;gBACzB;YACJ;YACA,MAAM,WAAW,MAAM,iBAAiB;gBACpC,MAAM,iBAAiB;gBACvB;gBACA,SAAS;YACb,GAAG;YACH,IAAI,UACA,OAAO;gBAAE;gBAAU;YAAQ;YAC/B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,CAAA,GAAA,mPAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa,KAAK,EAAE,kBAAkB,SAAS;YAChH,IAAI,eAAe;gBACf,MAAM,eAAe;oBACjB,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,SAAS,KAAK,KAAK;oBACnB,KAAK,KAAK,EAAE,EAAE;gBAClB;gBACA,MAAM,QAAQ,MAAM,UAAU,GAAG,CAAC;oBAC9B,OAAO;oBACP;oBACA;oBACA,SAAS;oBACT;oBACA,SAAS,YAAY,WAAW;gBACpC;gBACA,iCAAiC;gBACjC,IAAI,UAAU,MAAM;oBAChB,QAAQ,IAAI,IAAI,aAAa,KAAK;gBACtC,OACK;oBACD,MAAM,OAAO,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI;oBAC9C,eAAe;oBACf,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC;wBAAE,GAAG,GAAG;wBAAE;wBAAO;oBAAK;oBACxD,yBAAyB;oBACzB,MAAM,gBAAgB,IAAI;oBAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK,gBAAgB;oBAChE,MAAM,iBAAiB,aAAa,KAAK,CAAC,UAAU;wBAChD,SAAS;oBACb;oBACA,QAAQ,IAAI,IAAI;gBACpB;YACJ,OACK;gBACD,+BAA+B;gBAC/B,QAAQ,IAAI,CAAC;oBACT,MAAM,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI;oBACvC,OAAO,QAAQ,YAAY;oBAC3B,SAAS;wBACL,GAAG,QAAQ,OAAO,CAAC,YAAY,CAAC,OAAO;wBACvC,SAAS,QAAQ,OAAO;oBAC5B;gBACJ;YACJ;YACA,MAAM,OAAO,MAAM,GAAG;gBAClB;gBACA;gBACA,SAAS;gBACT;YACJ;YACA,sCAAsC;YACtC,2EAA2E;YAC3E,+EAA+E;YAC/E,IAAI,aAAa,MAAM,OAAO,EAAE;gBAC5B,OAAO;oBACH,UAAU,GAAG,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,OAAO,MAAM,MAAM,IAAI,gBAAgB;wBAAE;oBAAY,IAAI;oBAC7G;gBACJ;YACJ;YACA,OAAO;gBAAE,UAAU;gBAAa;YAAQ;QAC5C,OACK,IAAI,SAAS,IAAI,KAAK,SAAS;YAChC,MAAM,aAAa,OAAO;YAC1B,MAAM,kBAAkB,OAAO;YAC/B,IAAI,CAAC,YAAY;gBACb,MAAM,IAAI,IAAI,UAAU,qHAAqH;oBAAE,OAAO;wBAAE,UAAU,CAAC,CAAC;oBAAW;gBAAE;gBACjL,EAAE,IAAI,GAAG;gBACT,MAAM;YACV;YACA,MAAM,SAAS,SAAS,MAAM,IAAI,QAAQ,MAAM;YAChD,kDAAkD;YAClD,MAAM,SAAS,MAAM,QAAQ,oBAAoB,CAAC;gBAC9C,wFAAwF;gBACxF,YAAY;gBACZ,OAAO,MAAM,CAAA,GAAA,yNAAA,CAAA,aAAU,AAAD,EAAE,GAAG,aAAa,QAAQ;YACpD;YACA,MAAM,YAAY,CAAC,CAAC;YACpB,MAAM,UAAU,aAAa,OAAO,OAAO,CAAC,OAAO,KAAK,KAAK,GAAG;YAChE,MAAM,gBAAgB,CAAC,aACnB,WAGC,mBAAmB,OAAO,UAAU,KAAK;YAC9C,IAAI,eACA,MAAM,IAAI,4MAAA,CAAA,eAAY,CAAC;gBAAE;gBAAW;YAAQ;YAChD,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,MAAM,OAAO,AAAC,MAAM,QAAQ,cAAc,CAAC,eAAgB;gBACvD,IAAI,OAAO,UAAU;gBACrB,OAAO;gBACP,eAAe;YACnB;YACA,MAAM,UAAU;gBACZ,mBAAmB,KAAK,KAAK;gBAC7B,QAAQ,KAAK,EAAE;gBACf,MAAM;gBACN,UAAU,SAAS,EAAE;YACzB;YACA,MAAM,WAAW,MAAM,iBAAiB;gBAAE;gBAAM;YAAQ,GAAG;YAC3D,IAAI,UACA,OAAO;gBAAE;gBAAU;YAAQ;YAC/B,eAAe;YACf,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,EAAE,SAAS,EAAG,GAAG,MAAM,CAAA,GAAA,mPAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa,KAAK,EAAE,MAAM,SAAS;YACnH,IAAI,eAAe;gBACf,MAAM,eAAe;oBACjB,MAAM,aAAa,IAAI;oBACvB,OAAO,aAAa,KAAK;oBACzB,SAAS,aAAa,KAAK;oBAC3B,KAAK,aAAa,EAAE,EAAE;gBAC1B;gBACA,MAAM,QAAQ,MAAM,UAAU,GAAG,CAAC;oBAC9B,OAAO;oBACP,MAAM;oBACN;oBACA;oBACA,SAAS,YAAY,WAAW;gBACpC;gBACA,iCAAiC;gBACjC,IAAI,UAAU,MAAM;oBAChB,QAAQ,IAAI,IAAI,aAAa,KAAK;gBACtC,OACK;oBACD,MAAM,OAAO,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI;oBAC9C,eAAe;oBACf,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC;wBAAE,GAAG,GAAG;wBAAE;wBAAO;oBAAK;oBACxD,yBAAyB;oBACzB,MAAM,gBAAgB,IAAI;oBAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK,gBAAgB;oBAChE,MAAM,iBAAiB,aAAa,KAAK,CAAC,UAAU;wBAChD,SAAS;oBACb;oBACA,QAAQ,IAAI,IAAI;gBACpB;YACJ,OACK;gBACD,+BAA+B;gBAC/B,QAAQ,IAAI,CAAC;oBACT,MAAM,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI;oBACvC,OAAO,QAAQ,YAAY;oBAC3B,SAAS;wBACL,GAAG,QAAQ,OAAO,CAAC,YAAY,CAAC,OAAO;wBACvC,SAAS,QAAQ,OAAO;oBAC5B;gBACJ;YACJ;YACA,MAAM,OAAO,MAAM,GAAG;gBAAE,MAAM;gBAAc;gBAAS;YAAU;YAC/D,sCAAsC;YACtC,2EAA2E;YAC3E,+EAA+E;YAC/E,IAAI,aAAa,MAAM,OAAO,EAAE;gBAC5B,OAAO;oBACH,UAAU,GAAG,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,OAAO,MAAM,MAAM,IAAI,gBAAgB;wBAAE;oBAAY,IAAI;oBAC7G;gBACJ;YACJ;YACA,8EAA8E;YAC9E,OAAO;gBAAE,UAAU;gBAAa;YAAQ;QAC5C,OACK,IAAI,SAAS,IAAI,KAAK,iBAAiB,WAAW,QAAQ;YAC3D,MAAM,cAAc,QAAQ,CAAC;YAC7B,yEAAyE;YACzE,OAAO,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,GAAK,IAAI,YAAY,CAAC,GAAG,CAAC,GAAG;YACxE,MAAM,oBAAoB,MAAM,SAAS,SAAS,CAAC,aACnD,kBAAkB;YAClB,IAAI,QAAQ,KAAK;gBAAE;gBAAS;gBAAQ,MAAM,KAAK,SAAS,CAAC;YAAM;YAC/D,MAAM,OAAO;YACb,IAAI,CAAC,MACD,MAAM,IAAI,4MAAA,CAAA,oBAAiB;iBAE3B,KAAK,EAAE,GAAG,KAAK,EAAE,EAAE,cAAc,OAAO,UAAU;YACtD,MAAM,UAAU;gBACZ,mBAAmB,KAAK,EAAE;gBAC1B,MAAM;gBACN,UAAU,SAAS,EAAE;YACzB;YACA,MAAM,WAAW,MAAM,iBAAiB;gBAAE;gBAAM;gBAAS;YAAY,GAAG;YACxE,IAAI,UACA,OAAO;gBAAE;gBAAU;YAAQ;YAC/B,MAAM,eAAe;gBACjB,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,SAAS,KAAK,KAAK;gBACnB,KAAK,KAAK,EAAE;YAChB;YACA,MAAM,QAAQ,MAAM,UAAU,GAAG,CAAC;gBAC9B,OAAO;gBACP;gBACA;gBACA,WAAW;gBACX,SAAS;YACb;YACA,iCAAiC;YACjC,IAAI,UAAU,MAAM;gBAChB,QAAQ,IAAI,IAAI,aAAa,KAAK;YACtC,OACK;gBACD,MAAM,OAAO,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI;gBAC9C,eAAe;gBACf,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC;oBAAE,GAAG,GAAG;oBAAE;oBAAO;gBAAK;gBACxD,yBAAyB;gBACzB,MAAM,gBAAgB,IAAI;gBAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK,gBAAgB;gBAChE,MAAM,iBAAiB,aAAa,KAAK,CAAC,UAAU;oBAChD,SAAS;gBACb;gBACA,QAAQ,IAAI,IAAI;YACpB;YACA,MAAM,OAAO,MAAM,GAAG;gBAAE;gBAAM;YAAQ;YACtC,OAAO;gBAAE,UAAU;gBAAa;YAAQ;QAC5C,OACK,IAAI,SAAS,IAAI,KAAK,cAAc,WAAW,QAAQ;YACxD,qFAAqF;YACrF,MAAM,SAAS,QAAQ,IAAI,EAAE;YAC7B,IAAI,OAAO,WAAW,YACjB,WAAW,kBAAkB,WAAW,YAAa;gBACtD,MAAM,IAAI,4MAAA,CAAA,YAAS,CAAC;YACxB;YACA,+DAA+D;YAC/D,8BAA8B;YAC9B,MAAM,eAAe,CAAA,GAAA,uOAAA,CAAA,gCAA6B,AAAD,EAAE;YACnD,wDAAwD;YACxD,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,OAAQ;gBACJ,KAAK;oBAAgB;wBACjB,MAAM,WAAW,MAAM,CAAA,GAAA,uOAAA,CAAA,qBAAkB,AAAD,EAAE,cAAc,SAAS;wBACjE,OAAO,SAAS,IAAI;wBACpB,UAAU,SAAS,OAAO;wBAC1B;oBACJ;gBACA,KAAK;oBAAY;wBACb,MAAM,WAAW,MAAM,CAAA,GAAA,uOAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,SAAS;wBACxD,OAAO,SAAS,IAAI;wBACpB,UAAU,SAAS,OAAO;wBAC1B,gBAAgB,SAAS,aAAa;wBACtC;oBACJ;YACJ;YACA,sCAAsC;YACtC,MAAM,iBAAiB;gBAAE;gBAAM;YAAQ,GAAG;YAC1C,0DAA0D;YAC1D,MAAM,EAAE,MAAM,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,cAAc,EAAG,GAAG,MAAM,CAAA,GAAA,mPAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa,KAAK,EAAE,MAAM,SAAS;YAC5I,IAAI,CAAC,gBAAgB;gBACjB,qEAAqE;gBACrE,MAAM,IAAI,4MAAA,CAAA,YAAS,CAAC;YACxB;YACA,qCAAqC;YACrC,IAAI,iBAAiB,aAAa,EAAE,EAAE;gBAClC,MAAM,aAAa,OAAO,CAAC,mBAAmB,CAAC;oBAC3C,GAAG,aAAa;oBAChB,QAAQ,aAAa,EAAE;gBAC3B;YACJ;YACA,mCAAmC;YACnC,IAAI,eAAe;gBACf,MAAM,eAAe;oBACjB,MAAM,aAAa,IAAI;oBACvB,OAAO,aAAa,KAAK;oBACzB,SAAS,aAAa,KAAK;oBAC3B,KAAK,aAAa,EAAE,EAAE;gBAC1B;gBACA,MAAM,QAAQ,MAAM,UAAU,GAAG,CAAC;oBAC9B,OAAO;oBACP,MAAM;oBACN,SAAS;oBACT;oBACA,SAAS,YAAY,WAAW;gBACpC;gBACA,iCAAiC;gBACjC,IAAI,UAAU,MAAM;oBAChB,QAAQ,IAAI,IAAI,aAAa,KAAK;gBACtC,OACK;oBACD,MAAM,OAAO,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI;oBAC9C,eAAe;oBACf,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC;wBAAE,GAAG,GAAG;wBAAE;wBAAO;oBAAK;oBACxD,yBAAyB;oBACzB,MAAM,gBAAgB,IAAI;oBAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK,gBAAgB;oBAChE,MAAM,iBAAiB,aAAa,KAAK,CAAC,UAAU;wBAChD,SAAS;oBACb;oBACA,QAAQ,IAAI,IAAI;gBACpB;YACJ,OACK;gBACD,+BAA+B;gBAC/B,QAAQ,IAAI,CAAC;oBACT,MAAM,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI;oBACvC,OAAO,QAAQ,YAAY;oBAC3B,SAAS;wBACL,GAAG,QAAQ,OAAO,CAAC,YAAY,CAAC,OAAO;wBACvC,SAAS,QAAQ,OAAO;oBAC5B;gBACJ;YACJ;YACA,MAAM,OAAO,MAAM,GAAG;gBAClB,MAAM;gBACN,SAAS;gBACT;YACJ;YACA,sCAAsC;YACtC,2EAA2E;YAC3E,+EAA+E;YAC/E,IAAI,aAAa,MAAM,OAAO,EAAE;gBAC5B,OAAO;oBACH,UAAU,GAAG,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,OAAO,MAAM,MAAM,IAAI,gBAAgB;wBAAE;oBAAY,IAAI;oBAC7G;gBACJ;YACJ;YACA,8EAA8E;YAC9E,OAAO;gBAAE,UAAU;gBAAa;YAAQ;QAC5C;QACA,MAAM,IAAI,4MAAA,CAAA,kBAAe,CAAC,CAAC,4BAA4B,EAAE,SAAS,IAAI,CAAC,kBAAkB,CAAC;IAC9F,EACA,OAAO,GAAG;QACN,IAAI,aAAa,4MAAA,CAAA,YAAS,EACtB,MAAM;QACV,MAAM,QAAQ,IAAI,4MAAA,CAAA,qBAAkB,CAAC,GAAG;YAAE,UAAU,SAAS,EAAE;QAAC;QAChE,OAAO,KAAK,CAAC,gCAAgC;YAAE;YAAQ;YAAO;QAAK;QACnE,MAAM;IACV;AACJ;AACA,eAAe,iBAAiB,MAAM,EAAE,MAAM;IAC1C,IAAI;IACJ,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,SAAS;IAC7C,IAAI;QACA,aAAa,MAAM,OAAO;IAC9B,EACA,OAAO,GAAG;QACN,IAAI,aAAa,4MAAA,CAAA,YAAS,EACtB,MAAM;QACV,MAAM,IAAI,4MAAA,CAAA,eAAY,CAAC;IAC3B;IACA,IAAI,CAAC,YACD,MAAM,IAAI,4MAAA,CAAA,eAAY,CAAC;IAC3B,IAAI,OAAO,eAAe,UACtB;IACJ,OAAO,MAAM,SAAS;QAAE,KAAK;QAAY,SAAS,OAAO,GAAG,CAAC,MAAM;IAAC;AACxE", "ignoreList": [0]}}, {"offset": {"line": 4489, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/actions/session.js"], "sourcesContent": ["import { JWTSessionError, SessionTokenError } from \"../../errors.js\";\nimport { fromDate } from \"../utils/date.js\";\n/** Return a session object filtered via `callbacks.session` */\nexport async function session(options, sessionStore, cookies, isUpdate, newSession) {\n    const { adapter, jwt, events, callbacks, logger, session: { strategy: sessionStrategy, maxAge: sessionMaxAge }, } = options;\n    const response = {\n        body: null,\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...(!isUpdate && {\n                \"Cache-Control\": \"private, no-cache, no-store\",\n                Expires: \"0\",\n                Pragma: \"no-cache\",\n            }),\n        },\n        cookies,\n    };\n    const sessionToken = sessionStore.value;\n    if (!sessionToken)\n        return response;\n    if (sessionStrategy === \"jwt\") {\n        try {\n            const salt = options.cookies.sessionToken.name;\n            const payload = await jwt.decode({ ...jwt, token: sessionToken, salt });\n            if (!payload)\n                throw new Error(\"Invalid JWT\");\n            // @ts-expect-error\n            const token = await callbacks.jwt({\n                token: payload,\n                ...(isUpdate && { trigger: \"update\" }),\n                session: newSession,\n            });\n            const newExpires = fromDate(sessionMaxAge);\n            if (token !== null) {\n                // By default, only exposes a limited subset of information to the client\n                // as needed for presentation purposes (e.g. \"you are logged in as...\").\n                const session = {\n                    user: { name: token.name, email: token.email, image: token.picture },\n                    expires: newExpires.toISOString(),\n                };\n                // @ts-expect-error\n                const newSession = await callbacks.session({ session, token });\n                // Return session payload as response\n                response.body = newSession;\n                // Refresh JWT expiry by re-signing it, with an updated expiry date\n                const newToken = await jwt.encode({ ...jwt, token, salt });\n                // Set cookie, to also update expiry date on cookie\n                const sessionCookies = sessionStore.chunk(newToken, {\n                    expires: newExpires,\n                });\n                response.cookies?.push(...sessionCookies);\n                await events.session?.({ session: newSession, token });\n            }\n            else {\n                response.cookies?.push(...sessionStore.clean());\n            }\n        }\n        catch (e) {\n            logger.error(new JWTSessionError(e));\n            // If the JWT is not verifiable remove the broken session cookie(s).\n            response.cookies?.push(...sessionStore.clean());\n        }\n        return response;\n    }\n    // Retrieve session from database\n    try {\n        const { getSessionAndUser, deleteSession, updateSession } = adapter;\n        let userAndSession = await getSessionAndUser(sessionToken);\n        // If session has expired, clean up the database\n        if (userAndSession &&\n            userAndSession.session.expires.valueOf() < Date.now()) {\n            await deleteSession(sessionToken);\n            userAndSession = null;\n        }\n        if (userAndSession) {\n            const { user, session } = userAndSession;\n            const sessionUpdateAge = options.session.updateAge;\n            // Calculate last updated date to throttle write updates to database\n            // Formula: ({expiry date} - sessionMaxAge) + sessionUpdateAge\n            //     e.g. ({expiry date} - 30 days) + 1 hour\n            const sessionIsDueToBeUpdatedDate = session.expires.valueOf() -\n                sessionMaxAge * 1000 +\n                sessionUpdateAge * 1000;\n            const newExpires = fromDate(sessionMaxAge);\n            // Trigger update of session expiry date and write to database, only\n            // if the session was last updated more than {sessionUpdateAge} ago\n            if (sessionIsDueToBeUpdatedDate <= Date.now()) {\n                await updateSession({\n                    sessionToken: sessionToken,\n                    expires: newExpires,\n                });\n            }\n            // Pass Session through to the session callback\n            const sessionPayload = await callbacks.session({\n                // TODO: user already passed below,\n                // remove from session object in https://github.com/nextauthjs/next-auth/pull/9702\n                // @ts-expect-error\n                session: { ...session, user },\n                user,\n                newSession,\n                ...(isUpdate ? { trigger: \"update\" } : {}),\n            });\n            // Return session payload as response\n            response.body = sessionPayload;\n            // Set cookie again to update expiry\n            response.cookies?.push({\n                name: options.cookies.sessionToken.name,\n                value: sessionToken,\n                options: {\n                    ...options.cookies.sessionToken.options,\n                    expires: newExpires,\n                },\n            });\n            // @ts-expect-error\n            await events.session?.({ session: sessionPayload });\n        }\n        else if (sessionToken) {\n            // If `sessionToken` was found set but it's not valid for a session then\n            // remove the sessionToken cookie from browser.\n            response.cookies?.push(...sessionStore.clean());\n        }\n    }\n    catch (e) {\n        logger.error(new SessionTokenError(e));\n    }\n    return response;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,QAAQ,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU;IAC9E,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,eAAe,EAAE,QAAQ,aAAa,EAAE,EAAG,GAAG;IACpH,MAAM,WAAW;QACb,MAAM;QACN,SAAS;YACL,gBAAgB;YAChB,GAAI,CAAC,YAAY;gBACb,iBAAiB;gBACjB,SAAS;gBACT,QAAQ;YACZ,CAAC;QACL;QACA;IACJ;IACA,MAAM,eAAe,aAAa,KAAK;IACvC,IAAI,CAAC,cACD,OAAO;IACX,IAAI,oBAAoB,OAAO;QAC3B,IAAI;YACA,MAAM,OAAO,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI;YAC9C,MAAM,UAAU,MAAM,IAAI,MAAM,CAAC;gBAAE,GAAG,GAAG;gBAAE,OAAO;gBAAc;YAAK;YACrE,IAAI,CAAC,SACD,MAAM,IAAI,MAAM;YACpB,mBAAmB;YACnB,MAAM,QAAQ,MAAM,UAAU,GAAG,CAAC;gBAC9B,OAAO;gBACP,GAAI,YAAY;oBAAE,SAAS;gBAAS,CAAC;gBACrC,SAAS;YACb;YACA,MAAM,aAAa,CAAA,GAAA,0NAAA,CAAA,WAAQ,AAAD,EAAE;YAC5B,IAAI,UAAU,MAAM;gBAChB,yEAAyE;gBACzE,wEAAwE;gBACxE,MAAM,UAAU;oBACZ,MAAM;wBAAE,MAAM,MAAM,IAAI;wBAAE,OAAO,MAAM,KAAK;wBAAE,OAAO,MAAM,OAAO;oBAAC;oBACnE,SAAS,WAAW,WAAW;gBACnC;gBACA,mBAAmB;gBACnB,MAAM,aAAa,MAAM,UAAU,OAAO,CAAC;oBAAE;oBAAS;gBAAM;gBAC5D,qCAAqC;gBACrC,SAAS,IAAI,GAAG;gBAChB,mEAAmE;gBACnE,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC;oBAAE,GAAG,GAAG;oBAAE;oBAAO;gBAAK;gBACxD,mDAAmD;gBACnD,MAAM,iBAAiB,aAAa,KAAK,CAAC,UAAU;oBAChD,SAAS;gBACb;gBACA,SAAS,OAAO,EAAE,QAAQ;gBAC1B,MAAM,OAAO,OAAO,GAAG;oBAAE,SAAS;oBAAY;gBAAM;YACxD,OACK;gBACD,SAAS,OAAO,EAAE,QAAQ,aAAa,KAAK;YAChD;QACJ,EACA,OAAO,GAAG;YACN,OAAO,KAAK,CAAC,IAAI,4MAAA,CAAA,kBAAe,CAAC;YACjC,oEAAoE;YACpE,SAAS,OAAO,EAAE,QAAQ,aAAa,KAAK;QAChD;QACA,OAAO;IACX;IACA,iCAAiC;IACjC,IAAI;QACA,MAAM,EAAE,iBAAiB,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG;QAC5D,IAAI,iBAAiB,MAAM,kBAAkB;QAC7C,gDAAgD;QAChD,IAAI,kBACA,eAAe,OAAO,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,GAAG,IAAI;YACvD,MAAM,cAAc;YACpB,iBAAiB;QACrB;QACA,IAAI,gBAAgB;YAChB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;YAC1B,MAAM,mBAAmB,QAAQ,OAAO,CAAC,SAAS;YAClD,oEAAoE;YACpE,8DAA8D;YAC9D,8CAA8C;YAC9C,MAAM,8BAA8B,QAAQ,OAAO,CAAC,OAAO,KACvD,gBAAgB,OAChB,mBAAmB;YACvB,MAAM,aAAa,CAAA,GAAA,0NAAA,CAAA,WAAQ,AAAD,EAAE;YAC5B,oEAAoE;YACpE,mEAAmE;YACnE,IAAI,+BAA+B,KAAK,GAAG,IAAI;gBAC3C,MAAM,cAAc;oBAChB,cAAc;oBACd,SAAS;gBACb;YACJ;YACA,+CAA+C;YAC/C,MAAM,iBAAiB,MAAM,UAAU,OAAO,CAAC;gBAC3C,mCAAmC;gBACnC,kFAAkF;gBAClF,mBAAmB;gBACnB,SAAS;oBAAE,GAAG,OAAO;oBAAE;gBAAK;gBAC5B;gBACA;gBACA,GAAI,WAAW;oBAAE,SAAS;gBAAS,IAAI,CAAC,CAAC;YAC7C;YACA,qCAAqC;YACrC,SAAS,IAAI,GAAG;YAChB,oCAAoC;YACpC,SAAS,OAAO,EAAE,KAAK;gBACnB,MAAM,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI;gBACvC,OAAO;gBACP,SAAS;oBACL,GAAG,QAAQ,OAAO,CAAC,YAAY,CAAC,OAAO;oBACvC,SAAS;gBACb;YACJ;YACA,mBAAmB;YACnB,MAAM,OAAO,OAAO,GAAG;gBAAE,SAAS;YAAe;QACrD,OACK,IAAI,cAAc;YACnB,wEAAwE;YACxE,+CAA+C;YAC/C,SAAS,OAAO,EAAE,QAAQ,aAAa,KAAK;QAChD;IACJ,EACA,OAAO,GAAG;QACN,OAAO,KAAK,CAAC,IAAI,4MAAA,CAAA,oBAAiB,CAAC;IACvC;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 4644, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/actions/signin/authorization-url.js"], "sourcesContent": ["import * as checks from \"../callback/oauth/checks.js\";\nimport * as o from \"oauth4webapi\";\nimport { customFetch } from \"../../symbols.js\";\n/**\n * Generates an authorization/request token URL.\n *\n * [OAuth 2](https://www.oauth.com/oauth2-servers/authorization/the-authorization-request/)\n */\nexport async function getAuthorizationUrl(query, options) {\n    const { logger, provider } = options;\n    let url = provider.authorization?.url;\n    let as;\n    // Falls back to authjs.dev if the user only passed params\n    if (!url || url.host === \"authjs.dev\") {\n        // If url is undefined, we assume that issuer is always defined\n        // We check this in assert.ts\n        const issuer = new URL(provider.issuer);\n        const discoveryResponse = await o.discoveryRequest(issuer, {\n            [o.customFetch]: provider[customFetch],\n            // TODO: move away from allowing insecure HTTP requests\n            [o.allowInsecureRequests]: true,\n        });\n        const as = await o\n            .processDiscoveryResponse(issuer, discoveryResponse)\n            .catch((error) => {\n            if (!(error instanceof TypeError) || error.message !== \"Invalid URL\")\n                throw error;\n            throw new TypeError(`Discovery request responded with an invalid issuer. expected: ${issuer}`);\n        });\n        if (!as.authorization_endpoint) {\n            throw new TypeError(\"Authorization server did not provide an authorization endpoint.\");\n        }\n        url = new URL(as.authorization_endpoint);\n    }\n    const authParams = url.searchParams;\n    let redirect_uri = provider.callbackUrl;\n    let data;\n    if (!options.isOnRedirectProxy && provider.redirectProxyUrl) {\n        redirect_uri = provider.redirectProxyUrl;\n        data = provider.callbackUrl;\n        logger.debug(\"using redirect proxy\", { redirect_uri, data });\n    }\n    const params = Object.assign({\n        response_type: \"code\",\n        // clientId can technically be undefined, should we check this in assert.ts or rely on the Authorization Server to do it?\n        client_id: provider.clientId,\n        redirect_uri,\n        // @ts-expect-error TODO:\n        ...provider.authorization?.params,\n    }, Object.fromEntries(provider.authorization?.url.searchParams ?? []), query);\n    for (const k in params)\n        authParams.set(k, params[k]);\n    const cookies = [];\n    if (\n    // Otherwise \"POST /redirect_uri\" wouldn't include the cookies\n    provider.authorization?.url.searchParams.get(\"response_mode\") ===\n        \"form_post\") {\n        options.cookies.state.options.sameSite = \"none\";\n        options.cookies.state.options.secure = true;\n        options.cookies.nonce.options.sameSite = \"none\";\n        options.cookies.nonce.options.secure = true;\n    }\n    const state = await checks.state.create(options, data);\n    if (state) {\n        authParams.set(\"state\", state.value);\n        cookies.push(state.cookie);\n    }\n    if (provider.checks?.includes(\"pkce\")) {\n        if (as && !as.code_challenge_methods_supported?.includes(\"S256\")) {\n            // We assume S256 PKCE support, if the server does not advertise that,\n            // a random `nonce` must be used for CSRF protection.\n            if (provider.type === \"oidc\")\n                provider.checks = [\"nonce\"];\n        }\n        else {\n            const { value, cookie } = await checks.pkce.create(options);\n            authParams.set(\"code_challenge\", value);\n            authParams.set(\"code_challenge_method\", \"S256\");\n            cookies.push(cookie);\n        }\n    }\n    const nonce = await checks.nonce.create(options);\n    if (nonce) {\n        authParams.set(\"nonce\", nonce.value);\n        cookies.push(nonce.cookie);\n    }\n    // TODO: This does not work in normalizeOAuth because authorization endpoint can come from discovery\n    // Need to make normalizeOAuth async\n    if (provider.type === \"oidc\" && !url.searchParams.has(\"scope\")) {\n        url.searchParams.set(\"scope\", \"openid profile email\");\n    }\n    logger.debug(\"authorization url is ready\", { url, cookies, provider });\n    return { redirect: url.toString(), cookies };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAMO,eAAe,oBAAoB,KAAK,EAAE,OAAO;IACpD,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG;IAC7B,IAAI,MAAM,SAAS,aAAa,EAAE;IAClC,IAAI;IACJ,0DAA0D;IAC1D,IAAI,CAAC,OAAO,IAAI,IAAI,KAAK,cAAc;QACnC,+DAA+D;QAC/D,6BAA6B;QAC7B,MAAM,SAAS,IAAI,IAAI,SAAS,MAAM;QACtC,MAAM,oBAAoB,MAAM,CAAA,GAAA,+MAAA,CAAA,mBAAkB,AAAD,EAAE,QAAQ;YACvD,CAAC,+MAAA,CAAA,cAAa,CAAC,EAAE,QAAQ,CAAC,oNAAA,CAAA,cAAW,CAAC;YACtC,uDAAuD;YACvD,CAAC,+MAAA,CAAA,wBAAuB,CAAC,EAAE;QAC/B;QACA,MAAM,KAAK,MAAM,CAAA,GAAA,+MAAA,CAAA,2BACY,AAAD,EAAE,QAAQ,mBACjC,KAAK,CAAC,CAAC;YACR,IAAI,CAAC,CAAC,iBAAiB,SAAS,KAAK,MAAM,OAAO,KAAK,eACnD,MAAM;YACV,MAAM,IAAI,UAAU,CAAC,8DAA8D,EAAE,QAAQ;QACjG;QACA,IAAI,CAAC,GAAG,sBAAsB,EAAE;YAC5B,MAAM,IAAI,UAAU;QACxB;QACA,MAAM,IAAI,IAAI,GAAG,sBAAsB;IAC3C;IACA,MAAM,aAAa,IAAI,YAAY;IACnC,IAAI,eAAe,SAAS,WAAW;IACvC,IAAI;IACJ,IAAI,CAAC,QAAQ,iBAAiB,IAAI,SAAS,gBAAgB,EAAE;QACzD,eAAe,SAAS,gBAAgB;QACxC,OAAO,SAAS,WAAW;QAC3B,OAAO,KAAK,CAAC,wBAAwB;YAAE;YAAc;QAAK;IAC9D;IACA,MAAM,SAAS,OAAO,MAAM,CAAC;QACzB,eAAe;QACf,yHAAyH;QACzH,WAAW,SAAS,QAAQ;QAC5B;QACA,yBAAyB;QACzB,GAAG,SAAS,aAAa,EAAE,MAAM;IACrC,GAAG,OAAO,WAAW,CAAC,SAAS,aAAa,EAAE,IAAI,gBAAgB,EAAE,GAAG;IACvE,IAAK,MAAM,KAAK,OACZ,WAAW,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE;IAC/B,MAAM,UAAU,EAAE;IAClB,IACA,8DAA8D;IAC9D,SAAS,aAAa,EAAE,IAAI,aAAa,IAAI,qBACzC,aAAa;QACb,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,GAAG;QACzC,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG;QACvC,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,GAAG;QACzC,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG;IAC3C;IACA,MAAM,QAAQ,MAAM,mPAAA,CAAA,QAAY,CAAC,MAAM,CAAC,SAAS;IACjD,IAAI,OAAO;QACP,WAAW,GAAG,CAAC,SAAS,MAAM,KAAK;QACnC,QAAQ,IAAI,CAAC,MAAM,MAAM;IAC7B;IACA,IAAI,SAAS,MAAM,EAAE,SAAS,SAAS;QACnC,IAAI,MAAM,CAAC,GAAG,gCAAgC,EAAE,SAAS,SAAS;YAC9D,sEAAsE;YACtE,qDAAqD;YACrD,IAAI,SAAS,IAAI,KAAK,QAClB,SAAS,MAAM,GAAG;gBAAC;aAAQ;QACnC,OACK;YACD,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,mPAAA,CAAA,OAAW,CAAC,MAAM,CAAC;YACnD,WAAW,GAAG,CAAC,kBAAkB;YACjC,WAAW,GAAG,CAAC,yBAAyB;YACxC,QAAQ,IAAI,CAAC;QACjB;IACJ;IACA,MAAM,QAAQ,MAAM,mPAAA,CAAA,QAAY,CAAC,MAAM,CAAC;IACxC,IAAI,OAAO;QACP,WAAW,GAAG,CAAC,SAAS,MAAM,KAAK;QACnC,QAAQ,IAAI,CAAC,MAAM,MAAM;IAC7B;IACA,oGAAoG;IACpG,oCAAoC;IACpC,IAAI,SAAS,IAAI,KAAK,UAAU,CAAC,IAAI,YAAY,CAAC,GAAG,CAAC,UAAU;QAC5D,IAAI,YAAY,CAAC,GAAG,CAAC,SAAS;IAClC;IACA,OAAO,KAAK,CAAC,8BAA8B;QAAE;QAAK;QAAS;IAAS;IACpE,OAAO;QAAE,UAAU,IAAI,QAAQ;QAAI;IAAQ;AAC/C", "ignoreList": [0]}}, {"offset": {"line": 4749, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/actions/signin/send-token.js"], "sourcesContent": ["import { createHash, randomString, toRequest } from \"../../utils/web.js\";\nimport { AccessDenied } from \"../../../errors.js\";\n/**\n * Starts an e-mail login flow, by generating a token,\n * and sending it to the user's e-mail (with the help of a DB adapter).\n * At the end, it returns a redirect to the `verify-request` page.\n */\nexport async function sendToken(request, options) {\n    const { body } = request;\n    const { provider, callbacks, adapter } = options;\n    const normalizer = provider.normalizeIdentifier ?? defaultNormalizer;\n    const email = normalizer(body?.email);\n    const defaultUser = { id: crypto.randomUUID(), email, emailVerified: null };\n    const user = (await adapter.getUserByEmail(email)) ?? defaultUser;\n    const account = {\n        providerAccountId: email,\n        userId: user.id,\n        type: \"email\",\n        provider: provider.id,\n    };\n    let authorized;\n    try {\n        authorized = await callbacks.signIn({\n            user,\n            account,\n            email: { verificationRequest: true },\n        });\n    }\n    catch (e) {\n        throw new AccessDenied(e);\n    }\n    if (!authorized)\n        throw new AccessDenied(\"AccessDenied\");\n    if (typeof authorized === \"string\") {\n        return {\n            redirect: await callbacks.redirect({\n                url: authorized,\n                baseUrl: options.url.origin,\n            }),\n        };\n    }\n    const { callbackUrl, theme } = options;\n    const token = (await provider.generateVerificationToken?.()) ?? randomString(32);\n    const ONE_DAY_IN_SECONDS = 86400;\n    const expires = new Date(Date.now() + (provider.maxAge ?? ONE_DAY_IN_SECONDS) * 1000);\n    const secret = provider.secret ?? options.secret;\n    const baseUrl = new URL(options.basePath, options.url.origin);\n    const sendRequest = provider.sendVerificationRequest({\n        identifier: email,\n        token,\n        expires,\n        url: `${baseUrl}/callback/${provider.id}?${new URLSearchParams({\n            callbackUrl,\n            token,\n            email,\n        })}`,\n        provider,\n        theme,\n        request: toRequest(request),\n    });\n    const createToken = adapter.createVerificationToken?.({\n        identifier: email,\n        token: await createHash(`${token}${secret}`),\n        expires,\n    });\n    await Promise.all([sendRequest, createToken]);\n    return {\n        redirect: `${baseUrl}/verify-request?${new URLSearchParams({\n            provider: provider.id,\n            type: provider.type,\n        })}`,\n    };\n}\nfunction defaultNormalizer(email) {\n    if (!email)\n        throw new Error(\"Missing email from request body.\");\n    // Get the first two elements only,\n    // separated by `@` from user input.\n    let [local, domain] = email.toLowerCase().trim().split(\"@\");\n    // The part before \"@\" can contain a \",\"\n    // but we remove it on the domain part\n    domain = domain.split(\",\")[0];\n    return `${local}@${domain}`;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAMO,eAAe,UAAU,OAAO,EAAE,OAAO;IAC5C,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;IACzC,MAAM,aAAa,SAAS,mBAAmB,IAAI;IACnD,MAAM,QAAQ,WAAW,MAAM;IAC/B,MAAM,cAAc;QAAE,IAAI,OAAO,UAAU;QAAI;QAAO,eAAe;IAAK;IAC1E,MAAM,OAAO,AAAC,MAAM,QAAQ,cAAc,CAAC,UAAW;IACtD,MAAM,UAAU;QACZ,mBAAmB;QACnB,QAAQ,KAAK,EAAE;QACf,MAAM;QACN,UAAU,SAAS,EAAE;IACzB;IACA,IAAI;IACJ,IAAI;QACA,aAAa,MAAM,UAAU,MAAM,CAAC;YAChC;YACA;YACA,OAAO;gBAAE,qBAAqB;YAAK;QACvC;IACJ,EACA,OAAO,GAAG;QACN,MAAM,IAAI,4MAAA,CAAA,eAAY,CAAC;IAC3B;IACA,IAAI,CAAC,YACD,MAAM,IAAI,4MAAA,CAAA,eAAY,CAAC;IAC3B,IAAI,OAAO,eAAe,UAAU;QAChC,OAAO;YACH,UAAU,MAAM,UAAU,QAAQ,CAAC;gBAC/B,KAAK;gBACL,SAAS,QAAQ,GAAG,CAAC,MAAM;YAC/B;QACJ;IACJ;IACA,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG;IAC/B,MAAM,QAAQ,AAAC,MAAM,SAAS,yBAAyB,QAAS,CAAA,GAAA,yNAAA,CAAA,eAAY,AAAD,EAAE;IAC7E,MAAM,qBAAqB;IAC3B,MAAM,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,CAAC,SAAS,MAAM,IAAI,kBAAkB,IAAI;IAChF,MAAM,SAAS,SAAS,MAAM,IAAI,QAAQ,MAAM;IAChD,MAAM,UAAU,IAAI,IAAI,QAAQ,QAAQ,EAAE,QAAQ,GAAG,CAAC,MAAM;IAC5D,MAAM,cAAc,SAAS,uBAAuB,CAAC;QACjD,YAAY;QACZ;QACA;QACA,KAAK,GAAG,QAAQ,UAAU,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,gBAAgB;YAC3D;YACA;YACA;QACJ,IAAI;QACJ;QACA;QACA,SAAS,CAAA,GAAA,yNAAA,CAAA,YAAS,AAAD,EAAE;IACvB;IACA,MAAM,cAAc,QAAQ,uBAAuB,GAAG;QAClD,YAAY;QACZ,OAAO,MAAM,CAAA,GAAA,yNAAA,CAAA,aAAU,AAAD,EAAE,GAAG,QAAQ,QAAQ;QAC3C;IACJ;IACA,MAAM,QAAQ,GAAG,CAAC;QAAC;QAAa;KAAY;IAC5C,OAAO;QACH,UAAU,GAAG,QAAQ,gBAAgB,EAAE,IAAI,gBAAgB;YACvD,UAAU,SAAS,EAAE;YACrB,MAAM,SAAS,IAAI;QACvB,IAAI;IACR;AACJ;AACA,SAAS,kBAAkB,KAAK;IAC5B,IAAI,CAAC,OACD,MAAM,IAAI,MAAM;IACpB,mCAAmC;IACnC,oCAAoC;IACpC,IAAI,CAAC,OAAO,OAAO,GAAG,MAAM,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC;IACvD,wCAAwC;IACxC,sCAAsC;IACtC,SAAS,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7B,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ;AAC/B", "ignoreList": [0]}}, {"offset": {"line": 4845, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/actions/signin/index.js"], "sourcesContent": ["import { getAuthorizationUrl } from \"./authorization-url.js\";\nimport { sendToken } from \"./send-token.js\";\nexport async function signIn(request, cookies, options) {\n    const signInUrl = `${options.url.origin}${options.basePath}/signin`;\n    if (!options.provider)\n        return { redirect: signInUrl, cookies };\n    switch (options.provider.type) {\n        case \"oauth\":\n        case \"oidc\": {\n            const { redirect, cookies: authCookies } = await getAuthorizationUrl(request.query, options);\n            if (authCookies)\n                cookies.push(...authCookies);\n            return { redirect, cookies };\n        }\n        case \"email\": {\n            const response = await sendToken(request, options);\n            return { ...response, cookies };\n        }\n        default:\n            return { redirect: signInUrl, cookies };\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,eAAe,OAAO,OAAO,EAAE,OAAO,EAAE,OAAO;IAClD,MAAM,YAAY,GAAG,QAAQ,GAAG,CAAC,MAAM,GAAG,QAAQ,QAAQ,CAAC,OAAO,CAAC;IACnE,IAAI,CAAC,QAAQ,QAAQ,EACjB,OAAO;QAAE,UAAU;QAAW;IAAQ;IAC1C,OAAQ,QAAQ,QAAQ,CAAC,IAAI;QACzB,KAAK;QACL,KAAK;YAAQ;gBACT,MAAM,EAAE,QAAQ,EAAE,SAAS,WAAW,EAAE,GAAG,MAAM,CAAA,GAAA,sPAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,KAAK,EAAE;gBACpF,IAAI,aACA,QAAQ,IAAI,IAAI;gBACpB,OAAO;oBAAE;oBAAU;gBAAQ;YAC/B;QACA,KAAK;YAAS;gBACV,MAAM,WAAW,MAAM,CAAA,GAAA,+OAAA,CAAA,YAAS,AAAD,EAAE,SAAS;gBAC1C,OAAO;oBAAE,GAAG,QAAQ;oBAAE;gBAAQ;YAClC;QACA;YACI,OAAO;gBAAE,UAAU;gBAAW;YAAQ;IAC9C;AACJ", "ignoreList": [0]}}, {"offset": {"line": 4890, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/actions/signout.js"], "sourcesContent": ["import { SignOutError } from \"../../errors.js\";\n/**\n * Destroys the session.\n * If the session strategy is database,\n * The session is also deleted from the database.\n * In any case, the session cookie is cleared and\n * {@link AuthConfig[\"events\"].signOut} is emitted.\n */\nexport async function signOut(cookies, sessionStore, options) {\n    const { jwt, events, callbackUrl: redirect, logger, session } = options;\n    const sessionToken = sessionStore.value;\n    if (!sessionToken)\n        return { redirect, cookies };\n    try {\n        if (session.strategy === \"jwt\") {\n            const salt = options.cookies.sessionToken.name;\n            const token = await jwt.decode({ ...jwt, token: sessionToken, salt });\n            await events.signOut?.({ token });\n        }\n        else {\n            const session = await options.adapter?.deleteSession(sessionToken);\n            await events.signOut?.({ session });\n        }\n    }\n    catch (e) {\n        logger.error(new SignOutError(e));\n    }\n    cookies.push(...sessionStore.clean());\n    return { redirect, cookies };\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,eAAe,QAAQ,OAAO,EAAE,YAAY,EAAE,OAAO;IACxD,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,aAAa,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG;IAChE,MAAM,eAAe,aAAa,KAAK;IACvC,IAAI,CAAC,cACD,OAAO;QAAE;QAAU;IAAQ;IAC/B,IAAI;QACA,IAAI,QAAQ,QAAQ,KAAK,OAAO;YAC5B,MAAM,OAAO,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI;YAC9C,MAAM,QAAQ,MAAM,IAAI,MAAM,CAAC;gBAAE,GAAG,GAAG;gBAAE,OAAO;gBAAc;YAAK;YACnE,MAAM,OAAO,OAAO,GAAG;gBAAE;YAAM;QACnC,OACK;YACD,MAAM,UAAU,MAAM,QAAQ,OAAO,EAAE,cAAc;YACrD,MAAM,OAAO,OAAO,GAAG;gBAAE;YAAQ;QACrC;IACJ,EACA,OAAO,GAAG;QACN,OAAO,KAAK,CAAC,IAAI,4MAAA,CAAA,eAAY,CAAC;IAClC;IACA,QAAQ,IAAI,IAAI,aAAa,KAAK;IAClC,OAAO;QAAE;QAAU;IAAQ;AAC/B", "ignoreList": [0]}}, {"offset": {"line": 4934, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/utils/session.js"], "sourcesContent": ["/**\n * Returns the currently logged in user, if any.\n */\nexport async function getLoggedInUser(options, sessionStore) {\n    const { adapter, jwt, session: { strategy: sessionStrategy }, } = options;\n    const sessionToken = sessionStore.value;\n    if (!sessionToken)\n        return null;\n    // Try to decode JWT\n    if (sessionStrategy === \"jwt\") {\n        const salt = options.cookies.sessionToken.name;\n        const payload = await jwt.decode({ ...jwt, token: sessionToken, salt });\n        if (payload && payload.sub) {\n            return {\n                id: payload.sub,\n                name: payload.name,\n                email: payload.email,\n                image: payload.picture,\n            };\n        }\n    }\n    else {\n        const userAndSession = await adapter?.getSessionAndUser(sessionToken);\n        if (userAndSession) {\n            return userAndSession.user;\n        }\n    }\n    return null;\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACM,eAAe,gBAAgB,OAAO,EAAE,YAAY;IACvD,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,UAAU,eAAe,EAAE,EAAG,GAAG;IAClE,MAAM,eAAe,aAAa,KAAK;IACvC,IAAI,CAAC,cACD,OAAO;IACX,oBAAoB;IACpB,IAAI,oBAAoB,OAAO;QAC3B,MAAM,OAAO,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI;QAC9C,MAAM,UAAU,MAAM,IAAI,MAAM,CAAC;YAAE,GAAG,GAAG;YAAE,OAAO;YAAc;QAAK;QACrE,IAAI,WAAW,QAAQ,GAAG,EAAE;YACxB,OAAO;gBACH,IAAI,QAAQ,GAAG;gBACf,MAAM,QAAQ,IAAI;gBAClB,OAAO,QAAQ,KAAK;gBACpB,OAAO,QAAQ,OAAO;YAC1B;QACJ;IACJ,OACK;QACD,MAAM,iBAAiB,MAAM,SAAS,kBAAkB;QACxD,IAAI,gBAAgB;YAChB,OAAO,eAAe,IAAI;QAC9B;IACJ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 4973, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/actions/webauthn-options.js"], "sourcesContent": ["import { getLoggedInUser } from \"../utils/session.js\";\nimport { assertInternalOptionsWebAuthn, inferWebAuthnOptions, getAuthenticationResponse, getRegistrationResponse, } from \"../utils/webauthn-utils.js\";\n/**\n * Returns authentication or registration options for a WebAuthn flow\n * depending on the parameters provided.\n */\nexport async function webAuthnOptions(request, options, sessionStore, cookies\n// @ts-expect-error issue with returning from a switch case\n) {\n    // Return an error if the adapter is missing or if the provider\n    // is not a webauthn provider.\n    const narrowOptions = assertInternalOptionsWebAuthn(options);\n    const { provider } = narrowOptions;\n    // Extract the action from the query parameters\n    const { action } = (request.query ?? {});\n    // Action must be either \"register\", \"authenticate\", or undefined\n    if (action !== \"register\" &&\n        action !== \"authenticate\" &&\n        typeof action !== \"undefined\") {\n        return {\n            status: 400,\n            body: { error: \"Invalid action\" },\n            cookies,\n            headers: {\n                \"Content-Type\": \"application/json\",\n            },\n        };\n    }\n    // Get the user info from the session\n    const sessionUser = await getLoggedInUser(options, sessionStore);\n    // Extract user info from request\n    // If session user exists, we don't need to call getUserInfo\n    const getUserInfoResponse = sessionUser\n        ? {\n            user: sessionUser,\n            exists: true,\n        }\n        : await provider.getUserInfo(options, request);\n    const userInfo = getUserInfoResponse?.user;\n    // Make a decision on what kind of webauthn options to return\n    const decision = inferWebAuthnOptions(action, !!sessionUser, getUserInfoResponse);\n    switch (decision) {\n        case \"authenticate\":\n            return getAuthenticationResponse(narrowOptions, request, userInfo, cookies);\n        case \"register\":\n            if (typeof userInfo?.email === \"string\") {\n                return getRegistrationResponse(narrowOptions, request, userInfo, cookies);\n            }\n            break;\n        default:\n            return {\n                status: 400,\n                body: { error: \"Invalid request\" },\n                cookies,\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                },\n            };\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAKO,eAAe,gBAAgB,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO;IAGzE,+DAA+D;IAC/D,8BAA8B;IAC9B,MAAM,gBAAgB,CAAA,GAAA,uOAAA,CAAA,gCAA6B,AAAD,EAAE;IACpD,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,+CAA+C;IAC/C,MAAM,EAAE,MAAM,EAAE,GAAI,QAAQ,KAAK,IAAI,CAAC;IACtC,iEAAiE;IACjE,IAAI,WAAW,cACX,WAAW,kBACX,OAAO,WAAW,aAAa;QAC/B,OAAO;YACH,QAAQ;YACR,MAAM;gBAAE,OAAO;YAAiB;YAChC;YACA,SAAS;gBACL,gBAAgB;YACpB;QACJ;IACJ;IACA,qCAAqC;IACrC,MAAM,cAAc,MAAM,CAAA,GAAA,6NAAA,CAAA,kBAAe,AAAD,EAAE,SAAS;IACnD,iCAAiC;IACjC,4DAA4D;IAC5D,MAAM,sBAAsB,cACtB;QACE,MAAM;QACN,QAAQ;IACZ,IACE,MAAM,SAAS,WAAW,CAAC,SAAS;IAC1C,MAAM,WAAW,qBAAqB;IACtC,6DAA6D;IAC7D,MAAM,WAAW,CAAA,GAAA,uOAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,CAAC,CAAC,aAAa;IAC7D,OAAQ;QACJ,KAAK;YACD,OAAO,CAAA,GAAA,uOAAA,CAAA,4BAAyB,AAAD,EAAE,eAAe,SAAS,UAAU;QACvE,KAAK;YACD,IAAI,OAAO,UAAU,UAAU,UAAU;gBACrC,OAAO,CAAA,GAAA,uOAAA,CAAA,0BAAuB,AAAD,EAAE,eAAe,SAAS,UAAU;YACrE;YACA;QACJ;YACI,OAAO;gBACH,QAAQ;gBACR,MAAM;oBAAE,OAAO;gBAAkB;gBACjC;gBACA,SAAS;oBACL,gBAAgB;gBACpB;YACJ;IACR;AACJ", "ignoreList": [0]}}, {"offset": {"line": 5038, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/actions/index.js"], "sourcesContent": ["export { callback } from \"./callback/index.js\";\nexport { session } from \"./session.js\";\nexport { signIn } from \"./signin/index.js\";\nexport { signOut } from \"./signout.js\";\nexport { webAuthnOptions } from \"./webauthn-options.js\";\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 5068, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/index.js"], "sourcesContent": ["import { UnknownAction } from \"../errors.js\";\nimport { SessionStore } from \"./utils/cookie.js\";\nimport { init } from \"./init.js\";\nimport renderPage from \"./pages/index.js\";\nimport * as actions from \"./actions/index.js\";\nimport { validateCSRF } from \"./actions/callback/oauth/csrf-token.js\";\nimport { skipCSRFCheck } from \"./symbols.js\";\nexport { customFetch, raw, skipCSRFCheck } from \"./symbols.js\";\n/** @internal */\nexport async function AuthInternal(request, authOptions) {\n    const { action, providerId, error, method } = request;\n    const csrfDisabled = authOptions.skipCSRFCheck === skipCSRFCheck;\n    const { options, cookies } = await init({\n        authOptions,\n        action,\n        providerId,\n        url: request.url,\n        callbackUrl: request.body?.callbackUrl ?? request.query?.callbackUrl,\n        csrfToken: request.body?.csrfToken,\n        cookies: request.cookies,\n        isPost: method === \"POST\",\n        csrfDisabled,\n    });\n    const sessionStore = new SessionStore(options.cookies.sessionToken, request.cookies, options.logger);\n    if (method === \"GET\") {\n        const render = renderPage({ ...options, query: request.query, cookies });\n        switch (action) {\n            case \"callback\":\n                return await actions.callback(request, options, sessionStore, cookies);\n            case \"csrf\":\n                return render.csrf(csrfDisabled, options, cookies);\n            case \"error\":\n                return render.error(error);\n            case \"providers\":\n                return render.providers(options.providers);\n            case \"session\":\n                return await actions.session(options, sessionStore, cookies);\n            case \"signin\":\n                return render.signin(providerId, error);\n            case \"signout\":\n                return render.signout();\n            case \"verify-request\":\n                return render.verifyRequest();\n            case \"webauthn-options\":\n                return await actions.webAuthnOptions(request, options, sessionStore, cookies);\n            default:\n        }\n    }\n    else {\n        const { csrfTokenVerified } = options;\n        switch (action) {\n            case \"callback\":\n                if (options.provider.type === \"credentials\")\n                    // Verified CSRF Token required for credentials providers only\n                    validateCSRF(action, csrfTokenVerified);\n                return await actions.callback(request, options, sessionStore, cookies);\n            case \"session\":\n                validateCSRF(action, csrfTokenVerified);\n                return await actions.session(options, sessionStore, cookies, true, request.body?.data);\n            case \"signin\":\n                validateCSRF(action, csrfTokenVerified);\n                return await actions.signIn(request, cookies, options);\n            case \"signout\":\n                validateCSRF(action, csrfTokenVerified);\n                return await actions.signOut(cookies, sessionStore, options);\n            default:\n        }\n    }\n    throw new UnknownAction(`Cannot handle action: ${action}`);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;;;;AAGO,eAAe,aAAa,OAAO,EAAE,WAAW;IACnD,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;IAC9C,MAAM,eAAe,YAAY,aAAa,KAAK,oNAAA,CAAA,gBAAa;IAChE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,iNAAA,CAAA,OAAI,AAAD,EAAE;QACpC;QACA;QACA;QACA,KAAK,QAAQ,GAAG;QAChB,aAAa,QAAQ,IAAI,EAAE,eAAe,QAAQ,KAAK,EAAE;QACzD,WAAW,QAAQ,IAAI,EAAE;QACzB,SAAS,QAAQ,OAAO;QACxB,QAAQ,WAAW;QACnB;IACJ;IACA,MAAM,eAAe,IAAI,4NAAA,CAAA,eAAY,CAAC,QAAQ,OAAO,CAAC,YAAY,EAAE,QAAQ,OAAO,EAAE,QAAQ,MAAM;IACnG,IAAI,WAAW,OAAO;QAClB,MAAM,SAAS,CAAA,GAAA,2NAAA,CAAA,UAAU,AAAD,EAAE;YAAE,GAAG,OAAO;YAAE,OAAO,QAAQ,KAAK;YAAE;QAAQ;QACtE,OAAQ;YACJ,KAAK;gBACD,OAAO,MAAM,CAAA,GAAA,yOAAA,CAAA,WAAgB,AAAD,EAAE,SAAS,SAAS,cAAc;YAClE,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,cAAc,SAAS;YAC9C,KAAK;gBACD,OAAO,OAAO,KAAK,CAAC;YACxB,KAAK;gBACD,OAAO,OAAO,SAAS,CAAC,QAAQ,SAAS;YAC7C,KAAK;gBACD,OAAO,MAAM,CAAA,GAAA,+NAAA,CAAA,UAAe,AAAD,EAAE,SAAS,cAAc;YACxD,KAAK;gBACD,OAAO,OAAO,MAAM,CAAC,YAAY;YACrC,KAAK;gBACD,OAAO,OAAO,OAAO;YACzB,KAAK;gBACD,OAAO,OAAO,aAAa;YAC/B,KAAK;gBACD,OAAO,MAAM,CAAA,GAAA,2OAAA,CAAA,kBAAuB,AAAD,EAAE,SAAS,SAAS,cAAc;YACzE;QACJ;IACJ,OACK;QACD,MAAM,EAAE,iBAAiB,EAAE,GAAG;QAC9B,OAAQ;YACJ,KAAK;gBACD,IAAI,QAAQ,QAAQ,CAAC,IAAI,KAAK,eAC1B,8DAA8D;gBAC9D,CAAA,GAAA,0PAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;gBACzB,OAAO,MAAM,CAAA,GAAA,yOAAA,CAAA,WAAgB,AAAD,EAAE,SAAS,SAAS,cAAc;YAClE,KAAK;gBACD,CAAA,GAAA,0PAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;gBACrB,OAAO,MAAM,CAAA,GAAA,+NAAA,CAAA,UAAe,AAAD,EAAE,SAAS,cAAc,SAAS,MAAM,QAAQ,IAAI,EAAE;YACrF,KAAK;gBACD,CAAA,GAAA,0PAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;gBACrB,OAAO,MAAM,CAAA,GAAA,uOAAA,CAAA,SAAc,AAAD,EAAE,SAAS,SAAS;YAClD,KAAK;gBACD,CAAA,GAAA,0PAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;gBACrB,OAAO,MAAM,CAAA,GAAA,+NAAA,CAAA,UAAe,AAAD,EAAE,SAAS,cAAc;YACxD;QACJ;IACJ;IACA,MAAM,IAAI,4MAAA,CAAA,gBAAa,CAAC,CAAC,sBAAsB,EAAE,QAAQ;AAC7D", "ignoreList": [0]}}, {"offset": {"line": 5175, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/lib/utils/env.js"], "sourcesContent": ["import { setLogger } from \"./logger.js\";\n/**\n *  Set default env variables on the config object\n * @param suppressWarnings intended for framework authors.\n */\nexport function setEnvDefaults(envObject, config, suppressBasePathWarning = false) {\n    try {\n        const url = envObject.AUTH_URL;\n        if (url) {\n            if (config.basePath) {\n                if (!suppressBasePathWarning) {\n                    const logger = setLogger(config);\n                    logger.warn(\"env-url-basepath-redundant\");\n                }\n            }\n            else {\n                config.basePath = new URL(url).pathname;\n            }\n        }\n    }\n    catch {\n        // Catching and swallowing potential URL parsing errors, we'll fall\n        // back to `/auth` below.\n    }\n    finally {\n        config.basePath ?? (config.basePath = `/auth`);\n    }\n    if (!config.secret?.length) {\n        config.secret = [];\n        const secret = envObject.AUTH_SECRET;\n        if (secret)\n            config.secret.push(secret);\n        for (const i of [1, 2, 3]) {\n            const secret = envObject[`AUTH_SECRET_${i}`];\n            if (secret)\n                config.secret.unshift(secret);\n        }\n    }\n    config.redirectProxyUrl ?? (config.redirectProxyUrl = envObject.AUTH_REDIRECT_PROXY_URL);\n    config.trustHost ?? (config.trustHost = !!(envObject.AUTH_URL ??\n        envObject.AUTH_TRUST_HOST ??\n        envObject.VERCEL ??\n        envObject.CF_PAGES ??\n        envObject.NODE_ENV !== \"production\"));\n    config.providers = config.providers.map((provider) => {\n        const { id } = typeof provider === \"function\" ? provider({}) : provider;\n        const ID = id.toUpperCase().replace(/-/g, \"_\");\n        const clientId = envObject[`AUTH_${ID}_ID`];\n        const clientSecret = envObject[`AUTH_${ID}_SECRET`];\n        const issuer = envObject[`AUTH_${ID}_ISSUER`];\n        const apiKey = envObject[`AUTH_${ID}_KEY`];\n        const finalProvider = typeof provider === \"function\"\n            ? provider({ clientId, clientSecret, issuer, apiKey })\n            : provider;\n        if (finalProvider.type === \"oauth\" || finalProvider.type === \"oidc\") {\n            finalProvider.clientId ?? (finalProvider.clientId = clientId);\n            finalProvider.clientSecret ?? (finalProvider.clientSecret = clientSecret);\n            finalProvider.issuer ?? (finalProvider.issuer = issuer);\n        }\n        else if (finalProvider.type === \"email\") {\n            finalProvider.apiKey ?? (finalProvider.apiKey = apiKey);\n        }\n        return finalProvider;\n    });\n}\nexport function createActionURL(action, protocol, headers, envObject, config) {\n    const basePath = config?.basePath;\n    const envUrl = envObject.AUTH_URL ?? envObject.NEXTAUTH_URL;\n    let url;\n    if (envUrl) {\n        url = new URL(envUrl);\n        if (basePath && basePath !== \"/\" && url.pathname !== \"/\") {\n            if (url.pathname !== basePath) {\n                const logger = setLogger(config);\n                logger.warn(\"env-url-basepath-mismatch\");\n            }\n            url.pathname = \"/\";\n        }\n    }\n    else {\n        const detectedHost = headers.get(\"x-forwarded-host\") ?? headers.get(\"host\");\n        const detectedProtocol = headers.get(\"x-forwarded-proto\") ?? protocol ?? \"https\";\n        const _protocol = detectedProtocol.endsWith(\":\")\n            ? detectedProtocol\n            : detectedProtocol + \":\";\n        url = new URL(`${_protocol}//${detectedHost}`);\n    }\n    // remove trailing slash\n    const sanitizedUrl = url.toString().replace(/\\/$/, \"\");\n    if (basePath) {\n        // remove leading and trailing slash\n        const sanitizedBasePath = basePath?.replace(/(^\\/|\\/$)/g, \"\") ?? \"\";\n        return new URL(`${sanitizedUrl}/${sanitizedBasePath}/${action}`);\n    }\n    return new URL(`${sanitizedUrl}/${action}`);\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAKO,SAAS,eAAe,SAAS,EAAE,MAAM,EAAE,0BAA0B,KAAK;IAC7E,IAAI;QACA,MAAM,MAAM,UAAU,QAAQ;QAC9B,IAAI,KAAK;YACL,IAAI,OAAO,QAAQ,EAAE;gBACjB,IAAI,CAAC,yBAAyB;oBAC1B,MAAM,SAAS,CAAA,GAAA,4NAAA,CAAA,YAAS,AAAD,EAAE;oBACzB,OAAO,IAAI,CAAC;gBAChB;YACJ,OACK;gBACD,OAAO,QAAQ,GAAG,IAAI,IAAI,KAAK,QAAQ;YAC3C;QACJ;IACJ,EACA,OAAM;IACF,mEAAmE;IACnE,yBAAyB;IAC7B,SACQ;QACJ,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,GAAG,CAAC,KAAK,CAAC;IACjD;IACA,IAAI,CAAC,OAAO,MAAM,EAAE,QAAQ;QACxB,OAAO,MAAM,GAAG,EAAE;QAClB,MAAM,SAAS,UAAU,WAAW;QACpC,IAAI,QACA,OAAO,MAAM,CAAC,IAAI,CAAC;QACvB,KAAK,MAAM,KAAK;YAAC;YAAG;YAAG;SAAE,CAAE;YACvB,MAAM,SAAS,SAAS,CAAC,CAAC,YAAY,EAAE,GAAG,CAAC;YAC5C,IAAI,QACA,OAAO,MAAM,CAAC,OAAO,CAAC;QAC9B;IACJ;IACA,OAAO,gBAAgB,IAAI,CAAC,OAAO,gBAAgB,GAAG,UAAU,uBAAuB;IACvF,OAAO,SAAS,IAAI,CAAC,OAAO,SAAS,GAAG,CAAC,CAAC,CAAC,UAAU,QAAQ,IACzD,UAAU,eAAe,IACzB,UAAU,MAAM,IAChB,UAAU,QAAQ,IAClB,UAAU,QAAQ,KAAK,YAAY,CAAC;IACxC,OAAO,SAAS,GAAG,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;QACrC,MAAM,EAAE,EAAE,EAAE,GAAG,OAAO,aAAa,aAAa,SAAS,CAAC,KAAK;QAC/D,MAAM,KAAK,GAAG,WAAW,GAAG,OAAO,CAAC,MAAM;QAC1C,MAAM,WAAW,SAAS,CAAC,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC;QAC3C,MAAM,eAAe,SAAS,CAAC,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC,CAAC;QACnD,MAAM,SAAS,SAAS,CAAC,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC,CAAC;QAC7C,MAAM,SAAS,SAAS,CAAC,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;QAC1C,MAAM,gBAAgB,OAAO,aAAa,aACpC,SAAS;YAAE;YAAU;YAAc;YAAQ;QAAO,KAClD;QACN,IAAI,cAAc,IAAI,KAAK,WAAW,cAAc,IAAI,KAAK,QAAQ;YACjE,cAAc,QAAQ,IAAI,CAAC,cAAc,QAAQ,GAAG,QAAQ;YAC5D,cAAc,YAAY,IAAI,CAAC,cAAc,YAAY,GAAG,YAAY;YACxE,cAAc,MAAM,IAAI,CAAC,cAAc,MAAM,GAAG,MAAM;QAC1D,OACK,IAAI,cAAc,IAAI,KAAK,SAAS;YACrC,cAAc,MAAM,IAAI,CAAC,cAAc,MAAM,GAAG,MAAM;QAC1D;QACA,OAAO;IACX;AACJ;AACO,SAAS,gBAAgB,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM;IACxE,MAAM,WAAW,QAAQ;IACzB,MAAM,SAAS,UAAU,QAAQ,IAAI,UAAU,YAAY;IAC3D,IAAI;IACJ,IAAI,QAAQ;QACR,MAAM,IAAI,IAAI;QACd,IAAI,YAAY,aAAa,OAAO,IAAI,QAAQ,KAAK,KAAK;YACtD,IAAI,IAAI,QAAQ,KAAK,UAAU;gBAC3B,MAAM,SAAS,CAAA,GAAA,4NAAA,CAAA,YAAS,AAAD,EAAE;gBACzB,OAAO,IAAI,CAAC;YAChB;YACA,IAAI,QAAQ,GAAG;QACnB;IACJ,OACK;QACD,MAAM,eAAe,QAAQ,GAAG,CAAC,uBAAuB,QAAQ,GAAG,CAAC;QACpE,MAAM,mBAAmB,QAAQ,GAAG,CAAC,wBAAwB,YAAY;QACzE,MAAM,YAAY,iBAAiB,QAAQ,CAAC,OACtC,mBACA,mBAAmB;QACzB,MAAM,IAAI,IAAI,GAAG,UAAU,EAAE,EAAE,cAAc;IACjD;IACA,wBAAwB;IACxB,MAAM,eAAe,IAAI,QAAQ,GAAG,OAAO,CAAC,OAAO;IACnD,IAAI,UAAU;QACV,oCAAoC;QACpC,MAAM,oBAAoB,UAAU,QAAQ,cAAc,OAAO;QACjE,OAAO,IAAI,IAAI,GAAG,aAAa,CAAC,EAAE,kBAAkB,CAAC,EAAE,QAAQ;IACnE;IACA,OAAO,IAAI,IAAI,GAAG,aAAa,CAAC,EAAE,QAAQ;AAC9C", "ignoreList": [0]}}, {"offset": {"line": 5272, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/index.js"], "sourcesContent": ["/**\n *\n * :::warning Experimental\n * `@auth/core` is under active development.\n * :::\n *\n * This is the main entry point to the Auth.js library.\n *\n * Based on the {@link https://developer.mozilla.org/en-US/docs/Web/API/Request Request}\n * and {@link https://developer.mozilla.org/en-US/docs/Web/API/Response Response} Web standard APIs.\n * Primarily used to implement [framework](https://authjs.dev/getting-started/integrations)-specific packages,\n * but it can also be used directly.\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install @auth/core\n * ```\n *\n * ## Usage\n *\n * ```ts\n * import { Auth } from \"@auth/core\"\n *\n * const request = new Request(\"https://example.com\")\n * const response = await Auth(request, {...})\n *\n * console.log(response instanceof Response) // true\n * ```\n *\n * ## Resources\n *\n * - [Getting started](https://authjs.dev/getting-started)\n * - [Guides](https://authjs.dev/guides)\n *\n * @module @auth/core\n */\nimport { assertConfig } from \"./lib/utils/assert.js\";\nimport { AuthError, CredentialsSignin, ErrorPageLoop, isClientError, } from \"./errors.js\";\nimport { AuthInternal, raw, skipCSRFCheck } from \"./lib/index.js\";\nimport { setEnvDefaults, createActionURL } from \"./lib/utils/env.js\";\nimport renderPage from \"./lib/pages/index.js\";\nimport { setLogger } from \"./lib/utils/logger.js\";\nimport { toInternalRequest, toResponse } from \"./lib/utils/web.js\";\nimport { isAuthAction } from \"./lib/utils/actions.js\";\nexport { customFetch } from \"./lib/symbols.js\";\nexport { skipCSRFCheck, raw, setEnvDefaults, createActionURL, isAuthAction };\n/**\n * Core functionality provided by Auth.js.\n *\n * Receives a standard {@link Request} and returns a {@link Response}.\n *\n * @example\n * ```ts\n * import { Auth } from \"@auth/core\"\n *\n * const request = new Request(\"https://example.com\")\n * const response = await Auth(request, {\n *   providers: [Google],\n *   secret: \"...\",\n *   trustHost: true,\n * })\n *```\n * @see [Documentation](https://authjs.dev)\n */\nexport async function Auth(request, config) {\n    const logger = setLogger(config);\n    const internalRequest = await toInternalRequest(request, config);\n    // There was an error parsing the request\n    if (!internalRequest)\n        return Response.json(`Bad request.`, { status: 400 });\n    const warningsOrError = assertConfig(internalRequest, config);\n    if (Array.isArray(warningsOrError)) {\n        warningsOrError.forEach(logger.warn);\n    }\n    else if (warningsOrError) {\n        // If there's an error in the user config, bail out early\n        logger.error(warningsOrError);\n        const htmlPages = new Set([\n            \"signin\",\n            \"signout\",\n            \"error\",\n            \"verify-request\",\n        ]);\n        if (!htmlPages.has(internalRequest.action) ||\n            internalRequest.method !== \"GET\") {\n            const message = \"There was a problem with the server configuration. Check the server logs for more information.\";\n            return Response.json({ message }, { status: 500 });\n        }\n        const { pages, theme } = config;\n        // If this is true, the config required auth on the error page\n        // which could cause a redirect loop\n        const authOnErrorPage = pages?.error &&\n            internalRequest.url.searchParams\n                .get(\"callbackUrl\")\n                ?.startsWith(pages.error);\n        // Either there was no error page configured or the configured one contains infinite redirects\n        if (!pages?.error || authOnErrorPage) {\n            if (authOnErrorPage) {\n                logger.error(new ErrorPageLoop(`The error page ${pages?.error} should not require authentication`));\n            }\n            const page = renderPage({ theme }).error(\"Configuration\");\n            return toResponse(page);\n        }\n        const url = `${internalRequest.url.origin}${pages.error}?error=Configuration`;\n        return Response.redirect(url);\n    }\n    const isRedirect = request.headers?.has(\"X-Auth-Return-Redirect\");\n    const isRaw = config.raw === raw;\n    try {\n        const internalResponse = await AuthInternal(internalRequest, config);\n        if (isRaw)\n            return internalResponse;\n        const response = toResponse(internalResponse);\n        const url = response.headers.get(\"Location\");\n        if (!isRedirect || !url)\n            return response;\n        return Response.json({ url }, { headers: response.headers });\n    }\n    catch (e) {\n        const error = e;\n        logger.error(error);\n        const isAuthError = error instanceof AuthError;\n        if (isAuthError && isRaw && !isRedirect)\n            throw error;\n        // If the CSRF check failed for POST/session, return a 400 status code.\n        // We should not redirect to a page as this is an API route\n        if (request.method === \"POST\" && internalRequest.action === \"session\")\n            return Response.json(null, { status: 400 });\n        const isClientSafeErrorType = isClientError(error);\n        const type = isClientSafeErrorType ? error.type : \"Configuration\";\n        const params = new URLSearchParams({ error: type });\n        if (error instanceof CredentialsSignin)\n            params.set(\"code\", error.code);\n        const pageKind = (isAuthError && error.kind) || \"error\";\n        const pagePath = config.pages?.[pageKind] ?? `${config.basePath}/${pageKind.toLowerCase()}`;\n        const url = `${internalRequest.url.origin}${pagePath}?${params}`;\n        if (isRedirect)\n            return Response.json({ url });\n        return Response.redirect(url);\n    }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCC;;;AACD;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAqBO,eAAe,KAAK,OAAO,EAAE,MAAM;IACtC,MAAM,SAAS,CAAA,GAAA,4NAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,kBAAkB,MAAM,CAAA,GAAA,yNAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;IACzD,yCAAyC;IACzC,IAAI,CAAC,iBACD,OAAO,SAAS,IAAI,CAAC,CAAC,YAAY,CAAC,EAAE;QAAE,QAAQ;IAAI;IACvD,MAAM,kBAAkB,CAAA,GAAA,4NAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;IACtD,IAAI,MAAM,OAAO,CAAC,kBAAkB;QAChC,gBAAgB,OAAO,CAAC,OAAO,IAAI;IACvC,OACK,IAAI,iBAAiB;QACtB,yDAAyD;QACzD,OAAO,KAAK,CAAC;QACb,MAAM,YAAY,IAAI,IAAI;YACtB;YACA;YACA;YACA;SACH;QACD,IAAI,CAAC,UAAU,GAAG,CAAC,gBAAgB,MAAM,KACrC,gBAAgB,MAAM,KAAK,OAAO;YAClC,MAAM,UAAU;YAChB,OAAO,SAAS,IAAI,CAAC;gBAAE;YAAQ,GAAG;gBAAE,QAAQ;YAAI;QACpD;QACA,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG;QACzB,8DAA8D;QAC9D,oCAAoC;QACpC,MAAM,kBAAkB,OAAO,SAC3B,gBAAgB,GAAG,CAAC,YAAY,CAC3B,GAAG,CAAC,gBACH,WAAW,MAAM,KAAK;QAChC,8FAA8F;QAC9F,IAAI,CAAC,OAAO,SAAS,iBAAiB;YAClC,IAAI,iBAAiB;gBACjB,OAAO,KAAK,CAAC,IAAI,4MAAA,CAAA,gBAAa,CAAC,CAAC,eAAe,EAAE,OAAO,MAAM,kCAAkC,CAAC;YACrG;YACA,MAAM,OAAO,CAAA,GAAA,2NAAA,CAAA,UAAU,AAAD,EAAE;gBAAE;YAAM,GAAG,KAAK,CAAC;YACzC,OAAO,CAAA,GAAA,yNAAA,CAAA,aAAU,AAAD,EAAE;QACtB;QACA,MAAM,MAAM,GAAG,gBAAgB,GAAG,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,oBAAoB,CAAC;QAC7E,OAAO,SAAS,QAAQ,CAAC;IAC7B;IACA,MAAM,aAAa,QAAQ,OAAO,EAAE,IAAI;IACxC,MAAM,QAAQ,OAAO,GAAG,KAAK,oNAAA,CAAA,MAAG;IAChC,IAAI;QACA,MAAM,mBAAmB,MAAM,CAAA,GAAA,kOAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAC7D,IAAI,OACA,OAAO;QACX,MAAM,WAAW,CAAA,GAAA,yNAAA,CAAA,aAAU,AAAD,EAAE;QAC5B,MAAM,MAAM,SAAS,OAAO,CAAC,GAAG,CAAC;QACjC,IAAI,CAAC,cAAc,CAAC,KAChB,OAAO;QACX,OAAO,SAAS,IAAI,CAAC;YAAE;QAAI,GAAG;YAAE,SAAS,SAAS,OAAO;QAAC;IAC9D,EACA,OAAO,GAAG;QACN,MAAM,QAAQ;QACd,OAAO,KAAK,CAAC;QACb,MAAM,cAAc,iBAAiB,4MAAA,CAAA,YAAS;QAC9C,IAAI,eAAe,SAAS,CAAC,YACzB,MAAM;QACV,uEAAuE;QACvE,2DAA2D;QAC3D,IAAI,QAAQ,MAAM,KAAK,UAAU,gBAAgB,MAAM,KAAK,WACxD,OAAO,SAAS,IAAI,CAAC,MAAM;YAAE,QAAQ;QAAI;QAC7C,MAAM,wBAAwB,CAAA,GAAA,4MAAA,CAAA,gBAAa,AAAD,EAAE;QAC5C,MAAM,OAAO,wBAAwB,MAAM,IAAI,GAAG;QAClD,MAAM,SAAS,IAAI,gBAAgB;YAAE,OAAO;QAAK;QACjD,IAAI,iBAAiB,4MAAA,CAAA,oBAAiB,EAClC,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI;QACjC,MAAM,WAAW,AAAC,eAAe,MAAM,IAAI,IAAK;QAChD,MAAM,WAAW,OAAO,KAAK,EAAE,CAAC,SAAS,IAAI,GAAG,OAAO,QAAQ,CAAC,CAAC,EAAE,SAAS,WAAW,IAAI;QAC3F,MAAM,MAAM,GAAG,gBAAgB,GAAG,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,QAAQ;QAChE,IAAI,YACA,OAAO,SAAS,IAAI,CAAC;YAAE;QAAI;QAC/B,OAAO,SAAS,QAAQ,CAAC;IAC7B;AACJ", "ignoreList": [0]}}, {"offset": {"line": 5436, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@auth+core@0.39.0/node_modules/@auth/core/providers/credentials.js"], "sourcesContent": ["/**\n * The Credentials provider allows you to handle signing in with arbitrary credentials,\n * such as a username and password, domain, or two factor authentication or hardware device (e.g. YubiKey U2F / FIDO).\n *\n * It is intended to support use cases where you have an existing system you need to authenticate users against.\n *\n * It comes with the constraint that users authenticated in this manner are not persisted in the database,\n * and consequently that the Credentials provider can only be used if JSON Web Tokens are enabled for sessions.\n *\n * :::caution\n * The functionality provided for credentials-based authentication is intentionally limited to discourage the use of passwords due to the inherent security risks of the username-password model.\n *\n * OAuth providers spend significant amounts of money, time, and engineering effort to build:\n *\n * - abuse detection (bot-protection, rate-limiting)\n * - password management (password reset, credential stuffing, rotation)\n * - data security (encryption/salting, strength validation)\n *\n * and much more for authentication solutions. It is likely that your application would benefit from leveraging these battle-tested solutions rather than try to rebuild them from scratch.\n *\n * If you'd still like to build password-based authentication for your application despite these risks, Auth.js gives you full control to do so.\n *\n * :::\n *\n * See the [callbacks documentation](/reference/core#authconfig#callbacks) for more information on how to interact with the token. For example, you can add additional information to the token by returning an object from the `jwt()` callback:\n *\n * ```ts\n * callbacks: {\n *   async jwt({ token, user, account, profile, isNewUser }) {\n *     if (user) {\n *       token.id = user.id\n *     }\n *     return token\n *   }\n * }\n * ```\n *\n * @example\n * ```ts\n * import { Auth } from \"@auth/core\"\n * import Credentials from \"@auth/core/providers/credentials\"\n *\n * const request = new Request(\"https://example.com\")\n * const response = await AuthHandler(request, {\n *   providers: [\n *     Credentials({\n *       credentials: {\n *         username: { label: \"Username\" },\n *         password: {  label: \"Password\", type: \"password\" }\n *       },\n *       async authorize({ request }) {\n *         const response = await fetch(request)\n *         if(!response.ok) return null\n *         return await response.json() ?? null\n *       }\n *     })\n *   ],\n *   secret: \"...\",\n *   trustHost: true,\n * })\n * ```\n * @see [Username/Password Example](https://authjs.dev/getting-started/authentication/credentials)\n */\nexport default function Credentials(config) {\n    return {\n        id: \"credentials\",\n        name: \"Credentials\",\n        type: \"credentials\",\n        credentials: {},\n        authorize: () => null,\n        // @ts-expect-error\n        options: config,\n    };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8DC;;;AACc,SAAS,YAAY,MAAM;IACtC,OAAO;QACH,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa,CAAC;QACd,WAAW,IAAM;QACjB,mBAAmB;QACnB,SAAS;IACb;AACJ", "ignoreList": [0]}}]}