{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/manifest.ts"], "sourcesContent": ["import { MetadataRoute } from 'next'\n\nexport default function manifest(): MetadataRoute.Manifest {\n  return {\n    name: '업무지원(챗봇)',\n    short_name: 'GeOn',\n    description: 'AI 기반 지도 서비스 - 말로 만드는 지도와 지능형 어시스턴트',\n    start_url: '/',\n    display: 'standalone',\n    background_color: '#ffffff',\n    theme_color: '#000000',\n    icons: [\n      {\n        src: '/favicon.ico',\n        sizes: 'any',\n        type: 'image/x-icon',\n      },\n      {\n        src: '/images/main-logo.png',\n        sizes: '192x192',\n        type: 'image/png',\n      },\n      {\n        src: '/images/main-logo.png',\n        sizes: '512x512',\n        type: 'image/png',\n      },\n    ],\n    categories: ['productivity', 'utilities', 'navigation'],\n    lang: 'ko',\n    orientation: 'portrait-primary',\n  }\n}\n"], "names": [], "mappings": ";;;AAEe,SAAS;IACtB,OAAO;QACL,MAAM;QACN,YAAY;QACZ,aAAa;QACb,WAAW;QACX,SAAS;QACT,kBAAkB;QAClB,aAAa;QACb,OAAO;YACL;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;YACR;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;YACR;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;YACR;SACD;QACD,YAAY;YAAC;YAAgB;YAAa;SAAa;QACvD,MAAM;QACN,aAAa;IACf;AACF", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/manifest--route-entry.js"], "sourcesContent": ["            import { NextResponse } from 'next/server'\n            import handler from \"./manifest.ts\"\n            import { resolveRouteData } from\n'next/dist/build/webpack/loaders/metadata/resolve-route-data'\n\n            const contentType = \"application/manifest+json\"\n            const cacheControl = \"public, max-age=0, must-revalidate\"\n            const fileType = \"manifest\"\n\n            if (typeof handler !== 'function') {\n                throw new Error('Default export is missing in \"./manifest.ts\"')\n            }\n\n            export async function GET() {\n              const data = await handler()\n              const content = resolveRouteData(data, fileType)\n\n              return new NextResponse(content, {\n                headers: {\n                  'Content-Type': contentType,\n                  'Cache-Control': cacheControl,\n                },\n              })\n            }\n        "], "names": [], "mappings": ";;;AAAY;AACA;AACA;;;;AAGA,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,WAAW;AAEjB,IAAI,OAAO,+GAAA,CAAA,UAAO,KAAK,YAAY;IAC/B,MAAM,IAAI,MAAM;AACpB;AAEO,eAAe;IACpB,MAAM,OAAO,MAAM,CAAA,GAAA,+GAAA,CAAA,UAAO,AAAD;IACzB,MAAM,UAAU,CAAA,GAAA,4SAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;IAEvC,OAAO,IAAI,uOAAA,CAAA,eAAY,CAAC,SAAS;QAC/B,SAAS;YACP,gBAAgB;YAChB,iBAAiB;QACnB;IACF;AACF", "debugId": null}}]}