[Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 스와이퍼 추가', '언어': 'html', '기능': '지도생성 필수'}, page_content='<html>\n<head>\n<meta charset="utf-8">\n<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">\n<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>\n</head>\n<body>\n<div id="map" class="odf-view" style="height:550px;"></div>\n</body>'), Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 스와이퍼 추가', '언어': 'javascript', '기능': '맵객체 생성'}, page_content="<script>\n/* 맵 타겟 */\nvar mapContainer = document.getElementById('map');  \n/* 맵 중심점 */\nvar coord = new odf.Coordinate(199312.9996,551784.6924);  \n/* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */\nvar mapOption = {\ncenter : coord,\nzoom : 11,\nprojection : 'EPSG:5186',\n//proxyURL: 'proxyUrl.jsp',\n//proxyParam: 'url',\nbaroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',\nbaroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',"), Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 스와이퍼 추가', '언어': 'javascript', '기능': '배경지도 생성'}, page_content="basemap : {\nbaroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],\n},\n};\n/*\n* 배경지도 종류\neMapBasic - 바로e맵 일반 지도\neMapColor - 바로e맵 색각 지도\neMapLowV - 바로e맵 큰글씨 지도\neMapWhite - 바로e맵 백지도\neMapEnglish - 바로e맵 영어 지도\neMapChinese - 바로e맵 중어 지도\neMapJapanese - 바로e맵 일어 지도\neMapWhiteEdu - 바로e맵 교육용 백지도\neMapAIR - 바로e맵  항공지도  \n* 프록시 사용\nproxyURL: 'proxy.jsp' 프록시 설정\n*/  \n/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */\nvar map = new odf.Map(mapContainer, mapOption);  \n/* 베이스맵 컨트롤 생성 */\nvar basemapControl = new odf.BasemapControl();\nbasemapControl.setMap(map);\n</script>\n</html>"), Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 배경지도 최적화', '언어': 'html', '기능': '지도생성 필수'}, page_content='<html>\n<head>\n<meta charset="utf-8">\n<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">\n<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>\n</head>\n<body>\n<div id="map" class="odf-view" style="height:550px;"></div>\n<div>현재 좌표계 : <span id="projection"></span></div>\n<div>\n※ 배경지도 변경시 좌표계가 변경되면서 콘솔로그에 변경된 좌표계 값이 찍힙니다.\n※ 같은 그룹 끼리는 좌표계가 변경되지 않습니다.\n</div>\n</body>'), Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 배경지도 최적화', '언어': 'javascript', '기능': '맵객체 생성'}, page_content="<script>\n/* 맵 타겟 */\nvar mapContainer = document.getElementById('map');  \n/* 맵 중심점 */\nvar coord = new odf.Coordinate(199312.9996,551784.6924);  \n/* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */\nvar mapOption = {\ncenter : coord,\nzoom : 11,\nprojection : 'EPSG:5186',\n//proxyURL: 'proxyUrl.jsp',\n//proxyParam: 'url',\nbaroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',\nbaroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',  \nbasemap : {\nbaroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],\n},\n};\nmapOption.basemap.OSM = true;\n/*\n* 배경지도 종류\neMapBasic - 바로e맵 일반 지도\neMapColor - 바로e맵 색각 지도\neMapLowV - 바로e맵 큰글씨 지도\neMapWhite - 바로e맵 백지도\neMapEnglish - 바로e맵 영어 지도\neMapChinese - 바로e맵 중어 지도\neMapJapanese - 바로e맵 일어 지도\neMapWhiteEdu - 바로e맵 교육용 백지도\neMapAIR - 바로e맵  항공지도  \n* 프록시 사용\nproxyURL: 'proxy.jsp' 프록시 설정\n*/  \n/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */\nvar map = new odf.Map(mapContainer, {\n...mapOption,\n//배경지도 최적화 on\noptimization: true,\n});  \n/* 베이스맵 컨트롤 생성 */\nvar basemapControl = new odf.BasemapControl({});\nbasemapControl.setMap(map);"), Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 배경지도 최적화', '언어': 'javascript', '기능': 'wms 레이어 생성'}, page_content="/* wms 레이어 생성 */\nvar wmsLayer = odf.LayerFactory.produce('geoserver', {\nmethod : 'get',\nserver : 'https://geoserver.geon.kr/geoserver',\nlayer : 'geonpaas:L100000254',\nservice : 'wms',\n});\nwmsLayer.setMap(map);  \nvar sld = odf.StyleFactory.produceSLD({\nrules : [ {\nname : 'My Rule', /*룰 이름*/\nsymbolizers : [ {\nkind : 'Fill',\n/*채우기색\nrgba 값 입력시 자동변환. 네번째 인수 (투명도) 존재시 fillOpacity 보다 우선 적용됨\n*/\ncolor : '#FF9966',\n/*채우기 투명도 0~1*/\nfillOpacity : 0.7,\n/*윤곽선색\nrgba 값 입력시 자동변환. 네번째 인수 (투명도) 존재시 outlineOpacity보다 우선 적용됨\n*/\noutlineColor : '#338866',\n/*윤곽선 두께*/\noutlineWidth : 2,\n}, ],\n}, ],\n});  \n//sld 적용\nwmsLayer.setSLD(sld);  \n// wfs 레이어 생성\n// LayerFactory의 produce 함수는 option이 다양하니 개발자지원센터 '지도문서'를 확인하세요\nvar wfsLayer = odf.LayerFactory.produce('geoserver'/*레이어를 생성하기위 한 테이터 호출 방법*/, {\nmethod : 'get',//'post'\nserver : 'https://geoserver.geon.kr/geoserver', // 레이어가 발행된 서버 주소\nlayer : 'geonpaas:L100000258', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)\nservice : 'wfs', // 호출하고자 하는 레이여 형태(wms, wfs, wmts)\n}/*레이어 생성을 위한 옵션*/);\nwfsLayer.setMap(map);\n// 해당 layer가 한눈에 보이는 보여주는 extent로 화면 위치 이동 및 줌 레벨 변경\nwfsLayer.fit();  \ndocument.querySelector('#projection').innerText =map.getView().getProjection().getCode();\nodf.event.addListener(map,'change:view',()=>{\ndocument.querySelector('#projection').innerText =map.getView().getProjection().getCode();\n})\n</script>\n</html>"), Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 분할지도 추가', '언어': 'html', '기능': '지도생성 필수'}, page_content='<html>\n<head>\n<meta charset="utf-8">\n<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">\n<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>\n</head>\n<body>\n<div id="map" style="height:550px;"></div>\n<div style="margin-top: 15px">\n<input type="button" class="onoffOnlyBtn toggle grp1" onclick="setConnect(true)" value="동기화">\n<input type="button" class="onoffOnlyBtn toggle grp1" onclick="setConnect(false)" value="비동기화">\n<input type="button" class="onoffOnlyBtn toggle grp2" onclick="setOn(\'dualMap\',true)" value="2분할 열기">\n<input type="button" class="onoffOnlyBtn toggle grp2" onclick="setOn(\'quadMap\',true)" value="4분할 열기">\n<input type="button" class="onoffOnlyBtn toggle grp2" onclick="setOn(\'dualMap\',false)" value="분할 닫기">\n</div>\n</body>'), Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 분할지도 추가', '언어': 'javascript', '기능': '맵객체 생성'}, page_content="<script>  \n/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */\nvar mapContainer = document.getElementById('map');\nvar coord = new odf.Coordinate(199312.9996,551784.6924);\nvar mapOption = {\ncenter : coord,\nzoom : 11,\nprojection : 'EPSG:5186',\n//proxyURL: 'proxyUrl.jsp',\n//proxyParam: 'url',\nbaroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',\nbaroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',  \nbasemap : {\nbaroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],\n},\n};\nvar map = new odf.Map(mapContainer, mapOption);"), Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 분할지도 추가', '언어': 'javascript', '기능': '배경지도 생성'}, page_content='/* 배경지도 컨트롤 생성 */\nvar basemapControl = new odf.BasemapControl();\nbasemapControl.setMap(map);  \n/* 줌 컨트롤 생성 */\nvar zoomControl = new odf.ZoomControl();\nzoomControl.setMap(map);'), Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 분할지도 추가', '언어': 'javascript', '기능': '분할지도'}, page_content="var dmc = new odf.DivideMapControl({\ndualMap : [\n{\nposition : 1,\nmapOption : {\n// 해당 분할지도의 basemap 옵션\n// 정의하지 않는 경우, map 객체 생성시 사용한 basemap option 사용\nbasemap : {\nbaroEMap : [ 'eMapWhite' ]\n},\n},\n// 사용할 컨트롤 지정\n// 정의하지 않는 경우, 기본 값 적용(배경지도 컨트롤만 이용)\ncontrolOption : {\nbasemap: true,// 기본값 true\nzoom: false,// 기본값 false\nclear: false,// 기본값 false\ndownload: false,// 기본값 false\nprint: false,// 기본값 false\noverviewmap: false,// 기본값 false\ndraw: false,// 기본값 false\nmeasure: false,// 기본값 false\nmove: false,// 기본값 false\n}\n}\n],\nthreepleMap: [\n{\n// position: 1, //지정안하면 기본 1\nmapOption: {\nbasemap: {\nbaroEMap :['eMapWhite']\n},\n},\n},\n{\n// position: 2, //지정안하면 기본 3\nmapOption: {\nbasemap:{\nbaroEMap : ['eMapColor']\n},\n},\n}\n],\nquadMap : [\n{\n// position: 1, //지정안하면 기본 1\nmapOption : {\nbasemap : {\nbaroEMap  : [ 'eMapWhite' ]\n},\n},\n},\n{\n//position: 2, //지정안하면 기본 3\nmapOption : {\nbasemap : {\nbaroEMap  : [ 'eMapColor' ]\n},\n},\n},\n{\n//position: 4,//지정안하면 기본 4\nmapOption : {\nbasemap : {\nbaroEMap  : [ 'eMapAIR' ]\n},\n},\ncontrolOption : {//사용할 컨트롤 지정\ndownload : true,\n},\n}\n],\n// 분할지도 상세 생성 옵션\n// 정의하지 않으면 기본 값 적용\nconfig : {\n//분할지도 내 컨트롤 ui 생성 여부, 기본값 true\ncreateElementFlag : true,\n// 2분할지도 상세 생성 옵션\ndualMap : {\n/** 2분할지도 분할 유형.\n* - 'vertical' : 수직 분할 (기본값)\n* ┌─┬─┐\n* │1│2│\n* └─┴─┘\n* - 'horizonal' : 수평 분할\n* ┌───┐\n* │ 1 │\n* ├───┤\n* │ 2 │\n* └───┘\n*/\ndivType : 'vertical'//수직 분할 (기본값)\n},\n//3분할지도 상세 생성 옵션\nthreepleMap : {\n/** 3분할지도 분할 유형\n* - 'vertical' : 수직 분할 (기본값)\n* ┌─┬─┬─┐\n* │1│2│3│\n* └─┴─┴─┘\n* - 'horizonal' : 수평 분할\n* ┌───┐\n* │ 1 │\n* ├───┤\n* │ 2 │\n* ├───┤\n* │ 3 │\n* └───┘\n* - 'complex-01' : 복합형 1\n* ┌─┬───┐\n* │ │ 2 │\n* │1├───┤\n* │ │ 3 │\n* └─┴───┘\n* - 'complex-02' : 복합형 2\n* ┌─────┐\n* │  1  │\n* ├──┬──┤\n* │2 │ 3│\n* └──┴──┘\n* - 'complex-03' : 복합형 3\n* ┌──┬─┐\n* │2 │ │\n* ├──┤1│\n* │3 │ │\n* └──┴─┘\n* - 'complex-04' : 복합형 4\n* ┌──┬──┐\n* │ 1│2 │\n* ├──┴──┤\n* │  3  │\n* └─────┘\n*/\ndivType : 'vertical'//수직 분할 (기본값)\n},\n//3분할지도 상세 생성 옵션\nquadMap : {\n/**\n* - 'complex' : 수직,수평 분할 (기본값)\n* ┌───┬───┐\n* │ 1 │ 2 │\n* ├───┼───┤\n* │ 3 │ 4 │\n* └───┴───┘\n* - 'vertical' : 수직 분할\n* ┌─┬─┬─┬─┐\n* │1│2│3│4│\n* └─┴─┴─┴─┘\n* - 'horizonal' : 수평 분할\n* ┌─────┐\n* │  1  │\n* ├─────┤\n* │  2  │\n* ├─────┤\n* │  3  │\n* ├─────┤\n* │  4  │\n* └─────┘\n*/\ndivType : 'complex'//수직,수평 분할 (기본값)\n}\n}\n});\ndmc.setMap(map);\nmap.setResizable(true);\nfunction setConnect(flag) {\ndmc.setConnect(flag);\n}  \nfunction setOn(key, flag) {\ndmc.setOn(key, flag);\n}\n</script>\n</html>"), Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 스와이퍼 추가', '언어': 'html', '기능': '지도생성 필수'}, page_content='<html>\n<head>\n<meta charset="utf-8">\n<link href="https://developer.geon.kr/js/odf/odf.css" rel="stylesheet">\n<script type="text/javascript" src="https://developer.geon.kr/js/odf/odf.min.js"></script>\n</head>\n<body>\n<div id="map" style="height:550px;"></div>\n<div style="margin-top: 15px;">\n<input type="button" id="changeStrictMode" class="onoffBtn toggle" onclick="changeStrictMode()" value="strict모드 변경">\n</div>\n</body>'), Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 스와이퍼 추가', '언어': 'javascript', '기능': '맵객체 생성'}, page_content="<script>  \n/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */\nvar mapContainer = document.getElementById('map');\nvar coord = new odf.Coordinate(199312.9996,551784.6924);\nvar mapOption = {\ncenter : coord,\nzoom : 11,\nprojection : 'EPSG:5186',\n//proxyURL: 'proxyUrl.jsp',\n//proxyParam: 'url',\nbaroEMapURL : 'https://geon-gateway.geon.kr/map/api/map/baroemap',\nbaroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',  \nbasemap : {\nbaroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],\n},\n};\nvar map = new odf.Map(mapContainer, mapOption);"), Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 스와이퍼 추가', '언어': 'javascript', '기능': '배경지도 생성'}, page_content="var basemapControl = new odf.BasemapControl();\nbasemapControl.setMap(map);  \nvar wfsLayer = odf.LayerFactory.produce('geoserver', {\nmethod : 'get',\nserver : 'https://geoserver.geon.kr/geoserver',\nlayer : 'geonpaas:L100000254',\nservice : 'wfs',\n});\nwfsLayer.setMap(map);\nmap.setCenter([192396.63847319243, 534166.8213405443]);\nmap.setZoom(14);"), Document(metadata={'구분': 'odf', '설명': '기본지도 생성 및 스와이퍼 추가', '언어': 'javascript', '기능': '스와이퍼'}, page_content="//스와이퍼 컨트롤 추가\nvar swiperControl = new odf.SwiperControl({\n/**기존 사용중이던 레이어를 swiper레이어로 이용\n- true : 현재 지도에서 사용중인 레이어를 왼쪽 영영에 표출(기본값)\n- false : 왼쪽/오른쪽 영역 표출 레이어 직접 지정\n*/\nuseOriginalLayerFlag : true,//원본레이어를 왼쪽영역에 나타낼지 여부, 기본값 true  \n/** 엄격한 모드 사용 여부\n※ useOriginalLayerFlag 값이 true 일경우에만 적용\n- true : 배경지도를 제외한 레이어를 왼쪽 영역에만 표출\n- false : 배경지도를 제외한 레이어를 모든 영역에 표출(기본값)\n*/\nswipeStrictFlag : false,  \n// 스와이퍼 컨트롤의 슬라이더 너비 (픽셀)\n// default값 100. 최소 0, 최대 2000\nsize : 200,  \n/**스와이퍼로 나타낼 레이어 배열\n- [레이어1, 레이어2, ...] : useOriginalLayerFlag가 true일때 이와 같은 양식 적용.\n오른쪽 영역에 표출할 레이어 목록 정의\n- [[레이어1, 레이어2, ...],[레이어5, 레이어6, ...]] : useOriginalLayerFlag가 false일때 이와 같은 양식 적용.\n[왼쪽 영역에 표출할 레이어 배열, 오른쪽 영역에 표출할 레이어 배열]\n*/\nlayers : [ basemapControl.getBaseLayer('eMapAIR') ],\n});\nswiperControl.setMap(map);  \n//layers 값 셋팅\n//swiperControl.setLayers([ basemapControl.getBaseLayer('eMapColor') ]);  \n//SwiperControl의 슬라이더 값 셋팅\n//swiperControl.setSliderValue(30/*셋팅할 슬라이더 값 (0~100사이의 숫자)*/);  \n//SwiperControl의 슬라이더 값 조회\n//console.log(swiperControl.getSliderValue());  \n//SwiperControl에 엄격모드 적용\n//swiperControl.setSwipeStrictFlag(true);  \n//슬라이더 적용 여부 정의\n//swiperControl.setState(true/*슬라이더 적용 여부(true=>적용/false=>미적용)*/);  \n//슬라이더 적용 여부 조회\n//console.log(swiperControl.getState());  \nvar strictMode = false;\nfunction changeStrictMode() {\n//SwiperControl에 엄격모드 적용\nswiperControl.setSwipeStrictFlag(strictMode);\nstrictMode = !strictMode;\n}\n</script>\n</html>")]