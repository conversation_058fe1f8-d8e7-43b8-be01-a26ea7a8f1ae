exports.id=861,exports.ids=[861],exports.modules={1770:(e,t,i)=>{"use strict";i.d(t,{Y:()=>s,i:()=>n});var r=i(7160);function s(e){return(0,r.ll)`${e} asc`}function n(e){return(0,r.ll)`${e} desc`}},4471:(e,t,i)=>{"use strict";i.d(t,{Pe:()=>u});var r=i(7160),s=i(98100),n=i(46866);class l{constructor(e,t){this.unique=e,this.name=t}static [s.i]="PgIndexBuilderOn";on(...e){return new o(e.map(e=>{if((0,s.is)(e,r.Xs))return e;let t=new n.ae(e.name,e.columnType,e.indexConfig);return e.indexConfig=JSON.parse(JSON.stringify(e.defaultConfig)),t}),this.unique,!1,this.name)}onOnly(...e){return new o(e.map(e=>{if((0,s.is)(e,r.Xs))return e;let t=new n.ae(e.name,e.columnType,e.indexConfig);return e.indexConfig=e.defaultConfig,t}),this.unique,!0,this.name)}using(e,...t){return new o(t.map(e=>{if((0,s.is)(e,r.Xs))return e;let t=new n.ae(e.name,e.columnType,e.indexConfig);return e.indexConfig=JSON.parse(JSON.stringify(e.defaultConfig)),t}),this.unique,!0,this.name,e)}}class o{static [s.i]="PgIndexBuilder";config;constructor(e,t,i,r,s="btree"){this.config={name:r,columns:e,unique:t,only:i,method:s}}concurrently(){return this.config.concurrently=!0,this}with(e){return this.config.with=e,this}where(e){return this.config.where=e,this}build(e){return new a(this.config,e)}}class a{static [s.i]="PgIndex";config;constructor(e,t){this.config={...e,table:t}}}function u(e){return new l(!1,e)}},7160:(e,t,i)=>{"use strict";i.d(t,{Iw:()=>v,Or:()=>x,Xs:()=>m,DJ:()=>g,Ss:()=>N,Ct:()=>T,eG:()=>b,qt:()=>p,ll:()=>$});var r=i(98100),s=i(46866);let n=Symbol.for("drizzle:isPgEnum");class l extends s.pe{static [r.i]="PgEnumColumnBuilder";constructor(e,t){super(e,"string","PgEnumColumn"),this.config.enum=t}build(e){return new o(e,this.config)}}class o extends s.Kl{static [r.i]="PgEnumColumn";enum=this.config.enum;enumValues=this.config.enum.enumValues;constructor(e,t){super(e,t),this.enum=t.enum}getSQLType(){return this.enum.enumName}}var a=i(92821),u=i(15061),c=i(97430),h=i(19745),d=i(74059);class f{static [r.i]=null}function p(e){return null!=e&&"function"==typeof e.getSQL}class g{static [r.i]="StringChunk";value;constructor(e){this.value=Array.isArray(e)?e:[e]}getSQL(){return new m([this])}}class m{constructor(e){this.queryChunks=e}static [r.i]="SQL";decoder=w;shouldInlineParams=!1;append(e){return this.queryChunks.push(...e.queryChunks),this}toQuery(e){return u.k.startActiveSpan("drizzle.buildSQL",t=>{let i=this.buildQueryFromSourceParams(this.queryChunks,e);return t?.setAttributes({"drizzle.query.text":i.sql,"drizzle.query.params":JSON.stringify(i.params)}),i})}buildQueryFromSourceParams(e,t){let i=Object.assign({},t,{inlineParams:t.inlineParams||this.shouldInlineParams,paramStartIndex:t.paramStartIndex||{value:0}}),{escapeName:s,escapeParam:l,prepareTyping:o,inlineParams:u,paramStartIndex:f}=i;var b=e.map(e=>{if((0,r.is)(e,g))return{sql:e.value.join(""),params:[]};if((0,r.is)(e,y))return{sql:s(e.value),params:[]};if(void 0===e)return{sql:"",params:[]};if(Array.isArray(e)){let t=[new g("(")];for(let[i,r]of e.entries())t.push(r),i<e.length-1&&t.push(new g(", "));return t.push(new g(")")),this.buildQueryFromSourceParams(t,i)}if((0,r.is)(e,m))return this.buildQueryFromSourceParams(e.queryChunks,{...i,inlineParams:u||e.shouldInlineParams});if((0,r.is)(e,d.XI)){let t=e[d.XI.Symbol.Schema],i=e[d.XI.Symbol.Name];return{sql:void 0===t?s(i):s(t)+"."+s(i),params:[]}}if((0,r.is)(e,h.V))return"indexes"===t.invokeSource?{sql:s(e.name),params:[]}:{sql:s(e.table[d.XI.Symbol.Name])+"."+s(e.name),params:[]};if((0,r.is)(e,N)){let t=e[c.n].schema,i=e[c.n].name;return{sql:void 0===t?s(i):s(t)+"."+s(i),params:[]}}if((0,r.is)(e,v)){let t,s=null===e.value?null:e.encoder.mapToDriverValue(e.value);return(0,r.is)(s,m)?this.buildQueryFromSourceParams([s],i):u?{sql:this.mapInlineParam(s,i),params:[]}:(o&&(t=[o(e.encoder)]),{sql:l(f.value++,s),params:[s],typings:t})}return(0,r.is)(e,x)?{sql:l(f.value++,e),params:[e],typings:["none"]}:(0,r.is)(e,m.Aliased)&&void 0!==e.fieldAlias?{sql:s(e.fieldAlias),params:[]}:(0,r.is)(e,a.n)?e._.isWith?{sql:s(e._.alias),params:[]}:this.buildQueryFromSourceParams([new g("("),e._.sql,new g(") "),new y(e._.alias)],i):e&&"function"==typeof e&&n in e&&!0===e[n]?e.schema?{sql:s(e.schema)+"."+s(e.enumName),params:[]}:{sql:s(e.enumName),params:[]}:p(e)?e.shouldOmitSQLParens?.()?this.buildQueryFromSourceParams([e.getSQL()],i):this.buildQueryFromSourceParams([new g("("),e.getSQL(),new g(")")],i):u?{sql:this.mapInlineParam(e,i),params:[]}:{sql:l(f.value++,e),params:[e]}});let w={sql:"",params:[]};for(let e of b)w.sql+=e.sql,w.params.push(...e.params),e.typings?.length&&(w.typings||(w.typings=[]),w.typings.push(...e.typings));return w}mapInlineParam(e,{escapeString:t}){if(null===e)return"null";if("number"==typeof e||"boolean"==typeof e)return e.toString();if("string"==typeof e)return t(e);if("object"==typeof e){let i=e.toString();return"[object Object]"===i?t(JSON.stringify(e)):t(i)}throw Error("Unexpected param value: "+e)}getSQL(){return this}as(e){return void 0===e?this:new m.Aliased(this,e)}mapWith(e){return this.decoder="function"==typeof e?{mapFromDriverValue:e}:e,this}inlineParams(){return this.shouldInlineParams=!0,this}if(e){return e?this:void 0}}class y{constructor(e){this.value=e}static [r.i]="Name";brand;getSQL(){return new m([this])}}function b(e){return"object"==typeof e&&null!==e&&"mapToDriverValue"in e&&"function"==typeof e.mapToDriverValue}let w={mapFromDriverValue:e=>e},S={mapToDriverValue:e=>e};({...w,...S});class v{constructor(e,t=S){this.value=e,this.encoder=t}static [r.i]="Param";brand;getSQL(){return new m([this])}}function $(e,...t){let i=[];for(let[r,s]of((t.length>0||e.length>0&&""!==e[0])&&i.push(new g(e[0])),t.entries()))i.push(s,new g(e[r+1]));return new m(i)}(e=>{e.empty=function(){return new m([])},e.fromList=function(e){return new m(e)},e.raw=function(e){return new m([new g(e)])},e.join=function(e,t){let i=[];for(let[r,s]of e.entries())r>0&&void 0!==t&&i.push(t),i.push(s);return new m(i)},e.identifier=function(e){return new y(e)},e.placeholder=function(e){return new x(e)},e.param=function(e,t){return new v(e,t)}})($||($={})),(e=>{class t{constructor(e,t){this.sql=e,this.fieldAlias=t}static [r.i]="SQL.Aliased";isSelectionField=!1;getSQL(){return this.sql}clone(){return new t(this.sql,this.fieldAlias)}}e.Aliased=t})(m||(m={}));class x{constructor(e){this.name=e}static [r.i]="Placeholder";getSQL(){return new m([this])}}function T(e,t){return e.map(e=>{if((0,r.is)(e,x)){if(!(e.name in t))throw Error(`No value for placeholder "${e.name}" was provided`);return t[e.name]}return e})}class N{static [r.i]="View";[c.n];constructor({name:e,schema:t,selectedFields:i,query:r}){this[c.n]={name:e,originalName:e,schema:t,selectedFields:i,query:r,isExisting:!r,isAlias:!1}}getSQL(){return new m([this])}}h.V.prototype.getSQL=function(){return new m([this])},d.XI.prototype.getSQL=function(){return new m([this])},a.n.prototype.getSQL=function(){return new m([this])}},7320:(e,t,i)=>{"use strict";i.d(t,{dL:()=>o,uR:()=>a});var r=i(98100),s=i(7160),n=i(46866);class l extends n.pe{static [r.i]="PgUUIDBuilder";constructor(e){super(e,"string","PgUUID")}defaultRandom(){return this.default((0,s.ll)`gen_random_uuid()`)}build(e){return new o(e,this.config)}}class o extends n.Kl{static [r.i]="PgUUID";getSQLType(){return"uuid"}}function a(e){return new l(e)}},8738:(e,t,i)=>{"use strict";i.d(t,{f:()=>eP});var r=i(42449),s=i(98100);class n{static [s.i]="ConsoleLogWriter";write(e){console.log(e)}}class l{static [s.i]="DefaultLogger";writer;constructor(e){this.writer=e?.writer??new n}logQuery(e,t){let i=t.map(e=>{try{return JSON.stringify(e)}catch{return String(e)}}),r=i.length?` -- params: [${i.join(", ")}]`:"";this.writer.write(`Query: ${e}${r}`)}}class o{static [s.i]="NoopLogger";logQuery(){}}var a=i(19745),u=i(7160),c=i(74059),h=i(97430);class d{constructor(e){this.table=e}static [s.i]="ColumnAliasProxyHandler";get(e,t){return"table"===t?this.table:e[t]}}class f{constructor(e,t){this.alias=e,this.replaceOriginalName=t}static [s.i]="TableAliasProxyHandler";get(e,t){if(t===c.XI.Symbol.IsAlias)return!0;if(t===c.XI.Symbol.Name||this.replaceOriginalName&&t===c.XI.Symbol.OriginalName)return this.alias;if(t===h.n)return{...e[h.n],name:this.alias,isAlias:!0};if(t===c.XI.Symbol.Columns){let t=e[c.XI.Symbol.Columns];if(!t)return t;let i={};return Object.keys(t).map(r=>{i[r]=new Proxy(t[r],new d(new Proxy(e,this)))}),i}let i=e[t];return(0,s.is)(i,a.V)?new Proxy(i,new d(new Proxy(e,this))):i}}class p{constructor(e){this.alias=e}static [s.i]=null;get(e,t){return"sourceTable"===t?g(e.sourceTable,this.alias):e[t]}}function g(e,t){return new Proxy(e,new f(t,!1))}function m(e,t){return new Proxy(e,new d(new Proxy(e.table,new f(t,!1))))}function y(e,t){return new u.Xs.Aliased(b(e.sql,t),e.fieldAlias)}function b(e,t){return u.ll.join(e.queryChunks.map(e=>(0,s.is)(e,a.V)?m(e,t):(0,s.is)(e,u.Xs)?b(e,t):(0,s.is)(e,u.Xs.Aliased)?y(e,t):e))}class w extends Error{static [s.i]="DrizzleError";constructor({message:e,cause:t}){super(e),this.name="DrizzleError",this.cause=t}}class S extends w{static [s.i]="TransactionRollbackError";constructor(){super({message:"Rollback"})}}var v=i(46866);class $ extends v.pe{static [s.i]="PgJsonbBuilder";constructor(e){super(e,"json","PgJsonb")}build(e){return new x(e,this.config)}}class x extends v.Kl{static [s.i]="PgJsonb";constructor(e,t){super(e,t)}getSQLType(){return"jsonb"}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){if("string"==typeof e)try{return JSON.parse(e)}catch{}return e}}var T=i(16077);class N extends v.pe{static [s.i]="PgNumericBuilder";constructor(e,t,i){super(e,"string","PgNumeric"),this.config.precision=t,this.config.scale=i}build(e){return new q(e,this.config)}}class q extends v.Kl{static [s.i]="PgNumeric";precision;scale;constructor(e,t){super(e,t),this.precision=t.precision,this.scale=t.scale}getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`numeric(${this.precision}, ${this.scale})`:void 0===this.precision?"numeric":`numeric(${this.precision})`}}var P=i(24764);class Q extends P.u{constructor(e,t,i){super(e,"string","PgTime"),this.withTimezone=t,this.precision=i,this.config.withTimezone=t,this.config.precision=i}static [s.i]="PgTimeBuilder";build(e){return new L(e,this.config)}}class L extends v.Kl{static [s.i]="PgTime";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":`(${this.precision})`;return`time${e}${this.withTimezone?" with time zone":""}`}}var O=i(52363);class C extends P.u{static [s.i]="PgDateBuilder";constructor(e){super(e,"date","PgDate")}build(e){return new A(e,this.config)}}class A extends v.Kl{static [s.i]="PgDate";getSQLType(){return"date"}mapFromDriverValue(e){return new Date(e)}mapToDriverValue(e){return e.toISOString()}}class I extends P.u{static [s.i]="PgDateStringBuilder";constructor(e){super(e,"string","PgDateString")}build(e){return new z(e,this.config)}}class z extends v.Kl{static [s.i]="PgDateString";getSQLType(){return"date"}}var j=i(7320),k=i(32019),B=i(33094),_=i(60988),D=i(92821);function E(e,t){return Object.entries(e).reduce((e,[i,r])=>{if("string"!=typeof i)return e;let n=t?[...t,i]:[i];return(0,s.is)(r,a.V)||(0,s.is)(r,u.Xs)||(0,s.is)(r,u.Xs.Aliased)?e.push({path:n,field:r}):(0,s.is)(r,c.XI)?e.push(...E(r[c.XI.Symbol.Columns],n)):e.push(...E(r,n)),e},[])}function X(e,t){let i=Object.keys(e),r=Object.keys(t);if(i.length!==r.length)return!1;for(let[e,t]of i.entries())if(t!==r[e])return!1;return!0}function F(e,t){let i=Object.entries(t).filter(([,e])=>void 0!==e).map(([t,i])=>(0,s.is)(i,u.Xs)?[t,i]:[t,new u.Iw(i,e[c.XI.Symbol.Columns][t])]);if(0===i.length)throw Error("No values to set");return Object.fromEntries(i)}function K(e){return(0,s.is)(e,D.n)?e._.alias:(0,s.is)(e,u.Ss)?e[h.n].name:(0,s.is)(e,u.Xs)?void 0:e[c.XI.Symbol.IsAlias]?e[c.XI.Symbol.Name]:e[c.XI.Symbol.BaseName]}class V extends u.Ss{static [s.i]="PgViewBase"}class U{static [s.i]="PgDialect";async migrate(e,t,i){let r="string"==typeof i?"__drizzle_migrations":i.migrationsTable??"__drizzle_migrations",s="string"==typeof i?"drizzle":i.migrationsSchema??"drizzle",n=(0,u.ll)`
			CREATE TABLE IF NOT EXISTS ${u.ll.identifier(s)}.${u.ll.identifier(r)} (
				id SERIAL PRIMARY KEY,
				hash text NOT NULL,
				created_at bigint
			)
		`;await t.execute((0,u.ll)`CREATE SCHEMA IF NOT EXISTS ${u.ll.identifier(s)}`),await t.execute(n);let l=(await t.all((0,u.ll)`select id, hash, created_at from ${u.ll.identifier(s)}.${u.ll.identifier(r)} order by created_at desc limit 1`))[0];await t.transaction(async t=>{for await(let i of e)if(!l||Number(l.created_at)<i.folderMillis){for(let e of i.sql)await t.execute(u.ll.raw(e));await t.execute((0,u.ll)`insert into ${u.ll.identifier(s)}.${u.ll.identifier(r)} ("hash", "created_at") values(${i.hash}, ${i.folderMillis})`)}})}escapeName(e){return`"${e}"`}escapeParam(e){return`$${e+1}`}escapeString(e){return`'${e.replace(/'/g,"''")}'`}buildWithCTE(e){if(!e?.length)return;let t=[(0,u.ll)`with `];for(let[i,r]of e.entries())t.push((0,u.ll)`${u.ll.identifier(r._.alias)} as (${r._.sql})`),i<e.length-1&&t.push((0,u.ll)`, `);return t.push((0,u.ll)` `),u.ll.join(t)}buildDeleteQuery({table:e,where:t,returning:i,withList:r}){let s=this.buildWithCTE(r),n=i?(0,u.ll)` returning ${this.buildSelection(i,{isSingleTable:!0})}`:void 0,l=t?(0,u.ll)` where ${t}`:void 0;return(0,u.ll)`${s}delete from ${e}${l}${n}`}buildUpdateSet(e,t){let i=e[c.XI.Symbol.Columns],r=Object.keys(i).filter(e=>void 0!==t[e]||i[e]?.onUpdateFn!==void 0),s=r.length;return u.ll.join(r.flatMap((e,r)=>{let n=i[e],l=t[e]??u.ll.param(n.onUpdateFn(),n),o=(0,u.ll)`${u.ll.identifier(n.name)} = ${l}`;return r<s-1?[o,u.ll.raw(", ")]:[o]}))}buildUpdateQuery({table:e,set:t,where:i,returning:r,withList:s}){let n=this.buildWithCTE(s),l=this.buildUpdateSet(e,t),o=r?(0,u.ll)` returning ${this.buildSelection(r,{isSingleTable:!0})}`:void 0,a=i?(0,u.ll)` where ${i}`:void 0;return(0,u.ll)`${n}update ${e} set ${l}${a}${o}`}buildSelection(e,{isSingleTable:t=!1}={}){let i=e.length,r=e.flatMap(({field:e},r)=>{let n=[];if((0,s.is)(e,u.Xs.Aliased)&&e.isSelectionField)n.push(u.ll.identifier(e.fieldAlias));else if((0,s.is)(e,u.Xs.Aliased)||(0,s.is)(e,u.Xs)){let i=(0,s.is)(e,u.Xs.Aliased)?e.sql:e;t?n.push(new u.Xs(i.queryChunks.map(e=>(0,s.is)(e,v.Kl)?u.ll.identifier(e.name):e))):n.push(i),(0,s.is)(e,u.Xs.Aliased)&&n.push((0,u.ll)` as ${u.ll.identifier(e.fieldAlias)}`)}else(0,s.is)(e,a.V)&&(t?n.push(u.ll.identifier(e.name)):n.push(e));return r<i-1&&n.push((0,u.ll)`, `),n});return u.ll.join(r)}buildSelectQuery({withList:e,fields:t,fieldsFlat:i,where:r,having:n,table:l,joins:o,orderBy:d,groupBy:f,limit:p,offset:g,lockingClause:m,distinct:y,setOperators:b}){let w,S,v,$=i??E(t);for(let e of $){let t;if((0,s.is)(e.field,a.V)&&(0,c.Io)(e.field.table)!==((0,s.is)(l,D.n)?l._.alias:(0,s.is)(l,V)?l[h.n].name:(0,s.is)(l,u.Xs)?void 0:(0,c.Io)(l))&&(t=e.field.table,!o?.some(({alias:e})=>e===(t[c.XI.Symbol.IsAlias]?(0,c.Io)(t):t[c.XI.Symbol.BaseName])))){let t=(0,c.Io)(e.field.table);throw Error(`Your "${e.path.join("->")}" field references a column "${t}"."${e.field.name}", but the table "${t}" is not part of the query! Did you forget to join it?`)}}let x=!o||0===o.length,T=this.buildWithCTE(e);y&&(w=!0===y?(0,u.ll)` distinct`:(0,u.ll)` distinct on (${u.ll.join(y.on,(0,u.ll)`, `)})`);let N=this.buildSelection($,{isSingleTable:x}),q=(()=>{if((0,s.is)(l,c.XI)&&l[c.XI.Symbol.OriginalName]!==l[c.XI.Symbol.Name]){let e=(0,u.ll)`${u.ll.identifier(l[c.XI.Symbol.OriginalName])}`;return l[c.XI.Symbol.Schema]&&(e=(0,u.ll)`${u.ll.identifier(l[c.XI.Symbol.Schema])}.${e}`),(0,u.ll)`${e} ${u.ll.identifier(l[c.XI.Symbol.Name])}`}return l})(),P=[];if(o)for(let[e,t]of o.entries()){0===e&&P.push((0,u.ll)` `);let i=t.table,r=t.lateral?(0,u.ll)` lateral`:void 0;if((0,s.is)(i,k.mu)){let e=i[k.mu.Symbol.Name],s=i[k.mu.Symbol.Schema],n=i[k.mu.Symbol.OriginalName],l=e===n?void 0:t.alias;P.push((0,u.ll)`${u.ll.raw(t.joinType)} join${r} ${s?(0,u.ll)`${u.ll.identifier(s)}.`:void 0}${u.ll.identifier(n)}${l&&(0,u.ll)` ${u.ll.identifier(l)}`} on ${t.on}`)}else if((0,s.is)(i,u.Ss)){let e=i[h.n].name,s=i[h.n].schema,n=i[h.n].originalName,l=e===n?void 0:t.alias;P.push((0,u.ll)`${u.ll.raw(t.joinType)} join${r} ${s?(0,u.ll)`${u.ll.identifier(s)}.`:void 0}${u.ll.identifier(n)}${l&&(0,u.ll)` ${u.ll.identifier(l)}`} on ${t.on}`)}else P.push((0,u.ll)`${u.ll.raw(t.joinType)} join${r} ${i} on ${t.on}`);e<o.length-1&&P.push((0,u.ll)` `)}let Q=u.ll.join(P),L=r?(0,u.ll)` where ${r}`:void 0,O=n?(0,u.ll)` having ${n}`:void 0;d&&d.length>0&&(S=(0,u.ll)` order by ${u.ll.join(d,(0,u.ll)`, `)}`),f&&f.length>0&&(v=(0,u.ll)` group by ${u.ll.join(f,(0,u.ll)`, `)}`);let C=p?(0,u.ll)` limit ${p}`:void 0,A=g?(0,u.ll)` offset ${g}`:void 0,I=u.ll.empty();if(m){let e=(0,u.ll)` for ${u.ll.raw(m.strength)}`;m.config.of&&e.append((0,u.ll)` of ${u.ll.join(Array.isArray(m.config.of)?m.config.of:[m.config.of],(0,u.ll)`, `)}`),m.config.noWait?e.append((0,u.ll)` no wait`):m.config.skipLocked&&e.append((0,u.ll)` skip locked`),I.append(e)}let z=(0,u.ll)`${T}select${w} ${N} from ${q}${Q}${L}${v}${O}${S}${C}${A}${I}`;return b.length>0?this.buildSetOperations(z,b):z}buildSetOperations(e,t){let[i,...r]=t;if(!i)throw Error("Cannot pass undefined values to any set operator");return 0===r.length?this.buildSetOperationQuery({leftSelect:e,setOperator:i}):this.buildSetOperations(this.buildSetOperationQuery({leftSelect:e,setOperator:i}),r)}buildSetOperationQuery({leftSelect:e,setOperator:{type:t,isAll:i,rightSelect:r,limit:n,orderBy:l,offset:o}}){let a,c=(0,u.ll)`(${e.getSQL()}) `,h=(0,u.ll)`(${r.getSQL()})`;if(l&&l.length>0){let e=[];for(let t of l)if((0,s.is)(t,v.Kl))e.push(u.ll.identifier(t.name));else if((0,s.is)(t,u.Xs)){for(let e=0;e<t.queryChunks.length;e++){let i=t.queryChunks[e];(0,s.is)(i,v.Kl)&&(t.queryChunks[e]=u.ll.identifier(i.name))}e.push((0,u.ll)`${t}`)}else e.push((0,u.ll)`${t}`);a=(0,u.ll)` order by ${u.ll.join(e,(0,u.ll)`, `)} `}let d=n?(0,u.ll)` limit ${n}`:void 0,f=u.ll.raw(`${t} ${i?"all ":""}`),p=o?(0,u.ll)` offset ${o}`:void 0;return(0,u.ll)`${c}${f}${h}${a}${d}${p}`}buildInsertQuery({table:e,values:t,onConflict:i,returning:r,withList:n}){let l=[],o=Object.entries(e[c.XI.Symbol.Columns]),a=o.map(([,e])=>u.ll.identifier(e.name));for(let[e,i]of t.entries()){let r=[];for(let[e,t]of o){let n=i[e];if(void 0===n||(0,s.is)(n,u.Iw)&&void 0===n.value)if(void 0!==t.defaultFn){let e=t.defaultFn(),i=(0,s.is)(e,u.Xs)?e:u.ll.param(e,t);r.push(i)}else if(t.default||void 0===t.onUpdateFn)r.push((0,u.ll)`default`);else{let e=t.onUpdateFn(),i=(0,s.is)(e,u.Xs)?e:u.ll.param(e,t);r.push(i)}else r.push(n)}l.push(r),e<t.length-1&&l.push((0,u.ll)`, `)}let h=this.buildWithCTE(n),d=u.ll.join(l),f=r?(0,u.ll)` returning ${this.buildSelection(r,{isSingleTable:!0})}`:void 0,p=i?(0,u.ll)` on conflict ${i}`:void 0;return(0,u.ll)`${h}insert into ${e} ${a} values ${d}${p}${f}`}buildRefreshMaterializedViewQuery({view:e,concurrently:t,withNoData:i}){let r=t?(0,u.ll)` concurrently`:void 0,s=i?(0,u.ll)` with no data`:void 0;return(0,u.ll)`refresh materialized view${r} ${e}${s}`}prepareTyping(e){if((0,s.is)(e,x)||(0,s.is)(e,T.iX))return"json";if((0,s.is)(e,q))return"decimal";if((0,s.is)(e,L))return"time";if((0,s.is)(e,O.KM)||(0,s.is)(e,O.xQ))return"timestamp";if((0,s.is)(e,A)||(0,s.is)(e,z))return"date";else if((0,s.is)(e,j.dL))return"uuid";else return"none"}sqlToQuery(e,t){return e.toQuery({escapeName:this.escapeName,escapeParam:this.escapeParam,escapeString:this.escapeString,prepareTyping:this.prepareTyping,invokeSource:t})}buildRelationalQueryWithoutPK({fullSchema:e,schema:t,tableNamesMap:i,table:r,tableConfig:n,queryConfig:l,tableAlias:o,nestedQueryRelation:h,joinOn:d}){let f,p=[],S,v,$=[],x,T=[];if(!0===l)p=Object.entries(n.columns).map(([e,t])=>({dbKey:t.name,tsKey:e,field:m(t,o),relationTableTsKey:void 0,isJson:!1,selection:[]}));else{let r=Object.fromEntries(Object.entries(n.columns).map(([e,t])=>[e,m(t,o)]));if(l.where){let e="function"==typeof l.where?l.where(r,(0,B.mm)()):l.where;x=e&&b(e,o)}let h=[],d=[];if(l.columns){let e=!1;for(let[t,i]of Object.entries(l.columns))void 0!==i&&t in n.columns&&(e||!0!==i||(e=!0),d.push(t));d.length>0&&(d=e?d.filter(e=>l.columns?.[e]===!0):Object.keys(n.columns).filter(e=>!d.includes(e)))}else d=Object.keys(n.columns);for(let e of d){let t=n.columns[e];h.push({tsKey:e,value:t})}let f=[];if(l.with&&(f=Object.entries(l.with).filter(e=>!!e[1]).map(([e,t])=>({tsKey:e,queryConfig:t,relation:n.relations[e]}))),l.extras)for(let[e,t]of Object.entries("function"==typeof l.extras?l.extras(r,{sql:u.ll}):l.extras))h.push({tsKey:e,value:y(t,o)});for(let{tsKey:e,value:t}of h)p.push({dbKey:(0,s.is)(t,u.Xs.Aliased)?t.fieldAlias:n.columns[e].name,tsKey:e,field:(0,s.is)(t,a.V)?m(t,o):t,relationTableTsKey:void 0,isJson:!1,selection:[]});let g="function"==typeof l.orderBy?l.orderBy(r,(0,B.rl)()):l.orderBy??[];for(let{tsKey:r,queryConfig:n,relation:h}of(Array.isArray(g)||(g=[g]),$=g.map(e=>(0,s.is)(e,a.V)?m(e,o):b(e,o)),S=l.limit,v=l.offset,f)){let l=(0,B.W0)(t,i,h),a=i[(0,c.Lf)(h.referencedTable)],d=`${o}_${r}`,f=(0,_.Uo)(...l.fields.map((e,t)=>(0,_.eq)(m(l.references[t],d),m(e,o)))),g=this.buildRelationalQueryWithoutPK({fullSchema:e,schema:t,tableNamesMap:i,table:e[a],tableConfig:t[a],queryConfig:(0,s.is)(h,B.pD)?!0===n?{limit:1}:{...n,limit:1}:n,tableAlias:d,joinOn:f,nestedQueryRelation:h}),y=(0,u.ll)`${u.ll.identifier(d)}.${u.ll.identifier("data")}`.as(r);T.push({on:(0,u.ll)`true`,table:new D.n(g.sql,{},d),alias:d,joinType:"left",lateral:!0}),p.push({dbKey:r,tsKey:r,field:y,relationTableTsKey:a,isJson:!0,selection:g.selection})}}if(0===p.length)throw new w({message:`No fields selected for table "${n.tsName}" ("${o}")`});if(x=(0,_.Uo)(d,x),h){let e=(0,u.ll)`json_build_array(${u.ll.join(p.map(({field:e,tsKey:t,isJson:i})=>i?(0,u.ll)`${u.ll.identifier(`${o}_${t}`)}.${u.ll.identifier("data")}`:(0,s.is)(e,u.Xs.Aliased)?e.sql:e),(0,u.ll)`, `)})`;(0,s.is)(h,B.iv)&&(e=(0,u.ll)`coalesce(json_agg(${e}${$.length>0?(0,u.ll)` order by ${u.ll.join($,(0,u.ll)`, `)}`:void 0}), '[]'::json)`);let t=[{dbKey:"data",tsKey:"data",field:e.as("data"),isJson:!0,relationTableTsKey:n.tsName,selection:p}];void 0!==S||void 0!==v||$.length>0?(f=this.buildSelectQuery({table:g(r,o),fields:{},fieldsFlat:[{path:[],field:u.ll.raw("*")}],where:x,limit:S,offset:v,orderBy:$,setOperators:[]}),x=void 0,S=void 0,v=void 0,$=[]):f=g(r,o),f=this.buildSelectQuery({table:(0,s.is)(f,k.mu)?f:new D.n(f,{},o),fields:{},fieldsFlat:t.map(({field:e})=>({path:[],field:(0,s.is)(e,a.V)?m(e,o):e})),joins:T,where:x,limit:S,offset:v,orderBy:$,setOperators:[]})}else f=this.buildSelectQuery({table:g(r,o),fields:{},fieldsFlat:p.map(({field:e})=>({path:[],field:(0,s.is)(e,a.V)?m(e,o):e})),joins:T,where:x,limit:S,offset:v,orderBy:$,setOperators:[]});return{tableTsKey:n.tsName,sql:f,selection:p}}}class M{static [s.i]="SelectionProxyHandler";config;constructor(e){this.config={...e}}get(e,t){if("_"===t)return{...e._,selectedFields:new Proxy(e._.selectedFields,this)};if(t===h.n)return{...e[h.n],selectedFields:new Proxy(e[h.n].selectedFields,this)};if("symbol"==typeof t)return e[t];let i=((0,s.is)(e,D.n)?e._.selectedFields:(0,s.is)(e,u.Ss)?e[h.n].selectedFields:e)[t];if((0,s.is)(i,u.Xs.Aliased)){if("sql"===this.config.sqlAliasedBehavior&&!i.isSelectionField)return i.sql;let e=i.clone();return e.isSelectionField=!0,e}if((0,s.is)(i,u.Xs)){if("sql"===this.config.sqlBehavior)return i;throw Error(`You tried to reference "${t}" field from a subquery, which is a raw SQL field, but it doesn't have an alias declared. Please add an alias to the field using ".as('alias')" method.`)}return(0,s.is)(i,a.V)?this.config.alias?new Proxy(i,new d(new Proxy(i.table,new f(this.config.alias,this.config.replaceOriginalName??!1)))):i:"object"!=typeof i||null===i?i:new Proxy(i,new M(this.config))}}class J{static [s.i]="TypedQueryBuilder";getSelectedFields(){return this._.selectedFields}}class R{static [s.i]="QueryPromise";[Symbol.toStringTag]="QueryPromise";catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}then(e,t){return this.execute().then(e,t)}}var H=i(15061);class W{static [s.i]="PgSelectBuilder";fields;session;dialect;withList=[];distinct;constructor(e){this.fields=e.fields,this.session=e.session,this.dialect=e.dialect,e.withList&&(this.withList=e.withList),this.distinct=e.distinct}from(e){let t,i=!!this.fields;return t=this.fields?this.fields:(0,s.is)(e,D.n)?Object.fromEntries(Object.keys(e._.selectedFields).map(t=>[t,e[t]])):(0,s.is)(e,V)?e[h.n].selectedFields:(0,s.is)(e,u.Xs)?{}:e[c.XI.Symbol.Columns],new Z({table:e,fields:t,isPartialSelect:i,session:this.session,dialect:this.dialect,withList:this.withList,distinct:this.distinct})}}class Y extends J{static [s.i]="PgSelectQueryBuilder";_;config;joinsNotNullableMap;tableName;isPartialSelect;session;dialect;constructor({table:e,fields:t,isPartialSelect:i,session:r,dialect:s,withList:n,distinct:l}){super(),this.config={withList:n,table:e,fields:{...t},distinct:l,setOperators:[]},this.isPartialSelect=i,this.session=r,this.dialect=s,this._={selectedFields:t},this.tableName=K(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{}}createJoin(e){return(t,i)=>{let r=this.tableName,n=K(t);if("string"==typeof n&&this.config.joins?.some(e=>e.alias===n))throw Error(`Alias "${n}" is already used in this query`);if(!this.isPartialSelect&&(1===Object.keys(this.joinsNotNullableMap).length&&"string"==typeof r&&(this.config.fields={[r]:this.config.fields}),"string"==typeof n&&!(0,s.is)(t,u.Xs))){let e=(0,s.is)(t,D.n)?t._.selectedFields:(0,s.is)(t,u.Ss)?t[h.n].selectedFields:t[c.XI.Symbol.Columns];this.config.fields[n]=e}if("function"==typeof i&&(i=i(new Proxy(this.config.fields,new M({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.joins||(this.config.joins=[]),this.config.joins.push({on:i,table:t,joinType:e,alias:n}),"string"==typeof n)switch(e){case"left":this.joinsNotNullableMap[n]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[n]=!0;break;case"inner":this.joinsNotNullableMap[n]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[n]=!1}return this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");createSetOperator(e,t){return i=>{let r="function"==typeof i?i(ee()):i;if(!X(this.getSelectedFields(),r.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return this.config.setOperators.push({type:e,isAll:t,rightSelect:r}),this}}union=this.createSetOperator("union",!1);unionAll=this.createSetOperator("union",!0);intersect=this.createSetOperator("intersect",!1);intersectAll=this.createSetOperator("intersect",!0);except=this.createSetOperator("except",!1);exceptAll=this.createSetOperator("except",!0);addSetOperators(e){return this.config.setOperators.push(...e),this}where(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new M({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.where=e,this}having(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new M({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.having=e,this}groupBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new M({sqlAliasedBehavior:"alias",sqlBehavior:"sql"})));this.config.groupBy=Array.isArray(t)?t:[t]}else this.config.groupBy=e;return this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new M({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),i=Array.isArray(t)?t:[t];this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=i:this.config.orderBy=i}else this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=e:this.config.orderBy=e;return this}limit(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).limit=e:this.config.limit=e,this}offset(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).offset=e:this.config.offset=e,this}for(e,t={}){return this.config.lockingClause={strength:e,config:t},this}getSQL(){return this.dialect.buildSelectQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}as(e){return new Proxy(new D.n(this.getSQL(),this.config.fields,e),new M({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}getSelectedFields(){return new Proxy(this.config.fields,new M({alias:this.tableName,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}$dynamic(){return this}}class Z extends Y{static [s.i]="PgSelect";_prepare(e){let{session:t,config:i,dialect:r,joinsNotNullableMap:s}=this;if(!t)throw Error("Cannot execute a query on a query builder. Please use a database instance instead.");return H.k.startActiveSpan("drizzle.prepareQuery",()=>{let n=E(i.fields),l=t.prepareQuery(r.sqlToQuery(this.getSQL()),n,e,!0);return l.joinsNotNullableMap=s,l})}prepare(e){return this._prepare(e)}execute=e=>H.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e))}for(let e of[R])for(let t of Object.getOwnPropertyNames(e.prototype))"constructor"!==t&&Object.defineProperty(Z.prototype,t,Object.getOwnPropertyDescriptor(e.prototype,t)||Object.create(null));function G(e,t){return(i,r,...s)=>{let n=[r,...s].map(i=>({type:e,isAll:t,rightSelect:i}));for(let e of n)if(!X(i.getSelectedFields(),e.rightSelect.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return i.addSetOperators(n)}}let ee=()=>({union:et,unionAll:ei,intersect:er,intersectAll:es,except:en,exceptAll:el}),et=G("union",!1),ei=G("union",!0),er=G("intersect",!1),es=G("intersect",!0),en=G("except",!1),el=G("except",!0);class eo{static [s.i]="PgQueryBuilder";dialect;$with(e){let t=this;return{as:i=>("function"==typeof i&&(i=i(t)),new Proxy(new D.J(i.getSQL(),i.getSelectedFields(),e,!0),new M({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}}with(...e){let t=this;return{select:function(i){return new W({fields:i??void 0,session:void 0,dialect:t.getDialect(),withList:e})},selectDistinct:function(e){return new W({fields:e??void 0,session:void 0,dialect:t.getDialect(),distinct:!0})},selectDistinctOn:function(e,i){return new W({fields:i??void 0,session:void 0,dialect:t.getDialect(),distinct:{on:e}})}}}select(e){return new W({fields:e??void 0,session:void 0,dialect:this.getDialect()})}selectDistinct(e){return new W({fields:e??void 0,session:void 0,dialect:this.getDialect(),distinct:!0})}selectDistinctOn(e,t){return new W({fields:t??void 0,session:void 0,dialect:this.getDialect(),distinct:{on:e}})}getDialect(){return this.dialect||(this.dialect=new U),this.dialect}}class ea{constructor(e,t,i,r){this.table=e,this.session=t,this.dialect=i,this.withList=r}static [s.i]="PgUpdateBuilder";set(e){return new eu(this.table,F(this.table,e),this.session,this.dialect,this.withList)}}class eu extends R{constructor(e,t,i,r,s){super(),this.session=i,this.dialect=r,this.config={set:t,table:e,withList:s}}static [s.i]="PgUpdate";config;where(e){return this.config.where=e,this}returning(e=this.config.table[c.XI.Symbol.Columns]){return this.config.returning=E(e),this}getSQL(){return this.dialect.buildUpdateQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0)}prepare(e){return this._prepare(e)}execute=e=>this._prepare().execute(e);$dynamic(){return this}}class ec{constructor(e,t,i,r){this.table=e,this.session=t,this.dialect=i,this.withList=r}static [s.i]="PgInsertBuilder";values(e){if(0===(e=Array.isArray(e)?e:[e]).length)throw Error("values() must be called with at least one value");let t=e.map(e=>{let t={},i=this.table[c.XI.Symbol.Columns];for(let r of Object.keys(e)){let n=e[r];t[r]=(0,s.is)(n,u.Xs)?n:new u.Iw(n,i[r])}return t});return new eh(this.table,t,this.session,this.dialect,this.withList)}}class eh extends R{constructor(e,t,i,r,s){super(),this.session=i,this.dialect=r,this.config={table:e,values:t,withList:s}}static [s.i]="PgInsert";config;returning(e=this.config.table[c.XI.Symbol.Columns]){return this.config.returning=E(e),this}onConflictDoNothing(e={}){if(void 0===e.target)this.config.onConflict=(0,u.ll)`do nothing`;else{let t="";t=Array.isArray(e.target)?e.target.map(e=>this.dialect.escapeName(e.name)).join(","):this.dialect.escapeName(e.target.name);let i=e.where?(0,u.ll)` where ${e.where}`:void 0;this.config.onConflict=(0,u.ll)`(${u.ll.raw(t)})${i} do nothing`}return this}onConflictDoUpdate(e){if(e.where&&(e.targetWhere||e.setWhere))throw Error('You cannot use both "where" and "targetWhere"/"setWhere" at the same time - "where" is deprecated, use "targetWhere" or "setWhere" instead.');let t=e.where?(0,u.ll)` where ${e.where}`:void 0,i=e.targetWhere?(0,u.ll)` where ${e.targetWhere}`:void 0,r=e.setWhere?(0,u.ll)` where ${e.setWhere}`:void 0,s=this.dialect.buildUpdateSet(this.config.table,F(this.config.table,e.set)),n="";return n=Array.isArray(e.target)?e.target.map(e=>this.dialect.escapeName(e.name)).join(","):this.dialect.escapeName(e.target.name),this.config.onConflict=(0,u.ll)`(${u.ll.raw(n)})${i} do update set ${s}${t}${r}`,this}getSQL(){return this.dialect.buildInsertQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return H.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0))}prepare(e){return this._prepare(e)}execute=e=>H.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e));$dynamic(){return this}}class ed extends R{constructor(e,t,i,r){super(),this.session=t,this.dialect=i,this.config={table:e,withList:r}}static [s.i]="PgDelete";config;where(e){return this.config.where=e,this}returning(e=this.config.table[c.XI.Symbol.Columns]){return this.config.returning=E(e),this}getSQL(){return this.dialect.buildDeleteQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return H.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0))}prepare(e){return this._prepare(e)}execute=e=>H.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e));$dynamic(){return this}}class ef{constructor(e,t,i,r,s,n,l){this.fullSchema=e,this.schema=t,this.tableNamesMap=i,this.table=r,this.tableConfig=s,this.dialect=n,this.session=l}static [s.i]="PgRelationalQueryBuilder";findMany(e){return new ep(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e||{},"many")}findFirst(e){return new ep(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e?{...e,limit:1}:{limit:1},"first")}}class ep extends R{constructor(e,t,i,r,s,n,l,o,a){super(),this.fullSchema=e,this.schema=t,this.tableNamesMap=i,this.table=r,this.tableConfig=s,this.dialect=n,this.session=l,this.config=o,this.mode=a}static [s.i]="PgRelationalQuery";_prepare(e){return H.k.startActiveSpan("drizzle.prepareQuery",()=>{let{query:t,builtQuery:i}=this._toSQL();return this.session.prepareQuery(i,void 0,e,!0,(e,i)=>{let r=e.map(e=>(0,B.I$)(this.schema,this.tableConfig,e,t.selection,i));return"first"===this.mode?r[0]:r})})}prepare(e){return this._prepare(e)}_getQuery(){return this.dialect.buildRelationalQueryWithoutPK({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName})}getSQL(){return this._getQuery().sql}_toSQL(){let e=this._getQuery(),t=this.dialect.sqlToQuery(e.sql);return{query:e,builtQuery:t}}toSQL(){return this._toSQL().builtQuery}execute(){return H.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute())}}class eg extends R{constructor(e,t,i,r){super(),this.execute=e,this.sql=t,this.query=i,this.mapBatchResult=r}static [s.i]="PgRaw";getSQL(){return this.sql}getQuery(){return this.query}mapResult(e,t){return t?this.mapBatchResult(e):e}_prepare(){return this}isResponseInArrayMode(){return!1}}class em extends R{constructor(e,t,i){super(),this.session=t,this.dialect=i,this.config={view:e}}static [s.i]="PgRefreshMaterializedView";config;concurrently(){if(void 0!==this.config.withNoData)throw Error("Cannot use concurrently and withNoData together");return this.config.concurrently=!0,this}withNoData(){if(void 0!==this.config.concurrently)throw Error("Cannot use concurrently and withNoData together");return this.config.withNoData=!0,this}getSQL(){return this.dialect.buildRefreshMaterializedViewQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return H.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),void 0,e,!0))}prepare(e){return this._prepare(e)}execute=e=>H.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e))}class ey{constructor(e,t,i){if(this.dialect=e,this.session=t,this._=i?{schema:i.schema,fullSchema:i.fullSchema,tableNamesMap:i.tableNamesMap,session:t}:{schema:void 0,fullSchema:{},tableNamesMap:{},session:t},this.query={},this._.schema)for(let[r,s]of Object.entries(this._.schema))this.query[r]=new ef(i.fullSchema,this._.schema,this._.tableNamesMap,i.fullSchema[r],s,e,t)}static [s.i]="PgDatabase";query;$with(e){return{as:t=>("function"==typeof t&&(t=t(new eo)),new Proxy(new D.J(t.getSQL(),t.getSelectedFields(),e,!0),new M({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}}with(...e){let t=this;return{select:function(i){return new W({fields:i??void 0,session:t.session,dialect:t.dialect,withList:e})},selectDistinct:function(i){return new W({fields:i??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:!0})},selectDistinctOn:function(i,r){return new W({fields:r??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:{on:i}})},update:function(i){return new ea(i,t.session,t.dialect,e)},insert:function(i){return new ec(i,t.session,t.dialect,e)},delete:function(i){return new ed(i,t.session,t.dialect,e)}}}select(e){return new W({fields:e??void 0,session:this.session,dialect:this.dialect})}selectDistinct(e){return new W({fields:e??void 0,session:this.session,dialect:this.dialect,distinct:!0})}selectDistinctOn(e,t){return new W({fields:t??void 0,session:this.session,dialect:this.dialect,distinct:{on:e}})}update(e){return new ea(e,this.session,this.dialect)}insert(e){return new ec(e,this.session,this.dialect)}delete(e){return new ed(e,this.session,this.dialect)}refreshMaterializedView(e){return new em(e,this.session,this.dialect)}execute(e){let t=e.getSQL(),i=this.dialect.sqlToQuery(t),r=this.session.prepareQuery(i,void 0,void 0,!1);return new eg(()=>r.execute(),t,i,e=>r.mapResult(e,!0))}transaction(e,t){return this.session.transaction(e,t)}}class eb{constructor(e){this.query=e}getQuery(){return this.query}mapResult(e,t){return e}static [s.i]="PgPreparedQuery";joinsNotNullableMap}class ew{constructor(e){this.dialect=e}static [s.i]="PgSession";execute(e){return H.k.startActiveSpan("drizzle.operation",()=>H.k.startActiveSpan("drizzle.prepareQuery",()=>this.prepareQuery(this.dialect.sqlToQuery(e),void 0,void 0,!1)).execute())}all(e){return this.prepareQuery(this.dialect.sqlToQuery(e),void 0,void 0,!1).all()}}class eS extends ey{constructor(e,t,i,r=0){super(e,t,i),this.schema=i,this.nestedIndex=r}static [s.i]="PgTransaction";rollback(){throw new S}getTransactionConfigSQL(e){let t=[];return e.isolationLevel&&t.push(`isolation level ${e.isolationLevel}`),e.accessMode&&t.push(e.accessMode),"boolean"==typeof e.deferrable&&t.push(e.deferrable?"deferrable":"not deferrable"),u.ll.raw(t.join(" "))}setTransaction(e){return this.session.execute((0,u.ll)`set transaction ${this.getTransactionConfigSQL(e)}`)}}let{Pool:ev}=r;class e$ extends eb{constructor(e,t,i,r,s,n,l,o){super({sql:t,params:i}),this.client=e,this.params=i,this.logger=r,this.fields=s,this._isResponseInArrayMode=l,this.customResultMapper=o,this.rawQueryConfig={name:n,text:t},this.queryConfig={name:n,text:t,rowMode:"array"}}static [s.i]="NodePgPreparedQuery";rawQueryConfig;queryConfig;async execute(e={}){return H.k.startActiveSpan("drizzle.execute",async()=>{let t=(0,u.Ct)(this.params,e);this.logger.logQuery(this.rawQueryConfig.text,t);let{fields:i,rawQueryConfig:r,client:n,queryConfig:l,joinsNotNullableMap:o,customResultMapper:h}=this;if(!i&&!h)return H.k.startActiveSpan("drizzle.driver.execute",async e=>(e?.setAttributes({"drizzle.query.name":r.name,"drizzle.query.text":r.text,"drizzle.query.params":JSON.stringify(t)}),n.query(r,t)));let d=await H.k.startActiveSpan("drizzle.driver.execute",e=>(e?.setAttributes({"drizzle.query.name":l.name,"drizzle.query.text":l.text,"drizzle.query.params":JSON.stringify(t)}),n.query(l,t)));return H.k.startActiveSpan("drizzle.mapResponse",()=>h?h(d.rows):d.rows.map(e=>(function(e,t,i){let r={},n=e.reduce((e,{path:n,field:l},o)=>{let h;h=(0,s.is)(l,a.V)?l:(0,s.is)(l,u.Xs)?l.decoder:l.sql.decoder;let d=e;for(let[e,u]of n.entries())if(e<n.length-1)u in d||(d[u]={}),d=d[u];else{let e=t[o],f=d[u]=null===e?null:h.mapFromDriverValue(e);if(i&&(0,s.is)(l,a.V)&&2===n.length){let e=n[0];e in r?"string"==typeof r[e]&&r[e]!==(0,c.Io)(l.table)&&(r[e]=!1):r[e]=null===f&&(0,c.Io)(l.table)}}return e},{});if(i&&Object.keys(r).length>0)for(let[e,t]of Object.entries(r))"string"!=typeof t||i[t]||(n[e]=null);return n})(i,e,o)))})}all(e={}){return H.k.startActiveSpan("drizzle.execute",()=>{let t=(0,u.Ct)(this.params,e);return this.logger.logQuery(this.rawQueryConfig.text,t),H.k.startActiveSpan("drizzle.driver.execute",e=>(e?.setAttributes({"drizzle.query.name":this.rawQueryConfig.name,"drizzle.query.text":this.rawQueryConfig.text,"drizzle.query.params":JSON.stringify(t)}),this.client.query(this.rawQueryConfig,t).then(e=>e.rows)))})}isResponseInArrayMode(){return this._isResponseInArrayMode}}class ex extends ew{constructor(e,t,i,r={}){super(t),this.client=e,this.schema=i,this.options=r,this.logger=r.logger??new o}static [s.i]="NodePgSession";logger;prepareQuery(e,t,i,r,s){return new e$(this.client,e.sql,e.params,this.logger,t,i,r,s)}async transaction(e,t){let i=this.client instanceof ev?new ex(await this.client.connect(),this.dialect,this.schema,this.options):this,r=new eT(this.dialect,i,this.schema);await r.execute((0,u.ll)`begin${t?(0,u.ll)` ${r.getTransactionConfigSQL(t)}`:void 0}`);try{let t=await e(r);return await r.execute((0,u.ll)`commit`),t}catch(e){throw await r.execute((0,u.ll)`rollback`),e}finally{this.client instanceof ev&&i.client.release()}}}class eT extends eS{static [s.i]="NodePgTransaction";async transaction(e){let t=`sp${this.nestedIndex+1}`,i=new eT(this.dialect,this.session,this.schema,this.nestedIndex+1);await i.execute(u.ll.raw(`savepoint ${t}`));try{let r=await e(i);return await i.execute(u.ll.raw(`release savepoint ${t}`)),r}catch(e){throw await i.execute(u.ll.raw(`rollback to savepoint ${t}`)),e}}}let{types:eN}=r;class eq{constructor(e,t,i={}){this.client=e,this.dialect=t,this.options=i,this.initMappers()}static [s.i]="NodePgDriver";createSession(e){return new ex(this.client,this.dialect,e,{logger:this.options.logger})}initMappers(){eN.setTypeParser(eN.builtins.TIMESTAMPTZ,e=>e),eN.setTypeParser(eN.builtins.TIMESTAMP,e=>e),eN.setTypeParser(eN.builtins.DATE,e=>e),eN.setTypeParser(eN.builtins.INTERVAL,e=>e)}}function eP(e,t={}){let i,r,s=new U;if(!0===t.logger?i=new l:!1!==t.logger&&(i=t.logger),t.schema){let e=(0,B._k)(t.schema,B.DZ);r={fullSchema:t.schema,schema:e.tables,tableNamesMap:e.tableNamesMap}}let n=new eq(e,s,{logger:i}).createSession(r);return new ey(s,n,r)}},9652:(e,t,i)=>{let{createId:r,init:s,getConstants:n,isCuid:l}=i(79373);e.exports.sX=r},10262:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.add5L=t.add5H=t.add4H=t.add4L=t.add3H=t.add3L=t.rotlBL=t.rotlBH=t.rotlSL=t.rotlSH=t.rotr32L=t.rotr32H=t.rotrBL=t.rotrBH=t.rotrSL=t.rotrSH=t.shrSL=t.shrSH=t.toBig=void 0,t.fromBig=s,t.split=n,t.add=w;let i=BigInt(0x100000000-1),r=BigInt(32);function s(e,t=!1){return t?{h:Number(e&i),l:Number(e>>r&i)}:{h:0|Number(e>>r&i),l:0|Number(e&i)}}function n(e,t=!1){let i=new Uint32Array(e.length),r=new Uint32Array(e.length);for(let n=0;n<e.length;n++){let{h:l,l:o}=s(e[n],t);[i[n],r[n]]=[l,o]}return[i,r]}let l=(e,t)=>BigInt(e>>>0)<<r|BigInt(t>>>0);t.toBig=l;let o=(e,t,i)=>e>>>i;t.shrSH=o;let a=(e,t,i)=>e<<32-i|t>>>i;t.shrSL=a;let u=(e,t,i)=>e>>>i|t<<32-i;t.rotrSH=u;let c=(e,t,i)=>e<<32-i|t>>>i;t.rotrSL=c;let h=(e,t,i)=>e<<64-i|t>>>i-32;t.rotrBH=h;let d=(e,t,i)=>e>>>i-32|t<<64-i;t.rotrBL=d;let f=(e,t)=>t;t.rotr32H=f;let p=(e,t)=>e;t.rotr32L=p;let g=(e,t,i)=>e<<i|t>>>32-i;t.rotlSH=g;let m=(e,t,i)=>t<<i|e>>>32-i;t.rotlSL=m;let y=(e,t,i)=>t<<i-32|e>>>64-i;t.rotlBH=y;let b=(e,t,i)=>e<<i-32|t>>>64-i;function w(e,t,i,r){let s=(t>>>0)+(r>>>0);return{h:e+i+(s/0x100000000|0)|0,l:0|s}}t.rotlBL=b;let S=(e,t,i)=>(e>>>0)+(t>>>0)+(i>>>0);t.add3L=S;let v=(e,t,i,r)=>t+i+r+(e/0x100000000|0)|0;t.add3H=v;let $=(e,t,i,r)=>(e>>>0)+(t>>>0)+(i>>>0)+(r>>>0);t.add4L=$;let x=(e,t,i,r,s)=>t+i+r+s+(e/0x100000000|0)|0;t.add4H=x;let T=(e,t,i,r,s)=>(e>>>0)+(t>>>0)+(i>>>0)+(r>>>0)+(s>>>0);t.add5L=T;let N=(e,t,i,r,s,n)=>t+i+r+s+n+(e/0x100000000|0)|0;t.add5H=N,t.default={fromBig:s,split:n,toBig:l,shrSH:o,shrSL:a,rotrSH:u,rotrSL:c,rotrBH:h,rotrBL:d,rotr32H:f,rotr32L:p,rotlSH:g,rotlSL:m,rotlBH:y,rotlBL:b,add:w,add3L:S,add3H:v,add4L:$,add4H:x,add5H:N,add5L:T}},10302:(e,t,i)=>{"use strict";i.d(t,{yf:()=>o});var r=i(98100),s=i(46866);class n extends s.pe{static [r.i]="PgVarcharBuilder";constructor(e,t){super(e,"string","PgVarchar"),this.config.length=t.length,this.config.enumValues=t.enum}build(e){return new l(e,this.config)}}class l extends s.Kl{static [r.i]="PgVarchar";length=this.config.length;enumValues=this.config.enumValues;getSQLType(){return void 0===this.length?"varchar":`varchar(${this.length})`}}function o(e,t={}){return new n(e,t)}},12163:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.shake256=t.shake128=t.keccak_512=t.keccak_384=t.keccak_256=t.keccak_224=t.sha3_512=t.sha3_384=t.sha3_256=t.sha3_224=t.Keccak=void 0,t.keccakP=w;let r=i(34321),s=i(10262),n=i(83527),l=[],o=[],a=[],u=BigInt(0),c=BigInt(1),h=BigInt(2),d=BigInt(7),f=BigInt(256),p=BigInt(113);for(let e=0,t=c,i=1,r=0;e<24;e++){[i,r]=[r,(2*i+3*r)%5],l.push(2*(5*r+i)),o.push((e+1)*(e+2)/2%64);let s=u;for(let e=0;e<7;e++)(t=(t<<c^(t>>d)*p)%f)&h&&(s^=c<<(c<<BigInt(e))-c);a.push(s)}let[g,m]=(0,s.split)(a,!0),y=(e,t,i)=>i>32?(0,s.rotlBH)(e,t,i):(0,s.rotlSH)(e,t,i),b=(e,t,i)=>i>32?(0,s.rotlBL)(e,t,i):(0,s.rotlSL)(e,t,i);function w(e,t=24){let i=new Uint32Array(10);for(let r=24-t;r<24;r++){for(let t=0;t<10;t++)i[t]=e[t]^e[t+10]^e[t+20]^e[t+30]^e[t+40];for(let t=0;t<10;t+=2){let r=(t+8)%10,s=(t+2)%10,n=i[s],l=i[s+1],o=y(n,l,1)^i[r],a=b(n,l,1)^i[r+1];for(let i=0;i<50;i+=10)e[t+i]^=o,e[t+i+1]^=a}let t=e[2],s=e[3];for(let i=0;i<24;i++){let r=o[i],n=y(t,s,r),a=b(t,s,r),u=l[i];t=e[u],s=e[u+1],e[u]=n,e[u+1]=a}for(let t=0;t<50;t+=10){for(let r=0;r<10;r++)i[r]=e[t+r];for(let r=0;r<10;r++)e[t+r]^=~i[(r+2)%10]&i[(r+4)%10]}e[0]^=g[r],e[1]^=m[r]}i.fill(0)}class S extends n.Hash{constructor(e,t,i,s=!1,l=24){if(super(),this.blockLen=e,this.suffix=t,this.outputLen=i,this.enableXOF=s,this.rounds=l,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,(0,r.anumber)(i),0>=this.blockLen||this.blockLen>=200)throw Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=(0,n.u32)(this.state)}keccak(){n.isLE||(0,n.byteSwap32)(this.state32),w(this.state32,this.rounds),n.isLE||(0,n.byteSwap32)(this.state32),this.posOut=0,this.pos=0}update(e){(0,r.aexists)(this);let{blockLen:t,state:i}=this,s=(e=(0,n.toBytes)(e)).length;for(let r=0;r<s;){let n=Math.min(t-this.pos,s-r);for(let t=0;t<n;t++)i[this.pos++]^=e[r++];this.pos===t&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:e,suffix:t,pos:i,blockLen:r}=this;e[i]^=t,(128&t)!=0&&i===r-1&&this.keccak(),e[r-1]^=128,this.keccak()}writeInto(e){(0,r.aexists)(this,!1),(0,r.abytes)(e),this.finish();let t=this.state,{blockLen:i}=this;for(let r=0,s=e.length;r<s;){this.posOut>=i&&this.keccak();let n=Math.min(i-this.posOut,s-r);e.set(t.subarray(this.posOut,this.posOut+n),r),this.posOut+=n,r+=n}return e}xofInto(e){if(!this.enableXOF)throw Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return(0,r.anumber)(e),this.xofInto(new Uint8Array(e))}digestInto(e){if((0,r.aoutput)(e,this),this.finished)throw Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(e){let{blockLen:t,suffix:i,outputLen:r,rounds:s,enableXOF:n}=this;return e||(e=new S(t,i,r,n,s)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=s,e.suffix=i,e.outputLen=r,e.enableXOF=n,e.destroyed=this.destroyed,e}}t.Keccak=S;let v=(e,t,i)=>(0,n.wrapConstructor)(()=>new S(t,e,i));t.sha3_224=v(6,144,28),t.sha3_256=v(6,136,32),t.sha3_384=v(6,104,48),t.sha3_512=v(6,72,64),t.keccak_224=v(1,144,28),t.keccak_256=v(1,136,32),t.keccak_384=v(1,104,48),t.keccak_512=v(1,72,64);let $=(e,t,i)=>(0,n.wrapXOFConstructorWithOpts)((r={})=>new S(t,e,void 0===r.dkLen?i:r.dkLen,!0));t.shake128=$(31,168,16),t.shake256=$(31,136,32)},15061:(e,t,i)=>{"use strict";let r,s;i.d(t,{k:()=>l});var n=i(16581);let l={startActiveSpan:(e,t)=>r?(s||(s=r.trace.getTracer("drizzle-orm","0.31.4")),(0,n.i)((i,r)=>r.startActiveSpan(e,e=>{try{return t(e)}catch(t){throw e.setStatus({code:i.SpanStatusCode.ERROR,message:t instanceof Error?t.message:"Unknown error"}),t}finally{e.end()}}),r,s)):t()}},16077:(e,t,i)=>{"use strict";i.d(t,{Pq:()=>o,iX:()=>l});var r=i(98100),s=i(46866);class n extends s.pe{static [r.i]="PgJsonBuilder";constructor(e){super(e,"json","PgJson")}build(e){return new l(e,this.config)}}class l extends s.Kl{static [r.i]="PgJson";constructor(e,t){super(e,t)}getSQLType(){return"json"}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){if("string"==typeof e)try{return JSON.parse(e)}catch{}return e}}function o(e){return new n(e)}},16581:(e,t,i)=>{"use strict";function r(e,...t){return e(...t)}i.d(t,{i:()=>r})},19745:(e,t,i)=>{"use strict";i.d(t,{V:()=>s});var r=i(98100);class s{constructor(e,t){this.table=e,this.config=t,this.name=t.name,this.notNull=t.notNull,this.default=t.default,this.defaultFn=t.defaultFn,this.onUpdateFn=t.onUpdateFn,this.hasDefault=t.hasDefault,this.primary=t.primaryKey,this.isUnique=t.isUnique,this.uniqueName=t.uniqueName,this.uniqueType=t.uniqueType,this.dataType=t.dataType,this.columnType=t.columnType}static [r.i]="Column";name;primary;notNull;default;defaultFn;onUpdateFn;hasDefault;isUnique;uniqueName;uniqueType;dataType;columnType;enumValues=void 0;config;mapFromDriverValue(e){return e}mapToDriverValue(e){return e}}},24764:(e,t,i)=>{"use strict";i.d(t,{u:()=>l});var r=i(98100),s=i(7160),n=i(46866);class l extends n.pe{static [r.i]="PgDateColumnBaseBuilder";defaultNow(){return this.default((0,s.ll)`now()`)}}},32019:(e,t,i)=>{"use strict";i.d(t,{cJ:()=>o,mu:()=>l});var r=i(98100),s=i(74059);let n=Symbol.for("drizzle:PgInlineForeignKeys");class l extends s.XI{static [r.i]="PgTable";static Symbol=Object.assign({},s.XI.Symbol,{InlineForeignKeys:n});[n]=[];[s.XI.Symbol.ExtraConfigBuilder]=void 0}let o=(e,t,i)=>(function(e,t,i,r,o=e){let a=new l(e,r,o),u=Object.fromEntries(Object.entries(t).map(([e,t])=>{let i=t.build(a);return a[n].push(...t.buildForeignKeys(i,a)),[e,i]})),c=Object.fromEntries(Object.entries(t).map(([e,t])=>[e,t.buildExtraConfigColumn(a)])),h=Object.assign(a,u);return h[s.XI.Symbol.Columns]=u,h[s.XI.Symbol.ExtraConfigColumns]=c,i&&(h[l.Symbol.ExtraConfigBuilder]=i),h})(e,t,i,void 0)},33094:(e,t,i)=>{"use strict";i.d(t,{DZ:()=>w,I$:()=>function e(t,i,r,l,o=e=>e){let a={};for(let[c,h]of l.entries())if(h.isJson){let s=i.relations[h.tsKey],l=r[c],u="string"==typeof l?JSON.parse(l):l;a[h.tsKey]=(0,n.is)(s,d)?u&&e(t,t[h.relationTableTsKey],u,h.selection,o):u.map(i=>e(t,t[h.relationTableTsKey],i,h.selection,o))}else{let e,t=o(r[c]),i=h.field;e=(0,n.is)(i,s.V)?i:(0,n.is)(i,u.Xs)?i.decoder:i.sql.decoder,a[h.tsKey]=null===t?null:e.mapFromDriverValue(t)}return a},K1:()=>y,W0:()=>b,_k:()=>m,iv:()=>f,mm:()=>p,pD:()=>d,rl:()=>g});var r=i(74059),s=i(19745),n=i(98100),l=i(56850),o=i(60988),a=i(1770),u=i(7160);class c{constructor(e,t,i){this.sourceTable=e,this.referencedTable=t,this.relationName=i,this.referencedTableName=t[r.XI.Symbol.Name]}static [n.i]="Relation";referencedTableName;fieldName}class h{constructor(e,t){this.table=e,this.config=t}static [n.i]="Relations"}class d extends c{constructor(e,t,i,r){super(e,t,i?.relationName),this.config=i,this.isNullable=r}static [n.i]="One";withFieldName(e){let t=new d(this.sourceTable,this.referencedTable,this.config,this.isNullable);return t.fieldName=e,t}}class f extends c{constructor(e,t,i){super(e,t,i?.relationName),this.config=i}static [n.i]="Many";withFieldName(e){let t=new f(this.sourceTable,this.referencedTable,this.config);return t.fieldName=e,t}}function p(){return{and:o.Uo,between:o.Tq,eq:o.eq,exists:o.t2,gt:o.gt,gte:o.RO,ilike:o.B3,inArray:o.RV,isNull:o.kZ,isNotNull:o.Pe,like:o.mj,lt:o.lt,lte:o.wJ,ne:o.ne,not:o.AU,notBetween:o.o8,notExists:o.KJ,notLike:o.RK,notIlike:o.q1,notInArray:o.KL,or:o.or,sql:u.ll}}function g(){return{sql:u.ll,asc:a.Y,desc:a.i}}function m(e,t){1===Object.keys(e).length&&"default"in e&&!(0,n.is)(e.default,r.XI)&&(e=e.default);let i={},s={},o={};for(let[a,u]of Object.entries(e))if((0,n.is)(u,r.XI)){let e=(0,r.Lf)(u),t=s[e];for(let s of(i[e]=a,o[a]={tsName:a,dbName:u[r.XI.Symbol.Name],schema:u[r.XI.Symbol.Schema],columns:u[r.XI.Symbol.Columns],relations:t?.relations??{},primaryKey:t?.primaryKey??[]},Object.values(u[r.XI.Symbol.Columns])))s.primary&&o[a].primaryKey.push(s);let c=u[r.XI.Symbol.ExtraConfigBuilder]?.(u[r.XI.Symbol.ExtraConfigColumns]);if(c)for(let e of Object.values(c))(0,n.is)(e,l.hv)&&o[a].primaryKey.push(...e.columns)}else if((0,n.is)(u,h)){let e,n=(0,r.Lf)(u.table),l=i[n];for(let[i,r]of Object.entries(u.config(t(u.table))))if(l){let t=o[l];t.relations[i]=r,e&&t.primaryKey.push(...e)}else n in s||(s[n]={relations:{},primaryKey:e}),s[n].relations[i]=r}return{tables:o,tableNamesMap:i}}function y(e,t){return new h(e,e=>Object.fromEntries(Object.entries(t(e)).map(([e,t])=>[e,t.withFieldName(e)])))}function b(e,t,i){if((0,n.is)(i,d)&&i.config)return{fields:i.config.fields,references:i.config.references};let s=t[(0,r.Lf)(i.referencedTable)];if(!s)throw Error(`Table "${i.referencedTable[r.XI.Symbol.Name]}" not found in schema`);let l=e[s];if(!l)throw Error(`Table "${s}" not found in schema`);let o=i.sourceTable,a=t[(0,r.Lf)(o)];if(!a)throw Error(`Table "${o[r.XI.Symbol.Name]}" not found in schema`);let u=[];for(let e of Object.values(l.relations))(i.relationName&&i!==e&&e.relationName===i.relationName||!i.relationName&&e.referencedTable===i.sourceTable)&&u.push(e);if(u.length>1)throw i.relationName?Error(`There are multiple relations with name "${i.relationName}" in table "${s}"`):Error(`There are multiple relations between "${s}" and "${i.sourceTable[r.XI.Symbol.Name]}". Please specify relation name`);if(u[0]&&(0,n.is)(u[0],d)&&u[0].config)return{fields:u[0].config.references,references:u[0].config.fields};throw Error(`There is not enough information to infer relation "${a}.${i.fieldName}"`)}function w(e){return{one:function(t,i){return new d(e,t,i,i?.fields.reduce((e,t)=>e&&t.notNull,!0)??!1)},many:function(t,i){return new f(e,t,i)}}}},34321:(e,t)=>{"use strict";function i(e){if(!Number.isSafeInteger(e)||e<0)throw Error("positive integer expected, got "+e)}function r(e,...t){if(!(e instanceof Uint8Array||ArrayBuffer.isView(e)&&"Uint8Array"===e.constructor.name))throw Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw Error("Uint8Array expected of length "+t+", got length="+e.length)}function s(e){if("function"!=typeof e||"function"!=typeof e.create)throw Error("Hash should be wrapped by utils.wrapConstructor");i(e.outputLen),i(e.blockLen)}function n(e,t=!0){if(e.destroyed)throw Error("Hash instance has been destroyed");if(t&&e.finished)throw Error("Hash#digest() has already been called")}function l(e,t){r(e);let i=t.outputLen;if(e.length<i)throw Error("digestInto() expects output buffer of length at least "+i)}Object.defineProperty(t,"__esModule",{value:!0}),t.anumber=i,t.number=i,t.abytes=r,t.bytes=r,t.ahash=s,t.aexists=n,t.aoutput=l,t.default={number:i,bytes:r,hash:s,exists:n,output:l}},46866:(e,t,i)=>{"use strict";i.d(t,{ae:()=>b,Kl:()=>m,pe:()=>g});var r=i(98100);class s{static [r.i]="ColumnBuilder";config;constructor(e,t,i){this.config={name:e,notNull:!1,default:void 0,hasDefault:!1,primaryKey:!1,isUnique:!1,uniqueName:void 0,uniqueType:void 0,dataType:t,columnType:i}}$type(){return this}notNull(){return this.config.notNull=!0,this}default(e){return this.config.default=e,this.config.hasDefault=!0,this}$defaultFn(e){return this.config.defaultFn=e,this.config.hasDefault=!0,this}$default=this.$defaultFn;$onUpdateFn(e){return this.config.onUpdateFn=e,this.config.hasDefault=!0,this}$onUpdate=this.$onUpdateFn;primaryKey(){return this.config.primaryKey=!0,this.config.notNull=!0,this}}var n=i(19745),l=i(32019);class o{static [r.i]="PgForeignKeyBuilder";reference;_onUpdate="no action";_onDelete="no action";constructor(e,t){this.reference=()=>{let{name:t,columns:i,foreignColumns:r}=e();return{name:t,columns:i,foreignTable:r[0].table,foreignColumns:r}},t&&(this._onUpdate=t.onUpdate,this._onDelete=t.onDelete)}onUpdate(e){return this._onUpdate=void 0===e?"no action":e,this}onDelete(e){return this._onDelete=void 0===e?"no action":e,this}build(e){return new a(e,this)}}class a{constructor(e,t){this.table=e,this.reference=t.reference,this.onUpdate=t._onUpdate,this.onDelete=t._onDelete}static [r.i]="PgForeignKey";reference;onUpdate;onDelete;getName(){let{name:e,columns:t,foreignColumns:i}=this.reference(),r=t.map(e=>e.name),s=i.map(e=>e.name),n=[this.table[l.mu.Symbol.Name],...r,i[0].table[l.mu.Symbol.Name],...s];return e??`${n.join("_")}_fk`}}var u=i(16581);function c(e,t){return`${e[l.mu.Symbol.Name]}_${t.join("_")}_unique`}class h{constructor(e,t){this.name=t,this.columns=e}static [r.i]=null;columns;nullsNotDistinctConfig=!1;nullsNotDistinct(){return this.nullsNotDistinctConfig=!0,this}build(e){return new f(e,this.columns,this.nullsNotDistinctConfig,this.name)}}class d{static [r.i]=null;name;constructor(e){this.name=e}on(...e){return new h(e,this.name)}}class f{constructor(e,t,i,r){this.table=e,this.columns=t,this.name=r??c(this.table,this.columns.map(e=>e.name)),this.nullsNotDistinct=i}static [r.i]=null;columns;name;nullsNotDistinct=!1;getName(){return this.name}}function p(e,t,i){for(let r=t;r<e.length;r++){let s=e[r];if("\\"===s){r++;continue}if('"'===s)return[e.slice(t,r).replace(/\\/g,""),r+1];if(!i&&(","===s||"}"===s))return[e.slice(t,r).replace(/\\/g,""),r]}return[e.slice(t).replace(/\\/g,""),e.length]}class g extends s{foreignKeyConfigs=[];static [r.i]="PgColumnBuilder";array(e){return new w(this.config.name,this,e)}references(e,t={}){return this.foreignKeyConfigs.push({ref:e,actions:t}),this}unique(e,t){return this.config.isUnique=!0,this.config.uniqueName=e,this.config.uniqueType=t?.nulls,this}buildForeignKeys(e,t){return this.foreignKeyConfigs.map(({ref:i,actions:r})=>(0,u.i)((i,r)=>{let s=new o(()=>({columns:[e],foreignColumns:[i()]}));return r.onUpdate&&s.onUpdate(r.onUpdate),r.onDelete&&s.onDelete(r.onDelete),s.build(t)},i,r))}buildExtraConfigColumn(e){return new y(e,this.config)}}class m extends n.V{constructor(e,t){t.uniqueName||(t.uniqueName=c(e,[t.name])),super(e,t),this.table=e}static [r.i]="PgColumn"}class y extends m{static [r.i]="ExtraConfigColumn";getSQLType(){return this.getSQLType()}indexConfig={order:this.config.order??"asc",nulls:this.config.nulls??"last",opClass:this.config.opClass};defaultConfig={order:"asc",nulls:"last",opClass:void 0};asc(){return this.indexConfig.order="asc",this}desc(){return this.indexConfig.order="desc",this}nullsFirst(){return this.indexConfig.nulls="first",this}nullsLast(){return this.indexConfig.nulls="last",this}op(e){return this.indexConfig.opClass=e,this}}class b{static [r.i]="IndexedColumn";constructor(e,t,i){this.name=e,this.type=t,this.indexConfig=i}name;type;indexConfig}class w extends g{static [r.i]="PgArrayBuilder";constructor(e,t,i){super(e,"array","PgArray"),this.config.baseBuilder=t,this.config.size=i}build(e){let t=this.config.baseBuilder.build(e);return new S(e,this.config,t)}}class S extends m{constructor(e,t,i,r){super(e,t),this.baseColumn=i,this.range=r,this.size=t.size}size;static [r.i]="PgArray";getSQLType(){return`${this.baseColumn.getSQLType()}[${"number"==typeof this.size?this.size:""}]`}mapFromDriverValue(e){return"string"==typeof e&&(e=function(e){let[t]=function e(t,i=0){let r=[],s=i,n=!1;for(;s<t.length;){let l=t[s];if(","===l){(n||s===i)&&r.push(""),n=!0,s++;continue}if(n=!1,"\\"===l){s+=2;continue}if('"'===l){let[e,i]=p(t,s+1,!0);r.push(e),s=i;continue}if("}"===l)return[r,s+1];if("{"===l){let[i,n]=e(t,s+1);r.push(i),s=n;continue}let[o,a]=p(t,s,!1);r.push(o),s=a}return[r,s]}(e,1);return t}(e)),e.map(e=>this.baseColumn.mapFromDriverValue(e))}mapToDriverValue(e,t=!1){let i=e.map(e=>null===e?null:(0,r.is)(this.baseColumn,S)?this.baseColumn.mapToDriverValue(e,!0):this.baseColumn.mapToDriverValue(e));return t?i:function e(t){return`{${t.map(t=>Array.isArray(t)?e(t):"string"==typeof t?`"${t.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}"`:`${t}`).join(",")}}`}(i)}}},52363:(e,t,i)=>{"use strict";i.d(t,{KM:()=>o,vE:()=>c,xQ:()=>u});var r=i(98100),s=i(46866),n=i(24764);class l extends n.u{static [r.i]="PgTimestampBuilder";constructor(e,t,i){super(e,"date","PgTimestamp"),this.config.withTimezone=t,this.config.precision=i}build(e){return new o(e,this.config)}}class o extends s.Kl{static [r.i]="PgTimestamp";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":` (${this.precision})`;return`timestamp${e}${this.withTimezone?" with time zone":""}`}mapFromDriverValue=e=>new Date(this.withTimezone?e:e+"+0000");mapToDriverValue=e=>e.toISOString()}class a extends n.u{static [r.i]="PgTimestampStringBuilder";constructor(e,t,i){super(e,"string","PgTimestampString"),this.config.withTimezone=t,this.config.precision=i}build(e){return new u(e,this.config)}}class u extends s.Kl{static [r.i]="PgTimestampString";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":`(${this.precision})`;return`timestamp${e}${this.withTimezone?" with time zone":""}`}}function c(e,t={}){return"string"===t.mode?new a(e,t.withTimezone??!1,t.precision):new l(e,t.withTimezone??!1,t.precision)}},56839:(e,t,i)=>{"use strict";i.d(t,{zM:()=>o});var r=i(98100),s=i(46866);class n extends s.pe{static [r.i]="PgBooleanBuilder";constructor(e){super(e,"boolean","PgBoolean")}build(e){return new l(e,this.config)}}class l extends s.Kl{static [r.i]="PgBoolean";getSQLType(){return"boolean"}}function o(e){return new n(e)}},56850:(e,t,i)=>{"use strict";i.d(t,{hv:()=>l,ie:()=>n});var r=i(98100),s=i(32019);function n(...e){return e[0].columns?new l(e[0].columns,e[0].name):new l(e)}class l{static [r.i]="PgPrimaryKeyBuilder";columns;name;constructor(e,t){this.columns=e,this.name=t}build(e){return new o(e,this.columns,this.name)}}class o{constructor(e,t,i){this.table=e,this.columns=t,this.name=i}static [r.i]="PgPrimaryKey";columns;name;getName(){return this.name??`${this.table[s.mu.Symbol.Name]}_${this.columns.map(e=>e.name).join("_")}_pk`}}},57052:(e,t,i)=>{"use strict";i.d(t,{Qq:()=>o});var r=i(98100),s=i(46866);class n extends s.pe{static [r.i]="PgTextBuilder";constructor(e,t){super(e,"string","PgText"),this.config.enumValues=t.enum}build(e){return new l(e,this.config)}}class l extends s.Kl{static [r.i]="PgText";enumValues=this.config.enumValues;getSQLType(){return"text"}}function o(e,t={}){return new n(e,t)}},60988:(e,t,i)=>{"use strict";i.d(t,{AU:()=>d,B3:()=>P,KJ:()=>$,KL:()=>b,Pe:()=>S,RK:()=>q,RO:()=>p,RV:()=>y,Tq:()=>x,Uo:()=>c,eq:()=>a,gt:()=>f,kZ:()=>w,lt:()=>g,mj:()=>N,ne:()=>u,o8:()=>T,or:()=>h,q1:()=>Q,t2:()=>v,wJ:()=>m});var r=i(19745),s=i(98100),n=i(74059),l=i(7160);function o(e,t){return!(0,l.eG)(t)||(0,l.qt)(e)||(0,s.is)(e,l.Iw)||(0,s.is)(e,l.Or)||(0,s.is)(e,r.V)||(0,s.is)(e,n.XI)||(0,s.is)(e,l.Ss)?e:new l.Iw(e,t)}let a=(e,t)=>(0,l.ll)`${e} = ${o(t,e)}`,u=(e,t)=>(0,l.ll)`${e} <> ${o(t,e)}`;function c(...e){let t=e.filter(e=>void 0!==e);if(0!==t.length)return new l.Xs(1===t.length?t:[new l.DJ("("),l.ll.join(t,new l.DJ(" and ")),new l.DJ(")")])}function h(...e){let t=e.filter(e=>void 0!==e);if(0!==t.length)return new l.Xs(1===t.length?t:[new l.DJ("("),l.ll.join(t,new l.DJ(" or ")),new l.DJ(")")])}function d(e){return(0,l.ll)`not ${e}`}let f=(e,t)=>(0,l.ll)`${e} > ${o(t,e)}`,p=(e,t)=>(0,l.ll)`${e} >= ${o(t,e)}`,g=(e,t)=>(0,l.ll)`${e} < ${o(t,e)}`,m=(e,t)=>(0,l.ll)`${e} <= ${o(t,e)}`;function y(e,t){if(Array.isArray(t)){if(0===t.length)throw Error("inArray requires at least one value");return(0,l.ll)`${e} in ${t.map(t=>o(t,e))}`}return(0,l.ll)`${e} in ${o(t,e)}`}function b(e,t){if(Array.isArray(t)){if(0===t.length)throw Error("notInArray requires at least one value");return(0,l.ll)`${e} not in ${t.map(t=>o(t,e))}`}return(0,l.ll)`${e} not in ${o(t,e)}`}function w(e){return(0,l.ll)`${e} is null`}function S(e){return(0,l.ll)`${e} is not null`}function v(e){return(0,l.ll)`exists ${e}`}function $(e){return(0,l.ll)`not exists ${e}`}function x(e,t,i){return(0,l.ll)`${e} between ${o(t,e)} and ${o(i,e)}`}function T(e,t,i){return(0,l.ll)`${e} not between ${o(t,e)} and ${o(i,e)}`}function N(e,t){return(0,l.ll)`${e} like ${t}`}function q(e,t){return(0,l.ll)`${e} not like ${t}`}function P(e,t){return(0,l.ll)`${e} ilike ${t}`}function Q(e,t){return(0,l.ll)`${e} not ilike ${t}`}},74059:(e,t,i)=>{"use strict";i.d(t,{Io:()=>f,Lf:()=>p,XI:()=>d});var r=i(98100);let s=Symbol.for("drizzle:Name"),n=Symbol.for("drizzle:Schema"),l=Symbol.for("drizzle:Columns"),o=Symbol.for("drizzle:ExtraConfigColumns"),a=Symbol.for("drizzle:OriginalName"),u=Symbol.for("drizzle:BaseName"),c=Symbol.for("drizzle:IsAlias"),h=Symbol.for("drizzle:ExtraConfigBuilder");Symbol.for("drizzle:IsDrizzleTable");class d{static [r.i]="Table";static Symbol={Name:s,Schema:n,OriginalName:a,Columns:l,ExtraConfigColumns:o,BaseName:u,IsAlias:c,ExtraConfigBuilder:h};[s];[a];[n];[l];[o];[u];[c]=!1;[h]=void 0;constructor(e,t,i){this[s]=this[a]=e,this[n]=t,this[u]=i}}function f(e){return e[s]}function p(e){return`${e[n]??"public"}.${e[s]}`}},79373:(e,t,i)=>{let{sha3_512:r}=i(12163),s=24,n=32,l=(e=4,t=Math.random)=>{let i="";for(;i.length<e;)i+=Math.floor(36*t()).toString(36);return i};function o(e){let t=0n;for(let i of e.values())t=(t<<8n)+BigInt(i);return t}let a=(e="")=>o(r(e)).toString(36).slice(1),u=Array.from({length:26},(e,t)=>String.fromCharCode(t+97)),c=e=>u[Math.floor(e()*u.length)],h=({globalObj:e="undefined"!=typeof global?global:"undefined"!=typeof window?window:{},random:t=Math.random}={})=>{let i=Object.keys(e).toString();return a(i.length?i+l(n,t):l(n,t)).substring(0,n)},d=e=>()=>e++,f=0x1c6b1f1f,p=({random:e=Math.random,counter:t=d(Math.floor(e()*f)),length:i=s,fingerprint:r=h({random:e})}={})=>function(){let s=c(e),n=Date.now().toString(36),o=t().toString(36),u=l(i,e),h=`${n+u+o+r}`;return`${s+a(h).substring(1,i)}`},g=p();e.exports.getConstants=()=>({defaultLength:s,bigLength:n}),e.exports.init=p,e.exports.createId=g,e.exports.bufToBigInt=o,e.exports.createCounter=d,e.exports.createFingerprint=h,e.exports.isCuid=(e,{minLength:t=2,maxLength:i=n}={})=>{let r=e.length;return!!("string"==typeof e&&r>=t&&r<=i&&/^[0-9a-z]+$/.test(e))}},83527:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Hash=t.nextTick=t.byteSwapIfBE=t.byteSwap=t.isLE=t.rotl=t.rotr=t.createView=t.u32=t.u8=void 0,t.isBytes=function(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&"Uint8Array"===e.constructor.name},t.byteSwap32=function(e){for(let i=0;i<e.length;i++)e[i]=(0,t.byteSwap)(e[i])},t.bytesToHex=function(e){(0,s.abytes)(e);let t="";for(let i=0;i<e.length;i++)t+=n[e[i]];return t},t.hexToBytes=function(e){if("string"!=typeof e)throw Error("hex string expected, got "+typeof e);let t=e.length,i=t/2;if(t%2)throw Error("hex string expected, got unpadded hex of length "+t);let r=new Uint8Array(i);for(let t=0,s=0;t<i;t++,s+=2){let i=o(e.charCodeAt(s)),n=o(e.charCodeAt(s+1));if(void 0===i||void 0===n)throw Error('hex string expected, got non-hex character "'+(e[s]+e[s+1])+'" at index '+s);r[t]=16*i+n}return r},t.asyncLoop=a,t.utf8ToBytes=u,t.toBytes=c,t.concatBytes=function(...e){let t=0;for(let i=0;i<e.length;i++){let r=e[i];(0,s.abytes)(r),t+=r.length}let i=new Uint8Array(t);for(let t=0,r=0;t<e.length;t++){let s=e[t];i.set(s,r),r+=s.length}return i},t.checkOpts=function(e,t){if(void 0!==t&&"[object Object]"!==({}).toString.call(t))throw Error("Options should be object or undefined");return Object.assign(e,t)},t.wrapConstructor=function(e){let t=t=>e().update(c(t)).digest(),i=e();return t.outputLen=i.outputLen,t.blockLen=i.blockLen,t.create=()=>e(),t},t.wrapConstructorWithOpts=function(e){let t=(t,i)=>e(i).update(c(t)).digest(),i=e({});return t.outputLen=i.outputLen,t.blockLen=i.blockLen,t.create=t=>e(t),t},t.wrapXOFConstructorWithOpts=function(e){let t=(t,i)=>e(i).update(c(t)).digest(),i=e({});return t.outputLen=i.outputLen,t.blockLen=i.blockLen,t.create=t=>e(t),t},t.randomBytes=function(e=32){if(r.crypto&&"function"==typeof r.crypto.getRandomValues)return r.crypto.getRandomValues(new Uint8Array(e));if(r.crypto&&"function"==typeof r.crypto.randomBytes)return r.crypto.randomBytes(e);throw Error("crypto.getRandomValues must be defined")};let r=i(83945),s=i(34321);t.u8=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),t.u32=e=>new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4)),t.createView=e=>new DataView(e.buffer,e.byteOffset,e.byteLength),t.rotr=(e,t)=>e<<32-t|e>>>t,t.rotl=(e,t)=>e<<t|e>>>32-t>>>0,t.isLE=68===new Uint8Array(new Uint32Array([0x11223344]).buffer)[0],t.byteSwap=e=>e<<24&0xff000000|e<<8&0xff0000|e>>>8&65280|e>>>24&255,t.byteSwapIfBE=t.isLE?e=>e:e=>(0,t.byteSwap)(e);let n=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0")),l={_0:48,_9:57,A:65,F:70,a:97,f:102};function o(e){return e>=l._0&&e<=l._9?e-l._0:e>=l.A&&e<=l.F?e-(l.A-10):e>=l.a&&e<=l.f?e-(l.a-10):void 0}async function a(e,i,r){let s=Date.now();for(let n=0;n<e;n++){r(n);let e=Date.now()-s;e>=0&&e<i||(await (0,t.nextTick)(),s+=e)}}function u(e){if("string"!=typeof e)throw Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array(new TextEncoder().encode(e))}function c(e){return"string"==typeof e&&(e=u(e)),(0,s.abytes)(e),e}t.nextTick=async()=>{};class h{clone(){return this._cloneInto()}}t.Hash=h},83945:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.crypto=void 0;let r=i(77598);t.crypto=r&&"object"==typeof r&&"webcrypto"in r?r.webcrypto:r&&"object"==typeof r&&"randomBytes"in r?r:void 0},92821:(e,t,i)=>{"use strict";i.d(t,{J:()=>n,n:()=>s});var r=i(98100);class s{static [r.i]="Subquery";constructor(e,t,i,r=!1){this._={brand:"Subquery",sql:e,selectedFields:t,alias:i,isWith:r}}}class n extends s{static [r.i]="WithSubquery"}},94485:(e,t,i)=>{"use strict";i.d(t,{nd:()=>o});var r=i(98100),s=i(46866);class n extends s.pe{static [r.i]="PgIntegerBuilder";constructor(e){super(e,"number","PgInteger")}build(e){return new l(e,this.config)}}class l extends s.Kl{static [r.i]="PgInteger";getSQLType(){return"integer"}mapFromDriverValue(e){return"string"==typeof e?Number.parseInt(e):e}}function o(e){return new n(e)}},97430:(e,t,i)=>{"use strict";i.d(t,{n:()=>r});let r=Symbol.for("drizzle:ViewBaseConfig")},98100:(e,t,i)=>{"use strict";i.d(t,{i:()=>r,is:()=>s});let r=Symbol.for("drizzle:entityKind");function s(e,t){if(!e||"object"!=typeof e)return!1;if(e instanceof t)return!0;if(!Object.prototype.hasOwnProperty.call(t,r))throw Error(`Class "${t.name??"<unknown>"}" doesn't look like a Drizzle entity. If this is incorrect and the class is provided by Drizzle, please report this as a bug.`);let i=e.constructor;if(i)for(;i;){if(r in i&&i[r]===t[r])return!0;i=Object.getPrototypeOf(i)}return!1}Symbol.for("drizzle:hasOwnEntityKind")}};