
/** @type {import('next').NextConfig} */
const nextConfig = {
    output: "standalone", // for self-hosting
    eslint: {
        // 빌드 시 ESLint 오류를 무시 (개발 중에는 IDE에서 확인)
        ignoreDuringBuilds: true,
    },
    pageExtensions: ['js', 'jsx', 'mdx', 'ts', 'tsx'],
    images: {
        remotePatterns: [
            {
                protocol: 'https',
                hostname: 'avatars.githubusercontent.com',
                port: '',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'gitlab.geon.kr',
                port: '',
                pathname: '/**',
            },
        ],
    },
};

export default nextConfig

