"use server";

import { z } from "zod";

import { signIn } from "./auth";

const authFormSchema = z.object({
	id: z.string(),
	email: z.string().email().nullish(),
	password: z.string().min(6),
});

export interface LoginActionState {
	status: "idle" | "in_progress" | "success" | "failed" | "invalid_data";
}

export const login = async (
	_: LoginActionState,
	formData: FormData,
): Promise<LoginActionState> => {
	try {
		const validatedData = authFormSchema.parse({
			id: formData.get("id"),
			// email: formData.get("email"),
			password: formData.get("password"),
		});
		await signIn("credentials", {
			id: validatedData.id,
			password: validatedData.password,
			redirect: false,
		});

		return { status: "success" };
	} catch (error) {
		if (error instanceof z.ZodError) {
			return { status: "invalid_data" };
		}

		return { status: "failed" };
	}
};

export interface RegisterActionState {
	status:
		| "idle"
		| "in_progress"
		| "success"
		| "failed"
		| "user_exists"
		| "invalid_data";
}

// export const register = async (
// 	_: RegisterActionState,
// 	formData: FormData,
// ): Promise<RegisterActionState> => {
// 	try {
// 		const validatedData = authFormSchema.parse({
// 			email: formData.get("email"),
// 			password: formData.get("password"),
// 		});
//
// 		let [user] = await getUser(validatedData.email);
//
// 		if (user) {
// 			return { status: "user_exists" } as RegisterActionState;
// 		} else {
// 			await createUser(validatedData.email, validatedData.password);
// 			await signIn("credentials", {
// 				email: validatedData.email,
// 				password: validatedData.password,
// 				redirect: false,
// 			});
//
// 			return { status: "success" };
// 		}
// 	} catch (error) {
// 		if (error instanceof z.ZodError) {
// 			return { status: "invalid_data" };
// 		}
//
// 		return { status: "failed" };
// 	}
// };
