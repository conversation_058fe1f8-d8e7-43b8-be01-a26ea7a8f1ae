#!/bin/bash

# 인자로 받은 경로를 변수에 저장
target_dir="$1"

# 경로가 비어있지 않고 디렉토리인 경우에만 작업 수행
if [ -d "$target_dir" ]; then
  # 지정된 경로로 이동
  cd "$target_dir" || exit

  # 현재 디렉토리에 있는 .gguf 파일들을 확인
  for file in *.gguf; do
    # 파일명이 존재하는 경우에만 작업 수행
    if [ -f "$file" ]; then
      # 확장자를 제외한 파일명 추출
      dirname="${file%.gguf}"
      
      # 디렉토리가 존재하지 않으면 생성
      if [ ! -d "$dirname" ]; then
        mkdir "$dirname"
      fi
      
      # 파일을 해당 디렉토리로 이동
      mv "$file" "$dirname/"
    fi
  done

  echo "파일 이동 완료!"
else
  echo "지정된 경로가 유효하지 않습니다: $target_dir"
  exit 1
fi
