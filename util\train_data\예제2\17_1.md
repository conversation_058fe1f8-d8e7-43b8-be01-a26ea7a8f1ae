## 기능 : 지도 생성

**설명**: 이 기능은 기본 지도(Map)를 생성합니다.

**입력**:
- `containerId` (string): 지도가 렌더링될 HTML 요소의 ID. 
- `centerX` (Number): 지도의 중심 X 좌표.
- `centerY` (Number): 지도의 중심 Y 좌표.
- `zoom` (number): 초기 줌 레벨.
- `projection` (String) : 지도의 프로젝션 정보를 나타내는 코드입니다. 예를 들어:
    - `EPSG:4326` (WGS84)
    - `EPSG:3857` (Web Mercator)
    - `EPSG:5186` (Korea 2000 / Unified CS)
    - `EPSG:5179` (Korea 2000 / Korea Polyconic)

**출력**:
- 생성된 지도 객체(map).


**코드 예제**:
```javascript
var mapContainer = document.getElementById(containerId);
// 중심 좌표 정의
var coord = new odf.Coordinate(centerX, centerY);
var mapOption = {
    center:coord,
    zoom:zoom,
    projection:projection,
    //배경지도로 사용할 바로e맵 wmts 서비스 경로
    baroEMapURL: 'https://geon-gateway.geon.kr/map/api/map/baroemap',
    //배경지도로 사용할 바로e맵 항공 wmts 서비스 경로
    baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',
    //사용할 배경지도 정의
    basemap:{
        //바로e맵 서비스중, 기본지도와 항공지도 이용
        baroEMap:['eMapBasic','eMapAIR']
    },
};
//지도 객체 생성
var map = new odf.Map(mapContainer, mapOption);
```

## 기능 : 배경지도 컨트롤 생성

**설명**: 기본 지도를 생성한 후 배경지도 컨트롤(BasemapControl)을 추가합니다.

**입력**:
- `map` (Map): 지도 객체

**출력**:
- 생성된 배경지도 컨트롤 객체(basemapControl).


**코드 예제**:
```javascript
//배경지도 컨트롤 생성
var basemapControl = new odf.BasemapControl();
//생성한 배경지도 컨트롤을 지도 객체에 추가
basemapControl.setMap(map);
```
## 기능 : 줌 컨트롤 생성

**설명**: 기본 지도를 생성한 후 줌 컨트롤(ZoomControl)을 추가합니다.

**입력**:
- `map` (Map): 지도 객체

**출력**:
- 생성된 줌 컨트롤 객체(zoomControl).


**코드 예제**:
```javascript
//줌 컨트롤 생성
var zoomControl = new odf.ZoomControl();
//생성한 줌 컨트롤을 지도 객체에 추가
zoomControl.setMap(map);
```
## 기능 : 인덱스맵 컨트롤 생성

**설명**: 기본 지도를 생성한 후 인덱스맵(개요지도,오버뷰맵,overview map) 컨트롤(OverviewMapControl)을 추가합니다.

**입력**:
- `map` (Map): 지도 객체

**출력**:
- 생성된 인덱스맵 컨트롤 객체(overviewMapControl).


**코드 예제**:
```javascript
//인덱스맵 컨트롤 생성
var overviewMapControl = new odf.OverviewMapControl();
//생성한 인덱스맵 컨트롤을 지도 객체에 추가
overviewMapControl.setMap(map);
```

## 기능 : 축척 컨트롤 생성

**설명**: 기본 지도를 생성한 후 축척 컨트롤(ScaleControl)을 추가합니다.

**입력**:
- `map` (Map): 지도 객체

**출력**:
- 생성된 축척 컨트롤 객체(scaleControl).


**코드 예제**:
```javascript
//축척 컨트롤 생성
var scaleControl = new odf.ScaleControl();
//생성한 축척 컨트롤을 지도 객체에 추가
scaleControl.setMap(map);
```

## 기능 : 무브 컨트롤 생성

**설명**: 기본 지도를 생성한 후 무브(이동, 이전/다음) 컨트롤(MoveControl)을 추가합니다.

**입력**:
- `map` (Map): 지도 객체

**출력**:
- 생성된 무브 컨트롤 객체(moveControl).


**코드 예제**:
```javascript
//무브 컨트롤 생성
var moveControl = new odf.MoveControl();
//생성한 무브 컨트롤을 지도 객체에 추가
moveControl.setMap(map);
```
## 기능 : 마우스 좌표 표시 컨트롤 생성

**설명**: 기본 지도를 생성한 후 마우스 좌표 표시 컨트롤(MousePositionControl)을 추가합니다.

**입력**:
- `map` (Map): 지도 객체
- `showCoordinateTargetId` (Map): 지도 객체

**출력**:
- 생성된 마우스 좌표 표시 컨트롤 객체(mousePositionControl).


**코드 예제**:
```javascript
//마우스 좌표 표시 컨트롤 생성
var mousePositionControl  = new odf.MousePositionControl({
    //좌표값을 특정 element에 표시
    element: document.getElementById(showCoordinateTargetId),
    //좌표값을 callback의 매개변수로 넘김
    //callback: function (position) {
    // console.log(position);
    //},
});
//생성한 마우스 좌표 표시 컨트롤을 지도 객체에 추가
mousePositionControl.setMap(map);
```
## 기능 : 그리기 컨트롤 생성

**설명**: 기본 지도를 생성한 후 그리기 컨트롤(그리기 도구, DrawControl)을 추가합니다.

**입력**:
- `map` (Map): 지도 객체

**출력**:
- 생성된 그리기 컨트롤 객체(drawControl).


**코드 예제**:
```javascript
//그리기 컨트롤 생성
var drawControl = new odf.DrawControl();
//생성한 그리기 컨트롤을 지도 객체에 추가
drawControl.setMap(map);
```
## 기능 : 측정 컨트롤 생성

**설명**: 기본 지도를 생성한 후 측정 컨트롤(측정 도구, MeasureControl)을 추가합니다.

**입력**:
- `map` (Map): 지도 객체

**출력**:
- 생성된 측정 컨트롤 객체(measureControl).


**코드 예제**:
```javascript
//측정 컨트롤 생성
var measureControl = new odf.MeasureControl();
//생성한 측정 컨트롤을 지도 객체에 추가
measureControl.setMap(map);
```
## 기능 : 측정 컨트롤 생성

**설명**: 기본 지도를 생성한 후 초기화 컨트롤(ClearControl)을 추가합니다. 초기화 컨트롤은 그리기 컨트롤과 측정 컨트롤을 통해 생성된 도형과 오버레이를 지우는 기능을 합니다.

**입력**:
- `map` (Map): 지도 객체

**출력**:
- 생성된 초기화 컨트롤 객체(clearControl).


**코드 예제**:
```javascript
//측정 컨트롤 생성
var clearControl = new odf.ClearControl();
//생성한 측정 컨트롤을 지도 객체에 추가
clearControl.setMap(map);
```

## 기능 : 스와이퍼 컨트롤 생성

**설명**: 기본 지도를 생성한 후 스와이퍼 컨트롤(SwiperControl)을 추가합니다.

**입력**:
- `map` (Map): 지도 객체
- `basemapControl` (BasemapControl): 배경지도 컨트롤 객체
- `basemapKey` (String) 배경지도 키. 예를 들어:
    - `eMapBasic` 바로e맵 기본 지도
    - `eMapColor` 바로e맵 색각 지도
    - `eMapLowV` 바로e맵 큰 글씨 지도
    - `eMapWhite` 바로e맵 백지도
    - `eMapEnglish` 바로e맵 영어 지도
    - `eMapChinese` 바로e맵 중국어 지도
    - `eMapKorean` 바로e맵 한글 지도
    - `eMapWhiteEdu` 바로e맵 교육용 백지도
    - `eMapAIR` 바로e맵 항공(영상) 지도
    - `vWorldBase` vWorld 기본 지도
    - `vWorldWhite` vWorld 백지도
    - `vWorldMidnight` vWorld 야간 지도
    - `vWorldHybrid` vWorld 하이브리드 지도
    - `vWorldSatellite` vWorld 영상(항공) 지도
    - `kakaoBase` 카카오(다음) 기본 지도
    - `kakaoSkyview` 카카오(다음) 항공 지도
    - `OSM` OSM 기본 지도


**출력**:
- 생성된 스와이퍼 컨트롤 객체(swiperControl).


**코드 예제**:
```javascript
// 배경지도 컨트롤에서 바로e맵 항공지도 레이어 조회 
var rightLayer = basemapControl.getBaseLayer(basemapKey);
//스와이퍼 컨트롤 생성(원본 레이어를 왼쪽 영역에 표출, 정의한 레이어는 오른쪽 영역에 표출)
var swiperControl  = new odf.SwiperControl({
    layers : [rightLayer]
});
//생성한 스와이퍼 컨트롤을 지도 객체에 추가
swiperControl.setMap(map);
```
## 기능 : 분할지도 컨트롤 생성

**설명**: 기본 지도를 생성한 후 분할지도 컨트롤(DivideMapControl)을 추가합니다. 
바로e맵 색각지도, 백지도, 항공지도, 기본 지도를 활용하여 분할지도의 배경 지도를 설정합니다. 

**입력**:
- `map` (Map): 지도 객체

**출력**:
- 생성된 분할지도 컨트롤 객체(divideMapControl).


**코드 예제**:
```javascript
//분할지도 컨트롤 생성
var divideMapControl = new odf.DivideMapControl({
  //2분할지도 정의
  dualMap : [
    {
      mapOption : {
        basemap : {baroEMap : ['eMapAIR']},
      },
    },
  ],
  //3분할지도 정의
  threepleMap: [
    {mapOption: {
        basemap: {baroEMap:['eMapColor']},
      },
    },
    { mapOption: {
        basemap:{baroEMap: ['eMapWhite']},
      },
    },
  ],
  //4분할지도 정의
  quadMap: [
    {
      mapOption: {
        basemap: {baroEMap : ['eMapAIR']},
      },
    },
    {
      mapOption: {
        basemap: {baroEMap : ['eMapColor']} ,
      },
    },
    {
      mapOption: {
        basemap: {baroEMap : ['eMapWhite']} ,
      },
    },
  ],
});
//생성한 분할지도 컨트롤을 지도 객체에 추가
divideMapControl.setMap(map);
```
## 기능 : 다운로드 컨트롤 생성

**설명**: 기본 지도를 생성한 후 다운로드 컨트롤(DrawControl)을 추가합니다. 다운로드 컨트롤은 화면상 보이는 지도를 JPG 또는 PNG 파일로 다운로드하는 기능을 합니다.

**입력**:
- `map` (Map): 지도 객체

**출력**:
- 생성된 다운로드 컨트롤 객체(downloadControl).


**코드 예제**:
```javascript
//다운로드 컨트롤 생성
var downloadControl = new odf.DownloadControl();
//생성한 다운로드 컨트롤을 지도 객체에 추가
downloadControl.setMap(map);
```
## 기능 : 프린트 컨트롤 생성

**설명**: 기본 지도를 생성한 후 프린트 컨트롤(PrintControl)을 추가합니다.

**입력**:
- `map` (Map): 지도 객체

**출력**:
- 생성된 프린트 컨트롤 객체(printControl).


**코드 예제**:
```javascript
//프린트 컨트롤 생성
var printControl = new odf.PrintControl();
//생성한 프린트 컨트롤을 지도 객체에 추가
printControl.setMap(map);
```
## 기능 : 프린트 컨트롤 생성

**설명**: 기본 지도를 생성한 후 배경지도/줌/인덱스맵/축척/무브/마우스 좌표 표시/그리기/측정/초기화/스와이퍼/분할지도/다운로드/출력 컨트롤을 추가합니다.

**입력**:
- `containerId` (string): 지도가 렌더링될 HTML 요소의 ID.
- `centerX` (Number): 지도의 중심 X 좌표.
- `centerY` (Number): 지도의 중심 Y 좌표.
- `zoom` (number): 초기 줌 레벨.
- `projection` (String) : 지도의 프로젝션 정보를 나타내는 코드입니다. 예를 들어:
    - `EPSG:4326` WGS84
    - `EPSG:3857` Web Mercator
    - `EPSG:5186` Korea 2000 / Unified CS
    - `EPSG:5179` Korea 2000 / Korea Polyconic
- `showCoordinateTargetId` (Map): 지도 객체
- `basemapKey` (String) 배경지도 키. 예를 들어:
    - `eMapBasic` 바로e맵 기본 지도
    - `eMapColor` 바로e맵 색각 지도
    - `eMapLowV` 바로e맵 큰 글씨 지도
    - `eMapWhite` 바로e맵 백지도
    - `eMapEnglish` 바로e맵 영어 지도
    - `eMapChinese` 바로e맵 중국어 지도
    - `eMapKorean` 바로e맵 한글 지도
    - `eMapWhiteEdu` 바로e맵 교육용 백지도
    - `eMapAIR` 바로e맵 항공(영상) 지도
    - `vWorldBase` vWorld 기본 지도
    - `vWorldWhite` vWorld 백지도
    - `vWorldMidnight` vWorld 야간 지도
    - `vWorldHybrid` vWorld 하이브리드 지도
    - `vWorldSatellite` vWorld 영상(항공) 지도
    - `kakaoBase` 카카오(다음) 기본 지도
    - `kakaoSkyview` 카카오(다음) 항공 지도
    - `OSM` OSM 기본 지도

**출력**:
- 배경지도/줌/인덱스맵/축척/무브/마우스 좌표 표시/그리기/측정/초기화/스와이퍼/분할지도/다운로드/출력 컨트롤이 추가된 지도 객체
- 배경지도/줌/인덱스맵/축척/무브/마우스 좌표 표시/그리기/측정/초기화/스와이퍼/분할지도/다운로드/출력 컨트롤 객체.


**코드 예제**:
```javascript
var mapContainer = document.getElementById(containerId);
// 중심 좌표 정의
var coord = new odf.Coordinate(centerX, centerY);
var mapOption = {
    center:coord,
    zoom:zoom,
    projection:projection,
    //배경지도로 사용할 바로e맵 wmts 서비스 경로
    baroEMapURL: 'https://geon-gateway.geon.kr/map/api/map/baroemap',
    //배경지도로 사용할 바로e맵 항공 wmts 서비스 경로
    baroEMapAirURL : 'https://geon-gateway.geon.kr/map/api/map/ngisair',
    //사용할 배경지도 정의
    basemap:{
        //바로e맵 서비스중, 기본지도와 항공지도 이용
        baroEMap:['eMapBasic','eMapAIR']
    },
};
//지도 객체 생성
var map = new odf.Map(mapContainer, mapOption);


//배경지도 컨트롤 생성
var basemapControl = new odf.BasemapControl();
//생성한 배경지도 컨트롤을 지도 객체에 추가
basemapControl.setMap(map);


//줌 컨트롤 생성
var zoomControl = new odf.ZoomControl();
//생성한 줌 컨트롤을 지도 객체에 추가
zoomControl.setMap(map);


//인덱스맵 컨트롤 생성
var overviewMapControl = new odf.OverviewMapControl();
//생성한 인덱스맵 컨트롤을 지도 객체에 추가
overviewMapControl.setMap(map);


//축척 컨트롤 생성
var scaleControl = new odf.ScaleControl();
//생성한 축척 컨트롤을 지도 객체에 추가
scaleControl.setMap(map);


//무브 컨트롤 생성
var moveControl = new odf.MoveControl();
//생성한 무브 컨트롤을 지도 객체에 추가
moveControl.setMap(map);


//마우스 좌표 표시 컨트롤 생성
var mousePositionControl  = new odf.MousePositionControl({
    //좌표값을 특정 element에 표시
    element: document.getElementById(showCoordinateTargetId),
    //좌표값을 callback의 매개변수로 넘김
    //callback: function (position) {
    // console.log(position);
    //},
});
//생성한 마우스 좌표 표시 컨트롤을 지도 객체에 추가
mousePositionControl.setMap(map);


//그리기 컨트롤 생성
var drawControl = new odf.DrawControl();
//생성한 그리기 컨트롤을 지도 객체에 추가
drawControl.setMap(map);


//측정 컨트롤 생성
var measureControl = new odf.MeasureControl();
//생성한 측정 컨트롤을 지도 객체에 추가
measureControl.setMap(map);


//분할지도 컨트롤 생성
var divideMapControl = new odf.DivideMapControl({
    //2분할지도 정의
    dualMap : [
        {
            mapOption : {
                basemap : {baroEMap : ['eMapAIR']},
            },
        },
    ],
    //3분할지도 정의
    threepleMap: [
        {mapOption: {
                basemap: {baroEMap:['eMapColor']},
            },
        },
        { mapOption: {
                basemap:{baroEMap: ['eMapWhite']},
            },
        },
    ],
    //4분할지도 정의
    quadMap: [
        {
            mapOption: {
                basemap: {baroEMap : ['eMapAIR']},
            },
        },
        {
            mapOption: {
                basemap: {baroEMap : ['eMapColor']} ,
            },
        },
        {
            mapOption: {
                basemap: {baroEMap : ['eMapWhite']} ,
            },
        },
    ],
});
//생성한 분할지도 컨트롤을 지도 객체에 추가
divideMapControl.setMap(map);


//다운로드 컨트롤 생성
var downloadControl = new odf.DownloadControl();
//생성한 다운로드 컨트롤을 지도 객체에 추가
downloadControl.setMap(map);


//프린트 컨트롤 생성
var printControl = new odf.PrintControl();
//생성한 프린트 컨트롤을 지도 객체에 추가
printControl.setMap(map);
```
