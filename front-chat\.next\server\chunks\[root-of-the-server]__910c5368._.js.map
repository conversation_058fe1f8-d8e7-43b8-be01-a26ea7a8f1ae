{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/api/layer/detailed-info/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\n\ninterface LayerColumnInfo {\n  lyrId: string;\n  columnOrdr: number;\n  columnNm: string;\n  columnNcm: string;\n  dataTy: string;\n  indictAt: string;\n  editPosblAt: string;\n  esntlAt: string;\n  mummLt?: number;\n  mxmmLt?: number;\n  cmmnGroupCode: string | null;\n  cmmnGroupCodeNm: string | null;\n  useAt: string;\n  registerId: string;\n  registDt: string;\n  updusrId: string;\n  updtDt: string;\n}\n\ninterface LayerColumnResponse {\n  code: number;\n  message: string;\n  result: LayerColumnInfo[];\n}\n\ninterface LayerAttributesResponse {\n  code: number;\n  message: string;\n  result: {\n    pageInfo: {\n      pageSize: number;\n      pageIndex: number;\n      totalCount: number;\n    };\n    features: Array<{\n      properties: Record<string, any>;\n    }>;\n    pkColumnName: string;\n  };\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { userId, lyrId, namespace, cntntsId, pageSize = 50, pageIndex = 1 } = await request.json();\n\n    const apiKey = process.env.GEON_API_KEY;\n    const apiBaseUrl = process.env.GEON_API_BASE_URL;\n    \n    if (!apiKey || !apiBaseUrl) {\n      return NextResponse.json(\n        { error: \"API 키 또는 기본 URL이 설정되지 않았습니다.\" },\n        { status: 500 }\n      );\n    }\n\n    // 1. 컬럼 정보 조회\n    const columnResponse = await fetch(\n      `${apiBaseUrl}/builder/layer/column/select?crtfckey=${apiKey}&lyrId=${lyrId}&userId=${userId}`,\n      {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          crtfckey: apiKey,\n        },\n      }\n    );\n\n    if (!columnResponse.ok) {\n      throw new Error(\n        `컬럼 정보 조회 실패: ${columnResponse.status} ${columnResponse.statusText}`\n      );\n    }\n\n    const columnData: LayerColumnResponse = await columnResponse.json();\n\n    // 2. 속성 데이터 조회\n    const dataResponse = await fetch(\n      `${apiBaseUrl}/builder/layer/attributes/select?crtfckey=${apiKey}`,\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          crtfckey: apiKey,\n        },\n        body: JSON.stringify({\n          typeName: `${namespace}:${cntntsId}`,\n          pageIndex: pageIndex,\n          pageSize: pageSize,\n        }),\n      }\n    );\n\n    if (!dataResponse.ok) {\n      throw new Error(\n        `속성 데이터 조회 실패: ${dataResponse.status} ${dataResponse.statusText}`\n      );\n    }\n\n    const attributeData: LayerAttributesResponse = await dataResponse.json();\n\n    if (\n      !columnData.code ||\n      columnData.code !== 200 ||\n      !attributeData.code ||\n      attributeData.code !== 200\n    ) {\n      return NextResponse.json(\n        { error: \"레이어 정보를 찾을 수 없습니다.\" },\n        { status: 404 }\n      );\n    }\n\n    // 컬럼 정보와 속성 데이터를 함께 반환\n    const result = {\n      columns: columnData.result.map((col) => ({\n        name: col.columnNm,\n        description: col.columnNcm,\n        type: col.dataTy,\n        editable: col.editPosblAt === \"Y\",\n        required: col.esntlAt === \"Y\",\n        minValue: col.mummLt,\n        maxValue: col.mxmmLt,\n        groupCode: col.cmmnGroupCode,\n        groupName: col.cmmnGroupCodeNm,\n        order: col.columnOrdr,\n        indicator: col.indictAt === \"Y\",\n      })),\n      data: attributeData.result.features.map(\n        (feature) => feature.properties\n      ),\n      totalCount: attributeData.result.pageInfo.totalCount,\n      pageInfo: attributeData.result.pageInfo,\n      pkColumnName: attributeData.result.pkColumnName,\n    };\n\n    return NextResponse.json(result);\n  } catch (error: any) {\n    console.error(\"Error fetching layer detailed information:\", error);\n    return NextResponse.json(\n      { error: `레이어 상세 정보 조회 중 오류가 발생했습니다: ${error.message}` },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AA4CO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,EAAE,YAAY,CAAC,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE/F,MAAM,SAAS,QAAQ,GAAG,CAAC,YAAY;QACvC,MAAM,aAAa,QAAQ,GAAG,CAAC,iBAAiB;QAEhD,IAAI,CAAC,UAAU,CAAC,YAAY;YAC1B,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+B,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,cAAc;QACd,MAAM,iBAAiB,MAAM,MAC3B,GAAG,WAAW,sCAAsC,EAAE,OAAO,OAAO,EAAE,MAAM,QAAQ,EAAE,QAAQ,EAC9F;YACE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;QACF;QAGF,IAAI,CAAC,eAAe,EAAE,EAAE;YACtB,MAAM,IAAI,MACR,CAAC,aAAa,EAAE,eAAe,MAAM,CAAC,CAAC,EAAE,eAAe,UAAU,EAAE;QAExE;QAEA,MAAM,aAAkC,MAAM,eAAe,IAAI;QAEjE,eAAe;QACf,MAAM,eAAe,MAAM,MACzB,GAAG,WAAW,0CAA0C,EAAE,QAAQ,EAClE;YACE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,UAAU,GAAG,UAAU,CAAC,EAAE,UAAU;gBACpC,WAAW;gBACX,UAAU;YACZ;QACF;QAGF,IAAI,CAAC,aAAa,EAAE,EAAE;YACpB,MAAM,IAAI,MACR,CAAC,cAAc,EAAE,aAAa,MAAM,CAAC,CAAC,EAAE,aAAa,UAAU,EAAE;QAErE;QAEA,MAAM,gBAAyC,MAAM,aAAa,IAAI;QAEtE,IACE,CAAC,WAAW,IAAI,IAChB,WAAW,IAAI,KAAK,OACpB,CAAC,cAAc,IAAI,IACnB,cAAc,IAAI,KAAK,KACvB;YACA,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqB,GAC9B;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,SAAS;YACb,SAAS,WAAW,MAAM,CAAC,GAAG,CAAC,CAAC,MAAQ,CAAC;oBACvC,MAAM,IAAI,QAAQ;oBAClB,aAAa,IAAI,SAAS;oBAC1B,MAAM,IAAI,MAAM;oBAChB,UAAU,IAAI,WAAW,KAAK;oBAC9B,UAAU,IAAI,OAAO,KAAK;oBAC1B,UAAU,IAAI,MAAM;oBACpB,UAAU,IAAI,MAAM;oBACpB,WAAW,IAAI,aAAa;oBAC5B,WAAW,IAAI,eAAe;oBAC9B,OAAO,IAAI,UAAU;oBACrB,WAAW,IAAI,QAAQ,KAAK;gBAC9B,CAAC;YACD,MAAM,cAAc,MAAM,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,UAAY,QAAQ,UAAU;YAEjC,YAAY,cAAc,MAAM,CAAC,QAAQ,CAAC,UAAU;YACpD,UAAU,cAAc,MAAM,CAAC,QAAQ;YACvC,cAAc,cAAc,MAAM,CAAC,YAAY;QACjD;QAEA,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,CAAC,2BAA2B,EAAE,MAAM,OAAO,EAAE;QAAC,GACvD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}