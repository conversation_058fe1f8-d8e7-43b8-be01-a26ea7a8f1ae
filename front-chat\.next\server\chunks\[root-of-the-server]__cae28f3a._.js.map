{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/api-config.ts"], "sourcesContent": ["/**\n * API 설정 및 인증 정보를 관리하는 유틸리티\n */\n\nexport interface ApiConfig {\n  baseUrl: string;\n  headers: {\n    crtfckey: string;\n  };\n  auth: {\n    userId: string;\n    password: string;\n  };\n}\n\n/**\n * API 요청을 위한 설정을 반환합니다.\n * 프론트엔드 로그인과 별개로 백엔드 API 요청 시에는 항상 geonuser 계정을 사용합니다.\n */\nexport const getApiConfig = (): ApiConfig => {\n  const baseUrl =\n    process.env.GEON_API_BASE_URL || \"http://121.163.19.101:14090\";\n\n  // MCP 서버 자체 API 키 사용 (클라이언트 토큰과 별개)\n  const apiKey = process.env.GEON_API_KEY;\n  if (!apiKey) {\n    console.warn(\"GEON_API_KEY가 설정되지 않았습니다.\");\n  }\n\n  // 백엔드 API 요청용 계정 정보 (환경변수에서 가져오거나 기본값 사용)\n  const apiUserId = process.env.GEON_API_USER_ID || 'geonuser';\n  const apiUserPassword = process.env.GEON_API_USER_PASSWORD || 'wavus1234!';\n\n  return {\n    baseUrl,\n    headers: {\n      crtfckey: apiKey || \"\",\n    },\n    auth: {\n      userId: apiUserId,\n      password: apiUserPassword,\n    },\n  };\n};\n\n/**\n * API 요청 시 사용할 URLSearchParams에 인증 정보를 추가합니다.\n */\nexport const addAuthToParams = (params: URLSearchParams, config?: ApiConfig): URLSearchParams => {\n  const apiConfig = config || getApiConfig();\n  \n  // API 키 추가\n  if (apiConfig.headers.crtfckey) {\n    params.append(\"crtfckey\", apiConfig.headers.crtfckey);\n  }\n  \n  return params;\n};\n\n/**\n * API 요청 시 사용할 헤더를 반환합니다.\n */\nexport const getApiHeaders = (config?: ApiConfig): Record<string, string> => {\n  const apiConfig = config || getApiConfig();\n  \n  return {\n    \"Content-Type\": \"application/json\",\n    ...apiConfig.headers,\n  };\n};\n\n/**\n * 백엔드 API 요청에서 사용할 userId를 반환합니다.\n * 프론트엔드 로그인 계정과 관계없이 항상 geonuser를 사용합니다.\n */\nexport const getApiUserId = (config?: ApiConfig): string => {\n  const apiConfig = config || getApiConfig();\n  return apiConfig.auth.userId;\n};\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;AAiBM,MAAM,eAAe;IAC1B,MAAM,UACJ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;IAEnC,oCAAoC;IACpC,MAAM,SAAS,QAAQ,GAAG,CAAC,YAAY;IACvC,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;IACf;IAEA,0CAA0C;IAC1C,MAAM,YAAY,QAAQ,GAAG,CAAC,gBAAgB,IAAI;IAClD,MAAM,kBAAkB,QAAQ,GAAG,CAAC,sBAAsB,IAAI;IAE9D,OAAO;QACL;QACA,SAAS;YACP,UAAU,UAAU;QACtB;QACA,MAAM;YACJ,QAAQ;YACR,UAAU;QACZ;IACF;AACF;AAKO,MAAM,kBAAkB,CAAC,QAAyB;IACvD,MAAM,YAAY,UAAU;IAE5B,WAAW;IACX,IAAI,UAAU,OAAO,CAAC,QAAQ,EAAE;QAC9B,OAAO,MAAM,CAAC,YAAY,UAAU,OAAO,CAAC,QAAQ;IACtD;IAEA,OAAO;AACT;AAKO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,YAAY,UAAU;IAE5B,OAAO;QACL,gBAAgB;QAChB,GAAG,UAAU,OAAO;IACtB;AACF;AAMO,MAAM,eAAe,CAAC;IAC3B,MAAM,YAAY,UAAU;IAC5B,OAAO,UAAU,IAAI,CAAC,MAAM;AAC9B", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/api/layers/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { getApiConfig, addAuthToParams, getApiHeaders, getApiUserId } from \"@/lib/api-config\";\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const config = getApiConfig();\n\n    // URL 파라미터 구성\n    const params = new URLSearchParams();\n\n    // 필수 파라미터 - 항상 geonuser 계정 사용 (프론트엔드 로그인 계정과 무관)\n    params.append(\"userId\", getApiUserId(config));\n    params.append(\"holdDataSeCode\", searchParams.get(\"holdDataSeCode\") || \"0\");\n    params.append(\"pageIndex\", searchParams.get(\"pageIndex\") || \"1\");\n    params.append(\"pageSize\", searchParams.get(\"pageSize\") || \"10\");\n\n    // 선택적 파라미터\n    const searchTxt = searchParams.get(\"searchTxt\");\n    if (searchTxt && searchTxt.trim() !== \"\") {\n      params.append(\"searchTxt\", searchTxt.trim());\n    }\n\n    const lyrTySeCode = searchParams.get(\"lyrTySeCode\");\n    if (lyrTySeCode && lyrTySeCode.trim() !== \"\") {\n      params.append(\"lyrTySeCode\", lyrTySeCode.trim());\n    }\n\n    // 인증 정보 추가\n    addAuthToParams(params, config);\n\n    // 외부 API 호출\n    const response = await fetch(\n      `${config.baseUrl}/smt/layer/info/list?${params.toString()}`,\n      {\n        method: \"GET\",\n        headers: getApiHeaders(config),\n      }\n    );\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      console.error(\"API request failed with data\", errorData);\n      return NextResponse.json(\n        { error: `API request failed with status ${response.status}` },\n        { status: response.status }\n      );\n    }\n\n    const data = await response.json();\n\n    if (!data || !data.result) {\n      return NextResponse.json(\n        { error: \"레이어 목록 조회 실패: 응답 데이터가 없습니다.\" },\n        { status: 500 }\n      );\n    }\n\n    return NextResponse.json(data);\n  } catch (error: any) {\n    console.error(\"Layer list API error:\", error);\n    return NextResponse.json(\n      { error: `레이어 목록 조회 실패: ${error.message}` },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;QAE1B,cAAc;QACd,MAAM,SAAS,IAAI;QAEnB,iDAAiD;QACjD,OAAO,MAAM,CAAC,UAAU,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;QACrC,OAAO,MAAM,CAAC,kBAAkB,aAAa,GAAG,CAAC,qBAAqB;QACtE,OAAO,MAAM,CAAC,aAAa,aAAa,GAAG,CAAC,gBAAgB;QAC5D,OAAO,MAAM,CAAC,YAAY,aAAa,GAAG,CAAC,eAAe;QAE1D,WAAW;QACX,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,IAAI,aAAa,UAAU,IAAI,OAAO,IAAI;YACxC,OAAO,MAAM,CAAC,aAAa,UAAU,IAAI;QAC3C;QAEA,MAAM,cAAc,aAAa,GAAG,CAAC;QACrC,IAAI,eAAe,YAAY,IAAI,OAAO,IAAI;YAC5C,OAAO,MAAM,CAAC,eAAe,YAAY,IAAI;QAC/C;QAEA,WAAW;QACX,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ;QAExB,YAAY;QACZ,MAAM,WAAW,MAAM,MACrB,GAAG,OAAO,OAAO,CAAC,qBAAqB,EAAE,OAAO,QAAQ,IAAI,EAC5D;YACE,QAAQ;YACR,SAAS,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE;QACzB;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,CAAC,+BAA+B,EAAE,SAAS,MAAM,EAAE;YAAC,GAC7D;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAM,EAAE;YACzB,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,CAAC,cAAc,EAAE,MAAM,OAAO,EAAE;QAAC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}