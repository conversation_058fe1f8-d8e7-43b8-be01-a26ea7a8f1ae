(()=>{var e={};e.id=72,e.ids=[72],e.modules={1635:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ImageResponse:function(){return n.ImageResponse},NextRequest:function(){return i.NextRequest},NextResponse:function(){return a.NextResponse},URLPattern:function(){return s.URLPattern},after:function(){return c.after},connection:function(){return u.connection},unstable_rootParams:function(){return l.unstable_rootParams},userAgent:function(){return o.userAgent},userAgentFromString:function(){return o.userAgentFromString}});let n=r(99882),i=r(95054),a=r(27738),o=r(38762),s=r(43875),c=r(37791),u=r(44502),l=r(64493)},3219:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return d},APP_DIR_ALIAS:function(){return C},CACHE_ONE_YEAR:function(){return k},DOT_NEXT_ALIAS:function(){return T},ESLINT_DEFAULT_DIRS:function(){return X},GSP_NO_RETURNED_VALUE:function(){return B},GSSP_COMPONENT_MEMBER_ERROR:function(){return q},GSSP_NO_RETURNED_VALUE:function(){return F},INFINITE_CACHE:function(){return S},INSTRUMENTATION_HOOK_FILENAME:function(){return P},MATCHED_PATH_HEADER:function(){return i},MIDDLEWARE_FILENAME:function(){return A},MIDDLEWARE_LOCATION_REGEXP:function(){return x},NEXT_BODY_SUFFIX:function(){return h},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return E},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return y},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return b},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return v},NEXT_CACHE_TAGS_HEADER:function(){return m},NEXT_CACHE_TAG_MAX_ITEMS:function(){return w},NEXT_CACHE_TAG_MAX_LENGTH:function(){return _},NEXT_DATA_SUFFIX:function(){return p},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return f},NEXT_QUERY_PARAM_PREFIX:function(){return r},NEXT_RESUME_HEADER:function(){return g},NON_STANDARD_NODE_ENV:function(){return G},PAGES_DIR_ALIAS:function(){return R},PRERENDER_REVALIDATE_HEADER:function(){return a},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return o},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return D},ROOT_DIR_ALIAS:function(){return O},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return $},RSC_ACTION_ENCRYPTION_ALIAS:function(){return H},RSC_ACTION_PROXY_ALIAS:function(){return I},RSC_ACTION_VALIDATE_ALIAS:function(){return U},RSC_CACHE_WRAPPER_ALIAS:function(){return N},RSC_MOD_REF_PROXY_ALIAS:function(){return j},RSC_PREFETCH_SUFFIX:function(){return s},RSC_SEGMENTS_DIR_SUFFIX:function(){return c},RSC_SEGMENT_SUFFIX:function(){return u},RSC_SUFFIX:function(){return l},SERVER_PROPS_EXPORT_ERROR:function(){return J},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return M},SERVER_PROPS_SSG_CONFLICT:function(){return W},SERVER_RUNTIME:function(){return Y},SSG_FALLBACK_EXPORT_ERROR:function(){return V},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return L},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return K},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return z},WEBPACK_LAYERS:function(){return Q},WEBPACK_RESOURCE_QUERIES:function(){return ee}});let r="nxtP",n="nxtI",i="x-matched-path",a="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",s=".prefetch.rsc",c=".segments",u=".segment.rsc",l=".rsc",d=".action",p=".json",f=".meta",h=".body",m="x-next-cache-tags",y="x-next-revalidated-tags",b="x-next-revalidate-tag-token",g="next-resume",w=128,_=256,v=1024,E="_N_T_",k=31536e3,S=0xfffffffe,A="middleware",x=`(?:src/)?${A}`,P="instrumentation",R="private-next-pages",T="private-dot-next",O="private-next-root-dir",C="private-next-app-dir",j="next/dist/build/webpack/loaders/next-flight-loader/module-proxy",U="private-next-rsc-action-validate",I="private-next-rsc-server-reference",N="private-next-rsc-cache-wrapper",H="private-next-rsc-action-encryption",$="private-next-rsc-action-client-wrapper",D="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",L="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",M="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",W="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",K="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",J="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",B="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",F="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",z="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",q="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",G='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',V="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",X=["app","pages","components","lib","src"],Y={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},Z={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"},Q={...Z,GROUP:{builtinReact:[Z.reactServerComponents,Z.actionBrowser],serverOnly:[Z.reactServerComponents,Z.actionBrowser,Z.instrument,Z.middleware],neutralTarget:[Z.apiNode,Z.apiEdge],clientOnly:[Z.serverSideRendering,Z.appPagesBrowser],bundled:[Z.reactServerComponents,Z.actionBrowser,Z.serverSideRendering,Z.appPagesBrowser,Z.shared,Z.instrument,Z.middleware],appPages:[Z.reactServerComponents,Z.serverSideRendering,Z.appPagesBrowser,Z.actionBrowser]}},ee={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7654:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextURL",{enumerable:!0,get:function(){return l}});let n=r(90896),i=r(28093),a=r(66684),o=r(28846),s=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function c(e,t){return new URL(String(e).replace(s,"localhost"),t&&String(t).replace(s,"localhost"))}let u=Symbol("NextURLInternal");class l{constructor(e,t,r){let n,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,i=r||{}):i=r||t||{},this[u]={url:c(e,n??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,r,i,s;let c=(0,o.getNextPathnameInfo)(this[u].url.pathname,{nextConfig:this[u].options.nextConfig,parseData:!0,i18nProvider:this[u].options.i18nProvider}),l=(0,a.getHostname)(this[u].url,this[u].options.headers);this[u].domainLocale=this[u].options.i18nProvider?this[u].options.i18nProvider.detectDomainLocale(l):(0,n.detectDomainLocale)(null==(t=this[u].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,l);let d=(null==(r=this[u].domainLocale)?void 0:r.defaultLocale)||(null==(s=this[u].options.nextConfig)||null==(i=s.i18n)?void 0:i.defaultLocale);this[u].url.pathname=c.pathname,this[u].defaultLocale=d,this[u].basePath=c.basePath??"",this[u].buildId=c.buildId,this[u].locale=c.locale??d,this[u].trailingSlash=c.trailingSlash}formatPathname(){return(0,i.formatNextPathnameInfo)({basePath:this[u].basePath,buildId:this[u].buildId,defaultLocale:this[u].options.forceLocale?void 0:this[u].defaultLocale,locale:this[u].locale,pathname:this[u].url.pathname,trailingSlash:this[u].trailingSlash})}formatSearch(){return this[u].url.search}get buildId(){return this[u].buildId}set buildId(e){this[u].buildId=e}get locale(){return this[u].locale??""}set locale(e){var t,r;if(!this[u].locale||!(null==(r=this[u].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[u].locale=e}get defaultLocale(){return this[u].defaultLocale}get domainLocale(){return this[u].domainLocale}get searchParams(){return this[u].url.searchParams}get host(){return this[u].url.host}set host(e){this[u].url.host=e}get hostname(){return this[u].url.hostname}set hostname(e){this[u].url.hostname=e}get port(){return this[u].url.port}set port(e){this[u].url.port=e}get protocol(){return this[u].url.protocol}set protocol(e){this[u].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[u].url=c(e),this.analyze()}get origin(){return this[u].url.origin}get pathname(){return this[u].url.pathname}set pathname(e){this[u].url.pathname=e}get hash(){return this[u].url.hash}set hash(e){this[u].url.hash=e}get search(){return this[u].url.search}set search(e){this[u].url.search=e}get password(){return this[u].url.password}set password(e){this[u].url.password=e}get username(){return this[u].url.username}set username(e){this[u].url.username=e}get basePath(){return this[u].basePath}set basePath(e){this[u].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new l(String(this),this[u].options)}}},8009:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DISALLOWED_FORM_PROPS:function(){return r},checkFormActionUrl:function(){return i},createFormSubmitDestinationUrl:function(){return n},hasReactClientActionAttributes:function(){return u},hasUnsupportedSubmitterAttributes:function(){return c},isSupportedFormEncType:function(){return a},isSupportedFormMethod:function(){return o},isSupportedFormTarget:function(){return s}});let r=["method","encType","target"];function n(e,t){let r;try{let t=window.location.href;r=new URL(e,t)}catch(t){throw Object.defineProperty(Error('Cannot parse form action "'+e+'" as a URL',{cause:t}),"__NEXT_ERROR_CODE",{value:"E152",enumerable:!1,configurable:!0})}for(let[e,n]of(r.searchParams.size&&(r.search=""),new FormData(t)))"string"!=typeof n&&(n=n.name),r.searchParams.append(e,n);return r}function i(e,t){let r,n="action"===t?"an `action`":"a `formAction`";try{r=new URL(e,"http://n")}catch(t){console.error("<Form> received "+n+' that cannot be parsed as a URL: "'+e+'".');return}r.searchParams.size&&console.warn("<Form> received "+n+' that contains search params: "'+e+'". This is not supported, and they will be ignored. If you need to pass in additional search params, use an `<input type="hidden" />` instead.')}let a=e=>"application/x-www-form-urlencoded"===e,o=e=>"get"===e,s=e=>"_self"===e;function c(e){let t=e.getAttribute("formEncType");if(null!==t&&!a(t))return!0;let r=e.getAttribute("formMethod");if(null!==r&&!o(r))return!0;let n=e.getAttribute("formTarget");return!(null===n||s(n))}function u(e){let t=e.getAttribute("formAction");return t&&/\s*javascript:/i.test(t)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9066:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return l},RedirectType:function(){return i.RedirectType},forbidden:function(){return o.forbidden},notFound:function(){return a.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return s.unauthorized},unstable_rethrow:function(){return c.unstable_rethrow}});let n=r(98171),i=r(54114),a=r(85941),o=r(32018),s=r(49953),c=r(53833);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class l extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12541:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathSuffix",{enumerable:!0,get:function(){return i}});let n=r(67157);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:a}=(0,n.parsePath)(e);return""+r+t+i+a}},16700:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16939:(e,t,r)=>{"use strict";let n,i,a,o,s,c,u;r.r(t),r.d(t,{"7f992a250a06ce14f46b235bd7731d78ab71c40336":()=>oA});var l={};r.r(l),r.d(l,{q:()=>tG,l:()=>tY});var d=r(24304);r(75566);var p=r(44722),f=function(e,t,r,n,i){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?i.call(e,r):i?i.value=r:t.set(e,r),r},h=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};function m(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class y{constructor(e,t,r){if(rE.add(this),rk.set(this,{}),rS.set(this,void 0),rA.set(this,void 0),f(this,rA,r,"f"),f(this,rS,e,"f"),!t)return;let{name:n}=e;for(let[e,r]of Object.entries(t))e.startsWith(n)&&r&&(h(this,rk,"f")[e]=r)}get value(){return Object.keys(h(this,rk,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>h(this,rk,"f")[e]).join("")}chunk(e,t){let r=h(this,rE,"m",rP).call(this);for(let n of h(this,rE,"m",rx).call(this,{name:h(this,rS,"f").name,value:e,options:{...h(this,rS,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(h(this,rE,"m",rP).call(this))}}rk=new WeakMap,rS=new WeakMap,rA=new WeakMap,rE=new WeakSet,rx=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return h(this,rk,"f")[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,i=e.value.substr(3936*n,3936);r.push({...e,name:t,value:i}),h(this,rk,"f")[t]=i}return h(this,rA,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},rP=function(){let e={};for(let t in h(this,rk,"f"))delete h(this,rk,"f")?.[t],e[t]={name:t,value:"",options:{...h(this,rS,"f").options,maxAge:0}};return e};class b extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class g extends b{}g.kind="signIn";class w extends b{}w.type="AdapterError";class _ extends b{}_.type="AccessDenied";class v extends b{}v.type="CallbackRouteError";class E extends b{}E.type="ErrorPageLoop";class k extends b{}k.type="EventError";class S extends b{}S.type="InvalidCallbackUrl";class A extends g{constructor(){super(...arguments),this.code="credentials"}}A.type="CredentialsSignin";class x extends b{}x.type="InvalidEndpoints";class P extends b{}P.type="InvalidCheck";class R extends b{}R.type="JWTSessionError";class T extends b{}T.type="MissingAdapter";class O extends b{}O.type="MissingAdapterMethods";class C extends b{}C.type="MissingAuthorize";class j extends b{}j.type="MissingSecret";class U extends g{}U.type="OAuthAccountNotLinked";class I extends g{}I.type="OAuthCallbackError";class N extends b{}N.type="OAuthProfileParseError";class H extends b{}H.type="SessionTokenError";class $ extends g{}$.type="OAuthSignInError";class D extends g{}D.type="EmailSignInError";class L extends b{}L.type="SignOutError";class M extends b{}M.type="UnknownAction";class W extends b{}W.type="UnsupportedStrategy";class K extends b{}K.type="InvalidProvider";class J extends b{}J.type="UntrustedHost";class B extends b{}B.type="Verification";class F extends g{}F.type="MissingCSRF";let z=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class q extends b{}q.type="DuplicateConditionalUI";class G extends b{}G.type="MissingWebAuthnAutocomplete";class V extends b{}V.type="WebAuthnVerificationError";class X extends g{}X.type="AccountNotLinked";class Y extends b{}Y.type="ExperimentalFeatureNotEnabled";let Z=!1;function Q(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let ee=!1,et=!1,er=!1,en=["createVerificationToken","useVerificationToken","getUserByEmail"],ei=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],ea=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];var eo=r(55511);let es=(e,t,r,n,i)=>{let a=parseInt(e.substr(3),10)>>3||20,o=(0,eo.createHmac)(e,r.byteLength?r:new Uint8Array(a)).update(t).digest(),s=Math.ceil(i/a),c=new Uint8Array(a*s+n.byteLength+1),u=0,l=0;for(let t=1;t<=s;t++)c.set(n,l),c[l+n.byteLength]=t,c.set((0,eo.createHmac)(e,o).update(c.subarray(u,l+n.byteLength+1)).digest(),l),u=l,l+=a;return c.slice(0,i)};"function"!=typeof eo.hkdf||process.versions.electron||(n=async(...e)=>new Promise((t,r)=>{eo.hkdf(...e,(e,n)=>{e?r(e):t(new Uint8Array(n))})}));let ec=async(e,t,r,i,a)=>(n||es)(e,t,r,i,a);function eu(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function el(e,t,r,n,i){return ec(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=eu(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),eu(r,"salt"),function(e){let t=eu(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(i,e))}let ed=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(r,t))},ep=new TextEncoder,ef=new TextDecoder;function eh(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}function em(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function ey(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return em(r,t,0),em(r,e%0x100000000,4),r}function eb(e){let t=new Uint8Array(4);return em(t,e),t}function eg(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:ef.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=ef.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{var r=t;if(Uint8Array.fromBase64)return Uint8Array.fromBase64(r);let e=atob(r),n=new Uint8Array(e.length);for(let t=0;t<e.length;t++)n[t]=e.charCodeAt(t);return n}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function ew(e){let t=e;return("string"==typeof t&&(t=ep.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class e_ extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class ev extends e_{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class eE extends e_{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class ek extends e_{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class eS extends e_{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class eA extends e_{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class ex extends e_{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class eP extends e_{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class eR extends e_{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class eT extends e_{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}function eO(e){if(!eC(e))throw Error("CryptoKey instance expected")}function eC(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function ej(e){return e?.[Symbol.toStringTag]==="KeyObject"}let eU=e=>eC(e)||ej(e),eI=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function eN(e){return eI(e)&&"string"==typeof e.kty}function eH(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let e$=(e,...t)=>eH("Key must be ",e,...t);function eD(e,t,...r){return eH(`Key for the ${e} algorithm must be `,t,...r)}async function eL(e){if(ej(e))if("secret"!==e.type)return e.export({format:"jwk"});else e=e.export();if(e instanceof Uint8Array)return{kty:"oct",k:ew(e)};if(!eC(e))throw TypeError(e$(e,"CryptoKey","KeyObject","Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:n,use:i,...a}=await crypto.subtle.exportKey("jwk",e);return a}async function eM(e){return eL(e)}let eW=(e,t)=>{if("string"!=typeof e||!e)throw new eR(`${t} missing or invalid`)};async function eK(e,t){let r,n;if(eN(e))r=e;else if(eU(e))r=await eM(e);else throw TypeError(e$(e,"CryptoKey","KeyObject","JSON Web Key"));if("sha256"!==(t??="sha256")&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(r.kty){case"EC":eW(r.crv,'"crv" (Curve) Parameter'),eW(r.x,'"x" (X Coordinate) Parameter'),eW(r.y,'"y" (Y Coordinate) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x,y:r.y};break;case"OKP":eW(r.crv,'"crv" (Subtype of Key Pair) Parameter'),eW(r.x,'"x" (Public Key) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x};break;case"RSA":eW(r.e,'"e" (Exponent) Parameter'),eW(r.n,'"n" (Modulus) Parameter'),n={e:r.e,kty:r.kty,n:r.n};break;case"oct":eW(r.k,'"k" (Key Value) Parameter'),n={k:r.k,kty:r.kty};break;default:throw new eS('"kty" (Key Type) Parameter missing or unsupported')}let i=ep.encode(JSON.stringify(n));return ew(await ed(t,i))}let eJ=Symbol();function eB(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new eS(`Unsupported JWE Algorithm: ${e}`)}}let eF=e=>crypto.getRandomValues(new Uint8Array(eB(e)>>3)),ez=(e,t)=>{if(t.length<<3!==eB(e))throw new ex("Invalid Initialization Vector length")},eq=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new ex(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)};function eG(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function eV(e,t){return e.name===t}function eX(e,t,r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!eV(e.algorithm,"AES-GCM"))throw eG("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eG(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!eV(e.algorithm,"AES-KW"))throw eG("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eG(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":break;default:throw eG("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!eV(e.algorithm,"PBKDF2"))throw eG("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!eV(e.algorithm,"RSA-OAEP"))throw eG("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw eG(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}var n=e,i=r;if(i&&!n.usages.includes(i))throw TypeError(`CryptoKey does not support this operation, its usages must include ${i}.`)}async function eY(e,t,r,n,i){if(!(r instanceof Uint8Array))throw TypeError(e$(r,"Uint8Array"));let a=parseInt(e.slice(1,4),10),o=await crypto.subtle.importKey("raw",r.subarray(a>>3),"AES-CBC",!1,["encrypt"]),s=await crypto.subtle.importKey("raw",r.subarray(0,a>>3),{hash:`SHA-${a<<1}`,name:"HMAC"},!1,["sign"]),c=new Uint8Array(await crypto.subtle.encrypt({iv:n,name:"AES-CBC"},o,t)),u=eh(i,n,c,ey(i.length<<3));return{ciphertext:c,tag:new Uint8Array((await crypto.subtle.sign("HMAC",s,u)).slice(0,a>>3)),iv:n}}async function eZ(e,t,r,n,i){let a;r instanceof Uint8Array?a=await crypto.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(eX(r,e,"encrypt"),a=r);let o=new Uint8Array(await crypto.subtle.encrypt({additionalData:i,iv:n,name:"AES-GCM",tagLength:128},a,t)),s=o.slice(-16);return{ciphertext:o.slice(0,-16),tag:s,iv:n}}let eQ=async(e,t,r,n,i)=>{if(!eC(r)&&!(r instanceof Uint8Array))throw TypeError(e$(r,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));switch(n?ez(e,n):n=eF(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&eq(r,parseInt(e.slice(-3),10)),eY(e,t,r,n,i);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&eq(r,parseInt(e.slice(1,4),10)),eZ(e,t,r,n,i);default:throw new eS("Unsupported JWE Content Encryption Algorithm")}};function e0(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function e1(e,t,r){return e instanceof Uint8Array?crypto.subtle.importKey("raw",e,"AES-KW",!0,[r]):(eX(e,t,r),e)}async function e2(e,t,r){let n=await e1(t,e,"wrapKey");e0(n,e);let i=await crypto.subtle.importKey("raw",r,{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",i,n,"AES-KW"))}async function e6(e,t,r){let n=await e1(t,e,"unwrapKey");e0(n,e);let i=await crypto.subtle.unwrapKey("raw",r,n,"AES-KW",{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",i))}function e5(e){return eh(eb(e.length),e)}async function e3(e,t,r){let n=Math.ceil((t>>3)/32),i=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(eb(t+1)),n.set(e,4),n.set(r,4+e.length),i.set(await ed("sha256",n),32*t)}return i.slice(0,t>>3)}async function e4(e,t,r,n,i=new Uint8Array(0),a=new Uint8Array(0)){let o;eX(e,"ECDH"),eX(t,"ECDH","deriveBits");let s=eh(e5(ep.encode(r)),e5(i),e5(a),eb(n));return o="X25519"===e.algorithm.name?256:Math.ceil(parseInt(e.algorithm.namedCurve.slice(-3),10)/8)<<3,e3(new Uint8Array(await crypto.subtle.deriveBits({name:e.algorithm.name,public:e},t,o)),n,s)}function e8(e){switch(e.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return!0;default:return"X25519"===e.algorithm.name}}let e9=(e,t)=>eh(ep.encode(e),new Uint8Array([0]),t);async function e7(e,t,r,n){if(!(e instanceof Uint8Array)||e.length<8)throw new ex("PBES2 Salt Input must be 8 or more octets");let i=e9(t,e),a=parseInt(t.slice(13,16),10),o={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:i},s=await (n instanceof Uint8Array?crypto.subtle.importKey("raw",n,"PBKDF2",!1,["deriveBits"]):(eX(n,t,"deriveBits"),n));return new Uint8Array(await crypto.subtle.deriveBits(o,s,a))}async function te(e,t,r,n=2048,i=crypto.getRandomValues(new Uint8Array(16))){let a=await e7(i,e,n,t);return{encryptedKey:await e2(e.slice(-6),a,r),p2c:n,p2s:ew(i)}}async function tt(e,t,r,n,i){let a=await e7(i,e,n,t);return e6(e.slice(-6),a,r)}let tr=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},tn=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new eS(`alg ${e} is not supported either by JOSE or your javascript runtime`)}};async function ti(e,t,r){return eX(t,e,"encrypt"),tr(e,t),new Uint8Array(await crypto.subtle.encrypt(tn(e),t,r))}async function ta(e,t,r){return eX(t,e,"decrypt"),tr(e,t),new Uint8Array(await crypto.subtle.decrypt(tn(e),t,r))}let to=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new eS('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new eS('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new eS('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new eS('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),n={...e};return delete n.alg,delete n.use,crypto.subtle.importKey("jwk",n,t,e.ext??!e.d,e.key_ops??r)},ts=async(e,t,r,n=!1)=>{let a=(i||=new WeakMap).get(e);if(a?.[r])return a[r];let o=await to({...t,alg:r});return n&&Object.freeze(e),a?a[r]=o:i.set(e,{[r]:o}),o},tc=(e,t)=>{let r,n=(i||=new WeakMap).get(e);if(n?.[t])return n[t];let a="public"===e.type,o=!!a;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,o,a?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,o,[a?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let n;switch(t){case"RSA-OAEP":n="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":n="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":n="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":n="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:n},o,a?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:n},o,[a?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let n=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!n)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[a?"verify":"sign"])),"ES384"===t&&"P-384"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[a?"verify":"sign"])),"ES512"===t&&"P-521"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[a?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:n},o,a?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return n?n[t]=r:i.set(e,{[t]:r}),r},tu=async(e,t)=>{if(e instanceof Uint8Array||eC(e))return e;if(ej(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return tc(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return ts(e,r,t)}if(eN(e))return e.k?eg(e.k):ts(e,e,t,!0);throw Error("unreachable")};function tl(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new eS(`Unsupported JWE Algorithm: ${e}`)}}let td=e=>crypto.getRandomValues(new Uint8Array(tl(e)>>3));async function tp(e,t){if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");let r={name:"HMAC",hash:"SHA-256"},n=await crypto.subtle.generateKey(r,!1,["sign"]),i=new Uint8Array(await crypto.subtle.sign(r,n,e)),a=new Uint8Array(await crypto.subtle.sign(r,n,t)),o=0,s=-1;for(;++s<32;)o|=i[s]^a[s];return 0===o}async function tf(e,t,r,n,i,a){let o,s;if(!(t instanceof Uint8Array))throw TypeError(e$(t,"Uint8Array"));let c=parseInt(e.slice(1,4),10),u=await crypto.subtle.importKey("raw",t.subarray(c>>3),"AES-CBC",!1,["decrypt"]),l=await crypto.subtle.importKey("raw",t.subarray(0,c>>3),{hash:`SHA-${c<<1}`,name:"HMAC"},!1,["sign"]),d=eh(a,n,r,ey(a.length<<3)),p=new Uint8Array((await crypto.subtle.sign("HMAC",l,d)).slice(0,c>>3));try{o=await tp(i,p)}catch{}if(!o)throw new eA;try{s=new Uint8Array(await crypto.subtle.decrypt({iv:n,name:"AES-CBC"},u,r))}catch{}if(!s)throw new eA;return s}async function th(e,t,r,n,i,a){let o;t instanceof Uint8Array?o=await crypto.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(eX(t,e,"decrypt"),o=t);try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:a,iv:n,name:"AES-GCM",tagLength:128},o,eh(r,i)))}catch{throw new eA}}let tm=async(e,t,r,n,i,a)=>{if(!eC(t)&&!(t instanceof Uint8Array))throw TypeError(e$(t,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(!n)throw new ex("JWE Initialization Vector missing");if(!i)throw new ex("JWE Authentication Tag missing");switch(ez(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&eq(t,parseInt(e.slice(-3),10)),tf(e,t,r,n,i,a);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&eq(t,parseInt(e.slice(1,4),10)),th(e,t,r,n,i,a);default:throw new eS("Unsupported JWE Content Encryption Algorithm")}};async function ty(e,t,r,n){let i=e.slice(0,7),a=await eQ(i,r,t,n,new Uint8Array(0));return{encryptedKey:a.ciphertext,iv:ew(a.iv),tag:ew(a.tag)}}async function tb(e,t,r,n,i){return tm(e.slice(0,7),t,r,n,i,new Uint8Array(0))}let tg=async(e,t,r,n,i={})=>{let a,o,s;switch(e){case"dir":s=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let c;if(eO(r),!e8(r))throw new eS("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:u,apv:l}=i;c=i.epk?await tu(i.epk,e):(await crypto.subtle.generateKey(r.algorithm,!0,["deriveBits"])).privateKey;let{x:d,y:p,crv:f,kty:h}=await eM(c),m=await e4(r,c,"ECDH-ES"===e?t:e,"ECDH-ES"===e?tl(t):parseInt(e.slice(-5,-2),10),u,l);if(o={epk:{x:d,crv:f,kty:h}},"EC"===h&&(o.epk.y=p),u&&(o.apu=ew(u)),l&&(o.apv=ew(l)),"ECDH-ES"===e){s=m;break}s=n||td(t);let y=e.slice(-6);a=await e2(y,m,s);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s=n||td(t),eO(r),a=await ti(e,r,s);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{s=n||td(t);let{p2c:c,p2s:u}=i;({encryptedKey:a,...o}=await te(e,r,s,c,u));break}case"A128KW":case"A192KW":case"A256KW":s=n||td(t),a=await e2(e,r,s);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{s=n||td(t);let{iv:c}=i;({encryptedKey:a,...o}=await ty(e,r,s,c));break}default:throw new eS('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:s,encryptedKey:a,parameters:o}},tw=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},t_=(e,t,r,n,i)=>{let a;if(void 0!==i.crit&&n?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(a=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!a.has(o))throw new eS(`Extension Header Parameter "${o}" is not recognized`);if(void 0===i[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(a.get(o)&&void 0===n[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(n.crit)},tv=e=>e?.[Symbol.toStringTag],tE=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let n;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):n=r;break;case e.startsWith("PBES2"):n="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):n=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):n="wrapKey";break;case"decrypt"===r:n=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(n&&t.key_ops?.includes?.(n)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${n}" when present`)}return!0},tk=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(eN(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&tE(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!eU(t))throw TypeError(eD(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${tv(t)} instances for symmetric algorithms must be of type "secret"`)}},tS=(e,t,r)=>{if(eN(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&tE(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&tE(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!eU(t))throw TypeError(eD(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${tv(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${tv(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${tv(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${tv(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${tv(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},tA=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?tk(e,t,r):tS(e,t,r)};class tx{#e;#t;#r;#n;#i;#a;#o;#s;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this.#e=e}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setSharedUnprotectedHeader(e){if(this.#r)throw TypeError("setSharedUnprotectedHeader can only be called once");return this.#r=e,this}setUnprotectedHeader(e){if(this.#n)throw TypeError("setUnprotectedHeader can only be called once");return this.#n=e,this}setAdditionalAuthenticatedData(e){return this.#i=e,this}setContentEncryptionKey(e){if(this.#a)throw TypeError("setContentEncryptionKey can only be called once");return this.#a=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}async encrypt(e,t){let r,n,i,a,o;if(!this.#t&&!this.#n&&!this.#r)throw new ex("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!tw(this.#t,this.#n,this.#r))throw new ex("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let s={...this.#t,...this.#n,...this.#r};if(t_(ex,new Map,t?.crit,this.#t,s),void 0!==s.zip)throw new eS('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:u}=s;if("string"!=typeof c||!c)throw new ex('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof u||!u)throw new ex('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this.#a&&("dir"===c||"ECDH-ES"===c))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${c}`);tA("dir"===c?u:c,e,"encrypt");{let i,a=await tu(e,c);({cek:n,encryptedKey:r,parameters:i}=await tg(c,u,a,this.#a,this.#s)),i&&(t&&eJ in t?this.#n?this.#n={...this.#n,...i}:this.setUnprotectedHeader(i):this.#t?this.#t={...this.#t,...i}:this.setProtectedHeader(i))}a=this.#t?ep.encode(ew(JSON.stringify(this.#t))):ep.encode(""),this.#i?(o=ew(this.#i),i=eh(a,ep.encode("."),ep.encode(o))):i=a;let{ciphertext:l,tag:d,iv:p}=await eQ(u,this.#e,n,this.#o,i),f={ciphertext:ew(l)};return p&&(f.iv=ew(p)),d&&(f.tag=ew(d)),r&&(f.encrypted_key=ew(r)),o&&(f.aad=o),this.#t&&(f.protected=ef.decode(a)),this.#r&&(f.unprotected=this.#r),this.#n&&(f.header=this.#n),f}}class tP{#c;constructor(e){this.#c=new tx(e)}setContentEncryptionKey(e){return this.#c.setContentEncryptionKey(e),this}setInitializationVector(e){return this.#c.setInitializationVector(e),this}setProtectedHeader(e){return this.#c.setProtectedHeader(e),this}setKeyManagementParameters(e){return this.#c.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this.#c.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let tR=e=>Math.floor(e.getTime()/1e3),tT=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,tO=e=>{let t,r=tT.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(0x1e187e0*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function tC(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}let tj=e=>e.includes("/")?e.toLowerCase():`application/${e.toLowerCase()}`,tU=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));class tI{#u;constructor(e){if(!eI(e))throw TypeError("JWT Claims Set MUST be an object");this.#u=structuredClone(e)}data(){return ep.encode(JSON.stringify(this.#u))}get iss(){return this.#u.iss}set iss(e){this.#u.iss=e}get sub(){return this.#u.sub}set sub(e){this.#u.sub=e}get aud(){return this.#u.aud}set aud(e){this.#u.aud=e}set jti(e){this.#u.jti=e}set nbf(e){"number"==typeof e?this.#u.nbf=tC("setNotBefore",e):e instanceof Date?this.#u.nbf=tC("setNotBefore",tR(e)):this.#u.nbf=tR(new Date)+tO(e)}set exp(e){"number"==typeof e?this.#u.exp=tC("setExpirationTime",e):e instanceof Date?this.#u.exp=tC("setExpirationTime",tR(e)):this.#u.exp=tR(new Date)+tO(e)}set iat(e){void 0===e?this.#u.iat=tR(new Date):e instanceof Date?this.#u.iat=tC("setIssuedAt",tR(e)):"string"==typeof e?this.#u.iat=tC("setIssuedAt",tR(new Date)+tO(e)):this.#u.iat=tC("setIssuedAt",e)}}class tN{#a;#o;#s;#t;#l;#d;#p;#f;constructor(e={}){this.#f=new tI(e)}setIssuer(e){return this.#f.iss=e,this}setSubject(e){return this.#f.sub=e,this}setAudience(e){return this.#f.aud=e,this}setJti(e){return this.#f.jti=e,this}setNotBefore(e){return this.#f.nbf=e,this}setExpirationTime(e){return this.#f.exp=e,this}setIssuedAt(e){return this.#f.iat=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setContentEncryptionKey(e){if(this.#a)throw TypeError("setContentEncryptionKey can only be called once");return this.#a=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}replicateIssuerAsHeader(){return this.#l=!0,this}replicateSubjectAsHeader(){return this.#d=!0,this}replicateAudienceAsHeader(){return this.#p=!0,this}async encrypt(e,t){let r=new tP(this.#f.data());return this.#t&&(this.#l||this.#d||this.#p)&&(this.#t={...this.#t,iss:this.#l?this.#f.iss:void 0,sub:this.#d?this.#f.sub:void 0,aud:this.#p?this.#f.aud:void 0}),r.setProtectedHeader(this.#t),this.#o&&r.setInitializationVector(this.#o),this.#a&&r.setContentEncryptionKey(this.#a),this.#s&&r.setKeyManagementParameters(this.#s),r.encrypt(e,t)}}async function tH(e,t,r){let n;if(!eI(e))throw TypeError("JWK must be an object");switch(t??=e.alg,n??=r?.extractable??e.ext,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return eg(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new eS('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return to({...e,alg:t,ext:n});default:throw new eS('Unsupported "kty" (Key Type) Parameter value')}}let t$=async(e,t,r,n,i)=>{switch(e){case"dir":if(void 0!==r)throw new ex("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new ex("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i,a;if(!eI(n.epk))throw new ex('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(eO(t),!e8(t))throw new eS("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await tH(n.epk,e);if(eO(o),void 0!==n.apu){if("string"!=typeof n.apu)throw new ex('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{i=eg(n.apu)}catch{throw new ex("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new ex('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{a=eg(n.apv)}catch{throw new ex("Failed to base64url decode the apv")}}let s=await e4(o,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?tl(n.enc):parseInt(e.slice(-5,-2),10),i,a);if("ECDH-ES"===e)return s;if(void 0===r)throw new ex("JWE Encrypted Key missing");return e6(e.slice(-6),s,r)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new ex("JWE Encrypted Key missing");return eO(t),ta(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let a;if(void 0===r)throw new ex("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new ex('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=i?.maxPBES2Count||1e4;if(n.p2c>o)throw new ex('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new ex('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{a=eg(n.p2s)}catch{throw new ex("Failed to base64url decode the p2s")}return tt(e,t,r,n.p2c,a)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new ex("JWE Encrypted Key missing");return e6(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let i,a;if(void 0===r)throw new ex("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new ex('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new ex('JOSE Header "tag" (Authentication Tag) missing or invalid');try{i=eg(n.iv)}catch{throw new ex("Failed to base64url decode the iv")}try{a=eg(n.tag)}catch{throw new ex("Failed to base64url decode the tag")}return tb(e,t,r,i,a)}default:throw new eS('Invalid or unsupported "alg" (JWE Algorithm) header value')}},tD=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function tL(e,t,r){let n,i,a,o,s,c,u;if(!eI(e))throw new ex("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new ex("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new ex("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new ex("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new ex("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new ex("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new ex("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new ex("JWE AAD incorrect type");if(void 0!==e.header&&!eI(e.header))throw new ex("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!eI(e.unprotected))throw new ex("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=eg(e.protected);n=JSON.parse(ef.decode(t))}catch{throw new ex("JWE Protected Header is invalid")}if(!tw(n,e.header,e.unprotected))throw new ex("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let l={...n,...e.header,...e.unprotected};if(t_(ex,new Map,r?.crit,n,l),void 0!==l.zip)throw new eS('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:d,enc:p}=l;if("string"!=typeof d||!d)throw new ex("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof p||!p)throw new ex("missing JWE Encryption Algorithm (enc) in JWE Header");let f=r&&tD("keyManagementAlgorithms",r.keyManagementAlgorithms),h=r&&tD("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(f&&!f.has(d)||!f&&d.startsWith("PBES2"))throw new ek('"alg" (Algorithm) Header Parameter value not allowed');if(h&&!h.has(p))throw new ek('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{i=eg(e.encrypted_key)}catch{throw new ex("Failed to base64url decode the encrypted_key")}let m=!1;"function"==typeof t&&(t=await t(n,e),m=!0),tA("dir"===d?p:d,t,"decrypt");let y=await tu(t,d);try{a=await t$(d,y,i,l,r)}catch(e){if(e instanceof TypeError||e instanceof ex||e instanceof eS)throw e;a=td(p)}if(void 0!==e.iv)try{o=eg(e.iv)}catch{throw new ex("Failed to base64url decode the iv")}if(void 0!==e.tag)try{s=eg(e.tag)}catch{throw new ex("Failed to base64url decode the tag")}let b=ep.encode(e.protected??"");c=void 0!==e.aad?eh(b,ep.encode("."),ep.encode(e.aad)):b;try{u=eg(e.ciphertext)}catch{throw new ex("Failed to base64url decode the ciphertext")}let g={plaintext:await tm(p,a,u,o,s,c)};if(void 0!==e.protected&&(g.protectedHeader=n),void 0!==e.aad)try{g.additionalAuthenticatedData=eg(e.aad)}catch{throw new ex("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(g.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(g.unprotectedHeader=e.header),m)?{...g,key:y}:g}async function tM(e,t,r){if(e instanceof Uint8Array&&(e=ef.decode(e)),"string"!=typeof e)throw new ex("Compact JWE must be a string or Uint8Array");let{0:n,1:i,2:a,3:o,4:s,length:c}=e.split(".");if(5!==c)throw new ex("Invalid Compact JWE");let u=await tL({ciphertext:o,iv:a||void 0,protected:n,tag:s||void 0,encrypted_key:i||void 0},t,r),l={plaintext:u.plaintext,protectedHeader:u.protectedHeader};return"function"==typeof t?{...l,key:u.key}:l}async function tW(e,t,r){let n=await tM(e,t,r),i=function(e,t,r={}){let n,i;try{n=JSON.parse(ef.decode(t))}catch{}if(!eI(n))throw new eP("JWT Claims Set must be a top-level JSON object");let{typ:a}=r;if(a&&("string"!=typeof e.typ||tj(e.typ)!==tj(a)))throw new ev('unexpected "typ" JWT header value',n,"typ","check_failed");let{requiredClaims:o=[],issuer:s,subject:c,audience:u,maxTokenAge:l}=r,d=[...o];for(let e of(void 0!==l&&d.push("iat"),void 0!==u&&d.push("aud"),void 0!==c&&d.push("sub"),void 0!==s&&d.push("iss"),new Set(d.reverse())))if(!(e in n))throw new ev(`missing required "${e}" claim`,n,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(n.iss))throw new ev('unexpected "iss" claim value',n,"iss","check_failed");if(c&&n.sub!==c)throw new ev('unexpected "sub" claim value',n,"sub","check_failed");if(u&&!tU(n.aud,"string"==typeof u?[u]:u))throw new ev('unexpected "aud" claim value',n,"aud","check_failed");switch(typeof r.clockTolerance){case"string":i=tO(r.clockTolerance);break;case"number":i=r.clockTolerance;break;case"undefined":i=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:p}=r,f=tR(p||new Date);if((void 0!==n.iat||l)&&"number"!=typeof n.iat)throw new ev('"iat" claim must be a number',n,"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new ev('"nbf" claim must be a number',n,"nbf","invalid");if(n.nbf>f+i)throw new ev('"nbf" claim timestamp check failed',n,"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new ev('"exp" claim must be a number',n,"exp","invalid");if(n.exp<=f-i)throw new eE('"exp" claim timestamp check failed',n,"exp","check_failed")}if(l){let e=f-n.iat;if(e-i>("number"==typeof l?l:tO(l)))throw new eE('"iat" claim timestamp check failed (too far in the past)',n,"iat","check_failed");if(e<0-i)throw new ev('"iat" claim timestamp check failed (it should be in the past)',n,"iat","check_failed")}return n}(n.protectedHeader,n.plaintext,r),{protectedHeader:a}=n;if(void 0!==a.iss&&a.iss!==i.iss)throw new ev('replicated "iss" claim header parameter mismatch',i,"iss","mismatch");if(void 0!==a.sub&&a.sub!==i.sub)throw new ev('replicated "sub" claim header parameter mismatch',i,"sub","mismatch");if(void 0!==a.aud&&JSON.stringify(a.aud)!==JSON.stringify(i.aud))throw new ev('replicated "aud" claim header parameter mismatch',i,"aud","mismatch");let o={payload:i,protectedHeader:a};return"function"==typeof t?{...o,key:n.key}:o}let tK=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,tJ=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,tB=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,tF=/^[\u0020-\u003A\u003D-\u007E]*$/,tz=Object.prototype.toString,tq=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function tG(e,t){let r=new tq,n=e.length;if(n<2)return r;let i=t?.decode||tZ,a=0;do{let t=e.indexOf("=",a);if(-1===t)break;let o=e.indexOf(";",a),s=-1===o?n:o;if(t>s){a=e.lastIndexOf(";",t-1)+1;continue}let c=tV(e,a,t),u=tX(e,t,c),l=e.slice(c,u);if(void 0===r[l]){let n=tV(e,t+1,s),a=tX(e,s,n),o=i(e.slice(n,a));r[l]=o}a=s+1}while(a<n);return r}function tV(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function tX(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function tY(e,t,r){let n=r?.encode||encodeURIComponent;if(!tK.test(e))throw TypeError(`argument name is invalid: ${e}`);let i=n(t);if(!tJ.test(i))throw TypeError(`argument val is invalid: ${t}`);let a=e+"="+i;if(!r)return a;if(void 0!==r.maxAge){if(!Number.isInteger(r.maxAge))throw TypeError(`option maxAge is invalid: ${r.maxAge}`);a+="; Max-Age="+r.maxAge}if(r.domain){if(!tB.test(r.domain))throw TypeError(`option domain is invalid: ${r.domain}`);a+="; Domain="+r.domain}if(r.path){if(!tF.test(r.path))throw TypeError(`option path is invalid: ${r.path}`);a+="; Path="+r.path}if(r.expires){var o;if(o=r.expires,"[object Date]"!==tz.call(o)||!Number.isFinite(r.expires.valueOf()))throw TypeError(`option expires is invalid: ${r.expires}`);a+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(a+="; HttpOnly"),r.secure&&(a+="; Secure"),r.partitioned&&(a+="; Partitioned"),r.priority)switch("string"==typeof r.priority?r.priority.toLowerCase():void 0){case"low":a+="; Priority=Low";break;case"medium":a+="; Priority=Medium";break;case"high":a+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${r.priority}`)}if(r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"none":a+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${r.sameSite}`)}return a}function tZ(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}let{q:tQ}=l,t0=()=>Date.now()/1e3|0,t1="A256CBC-HS512";async function t2(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:i}=e,a=Array.isArray(r)?r:[r],o=await t5(t1,a[0],i),s=await eK({kty:"oct",k:ew(o)},`sha${o.byteLength<<3}`);return await new tN(t).setProtectedHeader({alg:"dir",enc:t1,kid:s}).setIssuedAt().setExpirationTime(t0()+n).setJti(crypto.randomUUID()).encrypt(o)}async function t6(e){let{token:t,secret:r,salt:n}=e,i=Array.isArray(r)?r:[r];if(!t)return null;let{payload:a}=await tW(t,async({kid:e,enc:t})=>{for(let r of i){let i=await t5(t,r,n);if(void 0===e||e===await eK({kty:"oct",k:ew(i)},`sha${i.byteLength<<3}`))return i}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[t1,"A256GCM"]});return a}async function t5(e,t,r){let n;switch(e){case"A256CBC-HS512":n=64;break;case"A256GCM":n=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await el("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,n)}async function t3({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:i}=e,a=n.origin;return t?a=await i.redirect({url:t,baseUrl:n.origin}):r&&(a=await i.redirect({url:r,baseUrl:n.origin})),{callbackUrl:a,callbackUrlCookie:a!==r?a:void 0}}let t4="\x1b[31m",t8="\x1b[0m",t9={error(e){let t=e instanceof b?e.type:e.name;if(console.error(`${t4}[auth][error]${t8} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${t4}[auth][cause]${t8}:`,t.stack),r&&console.error(`${t4}[auth][details]${t8}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){console.warn(`\x1b[33m[auth][warn][${e}]${t8}`,"Read more: https://warnings.authjs.dev")},debug(e,t){console.log(`\x1b[90m[auth][debug]:${t8} ${e}`,JSON.stringify(t,null,2))}};function t7(e){let t={...t9};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let re=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"],{q:rt,l:rr}=l;async function rn(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function ri(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new M("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:n,providerId:i}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new M(`Cannot parse action at ${e}`);let n=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==n.length&&2!==n.length)throw new M(`Cannot parse action at ${e}`);let[i,a]=n;if(!re.includes(i)||a&&!["signin","callback","webauthn-options"].includes(i))throw new M(`Cannot parse action at ${e}`);return{action:i,providerId:a}}(r.pathname,t.basePath);return{url:r,action:n,providerId:i,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await rn(e):void 0,cookies:rt(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(n){let r=t7(t);r.error(n),r.debug("request",e)}}function ra(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:n,options:i}=e,a=rr(r,n,i);t.has("Set-Cookie")?t.append("Set-Cookie",a):t.set("Set-Cookie",a)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let n=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&n.headers.set("Location",e.redirect),n}async function ro(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function rs(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function rc({options:e,cookieValue:t,isPost:r,bodyValue:n}){if(t){let[i,a]=t.split("|");if(a===await ro(`${i}${e.secret}`))return{csrfTokenVerified:r&&i===n,csrfToken:i}}let i=rs(32),a=await ro(`${i}${e.secret}`);return{cookie:`${i}|${a}`,csrfToken:i}}function ru(e,t){if(!t)throw new F(`CSRF token was missing during an action ${e}`)}function rl(e){return null!==e&&"object"==typeof e}function rd(e,...t){if(!t.length)return e;let r=t.shift();if(rl(e)&&rl(r))for(let t in r)rl(r[t])?(rl(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),rd(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return rd(e,...t)}let rp=Symbol("skip-csrf-check"),rf=Symbol("return-type-raw"),rh=Symbol("custom-fetch"),rm=Symbol("conform-internal"),ry=e=>rg({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),rb=e=>rg({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function rg(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}function rw(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,n]of Object.entries(e.params))"claims"===t&&(n=JSON.stringify(n)),r.searchParams.set(t,String(n));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let r_={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function rv({authOptions:e,providerId:t,action:r,url:n,cookies:i,callbackUrl:a,csrfToken:o,csrfDisabled:s,isPost:c}){var u,l;let d=t7(e),{providers:p,provider:f}=function(e){let{providerId:t,config:r}=e,n=new URL(r.basePath??"/auth",e.url.origin),i=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:i,...a}=t,o=i?.id??a.id,s=rd(a,i,{signinUrl:`${n}/signin/${o}`,callbackUrl:`${n}/callback/${o}`});if("oauth"===t.type||"oidc"===t.type){s.redirectProxyUrl??(s.redirectProxyUrl=i?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=rw(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=rw(e.token,e.issuer),n=rw(e.userinfo,e.issuer),i=e.checks??["pkce"];return e.redirectProxyUrl&&(i.includes("state")||i.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:i,userinfo:n,profile:e.profile??ry,account:e.account??rb}}(s);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[rh]??(e[rh]=i?.[rh]),e}return s}),a=i.find(({id:e})=>e===t);if(t&&!a){let e=i.map(e=>e.id).join(", ");throw Error(`Provider with id "${t}" not found. Available providers: [${e}].`)}return{providers:i,provider:a}}({url:n,providerId:t,config:e}),h=!1;if((f?.type==="oauth"||f?.type==="oidc")&&f.redirectProxyUrl)try{h=new URL(f.redirectProxyUrl).origin===n.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${f.redirectProxyUrl}`)}let y={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:n,action:r,provider:f,cookies:rd(m(e.useSecureCookies??"https:"===n.protocol),e.cookies),providers:p,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:t2,decode:t6,...e.jwt},events:(u=e.events??{},l=d,Object.keys(u).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=u[t];return await r(...e)}catch(e){l.error(new k(e))}},e),{})),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,n)=>(r[n]=async(...r)=>{try{t.debug(`adapter_${n}`,{args:r});let i=e[n];return await i(...r)}catch(r){let e=new w(r);throw t.error(e),e}},r),{})}(e.adapter,d),callbacks:{...r_,...e.callbacks},logger:d,callbackUrl:n.origin,isOnRedirectProxy:h,experimental:{...e.experimental}},b=[];if(s)y.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await rc({options:y,cookieValue:i?.[y.cookies.csrfToken.name],isPost:c,bodyValue:o});y.csrfToken=e,y.csrfTokenVerified=r,t&&b.push({name:y.cookies.csrfToken.name,value:t,options:y.cookies.csrfToken.options})}let{callbackUrl:g,callbackUrlCookie:_}=await t3({options:y,cookieValue:i?.[y.cookies.callbackUrl.name],paramValue:a});return y.callbackUrl=g,_&&b.push({name:y.cookies.callbackUrl.name,value:_,options:y.cookies.callbackUrl.options}),{options:y,cookies:b}}var rE,rk,rS,rA,rx,rP,rR,rT,rO,rC,rj,rU,rI,rN,rH,r$,rD={},rL=[],rM=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,rW=Array.isArray;function rK(e,t){for(var r in t)e[r]=t[r];return e}function rJ(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function rB(e,t,r){var n,i,a,o={};for(a in t)"key"==a?n=t[a]:"ref"==a?i=t[a]:o[a]=t[a];if(arguments.length>2&&(o.children=arguments.length>3?rR.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(a in e.defaultProps)void 0===o[a]&&(o[a]=e.defaultProps[a]);return rF(e,o,n,i,null)}function rF(e,t,r,n,i){var a={type:e,props:t,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==i?++rO:i,__i:-1,__u:0};return null==i&&null!=rT.vnode&&rT.vnode(a),a}function rz(e){return e.children}function rq(e,t){this.props=e,this.context=t}function rG(e,t){if(null==t)return e.__?rG(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?rG(e):null}function rV(e){(!e.__d&&(e.__d=!0)&&rC.push(e)&&!rX.__r++||rj!==rT.debounceRendering)&&((rj=rT.debounceRendering)||rU)(rX)}function rX(){var e,t,r,n,i,a,o,s;for(rC.sort(rI);e=rC.shift();)e.__d&&(t=rC.length,n=void 0,a=(i=(r=e).__v).__e,o=[],s=[],r.__P&&((n=rK({},i)).__v=i.__v+1,rT.vnode&&rT.vnode(n),r1(r.__P,n,i,r.__n,r.__P.namespaceURI,32&i.__u?[a]:null,o,null==a?rG(i):a,!!(32&i.__u),s),n.__v=i.__v,n.__.__k[n.__i]=n,r2(o,n,s),n.__e!=a&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(n)),rC.length>t&&rC.sort(rI));rX.__r=0}function rY(e,t,r,n,i,a,o,s,c,u,l){var d,p,f,h,m,y=n&&n.__k||rL,b=t.length;for(r.__d=c,function(e,t,r){var n,i,a,o,s,c=t.length,u=r.length,l=u,d=0;for(e.__k=[],n=0;n<c;n++)null!=(i=t[n])&&"boolean"!=typeof i&&"function"!=typeof i?(o=n+d,(i=e.__k[n]="string"==typeof i||"number"==typeof i||"bigint"==typeof i||i.constructor==String?rF(null,i,null,null,null):rW(i)?rF(rz,{children:i},null,null,null):void 0===i.constructor&&i.__b>0?rF(i.type,i.props,i.key,i.ref?i.ref:null,i.__v):i).__=e,i.__b=e.__b+1,a=null,-1!==(s=i.__i=function(e,t,r,n){var i=e.key,a=e.type,o=r-1,s=r+1,c=t[r];if(null===c||c&&i==c.key&&a===c.type&&0==(131072&c.__u))return r;if(n>+(null!=c&&0==(131072&c.__u)))for(;o>=0||s<t.length;){if(o>=0){if((c=t[o])&&0==(131072&c.__u)&&i==c.key&&a===c.type)return o;o--}if(s<t.length){if((c=t[s])&&0==(131072&c.__u)&&i==c.key&&a===c.type)return s;s++}}return -1}(i,r,o,l))&&(l--,(a=r[s])&&(a.__u|=131072)),null==a||null===a.__v?(-1==s&&d--,"function"!=typeof i.type&&(i.__u|=65536)):s!==o&&(s==o-1?d--:s==o+1?d++:(s>o?d--:d++,i.__u|=65536))):i=e.__k[n]=null;if(l)for(n=0;n<u;n++)null!=(a=r[n])&&0==(131072&a.__u)&&(a.__e==e.__d&&(e.__d=rG(a)),function e(t,r,n){var i,a;if(rT.unmount&&rT.unmount(t),(i=t.ref)&&(i.current&&i.current!==t.__e||r6(i,null,r)),null!=(i=t.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(e){rT.__e(e,r)}i.base=i.__P=null}if(i=t.__k)for(a=0;a<i.length;a++)i[a]&&e(i[a],r,n||"function"!=typeof t.type);n||rJ(t.__e),t.__c=t.__=t.__e=t.__d=void 0}(a,a))}(r,t,y),c=r.__d,d=0;d<b;d++)null!=(f=r.__k[d])&&(p=-1===f.__i?rD:y[f.__i]||rD,f.__i=d,r1(e,f,p,i,a,o,s,c,u,l),h=f.__e,f.ref&&p.ref!=f.ref&&(p.ref&&r6(p.ref,null,f),l.push(f.ref,f.__c||h,f)),null==m&&null!=h&&(m=h),65536&f.__u||p.__k===f.__k?c=function e(t,r,n){var i,a;if("function"==typeof t.type){for(i=t.__k,a=0;i&&a<i.length;a++)i[a]&&(i[a].__=t,r=e(i[a],r,n));return r}t.__e!=r&&(r&&t.type&&!n.contains(r)&&(r=rG(t)),n.insertBefore(t.__e,r||null),r=t.__e);do r=r&&r.nextSibling;while(null!=r&&8===r.nodeType);return r}(f,c,e):"function"==typeof f.type&&void 0!==f.__d?c=f.__d:h&&(c=h.nextSibling),f.__d=void 0,f.__u&=-196609);r.__d=c,r.__e=m}function rZ(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||rM.test(t)?r:r+"px"}function rQ(e,t,r,n,i){var a;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||rZ(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||rZ(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])a=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+a]=r,r?n?r.u=n.u:(r.u=rN,e.addEventListener(t,a?r$:rH,a)):e.removeEventListener(t,a?r$:rH,a);else{if("http://www.w3.org/2000/svg"==i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==r?"":r))}}function r0(e){return function(t){if(this.l){var r=this.l[t.type+e];if(null==t.t)t.t=rN++;else if(t.t<r.u)return;return r(rT.event?rT.event(t):t)}}}function r1(e,t,r,n,i,a,o,s,c,u){var l,d,p,f,h,m,y,b,g,w,_,v,E,k,S,A,x=t.type;if(void 0!==t.constructor)return null;128&r.__u&&(c=!!(32&r.__u),a=[s=t.__e=r.__e]),(l=rT.__b)&&l(t);e:if("function"==typeof x)try{if(b=t.props,g="prototype"in x&&x.prototype.render,w=(l=x.contextType)&&n[l.__c],_=l?w?w.props.value:l.__:n,r.__c?y=(d=t.__c=r.__c).__=d.__E:(g?t.__c=d=new x(b,_):(t.__c=d=new rq(b,_),d.constructor=x,d.render=r5),w&&w.sub(d),d.props=b,d.state||(d.state={}),d.context=_,d.__n=n,p=d.__d=!0,d.__h=[],d._sb=[]),g&&null==d.__s&&(d.__s=d.state),g&&null!=x.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=rK({},d.__s)),rK(d.__s,x.getDerivedStateFromProps(b,d.__s))),f=d.props,h=d.state,d.__v=t,p)g&&null==x.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),g&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(g&&null==x.getDerivedStateFromProps&&b!==f&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(b,_),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(b,d.__s,_)||t.__v===r.__v)){for(t.__v!==r.__v&&(d.props=b,d.state=d.__s,d.__d=!1),t.__e=r.__e,t.__k=r.__k,t.__k.some(function(e){e&&(e.__=t)}),v=0;v<d._sb.length;v++)d.__h.push(d._sb[v]);d._sb=[],d.__h.length&&o.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(b,d.__s,_),g&&null!=d.componentDidUpdate&&d.__h.push(function(){d.componentDidUpdate(f,h,m)})}if(d.context=_,d.props=b,d.__P=e,d.__e=!1,E=rT.__r,k=0,g){for(d.state=d.__s,d.__d=!1,E&&E(t),l=d.render(d.props,d.state,d.context),S=0;S<d._sb.length;S++)d.__h.push(d._sb[S]);d._sb=[]}else do d.__d=!1,E&&E(t),l=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++k<25);d.state=d.__s,null!=d.getChildContext&&(n=rK(rK({},n),d.getChildContext())),g&&!p&&null!=d.getSnapshotBeforeUpdate&&(m=d.getSnapshotBeforeUpdate(f,h)),rY(e,rW(A=null!=l&&l.type===rz&&null==l.key?l.props.children:l)?A:[A],t,r,n,i,a,o,s,c,u),d.base=t.__e,t.__u&=-161,d.__h.length&&o.push(d),y&&(d.__E=d.__=null)}catch(e){if(t.__v=null,c||null!=a){for(t.__u|=c?160:128;s&&8===s.nodeType&&s.nextSibling;)s=s.nextSibling;a[a.indexOf(s)]=null,t.__e=s}else t.__e=r.__e,t.__k=r.__k;rT.__e(e,t,r)}else null==a&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,n,i,a,o,s,c){var u,l,d,p,f,h,m,y=r.props,b=t.props,g=t.type;if("svg"===g?i="http://www.w3.org/2000/svg":"math"===g?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),null!=a){for(u=0;u<a.length;u++)if((f=a[u])&&"setAttribute"in f==!!g&&(g?f.localName===g:3===f.nodeType)){e=f,a[u]=null;break}}if(null==e){if(null===g)return document.createTextNode(b);e=document.createElementNS(i,g,b.is&&b),s&&(rT.__m&&rT.__m(t,a),s=!1),a=null}if(null===g)y===b||s&&e.data===b||(e.data=b);else{if(a=a&&rR.call(e.childNodes),y=r.props||rD,!s&&null!=a)for(y={},u=0;u<e.attributes.length;u++)y[(f=e.attributes[u]).name]=f.value;for(u in y)if(f=y[u],"children"==u);else if("dangerouslySetInnerHTML"==u)d=f;else if(!(u in b)){if("value"==u&&"defaultValue"in b||"checked"==u&&"defaultChecked"in b)continue;rQ(e,u,null,f,i)}for(u in b)f=b[u],"children"==u?p=f:"dangerouslySetInnerHTML"==u?l=f:"value"==u?h=f:"checked"==u?m=f:s&&"function"!=typeof f||y[u]===f||rQ(e,u,f,y[u],i);if(l)s||d&&(l.__html===d.__html||l.__html===e.innerHTML)||(e.innerHTML=l.__html),t.__k=[];else if(d&&(e.innerHTML=""),rY(e,rW(p)?p:[p],t,r,n,"foreignObject"===g?"http://www.w3.org/1999/xhtml":i,a,o,a?a[0]:r.__k&&rG(r,0),s,c),null!=a)for(u=a.length;u--;)rJ(a[u]);s||(u="value","progress"===g&&null==h?e.removeAttribute("value"):void 0===h||h===e[u]&&("progress"!==g||h)&&("option"!==g||h===y[u])||rQ(e,u,h,y[u],i),u="checked",void 0!==m&&m!==e[u]&&rQ(e,u,m,y[u],i))}return e}(r.__e,t,r,n,i,a,o,c,u);(l=rT.diffed)&&l(t)}function r2(e,t,r){t.__d=void 0;for(var n=0;n<r.length;n++)r6(r[n],r[++n],r[++n]);rT.__c&&rT.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){rT.__e(e,t.__v)}})}function r6(e,t,r){try{if("function"==typeof e){var n="function"==typeof e.__u;n&&e.__u(),n&&null==t||(e.__u=e(t))}else e.current=t}catch(e){rT.__e(e,r)}}function r5(e,t,r){return this.constructor(e,r)}function r3(e,t){var r,n,i,a,o;r=e,rT.__&&rT.__(r,t),i=(n="function"==typeof r3)?null:r3&&r3.__k||t.__k,a=[],o=[],r1(t,r=(!n&&r3||t).__k=rB(rz,null,[r]),i||rD,rD,t.namespaceURI,!n&&r3?[r3]:i?null:t.firstChild?rR.call(t.childNodes):null,a,!n&&r3?r3:i?i.__e:t.firstChild,n,o),r2(a,r,o)}rR=rL.slice,rT={__e:function(e,t,r,n){for(var i,a,o;t=t.__;)if((i=t.__c)&&!i.__)try{if((a=i.constructor)&&null!=a.getDerivedStateFromError&&(i.setState(a.getDerivedStateFromError(e)),o=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,n||{}),o=i.__d),o)return i.__E=i}catch(t){e=t}throw e}},rO=0,rq.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=rK({},this.state),"function"==typeof e&&(e=e(rK({},r),this.props)),e&&rK(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),rV(this))},rq.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),rV(this))},rq.prototype.render=rz,rC=[],rU="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,rI=function(e,t){return e.__v.__b-t.__v.__b},rX.__r=0,rN=0,rH=r0(!1),r$=r0(!0);var r4=/[\s\n\\/='"\0<>]/,r8=/^(xlink|xmlns|xml)([A-Z])/,r9=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,r7=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,ne=new Set(["draggable","spellcheck"]),nt=/["&<]/;function nr(e){if(0===e.length||!1===nt.test(e))return e;for(var t=0,r=0,n="",i="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:i="&quot;";break;case 38:i="&amp;";break;case 60:i="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=i,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var nn={},ni=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),na=/[A-Z]/g;function no(){this.__d=!0}var ns=null,nc,nu,nl,nd,np={},nf=[],nh=Array.isArray,nm=Object.assign;function ny(e,t){var r,n=e.type,i=!0;return e.__c?(i=!1,(r=e.__c).state=r.__s):r=new n(e.props,t),e.__c=r,r.__v=e,r.props=e.props,r.context=t,r.__d=!0,null==r.state&&(r.state=np),null==r.__s&&(r.__s=r.state),n.getDerivedStateFromProps?r.state=nm({},r.state,n.getDerivedStateFromProps(r.props,r.state)):i&&r.componentWillMount?(r.componentWillMount(),r.state=r.__s!==r.state?r.__s:r.state):!i&&r.componentWillUpdate&&r.componentWillUpdate(),nl&&nl(e),r.render(r.props,r.state,t)}var nb=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),ng=/["&<]/,nw=0;function n_(e,t,r,n,i,a){t||(t={});var o,s,c=t;"ref"in t&&(o=t.ref,delete t.ref);var u={type:e,props:c,key:r,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--nw,__i:-1,__u:0,__source:i,__self:a};if("function"==typeof e&&(o=e.defaultProps))for(s in o)void 0===c[s]&&(c[s]=o[s]);return rT.vnode&&rT.vnode(u),u}async function nv(e,t){let r=window.SimpleWebAuthnBrowser;async function n(r){let n=new URL(`${e}/webauthn-options/${t}`);r&&n.searchParams.append("action",r),a().forEach(e=>{n.searchParams.append(e.name,e.value)});let i=await fetch(n);return i.ok?i.json():void console.error("Failed to fetch options",i)}function i(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function a(){return Array.from(i().querySelectorAll("input[data-form-field]"))}async function o(e,t){let r=i();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function s(e,t){let n=await r.startAuthentication(e,t);return await o("authenticate",n)}async function c(e){a().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await o("register",t)}async function u(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await n("authenticate");if(!e)return void console.error("Failed to fetch option for autofill authentication");try{await s(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=i();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await n(void 0);if(!t)return void console.error("Failed to fetch options for form submission");if("authenticate"===t.action)try{await s(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),u()}let nE={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},nk=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function nS({html:e,title:t,status:r,cookies:n,theme:i,headTags:a}){return{cookies:n,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${nk}</style><title>${t}</title>${a??""}</head><body class="__next-auth-theme-${i?.colorScheme??"auto"}"><div class="page">${function(e,t,r){var n=rT.__s;rT.__s=!0,nc=rT.__b,nu=rT.diffed,nl=rT.__r,nd=rT.unmount;var i=rB(rz,null);i.__k=[e];try{var a=function e(t,r,n,i,a,o,s){if(null==t||!0===t||!1===t||""===t)return"";var c=typeof t;if("object"!=c)return"function"==c?"":"string"==c?nr(t):t+"";if(nh(t)){var u,l="";a.__k=t;for(var d=0;d<t.length;d++){var p=t[d];if(null!=p&&"boolean"!=typeof p){var f,h=e(p,r,n,i,a,o,s);"string"==typeof h?l+=h:(u||(u=[]),l&&u.push(l),l="",nh(h)?(f=u).push.apply(f,h):u.push(h))}}return u?(l&&u.push(l),u):l}if(void 0!==t.constructor)return"";t.__=a,nc&&nc(t);var m=t.type,y=t.props;if("function"==typeof m){var b,g,w,_=r;if(m===rz){if("tpl"in y){for(var v="",E=0;E<y.tpl.length;E++)if(v+=y.tpl[E],y.exprs&&E<y.exprs.length){var k=y.exprs[E];if(null==k)continue;"object"==typeof k&&(void 0===k.constructor||nh(k))?v+=e(k,r,n,i,t,o,s):v+=k}return v}if("UNSTABLE_comment"in y)return"\x3c!--"+nr(y.UNSTABLE_comment)+"--\x3e";g=y.children}else{if(null!=(b=m.contextType)){var S=r[b.__c];_=S?S.props.value:b.__}var A=m.prototype&&"function"==typeof m.prototype.render;if(A)g=ny(t,_),w=t.__c;else{t.__c=w={__v:t,context:_,props:t.props,setState:no,forceUpdate:no,__d:!0,__h:[]};for(var x=0;w.__d&&x++<25;)w.__d=!1,nl&&nl(t),g=m.call(w,y,_);w.__d=!0}if(null!=w.getChildContext&&(r=nm({},r,w.getChildContext())),A&&rT.errorBoundaries&&(m.getDerivedStateFromError||w.componentDidCatch)){g=null!=g&&g.type===rz&&null==g.key&&null==g.props.tpl?g.props.children:g;try{return e(g,r,n,i,t,o,s)}catch(a){return m.getDerivedStateFromError&&(w.__s=m.getDerivedStateFromError(a)),w.componentDidCatch&&w.componentDidCatch(a,np),w.__d?(g=ny(t,r),null!=(w=t.__c).getChildContext&&(r=nm({},r,w.getChildContext())),e(g=null!=g&&g.type===rz&&null==g.key&&null==g.props.tpl?g.props.children:g,r,n,i,t,o,s)):""}finally{nu&&nu(t),t.__=null,nd&&nd(t)}}}g=null!=g&&g.type===rz&&null==g.key&&null==g.props.tpl?g.props.children:g;try{var P=e(g,r,n,i,t,o,s);return nu&&nu(t),t.__=null,rT.unmount&&rT.unmount(t),P}catch(a){if(!o&&s&&s.onError){var R=s.onError(a,t,function(a){return e(a,r,n,i,t,o,s)});if(void 0!==R)return R;var T=rT.__e;return T&&T(a,t),""}if(!o||!a||"function"!=typeof a.then)throw a;return a.then(function a(){try{return e(g,r,n,i,t,o,s)}catch(c){if(!c||"function"!=typeof c.then)throw c;return c.then(function(){return e(g,r,n,i,t,o,s)},a)}})}}var O,C="<"+m,j="";for(var U in y){var I=y[U];if("function"!=typeof I||"class"===U||"className"===U){switch(U){case"children":O=I;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in y)continue;U="for";break;case"className":if("class"in y)continue;U="class";break;case"defaultChecked":U="checked";break;case"defaultSelected":U="selected";break;case"defaultValue":case"value":switch(U="value",m){case"textarea":O=I;continue;case"select":i=I;continue;case"option":i!=I||"selected"in y||(C+=" selected")}break;case"dangerouslySetInnerHTML":j=I&&I.__html;continue;case"style":"object"==typeof I&&(I=function(e){var t="";for(var r in e){var n=e[r];if(null!=n&&""!==n){var i="-"==r[0]?r:nn[r]||(nn[r]=r.replace(na,"-$&").toLowerCase()),a=";";"number"!=typeof n||i.startsWith("--")||ni.has(i)||(a="px;"),t=t+i+":"+n+a}}return t||void 0}(I));break;case"acceptCharset":U="accept-charset";break;case"httpEquiv":U="http-equiv";break;default:if(r8.test(U))U=U.replace(r8,"$1:$2").toLowerCase();else{if(r4.test(U))continue;("-"===U[4]||ne.has(U))&&null!=I?I+="":n?r7.test(U)&&(U="panose1"===U?"panose-1":U.replace(/([A-Z])/g,"-$1").toLowerCase()):r9.test(U)&&(U=U.toLowerCase())}}null!=I&&!1!==I&&(C=!0===I||""===I?C+" "+U:C+" "+U+'="'+("string"==typeof I?nr(I):I+"")+'"')}}if(r4.test(m))throw Error(m+" is not a valid HTML tag name in "+C+">");if(j||("string"==typeof O?j=nr(O):null!=O&&!1!==O&&!0!==O&&(j=e(O,r,"svg"===m||"foreignObject"!==m&&n,i,t,o,s))),nu&&nu(t),t.__=null,nd&&nd(t),!j&&nb.has(m))return C+"/>";var N="</"+m+">",H=C+">";return nh(j)?[H].concat(j,[N]):"string"!=typeof j?[H,j,N]:H+j+N}(e,np,!1,void 0,i,!1,void 0);return nh(a)?a.join(""):a}catch(e){if(e.then)throw Error('Use "renderToStringAsync" for suspenseful rendering.');throw e}finally{rT.__c&&rT.__c(e,nf),rT.__s=n,nf.length=0}}(e)}</div></body></html>`}}function nA(e){let{url:t,theme:r,query:n,cookies:i,pages:a,providers:o}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:n,signinUrl:i,callbackUrl:a})=>(e[t]={id:t,name:r,type:n,signinUrl:i,callbackUrl:a},e),{})}),signin(t,s){if(t)throw new M("Unsupported action");if(a?.signIn){let t=`${a.signIn}${a.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return s&&(t=`${t}&${new URLSearchParams({error:s})}`),{redirect:t,cookies:i}}let c=o?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),u="";if(c){let{simpleWebAuthnBrowserVersion:e}=c;u=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return nS({cookies:i,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:n,theme:i,email:a,error:o}=e;"undefined"!=typeof document&&i?.brandColor&&document.documentElement.style.setProperty("--brand-color",i.brandColor),"undefined"!=typeof document&&i?.buttonText&&document.documentElement.style.setProperty("--button-text-color",i.buttonText);let s=o&&(nE[o]??nE.default),c=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return n_("div",{className:"signin",children:[i?.brandColor&&n_("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${i.brandColor}}`}}),i?.buttonText&&n_("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${i.buttonText}
        }
      `}}),n_("div",{className:"card",children:[s&&n_("div",{className:"error",children:n_("p",{children:s})}),i?.logo&&n_("img",{src:i.logo,alt:"Logo",className:"logo"}),r.map((e,i)=>{let o,s,c;("oauth"===e.type||"oidc"===e.type)&&({bg:o="#fff",brandColor:s,logo:c=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let u=s??o??"#fff";return n_("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?n_("form",{action:e.signinUrl,method:"POST",children:[n_("input",{type:"hidden",name:"csrfToken",value:t}),n&&n_("input",{type:"hidden",name:"callbackUrl",value:n}),n_("button",{type:"submit",className:"button",style:{"--provider-brand-color":u},tabIndex:0,children:[n_("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),c&&n_("img",{loading:"lazy",height:24,src:c})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i>0&&"email"!==r[i-1].type&&"credentials"!==r[i-1].type&&"webauthn"!==r[i-1].type&&n_("hr",{}),"email"===e.type&&n_("form",{action:e.signinUrl,method:"POST",children:[n_("input",{type:"hidden",name:"csrfToken",value:t}),n_("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),n_("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:a,placeholder:"<EMAIL>",required:!0}),n_("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&n_("form",{action:e.callbackUrl,method:"POST",children:[n_("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>n_("div",{children:[n_("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),n_("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),n_("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&n_("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[n_("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>n_("div",{children:[n_("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),n_("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),n_("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i+1<r.length&&n_("hr",{})]},e.id)})]}),c&&n_(rz,{children:n_("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${nv})(authURL, "${c}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:s,...n}),title:"Sign In",headTags:u})},signout:()=>a?.signOut?{redirect:a.signOut,cookies:i}:nS({cookies:i,theme:r,html:function(e){let{url:t,csrfToken:r,theme:n}=e;return n_("div",{className:"signout",children:[n?.brandColor&&n_("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n.brandColor}
        }
      `}}),n?.buttonText&&n_("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),n_("div",{className:"card",children:[n?.logo&&n_("img",{src:n.logo,alt:"Logo",className:"logo"}),n_("h1",{children:"Signout"}),n_("p",{children:"Are you sure you want to sign out?"}),n_("form",{action:t?.toString(),method:"POST",children:[n_("input",{type:"hidden",name:"csrfToken",value:r}),n_("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>a?.verifyRequest?{redirect:`${a.verifyRequest}${t?.search??""}`,cookies:i}:nS({cookies:i,theme:r,html:function(e){let{url:t,theme:r}=e;return n_("div",{className:"verify-request",children:[r.brandColor&&n_("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),n_("div",{className:"card",children:[r.logo&&n_("img",{src:r.logo,alt:"Logo",className:"logo"}),n_("h1",{children:"Check your email"}),n_("p",{children:"A sign in link has been sent to your email address."}),n_("p",{children:n_("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>a?.error?{redirect:`${a.error}${a.error.includes("?")?"&":"?"}error=${e}`,cookies:i}:nS({cookies:i,theme:r,...function(e){let{url:t,error:r="default",theme:n}=e,i=`${t}/signin`,a={default:{status:200,heading:"Error",message:n_("p",{children:n_("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:n_("div",{children:[n_("p",{children:"There is a problem with the server configuration."}),n_("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:n_("div",{children:[n_("p",{children:"You do not have permission to sign in."}),n_("p",{children:n_("a",{className:"button",href:i,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:n_("div",{children:[n_("p",{children:"The sign in link is no longer valid."}),n_("p",{children:"It may have been used already or it may have expired."})]}),signin:n_("a",{className:"button",href:i,children:"Sign in"})}},{status:o,heading:s,message:c,signin:u}=a[r]??a.default;return{status:o,html:n_("div",{className:"error",children:[n?.brandColor&&n_("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n?.brandColor}
        }
      `}}),n_("div",{className:"card",children:[n?.logo&&n_("img",{src:n?.logo,alt:"Logo",className:"logo"}),n_("h1",{children:s}),n_("div",{className:"message",children:c}),u]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function nx(e,t=Date.now()){return new Date(t+1e3*e)}async function nP(e,t,r,n){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:i,jwt:a,events:o,session:{strategy:s,generateSessionToken:c}}=n;if(!i)return{user:t,account:r};let u=r,{createUser:l,updateUser:d,getUser:p,getUserByAccount:f,getUserByEmail:h,linkAccount:m,createSession:y,getSessionAndUser:b,deleteSession:g}=i,w=null,_=null,v=!1,E="jwt"===s;if(e)if(E)try{let t=n.cookies.sessionToken.name;(w=await a.decode({...a,token:e,salt:t}))&&"sub"in w&&w.sub&&(_=await p(w.sub))}catch{}else{let t=await b(e);t&&(w=t.session,_=t.user)}if("email"===u.type){let r=await h(t.email);return r?(_?.id!==r.id&&!E&&e&&await g(e),_=await d({id:r.id,emailVerified:new Date}),await o.updateUser?.({user:_})):(_=await l({...t,emailVerified:new Date}),await o.createUser?.({user:_}),v=!0),{session:w=E?{}:await y({sessionToken:c(),userId:_.id,expires:nx(n.session.maxAge)}),user:_,isNewUser:v}}if("webauthn"===u.type){let e=await f({providerAccountId:u.providerAccountId,provider:u.provider});if(e){if(_){if(e.id===_.id){let e={...u,userId:_.id};return{session:w,user:_,isNewUser:v,account:e}}throw new X("The account is already associated with another user",{provider:u.provider})}w=E?{}:await y({sessionToken:c(),userId:e.id,expires:nx(n.session.maxAge)});let t={...u,userId:e.id};return{session:w,user:e,isNewUser:v,account:t}}{if(_){await m({...u,userId:_.id}),await o.linkAccount?.({user:_,account:u,profile:t});let e={...u,userId:_.id};return{session:w,user:_,isNewUser:v,account:e}}if(t.email?await h(t.email):null)throw new X("Another account already exists with the same e-mail address",{provider:u.provider});_=await l({...t}),await o.createUser?.({user:_}),await m({...u,userId:_.id}),await o.linkAccount?.({user:_,account:u,profile:t}),w=E?{}:await y({sessionToken:c(),userId:_.id,expires:nx(n.session.maxAge)});let e={...u,userId:_.id};return{session:w,user:_,isNewUser:!0,account:e}}}let k=await f({providerAccountId:u.providerAccountId,provider:u.provider});if(k){if(_){if(k.id===_.id)return{session:w,user:_,isNewUser:v};throw new U("The account is already associated with another user",{provider:u.provider})}return{session:w=E?{}:await y({sessionToken:c(),userId:k.id,expires:nx(n.session.maxAge)}),user:k,isNewUser:v}}{let{provider:e}=n,{type:r,provider:i,providerAccountId:a,userId:s,...d}=u;if(u=Object.assign(e.account(d)??{},{providerAccountId:a,provider:i,type:r,userId:s}),_)return await m({...u,userId:_.id}),await o.linkAccount?.({user:_,account:u,profile:t}),{session:w,user:_,isNewUser:v};let p=t.email?await h(t.email):null;if(p){let e=n.provider;if(e?.allowDangerousEmailAccountLinking)_=p,v=!1;else throw new U("Another account already exists with the same e-mail address",{provider:u.provider})}else _=await l({...t,emailVerified:null}),v=!0;return await o.createUser?.({user:_}),await m({...u,userId:_.id}),await o.linkAccount?.({user:_,account:u,profile:t}),{session:w=E?{}:await y({sessionToken:c(),userId:_.id,expires:nx(n.session.maxAge)}),user:_,isNewUser:v}}}function nR(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(a="oauth4webapi/v3.5.2");let nT="ERR_INVALID_ARG_VALUE",nO="ERR_INVALID_ARG_TYPE";function nC(e,t,r){let n=TypeError(e,{cause:r});return Object.assign(n,{code:t}),n}let nj=Symbol(),nU=Symbol(),nI=Symbol(),nN=Symbol(),nH=Symbol(),n$=Symbol(),nD=Symbol(),nL=new TextEncoder,nM=new TextDecoder;function nW(e){return"string"==typeof e?nL.encode(e):nM.decode(e)}function nK(e){return"string"==typeof e?s(e):o(e)}o=Uint8Array.prototype.toBase64?e=>(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e.toBase64({alphabet:"base64url",omitPadding:!0})):e=>{e instanceof ArrayBuffer&&(e=new Uint8Array(e));let t=[];for(let r=0;r<e.byteLength;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},s=Uint8Array.fromBase64?e=>{try{return Uint8Array.fromBase64(e,{alphabet:"base64url"})}catch(e){throw nC("The input to be decoded is not correctly encoded.",nT,e)}}:e=>{try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw nC("The input to be decoded is not correctly encoded.",nT,e)}};class nJ extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=i0,Error.captureStackTrace?.(this,this.constructor)}}class nB extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function nF(e,t,r){return new nB(e,{code:t,cause:r})}function nz(e,t){if(!(e instanceof CryptoKey))throw nC(`${t} must be a CryptoKey`,nO)}function nq(e,t){if(nz(e,t),"private"!==e.type)throw nC(`${t} must be a private CryptoKey`,nT)}function nG(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function nV(e){nR(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e);if(a&&!t.has("user-agent")&&t.set("user-agent",a),t.has("authorization"))throw nC('"options.headers" must not include the "authorization" header name',nT);return t}function nX(e){if("function"==typeof e&&(e=e()),!(e instanceof AbortSignal))throw nC('"options.signal" must return or be an instance of AbortSignal',nO);return e}function nY(e){return e.includes("//")?e.replace("//","/"):e}async function nZ(e,t,r,n){if(!(e instanceof URL))throw nC(`"${t}" must be an instance of URL`,nO);iu(e,n?.[nj]!==!0);let i=r(new URL(e.href)),a=nV(n?.headers);return a.set("accept","application/json"),(n?.[nN]||fetch)(i.href,{body:void 0,headers:Object.fromEntries(a.entries()),method:"GET",redirect:"manual",signal:n?.signal?nX(n.signal):void 0})}async function nQ(e,t){return nZ(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=nY(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":var r,n;n=".well-known/oauth-authorization-server","/"===(r=e).pathname?r.pathname=n:r.pathname=nY(`${n}/${r.pathname}`);break;default:throw nC('"options.algorithm" must be "oidc" (default), or "oauth2"',nT)}return e},t)}function n0(e,t,r,n,i){try{if("number"!=typeof e||!Number.isFinite(e))throw nC(`${r} must be a number`,nO,i);if(e>0)return;if(t){if(0!==e)throw nC(`${r} must be a non-negative number`,nT,i);return}throw nC(`${r} must be a positive number`,nT,i)}catch(e){if(n)throw nF(e.message,n,i);throw e}}function n1(e,t,r,n){try{if("string"!=typeof e)throw nC(`${t} must be a string`,nO,n);if(0===e.length)throw nC(`${t} must not be empty`,nT,n)}catch(e){if(r)throw nF(e.message,r,n);throw e}}async function n2(e,t){if(!(e instanceof URL)&&e!==aR)throw nC('"expectedIssuerIdentifier" must be an instance of URL',nO);if(!nR(t,Response))throw nC('"response" must be an instance of Response',nO);if(200!==t.status)throw nF('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',i8,t);as(t);let r=await ax(t);if(n1(r.issuer,'"response" body "issuer" property',i5,{body:r}),e!==aR&&new URL(r.issuer).href!==e.href)throw nF('"response" body "issuer" property does not match the expected value',ar,{expected:e.href,body:r,attribute:"issuer"});return r}function n6(e){var t=e,r="application/json";if(iO(t)!==r)throw n5(t,r)}function n5(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return nF(r,i4,e)}function n3(){return nK(crypto.getRandomValues(new Uint8Array(32)))}async function n4(e){return n1(e,"codeVerifier"),nK(await crypto.subtle.digest("SHA-256",nW(e)))}function n8(e){switch(e.algorithm.name){case"RSA-PSS":switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new nJ("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new nJ("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"ECDSA":switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new nJ("unsupported EcKeyAlgorithm namedCurve",{cause:e})}case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nJ("unsupported CryptoKey algorithm name",{cause:e})}}function n9(e){let t=e?.[nU];return"number"==typeof t&&Number.isFinite(t)?t:0}function n7(e){let t=e?.[nI];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function ie(){return Math.floor(Date.now()/1e3)}function it(e){if("object"!=typeof e||null===e)throw nC('"as" must be an object',nO);n1(e.issuer,'"as.issuer"')}function ir(e){if("object"!=typeof e||null===e)throw nC('"client" must be an object',nO);n1(e.client_id,'"client.client_id"')}function ii(e,t){let r=ie()+n9(t);return{jti:n3(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function ia(e,t,r){if(!r.usages.includes("sign"))throw nC('CryptoKey instances used for signing assertions must include "sign" in their "usages"',nT);let n=`${nK(nW(JSON.stringify(e)))}.${nK(nW(JSON.stringify(t)))}`,i=nK(await crypto.subtle.sign(ap(r),r,nW(n)));return`${n}.${i}`}async function io(e){let{kty:t,e:r,n,x:i,y:a,crv:o}=await crypto.subtle.exportKey("jwk",e),s={kty:t,e:r,n,x:i,y:a,crv:o};return c.set(e,s),s}async function is(e){return(c||=new WeakMap).get(e)||io(e)}let ic=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function iu(e,t){if(t&&"https:"!==e.protocol)throw nF("only requests to HTTPS are allowed",i9,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw nF("only HTTP and HTTPS requests are allowed",i7,e)}function il(e,t,r,n){let i;if("string"!=typeof e||!(i=ic(e)))throw nF(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?ai:aa,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return iu(i,n),i}function id(e,t,r,n){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?il(e.mtls_endpoint_aliases[t],t,r,n):il(e[t],t,r,n)}class ip extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=iQ,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class ih extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=i1,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class im extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=iZ,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let iy="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",ib=RegExp("^[,\\s]*("+iy+")\\s(.*)"),ig=RegExp("^[,\\s]*("+iy+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),iw=RegExp("^[,\\s]*"+("("+iy+")\\s*=\\s*(")+iy+")[,\\s]*(.*)"),i_=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function iv(e){if(e.status>399&&e.status<500){as(e),n6(e);try{let t=await e.clone().json();if(nG(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function iE(e,t,r){if(e.status!==t){let t;if(t=await iv(e))throw await e.body?.cancel(),new ip("server responded with an error in the response body",{cause:t,response:e});throw nF(`"response" is not a conform ${r} response (unexpected HTTP status code)`,i8,e)}}function ik(e){if(!iJ.has(e))throw nC('"options.DPoP" is not a valid DPoPHandle',nT)}async function iS(e,t,r,n,i,a){if(n1(e,'"accessToken"'),!(r instanceof URL))throw nC('"url" must be an instance of URL',nO);iu(r,a?.[nj]!==!0),n=nV(n),a?.DPoP&&(ik(a.DPoP),await a.DPoP.addProof(r,n,t.toUpperCase(),e)),n.set("authorization",`${n.has("dpop")?"DPoP":"Bearer"} ${e}`);let o=await (a?.[nN]||fetch)(r.href,{body:i,headers:Object.fromEntries(n.entries()),method:t,redirect:"manual",signal:a?.signal?nX(a.signal):void 0});return a?.DPoP?.cacheNonce(o),o}async function iA(e,t,r,n){it(e),ir(t);let i=id(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,n?.[nj]!==!0),a=nV(n?.headers);return t.userinfo_signed_response_alg?a.set("accept","application/jwt"):(a.set("accept","application/json"),a.append("accept","application/jwt")),iS(r,"GET",i,a,null,{...n,[nU]:n9(t)})}function ix(e,t,r,n){(u||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return ie()-this.uat}}),n&&Object.assign(n,{jwks:structuredClone(t),uat:r})}function iP(e,t){u?.delete(e),delete t?.jwks,delete t?.uat}async function iR(e,t,r){var n;let i,a,o,{alg:s,kid:c}=r;if(function(e){if(!al(e.alg))throw new nJ('unsupported JWS "alg" identifier',{cause:{alg:e.alg}})}(r),!u?.has(e)&&!("object"!=typeof(n=t?.[nD])||null===n||!("uat"in n)||"number"!=typeof n.uat||ie()-n.uat>=300)&&"jwks"in n&&nG(n.jwks)&&Array.isArray(n.jwks.keys)&&Array.prototype.every.call(n.jwks.keys,nG)&&ix(e,t?.[nD].jwks,t?.[nD].uat),u?.has(e)){if({jwks:i,age:a}=u.get(e),a>=300)return iP(e,t?.[nD]),iR(e,t,r)}else i=await ac(e,t).then(au),a=0,ix(e,i,ie(),t?.[nD]);switch(s.slice(0,2)){case"RS":case"PS":o="RSA";break;case"ES":o="EC";break;case"Ed":o="OKP";break;default:throw new nJ("unsupported JWS algorithm",{cause:{alg:s}})}let l=i.keys.filter(e=>{if(e.kty!==o||void 0!==c&&c!==e.kid||void 0!==e.alg&&s!==e.alg||void 0!==e.use&&"sig"!==e.use||e.key_ops?.includes("verify")===!1)return!1;switch(!0){case"ES256"===s&&"P-256"!==e.crv:case"ES384"===s&&"P-384"!==e.crv:case"ES512"===s&&"P-521"!==e.crv:case"Ed25519"===s&&"Ed25519"!==e.crv:case"EdDSA"===s&&"Ed25519"!==e.crv:return!1}return!0}),{0:d,length:p}=l;if(!p){if(a>=60)return iP(e,t?.[nD]),iR(e,t,r);throw nF("error when selecting a JWT verification key, no applicable keys found",an,{header:r,candidates:l,jwks_uri:new URL(e.jwks_uri)})}if(1!==p)throw nF('error when selecting a JWT verification key, multiple applicable keys found, a "kid" JWT Header Parameter is required',an,{header:r,candidates:l,jwks_uri:new URL(e.jwks_uri)});return aS(s,d)}let iT=Symbol();function iO(e){return e.headers.get("content-type")?.split(";")[0]}async function iC(e,t,r,n,i){let a;if(it(e),ir(t),!nR(n,Response))throw nC('"response" must be an instance of Response',nO);if(iD(n),200!==n.status)throw nF('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',i8,n);if(as(n),"application/jwt"===iO(n)){let{claims:r,jwt:o}=await ah(await n.text(),aw.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),n9(t),n7(t),i?.[n$]).then(iL.bind(void 0,t.client_id)).then(iW.bind(void 0,e));iN.set(n,o),a=r}else{if(t.userinfo_signed_response_alg)throw nF("JWT UserInfo Response expected",i2,n);a=await ax(n)}if(n1(a.sub,'"response" body "sub" property',i5,{body:a}),r===iT);else if(n1(r,'"expectedSubject"'),a.sub!==r)throw nF('unexpected "response" body "sub" property value',ar,{expected:r,body:a,attribute:"sub"});return a}async function ij(e,t,r,n,i,a,o){return await r(e,t,i,a),a.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(o?.[nN]||fetch)(n.href,{body:i,headers:Object.fromEntries(a.entries()),method:"POST",redirect:"manual",signal:o?.signal?nX(o.signal):void 0})}async function iU(e,t,r,n,i,a){let o=id(e,"token_endpoint",t.use_mtls_endpoint_aliases,a?.[nj]!==!0);i.set("grant_type",n);let s=nV(a?.headers);s.set("accept","application/json"),a?.DPoP!==void 0&&(ik(a.DPoP),await a.DPoP.addProof(o,s,"POST"));let c=await ij(e,t,r,o,i,s,a);return a?.DPoP?.cacheNonce(c),c}let iI=new WeakMap,iN=new WeakMap;function iH(e){if(!e.id_token)return;let t=iI.get(e);if(!t)throw nC('"ref" was already garbage collected or did not resolve from the proper sources',nT);return t}async function i$(e,t,r,n,i){if(it(e),ir(t),!nR(r,Response))throw nC('"response" must be an instance of Response',nO);iD(r),await iE(r,200,"Token Endpoint"),as(r);let a=await ax(r);if(n1(a.access_token,'"response" body "access_token" property',i5,{body:a}),n1(a.token_type,'"response" body "token_type" property',i5,{body:a}),a.token_type=a.token_type.toLowerCase(),"dpop"!==a.token_type&&"bearer"!==a.token_type)throw new nJ("unsupported `token_type` value",{cause:{body:a}});if(void 0!==a.expires_in){let e="number"!=typeof a.expires_in?parseFloat(a.expires_in):a.expires_in;n0(e,!1,'"response" body "expires_in" property',i5,{body:a}),a.expires_in=e}if(void 0!==a.refresh_token&&n1(a.refresh_token,'"response" body "refresh_token" property',i5,{body:a}),void 0!==a.scope&&"string"!=typeof a.scope)throw nF('"response" body "scope" property must be a string',i5,{body:a});if(void 0!==a.id_token){n1(a.id_token,'"response" body "id_token" property',i5,{body:a});let o=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&o.push("auth_time"),void 0!==t.default_max_age&&(n0(t.default_max_age,!1,'"client.default_max_age"'),o.push("auth_time")),n?.length&&o.push(...n);let{claims:s,jwt:c}=await ah(a.id_token,aw.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),n9(t),n7(t),i?.[n$]).then(iz.bind(void 0,o)).then(iK.bind(void 0,e)).then(iM.bind(void 0,t.client_id));if(Array.isArray(s.aud)&&1!==s.aud.length){if(void 0===s.azp)throw nF('ID Token "aud" (audience) claim includes additional untrusted audiences',at,{claims:s,claim:"aud"});if(s.azp!==t.client_id)throw nF('unexpected ID Token "azp" (authorized party) claim value',at,{expected:t.client_id,claims:s,claim:"azp"})}void 0!==s.auth_time&&n0(s.auth_time,!1,'ID Token "auth_time" (authentication time)',i5,{claims:s}),iN.set(r,c),iI.set(a,s)}return a}function iD(e){let t;if(t=function(e){if(!nR(e,Response))throw nC('"response" must be an instance of Response',nO);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],n=t;for(;n;){let e,t=n.match(ib),i=t?.["1"].toLowerCase();if(n=t?.["2"],!i)return;let a={};for(;n;){let r,i;if(t=n.match(ig)){if([,r,i,n]=t,i.includes("\\"))try{i=JSON.parse(`"${i}"`)}catch{}a[r.toLowerCase()]=i;continue}if(t=n.match(iw)){[,r,i,n]=t,a[r.toLowerCase()]=i;continue}if(t=n.match(i_)){if(Object.keys(a).length)break;[,e,n]=t;break}return}let o={scheme:i,parameters:a};e&&(o.token68=e),r.push(o)}if(r.length)return r}(e))throw new im("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function iL(e,t){return void 0!==t.claims.aud?iM(e,t):t}function iM(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw nF('unexpected JWT "aud" (audience) claim value',at,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw nF('unexpected JWT "aud" (audience) claim value',at,{expected:e,claims:t.claims,claim:"aud"});return t}function iW(e,t){return void 0!==t.claims.iss?iK(e,t):t}function iK(e,t){let r=e[aT]?.(t)??e.issuer;if(t.claims.iss!==r)throw nF('unexpected JWT "iss" (issuer) claim value',at,{expected:r,claims:t.claims,claim:"iss"});return t}let iJ=new WeakSet;async function iB(e,t,r,n,i,a,o){if(it(e),ir(t),!iJ.has(n))throw nC('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',nT);n1(i,'"redirectUri"');let s=a_(n,"code");if(!s)throw nF('no authorization code in "callbackParameters"',i5);let c=new URLSearchParams(o?.additionalParameters);return c.set("redirect_uri",i),c.set("code",s),a!==aP&&(n1(a,'"codeVerifier"'),c.set("code_verifier",a)),iU(e,t,r,"authorization_code",c,o)}let iF={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function iz(e,t){for(let r of e)if(void 0===t.claims[r])throw nF(`JWT "${r}" (${iF[r]}) claim missing`,i5,{claims:t.claims});return t}let iq=Symbol(),iG=Symbol();async function iV(e,t,r,n){return"string"==typeof n?.expectedNonce||"number"==typeof n?.maxAge||n?.requireIdToken?iX(e,t,r,n.expectedNonce,n.maxAge,{[n$]:n[n$]}):iY(e,t,r,n)}async function iX(e,t,r,n,i,a){let o=[];switch(n){case void 0:n=iq;break;case iq:break;default:n1(n,'"expectedNonce" argument'),o.push("nonce")}switch(i??=t.default_max_age){case void 0:i=iG;break;case iG:break;default:n0(i,!1,'"maxAge" argument'),o.push("auth_time")}let s=await i$(e,t,r,o,a);n1(s.id_token,'"response" body "id_token" property',i5,{body:s});let c=iH(s);if(i!==iG){let e=ie()+n9(t),r=n7(t);if(c.auth_time+i<e-r)throw nF("too much time has elapsed since the last End-User authentication",ae,{claims:c,now:e,tolerance:r,claim:"auth_time"})}if(n===iq){if(void 0!==c.nonce)throw nF('unexpected ID Token "nonce" claim value',at,{expected:void 0,claims:c,claim:"nonce"})}else if(c.nonce!==n)throw nF('unexpected ID Token "nonce" claim value',at,{expected:n,claims:c,claim:"nonce"});return s}async function iY(e,t,r,n){let i=await i$(e,t,r,void 0,n),a=iH(i);if(a){if(void 0!==t.default_max_age){n0(t.default_max_age,!1,'"client.default_max_age"');let e=ie()+n9(t),r=n7(t);if(a.auth_time+t.default_max_age<e-r)throw nF("too much time has elapsed since the last End-User authentication",ae,{claims:a,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==a.nonce)throw nF('unexpected ID Token "nonce" claim value',at,{expected:void 0,claims:a,claim:"nonce"})}return i}let iZ="OAUTH_WWW_AUTHENTICATE_CHALLENGE",iQ="OAUTH_RESPONSE_BODY_ERROR",i0="OAUTH_UNSUPPORTED_OPERATION",i1="OAUTH_AUTHORIZATION_RESPONSE_ERROR",i2="OAUTH_JWT_USERINFO_EXPECTED",i6="OAUTH_PARSE_ERROR",i5="OAUTH_INVALID_RESPONSE",i3="OAUTH_INVALID_REQUEST",i4="OAUTH_RESPONSE_IS_NOT_JSON",i8="OAUTH_RESPONSE_IS_NOT_CONFORM",i9="OAUTH_HTTP_REQUEST_FORBIDDEN",i7="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",ae="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",at="OAUTH_JWT_CLAIM_COMPARISON_FAILED",ar="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",an="OAUTH_KEY_SELECTION_FAILED",ai="OAUTH_MISSING_SERVER_METADATA",aa="OAUTH_INVALID_SERVER_METADATA";function ao(e,t){if("string"!=typeof t.header.typ||t.header.typ.toLowerCase().replace(/^application\//,"")!==e)throw nF('unexpected JWT "typ" header parameter value',i5,{header:t.header});return t}function as(e){if(e.bodyUsed)throw nC('"response" body has been used already',nT)}async function ac(e,t){it(e);let r=id(e,"jwks_uri",!1,t?.[nj]!==!0),n=nV(t?.headers);return n.set("accept","application/json"),n.append("accept","application/jwk-set+json"),(t?.[nN]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(n.entries()),method:"GET",redirect:"manual",signal:t?.signal?nX(t.signal):void 0})}async function au(e){if(!nR(e,Response))throw nC('"response" must be an instance of Response',nO);if(200!==e.status)throw nF('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',i8,e);as(e);let t=await ax(e,e=>(function(e,...t){if(!t.includes(iO(e)))throw n5(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw nF('"response" body "keys" property must be an array',i5,{body:t});if(!Array.prototype.every.call(t.keys,nG))throw nF('"response" body "keys" property members must be JWK formatted objects',i5,{body:t});return t}function al(e){switch(e){case"PS256":case"ES256":case"RS256":case"PS384":case"ES384":case"RS384":case"PS512":case"ES512":case"RS512":case"Ed25519":case"EdDSA":return!0;default:return!1}}function ad(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new nJ(`unsupported ${t.name} modulusLength`,{cause:e})}function ap(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new nJ("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(ad(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new nJ("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return ad(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new nJ("unsupported CryptoKey algorithm name",{cause:e})}async function af(e,t,r,n){let i=nW(`${e}.${t}`),a=ap(r);if(!await crypto.subtle.verify(a,r,n,i))throw nF("JWT signature verification failed",i5,{key:r,data:i,signature:n,algorithm:a})}async function ah(e,t,r,n,i){let a,o,{0:s,1:c,length:u}=e.split(".");if(5===u)if(void 0!==i)e=await i(e),{0:s,1:c,length:u}=e.split(".");else throw new nJ("JWE decryption is not configured",{cause:e});if(3!==u)throw nF("Invalid JWT",i5,e);try{a=JSON.parse(nW(nK(s)))}catch(e){throw nF("failed to parse JWT Header body as base64url encoded JSON",i6,e)}if(!nG(a))throw nF("JWT Header must be a top level object",i5,e);if(t(a),void 0!==a.crit)throw new nJ('no JWT "crit" header parameter extensions are supported',{cause:{header:a}});try{o=JSON.parse(nW(nK(c)))}catch(e){throw nF("failed to parse JWT Payload body as base64url encoded JSON",i6,e)}if(!nG(o))throw nF("JWT Payload must be a top level object",i5,e);let l=ie()+r;if(void 0!==o.exp){if("number"!=typeof o.exp)throw nF('unexpected JWT "exp" (expiration time) claim type',i5,{claims:o});if(o.exp<=l-n)throw nF('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',ae,{claims:o,now:l,tolerance:n,claim:"exp"})}if(void 0!==o.iat&&"number"!=typeof o.iat)throw nF('unexpected JWT "iat" (issued at) claim type',i5,{claims:o});if(void 0!==o.iss&&"string"!=typeof o.iss)throw nF('unexpected JWT "iss" (issuer) claim type',i5,{claims:o});if(void 0!==o.nbf){if("number"!=typeof o.nbf)throw nF('unexpected JWT "nbf" (not before) claim type',i5,{claims:o});if(o.nbf>l+n)throw nF('unexpected JWT "nbf" (not before) claim value',ae,{claims:o,now:l,tolerance:n,claim:"nbf"})}if(void 0!==o.aud&&"string"!=typeof o.aud&&!Array.isArray(o.aud))throw nF('unexpected JWT "aud" (audience) claim type',i5,{claims:o});return{header:a,claims:o,jwt:e}}async function am(e,t,r){let n;switch(t.alg){case"RS256":case"PS256":case"ES256":n="SHA-256";break;case"RS384":case"PS384":case"ES384":n="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":n="SHA-512";break;default:throw new nJ(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let i=await crypto.subtle.digest(n,nW(e));return nK(i.slice(0,i.byteLength/2))}async function ay(e,t,r,n){return t===await am(e,r,n)}async function ab(e){if(e.bodyUsed)throw nC("form_post Request instances must contain a readable body",nT,{cause:e});return e.text()}async function ag(e){if("POST"!==e.method)throw nC("form_post responses are expected to use the POST method",nT,{cause:e});if("application/x-www-form-urlencoded"!==iO(e))throw nC("form_post responses are expected to use the application/x-www-form-urlencoded content-type",nT,{cause:e});return ab(e)}function aw(e,t,r,n){if(void 0!==e){if("string"==typeof e?n.alg!==e:!e.includes(n.alg))throw nF('unexpected JWT "alg" header parameter',i5,{header:n,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(n.alg))throw nF('unexpected JWT "alg" header parameter',i5,{header:n,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?n.alg!==r:"function"==typeof r?!r(n.alg):!r.includes(n.alg))throw nF('unexpected JWT "alg" header parameter',i5,{header:n,expected:r,reason:"default value"});return}throw nF('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function a_(e,t){let{0:r,length:n}=e.getAll(t);if(n>1)throw nF(`"${t}" parameter must be provided only once`,i5);return r}let av=Symbol(),aE=Symbol();function ak(e,t,r,n){var i;if(it(e),ir(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw nC('"parameters" must be an instance of URLSearchParams, or URL',nO);if(a_(r,"response"))throw nF('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',i5,{parameters:r});let a=a_(r,"iss"),o=a_(r,"state");if(!a&&e.authorization_response_iss_parameter_supported)throw nF('response parameter "iss" (issuer) missing',i5,{parameters:r});if(a&&a!==e.issuer)throw nF('unexpected "iss" (issuer) response parameter value',i5,{expected:e.issuer,parameters:r});switch(n){case void 0:case aE:if(void 0!==o)throw nF('unexpected "state" response parameter encountered',i5,{expected:void 0,parameters:r});break;case av:break;default:if(n1(n,'"expectedState" argument'),o!==n)throw nF(void 0===o?'response parameter "state" missing':'unexpected "state" response parameter value',i5,{expected:n,parameters:r})}if(a_(r,"error"))throw new ih("authorization response from the server is an error",{cause:r});let s=a_(r,"id_token"),c=a_(r,"token");if(void 0!==s||void 0!==c)throw new nJ("implicit and hybrid flows are not supported");return i=new URLSearchParams(r),iJ.add(i),i}async function aS(e,t){let{ext:r,key_ops:n,use:i,...a}=t;return crypto.subtle.importKey("jwk",a,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nJ("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}function aA(e){let t=new URL(e);return t.search="",t.hash="",t.href}async function ax(e,t=n6){let r;try{r=await e.json()}catch(r){throw t(e),nF('failed to parse "response" body as JSON',i6,r)}if(!nG(r))throw nF('"response" body must be a top level object',i5,{body:r});return r}let aP=Symbol(),aR=Symbol(),aT=Symbol();async function aO(e,t,r){let{cookies:n,logger:i}=r,a=n[e],o=new Date;o.setTime(o.getTime()+9e5),i.debug(`CREATE_${e.toUpperCase()}`,{name:a.name,payload:t,COOKIE_TTL:900,expires:o});let s=await t2({...r.jwt,maxAge:900,token:{value:t},salt:a.name}),c={...a.options,expires:o};return{name:a.name,value:s,options:c}}async function aC(e,t,r){try{let{logger:n,cookies:i,jwt:a}=r;if(n.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new P(`${e} cookie was missing`);let o=await t6({...a,token:t,salt:i[e].name});if(o?.value)return o.value;throw Error("Invalid cookie")}catch(t){throw new P(`${e} value could not be parsed`,{cause:t})}}function aj(e,t,r){let{logger:n,cookies:i}=t,a=i[e];n.debug(`CLEAR_${e.toUpperCase()}`,{cookie:a}),r.push({name:a.name,value:"",options:{...i[e].options,maxAge:0}})}function aU(e,t){return async function(r,n,i){let{provider:a,logger:o}=i;if(!a?.checks?.includes(e))return;let s=r?.[i.cookies[t].name];o.debug(`USE_${t.toUpperCase()}`,{value:s});let c=await aC(t,s,i);return aj(t,i,n),c}}let aI={async create(e){let t=n3(),r=await n4(t);return{cookie:await aO("pkceCodeVerifier",t,e),value:r}},use:aU("pkce","pkceCodeVerifier")},aN="encodedState",aH={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new P("State data was provided but the provider is not configured to use state");return}let n={origin:t,random:n3()},i=await t2({secret:e.jwt.secret,token:n,salt:aN,maxAge:900});return{cookie:await aO("state",i,e),value:i}},use:aU("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await t6({secret:t.jwt.secret,token:e,salt:aN});if(r)return r;throw Error("Invalid state")}catch(e){throw new P("State could not be decoded",{cause:e})}}},a$={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=n3();return{cookie:await aO("nonce",t,e),value:t}},use:aU("nonce","nonce")},aD="encodedWebauthnChallenge",aL={create:async(e,t,r)=>({cookie:await aO("webauthnChallenge",await t2({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:aD,maxAge:900}),e)}),async use(e,t,r){let n=t?.[e.cookies.webauthnChallenge.name],i=await aC("webauthnChallenge",n,e),a=await t6({secret:e.jwt.secret,token:i,salt:aD});if(aj("webauthnChallenge",e,r),!a)throw new P("WebAuthn challenge was missing");return a}};function aM(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function aW(e,t,r){let n,i,a,{logger:o,provider:s}=r,{token:c,userinfo:u}=s;if(c?.url&&"authjs.dev"!==c.url.host||u?.url&&"authjs.dev"!==u.url.host)n={issuer:s.issuer??"https://authjs.dev",token_endpoint:c?.url.toString(),userinfo_endpoint:u?.url.toString()};else{let e=new URL(s.issuer),t=await nQ(e,{[nj]:!0,[nN]:s[rh]});if(!(n=await n2(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!n.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let l={client_id:s.clientId,...s.client};switch(l.token_endpoint_auth_method){case void 0:case"client_secret_basic":i=(e,t,r,n)=>{n.set("authorization",function(e,t){let r=aM(e),n=aM(t),i=btoa(`${r}:${n}`);return`Basic ${i}`}(s.clientId,s.clientSecret))};break;case"client_secret_post":var d;n1(d=s.clientSecret,'"clientSecret"'),i=(e,t,r,n)=>{r.set("client_id",t.client_id),r.set("client_secret",d)};break;case"client_secret_jwt":i=function(e,t){let r;n1(e,'"clientSecret"');let n=void 0;return async(t,i,a,o)=>{r||=await crypto.subtle.importKey("raw",nW(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let s={alg:"HS256"},c=ii(t,i);n?.(s,c);let u=`${nK(nW(JSON.stringify(s)))}.${nK(nW(JSON.stringify(c)))}`,l=await crypto.subtle.sign(r.algorithm,r,nW(u));a.set("client_id",i.client_id),a.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),a.set("client_assertion",`${u}.${nK(new Uint8Array(l))}`)}}(s.clientSecret);break;case"private_key_jwt":i=function(e,t){var r;let{key:n,kid:i}=(r=e)instanceof CryptoKey?{key:r}:r?.key instanceof CryptoKey?(void 0!==r.kid&&n1(r.kid,'"kid"'),{key:r.key,kid:r.kid}):{};return nq(n,'"clientPrivateKey.key"'),async(e,r,a,o)=>{let s={alg:n8(n),kid:i},c=ii(e,r);t?.[nH]?.(s,c),a.set("client_id",r.client_id),a.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),a.set("client_assertion",await ia(s,c,n))}}(s.token.clientPrivateKey,{[nH](e,t){t.aud=[n.issuer,n.token_endpoint]}});break;case"none":i=(e,t,r,n)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let p=[],f=await aH.use(t,p,r);try{a=ak(n,l,new URLSearchParams(e),s.checks.includes("state")?f:av)}catch(e){if(e instanceof ih){let t={providerId:s.id,...Object.fromEntries(e.cause.entries())};throw o.debug("OAuthCallbackError",t),new I("OAuth Provider returned an error",t)}throw e}let h=await aI.use(t,p,r),m=s.callbackUrl;!r.isOnRedirectProxy&&s.redirectProxyUrl&&(m=s.redirectProxyUrl);let y=await iB(n,l,i,a,m,h??"decoy",{[nj]:!0,[nN]:(...e)=>(s.checks.includes("pkce")||e[1].body.delete("code_verifier"),(s[rh]??fetch)(...e))});s.token?.conform&&(y=await s.token.conform(y.clone())??y);let b={},g="oidc"===s.type;if(s[rm])switch(s.id){case"microsoft-entra-id":case"azure-ad":{let e=await y.clone().json();if(e.error){let t={providerId:s.id,...e};throw new I(`OAuth Provider returned an error: ${e.error}`,t)}let{tid:t}=function(e){let t,r;if("string"!=typeof e)throw new eP("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:i}=e.split(".");if(5===i)throw new eP("Only JWTs using Compact JWS serialization can be decoded");if(3!==i)throw new eP("Invalid JWT");if(!n)throw new eP("JWTs must contain a payload");try{t=eg(n)}catch{throw new eP("Failed to base64url decode the payload")}try{r=JSON.parse(ef.decode(t))}catch{throw new eP("Failed to parse the decoded payload as JSON")}if(!eI(r))throw new eP("Invalid JWT Claims Set");return r}(e.id_token);if("string"==typeof t){let e=n.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(n.issuer.replace(e,t)),i=await nQ(r,{[nN]:s[rh]});n=await n2(r,i)}}}let w=await iV(n,l,y,{expectedNonce:await a$.use(t,p,r),requireIdToken:g});if(g){let t=iH(w);if(b=t,s[rm]&&"apple"===s.id)try{b.user=JSON.parse(e?.user)}catch{}if(!1===s.idToken){let e=await iA(n,l,w.access_token,{[nN]:s[rh],[nj]:!0});b=await iC(n,l,t.sub,e)}}else if(u?.request){let e=await u.request({tokens:w,provider:s});e instanceof Object&&(b=e)}else if(u?.url){let e=await iA(n,l,w.access_token,{[nN]:s[rh],[nj]:!0});b=await e.json()}else throw TypeError("No userinfo endpoint configured");return w.expires_in&&(w.expires_at=Math.floor(Date.now()/1e3)+Number(w.expires_in)),{...await aK(b,s,w,o),profile:b,cookies:p}}async function aK(e,t,r,n){try{let n=await t.profile(e,r);return{user:{...n,id:crypto.randomUUID(),email:n.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:n.id??crypto.randomUUID()}}}catch(r){n.debug("getProfile error details",e),n.error(new N(r,{provider:t.id}))}}async function aJ(e,t,r,n){let i=await aG(e,t,r),{cookie:a}=await aL.create(e,i.challenge,r);return{status:200,cookies:[...n??[],a],body:{action:"register",options:i},headers:{"Content-Type":"application/json"}}}async function aB(e,t,r,n){let i=await aq(e,t,r),{cookie:a}=await aL.create(e,i.challenge);return{status:200,cookies:[...n??[],a],body:{action:"authenticate",options:i},headers:{"Content-Type":"application/json"}}}async function aF(e,t,r){let n,{adapter:i,provider:a}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new b("Invalid WebAuthn Authentication response");let s=aY(aX(o.id)),c=await i.getAuthenticator(s);if(!c)throw new b(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:s})}`);let{challenge:u}=await aL.use(e,t.cookies,r);try{var l;let r=a.getRelayingParty(e,t);n=await a.simpleWebAuthn.verifyAuthenticationResponse({...a.verifyAuthenticationOptions,expectedChallenge:u,response:o,authenticator:{...l=c,credentialDeviceType:l.credentialDeviceType,transports:aZ(l.transports),credentialID:aX(l.credentialID),credentialPublicKey:aX(l.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new V(e)}let{verified:d,authenticationInfo:p}=n;if(!d)throw new V("WebAuthn authentication response could not be verified");try{let{newCounter:e}=p;await i.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new w(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:s,oldCounter:c.counter,newCounter:p.newCounter})}`,e)}let f=await i.getAccount(c.providerAccountId,a.id);if(!f)throw new b(`WebAuthn account not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId})}`);let h=await i.getUser(f.userId);if(!h)throw new b(`WebAuthn user not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId,userID:f.userId})}`);return{account:f,user:h}}async function az(e,t,r){var n;let i,{provider:a}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new b("Invalid WebAuthn Registration response");let{challenge:s,registerData:c}=await aL.use(e,t.cookies,r);if(!c)throw new b("Missing user registration data in WebAuthn challenge cookie");try{let r=a.getRelayingParty(e,t);i=await a.simpleWebAuthn.verifyRegistrationResponse({...a.verifyRegistrationOptions,expectedChallenge:s,response:o,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new V(e)}if(!i.verified||!i.registrationInfo)throw new V("WebAuthn registration response could not be verified");let u={providerAccountId:aY(i.registrationInfo.credentialID),provider:e.provider.id,type:a.type},l={providerAccountId:u.providerAccountId,counter:i.registrationInfo.counter,credentialID:aY(i.registrationInfo.credentialID),credentialPublicKey:aY(i.registrationInfo.credentialPublicKey),credentialBackedUp:i.registrationInfo.credentialBackedUp,credentialDeviceType:i.registrationInfo.credentialDeviceType,transports:(n=o.response.transports,n?.join(","))};return{user:c,account:u,authenticator:l}}async function aq(e,t,r){let{provider:n,adapter:i}=e,a=r&&r.id?await i.listAuthenticatorsByUserId(r.id):null,o=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateAuthenticationOptions({...n.authenticationOptions,rpID:o.id,allowCredentials:a?.map(e=>({id:aX(e.credentialID),type:"public-key",transports:aZ(e.transports)}))})}async function aG(e,t,r){let{provider:n,adapter:i}=e,a=r.id?await i.listAuthenticatorsByUserId(r.id):null,o=rs(32),s=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateRegistrationOptions({...n.registrationOptions,userID:o,userName:r.email,userDisplayName:r.name??void 0,rpID:s.id,rpName:s.name,excludeCredentials:a?.map(e=>({id:aX(e.credentialID),type:"public-key",transports:aZ(e.transports)}))})}function aV(e){let{provider:t,adapter:r}=e;if(!r)throw new T("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new K("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function aX(e){return new Uint8Array(Buffer.from(e,"base64"))}function aY(e){return Buffer.from(e).toString("base64")}function aZ(e){return e?e.split(","):void 0}async function aQ(e,t,r,n){if(!t.provider)throw new K("Callback route called without provider");let{query:i,body:a,method:o,headers:s}=e,{provider:c,adapter:u,url:l,callbackUrl:d,pages:p,jwt:f,events:h,callbacks:m,session:{strategy:y,maxAge:g},logger:w}=t,_="jwt"===y;try{if("oauth"===c.type||"oidc"===c.type){let o,s=c.authorization?.url.searchParams.get("response_mode")==="form_post"?a:i;if(t.isOnRedirectProxy&&s?.state){let e=await aH.decode(s.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(s)}`;return w.debug("Proxy redirecting to",t),{redirect:t,cookies:n}}}let y=await aW(s,e.cookies,t);y.cookies.length&&n.push(...y.cookies),w.debug("authorization result",y);let{user:b,account:v,profile:E}=y;if(!b||!v||!E)return{redirect:`${l}/signin`,cookies:n};if(u){let{getUserByAccount:e}=u;o=await e({providerAccountId:v.providerAccountId,provider:c.id})}let k=await a0({user:o??b,account:v,profile:E},t);if(k)return{redirect:k,cookies:n};let{user:S,session:A,isNewUser:x}=await nP(r.value,b,v,t);if(_){let e={name:S.name,email:S.email,picture:S.image,sub:S.id?.toString()},i=await m.jwt({token:e,user:S,account:v,profile:E,isNewUser:x,trigger:x?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await f.encode({...f,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*g);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:A.sessionToken,options:{...t.cookies.sessionToken.options,expires:A.expires}});if(await h.signIn?.({user:S,account:v,profile:E,isNewUser:x}),x&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("email"===c.type){let e=i?.token,a=i?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let o=c.secret??t.secret,s=await u.useVerificationToken({identifier:a,token:await ro(`${e}${o}`)}),l=!!s,y=l&&s.expires.valueOf()<Date.now();if(!l||y||a&&s.identifier!==a)throw new B({hasInvite:l,expired:y});let{identifier:b}=s,w=await u.getUserByEmail(b)??{id:crypto.randomUUID(),email:b,emailVerified:null},v={providerAccountId:w.email,userId:w.id,type:"email",provider:c.id},E=await a0({user:w,account:v},t);if(E)return{redirect:E,cookies:n};let{user:k,session:S,isNewUser:A}=await nP(r.value,w,v,t);if(_){let e={name:k.name,email:k.email,picture:k.image,sub:k.id?.toString()},i=await m.jwt({token:e,user:k,account:v,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await f.encode({...f,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*g);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:S.sessionToken,options:{...t.cookies.sessionToken.options,expires:S.expires}});if(await h.signIn?.({user:k,account:v,isNewUser:A}),A&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("credentials"===c.type&&"POST"===o){let e=a??{};Object.entries(i??{}).forEach(([e,t])=>l.searchParams.set(e,t));let u=await c.authorize(e,new Request(l,{headers:s,method:o,body:JSON.stringify(a)}));if(u)u.id=u.id?.toString()??crypto.randomUUID();else throw new A;let p={providerAccountId:u.id,type:"credentials",provider:c.id},y=await a0({user:u,account:p,credentials:e},t);if(y)return{redirect:y,cookies:n};let b={name:u.name,email:u.email,picture:u.image,sub:u.id},w=await m.jwt({token:b,user:u,account:p,isNewUser:!1,trigger:"signIn"});if(null===w)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await f.encode({...f,token:w,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*g);let o=r.chunk(i,{expires:a});n.push(...o)}return await h.signIn?.({user:u,account:p}),{redirect:d,cookies:n}}else if("webauthn"===c.type&&"POST"===o){let i,a,o,s=e.body?.action;if("string"!=typeof s||"authenticate"!==s&&"register"!==s)throw new b("Invalid action parameter");let c=aV(t);switch(s){case"authenticate":{let t=await aF(c,e,n);i=t.user,a=t.account;break}case"register":{let r=await az(t,e,n);i=r.user,a=r.account,o=r.authenticator}}await a0({user:i,account:a},t);let{user:u,isNewUser:l,session:y,account:w}=await nP(r.value,i,a,t);if(!w)throw new b("Error creating or finding account");if(o&&u.id&&await c.adapter.createAuthenticator({...o,userId:u.id}),_){let e={name:u.name,email:u.email,picture:u.image,sub:u.id?.toString()},i=await m.jwt({token:e,user:u,account:w,isNewUser:l,trigger:l?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await f.encode({...f,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*g);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:y.sessionToken,options:{...t.cookies.sessionToken.options,expires:y.expires}});if(await h.signIn?.({user:u,account:w,isNewUser:l}),l&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}throw new K(`Callback for provider type (${c.type}) is not supported`)}catch(t){if(t instanceof b)throw t;let e=new v(t,{provider:c.id});throw w.debug("callback route error details",{method:o,query:i,body:a}),e}}async function a0(e,t){let r,{signIn:n,redirect:i}=t.callbacks;try{r=await n(e)}catch(e){if(e instanceof b)throw e;throw new _(e)}if(!r)throw new _("AccessDenied");if("string"==typeof r)return await i({url:r,baseUrl:t.url.origin})}async function a1(e,t,r,n,i){let{adapter:a,jwt:o,events:s,callbacks:c,logger:u,session:{strategy:l,maxAge:d}}=e,p={body:null,headers:{"Content-Type":"application/json",...!n&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:r},f=t.value;if(!f)return p;if("jwt"===l){try{let r=e.cookies.sessionToken.name,a=await o.decode({...o,token:f,salt:r});if(!a)throw Error("Invalid JWT");let u=await c.jwt({token:a,...n&&{trigger:"update"},session:i}),l=nx(d);if(null!==u){let e={user:{name:u.name,email:u.email,image:u.picture},expires:l.toISOString()},n=await c.session({session:e,token:u});p.body=n;let i=await o.encode({...o,token:u,salt:r}),a=t.chunk(i,{expires:l});p.cookies?.push(...a),await s.session?.({session:n,token:u})}else p.cookies?.push(...t.clean())}catch(e){u.error(new R(e)),p.cookies?.push(...t.clean())}return p}try{let{getSessionAndUser:r,deleteSession:o,updateSession:u}=a,l=await r(f);if(l&&l.session.expires.valueOf()<Date.now()&&(await o(f),l=null),l){let{user:t,session:r}=l,a=e.session.updateAge,o=r.expires.valueOf()-1e3*d+1e3*a,h=nx(d);o<=Date.now()&&await u({sessionToken:f,expires:h});let m=await c.session({session:{...r,user:t},user:t,newSession:i,...n?{trigger:"update"}:{}});p.body=m,p.cookies?.push({name:e.cookies.sessionToken.name,value:f,options:{...e.cookies.sessionToken.options,expires:h}}),await s.session?.({session:m})}else f&&p.cookies?.push(...t.clean())}catch(e){u.error(new H(e))}return p}async function a2(e,t){let r,n,{logger:i,provider:a}=t,o=a.authorization?.url;if(!o||"authjs.dev"===o.host){let e=new URL(a.issuer),t=await nQ(e,{[nN]:a[rh],[nj]:!0}),r=await n2(e,t).catch(t=>{if(!(t instanceof TypeError)||"Invalid URL"!==t.message)throw t;throw TypeError(`Discovery request responded with an invalid issuer. expected: ${e}`)});if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");o=new URL(r.authorization_endpoint)}let s=o.searchParams,c=a.callbackUrl;!t.isOnRedirectProxy&&a.redirectProxyUrl&&(c=a.redirectProxyUrl,n=a.callbackUrl,i.debug("using redirect proxy",{redirect_uri:c,data:n}));let u=Object.assign({response_type:"code",client_id:a.clientId,redirect_uri:c,...a.authorization?.params},Object.fromEntries(a.authorization?.url.searchParams??[]),e);for(let e in u)s.set(e,u[e]);let l=[];a.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let d=await aH.create(t,n);if(d&&(s.set("state",d.value),l.push(d.cookie)),a.checks?.includes("pkce"))if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===a.type&&(a.checks=["nonce"]);else{let{value:e,cookie:r}=await aI.create(t);s.set("code_challenge",e),s.set("code_challenge_method","S256"),l.push(r)}let p=await a$.create(t);return p&&(s.set("nonce",p.value),l.push(p.cookie)),"oidc"!==a.type||o.searchParams.has("scope")||o.searchParams.set("scope","openid profile email"),i.debug("authorization url is ready",{url:o,cookies:l,provider:a}),{redirect:o.toString(),cookies:l}}async function a6(e,t){let r,{body:n}=e,{provider:i,callbacks:a,adapter:o}=t,s=(i.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(n?.email),c={id:crypto.randomUUID(),email:s,emailVerified:null},u=await o.getUserByEmail(s)??c,l={providerAccountId:s,userId:u.id,type:"email",provider:i.id};try{r=await a.signIn({user:u,account:l,email:{verificationRequest:!0}})}catch(e){throw new _(e)}if(!r)throw new _("AccessDenied");if("string"==typeof r)return{redirect:await a.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:d,theme:p}=t,f=await i.generateVerificationToken?.()??rs(32),h=new Date(Date.now()+(i.maxAge??86400)*1e3),m=i.secret??t.secret,y=new URL(t.basePath,t.url.origin),b=i.sendVerificationRequest({identifier:s,token:f,expires:h,url:`${y}/callback/${i.id}?${new URLSearchParams({callbackUrl:d,token:f,email:s})}`,provider:i,theme:p,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),g=o.createVerificationToken?.({identifier:s,token:await ro(`${f}${m}`),expires:h});return await Promise.all([b,g]),{redirect:`${y}/verify-request?${new URLSearchParams({provider:i.id,type:i.type})}`}}async function a5(e,t,r){let n=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:n,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:n,cookies:i}=await a2(e.query,r);return i&&t.push(...i),{redirect:n,cookies:t}}case"email":return{...await a6(e,r),cookies:t};default:return{redirect:n,cookies:t}}}async function a3(e,t,r){let{jwt:n,events:i,callbackUrl:a,logger:o,session:s}=r,c=t.value;if(!c)return{redirect:a,cookies:e};try{if("jwt"===s.strategy){let e=r.cookies.sessionToken.name,t=await n.decode({...n,token:c,salt:e});await i.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(c);await i.signOut?.({session:e})}}catch(e){o.error(new L(e))}return e.push(...t.clean()),{redirect:a,cookies:e}}async function a4(e,t){let{adapter:r,jwt:n,session:{strategy:i}}=e,a=t.value;if(!a)return null;if("jwt"===i){let t=e.cookies.sessionToken.name,r=await n.decode({...n,token:a,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(a);if(e)return e.user}return null}async function a8(e,t,r,n){let i=aV(t),{provider:a}=i,{action:o}=e.query??{};if("register"!==o&&"authenticate"!==o&&void 0!==o)return{status:400,body:{error:"Invalid action"},cookies:n,headers:{"Content-Type":"application/json"}};let s=await a4(t,r),c=s?{user:s,exists:!0}:await a.getUserInfo(t,e),u=c?.user;switch(function(e,t,r){let{user:n,exists:i=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(n&&t===i)return"register";break;case void 0:if(!t)if(!n)return"authenticate";else if(i)return"authenticate";else return"register"}return null}(o,!!s,c)){case"authenticate":return aB(i,e,u,n);case"register":if("string"==typeof u?.email)return aJ(i,e,u,n);break;default:return{status:400,body:{error:"Invalid request"},cookies:n,headers:{"Content-Type":"application/json"}}}}async function a9(e,t){let{action:r,providerId:n,error:i,method:a}=e,o=t.skipCSRFCheck===rp,{options:s,cookies:c}=await rv({authOptions:t,action:r,providerId:n,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===a,csrfDisabled:o}),u=new y(s.cookies.sessionToken,e.cookies,s.logger);if("GET"===a){let t=nA({...s,query:e.query,cookies:c});switch(r){case"callback":return await aQ(e,s,u,c);case"csrf":return t.csrf(o,s,c);case"error":return t.error(i);case"providers":return t.providers(s.providers);case"session":return await a1(s,u,c);case"signin":return t.signin(n,i);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await a8(e,s,u,c)}}else{let{csrfTokenVerified:t}=s;switch(r){case"callback":return"credentials"===s.provider.type&&ru(r,t),await aQ(e,s,u,c);case"session":return ru(r,t),await a1(s,u,c,!0,e.body?.data);case"signin":return ru(r,t),await a5(e,c,s);case"signout":return ru(r,t),await a3(c,u,s)}}throw new M(`Cannot handle action: ${r}`)}function a7(e,t,r,n,i){let a,o=i?.basePath,s=n.AUTH_URL??n.NEXTAUTH_URL;if(s)a=new URL(s),o&&"/"!==o&&"/"!==a.pathname&&(a.pathname!==o&&t7(i).warn("env-url-basepath-mismatch"),a.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),n=r.get("x-forwarded-proto")??t??"https",i=n.endsWith(":")?n:n+":";a=new URL(`${i}//${e}`)}let c=a.toString().replace(/\/$/,"");if(o){let t=o?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${c}/${t}/${e}`)}return new URL(`${c}/${e}`)}async function oe(e,t){let r=t7(t),n=await ri(e,t);if(!n)return Response.json("Bad request.",{status:400});let i=function(e,t){let{url:r}=e,n=[];if(!Z&&t.debug&&n.push("debug-enabled"),!t.trustHost)return new J(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new j("Please define a `secret`");let i=e.query?.callbackUrl;if(i&&!Q(i,r.origin))return new S(`Invalid callback URL. Received: ${i}`);let{callbackUrl:a}=m(t.useSecureCookies??"https:"===r.protocol),o=e.cookies?.[t.cookies?.callbackUrl?.name??a.name];if(o&&!Q(o,r.origin))return new S(`Invalid callback URL. Received: ${o}`);let s=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e,{authorization:r,token:n,userinfo:i}=t;if("string"==typeof r||r?.url?"string"==typeof n||n?.url?"string"==typeof i||i?.url||(e="userinfo"):e="token":e="authorization",e)return new x(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)ee=!0;else if("email"===t.type)et=!0;else if("webauthn"===t.type){var c;if(er=!0,t.simpleWebAuthnBrowserVersion&&(c=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(c)))return new b(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(s)return new q("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(s=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new G(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(ee){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new W("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new C("Must define an authorize() handler to use credentials authentication provider")}let{adapter:u,session:l}=t,d=[];if(et||l?.strategy==="database"||!l?.strategy&&u)if(et){if(!u)return new T("Email login requires an adapter");d.push(...en)}else{if(!u)return new T("Database session requires an adapter");d.push(...ei)}if(er){if(!t.experimental?.enableWebAuthn)return new Y("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(n.push("experimental-webauthn"),!u)return new T("WebAuthn requires an adapter");d.push(...ea)}if(u){let e=d.filter(e=>!(e in u));if(e.length)return new O(`Required adapter methods were missing: ${e.join(", ")}`)}return Z||(Z=!0),n}(n,t);if(Array.isArray(i))i.forEach(r.warn);else if(i){if(r.error(i),!new Set(["signin","signout","error","verify-request"]).has(n.action)||"GET"!==n.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:a}=t,o=e?.error&&n.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||o)return o&&r.error(new E(`The error page ${e?.error} should not require authentication`)),ra(nA({theme:a}).error("Configuration"));let s=`${n.url.origin}${e.error}?error=Configuration`;return Response.redirect(s)}let a=e.headers?.has("X-Auth-Return-Redirect"),o=t.raw===rf;try{let e=await a9(n,t);if(o)return e;let r=ra(e),i=r.headers.get("Location");if(!a||!i)return r;return Response.json({url:i},{headers:r.headers})}catch(d){r.error(d);let i=d instanceof b;if(i&&o&&!a)throw d;if("POST"===e.method&&"session"===n.action)return Response.json(null,{status:400});let s=new URLSearchParams({error:d instanceof b&&z.has(d.type)?d.type:"Configuration"});d instanceof A&&s.set("code",d.code);let c=i&&d.kind||"error",u=t.pages?.[c]??`${t.basePath}/${c.toLowerCase()}`,l=`${n.url.origin}${u}?${s}`;if(a)return Response.json({url:l});return Response.redirect(l)}}var ot=r(1635);function or(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:n,origin:i}=e.nextUrl;return new ot.NextRequest(n.replace(i,r),e)}function on(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let n=e.AUTH_URL;n&&(t.basePath?r||t7(t).warn("env-url-basepath-redundant"):t.basePath=new URL(n).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let n of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${n}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,n=r.toUpperCase().replace(/-/g,"_"),i=e[`AUTH_${n}_ID`],a=e[`AUTH_${n}_SECRET`],o=e[`AUTH_${n}_ISSUER`],s=e[`AUTH_${n}_KEY`],c="function"==typeof t?t({clientId:i,clientSecret:a,issuer:o,apiKey:s}):t;return"oauth"===c.type||"oidc"===c.type?(c.clientId??(c.clientId=i),c.clientSecret??(c.clientSecret=a),c.issuer??(c.issuer=o)):"email"===c.type&&(c.apiKey??(c.apiKey=s)),c})}(process.env,e,!0)}}var oi=r(98191);async function oa(e,t){return oe(new Request(a7("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function oo(e){return"function"==typeof e}function os(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await (0,oi.b3)(),n=await e(void 0);return t?.(n),oa(r,n).then(e=>e.json())}if(r[0]instanceof Request){let n=r[0],i=r[1],a=await e(n);return t?.(a),oc([n,i],a)}if(oo(r[0])){let n=r[0];return async(...r)=>{let i=await e(r[0]);return t?.(i),oc(r,i,n)}}let n="req"in r[0]?r[0].req:r[0],i="res"in r[0]?r[0].res:r[1],a=await e(n);return t?.(a),oa(new Headers(n.headers),a).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in i?i.headers.append("set-cookie",t):i.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve((0,oi.b3)()).then(t=>oa(t,e).then(e=>e.json()));if(t[0]instanceof Request)return oc([t[0],t[1]],e);if(oo(t[0])){let r=t[0];return async(...t)=>oc(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],n="res"in t[0]?t[0].res:t[1];return oa(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}}async function oc(e,t,r){let n=or(e[0]),i=await oa(n.headers,t),a=await i.json(),o=!0;t.callbacks?.authorized&&(o=await t.callbacks.authorized({request:n,auth:a}));let s=ot.NextResponse.next?.();if(o instanceof Response){s=o;let e=o.headers.get("Location"),{pathname:r}=n.nextUrl;e&&function(e,t,r){let n=t.replace(`${e}/`,""),i=Object.values(r.pages??{});return(ou.has(n)||i.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(o=!0)}else if(r)n.auth=a,s=await r(n,e[1])??ot.NextResponse.next();else if(!o){let e=t.pages?.signIn??`${t.basePath}/signin`;if(n.nextUrl.pathname!==e){let t=n.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",n.nextUrl.href),s=ot.NextResponse.redirect(t)}}let c=new Response(s?.body,s);for(let e of i.headers.getSetCookie())c.headers.append("set-cookie",e);return c}let ou=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var ol=r(9066);async function od(e,t={},r,n){let i=new Headers(await (0,oi.b3)()),{redirect:a=!0,redirectTo:o,...s}=t instanceof FormData?Object.fromEntries(t):t,c=o?.toString()??i.get("Referer")??"/",u=a7("signin",i.get("x-forwarded-proto"),i,process.env,n);if(!e)return u.searchParams.append("callbackUrl",c),a&&(0,ol.redirect)(u.toString()),u.toString();let l=`${u}/${e}?${new URLSearchParams(r)}`,d={};for(let t of n.providers){let{options:r,...n}="function"==typeof t?t():t,i=r?.id??n.id;if(i===e){d={id:i,type:r?.type??n.type};break}}if(!d.id){let e=`${u}?${new URLSearchParams({callbackUrl:c})}`;return a&&(0,ol.redirect)(e),e}"credentials"===d.type&&(l=l.replace("signin","callback")),i.set("Content-Type","application/x-www-form-urlencoded");let p=new Request(l,{method:"POST",headers:i,body:new URLSearchParams({...s,callbackUrl:c})}),f=await oe(p,{...n,raw:rf,skipCSRFCheck:rp}),h=await (0,oi.UL)();for(let e of f?.cookies??[])h.set(e.name,e.value,e.options);let m=(f instanceof Response?f.headers.get("Location"):f.redirect)??l;return a?(0,ol.redirect)(m):m}async function op(e,t){let r=new Headers(await (0,oi.b3)());r.set("Content-Type","application/x-www-form-urlencoded");let n=a7("signout",r.get("x-forwarded-proto"),r,process.env,t),i=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),a=new Request(n,{method:"POST",headers:r,body:i}),o=await oe(a,{...t,raw:rf,skipCSRFCheck:rp}),s=await (0,oi.UL)();for(let e of o?.cookies??[])s.set(e.name,e.value,e.options);return e?.redirect??!0?(0,ol.redirect)(o.redirect):o}async function of(e,t){let r=new Headers(await (0,oi.b3)());r.set("Content-Type","application/json");let n=new Request(a7("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),i=await oe(n,{...t,raw:rf,skipCSRFCheck:rp}),a=await (0,oi.UL)();for(let e of i?.cookies??[])a.set(e.name,e.value,e.options);return i.body}let oh="tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh",om="https://gsapi.geon.kr/smt";async function oy(e,t){let r=new URLSearchParams({crtfckey:oh,userId:e,password:t}),n=await fetch(`${om}/login/validation?${r.toString()}`,{method:"POST",headers:{"Content-Type":"application/json",crtfckey:oh}}),i=await n.json();if(!n.ok)throw Error("로그인 검증에 실패했습니다.");return i}async function ob(e){let t=new URLSearchParams({crtfckey:oh,userId:e}),r=await fetch(`${om}/users/id?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",crtfckey:oh}}),n=await r.json();if(!r.ok)throw Error("사용자 정보를 가져오는데 실패했습니다.");return n}let og=[{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:{credentials:{},async authorize({id:e,password:t}){try{let r=process.env.FRONTEND_LOGIN_USER_ID||"admin",n=process.env.FRONTEND_LOGIN_PASSWORD||"password1234";if(e===r&&t===n)return{id:r,name:"GeOn City",email:"@example.com",userId:r,userNm:"GeOn City",emailaddr:"@example.com",userSeCode:"14",userSeCodeNm:"관리자",userImage:null,insttCode:"GEON",insttNm:"GeOn",insttUrl:null,message:"로그인 성공"};if("geonuser"===e){let r=await oy(e,t);if(!r.result.isValid)throw new A(r.result.message);let n=await ob(e);if(200!==n.code)return new A(n.result.message);return{...n.result,id:n.result.userId,name:n.result.userNm||e,email:n.result.emailaddr||`${n.result.userNm}`}}throw new A("admin 또는 geonuser 계정으로만 로그인할 수 있습니다.")}catch(e){throw console.error("Auth error:",e),e}}}}];og.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"credentials"!==e.id);let{handlers:ow,auth:o_,signIn:ov,signOut:oE}=function(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return on(r),oe(or(t),r)};return{handlers:{GET:t,POST:t},auth:os(e,e=>on(e)),signIn:async(t,r,n)=>{let i=await e(void 0);return on(i),od(t,r,n,i)},signOut:async t=>{let r=await e(void 0);return on(r),op(t,r)},unstable_update:async t=>{let r=await e(void 0);return on(r),of(t,r)}}}on(e);let t=t=>oe(or(t),e);return{handlers:{GET:t,POST:t},auth:os(e),signIn:(t,r,n)=>od(t,r,n,e),signOut:t=>op(t,e),unstable_update:t=>of(t,e)}}({pages:{signIn:"/login",newUser:"/"},providers:[],callbacks:{authorized({auth:e,request:{nextUrl:t}}){let r=!!e?.user,n=t.pathname.startsWith("/geon-2d-map"),i=t.pathname.startsWith("/login");return"/"===t.pathname||r&&i?Response.redirect(new URL("/geon-2d-map",t)):!!i||!n||r}},providers:og,session:{strategy:"jwt",maxAge:1800},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id),e),session:async({session:e,token:t})=>(e.user&&(e.user.id=t.id),e)}});var ok=r(13514);let oS=p.z.object({id:p.z.string(),email:p.z.string().email().nullish(),password:p.z.string().min(6)}),oA=async(e,t)=>{try{let e=oS.parse({id:t.get("id"),password:t.get("password")});return await ov("credentials",{id:e.id,password:e.password,redirect:!1}),{status:"success"}}catch(e){if(e instanceof p.z.ZodError)return{status:"invalid_data"};return{status:"failed"}}};(0,ok.D)([oA]),(0,d.A)(oA,"7f992a250a06ce14f46b235bd7731d78ab71c40336",null)},17638:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>n});let n={title:"로그인",description:"말로 만드는 지도 demo 로그인",keywords:["로그인","GeOn","지온파스","인증"],openGraph:{title:"로그인",description:"말로 만드는 지도 demo 로그인.",type:"website"},twitter:{card:"summary_large_image",title:"로그인",description:"말로 만드는 지도 demo 로그인."},robots:{index:!1,follow:!1}};function i({children:e}){return e}},18535:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22126:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return i}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},22397:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return i},getAccessFallbackErrorTypeByStatus:function(){return s},getAccessFallbackHTTPStatus:function(){return o},isHTTPAccessFallbackError:function(){return a}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),i="NEXT_HTTP_ERROR_FALLBACK";function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}function o(e){return Number(e.digest.split(";")[1])}function s(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26493:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(35306).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\chatbot\\\\front-chat\\\\app\\\\(auth)\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\chatbot\\front-chat\\app\\(auth)\\login\\page.tsx","default")},27616:(e,t,r)=>{"use strict";r.d(t,{J:()=>u});var n=r(84464),i=r(63185),a=r(80656),o=r(27872),s=r(72487);let c=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),u=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)(a.b,{ref:r,className:(0,s.cn)(c(),e),...t}));u.displayName=a.b.displayName},27738:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextResponse",{enumerable:!0,get:function(){return d}});let n=r(46628),i=r(7654),a=r(61016),o=r(41245),s=r(46628),c=Symbol("internal response"),u=new Set([301,302,303,307,308]);function l(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class d extends Response{constructor(e,t={}){super(e,t);let r=this.headers,u=new Proxy(new s.ResponseCookies(r),{get(e,i,a){switch(i){case"delete":case"set":return(...a)=>{let o=Reflect.apply(e[i],e,a),c=new Headers(r);return o instanceof s.ResponseCookies&&r.set("x-middleware-set-cookie",o.getAll().map(e=>(0,n.stringifyCookie)(e)).join(",")),l(t,c),o};default:return o.ReflectAdapter.get(e,i,a)}}});this[c]={cookies:u,url:t.url?new i.NextURL(t.url,{headers:(0,a.toNodeOutgoingHttpHeaders)(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[c].cookies}static json(e,t){let r=Response.json(e,t);return new d(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!u.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},i=new Headers(null==n?void 0:n.headers);return i.set("Location",(0,a.validateURL)(e)),new d(null,{...n,headers:i,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",(0,a.validateURL)(e)),l(t,r),new d(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),l(e,t),new d(null,{...e,headers:t})}}},28093:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"formatNextPathnameInfo",{enumerable:!0,get:function(){return s}});let n=r(18535),i=r(64182),a=r(12541),o=r(93870);function s(e){let t=(0,o.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=(0,n.removeTrailingSlash)(t)),e.buildId&&(t=(0,a.addPathSuffix)((0,i.addPathPrefix)(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=(0,i.addPathPrefix)(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:(0,a.addPathSuffix)(t,"/"):(0,n.removeTrailingSlash)(t)}},28846:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getNextPathnameInfo",{enumerable:!0,get:function(){return o}});let n=r(54759),i=r(66491),a=r(87437);function o(e,t){var r,o;let{basePath:s,i18n:c,trailingSlash:u}=null!=(r=t.nextConfig)?r:{},l={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):u};s&&(0,a.pathHasPrefix)(l.pathname,s)&&(l.pathname=(0,i.removePathPrefix)(l.pathname,s),l.basePath=s);let d=l.pathname;if(l.pathname.startsWith("/_next/data/")&&l.pathname.endsWith(".json")){let e=l.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");l.buildId=e[0],d="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(l.pathname=d)}if(c){let e=t.i18nProvider?t.i18nProvider.analyze(l.pathname):(0,n.normalizeLocalePath)(l.pathname,c.locales);l.locale=e.detectedLocale,l.pathname=null!=(o=e.pathname)?o:l.pathname,!e.detectedLocale&&l.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(d):(0,n.normalizeLocalePath)(d,c.locales)).detectedLocale&&(l.locale=e.detectedLocale)}return l}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29713:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let n=r(84464),i=r(63185),a=r(8740),o=r(9479),s=r(47773),c=r(10267),u=r(8009),l=r(54275);function d(e){let{replace:t,scroll:r,prefetch:d,ref:p,...f}=e,h=(0,i.useContext)(s.AppRouterContext),m=f.action,y="string"==typeof m;for(let e of u.DISALLOWED_FORM_PROPS)e in f&&delete f[e];let b=!!h&&y&&null===(!1===d||null===d?d:null),g=(0,i.useCallback)(e=>(b&&null!==h&&(0,l.mountFormInstance)(e,m,h,c.PrefetchKind.AUTO),()=>{(0,l.unmountPrefetchableInstance)(e)}),[b,m,h]),w=(0,o.useMergedRef)(g,null!=p?p:null);if(!y)return(0,n.jsx)("form",{...f,ref:w});let _=(0,a.addBasePath)(m);return(0,n.jsx)("form",{...f,ref:w,action:_,onSubmit:e=>(function(e,t){let{actionHref:r,onSubmit:n,replace:i,scroll:a,router:o}=t;if("function"==typeof n&&(n(e),e.defaultPrevented)||!o)return;let s=e.currentTarget,c=e.nativeEvent.submitter,l=r;if(c){if((0,u.hasUnsupportedSubmitterAttributes)(c)||(0,u.hasReactClientActionAttributes)(c))return;let e=c.getAttribute("formAction");null!==e&&(l=e)}let d=(0,u.createFormSubmitDestinationUrl)(l,s);e.preventDefault();let p=d.href;o[i?"replace":"push"](p,{scroll:a})})(e,{router:h,actionHref:_,replace:t,scroll:r,onSubmit:f.onSubmit})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30075:(e,t,r)=>{"use strict";r.d(t,{E:()=>s});var n=r(84464);r(63185);var i=r(27872),a=r(72487);let o=(0,i.F)("inline-flex items-center rounded-full border px-3 py-1.5 text-xs font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer select-none",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80 hover:scale-105 active:scale-95",secondary:"border-transparent bg-gray-100 text-gray-700 hover:bg-gray-200 hover:scale-105 active:scale-95 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 hover:scale-105 active:scale-95",outline:"text-foreground border-border hover:bg-accent hover:text-accent-foreground hover:scale-105 active:scale-95","ai-active":"border-transparent bg-gradient-to-r from-blue-600 to-indigo-600 text-white border border-blue-500 hover:from-blue-700 hover:to-indigo-700 hover:border-blue-600 hover:scale-105 active:scale-95 shadow-md dark:from-blue-700 dark:to-indigo-700 dark:text-white dark:border-blue-600 dark:hover:from-blue-800 dark:hover:to-indigo-800","nav-active":"border-transparent bg-gradient-to-r from-emerald-600 to-green-600 text-white border border-emerald-500 hover:from-emerald-700 hover:to-green-700 hover:border-emerald-600 hover:scale-105 active:scale-95 shadow-md dark:from-emerald-700 dark:to-green-700 dark:text-white dark:border-emerald-600 dark:hover:from-emerald-800 dark:hover:to-green-800"}},defaultVariants:{variant:"default"}});function s({className:e,variant:t,...r}){return(0,n.jsx)("div",{className:(0,a.cn)(o({variant:t}),e),...r})}},32018:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(86626).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33489:(e,t,r)=>{Promise.resolve().then(r.bind(r,44989))},33873:e=>{"use strict";e.exports=require("path")},37321:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return i}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},37758:()=>{},37791:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){Object.keys(e).forEach(function(r){"default"===r||Object.prototype.hasOwnProperty.call(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[r]}})})}(r(80461),t)},38762:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isBot:function(){return i},userAgent:function(){return o},userAgentFromString:function(){return a}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(69093));function i(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}function a(e){return{...(0,n.default)(e),isBot:void 0!==e&&i(e)}}function o({headers:e}){return a(e.get("user-agent")||void 0)}},43764:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return s},throwWithStaticGenerationBailoutError:function(){return a},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return o}});let n=r(33746),i=r(3295);function a(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function o(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} with \`dynamic = "error"\` couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function s(e){let t=Object.defineProperty(Error(`Route ${e.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw e.invalidUsageError??=t,t}function c(){let e=i.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},43875:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"URLPattern",{enumerable:!0,get:function(){return r}});let r="undefined"==typeof URLPattern?void 0:URLPattern},44502:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"connection",{enumerable:!0,get:function(){return u}});let n=r(29294),i=r(63033),a=r(68471),o=r(7647),s=r(888),c=r(39333);function u(){let e=n.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,c.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside "after(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but "after(...)" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E186",enumerable:!1,configurable:!0});if(e.forceStatic)return Promise.resolve(void 0);if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside "use cache". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E111",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside a function cached with "unstable_cache(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E1",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new o.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`connection\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E562",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type)return(0,s.makeHangingPromise)(t.renderSignal,"`connection()`");else"prerender-ppr"===t.type?(0,a.postponeWithTracking)(e.route,"connection",t.dynamicTracking):"prerender-legacy"===t.type&&(0,a.throwToInterruptStaticGeneration)("connection",e,t);(0,a.trackDynamicDataInDynamicRender)(e,t)}return Promise.resolve(void 0)}},44989:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var n=r(84464),i=r(86270),a=r(63185);r(55703);var o=r(3824);let s=(0,o.createServerReference)("7f992a250a06ce14f46b235bd7731d78ab71c40336",o.callServer,void 0,o.findSourceMapURL,"login");var c=r(29713),u=r.n(c),l=r(27616),d=r(67334);function p({action:e,children:t,defaultEmail:r=""}){return(0,n.jsxs)(u(),{action:e,className:"flex flex-col gap-4 px-4 sm:px-16",children:[(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,n.jsx)(l.J,{htmlFor:"id",className:"text-zinc-600 font-normal dark:text-zinc-400",children:"아이디"}),(0,n.jsx)(d.p,{id:"id",name:"id",className:"bg-muted text-md md:text-sm",type:"text",placeholder:"admin",autoComplete:"username",required:!0,defaultValue:r}),(0,n.jsx)(l.J,{htmlFor:"password",className:"text-zinc-600 font-normal dark:text-zinc-400",children:"비밀번호"}),(0,n.jsx)(d.p,{id:"password",name:"password",className:"bg-muted text-md md:text-sm",type:"password",required:!0})]}),t]})}var f=r(67108);let h=(0,r(71410).A)("Loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]]);var m=r(96684);function y({children:e}){let{pending:t}=(0,f.useFormStatus)();return(0,n.jsxs)(m.$,{type:t?"button":"submit","aria-disabled":t,className:"relative",children:[e,t&&(0,n.jsx)("span",{className:"animate-spin absolute right-4",children:(0,n.jsx)(h,{})}),(0,n.jsx)("span",{"aria-live":"polite",className:"sr-only",role:"status",children:t?"Loading":"Submit form"})]})}var b=r(30075);function g(){(0,i.useRouter)();let[e,t]=(0,a.useState)("admin"),[r,o]=(0,a.useActionState)(s,{status:"idle"});return(0,n.jsx)("div",{className:"flex h-screen w-screen items-center justify-center bg-background",children:(0,n.jsxs)("div",{className:"w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12",children:[(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16",children:[(0,n.jsxs)("h3",{className:"flex text-xl font-semibold gap-2 dark:text-zinc-50",children:["로그인",(0,n.jsx)(b.E,{variant:"secondary",children:"GeOn"})]}),(0,n.jsx)("p",{className:"text-sm text-gray-500 dark:text-zinc-400",children:"admin 계정으로 로그인하세요."})]}),(0,n.jsx)(p,{action:e=>{t(e.get("email")),o(e)},defaultEmail:e,children:(0,n.jsx)(y,{children:"Sign in"})})]})})}},45714:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return i},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return a}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function i(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let a=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},49953:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(86626).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51180:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},52349:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return i},RedirectType:function(){return a},isRedirectError:function(){return o}});let n=r(53043),i="NEXT_REDIRECT";var a=function(e){return e.push="push",e.replace="replace",e}({});function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,a]=t,o=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===i&&("replace"===a||"push"===a)&&"string"==typeof o&&!isNaN(s)&&s in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53043:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53586:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return a}});let n=r(86626),i=r(54114);function a(e){return(0,i.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53833:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(66384).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54114:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return i},RedirectType:function(){return a},isRedirectError:function(){return o}});let n=r(16700),i="NEXT_REDIRECT";var a=function(e){return e.push="push",e.replace="replace",e}({});function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,a]=t,o=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===i&&("replace"===a||"push"===a)&&"string"==typeof o&&!isNaN(s)&&s in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54284:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},54759:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return n}});let r=new WeakMap;function n(e,t){let n;if(!t)return{pathname:e};let i=r.get(t);i||(i=t.map(e=>e.toLowerCase()),r.set(t,i));let a=e.split("/",2);if(!a[1])return{pathname:e};let o=a[1].toLowerCase(),s=i.indexOf(o);return s<0?{pathname:e}:(n=t[s],{pathname:e=e.slice(n.length+1)||"/",detectedLocale:n})}},55511:e=>{"use strict";e.exports=require("crypto")},60953:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PageSignatureError:function(){return r},RemovedPageError:function(){return n},RemovedUAError:function(){return i}});class r extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class n extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class i extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},61016:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fromNodeOutgoingHttpHeaders:function(){return i},normalizeNextQueryParam:function(){return c},splitCookiesString:function(){return a},toNodeOutgoingHttpHeaders:function(){return o},validateURL:function(){return s}});let n=r(3219);function i(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}function a(e){var t,r,n,i,a,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,a=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(a=!0,s=i,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!a||s>=e.length)&&o.push(e.substring(t,e.length))}return o}function o(e){let t={},r=[];if(e)for(let[n,i]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...a(i)),t[n]=1===r.length?r[0]:r):t[n]=i;return t}function s(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}function c(e){for(let t of[n.NEXT_QUERY_PARAM_PREFIX,n.NEXT_INTERCEPTION_MARKER_PREFIX])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64182:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let n=r(67157);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:a}=(0,n.parsePath)(e);return""+t+r+i+a}},64493:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rootParams",{enumerable:!0,get:function(){return l}});let n=r(24679),i=r(68471),a=r(29294),o=r(63033),s=r(888),c=r(97535),u=new WeakMap;async function l(){let e=a.workAsyncStorage.getStore();if(!e)throw Object.defineProperty(new n.InvariantError("Missing workStore in unstable_rootParams"),"__NEXT_ERROR_CODE",{value:"E615",enumerable:!1,configurable:!0});let t=o.workUnitAsyncStorage.getStore();if(!t)throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` in Pages Router. This API is only available within App Router.`),"__NEXT_ERROR_CODE",{value:"E641",enumerable:!1,configurable:!0});switch(t.type){case"unstable-cache":case"cache":throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` inside \`"use cache"\` or \`unstable_cache\`. Support for this API inside cache scopes is planned for a future version of Next.js.`),"__NEXT_ERROR_CODE",{value:"E642",enumerable:!1,configurable:!0});case"prerender":case"prerender-ppr":case"prerender-legacy":return function(e,t,r){let n=t.fallbackRouteParams;if(n){let p=!1;for(let t in e)if(n.has(t)){p=!0;break}if(p){if("prerender"===r.type){let t=u.get(e);if(t)return t;let n=(0,s.makeHangingPromise)(r.renderSignal,"`unstable_rootParams`");return u.set(e,n),n}var a=e,o=n,l=t,d=r;let p=u.get(a);if(p)return p;let f={...a},h=Promise.resolve(f);return u.set(a,h),Object.keys(a).forEach(e=>{c.wellKnownProperties.has(e)||(o.has(e)?Object.defineProperty(f,e,{get(){let t=(0,c.describeStringPropertyAccess)("unstable_rootParams",e);"prerender-ppr"===d.type?(0,i.postponeWithTracking)(l.route,t,d.dynamicTracking):(0,i.throwToInterruptStaticGeneration)(t,l,d)},enumerable:!0}):h[e]=a[e])}),h}}return Promise.resolve(e)}(t.rootParams,e,t);default:return Promise.resolve(t.rootParams)}}},65715:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var s=a?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(n,o,s):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(79907));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}let a={current:null},o="function"==typeof n.cache?n.cache:e=>e,s=console.warn;function c(e){return function(...t){s(e(...t))}}o(e=>{try{s(a.current)}finally{a.current=null}})},66384:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,o.isNextRouterError)(t)||(0,a.isBailoutToCSRError)(t)||(0,c.isDynamicServerError)(t)||(0,s.isDynamicPostpone)(t)||(0,i.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(888),i=r(99741),a=r(22126),o=r(53586),s=r(68471),c=r(2001);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66491:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removePathPrefix",{enumerable:!0,get:function(){return i}});let n=r(87437);function i(e,t){if(!(0,n.pathHasPrefix)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}},66684:(e,t)=>{"use strict";function r(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getHostname",{enumerable:!0,get:function(){return r}})},67157:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},67334:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var n=r(84464),i=r(63185),a=r(72487);let o=i.forwardRef(({className:e,type:t,...r},i)=>(0,n.jsx)("input",{type:t,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:i,...r}));o.displayName="Input"},68337:(e,t,r)=>{Promise.resolve().then(r.bind(r,26493))},68781:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return a}});let n=r(22397),i=r(52349);function a(e){return(0,i.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},69093:(e,t,r)=>{var n;(()=>{var i={226:function(i,a){!function(o,s){"use strict";var c="function",u="undefined",l="object",d="string",p="major",f="model",h="name",m="type",y="vendor",b="version",g="architecture",w="console",_="mobile",v="tablet",E="smarttv",k="wearable",S="embedded",A="Amazon",x="Apple",P="ASUS",R="BlackBerry",T="Browser",O="Chrome",C="Firefox",j="Google",U="Huawei",I="Microsoft",N="Motorola",H="Opera",$="Samsung",D="Sharp",L="Sony",M="Xiaomi",W="Zebra",K="Facebook",J="Chromium OS",B="Mac OS",F=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},z=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},q=function(e,t){return typeof e===d&&-1!==G(t).indexOf(G(e))},G=function(e){return e.toLowerCase()},V=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===u?e:e.substring(0,350)},X=function(e,t){for(var r,n,i,a,o,u,d=0;d<t.length&&!o;){var p=t[d],f=t[d+1];for(r=n=0;r<p.length&&!o&&p[r];)if(o=p[r++].exec(e))for(i=0;i<f.length;i++)u=o[++n],typeof(a=f[i])===l&&a.length>0?2===a.length?typeof a[1]==c?this[a[0]]=a[1].call(this,u):this[a[0]]=a[1]:3===a.length?typeof a[1]!==c||a[1].exec&&a[1].test?this[a[0]]=u?u.replace(a[1],a[2]):void 0:this[a[0]]=u?a[1].call(this,u,a[2]):void 0:4===a.length&&(this[a[0]]=u?a[3].call(this,u.replace(a[1],a[2])):s):this[a]=u||s;d+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===l&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(q(t[r][n],e))return"?"===r?s:r}else if(q(t[r],e))return"?"===r?s:r;return e},Z={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[b,[h,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[b,[h,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[h,b],[/opios[\/ ]+([\w\.]+)/i],[b,[h,H+" Mini"]],[/\bopr\/([\w\.]+)/i],[b,[h,H]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[h,b],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[b,[h,"UC"+T]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[b,[h,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[b,[h,"WeChat"]],[/konqueror\/([\w\.]+)/i],[b,[h,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[b,[h,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[b,[h,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[h,/(.+)/,"$1 Secure "+T],b],[/\bfocus\/([\w\.]+)/i],[b,[h,C+" Focus"]],[/\bopt\/([\w\.]+)/i],[b,[h,H+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[b,[h,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[b,[h,"Dolphin"]],[/coast\/([\w\.]+)/i],[b,[h,H+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[b,[h,"MIUI "+T]],[/fxios\/([-\w\.]+)/i],[b,[h,C]],[/\bqihu|(qi?ho?o?|360)browser/i],[[h,"360 "+T]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[h,/(.+)/,"$1 "+T],b],[/(comodo_dragon)\/([\w\.]+)/i],[[h,/_/g," "],b],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[h,b],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[h],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[h,K],b],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[h,b],[/\bgsa\/([\w\.]+) .*safari\//i],[b,[h,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[b,[h,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[b,[h,O+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[h,O+" WebView"],b],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[b,[h,"Android "+T]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[h,b],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[b,[h,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[b,h],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[h,[b,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[h,b],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[h,"Netscape"],b],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[b,[h,C+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[h,b],[/(cobalt)\/([\w\.]+)/i],[h,[b,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[g,"amd64"]],[/(ia32(?=;))/i],[[g,G]],[/((?:i[346]|x)86)[;\)]/i],[[g,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[g,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[g,"armhf"]],[/windows (ce|mobile); ppc;/i],[[g,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[g,/ower/,"",G]],[/(sun4\w)[;\)]/i],[[g,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[g,G]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[f,[y,$],[m,v]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[f,[y,$],[m,_]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[f,[y,x],[m,_]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[f,[y,x],[m,v]],[/(macintosh);/i],[f,[y,x]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[f,[y,D],[m,_]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[f,[y,U],[m,v]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[f,[y,U],[m,_]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[f,/_/g," "],[y,M],[m,_]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[f,/_/g," "],[y,M],[m,v]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[f,[y,"OPPO"],[m,_]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[f,[y,"Vivo"],[m,_]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[f,[y,"Realme"],[m,_]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[f,[y,N],[m,_]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[f,[y,N],[m,v]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[f,[y,"LG"],[m,v]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[f,[y,"LG"],[m,_]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[f,[y,"Lenovo"],[m,v]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[f,/_/g," "],[y,"Nokia"],[m,_]],[/(pixel c)\b/i],[f,[y,j],[m,v]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[f,[y,j],[m,_]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[f,[y,L],[m,_]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[f,"Xperia Tablet"],[y,L],[m,v]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[f,[y,"OnePlus"],[m,_]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[f,[y,A],[m,v]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[f,/(.+)/g,"Fire Phone $1"],[y,A],[m,_]],[/(playbook);[-\w\),; ]+(rim)/i],[f,y,[m,v]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[f,[y,R],[m,_]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[f,[y,P],[m,v]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[f,[y,P],[m,_]],[/(nexus 9)/i],[f,[y,"HTC"],[m,v]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[y,[f,/_/g," "],[m,_]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[f,[y,"Acer"],[m,v]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[f,[y,"Meizu"],[m,_]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[y,f,[m,_]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[y,f,[m,v]],[/(surface duo)/i],[f,[y,I],[m,v]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[f,[y,"Fairphone"],[m,_]],[/(u304aa)/i],[f,[y,"AT&T"],[m,_]],[/\bsie-(\w*)/i],[f,[y,"Siemens"],[m,_]],[/\b(rct\w+) b/i],[f,[y,"RCA"],[m,v]],[/\b(venue[\d ]{2,7}) b/i],[f,[y,"Dell"],[m,v]],[/\b(q(?:mv|ta)\w+) b/i],[f,[y,"Verizon"],[m,v]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[f,[y,"Barnes & Noble"],[m,v]],[/\b(tm\d{3}\w+) b/i],[f,[y,"NuVision"],[m,v]],[/\b(k88) b/i],[f,[y,"ZTE"],[m,v]],[/\b(nx\d{3}j) b/i],[f,[y,"ZTE"],[m,_]],[/\b(gen\d{3}) b.+49h/i],[f,[y,"Swiss"],[m,_]],[/\b(zur\d{3}) b/i],[f,[y,"Swiss"],[m,v]],[/\b((zeki)?tb.*\b) b/i],[f,[y,"Zeki"],[m,v]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[y,"Dragon Touch"],f,[m,v]],[/\b(ns-?\w{0,9}) b/i],[f,[y,"Insignia"],[m,v]],[/\b((nxa|next)-?\w{0,9}) b/i],[f,[y,"NextBook"],[m,v]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[y,"Voice"],f,[m,_]],[/\b(lvtel\-)?(v1[12]) b/i],[[y,"LvTel"],f,[m,_]],[/\b(ph-1) /i],[f,[y,"Essential"],[m,_]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[f,[y,"Envizen"],[m,v]],[/\b(trio[-\w\. ]+) b/i],[f,[y,"MachSpeed"],[m,v]],[/\btu_(1491) b/i],[f,[y,"Rotor"],[m,v]],[/(shield[\w ]+) b/i],[f,[y,"Nvidia"],[m,v]],[/(sprint) (\w+)/i],[y,f,[m,_]],[/(kin\.[onetw]{3})/i],[[f,/\./g," "],[y,I],[m,_]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[f,[y,W],[m,v]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[f,[y,W],[m,_]],[/smart-tv.+(samsung)/i],[y,[m,E]],[/hbbtv.+maple;(\d+)/i],[[f,/^/,"SmartTV"],[y,$],[m,E]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[y,"LG"],[m,E]],[/(apple) ?tv/i],[y,[f,x+" TV"],[m,E]],[/crkey/i],[[f,O+"cast"],[y,j],[m,E]],[/droid.+aft(\w)( bui|\))/i],[f,[y,A],[m,E]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[f,[y,D],[m,E]],[/(bravia[\w ]+)( bui|\))/i],[f,[y,L],[m,E]],[/(mitv-\w{5}) bui/i],[f,[y,M],[m,E]],[/Hbbtv.*(technisat) (.*);/i],[y,f,[m,E]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[y,V],[f,V],[m,E]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[m,E]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[y,f,[m,w]],[/droid.+; (shield) bui/i],[f,[y,"Nvidia"],[m,w]],[/(playstation [345portablevi]+)/i],[f,[y,L],[m,w]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[f,[y,I],[m,w]],[/((pebble))app/i],[y,f,[m,k]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[f,[y,x],[m,k]],[/droid.+; (glass) \d/i],[f,[y,j],[m,k]],[/droid.+; (wt63?0{2,3})\)/i],[f,[y,W],[m,k]],[/(quest( 2| pro)?)/i],[f,[y,K],[m,k]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[y,[m,S]],[/(aeobc)\b/i],[f,[y,A],[m,S]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[f,[m,_]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[f,[m,v]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[m,v]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[m,_]],[/(android[-\w\. ]{0,9});.+buil/i],[f,[y,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[b,[h,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[b,[h,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[h,b],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[b,h]],os:[[/microsoft (windows) (vista|xp)/i],[h,b],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[h,[b,Y,Z]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[h,"Windows"],[b,Y,Z]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[b,/_/g,"."],[h,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[h,B],[b,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[b,h],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[h,b],[/\(bb(10);/i],[b,[h,R]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[b,[h,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[b,[h,C+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[b,[h,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[b,[h,"watchOS"]],[/crkey\/([\d\.]+)/i],[b,[h,O+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[h,J],b],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[h,b],[/(sunos) ?([\w\.\d]*)/i],[[h,"Solaris"],b],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[h,b]]},ee=function(e,t){if(typeof e===l&&(t=e,e=s),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof o!==u&&o.navigator?o.navigator:s,n=e||(r&&r.userAgent?r.userAgent:""),i=r&&r.userAgentData?r.userAgentData:s,a=t?F(Q,t):Q,w=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[h]=s,t[b]=s,X.call(t,n,a.browser),t[p]=typeof(e=t[b])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:s,w&&r&&r.brave&&typeof r.brave.isBrave==c&&(t[h]="Brave"),t},this.getCPU=function(){var e={};return e[g]=s,X.call(e,n,a.cpu),e},this.getDevice=function(){var e={};return e[y]=s,e[f]=s,e[m]=s,X.call(e,n,a.device),w&&!e[m]&&i&&i.mobile&&(e[m]=_),w&&"Macintosh"==e[f]&&r&&typeof r.standalone!==u&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[f]="iPad",e[m]=v),e},this.getEngine=function(){var e={};return e[h]=s,e[b]=s,X.call(e,n,a.engine),e},this.getOS=function(){var e={};return e[h]=s,e[b]=s,X.call(e,n,a.os),w&&!e[h]&&i&&"Unknown"!=i.platform&&(e[h]=i.platform.replace(/chrome os/i,J).replace(/macos/i,B)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?V(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=z([h,b,p]),ee.CPU=z([g]),ee.DEVICE=z([f,y,m,w,_,E,v,k,S]),ee.ENGINE=ee.OS=z([h,b]),typeof a!==u?(i.exports&&(a=i.exports=ee),a.UAParser=ee):r.amdO?void 0===(n=(function(){return ee}).call(t,r,t,e))||(e.exports=n):typeof o!==u&&(o.UAParser=ee);var et=typeof o!==u&&(o.jQuery||o.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},a={};function o(e){var t=a[e];if(void 0!==t)return t.exports;var r=a[e]={exports:{}},n=!0;try{i[e].call(r.exports,r,r.exports,o),n=!1}finally{n&&delete a[e]}return r.exports}o.ab=__dirname+"/",e.exports=o(226)})()},77926:()=>{},80461:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"after",{enumerable:!0,get:function(){return i}});let n=r(29294);function i(e){let t=n.workAsyncStorage.getStore();if(!t)throw Object.defineProperty(Error("`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context"),"__NEXT_ERROR_CODE",{value:"E468",enumerable:!1,configurable:!0});let{afterContext:r}=t;return r.after(e)}},80656:(e,t,r)=>{"use strict";r.d(t,{b:()=>s});var n=r(63185),i=r(34245),a=r(84464),o=n.forwardRef((e,t)=>(0,a.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var s=o},85912:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>l,routeModule:()=>p,tree:()=>u});var n=r(49994),i=r(18765),a=r(42117),o=r.n(a),s=r(91962),c={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>s[e]);r.d(t,c);let u={children:["",{children:["(auth)",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,26493)),"C:\\chatbot\\front-chat\\app\\(auth)\\login\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,17638)),"C:\\chatbot\\front-chat\\app\\(auth)\\login\\layout.tsx"]}]},{forbidden:[()=>Promise.resolve().then(r.t.bind(r,81494,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,61135,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(r.bind(r,96592)),"C:\\chatbot\\front-chat\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,76532)),"C:\\chatbot\\front-chat\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,81494,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,61135,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,l=["C:\\chatbot\\front-chat\\app\\(auth)\\login\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(auth)/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},85941:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return i}});let n=""+r(86626).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function i(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86626:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return i},getAccessFallbackErrorTypeByStatus:function(){return s},getAccessFallbackHTTPStatus:function(){return o},isHTTPAccessFallbackError:function(){return a}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),i="NEXT_HTTP_ERROR_FALLBACK";function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}function o(e){return Number(e.digest.split(";")[1])}function s(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let n=r(67157);function i(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},90896:(e,t)=>{"use strict";function r(e,t,r){if(e)for(let a of(r&&(r=r.toLowerCase()),e)){var n,i;if(t===(null==(n=a.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===a.defaultLocale.toLowerCase()||(null==(i=a.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return a}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"detectDomainLocale",{enumerable:!0,get:function(){return r}})},93870:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return a}});let n=r(64182),i=r(87437);function a(e,t,r,a){if(!t||t===r)return e;let o=e.toLowerCase();return!a&&((0,i.pathHasPrefix)(o,"/api")||(0,i.pathHasPrefix)(o,"/"+t.toLowerCase()))?e:(0,n.addPathPrefix)(e,"/"+t)}},95054:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERNALS:function(){return s},NextRequest:function(){return c}});let n=r(7654),i=r(61016),a=r(60953),o=r(46628),s=Symbol("internal request");class c extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);(0,i.validateURL)(r),t.body&&"half"!==t.duplex&&(t.duplex="half"),e instanceof Request?super(e,t):super(r,t);let a=new n.NextURL(r,{headers:(0,i.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:t.nextConfig});this[s]={cookies:new o.RequestCookies(this.headers),nextUrl:a,url:a.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[s].cookies}get nextUrl(){return this[s].nextUrl}get page(){throw new a.RemovedPageError}get ua(){throw new a.RemovedUAError}get url(){return this[s].url}}},96684:(e,t,r)=>{"use strict";r.d(t,{$:()=>u,r:()=>c});var n=r(84464),i=r(63185),a=r(3166),o=r(27872),s=r(72487);let c=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=i.forwardRef(({className:e,variant:t,size:r,asChild:i=!1,...o},u)=>{let l=i?a.DX:"button";return(0,n.jsx)(l,{className:(0,s.cn)(c({variant:t,size:r,className:e})),ref:u,...o})});u.displayName="Button"},97535:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return i},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return a}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function i(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let a=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},98171:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return d},getRedirectTypeFromError:function(){return l},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return c},redirect:function(){return s}});let n=r(16700),i=r(54114),a=r(19121).actionAsyncStorage;function o(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let a=Object.defineProperty(Error(i.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return a.digest=i.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",a}function s(e,t){var r;throw null!=t||(t=(null==a||null==(r=a.getStore())?void 0:r.isAction)?i.RedirectType.push:i.RedirectType.replace),o(e,t,n.RedirectStatusCode.TemporaryRedirect)}function c(e,t){throw void 0===t&&(t=i.RedirectType.replace),o(e,t,n.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,i.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function l(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function d(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},99741:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},99882:(e,t)=>{"use strict";function r(){throw Object.defineProperty(Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead'),"__NEXT_ERROR_CODE",{value:"E183",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageResponse",{enumerable:!0,get:function(){return r}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[332,180,228,309,391],()=>r(85912));module.exports=n})();