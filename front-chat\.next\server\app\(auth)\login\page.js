const CHUNK_PUBLIC_PATH = "server/app/(auth)/login/page.js";
const runtime = require("../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/1e20b_next_dist_4c450b9f._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__50a7c09c._.js");
runtime.loadChunk("server/chunks/ssr/_99f71a29._.js");
runtime.loadChunk("server/chunks/ssr/_5616be1d._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_166b3b69._.js");
runtime.loadChunk("server/chunks/ssr/1e20b_next_dist_client_components_unauthorized-error_fa04f8ae.js");
runtime.loadChunk("server/chunks/ssr/app_(auth)_login_layout_tsx_ebe000a3._.js");
runtime.loadChunk("server/chunks/ssr/1e20b_next_3406427c._.js");
runtime.loadChunk("server/chunks/ssr/cdf59_zod_lib_index_mjs_a06c286f._.js");
runtime.loadChunk("server/chunks/ssr/9c5b9_@auth_core_07b45ed2._.js");
runtime.loadChunk("server/chunks/ssr/96a70_jose_dist_webapi_22776612._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_3ed018b9._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__0abe80a2._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/(auth)/login/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(auth)/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/esm/build/templates/app-page.js?page=/(auth)/login/page { MODULE_0 => \"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/app/(auth)/login/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/app/(auth)/login/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/esm/build/templates/app-page.js?page=/(auth)/login/page { MODULE_0 => \"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/app/(auth)/login/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/app/(auth)/login/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
