# 배경지도 컨트롤 (BasemapControl)

## 설명
배경지도를 설정하고 변경할 수 있는 컨트롤입니다. 다양한 유형의 배경지도를 지원하며, 사용자가 쉽게 전환할 수 있도록 합니다.

### 키워드:
[배경지도 생성, 베이스맵, 베이스맵 컨트롤, basemapControl, 배경지도 설정]

### 사용 예시:
```javascript
//배경지도 컨트롤 생성
var basemapControl = new odf.BasemapControl();
basemapControl.setMap(map);

//특정 배경지도(색각지도)로 전환
basemapControl.switchBaseLayer('eMapColor');

//설정 가능한 배경지도 목록 조회
var basemapList = basemapControl.getSetableBasemapList();
```

### 주요 메서드:
- setMap(map): 컨트롤을 지도에 추가
- switchBaseLayer(layerKey): 특정 배경지도로 전환
- getSetableBasemapList(): 설정 가능한 배경지도 목록 조회

### 일반적인 사용 시나리오:
1. 기본 지도와 위성 이미지 간 전환
2. 테마별 지도 (교통, 지형 등) 제공
3. 사용자 정의 배경지도 추가 및 관리

# 줌 컨트롤 (ZoomControl)

## 설명
지도의 확대 및 축소 기능을 제공하는 컨트롤입니다. 사용자가 지도의 상세 수준을 쉽게 조절할 수 있게 합니다.

### 키워드:
[줌 컨트롤, 지도 확대, 지도 축소, 줌 레벨 조정]

### 사용 예시:
```javascript
//줌 컨트롤 생성
var zoomControl = new odf.ZoomControl();
zoomControl.setMap(map);

//지도 확대
zoomControl.zoomIn();

//지도 축소
zoomControl.zoomOut();
```

### 주요 메서드:
- setMap(map): 컨트롤을 지도에 추가
- zoomIn(): 지도 확대
- zoomOut(): 지도 축소

### 일반적인 사용 시나리오:
1. 특정 지역의 상세 정보 확인
2. 넓은 영역의 개요 파악
3. 동적인 지도 탐색

# 오버뷰 컨트롤 (OverviewMapControl)

## 설명
현재 보고 있는 지도 영역의 전체적인 위치를 작은 지도로 보여주는 컨트롤입니다.

### 키워드:
[오버뷰 컨트롤, 미니맵, 전체 지도 보기, 위치 컨텍스트]

### 사용 예시:
```javascript
//overviewMapControl 생성
var overviewMapControl = new odf.OverviewMapControl();
overviewMapControl.setMap(map);
```

### 주요 메서드:
- setMap(map): 컨트롤을 지도에 추가

### 일반적인 사용 시나리오:
1. 현재 보고 있는 영역의 주변 지역 파악
2. 빠른 네비게이션 및 위치 이동
3. 지도 전체에서의 현재 위치 확인

# 축척 컨트롤 (ScaleControl)

## 설명
지도의 현재 축척을 표시하는 컨트롤입니다. 사용자에게 지도의 거리 정보를 제공합니다.

### 키워드:
[축척, 지도 축척, 거리 표시, 스케일 바]

### 사용 예시:
```javascript
var scaleControl = new odf.ScaleControl();
scaleControl.setMap(map);
```

### 주요 메서드:
- setMap(map): 컨트롤을 지도에 추가
- setScaleValue(scale): 축척 설정 (1:5000 -> 5000 입력)

### 일반적인 사용 시나리오:
1. 지도 상의 실제 거리 파악
2. 정확한 측정 및 계획 수립
3. 다양한 줌 레벨에서의 거리 감각 제공

# 이전/다음 화면 이동 컨트롤 (MoveControl)

## 설명
이전에 본 지도 위치로 이동하거나 다음 위치로 이동할 수 있는 기능을 제공하는 컨트롤입니다.

### 키워드:
[이전/다음 화면 이동, 지도 네비게이션, 위치 기억, 브라우징 히스토리]

### 사용 예시:
```javascript
var moveControl = new odf.MoveControl();
moveControl.setMap(map);

//이전 위치로 이동
moveControl.back();

//다음 위치로 이동
moveControl.forward();
```

### 주요 메서드:
- setMap(map): 컨트롤을 지도에 추가
- back(): 이전 위치로 이동
- forward(): 다음 위치로 이동

### 일반적인 사용 시나리오:
1. 여러 위치를 탐색한 후 이전 위치로 빠르게 돌아가기
2. 지도 브라우징 히스토리 관리
3. 효율적인 지도 탐색 경험 제공

# 마우스 좌표 컨트롤 (MousePositionControl)
## 설명
마우스 커서의 현재 위치 좌표를 실시간으로 표시해주는 컨트롤입니다.
### 키워드:
[마우스 좌표 표시, 위치 정보, 좌표계, 실시간 위치]
### 사용 예시:
```html
<div id="coordDiv"></div>
```
```javascript
var mousePositionControl = new odf.MousePositionControl({
  element: document.querySelector('#coordDiv'), // 마우스 좌표를 표시할 element 영역
  callback: function (position) {
    console.log(position);
  },
});
mousePositionControl.setMap(map);
```

### 주요 메서드:
- setMap(map): 컨트롤을 지도에 추가

### 일반적인 사용 시나리오:
1. 정확한 위치 정보 필요 시 (예: GIS 애플리케이션)
2. 개발 및 디버깅 과정에서의 위치 확인
3. 사용자에게 상세한 위치 정보 제공

# 그리기 도구 컨트롤 (DrawControl)

## 설명
지도 위에 다양한 도형이나 마커를 그릴 수 있는 도구를 제공하는 컨트롤입니다.

### 키워드:
[그리기 도구, 지도 마킹, 도형 그리기, 사용자 정의 오버레이]

### 사용 예시:
```javascript
var drawControl = new odf.DrawControl();
drawControl.setMap(map);

```

### 주요 메서드:
- setMap(map): 컨트롤을 지도에 추가
- setDrawingMode(mode): 그리기 모드 설정 (점, 선, 다각형 등)
- getFeatures(): 그려진 객체 목록 가져오기

### 일반적인 사용 시나리오:
1. 사용자 정의 영역 표시
2. 거리 또는 면적 측정
3. 협업을 위한 지도 상 정보 공유

# 레이어 추가 (odf.LayerFactory.produce)

## 설명
지도 위에 레이어를 추가할 수 있습니다.

### 키워드:
[레이어 추가, WMS, 지도 레이어, 데이터 시각화]

### 사용 예시:
```javascript
// WMS 레이어 생성
var wmsLayer = odf.LayerFactory.produce('geoserver', {
    method: 'get',
    server: 'https://geoserver.geon.kr/geoserver',
    layer: 'geonpaas:L100000254',
    service: 'wms',
    tiled: false,
});
```

### layer 정보:
- Wmappickadmin:L100002910 : 지하철역
- Wh2jung:L100002896 : 버거킹

// 레이어를 지도에 추가
wmsLayer.setMap(map);

// 레이어가 한눈에 보이는 extent로 화면 위치 이동 및 줌 레벨 변경
wmsLayer.fit();

### 주요 메서드:
- produce(): 레이어 생성
- setMap(map): 레이어를 지도에 추가
- fit(): 레이어에 맞춰 지도 뷰 조정

### 일반적인 사용 시나리오:
1. 외부 데이터 소스의 지리 정보 표시
2. 주제별 레이어 추가 (예: 인구 밀도, 기상 정보)
3. 동적 데이터 시각화

# 스와이퍼 컨트롤 (SwiperControl)

## 설명
기본 지도를 생성한 후 스와이퍼 컨트롤(SwiperControl)을 추가합니다. 이 컨트롤은 두 개의 지도 레이어를 나란히 비교할 수 있게 해줍니다.

### 키워드
[스와이퍼, 지도 비교, 레이어 비교, swiperControl]

### 사용 예시
```javascript
// 배경지도 컨트롤에서 바로e맵 항공지도 레이어 조회, basemapControl이 위에 선언되어 있어야 합니다.
var rightLayer = basemapControl.getBaseLayer(basemapKey);
//스와이퍼 컨트롤 생성(원본 레이어를 왼쪽 영역에 표출, 정의한 레이어는 오른쪽 영역에 표출)
var swiperControl  = new odf.SwiperControl({
    layers : [rightLayer]
});
//생성한 스와이퍼 컨트롤을 지도 객체에 추가
swiperControl.setMap(map);trol.setMap(map);
```

### 주요 메서드
- setMap(map): 컨트롤을 지도에 추가

### 일반적인 사용 시나리오
1. 서로 다른 시기의 위성 이미지 비교
2. 일반 지도와 테마 지도 비교
3. 서로 다른 데이터 레이어 비교

# 분할지도 컨트롤 (DivideMapControl)

## 설명
기본 지도를 생성한 후 분할지도 컨트롤(DivideMapControl)을 추가합니다. 이 컨트롤은 화면을 여러 개의 지도 영역으로 나누어 다양한 배경지도를 동시에 표시할 수 있게 해줍니다.

### 키워드
[분할지도, 멀티뷰, 다중 지도, divideMapControl]

### 사용 예시
```javascript
//분할지도 컨트롤 생성
var divideMapControl = new odf.DivideMapControl({
    //2분할지도 정의
    dualMap : [
        {
            mapOption : {
                basemap : {baroEMap : ['eMapAIR']},
            },
        },
    ],
    //3분할지도 정의
    threepleMap: [
        {mapOption: {
                basemap: {baroEMap:['eMapColor']},
            },
        },
        { mapOption: {
                basemap:{baroEMap: ['eMapWhite']},
            },
        },
    ],
    //4분할지도 정의
    quadMap: [
        {
            mapOption: {
                basemap: {baroEMap : ['eMapAIR']},
            },
        },
        {
            mapOption: {
                basemap: {baroEMap : ['eMapColor']} ,
            },
        },
        {
            mapOption: {
                basemap: {baroEMap : ['eMapWhite']} ,
            },
        },
    ],
});
//생성한 분할지도 컨트롤을 지도 객체에 추가
divideMapControl.setMap(map);
```

### 주요 메서드
- setMap(map): 컨트롤을 지도에 추가

### 일반적인 사용 시나리오
1. 여러 지역의 동시 비교
2. 다양한 테마 지도의 동시 표시
3. 시계열 데이터의 동시 표현
