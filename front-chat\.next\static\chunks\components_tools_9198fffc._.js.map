{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/common/compact-result-trigger.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useId, useEffect } from \"react\";\nimport { cn } from \"@/lib/utils\";\nimport {\n  Accordion,\n  AccordionItem,\n  AccordionTrigger,\n  AccordionContent,\n} from \"@/components/ui/accordion\";\nimport { ChevronDown, ChevronsUpDown, Expand } from \"lucide-react\";\nimport { componentStyles } from \"@/lib/design-tokens\";\n\ninterface CompactResultTriggerProps {\n  /** 도구 아이콘 (기본 상태) */\n  icon: React.ReactNode;\n  /** 도구 이름 */\n  title: string;\n  /** 상태 (\"call\" | \"result\" | \"partial-call\") */\n  state: \"call\" | \"result\" | \"partial-call\";\n  /** 아코디언 내용 */\n  children: React.ReactNode;\n  /** 추가 CSS 클래스 */\n  className?: string;\n  /** 초기 열림 상태 */\n  initialOpen?: boolean;\n  /** 외부에서 제어하는 열림 상태 */\n  open?: boolean;\n  /** 도구 이름 옆에 표시할 추가 정보 */\n  titleExtra?: React.ReactNode;\n}\n\nfunction getStateConfig(state: \"call\" | \"result\" | \"partial-call\") {\n  switch (state) {\n    case \"call\":\n      return {\n        color: \"bg-orange-500\", // 주황색 원\n        label: \"처리 중\",\n      };\n    case \"partial-call\":\n      return {\n        color: \"bg-red-500\", // 빨간색 원\n        label: \"진행 중\",\n      };\n    case \"result\":\n      return {\n        color: \"bg-green-500\", // 초록색 원\n        label: \"완료\",\n      };\n    default:\n      return {\n        color: \"bg-gray-500\", // 회색 원\n        label: \"알 수 없음\",\n      };\n  }\n}\n\nexport function CompactResultTrigger({\n  icon,\n  title,\n  state,\n  children,\n  className,\n  initialOpen = false,\n  open,\n  titleExtra,\n}: CompactResultTriggerProps) {\n  const uniqueId = useId();\n  const itemValue = `result-${uniqueId}`;\n  const [isOpenInternal, setIsOpenInternal] = useState(initialOpen);\n  const stateConfig = getStateConfig(state);\n\n  useEffect(() => {\n    if (open !== undefined) {\n      setIsOpenInternal(open);\n    }\n  }, [open]);\n\n  const accordionValue = isOpenInternal ? itemValue : \"\";\n\n  return (\n    <div className={cn(componentStyles.card.base, \"bg-white/90 border-neutral-200/60\", className)}>\n      <Accordion\n        type=\"single\"\n        collapsible\n        value={accordionValue}\n        onValueChange={(val) => setIsOpenInternal(val === itemValue)}\n        className=\"w-full\"\n      >\n        <AccordionItem value={itemValue} className=\"border-0\">\n          <AccordionTrigger className=\"flex items-center gap-3 p-2 pr-4 hover:no-underline [&>svg]:hidden group hover:bg-neutral-50/50 transition-colors min-h-[40px]\">\n            {/* 왼쪽: 도구 아이콘 + 도구명 */}\n            <div className=\"flex items-center gap-1 flex-1 min-w-0\">\n              {/* 도구 아이콘 - hover 시 expand로 변경 */}\n              <div className={cn(componentStyles.iconContainer.sm, \"bg-neutral-100/80 text-neutral-600 group-hover:bg-neutral-200/80 transition-colors flex-shrink-0\")}>\n                <div className=\"group-hover:hidden\">\n                  {icon}\n                </div>\n                <div className=\"hidden group-hover:block\">\n                  <ChevronsUpDown className=\"w-4 h-4\" />\n                </div>\n              </div>\n\n              {/* 도구 이름 */}\n              <span className=\"text-neutral-900 text-[13px] truncate\">\n                {title}\n              </span>\n\n              {/* 추가 정보 */}\n              {titleExtra && (\n                <div\n                  className=\"flex-shrink-0 ml-1\"\n                  onClick={(e) => e.stopPropagation()}\n                >\n                  {titleExtra}\n                </div>\n              )}\n            </div>\n\n            {/* 오른쪽: 상태 컬러 원만 표시 */}\n            <div className=\"flex items-center gap-2 flex-shrink-0\">\n              <div\n                className={cn(\"w-2.5 h-2.5 rounded-full\", stateConfig.color)}\n                title={stateConfig.label}\n              />\n            </div>\n          </AccordionTrigger>\n\n          <AccordionContent className=\"px-3 pb-3\">\n            <div className=\"mt-2 rounded-lg bg-neutral-50/60 border border-neutral-200/50 p-3 text-sm\">\n              {children}\n            </div>\n          </AccordionContent>\n        </AccordionItem>\n      </Accordion>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAMA;AACA;;;AAXA;;;;;;AAgCA,SAAS,eAAe,KAAyC;IAC/D,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,OAAO;YACT;QACF,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,OAAO;YACT;QACF,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,OAAO;YACT;QACF;YACE,OAAO;gBACL,OAAO;gBACP,OAAO;YACT;IACJ;AACF;AAEO,SAAS,qBAAqB,EACnC,IAAI,EACJ,KAAK,EACL,KAAK,EACL,QAAQ,EACR,SAAS,EACT,cAAc,KAAK,EACnB,IAAI,EACJ,UAAU,EACgB;;IAC1B,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,QAAK,AAAD;IACrB,MAAM,YAAY,CAAC,OAAO,EAAE,UAAU;IACtC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,cAAc,eAAe;IAEnC,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,SAAS,WAAW;gBACtB,kBAAkB;YACpB;QACF;yCAAG;QAAC;KAAK;IAET,MAAM,iBAAiB,iBAAiB,YAAY;IAEpD,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,IAAI,EAAE,qCAAqC;kBACjF,cAAA,sSAAC,iIAAA,CAAA,YAAS;YACR,MAAK;YACL,WAAW;YACX,OAAO;YACP,eAAe,CAAC,MAAQ,kBAAkB,QAAQ;YAClD,WAAU;sBAEV,cAAA,sSAAC,iIAAA,CAAA,gBAAa;gBAAC,OAAO;gBAAW,WAAU;;kCACzC,sSAAC,iIAAA,CAAA,mBAAgB;wBAAC,WAAU;;0CAE1B,sSAAC;gCAAI,WAAU;;kDAEb,sSAAC;wCAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE,EAAE;;0DACnD,sSAAC;gDAAI,WAAU;0DACZ;;;;;;0DAEH,sSAAC;gDAAI,WAAU;0DACb,cAAA,sSAAC,qTAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAK9B,sSAAC;wCAAK,WAAU;kDACb;;;;;;oCAIF,4BACC,sSAAC;wCACC,WAAU;wCACV,SAAS,CAAC,IAAM,EAAE,eAAe;kDAEhC;;;;;;;;;;;;0CAMP,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC;oCACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B,YAAY,KAAK;oCAC3D,OAAO,YAAY,KAAK;;;;;;;;;;;;;;;;;kCAK9B,sSAAC,iIAAA,CAAA,mBAAgB;wBAAC,WAAU;kCAC1B,cAAA,sSAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAhFgB;;QAUG,sQAAA,CAAA,QAAK;;;KAVR", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/tool-result.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { AddressResponse } from \"@/types/tools\";\r\nimport {\r\n  Layers3,\r\n  Compass,\r\n  HelpCircle,\r\n  CheckCircle,\r\n  MapPin,\r\n  Search as SearchIcon,\r\n  Zap,\r\n  Bot,\r\n  Palette\r\n} from \"lucide-react\";\r\nimport { componentStyles } from \"@/lib/design-tokens\";\r\nimport { CompactResultTrigger } from \"./common/compact-result-trigger\";\r\nimport { getToolDisplayInfo } from \"../annotations\";\r\n\r\ntype DisplayResult = string | object | AddressResponse;\r\n\r\ninterface ToolResultProps {\r\n  toolName: string;\r\n  state: \"call\" | \"result\" | \"partial-call\";\r\n  content: string;\r\n  className?: string;\r\n  initialOpen?: boolean;\r\n  open?: boolean;\r\n}\r\n\r\n\r\n\r\nexport function ToolResult({\r\n  toolName,\r\n  state,\r\n  content,\r\n  className,\r\n  initialOpen = false,\r\n  open,\r\n}: ToolResultProps) {\r\n  const isSuccess = state === \"result\";\r\n\r\n  // Map internal tool names to user-friendly labels\r\n  const TOOL_DISPLAY_NAMES: Record<string, string> = {\r\n    createLayerFilter: \"레이어 필터 적용 완료\",\r\n    getLayer: \"레이어 정보 조회 완료\",\r\n    getLayerAttributes: \"레이어 속성 조회 완료\",\r\n    getLayerList: \"레이어 목록 검색 완료\",\r\n    performDensityAnalysis: \"밀도 분석 완료\",\r\n    searchAddress: \"주소 검색 완료\",\r\n    searchOrigin: \"출발지 검색 완료\",\r\n    searchDestination: \"목적지 검색 완료\",\r\n    searchDirections: \"경로 탐색 완료\",\r\n    changeBasemap: \"배경지도 변경 완료\",\r\n    createVectorStyle: \"벡터 스타일 생성 완료\",\r\n    getLocation: \"현재 위치 확인 완료\",\r\n    highlightGeometry: \"도형 강조 완료\",\r\n    setCenter: \"지도 중심 이동 완료\",\r\n    chooseOption: \"선택 완료\",\r\n    getUserInput: \"정보 입력 완료\",\r\n    confirmWithCheckbox: \"확인 완료\",\r\n    moveMapByDirection: \"지도 이동 완료\",\r\n    setMapZoom: \"지도 확대/축소 완료\",\r\n    generateCategoricalStyle: \"유형별 스타일 생성 완료\",\r\n  };\r\n\r\n  const friendlyName = TOOL_DISPLAY_NAMES[toolName] || `${toolName} 실행 결과`;\r\n  const HIL_TOOLS = [\"chooseOption\", \"getUserInput\", \"confirmWithCheckbox\", \"getLocation\"];\r\n  const isHilTool = HIL_TOOLS.includes(toolName);\r\n\r\n  // 아이콘 매핑 (tool-call.tsx의 TOOL_CONFIG와 유사하게 구성)\r\n  const TOOL_ICONS: Record<string, React.ReactNode> = {\r\n    createLayerFilter: <Layers3 className=\"h-4 w-4\" />,\r\n    updateLayerStyle: <Palette className=\"h-4 w-4\" />,\r\n    getLayer: <Layers3 className=\"h-4 w-4\" />,\r\n    getLayerAttributes: <SearchIcon className=\"h-4 w-4\" />,\r\n    getLayerList: <SearchIcon className=\"h-4 w-4\" />,\r\n    performDensityAnalysis: <Zap className=\"h-4 w-4\" />,\r\n    searchAddress: <SearchIcon className=\"h-4 w-4\" />,\r\n    searchOrigin: <MapPin className=\"h-4 w-4 text-green-600\" />,\r\n    searchDestination: <MapPin className=\"h-4 w-4 text-blue-600\" />,\r\n    searchDirections: <Compass className=\"h-4 w-4\" />,\r\n    changeBasemap: <MapPin className=\"h-4 w-4\" />,\r\n    createVectorStyle: <Zap className=\"h-4 w-4\" />,\r\n    getLocation: <MapPin className=\"h-4 w-4\" />,\r\n    highlightGeometry: <Zap className=\"h-4 w-4\" />,\r\n    setCenter: <MapPin className=\"h-4 w-4\" />,\r\n    chooseOption: <Bot className=\"h-4 w-4\" />,\r\n    getUserInput: <HelpCircle className=\"h-4 w-4\" />,\r\n    confirmWithCheckbox: <CheckCircle className=\"h-4 w-4\" />,\r\n    moveMapByDirection: <Compass className=\"h-4 w-4\" />,\r\n    setMapZoom: <Compass className=\"h-4 w-4\" />,\r\n    generateCategoricalStyle: <Palette className=\"h-4 w-4\" />,\r\n  };\r\n  const toolIcon = TOOL_ICONS[toolName] || <Zap className=\"h-4 w-4\" />; // 기본 아이콘\r\n\r\n  const resultMessage = isSuccess\r\n    ? \"실행 결과\"\r\n    : state === \"partial-call\"\r\n    ? \"작업이 부분적으로 완료되었어요\"\r\n    : \"작업을 처리하는 중이에요\";\r\n\r\n  let displayResult: DisplayResult = content;\r\n  if (typeof content === \"string\") {\r\n    try {\r\n      const parsedResult = JSON.parse(content);\r\n      if (parsedResult && typeof parsedResult === \"object\") {\r\n        displayResult = parsedResult as AddressResponse;\r\n      }\r\n    } catch (e) {\r\n      // 파싱 실패 시 문자열 그대로 사용\r\n    }\r\n  }\r\n\r\n  if (isHilTool) {\r\n    let hilContent = \"\";\r\n    if (typeof displayResult === 'object' && displayResult !== null) {\r\n      const obj = displayResult as Record<string, any>; // Cast to an indexable type\r\n      if (toolName === \"chooseOption\") {\r\n        hilContent = `선택: ${'option' in obj ? obj.option : JSON.stringify(obj)}`;\r\n      } else if (toolName === \"getUserInput\") {\r\n        hilContent = `입력: ${'input' in obj ? obj.input : JSON.stringify(obj)}`;\r\n      } else if (toolName === \"confirmWithCheckbox\") {\r\n        hilContent = ('confirmed' in obj && obj.confirmed === true) ? \"확인 및 동의 완료\" : \"동의하지 않음\";\r\n      } else if (toolName === \"getLocation\") {\r\n        if ('latitude' in obj && 'longitude' in obj) {\r\n          const lat = Number(obj.latitude).toFixed(6);\r\n          const lng = Number(obj.longitude).toFixed(6);\r\n          const accuracy = obj.accuracy ? ` (정확도: ${Math.round(obj.accuracy)}m)` : '';\r\n          hilContent = `위치: ${lat}, ${lng}${accuracy}`;\r\n        } else {\r\n          hilContent = JSON.stringify(obj);\r\n        }\r\n      } else {\r\n        hilContent = JSON.stringify(obj); // Should not happen for defined HIL tools\r\n      }\r\n    } else if (typeof displayResult === 'string') {\r\n      if (toolName === \"chooseOption\") {\r\n        const [selectedOption] = displayResult.split('|');\r\n        hilContent = `${selectedOption}`;\r\n      }\r\n      else if (toolName === \"confirmWithCheckbox\") {\r\n        hilContent = displayResult.toLowerCase() === 'true' ? \"확인 및 동의 완료\" : \"동의하지 않음\";\r\n      } else {\r\n        hilContent = displayResult; // For chooseOption, getUserInput, getLocation\r\n      }\r\n    } else if (typeof displayResult === 'boolean') {\r\n      if (toolName === \"confirmWithCheckbox\") {\r\n        hilContent = displayResult === true ? \"확인 및 동의 완료\" : \"동의하지 않음\";\r\n      } else {\r\n         hilContent = String(displayResult); // Should be rare for other HIL tools\r\n      }\r\n    } else {\r\n      // For null, undefined, numbers, or other unexpected types\r\n      hilContent = JSON.stringify(displayResult);\r\n    }\r\n\r\n    return (\r\n      <Card className={cn(componentStyles.card.base, \"bg-neutral-50/60 border-neutral-200/50\")}>\r\n        <CardHeader className=\"py-3 px-4\">\r\n          <CardTitle className=\"text-sm font-medium flex items-center gap-2\">\r\n            <div className={cn(componentStyles.iconContainer.sm, \"bg-neutral-100/80\")}>\r\n              {toolIcon}\r\n            </div>\r\n            <span className=\"text-neutral-900\">{friendlyName}</span>\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent className=\"px-4 pb-3 text-sm text-neutral-700\">\r\n          <p>{hilContent}</p>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  const toolInfo = getToolDisplayInfo(toolName);\r\n\r\n  return (\r\n    <CompactResultTrigger\r\n      icon={toolInfo.icon}\r\n      title={toolInfo.label}\r\n      state={state}\r\n      className={className}\r\n      initialOpen={initialOpen}\r\n      open={open}\r\n    >\r\n      {/* 결과 메시지 */}\r\n      <div className=\"text-xs text-neutral-600 mb-2\">{resultMessage}</div>\r\n\r\n      {/* 결과 내용 */}\r\n      <div className=\"text-neutral-700\">\r\n        {typeof displayResult === \"object\" ? (\r\n          <pre className=\"whitespace-pre-wrap text-xs font-mono bg-white/60 p-2 rounded border border-neutral-200/40 overflow-x-auto max-h-32 overflow-y-auto\">\r\n            {JSON.stringify(displayResult, null, 2)}\r\n          </pre>\r\n        ) : (\r\n          <div className=\"bg-white/60 p-2 rounded border border-neutral-200/40 max-h-32 overflow-y-auto\">\r\n            <p className=\"whitespace-pre-wrap text-xs\">{displayResult}</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </CompactResultTrigger>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AAnBA;;;;;;;;AAkCO,SAAS,WAAW,EACzB,QAAQ,EACR,KAAK,EACL,OAAO,EACP,SAAS,EACT,cAAc,KAAK,EACnB,IAAI,EACY;IAChB,MAAM,YAAY,UAAU;IAE5B,kDAAkD;IAClD,MAAM,qBAA6C;QACjD,mBAAmB;QACnB,UAAU;QACV,oBAAoB;QACpB,cAAc;QACd,wBAAwB;QACxB,eAAe;QACf,cAAc;QACd,mBAAmB;QACnB,kBAAkB;QAClB,eAAe;QACf,mBAAmB;QACnB,aAAa;QACb,mBAAmB;QACnB,WAAW;QACX,cAAc;QACd,cAAc;QACd,qBAAqB;QACrB,oBAAoB;QACpB,YAAY;QACZ,0BAA0B;IAC5B;IAEA,MAAM,eAAe,kBAAkB,CAAC,SAAS,IAAI,GAAG,SAAS,MAAM,CAAC;IACxE,MAAM,YAAY;QAAC;QAAgB;QAAgB;QAAuB;KAAc;IACxF,MAAM,YAAY,UAAU,QAAQ,CAAC;IAErC,+CAA+C;IAC/C,MAAM,aAA8C;QAClD,iCAAmB,sSAAC,mSAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACtC,gCAAkB,sSAAC,+RAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACrC,wBAAU,sSAAC,mSAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAC7B,kCAAoB,sSAAC,6RAAA,CAAA,SAAU;YAAC,WAAU;;;;;;QAC1C,4BAAc,sSAAC,6RAAA,CAAA,SAAU;YAAC,WAAU;;;;;;QACpC,sCAAwB,sSAAC,uRAAA,CAAA,MAAG;YAAC,WAAU;;;;;;QACvC,6BAAe,sSAAC,6RAAA,CAAA,SAAU;YAAC,WAAU;;;;;;QACrC,4BAAc,sSAAC,iSAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAChC,iCAAmB,sSAAC,iSAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACrC,gCAAkB,sSAAC,+RAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACrC,6BAAe,sSAAC,iSAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACjC,iCAAmB,sSAAC,uRAAA,CAAA,MAAG;YAAC,WAAU;;;;;;QAClC,2BAAa,sSAAC,iSAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAC/B,iCAAmB,sSAAC,uRAAA,CAAA,MAAG;YAAC,WAAU;;;;;;QAClC,yBAAW,sSAAC,iSAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAC7B,4BAAc,sSAAC,uRAAA,CAAA,MAAG;YAAC,WAAU;;;;;;QAC7B,4BAAc,sSAAC,ySAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QACpC,mCAAqB,sSAAC,kTAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC5C,kCAAoB,sSAAC,+RAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACvC,0BAAY,sSAAC,+RAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAC/B,wCAA0B,sSAAC,+RAAA,CAAA,UAAO;YAAC,WAAU;;;;;;IAC/C;IACA,MAAM,WAAW,UAAU,CAAC,SAAS,kBAAI,sSAAC,uRAAA,CAAA,MAAG;QAAC,WAAU;;;;;cAAc,SAAS;IAE/E,MAAM,gBAAgB,YAClB,UACA,UAAU,iBACV,qBACA;IAEJ,IAAI,gBAA+B;IACnC,IAAI,OAAO,YAAY,UAAU;QAC/B,IAAI;YACF,MAAM,eAAe,KAAK,KAAK,CAAC;YAChC,IAAI,gBAAgB,OAAO,iBAAiB,UAAU;gBACpD,gBAAgB;YAClB;QACF,EAAE,OAAO,GAAG;QACV,qBAAqB;QACvB;IACF;IAEA,IAAI,WAAW;QACb,IAAI,aAAa;QACjB,IAAI,OAAO,kBAAkB,YAAY,kBAAkB,MAAM;YAC/D,MAAM,MAAM,eAAsC,4BAA4B;YAC9E,IAAI,aAAa,gBAAgB;gBAC/B,aAAa,CAAC,IAAI,EAAE,YAAY,MAAM,IAAI,MAAM,GAAG,KAAK,SAAS,CAAC,MAAM;YAC1E,OAAO,IAAI,aAAa,gBAAgB;gBACtC,aAAa,CAAC,IAAI,EAAE,WAAW,MAAM,IAAI,KAAK,GAAG,KAAK,SAAS,CAAC,MAAM;YACxE,OAAO,IAAI,aAAa,uBAAuB;gBAC7C,aAAa,AAAC,eAAe,OAAO,IAAI,SAAS,KAAK,OAAQ,eAAe;YAC/E,OAAO,IAAI,aAAa,eAAe;gBACrC,IAAI,cAAc,OAAO,eAAe,KAAK;oBAC3C,MAAM,MAAM,OAAO,IAAI,QAAQ,EAAE,OAAO,CAAC;oBACzC,MAAM,MAAM,OAAO,IAAI,SAAS,EAAE,OAAO,CAAC;oBAC1C,MAAM,WAAW,IAAI,QAAQ,GAAG,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,IAAI,QAAQ,EAAE,EAAE,CAAC,GAAG;oBACzE,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,MAAM,UAAU;gBAC9C,OAAO;oBACL,aAAa,KAAK,SAAS,CAAC;gBAC9B;YACF,OAAO;gBACL,aAAa,KAAK,SAAS,CAAC,MAAM,0CAA0C;YAC9E;QACF,OAAO,IAAI,OAAO,kBAAkB,UAAU;YAC5C,IAAI,aAAa,gBAAgB;gBAC/B,MAAM,CAAC,eAAe,GAAG,cAAc,KAAK,CAAC;gBAC7C,aAAa,GAAG,gBAAgB;YAClC,OACK,IAAI,aAAa,uBAAuB;gBAC3C,aAAa,cAAc,WAAW,OAAO,SAAS,eAAe;YACvE,OAAO;gBACL,aAAa,eAAe,8CAA8C;YAC5E;QACF,OAAO,IAAI,OAAO,kBAAkB,WAAW;YAC7C,IAAI,aAAa,uBAAuB;gBACtC,aAAa,kBAAkB,OAAO,eAAe;YACvD,OAAO;gBACJ,aAAa,OAAO,gBAAgB,qCAAqC;YAC5E;QACF,OAAO;YACL,0DAA0D;YAC1D,aAAa,KAAK,SAAS,CAAC;QAC9B;QAEA,qBACE,sSAAC,4HAAA,CAAA,OAAI;YAAC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,IAAI,EAAE;;8BAC7C,sSAAC,4HAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,sSAAC,4HAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,sSAAC;gCAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE,EAAE;0CAClD;;;;;;0CAEH,sSAAC;gCAAK,WAAU;0CAAoB;;;;;;;;;;;;;;;;;8BAGxC,sSAAC,4HAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,sSAAC;kCAAG;;;;;;;;;;;;;;;;;IAIZ;IAEA,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;IAEpC,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;QACnB,MAAM,SAAS,IAAI;QACnB,OAAO,SAAS,KAAK;QACrB,OAAO;QACP,WAAW;QACX,aAAa;QACb,MAAM;;0BAGN,sSAAC;gBAAI,WAAU;0BAAiC;;;;;;0BAGhD,sSAAC;gBAAI,WAAU;0BACZ,OAAO,kBAAkB,yBACxB,sSAAC;oBAAI,WAAU;8BACZ,KAAK,SAAS,CAAC,eAAe,MAAM;;;;;yCAGvC,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;wBAAE,WAAU;kCAA+B;;;;;;;;;;;;;;;;;;;;;;AAMxD;KA1KgB", "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/suggestion-card.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport type { ToolInvocation } from \"ai\";\r\nimport { <PERSON>, Card<PERSON><PERSON>er, <PERSON><PERSON>ontent, CardFooter } from \"@/components/ui/card\";\r\nimport { Avatar, AvatarFallback } from \"@/components/ui/avatar\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\n\r\ninterface Props {\r\n  invocation: ToolInvocation;\r\n  onDone: (result: any) => void;\r\n}\r\n\r\n// Map internal tool names to user-friendly labels\r\nconst TOOL_DISPLAY_NAMES: Record<string, string> = {\r\n  chooseOption: \"옵션 선택\",\r\n  getUserInput: \"입력 요청\",\r\n  confirmWithCheckbox: \"확인 요청\",\r\n  getLocation: \"위치 정보 요청\",\r\n};\r\n\r\nconst getBadge = (state: \"call\" | \"partial-call\" | \"result\") => {\r\n  switch (state) {\r\n    case \"call\":\r\n      return <Badge variant=\"outline\" className=\"border-yellow-500 text-yellow-700\">실행 중</Badge>;\r\n    case \"partial-call\":\r\n      return <Badge variant=\"outline\" className=\"border-red-500 text-red-700\">진행 중</Badge>;\r\n    case \"result\":\r\n      return <Badge variant=\"outline\" className=\"border-green-500 text-green-700\">완료</Badge>;\r\n    default:\r\n      return null;\r\n  }\r\n};\r\n\r\nexport function SuggestionCard({ invocation, onDone }: Props) {\r\n  const { toolName, toolCallId, state } = invocation;\r\n  const friendlyName = TOOL_DISPLAY_NAMES[toolName] ?? toolName;\r\n\r\n  const renderInteractive = () => {\r\n    switch (toolName) {\r\n      case \"chooseOption\": {\r\n        const { message, options } = (invocation.args || {}) as any;\r\n\r\n        // 옵션 개수에 따라 그리드 컬럼 수 결정\r\n        const getGridCols = (optionCount: number) => {\r\n          if (optionCount <= 2) return \"grid-cols-1 sm:grid-cols-2\";\r\n          if (optionCount <= 4) return \"grid-cols-2 sm:grid-cols-2\";\r\n          if (optionCount <= 6) return \"grid-cols-2 sm:grid-cols-3\";\r\n          return \"grid-cols-2 sm:grid-cols-3 lg:grid-cols-4\";\r\n        };\r\n\r\n        const gridCols = getGridCols(options?.length || 0);\r\n\r\n        return (\r\n          <>\r\n            <p className=\"text-sm whitespace-pre-wrap break-all mb-2\">{message}</p>\r\n            <div className={`grid ${gridCols} gap-2`}>\r\n              {options?.map((opt: string, index: number) => {\r\n                // \"레이어명|레이어ID\" 형태에서 레이어명만 추출하여 표시\r\n                const displayText = opt.includes('|') ? opt.split('|')[0] : opt;\r\n\r\n                return (\r\n                  <Button\r\n                    key={`${toolCallId}-${index}-${opt.substring(0, 20)}`}\r\n                    variant=\"outline\"\r\n                    className=\"justify-start gap-2 h-auto hover:bg-primary/10 break-words whitespace-break-spaces transition-transform hover:-translate-y-0.5\"\r\n                    onClick={() => {\r\n                      onDone({toolCallId, result: opt}); // 전체 문자열 반환\r\n                    }}\r\n                  >\r\n                    {displayText}\r\n                  </Button>\r\n                );\r\n              })}\r\n            </div>\r\n          </>\r\n        );\r\n      }\r\n      case \"getUserInput\": {\r\n        const { message, default: defaultVal } = (invocation.args || {}) as any;\r\n        const [value, setValue] = useState<string>(defaultVal ?? \"\");\r\n        return (\r\n          <>\r\n            <p className=\"text-sm whitespace-pre-wrap break-all mb-2\">{message}</p>\r\n            <Input value={value} onChange={(e) => setValue(e.target.value)} />\r\n            <div className=\"mt-2 flex gap-2\">\r\n              <Button onClick={() => onDone({toolCallId, result: value})} disabled={value.trim() === \"\"}>확인</Button>\r\n            </div>\r\n          </>\r\n        );\r\n      }\r\n      case \"confirmWithCheckbox\": {\r\n        const { message, label } = (invocation.args || {}) as any;\r\n        const [checked, setChecked] = useState(false);\r\n        return (\r\n          <>\r\n            <p className=\"text-sm whitespace-pre-wrap break-all mb-2\">{message}</p>\r\n            <label className=\"flex items-center gap-2 mb-2\">\r\n              <Checkbox checked={checked} onCheckedChange={setChecked as any} /> <span>{label}</span>\r\n            </label>\r\n            <Button onClick={() => onDone({toolCallId, result: checked})} disabled={!checked}>확인</Button>\r\n          </>\r\n        );\r\n      }\r\n      case \"getLocation\": {\r\n        const { message } = (invocation.args || {}) as any;\r\n        const [isLoading, setIsLoading] = useState(false);\r\n        const [error, setError] = useState<string | null>(null);\r\n\r\n        const handleGetLocation = () => {\r\n          setIsLoading(true);\r\n          setError(null);\r\n\r\n          if (!navigator.geolocation) {\r\n            setError(\"이 브라우저는 위치 서비스를 지원하지 않습니다.\");\r\n            setIsLoading(false);\r\n            return;\r\n          }\r\n\r\n          navigator.geolocation.getCurrentPosition(\r\n            (position) => {\r\n              const { latitude, longitude } = position.coords;\r\n              setIsLoading(false);\r\n              onDone({\r\n                toolCallId,\r\n                result: {\r\n                  latitude,\r\n                  longitude,\r\n                  accuracy: position.coords.accuracy,\r\n                  timestamp: position.timestamp\r\n                }\r\n              });\r\n            },\r\n            (error) => {\r\n              setIsLoading(false);\r\n              let errorMessage = \"위치 정보를 가져올 수 없습니다.\";\r\n\r\n              switch (error.code) {\r\n                case error.PERMISSION_DENIED:\r\n                  errorMessage = \"위치 접근 권한이 거부되었습니다. 브라우저 설정에서 위치 권한을 허용해주세요.\";\r\n                  break;\r\n                case error.POSITION_UNAVAILABLE:\r\n                  errorMessage = \"위치 정보를 사용할 수 없습니다.\";\r\n                  break;\r\n                case error.TIMEOUT:\r\n                  errorMessage = \"위치 정보 요청 시간이 초과되었습니다.\";\r\n                  break;\r\n              }\r\n\r\n              setError(errorMessage);\r\n            },\r\n            {\r\n              enableHighAccuracy: true,\r\n              timeout: 10000,\r\n              maximumAge: 300000 // 5분\r\n            }\r\n          );\r\n        };\r\n\r\n        return (\r\n          <>\r\n            <p className=\"text-sm whitespace-pre-wrap break-all mb-2\">\r\n              {message || \"현재 위치 정보를 가져오시겠습니까?\"}\r\n            </p>\r\n            {error && (\r\n              <div className=\"mb-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700\">\r\n                {error}\r\n              </div>\r\n            )}\r\n            <div className=\"flex gap-2\">\r\n              <Button\r\n                onClick={handleGetLocation}\r\n                disabled={isLoading}\r\n                className=\"flex items-center gap-2\"\r\n              >\r\n                {isLoading ? (\r\n                  <>\r\n                    <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\" />\r\n                    위치 확인 중...\r\n                  </>\r\n                ) : (\r\n                  \"현재 위치 확인\"\r\n                )}\r\n              </Button>\r\n              {error && (\r\n                <Button\r\n                  variant=\"outline\"\r\n                  onClick={() => {\r\n                    setError(null);\r\n                    handleGetLocation();\r\n                  }}\r\n                >\r\n                  다시 시도\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </>\r\n        );\r\n      }\r\n      default:\r\n        return <pre className=\"text-sm bg-accent/20 p-2 rounded-md max-h-40 overflow-auto\">{JSON.stringify(invocation.args ?? {}, null, 2)}</pre>;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card className=\"w-full shadow-sm border-0\">\r\n      <CardHeader className=\"flex px-0 py-2 flex-row items-center gap-3\">\r\n        <Avatar className=\"h-6 w-6\">\r\n          <AvatarFallback>🤖</AvatarFallback>\r\n        </Avatar>\r\n        <span className=\"text-sm font-medium\">{friendlyName} 제안을 준비했어요!</span>\r\n      </CardHeader>\r\n      <CardContent className=\"p-2\">\r\n        {renderInteractive()}\r\n      </CardContent>\r\n      {/* Footer reserved for future */}\r\n      <CardFooter className=\"p-0\"/>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAgBA,kDAAkD;AAClD,MAAM,qBAA6C;IACjD,cAAc;IACd,cAAc;IACd,qBAAqB;IACrB,aAAa;AACf;AAEA,MAAM,WAAW,CAAC;IAChB,OAAQ;QACN,KAAK;YACH,qBAAO,sSAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;0BAAoC;;;;;;QAChF,KAAK;YACH,qBAAO,sSAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;0BAA8B;;;;;;QAC1E,KAAK;YACH,qBAAO,sSAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;0BAAkC;;;;;;QAC9E;YACE,OAAO;IACX;AACF;AAEO,SAAS,eAAe,EAAE,UAAU,EAAE,MAAM,EAAS;IAC1D,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG;IACxC,MAAM,eAAe,kBAAkB,CAAC,SAAS,IAAI;IAErD,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBAAgB;oBACnB,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAI,WAAW,IAAI,IAAI,CAAC;oBAElD,wBAAwB;oBACxB,MAAM,cAAc,CAAC;wBACnB,IAAI,eAAe,GAAG,OAAO;wBAC7B,IAAI,eAAe,GAAG,OAAO;wBAC7B,IAAI,eAAe,GAAG,OAAO;wBAC7B,OAAO;oBACT;oBAEA,MAAM,WAAW,YAAY,SAAS,UAAU;oBAEhD,qBACE;;0CACE,sSAAC;gCAAE,WAAU;0CAA8C;;;;;;0CAC3D,sSAAC;gCAAI,WAAW,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC;0CACrC,SAAS,IAAI,CAAC,KAAa;oCAC1B,kCAAkC;oCAClC,MAAM,cAAc,IAAI,QAAQ,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;oCAE5D,qBACE,sSAAC,8HAAA,CAAA,SAAM;wCAEL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,OAAO;gDAAC;gDAAY,QAAQ;4CAAG,IAAI,YAAY;wCACjD;kDAEC;uCAPI,GAAG,WAAW,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,SAAS,CAAC,GAAG,KAAK;;;;;gCAU3D;;;;;;;;gBAIR;YACA,KAAK;gBAAgB;oBACnB,MAAM,EAAE,OAAO,EAAE,SAAS,UAAU,EAAE,GAAI,WAAW,IAAI,IAAI,CAAC;oBAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAU,cAAc;oBACzD,qBACE;;0CACE,sSAAC;gCAAE,WAAU;0CAA8C;;;;;;0CAC3D,sSAAC,6HAAA,CAAA,QAAK;gCAAC,OAAO;gCAAO,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;0CAC7D,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC,8HAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,OAAO;4CAAC;4CAAY,QAAQ;wCAAK;oCAAI,UAAU,MAAM,IAAI,OAAO;8CAAI;;;;;;;;;;;;;gBAInG;YACA,KAAK;gBAAuB;oBAC1B,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAI,WAAW,IAAI,IAAI,CAAC;oBAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;oBACvC,qBACE;;0CACE,sSAAC;gCAAE,WAAU;0CAA8C;;;;;;0CAC3D,sSAAC;gCAAM,WAAU;;kDACf,sSAAC,gIAAA,CAAA,WAAQ;wCAAC,SAAS;wCAAS,iBAAiB;;;;;;oCAAqB;kDAAC,sSAAC;kDAAM;;;;;;;;;;;;0CAE5E,sSAAC,8HAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,OAAO;wCAAC;wCAAY,QAAQ;oCAAO;gCAAI,UAAU,CAAC;0CAAS;;;;;;;;gBAGxF;YACA,KAAK;gBAAe;oBAClB,MAAM,EAAE,OAAO,EAAE,GAAI,WAAW,IAAI,IAAI,CAAC;oBACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;oBAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;oBAElD,MAAM,oBAAoB;wBACxB,aAAa;wBACb,SAAS;wBAET,IAAI,CAAC,UAAU,WAAW,EAAE;4BAC1B,SAAS;4BACT,aAAa;4BACb;wBACF;wBAEA,UAAU,WAAW,CAAC,kBAAkB,CACtC,CAAC;4BACC,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,SAAS,MAAM;4BAC/C,aAAa;4BACb,OAAO;gCACL;gCACA,QAAQ;oCACN;oCACA;oCACA,UAAU,SAAS,MAAM,CAAC,QAAQ;oCAClC,WAAW,SAAS,SAAS;gCAC/B;4BACF;wBACF,GACA,CAAC;4BACC,aAAa;4BACb,IAAI,eAAe;4BAEnB,OAAQ,MAAM,IAAI;gCAChB,KAAK,MAAM,iBAAiB;oCAC1B,eAAe;oCACf;gCACF,KAAK,MAAM,oBAAoB;oCAC7B,eAAe;oCACf;gCACF,KAAK,MAAM,OAAO;oCAChB,eAAe;oCACf;4BACJ;4BAEA,SAAS;wBACX,GACA;4BACE,oBAAoB;4BACpB,SAAS;4BACT,YAAY,OAAO,KAAK;wBAC1B;oBAEJ;oBAEA,qBACE;;0CACE,sSAAC;gCAAE,WAAU;0CACV,WAAW;;;;;;4BAEb,uBACC,sSAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,8HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,0BACC;;8DACE,sSAAC;oDAAI,WAAU;;;;;;gDAAiF;;2DAIlG;;;;;;oCAGH,uBACC,sSAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;4CACP,SAAS;4CACT;wCACF;kDACD;;;;;;;;;;;;;;gBAOX;YACA;gBACE,qBAAO,sSAAC;oBAAI,WAAU;8BAA8D,KAAK,SAAS,CAAC,WAAW,IAAI,IAAI,CAAC,GAAG,MAAM;;;;;;QACpI;IACF;IAEA,qBACE,sSAAC,4HAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,sSAAC,4HAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,sSAAC,8HAAA,CAAA,SAAM;wBAAC,WAAU;kCAChB,cAAA,sSAAC,8HAAA,CAAA,iBAAc;sCAAC;;;;;;;;;;;kCAElB,sSAAC;wBAAK,WAAU;;4BAAuB;4BAAa;;;;;;;;;;;;;0BAEtD,sSAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;0BACpB;;;;;;0BAGH,sSAAC,4HAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;;;;;;;AAG5B;KAzLgB", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/tool-call.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useId, useRef, useState } from \"react\";\r\nimport type { ToolInvocation } from \"ai\";\r\nimport {\r\n  Accordion,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n  AccordionContent,\r\n} from \"@/components/ui/accordion\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  Bot,\r\n  Check,\r\n  Loader2 as Loader,\r\n  Zap,\r\n  MapPin,\r\n  Search,\r\n  Layers3,\r\n  Compass,\r\n  CheckCircle,\r\n  AlertCircle,\r\n  HelpCircle,\r\n  Palette,\r\n} from \"lucide-react\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { SuggestionCard } from \"./suggestion-card\";\r\nimport { LayerStyleResult } from \"./layer-style-result\";\r\n\r\ninterface ToolCallProps {\r\n  invocation: ToolInvocation;\r\n  addToolResult: (params: { toolCallId: string; result: any }) => void;\r\n  className?: string;\r\n  initialOpen?: boolean;\r\n  open?: boolean;\r\n}\r\n\r\n/**\r\n * UI shown while a tool invocation is in progress (state=\"call\" or \"partial-call\").\r\n * Very similar to `ToolResult` but with a spinner and different default colours.\r\n */\r\nexport function ToolCall({\r\n  invocation,\r\n  addToolResult,\r\n  className,\r\n  initialOpen = true,\r\n  open,\r\n}: ToolCallProps) {\r\n  // 고유한 ID 생성하여 각 컴포넌트의 value가 중복되지 않도록 함\r\n  const uniqueId = useId();\r\n  const itemValue = `tool-call-${uniqueId}`;\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n\r\n  // 하이드레이션 오류 방지를 위해 초기 상태를 false로 설정하고, 마운트 후에 실제 값으로 업데이트\r\n  const [isMounted, setIsMounted] = React.useState(false);\r\n  const [isOpenInternal, setIsOpenInternal] = useState(false);\r\n\r\n  // 마운트 후에만 상태 업데이트\r\n  React.useEffect(() => {\r\n    setIsMounted(true);\r\n    setIsOpenInternal(initialOpen);\r\n  }, []);\r\n\r\n  // external open prop 반영\r\n  React.useEffect(() => {\r\n    if (open !== undefined) {\r\n      setIsOpenInternal(open);\r\n    }\r\n  }, [open]);\r\n\r\n  const accordionValue = isOpenInternal ? itemValue : \"\";\r\n\r\n  const getStateConfig = (\r\n    st: \"call\" | \"partial-call\" | \"result\"\r\n  ): { label: string; className: string; icon: React.ReactNode } => {\r\n    switch (st) {\r\n      case \"call\":\r\n        return {\r\n          label: \"처리 중\",\r\n          className: \"border-blue-500 bg-blue-50 text-blue-700\",\r\n          icon: <Loader className=\"h-3 w-3 animate-spin\" />,\r\n        };\r\n      case \"partial-call\":\r\n        return {\r\n          label: \"진행 중\",\r\n          className: \"border-purple-500 bg-purple-50 text-purple-700\",\r\n          icon: <Loader className=\"h-3 w-3 animate-spin\" />,\r\n        };\r\n      case \"result\":\r\n        return {\r\n          label: \"완료\",\r\n          className: \"border-green-500 bg-green-50 text-green-700\",\r\n          icon: <Check className=\"h-3 w-3\" />,\r\n        };\r\n      default:\r\n        return {\r\n          label: st,\r\n          className: \"border-gray-300 bg-gray-50 text-gray-700\",\r\n          icon: <AlertCircle className=\"h-3 w-3\" />,\r\n        };\r\n    }\r\n  };\r\n\r\n  const { toolName, state } = invocation;\r\n  const content = JSON.stringify(invocation.args ?? {}, null, 2);\r\n\r\n  const stateConfig = getStateConfig(state);\r\n\r\n  // Check if this is an HIL (Human-in-the-Loop) component\r\n  const isHILComponent = [\r\n    \"chooseOption\",\r\n    \"getUserInput\",\r\n    \"confirmWithCheckbox\",\r\n    \"getLocation\"\r\n  ].includes(toolName);\r\n\r\n  // Map internal tool names to user-friendly labels and icons\r\n  const TOOL_CONFIG: Record<\r\n    string,\r\n    { name: string; icon: React.ReactNode; description: string }\r\n  > = {\r\n    createLayerFilter: {\r\n      name: \"레이어 필터 생성\",\r\n      icon: <Layers3 className=\"h-4 w-4\" />,\r\n      description: \"지정한 조건으로 레이어에 필터를 적용합니다.\",\r\n    },\r\n    updateLayerStyle: {\r\n      name: \"레이어 스타일 변경\",\r\n      icon: <Palette className=\"h-4 w-4\" />,\r\n      description: \"레이어의 색상, 투명도, 크기 등을 변경합니다.\",\r\n    },\r\n    generateCategoricalStyle: {\r\n      name: \"유형별 스타일 생성\",\r\n      icon: <Palette className=\"h-4 w-4\" />,\r\n      description: \"속성 값에 따라 다른 색상으로 레이어를 표시합니다.\",\r\n    },\r\n    getLayer: {\r\n      name: \"레이어 정보 조회\",\r\n      icon: <Layers3 className=\"h-4 w-4\" />,\r\n      description: \"선택한 레이어의 상세 정보를 가져옵니다.\",\r\n    },\r\n    getLayerAttributes: {\r\n      name: \"레이어 속성 조회\",\r\n      icon: <Search className=\"h-4 w-4\" />,\r\n      description: \"레이어의 속성 정보를 확인합니다.\",\r\n    },\r\n    getLayerList: {\r\n      name: \"레이어 목록 검색\",\r\n      icon: <Search className=\"h-4 w-4\" />,\r\n      description: \"입력한 키워드로 레이어 목록을 검색합니다.\",\r\n    },\r\n    performDensityAnalysis: {\r\n      name: \"밀도 분석 수행\",\r\n      icon: <Zap className=\"h-4 w-4\" />,\r\n      description: \"선택한 레이어에 대한 밀도 분석을 실행합니다.\",\r\n    },\r\n    searchAddress: {\r\n      name: \"주소 검색\",\r\n      icon: <Search className=\"h-4 w-4\" />,\r\n      description: \"입력한 주소 또는 장소를 검색합니다.\",\r\n    },\r\n    searchDirections: {\r\n      name: \"경로 탐색\",\r\n      icon: <Compass className=\"h-4 w-4\" />,\r\n      description: \"출발지와 도착지 간의 경로를 탐색합니다.\",\r\n    },\r\n    changeBasemap: {\r\n      name: \"배경지도 변경\",\r\n      icon: <MapPin className=\"h-4 w-4\" />,\r\n      description: \"선택한 배경지도로 변경합니다.\",\r\n    },\r\n    createVectorStyle: {\r\n      name: \"벡터 스타일 생성\",\r\n      icon: <Zap className=\"h-4 w-4\" />,\r\n      description: \"레이어에 적용할 새로운 벡터 스타일을 생성합니다.\",\r\n    },\r\n    getLocation: {\r\n      name: \"현재 위치 확인\",\r\n      icon: <MapPin className=\"h-4 w-4\" />,\r\n      description: \"현재 사용자의 위치 정보를 가져옵니다.\",\r\n    },\r\n    highlightGeometry: {\r\n      name: \"도형 강조\",\r\n      icon: <Zap className=\"h-4 w-4\" />,\r\n      description: \"지도 위의 특정 도형을 강조하여 표시합니다.\",\r\n    },\r\n    setCenter: {\r\n      name: \"지도 중심 이동\",\r\n      icon: <MapPin className=\"h-4 w-4\" />,\r\n      description: \"지정한 위치로 지도 중심을 이동합니다.\",\r\n    },\r\n    chooseOption: {\r\n      name: \"옵션 선택\",\r\n      icon: <Layers3 className=\"h-4 w-4\" />,\r\n      description: \"제시된 옵션 중 하나를 선택해 주세요.\",\r\n    },\r\n    getUserInput: {\r\n      name: \"정보 입력 요청\",\r\n      icon: <HelpCircle className=\"h-4 w-4\" />,\r\n      description: \"작업을 계속하려면 추가 정보를 입력해 주세요.\",\r\n    },\r\n    confirmWithCheckbox: {\r\n      name: \"확인 요청\",\r\n      icon: <CheckCircle className=\"h-4 w-4\" />,\r\n      description: \"내용을 확인하고 동의해 주세요.\",\r\n    },\r\n  };\r\n\r\n  // Default configuration for unknown tools\r\n  const defaultConfig = {\r\n    name: toolName,\r\n    icon: <Zap className=\"h-4 w-4\" />,\r\n    description: \"작업을 처리하고 있습니다...\",\r\n  };\r\n\r\n  // Get tool configuration\r\n  const toolConfig = TOOL_CONFIG[toolName] || defaultConfig;\r\n  const { name: friendlyName, icon: toolIcon, description } = toolConfig;\r\n\r\n  // For HIL components, render a simplified UI\r\n  if (isHILComponent) {\r\n    return (\r\n        <SuggestionCard invocation={invocation} onDone={addToolResult} />\r\n    );\r\n  }\r\n\r\n  // For non-HIL components, show the detailed view\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 10 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      // transition={{ duration: 0.2 }}\r\n      className={cn(\"rounded-xl border bg-card shadow-sm\", className)}\r\n    >\r\n      <Accordion\r\n        type=\"single\"\r\n        collapsible\r\n        value={accordionValue}\r\n        onValueChange={(value) => setIsOpenInternal(value === itemValue)}\r\n        className=\"w-full\"\r\n      >\r\n        <AccordionItem value={itemValue} className=\"border-0\">\r\n          <div className=\"flex items-start gap-3 p-4\">\r\n            <div className=\"mt-0.5 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-blue-50\">\r\n              <Bot className=\"h-4 w-4 text-blue-600\" />\r\n            </div>\r\n            <div className=\"flex-1 min-w-0\">\r\n              <div className=\"flex items-center justify-between gap-2\">\r\n                <AccordionTrigger className=\"group flex items-center gap-2 p-0 text-sm hover:no-underline [&[data-state=open]>svg]:rotate-180\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <div\r\n                      className={cn(\r\n                        \"flex h-5 w-5 items-center justify-center rounded-full border-2\",\r\n                        stateConfig.className\r\n                      )}\r\n                    >\r\n                      {stateConfig.icon}\r\n                    </div>\r\n                    <span className=\"font-medium text-foreground text-left\">\r\n                      {friendlyName}\r\n                    </span>\r\n                  </div>\r\n                </AccordionTrigger>\r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <Badge\r\n                        variant=\"outline\"\r\n                        className={cn(\r\n                          \"flex items-center gap-1 text-xs font-normal h-6 px-2 py-0.5\",\r\n                          stateConfig.className\r\n                        )}\r\n                      >\r\n                        {stateConfig.icon}\r\n                        {stateConfig.label}\r\n                      </Badge>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent side=\"top\">\r\n                      <p className=\"text-xs\">{description}</p>\r\n                    </TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              </div>\r\n\r\n              <AccordionContent className=\"mt-3 pl-0 pb-0\">\r\n                <motion.div\r\n                  initial={{ opacity: 0, height: 0 }}\r\n                  animate={{ opacity: 1, height: \"auto\" }}\r\n                  className=\"overflow-hidden\"\r\n                >\r\n                  <div\r\n                    ref={contentRef}\r\n                    className=\"mt-2 rounded-lg bg-muted/30 p-3 text-sm\"\r\n                  >\r\n                    <div className=\"flex items-start gap-3\">\r\n                      <div className=\"mt-0.5 flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 text-blue-600\">\r\n                        {toolIcon}\r\n                      </div>\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <p className=\"font-medium text-foreground\">\r\n                          {friendlyName}\r\n                        </p>\r\n                        <p className=\"text-muted-foreground text-sm mt-0.5\">\r\n                          {description}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"mt-3 rounded-md border bg-background p-3\">\r\n                      <pre className=\"whitespace-pre-wrap break-all text-xs\">\r\n                        {content}\r\n                      </pre>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              </AccordionContent>\r\n            </div>\r\n          </div>\r\n        </AccordionItem>\r\n      </Accordion>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAMA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAMA;;;AAjCA;;;;;;;;;AAgDO,SAAS,SAAS,EACvB,UAAU,EACV,aAAa,EACb,SAAS,EACT,cAAc,IAAI,EAClB,IAAI,EACU;;IACd,wCAAwC;IACxC,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,QAAK,AAAD;IACrB,MAAM,YAAY,CAAC,UAAU,EAAE,UAAU;IACzC,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,0DAA0D;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,sQAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,kBAAkB;IAClB,sQAAA,CAAA,UAAK,CAAC,SAAS;8BAAC;YACd,aAAa;YACb,kBAAkB;QACpB;6BAAG,EAAE;IAEL,wBAAwB;IACxB,sQAAA,CAAA,UAAK,CAAC,SAAS;8BAAC;YACd,IAAI,SAAS,WAAW;gBACtB,kBAAkB;YACpB;QACF;6BAAG;QAAC;KAAK;IAET,MAAM,iBAAiB,iBAAiB,YAAY;IAEpD,MAAM,iBAAiB,CACrB;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,WAAW;oBACX,oBAAM,sSAAC,wSAAA,CAAA,UAAM;wBAAC,WAAU;;;;;;gBAC1B;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,WAAW;oBACX,oBAAM,sSAAC,wSAAA,CAAA,UAAM;wBAAC,WAAU;;;;;;gBAC1B;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,WAAW;oBACX,oBAAM,sSAAC,2RAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;gBACzB;YACF;gBACE,OAAO;oBACL,OAAO;oBACP,WAAW;oBACX,oBAAM,sSAAC,2SAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;gBAC/B;QACJ;IACF;IAEA,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAC5B,MAAM,UAAU,KAAK,SAAS,CAAC,WAAW,IAAI,IAAI,CAAC,GAAG,MAAM;IAE5D,MAAM,cAAc,eAAe;IAEnC,wDAAwD;IACxD,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;KACD,CAAC,QAAQ,CAAC;IAEX,4DAA4D;IAC5D,MAAM,cAGF;QACF,mBAAmB;YACjB,MAAM;YACN,oBAAM,sSAAC,mSAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,aAAa;QACf;QACA,kBAAkB;YAChB,MAAM;YACN,oBAAM,sSAAC,+RAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,aAAa;QACf;QACA,0BAA0B;YACxB,MAAM;YACN,oBAAM,sSAAC,+RAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,aAAa;QACf;QACA,UAAU;YACR,MAAM;YACN,oBAAM,sSAAC,mSAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,aAAa;QACf;QACA,oBAAoB;YAClB,MAAM;YACN,oBAAM,sSAAC,6RAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,aAAa;QACf;QACA,cAAc;YACZ,MAAM;YACN,oBAAM,sSAAC,6RAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,aAAa;QACf;QACA,wBAAwB;YACtB,MAAM;YACN,oBAAM,sSAAC,uRAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,aAAa;QACf;QACA,eAAe;YACb,MAAM;YACN,oBAAM,sSAAC,6RAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,aAAa;QACf;QACA,kBAAkB;YAChB,MAAM;YACN,oBAAM,sSAAC,+RAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,aAAa;QACf;QACA,eAAe;YACb,MAAM;YACN,oBAAM,sSAAC,iSAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,aAAa;QACf;QACA,mBAAmB;YACjB,MAAM;YACN,oBAAM,sSAAC,uRAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,aAAa;QACf;QACA,aAAa;YACX,MAAM;YACN,oBAAM,sSAAC,iSAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,aAAa;QACf;QACA,mBAAmB;YACjB,MAAM;YACN,oBAAM,sSAAC,uRAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,aAAa;QACf;QACA,WAAW;YACT,MAAM;YACN,oBAAM,sSAAC,iSAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,aAAa;QACf;QACA,cAAc;YACZ,MAAM;YACN,oBAAM,sSAAC,mSAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,aAAa;QACf;QACA,cAAc;YACZ,MAAM;YACN,oBAAM,sSAAC,ySAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,aAAa;QACf;QACA,qBAAqB;YACnB,MAAM;YACN,oBAAM,sSAAC,kTAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,aAAa;QACf;IACF;IAEA,0CAA0C;IAC1C,MAAM,gBAAgB;QACpB,MAAM;QACN,oBAAM,sSAAC,uRAAA,CAAA,MAAG;YAAC,WAAU;;;;;;QACrB,aAAa;IACf;IAEA,yBAAyB;IACzB,MAAM,aAAa,WAAW,CAAC,SAAS,IAAI;IAC5C,MAAM,EAAE,MAAM,YAAY,EAAE,MAAM,QAAQ,EAAE,WAAW,EAAE,GAAG;IAE5D,6CAA6C;IAC7C,IAAI,gBAAgB;QAClB,qBACI,sSAAC,6IAAA,CAAA,iBAAc;YAAC,YAAY;YAAY,QAAQ;;;;;;IAEtD;IAEA,iDAAiD;IACjD,qBACE,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,iCAAiC;QACjC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;kBAErD,cAAA,sSAAC,iIAAA,CAAA,YAAS;YACR,MAAK;YACL,WAAW;YACX,OAAO;YACP,eAAe,CAAC,QAAU,kBAAkB,UAAU;YACtD,WAAU;sBAEV,cAAA,sSAAC,iIAAA,CAAA,gBAAa;gBAAC,OAAO;gBAAW,WAAU;0BACzC,cAAA,sSAAC;oBAAI,WAAU;;sCACb,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,uRAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;sCAEjB,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,iIAAA,CAAA,mBAAgB;4CAAC,WAAU;sDAC1B,cAAA,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA,YAAY,SAAS;kEAGtB,YAAY,IAAI;;;;;;kEAEnB,sSAAC;wDAAK,WAAU;kEACb;;;;;;;;;;;;;;;;;sDAIP,sSAAC,+HAAA,CAAA,kBAAe;sDACd,cAAA,sSAAC,+HAAA,CAAA,UAAO;;kEACN,sSAAC,+HAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,sSAAC,6HAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+DACA,YAAY,SAAS;;gEAGtB,YAAY,IAAI;gEAChB,YAAY,KAAK;;;;;;;;;;;;kEAGtB,sSAAC,+HAAA,CAAA,iBAAc;wDAAC,MAAK;kEACnB,cAAA,sSAAC;4DAAE,WAAU;sEAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMhC,sSAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CAC1B,cAAA,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,QAAQ;wCAAE;wCACjC,SAAS;4CAAE,SAAS;4CAAG,QAAQ;wCAAO;wCACtC,WAAU;kDAEV,cAAA,sSAAC;4CACC,KAAK;4CACL,WAAU;;8DAEV,sSAAC;oDAAI,WAAU;;sEACb,sSAAC;4DAAI,WAAU;sEACZ;;;;;;sEAEH,sSAAC;4DAAI,WAAU;;8EACb,sSAAC;oEAAE,WAAU;8EACV;;;;;;8EAEH,sSAAC;oEAAE,WAAU;8EACV;;;;;;;;;;;;;;;;;;8DAKP,sSAAC;oDAAI,WAAU;8DACb,cAAA,sSAAC;wDAAI,WAAU;kEACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzB;GAzRgB;;QAQG,sQAAA,CAAA,QAAK;;;KARR", "debugId": null}}, {"offset": {"line": 1606, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/search-address-result.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useCallback } from \"react\";\r\nimport {\r\n  MapPin, Check, Navigation, Building2, Copy, ChevronDown, ChevronUp,\r\n  Route, MoreHorizontal, Star\r\n} from \"lucide-react\";\r\nimport { AddressResponse } from \"@/types/tools\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuSeparator\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { toast } from \"sonner\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { highlightPointOnMap, type UseMapReturn } from \"@/lib/map-utils\";\r\nimport { componentStyles } from \"@/lib/design-tokens\";\r\nimport { CompactResultTrigger } from \"./common/compact-result-trigger\";\r\nimport { getToolDisplayInfo } from \"../annotations\";\r\nimport { Badge } from \"../ui/badge\";\r\n\r\ninterface SearchAddressResultProps {\r\n  content: AddressResponse;\r\n  className?: string;\r\n  mapState?: UseMapReturn;\r\n  onDirectionsRequest?: (origin: string, destination: string) => void;\r\n}\r\n\r\n// 주소 아이템 타입 정의\r\ninterface AddressItem {\r\n  roadAddr: string;\r\n  jibunAddr: string;\r\n  buildName: string;\r\n  buildLo: string;\r\n  buildLa: string;\r\n  parcelLo: string;\r\n  parcelLa: string;\r\n  poiName: string;\r\n  buildGeom?: string;\r\n  geom?: string;\r\n}\r\n\r\n// 애니메이션 제거 - 정적 렌더링만 사용\r\n\r\nexport function SearchAddressResult({\r\n  content,\r\n  className,\r\n  mapState,\r\n  onDirectionsRequest\r\n}: SearchAddressResultProps) {\r\n  const [showAll, setShowAll] = useState(false);\r\n  const [selectedOrigin, setSelectedOrigin] = useState<AddressItem | null>(null);\r\n  const [selectedDestination, setSelectedDestination] = useState<AddressItem | null>(null);\r\n\r\n  let result: AddressResponse;\r\n\r\n  try {\r\n    result = content as AddressResponse;\r\n  } catch (e) {\r\n    return null; // 파싱 실패시 기본 UI로 fallback\r\n  }\r\n\r\n  // 자동 이동 로직 제거 - 이제 ToolInvocationProvider에서 처리\r\n\r\n  // 좌표를 searchDirections 형식으로 변환하는 함수\r\n  const formatCoordinateForDirections = useCallback((address: AddressItem): string => {\r\n    const longitude = parseFloat(address.buildLo);\r\n    const latitude = parseFloat(address.buildLa);\r\n    const name = address.buildName || address.poiName || address.roadAddr.split(' ').slice(0, 2).join(' ');\r\n    return `${longitude},${latitude},name=${name}`;\r\n  }, []);\r\n\r\n  // 길찾기 요청 함수\r\n  const handleDirectionsRequest = useCallback((origin: AddressItem, destination: AddressItem) => {\r\n    const originStr = formatCoordinateForDirections(origin);\r\n    const destinationStr = formatCoordinateForDirections(destination);\r\n\r\n    if (onDirectionsRequest) {\r\n      onDirectionsRequest(originStr, destinationStr);\r\n    }\r\n\r\n    toast.success(`${origin.buildName || '출발지'}에서 ${destination.buildName || '목적지'}까지 경로를 검색합니다`);\r\n  }, [formatCoordinateForDirections, onDirectionsRequest]);\r\n\r\n  const handleCopyAddress = useCallback((address: string) => {\r\n    navigator.clipboard.writeText(address);\r\n    toast.success(\"주소가 복사되었습니다\");\r\n  }, []);\r\n\r\n  const handleNavigate = useCallback((address: AddressItem) => {\r\n    if (!mapState) {\r\n      toast.error(\"지도가 로드되지 않았습니다\");\r\n      return;\r\n    }\r\n\r\n    const success = highlightPointOnMap(\r\n      mapState,\r\n      parseFloat(address.buildLo),\r\n      parseFloat(address.buildLa),\r\n      address.buildGeom || address.geom,\r\n      16\r\n    );\r\n\r\n    if (success) {\r\n      toast.success(`${address.roadAddr}로 이동했습니다`);\r\n    } else {\r\n      toast.error(\"지도 이동 중 오류가 발생했습니다\");\r\n    }\r\n  }, [mapState]);\r\n\r\n  // 출발지 설정\r\n  const handleSetAsOrigin = useCallback((address: AddressItem) => {\r\n    setSelectedOrigin(address);\r\n    toast.success(`출발지로 설정: ${address.buildName || address.roadAddr}`);\r\n  }, []);\r\n\r\n  // 목적지 설정\r\n  const handleSetAsDestination = useCallback((address: AddressItem) => {\r\n    setSelectedDestination(address);\r\n    toast.success(`목적지로 설정: ${address.buildName || address.roadAddr}`);\r\n  }, []);\r\n\r\n  // 선택된 출발지와 목적지로 길찾기\r\n  const handleStartDirections = useCallback(() => {\r\n    if (selectedOrigin && selectedDestination) {\r\n      handleDirectionsRequest(selectedOrigin, selectedDestination);\r\n      // 선택 초기화\r\n      setSelectedOrigin(null);\r\n      setSelectedDestination(null);\r\n    }\r\n  }, [selectedOrigin, selectedDestination, handleDirectionsRequest]);\r\n\r\n  if (!result.result?.jusoList?.length) {\r\n    return (\r\n      <div className={cn(\"rounded-xl border border-amber-200/60 bg-gradient-to-r from-amber-50/80 to-orange-50/80 backdrop-blur-sm p-3\", className)}>\r\n        <div className=\"flex items-center gap-3\">\r\n          <div className=\"flex h-7 w-7 items-center justify-center rounded-full bg-amber-100/80\">\r\n            <MapPin className=\"h-3.5 w-3.5 text-amber-600\" />\r\n          </div>\r\n          <div>\r\n            <p className=\"font-medium text-amber-900 text-sm\">검색 결과가 없습니다</p>\r\n            <p className=\"text-xs text-amber-700\">다른 키워드로 다시 검색해보세요</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const addresses = result.result.jusoList;\r\n  const displayAddresses = showAll ? addresses : addresses.slice(0, 3);\r\n\r\n  const toolInfo = getToolDisplayInfo(\"searchAddress\");\r\n\r\n  return (\r\n    <CompactResultTrigger\r\n      icon={toolInfo.icon}\r\n      title={toolInfo.label}\r\n      state=\"result\"\r\n      className={className}\r\n      titleExtra={\r\n        <Badge variant=\"outline\" className=\"text-xs py-0 border bg-emerald-100/80 text-emerald-700 border-emerald-200/60\">\r\n          {addresses.length}개 발견\r\n        </Badge>\r\n      }\r\n    >\r\n      <div className=\"space-y-2\">\r\n\r\n      {/* 길찾기 상태 표시 */}\r\n      {(selectedOrigin || selectedDestination) && (\r\n        <div className={cn(\r\n          componentStyles.card.base,\r\n          \"p-3 bg-gradient-to-r from-blue-50/95 to-indigo-50/90 border-blue-200/60\"\r\n        )}>\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <Route className=\"h-4 w-4 text-blue-600\" />\r\n              <div className=\"text-sm\">\r\n                <span className=\"font-medium text-blue-900\">길찾기 설정 중</span>\r\n                <div className=\"flex items-center gap-2 mt-1 text-xs text-blue-700\">\r\n                  {selectedOrigin && (\r\n                    <span className=\"bg-blue-100 px-2 py-0.5 rounded\">\r\n                      출발: {selectedOrigin.buildName || selectedOrigin.roadAddr.split(' ').slice(0, 2).join(' ')}\r\n                    </span>\r\n                  )}\r\n                  {selectedDestination && (\r\n                    <span className=\"bg-blue-100 px-2 py-0.5 rounded\">\r\n                      도착: {selectedDestination.buildName || selectedDestination.roadAddr.split(' ').slice(0, 2).join(' ')}\r\n                    </span>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            {selectedOrigin && selectedDestination && (\r\n              <Button\r\n                size=\"sm\"\r\n                onClick={handleStartDirections}\r\n                className={cn(componentStyles.button.primary, \"h-8 px-3 text-xs\")}\r\n              >\r\n                <Route className=\"h-3 w-3 mr-1\" />\r\n                길찾기 시작\r\n              </Button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* 주소 목록 */}\r\n      <div className=\"space-y-2\">\r\n        {displayAddresses.map((address, index) => (\r\n          <div key={index} className=\"group\">\r\n            <Card className={cn(\r\n              componentStyles.card.base,\r\n              componentStyles.card.interactive,\r\n              \"cursor-pointer bg-white/90 border-neutral-200/60 hover:border-blue-300/70\",\r\n              \"hover:bg-gradient-to-r hover:from-blue-50/60 hover:to-indigo-50/40\",\r\n              \"shadow-sm hover:shadow-md transition-all duration-200\"\r\n            )}>\r\n                <CardContent className=\"p-4\">\r\n                  <div className=\"flex items-center justify-between gap-4\">\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      {/* 메인 주소 */}\r\n                      <div className=\"flex items-start gap-3 mb-2\">\r\n                        <div className={cn(\r\n                          componentStyles.iconContainer.sm,\r\n                          \"bg-blue-500/10 border border-blue-200/50 mt-0.5 flex-shrink-0\"\r\n                        )}>\r\n                          <MapPin className=\"h-3 w-3 text-blue-600\" />\r\n                        </div>\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <p className=\"font-semibold text-neutral-900 text-sm leading-tight\">\r\n                            {address.roadAddr}\r\n                          </p>\r\n                          {address.buildName && (\r\n                            <div className=\"flex items-center gap-1.5 mt-1\">\r\n                              <Building2 className=\"h-3 w-3 text-blue-600 flex-shrink-0\" />\r\n                              <p className=\"text-xs text-blue-700 font-medium truncate\">\r\n                                {address.buildName}\r\n                              </p>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* 지번 주소 */}\r\n                      <p className=\"text-xs text-neutral-600 ml-9 truncate bg-neutral-50/80 px-2 py-1 rounded-md\">\r\n                        지번: {address.jibunAddr}\r\n                      </p>\r\n                    </div>\r\n\r\n                    {/* 개선된 액션 버튼들 */}\r\n                    <div className=\"flex items-center gap-2\">\r\n                      {/* 항상 보이는 주요 액션 */}\r\n                      <div className=\"flex gap-1\">\r\n                        <Button\r\n                          size=\"sm\"\r\n                          variant=\"ghost\"\r\n                          className={cn(\r\n                            componentStyles.button.ghost,\r\n                            \"h-8 px-2 text-xs hover:bg-blue-100/80 hover:text-blue-700 rounded-lg\"\r\n                          )}\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            handleNavigate(address);\r\n                          }}\r\n                        >\r\n                          <Navigation className=\"h-3 w-3 mr-1\" />\r\n                          보기\r\n                        </Button>\r\n                      </div>\r\n\r\n                      {/* 드롭다운 메뉴 */}\r\n                      <DropdownMenu>\r\n                        <DropdownMenuTrigger asChild>\r\n                          <Button\r\n                            size=\"sm\"\r\n                            variant=\"ghost\"\r\n                            className={cn(\r\n                              componentStyles.button.ghost,\r\n                              \"h-8 w-8 p-0 hover:bg-neutral-100/80 rounded-lg\"\r\n                            )}\r\n                          >\r\n                            <MoreHorizontal className=\"h-3.5 w-3.5\" />\r\n                          </Button>\r\n                        </DropdownMenuTrigger>\r\n                        <DropdownMenuContent align=\"end\" className=\"w-48\">\r\n                          <DropdownMenuItem\r\n                            onClick={() => handleSetAsOrigin(address)}\r\n                            className=\"flex items-center gap-2\"\r\n                          >\r\n                            <Star className=\"h-3.5 w-3.5 text-green-600\" />\r\n                            출발지로 설정\r\n                          </DropdownMenuItem>\r\n                          <DropdownMenuItem\r\n                            onClick={() => handleSetAsDestination(address)}\r\n                            className=\"flex items-center gap-2\"\r\n                          >\r\n                            <MapPin className=\"h-3.5 w-3.5 text-red-600\" />\r\n                            목적지로 설정\r\n                          </DropdownMenuItem>\r\n                          <DropdownMenuSeparator />\r\n                          <DropdownMenuItem\r\n                            onClick={() => handleCopyAddress(address.roadAddr)}\r\n                            className=\"flex items-center gap-2\"\r\n                          >\r\n                            <Copy className=\"h-3.5 w-3.5\" />\r\n                            주소 복사\r\n                          </DropdownMenuItem>\r\n                        </DropdownMenuContent>\r\n                      </DropdownMenu>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          ))}\r\n      </div>\r\n\r\n      {/* 더보기/접기 버튼 */}\r\n      {addresses.length > 3 && (\r\n        <div>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => setShowAll(!showAll)}\r\n            className={cn(\r\n              componentStyles.button.secondary,\r\n              \"w-full h-9 text-xs font-medium border-dashed border-neutral-300\",\r\n              \"hover:border-solid hover:border-blue-300 hover:bg-blue-50/60 hover:text-blue-700\",\r\n              \"transition-all duration-200\"\r\n            )}\r\n          >\r\n            {showAll ? (\r\n              <>\r\n                <ChevronUp className=\"h-3.5 w-3.5 mr-2\" />\r\n                접기\r\n              </>\r\n            ) : (\r\n              <>\r\n                <ChevronDown className=\"h-3.5 w-3.5 mr-2\" />\r\n                {addresses.length - 3}개 더보기\r\n              </>\r\n            )}\r\n          </Button>\r\n        </div>\r\n      )}\r\n      </div>\r\n    </CompactResultTrigger>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAvBA;;;;;;;;;;;;;AAgDO,SAAS,oBAAoB,EAClC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,mBAAmB,EACM;;IACzB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAsB;IACzE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAsB;IAEnF,IAAI;IAEJ,IAAI;QACF,SAAS;IACX,EAAE,OAAO,GAAG;QACV,OAAO,MAAM,yBAAyB;IACxC;IAEA,+CAA+C;IAE/C,oCAAoC;IACpC,MAAM,gCAAgC,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;0EAAE,CAAC;YACjD,MAAM,YAAY,WAAW,QAAQ,OAAO;YAC5C,MAAM,WAAW,WAAW,QAAQ,OAAO;YAC3C,MAAM,OAAO,QAAQ,SAAS,IAAI,QAAQ,OAAO,IAAI,QAAQ,QAAQ,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;YAClG,OAAO,GAAG,UAAU,CAAC,EAAE,SAAS,MAAM,EAAE,MAAM;QAChD;yEAAG,EAAE;IAEL,YAAY;IACZ,MAAM,0BAA0B,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;oEAAE,CAAC,QAAqB;YAChE,MAAM,YAAY,8BAA8B;YAChD,MAAM,iBAAiB,8BAA8B;YAErD,IAAI,qBAAqB;gBACvB,oBAAoB,WAAW;YACjC;YAEA,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,OAAO,SAAS,IAAI,MAAM,GAAG,EAAE,YAAY,SAAS,IAAI,MAAM,YAAY,CAAC;QAC9F;mEAAG;QAAC;QAA+B;KAAoB;IAEvD,MAAM,oBAAoB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;8DAAE,CAAC;YACrC,UAAU,SAAS,CAAC,SAAS,CAAC;YAC9B,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;6DAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YAClC,IAAI,CAAC,UAAU;gBACb,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAChC,UACA,WAAW,QAAQ,OAAO,GAC1B,WAAW,QAAQ,OAAO,GAC1B,QAAQ,SAAS,IAAI,QAAQ,IAAI,EACjC;YAGF,IAAI,SAAS;gBACX,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,QAAQ,QAAQ,CAAC,QAAQ,CAAC;YAC7C,OAAO;gBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;0DAAG;QAAC;KAAS;IAEb,SAAS;IACT,MAAM,oBAAoB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;8DAAE,CAAC;YACrC,kBAAkB;YAClB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,QAAQ,SAAS,IAAI,QAAQ,QAAQ,EAAE;QACnE;6DAAG,EAAE;IAEL,SAAS;IACT,MAAM,yBAAyB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mEAAE,CAAC;YAC1C,uBAAuB;YACvB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,QAAQ,SAAS,IAAI,QAAQ,QAAQ,EAAE;QACnE;kEAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,wBAAwB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;kEAAE;YACxC,IAAI,kBAAkB,qBAAqB;gBACzC,wBAAwB,gBAAgB;gBACxC,SAAS;gBACT,kBAAkB;gBAClB,uBAAuB;YACzB;QACF;iEAAG;QAAC;QAAgB;QAAqB;KAAwB;IAEjE,IAAI,CAAC,OAAO,MAAM,EAAE,UAAU,QAAQ;QACpC,qBACE,sSAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gHAAgH;sBACjI,cAAA,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC,iSAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,sSAAC;;0CACC,sSAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAClD,sSAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;;;;;;;;;;;;IAKhD;IAEA,MAAM,YAAY,OAAO,MAAM,CAAC,QAAQ;IACxC,MAAM,mBAAmB,UAAU,YAAY,UAAU,KAAK,CAAC,GAAG;IAElE,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;IAEpC,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;QACnB,MAAM,SAAS,IAAI;QACnB,OAAO,SAAS,KAAK;QACrB,OAAM;QACN,WAAW;QACX,0BACE,sSAAC,6HAAA,CAAA,QAAK;YAAC,SAAQ;YAAU,WAAU;;gBAChC,UAAU,MAAM;gBAAC;;;;;;;kBAItB,cAAA,sSAAC;YAAI,WAAU;;gBAGd,CAAC,kBAAkB,mBAAmB,mBACrC,sSAAC;oBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,IAAI,EACzB;8BAEA,cAAA,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,2RAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,sSAAC;gDAAI,WAAU;;oDACZ,gCACC,sSAAC;wDAAK,WAAU;;4DAAkC;4DAC3C,eAAe,SAAS,IAAI,eAAe,QAAQ,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;;oDAGxF,qCACC,sSAAC;wDAAK,WAAU;;4DAAkC;4DAC3C,oBAAoB,SAAS,IAAI,oBAAoB,QAAQ,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;4BAMxG,kBAAkB,qCACjB,sSAAC,8HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS;gCACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,OAAO,EAAE;;kDAE9C,sSAAC,2RAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAS5C,sSAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,sSAAC;4BAAgB,WAAU;sCACzB,cAAA,sSAAC,4HAAA,CAAA,OAAI;gCAAC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAChB,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,IAAI,EACzB,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,WAAW,EAChC,6EACA,sEACA;0CAEE,cAAA,sSAAC,4HAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEAEb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;gEAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,0HAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE,EAChC;0EAEA,cAAA,sSAAC,iSAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAEpB,sSAAC;gEAAI,WAAU;;kFACb,sSAAC;wEAAE,WAAU;kFACV,QAAQ,QAAQ;;;;;;oEAElB,QAAQ,SAAS,kBAChB,sSAAC;wEAAI,WAAU;;0FACb,sSAAC,uSAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;0FACrB,sSAAC;gFAAE,WAAU;0FACV,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;kEAQ5B,sSAAC;wDAAE,WAAU;;4DAA+E;4DACrF,QAAQ,SAAS;;;;;;;;;;;;;0DAK1B,sSAAC;gDAAI,WAAU;;kEAEb,sSAAC;wDAAI,WAAU;kEACb,cAAA,sSAAC,8HAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,KAAK,EAC5B;4DAEF,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,eAAe;4DACjB;;8EAEA,sSAAC,qSAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;kEAM3C,sSAAC,wIAAA,CAAA,eAAY;;0EACX,sSAAC,wIAAA,CAAA,sBAAmB;gEAAC,OAAO;0EAC1B,cAAA,sSAAC,8HAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,KAAK,EAC5B;8EAGF,cAAA,sSAAC,uSAAA,CAAA,iBAAc;wEAAC,WAAU;;;;;;;;;;;;;;;;0EAG9B,sSAAC,wIAAA,CAAA,sBAAmB;gEAAC,OAAM;gEAAM,WAAU;;kFACzC,sSAAC,wIAAA,CAAA,mBAAgB;wEACf,SAAS,IAAM,kBAAkB;wEACjC,WAAU;;0FAEV,sSAAC,yRAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EAA+B;;;;;;;kFAGjD,sSAAC,wIAAA,CAAA,mBAAgB;wEACf,SAAS,IAAM,uBAAuB;wEACtC,WAAU;;0FAEV,sSAAC,iSAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAA6B;;;;;;;kFAGjD,sSAAC,wIAAA,CAAA,wBAAqB;;;;;kFACtB,sSAAC,wIAAA,CAAA,mBAAgB;wEACf,SAAS,IAAM,kBAAkB,QAAQ,QAAQ;wEACjD,WAAU;;0FAEV,sSAAC,yRAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA/FxC;;;;;;;;;;gBA6Gb,UAAU,MAAM,GAAG,mBAClB,sSAAC;8BACC,cAAA,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,WAAW,CAAC;wBAC3B,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,SAAS,EAChC,mEACA,oFACA;kCAGD,wBACC;;8CACE,sSAAC,uSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAqB;;yDAI5C;;8CACE,sSAAC,2SAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCACtB,UAAU,MAAM,GAAG;gCAAE;;;;;;;;;;;;;;;;;;;;;;;;AAStC;GAhTgB;KAAA", "debugId": null}}, {"offset": {"line": 2234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/search-origin-result.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useCallback } from \"react\";\r\nimport {\r\n  MapPin, Navigation, Building2, Copy, ChevronDown, ChevronUp,\r\n  Route, MoreHorizontal, Star, ArrowRight\r\n} from \"lucide-react\";\r\nimport { AddressResponse } from \"@/types/tools\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuSeparator\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { toast } from \"sonner\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { highlightPointOnMap, type UseMapReturn } from \"@/lib/map-utils\";\r\nimport { componentStyles } from \"@/lib/design-tokens\";\r\nimport { CompactResultTrigger } from \"./common/compact-result-trigger\";\r\nimport { getToolDisplayInfo } from \"../annotations\";\r\n\r\ninterface SearchOriginResultProps {\r\n  content: AddressResponse;\r\n  className?: string;\r\n  mapState?: UseMapReturn;\r\n}\r\n\r\n// 주소 아이템 타입 정의\r\ninterface AddressItem {\r\n  roadAddr: string;\r\n  jibunAddr: string;\r\n  buildName: string;\r\n  buildLo: string;\r\n  buildLa: string;\r\n  parcelLo: string;\r\n  parcelLa: string;\r\n  poiName: string;\r\n  buildGeom?: string;\r\n  geom?: string;\r\n}\r\n\r\nexport function SearchOriginResult({\r\n  content,\r\n  className,\r\n  mapState\r\n}: SearchOriginResultProps) {\r\n  const [showAll, setShowAll] = useState(false);\r\n\r\n  let result: AddressResponse;\r\n\r\n  try {\r\n    result = content as AddressResponse;\r\n  } catch (e) {\r\n    return null;\r\n  }\r\n\r\n  if (!result.result?.jusoList?.length) {\r\n    return (\r\n      <div className={cn(componentStyles.card.error, \"p-3\", className)}>\r\n        <div className=\"flex items-center gap-3\">\r\n          <div className={cn(componentStyles.iconContainer.sm, \"bg-red-100/80 text-red-600 border border-red-200/60\")}>\r\n            <MapPin className=\"h-3.5 w-3.5\" />\r\n          </div>\r\n          <div>\r\n            <p className=\"font-medium text-red-900 text-sm\">출발지를 찾을 수 없습니다</p>\r\n            <p className=\"text-xs text-red-700\">다른 키워드로 다시 검색해보세요</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const addresses = result.result.jusoList;\r\n  const displayAddresses = showAll ? addresses : addresses.slice(0, 3);\r\n\r\n  const formatCoordinateForDirections = useCallback((address: AddressItem): string => {\r\n    return `${address.buildLo},${address.buildLa}${address.buildName ? `,name=${address.buildName}` : ''}`;\r\n  }, []);\r\n\r\n  const handleCopyAddress = useCallback((address: string) => {\r\n    navigator.clipboard.writeText(address);\r\n    toast.success(\"주소가 복사되었습니다\");\r\n  }, []);\r\n\r\n  const handleNavigate = useCallback((address: AddressItem) => {\r\n    if (!mapState) {\r\n      toast.error(\"지도가 로드되지 않았습니다\");\r\n      return;\r\n    }\r\n\r\n    const success = highlightPointOnMap(\r\n      mapState,\r\n      parseFloat(address.buildLo),\r\n      parseFloat(address.buildLa),\r\n      address.buildGeom || address.geom,\r\n      16\r\n    );\r\n\r\n    if (success) {\r\n      toast.success(`출발지 ${address.roadAddr}로 이동했습니다`);\r\n    } else {\r\n      toast.error(\"지도 이동 중 오류가 발생했습니다\");\r\n    }\r\n  }, [mapState]);\r\n\r\n  const toolInfo = getToolDisplayInfo(\"searchOrigin\");\r\n\r\n  return (\r\n    <CompactResultTrigger\r\n      icon={toolInfo.icon}\r\n      title={toolInfo.label}\r\n      state=\"result\"\r\n      className={className}\r\n      titleExtra={\r\n        <Badge variant=\"outline\" className=\"text-xs py-0 border bg-green-100/80 text-green-700 border-green-200/60\">\r\n          {addresses.length}개 발견\r\n        </Badge>\r\n      }\r\n    >\r\n      <div className=\"space-y-2\">\r\n        {displayAddresses.map((address, index) => (\r\n          <Card key={index} className={cn(componentStyles.card.interactive, \"group\")}>\r\n            <CardContent className=\"p-3\">\r\n              <div className=\"flex items-start justify-between gap-3\">\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <div className=\"flex items-center gap-2 mb-1\">\r\n                    <div className={cn(componentStyles.iconContainer.sm, \"bg-green-100/80 text-green-600 border border-green-200/60\")}>\r\n                      <ArrowRight className=\"h-3 w-3\" />\r\n                    </div>\r\n                    {address.buildName && (\r\n                      <span className=\"font-medium text-sm text-gray-900 truncate\">\r\n                        {address.buildName}\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                  <p className=\"text-xs text-gray-600 mb-1 leading-relaxed\">\r\n                    {address.roadAddr}\r\n                  </p>\r\n                  {address.jibunAddr && address.jibunAddr !== address.roadAddr && (\r\n                    <p className=\"text-xs text-gray-500\">\r\n                      지번: {address.jibunAddr}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"ghost\"\r\n                    onClick={() => handleNavigate(address)}\r\n                    className={cn(componentStyles.button.ghost, \"h-7 w-7 p-0\")}\r\n                  >\r\n                    <Navigation className=\"h-3 w-3\" />\r\n                  </Button>\r\n                  <DropdownMenu>\r\n                    <DropdownMenuTrigger asChild>\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"ghost\"\r\n                        className={cn(componentStyles.button.ghost, \"h-7 w-7 p-0\")}\r\n                      >\r\n                        <MoreHorizontal className=\"h-3 w-3\" />\r\n                      </Button>\r\n                    </DropdownMenuTrigger>\r\n                    <DropdownMenuContent align=\"end\" className=\"w-48\">\r\n                      <DropdownMenuItem onClick={() => handleCopyAddress(address.roadAddr)}>\r\n                        <Copy className=\"h-3 w-3 mr-2\" />\r\n                        도로명주소 복사\r\n                      </DropdownMenuItem>\r\n                      {address.jibunAddr && (\r\n                        <DropdownMenuItem onClick={() => handleCopyAddress(address.jibunAddr)}>\r\n                          <Copy className=\"h-3 w-3 mr-2\" />\r\n                          지번주소 복사\r\n                        </DropdownMenuItem>\r\n                      )}\r\n                      <DropdownMenuSeparator />\r\n                      <DropdownMenuItem onClick={() => handleCopyAddress(formatCoordinateForDirections(address))}>\r\n                        <Route className=\"h-3 w-3 mr-2\" />\r\n                        좌표 복사\r\n                      </DropdownMenuItem>\r\n                    </DropdownMenuContent>\r\n                  </DropdownMenu>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        ))}\r\n\r\n        {addresses.length > 3 && (\r\n          <div className=\"flex justify-center pt-2\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => setShowAll(!showAll)}\r\n              className={cn(componentStyles.button.ghost, \"text-xs\")}\r\n            >\r\n              {showAll ? (\r\n                <>\r\n                  <ChevronUp className=\"h-3 w-3 mr-1\" />\r\n                  접기\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <ChevronDown className=\"h-3 w-3 mr-1\" />\r\n                  {addresses.length - 3}개 더 보기\r\n                </>\r\n              )}\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </CompactResultTrigger>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;;;AAvBA;;;;;;;;;;;;;AA6CO,SAAS,mBAAmB,EACjC,OAAO,EACP,SAAS,EACT,QAAQ,EACgB;;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,IAAI;IAEJ,IAAI;QACF,SAAS;IACX,EAAE,OAAO,GAAG;QACV,OAAO;IACT;IAEA,IAAI,CAAC,OAAO,MAAM,EAAE,UAAU,QAAQ;QACpC,qBACE,sSAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO;sBACpD,cAAA,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE,EAAE;kCACnD,cAAA,sSAAC,iSAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,sSAAC;;0CACC,sSAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,sSAAC;gCAAE,WAAU;0CAAuB;;;;;;;;;;;;;;;;;;;;;;;IAK9C;IAEA,MAAM,YAAY,OAAO,MAAM,CAAC,QAAQ;IACxC,MAAM,mBAAmB,UAAU,YAAY,UAAU,KAAK,CAAC,GAAG;IAElE,MAAM,gCAAgC,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;yEAAE,CAAC;YACjD,OAAO,GAAG,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,OAAO,GAAG,QAAQ,SAAS,GAAG,CAAC,MAAM,EAAE,QAAQ,SAAS,EAAE,GAAG,IAAI;QACxG;wEAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACrC,UAAU,SAAS,CAAC,SAAS,CAAC;YAC9B,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;4DAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YAClC,IAAI,CAAC,UAAU;gBACb,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAChC,UACA,WAAW,QAAQ,OAAO,GAC1B,WAAW,QAAQ,OAAO,GAC1B,QAAQ,SAAS,IAAI,QAAQ,IAAI,EACjC;YAGF,IAAI,SAAS;gBACX,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,QAAQ,CAAC;YACjD,OAAO;gBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;yDAAG;QAAC;KAAS;IAEb,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;IAEpC,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;QACnB,MAAM,SAAS,IAAI;QACnB,OAAO,SAAS,KAAK;QACrB,OAAM;QACN,WAAW;QACX,0BACE,sSAAC,6HAAA,CAAA,QAAK;YAAC,SAAQ;YAAU,WAAU;;gBAChC,UAAU,MAAM;gBAAC;;;;;;;kBAItB,cAAA,sSAAC;YAAI,WAAU;;gBACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,sSAAC,4HAAA,CAAA,OAAI;wBAAa,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,WAAW,EAAE;kCAChE,cAAA,sSAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE,EAAE;kEACnD,cAAA,sSAAC,ySAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;oDAEvB,QAAQ,SAAS,kBAChB,sSAAC;wDAAK,WAAU;kEACb,QAAQ,SAAS;;;;;;;;;;;;0DAIxB,sSAAC;gDAAE,WAAU;0DACV,QAAQ,QAAQ;;;;;;4CAElB,QAAQ,SAAS,IAAI,QAAQ,SAAS,KAAK,QAAQ,QAAQ,kBAC1D,sSAAC;gDAAE,WAAU;;oDAAwB;oDAC9B,QAAQ,SAAS;;;;;;;;;;;;;kDAI5B,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,eAAe;gDAC9B,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,KAAK,EAAE;0DAE5C,cAAA,sSAAC,qSAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,sSAAC,wIAAA,CAAA,eAAY;;kEACX,sSAAC,wIAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,sSAAC,8HAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,KAAK,EAAE;sEAE5C,cAAA,sSAAC,uSAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG9B,sSAAC,wIAAA,CAAA,sBAAmB;wDAAC,OAAM;wDAAM,WAAU;;0EACzC,sSAAC,wIAAA,CAAA,mBAAgB;gEAAC,SAAS,IAAM,kBAAkB,QAAQ,QAAQ;;kFACjE,sSAAC,yRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;4DAGlC,QAAQ,SAAS,kBAChB,sSAAC,wIAAA,CAAA,mBAAgB;gEAAC,SAAS,IAAM,kBAAkB,QAAQ,SAAS;;kFAClE,sSAAC,yRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAIrC,sSAAC,wIAAA,CAAA,wBAAqB;;;;;0EACtB,sSAAC,wIAAA,CAAA,mBAAgB;gEAAC,SAAS,IAAM,kBAAkB,8BAA8B;;kFAC/E,sSAAC,2RAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAvDrC;;;;;gBAkEZ,UAAU,MAAM,GAAG,mBAClB,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,WAAW,CAAC;wBAC3B,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,KAAK,EAAE;kCAE3C,wBACC;;8CACE,sSAAC,uSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;yDAIxC;;8CACE,sSAAC,2SAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCACtB,UAAU,MAAM,GAAG;gCAAE;;;;;;;;;;;;;;;;;;;;;;;;AASxC;GA3KgB;KAAA", "debugId": null}}, {"offset": {"line": 2658, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/search-destination-result.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useCallback } from \"react\";\r\nimport {\r\n  MapPin, Navigation, Building2, Copy, ChevronDown, ChevronUp,\r\n  Route, MoreHorizontal, Star, Target\r\n} from \"lucide-react\";\r\nimport { AddressResponse } from \"@/types/tools\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuSeparator\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { toast } from \"sonner\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { highlightPointOnMap, type UseMapReturn } from \"@/lib/map-utils\";\r\nimport { componentStyles } from \"@/lib/design-tokens\";\r\nimport { CompactResultTrigger } from \"./common/compact-result-trigger\";\r\nimport { getToolDisplayInfo } from \"../annotations\";\r\n\r\ninterface SearchDestinationResultProps {\r\n  content: AddressResponse;\r\n  className?: string;\r\n  mapState?: UseMapReturn;\r\n}\r\n\r\n// 주소 아이템 타입 정의\r\ninterface AddressItem {\r\n  roadAddr: string;\r\n  jibunAddr: string;\r\n  buildName: string;\r\n  buildLo: string;\r\n  buildLa: string;\r\n  parcelLo: string;\r\n  parcelLa: string;\r\n  poiName: string;\r\n  buildGeom?: string;\r\n  geom?: string;\r\n}\r\n\r\nexport function SearchDestinationResult({\r\n  content,\r\n  className,\r\n  mapState\r\n}: SearchDestinationResultProps) {\r\n  const [showAll, setShowAll] = useState(false);\r\n\r\n  let result: AddressResponse;\r\n\r\n  try {\r\n    result = content as AddressResponse;\r\n  } catch (e) {\r\n    return null;\r\n  }\r\n\r\n  if (!result.result?.jusoList?.length) {\r\n    return (\r\n      <div className={cn(componentStyles.card.error, \"p-3\", className)}>\r\n        <div className=\"flex items-center gap-3\">\r\n          <div className={cn(componentStyles.iconContainer.sm, \"bg-red-100/80 text-red-600 border border-red-200/60\")}>\r\n            <Target className=\"h-3.5 w-3.5\" />\r\n          </div>\r\n          <div>\r\n            <p className=\"font-medium text-red-900 text-sm\">목적지를 찾을 수 없습니다</p>\r\n            <p className=\"text-xs text-red-700\">다른 키워드로 다시 검색해보세요</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const addresses = result.result.jusoList;\r\n  const displayAddresses = showAll ? addresses : addresses.slice(0, 3);\r\n\r\n  const formatCoordinateForDirections = useCallback((address: AddressItem): string => {\r\n    return `${address.buildLo},${address.buildLa}${address.buildName ? `,name=${address.buildName}` : ''}`;\r\n  }, []);\r\n\r\n  const handleCopyAddress = useCallback((address: string) => {\r\n    navigator.clipboard.writeText(address);\r\n    toast.success(\"주소가 복사되었습니다\");\r\n  }, []);\r\n\r\n  const handleNavigate = useCallback((address: AddressItem) => {\r\n    if (!mapState) {\r\n      toast.error(\"지도가 로드되지 않았습니다\");\r\n      return;\r\n    }\r\n\r\n    const success = highlightPointOnMap(\r\n      mapState,\r\n      parseFloat(address.buildLo),\r\n      parseFloat(address.buildLa),\r\n      address.buildGeom || address.geom,\r\n      16\r\n    );\r\n\r\n    if (success) {\r\n      toast.success(`목적지 ${address.roadAddr}로 이동했습니다`);\r\n    } else {\r\n      toast.error(\"지도 이동 중 오류가 발생했습니다\");\r\n    }\r\n  }, [mapState]);\r\n\r\n  const toolInfo = getToolDisplayInfo(\"searchDestination\");\r\n\r\n  return (\r\n    <CompactResultTrigger\r\n      icon={toolInfo.icon}\r\n      title={toolInfo.label}\r\n      state=\"result\"\r\n      className={className}\r\n      titleExtra={\r\n        <Badge variant=\"outline\" className=\"text-xs py-0 border bg-red-100/80 text-red-700 border-red-200/60\">\r\n          {addresses.length}개 발견\r\n        </Badge>\r\n      }\r\n    >\r\n      <div className=\"space-y-2\">\r\n        {displayAddresses.map((address, index) => (\r\n          <Card key={index} className={cn(componentStyles.card.interactive, \"group\")}>\r\n            <CardContent className=\"p-3\">\r\n              <div className=\"flex items-start justify-between gap-3\">\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <div className=\"flex items-center gap-2 mb-1\">\r\n                    <div className={cn(componentStyles.iconContainer.sm, \"bg-blue-100/80 text-blue-600 border border-blue-200/60\")}>\r\n                      <Target className=\"h-3 w-3\" />\r\n                    </div>\r\n                    {address.buildName && (\r\n                      <span className=\"font-medium text-sm text-gray-900 truncate\">\r\n                        {address.buildName}\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                  <p className=\"text-xs text-gray-600 mb-1 leading-relaxed\">\r\n                    {address.roadAddr}\r\n                  </p>\r\n                  {address.jibunAddr && address.jibunAddr !== address.roadAddr && (\r\n                    <p className=\"text-xs text-gray-500\">\r\n                      지번: {address.jibunAddr}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"ghost\"\r\n                    onClick={() => handleNavigate(address)}\r\n                    className={cn(componentStyles.button.ghost, \"h-7 w-7 p-0\")}\r\n                  >\r\n                    <Navigation className=\"h-3 w-3\" />\r\n                  </Button>\r\n                  <DropdownMenu>\r\n                    <DropdownMenuTrigger asChild>\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"ghost\"\r\n                        className={cn(componentStyles.button.ghost, \"h-7 w-7 p-0\")}\r\n                      >\r\n                        <MoreHorizontal className=\"h-3 w-3\" />\r\n                      </Button>\r\n                    </DropdownMenuTrigger>\r\n                    <DropdownMenuContent align=\"end\" className=\"w-48\">\r\n                      <DropdownMenuItem onClick={() => handleCopyAddress(address.roadAddr)}>\r\n                        <Copy className=\"h-3 w-3 mr-2\" />\r\n                        도로명주소 복사\r\n                      </DropdownMenuItem>\r\n                      {address.jibunAddr && (\r\n                        <DropdownMenuItem onClick={() => handleCopyAddress(address.jibunAddr)}>\r\n                          <Copy className=\"h-3 w-3 mr-2\" />\r\n                          지번주소 복사\r\n                        </DropdownMenuItem>\r\n                      )}\r\n                      <DropdownMenuSeparator />\r\n                      <DropdownMenuItem onClick={() => handleCopyAddress(formatCoordinateForDirections(address))}>\r\n                        <Route className=\"h-3 w-3 mr-2\" />\r\n                        좌표 복사\r\n                      </DropdownMenuItem>\r\n                    </DropdownMenuContent>\r\n                  </DropdownMenu>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        ))}\r\n\r\n        {addresses.length > 3 && (\r\n          <div className=\"flex justify-center pt-2\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => setShowAll(!showAll)}\r\n              className={cn(componentStyles.button.ghost, \"text-xs\")}\r\n            >\r\n              {showAll ? (\r\n                <>\r\n                  <ChevronUp className=\"h-3 w-3 mr-1\" />\r\n                  접기\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <ChevronDown className=\"h-3 w-3 mr-1\" />\r\n                  {addresses.length - 3}개 더 보기\r\n                </>\r\n              )}\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </CompactResultTrigger>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;;;AAvBA;;;;;;;;;;;;;AA6CO,SAAS,wBAAwB,EACtC,OAAO,EACP,SAAS,EACT,QAAQ,EACqB;;IAC7B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,IAAI;IAEJ,IAAI;QACF,SAAS;IACX,EAAE,OAAO,GAAG;QACV,OAAO;IACT;IAEA,IAAI,CAAC,OAAO,MAAM,EAAE,UAAU,QAAQ;QACpC,qBACE,sSAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO;sBACpD,cAAA,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE,EAAE;kCACnD,cAAA,sSAAC,6RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,sSAAC;;0CACC,sSAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,sSAAC;gCAAE,WAAU;0CAAuB;;;;;;;;;;;;;;;;;;;;;;;IAK9C;IAEA,MAAM,YAAY,OAAO,MAAM,CAAC,QAAQ;IACxC,MAAM,mBAAmB,UAAU,YAAY,UAAU,KAAK,CAAC,GAAG;IAElE,MAAM,gCAAgC,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;8EAAE,CAAC;YACjD,OAAO,GAAG,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,OAAO,GAAG,QAAQ,SAAS,GAAG,CAAC,MAAM,EAAE,QAAQ,SAAS,EAAE,GAAG,IAAI;QACxG;6EAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;kEAAE,CAAC;YACrC,UAAU,SAAS,CAAC,SAAS,CAAC;YAC9B,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;iEAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;+DAAE,CAAC;YAClC,IAAI,CAAC,UAAU;gBACb,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAChC,UACA,WAAW,QAAQ,OAAO,GAC1B,WAAW,QAAQ,OAAO,GAC1B,QAAQ,SAAS,IAAI,QAAQ,IAAI,EACjC;YAGF,IAAI,SAAS;gBACX,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,QAAQ,CAAC;YACjD,OAAO;gBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;8DAAG;QAAC;KAAS;IAEb,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;IAEpC,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;QACnB,MAAM,SAAS,IAAI;QACnB,OAAO,SAAS,KAAK;QACrB,OAAM;QACN,WAAW;QACX,0BACE,sSAAC,6HAAA,CAAA,QAAK;YAAC,SAAQ;YAAU,WAAU;;gBAChC,UAAU,MAAM;gBAAC;;;;;;;kBAItB,cAAA,sSAAC;YAAI,WAAU;;gBACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,sSAAC,4HAAA,CAAA,OAAI;wBAAa,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,WAAW,EAAE;kCAChE,cAAA,sSAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE,EAAE;kEACnD,cAAA,sSAAC,6RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;oDAEnB,QAAQ,SAAS,kBAChB,sSAAC;wDAAK,WAAU;kEACb,QAAQ,SAAS;;;;;;;;;;;;0DAIxB,sSAAC;gDAAE,WAAU;0DACV,QAAQ,QAAQ;;;;;;4CAElB,QAAQ,SAAS,IAAI,QAAQ,SAAS,KAAK,QAAQ,QAAQ,kBAC1D,sSAAC;gDAAE,WAAU;;oDAAwB;oDAC9B,QAAQ,SAAS;;;;;;;;;;;;;kDAI5B,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,eAAe;gDAC9B,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,KAAK,EAAE;0DAE5C,cAAA,sSAAC,qSAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,sSAAC,wIAAA,CAAA,eAAY;;kEACX,sSAAC,wIAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,sSAAC,8HAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,KAAK,EAAE;sEAE5C,cAAA,sSAAC,uSAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG9B,sSAAC,wIAAA,CAAA,sBAAmB;wDAAC,OAAM;wDAAM,WAAU;;0EACzC,sSAAC,wIAAA,CAAA,mBAAgB;gEAAC,SAAS,IAAM,kBAAkB,QAAQ,QAAQ;;kFACjE,sSAAC,yRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;4DAGlC,QAAQ,SAAS,kBAChB,sSAAC,wIAAA,CAAA,mBAAgB;gEAAC,SAAS,IAAM,kBAAkB,QAAQ,SAAS;;kFAClE,sSAAC,yRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAIrC,sSAAC,wIAAA,CAAA,wBAAqB;;;;;0EACtB,sSAAC,wIAAA,CAAA,mBAAgB;gEAAC,SAAS,IAAM,kBAAkB,8BAA8B;;kFAC/E,sSAAC,2RAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAvDrC;;;;;gBAkEZ,UAAU,MAAM,GAAG,mBAClB,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,WAAW,CAAC;wBAC3B,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,KAAK,EAAE;kCAE3C,wBACC;;8CACE,sSAAC,uSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;yDAIxC;;8CACE,sSAAC,2SAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCACtB,UAAU,MAAM,GAAG;gCAAE;;;;;;;;;;;;;;;;;;;;;;;;AASxC;GA3KgB;KAAA", "debugId": null}}, {"offset": {"line": 3081, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/search-directions-result.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Navigation, MapPin, Clock, Route, Car, AlertTriangle } from \"lucide-react\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { toast } from \"sonner\";\r\nimport { componentStyles } from \"@/lib/design-tokens\";\r\nimport { CompactResultTrigger } from \"./common/compact-result-trigger\";\r\nimport { getToolDisplayInfo } from \"../annotations\";\r\nimport { useLayerManager } from \"@/providers/tool-invocation-provider\";\r\nimport { UseMapReturn } from \"@geon-map/odf\";\r\n\r\n// 제공된 타입 정의 사용\r\nexport interface DirectionsResponse {\r\n  trans_id: string;\r\n  routes: Array<{\r\n    result_code: number;\r\n    result_msg: string;\r\n    summary: {\r\n      distance: number;\r\n      duration: number;\r\n      fare: {\r\n        taxi: number;\r\n        toll: number;\r\n      };\r\n    };\r\n    sections?: Array<{\r\n      distance: number;\r\n      duration: number;\r\n      roads: Array<{\r\n        name: string;\r\n        distance: number;\r\n        duration: number;\r\n        traffic_speed: number;\r\n        traffic_state: number;\r\n        vertexes: number[];\r\n      }>;\r\n    }>;\r\n  }>;\r\n}\r\n\r\ninterface SearchDirectionsResultProps {\r\n  content: DirectionsResponse | string;\r\n  className?: string;\r\n  mapState?: UseMapReturn;\r\n  toolCallId?: string;\r\n}\r\n\r\n// 애니메이션 제거 - 정적 렌더링만 사용\r\n\r\nconst formatDistance = (meters: number): string => {\r\n  if (meters < 1000) {\r\n    return `${Math.round(meters)}m`;\r\n  }\r\n  return `${(meters / 1000).toFixed(1)}km`;\r\n};\r\n\r\nconst formatDuration = (seconds: number): string => {\r\n  const hours = Math.floor(seconds / 3600);\r\n  const minutes = Math.floor((seconds % 3600) / 60);\r\n\r\n  if (hours > 0) {\r\n    return `${hours}시간 ${minutes}분`;\r\n  }\r\n  return `${minutes}분`;\r\n};\r\n\r\nexport function SearchDirectionsResult({ content, className, mapState, toolCallId }: SearchDirectionsResultProps) {\r\n  let result: DirectionsResponse;\r\n  const layerManager = useLayerManager();\r\n\r\n  try {\r\n    result = typeof content === 'string' ? JSON.parse(content) : content;\r\n  } catch (e) {\r\n    return null;\r\n  }\r\n\r\n  const handleToggleRouteLayer = () => {\r\n    if (!layerManager || !toolCallId) {\r\n      toast.error(\"레이어 관리자를 사용할 수 없습니다\");\r\n      return;\r\n    }\r\n\r\n    const routeLayerId = `route-${toolCallId}`;\r\n    const routeLayer = layerManager.getLayerById(routeLayerId);\r\n\r\n    if (routeLayer) {\r\n      layerManager.toggleVisibility(routeLayerId);\r\n      toast.success(routeLayer.visible ? \"경로를 지도에서 숨겼습니다\" : \"경로를 지도에 표시했습니다\");\r\n    } else {\r\n      toast.error(\"경로 레이어를 찾을 수 없습니다\");\r\n    }\r\n  };\r\n\r\n  // routes[0]에서 result_code와 result_msg 확인\r\n  if (!result.routes?.length || result.routes[0].result_code !== 0) {\r\n    const errorMsg = result.routes?.[0]?.result_msg || \"다른 출발지나 목적지를 시도해보세요\";\r\n    return (\r\n      <div className={cn(componentStyles.card.error, \"p-3\", className)}>\r\n        <div className=\"flex items-center gap-3\">\r\n          <div className={cn(componentStyles.iconContainer.sm, \"bg-red-100/80 text-red-600 border border-red-200/60\")}>\r\n            <AlertTriangle className=\"h-3.5 w-3.5\" />\r\n          </div>\r\n          <div>\r\n            <p className=\"font-medium text-red-900 text-sm\">경로를 찾을 수 없습니다</p>\r\n            <p className=\"text-xs text-red-700\">\r\n              {errorMsg}\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const route = result.routes[0];\r\n  const routeSummary = route.summary;\r\n\r\n  const toolInfo = getToolDisplayInfo(\"searchDirections\");\r\n\r\n  return (\r\n    <CompactResultTrigger\r\n      icon={toolInfo.icon}\r\n      title={toolInfo.label}\r\n      state=\"result\"\r\n      className={className}\r\n      titleExtra={\r\n        <div className=\"flex items-center gap-1\">\r\n          <Badge variant=\"outline\" className=\"text-xs py-0 border bg-emerald-100/80 text-emerald-700 border-emerald-200/60\">\r\n            {formatDistance(routeSummary.distance)}\r\n          </Badge>\r\n          <Badge variant=\"outline\" className=\"text-xs py-0 border bg-emerald-100/80 text-emerald-700 border-emerald-200/60\">\r\n            {formatDuration(routeSummary.duration)}\r\n          </Badge>\r\n        </div>\r\n      }\r\n    >\r\n      <div className=\"space-y-2\">\r\n\r\n        {/* 요금 정보 */}\r\n        {routeSummary.fare && (\r\n          <div>\r\n            <Card className={cn(componentStyles.card.base, \"bg-purple-50/60 border-purple-200/50\")}>\r\n              <CardContent className=\"p-3\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Car className=\"h-4 w-4 text-purple-600\" />\r\n                    <span className=\"text-sm font-medium text-purple-900\">예상 요금</span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-3 text-sm\">\r\n                    <div className=\"text-neutral-700\">\r\n                      택시: <span className=\"font-medium\">{routeSummary.fare.taxi.toLocaleString()}원</span>\r\n                    </div>\r\n                    <div className=\"text-neutral-700\">\r\n                      통행료: <span className=\"font-medium\">{routeSummary.fare.toll.toLocaleString()}원</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </div>\r\n        )}\r\n\r\n        {/* 구간별 상세 정보 */}\r\n        {route.sections && route.sections.length > 0 && (\r\n          <div>\r\n            <Card className={cn(componentStyles.card.base, \"bg-neutral-50/60 border-neutral-200/50\")}>\r\n              <CardContent className=\"p-3\">\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Route className=\"h-4 w-4 text-neutral-600\" />\r\n                    <span className=\"text-sm font-medium text-neutral-900\">구간 정보</span>\r\n                  </div>\r\n                  <Badge variant=\"outline\" className=\"text-xs bg-neutral-100/80 text-neutral-700 border-neutral-200/60\">\r\n                    {route.sections.length}개 구간\r\n                  </Badge>\r\n                </div>\r\n                <div className=\"space-y-1\">\r\n                  {route.sections.slice(0, 3).map((section: any, index: number) => (\r\n                    <div key={index} className=\"flex items-center justify-between text-xs p-2 bg-white/80 rounded border border-neutral-200/50\">\r\n                      <span className=\"text-neutral-700\">구간 {index + 1}</span>\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <span className=\"text-neutral-600\">{formatDistance(section.distance)}</span>\r\n                        <span className=\"text-neutral-600\">{formatDuration(section.duration)}</span>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                  {route.sections.length > 3 && (\r\n                    <div className=\"text-center text-xs text-neutral-500 py-1\">\r\n                      +{route.sections.length - 3}개 구간 더\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </div>\r\n        )}\r\n\r\n\r\n      </div>\r\n    </CompactResultTrigger>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;AAmDA,wBAAwB;AAExB,MAAM,iBAAiB,CAAC;IACtB,IAAI,SAAS,MAAM;QACjB,OAAO,GAAG,KAAK,KAAK,CAAC,QAAQ,CAAC,CAAC;IACjC;IACA,OAAO,GAAG,CAAC,SAAS,IAAI,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;AAC1C;AAEA,MAAM,iBAAiB,CAAC;IACtB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAE9C,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,GAAG,EAAE,QAAQ,CAAC,CAAC;IACjC;IACA,OAAO,GAAG,QAAQ,CAAC,CAAC;AACtB;AAEO,SAAS,uBAAuB,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAA+B;;IAC9G,IAAI;IACJ,MAAM,eAAe,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD;IAEnC,IAAI;QACF,SAAS,OAAO,YAAY,WAAW,KAAK,KAAK,CAAC,WAAW;IAC/D,EAAE,OAAO,GAAG;QACV,OAAO;IACT;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,gBAAgB,CAAC,YAAY;YAChC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,eAAe,CAAC,MAAM,EAAE,YAAY;QAC1C,MAAM,aAAa,aAAa,YAAY,CAAC;QAE7C,IAAI,YAAY;YACd,aAAa,gBAAgB,CAAC;YAC9B,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,WAAW,OAAO,GAAG,mBAAmB;QACxD,OAAO;YACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,yCAAyC;IACzC,IAAI,CAAC,OAAO,MAAM,EAAE,UAAU,OAAO,MAAM,CAAC,EAAE,CAAC,WAAW,KAAK,GAAG;QAChE,MAAM,WAAW,OAAO,MAAM,EAAE,CAAC,EAAE,EAAE,cAAc;QACnD,qBACE,sSAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO;sBACpD,cAAA,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE,EAAE;kCACnD,cAAA,sSAAC,+SAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;kCAE3B,sSAAC;;0CACC,sSAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,sSAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;;;;;;;IAMb;IAEA,MAAM,QAAQ,OAAO,MAAM,CAAC,EAAE;IAC9B,MAAM,eAAe,MAAM,OAAO;IAElC,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;IAEpC,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;QACnB,MAAM,SAAS,IAAI;QACnB,OAAO,SAAS,KAAK;QACrB,OAAM;QACN,WAAW;QACX,0BACE,sSAAC;YAAI,WAAU;;8BACb,sSAAC,6HAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAChC,eAAe,aAAa,QAAQ;;;;;;8BAEvC,sSAAC,6HAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAChC,eAAe,aAAa,QAAQ;;;;;;;;;;;;kBAK3C,cAAA,sSAAC;YAAI,WAAU;;gBAGZ,aAAa,IAAI,kBAChB,sSAAC;8BACC,cAAA,sSAAC,4HAAA,CAAA,OAAI;wBAAC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,IAAI,EAAE;kCAC7C,cAAA,sSAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,uRAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,sSAAC;gDAAK,WAAU;0DAAsC;;;;;;;;;;;;kDAExD,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;oDAAmB;kEAC5B,sSAAC;wDAAK,WAAU;;4DAAe,aAAa,IAAI,CAAC,IAAI,CAAC,cAAc;4DAAG;;;;;;;;;;;;;0DAE7E,sSAAC;gDAAI,WAAU;;oDAAmB;kEAC3B,sSAAC;wDAAK,WAAU;;4DAAe,aAAa,IAAI,CAAC,IAAI,CAAC,cAAc;4DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAUzF,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,GAAG,mBACzC,sSAAC;8BACC,cAAA,sSAAC,4HAAA,CAAA,OAAI;wBAAC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,IAAI,EAAE;kCAC7C,cAAA,sSAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAI,WAAU;;8DACb,sSAAC,2RAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,sSAAC;oDAAK,WAAU;8DAAuC;;;;;;;;;;;;sDAEzD,sSAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;;gDAChC,MAAM,QAAQ,CAAC,MAAM;gDAAC;;;;;;;;;;;;;8CAG3B,sSAAC;oCAAI,WAAU;;wCACZ,MAAM,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAc,sBAC7C,sSAAC;gDAAgB,WAAU;;kEACzB,sSAAC;wDAAK,WAAU;;4DAAmB;4DAAI,QAAQ;;;;;;;kEAC/C,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;gEAAK,WAAU;0EAAoB,eAAe,QAAQ,QAAQ;;;;;;0EACnE,sSAAC;gEAAK,WAAU;0EAAoB,eAAe,QAAQ,QAAQ;;;;;;;;;;;;;+CAJ7D;;;;;wCAQX,MAAM,QAAQ,CAAC,MAAM,GAAG,mBACvB,sSAAC;4CAAI,WAAU;;gDAA4C;gDACvD,MAAM,QAAQ,CAAC,MAAM,GAAG;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAalD;GAtIgB;;QAEO,+IAAA,CAAA,kBAAe;;;KAFtB", "debugId": null}}, {"offset": {"line": 3508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/layer-list-result.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport { Layers3, Search, Info, Plus, ChevronDown, ChevronUp, Database, User } from \"lucide-react\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { toast } from \"sonner\";\r\nimport { componentStyles } from \"@/lib/design-tokens\";\r\nimport { CompactResultTrigger } from \"./common/compact-result-trigger\";\r\nimport { getToolDisplayInfo } from \"../annotations\";\r\nimport { LayerConfig, LayerInfo, UseMapReturn } from \"@geon-map/odf\";\r\n\r\n// 실제 타입에 맞게 업데이트\r\ninterface LayerListItem {\r\n  lyrId: string;\r\n  cntntsId: string;\r\n  jobClCode: string;\r\n  jobClCodeNm: string;\r\n  lyrNm: string;\r\n  lyrClCode: string;\r\n  lyrClCodeNm: string;\r\n  lyrClSeCode: string;\r\n  lyrClSeCodeNm: string;\r\n  lyrPosesnSeCode: string;\r\n  lyrPosesnSeCodeNm: string;\r\n  lyrTySeCode: string;\r\n  lyrTySeCodeNm: string;\r\n  svcTySeCode: string;\r\n  svcTySeCodeNm: string;\r\n  cntmSeCode: string;\r\n  cntmSeCodeNm: string;\r\n  usePblonsipSeCode: string;\r\n  usePblonsipSeCodeNm: string;\r\n  useSttusSeCode: string;\r\n  useSttusSeCodeNm: string;\r\n  holdDataId: string;\r\n  lyrDc: string;\r\n  mapUrl: string;\r\n  mapUrlParamtr: string;\r\n  xyOrdrNrmltAt: string;\r\n  registerId: string;\r\n  registerNm: string;\r\n  registDt: string;\r\n  updusrId: string;\r\n  updusrNm: string;\r\n  updtDt: string;\r\n  userNm: string;\r\n  ownerNm: string;\r\n  ownerId: string;\r\n}\r\n\r\ninterface LayerListResponse {\r\n  code: number;\r\n  message: string;\r\n  result: {\r\n    pageInfo: {\r\n      pageSize: number;\r\n      pageIndex: number;\r\n      totalCount: number;\r\n    };\r\n    list: LayerListItem[];\r\n  };\r\n}\r\n\r\ninterface LayerListResultProps {\r\n  content: LayerListResponse | string;\r\n  className?: string;\r\n  mapState?: UseMapReturn;\r\n}\r\n\r\nconst getLayerTypeColor = (type: string) => {\r\n  const colors = {\r\n    'wms': 'bg-blue-100/80 text-blue-700 border-blue-200/60',\r\n    'wfs': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',\r\n    'vector': 'bg-purple-100/80 text-purple-700 border-purple-200/60',\r\n    'raster': 'bg-orange-100/80 text-orange-700 border-orange-200/60',\r\n    'default': 'bg-neutral-100/80 text-neutral-700 border-neutral-200/60'\r\n  };\r\n  return colors[type as keyof typeof colors] || colors.default;\r\n};\r\n\r\nexport function LayerListResult({ content, className, mapState }: LayerListResultProps) {\r\n  let result: LayerListResponse;\r\n\r\n  try {\r\n    result = typeof content === 'string' ? JSON.parse(content) : content;\r\n  } catch (e) {\r\n    return null;\r\n  }\r\n\r\n  const handleAddLayer = async (layer: LayerListItem) => {\r\n    if (!mapState) {\r\n      toast.error(\"지도가 로드되지 않았습니다\");\r\n      return;\r\n    }\r\n\r\n    // const success = await addLayerToMap(mapState, layerInfo, layerConfig);\r\n\r\n    // if (success) {\r\n    //   toast.success(`${layer.lyrNm} 레이어를 추가했습니다`);\r\n    // } else {\r\n    //   toast.error(\"레이어 추가에 실패했습니다\");\r\n    // }\r\n  };\r\n\r\n  const handleViewLayerInfo = (layer: LayerListItem) => {\r\n    console.log('레이어 정보 보기:', layer);\r\n    toast.info(`${layer.lyrNm} 레이어 정보를 표시합니다`);\r\n    // TODO: 레이어 상세 정보 모달 표시\r\n  };\r\n\r\n  if (result.code !== 200 || !result.result?.list?.length) {\r\n    return (\r\n      <div className={cn(componentStyles.card.warning, \"p-3\", className)}>\r\n        <div className=\"flex items-center gap-3\">\r\n          <div className={cn(componentStyles.iconContainer.sm, \"bg-amber-100/80 text-amber-600 border border-amber-200/60\")}>\r\n            <Search className=\"h-3.5 w-3.5\" />\r\n          </div>\r\n          <div>\r\n            <p className=\"font-medium text-amber-900 text-sm\">레이어 목록 검색 결과가 없습니다</p>\r\n            <p className=\"text-xs text-amber-700\">다른 키워드로 다시 검색해보세요</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const layers = result.result.list;\r\n  const totalCount = result.result.pageInfo.totalCount;\r\n  const [showAll, setShowAll] = useState(layers.length <= 5);\r\n  const displayLayers = showAll ? layers : layers.slice(0, 5);\r\n\r\n  const toolInfo = getToolDisplayInfo(\"getLayerList\");\r\n\r\n  return (\r\n    <CompactResultTrigger\r\n      icon={toolInfo.icon}\r\n      title={toolInfo.label}\r\n      state=\"result\"\r\n      className={className}\r\n      titleExtra={\r\n        <div className=\"flex items-center gap-1\">\r\n          <Badge variant=\"outline\" className=\"text-xs py-0 border bg-purple-100/80 text-purple-700 border-purple-200/60\">\r\n            {totalCount}개 발견\r\n          </Badge>\r\n          {layers.length > 5 && (\r\n            <Badge variant=\"outline\" className=\"text-xs py-0 border bg-neutral-100/80 text-neutral-700 border-neutral-200/60\">\r\n              상위 5개\r\n            </Badge>\r\n          )}\r\n        </div>\r\n      }\r\n    >\r\n      <div className=\"space-y-2\">\r\n\r\n      {/* 레이어 목록 */}\r\n      <div className=\"space-y-1.5\">\r\n        {displayLayers.map((layer) => (\r\n          <div\r\n            key={layer.lyrId}\r\n            className=\"group\"\r\n          >\r\n              <Card className={cn(componentStyles.card.base, componentStyles.card.interactive, \"bg-white/90 border-neutral-200/60 hover:border-purple-300/60 hover:bg-purple-50/30\")}>\r\n                <CardContent className=\"p-3\">\r\n                  <div className=\"flex items-center justify-between gap-3\">\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      {/* 레이어 정보 */}\r\n                      <div className=\"flex items-start gap-2 mb-1\">\r\n                        <div className={cn(componentStyles.iconContainer.sm, \"bg-purple-100/80 text-purple-600 border border-purple-200/60 mt-0.5 flex-shrink-0\")}>\r\n                          <Layers3 className=\"h-2.5 w-2.5\" />\r\n                        </div>\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <p className=\"font-medium text-neutral-900 text-sm leading-tight truncate\">\r\n                            {layer.lyrNm}\r\n                          </p>\r\n                          <div className=\"flex items-center gap-2 mt-0.5\">\r\n                            <Badge variant=\"outline\" className={cn(\"text-xs border\", getLayerTypeColor(layer.svcTySeCodeNm?.toLowerCase() || 'default'))}>\r\n                              {layer.svcTySeCodeNm}\r\n                            </Badge>\r\n                            {layer.lyrClCodeNm && (\r\n                              <Badge variant=\"outline\" className=\"text-xs bg-neutral-100/80 text-neutral-600 border-neutral-200/60\">\r\n                                {layer.lyrClCodeNm}\r\n                              </Badge>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* 추가 정보 */}\r\n                      <div className=\"ml-7 space-y-0.5\">\r\n                        {layer.lyrDc && (\r\n                          <p className=\"text-xs text-neutral-600 line-clamp-1\">\r\n                            {layer.lyrDc}\r\n                          </p>\r\n                        )}\r\n                        <div className=\"flex items-center gap-3 text-xs text-neutral-500\">\r\n                          {layer.ownerNm && (\r\n                            <div className=\"flex items-center gap-1\">\r\n                              <User className=\"h-2.5 w-2.5\" />\r\n                              <span>{layer.ownerNm}</span>\r\n                            </div>\r\n                          )}\r\n                          {layer.registDt && (\r\n                            <div className=\"flex items-center gap-1\">\r\n                              <Database className=\"h-2.5 w-2.5\" />\r\n                              <span>{new Date(layer.registDt).toLocaleDateString()}</span>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* 컴팩트 액션 버튼들 */}\r\n                    <div className=\"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity\">\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"default\"\r\n                        className={cn(componentStyles.button.primary, \"h-6 px-2 text-xs\")}\r\n                        onClick={(e) => {\r\n                          e.stopPropagation();\r\n                          handleAddLayer(layer);\r\n                        }}\r\n                      >\r\n                        <Plus className=\"h-2.5 w-2.5 mr-1\" />\r\n                        추가\r\n                      </Button>\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"ghost\"\r\n                        className={cn(componentStyles.button.ghost, \"h-6 w-6 p-0 hover:bg-purple-100/80\")}\r\n                        onClick={(e) => {\r\n                          e.stopPropagation();\r\n                          handleViewLayerInfo(layer);\r\n                        }}\r\n                      >\r\n                        <Info className=\"h-2.5 w-2.5\" />\r\n                      </Button>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          ))}\r\n      </div>\r\n\r\n      {/* 더보기/접기 버튼 */}\r\n      {layers.length > 5 && (\r\n        <div>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={() => setShowAll(!showAll)}\r\n            className={cn(componentStyles.button.secondary, \"w-full h-8 text-xs border-dashed border-neutral-300 hover:border-solid hover:border-purple-300 hover:bg-purple-50/60 hover:text-purple-700\")}\r\n          >\r\n            {showAll ? (\r\n              <>\r\n                <ChevronUp className=\"h-3 w-3 mr-1\" />\r\n                접기\r\n              </>\r\n            ) : (\r\n              <>\r\n                <ChevronDown className=\"h-3 w-3 mr-1\" />\r\n                {layers.length - 5}개 더보기\r\n              </>\r\n            )}\r\n          </Button>\r\n        </div>\r\n      )}\r\n\r\n      {/* 페이지 정보 */}\r\n      {totalCount > layers.length && (\r\n        <div>\r\n          <div className=\"text-center p-2 text-xs text-neutral-500 bg-neutral-50/60 rounded-lg border border-neutral-200/50\">\r\n            총 {totalCount}개 중 {layers.length}개 표시\r\n          </div>\r\n        </div>\r\n      )}\r\n      </div>\r\n    </CompactResultTrigger>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAwEA,MAAM,oBAAoB,CAAC;IACzB,MAAM,SAAS;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,WAAW;IACb;IACA,OAAO,MAAM,CAAC,KAA4B,IAAI,OAAO,OAAO;AAC9D;AAEO,SAAS,gBAAgB,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAwB;;IACpF,IAAI;IAEJ,IAAI;QACF,SAAS,OAAO,YAAY,WAAW,KAAK,KAAK,CAAC,WAAW;IAC/D,EAAE,OAAO,GAAG;QACV,OAAO;IACT;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI,CAAC,UAAU;YACb,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;IAEA,yEAAyE;IAEzE,iBAAiB;IACjB,iDAAiD;IACjD,WAAW;IACX,mCAAmC;IACnC,IAAI;IACN;IAEA,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,cAAc;QAC1B,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,GAAG,MAAM,KAAK,CAAC,cAAc,CAAC;IACzC,wBAAwB;IAC1B;IAEA,IAAI,OAAO,IAAI,KAAK,OAAO,CAAC,OAAO,MAAM,EAAE,MAAM,QAAQ;QACvD,qBACE,sSAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;sBACtD,cAAA,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE,EAAE;kCACnD,cAAA,sSAAC,6RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,sSAAC;;0CACC,sSAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAClD,sSAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;;;;;;;;;;;;IAKhD;IAEA,MAAM,SAAS,OAAO,MAAM,CAAC,IAAI;IACjC,MAAM,aAAa,OAAO,MAAM,CAAC,QAAQ,CAAC,UAAU;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,MAAM,IAAI;IACxD,MAAM,gBAAgB,UAAU,SAAS,OAAO,KAAK,CAAC,GAAG;IAEzD,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;IAEpC,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;QACnB,MAAM,SAAS,IAAI;QACnB,OAAO,SAAS,KAAK;QACrB,OAAM;QACN,WAAW;QACX,0BACE,sSAAC;YAAI,WAAU;;8BACb,sSAAC,6HAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;;wBAChC;wBAAW;;;;;;;gBAEb,OAAO,MAAM,GAAG,mBACf,sSAAC,6HAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAA+E;;;;;;;;;;;;kBAOxH,cAAA,sSAAC;YAAI,WAAU;;8BAGf,sSAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,sSAAC;4BAEC,WAAU;sCAER,cAAA,sSAAC,4HAAA,CAAA,OAAI;gCAAC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,IAAI,EAAE,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,WAAW,EAAE;0CAC/E,cAAA,sSAAC,4HAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEAEb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;gEAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE,EAAE;0EACnD,cAAA,sSAAC,mSAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;;;;;;0EAErB,sSAAC;gEAAI,WAAU;;kFACb,sSAAC;wEAAE,WAAU;kFACV,MAAM,KAAK;;;;;;kFAEd,sSAAC;wEAAI,WAAU;;0FACb,sSAAC,6HAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB,kBAAkB,MAAM,aAAa,EAAE,iBAAiB;0FAC9G,MAAM,aAAa;;;;;;4EAErB,MAAM,WAAW,kBAChB,sSAAC,6HAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;0FAChC,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;kEAQ5B,sSAAC;wDAAI,WAAU;;4DACZ,MAAM,KAAK,kBACV,sSAAC;gEAAE,WAAU;0EACV,MAAM,KAAK;;;;;;0EAGhB,sSAAC;gEAAI,WAAU;;oEACZ,MAAM,OAAO,kBACZ,sSAAC;wEAAI,WAAU;;0FACb,sSAAC,yRAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,sSAAC;0FAAM,MAAM,OAAO;;;;;;;;;;;;oEAGvB,MAAM,QAAQ,kBACb,sSAAC;wEAAI,WAAU;;0FACb,sSAAC,iSAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,sSAAC;0FAAM,IAAI,KAAK,MAAM,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQ5D,sSAAC;gDAAI,WAAU;;kEACb,sSAAC,8HAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,OAAO,EAAE;wDAC9C,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,eAAe;wDACjB;;0EAEA,sSAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAqB;;;;;;;kEAGvC,sSAAC,8HAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,KAAK,EAAE;wDAC5C,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,oBAAoB;wDACtB;kEAEA,cAAA,sSAAC,yRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA5EvB,MAAM,KAAK;;;;;;;;;;gBAuFrB,OAAO,MAAM,GAAG,mBACf,sSAAC;8BACC,cAAA,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,WAAW,CAAC;wBAC3B,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,SAAS,EAAE;kCAE/C,wBACC;;8CACE,sSAAC,uSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;yDAIxC;;8CACE,sSAAC,2SAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCACtB,OAAO,MAAM,GAAG;gCAAE;;;;;;;;;;;;;gBAQ5B,aAAa,OAAO,MAAM,kBACzB,sSAAC;8BACC,cAAA,sSAAC;wBAAI,WAAU;;4BAAoG;4BAC9G;4BAAW;4BAAK,OAAO,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;;;;;AAO7C;GAvMgB;KAAA", "debugId": null}}, {"offset": {"line": 3998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/layer-info-result.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Layers3, <PERSON>fo, Eye, EyeOff, Settings, Database, MapPin, Hash, Palette } from \"lucide-react\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Hover<PERSON>ard,\r\n  HoverCardContent,\r\n  HoverCardTrigger,\r\n} from \"@/components/ui/hover-card\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { toast } from \"sonner\";\r\nimport { componentStyles } from \"@/lib/design-tokens\";\r\nimport { UseMapReturn } from \"@geon-map/odf\";\r\nimport { CompactResultTrigger } from \"./common/compact-result-trigger\";\r\nimport { getToolDisplayInfo } from \"../annotations\";\r\n\r\ninterface LayerInfoResponse {\r\n  id: string;\r\n  name: string;\r\n  type: string;\r\n  visible: boolean;\r\n  zIndex: number;\r\n  server?: string;\r\n  layer?: string;\r\n  service?: string;\r\n  bbox?: boolean;\r\n  method?: string;\r\n  crtfckey?: string;\r\n  projection?: string;\r\n  geometryType?: string;\r\n  info: {\r\n    lyrId: string;\r\n    lyrNm: string;\r\n    description?: string;\r\n    metadata: {\r\n      cntntsId: string;\r\n      jobClCode?: string;\r\n      lyrClCode: string;\r\n      lyrTySeCode: string;\r\n      namespace?: string;\r\n    };\r\n  };\r\n  namespace?: string;\r\n  style?: any;\r\n  matrixSet?: string;\r\n  opacity?: number;\r\n  filter?: string;\r\n  dynmFlterCndCn?: string;\r\n}\r\n\r\ninterface LayerInfoResultProps {\r\n  content: LayerInfoResponse | string;\r\n  className?: string;\r\n  mapState?: UseMapReturn;\r\n}\r\n\r\nconst getServiceTypeColor = (service: string) => {\r\n  const colors = {\r\n    'wms': 'bg-blue-100/80 text-blue-700 border-blue-200/60',\r\n    'wfs': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',\r\n    'wmts': 'bg-purple-100/80 text-purple-700 border-purple-200/60',\r\n    'xyz': 'bg-orange-100/80 text-orange-700 border-orange-200/60',\r\n    'default': 'bg-neutral-100/80 text-neutral-700 border-neutral-200/60'\r\n  };\r\n  return colors[service as keyof typeof colors] || colors.default;\r\n};\r\n\r\nexport function LayerInfoResult({ content, className, mapState }: LayerInfoResultProps) {\r\n  let result: LayerInfoResponse;\r\n\r\n  try {\r\n    result = typeof content === 'string' ? JSON.parse(content) : content;\r\n  } catch (e) {\r\n    return null;\r\n  }\r\n\r\n  const handleToggleVisibility = () => {\r\n    if (mapState?.layer?.toggle) {\r\n      mapState.layer.toggle(result.id);\r\n      toast.success(`레이어가 ${result.visible ? '숨겨졌습니다' : '표시됩니다'}`);\r\n    } else {\r\n      toast.info(\"지도가 로드되지 않았습니다\");\r\n    }\r\n  };\r\n\r\n  const handleLayerSettings = () => {\r\n    toast.info(\"레이어 설정을 열었습니다\");\r\n  };\r\n\r\n  // 에러 처리는 실제 데이터 구조에 맞게 수정\r\n  if (!result || !result.id) {\r\n    return (\r\n      <div className={cn(componentStyles.card.error, \"p-3\", className)}>\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className={cn(componentStyles.iconContainer.sm, \"bg-red-100/80 text-red-600 border border-red-200/60\")}>\r\n            <Info className=\"h-3 w-3\" />\r\n          </div>\r\n          <div>\r\n            <p className=\"font-medium text-red-900 text-sm\">레이어 정보를 가져올 수 없습니다</p>\r\n            <p className=\"text-xs text-red-700\">\r\n              레이어가 존재하지 않거나 접근할 수 없습니다\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const toolInfo = getToolDisplayInfo(\"getLayerInfo\");\r\n\r\n  return (\r\n    <CompactResultTrigger\r\n      icon={toolInfo.icon}\r\n      title={toolInfo.label}\r\n      state=\"result\"\r\n      className={className}\r\n      titleExtra={\r\n        <div className=\"flex items-center gap-1\">\r\n          {result.service && (\r\n            <Badge variant=\"outline\" className={cn(\"text-xs py-0 border\", getServiceTypeColor(result.service))}>\r\n              {result.service.toUpperCase()}\r\n            </Badge>\r\n          )}\r\n          <Badge\r\n            variant=\"outline\"\r\n            className={cn(\r\n              \"text-xs border py-0\",\r\n              result.visible\r\n                ? \"bg-emerald-100/80 text-emerald-700 border-emerald-200/60\"\r\n                : \"bg-neutral-100/80 text-neutral-700 border-neutral-200/60\"\r\n            )}\r\n          >\r\n            {result.visible ? \"표시\" : \"숨김\"}\r\n          </Badge>\r\n        </div>\r\n      }\r\n    >\r\n      {/* 레이어 기본 정보 */}\r\n      <div className=\"space-y-2\">\r\n        <div className=\"text-xs text-neutral-600\">\r\n          ID: {result.info?.lyrId || result.id}\r\n        </div>\r\n\r\n        {/* 상세 정보 그리드 */}\r\n        <div className=\"grid grid-cols-2 gap-3 text-xs\">\r\n          <div className=\"space-y-1\">\r\n            <div className=\"flex items-center gap-1\">\r\n              <Layers3 className=\"h-3 w-3 text-neutral-500\" />\r\n              <span className=\"font-medium text-neutral-700\">타입</span>\r\n            </div>\r\n            <Badge variant=\"outline\" className=\"text-xs h-5\">\r\n              {result.type || 'Unknown'}\r\n            </Badge>\r\n          </div>\r\n\r\n          {result.service && (\r\n            <div className=\"space-y-1\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <Database className=\"h-3 w-3 text-neutral-500\" />\r\n                <span className=\"font-medium text-neutral-700\">서비스</span>\r\n              </div>\r\n              <Badge variant=\"outline\" className={cn(\"text-xs h-5\", getServiceTypeColor(result.service))}>\r\n                {result.service.toUpperCase()}\r\n              </Badge>\r\n            </div>\r\n          )}\r\n\r\n          {result.opacity !== undefined && (\r\n            <div className=\"space-y-1\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <Palette className=\"h-3 w-3 text-neutral-500\" />\r\n                <span className=\"font-medium text-neutral-700\">투명도</span>\r\n              </div>\r\n              <p className=\"text-neutral-600\">{Math.round(result.opacity * 100)}%</p>\r\n            </div>\r\n          )}\r\n\r\n          {result.zIndex !== undefined && (\r\n            <div className=\"space-y-1\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <MapPin className=\"h-3 w-3 text-neutral-500\" />\r\n                <span className=\"font-medium text-neutral-700\">Z-Index</span>\r\n              </div>\r\n              <p className=\"text-neutral-600\">{result.zIndex}</p>\r\n            </div>\r\n          )}\r\n\r\n          {result.projection && (\r\n            <div className=\"space-y-1 col-span-2\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <Database className=\"h-3 w-3 text-neutral-500\" />\r\n                <span className=\"font-medium text-neutral-700\">좌표계</span>\r\n              </div>\r\n              <p className=\"text-neutral-600 font-mono text-xs\">{result.projection}</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* 메타데이터 정보 */}\r\n        {result.info?.metadata && (\r\n          <div className=\"pt-2 border-t border-neutral-200\">\r\n            <h5 className=\"text-xs font-medium text-neutral-700 mb-2\">메타데이터</h5>\r\n            <div className=\"space-y-1 text-xs\">\r\n              {result.info.metadata.cntntsId && (\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-neutral-500\">콘텐츠 ID</span>\r\n                  <span className=\"text-neutral-700 font-mono\">{result.info.metadata.cntntsId}</span>\r\n                </div>\r\n              )}\r\n              {result.info.metadata.lyrClCode && (\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-neutral-500\">레이어 분류</span>\r\n                  <span className=\"text-neutral-700\">{result.info.metadata.lyrClCode}</span>\r\n                </div>\r\n              )}\r\n              {result.info.metadata.lyrTySeCode && (\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-neutral-500\">레이어 유형</span>\r\n                  <span className=\"text-neutral-700\">{result.info.metadata.lyrTySeCode}</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* 설명 */}\r\n        {result.info?.description && (\r\n          <div className=\"pt-2 border-t border-neutral-200\">\r\n            <h5 className=\"text-xs font-medium text-neutral-700 mb-1\">설명</h5>\r\n            <p className=\"text-xs text-neutral-600\">{result.info.description}</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </CompactResultTrigger>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AACA;AAOA;AACA;AACA;AAEA;AACA;AAhBA;;;;;;;;;AA0DA,MAAM,sBAAsB,CAAC;IAC3B,MAAM,SAAS;QACb,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,WAAW;IACb;IACA,OAAO,MAAM,CAAC,QAA+B,IAAI,OAAO,OAAO;AACjE;AAEO,SAAS,gBAAgB,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAwB;IACpF,IAAI;IAEJ,IAAI;QACF,SAAS,OAAO,YAAY,WAAW,KAAK,KAAK,CAAC,WAAW;IAC/D,EAAE,OAAO,GAAG;QACV,OAAO;IACT;IAEA,MAAM,yBAAyB;QAC7B,IAAI,UAAU,OAAO,QAAQ;YAC3B,SAAS,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE;YAC/B,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,OAAO,OAAO,GAAG,WAAW,SAAS;QAC7D,OAAO;YACL,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC;IACb;IAEA,0BAA0B;IAC1B,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE;QACzB,qBACE,sSAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO;sBACpD,cAAA,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE,EAAE;kCACnD,cAAA,sSAAC,yRAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,sSAAC;;0CACC,sSAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,sSAAC;gCAAE,WAAU;0CAAuB;;;;;;;;;;;;;;;;;;;;;;;IAO9C;IAEA,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;IAEpC,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;QACnB,MAAM,SAAS,IAAI;QACnB,OAAO,SAAS,KAAK;QACrB,OAAM;QACN,WAAW;QACX,0BACE,sSAAC;YAAI,WAAU;;gBACZ,OAAO,OAAO,kBACb,sSAAC,6HAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB,oBAAoB,OAAO,OAAO;8BAC7F,OAAO,OAAO,CAAC,WAAW;;;;;;8BAG/B,sSAAC,6HAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uBACA,OAAO,OAAO,GACV,6DACA;8BAGL,OAAO,OAAO,GAAG,OAAO;;;;;;;;;;;;kBAM/B,cAAA,sSAAC;YAAI,WAAU;;8BACb,sSAAC;oBAAI,WAAU;;wBAA2B;wBACnC,OAAO,IAAI,EAAE,SAAS,OAAO,EAAE;;;;;;;8BAItC,sSAAC;oBAAI,WAAU;;sCACb,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,mSAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,sSAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;8CAEjD,sSAAC,6HAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAChC,OAAO,IAAI,IAAI;;;;;;;;;;;;wBAInB,OAAO,OAAO,kBACb,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,iSAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,sSAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;8CAEjD,sSAAC,6HAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe,oBAAoB,OAAO,OAAO;8CACrF,OAAO,OAAO,CAAC,WAAW;;;;;;;;;;;;wBAKhC,OAAO,OAAO,KAAK,2BAClB,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,+RAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,sSAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;8CAEjD,sSAAC;oCAAE,WAAU;;wCAAoB,KAAK,KAAK,CAAC,OAAO,OAAO,GAAG;wCAAK;;;;;;;;;;;;;wBAIrE,OAAO,MAAM,KAAK,2BACjB,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,iSAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,sSAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;8CAEjD,sSAAC;oCAAE,WAAU;8CAAoB,OAAO,MAAM;;;;;;;;;;;;wBAIjD,OAAO,UAAU,kBAChB,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,iSAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,sSAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;8CAEjD,sSAAC;oCAAE,WAAU;8CAAsC,OAAO,UAAU;;;;;;;;;;;;;;;;;;gBAMzE,OAAO,IAAI,EAAE,0BACZ,sSAAC;oBAAI,WAAU;;sCACb,sSAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,sSAAC;4BAAI,WAAU;;gCACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,kBAC5B,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAK,WAAU;sDAAmB;;;;;;sDACnC,sSAAC;4CAAK,WAAU;sDAA8B,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ;;;;;;;;;;;;gCAG9E,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,kBAC7B,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAK,WAAU;sDAAmB;;;;;;sDACnC,sSAAC;4CAAK,WAAU;sDAAoB,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS;;;;;;;;;;;;gCAGrE,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,kBAC/B,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAK,WAAU;sDAAmB;;;;;;sDACnC,sSAAC;4CAAK,WAAU;sDAAoB,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;gBAQ7E,OAAO,IAAI,EAAE,6BACZ,sSAAC;oBAAI,WAAU;;sCACb,sSAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,sSAAC;4BAAE,WAAU;sCAA4B,OAAO,IAAI,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAM5E;KAxKgB", "debugId": null}}, {"offset": {"line": 4523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/density-analysis-result.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Zap, MapPin, Download } from \"lucide-react\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { toast } from \"sonner\";\r\nimport { CompactResultTrigger } from \"./common/compact-result-trigger\";\r\nimport { getToolDisplayInfo } from \"../annotations\";\r\n\r\n// GeoJSON FeatureCollection 구조에 맞춘 간소화된 인터페이스\r\ninterface DensityAnalysisResponse {\r\n  type?: string; // 'FeatureCollection'\r\n  features?: Array<any>; // GeoJSON features 배열\r\n  layerName?: string;\r\n  status?: string;\r\n  error?: string;\r\n}\r\n\r\ninterface DensityAnalysisResultProps {\r\n  content: DensityAnalysisResponse | string;\r\n  className?: string;\r\n}\r\n\r\nexport function DensityAnalysisResult({ content, className }: DensityAnalysisResultProps) {\r\n  let result: DensityAnalysisResponse;\r\n\r\n  try {\r\n    console.log('DensityAnalysisResult content:', content);\r\n    result = typeof content === 'string' ? JSON.parse(content) : content;\r\n  } catch (e) {\r\n    return null;\r\n  }\r\n\r\n  // GeoJSON FeatureCollection에서 피처 개수 추출\r\n  const featureCount = result.features?.length || 0;\r\n\r\n  const handleDownloadResult = () => {\r\n    if (!featureCount) {\r\n      toast.error(\"다운로드할 분석 결과가 없습니다\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const dataStr = JSON.stringify(result, null, 2);\r\n      const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);\r\n      const exportFileDefaultName = `density_analysis_${new Date().toISOString().split('T')[0]}.json`;\r\n\r\n      const linkElement = document.createElement('a');\r\n      linkElement.setAttribute('href', dataUri);\r\n      linkElement.setAttribute('download', exportFileDefaultName);\r\n      linkElement.click();\r\n\r\n      toast.success(\"분석 결과를 다운로드했습니다\");\r\n    } catch (error) {\r\n      toast.error(\"다운로드 중 오류가 발생했습니다\");\r\n    }\r\n  };\r\n\r\n  if (result.error || result.status === 'error') {\r\n    return (\r\n      <div className={cn(\"p-4 rounded-lg border border-red-200 bg-red-50\", className)}>\r\n        <div className=\"flex items-center gap-3\">\r\n          <div className=\"p-2 rounded-full bg-red-100 text-red-600\">\r\n            <Zap className=\"h-4 w-4\" />\r\n          </div>\r\n          <div>\r\n            <p className=\"font-medium text-red-900\">밀도 분석을 완료할 수 없습니다</p>\r\n            <p className=\"text-sm text-red-700\">\r\n              {result.error || \"레이어 데이터에 문제가 있거나 분석 조건이 올바르지 않습니다\"}\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const toolInfo = getToolDisplayInfo(\"performDensityAnalysis\");\r\n\r\n  return (\r\n    <CompactResultTrigger\r\n      icon={toolInfo.icon}\r\n      title={toolInfo.label}\r\n      state=\"result\"\r\n      className={className}\r\n      titleExtra={\r\n        <span className=\"text-xs text-neutral-600\">\r\n          {featureCount.toLocaleString()}개 데이터\r\n        </span>\r\n      }\r\n    >\r\n      <div className=\"space-y-3\">\r\n        <div className=\"text-xs text-neutral-600\">\r\n          총 <span className=\"font-medium\">{featureCount.toLocaleString()}개</span> 데이터 분석이 완료되었습니다.\r\n        </div>\r\n\r\n        {/* 다운로드 버튼 */}\r\n        <div className=\"flex justify-center\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={handleDownloadResult}\r\n            className=\"h-6 px-3 text-xs flex items-center gap-1\"\r\n          >\r\n            <Download className=\"h-3 w-3\" />\r\n            결과 다운로드\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </CompactResultTrigger>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAEA;AACA;AACA;AACA;AACA;AATA;;;;;;;;AAyBO,SAAS,sBAAsB,EAAE,OAAO,EAAE,SAAS,EAA8B;IACtF,IAAI;IAEJ,IAAI;QACF,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,SAAS,OAAO,YAAY,WAAW,KAAK,KAAK,CAAC,WAAW;IAC/D,EAAE,OAAO,GAAG;QACV,OAAO;IACT;IAEA,uCAAuC;IACvC,MAAM,eAAe,OAAO,QAAQ,EAAE,UAAU;IAEhD,MAAM,uBAAuB;QAC3B,IAAI,CAAC,cAAc;YACjB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,UAAU,KAAK,SAAS,CAAC,QAAQ,MAAM;YAC7C,MAAM,UAAU,yCAAyC,mBAAmB;YAC5E,MAAM,wBAAwB,CAAC,iBAAiB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAE/F,MAAM,cAAc,SAAS,aAAa,CAAC;YAC3C,YAAY,YAAY,CAAC,QAAQ;YACjC,YAAY,YAAY,CAAC,YAAY;YACrC,YAAY,KAAK;YAEjB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,IAAI,OAAO,KAAK,IAAI,OAAO,MAAM,KAAK,SAAS;QAC7C,qBACE,sSAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;sBACnE,cAAA,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC,uRAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;kCAEjB,sSAAC;;0CACC,sSAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,sSAAC;gCAAE,WAAU;0CACV,OAAO,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;IAM7B;IAEA,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;IAEpC,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;QACnB,MAAM,SAAS,IAAI;QACnB,OAAO,SAAS,KAAK;QACrB,OAAM;QACN,WAAW;QACX,0BACE,sSAAC;YAAK,WAAU;;gBACb,aAAa,cAAc;gBAAG;;;;;;;kBAInC,cAAA,sSAAC;YAAI,WAAU;;8BACb,sSAAC;oBAAI,WAAU;;wBAA2B;sCACtC,sSAAC;4BAAK,WAAU;;gCAAe,aAAa,cAAc;gCAAG;;;;;;;wBAAQ;;;;;;;8BAIzE,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,sSAAC,iSAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;;;;;;;;;;;AAO5C;KAvFgB", "debugId": null}}, {"offset": {"line": 4719, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/layer-filter-result.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Filter, <PERSON>, Setting<PERSON>, Eye, RotateCcw } from \"lucide-react\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { toast } from \"sonner\";\r\nimport { componentStyles } from \"@/lib/design-tokens\";\r\nimport { useLayerManager, useLayerById } from \"@/providers/tool-invocation-provider\";\r\nimport { CompactResultTrigger } from \"./common/compact-result-trigger\";\r\nimport { getToolDisplayInfo } from \"../annotations\";\r\n\r\n// 실제 타입에 맞게 업데이트\r\ninterface LayerFilterResponse {\r\n  lyr_id: string;\r\n  filter: string;\r\n  description: string;\r\n}\r\n\r\ninterface LayerFilterResultProps {\r\n  content: LayerFilterResponse | string;\r\n  className?: string;\r\n}\r\n\r\n// 컴팩트한 디자인을 위해 애니메이션 간소화\r\n\r\n\r\n\r\nexport function LayerFilterResult({ content, className }: LayerFilterResultProps) {\r\n  let result: LayerFilterResponse;\r\n\r\n  try {\r\n    result = typeof content === 'string' ? JSON.parse(content) : content;\r\n  } catch (e) {\r\n    result = { lyr_id: '', filter: '', description: 'Invalid content format' };\r\n  }\r\n\r\n  // Hook을 최상단에 위치\r\n  const { updateFilter } = useLayerManager();\r\n  const layerInfo = useLayerById(result.lyr_id);\r\n\r\n  const handleToggleFilter = async () => {\r\n    try {\r\n      updateFilter(result.lyr_id, result.filter);\r\n      toast.success(\"필터가 적용되었습니다\");\r\n    } catch (error) {\r\n      console.error(\"Filter update error:\", error);\r\n      toast.error(\"필터 적용 중 오류가 발생했습니다\");\r\n    }\r\n  };\r\n\r\n  const handleResetFilter = async () => {\r\n    try {\r\n      updateFilter(result.lyr_id, \"\");\r\n      toast.success(\"필터가 초기화되었습니다\");\r\n    } catch (error) {\r\n      console.error(\"Filter reset error:\", error);\r\n      toast.error(\"필터 초기화 중 오류가 발생했습니다\");\r\n    }\r\n  };\r\n\r\n  const handleEditFilter = () => {\r\n    // 필터 편집 기능은 추후 구현\r\n    toast.info(\"필터 편집 기능은 준비 중입니다\");\r\n  };\r\n\r\n  if (!result.lyr_id || !result.filter) {\r\n    return (\r\n      <div className={cn(componentStyles.card.error, \"p-3\", className)}>\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className={cn(componentStyles.iconContainer.sm, \"bg-red-100/80 text-red-600 border border-red-200/60\")}>\r\n            <Filter className=\"h-3 w-3\" />\r\n          </div>\r\n          <div>\r\n            <p className=\"font-medium text-red-900 text-sm\">필터 적용에 실패했습니다</p>\r\n            <p className=\"text-xs text-red-700\">필터 조건을 확인하고 다시 시도해주세요</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const toolInfo = getToolDisplayInfo(\"createLayerFilter\");\r\n\r\n  return (\r\n    <CompactResultTrigger\r\n      icon={toolInfo.icon}\r\n      title={toolInfo.label}\r\n      state=\"result\"\r\n      className={className}\r\n      titleExtra={\r\n        <Badge variant=\"outline\" className=\"text-xs py-0 border bg-emerald-100/80 text-emerald-700 border-emerald-200/60\">\r\n          필터 적용\r\n        </Badge>\r\n      }\r\n    >\r\n\r\n      <div className=\"space-y-3\">\r\n        <div className=\"text-xs text-neutral-600\">\r\n          레이어 ID: {result.lyr_id}\r\n        </div>\r\n\r\n        {/* 필터 조건 */}\r\n        <div>\r\n          <div className=\"flex items-center gap-2 mb-1\">\r\n            <Filter className=\"h-3 w-3 text-neutral-600\" />\r\n            <span className=\"text-sm font-medium text-neutral-700\">필터 조건</span>\r\n          </div>\r\n          <div className=\"p-2 bg-white/60 rounded border border-neutral-200/40\">\r\n            <code className=\"text-xs text-neutral-800 font-mono break-all\">\r\n              {result.filter}\r\n            </code>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 설명 */}\r\n        {result.description && (\r\n          <div>\r\n            <div className=\"flex items-center gap-2 mb-1\">\r\n              <Settings className=\"h-3 w-3 text-neutral-600\" />\r\n              <span className=\"text-sm font-medium text-neutral-700\">설명</span>\r\n            </div>\r\n            <p className=\"text-sm text-neutral-700\">{result.description}</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* 액션 버튼들 */}\r\n        <div className=\"flex items-center gap-2 pt-2\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={handleResetFilter}\r\n            className=\"flex items-center gap-1 h-6 px-2 text-xs\"\r\n          >\r\n            <RotateCcw className=\"h-3 w-3\" />\r\n            초기화\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={handleToggleFilter}\r\n            className=\"flex items-center gap-1 h-6 px-2 text-xs\"\r\n          >\r\n            <Eye className=\"h-3 w-3\" />\r\n            적용\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </CompactResultTrigger>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;AA8BO,SAAS,kBAAkB,EAAE,OAAO,EAAE,SAAS,EAA0B;;IAC9E,IAAI;IAEJ,IAAI;QACF,SAAS,OAAO,YAAY,WAAW,KAAK,KAAK,CAAC,WAAW;IAC/D,EAAE,OAAO,GAAG;QACV,SAAS;YAAE,QAAQ;YAAI,QAAQ;YAAI,aAAa;QAAyB;IAC3E;IAEA,gBAAgB;IAChB,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,+IAAA,CAAA,eAAY,AAAD,EAAE,OAAO,MAAM;IAE5C,MAAM,qBAAqB;QACzB,IAAI;YACF,aAAa,OAAO,MAAM,EAAE,OAAO,MAAM;YACzC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,aAAa,OAAO,MAAM,EAAE;YAC5B,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC;IACb;IAEA,IAAI,CAAC,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,EAAE;QACpC,qBACE,sSAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO;sBACpD,cAAA,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE,EAAE;kCACnD,cAAA,sSAAC,6RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,sSAAC;;0CACC,sSAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,sSAAC;gCAAE,WAAU;0CAAuB;;;;;;;;;;;;;;;;;;;;;;;IAK9C;IAEA,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;IAEpC,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;QACnB,MAAM,SAAS,IAAI;QACnB,OAAO,SAAS,KAAK;QACrB,OAAM;QACN,WAAW;QACX,0BACE,sSAAC,6HAAA,CAAA,QAAK;YAAC,SAAQ;YAAU,WAAU;sBAA+E;;;;;;kBAMpH,cAAA,sSAAC;YAAI,WAAU;;8BACb,sSAAC;oBAAI,WAAU;;wBAA2B;wBAC/B,OAAO,MAAM;;;;;;;8BAIxB,sSAAC;;sCACC,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,6RAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,sSAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAEzD,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC;gCAAK,WAAU;0CACb,OAAO,MAAM;;;;;;;;;;;;;;;;;gBAMnB,OAAO,WAAW,kBACjB,sSAAC;;sCACC,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,iSAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,sSAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAEzD,sSAAC;4BAAE,WAAU;sCAA4B,OAAO,WAAW;;;;;;;;;;;;8BAK/D,sSAAC;oBAAI,WAAU;;sCACb,sSAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,sSAAC,uSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGnC,sSAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,sSAAC,uRAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;;AAOvC;GA1HgB;;QAUW,+IAAA,CAAA,kBAAe;QACtB,+IAAA,CAAA,eAAY;;;KAXhB", "debugId": null}}, {"offset": {"line": 5035, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/layer-style-result.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { <PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON>, In<PERSON>, Eye, Settings, Hash, Layers3 } from \"lucide-react\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  HoverCard,\n  HoverCardContent,\n  HoverCardTrigger,\n} from \"@/components/ui/hover-card\";\nimport { cn } from \"@/lib/utils\";\nimport { componentStyles } from \"@/lib/design-tokens\";\nimport { CompactResultTrigger } from \"./common/compact-result-trigger\";\nimport { getToolDisplayInfo } from \"../annotations\";\n\ninterface LayerStyleResponse {\n  layerId: string;\n  styleUpdate: any;\n  description: string;\n  success: boolean;\n}\n\ninterface LayerStyleResultProps {\n  content: LayerStyleResponse | string;\n  className?: string;\n}\n\nconst getStyleTypeColor = (styleKey: string) => {\n  const colors = {\n    'color': 'bg-blue-100/80 text-blue-700 border-blue-200/60',\n    'fillOpacity': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',\n    'strokeColor': 'bg-purple-100/80 text-purple-700 border-purple-200/60',\n    'strokeWidth': 'bg-orange-100/80 text-orange-700 border-orange-200/60',\n    'radius': 'bg-pink-100/80 text-pink-700 border-pink-200/60',\n    'symbol': 'bg-yellow-100/80 text-yellow-700 border-yellow-200/60',\n    'default': 'bg-neutral-100/80 text-neutral-700 border-neutral-200/60'\n  };\n  return colors[styleKey as keyof typeof colors] || colors.default;\n};\n\nexport function LayerStyleResult({ content, className }: LayerStyleResultProps) {\n  let result: LayerStyleResponse;\n\n  try {\n    result = typeof content === 'string' ? JSON.parse(content) : content;\n  } catch (e) {\n    result = { \n      layerId: '', \n      styleUpdate: {}, \n      description: 'Invalid content format', \n      success: false \n    };\n  }\n\n  if (!result.success) {\n    return (\n      <div className={cn(componentStyles.card.error, \"p-3\", className)}>\n        <div className=\"flex items-center gap-2\">\n          <div className={cn(componentStyles.iconContainer.sm, \"bg-red-100/80 text-red-600 border border-red-200/60\")}>\n            <Palette className=\"h-3 w-3\" />\n          </div>\n          <div>\n            <p className=\"font-medium text-red-900 text-sm\">스타일 변경 실패</p>\n            <p className=\"text-xs text-red-700\">\n              {result.description || '스타일 변경 중 오류가 발생했습니다.'}\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // 스타일 속성을 사용자 친화적으로 표시\n  const getStyleDescription = (styleUpdate: any) => {\n    const descriptions = [];\n\n    if (styleUpdate.color) {\n      descriptions.push(`색상: ${styleUpdate.color}`);\n    }\n    if (styleUpdate.fillOpacity !== undefined) {\n      descriptions.push(`투명도: ${(styleUpdate.fillOpacity * 100).toFixed(0)}%`);\n    }\n    if (styleUpdate.strokeColor) {\n      descriptions.push(`윤곽선: ${styleUpdate.strokeColor}`);\n    }\n    if (styleUpdate.strokeWidth !== undefined) {\n      descriptions.push(`윤곽선 두께: ${styleUpdate.strokeWidth}px`);\n    }\n    if (styleUpdate.radius !== undefined) {\n      descriptions.push(`크기: ${styleUpdate.radius}px`);\n    }\n    if (styleUpdate.width !== undefined) {\n      descriptions.push(`선 두께: ${styleUpdate.width}px`);\n    }\n    if (styleUpdate.symbol) {\n      const symbolNames: Record<string, string> = {\n        circle: '원형',\n        square: '사각형',\n        triangle: '삼각형',\n        star: '별',\n        cross: '십자',\n        x: 'X자'\n      };\n      descriptions.push(`심볼: ${symbolNames[styleUpdate.symbol] || styleUpdate.symbol}`);\n    }\n\n    return descriptions.length > 0 ? descriptions.join(', ') : '스타일 속성 변경됨';\n  };\n\n  const styleUpdateEntries = Object.entries(result.styleUpdate);\n  const primaryStyleKey = styleUpdateEntries.length > 0 ? styleUpdateEntries[0][0] : 'default';\n\n  const toolInfo = getToolDisplayInfo(\"updateLayerStyle\");\n\n  return (\n    <CompactResultTrigger\n      icon={toolInfo.icon}\n      title={toolInfo.label}\n      state=\"result\"\n      className={className}\n      titleExtra={\n        styleUpdateEntries.length > 0 && (\n          <Badge variant=\"outline\" className={cn(\"text-xs border\", getStyleTypeColor(primaryStyleKey))}>\n            {primaryStyleKey}\n          </Badge>\n        )\n      }\n    >\n      {/* 스타일 변경 정보 */}\n      <div className=\"space-y-2\">\n        <div className=\"text-xs text-neutral-600\">\n          ID: {result.layerId}\n        </div>\n\n        <div className=\"text-xs text-neutral-600\">\n          {result.description}\n        </div>\n\n        {/* 변경된 스타일 속성 */}\n        {styleUpdateEntries.length > 0 && (\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-1\">\n              <Sparkles className=\"h-3 w-3 text-neutral-500\" />\n              <span className=\"font-medium text-neutral-700 text-xs\">변경된 속성</span>\n            </div>\n            <div className=\"text-xs text-neutral-600\">\n              {getStyleDescription(result.styleUpdate)}\n            </div>\n\n            {/* 스타일 속성 상세 정보 */}\n            <div className=\"pt-2 border-t border-neutral-200\">\n              <h5 className=\"text-xs font-medium text-neutral-700 mb-2\">스타일 속성 상세</h5>\n              <div className=\"space-y-1 text-xs\">\n                {styleUpdateEntries.map(([key, value]) => (\n                  <div key={key} className=\"flex justify-between\">\n                    <span className=\"text-neutral-500\">{key}</span>\n                    <span className=\"text-neutral-700 font-mono\">{String(value)}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </CompactResultTrigger>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAOA;AACA;AACA;AACA;AAdA;;;;;;;;AA4BA,MAAM,oBAAoB,CAAC;IACzB,MAAM,SAAS;QACb,SAAS;QACT,eAAe;QACf,eAAe;QACf,eAAe;QACf,UAAU;QACV,UAAU;QACV,WAAW;IACb;IACA,OAAO,MAAM,CAAC,SAAgC,IAAI,OAAO,OAAO;AAClE;AAEO,SAAS,iBAAiB,EAAE,OAAO,EAAE,SAAS,EAAyB;IAC5E,IAAI;IAEJ,IAAI;QACF,SAAS,OAAO,YAAY,WAAW,KAAK,KAAK,CAAC,WAAW;IAC/D,EAAE,OAAO,GAAG;QACV,SAAS;YACP,SAAS;YACT,aAAa,CAAC;YACd,aAAa;YACb,SAAS;QACX;IACF;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,qBACE,sSAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO;sBACpD,cAAA,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE,EAAE;kCACnD,cAAA,sSAAC,+RAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,sSAAC;;0CACC,sSAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,sSAAC;gCAAE,WAAU;0CACV,OAAO,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;IAMnC;IAEA,uBAAuB;IACvB,MAAM,sBAAsB,CAAC;QAC3B,MAAM,eAAe,EAAE;QAEvB,IAAI,YAAY,KAAK,EAAE;YACrB,aAAa,IAAI,CAAC,CAAC,IAAI,EAAE,YAAY,KAAK,EAAE;QAC9C;QACA,IAAI,YAAY,WAAW,KAAK,WAAW;YACzC,aAAa,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,YAAY,WAAW,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACzE;QACA,IAAI,YAAY,WAAW,EAAE;YAC3B,aAAa,IAAI,CAAC,CAAC,KAAK,EAAE,YAAY,WAAW,EAAE;QACrD;QACA,IAAI,YAAY,WAAW,KAAK,WAAW;YACzC,aAAa,IAAI,CAAC,CAAC,QAAQ,EAAE,YAAY,WAAW,CAAC,EAAE,CAAC;QAC1D;QACA,IAAI,YAAY,MAAM,KAAK,WAAW;YACpC,aAAa,IAAI,CAAC,CAAC,IAAI,EAAE,YAAY,MAAM,CAAC,EAAE,CAAC;QACjD;QACA,IAAI,YAAY,KAAK,KAAK,WAAW;YACnC,aAAa,IAAI,CAAC,CAAC,MAAM,EAAE,YAAY,KAAK,CAAC,EAAE,CAAC;QAClD;QACA,IAAI,YAAY,MAAM,EAAE;YACtB,MAAM,cAAsC;gBAC1C,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,GAAG;YACL;YACA,aAAa,IAAI,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,YAAY,MAAM,CAAC,IAAI,YAAY,MAAM,EAAE;QAClF;QAEA,OAAO,aAAa,MAAM,GAAG,IAAI,aAAa,IAAI,CAAC,QAAQ;IAC7D;IAEA,MAAM,qBAAqB,OAAO,OAAO,CAAC,OAAO,WAAW;IAC5D,MAAM,kBAAkB,mBAAmB,MAAM,GAAG,IAAI,kBAAkB,CAAC,EAAE,CAAC,EAAE,GAAG;IAEnF,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;IAEpC,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;QACnB,MAAM,SAAS,IAAI;QACnB,OAAO,SAAS,KAAK;QACrB,OAAM;QACN,WAAW;QACX,YACE,mBAAmB,MAAM,GAAG,mBAC1B,sSAAC,6HAAA,CAAA,QAAK;YAAC,SAAQ;YAAU,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB,kBAAkB;sBACxE;;;;;;kBAMP,cAAA,sSAAC;YAAI,WAAU;;8BACb,sSAAC;oBAAI,WAAU;;wBAA2B;wBACnC,OAAO,OAAO;;;;;;;8BAGrB,sSAAC;oBAAI,WAAU;8BACZ,OAAO,WAAW;;;;;;gBAIpB,mBAAmB,MAAM,GAAG,mBAC3B,sSAAC;oBAAI,WAAU;;sCACb,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,iSAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,sSAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAEzD,sSAAC;4BAAI,WAAU;sCACZ,oBAAoB,OAAO,WAAW;;;;;;sCAIzC,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAC1D,sSAAC;oCAAI,WAAU;8CACZ,mBAAmB,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACnC,sSAAC;4CAAc,WAAU;;8DACvB,sSAAC;oDAAK,WAAU;8DAAoB;;;;;;8DACpC,sSAAC;oDAAK,WAAU;8DAA8B,OAAO;;;;;;;2CAF7C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY5B;KA9HgB", "debugId": null}}, {"offset": {"line": 5321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/layer-remove-result.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { CompactResultTrigger } from \"./common/compact-result-trigger\";\nimport { getToolDisplayInfo } from \"../annotations\";\n\ninterface LayerRemoveResponse {\n  layerId: string;\n  description: string;\n  success: boolean;\n}\n\ninterface LayerRemoveResultProps {\n  content: LayerRemoveResponse | string;\n  className?: string;\n}\n\nexport function LayerRemoveResult({ content, className }: LayerRemoveResultProps) {\n  let result: LayerRemoveResponse;\n\n  try {\n    result = typeof content === 'string' ? JSON.parse(content) : content;\n  } catch (e) {\n    result = {\n      layerId: '',\n      description: 'Invalid content format',\n      success: false\n    };\n  }\n\n  const toolInfo = getToolDisplayInfo(\"removeLayer\");\n\n  if (!result.success) {\n    return (\n      <CompactResultTrigger\n        icon={toolInfo.icon}\n        title={toolInfo.label}\n        state=\"partial-call\"\n        className={className}\n        titleExtra={\n          <Badge variant=\"outline\" className=\"text-xs py-0 border bg-red-100/80 text-red-700 border-red-200/60\">\n            실패\n          </Badge>\n        }\n      >\n        <div className=\"text-xs text-red-700\">\n          {result.description || '레이어 삭제 중 오류가 발생했습니다.'}\n        </div>\n      </CompactResultTrigger>\n    );\n  }\n\n  return (\n    <CompactResultTrigger\n      icon={toolInfo.icon}\n      title={toolInfo.label}\n      state=\"result\"\n      className={className}\n      titleExtra={\n        <Badge variant=\"outline\" className=\"text-xs py-0 border bg-orange-100/80 text-orange-700 border-orange-200/60\">\n          삭제됨\n        </Badge>\n      }\n    >\n      <div className=\"space-y-2\">\n        <div className=\"text-xs text-neutral-600\">\n          {result.description}\n        </div>\n\n        <div className=\"text-xs text-neutral-500\">\n          삭제된 레이어 ID: {result.layerId}\n        </div>\n      </div>\n    </CompactResultTrigger>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAkBO,SAAS,kBAAkB,EAAE,OAAO,EAAE,SAAS,EAA0B;IAC9E,IAAI;IAEJ,IAAI;QACF,SAAS,OAAO,YAAY,WAAW,KAAK,KAAK,CAAC,WAAW;IAC/D,EAAE,OAAO,GAAG;QACV,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;QACX;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;IAEpC,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;YACnB,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,KAAK;YACrB,OAAM;YACN,WAAW;YACX,0BACE,sSAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;0BAAmE;;;;;;sBAKxG,cAAA,sSAAC;gBAAI,WAAU;0BACZ,OAAO,WAAW,IAAI;;;;;;;;;;;IAI/B;IAEA,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;QACnB,MAAM,SAAS,IAAI;QACnB,OAAO,SAAS,KAAK;QACrB,OAAM;QACN,WAAW;QACX,0BACE,sSAAC,6HAAA,CAAA,QAAK;YAAC,SAAQ;YAAU,WAAU;sBAA4E;;;;;;kBAKjH,cAAA,sSAAC;YAAI,WAAU;;8BACb,sSAAC;oBAAI,WAAU;8BACZ,OAAO,WAAW;;;;;;8BAGrB,sSAAC;oBAAI,WAAU;;wBAA2B;wBAC3B,OAAO,OAAO;;;;;;;;;;;;;;;;;;AAKrC;KA1DgB", "debugId": null}}, {"offset": {"line": 5434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/basemap-change-result.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Map, Check, Globe2, Mountain, Image, Paintbrush } from \"lucide-react\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { UseMapReturn } from \"@geon-map/odf\";\r\nimport { CompactResultTrigger } from \"./common/compact-result-trigger\";\r\nimport { getToolDisplayInfo } from \"../annotations\";\r\n\r\n\r\ninterface BasemapChangeResponse {\r\n  basemap?: string;\r\n  basemapId?: string;\r\n  basemapName?: string;\r\n  error?: string;\r\n}\r\n\r\ninterface BasemapChangeResultProps {\r\n  content: BasemapChangeResponse | string;\r\n  className?: string;\r\n  mapState?: UseMapReturn;\r\n}\r\n\r\n\r\n\r\n// 배경지도 정보 매핑\r\nconst basemapInfo = {\r\n  eMapBasic: {\r\n    name: \"일반지도\",\r\n    icon: Globe2,\r\n    color: \"#4C6EF5\",\r\n  },\r\n  eMapAIR: {\r\n    name: \"항공지도\",\r\n    icon: Image,\r\n    color: \"#40C057\",\r\n  },\r\n  eMapColor: {\r\n    name: \"색각지도\",\r\n    icon: Paintbrush,\r\n    color: \"#FA5252\",\r\n  },\r\n  eMapWhite: {\r\n    name: \"백지도\",\r\n    icon: Mountain,\r\n    color: \"#845EF7\",\r\n  }\r\n} as const;\r\n\r\ntype BasemapId = keyof typeof basemapInfo;\r\n\r\nconst getBasemapDisplayName = (basemapId: string): string => {\r\n  return basemapInfo[basemapId as BasemapId]?.name || basemapId;\r\n};\r\n\r\nconst getBasemapIcon = (basemapId: string) => {\r\n  return basemapInfo[basemapId as BasemapId]?.icon || Map;\r\n};\r\n\r\nconst getBasemapColor = (basemapId: string): string => {\r\n  return basemapInfo[basemapId as BasemapId]?.color || \"#4C6EF5\";\r\n};\r\n\r\nexport function BasemapChangeResult({ content, className, mapState }: BasemapChangeResultProps) {\r\n  let result: BasemapChangeResponse;\r\n\r\n  try {\r\n    result = typeof content === 'string' ? JSON.parse(content) : content;\r\n  } catch (e) {\r\n    result = { error: 'Invalid content format' };\r\n  }\r\n\r\n  // 배경지도 변경은 이제 ToolInvocationProvider의 스마트 네비게이션에서 처리됩니다.\r\n  console.log('Basemap change result:', result);\r\n\r\n  const toolInfo = getToolDisplayInfo(\"changeBasemap\");\r\n\r\n  // early return은 Hook 호출 후에\r\n  if (result.error) {\r\n    return (\r\n      <CompactResultTrigger\r\n        icon={toolInfo.icon}\r\n        title={toolInfo.label}\r\n        state=\"partial-call\"\r\n        className={className}\r\n        titleExtra={\r\n          <Badge variant=\"outline\" className=\"text-xs py-0 border bg-red-100/80 text-red-700 border-red-200/60\">\r\n            오류\r\n          </Badge>\r\n        }\r\n      >\r\n        <div className=\"text-xs text-red-700\">\r\n          {result.error}\r\n        </div>\r\n      </CompactResultTrigger>\r\n    );\r\n  }\r\n\r\n\r\n\r\n  const basemapId = result.basemap || result.basemapId || 'eMapBasic';\r\n  const displayName = result.basemapName || getBasemapDisplayName(basemapId);\r\n  const basemapIcon = getBasemapIcon(basemapId);\r\n  const iconColor = getBasemapColor(basemapId);\r\n\r\n  return (\r\n    <CompactResultTrigger\r\n      icon={toolInfo.icon}\r\n      title={toolInfo.label}\r\n      state=\"result\"\r\n      className={className}\r\n      titleExtra={\r\n        <div className=\"flex items-center gap-1\">\r\n          <Badge variant=\"outline\" className=\"text-xs py-0 border bg-emerald-100/80 text-emerald-700 border-emerald-200/60\">\r\n            {displayName}\r\n          </Badge>\r\n          <div\r\n            className=\"flex h-4 w-4 items-center justify-center rounded-sm\"\r\n            style={{ backgroundColor: `${iconColor}20` }}\r\n          >\r\n            {React.createElement(basemapIcon, {\r\n              className: \"h-2.5 w-2.5\",\r\n              style: { color: iconColor }\r\n            })}\r\n          </div>\r\n        </div>\r\n      }\r\n    >\r\n      <div className=\"text-xs text-neutral-600\">\r\n        배경지도가 <span className=\"font-medium\">{displayName}</span>로 변경되었습니다.\r\n      </div>\r\n    </CompactResultTrigger>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAGA;AACA;AARA;;;;;;;AA0BA,aAAa;AACb,MAAM,cAAc;IAClB,WAAW;QACT,MAAM;QACN,MAAM,4RAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,MAAM,2RAAA,CAAA,QAAK;QACX,OAAO;IACT;IACA,WAAW;QACT,MAAM;QACN,MAAM,qSAAA,CAAA,aAAU;QAChB,OAAO;IACT;IACA,WAAW;QACT,MAAM;QACN,MAAM,iSAAA,CAAA,WAAQ;QACd,OAAO;IACT;AACF;AAIA,MAAM,wBAAwB,CAAC;IAC7B,OAAO,WAAW,CAAC,UAAuB,EAAE,QAAQ;AACtD;AAEA,MAAM,iBAAiB,CAAC;IACtB,OAAO,WAAW,CAAC,UAAuB,EAAE,QAAQ,uRAAA,CAAA,MAAG;AACzD;AAEA,MAAM,kBAAkB,CAAC;IACvB,OAAO,WAAW,CAAC,UAAuB,EAAE,SAAS;AACvD;AAEO,SAAS,oBAAoB,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAA4B;IAC5F,IAAI;IAEJ,IAAI;QACF,SAAS,OAAO,YAAY,WAAW,KAAK,KAAK,CAAC,WAAW;IAC/D,EAAE,OAAO,GAAG;QACV,SAAS;YAAE,OAAO;QAAyB;IAC7C;IAEA,yDAAyD;IACzD,QAAQ,GAAG,CAAC,0BAA0B;IAEtC,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;IAEpC,2BAA2B;IAC3B,IAAI,OAAO,KAAK,EAAE;QAChB,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;YACnB,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,KAAK;YACrB,OAAM;YACN,WAAW;YACX,0BACE,sSAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;0BAAmE;;;;;;sBAKxG,cAAA,sSAAC;gBAAI,WAAU;0BACZ,OAAO,KAAK;;;;;;;;;;;IAIrB;IAIA,MAAM,YAAY,OAAO,OAAO,IAAI,OAAO,SAAS,IAAI;IACxD,MAAM,cAAc,OAAO,WAAW,IAAI,sBAAsB;IAChE,MAAM,cAAc,eAAe;IACnC,MAAM,YAAY,gBAAgB;IAElC,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;QACnB,MAAM,SAAS,IAAI;QACnB,OAAO,SAAS,KAAK;QACrB,OAAM;QACN,WAAW;QACX,0BACE,sSAAC;YAAI,WAAU;;8BACb,sSAAC,6HAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAChC;;;;;;8BAEH,sSAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,iBAAiB,GAAG,UAAU,EAAE,CAAC;oBAAC;8BAE1C,cAAA,sQAAA,CAAA,UAAK,CAAC,aAAa,CAAC,aAAa;wBAChC,WAAW;wBACX,OAAO;4BAAE,OAAO;wBAAU;oBAC5B;;;;;;;;;;;;kBAKN,cAAA,sSAAC;YAAI,WAAU;;gBAA2B;8BAClC,sSAAC;oBAAK,WAAU;8BAAe;;;;;;gBAAmB;;;;;;;;;;;;AAIhE;KAtEgB", "debugId": null}}, {"offset": {"line": 5608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/layer-attributes-detail-dialog.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { Database, Info, Hash, Type, BarChart3, Loader2, Search, Grid3X3, List, Table, Calendar, MapPin, ArrowUpDown } from \"lucide-react\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport {\n  Tabs,\n  TabsContent,\n  TabsList,\n  TabsTrigger,\n} from \"@/components/ui/tabs\";\nimport { Input } from \"@/components/ui/input\";\nimport { cn } from \"@/lib/utils\";\nimport { ServerDataTable } from \"@/components/ui/server-data-table\";\nimport { ColumnDef } from \"@tanstack/react-table\";\n\n\ninterface LayerColumn {\n  name: string;\n  description: string;\n  type: string;\n  editable: boolean;\n  required: boolean;\n  minValue: number | null;\n  maxValue: number | null;\n  groupCode: string | null;\n  groupName: string | null;\n}\n\ninterface DetailedLayerInfo {\n  columns: Array<{\n    name: string;\n    type: string;\n    description?: string;\n    required?: boolean;\n    editable?: boolean;\n    minValue?: number;\n    maxValue?: number;\n  }>;\n  data: Record<string, any>[];\n  totalCount: number;\n  pageInfo: {\n    pageSize: number;\n    pageIndex: number;\n  };\n  pkColumnName?: string;\n}\n\ninterface LayerAttributesDetailDialogProps {\n  isOpen: boolean;\n  onOpenChange: (open: boolean) => void;\n  columns: LayerColumn[];\n  data: Record<string, any>[];\n  layerInfo?: {\n    lyrId: string;\n    cntntsId: string;\n    namespace: string;\n    userId?: string;\n  } | null;\n}\n\n// 속성 타입에 따른 아이콘 반환\nconst getAttributeIcon = (type: string) => {\n  const lowerType = type.toLowerCase();\n  if (lowerType.includes('string') || lowerType.includes('text')) {\n    return <Type className=\"h-3 w-3\" />;\n  } else if (lowerType.includes('number') || lowerType.includes('int') || lowerType.includes('double')) {\n    return <Hash className=\"h-3 w-3\" />;\n  } else if (lowerType.includes('date') || lowerType.includes('time')) {\n    return <Calendar className=\"h-3 w-3\" />;\n  } else if (lowerType.includes('geometry') || lowerType.includes('point') || lowerType.includes('polygon')) {\n    return <MapPin className=\"h-3 w-3\" />;\n  }\n  return <Database className=\"h-3 w-3\" />;\n};\n\n// 속성 타입에 따른 색상 반환\nconst getAttributeTypeColor = (type: string) => {\n  const lowerType = type.toLowerCase();\n  if (lowerType.includes('string') || lowerType.includes('text')) {\n    return 'bg-blue-100/80 text-blue-700 border-blue-200/60';\n  } else if (lowerType.includes('number') || lowerType.includes('int') || lowerType.includes('double')) {\n    return 'bg-green-100/80 text-green-700 border-green-200/60';\n  } else if (lowerType.includes('date') || lowerType.includes('time')) {\n    return 'bg-purple-100/80 text-purple-700 border-purple-200/60';\n  } else if (lowerType.includes('geometry') || lowerType.includes('point') || lowerType.includes('polygon')) {\n    return 'bg-orange-100/80 text-orange-700 border-orange-200/60';\n  }\n  return 'bg-neutral-100/80 text-neutral-700 border-neutral-200/60';\n};\n\nexport function LayerAttributesDetailDialog({\n  isOpen,\n  onOpenChange,\n  columns,\n  data,\n  layerInfo,\n}: LayerAttributesDetailDialogProps) {\n  const [detailedInfo, setDetailedInfo] = useState<DetailedLayerInfo | null>(null);\n  const [isLoadingDetails, setIsLoadingDetails] = useState(false);\n  const [detailsError, setDetailsError] = useState<string | null>(null);\n\n  // 서버 사이드 페이지네이션 상태\n  const [currentPageIndex, setCurrentPageIndex] = useState(1);\n  const [currentPageSize, setCurrentPageSize] = useState(50);\n\n  // UX 개선을 위한 추가 상태\n  const [selectedAttribute, setSelectedAttribute] = useState<string | null>(null);\n  const [viewMode, setViewMode] = useState<'data' | 'attributes'>('data');\n  const [dataFilter, setDataFilter] = useState<string>('');\n\n  // 데이터 테이블 컬럼 정의 생성\n  const createDataTableColumns = (): ColumnDef<Record<string, any>>[] => {\n    const currentColumns = detailedInfo?.columns || columns;\n    return currentColumns.map((column) => ({\n      accessorKey: column.name,\n      header: ({ column: col }) => {\n        return (\n          <Button\n            variant=\"ghost\"\n            onClick={() => col.toggleSorting(col.getIsSorted() === \"asc\")}\n            className=\"h-auto p-1 font-medium text-left justify-start w-full\"\n          >\n            <div className=\"flex flex-col items-start gap-1 w-full min-w-0\">\n              {/* 첫 번째 줄: 컬럼명 + 정렬 아이콘 + PK 뱃지 */}\n              <div className=\"flex items-center gap-1 w-full min-w-0\">\n                {getAttributeIcon(column.type)}\n                <span className=\"text-sm font-medium truncate flex-1 min-w-0\">{column.name}</span>\n                {detailedInfo && column.name === detailedInfo.pkColumnName && (\n                  <Badge variant=\"outline\" className=\"text-[10px] px-1 py-0 bg-yellow-50 text-yellow-700 border-yellow-200 flex-shrink-0\">\n                    PK\n                  </Badge>\n                )}\n                <ArrowUpDown className=\"h-3 w-3 flex-shrink-0 text-muted-foreground\" />\n              </div>\n              \n              {/* 두 번째 줄: 타입 뱃지 + 설명 */}\n              <div className=\"flex items-center gap-1 w-full min-w-0\">\n                <Badge variant=\"outline\" className={cn(\"text-[10px] px-1 py-0 flex-shrink-0\", getAttributeTypeColor(column.type))}>\n                  {column.type}\n                </Badge>\n                {column.description && column.description !== column.name && (\n                  <span className=\"text-[11px] text-muted-foreground truncate flex-1 min-w-0\">\n                    {column.description}\n                  </span>\n                )}\n              </div>\n            </div>\n          </Button>\n        )\n      },\n      cell: ({ getValue }) => {\n        const value = getValue();\n        const displayValue = value !== null && value !== undefined ? String(value) : '-';\n        return (\n          <div className=\"max-w-[200px] min-w-0\">\n            <span className=\"text-xs block truncate\" title={displayValue}>\n              {displayValue}\n            </span>\n          </div>\n        );\n      },\n      minSize: 120,\n      maxSize: 300,\n      \n    }));\n  };\n\n  // API에서 상세 정보를 가져오는 함수\n  const fetchDetailedInfo = async (pageIndex: number = currentPageIndex, pageSize: number = currentPageSize) => {\n    // 레이어 정보가 있는 경우에만 API 호출\n    if (layerInfo) {\n      setIsLoadingDetails(true);\n      setDetailsError(null);\n\n      try {\n        const { lyrId, cntntsId, namespace, userId = 'geonuser' } = layerInfo;\n\n        const response = await fetch('/api/layer/detailed-info', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            userId,\n            lyrId,\n            namespace,\n            cntntsId,\n            pageIndex,\n            pageSize,\n          }),\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.error) {\n          throw new Error(data.error);\n        }\n\n        setDetailedInfo(data);\n      } catch (error: any) {\n        console.error('Error fetching detailed info:', error);\n        setDetailsError(error.message || '상세 정보를 가져오는 중 오류가 발생했습니다.');\n      } finally {\n        setIsLoadingDetails(false);\n      }\n    } else {\n      // 레이어 정보가 없는 경우 안내 메시지만 설정\n      setDetailsError('현재는 기본 속성 정보만 표시됩니다. 더 자세한 정보를 보려면 레이어 관리 기능을 이용해주세요.');\n    }\n  };\n\n  // 페이지네이션 핸들러 함수들\n  const handlePageChange = (newPageIndex: number) => {\n    setCurrentPageIndex(newPageIndex);\n    fetchDetailedInfo(newPageIndex, currentPageSize);\n  };\n\n  const handlePageSizeChange = (newPageSize: number) => {\n    setCurrentPageSize(newPageSize);\n    setCurrentPageIndex(1); // 페이지 크기 변경 시 첫 페이지로 이동\n    fetchDetailedInfo(1, newPageSize);\n  };\n\n  // Dialog가 열릴 때 상세 정보 로드\n  useEffect(() => {\n    if (isOpen && !detailedInfo && !isLoadingDetails) {\n      fetchDetailedInfo();\n    }\n  }, [isOpen]);\n\n  // Dialog가 닫힐 때 상태 초기화\n  useEffect(() => {\n    if (!isOpen) {\n      setDetailedInfo(null);\n      setDetailsError(null);\n      setCurrentPageIndex(1);\n      setCurrentPageSize(50);\n    }\n  }, [isOpen]);\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-7xl w-[95vw] max-h-[90vh] flex flex-col overflow-hidden\">\n        <DialogHeader className=\"flex-shrink-0\">\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Database className=\"h-5 w-5\" />\n            레이어 속성 상세 정보\n          </DialogTitle>\n          <DialogDescription>\n            {detailedInfo ? (\n              <>총 {detailedInfo.columns.length}개의 속성과 {detailedInfo.totalCount}개의 데이터 행이 있습니다.</>\n            ) : (\n              <>기본 정보: {columns.length}개의 속성과 {data.length}개의 데이터 행</>\n            )}\n          </DialogDescription>\n        </DialogHeader>\n\n        {/* 탭 컨테이너 - 오버플로우 처리 개선 */}\n        <div className=\"flex-1 flex flex-col min-h-0 mt-4 overflow-hidden\">\n          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'data' | 'attributes')} className=\"flex flex-col h-full overflow-hidden\">\n            <TabsList className=\"grid w-full grid-cols-2 flex-shrink-0\">\n              <TabsTrigger value=\"data\" className=\"flex items-center gap-2\">\n                <Grid3X3 className=\"h-4 w-4\" />\n                데이터\n              </TabsTrigger>\n              <TabsTrigger value=\"attributes\" className=\"flex items-center gap-2\">\n                <List className=\"h-4 w-4\" />\n                속성 목록\n              </TabsTrigger>\n            </TabsList>\n            \n            {/* 데이터 탭 - 고정 높이 스크롤 구조 */}\n            <TabsContent value=\"data\" className=\"flex-1 flex flex-col min-h-0 space-y-4 mt-4 overflow-hidden\">\n              {/* 에러 상태 */}\n              {detailsError && (\n                <div className=\"flex items-center gap-2 p-3 bg-amber-50 border border-amber-200 rounded flex-shrink-0\">\n                  <Info className=\"h-4 w-4 text-amber-600\" />\n                  <div>\n                    <p className=\"text-sm font-medium text-amber-900\">알림</p>\n                    <p className=\"text-xs text-amber-700\">{detailsError}</p>\n                  </div>\n                </div>\n              )}\n\n              {/* 서버 사이드 데이터 테이블 */}\n              <div className=\"flex-1 min-h-0 overflow-hidden\">\n                <ServerDataTable\n                  columns={createDataTableColumns()}\n                  data={detailedInfo?.data || data}\n                  searchKey={columns.length > 0 ? columns[0].name : undefined}\n                  searchPlaceholder=\"데이터 검색...\"\n                  pageIndex={currentPageIndex}\n                  pageSize={currentPageSize}\n                  totalCount={detailedInfo?.totalCount || data.length}\n                  onPageChange={handlePageChange}\n                  onPageSizeChange={handlePageSizeChange}\n                  isLoading={isLoadingDetails}\n                />\n              </div>\n\n              {!detailedInfo && !isLoadingDetails && data.length > 10 && (\n                <div className=\"text-center text-neutral-500 text-xs py-2 flex-shrink-0\">\n                  ... 및 더 많은 데이터가 있습니다. 상세 정보를 불러오면 더 많은 데이터를 확인할 수 있습니다.\n                </div>\n              )}\n            </TabsContent>\n\n            {/* 속성 목록 탭 - 오버플로우 처리 개선 */}\n            <TabsContent value=\"attributes\" className=\"flex-1 flex flex-col min-h-0 space-y-4 mt-4 overflow-hidden\">\n              <div className=\"flex items-center gap-2 flex-shrink-0\">\n                <Search className=\"h-4 w-4 text-neutral-500\" />\n                <Input\n                  placeholder=\"속성 이름으로 검색...\"\n                  value={dataFilter}\n                  onChange={(e) => setDataFilter(e.target.value)}\n                  className=\"max-w-sm\"\n                />\n              </div>\n\n              {/* 카드 목록 - 스크롤 영역 제한 */}\n              <div className=\"flex-1 min-h-0 overflow-y-auto pr-2\">\n                <div className=\"grid gap-3\">\n                  {(detailedInfo?.columns || columns)\n                    .filter(column =>\n                      dataFilter === '' ||\n                      column.name.toLowerCase().includes(dataFilter.toLowerCase()) ||\n                      (column.description && column.description.toLowerCase().includes(dataFilter.toLowerCase()))\n                    )\n                    .map((column, index) => (\n                      <Card\n                        key={index}\n                        className={cn(\n                          \"cursor-pointer transition-all hover:shadow-md flex-shrink-0\",\n                          selectedAttribute === column.name && \"ring-2 ring-blue-500\"\n                        )}\n                        onClick={() => setSelectedAttribute(selectedAttribute === column.name ? null : column.name)}\n                      >\n                        <CardContent className=\"p-3\">\n                          <div className=\"flex items-start justify-between gap-3\">\n                            <div className=\"flex items-start gap-3 min-w-0 flex-1\">\n                              <div className={cn(\"w-3 h-3 rounded-full flex-shrink-0 mt-0.5\", getAttributeTypeColor(column.type).split(' ')[0])}>\n                              </div>\n                              <div className=\"min-w-0 flex-1\">\n                                <div className=\"flex items-center gap-2 mb-2\">\n                                  {getAttributeIcon(column.type)}\n                                  <span className=\"text-sm font-semibold text-neutral-900 truncate flex-1\">{column.name}</span>\n                                  <div className=\"flex items-center gap-1 flex-shrink-0\">\n                                    {detailedInfo && column.name === detailedInfo.pkColumnName && (\n                                      <Badge variant=\"outline\" className=\"text-xs bg-yellow-100/80 text-yellow-700 border-yellow-200/60\">\n                                        PK\n                                      </Badge>\n                                    )}\n                                    {column.required && (\n                                      <Badge variant=\"outline\" className=\"text-xs bg-red-100/80 text-red-700 border-red-200/60\">\n                                        필수\n                                      </Badge>\n                                    )}\n                                    {column.editable && (\n                                      <Badge variant=\"outline\" className=\"text-xs bg-green-100/80 text-green-700 border-green-200/60\">\n                                        편집가능\n                                      </Badge>\n                                    )}\n                                  </div>\n                                </div>\n                                \n                                {column.description && column.description !== column.name && (\n                                  <p className=\"text-xs text-neutral-600 mb-2 break-words\">{column.description}</p>\n                                )}\n\n                                {(column.minValue !== undefined || column.maxValue !== undefined) && (\n                                  <div className=\"flex items-center gap-2\">\n                                    <Badge variant=\"outline\" className=\"text-xs bg-blue-100/80 text-blue-700 border-blue-200/60\">\n                                      범위: {column.minValue ?? '∞'} ~ {column.maxValue ?? '∞'}\n                                    </Badge>\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n                            <Badge variant=\"outline\" className={cn(\"text-xs flex-shrink-0\", getAttributeTypeColor(column.type))}>\n                              {column.type}\n                            </Badge>\n                          </div>\n                        </CardContent>\n                      </Card>\n                    ))}\n                </div>\n              </div>\n            </TabsContent>\n          </Tabs>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAOA;AAMA;AACA;AACA;;;AAtBA;;;;;;;;;;;AAsEA,mBAAmB;AACnB,MAAM,mBAAmB,CAAC;IACxB,MAAM,YAAY,KAAK,WAAW;IAClC,IAAI,UAAU,QAAQ,CAAC,aAAa,UAAU,QAAQ,CAAC,SAAS;QAC9D,qBAAO,sSAAC,yRAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB,OAAO,IAAI,UAAU,QAAQ,CAAC,aAAa,UAAU,QAAQ,CAAC,UAAU,UAAU,QAAQ,CAAC,WAAW;QACpG,qBAAO,sSAAC,yRAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB,OAAO,IAAI,UAAU,QAAQ,CAAC,WAAW,UAAU,QAAQ,CAAC,SAAS;QACnE,qBAAO,sSAAC,iSAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B,OAAO,IAAI,UAAU,QAAQ,CAAC,eAAe,UAAU,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,YAAY;QACzG,qBAAO,sSAAC,iSAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC3B;IACA,qBAAO,sSAAC,iSAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;AAC7B;AAEA,kBAAkB;AAClB,MAAM,wBAAwB,CAAC;IAC7B,MAAM,YAAY,KAAK,WAAW;IAClC,IAAI,UAAU,QAAQ,CAAC,aAAa,UAAU,QAAQ,CAAC,SAAS;QAC9D,OAAO;IACT,OAAO,IAAI,UAAU,QAAQ,CAAC,aAAa,UAAU,QAAQ,CAAC,UAAU,UAAU,QAAQ,CAAC,WAAW;QACpG,OAAO;IACT,OAAO,IAAI,UAAU,QAAQ,CAAC,WAAW,UAAU,QAAQ,CAAC,SAAS;QACnE,OAAO;IACT,OAAO,IAAI,UAAU,QAAQ,CAAC,eAAe,UAAU,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,YAAY;QACzG,OAAO;IACT;IACA,OAAO;AACT;AAEO,SAAS,4BAA4B,EAC1C,MAAM,EACN,YAAY,EACZ,OAAO,EACP,IAAI,EACJ,SAAS,EACwB;;IACjC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA4B;IAC3E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,mBAAmB;IACnB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,kBAAkB;IAClB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAyB;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,mBAAmB;IACnB,MAAM,yBAAyB;QAC7B,MAAM,iBAAiB,cAAc,WAAW;QAChD,OAAO,eAAe,GAAG,CAAC,CAAC,SAAW,CAAC;gBACrC,aAAa,OAAO,IAAI;gBACxB,QAAQ,CAAC,EAAE,QAAQ,GAAG,EAAE;oBACtB,qBACE,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS,IAAM,IAAI,aAAa,CAAC,IAAI,WAAW,OAAO;wBACvD,WAAU;kCAEV,cAAA,sSAAC;4BAAI,WAAU;;8CAEb,sSAAC;oCAAI,WAAU;;wCACZ,iBAAiB,OAAO,IAAI;sDAC7B,sSAAC;4CAAK,WAAU;sDAA+C,OAAO,IAAI;;;;;;wCACzE,gBAAgB,OAAO,IAAI,KAAK,aAAa,YAAY,kBACxD,sSAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAqF;;;;;;sDAI1H,sSAAC,+SAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAIzB,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC,sBAAsB,OAAO,IAAI;sDAC5G,OAAO,IAAI;;;;;;wCAEb,OAAO,WAAW,IAAI,OAAO,WAAW,KAAK,OAAO,IAAI,kBACvD,sSAAC;4CAAK,WAAU;sDACb,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;gBAOjC;gBACA,MAAM,CAAC,EAAE,QAAQ,EAAE;oBACjB,MAAM,QAAQ;oBACd,MAAM,eAAe,UAAU,QAAQ,UAAU,YAAY,OAAO,SAAS;oBAC7E,qBACE,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC;4BAAK,WAAU;4BAAyB,OAAO;sCAC7C;;;;;;;;;;;gBAIT;gBACA,SAAS;gBACT,SAAS;YAEX,CAAC;IACH;IAEA,uBAAuB;IACvB,MAAM,oBAAoB,OAAO,YAAoB,gBAAgB,EAAE,WAAmB,eAAe;QACvG,yBAAyB;QACzB,IAAI,WAAW;YACb,oBAAoB;YACpB,gBAAgB;YAEhB,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,UAAU,EAAE,GAAG;gBAE5D,MAAM,WAAW,MAAM,MAAM,4BAA4B;oBACvD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB;wBACA;wBACA;wBACA;wBACA;wBACA;oBACF;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;gBAC1D;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,KAAK,EAAE;oBACd,MAAM,IAAI,MAAM,KAAK,KAAK;gBAC5B;gBAEA,gBAAgB;YAClB,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,gBAAgB,MAAM,OAAO,IAAI;YACnC,SAAU;gBACR,oBAAoB;YACtB;QACF,OAAO;YACL,2BAA2B;YAC3B,gBAAgB;QAClB;IACF;IAEA,iBAAiB;IACjB,MAAM,mBAAmB,CAAC;QACxB,oBAAoB;QACpB,kBAAkB,cAAc;IAClC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB;QACnB,oBAAoB,IAAI,wBAAwB;QAChD,kBAAkB,GAAG;IACvB;IAEA,wBAAwB;IACxB,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;iDAAE;YACR,IAAI,UAAU,CAAC,gBAAgB,CAAC,kBAAkB;gBAChD;YACF;QACF;gDAAG;QAAC;KAAO;IAEX,sBAAsB;IACtB,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;iDAAE;YACR,IAAI,CAAC,QAAQ;gBACX,gBAAgB;gBAChB,gBAAgB;gBAChB,oBAAoB;gBACpB,mBAAmB;YACrB;QACF;gDAAG;QAAC;KAAO;IAEX,qBACE,sSAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,sSAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,sSAAC,8HAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,sSAAC,8HAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,sSAAC,iSAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,sSAAC,8HAAA,CAAA,oBAAiB;sCACf,6BACC;;oCAAE;oCAAG,aAAa,OAAO,CAAC,MAAM;oCAAC;oCAAQ,aAAa,UAAU;oCAAC;;6DAEjE;;oCAAE;oCAAQ,QAAQ,MAAM;oCAAC;oCAAQ,KAAK,MAAM;oCAAC;;;;;;;;;;;;;;8BAMnD,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC,4HAAA,CAAA,OAAI;wBAAC,OAAO;wBAAU,eAAe,CAAC,QAAU,YAAY;wBAAiC,WAAU;;0CACtG,sSAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,sSAAC,4HAAA,CAAA,cAAW;wCAAC,OAAM;wCAAO,WAAU;;0DAClC,sSAAC,mSAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGjC,sSAAC,4HAAA,CAAA,cAAW;wCAAC,OAAM;wCAAa,WAAU;;0DACxC,sSAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;0CAMhC,sSAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;gCAAO,WAAU;;oCAEjC,8BACC,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,sSAAC;;kEACC,sSAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,sSAAC;wDAAE,WAAU;kEAA0B;;;;;;;;;;;;;;;;;;kDAM7C,sSAAC;wCAAI,WAAU;kDACb,cAAA,sSAAC,+IAAA,CAAA,kBAAe;4CACd,SAAS;4CACT,MAAM,cAAc,QAAQ;4CAC5B,WAAW,QAAQ,MAAM,GAAG,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG;4CAClD,mBAAkB;4CAClB,WAAW;4CACX,UAAU;4CACV,YAAY,cAAc,cAAc,KAAK,MAAM;4CACnD,cAAc;4CACd,kBAAkB;4CAClB,WAAW;;;;;;;;;;;oCAId,CAAC,gBAAgB,CAAC,oBAAoB,KAAK,MAAM,GAAG,oBACnD,sSAAC;wCAAI,WAAU;kDAA0D;;;;;;;;;;;;0CAO7E,sSAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;gCAAa,WAAU;;kDACxC,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,6RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,sSAAC,6HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAKd,sSAAC;wCAAI,WAAU;kDACb,cAAA,sSAAC;4CAAI,WAAU;sDACZ,CAAC,cAAc,WAAW,OAAO,EAC/B,MAAM,CAAC,CAAA,SACN,eAAe,MACf,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,OAAO,WAAW,IAAI,OAAO,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAExF,GAAG,CAAC,CAAC,QAAQ,sBACZ,sSAAC,4HAAA,CAAA,OAAI;oDAEH,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+DACA,sBAAsB,OAAO,IAAI,IAAI;oDAEvC,SAAS,IAAM,qBAAqB,sBAAsB,OAAO,IAAI,GAAG,OAAO,OAAO,IAAI;8DAE1F,cAAA,sSAAC,4HAAA,CAAA,cAAW;wDAAC,WAAU;kEACrB,cAAA,sSAAC;4DAAI,WAAU;;8EACb,sSAAC;oEAAI,WAAU;;sFACb,sSAAC;4EAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C,sBAAsB,OAAO,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;sFAEhH,sSAAC;4EAAI,WAAU;;8FACb,sSAAC;oFAAI,WAAU;;wFACZ,iBAAiB,OAAO,IAAI;sGAC7B,sSAAC;4FAAK,WAAU;sGAA0D,OAAO,IAAI;;;;;;sGACrF,sSAAC;4FAAI,WAAU;;gGACZ,gBAAgB,OAAO,IAAI,KAAK,aAAa,YAAY,kBACxD,sSAAC,6HAAA,CAAA,QAAK;oGAAC,SAAQ;oGAAU,WAAU;8GAAgE;;;;;;gGAIpG,OAAO,QAAQ,kBACd,sSAAC,6HAAA,CAAA,QAAK;oGAAC,SAAQ;oGAAU,WAAU;8GAAuD;;;;;;gGAI3F,OAAO,QAAQ,kBACd,sSAAC,6HAAA,CAAA,QAAK;oGAAC,SAAQ;oGAAU,WAAU;8GAA6D;;;;;;;;;;;;;;;;;;gFAOrG,OAAO,WAAW,IAAI,OAAO,WAAW,KAAK,OAAO,IAAI,kBACvD,sSAAC;oFAAE,WAAU;8FAA6C,OAAO,WAAW;;;;;;gFAG7E,CAAC,OAAO,QAAQ,KAAK,aAAa,OAAO,QAAQ,KAAK,SAAS,mBAC9D,sSAAC;oFAAI,WAAU;8FACb,cAAA,sSAAC,6HAAA,CAAA,QAAK;wFAAC,SAAQ;wFAAU,WAAU;;4FAA0D;4FACtF,OAAO,QAAQ,IAAI;4FAAI;4FAAI,OAAO,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;8EAM7D,sSAAC,6HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,sBAAsB,OAAO,IAAI;8EAC9F,OAAO,IAAI;;;;;;;;;;;;;;;;;mDAjDb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+D7B;GAnTgB;KAAA", "debugId": null}}, {"offset": {"line": 6312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/layer-attributes-result.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport { Database, Hash, Type, Calendar, MapPin, Eye } from \"lucide-react\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { cn } from \"@/lib/utils\";\nimport { UseMapReturn } from \"@geon-map/odf\";\nimport { CompactResultTrigger } from \"./common/compact-result-trigger\";\nimport { LayerAttributesDetailDialog } from \"./layer-attributes-detail-dialog\";\n\ninterface LayerColumn {\n  name: string;\n  description: string;\n  type: string;\n  editable: boolean;\n  required: boolean;\n  minValue: number | null;\n  maxValue: number | null;\n  groupCode: string | null;\n  groupName: string | null;\n}\n\ninterface LayerAttributesResponse {\n  columns: LayerColumn[];\n  data: Record<string, any>[];\n  error?: string;\n  status?: string;\n  layerInfo?: {\n    lyrId: string;\n    cntntsId: string;\n    namespace: string;\n    userId?: string;\n  };\n}\n\ninterface LayerAttributesResultProps {\n  content: LayerAttributesResponse | string;\n  className?: string;\n  mapState?: UseMapReturn;\n  invocation?: {\n    args?: {\n      userId?: string;\n      lyrId?: string;\n      namespace?: string;\n      cntntsId?: string;\n    };\n  };\n}\n\n// 속성 타입에 따른 색상 반환\nconst getAttributeTypeColor = (type: string) => {\n  const colors = {\n    'string': 'bg-blue-100/80 text-blue-700 border-blue-200/60',\n    'text': 'bg-blue-100/80 text-blue-700 border-blue-200/60',\n    'varchar': 'bg-blue-100/80 text-blue-700 border-blue-200/60',\n    'integer': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',\n    'int': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',\n    'number': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',\n    'double': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',\n    'float': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',\n    'date': 'bg-purple-100/80 text-purple-700 border-purple-200/60',\n    'datetime': 'bg-purple-100/80 text-purple-700 border-purple-200/60',\n    'timestamp': 'bg-purple-100/80 text-purple-700 border-purple-200/60',\n    'geometry': 'bg-orange-100/80 text-orange-700 border-orange-200/60',\n    'point': 'bg-orange-100/80 text-orange-700 border-orange-200/60',\n    'polygon': 'bg-orange-100/80 text-orange-700 border-orange-200/60',\n    'boolean': 'bg-amber-100/80 text-amber-700 border-amber-200/60',\n  };\n\n  const lowerType = type.toLowerCase();\n  return colors[lowerType as keyof typeof colors] || 'bg-neutral-100/80 text-neutral-700 border-neutral-200/60';\n};\n\n// 속성 타입에 따른 아이콘 반환\nconst getAttributeIcon = (type: string) => {\n  const lowerType = type.toLowerCase();\n  if (lowerType.includes('string') || lowerType.includes('text') || lowerType.includes('varchar')) {\n    return <Type className=\"h-3 w-3\" />;\n  } else if (lowerType.includes('number') || lowerType.includes('int') || lowerType.includes('double') || lowerType.includes('float')) {\n    return <Hash className=\"h-3 w-3\" />;\n  } else if (lowerType.includes('date') || lowerType.includes('time')) {\n    return <Calendar className=\"h-3 w-3\" />;\n  } else if (lowerType.includes('geometry') || lowerType.includes('point') || lowerType.includes('polygon')) {\n    return <MapPin className=\"h-3 w-3\" />;\n  }\n  return <Database className=\"h-3 w-3\" />;\n};\n\nexport function LayerAttributesResult({ content, className, invocation }: LayerAttributesResultProps) {\n  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);\n\n  let result: LayerAttributesResponse;\n\n  try {\n    if (typeof content === 'string') {\n      result = JSON.parse(content);\n    } else {\n      result = content;\n    }\n  } catch (error) {\n    return (\n      <CompactResultTrigger\n        icon={<Database className=\"h-4 w-4\" />}\n        title=\"레이어 속성 조회\"\n        state=\"result\"\n        className={className}\n      >\n        <div className=\"text-red-600 text-sm\">\n          결과를 파싱하는 중 오류가 발생했습니다: {error instanceof Error ? error.message : '알 수 없는 오류'}\n        </div>\n      </CompactResultTrigger>\n    );\n  }\n\n  if (result.error) {\n    return (\n      <CompactResultTrigger\n        icon={<Database className=\"h-4 w-4\" />}\n        title=\"레이어 속성 조회\"\n        state=\"result\"\n        className={className}\n      >\n        <div className=\"text-red-600 text-sm\">\n          {result.error}\n        </div>\n      </CompactResultTrigger>\n    );\n  }\n\n  // invocation.args에서 레이어 정보 추출\n  const getLayerInfoFromArgs = () => {\n    if (result.layerInfo) {\n      return result.layerInfo;\n    }\n\n    if (invocation?.args) {\n      const { userId = 'geonuser', lyrId, namespace, cntntsId } = invocation.args;\n      if (lyrId && namespace && cntntsId) {\n        return {\n          lyrId,\n          cntntsId,\n          namespace,\n          userId,\n        };\n      }\n    }\n\n    return null;\n  };\n\n  const layerInfo = getLayerInfoFromArgs();\n\n  return (\n    <CompactResultTrigger\n      icon={<Database className=\"h-4 w-4\" />}\n      title=\"레이어 속성 조회\"\n      state=\"result\"\n      className={className}\n    >\n      <div className=\"space-y-2\">\n        {/* 주요 속성 미리보기 - 컴팩트 버전 */}\n        <div className=\"space-y-1\">\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-sm font-medium text-neutral-700\">주요 속성 ({result.columns.length}개)</span>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              className=\"h-6 text-xs px-2\"\n              onClick={() => setIsDetailDialogOpen(true)}\n            >\n              <Eye className=\"h-3 w-3 mr-1\" />\n              전체 보기\n            </Button>\n          </div>\n\n          <div className=\"grid gap-1\">\n            {result.columns.slice(0, 3).map((column, index) => (\n              <div key={index} className=\"flex items-center gap-2 p-1.5 bg-neutral-50/60 rounded border border-neutral-200/40\">\n                <div className=\"flex items-center gap-1.5 flex-1\">\n                  {getAttributeIcon(column.type)}\n                  <span className=\"text-xs font-medium text-neutral-900\">{column.name}</span>\n                  <Badge variant=\"outline\" className={cn(\"text-xs px-1 py-0\", getAttributeTypeColor(column.type))}>\n                    {column.type}\n                  </Badge>\n                </div>\n                {column.description && column.description !== column.name && (\n                  <span className=\"text-xs text-neutral-600 truncate max-w-[100px]\">{column.description}</span>\n                )}\n              </div>\n            ))}\n\n            {result.columns.length > 3 && (\n              <div className=\"text-center py-0.5\">\n                <span className=\"text-xs text-neutral-500\">... 및 {result.columns.length - 3}개 속성 더</span>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* 상세 정보 Dialog */}\n      <LayerAttributesDetailDialog\n        isOpen={isDetailDialogOpen}\n        onOpenChange={setIsDetailDialogOpen}\n        columns={result.columns}\n        data={result.data}\n        layerInfo={layerInfo}\n      />\n    </CompactResultTrigger>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;;;AATA;;;;;;;;AAkDA,kBAAkB;AAClB,MAAM,wBAAwB,CAAC;IAC7B,MAAM,SAAS;QACb,UAAU;QACV,QAAQ;QACR,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,UAAU;QACV,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,SAAS;QACT,WAAW;QACX,WAAW;IACb;IAEA,MAAM,YAAY,KAAK,WAAW;IAClC,OAAO,MAAM,CAAC,UAAiC,IAAI;AACrD;AAEA,mBAAmB;AACnB,MAAM,mBAAmB,CAAC;IACxB,MAAM,YAAY,KAAK,WAAW;IAClC,IAAI,UAAU,QAAQ,CAAC,aAAa,UAAU,QAAQ,CAAC,WAAW,UAAU,QAAQ,CAAC,YAAY;QAC/F,qBAAO,sSAAC,yRAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB,OAAO,IAAI,UAAU,QAAQ,CAAC,aAAa,UAAU,QAAQ,CAAC,UAAU,UAAU,QAAQ,CAAC,aAAa,UAAU,QAAQ,CAAC,UAAU;QACnI,qBAAO,sSAAC,yRAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB,OAAO,IAAI,UAAU,QAAQ,CAAC,WAAW,UAAU,QAAQ,CAAC,SAAS;QACnE,qBAAO,sSAAC,iSAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B,OAAO,IAAI,UAAU,QAAQ,CAAC,eAAe,UAAU,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,YAAY;QACzG,qBAAO,sSAAC,iSAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC3B;IACA,qBAAO,sSAAC,iSAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;AAC7B;AAEO,SAAS,sBAAsB,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAA8B;;IAClG,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,IAAI;IAEJ,IAAI;QACF,IAAI,OAAO,YAAY,UAAU;YAC/B,SAAS,KAAK,KAAK,CAAC;QACtB,OAAO;YACL,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;YACnB,oBAAM,sSAAC,iSAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAM;YACN,OAAM;YACN,WAAW;sBAEX,cAAA,sSAAC;gBAAI,WAAU;;oBAAuB;oBACZ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;;;;;;;;;;;;IAIzE;IAEA,IAAI,OAAO,KAAK,EAAE;QAChB,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;YACnB,oBAAM,sSAAC,iSAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAM;YACN,OAAM;YACN,WAAW;sBAEX,cAAA,sSAAC;gBAAI,WAAU;0BACZ,OAAO,KAAK;;;;;;;;;;;IAIrB;IAEA,8BAA8B;IAC9B,MAAM,uBAAuB;QAC3B,IAAI,OAAO,SAAS,EAAE;YACpB,OAAO,OAAO,SAAS;QACzB;QAEA,IAAI,YAAY,MAAM;YACpB,MAAM,EAAE,SAAS,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,WAAW,IAAI;YAC3E,IAAI,SAAS,aAAa,UAAU;gBAClC,OAAO;oBACL;oBACA;oBACA;oBACA;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,YAAY;IAElB,qBACE,sSAAC,iKAAA,CAAA,uBAAoB;QACnB,oBAAM,sSAAC,iSAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,OAAM;QACN,OAAM;QACN,WAAW;;0BAEX,sSAAC;gBAAI,WAAU;0BAEb,cAAA,sSAAC;oBAAI,WAAU;;sCACb,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAK,WAAU;;wCAAuC;wCAAQ,OAAO,OAAO,CAAC,MAAM;wCAAC;;;;;;;8CACrF,sSAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,sBAAsB;;sDAErC,sSAAC,uRAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAKpC,sSAAC;4BAAI,WAAU;;gCACZ,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBACvC,sSAAC;wCAAgB,WAAU;;0DACzB,sSAAC;gDAAI,WAAU;;oDACZ,iBAAiB,OAAO,IAAI;kEAC7B,sSAAC;wDAAK,WAAU;kEAAwC,OAAO,IAAI;;;;;;kEACnE,sSAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB,sBAAsB,OAAO,IAAI;kEAC1F,OAAO,IAAI;;;;;;;;;;;;4CAGf,OAAO,WAAW,IAAI,OAAO,WAAW,KAAK,OAAO,IAAI,kBACvD,sSAAC;gDAAK,WAAU;0DAAmD,OAAO,WAAW;;;;;;;uCAT/E;;;;;gCAcX,OAAO,OAAO,CAAC,MAAM,GAAG,mBACvB,sSAAC;oCAAI,WAAU;8CACb,cAAA,sSAAC;wCAAK,WAAU;;4CAA2B;4CAAO,OAAO,OAAO,CAAC,MAAM,GAAG;4CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtF,sSAAC,kKAAA,CAAA,8BAA2B;gBAC1B,QAAQ;gBACR,cAAc;gBACd,SAAS,OAAO,OAAO;gBACvB,MAAM,OAAO,IAAI;gBACjB,WAAW;;;;;;;;;;;;AAInB;GA1HgB;KAAA", "debugId": null}}, {"offset": {"line": 6660, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/tools/tool-invocation-part.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport type { ToolInvocation } from \"ai\";\r\nimport { ToolResult } from \"./tool-result\";\r\nimport { ToolCall } from \"./tool-call\";\r\nimport { SearchAddressResult } from \"./search-address-result\";\r\nimport { SearchOriginResult } from \"./search-origin-result\";\r\nimport { SearchDestinationResult } from \"./search-destination-result\";\r\nimport { SearchDirectionsResult } from \"./search-directions-result\";\r\nimport { LayerListResult } from \"./layer-list-result\";\r\nimport { LayerInfoResult } from \"./layer-info-result\";\r\nimport { DensityAnalysisResult } from \"./density-analysis-result\";\r\nimport { LayerFilterResult } from \"./layer-filter-result\";\r\nimport { LayerStyleResult } from \"./layer-style-result\";\r\nimport { LayerRemoveResult } from \"./layer-remove-result\";\r\nimport { BasemapChangeResult } from \"./basemap-change-result\";\r\nimport { LayerAttributesResult } from \"./layer-attributes-result\";\r\nimport { UseMapReturn } from \"@geon-map/odf\";\r\n\r\n\r\ninterface ToolInvocationPartProps {\r\n  invocation: ToolInvocation;\r\n  addToolResult: (params: { toolCallId: string; result: string }) => void;\r\n  className?: string;\r\n  status: \"submitted\" | \"streaming\" | \"ready\" | \"error\";\r\n  mapState?: UseMapReturn;\r\n}\r\n\r\nexport const ToolInvocationPart = ({\r\n  invocation,\r\n  addToolResult,\r\n  className,\r\n  status,\r\n  mapState,\r\n}: ToolInvocationPartProps) => {\r\n  // 길찾기 요청 처리 함수\r\n  const handleDirectionsRequest = (origin: string, destination: string) => {\r\n    // searchDirections 도구를 호출하는 새로운 도구 결과 추가\r\n    const directionsToolCall = {\r\n      toolCallId: `directions-${Date.now()}`,\r\n      result: JSON.stringify({\r\n        toolName: \"searchDirections\",\r\n        args: { origin, destination },\r\n        status: \"pending\"\r\n      })\r\n    };\r\n\r\n    addToolResult(directionsToolCall);\r\n  };\r\n\r\n  if (invocation.state === \"result\") {\r\n    // 각 도구별로 전용 결과 컴포넌트 사용\r\n    switch (invocation.toolName) {\r\n      case \"searchAddress\":\r\n        return (\r\n          <SearchAddressResult\r\n            key={invocation.toolCallId}\r\n            content={invocation.result}\r\n            className={className}\r\n            mapState={mapState}\r\n            onDirectionsRequest={handleDirectionsRequest}\r\n          />\r\n        );\r\n\r\n      case \"searchOrigin\":\r\n        return (\r\n          <SearchOriginResult\r\n            key={invocation.toolCallId}\r\n            content={invocation.result}\r\n            className={className}\r\n            mapState={mapState}\r\n          />\r\n        );\r\n\r\n      case \"searchDestination\":\r\n        return (\r\n          <SearchDestinationResult\r\n            key={invocation.toolCallId}\r\n            content={invocation.result}\r\n            className={className}\r\n            mapState={mapState}\r\n          />\r\n        );\r\n\r\n      case \"searchDirections\":\r\n        return (\r\n          <SearchDirectionsResult\r\n            key={invocation.toolCallId}\r\n            content={invocation.result}\r\n            className={className}\r\n            mapState={mapState}\r\n            toolCallId={invocation.toolCallId}\r\n          />\r\n        );\r\n\r\n      case \"getLayerList\":\r\n        return (\r\n          <LayerListResult\r\n            key={invocation.toolCallId}\r\n            content={invocation.result}\r\n            className={className}\r\n            mapState={mapState}\r\n          />\r\n        );\r\n\r\n      case \"getLayer\":\r\n        return (\r\n          <LayerInfoResult\r\n            key={invocation.toolCallId}\r\n            content={invocation.result}\r\n            className={className}\r\n            mapState={mapState}\r\n          />\r\n        );\r\n\r\n      case \"getLayerAttributes\":\r\n        return (\r\n          <LayerAttributesResult\r\n            key={invocation.toolCallId}\r\n            content={invocation.result}\r\n            invocation={invocation}\r\n            className={className}\r\n            mapState={mapState}\r\n          />\r\n        );\r\n\r\n      case \"performDensityAnalysis\":\r\n        return (\r\n          <DensityAnalysisResult\r\n            key={invocation.toolCallId}\r\n            content={invocation.result}\r\n            className={className}\r\n          />\r\n        );\r\n\r\n      case \"createLayerFilter\":\r\n        return (\r\n          <LayerFilterResult\r\n            key={invocation.toolCallId}\r\n            content={invocation.result}\r\n            className={className}\r\n          />\r\n        );\r\n\r\n      case \"updateLayerStyle\":\r\n        return (\r\n          <LayerStyleResult\r\n            key={invocation.toolCallId}\r\n            content={invocation.result}\r\n            className={className}\r\n          />\r\n        );\r\n\r\n      case \"removeLayer\":\r\n        return (\r\n          <LayerRemoveResult\r\n            key={invocation.toolCallId}\r\n            content={invocation.result}\r\n            className={className}\r\n          />\r\n        );\r\n\r\n      case \"changeBasemap\":\r\n        return (\r\n          <BasemapChangeResult\r\n            key={invocation.toolCallId}\r\n            content={invocation.result}\r\n            className={className}\r\n            mapState={mapState}\r\n          />\r\n        );\r\n\r\n      // 기본 ToolResult 컴포넌트 사용\r\n      default:\r\n        return (\r\n          <ToolResult\r\n            key={invocation.toolCallId}\r\n            toolName={invocation.toolName}\r\n            state=\"result\"\r\n            className={className}\r\n            content={\r\n              typeof (invocation as any).result === \"string\"\r\n                ? ((invocation as any).result as string)\r\n                : JSON.stringify(\r\n                  (invocation as any).result ?? invocation.args,\r\n                  null,\r\n                  2\r\n                )\r\n            }\r\n          />\r\n        );\r\n    }\r\n  }\r\n\r\n  if (invocation.state === \"call\" || invocation.state === \"partial-call\") {\r\n    return (\r\n      <ToolCall\r\n        key={invocation.toolCallId}\r\n        invocation={invocation}\r\n        addToolResult={addToolResult}\r\n        className={className}\r\n      />\r\n    );\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA;;;;;;;;;;;;;;;;AA6BO,MAAM,qBAAqB,CAAC,EACjC,UAAU,EACV,aAAa,EACb,SAAS,EACT,MAAM,EACN,QAAQ,EACgB;IACxB,eAAe;IACf,MAAM,0BAA0B,CAAC,QAAgB;QAC/C,yCAAyC;QACzC,MAAM,qBAAqB;YACzB,YAAY,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI;YACtC,QAAQ,KAAK,SAAS,CAAC;gBACrB,UAAU;gBACV,MAAM;oBAAE;oBAAQ;gBAAY;gBAC5B,QAAQ;YACV;QACF;QAEA,cAAc;IAChB;IAEA,IAAI,WAAW,KAAK,KAAK,UAAU;QACjC,uBAAuB;QACvB,OAAQ,WAAW,QAAQ;YACzB,KAAK;gBACH,qBACE,sSAAC,sJAAA,CAAA,sBAAmB;oBAElB,SAAS,WAAW,MAAM;oBAC1B,WAAW;oBACX,UAAU;oBACV,qBAAqB;mBAJhB,WAAW,UAAU;;;;;YAQhC,KAAK;gBACH,qBACE,sSAAC,qJAAA,CAAA,qBAAkB;oBAEjB,SAAS,WAAW,MAAM;oBAC1B,WAAW;oBACX,UAAU;mBAHL,WAAW,UAAU;;;;;YAOhC,KAAK;gBACH,qBACE,sSAAC,0JAAA,CAAA,0BAAuB;oBAEtB,SAAS,WAAW,MAAM;oBAC1B,WAAW;oBACX,UAAU;mBAHL,WAAW,UAAU;;;;;YAOhC,KAAK;gBACH,qBACE,sSAAC,yJAAA,CAAA,yBAAsB;oBAErB,SAAS,WAAW,MAAM;oBAC1B,WAAW;oBACX,UAAU;oBACV,YAAY,WAAW,UAAU;mBAJ5B,WAAW,UAAU;;;;;YAQhC,KAAK;gBACH,qBACE,sSAAC,kJAAA,CAAA,kBAAe;oBAEd,SAAS,WAAW,MAAM;oBAC1B,WAAW;oBACX,UAAU;mBAHL,WAAW,UAAU;;;;;YAOhC,KAAK;gBACH,qBACE,sSAAC,kJAAA,CAAA,kBAAe;oBAEd,SAAS,WAAW,MAAM;oBAC1B,WAAW;oBACX,UAAU;mBAHL,WAAW,UAAU;;;;;YAOhC,KAAK;gBACH,qBACE,sSAAC,wJAAA,CAAA,wBAAqB;oBAEpB,SAAS,WAAW,MAAM;oBAC1B,YAAY;oBACZ,WAAW;oBACX,UAAU;mBAJL,WAAW,UAAU;;;;;YAQhC,KAAK;gBACH,qBACE,sSAAC,wJAAA,CAAA,wBAAqB;oBAEpB,SAAS,WAAW,MAAM;oBAC1B,WAAW;mBAFN,WAAW,UAAU;;;;;YAMhC,KAAK;gBACH,qBACE,sSAAC,oJAAA,CAAA,oBAAiB;oBAEhB,SAAS,WAAW,MAAM;oBAC1B,WAAW;mBAFN,WAAW,UAAU;;;;;YAMhC,KAAK;gBACH,qBACE,sSAAC,mJAAA,CAAA,mBAAgB;oBAEf,SAAS,WAAW,MAAM;oBAC1B,WAAW;mBAFN,WAAW,UAAU;;;;;YAMhC,KAAK;gBACH,qBACE,sSAAC,oJAAA,CAAA,oBAAiB;oBAEhB,SAAS,WAAW,MAAM;oBAC1B,WAAW;mBAFN,WAAW,UAAU;;;;;YAMhC,KAAK;gBACH,qBACE,sSAAC,sJAAA,CAAA,sBAAmB;oBAElB,SAAS,WAAW,MAAM;oBAC1B,WAAW;oBACX,UAAU;mBAHL,WAAW,UAAU;;;;;YAOhC,wBAAwB;YACxB;gBACE,qBACE,sSAAC,yIAAA,CAAA,aAAU;oBAET,UAAU,WAAW,QAAQ;oBAC7B,OAAM;oBACN,WAAW;oBACX,SACE,OAAO,AAAC,WAAmB,MAAM,KAAK,WACjC,AAAC,WAAmB,MAAM,GAC3B,KAAK,SAAS,CACd,AAAC,WAAmB,MAAM,IAAI,WAAW,IAAI,EAC7C,MACA;mBAVD,WAAW,UAAU;;;;;QAelC;IACF;IAEA,IAAI,WAAW,KAAK,KAAK,UAAU,WAAW,KAAK,KAAK,gBAAgB;QACtE,qBACE,sSAAC,uIAAA,CAAA,WAAQ;YAEP,YAAY;YACZ,eAAe;YACf,WAAW;WAHN,WAAW,UAAU;;;;;IAMhC;AACF;KAhLa", "debugId": null}}]}