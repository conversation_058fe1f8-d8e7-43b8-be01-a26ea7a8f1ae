import { auth } from "@/app/(auth)/auth";
import { getMapsByUserId, saveMap, updateMapView } from "@/lib/db/queries";
import { generateId } from "ai";

import { Map } from "@/types/map";

export async function GET() {
  const session = await auth();

  if (!session?.user) {
    return Response.json("Unauthorized!", { status: 401 });
  }

  try {
    const maps = await getMapsByUserId({ userId: session.user.id ?? "" });
    return Response.json(maps, { status: 200 });
  } catch (error) {
    return Response.json("Failed to fetch maps", { status: 500 });
  }
}

export async function POST(request: Request) {
  const session = await auth();

  if (!session?.user) {
    return Response.json("Unauthorized!", { status: 401 });
  }

  if (!session.user.id) {
    return Response.json("User ID is required", { status: 401 });
  }

  try {
    const { name, map }: { name: string; map: Map } = await request.json();
    const userId = session.user.id;
    const mapId = generateId();

    // 지도 저장
    await saveMap({
      id: mapId,
      name,
      layers: map.mapInfo.layers,
      userId,
    });

    // map_view 저장
    await updateMapView({
      mapId,
      userId,
      view: map.view,
    });

    // 새로 생성된 지도 정보 조회해서 반환
    const newMap = await getMapsByUserId({ userId });
    const createdMap = newMap.find((m) => m.id === mapId);

    if (!createdMap) {
      return new Response("Failed to create map", { status: 500 });
    }

    return Response.json(createdMap, { status: 200 });
  } catch (error) {
    return Response.json("Failed to create map", { status: 500 });
  }
}
